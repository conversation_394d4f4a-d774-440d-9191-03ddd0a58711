// lib: , url: package:source_span/src/highlighter.dart

// class id: 1051138, size: 0x8
class :: {
}

// class id: 489, size: 0x1c, field offset: 0x8
class _Line extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc400f8, size: 0xe4
    // 0xc400f8: EnterFrame
    //     0xc400f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc400fc: mov             fp, SP
    // 0xc40100: AllocStack(0x18)
    //     0xc40100: sub             SP, SP, #0x18
    // 0xc40104: CheckStackOverflow
    //     0xc40104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc40108: cmp             SP, x16
    //     0xc4010c: b.ls            #0xc401d4
    // 0xc40110: ldr             x3, [fp, #0x10]
    // 0xc40114: LoadField: r2 = r3->field_b
    //     0xc40114: ldur            x2, [x3, #0xb]
    // 0xc40118: r0 = BoxInt64Instr(r2)
    //     0xc40118: sbfiz           x0, x2, #1, #0x1f
    //     0xc4011c: cmp             x2, x0, asr #1
    //     0xc40120: b.eq            #0xc4012c
    //     0xc40124: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc40128: stur            x2, [x0, #7]
    // 0xc4012c: r1 = Null
    //     0xc4012c: mov             x1, NULL
    // 0xc40130: r2 = 12
    //     0xc40130: movz            x2, #0xc
    // 0xc40134: stur            x0, [fp, #-8]
    // 0xc40138: r0 = AllocateArray()
    //     0xc40138: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4013c: mov             x2, x0
    // 0xc40140: ldur            x0, [fp, #-8]
    // 0xc40144: stur            x2, [fp, #-0x10]
    // 0xc40148: StoreField: r2->field_f = r0
    //     0xc40148: stur            w0, [x2, #0xf]
    // 0xc4014c: r16 = ": \""
    //     0xc4014c: add             x16, PP, #0x20, lsl #12  ; [pp+0x209c0] ": \""
    //     0xc40150: ldr             x16, [x16, #0x9c0]
    // 0xc40154: StoreField: r2->field_13 = r16
    //     0xc40154: stur            w16, [x2, #0x13]
    // 0xc40158: ldr             x0, [fp, #0x10]
    // 0xc4015c: LoadField: r1 = r0->field_7
    //     0xc4015c: ldur            w1, [x0, #7]
    // 0xc40160: DecompressPointer r1
    //     0xc40160: add             x1, x1, HEAP, lsl #32
    // 0xc40164: ArrayStore: r2[0] = r1  ; List_4
    //     0xc40164: stur            w1, [x2, #0x17]
    // 0xc40168: r16 = "\" ("
    //     0xc40168: add             x16, PP, #0x20, lsl #12  ; [pp+0x209c8] "\" ("
    //     0xc4016c: ldr             x16, [x16, #0x9c8]
    // 0xc40170: StoreField: r2->field_1b = r16
    //     0xc40170: stur            w16, [x2, #0x1b]
    // 0xc40174: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc40174: ldur            w1, [x0, #0x17]
    // 0xc40178: DecompressPointer r1
    //     0xc40178: add             x1, x1, HEAP, lsl #32
    // 0xc4017c: r16 = ", "
    //     0xc4017c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc40180: str             x16, [SP]
    // 0xc40184: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc40184: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc40188: r0 = join()
    //     0xc40188: bl              #0x86bd4c  ; [dart:core] _GrowableList::join
    // 0xc4018c: ldur            x1, [fp, #-0x10]
    // 0xc40190: ArrayStore: r1[4] = r0  ; List_4
    //     0xc40190: add             x25, x1, #0x1f
    //     0xc40194: str             w0, [x25]
    //     0xc40198: tbz             w0, #0, #0xc401b4
    //     0xc4019c: ldurb           w16, [x1, #-1]
    //     0xc401a0: ldurb           w17, [x0, #-1]
    //     0xc401a4: and             x16, x17, x16, lsr #2
    //     0xc401a8: tst             x16, HEAP, lsr #32
    //     0xc401ac: b.eq            #0xc401b4
    //     0xc401b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc401b4: ldur            x0, [fp, #-0x10]
    // 0xc401b8: r16 = ")"
    //     0xc401b8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc401bc: StoreField: r0->field_23 = r16
    //     0xc401bc: stur            w16, [x0, #0x23]
    // 0xc401c0: str             x0, [SP]
    // 0xc401c4: r0 = _interpolate()
    //     0xc401c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc401c8: LeaveFrame
    //     0xc401c8: mov             SP, fp
    //     0xc401cc: ldp             fp, lr, [SP], #0x10
    // 0xc401d0: ret
    //     0xc401d0: ret             
    // 0xc401d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc401d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc401d8: b               #0xc40110
  }
}

// class id: 490, size: 0x14, field offset: 0x8
class _Highlight extends Object {

  _ _Highlight(/* No info */) {
    // ** addr: 0xc19a34, size: 0x80
    // 0xc19a34: EnterFrame
    //     0xc19a34: stp             fp, lr, [SP, #-0x10]!
    //     0xc19a38: mov             fp, SP
    // 0xc19a3c: AllocStack(0x8)
    //     0xc19a3c: sub             SP, SP, #8
    // 0xc19a40: SetupParameters(_Highlight this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xc19a40: mov             x0, x1
    //     0xc19a44: stur            x1, [fp, #-8]
    //     0xc19a48: mov             x1, x2
    // 0xc19a4c: CheckStackOverflow
    //     0xc19a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc19a50: cmp             SP, x16
    //     0xc19a54: b.ls            #0xc19aac
    // 0xc19a58: r0 = _normalizeContext()
    //     0xc19a58: bl              #0xc1aecc  ; [package:source_span/src/highlighter.dart] _Highlight::_normalizeContext
    // 0xc19a5c: mov             x1, x0
    // 0xc19a60: r0 = _normalizeNewlines()
    //     0xc19a60: bl              #0xc1abfc  ; [package:source_span/src/highlighter.dart] _Highlight::_normalizeNewlines
    // 0xc19a64: mov             x1, x0
    // 0xc19a68: r0 = _normalizeTrailingNewline()
    //     0xc19a68: bl              #0xc1a51c  ; [package:source_span/src/highlighter.dart] _Highlight::_normalizeTrailingNewline
    // 0xc19a6c: mov             x1, x0
    // 0xc19a70: r0 = _normalizeEndOfLine()
    //     0xc19a70: bl              #0xc19ab4  ; [package:source_span/src/highlighter.dart] _Highlight::_normalizeEndOfLine
    // 0xc19a74: ldur            x1, [fp, #-8]
    // 0xc19a78: StoreField: r1->field_7 = r0
    //     0xc19a78: stur            w0, [x1, #7]
    //     0xc19a7c: ldurb           w16, [x1, #-1]
    //     0xc19a80: ldurb           w17, [x0, #-1]
    //     0xc19a84: and             x16, x17, x16, lsr #2
    //     0xc19a88: tst             x16, HEAP, lsr #32
    //     0xc19a8c: b.eq            #0xc19a94
    //     0xc19a90: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc19a94: r2 = true
    //     0xc19a94: add             x2, NULL, #0x20  ; true
    // 0xc19a98: StoreField: r1->field_b = r2
    //     0xc19a98: stur            w2, [x1, #0xb]
    // 0xc19a9c: r0 = Null
    //     0xc19a9c: mov             x0, NULL
    // 0xc19aa0: LeaveFrame
    //     0xc19aa0: mov             SP, fp
    //     0xc19aa4: ldp             fp, lr, [SP], #0x10
    // 0xc19aa8: ret
    //     0xc19aa8: ret             
    // 0xc19aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19aac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19ab0: b               #0xc19a58
  }
  static _ _normalizeEndOfLine(/* No info */) {
    // ** addr: 0xc19ab4, size: 0x390
    // 0xc19ab4: EnterFrame
    //     0xc19ab4: stp             fp, lr, [SP, #-0x10]!
    //     0xc19ab8: mov             fp, SP
    // 0xc19abc: AllocStack(0x58)
    //     0xc19abc: sub             SP, SP, #0x58
    // 0xc19ac0: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xc19ac0: mov             x2, x1
    //     0xc19ac4: stur            x1, [fp, #-8]
    // 0xc19ac8: CheckStackOverflow
    //     0xc19ac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc19acc: cmp             SP, x16
    //     0xc19ad0: b.ls            #0xc19e3c
    // 0xc19ad4: r0 = LoadClassIdInstr(r2)
    //     0xc19ad4: ldur            x0, [x2, #-1]
    //     0xc19ad8: ubfx            x0, x0, #0xc, #0x14
    // 0xc19adc: mov             x1, x2
    // 0xc19ae0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19ae0: sub             lr, x0, #1, lsl #12
    //     0xc19ae4: ldr             lr, [x21, lr, lsl #3]
    //     0xc19ae8: blr             lr
    // 0xc19aec: r1 = LoadClassIdInstr(r0)
    //     0xc19aec: ldur            x1, [x0, #-1]
    //     0xc19af0: ubfx            x1, x1, #0xc, #0x14
    // 0xc19af4: mov             x16, x0
    // 0xc19af8: mov             x0, x1
    // 0xc19afc: mov             x1, x16
    // 0xc19b00: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc19b00: sub             lr, x0, #0xfff
    //     0xc19b04: ldr             lr, [x21, lr, lsl #3]
    //     0xc19b08: blr             lr
    // 0xc19b0c: cbz             x0, #0xc19b20
    // 0xc19b10: ldur            x0, [fp, #-8]
    // 0xc19b14: LeaveFrame
    //     0xc19b14: mov             SP, fp
    //     0xc19b18: ldp             fp, lr, [SP], #0x10
    // 0xc19b1c: ret
    //     0xc19b1c: ret             
    // 0xc19b20: ldur            x2, [fp, #-8]
    // 0xc19b24: r0 = LoadClassIdInstr(r2)
    //     0xc19b24: ldur            x0, [x2, #-1]
    //     0xc19b28: ubfx            x0, x0, #0xc, #0x14
    // 0xc19b2c: mov             x1, x2
    // 0xc19b30: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19b30: sub             lr, x0, #1, lsl #12
    //     0xc19b34: ldr             lr, [x21, lr, lsl #3]
    //     0xc19b38: blr             lr
    // 0xc19b3c: r1 = LoadClassIdInstr(r0)
    //     0xc19b3c: ldur            x1, [x0, #-1]
    //     0xc19b40: ubfx            x1, x1, #0xc, #0x14
    // 0xc19b44: mov             x16, x0
    // 0xc19b48: mov             x0, x1
    // 0xc19b4c: mov             x1, x16
    // 0xc19b50: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19b50: sub             lr, x0, #1, lsl #12
    //     0xc19b54: ldr             lr, [x21, lr, lsl #3]
    //     0xc19b58: blr             lr
    // 0xc19b5c: mov             x3, x0
    // 0xc19b60: ldur            x2, [fp, #-8]
    // 0xc19b64: stur            x3, [fp, #-0x10]
    // 0xc19b68: r0 = LoadClassIdInstr(r2)
    //     0xc19b68: ldur            x0, [x2, #-1]
    //     0xc19b6c: ubfx            x0, x0, #0xc, #0x14
    // 0xc19b70: mov             x1, x2
    // 0xc19b74: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc19b74: sub             lr, x0, #0xfff
    //     0xc19b78: ldr             lr, [x21, lr, lsl #3]
    //     0xc19b7c: blr             lr
    // 0xc19b80: r1 = LoadClassIdInstr(r0)
    //     0xc19b80: ldur            x1, [x0, #-1]
    //     0xc19b84: ubfx            x1, x1, #0xc, #0x14
    // 0xc19b88: mov             x16, x0
    // 0xc19b8c: mov             x0, x1
    // 0xc19b90: mov             x1, x16
    // 0xc19b94: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19b94: sub             lr, x0, #1, lsl #12
    //     0xc19b98: ldr             lr, [x21, lr, lsl #3]
    //     0xc19b9c: blr             lr
    // 0xc19ba0: mov             x1, x0
    // 0xc19ba4: ldur            x0, [fp, #-0x10]
    // 0xc19ba8: cmp             x0, x1
    // 0xc19bac: b.ne            #0xc19bc0
    // 0xc19bb0: ldur            x0, [fp, #-8]
    // 0xc19bb4: LeaveFrame
    //     0xc19bb4: mov             SP, fp
    //     0xc19bb8: ldp             fp, lr, [SP], #0x10
    // 0xc19bbc: ret
    //     0xc19bbc: ret             
    // 0xc19bc0: ldur            x2, [fp, #-8]
    // 0xc19bc4: r0 = LoadClassIdInstr(r2)
    //     0xc19bc4: ldur            x0, [x2, #-1]
    //     0xc19bc8: ubfx            x0, x0, #0xc, #0x14
    // 0xc19bcc: mov             x1, x2
    // 0xc19bd0: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc19bd0: sub             lr, x0, #0xff3
    //     0xc19bd4: ldr             lr, [x21, lr, lsl #3]
    //     0xc19bd8: blr             lr
    // 0xc19bdc: mov             x3, x0
    // 0xc19be0: ldur            x2, [fp, #-8]
    // 0xc19be4: stur            x3, [fp, #-0x18]
    // 0xc19be8: r0 = LoadClassIdInstr(r2)
    //     0xc19be8: ldur            x0, [x2, #-1]
    //     0xc19bec: ubfx            x0, x0, #0xc, #0x14
    // 0xc19bf0: mov             x1, x2
    // 0xc19bf4: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc19bf4: sub             lr, x0, #0xff3
    //     0xc19bf8: ldr             lr, [x21, lr, lsl #3]
    //     0xc19bfc: blr             lr
    // 0xc19c00: LoadField: r1 = r0->field_7
    //     0xc19c00: ldur            w1, [x0, #7]
    // 0xc19c04: r0 = LoadInt32Instr(r1)
    //     0xc19c04: sbfx            x0, x1, #1, #0x1f
    // 0xc19c08: sub             x1, x0, #1
    // 0xc19c0c: lsl             x0, x1, #1
    // 0xc19c10: str             x0, [SP]
    // 0xc19c14: ldur            x1, [fp, #-0x18]
    // 0xc19c18: r2 = 0
    //     0xc19c18: movz            x2, #0
    // 0xc19c1c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc19c1c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc19c20: r0 = substring()
    //     0xc19c20: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc19c24: mov             x3, x0
    // 0xc19c28: ldur            x2, [fp, #-8]
    // 0xc19c2c: stur            x3, [fp, #-0x18]
    // 0xc19c30: r0 = LoadClassIdInstr(r2)
    //     0xc19c30: ldur            x0, [x2, #-1]
    //     0xc19c34: ubfx            x0, x0, #0xc, #0x14
    // 0xc19c38: mov             x1, x2
    // 0xc19c3c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc19c3c: sub             lr, x0, #0xfff
    //     0xc19c40: ldr             lr, [x21, lr, lsl #3]
    //     0xc19c44: blr             lr
    // 0xc19c48: mov             x3, x0
    // 0xc19c4c: ldur            x2, [fp, #-8]
    // 0xc19c50: stur            x3, [fp, #-0x20]
    // 0xc19c54: r0 = LoadClassIdInstr(r2)
    //     0xc19c54: ldur            x0, [x2, #-1]
    //     0xc19c58: ubfx            x0, x0, #0xc, #0x14
    // 0xc19c5c: mov             x1, x2
    // 0xc19c60: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19c60: sub             lr, x0, #1, lsl #12
    //     0xc19c64: ldr             lr, [x21, lr, lsl #3]
    //     0xc19c68: blr             lr
    // 0xc19c6c: r1 = LoadClassIdInstr(r0)
    //     0xc19c6c: ldur            x1, [x0, #-1]
    //     0xc19c70: ubfx            x1, x1, #0xc, #0x14
    // 0xc19c74: mov             x16, x0
    // 0xc19c78: mov             x0, x1
    // 0xc19c7c: mov             x1, x16
    // 0xc19c80: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc19c80: sub             lr, x0, #0xffc
    //     0xc19c84: ldr             lr, [x21, lr, lsl #3]
    //     0xc19c88: blr             lr
    // 0xc19c8c: sub             x2, x0, #1
    // 0xc19c90: ldur            x3, [fp, #-8]
    // 0xc19c94: stur            x2, [fp, #-0x10]
    // 0xc19c98: r0 = LoadClassIdInstr(r3)
    //     0xc19c98: ldur            x0, [x3, #-1]
    //     0xc19c9c: ubfx            x0, x0, #0xc, #0x14
    // 0xc19ca0: mov             x1, x3
    // 0xc19ca4: r0 = GDT[cid_x0 + -0xff0]()
    //     0xc19ca4: sub             lr, x0, #0xff0
    //     0xc19ca8: ldr             lr, [x21, lr, lsl #3]
    //     0xc19cac: blr             lr
    // 0xc19cb0: ldur            x2, [fp, #-8]
    // 0xc19cb4: r0 = LoadClassIdInstr(r2)
    //     0xc19cb4: ldur            x0, [x2, #-1]
    //     0xc19cb8: ubfx            x0, x0, #0xc, #0x14
    // 0xc19cbc: mov             x1, x2
    // 0xc19cc0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19cc0: sub             lr, x0, #1, lsl #12
    //     0xc19cc4: ldr             lr, [x21, lr, lsl #3]
    //     0xc19cc8: blr             lr
    // 0xc19ccc: r1 = LoadClassIdInstr(r0)
    //     0xc19ccc: ldur            x1, [x0, #-1]
    //     0xc19cd0: ubfx            x1, x1, #0xc, #0x14
    // 0xc19cd4: mov             x16, x0
    // 0xc19cd8: mov             x0, x1
    // 0xc19cdc: mov             x1, x16
    // 0xc19ce0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19ce0: sub             lr, x0, #1, lsl #12
    //     0xc19ce4: ldr             lr, [x21, lr, lsl #3]
    //     0xc19ce8: blr             lr
    // 0xc19cec: sub             x5, x0, #1
    // 0xc19cf0: ldur            x0, [fp, #-0x18]
    // 0xc19cf4: stur            x5, [fp, #-0x30]
    // 0xc19cf8: LoadField: r3 = r0->field_7
    //     0xc19cf8: ldur            w3, [x0, #7]
    // 0xc19cfc: mov             x1, x0
    // 0xc19d00: stur            x3, [fp, #-0x28]
    // 0xc19d04: r2 = "\n"
    //     0xc19d04: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc19d08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc19d08: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc19d0c: r0 = lastIndexOf()
    //     0xc19d0c: bl              #0x642150  ; [dart:core] _StringBase::lastIndexOf
    // 0xc19d10: mov             x1, x0
    // 0xc19d14: ldur            x0, [fp, #-0x28]
    // 0xc19d18: r2 = LoadInt32Instr(r0)
    //     0xc19d18: sbfx            x2, x0, #1, #0x1f
    // 0xc19d1c: sub             x0, x2, x1
    // 0xc19d20: sub             x3, x0, #1
    // 0xc19d24: stur            x3, [fp, #-0x38]
    // 0xc19d28: r0 = SourceLocation()
    //     0xc19d28: bl              #0xc1a510  ; AllocateSourceLocationStub -> SourceLocation (size=0x24)
    // 0xc19d2c: mov             x1, x0
    // 0xc19d30: ldur            x2, [fp, #-0x10]
    // 0xc19d34: ldur            x3, [fp, #-0x38]
    // 0xc19d38: ldur            x5, [fp, #-0x30]
    // 0xc19d3c: stur            x0, [fp, #-0x28]
    // 0xc19d40: r0 = SourceLocation()
    //     0xc19d40: bl              #0xc1a360  ; [package:source_span/src/location.dart] SourceLocation::SourceLocation
    // 0xc19d44: ldur            x2, [fp, #-8]
    // 0xc19d48: r0 = LoadClassIdInstr(r2)
    //     0xc19d48: ldur            x0, [x2, #-1]
    //     0xc19d4c: ubfx            x0, x0, #0xc, #0x14
    // 0xc19d50: mov             x1, x2
    // 0xc19d54: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc19d54: sub             lr, x0, #0xff4
    //     0xc19d58: ldr             lr, [x21, lr, lsl #3]
    //     0xc19d5c: blr             lr
    // 0xc19d60: LoadField: r1 = r0->field_7
    //     0xc19d60: ldur            w1, [x0, #7]
    // 0xc19d64: r2 = LoadInt32Instr(r1)
    //     0xc19d64: sbfx            x2, x1, #1, #0x1f
    // 0xc19d68: sub             x1, x2, #1
    // 0xc19d6c: lsl             x2, x1, #1
    // 0xc19d70: stp             x2, x0, [SP, #8]
    // 0xc19d74: r16 = "\n"
    //     0xc19d74: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc19d78: str             x16, [SP]
    // 0xc19d7c: r0 = _substringMatches()
    //     0xc19d7c: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0xc19d80: tbnz            w0, #4, #0xc19dec
    // 0xc19d84: ldur            x2, [fp, #-8]
    // 0xc19d88: r0 = LoadClassIdInstr(r2)
    //     0xc19d88: ldur            x0, [x2, #-1]
    //     0xc19d8c: ubfx            x0, x0, #0xc, #0x14
    // 0xc19d90: mov             x1, x2
    // 0xc19d94: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc19d94: sub             lr, x0, #0xff4
    //     0xc19d98: ldr             lr, [x21, lr, lsl #3]
    //     0xc19d9c: blr             lr
    // 0xc19da0: mov             x2, x0
    // 0xc19da4: ldur            x1, [fp, #-8]
    // 0xc19da8: stur            x2, [fp, #-0x40]
    // 0xc19dac: r0 = LoadClassIdInstr(r1)
    //     0xc19dac: ldur            x0, [x1, #-1]
    //     0xc19db0: ubfx            x0, x0, #0xc, #0x14
    // 0xc19db4: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc19db4: sub             lr, x0, #0xff4
    //     0xc19db8: ldr             lr, [x21, lr, lsl #3]
    //     0xc19dbc: blr             lr
    // 0xc19dc0: LoadField: r1 = r0->field_7
    //     0xc19dc0: ldur            w1, [x0, #7]
    // 0xc19dc4: r0 = LoadInt32Instr(r1)
    //     0xc19dc4: sbfx            x0, x1, #1, #0x1f
    // 0xc19dc8: sub             x1, x0, #1
    // 0xc19dcc: lsl             x0, x1, #1
    // 0xc19dd0: str             x0, [SP]
    // 0xc19dd4: ldur            x1, [fp, #-0x40]
    // 0xc19dd8: r2 = 0
    //     0xc19dd8: movz            x2, #0
    // 0xc19ddc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc19ddc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc19de0: r0 = substring()
    //     0xc19de0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc19de4: mov             x6, x0
    // 0xc19de8: b               #0xc19e08
    // 0xc19dec: ldur            x1, [fp, #-8]
    // 0xc19df0: r0 = LoadClassIdInstr(r1)
    //     0xc19df0: ldur            x0, [x1, #-1]
    //     0xc19df4: ubfx            x0, x0, #0xc, #0x14
    // 0xc19df8: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc19df8: sub             lr, x0, #0xff4
    //     0xc19dfc: ldr             lr, [x21, lr, lsl #3]
    //     0xc19e00: blr             lr
    // 0xc19e04: mov             x6, x0
    // 0xc19e08: stur            x6, [fp, #-8]
    // 0xc19e0c: r0 = SourceSpanWithContext()
    //     0xc19e0c: bl              #0xc1a354  ; AllocateSourceSpanWithContextStub -> SourceSpanWithContext (size=0x18)
    // 0xc19e10: mov             x1, x0
    // 0xc19e14: ldur            x2, [fp, #-0x20]
    // 0xc19e18: ldur            x3, [fp, #-0x28]
    // 0xc19e1c: ldur            x5, [fp, #-0x18]
    // 0xc19e20: ldur            x6, [fp, #-8]
    // 0xc19e24: stur            x0, [fp, #-8]
    // 0xc19e28: r0 = SourceSpanWithContext()
    //     0xc19e28: bl              #0xc19e44  ; [package:source_span/src/span_with_context.dart] SourceSpanWithContext::SourceSpanWithContext
    // 0xc19e2c: ldur            x0, [fp, #-8]
    // 0xc19e30: LeaveFrame
    //     0xc19e30: mov             SP, fp
    //     0xc19e34: ldp             fp, lr, [SP], #0x10
    // 0xc19e38: ret
    //     0xc19e38: ret             
    // 0xc19e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19e3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19e40: b               #0xc19ad4
  }
  static _ _normalizeTrailingNewline(/* No info */) {
    // ** addr: 0xc1a51c, size: 0x444
    // 0xc1a51c: EnterFrame
    //     0xc1a51c: stp             fp, lr, [SP, #-0x10]!
    //     0xc1a520: mov             fp, SP
    // 0xc1a524: AllocStack(0x68)
    //     0xc1a524: sub             SP, SP, #0x68
    // 0xc1a528: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xc1a528: mov             x2, x1
    //     0xc1a52c: stur            x1, [fp, #-8]
    // 0xc1a530: CheckStackOverflow
    //     0xc1a530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1a534: cmp             SP, x16
    //     0xc1a538: b.ls            #0xc1a958
    // 0xc1a53c: r0 = LoadClassIdInstr(r2)
    //     0xc1a53c: ldur            x0, [x2, #-1]
    //     0xc1a540: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a544: mov             x1, x2
    // 0xc1a548: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc1a548: sub             lr, x0, #0xff4
    //     0xc1a54c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a550: blr             lr
    // 0xc1a554: LoadField: r1 = r0->field_7
    //     0xc1a554: ldur            w1, [x0, #7]
    // 0xc1a558: r2 = LoadInt32Instr(r1)
    //     0xc1a558: sbfx            x2, x1, #1, #0x1f
    // 0xc1a55c: sub             x1, x2, #1
    // 0xc1a560: lsl             x2, x1, #1
    // 0xc1a564: stp             x2, x0, [SP, #8]
    // 0xc1a568: r16 = "\n"
    //     0xc1a568: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc1a56c: str             x16, [SP]
    // 0xc1a570: r0 = _substringMatches()
    //     0xc1a570: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0xc1a574: tbz             w0, #4, #0xc1a588
    // 0xc1a578: ldur            x0, [fp, #-8]
    // 0xc1a57c: LeaveFrame
    //     0xc1a57c: mov             SP, fp
    //     0xc1a580: ldp             fp, lr, [SP], #0x10
    // 0xc1a584: ret
    //     0xc1a584: ret             
    // 0xc1a588: ldur            x2, [fp, #-8]
    // 0xc1a58c: r0 = LoadClassIdInstr(r2)
    //     0xc1a58c: ldur            x0, [x2, #-1]
    //     0xc1a590: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a594: mov             x1, x2
    // 0xc1a598: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1a598: sub             lr, x0, #0xff3
    //     0xc1a59c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a5a0: blr             lr
    // 0xc1a5a4: LoadField: r1 = r0->field_7
    //     0xc1a5a4: ldur            w1, [x0, #7]
    // 0xc1a5a8: r2 = LoadInt32Instr(r1)
    //     0xc1a5a8: sbfx            x2, x1, #1, #0x1f
    // 0xc1a5ac: sub             x1, x2, #2
    // 0xc1a5b0: lsl             x2, x1, #1
    // 0xc1a5b4: stp             x2, x0, [SP, #8]
    // 0xc1a5b8: r16 = "\n\n"
    //     0xc1a5b8: ldr             x16, [PP, #0x3360]  ; [pp+0x3360] "\n\n"
    // 0xc1a5bc: str             x16, [SP]
    // 0xc1a5c0: r0 = _substringMatches()
    //     0xc1a5c0: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0xc1a5c4: tbnz            w0, #4, #0xc1a5d8
    // 0xc1a5c8: ldur            x0, [fp, #-8]
    // 0xc1a5cc: LeaveFrame
    //     0xc1a5cc: mov             SP, fp
    //     0xc1a5d0: ldp             fp, lr, [SP], #0x10
    // 0xc1a5d4: ret
    //     0xc1a5d4: ret             
    // 0xc1a5d8: ldur            x2, [fp, #-8]
    // 0xc1a5dc: r0 = LoadClassIdInstr(r2)
    //     0xc1a5dc: ldur            x0, [x2, #-1]
    //     0xc1a5e0: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a5e4: mov             x1, x2
    // 0xc1a5e8: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc1a5e8: sub             lr, x0, #0xff4
    //     0xc1a5ec: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a5f0: blr             lr
    // 0xc1a5f4: mov             x3, x0
    // 0xc1a5f8: ldur            x2, [fp, #-8]
    // 0xc1a5fc: stur            x3, [fp, #-0x10]
    // 0xc1a600: r0 = LoadClassIdInstr(r2)
    //     0xc1a600: ldur            x0, [x2, #-1]
    //     0xc1a604: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a608: mov             x1, x2
    // 0xc1a60c: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc1a60c: sub             lr, x0, #0xff4
    //     0xc1a610: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a614: blr             lr
    // 0xc1a618: LoadField: r1 = r0->field_7
    //     0xc1a618: ldur            w1, [x0, #7]
    // 0xc1a61c: r0 = LoadInt32Instr(r1)
    //     0xc1a61c: sbfx            x0, x1, #1, #0x1f
    // 0xc1a620: sub             x1, x0, #1
    // 0xc1a624: lsl             x0, x1, #1
    // 0xc1a628: str             x0, [SP]
    // 0xc1a62c: ldur            x1, [fp, #-0x10]
    // 0xc1a630: r2 = 0
    //     0xc1a630: movz            x2, #0
    // 0xc1a634: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc1a634: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc1a638: r0 = substring()
    //     0xc1a638: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc1a63c: mov             x3, x0
    // 0xc1a640: ldur            x2, [fp, #-8]
    // 0xc1a644: stur            x3, [fp, #-0x10]
    // 0xc1a648: r0 = LoadClassIdInstr(r2)
    //     0xc1a648: ldur            x0, [x2, #-1]
    //     0xc1a64c: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a650: mov             x1, x2
    // 0xc1a654: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1a654: sub             lr, x0, #0xff3
    //     0xc1a658: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a65c: blr             lr
    // 0xc1a660: mov             x3, x0
    // 0xc1a664: ldur            x2, [fp, #-8]
    // 0xc1a668: stur            x3, [fp, #-0x18]
    // 0xc1a66c: r0 = LoadClassIdInstr(r2)
    //     0xc1a66c: ldur            x0, [x2, #-1]
    //     0xc1a670: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a674: mov             x1, x2
    // 0xc1a678: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1a678: sub             lr, x0, #0xfff
    //     0xc1a67c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a680: blr             lr
    // 0xc1a684: mov             x3, x0
    // 0xc1a688: ldur            x2, [fp, #-8]
    // 0xc1a68c: stur            x3, [fp, #-0x20]
    // 0xc1a690: r0 = LoadClassIdInstr(r2)
    //     0xc1a690: ldur            x0, [x2, #-1]
    //     0xc1a694: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a698: mov             x1, x2
    // 0xc1a69c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1a69c: sub             lr, x0, #1, lsl #12
    //     0xc1a6a0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a6a4: blr             lr
    // 0xc1a6a8: mov             x3, x0
    // 0xc1a6ac: ldur            x2, [fp, #-8]
    // 0xc1a6b0: stur            x3, [fp, #-0x28]
    // 0xc1a6b4: r0 = LoadClassIdInstr(r2)
    //     0xc1a6b4: ldur            x0, [x2, #-1]
    //     0xc1a6b8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a6bc: mov             x1, x2
    // 0xc1a6c0: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1a6c0: sub             lr, x0, #0xff3
    //     0xc1a6c4: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a6c8: blr             lr
    // 0xc1a6cc: LoadField: r1 = r0->field_7
    //     0xc1a6cc: ldur            w1, [x0, #7]
    // 0xc1a6d0: r2 = LoadInt32Instr(r1)
    //     0xc1a6d0: sbfx            x2, x1, #1, #0x1f
    // 0xc1a6d4: sub             x1, x2, #1
    // 0xc1a6d8: lsl             x2, x1, #1
    // 0xc1a6dc: stp             x2, x0, [SP, #8]
    // 0xc1a6e0: r16 = "\n"
    //     0xc1a6e0: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc1a6e4: str             x16, [SP]
    // 0xc1a6e8: r0 = _substringMatches()
    //     0xc1a6e8: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0xc1a6ec: tbnz            w0, #4, #0xc1a910
    // 0xc1a6f0: ldur            x1, [fp, #-8]
    // 0xc1a6f4: r0 = _isTextAtEndOfContext()
    //     0xc1a6f4: bl              #0xc1aa6c  ; [package:source_span/src/highlighter.dart] _Highlight::_isTextAtEndOfContext
    // 0xc1a6f8: tbnz            w0, #4, #0xc1a910
    // 0xc1a6fc: ldur            x2, [fp, #-8]
    // 0xc1a700: r0 = LoadClassIdInstr(r2)
    //     0xc1a700: ldur            x0, [x2, #-1]
    //     0xc1a704: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a708: mov             x1, x2
    // 0xc1a70c: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1a70c: sub             lr, x0, #0xff3
    //     0xc1a710: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a714: blr             lr
    // 0xc1a718: mov             x3, x0
    // 0xc1a71c: ldur            x2, [fp, #-8]
    // 0xc1a720: stur            x3, [fp, #-0x30]
    // 0xc1a724: r0 = LoadClassIdInstr(r2)
    //     0xc1a724: ldur            x0, [x2, #-1]
    //     0xc1a728: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a72c: mov             x1, x2
    // 0xc1a730: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1a730: sub             lr, x0, #0xff3
    //     0xc1a734: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a738: blr             lr
    // 0xc1a73c: LoadField: r1 = r0->field_7
    //     0xc1a73c: ldur            w1, [x0, #7]
    // 0xc1a740: r0 = LoadInt32Instr(r1)
    //     0xc1a740: sbfx            x0, x1, #1, #0x1f
    // 0xc1a744: sub             x1, x0, #1
    // 0xc1a748: lsl             x0, x1, #1
    // 0xc1a74c: str             x0, [SP]
    // 0xc1a750: ldur            x1, [fp, #-0x30]
    // 0xc1a754: r2 = 0
    //     0xc1a754: movz            x2, #0
    // 0xc1a758: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc1a758: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc1a75c: r0 = substring()
    //     0xc1a75c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc1a760: mov             x2, x0
    // 0xc1a764: stur            x2, [fp, #-0x30]
    // 0xc1a768: LoadField: r0 = r2->field_7
    //     0xc1a768: ldur            w0, [x2, #7]
    // 0xc1a76c: cbnz            w0, #0xc1a77c
    // 0xc1a770: ldur            x1, [fp, #-0x20]
    // 0xc1a774: ldur            x0, [fp, #-0x20]
    // 0xc1a778: b               #0xc1a900
    // 0xc1a77c: ldur            x3, [fp, #-8]
    // 0xc1a780: r0 = LoadClassIdInstr(r3)
    //     0xc1a780: ldur            x0, [x3, #-1]
    //     0xc1a784: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a788: mov             x1, x3
    // 0xc1a78c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1a78c: sub             lr, x0, #1, lsl #12
    //     0xc1a790: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a794: blr             lr
    // 0xc1a798: r1 = LoadClassIdInstr(r0)
    //     0xc1a798: ldur            x1, [x0, #-1]
    //     0xc1a79c: ubfx            x1, x1, #0xc, #0x14
    // 0xc1a7a0: mov             x16, x0
    // 0xc1a7a4: mov             x0, x1
    // 0xc1a7a8: mov             x1, x16
    // 0xc1a7ac: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc1a7ac: sub             lr, x0, #0xffc
    //     0xc1a7b0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a7b4: blr             lr
    // 0xc1a7b8: sub             x2, x0, #1
    // 0xc1a7bc: ldur            x3, [fp, #-8]
    // 0xc1a7c0: stur            x2, [fp, #-0x38]
    // 0xc1a7c4: r0 = LoadClassIdInstr(r3)
    //     0xc1a7c4: ldur            x0, [x3, #-1]
    //     0xc1a7c8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a7cc: mov             x1, x3
    // 0xc1a7d0: r0 = GDT[cid_x0 + -0xff0]()
    //     0xc1a7d0: sub             lr, x0, #0xff0
    //     0xc1a7d4: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a7d8: blr             lr
    // 0xc1a7dc: ldur            x2, [fp, #-8]
    // 0xc1a7e0: r0 = LoadClassIdInstr(r2)
    //     0xc1a7e0: ldur            x0, [x2, #-1]
    //     0xc1a7e4: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a7e8: mov             x1, x2
    // 0xc1a7ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1a7ec: sub             lr, x0, #1, lsl #12
    //     0xc1a7f0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a7f4: blr             lr
    // 0xc1a7f8: r1 = LoadClassIdInstr(r0)
    //     0xc1a7f8: ldur            x1, [x0, #-1]
    //     0xc1a7fc: ubfx            x1, x1, #0xc, #0x14
    // 0xc1a800: mov             x16, x0
    // 0xc1a804: mov             x0, x1
    // 0xc1a808: mov             x1, x16
    // 0xc1a80c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1a80c: sub             lr, x0, #1, lsl #12
    //     0xc1a810: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a814: blr             lr
    // 0xc1a818: sub             x5, x0, #1
    // 0xc1a81c: ldur            x1, [fp, #-0x10]
    // 0xc1a820: stur            x5, [fp, #-0x40]
    // 0xc1a824: r0 = _lastLineLength()
    //     0xc1a824: bl              #0xc1a960  ; [package:source_span/src/highlighter.dart] _Highlight::_lastLineLength
    // 0xc1a828: stur            x0, [fp, #-0x48]
    // 0xc1a82c: r0 = SourceLocation()
    //     0xc1a82c: bl              #0xc1a510  ; AllocateSourceLocationStub -> SourceLocation (size=0x24)
    // 0xc1a830: mov             x1, x0
    // 0xc1a834: ldur            x2, [fp, #-0x38]
    // 0xc1a838: ldur            x3, [fp, #-0x48]
    // 0xc1a83c: ldur            x5, [fp, #-0x40]
    // 0xc1a840: stur            x0, [fp, #-0x50]
    // 0xc1a844: r0 = SourceLocation()
    //     0xc1a844: bl              #0xc1a360  ; [package:source_span/src/location.dart] SourceLocation::SourceLocation
    // 0xc1a848: ldur            x2, [fp, #-8]
    // 0xc1a84c: r0 = LoadClassIdInstr(r2)
    //     0xc1a84c: ldur            x0, [x2, #-1]
    //     0xc1a850: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a854: mov             x1, x2
    // 0xc1a858: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1a858: sub             lr, x0, #0xfff
    //     0xc1a85c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a860: blr             lr
    // 0xc1a864: r1 = LoadClassIdInstr(r0)
    //     0xc1a864: ldur            x1, [x0, #-1]
    //     0xc1a868: ubfx            x1, x1, #0xc, #0x14
    // 0xc1a86c: mov             x16, x0
    // 0xc1a870: mov             x0, x1
    // 0xc1a874: mov             x1, x16
    // 0xc1a878: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc1a878: sub             lr, x0, #0xffc
    //     0xc1a87c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a880: blr             lr
    // 0xc1a884: mov             x3, x0
    // 0xc1a888: ldur            x2, [fp, #-8]
    // 0xc1a88c: stur            x3, [fp, #-0x38]
    // 0xc1a890: r0 = LoadClassIdInstr(r2)
    //     0xc1a890: ldur            x0, [x2, #-1]
    //     0xc1a894: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a898: mov             x1, x2
    // 0xc1a89c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1a89c: sub             lr, x0, #1, lsl #12
    //     0xc1a8a0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a8a4: blr             lr
    // 0xc1a8a8: r1 = LoadClassIdInstr(r0)
    //     0xc1a8a8: ldur            x1, [x0, #-1]
    //     0xc1a8ac: ubfx            x1, x1, #0xc, #0x14
    // 0xc1a8b0: mov             x16, x0
    // 0xc1a8b4: mov             x0, x1
    // 0xc1a8b8: mov             x1, x16
    // 0xc1a8bc: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc1a8bc: sub             lr, x0, #0xffc
    //     0xc1a8c0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a8c4: blr             lr
    // 0xc1a8c8: mov             x1, x0
    // 0xc1a8cc: ldur            x0, [fp, #-0x38]
    // 0xc1a8d0: cmp             x0, x1
    // 0xc1a8d4: b.ne            #0xc1a8e0
    // 0xc1a8d8: ldur            x0, [fp, #-0x50]
    // 0xc1a8dc: b               #0xc1a8f8
    // 0xc1a8e0: ldur            x1, [fp, #-8]
    // 0xc1a8e4: r0 = LoadClassIdInstr(r1)
    //     0xc1a8e4: ldur            x0, [x1, #-1]
    //     0xc1a8e8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a8ec: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1a8ec: sub             lr, x0, #0xfff
    //     0xc1a8f0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a8f4: blr             lr
    // 0xc1a8f8: mov             x1, x0
    // 0xc1a8fc: ldur            x0, [fp, #-0x50]
    // 0xc1a900: ldur            x5, [fp, #-0x30]
    // 0xc1a904: mov             x2, x1
    // 0xc1a908: mov             x3, x0
    // 0xc1a90c: b               #0xc1a91c
    // 0xc1a910: ldur            x5, [fp, #-0x18]
    // 0xc1a914: ldur            x2, [fp, #-0x20]
    // 0xc1a918: ldur            x3, [fp, #-0x28]
    // 0xc1a91c: stur            x5, [fp, #-8]
    // 0xc1a920: stur            x2, [fp, #-0x18]
    // 0xc1a924: stur            x3, [fp, #-0x20]
    // 0xc1a928: r0 = SourceSpanWithContext()
    //     0xc1a928: bl              #0xc1a354  ; AllocateSourceSpanWithContextStub -> SourceSpanWithContext (size=0x18)
    // 0xc1a92c: mov             x1, x0
    // 0xc1a930: ldur            x2, [fp, #-0x18]
    // 0xc1a934: ldur            x3, [fp, #-0x20]
    // 0xc1a938: ldur            x5, [fp, #-8]
    // 0xc1a93c: ldur            x6, [fp, #-0x10]
    // 0xc1a940: stur            x0, [fp, #-8]
    // 0xc1a944: r0 = SourceSpanWithContext()
    //     0xc1a944: bl              #0xc19e44  ; [package:source_span/src/span_with_context.dart] SourceSpanWithContext::SourceSpanWithContext
    // 0xc1a948: ldur            x0, [fp, #-8]
    // 0xc1a94c: LeaveFrame
    //     0xc1a94c: mov             SP, fp
    //     0xc1a950: ldp             fp, lr, [SP], #0x10
    // 0xc1a954: ret
    //     0xc1a954: ret             
    // 0xc1a958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1a958: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1a95c: b               #0xc1a53c
  }
  static _ _lastLineLength(/* No info */) {
    // ** addr: 0xc1a960, size: 0x10c
    // 0xc1a960: EnterFrame
    //     0xc1a960: stp             fp, lr, [SP, #-0x10]!
    //     0xc1a964: mov             fp, SP
    // 0xc1a968: AllocStack(0x10)
    //     0xc1a968: sub             SP, SP, #0x10
    // 0xc1a96c: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0xc1a96c: mov             x2, x1
    // 0xc1a970: CheckStackOverflow
    //     0xc1a970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1a974: cmp             SP, x16
    //     0xc1a978: b.ls            #0xc1aa60
    // 0xc1a97c: LoadField: r0 = r2->field_7
    //     0xc1a97c: ldur            w0, [x2, #7]
    // 0xc1a980: cbnz            w0, #0xc1a994
    // 0xc1a984: r0 = 0
    //     0xc1a984: movz            x0, #0
    // 0xc1a988: LeaveFrame
    //     0xc1a988: mov             SP, fp
    //     0xc1a98c: ldp             fp, lr, [SP], #0x10
    // 0xc1a990: ret
    //     0xc1a990: ret             
    // 0xc1a994: r3 = LoadInt32Instr(r0)
    //     0xc1a994: sbfx            x3, x0, #1, #0x1f
    // 0xc1a998: stur            x3, [fp, #-8]
    // 0xc1a99c: sub             x4, x3, #1
    // 0xc1a9a0: mov             x0, x3
    // 0xc1a9a4: mov             x1, x4
    // 0xc1a9a8: cmp             x1, x0
    // 0xc1a9ac: b.hs            #0xc1aa68
    // 0xc1a9b0: r0 = LoadClassIdInstr(r2)
    //     0xc1a9b0: ldur            x0, [x2, #-1]
    //     0xc1a9b4: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a9b8: lsl             x0, x0, #1
    // 0xc1a9bc: cmp             w0, #0xbc
    // 0xc1a9c0: b.ne            #0xc1a9dc
    // 0xc1a9c4: ArrayLoad: r0 = r2[r4]  ; TypedUnsigned_1
    //     0xc1a9c4: add             x16, x2, x4
    //     0xc1a9c8: ldrb            w0, [x16, #0xf]
    // 0xc1a9cc: cmp             x0, #0xa
    // 0xc1a9d0: b.eq            #0xc1a9ec
    // 0xc1a9d4: mov             x0, x3
    // 0xc1a9d8: b               #0xc1aa38
    // 0xc1a9dc: add             x16, x2, x4, lsl #1
    // 0xc1a9e0: ldurh           w0, [x16, #0xf]
    // 0xc1a9e4: cmp             x0, #0xa
    // 0xc1a9e8: b.ne            #0xc1aa34
    // 0xc1a9ec: cmp             x3, #1
    // 0xc1a9f0: b.ne            #0xc1a9fc
    // 0xc1a9f4: r0 = 0
    //     0xc1a9f4: movz            x0, #0
    // 0xc1a9f8: b               #0xc1aa28
    // 0xc1a9fc: sub             x0, x3, #2
    // 0xc1aa00: lsl             x1, x0, #1
    // 0xc1aa04: str             x1, [SP]
    // 0xc1aa08: mov             x1, x2
    // 0xc1aa0c: r2 = "\n"
    //     0xc1aa0c: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc1aa10: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc1aa10: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc1aa14: r0 = lastIndexOf()
    //     0xc1aa14: bl              #0x642150  ; [dart:core] _StringBase::lastIndexOf
    // 0xc1aa18: mov             x1, x0
    // 0xc1aa1c: ldur            x0, [fp, #-8]
    // 0xc1aa20: sub             x2, x0, x1
    // 0xc1aa24: sub             x0, x2, #1
    // 0xc1aa28: LeaveFrame
    //     0xc1aa28: mov             SP, fp
    //     0xc1aa2c: ldp             fp, lr, [SP], #0x10
    // 0xc1aa30: ret
    //     0xc1aa30: ret             
    // 0xc1aa34: mov             x0, x3
    // 0xc1aa38: mov             x1, x2
    // 0xc1aa3c: r2 = "\n"
    //     0xc1aa3c: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc1aa40: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc1aa40: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc1aa44: r0 = lastIndexOf()
    //     0xc1aa44: bl              #0x642150  ; [dart:core] _StringBase::lastIndexOf
    // 0xc1aa48: ldur            x1, [fp, #-8]
    // 0xc1aa4c: sub             x2, x1, x0
    // 0xc1aa50: sub             x0, x2, #1
    // 0xc1aa54: LeaveFrame
    //     0xc1aa54: mov             SP, fp
    //     0xc1aa58: ldp             fp, lr, [SP], #0x10
    // 0xc1aa5c: ret
    //     0xc1aa5c: ret             
    // 0xc1aa60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1aa60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1aa64: b               #0xc1a97c
    // 0xc1aa68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1aa68: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _isTextAtEndOfContext(/* No info */) {
    // ** addr: 0xc1aa6c, size: 0x190
    // 0xc1aa6c: EnterFrame
    //     0xc1aa6c: stp             fp, lr, [SP, #-0x10]!
    //     0xc1aa70: mov             fp, SP
    // 0xc1aa74: AllocStack(0x28)
    //     0xc1aa74: sub             SP, SP, #0x28
    // 0xc1aa78: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xc1aa78: mov             x2, x1
    //     0xc1aa7c: stur            x1, [fp, #-8]
    // 0xc1aa80: CheckStackOverflow
    //     0xc1aa80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1aa84: cmp             SP, x16
    //     0xc1aa88: b.ls            #0xc1abf0
    // 0xc1aa8c: r0 = LoadClassIdInstr(r2)
    //     0xc1aa8c: ldur            x0, [x2, #-1]
    //     0xc1aa90: ubfx            x0, x0, #0xc, #0x14
    // 0xc1aa94: mov             x1, x2
    // 0xc1aa98: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc1aa98: sub             lr, x0, #0xff4
    //     0xc1aa9c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1aaa0: blr             lr
    // 0xc1aaa4: mov             x3, x0
    // 0xc1aaa8: ldur            x2, [fp, #-8]
    // 0xc1aaac: stur            x3, [fp, #-0x10]
    // 0xc1aab0: r0 = LoadClassIdInstr(r2)
    //     0xc1aab0: ldur            x0, [x2, #-1]
    //     0xc1aab4: ubfx            x0, x0, #0xc, #0x14
    // 0xc1aab8: mov             x1, x2
    // 0xc1aabc: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1aabc: sub             lr, x0, #0xff3
    //     0xc1aac0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1aac4: blr             lr
    // 0xc1aac8: mov             x3, x0
    // 0xc1aacc: ldur            x2, [fp, #-8]
    // 0xc1aad0: stur            x3, [fp, #-0x18]
    // 0xc1aad4: r0 = LoadClassIdInstr(r2)
    //     0xc1aad4: ldur            x0, [x2, #-1]
    //     0xc1aad8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1aadc: mov             x1, x2
    // 0xc1aae0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1aae0: sub             lr, x0, #0xfff
    //     0xc1aae4: ldr             lr, [x21, lr, lsl #3]
    //     0xc1aae8: blr             lr
    // 0xc1aaec: r1 = LoadClassIdInstr(r0)
    //     0xc1aaec: ldur            x1, [x0, #-1]
    //     0xc1aaf0: ubfx            x1, x1, #0xc, #0x14
    // 0xc1aaf4: mov             x16, x0
    // 0xc1aaf8: mov             x0, x1
    // 0xc1aafc: mov             x1, x16
    // 0xc1ab00: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1ab00: sub             lr, x0, #0xfff
    //     0xc1ab04: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ab08: blr             lr
    // 0xc1ab0c: ldur            x1, [fp, #-0x10]
    // 0xc1ab10: ldur            x2, [fp, #-0x18]
    // 0xc1ab14: mov             x3, x0
    // 0xc1ab18: r0 = findLineStart()
    //     0xc1ab18: bl              #0xc196ac  ; [package:source_span/src/utils.dart] ::findLineStart
    // 0xc1ab1c: mov             x2, x0
    // 0xc1ab20: stur            x2, [fp, #-0x10]
    // 0xc1ab24: cmp             w2, NULL
    // 0xc1ab28: b.eq            #0xc1abf8
    // 0xc1ab2c: ldur            x3, [fp, #-8]
    // 0xc1ab30: r0 = LoadClassIdInstr(r3)
    //     0xc1ab30: ldur            x0, [x3, #-1]
    //     0xc1ab34: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ab38: mov             x1, x3
    // 0xc1ab3c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1ab3c: sub             lr, x0, #0xfff
    //     0xc1ab40: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ab44: blr             lr
    // 0xc1ab48: r1 = LoadClassIdInstr(r0)
    //     0xc1ab48: ldur            x1, [x0, #-1]
    //     0xc1ab4c: ubfx            x1, x1, #0xc, #0x14
    // 0xc1ab50: mov             x16, x0
    // 0xc1ab54: mov             x0, x1
    // 0xc1ab58: mov             x1, x16
    // 0xc1ab5c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1ab5c: sub             lr, x0, #0xfff
    //     0xc1ab60: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ab64: blr             lr
    // 0xc1ab68: mov             x1, x0
    // 0xc1ab6c: ldur            x0, [fp, #-0x10]
    // 0xc1ab70: r2 = LoadInt32Instr(r0)
    //     0xc1ab70: sbfx            x2, x0, #1, #0x1f
    //     0xc1ab74: tbz             w0, #0, #0xc1ab7c
    //     0xc1ab78: ldur            x2, [x0, #7]
    // 0xc1ab7c: add             x3, x2, x1
    // 0xc1ab80: ldur            x2, [fp, #-8]
    // 0xc1ab84: stur            x3, [fp, #-0x20]
    // 0xc1ab88: r0 = LoadClassIdInstr(r2)
    //     0xc1ab88: ldur            x0, [x2, #-1]
    //     0xc1ab8c: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ab90: mov             x1, x2
    // 0xc1ab94: r0 = GDT[cid_x0 + -0xfea]()
    //     0xc1ab94: sub             lr, x0, #0xfea
    //     0xc1ab98: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ab9c: blr             lr
    // 0xc1aba0: mov             x1, x0
    // 0xc1aba4: ldur            x0, [fp, #-0x20]
    // 0xc1aba8: add             x2, x0, x1
    // 0xc1abac: ldur            x1, [fp, #-8]
    // 0xc1abb0: stur            x2, [fp, #-0x28]
    // 0xc1abb4: r0 = LoadClassIdInstr(r1)
    //     0xc1abb4: ldur            x0, [x1, #-1]
    //     0xc1abb8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1abbc: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc1abbc: sub             lr, x0, #0xff4
    //     0xc1abc0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1abc4: blr             lr
    // 0xc1abc8: LoadField: r1 = r0->field_7
    //     0xc1abc8: ldur            w1, [x0, #7]
    // 0xc1abcc: r2 = LoadInt32Instr(r1)
    //     0xc1abcc: sbfx            x2, x1, #1, #0x1f
    // 0xc1abd0: ldur            x1, [fp, #-0x28]
    // 0xc1abd4: cmp             x1, x2
    // 0xc1abd8: r16 = true
    //     0xc1abd8: add             x16, NULL, #0x20  ; true
    // 0xc1abdc: r17 = false
    //     0xc1abdc: add             x17, NULL, #0x30  ; false
    // 0xc1abe0: csel            x0, x16, x17, eq
    // 0xc1abe4: LeaveFrame
    //     0xc1abe4: mov             SP, fp
    //     0xc1abe8: ldp             fp, lr, [SP], #0x10
    // 0xc1abec: ret
    //     0xc1abec: ret             
    // 0xc1abf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1abf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1abf4: b               #0xc1aa8c
    // 0xc1abf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1abf8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _normalizeNewlines(/* No info */) {
    // ** addr: 0xc1abfc, size: 0x2d0
    // 0xc1abfc: EnterFrame
    //     0xc1abfc: stp             fp, lr, [SP, #-0x10]!
    //     0xc1ac00: mov             fp, SP
    // 0xc1ac04: AllocStack(0x38)
    //     0xc1ac04: sub             SP, SP, #0x38
    // 0xc1ac08: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xc1ac08: mov             x2, x1
    //     0xc1ac0c: stur            x1, [fp, #-8]
    // 0xc1ac10: CheckStackOverflow
    //     0xc1ac10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1ac14: cmp             SP, x16
    //     0xc1ac18: b.ls            #0xc1aebc
    // 0xc1ac1c: r0 = LoadClassIdInstr(r2)
    //     0xc1ac1c: ldur            x0, [x2, #-1]
    //     0xc1ac20: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ac24: mov             x1, x2
    // 0xc1ac28: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1ac28: sub             lr, x0, #0xff3
    //     0xc1ac2c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ac30: blr             lr
    // 0xc1ac34: mov             x3, x0
    // 0xc1ac38: stur            x3, [fp, #-0x10]
    // 0xc1ac3c: r0 = LoadClassIdInstr(r3)
    //     0xc1ac3c: ldur            x0, [x3, #-1]
    //     0xc1ac40: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ac44: mov             x1, x3
    // 0xc1ac48: r2 = "\r\n"
    //     0xc1ac48: add             x2, PP, #0x12, lsl #12  ; [pp+0x12260] "\r\n"
    //     0xc1ac4c: ldr             x2, [x2, #0x260]
    // 0xc1ac50: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc1ac50: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc1ac54: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc1ac54: sub             lr, x0, #0xffc
    //     0xc1ac58: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ac5c: blr             lr
    // 0xc1ac60: tbz             w0, #4, #0xc1ac74
    // 0xc1ac64: ldur            x0, [fp, #-8]
    // 0xc1ac68: LeaveFrame
    //     0xc1ac68: mov             SP, fp
    //     0xc1ac6c: ldp             fp, lr, [SP], #0x10
    // 0xc1ac70: ret
    //     0xc1ac70: ret             
    // 0xc1ac74: ldur            x3, [fp, #-8]
    // 0xc1ac78: ldur            x2, [fp, #-0x10]
    // 0xc1ac7c: r0 = LoadClassIdInstr(r3)
    //     0xc1ac7c: ldur            x0, [x3, #-1]
    //     0xc1ac80: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ac84: mov             x1, x3
    // 0xc1ac88: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1ac88: sub             lr, x0, #1, lsl #12
    //     0xc1ac8c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ac90: blr             lr
    // 0xc1ac94: r1 = LoadClassIdInstr(r0)
    //     0xc1ac94: ldur            x1, [x0, #-1]
    //     0xc1ac98: ubfx            x1, x1, #0xc, #0x14
    // 0xc1ac9c: mov             x16, x0
    // 0xc1aca0: mov             x0, x1
    // 0xc1aca4: mov             x1, x16
    // 0xc1aca8: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc1aca8: sub             lr, x0, #0xffc
    //     0xc1acac: ldr             lr, [x21, lr, lsl #3]
    //     0xc1acb0: blr             lr
    // 0xc1acb4: ldur            x2, [fp, #-0x10]
    // 0xc1acb8: LoadField: r1 = r2->field_7
    //     0xc1acb8: ldur            w1, [x2, #7]
    // 0xc1acbc: r3 = LoadInt32Instr(r1)
    //     0xc1acbc: sbfx            x3, x1, #1, #0x1f
    // 0xc1acc0: sub             x1, x3, #1
    // 0xc1acc4: r3 = LoadClassIdInstr(r2)
    //     0xc1acc4: ldur            x3, [x2, #-1]
    //     0xc1acc8: ubfx            x3, x3, #0xc, #0x14
    // 0xc1accc: lsl             x3, x3, #1
    // 0xc1acd0: mov             x4, x0
    // 0xc1acd4: r0 = 0
    //     0xc1acd4: movz            x0, #0
    // 0xc1acd8: stur            x4, [fp, #-0x18]
    // 0xc1acdc: CheckStackOverflow
    //     0xc1acdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1ace0: cmp             SP, x16
    //     0xc1ace4: b.ls            #0xc1aec4
    // 0xc1ace8: cmp             x0, x1
    // 0xc1acec: b.ge            #0xc1ad60
    // 0xc1acf0: cmp             w3, #0xbc
    // 0xc1acf4: b.ne            #0xc1ad0c
    // 0xc1acf8: ArrayLoad: r5 = r2[r0]  ; TypedUnsigned_1
    //     0xc1acf8: add             x16, x2, x0
    //     0xc1acfc: ldrb            w5, [x16, #0xf]
    // 0xc1ad00: cmp             x5, #0xd
    // 0xc1ad04: b.ne            #0xc1ad54
    // 0xc1ad08: b               #0xc1ad1c
    // 0xc1ad0c: add             x16, x2, x0, lsl #1
    // 0xc1ad10: ldurh           w5, [x16, #0xf]
    // 0xc1ad14: cmp             x5, #0xd
    // 0xc1ad18: b.ne            #0xc1ad54
    // 0xc1ad1c: add             x5, x0, #1
    // 0xc1ad20: cmp             w3, #0xbc
    // 0xc1ad24: b.ne            #0xc1ad3c
    // 0xc1ad28: ArrayLoad: r6 = r2[r5]  ; TypedUnsigned_1
    //     0xc1ad28: add             x16, x2, x5
    //     0xc1ad2c: ldrb            w6, [x16, #0xf]
    // 0xc1ad30: cmp             x6, #0xa
    // 0xc1ad34: b.ne            #0xc1ad54
    // 0xc1ad38: b               #0xc1ad4c
    // 0xc1ad3c: add             x16, x2, x5, lsl #1
    // 0xc1ad40: ldurh           w6, [x16, #0xf]
    // 0xc1ad44: cmp             x6, #0xa
    // 0xc1ad48: b.ne            #0xc1ad54
    // 0xc1ad4c: sub             x5, x4, #1
    // 0xc1ad50: mov             x4, x5
    // 0xc1ad54: add             x5, x0, #1
    // 0xc1ad58: mov             x0, x5
    // 0xc1ad5c: b               #0xc1acd8
    // 0xc1ad60: ldur            x3, [fp, #-8]
    // 0xc1ad64: r0 = LoadClassIdInstr(r3)
    //     0xc1ad64: ldur            x0, [x3, #-1]
    //     0xc1ad68: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ad6c: mov             x1, x3
    // 0xc1ad70: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1ad70: sub             lr, x0, #0xfff
    //     0xc1ad74: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ad78: blr             lr
    // 0xc1ad7c: mov             x3, x0
    // 0xc1ad80: ldur            x2, [fp, #-8]
    // 0xc1ad84: stur            x3, [fp, #-0x20]
    // 0xc1ad88: r0 = LoadClassIdInstr(r2)
    //     0xc1ad88: ldur            x0, [x2, #-1]
    //     0xc1ad8c: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ad90: mov             x1, x2
    // 0xc1ad94: r0 = GDT[cid_x0 + -0xff0]()
    //     0xc1ad94: sub             lr, x0, #0xff0
    //     0xc1ad98: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ad9c: blr             lr
    // 0xc1ada0: ldur            x2, [fp, #-8]
    // 0xc1ada4: r0 = LoadClassIdInstr(r2)
    //     0xc1ada4: ldur            x0, [x2, #-1]
    //     0xc1ada8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1adac: mov             x1, x2
    // 0xc1adb0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1adb0: sub             lr, x0, #1, lsl #12
    //     0xc1adb4: ldr             lr, [x21, lr, lsl #3]
    //     0xc1adb8: blr             lr
    // 0xc1adbc: r1 = LoadClassIdInstr(r0)
    //     0xc1adbc: ldur            x1, [x0, #-1]
    //     0xc1adc0: ubfx            x1, x1, #0xc, #0x14
    // 0xc1adc4: mov             x16, x0
    // 0xc1adc8: mov             x0, x1
    // 0xc1adcc: mov             x1, x16
    // 0xc1add0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1add0: sub             lr, x0, #1, lsl #12
    //     0xc1add4: ldr             lr, [x21, lr, lsl #3]
    //     0xc1add8: blr             lr
    // 0xc1addc: mov             x3, x0
    // 0xc1ade0: ldur            x2, [fp, #-8]
    // 0xc1ade4: stur            x3, [fp, #-0x28]
    // 0xc1ade8: r0 = LoadClassIdInstr(r2)
    //     0xc1ade8: ldur            x0, [x2, #-1]
    //     0xc1adec: ubfx            x0, x0, #0xc, #0x14
    // 0xc1adf0: mov             x1, x2
    // 0xc1adf4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1adf4: sub             lr, x0, #1, lsl #12
    //     0xc1adf8: ldr             lr, [x21, lr, lsl #3]
    //     0xc1adfc: blr             lr
    // 0xc1ae00: r1 = LoadClassIdInstr(r0)
    //     0xc1ae00: ldur            x1, [x0, #-1]
    //     0xc1ae04: ubfx            x1, x1, #0xc, #0x14
    // 0xc1ae08: mov             x16, x0
    // 0xc1ae0c: mov             x0, x1
    // 0xc1ae10: mov             x1, x16
    // 0xc1ae14: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1ae14: sub             lr, x0, #0xfff
    //     0xc1ae18: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ae1c: blr             lr
    // 0xc1ae20: stur            x0, [fp, #-0x30]
    // 0xc1ae24: r0 = SourceLocation()
    //     0xc1ae24: bl              #0xc1a510  ; AllocateSourceLocationStub -> SourceLocation (size=0x24)
    // 0xc1ae28: mov             x1, x0
    // 0xc1ae2c: ldur            x2, [fp, #-0x18]
    // 0xc1ae30: ldur            x3, [fp, #-0x30]
    // 0xc1ae34: ldur            x5, [fp, #-0x28]
    // 0xc1ae38: stur            x0, [fp, #-0x38]
    // 0xc1ae3c: r0 = SourceLocation()
    //     0xc1ae3c: bl              #0xc1a360  ; [package:source_span/src/location.dart] SourceLocation::SourceLocation
    // 0xc1ae40: ldur            x1, [fp, #-0x10]
    // 0xc1ae44: r2 = "\r\n"
    //     0xc1ae44: add             x2, PP, #0x12, lsl #12  ; [pp+0x12260] "\r\n"
    //     0xc1ae48: ldr             x2, [x2, #0x260]
    // 0xc1ae4c: r3 = "\n"
    //     0xc1ae4c: ldr             x3, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc1ae50: r0 = replaceAll()
    //     0xc1ae50: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xc1ae54: mov             x2, x0
    // 0xc1ae58: ldur            x1, [fp, #-8]
    // 0xc1ae5c: stur            x2, [fp, #-0x10]
    // 0xc1ae60: r0 = LoadClassIdInstr(r1)
    //     0xc1ae60: ldur            x0, [x1, #-1]
    //     0xc1ae64: ubfx            x0, x0, #0xc, #0x14
    // 0xc1ae68: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc1ae68: sub             lr, x0, #0xff4
    //     0xc1ae6c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1ae70: blr             lr
    // 0xc1ae74: mov             x1, x0
    // 0xc1ae78: r2 = "\r\n"
    //     0xc1ae78: add             x2, PP, #0x12, lsl #12  ; [pp+0x12260] "\r\n"
    //     0xc1ae7c: ldr             x2, [x2, #0x260]
    // 0xc1ae80: r3 = "\n"
    //     0xc1ae80: ldr             x3, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc1ae84: r0 = replaceAll()
    //     0xc1ae84: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xc1ae88: stur            x0, [fp, #-8]
    // 0xc1ae8c: r0 = SourceSpanWithContext()
    //     0xc1ae8c: bl              #0xc1a354  ; AllocateSourceSpanWithContextStub -> SourceSpanWithContext (size=0x18)
    // 0xc1ae90: mov             x1, x0
    // 0xc1ae94: ldur            x2, [fp, #-0x20]
    // 0xc1ae98: ldur            x3, [fp, #-0x38]
    // 0xc1ae9c: ldur            x5, [fp, #-0x10]
    // 0xc1aea0: ldur            x6, [fp, #-8]
    // 0xc1aea4: stur            x0, [fp, #-8]
    // 0xc1aea8: r0 = SourceSpanWithContext()
    //     0xc1aea8: bl              #0xc19e44  ; [package:source_span/src/span_with_context.dart] SourceSpanWithContext::SourceSpanWithContext
    // 0xc1aeac: ldur            x0, [fp, #-8]
    // 0xc1aeb0: LeaveFrame
    //     0xc1aeb0: mov             SP, fp
    //     0xc1aeb4: ldp             fp, lr, [SP], #0x10
    // 0xc1aeb8: ret
    //     0xc1aeb8: ret             
    // 0xc1aebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1aebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1aec0: b               #0xc1ac1c
    // 0xc1aec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1aec4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1aec8: b               #0xc1ace8
  }
  static _ _normalizeContext(/* No info */) {
    // ** addr: 0xc1aecc, size: 0x1cc
    // 0xc1aecc: EnterFrame
    //     0xc1aecc: stp             fp, lr, [SP, #-0x10]!
    //     0xc1aed0: mov             fp, SP
    // 0xc1aed4: AllocStack(0x50)
    //     0xc1aed4: sub             SP, SP, #0x50
    // 0xc1aed8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc1aed8: mov             x0, x1
    //     0xc1aedc: stur            x1, [fp, #-8]
    // 0xc1aee0: CheckStackOverflow
    //     0xc1aee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1aee4: cmp             SP, x16
    //     0xc1aee8: b.ls            #0xc1b090
    // 0xc1aeec: mov             x1, x0
    // 0xc1aef0: r0 = context()
    //     0xc1aef0: bl              #0xeb7550  ; [package:source_span/src/file.dart] _FileSpan::context
    // 0xc1aef4: mov             x4, x0
    // 0xc1aef8: ldur            x0, [fp, #-8]
    // 0xc1aefc: stur            x4, [fp, #-0x28]
    // 0xc1af00: LoadField: r5 = r0->field_7
    //     0xc1af00: ldur            w5, [x0, #7]
    // 0xc1af04: DecompressPointer r5
    //     0xc1af04: add             x5, x5, HEAP, lsl #32
    // 0xc1af08: stur            x5, [fp, #-0x20]
    // 0xc1af0c: LoadField: r6 = r0->field_b
    //     0xc1af0c: ldur            x6, [x0, #0xb]
    // 0xc1af10: stur            x6, [fp, #-0x18]
    // 0xc1af14: LoadField: r7 = r0->field_13
    //     0xc1af14: ldur            x7, [x0, #0x13]
    // 0xc1af18: mov             x1, x5
    // 0xc1af1c: mov             x2, x6
    // 0xc1af20: mov             x3, x7
    // 0xc1af24: stur            x7, [fp, #-0x10]
    // 0xc1af28: r0 = getText()
    //     0xc1af28: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xc1af2c: stur            x0, [fp, #-0x30]
    // 0xc1af30: r0 = FileLocation()
    //     0xc1af30: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xc1af34: mov             x1, x0
    // 0xc1af38: ldur            x2, [fp, #-0x20]
    // 0xc1af3c: ldur            x3, [fp, #-0x18]
    // 0xc1af40: stur            x0, [fp, #-0x38]
    // 0xc1af44: r0 = FileLocation._()
    //     0xc1af44: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xc1af48: ldur            x0, [fp, #-0x38]
    // 0xc1af4c: LoadField: r1 = r0->field_7
    //     0xc1af4c: ldur            w1, [x0, #7]
    // 0xc1af50: DecompressPointer r1
    //     0xc1af50: add             x1, x1, HEAP, lsl #32
    // 0xc1af54: LoadField: r2 = r0->field_b
    //     0xc1af54: ldur            x2, [x0, #0xb]
    // 0xc1af58: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc1af58: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc1af5c: r0 = getColumn()
    //     0xc1af5c: bl              #0xc1b124  ; [package:source_span/src/file.dart] SourceFile::getColumn
    // 0xc1af60: ldur            x1, [fp, #-0x28]
    // 0xc1af64: ldur            x2, [fp, #-0x30]
    // 0xc1af68: mov             x3, x0
    // 0xc1af6c: r0 = findLineStart()
    //     0xc1af6c: bl              #0xc196ac  ; [package:source_span/src/utils.dart] ::findLineStart
    // 0xc1af70: cmp             w0, NULL
    // 0xc1af74: b.eq            #0xc1af80
    // 0xc1af78: ldur            x0, [fp, #-8]
    // 0xc1af7c: b               #0xc1b084
    // 0xc1af80: r0 = FileLocation()
    //     0xc1af80: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xc1af84: mov             x1, x0
    // 0xc1af88: ldur            x2, [fp, #-0x20]
    // 0xc1af8c: ldur            x3, [fp, #-0x18]
    // 0xc1af90: stur            x0, [fp, #-8]
    // 0xc1af94: r0 = FileLocation._()
    //     0xc1af94: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xc1af98: ldur            x0, [fp, #-8]
    // 0xc1af9c: LoadField: r2 = r0->field_b
    //     0xc1af9c: ldur            x2, [x0, #0xb]
    // 0xc1afa0: stur            x2, [fp, #-0x40]
    // 0xc1afa4: r0 = SourceLocation()
    //     0xc1afa4: bl              #0xc1a510  ; AllocateSourceLocationStub -> SourceLocation (size=0x24)
    // 0xc1afa8: mov             x1, x0
    // 0xc1afac: ldur            x2, [fp, #-0x40]
    // 0xc1afb0: r3 = 0
    //     0xc1afb0: movz            x3, #0
    // 0xc1afb4: r5 = 0
    //     0xc1afb4: movz            x5, #0
    // 0xc1afb8: stur            x0, [fp, #-8]
    // 0xc1afbc: r0 = SourceLocation()
    //     0xc1afbc: bl              #0xc1a360  ; [package:source_span/src/location.dart] SourceLocation::SourceLocation
    // 0xc1afc0: r0 = FileLocation()
    //     0xc1afc0: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xc1afc4: mov             x1, x0
    // 0xc1afc8: ldur            x2, [fp, #-0x20]
    // 0xc1afcc: ldur            x3, [fp, #-0x10]
    // 0xc1afd0: stur            x0, [fp, #-0x28]
    // 0xc1afd4: r0 = FileLocation._()
    //     0xc1afd4: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xc1afd8: ldur            x0, [fp, #-0x28]
    // 0xc1afdc: LoadField: r4 = r0->field_b
    //     0xc1afdc: ldur            x4, [x0, #0xb]
    // 0xc1afe0: ldur            x1, [fp, #-0x20]
    // 0xc1afe4: ldur            x2, [fp, #-0x18]
    // 0xc1afe8: ldur            x3, [fp, #-0x10]
    // 0xc1afec: stur            x4, [fp, #-0x40]
    // 0xc1aff0: r0 = getText()
    //     0xc1aff0: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xc1aff4: mov             x1, x0
    // 0xc1aff8: r0 = countCodeUnits()
    //     0xc1aff8: bl              #0xc1b098  ; [package:source_span/src/utils.dart] ::countCodeUnits
    // 0xc1affc: ldur            x1, [fp, #-0x20]
    // 0xc1b000: ldur            x2, [fp, #-0x18]
    // 0xc1b004: ldur            x3, [fp, #-0x10]
    // 0xc1b008: stur            x0, [fp, #-0x48]
    // 0xc1b00c: r0 = getText()
    //     0xc1b00c: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xc1b010: mov             x1, x0
    // 0xc1b014: r0 = _lastLineLength()
    //     0xc1b014: bl              #0xc1a960  ; [package:source_span/src/highlighter.dart] _Highlight::_lastLineLength
    // 0xc1b018: stur            x0, [fp, #-0x50]
    // 0xc1b01c: r0 = SourceLocation()
    //     0xc1b01c: bl              #0xc1a510  ; AllocateSourceLocationStub -> SourceLocation (size=0x24)
    // 0xc1b020: mov             x1, x0
    // 0xc1b024: ldur            x2, [fp, #-0x40]
    // 0xc1b028: ldur            x3, [fp, #-0x50]
    // 0xc1b02c: ldur            x5, [fp, #-0x48]
    // 0xc1b030: stur            x0, [fp, #-0x28]
    // 0xc1b034: r0 = SourceLocation()
    //     0xc1b034: bl              #0xc1a360  ; [package:source_span/src/location.dart] SourceLocation::SourceLocation
    // 0xc1b038: ldur            x1, [fp, #-0x20]
    // 0xc1b03c: ldur            x2, [fp, #-0x18]
    // 0xc1b040: ldur            x3, [fp, #-0x10]
    // 0xc1b044: r0 = getText()
    //     0xc1b044: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xc1b048: ldur            x1, [fp, #-0x20]
    // 0xc1b04c: ldur            x2, [fp, #-0x18]
    // 0xc1b050: ldur            x3, [fp, #-0x10]
    // 0xc1b054: stur            x0, [fp, #-0x20]
    // 0xc1b058: r0 = getText()
    //     0xc1b058: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xc1b05c: stur            x0, [fp, #-0x30]
    // 0xc1b060: r0 = SourceSpanWithContext()
    //     0xc1b060: bl              #0xc1a354  ; AllocateSourceSpanWithContextStub -> SourceSpanWithContext (size=0x18)
    // 0xc1b064: mov             x1, x0
    // 0xc1b068: ldur            x2, [fp, #-8]
    // 0xc1b06c: ldur            x3, [fp, #-0x28]
    // 0xc1b070: ldur            x5, [fp, #-0x20]
    // 0xc1b074: ldur            x6, [fp, #-0x30]
    // 0xc1b078: stur            x0, [fp, #-8]
    // 0xc1b07c: r0 = SourceSpanWithContext()
    //     0xc1b07c: bl              #0xc19e44  ; [package:source_span/src/span_with_context.dart] SourceSpanWithContext::SourceSpanWithContext
    // 0xc1b080: ldur            x0, [fp, #-8]
    // 0xc1b084: LeaveFrame
    //     0xc1b084: mov             SP, fp
    //     0xc1b088: ldp             fp, lr, [SP], #0x10
    // 0xc1b08c: ret
    //     0xc1b08c: ret             
    // 0xc1b090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1b090: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1b094: b               #0xc1aeec
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3fe74, size: 0x284
    // 0xc3fe74: EnterFrame
    //     0xc3fe74: stp             fp, lr, [SP, #-0x10]!
    //     0xc3fe78: mov             fp, SP
    // 0xc3fe7c: AllocStack(0x28)
    //     0xc3fe7c: sub             SP, SP, #0x28
    // 0xc3fe80: CheckStackOverflow
    //     0xc3fe80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3fe84: cmp             SP, x16
    //     0xc3fe88: b.ls            #0xc400f0
    // 0xc3fe8c: r0 = StringBuffer()
    //     0xc3fe8c: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc3fe90: mov             x1, x0
    // 0xc3fe94: stur            x0, [fp, #-8]
    // 0xc3fe98: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc3fe98: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc3fe9c: r0 = StringBuffer()
    //     0xc3fe9c: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc3fea0: ldur            x1, [fp, #-8]
    // 0xc3fea4: r2 = "primary "
    //     0xc3fea4: add             x2, PP, #0x20, lsl #12  ; [pp+0x209d0] "primary "
    //     0xc3fea8: ldr             x2, [x2, #0x9d0]
    // 0xc3feac: r0 = write()
    //     0xc3feac: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc3feb0: ldr             x0, [fp, #0x10]
    // 0xc3feb4: LoadField: r2 = r0->field_7
    //     0xc3feb4: ldur            w2, [x0, #7]
    // 0xc3feb8: DecompressPointer r2
    //     0xc3feb8: add             x2, x2, HEAP, lsl #32
    // 0xc3febc: stur            x2, [fp, #-0x10]
    // 0xc3fec0: r0 = LoadClassIdInstr(r2)
    //     0xc3fec0: ldur            x0, [x2, #-1]
    //     0xc3fec4: ubfx            x0, x0, #0xc, #0x14
    // 0xc3fec8: mov             x1, x2
    // 0xc3fecc: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc3fecc: sub             lr, x0, #0xfff
    //     0xc3fed0: ldr             lr, [x21, lr, lsl #3]
    //     0xc3fed4: blr             lr
    // 0xc3fed8: r1 = LoadClassIdInstr(r0)
    //     0xc3fed8: ldur            x1, [x0, #-1]
    //     0xc3fedc: ubfx            x1, x1, #0xc, #0x14
    // 0xc3fee0: mov             x16, x0
    // 0xc3fee4: mov             x0, x1
    // 0xc3fee8: mov             x1, x16
    // 0xc3feec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc3feec: sub             lr, x0, #1, lsl #12
    //     0xc3fef0: ldr             lr, [x21, lr, lsl #3]
    //     0xc3fef4: blr             lr
    // 0xc3fef8: mov             x2, x0
    // 0xc3fefc: r0 = BoxInt64Instr(r2)
    //     0xc3fefc: sbfiz           x0, x2, #1, #0x1f
    //     0xc3ff00: cmp             x2, x0, asr #1
    //     0xc3ff04: b.eq            #0xc3ff10
    //     0xc3ff08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3ff0c: stur            x2, [x0, #7]
    // 0xc3ff10: r1 = Null
    //     0xc3ff10: mov             x1, NULL
    // 0xc3ff14: r2 = 14
    //     0xc3ff14: movz            x2, #0xe
    // 0xc3ff18: stur            x0, [fp, #-0x18]
    // 0xc3ff1c: r0 = AllocateArray()
    //     0xc3ff1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3ff20: mov             x2, x0
    // 0xc3ff24: ldur            x0, [fp, #-0x18]
    // 0xc3ff28: stur            x2, [fp, #-0x20]
    // 0xc3ff2c: StoreField: r2->field_f = r0
    //     0xc3ff2c: stur            w0, [x2, #0xf]
    // 0xc3ff30: r16 = ":"
    //     0xc3ff30: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc3ff34: StoreField: r2->field_13 = r16
    //     0xc3ff34: stur            w16, [x2, #0x13]
    // 0xc3ff38: ldur            x3, [fp, #-0x10]
    // 0xc3ff3c: r0 = LoadClassIdInstr(r3)
    //     0xc3ff3c: ldur            x0, [x3, #-1]
    //     0xc3ff40: ubfx            x0, x0, #0xc, #0x14
    // 0xc3ff44: mov             x1, x3
    // 0xc3ff48: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc3ff48: sub             lr, x0, #0xfff
    //     0xc3ff4c: ldr             lr, [x21, lr, lsl #3]
    //     0xc3ff50: blr             lr
    // 0xc3ff54: r1 = LoadClassIdInstr(r0)
    //     0xc3ff54: ldur            x1, [x0, #-1]
    //     0xc3ff58: ubfx            x1, x1, #0xc, #0x14
    // 0xc3ff5c: mov             x16, x0
    // 0xc3ff60: mov             x0, x1
    // 0xc3ff64: mov             x1, x16
    // 0xc3ff68: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc3ff68: sub             lr, x0, #0xfff
    //     0xc3ff6c: ldr             lr, [x21, lr, lsl #3]
    //     0xc3ff70: blr             lr
    // 0xc3ff74: mov             x2, x0
    // 0xc3ff78: r0 = BoxInt64Instr(r2)
    //     0xc3ff78: sbfiz           x0, x2, #1, #0x1f
    //     0xc3ff7c: cmp             x2, x0, asr #1
    //     0xc3ff80: b.eq            #0xc3ff8c
    //     0xc3ff84: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3ff88: stur            x2, [x0, #7]
    // 0xc3ff8c: ldur            x1, [fp, #-0x20]
    // 0xc3ff90: ArrayStore: r1[2] = r0  ; List_4
    //     0xc3ff90: add             x25, x1, #0x17
    //     0xc3ff94: str             w0, [x25]
    //     0xc3ff98: tbz             w0, #0, #0xc3ffb4
    //     0xc3ff9c: ldurb           w16, [x1, #-1]
    //     0xc3ffa0: ldurb           w17, [x0, #-1]
    //     0xc3ffa4: and             x16, x17, x16, lsr #2
    //     0xc3ffa8: tst             x16, HEAP, lsr #32
    //     0xc3ffac: b.eq            #0xc3ffb4
    //     0xc3ffb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3ffb4: ldur            x2, [fp, #-0x20]
    // 0xc3ffb8: r16 = "-"
    //     0xc3ffb8: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc3ffbc: StoreField: r2->field_1b = r16
    //     0xc3ffbc: stur            w16, [x2, #0x1b]
    // 0xc3ffc0: ldur            x3, [fp, #-0x10]
    // 0xc3ffc4: r0 = LoadClassIdInstr(r3)
    //     0xc3ffc4: ldur            x0, [x3, #-1]
    //     0xc3ffc8: ubfx            x0, x0, #0xc, #0x14
    // 0xc3ffcc: mov             x1, x3
    // 0xc3ffd0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc3ffd0: sub             lr, x0, #1, lsl #12
    //     0xc3ffd4: ldr             lr, [x21, lr, lsl #3]
    //     0xc3ffd8: blr             lr
    // 0xc3ffdc: r1 = LoadClassIdInstr(r0)
    //     0xc3ffdc: ldur            x1, [x0, #-1]
    //     0xc3ffe0: ubfx            x1, x1, #0xc, #0x14
    // 0xc3ffe4: mov             x16, x0
    // 0xc3ffe8: mov             x0, x1
    // 0xc3ffec: mov             x1, x16
    // 0xc3fff0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc3fff0: sub             lr, x0, #1, lsl #12
    //     0xc3fff4: ldr             lr, [x21, lr, lsl #3]
    //     0xc3fff8: blr             lr
    // 0xc3fffc: mov             x2, x0
    // 0xc40000: r0 = BoxInt64Instr(r2)
    //     0xc40000: sbfiz           x0, x2, #1, #0x1f
    //     0xc40004: cmp             x2, x0, asr #1
    //     0xc40008: b.eq            #0xc40014
    //     0xc4000c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc40010: stur            x2, [x0, #7]
    // 0xc40014: ldur            x1, [fp, #-0x20]
    // 0xc40018: ArrayStore: r1[4] = r0  ; List_4
    //     0xc40018: add             x25, x1, #0x1f
    //     0xc4001c: str             w0, [x25]
    //     0xc40020: tbz             w0, #0, #0xc4003c
    //     0xc40024: ldurb           w16, [x1, #-1]
    //     0xc40028: ldurb           w17, [x0, #-1]
    //     0xc4002c: and             x16, x17, x16, lsr #2
    //     0xc40030: tst             x16, HEAP, lsr #32
    //     0xc40034: b.eq            #0xc4003c
    //     0xc40038: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc4003c: ldur            x2, [fp, #-0x20]
    // 0xc40040: r16 = ":"
    //     0xc40040: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc40044: StoreField: r2->field_23 = r16
    //     0xc40044: stur            w16, [x2, #0x23]
    // 0xc40048: ldur            x1, [fp, #-0x10]
    // 0xc4004c: r0 = LoadClassIdInstr(r1)
    //     0xc4004c: ldur            x0, [x1, #-1]
    //     0xc40050: ubfx            x0, x0, #0xc, #0x14
    // 0xc40054: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc40054: sub             lr, x0, #1, lsl #12
    //     0xc40058: ldr             lr, [x21, lr, lsl #3]
    //     0xc4005c: blr             lr
    // 0xc40060: r1 = LoadClassIdInstr(r0)
    //     0xc40060: ldur            x1, [x0, #-1]
    //     0xc40064: ubfx            x1, x1, #0xc, #0x14
    // 0xc40068: mov             x16, x0
    // 0xc4006c: mov             x0, x1
    // 0xc40070: mov             x1, x16
    // 0xc40074: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc40074: sub             lr, x0, #0xfff
    //     0xc40078: ldr             lr, [x21, lr, lsl #3]
    //     0xc4007c: blr             lr
    // 0xc40080: mov             x2, x0
    // 0xc40084: r0 = BoxInt64Instr(r2)
    //     0xc40084: sbfiz           x0, x2, #1, #0x1f
    //     0xc40088: cmp             x2, x0, asr #1
    //     0xc4008c: b.eq            #0xc40098
    //     0xc40090: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc40094: stur            x2, [x0, #7]
    // 0xc40098: ldur            x1, [fp, #-0x20]
    // 0xc4009c: ArrayStore: r1[6] = r0  ; List_4
    //     0xc4009c: add             x25, x1, #0x27
    //     0xc400a0: str             w0, [x25]
    //     0xc400a4: tbz             w0, #0, #0xc400c0
    //     0xc400a8: ldurb           w16, [x1, #-1]
    //     0xc400ac: ldurb           w17, [x0, #-1]
    //     0xc400b0: and             x16, x17, x16, lsr #2
    //     0xc400b4: tst             x16, HEAP, lsr #32
    //     0xc400b8: b.eq            #0xc400c0
    //     0xc400bc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc400c0: ldur            x16, [fp, #-0x20]
    // 0xc400c4: str             x16, [SP]
    // 0xc400c8: r0 = _interpolate()
    //     0xc400c8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc400cc: ldur            x1, [fp, #-8]
    // 0xc400d0: mov             x2, x0
    // 0xc400d4: r0 = write()
    //     0xc400d4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc400d8: ldur            x16, [fp, #-8]
    // 0xc400dc: str             x16, [SP]
    // 0xc400e0: r0 = toString()
    //     0xc400e0: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc400e4: LeaveFrame
    //     0xc400e4: mov             SP, fp
    //     0xc400e8: ldp             fp, lr, [SP], #0x10
    // 0xc400ec: ret
    //     0xc400ec: ret             
    // 0xc400f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc400f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc400f4: b               #0xc3fe8c
  }
}

// class id: 491, size: 0x28, field offset: 0x8
class Highlighter extends Object {

  _ highlight(/* No info */) {
    // ** addr: 0xc14744, size: 0xa80
    // 0xc14744: EnterFrame
    //     0xc14744: stp             fp, lr, [SP, #-0x10]!
    //     0xc14748: mov             fp, SP
    // 0xc1474c: AllocStack(0xb8)
    //     0xc1474c: sub             SP, SP, #0xb8
    // 0xc14750: SetupParameters(Highlighter this /* r1 => r0, fp-0x10 */)
    //     0xc14750: mov             x0, x1
    //     0xc14754: stur            x1, [fp, #-0x10]
    // 0xc14758: CheckStackOverflow
    //     0xc14758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1475c: cmp             SP, x16
    //     0xc14760: b.ls            #0xc1517c
    // 0xc14764: LoadField: r2 = r0->field_7
    //     0xc14764: ldur            w2, [x0, #7]
    // 0xc14768: DecompressPointer r2
    //     0xc14768: add             x2, x2, HEAP, lsl #32
    // 0xc1476c: mov             x1, x2
    // 0xc14770: stur            x2, [fp, #-8]
    // 0xc14774: r0 = first()
    //     0xc14774: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xc14778: LoadField: r2 = r0->field_13
    //     0xc14778: ldur            w2, [x0, #0x13]
    // 0xc1477c: DecompressPointer r2
    //     0xc1477c: add             x2, x2, HEAP, lsl #32
    // 0xc14780: ldur            x1, [fp, #-0x10]
    // 0xc14784: r0 = _writeFileStart()
    //     0xc14784: bl              #0xc171c0  ; [package:source_span/src/highlighter.dart] Highlighter::_writeFileStart
    // 0xc14788: ldur            x3, [fp, #-0x10]
    // 0xc1478c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xc1478c: ldur            x4, [x3, #0x17]
    // 0xc14790: stur            x4, [fp, #-0x18]
    // 0xc14794: r0 = BoxInt64Instr(r4)
    //     0xc14794: sbfiz           x0, x4, #1, #0x1f
    //     0xc14798: cmp             x4, x0, asr #1
    //     0xc1479c: b.eq            #0xc147a8
    //     0xc147a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc147a4: stur            x4, [x0, #7]
    // 0xc147a8: mov             x2, x0
    // 0xc147ac: r1 = <_Highlight?>
    //     0xc147ac: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c578] TypeArguments: <_Highlight?>
    //     0xc147b0: ldr             x1, [x1, #0x578]
    // 0xc147b4: r0 = AllocateArray()
    //     0xc147b4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc147b8: mov             x3, x0
    // 0xc147bc: ldur            x2, [fp, #-0x10]
    // 0xc147c0: stur            x3, [fp, #-0x50]
    // 0xc147c4: LoadField: r4 = r2->field_23
    //     0xc147c4: ldur            w4, [x2, #0x23]
    // 0xc147c8: DecompressPointer r4
    //     0xc147c8: add             x4, x4, HEAP, lsl #32
    // 0xc147cc: stur            x4, [fp, #-0x48]
    // 0xc147d0: LoadField: r6 = r2->field_b
    //     0xc147d0: ldur            w6, [x2, #0xb]
    // 0xc147d4: DecompressPointer r6
    //     0xc147d4: add             x6, x6, HEAP, lsl #32
    // 0xc147d8: stur            x6, [fp, #-0x40]
    // 0xc147dc: r8 = 0
    //     0xc147dc: movz            x8, #0
    // 0xc147e0: ldur            x7, [fp, #-8]
    // 0xc147e4: ldur            x5, [fp, #-0x18]
    // 0xc147e8: stur            x8, [fp, #-0x38]
    // 0xc147ec: CheckStackOverflow
    //     0xc147ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc147f0: cmp             SP, x16
    //     0xc147f4: b.ls            #0xc15184
    // 0xc147f8: LoadField: r0 = r7->field_b
    //     0xc147f8: ldur            w0, [x7, #0xb]
    // 0xc147fc: r1 = LoadInt32Instr(r0)
    //     0xc147fc: sbfx            x1, x0, #1, #0x1f
    // 0xc14800: cmp             x8, x1
    // 0xc14804: b.ge            #0xc150cc
    // 0xc14808: LoadField: r9 = r7->field_f
    //     0xc14808: ldur            w9, [x7, #0xf]
    // 0xc1480c: DecompressPointer r9
    //     0xc1480c: add             x9, x9, HEAP, lsl #32
    // 0xc14810: ArrayLoad: r10 = r9[r8]  ; Unknown_4
    //     0xc14810: add             x16, x9, x8, lsl #2
    //     0xc14814: ldur            w10, [x16, #0xf]
    // 0xc14818: DecompressPointer r10
    //     0xc14818: add             x10, x10, HEAP, lsl #32
    // 0xc1481c: stur            x10, [fp, #-0x30]
    // 0xc14820: cmp             x8, #0
    // 0xc14824: b.le            #0xc149a8
    // 0xc14828: sub             x11, x8, #1
    // 0xc1482c: mov             x0, x1
    // 0xc14830: mov             x1, x11
    // 0xc14834: cmp             x1, x0
    // 0xc14838: b.hs            #0xc1518c
    // 0xc1483c: ArrayLoad: r1 = r9[r11]  ; Unknown_4
    //     0xc1483c: add             x16, x9, x11, lsl #2
    //     0xc14840: ldur            w1, [x16, #0xf]
    // 0xc14844: DecompressPointer r1
    //     0xc14844: add             x1, x1, HEAP, lsl #32
    // 0xc14848: stur            x1, [fp, #-0x28]
    // 0xc1484c: LoadField: r0 = r1->field_13
    //     0xc1484c: ldur            w0, [x1, #0x13]
    // 0xc14850: DecompressPointer r0
    //     0xc14850: add             x0, x0, HEAP, lsl #32
    // 0xc14854: LoadField: r9 = r10->field_13
    //     0xc14854: ldur            w9, [x10, #0x13]
    // 0xc14858: DecompressPointer r9
    //     0xc14858: add             x9, x9, HEAP, lsl #32
    // 0xc1485c: stur            x9, [fp, #-0x20]
    // 0xc14860: r11 = 60
    //     0xc14860: movz            x11, #0x3c
    // 0xc14864: branchIfSmi(r0, 0xc14870)
    //     0xc14864: tbz             w0, #0, #0xc14870
    // 0xc14868: r11 = LoadClassIdInstr(r0)
    //     0xc14868: ldur            x11, [x0, #-1]
    //     0xc1486c: ubfx            x11, x11, #0xc, #0x14
    // 0xc14870: stp             x9, x0, [SP]
    // 0xc14874: mov             x0, x11
    // 0xc14878: mov             lr, x0
    // 0xc1487c: ldr             lr, [x21, lr, lsl #3]
    // 0xc14880: blr             lr
    // 0xc14884: tbz             w0, #4, #0xc14924
    // 0xc14888: ldur            x1, [fp, #-0x10]
    // 0xc1488c: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc1488c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc14890: ldr             x0, [x0, #0x2ea8]
    //     0xc14894: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc14898: cmp             w0, w16
    //     0xc1489c: b.ne            #0xc148ac
    //     0xc148a0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc148a4: ldr             x2, [x2, #0x580]
    //     0xc148a8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc148ac: r1 = 3
    //     0xc148ac: movz            x1, #0x3
    // 0xc148b0: r0 = AllocateContext()
    //     0xc148b0: bl              #0xec126c  ; AllocateContextStub
    // 0xc148b4: mov             x1, x0
    // 0xc148b8: ldur            x0, [fp, #-0x10]
    // 0xc148bc: StoreField: r1->field_f = r0
    //     0xc148bc: stur            w0, [x1, #0xf]
    // 0xc148c0: r3 = "╵"
    //     0xc148c0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c588] "╵"
    //     0xc148c4: ldr             x3, [x3, #0x588]
    // 0xc148c8: ArrayStore: r1[0] = r3  ; List_4
    //     0xc148c8: stur            w3, [x1, #0x17]
    // 0xc148cc: mov             x2, x1
    // 0xc148d0: r1 = Function '<anonymous closure>':.
    //     0xc148d0: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c590] AnonymousClosure: (0xc18550), in [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar (0xc15380)
    //     0xc148d4: ldr             x1, [x1, #0x590]
    // 0xc148d8: r0 = AllocateClosure()
    //     0xc148d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xc148dc: r16 = <Null?>
    //     0xc148dc: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc148e0: ldur            lr, [fp, #-0x10]
    // 0xc148e4: stp             lr, x16, [SP, #0x10]
    // 0xc148e8: r16 = "[34m"
    //     0xc148e8: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c598] "[34m"
    //     0xc148ec: ldr             x16, [x16, #0x598]
    // 0xc148f0: stp             x16, x0, [SP]
    // 0xc148f4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc148f4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc148f8: r0 = _colorize()
    //     0xc148f8: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc148fc: ldur            x1, [fp, #-0x48]
    // 0xc14900: r2 = ""
    //     0xc14900: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc14904: r0 = write()
    //     0xc14904: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc14908: ldur            x1, [fp, #-0x48]
    // 0xc1490c: r2 = "\n"
    //     0xc1490c: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc14910: r0 = _writeString()
    //     0xc14910: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc14914: ldur            x1, [fp, #-0x10]
    // 0xc14918: ldur            x2, [fp, #-0x20]
    // 0xc1491c: r0 = _writeFileStart()
    //     0xc1491c: bl              #0xc171c0  ; [package:source_span/src/highlighter.dart] Highlighter::_writeFileStart
    // 0xc14920: b               #0xc149a8
    // 0xc14924: ldur            x2, [fp, #-0x30]
    // 0xc14928: ldur            x0, [fp, #-0x28]
    // 0xc1492c: LoadField: r1 = r0->field_b
    //     0xc1492c: ldur            x1, [x0, #0xb]
    // 0xc14930: add             x0, x1, #1
    // 0xc14934: LoadField: r1 = r2->field_b
    //     0xc14934: ldur            x1, [x2, #0xb]
    // 0xc14938: cmp             x0, x1
    // 0xc1493c: b.eq            #0xc149a8
    // 0xc14940: ldur            x1, [fp, #-0x10]
    // 0xc14944: r1 = 3
    //     0xc14944: movz            x1, #0x3
    // 0xc14948: r0 = AllocateContext()
    //     0xc14948: bl              #0xec126c  ; AllocateContextStub
    // 0xc1494c: mov             x1, x0
    // 0xc14950: ldur            x0, [fp, #-0x10]
    // 0xc14954: StoreField: r1->field_f = r0
    //     0xc14954: stur            w0, [x1, #0xf]
    // 0xc14958: r3 = "..."
    //     0xc14958: ldr             x3, [PP, #0xb00]  ; [pp+0xb00] "..."
    // 0xc1495c: StoreField: r1->field_13 = r3
    //     0xc1495c: stur            w3, [x1, #0x13]
    // 0xc14960: mov             x2, x1
    // 0xc14964: r1 = Function '<anonymous closure>':.
    //     0xc14964: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c590] AnonymousClosure: (0xc18550), in [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar (0xc15380)
    //     0xc14968: ldr             x1, [x1, #0x590]
    // 0xc1496c: r0 = AllocateClosure()
    //     0xc1496c: bl              #0xec1630  ; AllocateClosureStub
    // 0xc14970: r16 = <Null?>
    //     0xc14970: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc14974: ldur            lr, [fp, #-0x10]
    // 0xc14978: stp             lr, x16, [SP, #0x10]
    // 0xc1497c: r16 = "[34m"
    //     0xc1497c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c598] "[34m"
    //     0xc14980: ldr             x16, [x16, #0x598]
    // 0xc14984: stp             x16, x0, [SP]
    // 0xc14988: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc14988: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc1498c: r0 = _colorize()
    //     0xc1498c: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc14990: ldur            x1, [fp, #-0x48]
    // 0xc14994: r2 = ""
    //     0xc14994: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc14998: r0 = write()
    //     0xc14998: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc1499c: ldur            x1, [fp, #-0x48]
    // 0xc149a0: r2 = "\n"
    //     0xc149a0: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc149a4: r0 = _writeString()
    //     0xc149a4: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc149a8: ldur            x2, [fp, #-0x30]
    // 0xc149ac: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc149ac: ldur            w0, [x2, #0x17]
    // 0xc149b0: DecompressPointer r0
    //     0xc149b0: add             x0, x0, HEAP, lsl #32
    // 0xc149b4: stur            x0, [fp, #-0x28]
    // 0xc149b8: LoadField: r3 = r0->field_7
    //     0xc149b8: ldur            w3, [x0, #7]
    // 0xc149bc: DecompressPointer r3
    //     0xc149bc: add             x3, x3, HEAP, lsl #32
    // 0xc149c0: mov             x1, x3
    // 0xc149c4: stur            x3, [fp, #-0x20]
    // 0xc149c8: r0 = ReversedListIterable()
    //     0xc149c8: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xc149cc: mov             x1, x0
    // 0xc149d0: ldur            x0, [fp, #-0x28]
    // 0xc149d4: stur            x1, [fp, #-0x58]
    // 0xc149d8: StoreField: r1->field_b = r0
    //     0xc149d8: stur            w0, [x1, #0xb]
    // 0xc149dc: str             x1, [SP]
    // 0xc149e0: r0 = length()
    //     0xc149e0: bl              #0x92ab48  ; [dart:collection] MapView::length
    // 0xc149e4: r1 = LoadInt32Instr(r0)
    //     0xc149e4: sbfx            x1, x0, #1, #0x1f
    //     0xc149e8: tbz             w0, #0, #0xc149f0
    //     0xc149ec: ldur            x1, [x0, #7]
    // 0xc149f0: ldur            x2, [fp, #-0x30]
    // 0xc149f4: stur            x1, [fp, #-0x80]
    // 0xc149f8: LoadField: r3 = r2->field_b
    //     0xc149f8: ldur            x3, [x2, #0xb]
    // 0xc149fc: stur            x3, [fp, #-0x78]
    // 0xc14a00: LoadField: r4 = r2->field_7
    //     0xc14a00: ldur            w4, [x2, #7]
    // 0xc14a04: DecompressPointer r4
    //     0xc14a04: add             x4, x4, HEAP, lsl #32
    // 0xc14a08: stur            x4, [fp, #-0x70]
    // 0xc14a0c: LoadField: r0 = r4->field_7
    //     0xc14a0c: ldur            w0, [x4, #7]
    // 0xc14a10: r5 = LoadInt32Instr(r0)
    //     0xc14a10: sbfx            x5, x0, #1, #0x1f
    // 0xc14a14: stur            x5, [fp, #-0x68]
    // 0xc14a18: ldur            x8, [fp, #-0x18]
    // 0xc14a1c: ldur            x7, [fp, #-0x50]
    // 0xc14a20: r9 = 0
    //     0xc14a20: movz            x9, #0
    // 0xc14a24: ldur            x6, [fp, #-0x58]
    // 0xc14a28: stur            x9, [fp, #-0x60]
    // 0xc14a2c: CheckStackOverflow
    //     0xc14a2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc14a30: cmp             SP, x16
    //     0xc14a34: b.ls            #0xc15190
    // 0xc14a38: LoadField: r0 = r6->field_b
    //     0xc14a38: ldur            w0, [x6, #0xb]
    // 0xc14a3c: DecompressPointer r0
    //     0xc14a3c: add             x0, x0, HEAP, lsl #32
    // 0xc14a40: r10 = LoadClassIdInstr(r0)
    //     0xc14a40: ldur            x10, [x0, #-1]
    //     0xc14a44: ubfx            x10, x10, #0xc, #0x14
    // 0xc14a48: str             x0, [SP]
    // 0xc14a4c: mov             x0, x10
    // 0xc14a50: r0 = GDT[cid_x0 + 0xc834]()
    //     0xc14a50: movz            x17, #0xc834
    //     0xc14a54: add             lr, x0, x17
    //     0xc14a58: ldr             lr, [x21, lr, lsl #3]
    //     0xc14a5c: blr             lr
    // 0xc14a60: r1 = LoadInt32Instr(r0)
    //     0xc14a60: sbfx            x1, x0, #1, #0x1f
    //     0xc14a64: tbz             w0, #0, #0xc14a6c
    //     0xc14a68: ldur            x1, [x0, #7]
    // 0xc14a6c: ldur            x0, [fp, #-0x80]
    // 0xc14a70: cmp             x0, x1
    // 0xc14a74: b.ne            #0xc1515c
    // 0xc14a78: ldur            x3, [fp, #-0x60]
    // 0xc14a7c: cmp             x3, x1
    // 0xc14a80: b.ge            #0xc14d74
    // 0xc14a84: ldur            x1, [fp, #-0x58]
    // 0xc14a88: mov             x2, x3
    // 0xc14a8c: r0 = elementAt()
    //     0xc14a8c: bl              #0x892884  ; [dart:_internal] ReversedListIterable::elementAt
    // 0xc14a90: mov             x3, x0
    // 0xc14a94: ldur            x0, [fp, #-0x60]
    // 0xc14a98: stur            x3, [fp, #-0x90]
    // 0xc14a9c: add             x9, x0, #1
    // 0xc14aa0: stur            x9, [fp, #-0x88]
    // 0xc14aa4: cmp             w3, NULL
    // 0xc14aa8: b.ne            #0xc14adc
    // 0xc14aac: mov             x0, x3
    // 0xc14ab0: ldur            x2, [fp, #-0x20]
    // 0xc14ab4: r1 = Null
    //     0xc14ab4: mov             x1, NULL
    // 0xc14ab8: cmp             w2, NULL
    // 0xc14abc: b.eq            #0xc14adc
    // 0xc14ac0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc14ac0: ldur            w4, [x2, #0x17]
    // 0xc14ac4: DecompressPointer r4
    //     0xc14ac4: add             x4, x4, HEAP, lsl #32
    // 0xc14ac8: r8 = X0
    //     0xc14ac8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc14acc: LoadField: r9 = r4->field_7
    //     0xc14acc: ldur            x9, [x4, #7]
    // 0xc14ad0: r3 = Null
    //     0xc14ad0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c5a0] Null
    //     0xc14ad4: ldr             x3, [x3, #0x5a0]
    // 0xc14ad8: blr             x9
    // 0xc14adc: ldur            x2, [fp, #-0x90]
    // 0xc14ae0: LoadField: r3 = r2->field_7
    //     0xc14ae0: ldur            w3, [x2, #7]
    // 0xc14ae4: DecompressPointer r3
    //     0xc14ae4: add             x3, x3, HEAP, lsl #32
    // 0xc14ae8: stur            x3, [fp, #-0x98]
    // 0xc14aec: r0 = LoadClassIdInstr(r3)
    //     0xc14aec: ldur            x0, [x3, #-1]
    //     0xc14af0: ubfx            x0, x0, #0xc, #0x14
    // 0xc14af4: mov             x1, x3
    // 0xc14af8: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc14af8: sub             lr, x0, #0xfff
    //     0xc14afc: ldr             lr, [x21, lr, lsl #3]
    //     0xc14b00: blr             lr
    // 0xc14b04: r1 = LoadClassIdInstr(r0)
    //     0xc14b04: ldur            x1, [x0, #-1]
    //     0xc14b08: ubfx            x1, x1, #0xc, #0x14
    // 0xc14b0c: mov             x16, x0
    // 0xc14b10: mov             x0, x1
    // 0xc14b14: mov             x1, x16
    // 0xc14b18: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14b18: sub             lr, x0, #1, lsl #12
    //     0xc14b1c: ldr             lr, [x21, lr, lsl #3]
    //     0xc14b20: blr             lr
    // 0xc14b24: mov             x3, x0
    // 0xc14b28: ldur            x2, [fp, #-0x98]
    // 0xc14b2c: stur            x3, [fp, #-0x60]
    // 0xc14b30: r0 = LoadClassIdInstr(r2)
    //     0xc14b30: ldur            x0, [x2, #-1]
    //     0xc14b34: ubfx            x0, x0, #0xc, #0x14
    // 0xc14b38: mov             x1, x2
    // 0xc14b3c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14b3c: sub             lr, x0, #1, lsl #12
    //     0xc14b40: ldr             lr, [x21, lr, lsl #3]
    //     0xc14b44: blr             lr
    // 0xc14b48: r1 = LoadClassIdInstr(r0)
    //     0xc14b48: ldur            x1, [x0, #-1]
    //     0xc14b4c: ubfx            x1, x1, #0xc, #0x14
    // 0xc14b50: mov             x16, x0
    // 0xc14b54: mov             x0, x1
    // 0xc14b58: mov             x1, x16
    // 0xc14b5c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14b5c: sub             lr, x0, #1, lsl #12
    //     0xc14b60: ldr             lr, [x21, lr, lsl #3]
    //     0xc14b64: blr             lr
    // 0xc14b68: mov             x1, x0
    // 0xc14b6c: ldur            x0, [fp, #-0x60]
    // 0xc14b70: cmp             x0, x1
    // 0xc14b74: b.eq            #0xc14d48
    // 0xc14b78: ldur            x2, [fp, #-0x98]
    // 0xc14b7c: ldur            x3, [fp, #-0x78]
    // 0xc14b80: r0 = LoadClassIdInstr(r2)
    //     0xc14b80: ldur            x0, [x2, #-1]
    //     0xc14b84: ubfx            x0, x0, #0xc, #0x14
    // 0xc14b88: mov             x1, x2
    // 0xc14b8c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc14b8c: sub             lr, x0, #0xfff
    //     0xc14b90: ldr             lr, [x21, lr, lsl #3]
    //     0xc14b94: blr             lr
    // 0xc14b98: r1 = LoadClassIdInstr(r0)
    //     0xc14b98: ldur            x1, [x0, #-1]
    //     0xc14b9c: ubfx            x1, x1, #0xc, #0x14
    // 0xc14ba0: mov             x16, x0
    // 0xc14ba4: mov             x0, x1
    // 0xc14ba8: mov             x1, x16
    // 0xc14bac: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14bac: sub             lr, x0, #1, lsl #12
    //     0xc14bb0: ldr             lr, [x21, lr, lsl #3]
    //     0xc14bb4: blr             lr
    // 0xc14bb8: ldur            x2, [fp, #-0x78]
    // 0xc14bbc: cmp             x0, x2
    // 0xc14bc0: b.ne            #0xc14d3c
    // 0xc14bc4: ldur            x1, [fp, #-0x98]
    // 0xc14bc8: r0 = LoadClassIdInstr(r1)
    //     0xc14bc8: ldur            x0, [x1, #-1]
    //     0xc14bcc: ubfx            x0, x0, #0xc, #0x14
    // 0xc14bd0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc14bd0: sub             lr, x0, #0xfff
    //     0xc14bd4: ldr             lr, [x21, lr, lsl #3]
    //     0xc14bd8: blr             lr
    // 0xc14bdc: r1 = LoadClassIdInstr(r0)
    //     0xc14bdc: ldur            x1, [x0, #-1]
    //     0xc14be0: ubfx            x1, x1, #0xc, #0x14
    // 0xc14be4: mov             x16, x0
    // 0xc14be8: mov             x0, x1
    // 0xc14bec: mov             x1, x16
    // 0xc14bf0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc14bf0: sub             lr, x0, #0xfff
    //     0xc14bf4: ldr             lr, [x21, lr, lsl #3]
    //     0xc14bf8: blr             lr
    // 0xc14bfc: mov             x2, x0
    // 0xc14c00: r0 = BoxInt64Instr(r2)
    //     0xc14c00: sbfiz           x0, x2, #1, #0x1f
    //     0xc14c04: cmp             x2, x0, asr #1
    //     0xc14c08: b.eq            #0xc14c14
    //     0xc14c0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc14c10: stur            x2, [x0, #7]
    // 0xc14c14: mov             x2, x0
    // 0xc14c18: ldur            x3, [fp, #-0x68]
    // 0xc14c1c: r1 = 0
    //     0xc14c1c: movz            x1, #0
    // 0xc14c20: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xc14c20: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xc14c24: r0 = checkValidRange()
    //     0xc14c24: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xc14c28: ldur            x1, [fp, #-0x70]
    // 0xc14c2c: mov             x3, x0
    // 0xc14c30: r2 = 0
    //     0xc14c30: movz            x2, #0
    // 0xc14c34: r0 = _substringUnchecked()
    //     0xc14c34: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xc14c38: LoadField: r1 = r0->field_7
    //     0xc14c38: ldur            w1, [x0, #7]
    // 0xc14c3c: r2 = LoadInt32Instr(r1)
    //     0xc14c3c: sbfx            x2, x1, #1, #0x1f
    // 0xc14c40: r1 = LoadClassIdInstr(r0)
    //     0xc14c40: ldur            x1, [x0, #-1]
    //     0xc14c44: ubfx            x1, x1, #0xc, #0x14
    // 0xc14c48: lsl             x1, x1, #1
    // 0xc14c4c: r3 = 0
    //     0xc14c4c: movz            x3, #0
    // 0xc14c50: CheckStackOverflow
    //     0xc14c50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc14c54: cmp             SP, x16
    //     0xc14c58: b.ls            #0xc15198
    // 0xc14c5c: cmp             x3, x2
    // 0xc14c60: b.ge            #0xc14ca8
    // 0xc14c64: cmp             w1, #0xbc
    // 0xc14c68: b.ne            #0xc14c78
    // 0xc14c6c: ArrayLoad: r4 = r0[r3]  ; TypedUnsigned_1
    //     0xc14c6c: add             x16, x0, x3
    //     0xc14c70: ldrb            w4, [x16, #0xf]
    // 0xc14c74: b               #0xc14c80
    // 0xc14c78: add             x16, x0, x3, lsl #1
    // 0xc14c7c: ldurh           w4, [x16, #0xf]
    // 0xc14c80: add             x5, x3, #1
    // 0xc14c84: cmp             x4, #0x20
    // 0xc14c88: b.eq            #0xc14ca0
    // 0xc14c8c: cmp             x4, #9
    // 0xc14c90: b.eq            #0xc14ca0
    // 0xc14c94: ldur            x3, [fp, #-0x18]
    // 0xc14c98: ldur            x2, [fp, #-0x50]
    // 0xc14c9c: b               #0xc14d50
    // 0xc14ca0: mov             x3, x5
    // 0xc14ca4: b               #0xc14c50
    // 0xc14ca8: ldur            x3, [fp, #-0x18]
    // 0xc14cac: ldur            x2, [fp, #-0x50]
    // 0xc14cb0: r0 = 0
    //     0xc14cb0: movz            x0, #0
    // 0xc14cb4: CheckStackOverflow
    //     0xc14cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc14cb8: cmp             SP, x16
    //     0xc14cbc: b.ls            #0xc151a0
    // 0xc14cc0: cmp             x0, x3
    // 0xc14cc4: b.ge            #0xc14cf0
    // 0xc14cc8: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xc14cc8: add             x16, x2, x0, lsl #2
    //     0xc14ccc: ldur            w1, [x16, #0xf]
    // 0xc14cd0: DecompressPointer r1
    //     0xc14cd0: add             x1, x1, HEAP, lsl #32
    // 0xc14cd4: cmp             w1, NULL
    // 0xc14cd8: b.eq            #0xc14ce8
    // 0xc14cdc: add             x1, x0, #1
    // 0xc14ce0: mov             x0, x1
    // 0xc14ce4: b               #0xc14cb4
    // 0xc14ce8: mov             x4, x0
    // 0xc14cec: b               #0xc14cf4
    // 0xc14cf0: r4 = -1
    //     0xc14cf0: movn            x4, #0
    // 0xc14cf4: tbnz            x4, #0x3f, #0xc15104
    // 0xc14cf8: mov             x0, x3
    // 0xc14cfc: mov             x1, x4
    // 0xc14d00: cmp             x1, x0
    // 0xc14d04: b.hs            #0xc151a8
    // 0xc14d08: mov             x1, x2
    // 0xc14d0c: ldur            x0, [fp, #-0x90]
    // 0xc14d10: ArrayStore: r1[r4] = r0  ; List_4
    //     0xc14d10: add             x25, x1, x4, lsl #2
    //     0xc14d14: add             x25, x25, #0xf
    //     0xc14d18: str             w0, [x25]
    //     0xc14d1c: tbz             w0, #0, #0xc14d38
    //     0xc14d20: ldurb           w16, [x1, #-1]
    //     0xc14d24: ldurb           w17, [x0, #-1]
    //     0xc14d28: and             x16, x17, x16, lsr #2
    //     0xc14d2c: tst             x16, HEAP, lsr #32
    //     0xc14d30: b.eq            #0xc14d38
    //     0xc14d34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc14d38: b               #0xc14d50
    // 0xc14d3c: ldur            x3, [fp, #-0x18]
    // 0xc14d40: ldur            x2, [fp, #-0x50]
    // 0xc14d44: b               #0xc14d50
    // 0xc14d48: ldur            x3, [fp, #-0x18]
    // 0xc14d4c: ldur            x2, [fp, #-0x50]
    // 0xc14d50: ldur            x9, [fp, #-0x88]
    // 0xc14d54: mov             x8, x3
    // 0xc14d58: ldur            x3, [fp, #-0x78]
    // 0xc14d5c: ldur            x4, [fp, #-0x70]
    // 0xc14d60: mov             x7, x2
    // 0xc14d64: ldur            x1, [fp, #-0x80]
    // 0xc14d68: ldur            x5, [fp, #-0x68]
    // 0xc14d6c: ldur            x2, [fp, #-0x30]
    // 0xc14d70: b               #0xc14a24
    // 0xc14d74: ldur            x1, [fp, #-0x10]
    // 0xc14d78: ldur            x3, [fp, #-0x18]
    // 0xc14d7c: ldur            x0, [fp, #-0x78]
    // 0xc14d80: ldur            x2, [fp, #-0x50]
    // 0xc14d84: r1 = 3
    //     0xc14d84: movz            x1, #0x3
    // 0xc14d88: r0 = AllocateContext()
    //     0xc14d88: bl              #0xec126c  ; AllocateContextStub
    // 0xc14d8c: mov             x3, x0
    // 0xc14d90: ldur            x2, [fp, #-0x10]
    // 0xc14d94: stur            x3, [fp, #-0x20]
    // 0xc14d98: StoreField: r3->field_f = r2
    //     0xc14d98: stur            w2, [x3, #0xf]
    // 0xc14d9c: ldur            x4, [fp, #-0x78]
    // 0xc14da0: add             x5, x4, #1
    // 0xc14da4: r0 = BoxInt64Instr(r5)
    //     0xc14da4: sbfiz           x0, x5, #1, #0x1f
    //     0xc14da8: cmp             x5, x0, asr #1
    //     0xc14dac: b.eq            #0xc14db8
    //     0xc14db0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc14db4: stur            x5, [x0, #7]
    // 0xc14db8: r1 = 60
    //     0xc14db8: movz            x1, #0x3c
    // 0xc14dbc: branchIfSmi(r0, 0xc14dc8)
    //     0xc14dbc: tbz             w0, #0, #0xc14dc8
    // 0xc14dc0: r1 = LoadClassIdInstr(r0)
    //     0xc14dc0: ldur            x1, [x0, #-1]
    //     0xc14dc4: ubfx            x1, x1, #0xc, #0x14
    // 0xc14dc8: str             x0, [SP]
    // 0xc14dcc: mov             x0, x1
    // 0xc14dd0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc14dd0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc14dd4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc14dd4: movz            x17, #0x2b03
    //     0xc14dd8: add             lr, x0, x17
    //     0xc14ddc: ldr             lr, [x21, lr, lsl #3]
    //     0xc14de0: blr             lr
    // 0xc14de4: ldur            x2, [fp, #-0x20]
    // 0xc14de8: StoreField: r2->field_13 = r0
    //     0xc14de8: stur            w0, [x2, #0x13]
    //     0xc14dec: ldurb           w16, [x2, #-1]
    //     0xc14df0: ldurb           w17, [x0, #-1]
    //     0xc14df4: and             x16, x17, x16, lsr #2
    //     0xc14df8: tst             x16, HEAP, lsr #32
    //     0xc14dfc: b.eq            #0xc14e04
    //     0xc14e00: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc14e04: r1 = Function '<anonymous closure>':.
    //     0xc14e04: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c590] AnonymousClosure: (0xc18550), in [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar (0xc15380)
    //     0xc14e08: ldr             x1, [x1, #0x590]
    // 0xc14e0c: r0 = AllocateClosure()
    //     0xc14e0c: bl              #0xec1630  ; AllocateClosureStub
    // 0xc14e10: r16 = <Null?>
    //     0xc14e10: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc14e14: ldur            lr, [fp, #-0x10]
    // 0xc14e18: stp             lr, x16, [SP, #0x10]
    // 0xc14e1c: r16 = "[34m"
    //     0xc14e1c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c598] "[34m"
    //     0xc14e20: ldr             x16, [x16, #0x598]
    // 0xc14e24: stp             x16, x0, [SP]
    // 0xc14e28: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc14e28: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc14e2c: r0 = _colorize()
    //     0xc14e2c: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc14e30: ldur            x1, [fp, #-0x48]
    // 0xc14e34: r2 = " "
    //     0xc14e34: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc14e38: r0 = _writeString()
    //     0xc14e38: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc14e3c: ldur            x1, [fp, #-0x10]
    // 0xc14e40: ldur            x2, [fp, #-0x30]
    // 0xc14e44: ldur            x3, [fp, #-0x50]
    // 0xc14e48: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xc14e48: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xc14e4c: r0 = _writeMultilineHighlights()
    //     0xc14e4c: bl              #0xc16530  ; [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights
    // 0xc14e50: ldur            x0, [fp, #-0x18]
    // 0xc14e54: cbz             x0, #0xc14e64
    // 0xc14e58: ldur            x1, [fp, #-0x48]
    // 0xc14e5c: r2 = " "
    //     0xc14e5c: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc14e60: r0 = _writeString()
    //     0xc14e60: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc14e64: ldur            x2, [fp, #-0x28]
    // 0xc14e68: CheckStackOverflow
    //     0xc14e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc14e6c: cmp             SP, x16
    //     0xc14e70: b.ls            #0xc151ac
    // 0xc14e74: LoadField: r0 = r2->field_b
    //     0xc14e74: ldur            w0, [x2, #0xb]
    // 0xc14e78: r3 = LoadInt32Instr(r0)
    //     0xc14e78: sbfx            x3, x0, #1, #0x1f
    // 0xc14e7c: cmp             x3, #0
    // 0xc14e80: b.le            #0xc14e9c
    // 0xc14e84: mov             x0, x3
    // 0xc14e88: r1 = 0
    //     0xc14e88: movz            x1, #0
    // 0xc14e8c: cmp             x1, x0
    // 0xc14e90: b.hs            #0xc151b4
    // 0xc14e94: r4 = 0
    //     0xc14e94: movz            x4, #0
    // 0xc14e98: b               #0xc14ea0
    // 0xc14e9c: r4 = -1
    //     0xc14e9c: movn            x4, #0
    // 0xc14ea0: cmn             x4, #1
    // 0xc14ea4: b.ne            #0xc14eb0
    // 0xc14ea8: r3 = Null
    //     0xc14ea8: mov             x3, NULL
    // 0xc14eac: b               #0xc14ed8
    // 0xc14eb0: mov             x0, x3
    // 0xc14eb4: mov             x1, x4
    // 0xc14eb8: cmp             x1, x0
    // 0xc14ebc: b.hs            #0xc151b8
    // 0xc14ec0: LoadField: r0 = r2->field_f
    //     0xc14ec0: ldur            w0, [x2, #0xf]
    // 0xc14ec4: DecompressPointer r0
    //     0xc14ec4: add             x0, x0, HEAP, lsl #32
    // 0xc14ec8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc14ec8: add             x16, x0, x4, lsl #2
    //     0xc14ecc: ldur            w1, [x16, #0xf]
    // 0xc14ed0: DecompressPointer r1
    //     0xc14ed0: add             x1, x1, HEAP, lsl #32
    // 0xc14ed4: mov             x3, x1
    // 0xc14ed8: stur            x3, [fp, #-0x90]
    // 0xc14edc: cmp             w3, NULL
    // 0xc14ee0: b.eq            #0xc1503c
    // 0xc14ee4: ldur            x4, [fp, #-0x78]
    // 0xc14ee8: LoadField: r5 = r3->field_7
    //     0xc14ee8: ldur            w5, [x3, #7]
    // 0xc14eec: DecompressPointer r5
    //     0xc14eec: add             x5, x5, HEAP, lsl #32
    // 0xc14ef0: stur            x5, [fp, #-0x20]
    // 0xc14ef4: r0 = LoadClassIdInstr(r5)
    //     0xc14ef4: ldur            x0, [x5, #-1]
    //     0xc14ef8: ubfx            x0, x0, #0xc, #0x14
    // 0xc14efc: mov             x1, x5
    // 0xc14f00: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc14f00: sub             lr, x0, #0xfff
    //     0xc14f04: ldr             lr, [x21, lr, lsl #3]
    //     0xc14f08: blr             lr
    // 0xc14f0c: r1 = LoadClassIdInstr(r0)
    //     0xc14f0c: ldur            x1, [x0, #-1]
    //     0xc14f10: ubfx            x1, x1, #0xc, #0x14
    // 0xc14f14: mov             x16, x0
    // 0xc14f18: mov             x0, x1
    // 0xc14f1c: mov             x1, x16
    // 0xc14f20: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14f20: sub             lr, x0, #1, lsl #12
    //     0xc14f24: ldr             lr, [x21, lr, lsl #3]
    //     0xc14f28: blr             lr
    // 0xc14f2c: ldur            x2, [fp, #-0x78]
    // 0xc14f30: cmp             x0, x2
    // 0xc14f34: b.ne            #0xc14f7c
    // 0xc14f38: ldur            x3, [fp, #-0x20]
    // 0xc14f3c: r0 = LoadClassIdInstr(r3)
    //     0xc14f3c: ldur            x0, [x3, #-1]
    //     0xc14f40: ubfx            x0, x0, #0xc, #0x14
    // 0xc14f44: mov             x1, x3
    // 0xc14f48: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc14f48: sub             lr, x0, #0xfff
    //     0xc14f4c: ldr             lr, [x21, lr, lsl #3]
    //     0xc14f50: blr             lr
    // 0xc14f54: r1 = LoadClassIdInstr(r0)
    //     0xc14f54: ldur            x1, [x0, #-1]
    //     0xc14f58: ubfx            x1, x1, #0xc, #0x14
    // 0xc14f5c: mov             x16, x0
    // 0xc14f60: mov             x0, x1
    // 0xc14f64: mov             x1, x16
    // 0xc14f68: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc14f68: sub             lr, x0, #0xfff
    //     0xc14f6c: ldr             lr, [x21, lr, lsl #3]
    //     0xc14f70: blr             lr
    // 0xc14f74: mov             x4, x0
    // 0xc14f78: b               #0xc14f80
    // 0xc14f7c: r4 = 0
    //     0xc14f7c: movz            x4, #0
    // 0xc14f80: ldur            x3, [fp, #-0x20]
    // 0xc14f84: ldur            x2, [fp, #-0x78]
    // 0xc14f88: stur            x4, [fp, #-0x60]
    // 0xc14f8c: r0 = LoadClassIdInstr(r3)
    //     0xc14f8c: ldur            x0, [x3, #-1]
    //     0xc14f90: ubfx            x0, x0, #0xc, #0x14
    // 0xc14f94: mov             x1, x3
    // 0xc14f98: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14f98: sub             lr, x0, #1, lsl #12
    //     0xc14f9c: ldr             lr, [x21, lr, lsl #3]
    //     0xc14fa0: blr             lr
    // 0xc14fa4: r1 = LoadClassIdInstr(r0)
    //     0xc14fa4: ldur            x1, [x0, #-1]
    //     0xc14fa8: ubfx            x1, x1, #0xc, #0x14
    // 0xc14fac: mov             x16, x0
    // 0xc14fb0: mov             x0, x1
    // 0xc14fb4: mov             x1, x16
    // 0xc14fb8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14fb8: sub             lr, x0, #1, lsl #12
    //     0xc14fbc: ldr             lr, [x21, lr, lsl #3]
    //     0xc14fc0: blr             lr
    // 0xc14fc4: mov             x1, x0
    // 0xc14fc8: ldur            x0, [fp, #-0x78]
    // 0xc14fcc: cmp             x1, x0
    // 0xc14fd0: b.ne            #0xc15018
    // 0xc14fd4: ldur            x1, [fp, #-0x20]
    // 0xc14fd8: r0 = LoadClassIdInstr(r1)
    //     0xc14fd8: ldur            x0, [x1, #-1]
    //     0xc14fdc: ubfx            x0, x0, #0xc, #0x14
    // 0xc14fe0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc14fe0: sub             lr, x0, #1, lsl #12
    //     0xc14fe4: ldr             lr, [x21, lr, lsl #3]
    //     0xc14fe8: blr             lr
    // 0xc14fec: r1 = LoadClassIdInstr(r0)
    //     0xc14fec: ldur            x1, [x0, #-1]
    //     0xc14ff0: ubfx            x1, x1, #0xc, #0x14
    // 0xc14ff4: mov             x16, x0
    // 0xc14ff8: mov             x0, x1
    // 0xc14ffc: mov             x1, x16
    // 0xc15000: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc15000: sub             lr, x0, #0xfff
    //     0xc15004: ldr             lr, [x21, lr, lsl #3]
    //     0xc15008: blr             lr
    // 0xc1500c: mov             x5, x0
    // 0xc15010: ldur            x2, [fp, #-0x70]
    // 0xc15014: b               #0xc15028
    // 0xc15018: ldur            x2, [fp, #-0x70]
    // 0xc1501c: LoadField: r0 = r2->field_7
    //     0xc1501c: ldur            w0, [x2, #7]
    // 0xc15020: r1 = LoadInt32Instr(r0)
    //     0xc15020: sbfx            x1, x0, #1, #0x1f
    // 0xc15024: mov             x5, x1
    // 0xc15028: ldur            x1, [fp, #-0x10]
    // 0xc1502c: ldur            x3, [fp, #-0x60]
    // 0xc15030: ldur            x6, [fp, #-0x40]
    // 0xc15034: r0 = _writeHighlightedText()
    //     0xc15034: bl              #0xc1636c  ; [package:source_span/src/highlighter.dart] Highlighter::_writeHighlightedText
    // 0xc15038: b               #0xc15048
    // 0xc1503c: ldur            x2, [fp, #-0x70]
    // 0xc15040: ldur            x1, [fp, #-0x10]
    // 0xc15044: r0 = _writeText()
    //     0xc15044: bl              #0xc1627c  ; [package:source_span/src/highlighter.dart] Highlighter::_writeText
    // 0xc15048: ldur            x3, [fp, #-0x90]
    // 0xc1504c: ldur            x1, [fp, #-0x48]
    // 0xc15050: r2 = ""
    //     0xc15050: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc15054: r0 = write()
    //     0xc15054: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc15058: ldur            x1, [fp, #-0x48]
    // 0xc1505c: r2 = "\n"
    //     0xc1505c: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc15060: r0 = _writeString()
    //     0xc15060: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc15064: ldur            x3, [fp, #-0x90]
    // 0xc15068: cmp             w3, NULL
    // 0xc1506c: b.eq            #0xc15080
    // 0xc15070: ldur            x1, [fp, #-0x10]
    // 0xc15074: ldur            x2, [fp, #-0x30]
    // 0xc15078: ldur            x5, [fp, #-0x50]
    // 0xc1507c: r0 = _writeIndicator()
    //     0xc1507c: bl              #0xc155c8  ; [package:source_span/src/highlighter.dart] Highlighter::_writeIndicator
    // 0xc15080: ldur            x0, [fp, #-0x28]
    // 0xc15084: LoadField: r1 = r0->field_b
    //     0xc15084: ldur            w1, [x0, #0xb]
    // 0xc15088: r0 = LoadInt32Instr(r1)
    //     0xc15088: sbfx            x0, x1, #1, #0x1f
    // 0xc1508c: r1 = 0
    //     0xc1508c: movz            x1, #0
    // 0xc15090: CheckStackOverflow
    //     0xc15090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15094: cmp             SP, x16
    //     0xc15098: b.ls            #0xc151bc
    // 0xc1509c: cmp             x1, x0
    // 0xc150a0: b.ge            #0xc150b0
    // 0xc150a4: add             x2, x1, #1
    // 0xc150a8: mov             x1, x2
    // 0xc150ac: b               #0xc15090
    // 0xc150b0: ldur            x0, [fp, #-0x38]
    // 0xc150b4: add             x8, x0, #1
    // 0xc150b8: ldur            x2, [fp, #-0x10]
    // 0xc150bc: ldur            x4, [fp, #-0x48]
    // 0xc150c0: ldur            x6, [fp, #-0x40]
    // 0xc150c4: ldur            x3, [fp, #-0x50]
    // 0xc150c8: b               #0xc147e0
    // 0xc150cc: r0 = upEnd()
    //     0xc150cc: bl              #0xc15578  ; [package:term_glyph/src/generated/top_level.dart] ::upEnd
    // 0xc150d0: r16 = "╵"
    //     0xc150d0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c588] "╵"
    //     0xc150d4: ldr             x16, [x16, #0x588]
    // 0xc150d8: str             x16, [SP]
    // 0xc150dc: ldur            x1, [fp, #-0x10]
    // 0xc150e0: r4 = const [0, 0x2, 0x1, 0x1, end, 0x1, null]
    //     0xc150e0: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c5b0] List(7) [0, 0x2, 0x1, 0x1, "end", 0x1, Null]
    //     0xc150e4: ldr             x4, [x4, #0x5b0]
    // 0xc150e8: r0 = _writeSidebar()
    //     0xc150e8: bl              #0xc15380  ; [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar
    // 0xc150ec: ldur            x16, [fp, #-0x48]
    // 0xc150f0: str             x16, [SP]
    // 0xc150f4: r0 = toString()
    //     0xc150f4: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc150f8: LeaveFrame
    //     0xc150f8: mov             SP, fp
    //     0xc150fc: ldp             fp, lr, [SP], #0x10
    // 0xc15100: ret
    //     0xc15100: ret             
    // 0xc15104: mov             x0, x2
    // 0xc15108: r1 = Null
    //     0xc15108: mov             x1, NULL
    // 0xc1510c: r2 = 4
    //     0xc1510c: movz            x2, #0x4
    // 0xc15110: r0 = AllocateArray()
    //     0xc15110: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc15114: mov             x1, x0
    // 0xc15118: ldur            x0, [fp, #-0x50]
    // 0xc1511c: StoreField: r1->field_f = r0
    //     0xc1511c: stur            w0, [x1, #0xf]
    // 0xc15120: r16 = " contains no null elements."
    //     0xc15120: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c5b8] " contains no null elements."
    //     0xc15124: ldr             x16, [x16, #0x5b8]
    // 0xc15128: StoreField: r1->field_13 = r16
    //     0xc15128: stur            w16, [x1, #0x13]
    // 0xc1512c: str             x1, [SP]
    // 0xc15130: r0 = _interpolate()
    //     0xc15130: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc15134: stur            x0, [fp, #-8]
    // 0xc15138: r0 = ArgumentError()
    //     0xc15138: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc1513c: mov             x1, x0
    // 0xc15140: ldur            x0, [fp, #-8]
    // 0xc15144: ArrayStore: r1[0] = r0  ; List_4
    //     0xc15144: stur            w0, [x1, #0x17]
    // 0xc15148: r0 = false
    //     0xc15148: add             x0, NULL, #0x30  ; false
    // 0xc1514c: StoreField: r1->field_b = r0
    //     0xc1514c: stur            w0, [x1, #0xb]
    // 0xc15150: mov             x0, x1
    // 0xc15154: r0 = Throw()
    //     0xc15154: bl              #0xec04b8  ; ThrowStub
    // 0xc15158: brk             #0
    // 0xc1515c: ldur            x0, [fp, #-0x58]
    // 0xc15160: r0 = ConcurrentModificationError()
    //     0xc15160: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xc15164: mov             x1, x0
    // 0xc15168: ldur            x0, [fp, #-0x58]
    // 0xc1516c: StoreField: r1->field_b = r0
    //     0xc1516c: stur            w0, [x1, #0xb]
    // 0xc15170: mov             x0, x1
    // 0xc15174: r0 = Throw()
    //     0xc15174: bl              #0xec04b8  ; ThrowStub
    // 0xc15178: brk             #0
    // 0xc1517c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1517c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15180: b               #0xc14764
    // 0xc15184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15184: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15188: b               #0xc147f8
    // 0xc1518c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1518c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc15190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15190: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15194: b               #0xc14a38
    // 0xc15198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1519c: b               #0xc14c5c
    // 0xc151a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc151a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc151a4: b               #0xc14cc0
    // 0xc151a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc151a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc151ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc151ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc151b0: b               #0xc14e74
    // 0xc151b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc151b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc151b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc151b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc151bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc151bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc151c0: b               #0xc1509c
  }
  _ _writeSidebar(/* No info */) {
    // ** addr: 0xc15380, size: 0x1f8
    // 0xc15380: EnterFrame
    //     0xc15380: stp             fp, lr, [SP, #-0x10]!
    //     0xc15384: mov             fp, SP
    // 0xc15388: AllocStack(0x48)
    //     0xc15388: sub             SP, SP, #0x48
    // 0xc1538c: SetupParameters(Highlighter this /* r1 => r1, fp-0x20 */, {dynamic end = Null /* r2, fp-0x18 */, dynamic line = Null /* r5, fp-0x10 */, dynamic text = Null /* r0, fp-0x8 */})
    //     0xc1538c: stur            x1, [fp, #-0x20]
    //     0xc15390: ldur            w0, [x4, #0x13]
    //     0xc15394: ldur            w2, [x4, #0x1f]
    //     0xc15398: add             x2, x2, HEAP, lsl #32
    //     0xc1539c: ldr             x16, [PP, #0x540]  ; [pp+0x540] "end"
    //     0xc153a0: cmp             w2, w16
    //     0xc153a4: b.ne            #0xc153c4
    //     0xc153a8: ldur            w2, [x4, #0x23]
    //     0xc153ac: add             x2, x2, HEAP, lsl #32
    //     0xc153b0: sub             w3, w0, w2
    //     0xc153b4: add             x2, fp, w3, sxtw #2
    //     0xc153b8: ldr             x2, [x2, #8]
    //     0xc153bc: movz            x3, #0x1
    //     0xc153c0: b               #0xc153cc
    //     0xc153c4: movz            x3, #0
    //     0xc153c8: mov             x2, NULL
    //     0xc153cc: stur            x2, [fp, #-0x18]
    //     0xc153d0: lsl             x5, x3, #1
    //     0xc153d4: lsl             w6, w5, #1
    //     0xc153d8: add             w7, w6, #8
    //     0xc153dc: add             x16, x4, w7, sxtw #1
    //     0xc153e0: ldur            w8, [x16, #0xf]
    //     0xc153e4: add             x8, x8, HEAP, lsl #32
    //     0xc153e8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd658] "line"
    //     0xc153ec: ldr             x16, [x16, #0x658]
    //     0xc153f0: cmp             w8, w16
    //     0xc153f4: b.ne            #0xc15428
    //     0xc153f8: add             w3, w6, #0xa
    //     0xc153fc: add             x16, x4, w3, sxtw #1
    //     0xc15400: ldur            w6, [x16, #0xf]
    //     0xc15404: add             x6, x6, HEAP, lsl #32
    //     0xc15408: sub             w3, w0, w6
    //     0xc1540c: add             x6, fp, w3, sxtw #2
    //     0xc15410: ldr             x6, [x6, #8]
    //     0xc15414: add             w3, w5, #2
    //     0xc15418: sbfx            x5, x3, #1, #0x1f
    //     0xc1541c: mov             x3, x5
    //     0xc15420: mov             x5, x6
    //     0xc15424: b               #0xc1542c
    //     0xc15428: mov             x5, NULL
    //     0xc1542c: stur            x5, [fp, #-0x10]
    //     0xc15430: lsl             x6, x3, #1
    //     0xc15434: lsl             w3, w6, #1
    //     0xc15438: add             w6, w3, #8
    //     0xc1543c: add             x16, x4, w6, sxtw #1
    //     0xc15440: ldur            w7, [x16, #0xf]
    //     0xc15444: add             x7, x7, HEAP, lsl #32
    //     0xc15448: ldr             x16, [PP, #0x7060]  ; [pp+0x7060] "text"
    //     0xc1544c: cmp             w7, w16
    //     0xc15450: b.ne            #0xc15474
    //     0xc15454: add             w6, w3, #0xa
    //     0xc15458: add             x16, x4, w6, sxtw #1
    //     0xc1545c: ldur            w3, [x16, #0xf]
    //     0xc15460: add             x3, x3, HEAP, lsl #32
    //     0xc15464: sub             w4, w0, w3
    //     0xc15468: add             x0, fp, w4, sxtw #2
    //     0xc1546c: ldr             x0, [x0, #8]
    //     0xc15470: b               #0xc15478
    //     0xc15474: mov             x0, NULL
    //     0xc15478: stur            x0, [fp, #-8]
    // 0xc1547c: CheckStackOverflow
    //     0xc1547c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15480: cmp             SP, x16
    //     0xc15484: b.ls            #0xc15570
    // 0xc15488: r1 = 3
    //     0xc15488: movz            x1, #0x3
    // 0xc1548c: r0 = AllocateContext()
    //     0xc1548c: bl              #0xec126c  ; AllocateContextStub
    // 0xc15490: mov             x3, x0
    // 0xc15494: ldur            x2, [fp, #-0x20]
    // 0xc15498: stur            x3, [fp, #-0x28]
    // 0xc1549c: StoreField: r3->field_f = r2
    //     0xc1549c: stur            w2, [x3, #0xf]
    // 0xc154a0: ldur            x0, [fp, #-8]
    // 0xc154a4: StoreField: r3->field_13 = r0
    //     0xc154a4: stur            w0, [x3, #0x13]
    // 0xc154a8: ldur            x0, [fp, #-0x18]
    // 0xc154ac: ArrayStore: r3[0] = r0  ; List_4
    //     0xc154ac: stur            w0, [x3, #0x17]
    // 0xc154b0: ldur            x0, [fp, #-0x10]
    // 0xc154b4: cmp             w0, NULL
    // 0xc154b8: b.eq            #0xc15530
    // 0xc154bc: r1 = LoadInt32Instr(r0)
    //     0xc154bc: sbfx            x1, x0, #1, #0x1f
    //     0xc154c0: tbz             w0, #0, #0xc154c8
    //     0xc154c4: ldur            x1, [x0, #7]
    // 0xc154c8: add             x4, x1, #1
    // 0xc154cc: r0 = BoxInt64Instr(r4)
    //     0xc154cc: sbfiz           x0, x4, #1, #0x1f
    //     0xc154d0: cmp             x4, x0, asr #1
    //     0xc154d4: b.eq            #0xc154e0
    //     0xc154d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc154dc: stur            x4, [x0, #7]
    // 0xc154e0: r1 = 60
    //     0xc154e0: movz            x1, #0x3c
    // 0xc154e4: branchIfSmi(r0, 0xc154f0)
    //     0xc154e4: tbz             w0, #0, #0xc154f0
    // 0xc154e8: r1 = LoadClassIdInstr(r0)
    //     0xc154e8: ldur            x1, [x0, #-1]
    //     0xc154ec: ubfx            x1, x1, #0xc, #0x14
    // 0xc154f0: str             x0, [SP]
    // 0xc154f4: mov             x0, x1
    // 0xc154f8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc154f8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc154fc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc154fc: movz            x17, #0x2b03
    //     0xc15500: add             lr, x0, x17
    //     0xc15504: ldr             lr, [x21, lr, lsl #3]
    //     0xc15508: blr             lr
    // 0xc1550c: ldur            x2, [fp, #-0x28]
    // 0xc15510: StoreField: r2->field_13 = r0
    //     0xc15510: stur            w0, [x2, #0x13]
    //     0xc15514: ldurb           w16, [x2, #-1]
    //     0xc15518: ldurb           w17, [x0, #-1]
    //     0xc1551c: and             x16, x17, x16, lsr #2
    //     0xc15520: tst             x16, HEAP, lsr #32
    //     0xc15524: b.eq            #0xc1552c
    //     0xc15528: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc1552c: b               #0xc15534
    // 0xc15530: mov             x2, x3
    // 0xc15534: r1 = Function '<anonymous closure>':.
    //     0xc15534: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c590] AnonymousClosure: (0xc18550), in [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar (0xc15380)
    //     0xc15538: ldr             x1, [x1, #0x590]
    // 0xc1553c: r0 = AllocateClosure()
    //     0xc1553c: bl              #0xec1630  ; AllocateClosureStub
    // 0xc15540: r16 = <Null?>
    //     0xc15540: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc15544: ldur            lr, [fp, #-0x20]
    // 0xc15548: stp             lr, x16, [SP, #0x10]
    // 0xc1554c: r16 = "[34m"
    //     0xc1554c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c598] "[34m"
    //     0xc15550: ldr             x16, [x16, #0x598]
    // 0xc15554: stp             x16, x0, [SP]
    // 0xc15558: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc15558: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc1555c: r0 = _colorize()
    //     0xc1555c: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc15560: r0 = Null
    //     0xc15560: mov             x0, NULL
    // 0xc15564: LeaveFrame
    //     0xc15564: mov             SP, fp
    //     0xc15568: ldp             fp, lr, [SP], #0x10
    // 0xc1556c: ret
    //     0xc1556c: ret             
    // 0xc15570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15570: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15574: b               #0xc15488
  }
  _ _writeIndicator(/* No info */) {
    // ** addr: 0xc155c8, size: 0x45c
    // 0xc155c8: EnterFrame
    //     0xc155c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc155cc: mov             fp, SP
    // 0xc155d0: AllocStack(0x48)
    //     0xc155d0: sub             SP, SP, #0x48
    // 0xc155d4: SetupParameters(Highlighter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */, dynamic _ /* r5 => r3, fp-0x20 */)
    //     0xc155d4: mov             x0, x3
    //     0xc155d8: stur            x3, [fp, #-0x18]
    //     0xc155dc: mov             x3, x5
    //     0xc155e0: stur            x1, [fp, #-8]
    //     0xc155e4: stur            x2, [fp, #-0x10]
    //     0xc155e8: stur            x5, [fp, #-0x20]
    // 0xc155ec: CheckStackOverflow
    //     0xc155ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc155f0: cmp             SP, x16
    //     0xc155f4: b.ls            #0xc15a1c
    // 0xc155f8: r1 = 4
    //     0xc155f8: movz            x1, #0x4
    // 0xc155fc: r0 = AllocateContext()
    //     0xc155fc: bl              #0xec126c  ; AllocateContextStub
    // 0xc15600: mov             x2, x0
    // 0xc15604: ldur            x0, [fp, #-8]
    // 0xc15608: stur            x2, [fp, #-0x28]
    // 0xc1560c: StoreField: r2->field_f = r0
    //     0xc1560c: stur            w0, [x2, #0xf]
    // 0xc15610: ldur            x1, [fp, #-0x10]
    // 0xc15614: StoreField: r2->field_13 = r1
    //     0xc15614: stur            w1, [x2, #0x13]
    // 0xc15618: ldur            x1, [fp, #-0x18]
    // 0xc1561c: ArrayStore: r2[0] = r1  ; List_4
    //     0xc1561c: stur            w1, [x2, #0x17]
    // 0xc15620: LoadField: r3 = r0->field_b
    //     0xc15620: ldur            w3, [x0, #0xb]
    // 0xc15624: DecompressPointer r3
    //     0xc15624: add             x3, x3, HEAP, lsl #32
    // 0xc15628: stur            x3, [fp, #-0x10]
    // 0xc1562c: LoadField: r4 = r1->field_7
    //     0xc1562c: ldur            w4, [x1, #7]
    // 0xc15630: DecompressPointer r4
    //     0xc15630: add             x4, x4, HEAP, lsl #32
    // 0xc15634: mov             x1, x4
    // 0xc15638: r0 = isMultiline()
    //     0xc15638: bl              #0xc152bc  ; [package:source_span/src/utils.dart] ::isMultiline
    // 0xc1563c: tbz             w0, #4, #0xc15700
    // 0xc15640: ldur            x0, [fp, #-8]
    // 0xc15644: ldur            x3, [fp, #-0x20]
    // 0xc15648: ldur            x2, [fp, #-0x28]
    // 0xc1564c: mov             x1, x0
    // 0xc15650: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc15650: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc15654: r0 = _writeSidebar()
    //     0xc15654: bl              #0xc15380  ; [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar
    // 0xc15658: ldur            x0, [fp, #-8]
    // 0xc1565c: LoadField: r3 = r0->field_23
    //     0xc1565c: ldur            w3, [x0, #0x23]
    // 0xc15660: DecompressPointer r3
    //     0xc15660: add             x3, x3, HEAP, lsl #32
    // 0xc15664: mov             x1, x3
    // 0xc15668: stur            x3, [fp, #-0x18]
    // 0xc1566c: r2 = " "
    //     0xc1566c: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc15670: r0 = write()
    //     0xc15670: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc15674: ldur            x0, [fp, #-0x28]
    // 0xc15678: LoadField: r2 = r0->field_13
    //     0xc15678: ldur            w2, [x0, #0x13]
    // 0xc1567c: DecompressPointer r2
    //     0xc1567c: add             x2, x2, HEAP, lsl #32
    // 0xc15680: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc15680: ldur            w1, [x0, #0x17]
    // 0xc15684: DecompressPointer r1
    //     0xc15684: add             x1, x1, HEAP, lsl #32
    // 0xc15688: str             x1, [SP]
    // 0xc1568c: ldur            x1, [fp, #-8]
    // 0xc15690: ldur            x3, [fp, #-0x20]
    // 0xc15694: r4 = const [0, 0x4, 0x1, 0x3, current, 0x3, null]
    //     0xc15694: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c5c8] List(7) [0, 0x4, 0x1, 0x3, "current", 0x3, Null]
    //     0xc15698: ldr             x4, [x4, #0x5c8]
    // 0xc1569c: r0 = _writeMultilineHighlights()
    //     0xc1569c: bl              #0xc16530  ; [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights
    // 0xc156a0: ldur            x2, [fp, #-0x20]
    // 0xc156a4: LoadField: r0 = r2->field_b
    //     0xc156a4: ldur            w0, [x2, #0xb]
    // 0xc156a8: cbz             w0, #0xc156b8
    // 0xc156ac: ldur            x1, [fp, #-0x18]
    // 0xc156b0: r2 = " "
    //     0xc156b0: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc156b4: r0 = write()
    //     0xc156b4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc156b8: ldur            x0, [fp, #-0x28]
    // 0xc156bc: mov             x2, x0
    // 0xc156c0: r1 = Function '<anonymous closure>':.
    //     0xc156c0: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5d0] AnonymousClosure: (0xc15fdc), in [package:source_span/src/highlighter.dart] Highlighter::_writeIndicator (0xc155c8)
    //     0xc156c4: ldr             x1, [x1, #0x5d0]
    // 0xc156c8: r0 = AllocateClosure()
    //     0xc156c8: bl              #0xec1630  ; AllocateClosureStub
    // 0xc156cc: r16 = <int>
    //     0xc156cc: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc156d0: ldur            lr, [fp, #-8]
    // 0xc156d4: stp             lr, x16, [SP, #0x10]
    // 0xc156d8: ldur            x16, [fp, #-0x10]
    // 0xc156dc: stp             x16, x0, [SP]
    // 0xc156e0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc156e0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc156e4: r0 = _colorize()
    //     0xc156e4: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc156e8: ldur            x3, [fp, #-0x28]
    // 0xc156ec: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xc156ec: ldur            w2, [x3, #0x17]
    // 0xc156f0: DecompressPointer r2
    //     0xc156f0: add             x2, x2, HEAP, lsl #32
    // 0xc156f4: ldur            x1, [fp, #-8]
    // 0xc156f8: r0 = _writeLabel()
    //     0xc156f8: bl              #0xc15b08  ; [package:source_span/src/highlighter.dart] Highlighter::_writeLabel
    // 0xc156fc: b               #0xc15a0c
    // 0xc15700: ldur            x2, [fp, #-0x20]
    // 0xc15704: ldur            x3, [fp, #-0x28]
    // 0xc15708: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xc15708: ldur            w0, [x3, #0x17]
    // 0xc1570c: DecompressPointer r0
    //     0xc1570c: add             x0, x0, HEAP, lsl #32
    // 0xc15710: LoadField: r1 = r0->field_7
    //     0xc15710: ldur            w1, [x0, #7]
    // 0xc15714: DecompressPointer r1
    //     0xc15714: add             x1, x1, HEAP, lsl #32
    // 0xc15718: r0 = LoadClassIdInstr(r1)
    //     0xc15718: ldur            x0, [x1, #-1]
    //     0xc1571c: ubfx            x0, x0, #0xc, #0x14
    // 0xc15720: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc15720: sub             lr, x0, #0xfff
    //     0xc15724: ldr             lr, [x21, lr, lsl #3]
    //     0xc15728: blr             lr
    // 0xc1572c: r1 = LoadClassIdInstr(r0)
    //     0xc1572c: ldur            x1, [x0, #-1]
    //     0xc15730: ubfx            x1, x1, #0xc, #0x14
    // 0xc15734: mov             x16, x0
    // 0xc15738: mov             x0, x1
    // 0xc1573c: mov             x1, x16
    // 0xc15740: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc15740: sub             lr, x0, #1, lsl #12
    //     0xc15744: ldr             lr, [x21, lr, lsl #3]
    //     0xc15748: blr             lr
    // 0xc1574c: mov             x1, x0
    // 0xc15750: ldur            x0, [fp, #-0x28]
    // 0xc15754: LoadField: r2 = r0->field_13
    //     0xc15754: ldur            w2, [x0, #0x13]
    // 0xc15758: DecompressPointer r2
    //     0xc15758: add             x2, x2, HEAP, lsl #32
    // 0xc1575c: LoadField: r3 = r2->field_b
    //     0xc1575c: ldur            x3, [x2, #0xb]
    // 0xc15760: cmp             x1, x3
    // 0xc15764: b.ne            #0xc15848
    // 0xc15768: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc15768: ldur            w2, [x0, #0x17]
    // 0xc1576c: DecompressPointer r2
    //     0xc1576c: add             x2, x2, HEAP, lsl #32
    // 0xc15770: ldur            x1, [fp, #-0x20]
    // 0xc15774: r0 = contains()
    //     0xc15774: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xc15778: tbnz            w0, #4, #0xc1578c
    // 0xc1577c: r0 = Null
    //     0xc1577c: mov             x0, NULL
    // 0xc15780: LeaveFrame
    //     0xc15780: mov             SP, fp
    //     0xc15784: ldp             fp, lr, [SP], #0x10
    // 0xc15788: ret
    //     0xc15788: ret             
    // 0xc1578c: ldur            x1, [fp, #-8]
    // 0xc15790: ldur            x2, [fp, #-0x28]
    // 0xc15794: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc15794: ldur            w0, [x2, #0x17]
    // 0xc15798: DecompressPointer r0
    //     0xc15798: add             x0, x0, HEAP, lsl #32
    // 0xc1579c: r16 = <_Highlight>
    //     0xc1579c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c5d8] TypeArguments: <_Highlight>
    //     0xc157a0: ldr             x16, [x16, #0x5d8]
    // 0xc157a4: ldur            lr, [fp, #-0x20]
    // 0xc157a8: stp             lr, x16, [SP, #8]
    // 0xc157ac: str             x0, [SP]
    // 0xc157b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc157b0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc157b4: r0 = replaceFirstNull()
    //     0xc157b4: bl              #0xc151c4  ; [package:source_span/src/utils.dart] ::replaceFirstNull
    // 0xc157b8: ldur            x1, [fp, #-8]
    // 0xc157bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc157bc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc157c0: r0 = _writeSidebar()
    //     0xc157c0: bl              #0xc15380  ; [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar
    // 0xc157c4: ldur            x0, [fp, #-8]
    // 0xc157c8: LoadField: r3 = r0->field_23
    //     0xc157c8: ldur            w3, [x0, #0x23]
    // 0xc157cc: DecompressPointer r3
    //     0xc157cc: add             x3, x3, HEAP, lsl #32
    // 0xc157d0: mov             x1, x3
    // 0xc157d4: stur            x3, [fp, #-0x18]
    // 0xc157d8: r2 = " "
    //     0xc157d8: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc157dc: r0 = write()
    //     0xc157dc: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc157e0: ldur            x0, [fp, #-0x28]
    // 0xc157e4: LoadField: r2 = r0->field_13
    //     0xc157e4: ldur            w2, [x0, #0x13]
    // 0xc157e8: DecompressPointer r2
    //     0xc157e8: add             x2, x2, HEAP, lsl #32
    // 0xc157ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc157ec: ldur            w1, [x0, #0x17]
    // 0xc157f0: DecompressPointer r1
    //     0xc157f0: add             x1, x1, HEAP, lsl #32
    // 0xc157f4: str             x1, [SP]
    // 0xc157f8: ldur            x1, [fp, #-8]
    // 0xc157fc: ldur            x3, [fp, #-0x20]
    // 0xc15800: r4 = const [0, 0x4, 0x1, 0x3, current, 0x3, null]
    //     0xc15800: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c5c8] List(7) [0, 0x4, 0x1, 0x3, "current", 0x3, Null]
    //     0xc15804: ldr             x4, [x4, #0x5c8]
    // 0xc15808: r0 = _writeMultilineHighlights()
    //     0xc15808: bl              #0xc16530  ; [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights
    // 0xc1580c: ldur            x2, [fp, #-0x28]
    // 0xc15810: r1 = Function '<anonymous closure>':.
    //     0xc15810: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5e0] AnonymousClosure: (0xc15f10), in [package:source_span/src/highlighter.dart] Highlighter::_writeIndicator (0xc155c8)
    //     0xc15814: ldr             x1, [x1, #0x5e0]
    // 0xc15818: r0 = AllocateClosure()
    //     0xc15818: bl              #0xec1630  ; AllocateClosureStub
    // 0xc1581c: r16 = <void?>
    //     0xc1581c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xc15820: ldur            lr, [fp, #-8]
    // 0xc15824: stp             lr, x16, [SP, #0x10]
    // 0xc15828: ldur            x16, [fp, #-0x10]
    // 0xc1582c: stp             x16, x0, [SP]
    // 0xc15830: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc15830: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc15834: r0 = _colorize()
    //     0xc15834: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc15838: ldur            x1, [fp, #-0x18]
    // 0xc1583c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc1583c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc15840: r0 = writeln()
    //     0xc15840: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0xc15844: b               #0xc15a0c
    // 0xc15848: mov             x2, x0
    // 0xc1584c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc1584c: ldur            w0, [x2, #0x17]
    // 0xc15850: DecompressPointer r0
    //     0xc15850: add             x0, x0, HEAP, lsl #32
    // 0xc15854: LoadField: r1 = r0->field_7
    //     0xc15854: ldur            w1, [x0, #7]
    // 0xc15858: DecompressPointer r1
    //     0xc15858: add             x1, x1, HEAP, lsl #32
    // 0xc1585c: r0 = LoadClassIdInstr(r1)
    //     0xc1585c: ldur            x0, [x1, #-1]
    //     0xc15860: ubfx            x0, x0, #0xc, #0x14
    // 0xc15864: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc15864: sub             lr, x0, #1, lsl #12
    //     0xc15868: ldr             lr, [x21, lr, lsl #3]
    //     0xc1586c: blr             lr
    // 0xc15870: r1 = LoadClassIdInstr(r0)
    //     0xc15870: ldur            x1, [x0, #-1]
    //     0xc15874: ubfx            x1, x1, #0xc, #0x14
    // 0xc15878: mov             x16, x0
    // 0xc1587c: mov             x0, x1
    // 0xc15880: mov             x1, x16
    // 0xc15884: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc15884: sub             lr, x0, #1, lsl #12
    //     0xc15888: ldr             lr, [x21, lr, lsl #3]
    //     0xc1588c: blr             lr
    // 0xc15890: ldur            x2, [fp, #-0x28]
    // 0xc15894: LoadField: r1 = r2->field_13
    //     0xc15894: ldur            w1, [x2, #0x13]
    // 0xc15898: DecompressPointer r1
    //     0xc15898: add             x1, x1, HEAP, lsl #32
    // 0xc1589c: LoadField: r3 = r1->field_b
    //     0xc1589c: ldur            x3, [x1, #0xb]
    // 0xc158a0: cmp             x0, x3
    // 0xc158a4: b.ne            #0xc15a0c
    // 0xc158a8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc158a8: ldur            w0, [x2, #0x17]
    // 0xc158ac: DecompressPointer r0
    //     0xc158ac: add             x0, x0, HEAP, lsl #32
    // 0xc158b0: LoadField: r1 = r0->field_7
    //     0xc158b0: ldur            w1, [x0, #7]
    // 0xc158b4: DecompressPointer r1
    //     0xc158b4: add             x1, x1, HEAP, lsl #32
    // 0xc158b8: r0 = LoadClassIdInstr(r1)
    //     0xc158b8: ldur            x0, [x1, #-1]
    //     0xc158bc: ubfx            x0, x0, #0xc, #0x14
    // 0xc158c0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc158c0: sub             lr, x0, #1, lsl #12
    //     0xc158c4: ldr             lr, [x21, lr, lsl #3]
    //     0xc158c8: blr             lr
    // 0xc158cc: r1 = LoadClassIdInstr(r0)
    //     0xc158cc: ldur            x1, [x0, #-1]
    //     0xc158d0: ubfx            x1, x1, #0xc, #0x14
    // 0xc158d4: mov             x16, x0
    // 0xc158d8: mov             x0, x1
    // 0xc158dc: mov             x1, x16
    // 0xc158e0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc158e0: sub             lr, x0, #0xfff
    //     0xc158e4: ldr             lr, [x21, lr, lsl #3]
    //     0xc158e8: blr             lr
    // 0xc158ec: ldur            x2, [fp, #-0x28]
    // 0xc158f0: LoadField: r1 = r2->field_13
    //     0xc158f0: ldur            w1, [x2, #0x13]
    // 0xc158f4: DecompressPointer r1
    //     0xc158f4: add             x1, x1, HEAP, lsl #32
    // 0xc158f8: LoadField: r3 = r1->field_7
    //     0xc158f8: ldur            w3, [x1, #7]
    // 0xc158fc: DecompressPointer r3
    //     0xc158fc: add             x3, x3, HEAP, lsl #32
    // 0xc15900: LoadField: r1 = r3->field_7
    //     0xc15900: ldur            w1, [x3, #7]
    // 0xc15904: r3 = LoadInt32Instr(r1)
    //     0xc15904: sbfx            x3, x1, #1, #0x1f
    // 0xc15908: cmp             x0, x3
    // 0xc1590c: r16 = true
    //     0xc1590c: add             x16, NULL, #0x20  ; true
    // 0xc15910: r17 = false
    //     0xc15910: add             x17, NULL, #0x30  ; false
    // 0xc15914: csel            x1, x16, x17, eq
    // 0xc15918: StoreField: r2->field_1b = r1
    //     0xc15918: stur            w1, [x2, #0x1b]
    // 0xc1591c: tbnz            w1, #4, #0xc15954
    // 0xc15920: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc15920: ldur            w0, [x2, #0x17]
    // 0xc15924: DecompressPointer r0
    //     0xc15924: add             x0, x0, HEAP, lsl #32
    // 0xc15928: r16 = <_Highlight>
    //     0xc15928: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c5d8] TypeArguments: <_Highlight>
    //     0xc1592c: ldr             x16, [x16, #0x5d8]
    // 0xc15930: ldur            lr, [fp, #-0x20]
    // 0xc15934: stp             lr, x16, [SP, #8]
    // 0xc15938: str             x0, [SP]
    // 0xc1593c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc1593c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc15940: r0 = replaceWithNull()
    //     0xc15940: bl              #0xc15a24  ; [package:source_span/src/utils.dart] ::replaceWithNull
    // 0xc15944: r0 = Null
    //     0xc15944: mov             x0, NULL
    // 0xc15948: LeaveFrame
    //     0xc15948: mov             SP, fp
    //     0xc1594c: ldp             fp, lr, [SP], #0x10
    // 0xc15950: ret
    //     0xc15950: ret             
    // 0xc15954: ldur            x0, [fp, #-8]
    // 0xc15958: mov             x1, x0
    // 0xc1595c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc1595c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc15960: r0 = _writeSidebar()
    //     0xc15960: bl              #0xc15380  ; [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar
    // 0xc15964: ldur            x0, [fp, #-8]
    // 0xc15968: LoadField: r1 = r0->field_23
    //     0xc15968: ldur            w1, [x0, #0x23]
    // 0xc1596c: DecompressPointer r1
    //     0xc1596c: add             x1, x1, HEAP, lsl #32
    // 0xc15970: r2 = " "
    //     0xc15970: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc15974: r0 = write()
    //     0xc15974: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc15978: ldur            x0, [fp, #-0x28]
    // 0xc1597c: LoadField: r2 = r0->field_13
    //     0xc1597c: ldur            w2, [x0, #0x13]
    // 0xc15980: DecompressPointer r2
    //     0xc15980: add             x2, x2, HEAP, lsl #32
    // 0xc15984: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc15984: ldur            w1, [x0, #0x17]
    // 0xc15988: DecompressPointer r1
    //     0xc15988: add             x1, x1, HEAP, lsl #32
    // 0xc1598c: str             x1, [SP]
    // 0xc15990: ldur            x1, [fp, #-8]
    // 0xc15994: ldur            x3, [fp, #-0x20]
    // 0xc15998: r4 = const [0, 0x4, 0x1, 0x3, current, 0x3, null]
    //     0xc15998: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c5c8] List(7) [0, 0x4, 0x1, 0x3, "current", 0x3, Null]
    //     0xc1599c: ldr             x4, [x4, #0x5c8]
    // 0xc159a0: r0 = _writeMultilineHighlights()
    //     0xc159a0: bl              #0xc16530  ; [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights
    // 0xc159a4: ldur            x2, [fp, #-0x28]
    // 0xc159a8: r1 = Function '<anonymous closure>':.
    //     0xc159a8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5e8] AnonymousClosure: (0xc15b48), in [package:source_span/src/highlighter.dart] Highlighter::_writeIndicator (0xc155c8)
    //     0xc159ac: ldr             x1, [x1, #0x5e8]
    // 0xc159b0: r0 = AllocateClosure()
    //     0xc159b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xc159b4: r16 = <int>
    //     0xc159b4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc159b8: ldur            lr, [fp, #-8]
    // 0xc159bc: stp             lr, x16, [SP, #0x10]
    // 0xc159c0: ldur            x16, [fp, #-0x10]
    // 0xc159c4: stp             x16, x0, [SP]
    // 0xc159c8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc159c8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc159cc: r0 = _colorize()
    //     0xc159cc: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc159d0: ldur            x0, [fp, #-0x28]
    // 0xc159d4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc159d4: ldur            w2, [x0, #0x17]
    // 0xc159d8: DecompressPointer r2
    //     0xc159d8: add             x2, x2, HEAP, lsl #32
    // 0xc159dc: ldur            x1, [fp, #-8]
    // 0xc159e0: r0 = _writeLabel()
    //     0xc159e0: bl              #0xc15b08  ; [package:source_span/src/highlighter.dart] Highlighter::_writeLabel
    // 0xc159e4: ldur            x0, [fp, #-0x28]
    // 0xc159e8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc159e8: ldur            w1, [x0, #0x17]
    // 0xc159ec: DecompressPointer r1
    //     0xc159ec: add             x1, x1, HEAP, lsl #32
    // 0xc159f0: r16 = <_Highlight>
    //     0xc159f0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c5d8] TypeArguments: <_Highlight>
    //     0xc159f4: ldr             x16, [x16, #0x5d8]
    // 0xc159f8: ldur            lr, [fp, #-0x20]
    // 0xc159fc: stp             lr, x16, [SP, #8]
    // 0xc15a00: str             x1, [SP]
    // 0xc15a04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc15a04: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc15a08: r0 = replaceWithNull()
    //     0xc15a08: bl              #0xc15a24  ; [package:source_span/src/utils.dart] ::replaceWithNull
    // 0xc15a0c: r0 = Null
    //     0xc15a0c: mov             x0, NULL
    // 0xc15a10: LeaveFrame
    //     0xc15a10: mov             SP, fp
    //     0xc15a14: ldp             fp, lr, [SP], #0x10
    // 0xc15a18: ret
    //     0xc15a18: ret             
    // 0xc15a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15a1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15a20: b               #0xc155f8
  }
  _ _writeLabel(/* No info */) {
    // ** addr: 0xc15b08, size: 0x40
    // 0xc15b08: EnterFrame
    //     0xc15b08: stp             fp, lr, [SP, #-0x10]!
    //     0xc15b0c: mov             fp, SP
    // 0xc15b10: CheckStackOverflow
    //     0xc15b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15b14: cmp             SP, x16
    //     0xc15b18: b.ls            #0xc15b40
    // 0xc15b1c: LoadField: r0 = r1->field_23
    //     0xc15b1c: ldur            w0, [x1, #0x23]
    // 0xc15b20: DecompressPointer r0
    //     0xc15b20: add             x0, x0, HEAP, lsl #32
    // 0xc15b24: mov             x1, x0
    // 0xc15b28: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc15b28: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc15b2c: r0 = writeln()
    //     0xc15b2c: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0xc15b30: r0 = Null
    //     0xc15b30: mov             x0, NULL
    // 0xc15b34: LeaveFrame
    //     0xc15b34: mov             SP, fp
    //     0xc15b38: ldp             fp, lr, [SP], #0x10
    // 0xc15b3c: ret
    //     0xc15b3c: ret             
    // 0xc15b40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15b40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15b44: b               #0xc15b1c
  }
  [closure] int <anonymous closure>(dynamic) {
    // ** addr: 0xc15b48, size: 0x1ac
    // 0xc15b48: EnterFrame
    //     0xc15b48: stp             fp, lr, [SP, #-0x10]!
    //     0xc15b4c: mov             fp, SP
    // 0xc15b50: AllocStack(0x30)
    //     0xc15b50: sub             SP, SP, #0x30
    // 0xc15b54: SetupParameters()
    //     0xc15b54: ldr             x0, [fp, #0x10]
    //     0xc15b58: ldur            w1, [x0, #0x17]
    //     0xc15b5c: add             x1, x1, HEAP, lsl #32
    //     0xc15b60: stur            x1, [fp, #-0x18]
    // 0xc15b64: CheckStackOverflow
    //     0xc15b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15b68: cmp             SP, x16
    //     0xc15b6c: b.ls            #0xc15cec
    // 0xc15b70: LoadField: r2 = r1->field_f
    //     0xc15b70: ldur            w2, [x1, #0xf]
    // 0xc15b74: DecompressPointer r2
    //     0xc15b74: add             x2, x2, HEAP, lsl #32
    // 0xc15b78: stur            x2, [fp, #-0x20]
    // 0xc15b7c: LoadField: r0 = r2->field_23
    //     0xc15b7c: ldur            w0, [x2, #0x23]
    // 0xc15b80: DecompressPointer r0
    //     0xc15b80: add             x0, x0, HEAP, lsl #32
    // 0xc15b84: stur            x0, [fp, #-0x10]
    // 0xc15b88: LoadField: r3 = r0->field_b
    //     0xc15b88: ldur            x3, [x0, #0xb]
    // 0xc15b8c: LoadField: r4 = r0->field_27
    //     0xc15b8c: ldur            x4, [x0, #0x27]
    // 0xc15b90: add             x5, x3, x4
    // 0xc15b94: stur            x5, [fp, #-8]
    // 0xc15b98: LoadField: r3 = r1->field_1b
    //     0xc15b98: ldur            w3, [x1, #0x1b]
    // 0xc15b9c: DecompressPointer r3
    //     0xc15b9c: add             x3, x3, HEAP, lsl #32
    // 0xc15ba0: tbnz            w3, #4, #0xc15bc8
    // 0xc15ba4: r0 = horizontalLine()
    //     0xc15ba4: bl              #0xc15ec0  ; [package:term_glyph/src/generated/top_level.dart] ::horizontalLine
    // 0xc15ba8: r1 = "─"
    //     0xc15ba8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5f0] "─"
    //     0xc15bac: ldr             x1, [x1, #0x5f0]
    // 0xc15bb0: r2 = 3
    //     0xc15bb0: movz            x2, #0x3
    // 0xc15bb4: r0 = *()
    //     0xc15bb4: bl              #0xebce0c  ; [dart:core] _TwoByteString::*
    // 0xc15bb8: ldur            x1, [fp, #-0x10]
    // 0xc15bbc: mov             x2, x0
    // 0xc15bc0: r0 = write()
    //     0xc15bc0: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc15bc4: b               #0xc15ca4
    // 0xc15bc8: mov             x3, x1
    // 0xc15bcc: LoadField: r4 = r3->field_13
    //     0xc15bcc: ldur            w4, [x3, #0x13]
    // 0xc15bd0: DecompressPointer r4
    //     0xc15bd0: add             x4, x4, HEAP, lsl #32
    // 0xc15bd4: stur            x4, [fp, #-0x10]
    // 0xc15bd8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xc15bd8: ldur            w0, [x3, #0x17]
    // 0xc15bdc: DecompressPointer r0
    //     0xc15bdc: add             x0, x0, HEAP, lsl #32
    // 0xc15be0: LoadField: r1 = r0->field_7
    //     0xc15be0: ldur            w1, [x0, #7]
    // 0xc15be4: DecompressPointer r1
    //     0xc15be4: add             x1, x1, HEAP, lsl #32
    // 0xc15be8: r0 = LoadClassIdInstr(r1)
    //     0xc15be8: ldur            x0, [x1, #-1]
    //     0xc15bec: ubfx            x0, x0, #0xc, #0x14
    // 0xc15bf0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc15bf0: sub             lr, x0, #1, lsl #12
    //     0xc15bf4: ldr             lr, [x21, lr, lsl #3]
    //     0xc15bf8: blr             lr
    // 0xc15bfc: r1 = LoadClassIdInstr(r0)
    //     0xc15bfc: ldur            x1, [x0, #-1]
    //     0xc15c00: ubfx            x1, x1, #0xc, #0x14
    // 0xc15c04: mov             x16, x0
    // 0xc15c08: mov             x0, x1
    // 0xc15c0c: mov             x1, x16
    // 0xc15c10: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc15c10: sub             lr, x0, #0xfff
    //     0xc15c14: ldr             lr, [x21, lr, lsl #3]
    //     0xc15c18: blr             lr
    // 0xc15c1c: sub             x2, x0, #1
    // 0xc15c20: stur            x2, [fp, #-0x28]
    // 0xc15c24: cmp             x2, #0
    // 0xc15c28: b.le            #0xc15c34
    // 0xc15c2c: mov             x3, x2
    // 0xc15c30: b               #0xc15c88
    // 0xc15c34: tbz             x2, #0x3f, #0xc15c40
    // 0xc15c38: r3 = 0
    //     0xc15c38: movz            x3, #0
    // 0xc15c3c: b               #0xc15c88
    // 0xc15c40: r0 = BoxInt64Instr(r2)
    //     0xc15c40: sbfiz           x0, x2, #1, #0x1f
    //     0xc15c44: cmp             x2, x0, asr #1
    //     0xc15c48: b.eq            #0xc15c54
    //     0xc15c4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc15c50: stur            x2, [x0, #7]
    // 0xc15c54: r1 = 60
    //     0xc15c54: movz            x1, #0x3c
    // 0xc15c58: branchIfSmi(r0, 0xc15c64)
    //     0xc15c58: tbz             w0, #0, #0xc15c64
    // 0xc15c5c: r1 = LoadClassIdInstr(r0)
    //     0xc15c5c: ldur            x1, [x0, #-1]
    //     0xc15c60: ubfx            x1, x1, #0xc, #0x14
    // 0xc15c64: str             x0, [SP]
    // 0xc15c68: mov             x0, x1
    // 0xc15c6c: r0 = GDT[cid_x0 + -0xfb8]()
    //     0xc15c6c: sub             lr, x0, #0xfb8
    //     0xc15c70: ldr             lr, [x21, lr, lsl #3]
    //     0xc15c74: blr             lr
    // 0xc15c78: tbnz            w0, #4, #0xc15c84
    // 0xc15c7c: r3 = 0
    //     0xc15c7c: movz            x3, #0
    // 0xc15c80: b               #0xc15c88
    // 0xc15c84: ldur            x3, [fp, #-0x28]
    // 0xc15c88: r16 = false
    //     0xc15c88: add             x16, NULL, #0x30  ; false
    // 0xc15c8c: str             x16, [SP]
    // 0xc15c90: ldur            x1, [fp, #-0x20]
    // 0xc15c94: ldur            x2, [fp, #-0x10]
    // 0xc15c98: r4 = const [0, 0x4, 0x1, 0x3, beginning, 0x3, null]
    //     0xc15c98: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c5f8] List(7) [0, 0x4, 0x1, 0x3, "beginning", 0x3, Null]
    //     0xc15c9c: ldr             x4, [x4, #0x5f8]
    // 0xc15ca0: r0 = _writeArrow()
    //     0xc15ca0: bl              #0xc15cf4  ; [package:source_span/src/highlighter.dart] Highlighter::_writeArrow
    // 0xc15ca4: ldur            x2, [fp, #-0x18]
    // 0xc15ca8: ldur            x3, [fp, #-8]
    // 0xc15cac: LoadField: r4 = r2->field_f
    //     0xc15cac: ldur            w4, [x2, #0xf]
    // 0xc15cb0: DecompressPointer r4
    //     0xc15cb0: add             x4, x4, HEAP, lsl #32
    // 0xc15cb4: LoadField: r2 = r4->field_23
    //     0xc15cb4: ldur            w2, [x4, #0x23]
    // 0xc15cb8: DecompressPointer r2
    //     0xc15cb8: add             x2, x2, HEAP, lsl #32
    // 0xc15cbc: LoadField: r4 = r2->field_b
    //     0xc15cbc: ldur            x4, [x2, #0xb]
    // 0xc15cc0: LoadField: r5 = r2->field_27
    //     0xc15cc0: ldur            x5, [x2, #0x27]
    // 0xc15cc4: add             x2, x4, x5
    // 0xc15cc8: sub             x4, x2, x3
    // 0xc15ccc: r0 = BoxInt64Instr(r4)
    //     0xc15ccc: sbfiz           x0, x4, #1, #0x1f
    //     0xc15cd0: cmp             x4, x0, asr #1
    //     0xc15cd4: b.eq            #0xc15ce0
    //     0xc15cd8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc15cdc: stur            x4, [x0, #7]
    // 0xc15ce0: LeaveFrame
    //     0xc15ce0: mov             SP, fp
    //     0xc15ce4: ldp             fp, lr, [SP], #0x10
    // 0xc15ce8: ret
    //     0xc15ce8: ret             
    // 0xc15cec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15cec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15cf0: b               #0xc15b70
  }
  _ _writeArrow(/* No info */) {
    // ** addr: 0xc15cf4, size: 0x140
    // 0xc15cf4: EnterFrame
    //     0xc15cf4: stp             fp, lr, [SP, #-0x10]!
    //     0xc15cf8: mov             fp, SP
    // 0xc15cfc: AllocStack(0x28)
    //     0xc15cfc: sub             SP, SP, #0x28
    // 0xc15d00: SetupParameters(Highlighter this /* r1 => r5, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, {dynamic beginning = true /* r0 */})
    //     0xc15d00: mov             x5, x1
    //     0xc15d04: stur            x1, [fp, #-8]
    //     0xc15d08: stur            x3, [fp, #-0x10]
    //     0xc15d0c: ldur            w0, [x4, #0x13]
    //     0xc15d10: ldur            w1, [x4, #0x1f]
    //     0xc15d14: add             x1, x1, HEAP, lsl #32
    //     0xc15d18: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c600] "beginning"
    //     0xc15d1c: ldr             x16, [x16, #0x600]
    //     0xc15d20: cmp             w1, w16
    //     0xc15d24: b.ne            #0xc15d40
    //     0xc15d28: ldur            w1, [x4, #0x23]
    //     0xc15d2c: add             x1, x1, HEAP, lsl #32
    //     0xc15d30: sub             w4, w0, w1
    //     0xc15d34: add             x0, fp, w4, sxtw #2
    //     0xc15d38: ldr             x0, [x0, #8]
    //     0xc15d3c: b               #0xc15d44
    //     0xc15d40: add             x0, NULL, #0x20  ; true
    // 0xc15d44: CheckStackOverflow
    //     0xc15d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15d48: cmp             SP, x16
    //     0xc15d4c: b.ls            #0xc15e2c
    // 0xc15d50: LoadField: r4 = r2->field_7
    //     0xc15d50: ldur            w4, [x2, #7]
    // 0xc15d54: DecompressPointer r4
    //     0xc15d54: add             x4, x4, HEAP, lsl #32
    // 0xc15d58: tst             x0, #0x10
    // 0xc15d5c: cset            x1, ne
    // 0xc15d60: lsl             x1, x1, #1
    // 0xc15d64: r0 = LoadInt32Instr(r1)
    //     0xc15d64: sbfx            x0, x1, #1, #0x1f
    // 0xc15d68: add             x2, x3, x0
    // 0xc15d6c: r0 = BoxInt64Instr(r2)
    //     0xc15d6c: sbfiz           x0, x2, #1, #0x1f
    //     0xc15d70: cmp             x2, x0, asr #1
    //     0xc15d74: b.eq            #0xc15d80
    //     0xc15d78: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc15d7c: stur            x2, [x0, #7]
    // 0xc15d80: str             x0, [SP]
    // 0xc15d84: mov             x1, x4
    // 0xc15d88: r2 = 0
    //     0xc15d88: movz            x2, #0
    // 0xc15d8c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc15d8c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc15d90: r0 = substring()
    //     0xc15d90: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc15d94: ldur            x1, [fp, #-8]
    // 0xc15d98: mov             x2, x0
    // 0xc15d9c: r0 = _countTabs()
    //     0xc15d9c: bl              #0xc15e34  ; [package:source_span/src/highlighter.dart] Highlighter::_countTabs
    // 0xc15da0: mov             x1, x0
    // 0xc15da4: ldur            x0, [fp, #-8]
    // 0xc15da8: stur            x1, [fp, #-0x20]
    // 0xc15dac: LoadField: r2 = r0->field_23
    //     0xc15dac: ldur            w2, [x0, #0x23]
    // 0xc15db0: DecompressPointer r2
    //     0xc15db0: add             x2, x2, HEAP, lsl #32
    // 0xc15db4: stur            x2, [fp, #-0x18]
    // 0xc15db8: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc15db8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc15dbc: ldr             x0, [x0, #0x2ea8]
    //     0xc15dc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc15dc4: cmp             w0, w16
    //     0xc15dc8: b.ne            #0xc15dd8
    //     0xc15dcc: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc15dd0: ldr             x2, [x2, #0x580]
    //     0xc15dd4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc15dd8: ldur            x0, [fp, #-0x10]
    // 0xc15ddc: add             x1, x0, #1
    // 0xc15de0: ldur            x0, [fp, #-0x20]
    // 0xc15de4: r16 = 3
    //     0xc15de4: movz            x16, #0x3
    // 0xc15de8: mul             x2, x0, x16
    // 0xc15dec: add             x0, x1, x2
    // 0xc15df0: mov             x2, x0
    // 0xc15df4: r1 = "─"
    //     0xc15df4: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5f0] "─"
    //     0xc15df8: ldr             x1, [x1, #0x5f0]
    // 0xc15dfc: r0 = *()
    //     0xc15dfc: bl              #0xebce0c  ; [dart:core] _TwoByteString::*
    // 0xc15e00: ldur            x1, [fp, #-0x18]
    // 0xc15e04: mov             x2, x0
    // 0xc15e08: r0 = write()
    //     0xc15e08: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc15e0c: ldur            x1, [fp, #-0x18]
    // 0xc15e10: r2 = "^"
    //     0xc15e10: add             x2, PP, #0x18, lsl #12  ; [pp+0x18118] "^"
    //     0xc15e14: ldr             x2, [x2, #0x118]
    // 0xc15e18: r0 = write()
    //     0xc15e18: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc15e1c: r0 = Null
    //     0xc15e1c: mov             x0, NULL
    // 0xc15e20: LeaveFrame
    //     0xc15e20: mov             SP, fp
    //     0xc15e24: ldp             fp, lr, [SP], #0x10
    // 0xc15e28: ret
    //     0xc15e28: ret             
    // 0xc15e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15e2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15e30: b               #0xc15d50
  }
  _ _countTabs(/* No info */) {
    // ** addr: 0xc15e34, size: 0x8c
    // 0xc15e34: LoadField: r1 = r2->field_7
    //     0xc15e34: ldur            w1, [x2, #7]
    // 0xc15e38: r3 = LoadInt32Instr(r1)
    //     0xc15e38: sbfx            x3, x1, #1, #0x1f
    // 0xc15e3c: r1 = LoadClassIdInstr(r2)
    //     0xc15e3c: ldur            x1, [x2, #-1]
    //     0xc15e40: ubfx            x1, x1, #0xc, #0x14
    // 0xc15e44: lsl             x1, x1, #1
    // 0xc15e48: r5 = 0
    //     0xc15e48: movz            x5, #0
    // 0xc15e4c: r4 = 0
    //     0xc15e4c: movz            x4, #0
    // 0xc15e50: CheckStackOverflow
    //     0xc15e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15e54: cmp             SP, x16
    //     0xc15e58: b.ls            #0xc15ea8
    // 0xc15e5c: cmp             x4, x3
    // 0xc15e60: b.ge            #0xc15ea0
    // 0xc15e64: cmp             w1, #0xbc
    // 0xc15e68: b.ne            #0xc15e78
    // 0xc15e6c: ArrayLoad: r6 = r2[r4]  ; TypedUnsigned_1
    //     0xc15e6c: add             x16, x2, x4
    //     0xc15e70: ldrb            w6, [x16, #0xf]
    // 0xc15e74: b               #0xc15e80
    // 0xc15e78: add             x16, x2, x4, lsl #1
    // 0xc15e7c: ldurh           w6, [x16, #0xf]
    // 0xc15e80: add             x0, x4, #1
    // 0xc15e84: lsl             x4, x6, #1
    // 0xc15e88: cmp             w4, #0x12
    // 0xc15e8c: b.ne            #0xc15e98
    // 0xc15e90: add             x6, x5, #1
    // 0xc15e94: mov             x5, x6
    // 0xc15e98: mov             x4, x0
    // 0xc15e9c: b               #0xc15e50
    // 0xc15ea0: mov             x0, x5
    // 0xc15ea4: ret
    //     0xc15ea4: ret             
    // 0xc15ea8: EnterFrame
    //     0xc15ea8: stp             fp, lr, [SP, #-0x10]!
    //     0xc15eac: mov             fp, SP
    // 0xc15eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15eb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15eb4: LeaveFrame
    //     0xc15eb4: mov             SP, fp
    //     0xc15eb8: ldp             fp, lr, [SP], #0x10
    // 0xc15ebc: b               #0xc15e5c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc15f10, size: 0xac
    // 0xc15f10: EnterFrame
    //     0xc15f10: stp             fp, lr, [SP, #-0x10]!
    //     0xc15f14: mov             fp, SP
    // 0xc15f18: AllocStack(0x10)
    //     0xc15f18: sub             SP, SP, #0x10
    // 0xc15f1c: SetupParameters()
    //     0xc15f1c: ldr             x0, [fp, #0x10]
    //     0xc15f20: ldur            w1, [x0, #0x17]
    //     0xc15f24: add             x1, x1, HEAP, lsl #32
    // 0xc15f28: CheckStackOverflow
    //     0xc15f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15f2c: cmp             SP, x16
    //     0xc15f30: b.ls            #0xc15fb4
    // 0xc15f34: LoadField: r2 = r1->field_f
    //     0xc15f34: ldur            w2, [x1, #0xf]
    // 0xc15f38: DecompressPointer r2
    //     0xc15f38: add             x2, x2, HEAP, lsl #32
    // 0xc15f3c: stur            x2, [fp, #-0x10]
    // 0xc15f40: LoadField: r3 = r1->field_13
    //     0xc15f40: ldur            w3, [x1, #0x13]
    // 0xc15f44: DecompressPointer r3
    //     0xc15f44: add             x3, x3, HEAP, lsl #32
    // 0xc15f48: stur            x3, [fp, #-8]
    // 0xc15f4c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc15f4c: ldur            w0, [x1, #0x17]
    // 0xc15f50: DecompressPointer r0
    //     0xc15f50: add             x0, x0, HEAP, lsl #32
    // 0xc15f54: LoadField: r1 = r0->field_7
    //     0xc15f54: ldur            w1, [x0, #7]
    // 0xc15f58: DecompressPointer r1
    //     0xc15f58: add             x1, x1, HEAP, lsl #32
    // 0xc15f5c: r0 = LoadClassIdInstr(r1)
    //     0xc15f5c: ldur            x0, [x1, #-1]
    //     0xc15f60: ubfx            x0, x0, #0xc, #0x14
    // 0xc15f64: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc15f64: sub             lr, x0, #0xfff
    //     0xc15f68: ldr             lr, [x21, lr, lsl #3]
    //     0xc15f6c: blr             lr
    // 0xc15f70: r1 = LoadClassIdInstr(r0)
    //     0xc15f70: ldur            x1, [x0, #-1]
    //     0xc15f74: ubfx            x1, x1, #0xc, #0x14
    // 0xc15f78: mov             x16, x0
    // 0xc15f7c: mov             x0, x1
    // 0xc15f80: mov             x1, x16
    // 0xc15f84: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc15f84: sub             lr, x0, #0xfff
    //     0xc15f88: ldr             lr, [x21, lr, lsl #3]
    //     0xc15f8c: blr             lr
    // 0xc15f90: ldur            x1, [fp, #-0x10]
    // 0xc15f94: ldur            x2, [fp, #-8]
    // 0xc15f98: mov             x3, x0
    // 0xc15f9c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xc15f9c: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xc15fa0: r0 = _writeArrow()
    //     0xc15fa0: bl              #0xc15cf4  ; [package:source_span/src/highlighter.dart] Highlighter::_writeArrow
    // 0xc15fa4: r0 = Null
    //     0xc15fa4: mov             x0, NULL
    // 0xc15fa8: LeaveFrame
    //     0xc15fa8: mov             SP, fp
    //     0xc15fac: ldp             fp, lr, [SP], #0x10
    // 0xc15fb0: ret
    //     0xc15fb0: ret             
    // 0xc15fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15fb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15fb8: b               #0xc15f34
  }
  [closure] int <anonymous closure>(dynamic) {
    // ** addr: 0xc15fdc, size: 0xb8
    // 0xc15fdc: EnterFrame
    //     0xc15fdc: stp             fp, lr, [SP, #-0x10]!
    //     0xc15fe0: mov             fp, SP
    // 0xc15fe4: AllocStack(0x10)
    //     0xc15fe4: sub             SP, SP, #0x10
    // 0xc15fe8: SetupParameters()
    //     0xc15fe8: ldr             x0, [fp, #0x10]
    //     0xc15fec: ldur            w4, [x0, #0x17]
    //     0xc15ff0: add             x4, x4, HEAP, lsl #32
    //     0xc15ff4: stur            x4, [fp, #-0x10]
    // 0xc15ff8: CheckStackOverflow
    //     0xc15ff8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15ffc: cmp             SP, x16
    //     0xc16000: b.ls            #0xc1608c
    // 0xc16004: LoadField: r1 = r4->field_f
    //     0xc16004: ldur            w1, [x4, #0xf]
    // 0xc16008: DecompressPointer r1
    //     0xc16008: add             x1, x1, HEAP, lsl #32
    // 0xc1600c: LoadField: r0 = r1->field_23
    //     0xc1600c: ldur            w0, [x1, #0x23]
    // 0xc16010: DecompressPointer r0
    //     0xc16010: add             x0, x0, HEAP, lsl #32
    // 0xc16014: LoadField: r2 = r0->field_b
    //     0xc16014: ldur            x2, [x0, #0xb]
    // 0xc16018: LoadField: r3 = r0->field_27
    //     0xc16018: ldur            x3, [x0, #0x27]
    // 0xc1601c: add             x0, x2, x3
    // 0xc16020: stur            x0, [fp, #-8]
    // 0xc16024: LoadField: r2 = r4->field_13
    //     0xc16024: ldur            w2, [x4, #0x13]
    // 0xc16028: DecompressPointer r2
    //     0xc16028: add             x2, x2, HEAP, lsl #32
    // 0xc1602c: ArrayLoad: r3 = r4[0]  ; List_4
    //     0xc1602c: ldur            w3, [x4, #0x17]
    // 0xc16030: DecompressPointer r3
    //     0xc16030: add             x3, x3, HEAP, lsl #32
    // 0xc16034: LoadField: r5 = r3->field_7
    //     0xc16034: ldur            w5, [x3, #7]
    // 0xc16038: DecompressPointer r5
    //     0xc16038: add             x5, x5, HEAP, lsl #32
    // 0xc1603c: mov             x3, x5
    // 0xc16040: r0 = _writeUnderline()
    //     0xc16040: bl              #0xc16094  ; [package:source_span/src/highlighter.dart] Highlighter::_writeUnderline
    // 0xc16044: ldur            x2, [fp, #-0x10]
    // 0xc16048: LoadField: r3 = r2->field_f
    //     0xc16048: ldur            w3, [x2, #0xf]
    // 0xc1604c: DecompressPointer r3
    //     0xc1604c: add             x3, x3, HEAP, lsl #32
    // 0xc16050: LoadField: r2 = r3->field_23
    //     0xc16050: ldur            w2, [x3, #0x23]
    // 0xc16054: DecompressPointer r2
    //     0xc16054: add             x2, x2, HEAP, lsl #32
    // 0xc16058: LoadField: r3 = r2->field_b
    //     0xc16058: ldur            x3, [x2, #0xb]
    // 0xc1605c: LoadField: r4 = r2->field_27
    //     0xc1605c: ldur            x4, [x2, #0x27]
    // 0xc16060: add             x2, x3, x4
    // 0xc16064: ldur            x3, [fp, #-8]
    // 0xc16068: sub             x4, x2, x3
    // 0xc1606c: r0 = BoxInt64Instr(r4)
    //     0xc1606c: sbfiz           x0, x4, #1, #0x1f
    //     0xc16070: cmp             x4, x0, asr #1
    //     0xc16074: b.eq            #0xc16080
    //     0xc16078: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1607c: stur            x4, [x0, #7]
    // 0xc16080: LeaveFrame
    //     0xc16080: mov             SP, fp
    //     0xc16084: ldp             fp, lr, [SP], #0x10
    // 0xc16088: ret
    //     0xc16088: ret             
    // 0xc1608c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1608c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16090: b               #0xc16004
  }
  _ _writeUnderline(/* No info */) {
    // ** addr: 0xc16094, size: 0x1e8
    // 0xc16094: EnterFrame
    //     0xc16094: stp             fp, lr, [SP, #-0x10]!
    //     0xc16098: mov             fp, SP
    // 0xc1609c: AllocStack(0x40)
    //     0xc1609c: sub             SP, SP, #0x40
    // 0xc160a0: SetupParameters(Highlighter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xc160a0: mov             x4, x1
    //     0xc160a4: stur            x2, [fp, #-0x10]
    //     0xc160a8: mov             x16, x3
    //     0xc160ac: mov             x3, x2
    //     0xc160b0: mov             x2, x16
    //     0xc160b4: stur            x1, [fp, #-8]
    //     0xc160b8: stur            x2, [fp, #-0x18]
    // 0xc160bc: CheckStackOverflow
    //     0xc160bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc160c0: cmp             SP, x16
    //     0xc160c4: b.ls            #0xc16274
    // 0xc160c8: r0 = LoadClassIdInstr(r2)
    //     0xc160c8: ldur            x0, [x2, #-1]
    //     0xc160cc: ubfx            x0, x0, #0xc, #0x14
    // 0xc160d0: mov             x1, x2
    // 0xc160d4: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc160d4: sub             lr, x0, #0xfff
    //     0xc160d8: ldr             lr, [x21, lr, lsl #3]
    //     0xc160dc: blr             lr
    // 0xc160e0: r1 = LoadClassIdInstr(r0)
    //     0xc160e0: ldur            x1, [x0, #-1]
    //     0xc160e4: ubfx            x1, x1, #0xc, #0x14
    // 0xc160e8: mov             x16, x0
    // 0xc160ec: mov             x0, x1
    // 0xc160f0: mov             x1, x16
    // 0xc160f4: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc160f4: sub             lr, x0, #0xfff
    //     0xc160f8: ldr             lr, [x21, lr, lsl #3]
    //     0xc160fc: blr             lr
    // 0xc16100: mov             x2, x0
    // 0xc16104: ldur            x1, [fp, #-0x18]
    // 0xc16108: stur            x2, [fp, #-0x20]
    // 0xc1610c: r0 = LoadClassIdInstr(r1)
    //     0xc1610c: ldur            x0, [x1, #-1]
    //     0xc16110: ubfx            x0, x0, #0xc, #0x14
    // 0xc16114: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc16114: sub             lr, x0, #1, lsl #12
    //     0xc16118: ldr             lr, [x21, lr, lsl #3]
    //     0xc1611c: blr             lr
    // 0xc16120: r1 = LoadClassIdInstr(r0)
    //     0xc16120: ldur            x1, [x0, #-1]
    //     0xc16124: ubfx            x1, x1, #0xc, #0x14
    // 0xc16128: mov             x16, x0
    // 0xc1612c: mov             x0, x1
    // 0xc16130: mov             x1, x16
    // 0xc16134: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc16134: sub             lr, x0, #0xfff
    //     0xc16138: ldr             lr, [x21, lr, lsl #3]
    //     0xc1613c: blr             lr
    // 0xc16140: mov             x3, x0
    // 0xc16144: ldur            x0, [fp, #-0x10]
    // 0xc16148: stur            x3, [fp, #-0x28]
    // 0xc1614c: LoadField: r4 = r0->field_7
    //     0xc1614c: ldur            w4, [x0, #7]
    // 0xc16150: DecompressPointer r4
    //     0xc16150: add             x4, x4, HEAP, lsl #32
    // 0xc16154: ldur            x5, [fp, #-0x20]
    // 0xc16158: stur            x4, [fp, #-0x18]
    // 0xc1615c: r0 = BoxInt64Instr(r5)
    //     0xc1615c: sbfiz           x0, x5, #1, #0x1f
    //     0xc16160: cmp             x5, x0, asr #1
    //     0xc16164: b.eq            #0xc16170
    //     0xc16168: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1616c: stur            x5, [x0, #7]
    // 0xc16170: str             x0, [SP]
    // 0xc16174: mov             x1, x4
    // 0xc16178: r2 = 0
    //     0xc16178: movz            x2, #0
    // 0xc1617c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc1617c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc16180: r0 = substring()
    //     0xc16180: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc16184: ldur            x1, [fp, #-8]
    // 0xc16188: mov             x2, x0
    // 0xc1618c: r0 = _countTabs()
    //     0xc1618c: bl              #0xc15e34  ; [package:source_span/src/highlighter.dart] Highlighter::_countTabs
    // 0xc16190: mov             x4, x0
    // 0xc16194: ldur            x3, [fp, #-0x28]
    // 0xc16198: stur            x4, [fp, #-0x30]
    // 0xc1619c: r0 = BoxInt64Instr(r3)
    //     0xc1619c: sbfiz           x0, x3, #1, #0x1f
    //     0xc161a0: cmp             x3, x0, asr #1
    //     0xc161a4: b.eq            #0xc161b0
    //     0xc161a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc161ac: stur            x3, [x0, #7]
    // 0xc161b0: str             x0, [SP]
    // 0xc161b4: ldur            x1, [fp, #-0x18]
    // 0xc161b8: ldur            x2, [fp, #-0x20]
    // 0xc161bc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc161bc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc161c0: r0 = substring()
    //     0xc161c0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc161c4: ldur            x1, [fp, #-8]
    // 0xc161c8: mov             x2, x0
    // 0xc161cc: r0 = _countTabs()
    //     0xc161cc: bl              #0xc15e34  ; [package:source_span/src/highlighter.dart] Highlighter::_countTabs
    // 0xc161d0: mov             x1, x0
    // 0xc161d4: ldur            x0, [fp, #-0x30]
    // 0xc161d8: r16 = 3
    //     0xc161d8: movz            x16, #0x3
    // 0xc161dc: mul             x2, x0, x16
    // 0xc161e0: ldur            x3, [fp, #-0x20]
    // 0xc161e4: add             x4, x3, x2
    // 0xc161e8: stur            x4, [fp, #-0x38]
    // 0xc161ec: add             x2, x0, x1
    // 0xc161f0: r16 = 3
    //     0xc161f0: movz            x16, #0x3
    // 0xc161f4: mul             x0, x2, x16
    // 0xc161f8: ldur            x1, [fp, #-0x28]
    // 0xc161fc: add             x3, x1, x0
    // 0xc16200: ldur            x0, [fp, #-8]
    // 0xc16204: stur            x3, [fp, #-0x20]
    // 0xc16208: LoadField: r5 = r0->field_23
    //     0xc16208: ldur            w5, [x0, #0x23]
    // 0xc1620c: DecompressPointer r5
    //     0xc1620c: add             x5, x5, HEAP, lsl #32
    // 0xc16210: mov             x2, x4
    // 0xc16214: stur            x5, [fp, #-0x10]
    // 0xc16218: r1 = " "
    //     0xc16218: ldr             x1, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc1621c: r0 = *()
    //     0xc1621c: bl              #0xebcfc4  ; [dart:core] _OneByteString::*
    // 0xc16220: ldur            x1, [fp, #-0x10]
    // 0xc16224: mov             x2, x0
    // 0xc16228: r0 = write()
    //     0xc16228: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc1622c: ldur            x0, [fp, #-0x38]
    // 0xc16230: ldur            x1, [fp, #-0x20]
    // 0xc16234: sub             x2, x1, x0
    // 0xc16238: cmp             x2, #1
    // 0xc1623c: b.gt            #0xc1624c
    // 0xc16240: cmp             x2, #1
    // 0xc16244: b.ge            #0xc1624c
    // 0xc16248: r2 = 1
    //     0xc16248: movz            x2, #0x1
    // 0xc1624c: r1 = "^"
    //     0xc1624c: add             x1, PP, #0x18, lsl #12  ; [pp+0x18118] "^"
    //     0xc16250: ldr             x1, [x1, #0x118]
    // 0xc16254: r0 = *()
    //     0xc16254: bl              #0xebcfc4  ; [dart:core] _OneByteString::*
    // 0xc16258: ldur            x1, [fp, #-0x10]
    // 0xc1625c: mov             x2, x0
    // 0xc16260: r0 = write()
    //     0xc16260: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc16264: r0 = Null
    //     0xc16264: mov             x0, NULL
    // 0xc16268: LeaveFrame
    //     0xc16268: mov             SP, fp
    //     0xc1626c: ldp             fp, lr, [SP], #0x10
    // 0xc16270: ret
    //     0xc16270: ret             
    // 0xc16274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16274: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16278: b               #0xc160c8
  }
  _ _writeText(/* No info */) {
    // ** addr: 0xc1627c, size: 0xf0
    // 0xc1627c: EnterFrame
    //     0xc1627c: stp             fp, lr, [SP, #-0x10]!
    //     0xc16280: mov             fp, SP
    // 0xc16284: AllocStack(0x28)
    //     0xc16284: sub             SP, SP, #0x28
    // 0xc16288: SetupParameters(dynamic _ /* r2 => r0, fp-0x28 */)
    //     0xc16288: mov             x0, x2
    //     0xc1628c: stur            x2, [fp, #-0x28]
    // 0xc16290: CheckStackOverflow
    //     0xc16290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16294: cmp             SP, x16
    //     0xc16298: b.ls            #0xc1635c
    // 0xc1629c: LoadField: r2 = r0->field_7
    //     0xc1629c: ldur            w2, [x0, #7]
    // 0xc162a0: r3 = LoadInt32Instr(r2)
    //     0xc162a0: sbfx            x3, x2, #1, #0x1f
    // 0xc162a4: stur            x3, [fp, #-0x20]
    // 0xc162a8: r4 = LoadClassIdInstr(r0)
    //     0xc162a8: ldur            x4, [x0, #-1]
    //     0xc162ac: ubfx            x4, x4, #0xc, #0x14
    // 0xc162b0: lsl             x4, x4, #1
    // 0xc162b4: stur            x4, [fp, #-0x18]
    // 0xc162b8: LoadField: r5 = r1->field_23
    //     0xc162b8: ldur            w5, [x1, #0x23]
    // 0xc162bc: DecompressPointer r5
    //     0xc162bc: add             x5, x5, HEAP, lsl #32
    // 0xc162c0: stur            x5, [fp, #-0x10]
    // 0xc162c4: r1 = 0
    //     0xc162c4: movz            x1, #0
    // 0xc162c8: CheckStackOverflow
    //     0xc162c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc162cc: cmp             SP, x16
    //     0xc162d0: b.ls            #0xc16364
    // 0xc162d4: cmp             x1, x3
    // 0xc162d8: b.ge            #0xc1634c
    // 0xc162dc: cmp             w4, #0xbc
    // 0xc162e0: b.ne            #0xc162f0
    // 0xc162e4: ArrayLoad: r2 = r0[r1]  ; TypedUnsigned_1
    //     0xc162e4: add             x16, x0, x1
    //     0xc162e8: ldrb            w2, [x16, #0xf]
    // 0xc162ec: b               #0xc162f8
    // 0xc162f0: add             x16, x0, x1, lsl #1
    // 0xc162f4: ldurh           w2, [x16, #0xf]
    // 0xc162f8: add             x6, x1, #1
    // 0xc162fc: stur            x6, [fp, #-8]
    // 0xc16300: cmp             x2, #9
    // 0xc16304: b.ne            #0xc1632c
    // 0xc16308: r1 = " "
    //     0xc16308: ldr             x1, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc1630c: r2 = 4
    //     0xc1630c: movz            x2, #0x4
    // 0xc16310: r0 = *()
    //     0xc16310: bl              #0xebcfc4  ; [dart:core] _OneByteString::*
    // 0xc16314: LoadField: r1 = r0->field_7
    //     0xc16314: ldur            w1, [x0, #7]
    // 0xc16318: cbz             w1, #0xc16334
    // 0xc1631c: ldur            x1, [fp, #-0x10]
    // 0xc16320: mov             x2, x0
    // 0xc16324: r0 = _writeString()
    //     0xc16324: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc16328: b               #0xc16334
    // 0xc1632c: ldur            x1, [fp, #-0x10]
    // 0xc16330: r0 = writeCharCode()
    //     0xc16330: bl              #0x603f20  ; [dart:core] StringBuffer::writeCharCode
    // 0xc16334: ldur            x1, [fp, #-8]
    // 0xc16338: ldur            x0, [fp, #-0x28]
    // 0xc1633c: ldur            x5, [fp, #-0x10]
    // 0xc16340: ldur            x4, [fp, #-0x18]
    // 0xc16344: ldur            x3, [fp, #-0x20]
    // 0xc16348: b               #0xc162c8
    // 0xc1634c: r0 = Null
    //     0xc1634c: mov             x0, NULL
    // 0xc16350: LeaveFrame
    //     0xc16350: mov             SP, fp
    //     0xc16354: ldp             fp, lr, [SP], #0x10
    // 0xc16358: ret
    //     0xc16358: ret             
    // 0xc1635c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1635c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16360: b               #0xc1629c
    // 0xc16364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16364: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16368: b               #0xc162d4
  }
  _ _writeHighlightedText(/* No info */) {
    // ** addr: 0xc1636c, size: 0x134
    // 0xc1636c: EnterFrame
    //     0xc1636c: stp             fp, lr, [SP, #-0x10]!
    //     0xc16370: mov             fp, SP
    // 0xc16374: AllocStack(0x50)
    //     0xc16374: sub             SP, SP, #0x50
    // 0xc16378: SetupParameters(Highlighter this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0xc16378: mov             x0, x1
    //     0xc1637c: stur            x1, [fp, #-8]
    //     0xc16380: mov             x1, x2
    //     0xc16384: stur            x2, [fp, #-0x10]
    //     0xc16388: stur            x3, [fp, #-0x18]
    //     0xc1638c: stur            x5, [fp, #-0x20]
    //     0xc16390: stur            x6, [fp, #-0x28]
    // 0xc16394: CheckStackOverflow
    //     0xc16394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16398: cmp             SP, x16
    //     0xc1639c: b.ls            #0xc16498
    // 0xc163a0: r1 = 4
    //     0xc163a0: movz            x1, #0x4
    // 0xc163a4: r0 = AllocateContext()
    //     0xc163a4: bl              #0xec126c  ; AllocateContextStub
    // 0xc163a8: mov             x4, x0
    // 0xc163ac: ldur            x3, [fp, #-8]
    // 0xc163b0: stur            x4, [fp, #-0x30]
    // 0xc163b4: StoreField: r4->field_f = r3
    //     0xc163b4: stur            w3, [x4, #0xf]
    // 0xc163b8: ldur            x2, [fp, #-0x10]
    // 0xc163bc: StoreField: r4->field_13 = r2
    //     0xc163bc: stur            w2, [x4, #0x13]
    // 0xc163c0: ldur            x5, [fp, #-0x18]
    // 0xc163c4: r0 = BoxInt64Instr(r5)
    //     0xc163c4: sbfiz           x0, x5, #1, #0x1f
    //     0xc163c8: cmp             x5, x0, asr #1
    //     0xc163cc: b.eq            #0xc163d8
    //     0xc163d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc163d4: stur            x5, [x0, #7]
    // 0xc163d8: mov             x5, x0
    // 0xc163dc: ArrayStore: r4[0] = r5  ; List_4
    //     0xc163dc: stur            w5, [x4, #0x17]
    // 0xc163e0: ldur            x6, [fp, #-0x20]
    // 0xc163e4: r0 = BoxInt64Instr(r6)
    //     0xc163e4: sbfiz           x0, x6, #1, #0x1f
    //     0xc163e8: cmp             x6, x0, asr #1
    //     0xc163ec: b.eq            #0xc163f8
    //     0xc163f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc163f4: stur            x6, [x0, #7]
    // 0xc163f8: StoreField: r4->field_1b = r0
    //     0xc163f8: stur            w0, [x4, #0x1b]
    // 0xc163fc: str             x5, [SP]
    // 0xc16400: mov             x1, x2
    // 0xc16404: r2 = 0
    //     0xc16404: movz            x2, #0
    // 0xc16408: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc16408: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc1640c: r0 = substring()
    //     0xc1640c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc16410: ldur            x1, [fp, #-8]
    // 0xc16414: mov             x2, x0
    // 0xc16418: r0 = _writeText()
    //     0xc16418: bl              #0xc1627c  ; [package:source_span/src/highlighter.dart] Highlighter::_writeText
    // 0xc1641c: ldur            x2, [fp, #-0x30]
    // 0xc16420: r1 = Function '<anonymous closure>':.
    //     0xc16420: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c610] AnonymousClosure: (0xc164a0), in [package:source_span/src/highlighter.dart] Highlighter::_writeHighlightedText (0xc1636c)
    //     0xc16424: ldr             x1, [x1, #0x610]
    // 0xc16428: r0 = AllocateClosure()
    //     0xc16428: bl              #0xec1630  ; AllocateClosureStub
    // 0xc1642c: r16 = <void?>
    //     0xc1642c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xc16430: ldur            lr, [fp, #-8]
    // 0xc16434: stp             lr, x16, [SP, #0x10]
    // 0xc16438: ldur            x16, [fp, #-0x28]
    // 0xc1643c: stp             x16, x0, [SP]
    // 0xc16440: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc16440: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc16444: r0 = _colorize()
    //     0xc16444: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc16448: ldur            x0, [fp, #-0x30]
    // 0xc1644c: LoadField: r1 = r0->field_13
    //     0xc1644c: ldur            w1, [x0, #0x13]
    // 0xc16450: DecompressPointer r1
    //     0xc16450: add             x1, x1, HEAP, lsl #32
    // 0xc16454: LoadField: r2 = r0->field_1b
    //     0xc16454: ldur            w2, [x0, #0x1b]
    // 0xc16458: DecompressPointer r2
    //     0xc16458: add             x2, x2, HEAP, lsl #32
    // 0xc1645c: LoadField: r0 = r1->field_7
    //     0xc1645c: ldur            w0, [x1, #7]
    // 0xc16460: r3 = LoadInt32Instr(r2)
    //     0xc16460: sbfx            x3, x2, #1, #0x1f
    //     0xc16464: tbz             w2, #0, #0xc1646c
    //     0xc16468: ldur            x3, [x2, #7]
    // 0xc1646c: str             x0, [SP]
    // 0xc16470: mov             x2, x3
    // 0xc16474: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc16474: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc16478: r0 = substring()
    //     0xc16478: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc1647c: ldur            x1, [fp, #-8]
    // 0xc16480: mov             x2, x0
    // 0xc16484: r0 = _writeText()
    //     0xc16484: bl              #0xc1627c  ; [package:source_span/src/highlighter.dart] Highlighter::_writeText
    // 0xc16488: r0 = Null
    //     0xc16488: mov             x0, NULL
    // 0xc1648c: LeaveFrame
    //     0xc1648c: mov             SP, fp
    //     0xc16490: ldp             fp, lr, [SP], #0x10
    // 0xc16494: ret
    //     0xc16494: ret             
    // 0xc16498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1649c: b               #0xc163a0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc164a0, size: 0x90
    // 0xc164a0: EnterFrame
    //     0xc164a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc164a4: mov             fp, SP
    // 0xc164a8: AllocStack(0x10)
    //     0xc164a8: sub             SP, SP, #0x10
    // 0xc164ac: SetupParameters()
    //     0xc164ac: ldr             x0, [fp, #0x10]
    //     0xc164b0: ldur            w1, [x0, #0x17]
    //     0xc164b4: add             x1, x1, HEAP, lsl #32
    // 0xc164b8: CheckStackOverflow
    //     0xc164b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc164bc: cmp             SP, x16
    //     0xc164c0: b.ls            #0xc16528
    // 0xc164c4: LoadField: r0 = r1->field_f
    //     0xc164c4: ldur            w0, [x1, #0xf]
    // 0xc164c8: DecompressPointer r0
    //     0xc164c8: add             x0, x0, HEAP, lsl #32
    // 0xc164cc: stur            x0, [fp, #-8]
    // 0xc164d0: LoadField: r2 = r1->field_13
    //     0xc164d0: ldur            w2, [x1, #0x13]
    // 0xc164d4: DecompressPointer r2
    //     0xc164d4: add             x2, x2, HEAP, lsl #32
    // 0xc164d8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xc164d8: ldur            w3, [x1, #0x17]
    // 0xc164dc: DecompressPointer r3
    //     0xc164dc: add             x3, x3, HEAP, lsl #32
    // 0xc164e0: LoadField: r4 = r1->field_1b
    //     0xc164e0: ldur            w4, [x1, #0x1b]
    // 0xc164e4: DecompressPointer r4
    //     0xc164e4: add             x4, x4, HEAP, lsl #32
    // 0xc164e8: r1 = LoadInt32Instr(r3)
    //     0xc164e8: sbfx            x1, x3, #1, #0x1f
    //     0xc164ec: tbz             w3, #0, #0xc164f4
    //     0xc164f0: ldur            x1, [x3, #7]
    // 0xc164f4: str             x4, [SP]
    // 0xc164f8: mov             x16, x1
    // 0xc164fc: mov             x1, x2
    // 0xc16500: mov             x2, x16
    // 0xc16504: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc16504: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc16508: r0 = substring()
    //     0xc16508: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc1650c: ldur            x1, [fp, #-8]
    // 0xc16510: mov             x2, x0
    // 0xc16514: r0 = _writeText()
    //     0xc16514: bl              #0xc1627c  ; [package:source_span/src/highlighter.dart] Highlighter::_writeText
    // 0xc16518: r0 = Null
    //     0xc16518: mov             x0, NULL
    // 0xc1651c: LeaveFrame
    //     0xc1651c: mov             SP, fp
    //     0xc16520: ldp             fp, lr, [SP], #0x10
    // 0xc16524: ret
    //     0xc16524: ret             
    // 0xc16528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16528: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1652c: b               #0xc164c4
  }
  _ _writeMultilineHighlights(/* No info */) {
    // ** addr: 0xc16530, size: 0x3f8
    // 0xc16530: EnterFrame
    //     0xc16530: stp             fp, lr, [SP, #-0x10]!
    //     0xc16534: mov             fp, SP
    // 0xc16538: AllocStack(0x80)
    //     0xc16538: sub             SP, SP, #0x80
    // 0xc1653c: SetupParameters(Highlighter this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, {dynamic current = Null /* r0, fp-0x8 */})
    //     0xc1653c: stur            x1, [fp, #-0x10]
    //     0xc16540: stur            x2, [fp, #-0x18]
    //     0xc16544: stur            x3, [fp, #-0x20]
    //     0xc16548: ldur            w0, [x4, #0x13]
    //     0xc1654c: ldur            w5, [x4, #0x1f]
    //     0xc16550: add             x5, x5, HEAP, lsl #32
    //     0xc16554: add             x16, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0xc16558: ldr             x16, [x16, #0x110]
    //     0xc1655c: cmp             w5, w16
    //     0xc16560: b.ne            #0xc1657c
    //     0xc16564: ldur            w5, [x4, #0x23]
    //     0xc16568: add             x5, x5, HEAP, lsl #32
    //     0xc1656c: sub             w4, w0, w5
    //     0xc16570: add             x0, fp, w4, sxtw #2
    //     0xc16574: ldr             x0, [x0, #8]
    //     0xc16578: b               #0xc16580
    //     0xc1657c: mov             x0, NULL
    //     0xc16580: stur            x0, [fp, #-8]
    // 0xc16584: CheckStackOverflow
    //     0xc16584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16588: cmp             SP, x16
    //     0xc1658c: b.ls            #0xc16918
    // 0xc16590: r1 = 5
    //     0xc16590: movz            x1, #0x5
    // 0xc16594: r0 = AllocateContext()
    //     0xc16594: bl              #0xec126c  ; AllocateContextStub
    // 0xc16598: mov             x1, x0
    // 0xc1659c: ldur            x0, [fp, #-0x10]
    // 0xc165a0: stur            x1, [fp, #-0x58]
    // 0xc165a4: StoreField: r1->field_f = r0
    //     0xc165a4: stur            w0, [x1, #0xf]
    // 0xc165a8: ldur            x2, [fp, #-0x18]
    // 0xc165ac: StoreField: r1->field_13 = r2
    //     0xc165ac: stur            w2, [x1, #0x13]
    // 0xc165b0: ldur            x2, [fp, #-8]
    // 0xc165b4: ArrayStore: r1[0] = r2  ; List_4
    //     0xc165b4: stur            w2, [x1, #0x17]
    // 0xc165b8: r3 = false
    //     0xc165b8: add             x3, NULL, #0x30  ; false
    // 0xc165bc: StoreField: r1->field_1b = r3
    //     0xc165bc: stur            w3, [x1, #0x1b]
    // 0xc165c0: cmp             w2, NULL
    // 0xc165c4: b.ne            #0xc165d0
    // 0xc165c8: r3 = Null
    //     0xc165c8: mov             x3, NULL
    // 0xc165cc: b               #0xc165dc
    // 0xc165d0: LoadField: r2 = r0->field_b
    //     0xc165d0: ldur            w2, [x0, #0xb]
    // 0xc165d4: DecompressPointer r2
    //     0xc165d4: add             x2, x2, HEAP, lsl #32
    // 0xc165d8: mov             x3, x2
    // 0xc165dc: ldur            x2, [fp, #-0x20]
    // 0xc165e0: stur            x3, [fp, #-0x50]
    // 0xc165e4: LoadField: r4 = r2->field_7
    //     0xc165e4: ldur            w4, [x2, #7]
    // 0xc165e8: DecompressPointer r4
    //     0xc165e8: add             x4, x4, HEAP, lsl #32
    // 0xc165ec: stur            x4, [fp, #-0x48]
    // 0xc165f0: LoadField: r5 = r2->field_b
    //     0xc165f0: ldur            w5, [x2, #0xb]
    // 0xc165f4: r6 = LoadInt32Instr(r5)
    //     0xc165f4: sbfx            x6, x5, #1, #0x1f
    // 0xc165f8: stur            x6, [fp, #-0x40]
    // 0xc165fc: LoadField: r5 = r0->field_b
    //     0xc165fc: ldur            w5, [x0, #0xb]
    // 0xc16600: DecompressPointer r5
    //     0xc16600: add             x5, x5, HEAP, lsl #32
    // 0xc16604: stur            x5, [fp, #-0x38]
    // 0xc16608: LoadField: r7 = r0->field_23
    //     0xc16608: ldur            w7, [x0, #0x23]
    // 0xc1660c: DecompressPointer r7
    //     0xc1660c: add             x7, x7, HEAP, lsl #32
    // 0xc16610: stur            x7, [fp, #-0x30]
    // 0xc16614: r9 = 0
    //     0xc16614: movz            x9, #0
    // 0xc16618: r8 = false
    //     0xc16618: add             x8, NULL, #0x30  ; false
    // 0xc1661c: stur            x8, [fp, #-0x18]
    // 0xc16620: CheckStackOverflow
    //     0xc16620: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16624: cmp             SP, x16
    //     0xc16628: b.ls            #0xc16920
    // 0xc1662c: cmp             x9, x6
    // 0xc16630: b.ge            #0xc16908
    // 0xc16634: ArrayLoad: r10 = r2[r9]  ; Unknown_4
    //     0xc16634: add             x16, x2, x9, lsl #2
    //     0xc16638: ldur            w10, [x16, #0xf]
    // 0xc1663c: DecompressPointer r10
    //     0xc1663c: add             x10, x10, HEAP, lsl #32
    // 0xc16640: stur            x10, [fp, #-8]
    // 0xc16644: add             x11, x9, #1
    // 0xc16648: stur            x11, [fp, #-0x28]
    // 0xc1664c: r1 = 3
    //     0xc1664c: movz            x1, #0x3
    // 0xc16650: r0 = AllocateContext()
    //     0xc16650: bl              #0xec126c  ; AllocateContextStub
    // 0xc16654: mov             x4, x0
    // 0xc16658: ldur            x3, [fp, #-0x58]
    // 0xc1665c: stur            x4, [fp, #-0x60]
    // 0xc16660: StoreField: r4->field_b = r3
    //     0xc16660: stur            w3, [x4, #0xb]
    // 0xc16664: ldur            x5, [fp, #-8]
    // 0xc16668: cmp             w5, NULL
    // 0xc1666c: b.ne            #0xc166a0
    // 0xc16670: mov             x0, x5
    // 0xc16674: ldur            x2, [fp, #-0x48]
    // 0xc16678: r1 = Null
    //     0xc16678: mov             x1, NULL
    // 0xc1667c: cmp             w2, NULL
    // 0xc16680: b.eq            #0xc166a0
    // 0xc16684: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc16684: ldur            w4, [x2, #0x17]
    // 0xc16688: DecompressPointer r4
    //     0xc16688: add             x4, x4, HEAP, lsl #32
    // 0xc1668c: r8 = X0
    //     0xc1668c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc16690: LoadField: r9 = r4->field_7
    //     0xc16690: ldur            x9, [x4, #7]
    // 0xc16694: r3 = Null
    //     0xc16694: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c618] Null
    //     0xc16698: ldr             x3, [x3, #0x618]
    // 0xc1669c: blr             x9
    // 0xc166a0: ldur            x2, [fp, #-0x60]
    // 0xc166a4: ldur            x0, [fp, #-8]
    // 0xc166a8: StoreField: r2->field_f = r0
    //     0xc166a8: stur            w0, [x2, #0xf]
    // 0xc166ac: cmp             w0, NULL
    // 0xc166b0: b.ne            #0xc166bc
    // 0xc166b4: r0 = Null
    //     0xc166b4: mov             x0, NULL
    // 0xc166b8: b               #0xc16714
    // 0xc166bc: LoadField: r1 = r0->field_7
    //     0xc166bc: ldur            w1, [x0, #7]
    // 0xc166c0: DecompressPointer r1
    //     0xc166c0: add             x1, x1, HEAP, lsl #32
    // 0xc166c4: r0 = LoadClassIdInstr(r1)
    //     0xc166c4: ldur            x0, [x1, #-1]
    //     0xc166c8: ubfx            x0, x0, #0xc, #0x14
    // 0xc166cc: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc166cc: sub             lr, x0, #0xfff
    //     0xc166d0: ldr             lr, [x21, lr, lsl #3]
    //     0xc166d4: blr             lr
    // 0xc166d8: r1 = LoadClassIdInstr(r0)
    //     0xc166d8: ldur            x1, [x0, #-1]
    //     0xc166dc: ubfx            x1, x1, #0xc, #0x14
    // 0xc166e0: mov             x16, x0
    // 0xc166e4: mov             x0, x1
    // 0xc166e8: mov             x1, x16
    // 0xc166ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc166ec: sub             lr, x0, #1, lsl #12
    //     0xc166f0: ldr             lr, [x21, lr, lsl #3]
    //     0xc166f4: blr             lr
    // 0xc166f8: mov             x2, x0
    // 0xc166fc: r0 = BoxInt64Instr(r2)
    //     0xc166fc: sbfiz           x0, x2, #1, #0x1f
    //     0xc16700: cmp             x2, x0, asr #1
    //     0xc16704: b.eq            #0xc16710
    //     0xc16708: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1670c: stur            x2, [x0, #7]
    // 0xc16710: ldur            x2, [fp, #-0x60]
    // 0xc16714: StoreField: r2->field_13 = r0
    //     0xc16714: stur            w0, [x2, #0x13]
    //     0xc16718: tbz             w0, #0, #0xc16734
    //     0xc1671c: ldurb           w16, [x2, #-1]
    //     0xc16720: ldurb           w17, [x0, #-1]
    //     0xc16724: and             x16, x17, x16, lsr #2
    //     0xc16728: tst             x16, HEAP, lsr #32
    //     0xc1672c: b.eq            #0xc16734
    //     0xc16730: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc16734: LoadField: r0 = r2->field_f
    //     0xc16734: ldur            w0, [x2, #0xf]
    // 0xc16738: DecompressPointer r0
    //     0xc16738: add             x0, x0, HEAP, lsl #32
    // 0xc1673c: cmp             w0, NULL
    // 0xc16740: b.ne            #0xc1674c
    // 0xc16744: r0 = Null
    //     0xc16744: mov             x0, NULL
    // 0xc16748: b               #0xc167a4
    // 0xc1674c: LoadField: r1 = r0->field_7
    //     0xc1674c: ldur            w1, [x0, #7]
    // 0xc16750: DecompressPointer r1
    //     0xc16750: add             x1, x1, HEAP, lsl #32
    // 0xc16754: r0 = LoadClassIdInstr(r1)
    //     0xc16754: ldur            x0, [x1, #-1]
    //     0xc16758: ubfx            x0, x0, #0xc, #0x14
    // 0xc1675c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1675c: sub             lr, x0, #1, lsl #12
    //     0xc16760: ldr             lr, [x21, lr, lsl #3]
    //     0xc16764: blr             lr
    // 0xc16768: r1 = LoadClassIdInstr(r0)
    //     0xc16768: ldur            x1, [x0, #-1]
    //     0xc1676c: ubfx            x1, x1, #0xc, #0x14
    // 0xc16770: mov             x16, x0
    // 0xc16774: mov             x0, x1
    // 0xc16778: mov             x1, x16
    // 0xc1677c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1677c: sub             lr, x0, #1, lsl #12
    //     0xc16780: ldr             lr, [x21, lr, lsl #3]
    //     0xc16784: blr             lr
    // 0xc16788: mov             x2, x0
    // 0xc1678c: r0 = BoxInt64Instr(r2)
    //     0xc1678c: sbfiz           x0, x2, #1, #0x1f
    //     0xc16790: cmp             x2, x0, asr #1
    //     0xc16794: b.eq            #0xc167a0
    //     0xc16798: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1679c: stur            x2, [x0, #7]
    // 0xc167a0: ldur            x2, [fp, #-0x60]
    // 0xc167a4: ldur            x3, [fp, #-0x58]
    // 0xc167a8: ArrayStore: r2[0] = r0  ; List_4
    //     0xc167a8: stur            w0, [x2, #0x17]
    //     0xc167ac: tbz             w0, #0, #0xc167c8
    //     0xc167b0: ldurb           w16, [x2, #-1]
    //     0xc167b4: ldurb           w17, [x0, #-1]
    //     0xc167b8: and             x16, x17, x16, lsr #2
    //     0xc167bc: tst             x16, HEAP, lsr #32
    //     0xc167c0: b.eq            #0xc167c8
    //     0xc167c4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc167c8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xc167c8: ldur            w0, [x3, #0x17]
    // 0xc167cc: DecompressPointer r0
    //     0xc167cc: add             x0, x0, HEAP, lsl #32
    // 0xc167d0: cmp             w0, NULL
    // 0xc167d4: b.eq            #0xc16818
    // 0xc167d8: LoadField: r1 = r2->field_f
    //     0xc167d8: ldur            w1, [x2, #0xf]
    // 0xc167dc: DecompressPointer r1
    //     0xc167dc: add             x1, x1, HEAP, lsl #32
    // 0xc167e0: cmp             w1, w0
    // 0xc167e4: b.ne            #0xc16818
    // 0xc167e8: r1 = Function '<anonymous closure>':.
    //     0xc167e8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c628] AnonymousClosure: (0xc16f84), in [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights (0xc16530)
    //     0xc167ec: ldr             x1, [x1, #0x628]
    // 0xc167f0: r0 = AllocateClosure()
    //     0xc167f0: bl              #0xec1630  ; AllocateClosureStub
    // 0xc167f4: r16 = <Null?>
    //     0xc167f4: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc167f8: ldur            lr, [fp, #-0x10]
    // 0xc167fc: stp             lr, x16, [SP, #0x10]
    // 0xc16800: ldur            x16, [fp, #-0x50]
    // 0xc16804: stp             x16, x0, [SP]
    // 0xc16808: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc16808: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc1680c: r0 = _colorize()
    //     0xc1680c: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc16810: r8 = true
    //     0xc16810: add             x8, NULL, #0x20  ; true
    // 0xc16814: b               #0xc168e0
    // 0xc16818: ldur            x0, [fp, #-0x18]
    // 0xc1681c: tbnz            w0, #4, #0xc1684c
    // 0xc16820: r1 = Function '<anonymous closure>':.
    //     0xc16820: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c630] AnonymousClosure: (0xc16ec0), in [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights (0xc16530)
    //     0xc16824: ldr             x1, [x1, #0x630]
    // 0xc16828: r0 = AllocateClosure()
    //     0xc16828: bl              #0xec1630  ; AllocateClosureStub
    // 0xc1682c: r16 = <Null?>
    //     0xc1682c: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc16830: ldur            lr, [fp, #-0x10]
    // 0xc16834: stp             lr, x16, [SP, #0x10]
    // 0xc16838: ldur            x16, [fp, #-0x50]
    // 0xc1683c: stp             x16, x0, [SP]
    // 0xc16840: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc16840: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc16844: r0 = _colorize()
    //     0xc16844: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc16848: b               #0xc168dc
    // 0xc1684c: LoadField: r0 = r2->field_f
    //     0xc1684c: ldur            w0, [x2, #0xf]
    // 0xc16850: DecompressPointer r0
    //     0xc16850: add             x0, x0, HEAP, lsl #32
    // 0xc16854: cmp             w0, NULL
    // 0xc16858: b.ne            #0xc168b4
    // 0xc1685c: ldur            x0, [fp, #-0x58]
    // 0xc16860: LoadField: r1 = r0->field_1b
    //     0xc16860: ldur            w1, [x0, #0x1b]
    // 0xc16864: DecompressPointer r1
    //     0xc16864: add             x1, x1, HEAP, lsl #32
    // 0xc16868: tbnz            w1, #4, #0xc168a4
    // 0xc1686c: LoadField: r3 = r0->field_1f
    //     0xc1686c: ldur            w3, [x0, #0x1f]
    // 0xc16870: DecompressPointer r3
    //     0xc16870: add             x3, x3, HEAP, lsl #32
    // 0xc16874: stur            x3, [fp, #-8]
    // 0xc16878: r1 = Function '<anonymous closure>':.
    //     0xc16878: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c638] AnonymousClosure: (0xc16e38), in [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights (0xc16530)
    //     0xc1687c: ldr             x1, [x1, #0x638]
    // 0xc16880: r0 = AllocateClosure()
    //     0xc16880: bl              #0xec1630  ; AllocateClosureStub
    // 0xc16884: r16 = <void?>
    //     0xc16884: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xc16888: ldur            lr, [fp, #-0x10]
    // 0xc1688c: stp             lr, x16, [SP, #0x10]
    // 0xc16890: ldur            x16, [fp, #-8]
    // 0xc16894: stp             x16, x0, [SP]
    // 0xc16898: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc16898: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc1689c: r0 = _colorize()
    //     0xc1689c: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc168a0: b               #0xc168dc
    // 0xc168a4: ldur            x1, [fp, #-0x30]
    // 0xc168a8: r2 = " "
    //     0xc168a8: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc168ac: r0 = _writeString()
    //     0xc168ac: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xc168b0: b               #0xc168dc
    // 0xc168b4: r1 = Function '<anonymous closure>':.
    //     0xc168b4: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c640] AnonymousClosure: (0xc16928), in [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights (0xc16530)
    //     0xc168b8: ldr             x1, [x1, #0x640]
    // 0xc168bc: r0 = AllocateClosure()
    //     0xc168bc: bl              #0xec1630  ; AllocateClosureStub
    // 0xc168c0: r16 = <Null?>
    //     0xc168c0: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc168c4: ldur            lr, [fp, #-0x10]
    // 0xc168c8: stp             lr, x16, [SP, #0x10]
    // 0xc168cc: ldur            x16, [fp, #-0x38]
    // 0xc168d0: stp             x16, x0, [SP]
    // 0xc168d4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc168d4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc168d8: r0 = _colorize()
    //     0xc168d8: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc168dc: ldur            x8, [fp, #-0x18]
    // 0xc168e0: ldur            x9, [fp, #-0x28]
    // 0xc168e4: ldur            x0, [fp, #-0x10]
    // 0xc168e8: ldur            x2, [fp, #-0x20]
    // 0xc168ec: ldur            x1, [fp, #-0x58]
    // 0xc168f0: ldur            x3, [fp, #-0x50]
    // 0xc168f4: ldur            x5, [fp, #-0x38]
    // 0xc168f8: ldur            x7, [fp, #-0x30]
    // 0xc168fc: ldur            x4, [fp, #-0x48]
    // 0xc16900: ldur            x6, [fp, #-0x40]
    // 0xc16904: b               #0xc1661c
    // 0xc16908: r0 = Null
    //     0xc16908: mov             x0, NULL
    // 0xc1690c: LeaveFrame
    //     0xc1690c: mov             SP, fp
    //     0xc16910: ldp             fp, lr, [SP], #0x10
    // 0xc16914: ret
    //     0xc16914: ret             
    // 0xc16918: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16918: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1691c: b               #0xc16590
    // 0xc16920: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16920: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16924: b               #0xc1662c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xc16928, size: 0x36c
    // 0xc16928: EnterFrame
    //     0xc16928: stp             fp, lr, [SP, #-0x10]!
    //     0xc1692c: mov             fp, SP
    // 0xc16930: AllocStack(0x48)
    //     0xc16930: sub             SP, SP, #0x48
    // 0xc16934: SetupParameters()
    //     0xc16934: ldr             x0, [fp, #0x10]
    //     0xc16938: ldur            w1, [x0, #0x17]
    //     0xc1693c: add             x1, x1, HEAP, lsl #32
    //     0xc16940: stur            x1, [fp, #-8]
    // 0xc16944: CheckStackOverflow
    //     0xc16944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16948: cmp             SP, x16
    //     0xc1694c: b.ls            #0xc16c80
    // 0xc16950: r1 = 1
    //     0xc16950: movz            x1, #0x1
    // 0xc16954: r0 = AllocateContext()
    //     0xc16954: bl              #0xec126c  ; AllocateContextStub
    // 0xc16958: mov             x1, x0
    // 0xc1695c: ldur            x0, [fp, #-8]
    // 0xc16960: stur            x1, [fp, #-0x18]
    // 0xc16964: StoreField: r1->field_b = r0
    //     0xc16964: stur            w0, [x1, #0xb]
    // 0xc16968: LoadField: r2 = r0->field_b
    //     0xc16968: ldur            w2, [x0, #0xb]
    // 0xc1696c: DecompressPointer r2
    //     0xc1696c: add             x2, x2, HEAP, lsl #32
    // 0xc16970: stur            x2, [fp, #-0x10]
    // 0xc16974: LoadField: r3 = r2->field_1b
    //     0xc16974: ldur            w3, [x2, #0x1b]
    // 0xc16978: DecompressPointer r3
    //     0xc16978: add             x3, x3, HEAP, lsl #32
    // 0xc1697c: tbnz            w3, #4, #0xc16990
    // 0xc16980: r0 = cross()
    //     0xc16980: bl              #0xc16cec  ; [package:term_glyph/src/generated/top_level.dart] ::cross
    // 0xc16984: r1 = "┼"
    //     0xc16984: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c648] "┼"
    //     0xc16988: ldr             x1, [x1, #0x648]
    // 0xc1698c: b               #0xc169b8
    // 0xc16990: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc16990: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc16994: ldr             x0, [x0, #0x2ea8]
    //     0xc16998: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc1699c: cmp             w0, w16
    //     0xc169a0: b.ne            #0xc169b0
    //     0xc169a4: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc169a8: ldr             x2, [x2, #0x580]
    //     0xc169ac: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc169b0: r1 = "│"
    //     0xc169b0: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5c0] "│"
    //     0xc169b4: ldr             x1, [x1, #0x5c0]
    // 0xc169b8: ldur            x2, [fp, #-0x18]
    // 0xc169bc: ldur            x3, [fp, #-0x10]
    // 0xc169c0: mov             x0, x1
    // 0xc169c4: StoreField: r2->field_f = r0
    //     0xc169c4: stur            w0, [x2, #0xf]
    //     0xc169c8: ldurb           w16, [x2, #-1]
    //     0xc169cc: ldurb           w17, [x0, #-1]
    //     0xc169d0: and             x16, x17, x16, lsr #2
    //     0xc169d4: tst             x16, HEAP, lsr #32
    //     0xc169d8: b.eq            #0xc169e0
    //     0xc169dc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc169e0: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xc169e0: ldur            w0, [x3, #0x17]
    // 0xc169e4: DecompressPointer r0
    //     0xc169e4: add             x0, x0, HEAP, lsl #32
    // 0xc169e8: cmp             w0, NULL
    // 0xc169ec: b.eq            #0xc16a14
    // 0xc169f0: LoadField: r0 = r3->field_f
    //     0xc169f0: ldur            w0, [x3, #0xf]
    // 0xc169f4: DecompressPointer r0
    //     0xc169f4: add             x0, x0, HEAP, lsl #32
    // 0xc169f8: LoadField: r2 = r0->field_23
    //     0xc169f8: ldur            w2, [x0, #0x23]
    // 0xc169fc: DecompressPointer r2
    //     0xc169fc: add             x2, x2, HEAP, lsl #32
    // 0xc16a00: mov             x16, x1
    // 0xc16a04: mov             x1, x2
    // 0xc16a08: mov             x2, x16
    // 0xc16a0c: r0 = write()
    //     0xc16a0c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc16a10: b               #0xc16c70
    // 0xc16a14: ldur            x4, [fp, #-8]
    // 0xc16a18: LoadField: r5 = r4->field_13
    //     0xc16a18: ldur            w5, [x4, #0x13]
    // 0xc16a1c: DecompressPointer r5
    //     0xc16a1c: add             x5, x5, HEAP, lsl #32
    // 0xc16a20: LoadField: r0 = r3->field_13
    //     0xc16a20: ldur            w0, [x3, #0x13]
    // 0xc16a24: DecompressPointer r0
    //     0xc16a24: add             x0, x0, HEAP, lsl #32
    // 0xc16a28: LoadField: r6 = r0->field_b
    //     0xc16a28: ldur            x6, [x0, #0xb]
    // 0xc16a2c: r0 = BoxInt64Instr(r6)
    //     0xc16a2c: sbfiz           x0, x6, #1, #0x1f
    //     0xc16a30: cmp             x6, x0, asr #1
    //     0xc16a34: b.eq            #0xc16a40
    //     0xc16a38: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc16a3c: stur            x6, [x0, #7]
    // 0xc16a40: cmp             w5, w0
    // 0xc16a44: b.eq            #0xc16a80
    // 0xc16a48: and             w16, w5, w0
    // 0xc16a4c: branchIfSmi(r16, 0xc16b24)
    //     0xc16a4c: tbz             w16, #0, #0xc16b24
    // 0xc16a50: r16 = LoadClassIdInstr(r5)
    //     0xc16a50: ldur            x16, [x5, #-1]
    //     0xc16a54: ubfx            x16, x16, #0xc, #0x14
    // 0xc16a58: cmp             x16, #0x3d
    // 0xc16a5c: b.ne            #0xc16b24
    // 0xc16a60: r16 = LoadClassIdInstr(r0)
    //     0xc16a60: ldur            x16, [x0, #-1]
    //     0xc16a64: ubfx            x16, x16, #0xc, #0x14
    // 0xc16a68: cmp             x16, #0x3d
    // 0xc16a6c: b.ne            #0xc16b24
    // 0xc16a70: LoadField: r16 = r5->field_7
    //     0xc16a70: ldur            x16, [x5, #7]
    // 0xc16a74: LoadField: r17 = r0->field_7
    //     0xc16a74: ldur            x17, [x0, #7]
    // 0xc16a78: cmp             x16, x17
    // 0xc16a7c: b.ne            #0xc16b24
    // 0xc16a80: LoadField: r0 = r3->field_f
    //     0xc16a80: ldur            w0, [x3, #0xf]
    // 0xc16a84: DecompressPointer r0
    //     0xc16a84: add             x0, x0, HEAP, lsl #32
    // 0xc16a88: stur            x0, [fp, #-0x28]
    // 0xc16a8c: LoadField: r5 = r3->field_1f
    //     0xc16a8c: ldur            w5, [x3, #0x1f]
    // 0xc16a90: DecompressPointer r5
    //     0xc16a90: add             x5, x5, HEAP, lsl #32
    // 0xc16a94: stur            x5, [fp, #-0x20]
    // 0xc16a98: r1 = Function '<anonymous closure>':.
    //     0xc16a98: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c650] AnonymousClosure: (0xc16da8), in [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights (0xc16530)
    //     0xc16a9c: ldr             x1, [x1, #0x650]
    // 0xc16aa0: r0 = AllocateClosure()
    //     0xc16aa0: bl              #0xec1630  ; AllocateClosureStub
    // 0xc16aa4: r16 = <Null?>
    //     0xc16aa4: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc16aa8: ldur            lr, [fp, #-0x28]
    // 0xc16aac: stp             lr, x16, [SP, #0x10]
    // 0xc16ab0: ldur            x16, [fp, #-0x20]
    // 0xc16ab4: stp             x16, x0, [SP]
    // 0xc16ab8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc16ab8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc16abc: r0 = _colorize()
    //     0xc16abc: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc16ac0: ldur            x3, [fp, #-0x10]
    // 0xc16ac4: r0 = true
    //     0xc16ac4: add             x0, NULL, #0x20  ; true
    // 0xc16ac8: StoreField: r3->field_1b = r0
    //     0xc16ac8: stur            w0, [x3, #0x1b]
    // 0xc16acc: LoadField: r0 = r3->field_1f
    //     0xc16acc: ldur            w0, [x3, #0x1f]
    // 0xc16ad0: DecompressPointer r0
    //     0xc16ad0: add             x0, x0, HEAP, lsl #32
    // 0xc16ad4: cmp             w0, NULL
    // 0xc16ad8: b.ne            #0xc16c70
    // 0xc16adc: ldur            x4, [fp, #-8]
    // 0xc16ae0: LoadField: r0 = r4->field_f
    //     0xc16ae0: ldur            w0, [x4, #0xf]
    // 0xc16ae4: DecompressPointer r0
    //     0xc16ae4: add             x0, x0, HEAP, lsl #32
    // 0xc16ae8: cmp             w0, NULL
    // 0xc16aec: b.eq            #0xc16c88
    // 0xc16af0: LoadField: r0 = r3->field_f
    //     0xc16af0: ldur            w0, [x3, #0xf]
    // 0xc16af4: DecompressPointer r0
    //     0xc16af4: add             x0, x0, HEAP, lsl #32
    // 0xc16af8: LoadField: r1 = r0->field_b
    //     0xc16af8: ldur            w1, [x0, #0xb]
    // 0xc16afc: DecompressPointer r1
    //     0xc16afc: add             x1, x1, HEAP, lsl #32
    // 0xc16b00: mov             x0, x1
    // 0xc16b04: StoreField: r3->field_1f = r0
    //     0xc16b04: stur            w0, [x3, #0x1f]
    //     0xc16b08: ldurb           w16, [x3, #-1]
    //     0xc16b0c: ldurb           w17, [x0, #-1]
    //     0xc16b10: and             x16, x17, x16, lsr #2
    //     0xc16b14: tst             x16, HEAP, lsr #32
    //     0xc16b18: b.eq            #0xc16b20
    //     0xc16b1c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xc16b20: b               #0xc16c70
    // 0xc16b24: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xc16b24: ldur            w1, [x4, #0x17]
    // 0xc16b28: DecompressPointer r1
    //     0xc16b28: add             x1, x1, HEAP, lsl #32
    // 0xc16b2c: cmp             w1, w0
    // 0xc16b30: b.eq            #0xc16b6c
    // 0xc16b34: and             w16, w1, w0
    // 0xc16b38: branchIfSmi(r16, 0xc16c28)
    //     0xc16b38: tbz             w16, #0, #0xc16c28
    // 0xc16b3c: r16 = LoadClassIdInstr(r1)
    //     0xc16b3c: ldur            x16, [x1, #-1]
    //     0xc16b40: ubfx            x16, x16, #0xc, #0x14
    // 0xc16b44: cmp             x16, #0x3d
    // 0xc16b48: b.ne            #0xc16c28
    // 0xc16b4c: r16 = LoadClassIdInstr(r0)
    //     0xc16b4c: ldur            x16, [x0, #-1]
    //     0xc16b50: ubfx            x16, x16, #0xc, #0x14
    // 0xc16b54: cmp             x16, #0x3d
    // 0xc16b58: b.ne            #0xc16c28
    // 0xc16b5c: LoadField: r16 = r1->field_7
    //     0xc16b5c: ldur            x16, [x1, #7]
    // 0xc16b60: LoadField: r17 = r0->field_7
    //     0xc16b60: ldur            x17, [x0, #7]
    // 0xc16b64: cmp             x16, x17
    // 0xc16b68: b.ne            #0xc16c28
    // 0xc16b6c: LoadField: r0 = r4->field_f
    //     0xc16b6c: ldur            w0, [x4, #0xf]
    // 0xc16b70: DecompressPointer r0
    //     0xc16b70: add             x0, x0, HEAP, lsl #32
    // 0xc16b74: cmp             w0, NULL
    // 0xc16b78: b.eq            #0xc16c8c
    // 0xc16b7c: LoadField: r1 = r0->field_7
    //     0xc16b7c: ldur            w1, [x0, #7]
    // 0xc16b80: DecompressPointer r1
    //     0xc16b80: add             x1, x1, HEAP, lsl #32
    // 0xc16b84: r0 = LoadClassIdInstr(r1)
    //     0xc16b84: ldur            x0, [x1, #-1]
    //     0xc16b88: ubfx            x0, x0, #0xc, #0x14
    // 0xc16b8c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc16b8c: sub             lr, x0, #1, lsl #12
    //     0xc16b90: ldr             lr, [x21, lr, lsl #3]
    //     0xc16b94: blr             lr
    // 0xc16b98: r1 = LoadClassIdInstr(r0)
    //     0xc16b98: ldur            x1, [x0, #-1]
    //     0xc16b9c: ubfx            x1, x1, #0xc, #0x14
    // 0xc16ba0: mov             x16, x0
    // 0xc16ba4: mov             x0, x1
    // 0xc16ba8: mov             x1, x16
    // 0xc16bac: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc16bac: sub             lr, x0, #0xfff
    //     0xc16bb0: ldr             lr, [x21, lr, lsl #3]
    //     0xc16bb4: blr             lr
    // 0xc16bb8: mov             x1, x0
    // 0xc16bbc: ldur            x0, [fp, #-0x10]
    // 0xc16bc0: LoadField: r2 = r0->field_13
    //     0xc16bc0: ldur            w2, [x0, #0x13]
    // 0xc16bc4: DecompressPointer r2
    //     0xc16bc4: add             x2, x2, HEAP, lsl #32
    // 0xc16bc8: LoadField: r3 = r2->field_7
    //     0xc16bc8: ldur            w3, [x2, #7]
    // 0xc16bcc: DecompressPointer r3
    //     0xc16bcc: add             x3, x3, HEAP, lsl #32
    // 0xc16bd0: LoadField: r2 = r3->field_7
    //     0xc16bd0: ldur            w2, [x3, #7]
    // 0xc16bd4: r3 = LoadInt32Instr(r2)
    //     0xc16bd4: sbfx            x3, x2, #1, #0x1f
    // 0xc16bd8: cmp             x1, x3
    // 0xc16bdc: b.ne            #0xc16c2c
    // 0xc16be0: ldur            x1, [fp, #-8]
    // 0xc16be4: LoadField: r2 = r0->field_f
    //     0xc16be4: ldur            w2, [x0, #0xf]
    // 0xc16be8: DecompressPointer r2
    //     0xc16be8: add             x2, x2, HEAP, lsl #32
    // 0xc16bec: LoadField: r0 = r2->field_23
    //     0xc16bec: ldur            w0, [x2, #0x23]
    // 0xc16bf0: DecompressPointer r0
    //     0xc16bf0: add             x0, x0, HEAP, lsl #32
    // 0xc16bf4: stur            x0, [fp, #-0x20]
    // 0xc16bf8: LoadField: r2 = r1->field_f
    //     0xc16bf8: ldur            w2, [x1, #0xf]
    // 0xc16bfc: DecompressPointer r2
    //     0xc16bfc: add             x2, x2, HEAP, lsl #32
    // 0xc16c00: cmp             w2, NULL
    // 0xc16c04: b.eq            #0xc16c90
    // 0xc16c08: r1 = "└"
    //     0xc16c08: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c658] "└"
    //     0xc16c0c: ldr             x1, [x1, #0x658]
    // 0xc16c10: r0 = glyphOrAscii()
    //     0xc16c10: bl              #0xc16c94  ; [package:term_glyph/term_glyph.dart] ::glyphOrAscii
    // 0xc16c14: ldur            x1, [fp, #-0x20]
    // 0xc16c18: r2 = "└"
    //     0xc16c18: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c658] "└"
    //     0xc16c1c: ldr             x2, [x2, #0x658]
    // 0xc16c20: r0 = write()
    //     0xc16c20: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc16c24: b               #0xc16c70
    // 0xc16c28: mov             x0, x3
    // 0xc16c2c: LoadField: r3 = r0->field_f
    //     0xc16c2c: ldur            w3, [x0, #0xf]
    // 0xc16c30: DecompressPointer r3
    //     0xc16c30: add             x3, x3, HEAP, lsl #32
    // 0xc16c34: stur            x3, [fp, #-0x20]
    // 0xc16c38: LoadField: r4 = r0->field_1f
    //     0xc16c38: ldur            w4, [x0, #0x1f]
    // 0xc16c3c: DecompressPointer r4
    //     0xc16c3c: add             x4, x4, HEAP, lsl #32
    // 0xc16c40: ldur            x2, [fp, #-0x18]
    // 0xc16c44: stur            x4, [fp, #-8]
    // 0xc16c48: r1 = Function '<anonymous closure>':.
    //     0xc16c48: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c660] AnonymousClosure: (0xc16d3c), in [package:source_span/src/highlighter.dart] Highlighter::_writeMultilineHighlights (0xc16530)
    //     0xc16c4c: ldr             x1, [x1, #0x660]
    // 0xc16c50: r0 = AllocateClosure()
    //     0xc16c50: bl              #0xec1630  ; AllocateClosureStub
    // 0xc16c54: r16 = <Null?>
    //     0xc16c54: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xc16c58: ldur            lr, [fp, #-0x20]
    // 0xc16c5c: stp             lr, x16, [SP, #0x10]
    // 0xc16c60: ldur            x16, [fp, #-8]
    // 0xc16c64: stp             x16, x0, [SP]
    // 0xc16c68: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc16c68: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc16c6c: r0 = _colorize()
    //     0xc16c6c: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc16c70: r0 = Null
    //     0xc16c70: mov             x0, NULL
    // 0xc16c74: LeaveFrame
    //     0xc16c74: mov             SP, fp
    //     0xc16c78: ldp             fp, lr, [SP], #0x10
    // 0xc16c7c: ret
    //     0xc16c7c: ret             
    // 0xc16c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16c80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16c84: b               #0xc16950
    // 0xc16c88: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc16c88: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xc16c8c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc16c8c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xc16c90: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc16c90: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xc16d3c, size: 0x6c
    // 0xc16d3c: EnterFrame
    //     0xc16d3c: stp             fp, lr, [SP, #-0x10]!
    //     0xc16d40: mov             fp, SP
    // 0xc16d44: ldr             x0, [fp, #0x10]
    // 0xc16d48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc16d48: ldur            w1, [x0, #0x17]
    // 0xc16d4c: DecompressPointer r1
    //     0xc16d4c: add             x1, x1, HEAP, lsl #32
    // 0xc16d50: CheckStackOverflow
    //     0xc16d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16d54: cmp             SP, x16
    //     0xc16d58: b.ls            #0xc16da0
    // 0xc16d5c: LoadField: r0 = r1->field_b
    //     0xc16d5c: ldur            w0, [x1, #0xb]
    // 0xc16d60: DecompressPointer r0
    //     0xc16d60: add             x0, x0, HEAP, lsl #32
    // 0xc16d64: LoadField: r2 = r0->field_b
    //     0xc16d64: ldur            w2, [x0, #0xb]
    // 0xc16d68: DecompressPointer r2
    //     0xc16d68: add             x2, x2, HEAP, lsl #32
    // 0xc16d6c: LoadField: r0 = r2->field_f
    //     0xc16d6c: ldur            w0, [x2, #0xf]
    // 0xc16d70: DecompressPointer r0
    //     0xc16d70: add             x0, x0, HEAP, lsl #32
    // 0xc16d74: LoadField: r2 = r0->field_23
    //     0xc16d74: ldur            w2, [x0, #0x23]
    // 0xc16d78: DecompressPointer r2
    //     0xc16d78: add             x2, x2, HEAP, lsl #32
    // 0xc16d7c: LoadField: r0 = r1->field_f
    //     0xc16d7c: ldur            w0, [x1, #0xf]
    // 0xc16d80: DecompressPointer r0
    //     0xc16d80: add             x0, x0, HEAP, lsl #32
    // 0xc16d84: mov             x1, x2
    // 0xc16d88: mov             x2, x0
    // 0xc16d8c: r0 = write()
    //     0xc16d8c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc16d90: r0 = Null
    //     0xc16d90: mov             x0, NULL
    // 0xc16d94: LeaveFrame
    //     0xc16d94: mov             SP, fp
    //     0xc16d98: ldp             fp, lr, [SP], #0x10
    // 0xc16d9c: ret
    //     0xc16d9c: ret             
    // 0xc16da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16da0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16da4: b               #0xc16d5c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xc16da8, size: 0x90
    // 0xc16da8: EnterFrame
    //     0xc16da8: stp             fp, lr, [SP, #-0x10]!
    //     0xc16dac: mov             fp, SP
    // 0xc16db0: AllocStack(0x8)
    //     0xc16db0: sub             SP, SP, #8
    // 0xc16db4: SetupParameters()
    //     0xc16db4: ldr             x0, [fp, #0x10]
    //     0xc16db8: ldur            w1, [x0, #0x17]
    //     0xc16dbc: add             x1, x1, HEAP, lsl #32
    // 0xc16dc0: CheckStackOverflow
    //     0xc16dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16dc4: cmp             SP, x16
    //     0xc16dc8: b.ls            #0xc16e30
    // 0xc16dcc: LoadField: r0 = r1->field_b
    //     0xc16dcc: ldur            w0, [x1, #0xb]
    // 0xc16dd0: DecompressPointer r0
    //     0xc16dd0: add             x0, x0, HEAP, lsl #32
    // 0xc16dd4: LoadField: r1 = r0->field_b
    //     0xc16dd4: ldur            w1, [x0, #0xb]
    // 0xc16dd8: DecompressPointer r1
    //     0xc16dd8: add             x1, x1, HEAP, lsl #32
    // 0xc16ddc: LoadField: r0 = r1->field_f
    //     0xc16ddc: ldur            w0, [x1, #0xf]
    // 0xc16de0: DecompressPointer r0
    //     0xc16de0: add             x0, x0, HEAP, lsl #32
    // 0xc16de4: LoadField: r2 = r0->field_23
    //     0xc16de4: ldur            w2, [x0, #0x23]
    // 0xc16de8: DecompressPointer r2
    //     0xc16de8: add             x2, x2, HEAP, lsl #32
    // 0xc16dec: stur            x2, [fp, #-8]
    // 0xc16df0: LoadField: r0 = r1->field_1b
    //     0xc16df0: ldur            w0, [x1, #0x1b]
    // 0xc16df4: DecompressPointer r0
    //     0xc16df4: add             x0, x0, HEAP, lsl #32
    // 0xc16df8: tbnz            w0, #4, #0xc16e08
    // 0xc16dfc: r1 = "┬"
    //     0xc16dfc: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c680] "┬"
    //     0xc16e00: ldr             x1, [x1, #0x680]
    // 0xc16e04: b               #0xc16e10
    // 0xc16e08: r1 = "┌"
    //     0xc16e08: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c688] "┌"
    //     0xc16e0c: ldr             x1, [x1, #0x688]
    // 0xc16e10: r0 = glyphOrAscii()
    //     0xc16e10: bl              #0xc16c94  ; [package:term_glyph/term_glyph.dart] ::glyphOrAscii
    // 0xc16e14: ldur            x1, [fp, #-8]
    // 0xc16e18: mov             x2, x0
    // 0xc16e1c: r0 = write()
    //     0xc16e1c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc16e20: r0 = Null
    //     0xc16e20: mov             x0, NULL
    // 0xc16e24: LeaveFrame
    //     0xc16e24: mov             SP, fp
    //     0xc16e28: ldp             fp, lr, [SP], #0x10
    // 0xc16e2c: ret
    //     0xc16e2c: ret             
    // 0xc16e30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16e30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16e34: b               #0xc16dcc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc16e38, size: 0x88
    // 0xc16e38: EnterFrame
    //     0xc16e38: stp             fp, lr, [SP, #-0x10]!
    //     0xc16e3c: mov             fp, SP
    // 0xc16e40: AllocStack(0x8)
    //     0xc16e40: sub             SP, SP, #8
    // 0xc16e44: SetupParameters()
    //     0xc16e44: ldr             x0, [fp, #0x10]
    //     0xc16e48: ldur            w1, [x0, #0x17]
    //     0xc16e4c: add             x1, x1, HEAP, lsl #32
    // 0xc16e50: CheckStackOverflow
    //     0xc16e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16e54: cmp             SP, x16
    //     0xc16e58: b.ls            #0xc16eb8
    // 0xc16e5c: LoadField: r0 = r1->field_b
    //     0xc16e5c: ldur            w0, [x1, #0xb]
    // 0xc16e60: DecompressPointer r0
    //     0xc16e60: add             x0, x0, HEAP, lsl #32
    // 0xc16e64: LoadField: r1 = r0->field_f
    //     0xc16e64: ldur            w1, [x0, #0xf]
    // 0xc16e68: DecompressPointer r1
    //     0xc16e68: add             x1, x1, HEAP, lsl #32
    // 0xc16e6c: LoadField: r0 = r1->field_23
    //     0xc16e6c: ldur            w0, [x1, #0x23]
    // 0xc16e70: DecompressPointer r0
    //     0xc16e70: add             x0, x0, HEAP, lsl #32
    // 0xc16e74: stur            x0, [fp, #-8]
    // 0xc16e78: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc16e78: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc16e7c: ldr             x0, [x0, #0x2ea8]
    //     0xc16e80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc16e84: cmp             w0, w16
    //     0xc16e88: b.ne            #0xc16e98
    //     0xc16e8c: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc16e90: ldr             x2, [x2, #0x580]
    //     0xc16e94: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc16e98: ldur            x1, [fp, #-8]
    // 0xc16e9c: r2 = "─"
    //     0xc16e9c: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c5f0] "─"
    //     0xc16ea0: ldr             x2, [x2, #0x5f0]
    // 0xc16ea4: r0 = write()
    //     0xc16ea4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc16ea8: r0 = Null
    //     0xc16ea8: mov             x0, NULL
    // 0xc16eac: LeaveFrame
    //     0xc16eac: mov             SP, fp
    //     0xc16eb0: ldp             fp, lr, [SP], #0x10
    // 0xc16eb4: ret
    //     0xc16eb4: ret             
    // 0xc16eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16eb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16ebc: b               #0xc16e5c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xc16ec0, size: 0xc4
    // 0xc16ec0: EnterFrame
    //     0xc16ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xc16ec4: mov             fp, SP
    // 0xc16ec8: AllocStack(0x8)
    //     0xc16ec8: sub             SP, SP, #8
    // 0xc16ecc: SetupParameters()
    //     0xc16ecc: ldr             x0, [fp, #0x10]
    //     0xc16ed0: ldur            w1, [x0, #0x17]
    //     0xc16ed4: add             x1, x1, HEAP, lsl #32
    // 0xc16ed8: CheckStackOverflow
    //     0xc16ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16edc: cmp             SP, x16
    //     0xc16ee0: b.ls            #0xc16f7c
    // 0xc16ee4: LoadField: r0 = r1->field_b
    //     0xc16ee4: ldur            w0, [x1, #0xb]
    // 0xc16ee8: DecompressPointer r0
    //     0xc16ee8: add             x0, x0, HEAP, lsl #32
    // 0xc16eec: LoadField: r2 = r0->field_f
    //     0xc16eec: ldur            w2, [x0, #0xf]
    // 0xc16ef0: DecompressPointer r2
    //     0xc16ef0: add             x2, x2, HEAP, lsl #32
    // 0xc16ef4: LoadField: r0 = r2->field_23
    //     0xc16ef4: ldur            w0, [x2, #0x23]
    // 0xc16ef8: DecompressPointer r0
    //     0xc16ef8: add             x0, x0, HEAP, lsl #32
    // 0xc16efc: stur            x0, [fp, #-8]
    // 0xc16f00: LoadField: r2 = r1->field_f
    //     0xc16f00: ldur            w2, [x1, #0xf]
    // 0xc16f04: DecompressPointer r2
    //     0xc16f04: add             x2, x2, HEAP, lsl #32
    // 0xc16f08: cmp             w2, NULL
    // 0xc16f0c: b.ne            #0xc16f3c
    // 0xc16f10: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc16f10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc16f14: ldr             x0, [x0, #0x2ea8]
    //     0xc16f18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc16f1c: cmp             w0, w16
    //     0xc16f20: b.ne            #0xc16f30
    //     0xc16f24: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc16f28: ldr             x2, [x2, #0x580]
    //     0xc16f2c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc16f30: r2 = "─"
    //     0xc16f30: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c5f0] "─"
    //     0xc16f34: ldr             x2, [x2, #0x5f0]
    // 0xc16f38: b               #0xc16f64
    // 0xc16f3c: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc16f3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc16f40: ldr             x0, [x0, #0x2ea8]
    //     0xc16f44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc16f48: cmp             w0, w16
    //     0xc16f4c: b.ne            #0xc16f5c
    //     0xc16f50: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc16f54: ldr             x2, [x2, #0x580]
    //     0xc16f58: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc16f5c: r2 = "┼"
    //     0xc16f5c: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c648] "┼"
    //     0xc16f60: ldr             x2, [x2, #0x648]
    // 0xc16f64: ldur            x1, [fp, #-8]
    // 0xc16f68: r0 = write()
    //     0xc16f68: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc16f6c: r0 = Null
    //     0xc16f6c: mov             x0, NULL
    // 0xc16f70: LeaveFrame
    //     0xc16f70: mov             SP, fp
    //     0xc16f74: ldp             fp, lr, [SP], #0x10
    // 0xc16f78: ret
    //     0xc16f78: ret             
    // 0xc16f7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16f7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16f80: b               #0xc16ee4
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xc16f84, size: 0xe4
    // 0xc16f84: EnterFrame
    //     0xc16f84: stp             fp, lr, [SP, #-0x10]!
    //     0xc16f88: mov             fp, SP
    // 0xc16f8c: AllocStack(0x8)
    //     0xc16f8c: sub             SP, SP, #8
    // 0xc16f90: SetupParameters()
    //     0xc16f90: ldr             x0, [fp, #0x10]
    //     0xc16f94: ldur            w1, [x0, #0x17]
    //     0xc16f98: add             x1, x1, HEAP, lsl #32
    // 0xc16f9c: CheckStackOverflow
    //     0xc16f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16fa0: cmp             SP, x16
    //     0xc16fa4: b.ls            #0xc17060
    // 0xc16fa8: LoadField: r0 = r1->field_b
    //     0xc16fa8: ldur            w0, [x1, #0xb]
    // 0xc16fac: DecompressPointer r0
    //     0xc16fac: add             x0, x0, HEAP, lsl #32
    // 0xc16fb0: LoadField: r2 = r0->field_f
    //     0xc16fb0: ldur            w2, [x0, #0xf]
    // 0xc16fb4: DecompressPointer r2
    //     0xc16fb4: add             x2, x2, HEAP, lsl #32
    // 0xc16fb8: LoadField: r3 = r2->field_23
    //     0xc16fb8: ldur            w3, [x2, #0x23]
    // 0xc16fbc: DecompressPointer r3
    //     0xc16fbc: add             x3, x3, HEAP, lsl #32
    // 0xc16fc0: stur            x3, [fp, #-8]
    // 0xc16fc4: LoadField: r2 = r1->field_13
    //     0xc16fc4: ldur            w2, [x1, #0x13]
    // 0xc16fc8: DecompressPointer r2
    //     0xc16fc8: add             x2, x2, HEAP, lsl #32
    // 0xc16fcc: LoadField: r1 = r0->field_13
    //     0xc16fcc: ldur            w1, [x0, #0x13]
    // 0xc16fd0: DecompressPointer r1
    //     0xc16fd0: add             x1, x1, HEAP, lsl #32
    // 0xc16fd4: LoadField: r4 = r1->field_b
    //     0xc16fd4: ldur            x4, [x1, #0xb]
    // 0xc16fd8: r0 = BoxInt64Instr(r4)
    //     0xc16fd8: sbfiz           x0, x4, #1, #0x1f
    //     0xc16fdc: cmp             x4, x0, asr #1
    //     0xc16fe0: b.eq            #0xc16fec
    //     0xc16fe4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc16fe8: stur            x4, [x0, #7]
    // 0xc16fec: cmp             w2, w0
    // 0xc16ff0: b.eq            #0xc1702c
    // 0xc16ff4: and             w16, w2, w0
    // 0xc16ff8: branchIfSmi(r16, 0xc1703c)
    //     0xc16ff8: tbz             w16, #0, #0xc1703c
    // 0xc16ffc: r16 = LoadClassIdInstr(r2)
    //     0xc16ffc: ldur            x16, [x2, #-1]
    //     0xc17000: ubfx            x16, x16, #0xc, #0x14
    // 0xc17004: cmp             x16, #0x3d
    // 0xc17008: b.ne            #0xc1703c
    // 0xc1700c: r16 = LoadClassIdInstr(r0)
    //     0xc1700c: ldur            x16, [x0, #-1]
    //     0xc17010: ubfx            x16, x16, #0xc, #0x14
    // 0xc17014: cmp             x16, #0x3d
    // 0xc17018: b.ne            #0xc1703c
    // 0xc1701c: LoadField: r16 = r2->field_7
    //     0xc1701c: ldur            x16, [x2, #7]
    // 0xc17020: LoadField: r17 = r0->field_7
    //     0xc17020: ldur            x17, [x0, #7]
    // 0xc17024: cmp             x16, x17
    // 0xc17028: b.ne            #0xc1703c
    // 0xc1702c: r0 = topLeftCorner()
    //     0xc1702c: bl              #0xc170b8  ; [package:term_glyph/src/generated/top_level.dart] ::topLeftCorner
    // 0xc17030: r2 = "┌"
    //     0xc17030: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c688] "┌"
    //     0xc17034: ldr             x2, [x2, #0x688]
    // 0xc17038: b               #0xc17048
    // 0xc1703c: r0 = bottomLeftCorner()
    //     0xc1703c: bl              #0xc17068  ; [package:term_glyph/src/generated/top_level.dart] ::bottomLeftCorner
    // 0xc17040: r2 = "└"
    //     0xc17040: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c658] "└"
    //     0xc17044: ldr             x2, [x2, #0x658]
    // 0xc17048: ldur            x1, [fp, #-8]
    // 0xc1704c: r0 = write()
    //     0xc1704c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc17050: r0 = Null
    //     0xc17050: mov             x0, NULL
    // 0xc17054: LeaveFrame
    //     0xc17054: mov             SP, fp
    //     0xc17058: ldp             fp, lr, [SP], #0x10
    // 0xc1705c: ret
    //     0xc1705c: ret             
    // 0xc17060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17060: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17064: b               #0xc16fa8
  }
  _ _colorize(/* No info */) {
    // ** addr: 0xc17108, size: 0xb8
    // 0xc17108: EnterFrame
    //     0xc17108: stp             fp, lr, [SP, #-0x10]!
    //     0xc1710c: mov             fp, SP
    // 0xc17110: AllocStack(0x18)
    //     0xc17110: sub             SP, SP, #0x18
    // 0xc17114: CheckStackOverflow
    //     0xc17114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17118: cmp             SP, x16
    //     0xc1711c: b.ls            #0xc171b8
    // 0xc17120: ldr             x0, [fp, #0x20]
    // 0xc17124: LoadField: r3 = r0->field_b
    //     0xc17124: ldur            w3, [x0, #0xb]
    // 0xc17128: DecompressPointer r3
    //     0xc17128: add             x3, x3, HEAP, lsl #32
    // 0xc1712c: stur            x3, [fp, #-8]
    // 0xc17130: cmp             w3, NULL
    // 0xc17134: b.eq            #0xc17154
    // 0xc17138: ldr             x4, [fp, #0x10]
    // 0xc1713c: cmp             w4, NULL
    // 0xc17140: b.eq            #0xc17154
    // 0xc17144: LoadField: r1 = r0->field_23
    //     0xc17144: ldur            w1, [x0, #0x23]
    // 0xc17148: DecompressPointer r1
    //     0xc17148: add             x1, x1, HEAP, lsl #32
    // 0xc1714c: mov             x2, x4
    // 0xc17150: r0 = write()
    //     0xc17150: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc17154: ldur            x1, [fp, #-8]
    // 0xc17158: ldr             x16, [fp, #0x18]
    // 0xc1715c: str             x16, [SP]
    // 0xc17160: ldr             x0, [fp, #0x18]
    // 0xc17164: ClosureCall
    //     0xc17164: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xc17168: ldur            x2, [x0, #0x1f]
    //     0xc1716c: blr             x2
    // 0xc17170: mov             x3, x0
    // 0xc17174: ldur            x0, [fp, #-8]
    // 0xc17178: stur            x3, [fp, #-0x10]
    // 0xc1717c: cmp             w0, NULL
    // 0xc17180: b.eq            #0xc171a8
    // 0xc17184: ldr             x0, [fp, #0x10]
    // 0xc17188: cmp             w0, NULL
    // 0xc1718c: b.eq            #0xc171a8
    // 0xc17190: ldr             x0, [fp, #0x20]
    // 0xc17194: LoadField: r1 = r0->field_23
    //     0xc17194: ldur            w1, [x0, #0x23]
    // 0xc17198: DecompressPointer r1
    //     0xc17198: add             x1, x1, HEAP, lsl #32
    // 0xc1719c: r2 = "[0m"
    //     0xc1719c: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c690] "[0m"
    //     0xc171a0: ldr             x2, [x2, #0x690]
    // 0xc171a4: r0 = write()
    //     0xc171a4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc171a8: ldur            x0, [fp, #-0x10]
    // 0xc171ac: LeaveFrame
    //     0xc171ac: mov             SP, fp
    //     0xc171b0: ldp             fp, lr, [SP], #0x10
    // 0xc171b4: ret
    //     0xc171b4: ret             
    // 0xc171b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc171b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc171bc: b               #0xc17120
  }
  _ _writeFileStart(/* No info */) {
    // ** addr: 0xc171c0, size: 0x1cc
    // 0xc171c0: EnterFrame
    //     0xc171c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc171c4: mov             fp, SP
    // 0xc171c8: AllocStack(0x40)
    //     0xc171c8: sub             SP, SP, #0x40
    // 0xc171cc: SetupParameters(Highlighter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc171cc: mov             x0, x2
    //     0xc171d0: stur            x1, [fp, #-8]
    //     0xc171d4: stur            x2, [fp, #-0x10]
    // 0xc171d8: CheckStackOverflow
    //     0xc171d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc171dc: cmp             SP, x16
    //     0xc171e0: b.ls            #0xc17384
    // 0xc171e4: r1 = 1
    //     0xc171e4: movz            x1, #0x1
    // 0xc171e8: r0 = AllocateContext()
    //     0xc171e8: bl              #0xec126c  ; AllocateContextStub
    // 0xc171ec: mov             x4, x0
    // 0xc171f0: ldur            x3, [fp, #-8]
    // 0xc171f4: stur            x4, [fp, #-0x18]
    // 0xc171f8: StoreField: r4->field_f = r3
    //     0xc171f8: stur            w3, [x4, #0xf]
    // 0xc171fc: LoadField: r0 = r3->field_1f
    //     0xc171fc: ldur            w0, [x3, #0x1f]
    // 0xc17200: DecompressPointer r0
    //     0xc17200: add             x0, x0, HEAP, lsl #32
    // 0xc17204: tbnz            w0, #4, #0xc1725c
    // 0xc17208: ldur            x0, [fp, #-0x10]
    // 0xc1720c: r2 = Null
    //     0xc1720c: mov             x2, NULL
    // 0xc17210: r1 = Null
    //     0xc17210: mov             x1, NULL
    // 0xc17214: cmp             w0, NULL
    // 0xc17218: b.eq            #0xc1724c
    // 0xc1721c: branchIfSmi(r0, 0xc1724c)
    //     0xc1721c: tbz             w0, #0, #0xc1724c
    // 0xc17220: r3 = LoadClassIdInstr(r0)
    //     0xc17220: ldur            x3, [x0, #-1]
    //     0xc17224: ubfx            x3, x3, #0xc, #0x14
    // 0xc17228: cmp             x3, #0x90a
    // 0xc1722c: b.eq            #0xc17254
    // 0xc17230: r17 = 6700
    //     0xc17230: movz            x17, #0x1a2c
    // 0xc17234: cmp             x3, x17
    // 0xc17238: b.eq            #0xc17254
    // 0xc1723c: r17 = -6702
    //     0xc1723c: movn            x17, #0x1a2d
    // 0xc17240: add             x3, x3, x17
    // 0xc17244: cmp             x3, #1
    // 0xc17248: b.ls            #0xc17254
    // 0xc1724c: r0 = false
    //     0xc1724c: add             x0, NULL, #0x30  ; false
    // 0xc17250: b               #0xc17258
    // 0xc17254: r0 = true
    //     0xc17254: add             x0, NULL, #0x20  ; true
    // 0xc17258: tbz             w0, #4, #0xc17280
    // 0xc1725c: r0 = downEnd()
    //     0xc1725c: bl              #0xc18448  ; [package:term_glyph/src/generated/top_level.dart] ::downEnd
    // 0xc17260: r16 = "╷"
    //     0xc17260: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c698] "╷"
    //     0xc17264: ldr             x16, [x16, #0x698]
    // 0xc17268: str             x16, [SP]
    // 0xc1726c: ldur            x1, [fp, #-8]
    // 0xc17270: r4 = const [0, 0x2, 0x1, 0x1, end, 0x1, null]
    //     0xc17270: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c5b0] List(7) [0, 0x2, 0x1, 0x1, "end", 0x1, Null]
    //     0xc17274: ldr             x4, [x4, #0x5b0]
    // 0xc17278: r0 = _writeSidebar()
    //     0xc17278: bl              #0xc15380  ; [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar
    // 0xc1727c: b               #0xc17360
    // 0xc17280: ldur            x1, [fp, #-8]
    // 0xc17284: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc17284: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc17288: ldr             x0, [x0, #0x2ea8]
    //     0xc1728c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc17290: cmp             w0, w16
    //     0xc17294: b.ne            #0xc172a4
    //     0xc17298: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc1729c: ldr             x2, [x2, #0x580]
    //     0xc172a0: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc172a4: r16 = "┌"
    //     0xc172a4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c688] "┌"
    //     0xc172a8: ldr             x16, [x16, #0x688]
    // 0xc172ac: str             x16, [SP]
    // 0xc172b0: ldur            x1, [fp, #-8]
    // 0xc172b4: r4 = const [0, 0x2, 0x1, 0x1, end, 0x1, null]
    //     0xc172b4: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1c5b0] List(7) [0, 0x2, 0x1, 0x1, "end", 0x1, Null]
    //     0xc172b8: ldr             x4, [x4, #0x5b0]
    // 0xc172bc: r0 = _writeSidebar()
    //     0xc172bc: bl              #0xc15380  ; [package:source_span/src/highlighter.dart] Highlighter::_writeSidebar
    // 0xc172c0: ldur            x2, [fp, #-0x18]
    // 0xc172c4: r1 = Function '<anonymous closure>':.
    //     0xc172c4: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c6a0] AnonymousClosure: (0xc18498), in [package:source_span/src/highlighter.dart] Highlighter::_writeFileStart (0xc171c0)
    //     0xc172c8: ldr             x1, [x1, #0x6a0]
    // 0xc172cc: r0 = AllocateClosure()
    //     0xc172cc: bl              #0xec1630  ; AllocateClosureStub
    // 0xc172d0: r16 = <void?>
    //     0xc172d0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xc172d4: ldur            lr, [fp, #-8]
    // 0xc172d8: stp             lr, x16, [SP, #0x10]
    // 0xc172dc: r16 = "[34m"
    //     0xc172dc: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c598] "[34m"
    //     0xc172e0: ldr             x16, [x16, #0x598]
    // 0xc172e4: stp             x16, x0, [SP]
    // 0xc172e8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc172e8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc172ec: r0 = _colorize()
    //     0xc172ec: bl              #0xc17108  ; [package:source_span/src/highlighter.dart] Highlighter::_colorize
    // 0xc172f0: ldur            x0, [fp, #-8]
    // 0xc172f4: LoadField: r3 = r0->field_23
    //     0xc172f4: ldur            w3, [x0, #0x23]
    // 0xc172f8: DecompressPointer r3
    //     0xc172f8: add             x3, x3, HEAP, lsl #32
    // 0xc172fc: stur            x3, [fp, #-0x18]
    // 0xc17300: r1 = Null
    //     0xc17300: mov             x1, NULL
    // 0xc17304: r2 = 4
    //     0xc17304: movz            x2, #0x4
    // 0xc17308: r0 = AllocateArray()
    //     0xc17308: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1730c: stur            x0, [fp, #-0x20]
    // 0xc17310: r16 = " "
    //     0xc17310: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc17314: StoreField: r0->field_f = r16
    //     0xc17314: stur            w16, [x0, #0xf]
    // 0xc17318: ldur            x1, [fp, #-0x10]
    // 0xc1731c: r0 = prettyUri()
    //     0xc1731c: bl              #0xc1738c  ; [package:path/path.dart] ::prettyUri
    // 0xc17320: ldur            x1, [fp, #-0x20]
    // 0xc17324: ArrayStore: r1[1] = r0  ; List_4
    //     0xc17324: add             x25, x1, #0x13
    //     0xc17328: str             w0, [x25]
    //     0xc1732c: tbz             w0, #0, #0xc17348
    //     0xc17330: ldurb           w16, [x1, #-1]
    //     0xc17334: ldurb           w17, [x0, #-1]
    //     0xc17338: and             x16, x17, x16, lsr #2
    //     0xc1733c: tst             x16, HEAP, lsr #32
    //     0xc17340: b.eq            #0xc17348
    //     0xc17344: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc17348: ldur            x16, [fp, #-0x20]
    // 0xc1734c: str             x16, [SP]
    // 0xc17350: r0 = _interpolate()
    //     0xc17350: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc17354: ldur            x1, [fp, #-0x18]
    // 0xc17358: mov             x2, x0
    // 0xc1735c: r0 = write()
    //     0xc1735c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc17360: ldur            x0, [fp, #-8]
    // 0xc17364: LoadField: r1 = r0->field_23
    //     0xc17364: ldur            w1, [x0, #0x23]
    // 0xc17368: DecompressPointer r1
    //     0xc17368: add             x1, x1, HEAP, lsl #32
    // 0xc1736c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc1736c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc17370: r0 = writeln()
    //     0xc17370: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0xc17374: r0 = Null
    //     0xc17374: mov             x0, NULL
    // 0xc17378: LeaveFrame
    //     0xc17378: mov             SP, fp
    //     0xc1737c: ldp             fp, lr, [SP], #0x10
    // 0xc17380: ret
    //     0xc17380: ret             
    // 0xc17384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17384: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17388: b               #0xc171e4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc18498, size: 0xb8
    // 0xc18498: EnterFrame
    //     0xc18498: stp             fp, lr, [SP, #-0x10]!
    //     0xc1849c: mov             fp, SP
    // 0xc184a0: AllocStack(0x18)
    //     0xc184a0: sub             SP, SP, #0x18
    // 0xc184a4: SetupParameters()
    //     0xc184a4: ldr             x0, [fp, #0x10]
    //     0xc184a8: ldur            w1, [x0, #0x17]
    //     0xc184ac: add             x1, x1, HEAP, lsl #32
    // 0xc184b0: CheckStackOverflow
    //     0xc184b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc184b4: cmp             SP, x16
    //     0xc184b8: b.ls            #0xc18548
    // 0xc184bc: LoadField: r0 = r1->field_f
    //     0xc184bc: ldur            w0, [x1, #0xf]
    // 0xc184c0: DecompressPointer r0
    //     0xc184c0: add             x0, x0, HEAP, lsl #32
    // 0xc184c4: LoadField: r1 = r0->field_23
    //     0xc184c4: ldur            w1, [x0, #0x23]
    // 0xc184c8: DecompressPointer r1
    //     0xc184c8: add             x1, x1, HEAP, lsl #32
    // 0xc184cc: stur            x1, [fp, #-8]
    // 0xc184d0: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc184d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc184d4: ldr             x0, [x0, #0x2ea8]
    //     0xc184d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc184dc: cmp             w0, w16
    //     0xc184e0: b.ne            #0xc184f0
    //     0xc184e4: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc184e8: ldr             x2, [x2, #0x580]
    //     0xc184ec: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc184f0: r1 = "─"
    //     0xc184f0: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5f0] "─"
    //     0xc184f4: ldr             x1, [x1, #0x5f0]
    // 0xc184f8: r2 = 2
    //     0xc184f8: movz            x2, #0x2
    // 0xc184fc: r0 = *()
    //     0xc184fc: bl              #0xebce0c  ; [dart:core] _TwoByteString::*
    // 0xc18500: r1 = Null
    //     0xc18500: mov             x1, NULL
    // 0xc18504: r2 = 4
    //     0xc18504: movz            x2, #0x4
    // 0xc18508: stur            x0, [fp, #-0x10]
    // 0xc1850c: r0 = AllocateArray()
    //     0xc1850c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc18510: mov             x1, x0
    // 0xc18514: ldur            x0, [fp, #-0x10]
    // 0xc18518: StoreField: r1->field_f = r0
    //     0xc18518: stur            w0, [x1, #0xf]
    // 0xc1851c: r16 = ">"
    //     0xc1851c: ldr             x16, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xc18520: StoreField: r1->field_13 = r16
    //     0xc18520: stur            w16, [x1, #0x13]
    // 0xc18524: str             x1, [SP]
    // 0xc18528: r0 = _interpolate()
    //     0xc18528: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1852c: ldur            x1, [fp, #-8]
    // 0xc18530: mov             x2, x0
    // 0xc18534: r0 = write()
    //     0xc18534: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc18538: r0 = Null
    //     0xc18538: mov             x0, NULL
    // 0xc1853c: LeaveFrame
    //     0xc1853c: mov             SP, fp
    //     0xc18540: ldp             fp, lr, [SP], #0x10
    // 0xc18544: ret
    //     0xc18544: ret             
    // 0xc18548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18548: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1854c: b               #0xc184bc
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xc18550, size: 0xc0
    // 0xc18550: EnterFrame
    //     0xc18550: stp             fp, lr, [SP, #-0x10]!
    //     0xc18554: mov             fp, SP
    // 0xc18558: AllocStack(0x10)
    //     0xc18558: sub             SP, SP, #0x10
    // 0xc1855c: SetupParameters()
    //     0xc1855c: ldr             x0, [fp, #0x10]
    //     0xc18560: ldur            w3, [x0, #0x17]
    //     0xc18564: add             x3, x3, HEAP, lsl #32
    //     0xc18568: stur            x3, [fp, #-0x10]
    // 0xc1856c: CheckStackOverflow
    //     0xc1856c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18570: cmp             SP, x16
    //     0xc18574: b.ls            #0xc18608
    // 0xc18578: LoadField: r0 = r3->field_f
    //     0xc18578: ldur            w0, [x3, #0xf]
    // 0xc1857c: DecompressPointer r0
    //     0xc1857c: add             x0, x0, HEAP, lsl #32
    // 0xc18580: LoadField: r4 = r0->field_23
    //     0xc18580: ldur            w4, [x0, #0x23]
    // 0xc18584: DecompressPointer r4
    //     0xc18584: add             x4, x4, HEAP, lsl #32
    // 0xc18588: stur            x4, [fp, #-8]
    // 0xc1858c: LoadField: r1 = r3->field_13
    //     0xc1858c: ldur            w1, [x3, #0x13]
    // 0xc18590: DecompressPointer r1
    //     0xc18590: add             x1, x1, HEAP, lsl #32
    // 0xc18594: cmp             w1, NULL
    // 0xc18598: b.ne            #0xc185a0
    // 0xc1859c: r1 = ""
    //     0xc1859c: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc185a0: LoadField: r2 = r0->field_f
    //     0xc185a0: ldur            x2, [x0, #0xf]
    // 0xc185a4: r0 = LoadClassIdInstr(r1)
    //     0xc185a4: ldur            x0, [x1, #-1]
    //     0xc185a8: ubfx            x0, x0, #0xc, #0x14
    // 0xc185ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc185ac: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc185b0: r0 = GDT[cid_x0 + -0xfe8]()
    //     0xc185b0: sub             lr, x0, #0xfe8
    //     0xc185b4: ldr             lr, [x21, lr, lsl #3]
    //     0xc185b8: blr             lr
    // 0xc185bc: ldur            x1, [fp, #-8]
    // 0xc185c0: mov             x2, x0
    // 0xc185c4: r0 = write()
    //     0xc185c4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc185c8: ldur            x0, [fp, #-0x10]
    // 0xc185cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc185cc: ldur            w1, [x0, #0x17]
    // 0xc185d0: DecompressPointer r1
    //     0xc185d0: add             x1, x1, HEAP, lsl #32
    // 0xc185d4: cmp             w1, NULL
    // 0xc185d8: b.ne            #0xc185ec
    // 0xc185dc: r0 = verticalLine()
    //     0xc185dc: bl              #0xc18610  ; [package:term_glyph/src/generated/top_level.dart] ::verticalLine
    // 0xc185e0: r2 = "│"
    //     0xc185e0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c5c0] "│"
    //     0xc185e4: ldr             x2, [x2, #0x5c0]
    // 0xc185e8: b               #0xc185f0
    // 0xc185ec: mov             x2, x1
    // 0xc185f0: ldur            x1, [fp, #-8]
    // 0xc185f4: r0 = write()
    //     0xc185f4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc185f8: r0 = Null
    //     0xc185f8: mov             x0, NULL
    // 0xc185fc: LeaveFrame
    //     0xc185fc: mov             SP, fp
    //     0xc18600: ldp             fp, lr, [SP], #0x10
    // 0xc18604: ret
    //     0xc18604: ret             
    // 0xc18608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18608: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1860c: b               #0xc18578
  }
  _ Highlighter(/* No info */) {
    // ** addr: 0xc18698, size: 0x9c
    // 0xc18698: EnterFrame
    //     0xc18698: stp             fp, lr, [SP, #-0x10]!
    //     0xc1869c: mov             fp, SP
    // 0xc186a0: AllocStack(0x18)
    //     0xc186a0: sub             SP, SP, #0x18
    // 0xc186a4: SetupParameters(Highlighter this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc186a4: stur            x1, [fp, #-8]
    //     0xc186a8: stur            x2, [fp, #-0x10]
    // 0xc186ac: CheckStackOverflow
    //     0xc186ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc186b0: cmp             SP, x16
    //     0xc186b4: b.ls            #0xc1872c
    // 0xc186b8: r0 = _Highlight()
    //     0xc186b8: bl              #0xc1b79c  ; Allocate_HighlightStub -> _Highlight (size=0x14)
    // 0xc186bc: mov             x1, x0
    // 0xc186c0: ldur            x2, [fp, #-0x10]
    // 0xc186c4: stur            x0, [fp, #-0x10]
    // 0xc186c8: r0 = _Highlight()
    //     0xc186c8: bl              #0xc19a34  ; [package:source_span/src/highlighter.dart] _Highlight::_Highlight
    // 0xc186cc: r1 = Null
    //     0xc186cc: mov             x1, NULL
    // 0xc186d0: r2 = 2
    //     0xc186d0: movz            x2, #0x2
    // 0xc186d4: r0 = AllocateArray()
    //     0xc186d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc186d8: mov             x2, x0
    // 0xc186dc: ldur            x0, [fp, #-0x10]
    // 0xc186e0: stur            x2, [fp, #-0x18]
    // 0xc186e4: StoreField: r2->field_f = r0
    //     0xc186e4: stur            w0, [x2, #0xf]
    // 0xc186e8: r1 = <_Highlight>
    //     0xc186e8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5d8] TypeArguments: <_Highlight>
    //     0xc186ec: ldr             x1, [x1, #0x5d8]
    // 0xc186f0: r0 = AllocateGrowableArray()
    //     0xc186f0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xc186f4: mov             x1, x0
    // 0xc186f8: ldur            x0, [fp, #-0x18]
    // 0xc186fc: StoreField: r1->field_f = r0
    //     0xc186fc: stur            w0, [x1, #0xf]
    // 0xc18700: r0 = 2
    //     0xc18700: movz            x0, #0x2
    // 0xc18704: StoreField: r1->field_b = r0
    //     0xc18704: stur            w0, [x1, #0xb]
    // 0xc18708: r0 = _collateLines()
    //     0xc18708: bl              #0xc18d0c  ; [package:source_span/src/highlighter.dart] Highlighter::_collateLines
    // 0xc1870c: ldur            x1, [fp, #-8]
    // 0xc18710: mov             x2, x0
    // 0xc18714: r3 = Null
    //     0xc18714: mov             x3, NULL
    // 0xc18718: r0 = Highlighter._()
    //     0xc18718: bl              #0xc18734  ; [package:source_span/src/highlighter.dart] Highlighter::Highlighter._
    // 0xc1871c: r0 = Null
    //     0xc1871c: mov             x0, NULL
    // 0xc18720: LeaveFrame
    //     0xc18720: mov             SP, fp
    //     0xc18724: ldp             fp, lr, [SP], #0x10
    // 0xc18728: ret
    //     0xc18728: ret             
    // 0xc1872c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1872c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18730: b               #0xc186b8
  }
  _ Highlighter._(/* No info */) {
    // ** addr: 0xc18734, size: 0x208
    // 0xc18734: EnterFrame
    //     0xc18734: stp             fp, lr, [SP, #-0x10]!
    //     0xc18738: mov             fp, SP
    // 0xc1873c: AllocStack(0x38)
    //     0xc1873c: sub             SP, SP, #0x38
    // 0xc18740: SetupParameters(Highlighter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xc18740: stur            x1, [fp, #-8]
    //     0xc18744: mov             x16, x2
    //     0xc18748: mov             x2, x1
    //     0xc1874c: mov             x1, x16
    //     0xc18750: mov             x0, x3
    //     0xc18754: stur            x1, [fp, #-0x10]
    //     0xc18758: stur            x3, [fp, #-0x18]
    // 0xc1875c: CheckStackOverflow
    //     0xc1875c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18760: cmp             SP, x16
    //     0xc18764: b.ls            #0xc18934
    // 0xc18768: r0 = StringBuffer()
    //     0xc18768: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc1876c: mov             x1, x0
    // 0xc18770: stur            x0, [fp, #-0x20]
    // 0xc18774: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc18774: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc18778: r0 = StringBuffer()
    //     0xc18778: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc1877c: ldur            x0, [fp, #-0x20]
    // 0xc18780: ldur            x2, [fp, #-8]
    // 0xc18784: StoreField: r2->field_23 = r0
    //     0xc18784: stur            w0, [x2, #0x23]
    //     0xc18788: ldurb           w16, [x2, #-1]
    //     0xc1878c: ldurb           w17, [x0, #-1]
    //     0xc18790: and             x16, x17, x16, lsr #2
    //     0xc18794: tst             x16, HEAP, lsr #32
    //     0xc18798: b.eq            #0xc187a0
    //     0xc1879c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc187a0: ldur            x0, [fp, #-0x10]
    // 0xc187a4: StoreField: r2->field_7 = r0
    //     0xc187a4: stur            w0, [x2, #7]
    //     0xc187a8: ldurb           w16, [x2, #-1]
    //     0xc187ac: ldurb           w17, [x0, #-1]
    //     0xc187b0: and             x16, x17, x16, lsr #2
    //     0xc187b4: tst             x16, HEAP, lsr #32
    //     0xc187b8: b.eq            #0xc187c0
    //     0xc187bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc187c0: ldur            x0, [fp, #-0x18]
    // 0xc187c4: StoreField: r2->field_b = r0
    //     0xc187c4: stur            w0, [x2, #0xb]
    //     0xc187c8: ldurb           w16, [x2, #-1]
    //     0xc187cc: ldurb           w17, [x0, #-1]
    //     0xc187d0: and             x16, x17, x16, lsr #2
    //     0xc187d4: tst             x16, HEAP, lsr #32
    //     0xc187d8: b.eq            #0xc187e0
    //     0xc187dc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xc187e0: ldur            x1, [fp, #-0x10]
    // 0xc187e4: r0 = last()
    //     0xc187e4: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0xc187e8: LoadField: r1 = r0->field_b
    //     0xc187e8: ldur            x1, [x0, #0xb]
    // 0xc187ec: add             x2, x1, #1
    // 0xc187f0: r0 = BoxInt64Instr(r2)
    //     0xc187f0: sbfiz           x0, x2, #1, #0x1f
    //     0xc187f4: cmp             x2, x0, asr #1
    //     0xc187f8: b.eq            #0xc18804
    //     0xc187fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc18800: stur            x2, [x0, #7]
    // 0xc18804: r1 = 60
    //     0xc18804: movz            x1, #0x3c
    // 0xc18808: branchIfSmi(r0, 0xc18814)
    //     0xc18808: tbz             w0, #0, #0xc18814
    // 0xc1880c: r1 = LoadClassIdInstr(r0)
    //     0xc1880c: ldur            x1, [x0, #-1]
    //     0xc18810: ubfx            x1, x1, #0xc, #0x14
    // 0xc18814: str             x0, [SP]
    // 0xc18818: mov             x0, x1
    // 0xc1881c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc1881c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc18820: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc18820: movz            x17, #0x2b03
    //     0xc18824: add             lr, x0, x17
    //     0xc18828: ldr             lr, [x21, lr, lsl #3]
    //     0xc1882c: blr             lr
    // 0xc18830: LoadField: r2 = r0->field_7
    //     0xc18830: ldur            w2, [x0, #7]
    // 0xc18834: ldur            x1, [fp, #-0x10]
    // 0xc18838: stur            x2, [fp, #-0x18]
    // 0xc1883c: r0 = _contiguous()
    //     0xc1883c: bl              #0xc18b3c  ; [package:source_span/src/highlighter.dart] Highlighter::_contiguous
    // 0xc18840: tst             x0, #0x10
    // 0xc18844: cset            x1, eq
    // 0xc18848: sub             x1, x1, #1
    // 0xc1884c: and             x1, x1, #6
    // 0xc18850: ldur            x0, [fp, #-0x18]
    // 0xc18854: r2 = LoadInt32Instr(r0)
    //     0xc18854: sbfx            x2, x0, #1, #0x1f
    // 0xc18858: r0 = LoadInt32Instr(r1)
    //     0xc18858: sbfx            x0, x1, #1, #0x1f
    // 0xc1885c: cmp             x2, x0
    // 0xc18860: b.le            #0xc1886c
    // 0xc18864: mov             x1, x2
    // 0xc18868: b               #0xc18890
    // 0xc1886c: cmp             x2, x0
    // 0xc18870: b.ge            #0xc1887c
    // 0xc18874: mov             x1, x0
    // 0xc18878: b               #0xc18890
    // 0xc1887c: cbnz            x0, #0xc1888c
    // 0xc18880: tbz             x2, #0x3f, #0xc1888c
    // 0xc18884: mov             x1, x0
    // 0xc18888: b               #0xc18890
    // 0xc1888c: mov             x1, x2
    // 0xc18890: ldur            x0, [fp, #-8]
    // 0xc18894: add             x2, x1, #1
    // 0xc18898: StoreField: r0->field_f = r2
    //     0xc18898: stur            x2, [x0, #0xf]
    // 0xc1889c: r1 = Function '<anonymous closure>':.
    //     0xc1889c: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c6b0] AnonymousClosure: (0xc18c74), in [package:source_span/src/highlighter.dart] Highlighter::Highlighter._ (0xc18734)
    //     0xc188a0: ldr             x1, [x1, #0x6b0]
    // 0xc188a4: r2 = Null
    //     0xc188a4: mov             x2, NULL
    // 0xc188a8: r0 = AllocateClosure()
    //     0xc188a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xc188ac: r16 = <int>
    //     0xc188ac: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc188b0: ldur            lr, [fp, #-0x10]
    // 0xc188b4: stp             lr, x16, [SP, #8]
    // 0xc188b8: str             x0, [SP]
    // 0xc188bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc188bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc188c0: r0 = map()
    //     0xc188c0: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xc188c4: mov             x1, x0
    // 0xc188c8: r2 = Closure: (int, int) => int from Function 'max': static.
    //     0xc188c8: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1bae8] Closure: (int, int) => int from Function 'max': static. (0x7e54fb034a64)
    //     0xc188cc: ldr             x2, [x2, #0xae8]
    // 0xc188d0: r0 = reduce()
    //     0xc188d0: bl              #0x7f069c  ; [dart:_internal] ListIterable::reduce
    // 0xc188d4: r1 = LoadInt32Instr(r0)
    //     0xc188d4: sbfx            x1, x0, #1, #0x1f
    //     0xc188d8: tbz             w0, #0, #0xc188e0
    //     0xc188dc: ldur            x1, [x0, #7]
    // 0xc188e0: ldur            x0, [fp, #-8]
    // 0xc188e4: ArrayStore: r0[0] = r1  ; List_8
    //     0xc188e4: stur            x1, [x0, #0x17]
    // 0xc188e8: r1 = Function '<anonymous closure>':.
    //     0xc188e8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c6b8] Function: [dart:async] _BufferingStreamSubscription::_onDone (0xc1f1bc)
    //     0xc188ec: ldr             x1, [x1, #0x6b8]
    // 0xc188f0: r2 = Null
    //     0xc188f0: mov             x2, NULL
    // 0xc188f4: r0 = AllocateClosure()
    //     0xc188f4: bl              #0xec1630  ; AllocateClosureStub
    // 0xc188f8: r16 = <Object?>
    //     0xc188f8: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xc188fc: ldur            lr, [fp, #-0x10]
    // 0xc18900: stp             lr, x16, [SP, #8]
    // 0xc18904: str             x0, [SP]
    // 0xc18908: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc18908: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc1890c: r0 = map()
    //     0xc1890c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xc18910: mov             x1, x0
    // 0xc18914: r0 = isAllTheSame()
    //     0xc18914: bl              #0xc1893c  ; [package:source_span/src/utils.dart] ::isAllTheSame
    // 0xc18918: eor             x1, x0, #0x10
    // 0xc1891c: ldur            x2, [fp, #-8]
    // 0xc18920: StoreField: r2->field_1f = r1
    //     0xc18920: stur            w1, [x2, #0x1f]
    // 0xc18924: r0 = Null
    //     0xc18924: mov             x0, NULL
    // 0xc18928: LeaveFrame
    //     0xc18928: mov             SP, fp
    //     0xc1892c: ldp             fp, lr, [SP], #0x10
    // 0xc18930: ret
    //     0xc18930: ret             
    // 0xc18934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18934: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18938: b               #0xc18768
  }
  static _ _contiguous(/* No info */) {
    // ** addr: 0xc18b3c, size: 0x118
    // 0xc18b3c: EnterFrame
    //     0xc18b3c: stp             fp, lr, [SP, #-0x10]!
    //     0xc18b40: mov             fp, SP
    // 0xc18b44: AllocStack(0x20)
    //     0xc18b44: sub             SP, SP, #0x20
    // 0xc18b48: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */)
    //     0xc18b48: mov             x2, x1
    //     0xc18b4c: stur            x1, [fp, #-0x10]
    // 0xc18b50: CheckStackOverflow
    //     0xc18b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18b54: cmp             SP, x16
    //     0xc18b58: b.ls            #0xc18c3c
    // 0xc18b5c: r3 = 0
    //     0xc18b5c: movz            x3, #0
    // 0xc18b60: CheckStackOverflow
    //     0xc18b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18b64: cmp             SP, x16
    //     0xc18b68: b.ls            #0xc18c44
    // 0xc18b6c: LoadField: r0 = r2->field_b
    //     0xc18b6c: ldur            w0, [x2, #0xb]
    // 0xc18b70: r4 = LoadInt32Instr(r0)
    //     0xc18b70: sbfx            x4, x0, #1, #0x1f
    // 0xc18b74: sub             x0, x4, #1
    // 0xc18b78: cmp             x3, x0
    // 0xc18b7c: b.ge            #0xc18c2c
    // 0xc18b80: mov             x0, x4
    // 0xc18b84: mov             x1, x3
    // 0xc18b88: cmp             x1, x0
    // 0xc18b8c: b.hs            #0xc18c4c
    // 0xc18b90: LoadField: r5 = r2->field_f
    //     0xc18b90: ldur            w5, [x2, #0xf]
    // 0xc18b94: DecompressPointer r5
    //     0xc18b94: add             x5, x5, HEAP, lsl #32
    // 0xc18b98: ArrayLoad: r6 = r5[r3]  ; Unknown_4
    //     0xc18b98: add             x16, x5, x3, lsl #2
    //     0xc18b9c: ldur            w6, [x16, #0xf]
    // 0xc18ba0: DecompressPointer r6
    //     0xc18ba0: add             x6, x6, HEAP, lsl #32
    // 0xc18ba4: add             x7, x3, #1
    // 0xc18ba8: mov             x0, x4
    // 0xc18bac: mov             x1, x7
    // 0xc18bb0: stur            x7, [fp, #-8]
    // 0xc18bb4: cmp             x1, x0
    // 0xc18bb8: b.hs            #0xc18c50
    // 0xc18bbc: ArrayLoad: r0 = r5[r7]  ; Unknown_4
    //     0xc18bbc: add             x16, x5, x7, lsl #2
    //     0xc18bc0: ldur            w0, [x16, #0xf]
    // 0xc18bc4: DecompressPointer r0
    //     0xc18bc4: add             x0, x0, HEAP, lsl #32
    // 0xc18bc8: LoadField: r1 = r6->field_b
    //     0xc18bc8: ldur            x1, [x6, #0xb]
    // 0xc18bcc: add             x3, x1, #1
    // 0xc18bd0: LoadField: r1 = r0->field_b
    //     0xc18bd0: ldur            x1, [x0, #0xb]
    // 0xc18bd4: cmp             x3, x1
    // 0xc18bd8: b.eq            #0xc18c20
    // 0xc18bdc: LoadField: r1 = r6->field_13
    //     0xc18bdc: ldur            w1, [x6, #0x13]
    // 0xc18be0: DecompressPointer r1
    //     0xc18be0: add             x1, x1, HEAP, lsl #32
    // 0xc18be4: LoadField: r3 = r0->field_13
    //     0xc18be4: ldur            w3, [x0, #0x13]
    // 0xc18be8: DecompressPointer r3
    //     0xc18be8: add             x3, x3, HEAP, lsl #32
    // 0xc18bec: r0 = 60
    //     0xc18bec: movz            x0, #0x3c
    // 0xc18bf0: branchIfSmi(r1, 0xc18bfc)
    //     0xc18bf0: tbz             w1, #0, #0xc18bfc
    // 0xc18bf4: r0 = LoadClassIdInstr(r1)
    //     0xc18bf4: ldur            x0, [x1, #-1]
    //     0xc18bf8: ubfx            x0, x0, #0xc, #0x14
    // 0xc18bfc: stp             x3, x1, [SP]
    // 0xc18c00: mov             lr, x0
    // 0xc18c04: ldr             lr, [x21, lr, lsl #3]
    // 0xc18c08: blr             lr
    // 0xc18c0c: tbnz            w0, #4, #0xc18c20
    // 0xc18c10: r0 = false
    //     0xc18c10: add             x0, NULL, #0x30  ; false
    // 0xc18c14: LeaveFrame
    //     0xc18c14: mov             SP, fp
    //     0xc18c18: ldp             fp, lr, [SP], #0x10
    // 0xc18c1c: ret
    //     0xc18c1c: ret             
    // 0xc18c20: ldur            x3, [fp, #-8]
    // 0xc18c24: ldur            x2, [fp, #-0x10]
    // 0xc18c28: b               #0xc18b60
    // 0xc18c2c: r0 = true
    //     0xc18c2c: add             x0, NULL, #0x20  ; true
    // 0xc18c30: LeaveFrame
    //     0xc18c30: mov             SP, fp
    //     0xc18c34: ldp             fp, lr, [SP], #0x10
    // 0xc18c38: ret
    //     0xc18c38: ret             
    // 0xc18c3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18c3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18c40: b               #0xc18b5c
    // 0xc18c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18c44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18c48: b               #0xc18b6c
    // 0xc18c4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18c4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc18c50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc18c50: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, _Line) {
    // ** addr: 0xc18c74, size: 0x60
    // 0xc18c74: EnterFrame
    //     0xc18c74: stp             fp, lr, [SP, #-0x10]!
    //     0xc18c78: mov             fp, SP
    // 0xc18c7c: AllocStack(0x10)
    //     0xc18c7c: sub             SP, SP, #0x10
    // 0xc18c80: CheckStackOverflow
    //     0xc18c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18c84: cmp             SP, x16
    //     0xc18c88: b.ls            #0xc18ccc
    // 0xc18c8c: ldr             x0, [fp, #0x10]
    // 0xc18c90: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc18c90: ldur            w3, [x0, #0x17]
    // 0xc18c94: DecompressPointer r3
    //     0xc18c94: add             x3, x3, HEAP, lsl #32
    // 0xc18c98: stur            x3, [fp, #-8]
    // 0xc18c9c: r1 = Function '<anonymous closure>':.
    //     0xc18c9c: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c6c0] AnonymousClosure: (0xc18cd4), in [package:source_span/src/highlighter.dart] Highlighter::Highlighter._ (0xc18734)
    //     0xc18ca0: ldr             x1, [x1, #0x6c0]
    // 0xc18ca4: r2 = Null
    //     0xc18ca4: mov             x2, NULL
    // 0xc18ca8: r0 = AllocateClosure()
    //     0xc18ca8: bl              #0xec1630  ; AllocateClosureStub
    // 0xc18cac: ldur            x1, [fp, #-8]
    // 0xc18cb0: mov             x2, x0
    // 0xc18cb4: r0 = where()
    //     0xc18cb4: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xc18cb8: str             x0, [SP]
    // 0xc18cbc: r0 = length()
    //     0xc18cbc: bl              #0x913c28  ; [dart:core] Iterable::length
    // 0xc18cc0: LeaveFrame
    //     0xc18cc0: mov             SP, fp
    //     0xc18cc4: ldp             fp, lr, [SP], #0x10
    // 0xc18cc8: ret
    //     0xc18cc8: ret             
    // 0xc18ccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18ccc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18cd0: b               #0xc18c8c
  }
  [closure] bool <anonymous closure>(dynamic, _Highlight) {
    // ** addr: 0xc18cd4, size: 0x38
    // 0xc18cd4: EnterFrame
    //     0xc18cd4: stp             fp, lr, [SP, #-0x10]!
    //     0xc18cd8: mov             fp, SP
    // 0xc18cdc: CheckStackOverflow
    //     0xc18cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18ce0: cmp             SP, x16
    //     0xc18ce4: b.ls            #0xc18d04
    // 0xc18ce8: ldr             x0, [fp, #0x10]
    // 0xc18cec: LoadField: r1 = r0->field_7
    //     0xc18cec: ldur            w1, [x0, #7]
    // 0xc18cf0: DecompressPointer r1
    //     0xc18cf0: add             x1, x1, HEAP, lsl #32
    // 0xc18cf4: r0 = isMultiline()
    //     0xc18cf4: bl              #0xc152bc  ; [package:source_span/src/utils.dart] ::isMultiline
    // 0xc18cf8: LeaveFrame
    //     0xc18cf8: mov             SP, fp
    //     0xc18cfc: ldp             fp, lr, [SP], #0x10
    // 0xc18d00: ret
    //     0xc18d00: ret             
    // 0xc18d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18d04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18d08: b               #0xc18ce8
  }
  static _ _collateLines(/* No info */) {
    // ** addr: 0xc18d0c, size: 0x1c0
    // 0xc18d0c: EnterFrame
    //     0xc18d0c: stp             fp, lr, [SP, #-0x10]!
    //     0xc18d10: mov             fp, SP
    // 0xc18d14: AllocStack(0x38)
    //     0xc18d14: sub             SP, SP, #0x38
    // 0xc18d18: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc18d18: mov             x0, x1
    //     0xc18d1c: stur            x1, [fp, #-8]
    // 0xc18d20: CheckStackOverflow
    //     0xc18d20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18d24: cmp             SP, x16
    //     0xc18d28: b.ls            #0xc18ebc
    // 0xc18d2c: r1 = Function '<anonymous closure>': static.
    //     0xc18d2c: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c6d8] AnonymousClosure: static (0xc199e8), in [package:source_span/src/highlighter.dart] Highlighter::_collateLines (0xc18d0c)
    //     0xc18d30: ldr             x1, [x1, #0x6d8]
    // 0xc18d34: r2 = Null
    //     0xc18d34: mov             x2, NULL
    // 0xc18d38: r0 = AllocateClosure()
    //     0xc18d38: bl              #0xec1630  ; AllocateClosureStub
    // 0xc18d3c: r16 = <_Highlight, Object>
    //     0xc18d3c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c6e0] TypeArguments: <_Highlight, Object>
    //     0xc18d40: ldr             x16, [x16, #0x6e0]
    // 0xc18d44: ldur            lr, [fp, #-8]
    // 0xc18d48: stp             lr, x16, [SP, #8]
    // 0xc18d4c: str             x0, [SP]
    // 0xc18d50: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xc18d50: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xc18d54: r0 = groupBy()
    //     0xc18d54: bl              #0x737f78  ; [package:collection/src/functions.dart] ::groupBy
    // 0xc18d58: stur            x0, [fp, #-8]
    // 0xc18d5c: LoadField: r2 = r0->field_7
    //     0xc18d5c: ldur            w2, [x0, #7]
    // 0xc18d60: DecompressPointer r2
    //     0xc18d60: add             x2, x2, HEAP, lsl #32
    // 0xc18d64: r1 = Null
    //     0xc18d64: mov             x1, NULL
    // 0xc18d68: r3 = <X1>
    //     0xc18d68: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0xc18d6c: r0 = Null
    //     0xc18d6c: mov             x0, NULL
    // 0xc18d70: cmp             x2, x0
    // 0xc18d74: b.eq            #0xc18d84
    // 0xc18d78: r30 = InstantiateTypeArgumentsStub
    //     0xc18d78: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xc18d7c: LoadField: r30 = r30->field_7
    //     0xc18d7c: ldur            lr, [lr, #7]
    // 0xc18d80: blr             lr
    // 0xc18d84: mov             x1, x0
    // 0xc18d88: r0 = _CompactIterable()
    //     0xc18d88: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xc18d8c: mov             x1, x0
    // 0xc18d90: ldur            x0, [fp, #-8]
    // 0xc18d94: StoreField: r1->field_b = r0
    //     0xc18d94: stur            w0, [x1, #0xb]
    // 0xc18d98: r2 = -1
    //     0xc18d98: movn            x2, #0
    // 0xc18d9c: StoreField: r1->field_f = r2
    //     0xc18d9c: stur            x2, [x1, #0xf]
    // 0xc18da0: r2 = 2
    //     0xc18da0: movz            x2, #0x2
    // 0xc18da4: ArrayStore: r1[0] = r2  ; List_8
    //     0xc18da4: stur            x2, [x1, #0x17]
    // 0xc18da8: r0 = iterator()
    //     0xc18da8: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xc18dac: stur            x0, [fp, #-0x18]
    // 0xc18db0: LoadField: r2 = r0->field_7
    //     0xc18db0: ldur            w2, [x0, #7]
    // 0xc18db4: DecompressPointer r2
    //     0xc18db4: add             x2, x2, HEAP, lsl #32
    // 0xc18db8: stur            x2, [fp, #-0x10]
    // 0xc18dbc: CheckStackOverflow
    //     0xc18dbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18dc0: cmp             SP, x16
    //     0xc18dc4: b.ls            #0xc18ec4
    // 0xc18dc8: mov             x1, x0
    // 0xc18dcc: r0 = moveNext()
    //     0xc18dcc: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xc18dd0: tbnz            w0, #4, #0xc18e68
    // 0xc18dd4: ldur            x3, [fp, #-0x18]
    // 0xc18dd8: LoadField: r4 = r3->field_33
    //     0xc18dd8: ldur            w4, [x3, #0x33]
    // 0xc18ddc: DecompressPointer r4
    //     0xc18ddc: add             x4, x4, HEAP, lsl #32
    // 0xc18de0: stur            x4, [fp, #-0x20]
    // 0xc18de4: cmp             w4, NULL
    // 0xc18de8: b.ne            #0xc18e1c
    // 0xc18dec: mov             x0, x4
    // 0xc18df0: ldur            x2, [fp, #-0x10]
    // 0xc18df4: r1 = Null
    //     0xc18df4: mov             x1, NULL
    // 0xc18df8: cmp             w2, NULL
    // 0xc18dfc: b.eq            #0xc18e1c
    // 0xc18e00: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc18e00: ldur            w4, [x2, #0x17]
    // 0xc18e04: DecompressPointer r4
    //     0xc18e04: add             x4, x4, HEAP, lsl #32
    // 0xc18e08: r8 = X0
    //     0xc18e08: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc18e0c: LoadField: r9 = r4->field_7
    //     0xc18e0c: ldur            x9, [x4, #7]
    // 0xc18e10: r3 = Null
    //     0xc18e10: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c6e8] Null
    //     0xc18e14: ldr             x3, [x3, #0x6e8]
    // 0xc18e18: blr             x9
    // 0xc18e1c: ldur            x0, [fp, #-0x20]
    // 0xc18e20: r1 = Function '<anonymous closure>': static.
    //     0xc18e20: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c6f8] AnonymousClosure: static (0xc19974), in [package:source_span/src/highlighter.dart] Highlighter::_collateLines (0xc18d0c)
    //     0xc18e24: ldr             x1, [x1, #0x6f8]
    // 0xc18e28: r2 = Null
    //     0xc18e28: mov             x2, NULL
    // 0xc18e2c: r0 = AllocateClosure()
    //     0xc18e2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xc18e30: ldur            x1, [fp, #-0x20]
    // 0xc18e34: r2 = LoadClassIdInstr(r1)
    //     0xc18e34: ldur            x2, [x1, #-1]
    //     0xc18e38: ubfx            x2, x2, #0xc, #0x14
    // 0xc18e3c: str             x0, [SP]
    // 0xc18e40: mov             x0, x2
    // 0xc18e44: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc18e44: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc18e48: r0 = GDT[cid_x0 + 0x133e4]()
    //     0xc18e48: movz            x17, #0x33e4
    //     0xc18e4c: movk            x17, #0x1, lsl #16
    //     0xc18e50: add             lr, x0, x17
    //     0xc18e54: ldr             lr, [x21, lr, lsl #3]
    //     0xc18e58: blr             lr
    // 0xc18e5c: ldur            x0, [fp, #-0x18]
    // 0xc18e60: ldur            x2, [fp, #-0x10]
    // 0xc18e64: b               #0xc18dbc
    // 0xc18e68: ldur            x1, [fp, #-8]
    // 0xc18e6c: r0 = entries()
    //     0xc18e6c: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0xc18e70: r1 = Function '<anonymous closure>': static.
    //     0xc18e70: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c700] AnonymousClosure: static (0xc18ecc), in [package:source_span/src/highlighter.dart] Highlighter::_collateLines (0xc18d0c)
    //     0xc18e74: ldr             x1, [x1, #0x700]
    // 0xc18e78: r2 = Null
    //     0xc18e78: mov             x2, NULL
    // 0xc18e7c: stur            x0, [fp, #-8]
    // 0xc18e80: r0 = AllocateClosure()
    //     0xc18e80: bl              #0xec1630  ; AllocateClosureStub
    // 0xc18e84: r16 = <_Line>
    //     0xc18e84: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c708] TypeArguments: <_Line>
    //     0xc18e88: ldr             x16, [x16, #0x708]
    // 0xc18e8c: ldur            lr, [fp, #-8]
    // 0xc18e90: stp             lr, x16, [SP, #8]
    // 0xc18e94: str             x0, [SP]
    // 0xc18e98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc18e98: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc18e9c: r0 = expand()
    //     0xc18e9c: bl              #0x6a0158  ; [dart:core] Iterable::expand
    // 0xc18ea0: LoadField: r1 = r0->field_7
    //     0xc18ea0: ldur            w1, [x0, #7]
    // 0xc18ea4: DecompressPointer r1
    //     0xc18ea4: add             x1, x1, HEAP, lsl #32
    // 0xc18ea8: mov             x2, x0
    // 0xc18eac: r0 = _GrowableList.of()
    //     0xc18eac: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xc18eb0: LeaveFrame
    //     0xc18eb0: mov             SP, fp
    //     0xc18eb4: ldp             fp, lr, [SP], #0x10
    // 0xc18eb8: ret
    //     0xc18eb8: ret             
    // 0xc18ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18ec0: b               #0xc18d2c
    // 0xc18ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18ec4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18ec8: b               #0xc18dc8
  }
  [closure] static List<_Line> <anonymous closure>(dynamic, MapEntry<Object, List<_Highlight>>) {
    // ** addr: 0xc18ecc, size: 0x7c8
    // 0xc18ecc: EnterFrame
    //     0xc18ecc: stp             fp, lr, [SP, #-0x10]!
    //     0xc18ed0: mov             fp, SP
    // 0xc18ed4: AllocStack(0x98)
    //     0xc18ed4: sub             SP, SP, #0x98
    // 0xc18ed8: SetupParameters()
    //     0xc18ed8: ldr             x0, [fp, #0x18]
    //     0xc18edc: ldur            w3, [x0, #0x17]
    //     0xc18ee0: add             x3, x3, HEAP, lsl #32
    //     0xc18ee4: stur            x3, [fp, #-0x18]
    // 0xc18ee8: CheckStackOverflow
    //     0xc18ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18eec: cmp             SP, x16
    //     0xc18ef0: b.ls            #0xc1965c
    // 0xc18ef4: ldr             x0, [fp, #0x10]
    // 0xc18ef8: LoadField: r4 = r0->field_b
    //     0xc18ef8: ldur            w4, [x0, #0xb]
    // 0xc18efc: DecompressPointer r4
    //     0xc18efc: add             x4, x4, HEAP, lsl #32
    // 0xc18f00: stur            x4, [fp, #-0x10]
    // 0xc18f04: LoadField: r5 = r0->field_f
    //     0xc18f04: ldur            w5, [x0, #0xf]
    // 0xc18f08: DecompressPointer r5
    //     0xc18f08: add             x5, x5, HEAP, lsl #32
    // 0xc18f0c: stur            x5, [fp, #-8]
    // 0xc18f10: r1 = <_Line>
    //     0xc18f10: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c708] TypeArguments: <_Line>
    //     0xc18f14: ldr             x1, [x1, #0x708]
    // 0xc18f18: r2 = 0
    //     0xc18f18: movz            x2, #0
    // 0xc18f1c: r0 = _GrowableList()
    //     0xc18f1c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc18f20: mov             x3, x0
    // 0xc18f24: ldur            x2, [fp, #-8]
    // 0xc18f28: stur            x3, [fp, #-0x20]
    // 0xc18f2c: r0 = LoadClassIdInstr(r2)
    //     0xc18f2c: ldur            x0, [x2, #-1]
    //     0xc18f30: ubfx            x0, x0, #0xc, #0x14
    // 0xc18f34: mov             x1, x2
    // 0xc18f38: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xc18f38: movz            x17, #0xd35d
    //     0xc18f3c: add             lr, x0, x17
    //     0xc18f40: ldr             lr, [x21, lr, lsl #3]
    //     0xc18f44: blr             lr
    // 0xc18f48: mov             x2, x0
    // 0xc18f4c: stur            x2, [fp, #-0x28]
    // 0xc18f50: ldur            x3, [fp, #-0x20]
    // 0xc18f54: ldur            x4, [fp, #-0x10]
    // 0xc18f58: CheckStackOverflow
    //     0xc18f58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18f5c: cmp             SP, x16
    //     0xc18f60: b.ls            #0xc19664
    // 0xc18f64: r0 = LoadClassIdInstr(r2)
    //     0xc18f64: ldur            x0, [x2, #-1]
    //     0xc18f68: ubfx            x0, x0, #0xc, #0x14
    // 0xc18f6c: mov             x1, x2
    // 0xc18f70: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xc18f70: movz            x17, #0x292d
    //     0xc18f74: movk            x17, #0x1, lsl #16
    //     0xc18f78: add             lr, x0, x17
    //     0xc18f7c: ldr             lr, [x21, lr, lsl #3]
    //     0xc18f80: blr             lr
    // 0xc18f84: tbnz            w0, #4, #0xc19310
    // 0xc18f88: ldur            x2, [fp, #-0x28]
    // 0xc18f8c: r0 = LoadClassIdInstr(r2)
    //     0xc18f8c: ldur            x0, [x2, #-1]
    //     0xc18f90: ubfx            x0, x0, #0xc, #0x14
    // 0xc18f94: mov             x1, x2
    // 0xc18f98: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xc18f98: movz            x17, #0x384d
    //     0xc18f9c: movk            x17, #0x1, lsl #16
    //     0xc18fa0: add             lr, x0, x17
    //     0xc18fa4: ldr             lr, [x21, lr, lsl #3]
    //     0xc18fa8: blr             lr
    // 0xc18fac: LoadField: r2 = r0->field_7
    //     0xc18fac: ldur            w2, [x0, #7]
    // 0xc18fb0: DecompressPointer r2
    //     0xc18fb0: add             x2, x2, HEAP, lsl #32
    // 0xc18fb4: stur            x2, [fp, #-0x30]
    // 0xc18fb8: r0 = LoadClassIdInstr(r2)
    //     0xc18fb8: ldur            x0, [x2, #-1]
    //     0xc18fbc: ubfx            x0, x0, #0xc, #0x14
    // 0xc18fc0: mov             x1, x2
    // 0xc18fc4: r0 = GDT[cid_x0 + -0xff4]()
    //     0xc18fc4: sub             lr, x0, #0xff4
    //     0xc18fc8: ldr             lr, [x21, lr, lsl #3]
    //     0xc18fcc: blr             lr
    // 0xc18fd0: mov             x3, x0
    // 0xc18fd4: ldur            x2, [fp, #-0x30]
    // 0xc18fd8: stur            x3, [fp, #-0x38]
    // 0xc18fdc: r0 = LoadClassIdInstr(r2)
    //     0xc18fdc: ldur            x0, [x2, #-1]
    //     0xc18fe0: ubfx            x0, x0, #0xc, #0x14
    // 0xc18fe4: mov             x1, x2
    // 0xc18fe8: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc18fe8: sub             lr, x0, #0xff3
    //     0xc18fec: ldr             lr, [x21, lr, lsl #3]
    //     0xc18ff0: blr             lr
    // 0xc18ff4: mov             x3, x0
    // 0xc18ff8: ldur            x2, [fp, #-0x30]
    // 0xc18ffc: stur            x3, [fp, #-0x40]
    // 0xc19000: r0 = LoadClassIdInstr(r2)
    //     0xc19000: ldur            x0, [x2, #-1]
    //     0xc19004: ubfx            x0, x0, #0xc, #0x14
    // 0xc19008: mov             x1, x2
    // 0xc1900c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1900c: sub             lr, x0, #0xfff
    //     0xc19010: ldr             lr, [x21, lr, lsl #3]
    //     0xc19014: blr             lr
    // 0xc19018: r1 = LoadClassIdInstr(r0)
    //     0xc19018: ldur            x1, [x0, #-1]
    //     0xc1901c: ubfx            x1, x1, #0xc, #0x14
    // 0xc19020: mov             x16, x0
    // 0xc19024: mov             x0, x1
    // 0xc19028: mov             x1, x16
    // 0xc1902c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1902c: sub             lr, x0, #0xfff
    //     0xc19030: ldr             lr, [x21, lr, lsl #3]
    //     0xc19034: blr             lr
    // 0xc19038: ldur            x1, [fp, #-0x38]
    // 0xc1903c: ldur            x2, [fp, #-0x40]
    // 0xc19040: mov             x3, x0
    // 0xc19044: r0 = findLineStart()
    //     0xc19044: bl              #0xc196ac  ; [package:source_span/src/utils.dart] ::findLineStart
    // 0xc19048: cmp             w0, NULL
    // 0xc1904c: b.eq            #0xc1966c
    // 0xc19050: ldur            x4, [fp, #-0x38]
    // 0xc19054: LoadField: r1 = r4->field_7
    //     0xc19054: ldur            w1, [x4, #7]
    // 0xc19058: r3 = LoadInt32Instr(r1)
    //     0xc19058: sbfx            x3, x1, #1, #0x1f
    // 0xc1905c: mov             x2, x0
    // 0xc19060: r1 = 0
    //     0xc19060: movz            x1, #0
    // 0xc19064: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xc19064: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xc19068: r0 = checkValidRange()
    //     0xc19068: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xc1906c: ldur            x1, [fp, #-0x38]
    // 0xc19070: mov             x3, x0
    // 0xc19074: r2 = 0
    //     0xc19074: movz            x2, #0
    // 0xc19078: r0 = _substringUnchecked()
    //     0xc19078: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xc1907c: stur            x0, [fp, #-0x40]
    // 0xc19080: LoadField: r1 = r0->field_7
    //     0xc19080: ldur            w1, [x0, #7]
    // 0xc19084: stur            x1, [fp, #-0x70]
    // 0xc19088: r2 = LoadInt32Instr(r1)
    //     0xc19088: sbfx            x2, x1, #1, #0x1f
    // 0xc1908c: tbnz            x2, #0x3f, #0xc19610
    // 0xc19090: r1 = <Match>
    //     0xc19090: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c710] TypeArguments: <Match>
    //     0xc19094: ldr             x1, [x1, #0x710]
    // 0xc19098: r0 = _StringAllMatchesIterable()
    //     0xc19098: bl              #0xc196a0  ; Allocate_StringAllMatchesIterableStub -> _StringAllMatchesIterable (size=0x1c)
    // 0xc1909c: mov             x1, x0
    // 0xc190a0: ldur            x0, [fp, #-0x40]
    // 0xc190a4: StoreField: r1->field_b = r0
    //     0xc190a4: stur            w0, [x1, #0xb]
    // 0xc190a8: r2 = "\n"
    //     0xc190a8: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc190ac: StoreField: r1->field_f = r2
    //     0xc190ac: stur            w2, [x1, #0xf]
    // 0xc190b0: StoreField: r1->field_13 = rZR
    //     0xc190b0: stur            xzr, [x1, #0x13]
    // 0xc190b4: r0 = iterator()
    //     0xc190b4: bl              #0x887888  ; [dart:core] _StringAllMatchesIterable::iterator
    // 0xc190b8: mov             x2, x0
    // 0xc190bc: stur            x2, [fp, #-0x40]
    // 0xc190c0: r3 = 0
    //     0xc190c0: movz            x3, #0
    // 0xc190c4: stur            x3, [fp, #-0x48]
    // 0xc190c8: CheckStackOverflow
    //     0xc190c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc190cc: cmp             SP, x16
    //     0xc190d0: b.ls            #0xc19670
    // 0xc190d4: r0 = LoadClassIdInstr(r2)
    //     0xc190d4: ldur            x0, [x2, #-1]
    //     0xc190d8: ubfx            x0, x0, #0xc, #0x14
    // 0xc190dc: mov             x1, x2
    // 0xc190e0: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xc190e0: movz            x17, #0x292d
    //     0xc190e4: movk            x17, #0x1, lsl #16
    //     0xc190e8: add             lr, x0, x17
    //     0xc190ec: ldr             lr, [x21, lr, lsl #3]
    //     0xc190f0: blr             lr
    // 0xc190f4: tbnz            w0, #4, #0xc19108
    // 0xc190f8: ldur            x2, [fp, #-0x48]
    // 0xc190fc: add             x3, x2, #1
    // 0xc19100: ldur            x2, [fp, #-0x40]
    // 0xc19104: b               #0xc190c4
    // 0xc19108: ldur            x1, [fp, #-0x30]
    // 0xc1910c: ldur            x3, [fp, #-0x38]
    // 0xc19110: ldur            x2, [fp, #-0x48]
    // 0xc19114: r0 = LoadClassIdInstr(r1)
    //     0xc19114: ldur            x0, [x1, #-1]
    //     0xc19118: ubfx            x0, x0, #0xc, #0x14
    // 0xc1911c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc1911c: sub             lr, x0, #0xfff
    //     0xc19120: ldr             lr, [x21, lr, lsl #3]
    //     0xc19124: blr             lr
    // 0xc19128: r1 = LoadClassIdInstr(r0)
    //     0xc19128: ldur            x1, [x0, #-1]
    //     0xc1912c: ubfx            x1, x1, #0xc, #0x14
    // 0xc19130: mov             x16, x0
    // 0xc19134: mov             x0, x1
    // 0xc19138: mov             x1, x16
    // 0xc1913c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1913c: sub             lr, x0, #1, lsl #12
    //     0xc19140: ldr             lr, [x21, lr, lsl #3]
    //     0xc19144: blr             lr
    // 0xc19148: mov             x1, x0
    // 0xc1914c: ldur            x0, [fp, #-0x48]
    // 0xc19150: sub             x3, x1, x0
    // 0xc19154: ldur            x1, [fp, #-0x38]
    // 0xc19158: stur            x3, [fp, #-0x50]
    // 0xc1915c: r0 = LoadClassIdInstr(r1)
    //     0xc1915c: ldur            x0, [x1, #-1]
    //     0xc19160: ubfx            x0, x0, #0xc, #0x14
    // 0xc19164: r2 = "\n"
    //     0xc19164: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc19168: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19168: sub             lr, x0, #1, lsl #12
    //     0xc1916c: ldr             lr, [x21, lr, lsl #3]
    //     0xc19170: blr             lr
    // 0xc19174: mov             x3, x0
    // 0xc19178: stur            x3, [fp, #-0x38]
    // 0xc1917c: LoadField: r0 = r3->field_b
    //     0xc1917c: ldur            w0, [x3, #0xb]
    // 0xc19180: r4 = LoadInt32Instr(r0)
    //     0xc19180: sbfx            x4, x0, #1, #0x1f
    // 0xc19184: stur            x4, [fp, #-0x58]
    // 0xc19188: ldur            x7, [fp, #-0x50]
    // 0xc1918c: ldur            x5, [fp, #-0x20]
    // 0xc19190: r0 = 0
    //     0xc19190: movz            x0, #0
    // 0xc19194: ldur            x6, [fp, #-0x10]
    // 0xc19198: stur            x7, [fp, #-0x50]
    // 0xc1919c: CheckStackOverflow
    //     0xc1919c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc191a0: cmp             SP, x16
    //     0xc191a4: b.ls            #0xc19678
    // 0xc191a8: LoadField: r1 = r3->field_b
    //     0xc191a8: ldur            w1, [x3, #0xb]
    // 0xc191ac: r2 = LoadInt32Instr(r1)
    //     0xc191ac: sbfx            x2, x1, #1, #0x1f
    // 0xc191b0: cmp             x4, x2
    // 0xc191b4: b.ne            #0xc195f0
    // 0xc191b8: cmp             x0, x2
    // 0xc191bc: b.ge            #0xc19304
    // 0xc191c0: LoadField: r1 = r3->field_f
    //     0xc191c0: ldur            w1, [x3, #0xf]
    // 0xc191c4: DecompressPointer r1
    //     0xc191c4: add             x1, x1, HEAP, lsl #32
    // 0xc191c8: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0xc191c8: add             x16, x1, x0, lsl #2
    //     0xc191cc: ldur            w8, [x16, #0xf]
    // 0xc191d0: DecompressPointer r8
    //     0xc191d0: add             x8, x8, HEAP, lsl #32
    // 0xc191d4: stur            x8, [fp, #-0x30]
    // 0xc191d8: add             x9, x0, #1
    // 0xc191dc: stur            x9, [fp, #-0x48]
    // 0xc191e0: LoadField: r0 = r5->field_b
    //     0xc191e0: ldur            w0, [x5, #0xb]
    // 0xc191e4: r1 = LoadInt32Instr(r0)
    //     0xc191e4: sbfx            x1, x0, #1, #0x1f
    // 0xc191e8: cbz             x1, #0xc19228
    // 0xc191ec: cmp             x1, #0
    // 0xc191f0: b.le            #0xc195e4
    // 0xc191f4: sub             x2, x1, #1
    // 0xc191f8: mov             x0, x1
    // 0xc191fc: mov             x1, x2
    // 0xc19200: cmp             x1, x0
    // 0xc19204: b.hs            #0xc19680
    // 0xc19208: LoadField: r0 = r5->field_f
    //     0xc19208: ldur            w0, [x5, #0xf]
    // 0xc1920c: DecompressPointer r0
    //     0xc1920c: add             x0, x0, HEAP, lsl #32
    // 0xc19210: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xc19210: add             x16, x0, x2, lsl #2
    //     0xc19214: ldur            w1, [x16, #0xf]
    // 0xc19218: DecompressPointer r1
    //     0xc19218: add             x1, x1, HEAP, lsl #32
    // 0xc1921c: LoadField: r0 = r1->field_b
    //     0xc1921c: ldur            x0, [x1, #0xb]
    // 0xc19220: cmp             x7, x0
    // 0xc19224: b.le            #0xc192e4
    // 0xc19228: r1 = <_Highlight>
    //     0xc19228: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5d8] TypeArguments: <_Highlight>
    //     0xc1922c: ldr             x1, [x1, #0x5d8]
    // 0xc19230: r2 = 0
    //     0xc19230: movz            x2, #0
    // 0xc19234: r0 = _GrowableList()
    //     0xc19234: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc19238: stur            x0, [fp, #-0x40]
    // 0xc1923c: r0 = _Line()
    //     0xc1923c: bl              #0xc19694  ; Allocate_LineStub -> _Line (size=0x1c)
    // 0xc19240: mov             x2, x0
    // 0xc19244: ldur            x0, [fp, #-0x40]
    // 0xc19248: stur            x2, [fp, #-0x68]
    // 0xc1924c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc1924c: stur            w0, [x2, #0x17]
    // 0xc19250: ldur            x0, [fp, #-0x30]
    // 0xc19254: StoreField: r2->field_7 = r0
    //     0xc19254: stur            w0, [x2, #7]
    // 0xc19258: ldur            x0, [fp, #-0x50]
    // 0xc1925c: StoreField: r2->field_b = r0
    //     0xc1925c: stur            x0, [x2, #0xb]
    // 0xc19260: ldur            x3, [fp, #-0x10]
    // 0xc19264: StoreField: r2->field_13 = r3
    //     0xc19264: stur            w3, [x2, #0x13]
    // 0xc19268: ldur            x4, [fp, #-0x20]
    // 0xc1926c: LoadField: r1 = r4->field_b
    //     0xc1926c: ldur            w1, [x4, #0xb]
    // 0xc19270: LoadField: r5 = r4->field_f
    //     0xc19270: ldur            w5, [x4, #0xf]
    // 0xc19274: DecompressPointer r5
    //     0xc19274: add             x5, x5, HEAP, lsl #32
    // 0xc19278: LoadField: r6 = r5->field_b
    //     0xc19278: ldur            w6, [x5, #0xb]
    // 0xc1927c: r5 = LoadInt32Instr(r1)
    //     0xc1927c: sbfx            x5, x1, #1, #0x1f
    // 0xc19280: stur            x5, [fp, #-0x60]
    // 0xc19284: r1 = LoadInt32Instr(r6)
    //     0xc19284: sbfx            x1, x6, #1, #0x1f
    // 0xc19288: cmp             x5, x1
    // 0xc1928c: b.ne            #0xc19298
    // 0xc19290: mov             x1, x4
    // 0xc19294: r0 = _growToNextCapacity()
    //     0xc19294: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc19298: ldur            x3, [fp, #-0x20]
    // 0xc1929c: ldur            x2, [fp, #-0x60]
    // 0xc192a0: add             x0, x2, #1
    // 0xc192a4: lsl             x1, x0, #1
    // 0xc192a8: StoreField: r3->field_b = r1
    //     0xc192a8: stur            w1, [x3, #0xb]
    // 0xc192ac: LoadField: r1 = r3->field_f
    //     0xc192ac: ldur            w1, [x3, #0xf]
    // 0xc192b0: DecompressPointer r1
    //     0xc192b0: add             x1, x1, HEAP, lsl #32
    // 0xc192b4: ldur            x0, [fp, #-0x68]
    // 0xc192b8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc192b8: add             x25, x1, x2, lsl #2
    //     0xc192bc: add             x25, x25, #0xf
    //     0xc192c0: str             w0, [x25]
    //     0xc192c4: tbz             w0, #0, #0xc192e0
    //     0xc192c8: ldurb           w16, [x1, #-1]
    //     0xc192cc: ldurb           w17, [x0, #-1]
    //     0xc192d0: and             x16, x17, x16, lsr #2
    //     0xc192d4: tst             x16, HEAP, lsr #32
    //     0xc192d8: b.eq            #0xc192e0
    //     0xc192dc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc192e0: b               #0xc192e8
    // 0xc192e4: mov             x3, x5
    // 0xc192e8: ldur            x0, [fp, #-0x50]
    // 0xc192ec: add             x7, x0, #1
    // 0xc192f0: ldur            x0, [fp, #-0x48]
    // 0xc192f4: mov             x5, x3
    // 0xc192f8: ldur            x3, [fp, #-0x38]
    // 0xc192fc: ldur            x4, [fp, #-0x58]
    // 0xc19300: b               #0xc19194
    // 0xc19304: mov             x3, x5
    // 0xc19308: ldur            x2, [fp, #-0x28]
    // 0xc1930c: b               #0xc18f54
    // 0xc19310: ldur            x3, [fp, #-0x20]
    // 0xc19314: r1 = <_Highlight>
    //     0xc19314: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c5d8] TypeArguments: <_Highlight>
    //     0xc19318: ldr             x1, [x1, #0x5d8]
    // 0xc1931c: r2 = 0
    //     0xc1931c: movz            x2, #0
    // 0xc19320: r0 = _GrowableList()
    //     0xc19320: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xc19324: mov             x1, x0
    // 0xc19328: ldur            x0, [fp, #-0x20]
    // 0xc1932c: stur            x1, [fp, #-0x28]
    // 0xc19330: LoadField: r2 = r0->field_b
    //     0xc19330: ldur            w2, [x0, #0xb]
    // 0xc19334: r3 = LoadInt32Instr(r2)
    //     0xc19334: sbfx            x3, x2, #1, #0x1f
    // 0xc19338: stur            x3, [fp, #-0x58]
    // 0xc1933c: r6 = 0
    //     0xc1933c: movz            x6, #0
    // 0xc19340: r5 = 0
    //     0xc19340: movz            x5, #0
    // 0xc19344: ldur            x4, [fp, #-0x18]
    // 0xc19348: ldur            x2, [fp, #-8]
    // 0xc1934c: stur            x5, [fp, #-0x50]
    // 0xc19350: CheckStackOverflow
    //     0xc19350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc19354: cmp             SP, x16
    //     0xc19358: b.ls            #0xc19684
    // 0xc1935c: LoadField: r7 = r0->field_b
    //     0xc1935c: ldur            w7, [x0, #0xb]
    // 0xc19360: r8 = LoadInt32Instr(r7)
    //     0xc19360: sbfx            x8, x7, #1, #0x1f
    // 0xc19364: cmp             x3, x8
    // 0xc19368: b.ne            #0xc19640
    // 0xc1936c: cmp             x6, x8
    // 0xc19370: b.ge            #0xc195d4
    // 0xc19374: LoadField: r7 = r0->field_f
    //     0xc19374: ldur            w7, [x0, #0xf]
    // 0xc19378: DecompressPointer r7
    //     0xc19378: add             x7, x7, HEAP, lsl #32
    // 0xc1937c: ArrayLoad: r8 = r7[r6]  ; Unknown_4
    //     0xc1937c: add             x16, x7, x6, lsl #2
    //     0xc19380: ldur            w8, [x16, #0xf]
    // 0xc19384: DecompressPointer r8
    //     0xc19384: add             x8, x8, HEAP, lsl #32
    // 0xc19388: stur            x8, [fp, #-0x10]
    // 0xc1938c: add             x7, x6, #1
    // 0xc19390: stur            x7, [fp, #-0x48]
    // 0xc19394: r1 = 1
    //     0xc19394: movz            x1, #0x1
    // 0xc19398: r0 = AllocateContext()
    //     0xc19398: bl              #0xec126c  ; AllocateContextStub
    // 0xc1939c: mov             x3, x0
    // 0xc193a0: ldur            x0, [fp, #-0x18]
    // 0xc193a4: stur            x3, [fp, #-0x30]
    // 0xc193a8: StoreField: r3->field_b = r0
    //     0xc193a8: stur            w0, [x3, #0xb]
    // 0xc193ac: ldur            x1, [fp, #-0x10]
    // 0xc193b0: StoreField: r3->field_f = r1
    //     0xc193b0: stur            w1, [x3, #0xf]
    // 0xc193b4: mov             x2, x3
    // 0xc193b8: r1 = Function '<anonymous closure>': static.
    //     0xc193b8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c718] AnonymousClosure: static (0xc198d4), in [package:source_span/src/highlighter.dart] Highlighter::_collateLines (0xc18d0c)
    //     0xc193bc: ldr             x1, [x1, #0x718]
    // 0xc193c0: r0 = AllocateClosure()
    //     0xc193c0: bl              #0xec1630  ; AllocateClosureStub
    // 0xc193c4: ldur            x1, [fp, #-0x28]
    // 0xc193c8: mov             x2, x0
    // 0xc193cc: r3 = false
    //     0xc193cc: add             x3, NULL, #0x30  ; false
    // 0xc193d0: r0 = _filter()
    //     0xc193d0: bl              #0x6e29a0  ; [dart:collection] ListBase::_filter
    // 0xc193d4: ldur            x3, [fp, #-0x28]
    // 0xc193d8: LoadField: r4 = r3->field_b
    //     0xc193d8: ldur            w4, [x3, #0xb]
    // 0xc193dc: ldur            x5, [fp, #-8]
    // 0xc193e0: stur            x4, [fp, #-0x10]
    // 0xc193e4: r0 = LoadClassIdInstr(r5)
    //     0xc193e4: ldur            x0, [x5, #-1]
    //     0xc193e8: ubfx            x0, x0, #0xc, #0x14
    // 0xc193ec: mov             x1, x5
    // 0xc193f0: ldur            x2, [fp, #-0x50]
    // 0xc193f4: r0 = GDT[cid_x0 + 0xd3e2]()
    //     0xc193f4: movz            x17, #0xd3e2
    //     0xc193f8: add             lr, x0, x17
    //     0xc193fc: ldr             lr, [x21, lr, lsl #3]
    //     0xc19400: blr             lr
    // 0xc19404: r1 = LoadClassIdInstr(r0)
    //     0xc19404: ldur            x1, [x0, #-1]
    //     0xc19408: ubfx            x1, x1, #0xc, #0x14
    // 0xc1940c: mov             x16, x0
    // 0xc19410: mov             x0, x1
    // 0xc19414: mov             x1, x16
    // 0xc19418: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xc19418: movz            x17, #0xd35d
    //     0xc1941c: add             lr, x0, x17
    //     0xc19420: ldr             lr, [x21, lr, lsl #3]
    //     0xc19424: blr             lr
    // 0xc19428: mov             x2, x0
    // 0xc1942c: stur            x2, [fp, #-0x40]
    // 0xc19430: ldur            x3, [fp, #-0x28]
    // 0xc19434: ldur            x4, [fp, #-0x30]
    // 0xc19438: CheckStackOverflow
    //     0xc19438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1943c: cmp             SP, x16
    //     0xc19440: b.ls            #0xc1968c
    // 0xc19444: r0 = LoadClassIdInstr(r2)
    //     0xc19444: ldur            x0, [x2, #-1]
    //     0xc19448: ubfx            x0, x0, #0xc, #0x14
    // 0xc1944c: mov             x1, x2
    // 0xc19450: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xc19450: movz            x17, #0x292d
    //     0xc19454: movk            x17, #0x1, lsl #16
    //     0xc19458: add             lr, x0, x17
    //     0xc1945c: ldr             lr, [x21, lr, lsl #3]
    //     0xc19460: blr             lr
    // 0xc19464: tbnz            w0, #4, #0xc19578
    // 0xc19468: ldur            x3, [fp, #-0x30]
    // 0xc1946c: ldur            x2, [fp, #-0x40]
    // 0xc19470: r0 = LoadClassIdInstr(r2)
    //     0xc19470: ldur            x0, [x2, #-1]
    //     0xc19474: ubfx            x0, x0, #0xc, #0x14
    // 0xc19478: mov             x1, x2
    // 0xc1947c: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xc1947c: movz            x17, #0x384d
    //     0xc19480: movk            x17, #0x1, lsl #16
    //     0xc19484: add             lr, x0, x17
    //     0xc19488: ldr             lr, [x21, lr, lsl #3]
    //     0xc1948c: blr             lr
    // 0xc19490: mov             x2, x0
    // 0xc19494: stur            x2, [fp, #-0x68]
    // 0xc19498: LoadField: r1 = r2->field_7
    //     0xc19498: ldur            w1, [x2, #7]
    // 0xc1949c: DecompressPointer r1
    //     0xc1949c: add             x1, x1, HEAP, lsl #32
    // 0xc194a0: r0 = LoadClassIdInstr(r1)
    //     0xc194a0: ldur            x0, [x1, #-1]
    //     0xc194a4: ubfx            x0, x0, #0xc, #0x14
    // 0xc194a8: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc194a8: sub             lr, x0, #0xfff
    //     0xc194ac: ldr             lr, [x21, lr, lsl #3]
    //     0xc194b0: blr             lr
    // 0xc194b4: r1 = LoadClassIdInstr(r0)
    //     0xc194b4: ldur            x1, [x0, #-1]
    //     0xc194b8: ubfx            x1, x1, #0xc, #0x14
    // 0xc194bc: mov             x16, x0
    // 0xc194c0: mov             x0, x1
    // 0xc194c4: mov             x1, x16
    // 0xc194c8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc194c8: sub             lr, x0, #1, lsl #12
    //     0xc194cc: ldr             lr, [x21, lr, lsl #3]
    //     0xc194d0: blr             lr
    // 0xc194d4: mov             x1, x0
    // 0xc194d8: ldur            x0, [fp, #-0x30]
    // 0xc194dc: LoadField: r2 = r0->field_f
    //     0xc194dc: ldur            w2, [x0, #0xf]
    // 0xc194e0: DecompressPointer r2
    //     0xc194e0: add             x2, x2, HEAP, lsl #32
    // 0xc194e4: LoadField: r3 = r2->field_b
    //     0xc194e4: ldur            x3, [x2, #0xb]
    // 0xc194e8: cmp             x1, x3
    // 0xc194ec: b.gt            #0xc19570
    // 0xc194f0: ldur            x2, [fp, #-0x28]
    // 0xc194f4: LoadField: r1 = r2->field_b
    //     0xc194f4: ldur            w1, [x2, #0xb]
    // 0xc194f8: LoadField: r3 = r2->field_f
    //     0xc194f8: ldur            w3, [x2, #0xf]
    // 0xc194fc: DecompressPointer r3
    //     0xc194fc: add             x3, x3, HEAP, lsl #32
    // 0xc19500: LoadField: r4 = r3->field_b
    //     0xc19500: ldur            w4, [x3, #0xb]
    // 0xc19504: r3 = LoadInt32Instr(r1)
    //     0xc19504: sbfx            x3, x1, #1, #0x1f
    // 0xc19508: stur            x3, [fp, #-0x60]
    // 0xc1950c: r1 = LoadInt32Instr(r4)
    //     0xc1950c: sbfx            x1, x4, #1, #0x1f
    // 0xc19510: cmp             x3, x1
    // 0xc19514: b.ne            #0xc19520
    // 0xc19518: mov             x1, x2
    // 0xc1951c: r0 = _growToNextCapacity()
    //     0xc1951c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc19520: ldur            x3, [fp, #-0x28]
    // 0xc19524: ldur            x2, [fp, #-0x60]
    // 0xc19528: add             x0, x2, #1
    // 0xc1952c: lsl             x1, x0, #1
    // 0xc19530: StoreField: r3->field_b = r1
    //     0xc19530: stur            w1, [x3, #0xb]
    // 0xc19534: LoadField: r1 = r3->field_f
    //     0xc19534: ldur            w1, [x3, #0xf]
    // 0xc19538: DecompressPointer r1
    //     0xc19538: add             x1, x1, HEAP, lsl #32
    // 0xc1953c: ldur            x0, [fp, #-0x68]
    // 0xc19540: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc19540: add             x25, x1, x2, lsl #2
    //     0xc19544: add             x25, x25, #0xf
    //     0xc19548: str             w0, [x25]
    //     0xc1954c: tbz             w0, #0, #0xc19568
    //     0xc19550: ldurb           w16, [x1, #-1]
    //     0xc19554: ldurb           w17, [x0, #-1]
    //     0xc19558: and             x16, x17, x16, lsr #2
    //     0xc1955c: tst             x16, HEAP, lsr #32
    //     0xc19560: b.eq            #0xc19568
    //     0xc19564: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc19568: ldur            x2, [fp, #-0x40]
    // 0xc1956c: b               #0xc19434
    // 0xc19570: ldur            x3, [fp, #-0x28]
    // 0xc19574: b               #0xc1957c
    // 0xc19578: ldur            x3, [fp, #-0x28]
    // 0xc1957c: ldur            x2, [fp, #-0x50]
    // 0xc19580: ldur            x0, [fp, #-0x30]
    // 0xc19584: ldur            x1, [fp, #-0x10]
    // 0xc19588: LoadField: r4 = r3->field_b
    //     0xc19588: ldur            w4, [x3, #0xb]
    // 0xc1958c: r5 = LoadInt32Instr(r1)
    //     0xc1958c: sbfx            x5, x1, #1, #0x1f
    // 0xc19590: r1 = LoadInt32Instr(r4)
    //     0xc19590: sbfx            x1, x4, #1, #0x1f
    // 0xc19594: sub             x4, x1, x5
    // 0xc19598: add             x5, x2, x4
    // 0xc1959c: stur            x5, [fp, #-0x60]
    // 0xc195a0: LoadField: r1 = r0->field_f
    //     0xc195a0: ldur            w1, [x0, #0xf]
    // 0xc195a4: DecompressPointer r1
    //     0xc195a4: add             x1, x1, HEAP, lsl #32
    // 0xc195a8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc195a8: ldur            w0, [x1, #0x17]
    // 0xc195ac: DecompressPointer r0
    //     0xc195ac: add             x0, x0, HEAP, lsl #32
    // 0xc195b0: mov             x1, x0
    // 0xc195b4: mov             x2, x3
    // 0xc195b8: r0 = addAll()
    //     0xc195b8: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xc195bc: ldur            x6, [fp, #-0x48]
    // 0xc195c0: ldur            x5, [fp, #-0x60]
    // 0xc195c4: ldur            x0, [fp, #-0x20]
    // 0xc195c8: ldur            x1, [fp, #-0x28]
    // 0xc195cc: ldur            x3, [fp, #-0x58]
    // 0xc195d0: b               #0xc19344
    // 0xc195d4: ldur            x0, [fp, #-0x20]
    // 0xc195d8: LeaveFrame
    //     0xc195d8: mov             SP, fp
    //     0xc195dc: ldp             fp, lr, [SP], #0x10
    // 0xc195e0: ret
    //     0xc195e0: ret             
    // 0xc195e4: r0 = noElement()
    //     0xc195e4: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xc195e8: r0 = Throw()
    //     0xc195e8: bl              #0xec04b8  ; ThrowStub
    // 0xc195ec: brk             #0
    // 0xc195f0: mov             x0, x3
    // 0xc195f4: r0 = ConcurrentModificationError()
    //     0xc195f4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xc195f8: mov             x1, x0
    // 0xc195fc: ldur            x0, [fp, #-0x38]
    // 0xc19600: StoreField: r1->field_b = r0
    //     0xc19600: stur            w0, [x1, #0xb]
    // 0xc19604: mov             x0, x1
    // 0xc19608: r0 = Throw()
    //     0xc19608: bl              #0xec04b8  ; ThrowStub
    // 0xc1960c: brk             #0
    // 0xc19610: r0 = RangeError()
    //     0xc19610: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc19614: stur            x0, [fp, #-8]
    // 0xc19618: stp             xzr, x0, [SP, #0x18]
    // 0xc1961c: ldur            x16, [fp, #-0x70]
    // 0xc19620: stp             x16, xzr, [SP, #8]
    // 0xc19624: r16 = "start"
    //     0xc19624: ldr             x16, [PP, #0x530]  ; [pp+0x530] "start"
    // 0xc19628: str             x16, [SP]
    // 0xc1962c: r4 = const [0, 0x5, 0x5, 0x5, null]
    //     0xc1962c: ldr             x4, [PP, #0xce0]  ; [pp+0xce0] List(5) [0, 0x5, 0x5, 0x5, Null]
    // 0xc19630: r0 = RangeError.range()
    //     0xc19630: bl              #0x600404  ; [dart:core] RangeError::RangeError.range
    // 0xc19634: ldur            x0, [fp, #-8]
    // 0xc19638: r0 = Throw()
    //     0xc19638: bl              #0xec04b8  ; ThrowStub
    // 0xc1963c: brk             #0
    // 0xc19640: r0 = ConcurrentModificationError()
    //     0xc19640: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xc19644: mov             x1, x0
    // 0xc19648: ldur            x0, [fp, #-0x20]
    // 0xc1964c: StoreField: r1->field_b = r0
    //     0xc1964c: stur            w0, [x1, #0xb]
    // 0xc19650: mov             x0, x1
    // 0xc19654: r0 = Throw()
    //     0xc19654: bl              #0xec04b8  ; ThrowStub
    // 0xc19658: brk             #0
    // 0xc1965c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1965c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19660: b               #0xc18ef4
    // 0xc19664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19664: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19668: b               #0xc18f64
    // 0xc1966c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1966c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc19670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19670: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19674: b               #0xc190d4
    // 0xc19678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19678: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1967c: b               #0xc191a8
    // 0xc19680: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc19680: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc19684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19688: b               #0xc1935c
    // 0xc1968c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1968c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19690: b               #0xc19444
  }
  [closure] static bool <anonymous closure>(dynamic, _Highlight) {
    // ** addr: 0xc198d4, size: 0xa0
    // 0xc198d4: EnterFrame
    //     0xc198d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc198d8: mov             fp, SP
    // 0xc198dc: AllocStack(0x8)
    //     0xc198dc: sub             SP, SP, #8
    // 0xc198e0: SetupParameters()
    //     0xc198e0: ldr             x0, [fp, #0x18]
    //     0xc198e4: ldur            w2, [x0, #0x17]
    //     0xc198e8: add             x2, x2, HEAP, lsl #32
    //     0xc198ec: stur            x2, [fp, #-8]
    // 0xc198f0: CheckStackOverflow
    //     0xc198f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc198f4: cmp             SP, x16
    //     0xc198f8: b.ls            #0xc1996c
    // 0xc198fc: ldr             x0, [fp, #0x10]
    // 0xc19900: LoadField: r1 = r0->field_7
    //     0xc19900: ldur            w1, [x0, #7]
    // 0xc19904: DecompressPointer r1
    //     0xc19904: add             x1, x1, HEAP, lsl #32
    // 0xc19908: r0 = LoadClassIdInstr(r1)
    //     0xc19908: ldur            x0, [x1, #-1]
    //     0xc1990c: ubfx            x0, x0, #0xc, #0x14
    // 0xc19910: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19910: sub             lr, x0, #1, lsl #12
    //     0xc19914: ldr             lr, [x21, lr, lsl #3]
    //     0xc19918: blr             lr
    // 0xc1991c: r1 = LoadClassIdInstr(r0)
    //     0xc1991c: ldur            x1, [x0, #-1]
    //     0xc19920: ubfx            x1, x1, #0xc, #0x14
    // 0xc19924: mov             x16, x0
    // 0xc19928: mov             x0, x1
    // 0xc1992c: mov             x1, x16
    // 0xc19930: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc19930: sub             lr, x0, #1, lsl #12
    //     0xc19934: ldr             lr, [x21, lr, lsl #3]
    //     0xc19938: blr             lr
    // 0xc1993c: ldur            x1, [fp, #-8]
    // 0xc19940: LoadField: r2 = r1->field_f
    //     0xc19940: ldur            w2, [x1, #0xf]
    // 0xc19944: DecompressPointer r2
    //     0xc19944: add             x2, x2, HEAP, lsl #32
    // 0xc19948: LoadField: r1 = r2->field_b
    //     0xc19948: ldur            x1, [x2, #0xb]
    // 0xc1994c: cmp             x0, x1
    // 0xc19950: r16 = true
    //     0xc19950: add             x16, NULL, #0x20  ; true
    // 0xc19954: r17 = false
    //     0xc19954: add             x17, NULL, #0x30  ; false
    // 0xc19958: csel            x2, x16, x17, lt
    // 0xc1995c: mov             x0, x2
    // 0xc19960: LeaveFrame
    //     0xc19960: mov             SP, fp
    //     0xc19964: ldp             fp, lr, [SP], #0x10
    // 0xc19968: ret
    //     0xc19968: ret             
    // 0xc1996c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1996c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19970: b               #0xc198fc
  }
  [closure] static int <anonymous closure>(dynamic, _Highlight, _Highlight) {
    // ** addr: 0xc19974, size: 0x74
    // 0xc19974: EnterFrame
    //     0xc19974: stp             fp, lr, [SP, #-0x10]!
    //     0xc19978: mov             fp, SP
    // 0xc1997c: CheckStackOverflow
    //     0xc1997c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc19980: cmp             SP, x16
    //     0xc19984: b.ls            #0xc199e0
    // 0xc19988: ldr             x0, [fp, #0x18]
    // 0xc1998c: LoadField: r1 = r0->field_7
    //     0xc1998c: ldur            w1, [x0, #7]
    // 0xc19990: DecompressPointer r1
    //     0xc19990: add             x1, x1, HEAP, lsl #32
    // 0xc19994: ldr             x0, [fp, #0x10]
    // 0xc19998: LoadField: r2 = r0->field_7
    //     0xc19998: ldur            w2, [x0, #7]
    // 0xc1999c: DecompressPointer r2
    //     0xc1999c: add             x2, x2, HEAP, lsl #32
    // 0xc199a0: r0 = LoadClassIdInstr(r1)
    //     0xc199a0: ldur            x0, [x1, #-1]
    //     0xc199a4: ubfx            x0, x0, #0xc, #0x14
    // 0xc199a8: r0 = GDT[cid_x0 + 0x138b7]()
    //     0xc199a8: movz            x17, #0x38b7
    //     0xc199ac: movk            x17, #0x1, lsl #16
    //     0xc199b0: add             lr, x0, x17
    //     0xc199b4: ldr             lr, [x21, lr, lsl #3]
    //     0xc199b8: blr             lr
    // 0xc199bc: mov             x2, x0
    // 0xc199c0: r0 = BoxInt64Instr(r2)
    //     0xc199c0: sbfiz           x0, x2, #1, #0x1f
    //     0xc199c4: cmp             x2, x0, asr #1
    //     0xc199c8: b.eq            #0xc199d4
    //     0xc199cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc199d0: stur            x2, [x0, #7]
    // 0xc199d4: LeaveFrame
    //     0xc199d4: mov             SP, fp
    //     0xc199d8: ldp             fp, lr, [SP], #0x10
    // 0xc199dc: ret
    //     0xc199dc: ret             
    // 0xc199e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc199e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc199e4: b               #0xc19988
  }
  [closure] static Object <anonymous closure>(dynamic, _Highlight) {
    // ** addr: 0xc199e8, size: 0x4c
    // 0xc199e8: EnterFrame
    //     0xc199e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc199ec: mov             fp, SP
    // 0xc199f0: CheckStackOverflow
    //     0xc199f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc199f4: cmp             SP, x16
    //     0xc199f8: b.ls            #0xc19a2c
    // 0xc199fc: ldr             x0, [fp, #0x10]
    // 0xc19a00: LoadField: r1 = r0->field_7
    //     0xc19a00: ldur            w1, [x0, #7]
    // 0xc19a04: DecompressPointer r1
    //     0xc19a04: add             x1, x1, HEAP, lsl #32
    // 0xc19a08: r0 = LoadClassIdInstr(r1)
    //     0xc19a08: ldur            x0, [x1, #-1]
    //     0xc19a0c: ubfx            x0, x0, #0xc, #0x14
    // 0xc19a10: r0 = GDT[cid_x0 + -0xff0]()
    //     0xc19a10: sub             lr, x0, #0xff0
    //     0xc19a14: ldr             lr, [x21, lr, lsl #3]
    //     0xc19a18: blr             lr
    // 0xc19a1c: r0 = Object()
    //     0xc19a1c: bl              #0x60cd48  ; AllocateObjectStub -> Object (size=0x8)
    // 0xc19a20: LeaveFrame
    //     0xc19a20: mov             SP, fp
    //     0xc19a24: ldp             fp, lr, [SP], #0x10
    // 0xc19a28: ret
    //     0xc19a28: ret             
    // 0xc19a2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc19a2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc19a30: b               #0xc199fc
  }
}
