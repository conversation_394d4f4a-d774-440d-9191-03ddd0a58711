// lib: , url: package:source_span/src/location.dart

// class id: 1051139, size: 0x8
class :: {
}

// class id: 497, size: 0x24, field offset: 0x8
class SourceLocation extends Object
    implements Comparable<X0> {

  _ compareTo(/* No info */) {
    // ** addr: 0x6d5f3c, size: 0xc4
    // 0x6d5f3c: EnterFrame
    //     0x6d5f3c: stp             fp, lr, [SP, #-0x10]!
    //     0x6d5f40: mov             fp, SP
    // 0x6d5f44: AllocStack(0x18)
    //     0x6d5f44: sub             SP, SP, #0x18
    // 0x6d5f48: SetupParameters(SourceLocation this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x6d5f48: mov             x4, x1
    //     0x6d5f4c: mov             x3, x2
    //     0x6d5f50: stur            x1, [fp, #-8]
    //     0x6d5f54: stur            x2, [fp, #-0x10]
    // 0x6d5f58: CheckStackOverflow
    //     0x6d5f58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6d5f5c: cmp             SP, x16
    //     0x6d5f60: b.ls            #0x6d5ff8
    // 0x6d5f64: mov             x0, x3
    // 0x6d5f68: r2 = Null
    //     0x6d5f68: mov             x2, NULL
    // 0x6d5f6c: r1 = Null
    //     0x6d5f6c: mov             x1, NULL
    // 0x6d5f70: r4 = 60
    //     0x6d5f70: movz            x4, #0x3c
    // 0x6d5f74: branchIfSmi(r0, 0x6d5f80)
    //     0x6d5f74: tbz             w0, #0, #0x6d5f80
    // 0x6d5f78: r4 = LoadClassIdInstr(r0)
    //     0x6d5f78: ldur            x4, [x0, #-1]
    //     0x6d5f7c: ubfx            x4, x4, #0xc, #0x14
    // 0x6d5f80: sub             x4, x4, #0x1f1
    // 0x6d5f84: cmp             x4, #2
    // 0x6d5f88: b.ls            #0x6d5fa0
    // 0x6d5f8c: r8 = SourceLocation
    //     0x6d5f8c: add             x8, PP, #0x21, lsl #12  ; [pp+0x21ed8] Type: SourceLocation
    //     0x6d5f90: ldr             x8, [x8, #0xed8]
    // 0x6d5f94: r3 = Null
    //     0x6d5f94: add             x3, PP, #0x21, lsl #12  ; [pp+0x21ef0] Null
    //     0x6d5f98: ldr             x3, [x3, #0xef0]
    // 0x6d5f9c: r0 = DefaultTypeTest()
    //     0x6d5f9c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x6d5fa0: ldur            x2, [fp, #-0x10]
    // 0x6d5fa4: r0 = LoadClassIdInstr(r2)
    //     0x6d5fa4: ldur            x0, [x2, #-1]
    //     0x6d5fa8: ubfx            x0, x0, #0xc, #0x14
    // 0x6d5fac: mov             x1, x2
    // 0x6d5fb0: r0 = GDT[cid_x0 + -0xffb]()
    //     0x6d5fb0: sub             lr, x0, #0xffb
    //     0x6d5fb4: ldr             lr, [x21, lr, lsl #3]
    //     0x6d5fb8: blr             lr
    // 0x6d5fbc: ldur            x0, [fp, #-8]
    // 0x6d5fc0: LoadField: r2 = r0->field_b
    //     0x6d5fc0: ldur            x2, [x0, #0xb]
    // 0x6d5fc4: ldur            x1, [fp, #-0x10]
    // 0x6d5fc8: stur            x2, [fp, #-0x18]
    // 0x6d5fcc: r0 = LoadClassIdInstr(r1)
    //     0x6d5fcc: ldur            x0, [x1, #-1]
    //     0x6d5fd0: ubfx            x0, x0, #0xc, #0x14
    // 0x6d5fd4: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6d5fd4: sub             lr, x0, #0xffc
    //     0x6d5fd8: ldr             lr, [x21, lr, lsl #3]
    //     0x6d5fdc: blr             lr
    // 0x6d5fe0: ldur            x1, [fp, #-0x18]
    // 0x6d5fe4: sub             x2, x1, x0
    // 0x6d5fe8: mov             x0, x2
    // 0x6d5fec: LeaveFrame
    //     0x6d5fec: mov             SP, fp
    //     0x6d5ff0: ldp             fp, lr, [SP], #0x10
    // 0x6d5ff4: ret
    //     0x6d5ff4: ret             
    // 0x6d5ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6d5ff8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6d5ffc: b               #0x6d5f64
  }
  _ SourceLocation(/* No info */) {
    // ** addr: 0xc1a360, size: 0x1b0
    // 0xc1a360: EnterFrame
    //     0xc1a360: stp             fp, lr, [SP, #-0x10]!
    //     0xc1a364: mov             fp, SP
    // 0xc1a368: AllocStack(0x28)
    //     0xc1a368: sub             SP, SP, #0x28
    // 0xc1a36c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0xc1a36c: mov             x0, x2
    //     0xc1a370: stur            x2, [fp, #-8]
    //     0xc1a374: stur            x5, [fp, #-0x18]
    //     0xc1a378: stur            x3, [fp, #-0x20]
    // 0xc1a37c: CheckStackOverflow
    //     0xc1a37c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1a380: cmp             SP, x16
    //     0xc1a384: b.ls            #0xc1a508
    // 0xc1a388: StoreField: r1->field_b = r0
    //     0xc1a388: stur            x0, [x1, #0xb]
    // 0xc1a38c: StoreField: r1->field_13 = r5
    //     0xc1a38c: stur            x5, [x1, #0x13]
    // 0xc1a390: StoreField: r1->field_1b = r3
    //     0xc1a390: stur            x3, [x1, #0x1b]
    // 0xc1a394: tbnz            x0, #0x3f, #0xc1a3b0
    // 0xc1a398: tbnz            x5, #0x3f, #0xc1a420
    // 0xc1a39c: tbnz            x3, #0x3f, #0xc1a494
    // 0xc1a3a0: r0 = Null
    //     0xc1a3a0: mov             x0, NULL
    // 0xc1a3a4: LeaveFrame
    //     0xc1a3a4: mov             SP, fp
    //     0xc1a3a8: ldp             fp, lr, [SP], #0x10
    // 0xc1a3ac: ret
    //     0xc1a3ac: ret             
    // 0xc1a3b0: r1 = Null
    //     0xc1a3b0: mov             x1, NULL
    // 0xc1a3b4: r2 = 6
    //     0xc1a3b4: movz            x2, #0x6
    // 0xc1a3b8: r0 = AllocateArray()
    //     0xc1a3b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1a3bc: mov             x2, x0
    // 0xc1a3c0: r16 = "Offset may not be negative, was "
    //     0xc1a3c0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c538] "Offset may not be negative, was "
    //     0xc1a3c4: ldr             x16, [x16, #0x538]
    // 0xc1a3c8: StoreField: r2->field_f = r16
    //     0xc1a3c8: stur            w16, [x2, #0xf]
    // 0xc1a3cc: ldur            x3, [fp, #-8]
    // 0xc1a3d0: r0 = BoxInt64Instr(r3)
    //     0xc1a3d0: sbfiz           x0, x3, #1, #0x1f
    //     0xc1a3d4: cmp             x3, x0, asr #1
    //     0xc1a3d8: b.eq            #0xc1a3e4
    //     0xc1a3dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1a3e0: stur            x3, [x0, #7]
    // 0xc1a3e4: StoreField: r2->field_13 = r0
    //     0xc1a3e4: stur            w0, [x2, #0x13]
    // 0xc1a3e8: r16 = "."
    //     0xc1a3e8: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1a3ec: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1a3ec: stur            w16, [x2, #0x17]
    // 0xc1a3f0: str             x2, [SP]
    // 0xc1a3f4: r0 = _interpolate()
    //     0xc1a3f4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1a3f8: stur            x0, [fp, #-0x10]
    // 0xc1a3fc: r0 = RangeError()
    //     0xc1a3fc: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1a400: mov             x1, x0
    // 0xc1a404: ldur            x0, [fp, #-0x10]
    // 0xc1a408: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1a408: stur            w0, [x1, #0x17]
    // 0xc1a40c: r0 = false
    //     0xc1a40c: add             x0, NULL, #0x30  ; false
    // 0xc1a410: StoreField: r1->field_b = r0
    //     0xc1a410: stur            w0, [x1, #0xb]
    // 0xc1a414: mov             x0, x1
    // 0xc1a418: r0 = Throw()
    //     0xc1a418: bl              #0xec04b8  ; ThrowStub
    // 0xc1a41c: brk             #0
    // 0xc1a420: r0 = false
    //     0xc1a420: add             x0, NULL, #0x30  ; false
    // 0xc1a424: r1 = Null
    //     0xc1a424: mov             x1, NULL
    // 0xc1a428: r2 = 6
    //     0xc1a428: movz            x2, #0x6
    // 0xc1a42c: r0 = AllocateArray()
    //     0xc1a42c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1a430: mov             x2, x0
    // 0xc1a434: r16 = "Line may not be negative, was "
    //     0xc1a434: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c788] "Line may not be negative, was "
    //     0xc1a438: ldr             x16, [x16, #0x788]
    // 0xc1a43c: StoreField: r2->field_f = r16
    //     0xc1a43c: stur            w16, [x2, #0xf]
    // 0xc1a440: ldur            x3, [fp, #-0x18]
    // 0xc1a444: r0 = BoxInt64Instr(r3)
    //     0xc1a444: sbfiz           x0, x3, #1, #0x1f
    //     0xc1a448: cmp             x3, x0, asr #1
    //     0xc1a44c: b.eq            #0xc1a458
    //     0xc1a450: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1a454: stur            x3, [x0, #7]
    // 0xc1a458: StoreField: r2->field_13 = r0
    //     0xc1a458: stur            w0, [x2, #0x13]
    // 0xc1a45c: r16 = "."
    //     0xc1a45c: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1a460: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1a460: stur            w16, [x2, #0x17]
    // 0xc1a464: str             x2, [SP]
    // 0xc1a468: r0 = _interpolate()
    //     0xc1a468: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1a46c: stur            x0, [fp, #-0x10]
    // 0xc1a470: r0 = RangeError()
    //     0xc1a470: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1a474: mov             x1, x0
    // 0xc1a478: ldur            x0, [fp, #-0x10]
    // 0xc1a47c: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1a47c: stur            w0, [x1, #0x17]
    // 0xc1a480: r0 = false
    //     0xc1a480: add             x0, NULL, #0x30  ; false
    // 0xc1a484: StoreField: r1->field_b = r0
    //     0xc1a484: stur            w0, [x1, #0xb]
    // 0xc1a488: mov             x0, x1
    // 0xc1a48c: r0 = Throw()
    //     0xc1a48c: bl              #0xec04b8  ; ThrowStub
    // 0xc1a490: brk             #0
    // 0xc1a494: r0 = false
    //     0xc1a494: add             x0, NULL, #0x30  ; false
    // 0xc1a498: r1 = Null
    //     0xc1a498: mov             x1, NULL
    // 0xc1a49c: r2 = 6
    //     0xc1a49c: movz            x2, #0x6
    // 0xc1a4a0: r0 = AllocateArray()
    //     0xc1a4a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1a4a4: mov             x2, x0
    // 0xc1a4a8: r16 = "Column may not be negative, was "
    //     0xc1a4a8: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c790] "Column may not be negative, was "
    //     0xc1a4ac: ldr             x16, [x16, #0x790]
    // 0xc1a4b0: StoreField: r2->field_f = r16
    //     0xc1a4b0: stur            w16, [x2, #0xf]
    // 0xc1a4b4: ldur            x3, [fp, #-0x20]
    // 0xc1a4b8: r0 = BoxInt64Instr(r3)
    //     0xc1a4b8: sbfiz           x0, x3, #1, #0x1f
    //     0xc1a4bc: cmp             x3, x0, asr #1
    //     0xc1a4c0: b.eq            #0xc1a4cc
    //     0xc1a4c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1a4c8: stur            x3, [x0, #7]
    // 0xc1a4cc: StoreField: r2->field_13 = r0
    //     0xc1a4cc: stur            w0, [x2, #0x13]
    // 0xc1a4d0: r16 = "."
    //     0xc1a4d0: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1a4d4: ArrayStore: r2[0] = r16  ; List_4
    //     0xc1a4d4: stur            w16, [x2, #0x17]
    // 0xc1a4d8: str             x2, [SP]
    // 0xc1a4dc: r0 = _interpolate()
    //     0xc1a4dc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1a4e0: stur            x0, [fp, #-0x10]
    // 0xc1a4e4: r0 = RangeError()
    //     0xc1a4e4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xc1a4e8: mov             x1, x0
    // 0xc1a4ec: ldur            x0, [fp, #-0x10]
    // 0xc1a4f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1a4f0: stur            w0, [x1, #0x17]
    // 0xc1a4f4: r0 = false
    //     0xc1a4f4: add             x0, NULL, #0x30  ; false
    // 0xc1a4f8: StoreField: r1->field_b = r0
    //     0xc1a4f8: stur            w0, [x1, #0xb]
    // 0xc1a4fc: mov             x0, x1
    // 0xc1a500: r0 = Throw()
    //     0xc1a500: bl              #0xec04b8  ; ThrowStub
    // 0xc1a504: brk             #0
    // 0xc1a508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1a508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1a50c: b               #0xc1a388
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3fb9c, size: 0xc8
    // 0xc3fb9c: EnterFrame
    //     0xc3fb9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc3fba0: mov             fp, SP
    // 0xc3fba4: AllocStack(0x10)
    //     0xc3fba4: sub             SP, SP, #0x10
    // 0xc3fba8: CheckStackOverflow
    //     0xc3fba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3fbac: cmp             SP, x16
    //     0xc3fbb0: b.ls            #0xc3fc5c
    // 0xc3fbb4: r1 = Null
    //     0xc3fbb4: mov             x1, NULL
    // 0xc3fbb8: r2 = 14
    //     0xc3fbb8: movz            x2, #0xe
    // 0xc3fbbc: r0 = AllocateArray()
    //     0xc3fbbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3fbc0: mov             x2, x0
    // 0xc3fbc4: stur            x2, [fp, #-8]
    // 0xc3fbc8: r16 = "<"
    //     0xc3fbc8: ldr             x16, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xc3fbcc: StoreField: r2->field_f = r16
    //     0xc3fbcc: stur            w16, [x2, #0xf]
    // 0xc3fbd0: r16 = SourceLocation
    //     0xc3fbd0: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ed8] Type: SourceLocation
    //     0xc3fbd4: ldr             x16, [x16, #0xed8]
    // 0xc3fbd8: StoreField: r2->field_13 = r16
    //     0xc3fbd8: stur            w16, [x2, #0x13]
    // 0xc3fbdc: r16 = ": "
    //     0xc3fbdc: ldr             x16, [PP, #0x7d8]  ; [pp+0x7d8] ": "
    // 0xc3fbe0: ArrayStore: r2[0] = r16  ; List_4
    //     0xc3fbe0: stur            w16, [x2, #0x17]
    // 0xc3fbe4: ldr             x3, [fp, #0x10]
    // 0xc3fbe8: LoadField: r4 = r3->field_b
    //     0xc3fbe8: ldur            x4, [x3, #0xb]
    // 0xc3fbec: r0 = BoxInt64Instr(r4)
    //     0xc3fbec: sbfiz           x0, x4, #1, #0x1f
    //     0xc3fbf0: cmp             x4, x0, asr #1
    //     0xc3fbf4: b.eq            #0xc3fc00
    //     0xc3fbf8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3fbfc: stur            x4, [x0, #7]
    // 0xc3fc00: StoreField: r2->field_1b = r0
    //     0xc3fc00: stur            w0, [x2, #0x1b]
    // 0xc3fc04: r16 = " "
    //     0xc3fc04: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc3fc08: StoreField: r2->field_1f = r16
    //     0xc3fc08: stur            w16, [x2, #0x1f]
    // 0xc3fc0c: mov             x1, x3
    // 0xc3fc10: r0 = toolString()
    //     0xc3fc10: bl              #0xeb7318  ; [package:source_span/src/location.dart] SourceLocation::toolString
    // 0xc3fc14: ldur            x1, [fp, #-8]
    // 0xc3fc18: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3fc18: add             x25, x1, #0x23
    //     0xc3fc1c: str             w0, [x25]
    //     0xc3fc20: tbz             w0, #0, #0xc3fc3c
    //     0xc3fc24: ldurb           w16, [x1, #-1]
    //     0xc3fc28: ldurb           w17, [x0, #-1]
    //     0xc3fc2c: and             x16, x17, x16, lsr #2
    //     0xc3fc30: tst             x16, HEAP, lsr #32
    //     0xc3fc34: b.eq            #0xc3fc3c
    //     0xc3fc38: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3fc3c: ldur            x0, [fp, #-8]
    // 0xc3fc40: r16 = ">"
    //     0xc3fc40: ldr             x16, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xc3fc44: StoreField: r0->field_27 = r16
    //     0xc3fc44: stur            w16, [x0, #0x27]
    // 0xc3fc48: str             x0, [SP]
    // 0xc3fc4c: r0 = _interpolate()
    //     0xc3fc4c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3fc50: LeaveFrame
    //     0xc3fc50: mov             SP, fp
    //     0xc3fc54: ldp             fp, lr, [SP], #0x10
    // 0xc3fc58: ret
    //     0xc3fc58: ret             
    // 0xc3fc5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3fc5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3fc60: b               #0xc3fbb4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7da9c, size: 0xc4
    // 0xd7da9c: EnterFrame
    //     0xd7da9c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7daa0: mov             fp, SP
    // 0xd7daa4: AllocStack(0x8)
    //     0xd7daa4: sub             SP, SP, #8
    // 0xd7daa8: CheckStackOverflow
    //     0xd7daa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7daac: cmp             SP, x16
    //     0xd7dab0: b.ls            #0xd7db58
    // 0xd7dab4: ldr             x2, [fp, #0x10]
    // 0xd7dab8: cmp             w2, NULL
    // 0xd7dabc: b.ne            #0xd7dad0
    // 0xd7dac0: r0 = false
    //     0xd7dac0: add             x0, NULL, #0x30  ; false
    // 0xd7dac4: LeaveFrame
    //     0xd7dac4: mov             SP, fp
    //     0xd7dac8: ldp             fp, lr, [SP], #0x10
    // 0xd7dacc: ret
    //     0xd7dacc: ret             
    // 0xd7dad0: r0 = 60
    //     0xd7dad0: movz            x0, #0x3c
    // 0xd7dad4: branchIfSmi(r2, 0xd7dae0)
    //     0xd7dad4: tbz             w2, #0, #0xd7dae0
    // 0xd7dad8: r0 = LoadClassIdInstr(r2)
    //     0xd7dad8: ldur            x0, [x2, #-1]
    //     0xd7dadc: ubfx            x0, x0, #0xc, #0x14
    // 0xd7dae0: sub             x16, x0, #0x1f1
    // 0xd7dae4: cmp             x16, #2
    // 0xd7dae8: b.hi            #0xd7db48
    // 0xd7daec: ldr             x3, [fp, #0x18]
    // 0xd7daf0: r0 = LoadClassIdInstr(r2)
    //     0xd7daf0: ldur            x0, [x2, #-1]
    //     0xd7daf4: ubfx            x0, x0, #0xc, #0x14
    // 0xd7daf8: mov             x1, x2
    // 0xd7dafc: r0 = GDT[cid_x0 + -0xffb]()
    //     0xd7dafc: sub             lr, x0, #0xffb
    //     0xd7db00: ldr             lr, [x21, lr, lsl #3]
    //     0xd7db04: blr             lr
    // 0xd7db08: ldr             x0, [fp, #0x18]
    // 0xd7db0c: LoadField: r2 = r0->field_b
    //     0xd7db0c: ldur            x2, [x0, #0xb]
    // 0xd7db10: ldr             x1, [fp, #0x10]
    // 0xd7db14: stur            x2, [fp, #-8]
    // 0xd7db18: r0 = LoadClassIdInstr(r1)
    //     0xd7db18: ldur            x0, [x1, #-1]
    //     0xd7db1c: ubfx            x0, x0, #0xc, #0x14
    // 0xd7db20: r0 = GDT[cid_x0 + -0xffc]()
    //     0xd7db20: sub             lr, x0, #0xffc
    //     0xd7db24: ldr             lr, [x21, lr, lsl #3]
    //     0xd7db28: blr             lr
    // 0xd7db2c: ldur            x1, [fp, #-8]
    // 0xd7db30: cmp             x1, x0
    // 0xd7db34: r16 = true
    //     0xd7db34: add             x16, NULL, #0x20  ; true
    // 0xd7db38: r17 = false
    //     0xd7db38: add             x17, NULL, #0x30  ; false
    // 0xd7db3c: csel            x2, x16, x17, eq
    // 0xd7db40: mov             x0, x2
    // 0xd7db44: b               #0xd7db4c
    // 0xd7db48: r0 = false
    //     0xd7db48: add             x0, NULL, #0x30  ; false
    // 0xd7db4c: LeaveFrame
    //     0xd7db4c: mov             SP, fp
    //     0xd7db50: ldp             fp, lr, [SP], #0x10
    // 0xd7db54: ret
    //     0xd7db54: ret             
    // 0xd7db58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7db58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7db5c: b               #0xd7dab4
  }
  get _ toolString(/* No info */) {
    // ** addr: 0xeb7318, size: 0xac
    // 0xeb7318: EnterFrame
    //     0xeb7318: stp             fp, lr, [SP, #-0x10]!
    //     0xeb731c: mov             fp, SP
    // 0xeb7320: AllocStack(0x10)
    //     0xeb7320: sub             SP, SP, #0x10
    // 0xeb7324: SetupParameters(SourceLocation this /* r1 => r0, fp-0x8 */)
    //     0xeb7324: mov             x0, x1
    //     0xeb7328: stur            x1, [fp, #-8]
    // 0xeb732c: CheckStackOverflow
    //     0xeb732c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7330: cmp             SP, x16
    //     0xeb7334: b.ls            #0xeb73bc
    // 0xeb7338: r1 = Null
    //     0xeb7338: mov             x1, NULL
    // 0xeb733c: r2 = 10
    //     0xeb733c: movz            x2, #0xa
    // 0xeb7340: r0 = AllocateArray()
    //     0xeb7340: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb7344: mov             x2, x0
    // 0xeb7348: r16 = "unknown source"
    //     0xeb7348: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ed0] "unknown source"
    //     0xeb734c: ldr             x16, [x16, #0xed0]
    // 0xeb7350: StoreField: r2->field_f = r16
    //     0xeb7350: stur            w16, [x2, #0xf]
    // 0xeb7354: r16 = ":"
    //     0xeb7354: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xeb7358: StoreField: r2->field_13 = r16
    //     0xeb7358: stur            w16, [x2, #0x13]
    // 0xeb735c: ldur            x3, [fp, #-8]
    // 0xeb7360: LoadField: r0 = r3->field_13
    //     0xeb7360: ldur            x0, [x3, #0x13]
    // 0xeb7364: add             x4, x0, #1
    // 0xeb7368: r0 = BoxInt64Instr(r4)
    //     0xeb7368: sbfiz           x0, x4, #1, #0x1f
    //     0xeb736c: cmp             x4, x0, asr #1
    //     0xeb7370: b.eq            #0xeb737c
    //     0xeb7374: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb7378: stur            x4, [x0, #7]
    // 0xeb737c: ArrayStore: r2[0] = r0  ; List_4
    //     0xeb737c: stur            w0, [x2, #0x17]
    // 0xeb7380: r16 = ":"
    //     0xeb7380: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xeb7384: StoreField: r2->field_1b = r16
    //     0xeb7384: stur            w16, [x2, #0x1b]
    // 0xeb7388: LoadField: r0 = r3->field_1b
    //     0xeb7388: ldur            x0, [x3, #0x1b]
    // 0xeb738c: add             x3, x0, #1
    // 0xeb7390: r0 = BoxInt64Instr(r3)
    //     0xeb7390: sbfiz           x0, x3, #1, #0x1f
    //     0xeb7394: cmp             x3, x0, asr #1
    //     0xeb7398: b.eq            #0xeb73a4
    //     0xeb739c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb73a0: stur            x3, [x0, #7]
    // 0xeb73a4: StoreField: r2->field_1f = r0
    //     0xeb73a4: stur            w0, [x2, #0x1f]
    // 0xeb73a8: str             x2, [SP]
    // 0xeb73ac: r0 = _interpolate()
    //     0xeb73ac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb73b0: LeaveFrame
    //     0xeb73b0: mov             SP, fp
    //     0xeb73b4: ldp             fp, lr, [SP], #0x10
    // 0xeb73b8: ret
    //     0xeb73b8: ret             
    // 0xeb73bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb73bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb73c0: b               #0xeb7338
  }
  _ distance(/* No info */) {
    // ** addr: 0xeb7480, size: 0x90
    // 0xeb7480: EnterFrame
    //     0xeb7480: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7484: mov             fp, SP
    // 0xeb7488: AllocStack(0x18)
    //     0xeb7488: sub             SP, SP, #0x18
    // 0xeb748c: SetupParameters(SourceLocation this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xeb748c: mov             x3, x1
    //     0xeb7490: stur            x1, [fp, #-8]
    //     0xeb7494: stur            x2, [fp, #-0x10]
    // 0xeb7498: CheckStackOverflow
    //     0xeb7498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb749c: cmp             SP, x16
    //     0xeb74a0: b.ls            #0xeb7508
    // 0xeb74a4: r0 = LoadClassIdInstr(r2)
    //     0xeb74a4: ldur            x0, [x2, #-1]
    //     0xeb74a8: ubfx            x0, x0, #0xc, #0x14
    // 0xeb74ac: mov             x1, x2
    // 0xeb74b0: r0 = GDT[cid_x0 + -0xffb]()
    //     0xeb74b0: sub             lr, x0, #0xffb
    //     0xeb74b4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb74b8: blr             lr
    // 0xeb74bc: ldur            x0, [fp, #-8]
    // 0xeb74c0: LoadField: r2 = r0->field_b
    //     0xeb74c0: ldur            x2, [x0, #0xb]
    // 0xeb74c4: ldur            x1, [fp, #-0x10]
    // 0xeb74c8: stur            x2, [fp, #-0x18]
    // 0xeb74cc: r0 = LoadClassIdInstr(r1)
    //     0xeb74cc: ldur            x0, [x1, #-1]
    //     0xeb74d0: ubfx            x0, x0, #0xc, #0x14
    // 0xeb74d4: r0 = GDT[cid_x0 + -0xffc]()
    //     0xeb74d4: sub             lr, x0, #0xffc
    //     0xeb74d8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb74dc: blr             lr
    // 0xeb74e0: ldur            x1, [fp, #-0x18]
    // 0xeb74e4: sub             x2, x1, x0
    // 0xeb74e8: tbz             x2, #0x3f, #0xeb74f8
    // 0xeb74ec: neg             x1, x2
    // 0xeb74f0: mov             x0, x1
    // 0xeb74f4: b               #0xeb74fc
    // 0xeb74f8: mov             x0, x2
    // 0xeb74fc: LeaveFrame
    //     0xeb74fc: mov             SP, fp
    //     0xeb7500: ldp             fp, lr, [SP], #0x10
    // 0xeb7504: ret
    //     0xeb7504: ret             
    // 0xeb7508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb750c: b               #0xeb74a4
  }
  const get _ offset(/* No info */) {
    // ** addr: 0xeb793c, size: 0x8
    // 0xeb793c: LoadField: r0 = r1->field_b
    //     0xeb793c: ldur            x0, [x1, #0xb]
    // 0xeb7940: ret
    //     0xeb7940: ret             
  }
  const get _ column(/* No info */) {
    // ** addr: 0xeb79c0, size: 0x8
    // 0xeb79c0: LoadField: r0 = r1->field_1b
    //     0xeb79c0: ldur            x0, [x1, #0x1b]
    // 0xeb79c4: ret
    //     0xeb79c4: ret             
  }
  const get _ line(/* No info */) {
    // ** addr: 0xeb79c8, size: 0x8
    // 0xeb79c8: LoadField: r0 = r1->field_13
    //     0xeb79c8: ldur            x0, [x1, #0x13]
    // 0xeb79cc: ret
    //     0xeb79cc: ret             
  }
}
