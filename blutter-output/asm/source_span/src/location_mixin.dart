// lib: , url: package:source_span/src/location_mixin.dart

// class id: 1051140, size: 0x8
class :: {
}

// class id: 498, size: 0x8, field offset: 0x8
abstract class SourceLocationMixin extends Object
    implements SourceLocation {

  _ compareTo(/* No info */) {
    // ** addr: 0x6d5e78, size: 0xc4
    // 0x6d5e78: EnterFrame
    //     0x6d5e78: stp             fp, lr, [SP, #-0x10]!
    //     0x6d5e7c: mov             fp, SP
    // 0x6d5e80: AllocStack(0x18)
    //     0x6d5e80: sub             SP, SP, #0x18
    // 0x6d5e84: SetupParameters(SourceLocationMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x6d5e84: mov             x4, x1
    //     0x6d5e88: mov             x3, x2
    //     0x6d5e8c: stur            x1, [fp, #-8]
    //     0x6d5e90: stur            x2, [fp, #-0x10]
    // 0x6d5e94: CheckStackOverflow
    //     0x6d5e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6d5e98: cmp             SP, x16
    //     0x6d5e9c: b.ls            #0x6d5f34
    // 0x6d5ea0: mov             x0, x3
    // 0x6d5ea4: r2 = Null
    //     0x6d5ea4: mov             x2, NULL
    // 0x6d5ea8: r1 = Null
    //     0x6d5ea8: mov             x1, NULL
    // 0x6d5eac: r4 = 60
    //     0x6d5eac: movz            x4, #0x3c
    // 0x6d5eb0: branchIfSmi(r0, 0x6d5ebc)
    //     0x6d5eb0: tbz             w0, #0, #0x6d5ebc
    // 0x6d5eb4: r4 = LoadClassIdInstr(r0)
    //     0x6d5eb4: ldur            x4, [x0, #-1]
    //     0x6d5eb8: ubfx            x4, x4, #0xc, #0x14
    // 0x6d5ebc: sub             x4, x4, #0x1f1
    // 0x6d5ec0: cmp             x4, #2
    // 0x6d5ec4: b.ls            #0x6d5edc
    // 0x6d5ec8: r8 = SourceLocation
    //     0x6d5ec8: add             x8, PP, #0x21, lsl #12  ; [pp+0x21ed8] Type: SourceLocation
    //     0x6d5ecc: ldr             x8, [x8, #0xed8]
    // 0x6d5ed0: r3 = Null
    //     0x6d5ed0: add             x3, PP, #0x21, lsl #12  ; [pp+0x21ee0] Null
    //     0x6d5ed4: ldr             x3, [x3, #0xee0]
    // 0x6d5ed8: r0 = DefaultTypeTest()
    //     0x6d5ed8: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x6d5edc: ldur            x2, [fp, #-0x10]
    // 0x6d5ee0: r0 = LoadClassIdInstr(r2)
    //     0x6d5ee0: ldur            x0, [x2, #-1]
    //     0x6d5ee4: ubfx            x0, x0, #0xc, #0x14
    // 0x6d5ee8: mov             x1, x2
    // 0x6d5eec: r0 = GDT[cid_x0 + -0xffb]()
    //     0x6d5eec: sub             lr, x0, #0xffb
    //     0x6d5ef0: ldr             lr, [x21, lr, lsl #3]
    //     0x6d5ef4: blr             lr
    // 0x6d5ef8: ldur            x0, [fp, #-8]
    // 0x6d5efc: LoadField: r2 = r0->field_b
    //     0x6d5efc: ldur            x2, [x0, #0xb]
    // 0x6d5f00: ldur            x1, [fp, #-0x10]
    // 0x6d5f04: stur            x2, [fp, #-0x18]
    // 0x6d5f08: r0 = LoadClassIdInstr(r1)
    //     0x6d5f08: ldur            x0, [x1, #-1]
    //     0x6d5f0c: ubfx            x0, x0, #0xc, #0x14
    // 0x6d5f10: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6d5f10: sub             lr, x0, #0xffc
    //     0x6d5f14: ldr             lr, [x21, lr, lsl #3]
    //     0x6d5f18: blr             lr
    // 0x6d5f1c: ldur            x1, [fp, #-0x18]
    // 0x6d5f20: sub             x2, x1, x0
    // 0x6d5f24: mov             x0, x2
    // 0x6d5f28: LeaveFrame
    //     0x6d5f28: mov             SP, fp
    //     0x6d5f2c: ldp             fp, lr, [SP], #0x10
    // 0x6d5f30: ret
    //     0x6d5f30: ret             
    // 0x6d5f34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6d5f34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6d5f38: b               #0x6d5ea0
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3fa88, size: 0x114
    // 0xc3fa88: EnterFrame
    //     0xc3fa88: stp             fp, lr, [SP, #-0x10]!
    //     0xc3fa8c: mov             fp, SP
    // 0xc3fa90: AllocStack(0x10)
    //     0xc3fa90: sub             SP, SP, #0x10
    // 0xc3fa94: CheckStackOverflow
    //     0xc3fa94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3fa98: cmp             SP, x16
    //     0xc3fa9c: b.ls            #0xc3fb94
    // 0xc3faa0: r1 = Null
    //     0xc3faa0: mov             x1, NULL
    // 0xc3faa4: r2 = 14
    //     0xc3faa4: movz            x2, #0xe
    // 0xc3faa8: r0 = AllocateArray()
    //     0xc3faa8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3faac: stur            x0, [fp, #-8]
    // 0xc3fab0: r16 = "<"
    //     0xc3fab0: ldr             x16, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xc3fab4: StoreField: r0->field_f = r16
    //     0xc3fab4: stur            w16, [x0, #0xf]
    // 0xc3fab8: ldr             x16, [fp, #0x10]
    // 0xc3fabc: str             x16, [SP]
    // 0xc3fac0: r0 = runtimeType()
    //     0xc3fac0: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xc3fac4: ldur            x1, [fp, #-8]
    // 0xc3fac8: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3fac8: add             x25, x1, #0x13
    //     0xc3facc: str             w0, [x25]
    //     0xc3fad0: tbz             w0, #0, #0xc3faec
    //     0xc3fad4: ldurb           w16, [x1, #-1]
    //     0xc3fad8: ldurb           w17, [x0, #-1]
    //     0xc3fadc: and             x16, x17, x16, lsr #2
    //     0xc3fae0: tst             x16, HEAP, lsr #32
    //     0xc3fae4: b.eq            #0xc3faec
    //     0xc3fae8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3faec: ldur            x2, [fp, #-8]
    // 0xc3faf0: r16 = ": "
    //     0xc3faf0: ldr             x16, [PP, #0x7d8]  ; [pp+0x7d8] ": "
    // 0xc3faf4: ArrayStore: r2[0] = r16  ; List_4
    //     0xc3faf4: stur            w16, [x2, #0x17]
    // 0xc3faf8: ldr             x3, [fp, #0x10]
    // 0xc3fafc: LoadField: r4 = r3->field_b
    //     0xc3fafc: ldur            x4, [x3, #0xb]
    // 0xc3fb00: r0 = BoxInt64Instr(r4)
    //     0xc3fb00: sbfiz           x0, x4, #1, #0x1f
    //     0xc3fb04: cmp             x4, x0, asr #1
    //     0xc3fb08: b.eq            #0xc3fb14
    //     0xc3fb0c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc3fb10: stur            x4, [x0, #7]
    // 0xc3fb14: mov             x1, x2
    // 0xc3fb18: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3fb18: add             x25, x1, #0x1b
    //     0xc3fb1c: str             w0, [x25]
    //     0xc3fb20: tbz             w0, #0, #0xc3fb3c
    //     0xc3fb24: ldurb           w16, [x1, #-1]
    //     0xc3fb28: ldurb           w17, [x0, #-1]
    //     0xc3fb2c: and             x16, x17, x16, lsr #2
    //     0xc3fb30: tst             x16, HEAP, lsr #32
    //     0xc3fb34: b.eq            #0xc3fb3c
    //     0xc3fb38: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3fb3c: r16 = " "
    //     0xc3fb3c: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc3fb40: StoreField: r2->field_1f = r16
    //     0xc3fb40: stur            w16, [x2, #0x1f]
    // 0xc3fb44: mov             x1, x3
    // 0xc3fb48: r0 = toolString()
    //     0xc3fb48: bl              #0xeb71e4  ; [package:source_span/src/location_mixin.dart] SourceLocationMixin::toolString
    // 0xc3fb4c: ldur            x1, [fp, #-8]
    // 0xc3fb50: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3fb50: add             x25, x1, #0x23
    //     0xc3fb54: str             w0, [x25]
    //     0xc3fb58: tbz             w0, #0, #0xc3fb74
    //     0xc3fb5c: ldurb           w16, [x1, #-1]
    //     0xc3fb60: ldurb           w17, [x0, #-1]
    //     0xc3fb64: and             x16, x17, x16, lsr #2
    //     0xc3fb68: tst             x16, HEAP, lsr #32
    //     0xc3fb6c: b.eq            #0xc3fb74
    //     0xc3fb70: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3fb74: ldur            x0, [fp, #-8]
    // 0xc3fb78: r16 = ">"
    //     0xc3fb78: ldr             x16, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xc3fb7c: StoreField: r0->field_27 = r16
    //     0xc3fb7c: stur            w16, [x0, #0x27]
    // 0xc3fb80: str             x0, [SP]
    // 0xc3fb84: r0 = _interpolate()
    //     0xc3fb84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3fb88: LeaveFrame
    //     0xc3fb88: mov             SP, fp
    //     0xc3fb8c: ldp             fp, lr, [SP], #0x10
    // 0xc3fb90: ret
    //     0xc3fb90: ret             
    // 0xc3fb94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3fb94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3fb98: b               #0xc3faa0
  }
  get _ toolString(/* No info */) {
    // ** addr: 0xeb71e4, size: 0x124
    // 0xeb71e4: EnterFrame
    //     0xeb71e4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb71e8: mov             fp, SP
    // 0xeb71ec: AllocStack(0x28)
    //     0xeb71ec: sub             SP, SP, #0x28
    // 0xeb71f0: SetupParameters(SourceLocationMixin this /* r1 => r0, fp-0x8 */)
    //     0xeb71f0: mov             x0, x1
    //     0xeb71f4: stur            x1, [fp, #-8]
    // 0xeb71f8: CheckStackOverflow
    //     0xeb71f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb71fc: cmp             SP, x16
    //     0xeb7200: b.ls            #0xeb7300
    // 0xeb7204: r1 = Null
    //     0xeb7204: mov             x1, NULL
    // 0xeb7208: r2 = 10
    //     0xeb7208: movz            x2, #0xa
    // 0xeb720c: r0 = AllocateArray()
    //     0xeb720c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb7210: stur            x0, [fp, #-0x20]
    // 0xeb7214: r16 = "unknown source"
    //     0xeb7214: add             x16, PP, #0x21, lsl #12  ; [pp+0x21ed0] "unknown source"
    //     0xeb7218: ldr             x16, [x16, #0xed0]
    // 0xeb721c: StoreField: r0->field_f = r16
    //     0xeb721c: stur            w16, [x0, #0xf]
    // 0xeb7220: r16 = ":"
    //     0xeb7220: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xeb7224: StoreField: r0->field_13 = r16
    //     0xeb7224: stur            w16, [x0, #0x13]
    // 0xeb7228: ldur            x1, [fp, #-8]
    // 0xeb722c: LoadField: r3 = r1->field_7
    //     0xeb722c: ldur            w3, [x1, #7]
    // 0xeb7230: DecompressPointer r3
    //     0xeb7230: add             x3, x3, HEAP, lsl #32
    // 0xeb7234: stur            x3, [fp, #-0x18]
    // 0xeb7238: LoadField: r4 = r1->field_b
    //     0xeb7238: ldur            x4, [x1, #0xb]
    // 0xeb723c: mov             x1, x3
    // 0xeb7240: mov             x2, x4
    // 0xeb7244: stur            x4, [fp, #-0x10]
    // 0xeb7248: r0 = getLine()
    //     0xeb7248: bl              #0xc1b388  ; [package:source_span/src/file.dart] SourceFile::getLine
    // 0xeb724c: add             x2, x0, #1
    // 0xeb7250: r0 = BoxInt64Instr(r2)
    //     0xeb7250: sbfiz           x0, x2, #1, #0x1f
    //     0xeb7254: cmp             x2, x0, asr #1
    //     0xeb7258: b.eq            #0xeb7264
    //     0xeb725c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb7260: stur            x2, [x0, #7]
    // 0xeb7264: ldur            x1, [fp, #-0x20]
    // 0xeb7268: ArrayStore: r1[2] = r0  ; List_4
    //     0xeb7268: add             x25, x1, #0x17
    //     0xeb726c: str             w0, [x25]
    //     0xeb7270: tbz             w0, #0, #0xeb728c
    //     0xeb7274: ldurb           w16, [x1, #-1]
    //     0xeb7278: ldurb           w17, [x0, #-1]
    //     0xeb727c: and             x16, x17, x16, lsr #2
    //     0xeb7280: tst             x16, HEAP, lsr #32
    //     0xeb7284: b.eq            #0xeb728c
    //     0xeb7288: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeb728c: ldur            x0, [fp, #-0x20]
    // 0xeb7290: r16 = ":"
    //     0xeb7290: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xeb7294: StoreField: r0->field_1b = r16
    //     0xeb7294: stur            w16, [x0, #0x1b]
    // 0xeb7298: ldur            x1, [fp, #-0x18]
    // 0xeb729c: ldur            x2, [fp, #-0x10]
    // 0xeb72a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeb72a0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeb72a4: r0 = getColumn()
    //     0xeb72a4: bl              #0xc1b124  ; [package:source_span/src/file.dart] SourceFile::getColumn
    // 0xeb72a8: add             x2, x0, #1
    // 0xeb72ac: r0 = BoxInt64Instr(r2)
    //     0xeb72ac: sbfiz           x0, x2, #1, #0x1f
    //     0xeb72b0: cmp             x2, x0, asr #1
    //     0xeb72b4: b.eq            #0xeb72c0
    //     0xeb72b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb72bc: stur            x2, [x0, #7]
    // 0xeb72c0: ldur            x1, [fp, #-0x20]
    // 0xeb72c4: ArrayStore: r1[4] = r0  ; List_4
    //     0xeb72c4: add             x25, x1, #0x1f
    //     0xeb72c8: str             w0, [x25]
    //     0xeb72cc: tbz             w0, #0, #0xeb72e8
    //     0xeb72d0: ldurb           w16, [x1, #-1]
    //     0xeb72d4: ldurb           w17, [x0, #-1]
    //     0xeb72d8: and             x16, x17, x16, lsr #2
    //     0xeb72dc: tst             x16, HEAP, lsr #32
    //     0xeb72e0: b.eq            #0xeb72e8
    //     0xeb72e4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeb72e8: ldur            x16, [fp, #-0x20]
    // 0xeb72ec: str             x16, [SP]
    // 0xeb72f0: r0 = _interpolate()
    //     0xeb72f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb72f4: LeaveFrame
    //     0xeb72f4: mov             SP, fp
    //     0xeb72f8: ldp             fp, lr, [SP], #0x10
    // 0xeb72fc: ret
    //     0xeb72fc: ret             
    // 0xeb7300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7300: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7304: b               #0xeb7204
  }
}
