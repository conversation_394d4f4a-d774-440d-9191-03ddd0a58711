// lib: , url: package:source_span/src/span.dart

// class id: 1051141, size: 0x8
class :: {
}

// class id: 488, size: 0x8, field offset: 0x8
abstract class SourceSpan extends Object
    implements Comparable<X0> {
}

// class id: 494, size: 0x14, field offset: 0x8
abstract class SourceSpanBase extends SourceSpanMixin {

  _ SourceSpanBase(/* No info */) {
    // ** addr: 0xc1a098, size: 0x2bc
    // 0xc1a098: EnterFrame
    //     0xc1a098: stp             fp, lr, [SP, #-0x10]!
    //     0xc1a09c: mov             fp, SP
    // 0xc1a0a0: AllocStack(0x30)
    //     0xc1a0a0: sub             SP, SP, #0x30
    // 0xc1a0a4: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r2, fp-0x18 */)
    //     0xc1a0a4: mov             x4, x2
    //     0xc1a0a8: stur            x2, [fp, #-8]
    //     0xc1a0ac: mov             x2, x5
    //     0xc1a0b0: stur            x3, [fp, #-0x10]
    //     0xc1a0b4: stur            x5, [fp, #-0x18]
    // 0xc1a0b8: CheckStackOverflow
    //     0xc1a0b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1a0bc: cmp             SP, x16
    //     0xc1a0c0: b.ls            #0xc1a34c
    // 0xc1a0c4: mov             x0, x4
    // 0xc1a0c8: StoreField: r1->field_7 = r0
    //     0xc1a0c8: stur            w0, [x1, #7]
    //     0xc1a0cc: ldurb           w16, [x1, #-1]
    //     0xc1a0d0: ldurb           w17, [x0, #-1]
    //     0xc1a0d4: and             x16, x17, x16, lsr #2
    //     0xc1a0d8: tst             x16, HEAP, lsr #32
    //     0xc1a0dc: b.eq            #0xc1a0e4
    //     0xc1a0e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc1a0e4: mov             x0, x3
    // 0xc1a0e8: StoreField: r1->field_b = r0
    //     0xc1a0e8: stur            w0, [x1, #0xb]
    //     0xc1a0ec: ldurb           w16, [x1, #-1]
    //     0xc1a0f0: ldurb           w17, [x0, #-1]
    //     0xc1a0f4: and             x16, x17, x16, lsr #2
    //     0xc1a0f8: tst             x16, HEAP, lsr #32
    //     0xc1a0fc: b.eq            #0xc1a104
    //     0xc1a100: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc1a104: mov             x0, x2
    // 0xc1a108: StoreField: r1->field_f = r0
    //     0xc1a108: stur            w0, [x1, #0xf]
    //     0xc1a10c: ldurb           w16, [x1, #-1]
    //     0xc1a110: ldurb           w17, [x0, #-1]
    //     0xc1a114: and             x16, x17, x16, lsr #2
    //     0xc1a118: tst             x16, HEAP, lsr #32
    //     0xc1a11c: b.eq            #0xc1a124
    //     0xc1a120: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc1a124: r0 = LoadClassIdInstr(r3)
    //     0xc1a124: ldur            x0, [x3, #-1]
    //     0xc1a128: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a12c: mov             x1, x3
    // 0xc1a130: r0 = GDT[cid_x0 + -0xffb]()
    //     0xc1a130: sub             lr, x0, #0xffb
    //     0xc1a134: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a138: blr             lr
    // 0xc1a13c: ldur            x2, [fp, #-8]
    // 0xc1a140: r0 = LoadClassIdInstr(r2)
    //     0xc1a140: ldur            x0, [x2, #-1]
    //     0xc1a144: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a148: mov             x1, x2
    // 0xc1a14c: r0 = GDT[cid_x0 + -0xffb]()
    //     0xc1a14c: sub             lr, x0, #0xffb
    //     0xc1a150: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a154: blr             lr
    // 0xc1a158: ldur            x2, [fp, #-0x10]
    // 0xc1a15c: r0 = LoadClassIdInstr(r2)
    //     0xc1a15c: ldur            x0, [x2, #-1]
    //     0xc1a160: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a164: mov             x1, x2
    // 0xc1a168: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc1a168: sub             lr, x0, #0xffc
    //     0xc1a16c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a170: blr             lr
    // 0xc1a174: mov             x3, x0
    // 0xc1a178: ldur            x2, [fp, #-8]
    // 0xc1a17c: stur            x3, [fp, #-0x20]
    // 0xc1a180: r0 = LoadClassIdInstr(r2)
    //     0xc1a180: ldur            x0, [x2, #-1]
    //     0xc1a184: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a188: mov             x1, x2
    // 0xc1a18c: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc1a18c: sub             lr, x0, #0xffc
    //     0xc1a190: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a194: blr             lr
    // 0xc1a198: mov             x1, x0
    // 0xc1a19c: ldur            x0, [fp, #-0x20]
    // 0xc1a1a0: cmp             x0, x1
    // 0xc1a1a4: b.lt            #0xc1a1f8
    // 0xc1a1a8: ldur            x3, [fp, #-8]
    // 0xc1a1ac: ldur            x4, [fp, #-0x18]
    // 0xc1a1b0: LoadField: r5 = r4->field_7
    //     0xc1a1b0: ldur            w5, [x4, #7]
    // 0xc1a1b4: stur            x5, [fp, #-0x28]
    // 0xc1a1b8: r0 = LoadClassIdInstr(r3)
    //     0xc1a1b8: ldur            x0, [x3, #-1]
    //     0xc1a1bc: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a1c0: mov             x1, x3
    // 0xc1a1c4: ldur            x2, [fp, #-0x10]
    // 0xc1a1c8: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1a1c8: sub             lr, x0, #0xff3
    //     0xc1a1cc: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a1d0: blr             lr
    // 0xc1a1d4: mov             x1, x0
    // 0xc1a1d8: ldur            x0, [fp, #-0x28]
    // 0xc1a1dc: r2 = LoadInt32Instr(r0)
    //     0xc1a1dc: sbfx            x2, x0, #1, #0x1f
    // 0xc1a1e0: cmp             x2, x1
    // 0xc1a1e4: b.ne            #0xc1a26c
    // 0xc1a1e8: r0 = Null
    //     0xc1a1e8: mov             x0, NULL
    // 0xc1a1ec: LeaveFrame
    //     0xc1a1ec: mov             SP, fp
    //     0xc1a1f0: ldp             fp, lr, [SP], #0x10
    // 0xc1a1f4: ret
    //     0xc1a1f4: ret             
    // 0xc1a1f8: ldur            x0, [fp, #-8]
    // 0xc1a1fc: ldur            x3, [fp, #-0x10]
    // 0xc1a200: r1 = Null
    //     0xc1a200: mov             x1, NULL
    // 0xc1a204: r2 = 10
    //     0xc1a204: movz            x2, #0xa
    // 0xc1a208: r0 = AllocateArray()
    //     0xc1a208: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1a20c: r16 = "End "
    //     0xc1a20c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10920] "End "
    //     0xc1a210: ldr             x16, [x16, #0x920]
    // 0xc1a214: StoreField: r0->field_f = r16
    //     0xc1a214: stur            w16, [x0, #0xf]
    // 0xc1a218: ldur            x3, [fp, #-0x10]
    // 0xc1a21c: StoreField: r0->field_13 = r3
    //     0xc1a21c: stur            w3, [x0, #0x13]
    // 0xc1a220: r16 = " must come after start "
    //     0xc1a220: add             x16, PP, #0x10, lsl #12  ; [pp+0x10928] " must come after start "
    //     0xc1a224: ldr             x16, [x16, #0x928]
    // 0xc1a228: ArrayStore: r0[0] = r16  ; List_4
    //     0xc1a228: stur            w16, [x0, #0x17]
    // 0xc1a22c: ldur            x4, [fp, #-8]
    // 0xc1a230: StoreField: r0->field_1b = r4
    //     0xc1a230: stur            w4, [x0, #0x1b]
    // 0xc1a234: r16 = "."
    //     0xc1a234: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc1a238: StoreField: r0->field_1f = r16
    //     0xc1a238: stur            w16, [x0, #0x1f]
    // 0xc1a23c: str             x0, [SP]
    // 0xc1a240: r0 = _interpolate()
    //     0xc1a240: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1a244: stur            x0, [fp, #-0x28]
    // 0xc1a248: r0 = ArgumentError()
    //     0xc1a248: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc1a24c: mov             x1, x0
    // 0xc1a250: ldur            x0, [fp, #-0x28]
    // 0xc1a254: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1a254: stur            w0, [x1, #0x17]
    // 0xc1a258: r0 = false
    //     0xc1a258: add             x0, NULL, #0x30  ; false
    // 0xc1a25c: StoreField: r1->field_b = r0
    //     0xc1a25c: stur            w0, [x1, #0xb]
    // 0xc1a260: mov             x0, x1
    // 0xc1a264: r0 = Throw()
    //     0xc1a264: bl              #0xec04b8  ; ThrowStub
    // 0xc1a268: brk             #0
    // 0xc1a26c: ldur            x4, [fp, #-8]
    // 0xc1a270: ldur            x3, [fp, #-0x10]
    // 0xc1a274: ldur            x5, [fp, #-0x18]
    // 0xc1a278: r0 = false
    //     0xc1a278: add             x0, NULL, #0x30  ; false
    // 0xc1a27c: r1 = Null
    //     0xc1a27c: mov             x1, NULL
    // 0xc1a280: r2 = 10
    //     0xc1a280: movz            x2, #0xa
    // 0xc1a284: r0 = AllocateArray()
    //     0xc1a284: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc1a288: mov             x3, x0
    // 0xc1a28c: stur            x3, [fp, #-0x28]
    // 0xc1a290: r16 = "Text \""
    //     0xc1a290: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c770] "Text \""
    //     0xc1a294: ldr             x16, [x16, #0x770]
    // 0xc1a298: StoreField: r3->field_f = r16
    //     0xc1a298: stur            w16, [x3, #0xf]
    // 0xc1a29c: ldur            x0, [fp, #-0x18]
    // 0xc1a2a0: StoreField: r3->field_13 = r0
    //     0xc1a2a0: stur            w0, [x3, #0x13]
    // 0xc1a2a4: r16 = "\" must be "
    //     0xc1a2a4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c778] "\" must be "
    //     0xc1a2a8: ldr             x16, [x16, #0x778]
    // 0xc1a2ac: ArrayStore: r3[0] = r16  ; List_4
    //     0xc1a2ac: stur            w16, [x3, #0x17]
    // 0xc1a2b0: ldur            x1, [fp, #-8]
    // 0xc1a2b4: r0 = LoadClassIdInstr(r1)
    //     0xc1a2b4: ldur            x0, [x1, #-1]
    //     0xc1a2b8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a2bc: ldur            x2, [fp, #-0x10]
    // 0xc1a2c0: r0 = GDT[cid_x0 + -0xff3]()
    //     0xc1a2c0: sub             lr, x0, #0xff3
    //     0xc1a2c4: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a2c8: blr             lr
    // 0xc1a2cc: mov             x2, x0
    // 0xc1a2d0: r0 = BoxInt64Instr(r2)
    //     0xc1a2d0: sbfiz           x0, x2, #1, #0x1f
    //     0xc1a2d4: cmp             x2, x0, asr #1
    //     0xc1a2d8: b.eq            #0xc1a2e4
    //     0xc1a2dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1a2e0: stur            x2, [x0, #7]
    // 0xc1a2e4: ldur            x1, [fp, #-0x28]
    // 0xc1a2e8: ArrayStore: r1[3] = r0  ; List_4
    //     0xc1a2e8: add             x25, x1, #0x1b
    //     0xc1a2ec: str             w0, [x25]
    //     0xc1a2f0: tbz             w0, #0, #0xc1a30c
    //     0xc1a2f4: ldurb           w16, [x1, #-1]
    //     0xc1a2f8: ldurb           w17, [x0, #-1]
    //     0xc1a2fc: and             x16, x17, x16, lsr #2
    //     0xc1a300: tst             x16, HEAP, lsr #32
    //     0xc1a304: b.eq            #0xc1a30c
    //     0xc1a308: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc1a30c: ldur            x0, [fp, #-0x28]
    // 0xc1a310: r16 = " characters long."
    //     0xc1a310: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c780] " characters long."
    //     0xc1a314: ldr             x16, [x16, #0x780]
    // 0xc1a318: StoreField: r0->field_1f = r16
    //     0xc1a318: stur            w16, [x0, #0x1f]
    // 0xc1a31c: str             x0, [SP]
    // 0xc1a320: r0 = _interpolate()
    //     0xc1a320: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1a324: stur            x0, [fp, #-8]
    // 0xc1a328: r0 = ArgumentError()
    //     0xc1a328: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc1a32c: mov             x1, x0
    // 0xc1a330: ldur            x0, [fp, #-8]
    // 0xc1a334: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1a334: stur            w0, [x1, #0x17]
    // 0xc1a338: r0 = false
    //     0xc1a338: add             x0, NULL, #0x30  ; false
    // 0xc1a33c: StoreField: r1->field_b = r0
    //     0xc1a33c: stur            w0, [x1, #0xb]
    // 0xc1a340: mov             x0, x1
    // 0xc1a344: r0 = Throw()
    //     0xc1a344: bl              #0xec04b8  ; ThrowStub
    // 0xc1a348: brk             #0
    // 0xc1a34c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1a34c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1a350: b               #0xc1a0c4
  }
}
