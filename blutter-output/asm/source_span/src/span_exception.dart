// lib: , url: package:source_span/src/span_exception.dart

// class id: 1051142, size: 0x8
class :: {
}

// class id: 1545, size: 0x10, field offset: 0x8
abstract class SourceSpanException extends Object
    implements Exception {

  _ toString(/* No info */) {
    // ** addr: 0xc2b904, size: 0x140
    // 0xc2b904: EnterFrame
    //     0xc2b904: stp             fp, lr, [SP, #-0x10]!
    //     0xc2b908: mov             fp, SP
    // 0xc2b90c: AllocStack(0x20)
    //     0xc2b90c: sub             SP, SP, #0x20
    // 0xc2b910: SetupParameters(SourceSpanException this /* r2, fp-0x8 */)
    //     0xc2b910: ldur            w0, [x4, #0x13]
    //     0xc2b914: sub             x1, x0, #2
    //     0xc2b918: add             x2, fp, w1, sxtw #2
    //     0xc2b91c: ldr             x2, [x2, #0x10]
    //     0xc2b920: stur            x2, [fp, #-8]
    // 0xc2b924: CheckStackOverflow
    //     0xc2b924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2b928: cmp             SP, x16
    //     0xc2b92c: b.ls            #0xc2ba38
    // 0xc2b930: r0 = LoadClassIdInstr(r2)
    //     0xc2b930: ldur            x0, [x2, #-1]
    //     0xc2b934: ubfx            x0, x0, #0xc, #0x14
    // 0xc2b938: mov             x1, x2
    // 0xc2b93c: r0 = GDT[cid_x0 + -0xfcd]()
    //     0xc2b93c: sub             lr, x0, #0xfcd
    //     0xc2b940: ldr             lr, [x21, lr, lsl #3]
    //     0xc2b944: blr             lr
    // 0xc2b948: cmp             w0, NULL
    // 0xc2b94c: b.ne            #0xc2b980
    // 0xc2b950: ldur            x0, [fp, #-8]
    // 0xc2b954: r1 = LoadClassIdInstr(r0)
    //     0xc2b954: ldur            x1, [x0, #-1]
    //     0xc2b958: ubfx            x1, x1, #0xc, #0x14
    // 0xc2b95c: mov             x16, x0
    // 0xc2b960: mov             x0, x1
    // 0xc2b964: mov             x1, x16
    // 0xc2b968: r0 = GDT[cid_x0 + -0xd2b]()
    //     0xc2b968: sub             lr, x0, #0xd2b
    //     0xc2b96c: ldr             lr, [x21, lr, lsl #3]
    //     0xc2b970: blr             lr
    // 0xc2b974: LeaveFrame
    //     0xc2b974: mov             SP, fp
    //     0xc2b978: ldp             fp, lr, [SP], #0x10
    // 0xc2b97c: ret
    //     0xc2b97c: ret             
    // 0xc2b980: ldur            x0, [fp, #-8]
    // 0xc2b984: r1 = Null
    //     0xc2b984: mov             x1, NULL
    // 0xc2b988: r2 = 4
    //     0xc2b988: movz            x2, #0x4
    // 0xc2b98c: r0 = AllocateArray()
    //     0xc2b98c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc2b990: mov             x2, x0
    // 0xc2b994: stur            x2, [fp, #-0x10]
    // 0xc2b998: r16 = "Error on "
    //     0xc2b998: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c560] "Error on "
    //     0xc2b99c: ldr             x16, [x16, #0x560]
    // 0xc2b9a0: StoreField: r2->field_f = r16
    //     0xc2b9a0: stur            w16, [x2, #0xf]
    // 0xc2b9a4: ldur            x3, [fp, #-8]
    // 0xc2b9a8: r0 = LoadClassIdInstr(r3)
    //     0xc2b9a8: ldur            x0, [x3, #-1]
    //     0xc2b9ac: ubfx            x0, x0, #0xc, #0x14
    // 0xc2b9b0: mov             x1, x3
    // 0xc2b9b4: r0 = GDT[cid_x0 + -0xfcd]()
    //     0xc2b9b4: sub             lr, x0, #0xfcd
    //     0xc2b9b8: ldr             lr, [x21, lr, lsl #3]
    //     0xc2b9bc: blr             lr
    // 0xc2b9c0: mov             x2, x0
    // 0xc2b9c4: stur            x2, [fp, #-0x18]
    // 0xc2b9c8: cmp             w2, NULL
    // 0xc2b9cc: b.eq            #0xc2ba40
    // 0xc2b9d0: ldur            x1, [fp, #-8]
    // 0xc2b9d4: r0 = LoadClassIdInstr(r1)
    //     0xc2b9d4: ldur            x0, [x1, #-1]
    //     0xc2b9d8: ubfx            x0, x0, #0xc, #0x14
    // 0xc2b9dc: r0 = GDT[cid_x0 + -0xd2b]()
    //     0xc2b9dc: sub             lr, x0, #0xd2b
    //     0xc2b9e0: ldr             lr, [x21, lr, lsl #3]
    //     0xc2b9e4: blr             lr
    // 0xc2b9e8: ldur            x1, [fp, #-0x18]
    // 0xc2b9ec: mov             x2, x0
    // 0xc2b9f0: r3 = Null
    //     0xc2b9f0: mov             x3, NULL
    // 0xc2b9f4: r0 = message()
    //     0xc2b9f4: bl              #0xc14528  ; [package:source_span/src/span_mixin.dart] SourceSpanMixin::message
    // 0xc2b9f8: ldur            x1, [fp, #-0x10]
    // 0xc2b9fc: ArrayStore: r1[1] = r0  ; List_4
    //     0xc2b9fc: add             x25, x1, #0x13
    //     0xc2ba00: str             w0, [x25]
    //     0xc2ba04: tbz             w0, #0, #0xc2ba20
    //     0xc2ba08: ldurb           w16, [x1, #-1]
    //     0xc2ba0c: ldurb           w17, [x0, #-1]
    //     0xc2ba10: and             x16, x17, x16, lsr #2
    //     0xc2ba14: tst             x16, HEAP, lsr #32
    //     0xc2ba18: b.eq            #0xc2ba20
    //     0xc2ba1c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc2ba20: ldur            x16, [fp, #-0x10]
    // 0xc2ba24: str             x16, [SP]
    // 0xc2ba28: r0 = _interpolate()
    //     0xc2ba28: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc2ba2c: LeaveFrame
    //     0xc2ba2c: mov             SP, fp
    //     0xc2ba30: ldp             fp, lr, [SP], #0x10
    // 0xc2ba34: ret
    //     0xc2ba34: ret             
    // 0xc2ba38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2ba38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2ba3c: b               #0xc2b930
    // 0xc2ba40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc2ba40: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 1546, size: 0x14, field offset: 0x10
class SourceSpanFormatException extends SourceSpanException
    implements FormatException {

  get _ offset(/* No info */) {
    // ** addr: 0xdaa030, size: 0x7c
    // 0xdaa030: EnterFrame
    //     0xdaa030: stp             fp, lr, [SP, #-0x10]!
    //     0xdaa034: mov             fp, SP
    // 0xdaa038: AllocStack(0x10)
    //     0xdaa038: sub             SP, SP, #0x10
    // 0xdaa03c: CheckStackOverflow
    //     0xdaa03c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdaa040: cmp             SP, x16
    //     0xdaa044: b.ls            #0xdaa0a4
    // 0xdaa048: LoadField: r0 = r1->field_b
    //     0xdaa048: ldur            w0, [x1, #0xb]
    // 0xdaa04c: DecompressPointer r0
    //     0xdaa04c: add             x0, x0, HEAP, lsl #32
    // 0xdaa050: LoadField: r2 = r0->field_7
    //     0xdaa050: ldur            w2, [x0, #7]
    // 0xdaa054: DecompressPointer r2
    //     0xdaa054: add             x2, x2, HEAP, lsl #32
    // 0xdaa058: stur            x2, [fp, #-0x10]
    // 0xdaa05c: LoadField: r3 = r0->field_b
    //     0xdaa05c: ldur            x3, [x0, #0xb]
    // 0xdaa060: stur            x3, [fp, #-8]
    // 0xdaa064: r0 = FileLocation()
    //     0xdaa064: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xdaa068: mov             x1, x0
    // 0xdaa06c: ldur            x2, [fp, #-0x10]
    // 0xdaa070: ldur            x3, [fp, #-8]
    // 0xdaa074: stur            x0, [fp, #-0x10]
    // 0xdaa078: r0 = FileLocation._()
    //     0xdaa078: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xdaa07c: ldur            x2, [fp, #-0x10]
    // 0xdaa080: LoadField: r3 = r2->field_b
    //     0xdaa080: ldur            x3, [x2, #0xb]
    // 0xdaa084: r0 = BoxInt64Instr(r3)
    //     0xdaa084: sbfiz           x0, x3, #1, #0x1f
    //     0xdaa088: cmp             x3, x0, asr #1
    //     0xdaa08c: b.eq            #0xdaa098
    //     0xdaa090: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xdaa094: stur            x3, [x0, #7]
    // 0xdaa098: LeaveFrame
    //     0xdaa098: mov             SP, fp
    //     0xdaa09c: ldp             fp, lr, [SP], #0x10
    // 0xdaa0a0: ret
    //     0xdaa0a0: ret             
    // 0xdaa0a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdaa0a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdaa0a8: b               #0xdaa048
  }
}
