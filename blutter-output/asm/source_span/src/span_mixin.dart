// lib: , url: package:source_span/src/span_mixin.dart

// class id: 1051143, size: 0x8
class :: {
}

// class id: 492, size: 0x8, field offset: 0x8
abstract class SourceSpanMixin extends Object
    implements SourceSpan {

  _ compareTo(/* No info */) {
    // ** addr: 0x6d6000, size: 0x1c4
    // 0x6d6000: EnterFrame
    //     0x6d6000: stp             fp, lr, [SP, #-0x10]!
    //     0x6d6004: mov             fp, SP
    // 0x6d6008: AllocStack(0x28)
    //     0x6d6008: sub             SP, SP, #0x28
    // 0x6d600c: SetupParameters(SourceSpanMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x6d600c: mov             x4, x1
    //     0x6d6010: mov             x3, x2
    //     0x6d6014: stur            x1, [fp, #-8]
    //     0x6d6018: stur            x2, [fp, #-0x10]
    // 0x6d601c: CheckStackOverflow
    //     0x6d601c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6d6020: cmp             SP, x16
    //     0x6d6024: b.ls            #0x6d61bc
    // 0x6d6028: mov             x0, x3
    // 0x6d602c: r2 = Null
    //     0x6d602c: mov             x2, NULL
    // 0x6d6030: r1 = Null
    //     0x6d6030: mov             x1, NULL
    // 0x6d6034: r4 = 60
    //     0x6d6034: movz            x4, #0x3c
    // 0x6d6038: branchIfSmi(r0, 0x6d6044)
    //     0x6d6038: tbz             w0, #0, #0x6d6044
    // 0x6d603c: r4 = LoadClassIdInstr(r0)
    //     0x6d603c: ldur            x4, [x0, #-1]
    //     0x6d6040: ubfx            x4, x4, #0xc, #0x14
    // 0x6d6044: sub             x4, x4, #0x1ed
    // 0x6d6048: cmp             x4, #2
    // 0x6d604c: b.ls            #0x6d6064
    // 0x6d6050: r8 = SourceSpan
    //     0x6d6050: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c548] Type: SourceSpan
    //     0x6d6054: ldr             x8, [x8, #0x548]
    // 0x6d6058: r3 = Null
    //     0x6d6058: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c550] Null
    //     0x6d605c: ldr             x3, [x3, #0x550]
    // 0x6d6060: r0 = DefaultTypeTest()
    //     0x6d6060: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x6d6064: ldur            x0, [fp, #-8]
    // 0x6d6068: r1 = LoadClassIdInstr(r0)
    //     0x6d6068: ldur            x1, [x0, #-1]
    //     0x6d606c: ubfx            x1, x1, #0xc, #0x14
    // 0x6d6070: stur            x1, [fp, #-0x28]
    // 0x6d6074: cmp             x1, #0x1ed
    // 0x6d6078: b.ne            #0x6d60b4
    // 0x6d607c: LoadField: r2 = r0->field_7
    //     0x6d607c: ldur            w2, [x0, #7]
    // 0x6d6080: DecompressPointer r2
    //     0x6d6080: add             x2, x2, HEAP, lsl #32
    // 0x6d6084: stur            x2, [fp, #-0x20]
    // 0x6d6088: LoadField: r3 = r0->field_b
    //     0x6d6088: ldur            x3, [x0, #0xb]
    // 0x6d608c: stur            x3, [fp, #-0x18]
    // 0x6d6090: r0 = FileLocation()
    //     0x6d6090: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0x6d6094: mov             x1, x0
    // 0x6d6098: ldur            x2, [fp, #-0x20]
    // 0x6d609c: ldur            x3, [fp, #-0x18]
    // 0x6d60a0: stur            x0, [fp, #-0x20]
    // 0x6d60a4: r0 = FileLocation._()
    //     0x6d60a4: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0x6d60a8: ldur            x4, [fp, #-0x20]
    // 0x6d60ac: ldur            x2, [fp, #-8]
    // 0x6d60b0: b               #0x6d60c4
    // 0x6d60b4: mov             x2, x0
    // 0x6d60b8: LoadField: r0 = r2->field_7
    //     0x6d60b8: ldur            w0, [x2, #7]
    // 0x6d60bc: DecompressPointer r0
    //     0x6d60bc: add             x0, x0, HEAP, lsl #32
    // 0x6d60c0: mov             x4, x0
    // 0x6d60c4: ldur            x3, [fp, #-0x10]
    // 0x6d60c8: stur            x4, [fp, #-0x20]
    // 0x6d60cc: r0 = LoadClassIdInstr(r3)
    //     0x6d60cc: ldur            x0, [x3, #-1]
    //     0x6d60d0: ubfx            x0, x0, #0xc, #0x14
    // 0x6d60d4: mov             x1, x3
    // 0x6d60d8: r0 = GDT[cid_x0 + -0xfff]()
    //     0x6d60d8: sub             lr, x0, #0xfff
    //     0x6d60dc: ldr             lr, [x21, lr, lsl #3]
    //     0x6d60e0: blr             lr
    // 0x6d60e4: ldur            x1, [fp, #-0x20]
    // 0x6d60e8: r2 = LoadClassIdInstr(r1)
    //     0x6d60e8: ldur            x2, [x1, #-1]
    //     0x6d60ec: ubfx            x2, x2, #0xc, #0x14
    // 0x6d60f0: mov             x16, x0
    // 0x6d60f4: mov             x0, x2
    // 0x6d60f8: mov             x2, x16
    // 0x6d60fc: r0 = GDT[cid_x0 + 0x138b7]()
    //     0x6d60fc: movz            x17, #0x38b7
    //     0x6d6100: movk            x17, #0x1, lsl #16
    //     0x6d6104: add             lr, x0, x17
    //     0x6d6108: ldr             lr, [x21, lr, lsl #3]
    //     0x6d610c: blr             lr
    // 0x6d6110: cbnz            x0, #0x6d61b0
    // 0x6d6114: ldur            x0, [fp, #-0x28]
    // 0x6d6118: cmp             x0, #0x1ed
    // 0x6d611c: b.ne            #0x6d6158
    // 0x6d6120: ldur            x0, [fp, #-8]
    // 0x6d6124: LoadField: r2 = r0->field_7
    //     0x6d6124: ldur            w2, [x0, #7]
    // 0x6d6128: DecompressPointer r2
    //     0x6d6128: add             x2, x2, HEAP, lsl #32
    // 0x6d612c: stur            x2, [fp, #-0x20]
    // 0x6d6130: LoadField: r3 = r0->field_13
    //     0x6d6130: ldur            x3, [x0, #0x13]
    // 0x6d6134: stur            x3, [fp, #-0x18]
    // 0x6d6138: r0 = FileLocation()
    //     0x6d6138: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0x6d613c: mov             x1, x0
    // 0x6d6140: ldur            x2, [fp, #-0x20]
    // 0x6d6144: ldur            x3, [fp, #-0x18]
    // 0x6d6148: stur            x0, [fp, #-0x20]
    // 0x6d614c: r0 = FileLocation._()
    //     0x6d614c: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0x6d6150: ldur            x2, [fp, #-0x20]
    // 0x6d6154: b               #0x6d6168
    // 0x6d6158: ldur            x0, [fp, #-8]
    // 0x6d615c: LoadField: r1 = r0->field_b
    //     0x6d615c: ldur            w1, [x0, #0xb]
    // 0x6d6160: DecompressPointer r1
    //     0x6d6160: add             x1, x1, HEAP, lsl #32
    // 0x6d6164: mov             x2, x1
    // 0x6d6168: ldur            x1, [fp, #-0x10]
    // 0x6d616c: stur            x2, [fp, #-8]
    // 0x6d6170: r0 = LoadClassIdInstr(r1)
    //     0x6d6170: ldur            x0, [x1, #-1]
    //     0x6d6174: ubfx            x0, x0, #0xc, #0x14
    // 0x6d6178: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6d6178: sub             lr, x0, #1, lsl #12
    //     0x6d617c: ldr             lr, [x21, lr, lsl #3]
    //     0x6d6180: blr             lr
    // 0x6d6184: ldur            x1, [fp, #-8]
    // 0x6d6188: r2 = LoadClassIdInstr(r1)
    //     0x6d6188: ldur            x2, [x1, #-1]
    //     0x6d618c: ubfx            x2, x2, #0xc, #0x14
    // 0x6d6190: mov             x16, x0
    // 0x6d6194: mov             x0, x2
    // 0x6d6198: mov             x2, x16
    // 0x6d619c: r0 = GDT[cid_x0 + 0x138b7]()
    //     0x6d619c: movz            x17, #0x38b7
    //     0x6d61a0: movk            x17, #0x1, lsl #16
    //     0x6d61a4: add             lr, x0, x17
    //     0x6d61a8: ldr             lr, [x21, lr, lsl #3]
    //     0x6d61ac: blr             lr
    // 0x6d61b0: LeaveFrame
    //     0x6d61b0: mov             SP, fp
    //     0x6d61b4: ldp             fp, lr, [SP], #0x10
    // 0x6d61b8: ret
    //     0x6d61b8: ret             
    // 0x6d61bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6d61bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6d61c0: b               #0x6d6028
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3600, size: 0xfc
    // 0xbf3600: EnterFrame
    //     0xbf3600: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3604: mov             fp, SP
    // 0xbf3608: AllocStack(0x20)
    //     0xbf3608: sub             SP, SP, #0x20
    // 0xbf360c: CheckStackOverflow
    //     0xbf360c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3610: cmp             SP, x16
    //     0xbf3614: b.ls            #0xbf36f4
    // 0xbf3618: ldr             x0, [fp, #0x10]
    // 0xbf361c: r1 = LoadClassIdInstr(r0)
    //     0xbf361c: ldur            x1, [x0, #-1]
    //     0xbf3620: ubfx            x1, x1, #0xc, #0x14
    // 0xbf3624: stur            x1, [fp, #-0x18]
    // 0xbf3628: cmp             x1, #0x1ed
    // 0xbf362c: b.ne            #0xbf3668
    // 0xbf3630: LoadField: r2 = r0->field_7
    //     0xbf3630: ldur            w2, [x0, #7]
    // 0xbf3634: DecompressPointer r2
    //     0xbf3634: add             x2, x2, HEAP, lsl #32
    // 0xbf3638: stur            x2, [fp, #-0x10]
    // 0xbf363c: LoadField: r3 = r0->field_b
    //     0xbf363c: ldur            x3, [x0, #0xb]
    // 0xbf3640: stur            x3, [fp, #-8]
    // 0xbf3644: r0 = FileLocation()
    //     0xbf3644: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xbf3648: mov             x1, x0
    // 0xbf364c: ldur            x2, [fp, #-0x10]
    // 0xbf3650: ldur            x3, [fp, #-8]
    // 0xbf3654: stur            x0, [fp, #-0x10]
    // 0xbf3658: r0 = FileLocation._()
    //     0xbf3658: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xbf365c: ldur            x2, [fp, #-0x10]
    // 0xbf3660: ldr             x0, [fp, #0x10]
    // 0xbf3664: b               #0xbf3674
    // 0xbf3668: LoadField: r1 = r0->field_7
    //     0xbf3668: ldur            w1, [x0, #7]
    // 0xbf366c: DecompressPointer r1
    //     0xbf366c: add             x1, x1, HEAP, lsl #32
    // 0xbf3670: mov             x2, x1
    // 0xbf3674: ldur            x1, [fp, #-0x18]
    // 0xbf3678: stur            x2, [fp, #-0x20]
    // 0xbf367c: cmp             x1, #0x1ed
    // 0xbf3680: b.ne            #0xbf36b8
    // 0xbf3684: LoadField: r1 = r0->field_7
    //     0xbf3684: ldur            w1, [x0, #7]
    // 0xbf3688: DecompressPointer r1
    //     0xbf3688: add             x1, x1, HEAP, lsl #32
    // 0xbf368c: stur            x1, [fp, #-0x10]
    // 0xbf3690: LoadField: r3 = r0->field_13
    //     0xbf3690: ldur            x3, [x0, #0x13]
    // 0xbf3694: stur            x3, [fp, #-8]
    // 0xbf3698: r0 = FileLocation()
    //     0xbf3698: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xbf369c: mov             x1, x0
    // 0xbf36a0: ldur            x2, [fp, #-0x10]
    // 0xbf36a4: ldur            x3, [fp, #-8]
    // 0xbf36a8: stur            x0, [fp, #-0x10]
    // 0xbf36ac: r0 = FileLocation._()
    //     0xbf36ac: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xbf36b0: ldur            x2, [fp, #-0x10]
    // 0xbf36b4: b               #0xbf36c4
    // 0xbf36b8: LoadField: r1 = r0->field_b
    //     0xbf36b8: ldur            w1, [x0, #0xb]
    // 0xbf36bc: DecompressPointer r1
    //     0xbf36bc: add             x1, x1, HEAP, lsl #32
    // 0xbf36c0: mov             x2, x1
    // 0xbf36c4: ldur            x1, [fp, #-0x20]
    // 0xbf36c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf36c8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf36cc: r0 = hash()
    //     0xbf36cc: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf36d0: mov             x2, x0
    // 0xbf36d4: r0 = BoxInt64Instr(r2)
    //     0xbf36d4: sbfiz           x0, x2, #1, #0x1f
    //     0xbf36d8: cmp             x2, x0, asr #1
    //     0xbf36dc: b.eq            #0xbf36e8
    //     0xbf36e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf36e4: stur            x2, [x0, #7]
    // 0xbf36e8: LeaveFrame
    //     0xbf36e8: mov             SP, fp
    //     0xbf36ec: ldp             fp, lr, [SP], #0x10
    // 0xbf36f0: ret
    //     0xbf36f0: ret             
    // 0xbf36f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf36f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf36f8: b               #0xbf3618
  }
  _ message(/* No info */) {
    // ** addr: 0xc14528, size: 0x1cc
    // 0xc14528: EnterFrame
    //     0xc14528: stp             fp, lr, [SP, #-0x10]!
    //     0xc1452c: mov             fp, SP
    // 0xc14530: AllocStack(0x40)
    //     0xc14530: sub             SP, SP, #0x40
    // 0xc14534: SetupParameters(SourceSpanMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc14534: stur            x1, [fp, #-8]
    //     0xc14538: stur            x2, [fp, #-0x10]
    // 0xc1453c: CheckStackOverflow
    //     0xc1453c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc14540: cmp             SP, x16
    //     0xc14544: b.ls            #0xc146ec
    // 0xc14548: r0 = StringBuffer()
    //     0xc14548: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc1454c: mov             x1, x0
    // 0xc14550: stur            x0, [fp, #-0x18]
    // 0xc14554: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc14554: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc14558: r0 = StringBuffer()
    //     0xc14558: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc1455c: r1 = Null
    //     0xc1455c: mov             x1, NULL
    // 0xc14560: r2 = 8
    //     0xc14560: movz            x2, #0x8
    // 0xc14564: r0 = AllocateArray()
    //     0xc14564: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc14568: stur            x0, [fp, #-0x30]
    // 0xc1456c: r16 = "line "
    //     0xc1456c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c568] "line "
    //     0xc14570: ldr             x16, [x16, #0x568]
    // 0xc14574: StoreField: r0->field_f = r16
    //     0xc14574: stur            w16, [x0, #0xf]
    // 0xc14578: ldur            x1, [fp, #-8]
    // 0xc1457c: LoadField: r2 = r1->field_7
    //     0xc1457c: ldur            w2, [x1, #7]
    // 0xc14580: DecompressPointer r2
    //     0xc14580: add             x2, x2, HEAP, lsl #32
    // 0xc14584: stur            x2, [fp, #-0x28]
    // 0xc14588: LoadField: r3 = r1->field_b
    //     0xc14588: ldur            x3, [x1, #0xb]
    // 0xc1458c: stur            x3, [fp, #-0x20]
    // 0xc14590: r0 = FileLocation()
    //     0xc14590: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xc14594: mov             x1, x0
    // 0xc14598: ldur            x2, [fp, #-0x28]
    // 0xc1459c: ldur            x3, [fp, #-0x20]
    // 0xc145a0: stur            x0, [fp, #-0x38]
    // 0xc145a4: r0 = FileLocation._()
    //     0xc145a4: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xc145a8: ldur            x1, [fp, #-0x38]
    // 0xc145ac: r0 = line()
    //     0xc145ac: bl              #0xeb7984  ; [package:source_span/src/file.dart] FileLocation::line
    // 0xc145b0: add             x2, x0, #1
    // 0xc145b4: r0 = BoxInt64Instr(r2)
    //     0xc145b4: sbfiz           x0, x2, #1, #0x1f
    //     0xc145b8: cmp             x2, x0, asr #1
    //     0xc145bc: b.eq            #0xc145c8
    //     0xc145c0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc145c4: stur            x2, [x0, #7]
    // 0xc145c8: ldur            x1, [fp, #-0x30]
    // 0xc145cc: ArrayStore: r1[1] = r0  ; List_4
    //     0xc145cc: add             x25, x1, #0x13
    //     0xc145d0: str             w0, [x25]
    //     0xc145d4: tbz             w0, #0, #0xc145f0
    //     0xc145d8: ldurb           w16, [x1, #-1]
    //     0xc145dc: ldurb           w17, [x0, #-1]
    //     0xc145e0: and             x16, x17, x16, lsr #2
    //     0xc145e4: tst             x16, HEAP, lsr #32
    //     0xc145e8: b.eq            #0xc145f0
    //     0xc145ec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc145f0: ldur            x1, [fp, #-0x30]
    // 0xc145f4: r16 = ", column "
    //     0xc145f4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c570] ", column "
    //     0xc145f8: ldr             x16, [x16, #0x570]
    // 0xc145fc: ArrayStore: r1[0] = r16  ; List_4
    //     0xc145fc: stur            w16, [x1, #0x17]
    // 0xc14600: r0 = FileLocation()
    //     0xc14600: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xc14604: mov             x1, x0
    // 0xc14608: ldur            x2, [fp, #-0x28]
    // 0xc1460c: ldur            x3, [fp, #-0x20]
    // 0xc14610: stur            x0, [fp, #-0x28]
    // 0xc14614: r0 = FileLocation._()
    //     0xc14614: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xc14618: ldur            x1, [fp, #-0x28]
    // 0xc1461c: r0 = column()
    //     0xc1461c: bl              #0xeb7944  ; [package:source_span/src/file.dart] FileLocation::column
    // 0xc14620: add             x2, x0, #1
    // 0xc14624: r0 = BoxInt64Instr(r2)
    //     0xc14624: sbfiz           x0, x2, #1, #0x1f
    //     0xc14628: cmp             x2, x0, asr #1
    //     0xc1462c: b.eq            #0xc14638
    //     0xc14630: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc14634: stur            x2, [x0, #7]
    // 0xc14638: ldur            x1, [fp, #-0x30]
    // 0xc1463c: ArrayStore: r1[3] = r0  ; List_4
    //     0xc1463c: add             x25, x1, #0x1b
    //     0xc14640: str             w0, [x25]
    //     0xc14644: tbz             w0, #0, #0xc14660
    //     0xc14648: ldurb           w16, [x1, #-1]
    //     0xc1464c: ldurb           w17, [x0, #-1]
    //     0xc14650: and             x16, x17, x16, lsr #2
    //     0xc14654: tst             x16, HEAP, lsr #32
    //     0xc14658: b.eq            #0xc14660
    //     0xc1465c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc14660: ldur            x16, [fp, #-0x30]
    // 0xc14664: str             x16, [SP]
    // 0xc14668: r0 = _interpolate()
    //     0xc14668: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1466c: ldur            x1, [fp, #-0x18]
    // 0xc14670: mov             x2, x0
    // 0xc14674: r0 = write()
    //     0xc14674: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc14678: r1 = Null
    //     0xc14678: mov             x1, NULL
    // 0xc1467c: r2 = 4
    //     0xc1467c: movz            x2, #0x4
    // 0xc14680: r0 = AllocateArray()
    //     0xc14680: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc14684: r16 = ": "
    //     0xc14684: ldr             x16, [PP, #0x7d8]  ; [pp+0x7d8] ": "
    // 0xc14688: StoreField: r0->field_f = r16
    //     0xc14688: stur            w16, [x0, #0xf]
    // 0xc1468c: ldur            x1, [fp, #-0x10]
    // 0xc14690: StoreField: r0->field_13 = r1
    //     0xc14690: stur            w1, [x0, #0x13]
    // 0xc14694: str             x0, [SP]
    // 0xc14698: r0 = _interpolate()
    //     0xc14698: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1469c: ldur            x1, [fp, #-0x18]
    // 0xc146a0: mov             x2, x0
    // 0xc146a4: r0 = write()
    //     0xc146a4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc146a8: ldur            x1, [fp, #-8]
    // 0xc146ac: r0 = highlight()
    //     0xc146ac: bl              #0xc146f4  ; [package:source_span/src/span_mixin.dart] SourceSpanMixin::highlight
    // 0xc146b0: stur            x0, [fp, #-8]
    // 0xc146b4: LoadField: r1 = r0->field_7
    //     0xc146b4: ldur            w1, [x0, #7]
    // 0xc146b8: cbz             w1, #0xc146d4
    // 0xc146bc: ldur            x1, [fp, #-0x18]
    // 0xc146c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc146c0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc146c4: r0 = writeln()
    //     0xc146c4: bl              #0x702a88  ; [dart:core] StringBuffer::writeln
    // 0xc146c8: ldur            x1, [fp, #-0x18]
    // 0xc146cc: ldur            x2, [fp, #-8]
    // 0xc146d0: r0 = write()
    //     0xc146d0: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc146d4: ldur            x16, [fp, #-0x18]
    // 0xc146d8: str             x16, [SP]
    // 0xc146dc: r0 = toString()
    //     0xc146dc: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc146e0: LeaveFrame
    //     0xc146e0: mov             SP, fp
    //     0xc146e4: ldp             fp, lr, [SP], #0x10
    // 0xc146e8: ret
    //     0xc146e8: ret             
    // 0xc146ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc146ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc146f0: b               #0xc14548
  }
  _ highlight(/* No info */) {
    // ** addr: 0xc146f4, size: 0x50
    // 0xc146f4: EnterFrame
    //     0xc146f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc146f8: mov             fp, SP
    // 0xc146fc: AllocStack(0x8)
    //     0xc146fc: sub             SP, SP, #8
    // 0xc14700: SetupParameters(SourceSpanMixin this /* r1 => r2, fp-0x8 */)
    //     0xc14700: mov             x2, x1
    //     0xc14704: stur            x1, [fp, #-8]
    // 0xc14708: CheckStackOverflow
    //     0xc14708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1470c: cmp             SP, x16
    //     0xc14710: b.ls            #0xc1473c
    // 0xc14714: r0 = Highlighter()
    //     0xc14714: bl              #0xc1b7a8  ; AllocateHighlighterStub -> Highlighter (size=0x28)
    // 0xc14718: mov             x1, x0
    // 0xc1471c: ldur            x2, [fp, #-8]
    // 0xc14720: stur            x0, [fp, #-8]
    // 0xc14724: r0 = Highlighter()
    //     0xc14724: bl              #0xc18698  ; [package:source_span/src/highlighter.dart] Highlighter::Highlighter
    // 0xc14728: ldur            x1, [fp, #-8]
    // 0xc1472c: r0 = highlight()
    //     0xc1472c: bl              #0xc14744  ; [package:source_span/src/highlighter.dart] Highlighter::highlight
    // 0xc14730: LeaveFrame
    //     0xc14730: mov             SP, fp
    //     0xc14734: ldp             fp, lr, [SP], #0x10
    // 0xc14738: ret
    //     0xc14738: ret             
    // 0xc1473c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1473c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc14740: b               #0xc14714
  }
  _ toString(/* No info */) {
    // ** addr: 0xc3fc64, size: 0x210
    // 0xc3fc64: EnterFrame
    //     0xc3fc64: stp             fp, lr, [SP, #-0x10]!
    //     0xc3fc68: mov             fp, SP
    // 0xc3fc6c: AllocStack(0x28)
    //     0xc3fc6c: sub             SP, SP, #0x28
    // 0xc3fc70: CheckStackOverflow
    //     0xc3fc70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc3fc74: cmp             SP, x16
    //     0xc3fc78: b.ls            #0xc3fe6c
    // 0xc3fc7c: r1 = Null
    //     0xc3fc7c: mov             x1, NULL
    // 0xc3fc80: r2 = 18
    //     0xc3fc80: movz            x2, #0x12
    // 0xc3fc84: r0 = AllocateArray()
    //     0xc3fc84: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc3fc88: stur            x0, [fp, #-8]
    // 0xc3fc8c: r16 = "<"
    //     0xc3fc8c: ldr             x16, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xc3fc90: StoreField: r0->field_f = r16
    //     0xc3fc90: stur            w16, [x0, #0xf]
    // 0xc3fc94: ldr             x16, [fp, #0x10]
    // 0xc3fc98: str             x16, [SP]
    // 0xc3fc9c: r0 = runtimeType()
    //     0xc3fc9c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xc3fca0: ldur            x1, [fp, #-8]
    // 0xc3fca4: ArrayStore: r1[1] = r0  ; List_4
    //     0xc3fca4: add             x25, x1, #0x13
    //     0xc3fca8: str             w0, [x25]
    //     0xc3fcac: tbz             w0, #0, #0xc3fcc8
    //     0xc3fcb0: ldurb           w16, [x1, #-1]
    //     0xc3fcb4: ldurb           w17, [x0, #-1]
    //     0xc3fcb8: and             x16, x17, x16, lsr #2
    //     0xc3fcbc: tst             x16, HEAP, lsr #32
    //     0xc3fcc0: b.eq            #0xc3fcc8
    //     0xc3fcc4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3fcc8: ldur            x1, [fp, #-8]
    // 0xc3fccc: r16 = ": from "
    //     0xc3fccc: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c520] ": from "
    //     0xc3fcd0: ldr             x16, [x16, #0x520]
    // 0xc3fcd4: ArrayStore: r1[0] = r16  ; List_4
    //     0xc3fcd4: stur            w16, [x1, #0x17]
    // 0xc3fcd8: ldr             x0, [fp, #0x10]
    // 0xc3fcdc: r2 = LoadClassIdInstr(r0)
    //     0xc3fcdc: ldur            x2, [x0, #-1]
    //     0xc3fce0: ubfx            x2, x2, #0xc, #0x14
    // 0xc3fce4: stur            x2, [fp, #-0x20]
    // 0xc3fce8: cmp             x2, #0x1ed
    // 0xc3fcec: b.ne            #0xc3fd28
    // 0xc3fcf0: LoadField: r3 = r0->field_7
    //     0xc3fcf0: ldur            w3, [x0, #7]
    // 0xc3fcf4: DecompressPointer r3
    //     0xc3fcf4: add             x3, x3, HEAP, lsl #32
    // 0xc3fcf8: stur            x3, [fp, #-0x18]
    // 0xc3fcfc: LoadField: r4 = r0->field_b
    //     0xc3fcfc: ldur            x4, [x0, #0xb]
    // 0xc3fd00: stur            x4, [fp, #-0x10]
    // 0xc3fd04: r0 = FileLocation()
    //     0xc3fd04: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xc3fd08: mov             x1, x0
    // 0xc3fd0c: ldur            x2, [fp, #-0x18]
    // 0xc3fd10: ldur            x3, [fp, #-0x10]
    // 0xc3fd14: stur            x0, [fp, #-0x18]
    // 0xc3fd18: r0 = FileLocation._()
    //     0xc3fd18: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xc3fd1c: ldur            x0, [fp, #-0x18]
    // 0xc3fd20: ldr             x2, [fp, #0x10]
    // 0xc3fd24: b               #0xc3fd34
    // 0xc3fd28: mov             x2, x0
    // 0xc3fd2c: LoadField: r0 = r2->field_7
    //     0xc3fd2c: ldur            w0, [x2, #7]
    // 0xc3fd30: DecompressPointer r0
    //     0xc3fd30: add             x0, x0, HEAP, lsl #32
    // 0xc3fd34: ldur            x3, [fp, #-8]
    // 0xc3fd38: ldur            x4, [fp, #-0x20]
    // 0xc3fd3c: mov             x1, x3
    // 0xc3fd40: ArrayStore: r1[3] = r0  ; List_4
    //     0xc3fd40: add             x25, x1, #0x1b
    //     0xc3fd44: str             w0, [x25]
    //     0xc3fd48: tbz             w0, #0, #0xc3fd64
    //     0xc3fd4c: ldurb           w16, [x1, #-1]
    //     0xc3fd50: ldurb           w17, [x0, #-1]
    //     0xc3fd54: and             x16, x17, x16, lsr #2
    //     0xc3fd58: tst             x16, HEAP, lsr #32
    //     0xc3fd5c: b.eq            #0xc3fd64
    //     0xc3fd60: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3fd64: r16 = " to "
    //     0xc3fd64: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c528] " to "
    //     0xc3fd68: ldr             x16, [x16, #0x528]
    // 0xc3fd6c: StoreField: r3->field_1f = r16
    //     0xc3fd6c: stur            w16, [x3, #0x1f]
    // 0xc3fd70: cmp             x4, #0x1ed
    // 0xc3fd74: b.ne            #0xc3fdb0
    // 0xc3fd78: LoadField: r0 = r2->field_7
    //     0xc3fd78: ldur            w0, [x2, #7]
    // 0xc3fd7c: DecompressPointer r0
    //     0xc3fd7c: add             x0, x0, HEAP, lsl #32
    // 0xc3fd80: stur            x0, [fp, #-0x18]
    // 0xc3fd84: LoadField: r1 = r2->field_13
    //     0xc3fd84: ldur            x1, [x2, #0x13]
    // 0xc3fd88: stur            x1, [fp, #-0x10]
    // 0xc3fd8c: r0 = FileLocation()
    //     0xc3fd8c: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xc3fd90: mov             x1, x0
    // 0xc3fd94: ldur            x2, [fp, #-0x18]
    // 0xc3fd98: ldur            x3, [fp, #-0x10]
    // 0xc3fd9c: stur            x0, [fp, #-0x18]
    // 0xc3fda0: r0 = FileLocation._()
    //     0xc3fda0: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xc3fda4: ldur            x0, [fp, #-0x18]
    // 0xc3fda8: ldr             x2, [fp, #0x10]
    // 0xc3fdac: b               #0xc3fdb8
    // 0xc3fdb0: LoadField: r0 = r2->field_b
    //     0xc3fdb0: ldur            w0, [x2, #0xb]
    // 0xc3fdb4: DecompressPointer r0
    //     0xc3fdb4: add             x0, x0, HEAP, lsl #32
    // 0xc3fdb8: ldur            x4, [fp, #-8]
    // 0xc3fdbc: ldur            x3, [fp, #-0x20]
    // 0xc3fdc0: mov             x1, x4
    // 0xc3fdc4: ArrayStore: r1[5] = r0  ; List_4
    //     0xc3fdc4: add             x25, x1, #0x23
    //     0xc3fdc8: str             w0, [x25]
    //     0xc3fdcc: tbz             w0, #0, #0xc3fde8
    //     0xc3fdd0: ldurb           w16, [x1, #-1]
    //     0xc3fdd4: ldurb           w17, [x0, #-1]
    //     0xc3fdd8: and             x16, x17, x16, lsr #2
    //     0xc3fddc: tst             x16, HEAP, lsr #32
    //     0xc3fde0: b.eq            #0xc3fde8
    //     0xc3fde4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3fde8: r16 = " \""
    //     0xc3fde8: add             x16, PP, #0x10, lsl #12  ; [pp+0x109f0] " \""
    //     0xc3fdec: ldr             x16, [x16, #0x9f0]
    // 0xc3fdf0: StoreField: r4->field_27 = r16
    //     0xc3fdf0: stur            w16, [x4, #0x27]
    // 0xc3fdf4: cmp             x3, #0x1ed
    // 0xc3fdf8: b.ne            #0xc3fe18
    // 0xc3fdfc: LoadField: r1 = r2->field_7
    //     0xc3fdfc: ldur            w1, [x2, #7]
    // 0xc3fe00: DecompressPointer r1
    //     0xc3fe00: add             x1, x1, HEAP, lsl #32
    // 0xc3fe04: LoadField: r0 = r2->field_b
    //     0xc3fe04: ldur            x0, [x2, #0xb]
    // 0xc3fe08: LoadField: r3 = r2->field_13
    //     0xc3fe08: ldur            x3, [x2, #0x13]
    // 0xc3fe0c: mov             x2, x0
    // 0xc3fe10: r0 = getText()
    //     0xc3fe10: bl              #0x954b40  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xc3fe14: b               #0xc3fe20
    // 0xc3fe18: LoadField: r0 = r2->field_f
    //     0xc3fe18: ldur            w0, [x2, #0xf]
    // 0xc3fe1c: DecompressPointer r0
    //     0xc3fe1c: add             x0, x0, HEAP, lsl #32
    // 0xc3fe20: ldur            x2, [fp, #-8]
    // 0xc3fe24: mov             x1, x2
    // 0xc3fe28: ArrayStore: r1[7] = r0  ; List_4
    //     0xc3fe28: add             x25, x1, #0x2b
    //     0xc3fe2c: str             w0, [x25]
    //     0xc3fe30: tbz             w0, #0, #0xc3fe4c
    //     0xc3fe34: ldurb           w16, [x1, #-1]
    //     0xc3fe38: ldurb           w17, [x0, #-1]
    //     0xc3fe3c: and             x16, x17, x16, lsr #2
    //     0xc3fe40: tst             x16, HEAP, lsr #32
    //     0xc3fe44: b.eq            #0xc3fe4c
    //     0xc3fe48: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc3fe4c: r16 = "\">"
    //     0xc3fe4c: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c530] "\">"
    //     0xc3fe50: ldr             x16, [x16, #0x530]
    // 0xc3fe54: StoreField: r2->field_2f = r16
    //     0xc3fe54: stur            w16, [x2, #0x2f]
    // 0xc3fe58: str             x2, [SP]
    // 0xc3fe5c: r0 = _interpolate()
    //     0xc3fe5c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc3fe60: LeaveFrame
    //     0xc3fe60: mov             SP, fp
    //     0xc3fe64: ldp             fp, lr, [SP], #0x10
    // 0xc3fe68: ret
    //     0xc3fe68: ret             
    // 0xc3fe6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc3fe6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc3fe70: b               #0xc3fc7c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7db60, size: 0x1a8
    // 0xd7db60: EnterFrame
    //     0xd7db60: stp             fp, lr, [SP, #-0x10]!
    //     0xd7db64: mov             fp, SP
    // 0xd7db68: AllocStack(0x28)
    //     0xd7db68: sub             SP, SP, #0x28
    // 0xd7db6c: CheckStackOverflow
    //     0xd7db6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7db70: cmp             SP, x16
    //     0xd7db74: b.ls            #0xd7dd00
    // 0xd7db78: ldr             x1, [fp, #0x10]
    // 0xd7db7c: cmp             w1, NULL
    // 0xd7db80: b.ne            #0xd7db94
    // 0xd7db84: r0 = false
    //     0xd7db84: add             x0, NULL, #0x30  ; false
    // 0xd7db88: LeaveFrame
    //     0xd7db88: mov             SP, fp
    //     0xd7db8c: ldp             fp, lr, [SP], #0x10
    // 0xd7db90: ret
    //     0xd7db90: ret             
    // 0xd7db94: r0 = 60
    //     0xd7db94: movz            x0, #0x3c
    // 0xd7db98: branchIfSmi(r1, 0xd7dba4)
    //     0xd7db98: tbz             w1, #0, #0xd7dba4
    // 0xd7db9c: r0 = LoadClassIdInstr(r1)
    //     0xd7db9c: ldur            x0, [x1, #-1]
    //     0xd7dba0: ubfx            x0, x0, #0xc, #0x14
    // 0xd7dba4: sub             x16, x0, #0x1ed
    // 0xd7dba8: cmp             x16, #2
    // 0xd7dbac: b.hi            #0xd7dcf0
    // 0xd7dbb0: ldr             x0, [fp, #0x18]
    // 0xd7dbb4: r2 = LoadClassIdInstr(r0)
    //     0xd7dbb4: ldur            x2, [x0, #-1]
    //     0xd7dbb8: ubfx            x2, x2, #0xc, #0x14
    // 0xd7dbbc: stur            x2, [fp, #-0x18]
    // 0xd7dbc0: cmp             x2, #0x1ed
    // 0xd7dbc4: b.ne            #0xd7dc00
    // 0xd7dbc8: LoadField: r3 = r0->field_7
    //     0xd7dbc8: ldur            w3, [x0, #7]
    // 0xd7dbcc: DecompressPointer r3
    //     0xd7dbcc: add             x3, x3, HEAP, lsl #32
    // 0xd7dbd0: stur            x3, [fp, #-0x10]
    // 0xd7dbd4: LoadField: r4 = r0->field_b
    //     0xd7dbd4: ldur            x4, [x0, #0xb]
    // 0xd7dbd8: stur            x4, [fp, #-8]
    // 0xd7dbdc: r0 = FileLocation()
    //     0xd7dbdc: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xd7dbe0: mov             x1, x0
    // 0xd7dbe4: ldur            x2, [fp, #-0x10]
    // 0xd7dbe8: ldur            x3, [fp, #-8]
    // 0xd7dbec: stur            x0, [fp, #-0x10]
    // 0xd7dbf0: r0 = FileLocation._()
    //     0xd7dbf0: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xd7dbf4: ldur            x4, [fp, #-0x10]
    // 0xd7dbf8: ldr             x2, [fp, #0x18]
    // 0xd7dbfc: b               #0xd7dc10
    // 0xd7dc00: mov             x2, x0
    // 0xd7dc04: LoadField: r0 = r2->field_7
    //     0xd7dc04: ldur            w0, [x2, #7]
    // 0xd7dc08: DecompressPointer r0
    //     0xd7dc08: add             x0, x0, HEAP, lsl #32
    // 0xd7dc0c: mov             x4, x0
    // 0xd7dc10: ldr             x3, [fp, #0x10]
    // 0xd7dc14: stur            x4, [fp, #-0x10]
    // 0xd7dc18: r0 = LoadClassIdInstr(r3)
    //     0xd7dc18: ldur            x0, [x3, #-1]
    //     0xd7dc1c: ubfx            x0, x0, #0xc, #0x14
    // 0xd7dc20: mov             x1, x3
    // 0xd7dc24: r0 = GDT[cid_x0 + -0xfff]()
    //     0xd7dc24: sub             lr, x0, #0xfff
    //     0xd7dc28: ldr             lr, [x21, lr, lsl #3]
    //     0xd7dc2c: blr             lr
    // 0xd7dc30: mov             x1, x0
    // 0xd7dc34: ldur            x0, [fp, #-0x10]
    // 0xd7dc38: r2 = LoadClassIdInstr(r0)
    //     0xd7dc38: ldur            x2, [x0, #-1]
    //     0xd7dc3c: ubfx            x2, x2, #0xc, #0x14
    // 0xd7dc40: stp             x1, x0, [SP]
    // 0xd7dc44: mov             x0, x2
    // 0xd7dc48: mov             lr, x0
    // 0xd7dc4c: ldr             lr, [x21, lr, lsl #3]
    // 0xd7dc50: blr             lr
    // 0xd7dc54: tbnz            w0, #4, #0xd7dcf0
    // 0xd7dc58: ldur            x0, [fp, #-0x18]
    // 0xd7dc5c: cmp             x0, #0x1ed
    // 0xd7dc60: b.ne            #0xd7dc9c
    // 0xd7dc64: ldr             x0, [fp, #0x18]
    // 0xd7dc68: LoadField: r2 = r0->field_7
    //     0xd7dc68: ldur            w2, [x0, #7]
    // 0xd7dc6c: DecompressPointer r2
    //     0xd7dc6c: add             x2, x2, HEAP, lsl #32
    // 0xd7dc70: stur            x2, [fp, #-0x10]
    // 0xd7dc74: LoadField: r3 = r0->field_13
    //     0xd7dc74: ldur            x3, [x0, #0x13]
    // 0xd7dc78: stur            x3, [fp, #-8]
    // 0xd7dc7c: r0 = FileLocation()
    //     0xd7dc7c: bl              #0x6d6340  ; AllocateFileLocationStub -> FileLocation (size=0x14)
    // 0xd7dc80: mov             x1, x0
    // 0xd7dc84: ldur            x2, [fp, #-0x10]
    // 0xd7dc88: ldur            x3, [fp, #-8]
    // 0xd7dc8c: stur            x0, [fp, #-0x10]
    // 0xd7dc90: r0 = FileLocation._()
    //     0xd7dc90: bl              #0x6d61c4  ; [package:source_span/src/file.dart] FileLocation::FileLocation._
    // 0xd7dc94: ldur            x2, [fp, #-0x10]
    // 0xd7dc98: b               #0xd7dcac
    // 0xd7dc9c: ldr             x0, [fp, #0x18]
    // 0xd7dca0: LoadField: r1 = r0->field_b
    //     0xd7dca0: ldur            w1, [x0, #0xb]
    // 0xd7dca4: DecompressPointer r1
    //     0xd7dca4: add             x1, x1, HEAP, lsl #32
    // 0xd7dca8: mov             x2, x1
    // 0xd7dcac: ldr             x1, [fp, #0x10]
    // 0xd7dcb0: stur            x2, [fp, #-0x10]
    // 0xd7dcb4: r0 = LoadClassIdInstr(r1)
    //     0xd7dcb4: ldur            x0, [x1, #-1]
    //     0xd7dcb8: ubfx            x0, x0, #0xc, #0x14
    // 0xd7dcbc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xd7dcbc: sub             lr, x0, #1, lsl #12
    //     0xd7dcc0: ldr             lr, [x21, lr, lsl #3]
    //     0xd7dcc4: blr             lr
    // 0xd7dcc8: mov             x1, x0
    // 0xd7dccc: ldur            x0, [fp, #-0x10]
    // 0xd7dcd0: r2 = LoadClassIdInstr(r0)
    //     0xd7dcd0: ldur            x2, [x0, #-1]
    //     0xd7dcd4: ubfx            x2, x2, #0xc, #0x14
    // 0xd7dcd8: stp             x1, x0, [SP]
    // 0xd7dcdc: mov             x0, x2
    // 0xd7dce0: mov             lr, x0
    // 0xd7dce4: ldr             lr, [x21, lr, lsl #3]
    // 0xd7dce8: blr             lr
    // 0xd7dcec: b               #0xd7dcf4
    // 0xd7dcf0: r0 = false
    //     0xd7dcf0: add             x0, NULL, #0x30  ; false
    // 0xd7dcf4: LeaveFrame
    //     0xd7dcf4: mov             SP, fp
    //     0xd7dcf8: ldp             fp, lr, [SP], #0x10
    // 0xd7dcfc: ret
    //     0xd7dcfc: ret             
    // 0xd7dd00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7dd00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7dd04: b               #0xd7db78
  }
  get _ length(/* No info */) {
    // ** addr: 0xeb7160, size: 0x84
    // 0xeb7160: EnterFrame
    //     0xeb7160: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7164: mov             fp, SP
    // 0xeb7168: AllocStack(0x10)
    //     0xeb7168: sub             SP, SP, #0x10
    // 0xeb716c: SetupParameters(SourceSpanMixin this /* r1 => r2, fp-0x8 */)
    //     0xeb716c: mov             x2, x1
    //     0xeb7170: stur            x1, [fp, #-8]
    // 0xeb7174: CheckStackOverflow
    //     0xeb7174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7178: cmp             SP, x16
    //     0xeb717c: b.ls            #0xeb71dc
    // 0xeb7180: LoadField: r1 = r2->field_b
    //     0xeb7180: ldur            w1, [x2, #0xb]
    // 0xeb7184: DecompressPointer r1
    //     0xeb7184: add             x1, x1, HEAP, lsl #32
    // 0xeb7188: r0 = LoadClassIdInstr(r1)
    //     0xeb7188: ldur            x0, [x1, #-1]
    //     0xeb718c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb7190: r0 = GDT[cid_x0 + -0xffc]()
    //     0xeb7190: sub             lr, x0, #0xffc
    //     0xeb7194: ldr             lr, [x21, lr, lsl #3]
    //     0xeb7198: blr             lr
    // 0xeb719c: mov             x2, x0
    // 0xeb71a0: ldur            x0, [fp, #-8]
    // 0xeb71a4: stur            x2, [fp, #-0x10]
    // 0xeb71a8: LoadField: r1 = r0->field_7
    //     0xeb71a8: ldur            w1, [x0, #7]
    // 0xeb71ac: DecompressPointer r1
    //     0xeb71ac: add             x1, x1, HEAP, lsl #32
    // 0xeb71b0: r0 = LoadClassIdInstr(r1)
    //     0xeb71b0: ldur            x0, [x1, #-1]
    //     0xeb71b4: ubfx            x0, x0, #0xc, #0x14
    // 0xeb71b8: r0 = GDT[cid_x0 + -0xffc]()
    //     0xeb71b8: sub             lr, x0, #0xffc
    //     0xeb71bc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb71c0: blr             lr
    // 0xeb71c4: ldur            x1, [fp, #-0x10]
    // 0xeb71c8: sub             x2, x1, x0
    // 0xeb71cc: mov             x0, x2
    // 0xeb71d0: LeaveFrame
    //     0xeb71d0: mov             SP, fp
    //     0xeb71d4: ldp             fp, lr, [SP], #0x10
    // 0xeb71d8: ret
    //     0xeb71d8: ret             
    // 0xeb71dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb71dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb71e0: b               #0xeb7180
  }
  get _ sourceUrl(/* No info */) {
    // ** addr: 0xeb742c, size: 0x54
    // 0xeb742c: EnterFrame
    //     0xeb742c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7430: mov             fp, SP
    // 0xeb7434: CheckStackOverflow
    //     0xeb7434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7438: cmp             SP, x16
    //     0xeb743c: b.ls            #0xeb7478
    // 0xeb7440: LoadField: r0 = r1->field_7
    //     0xeb7440: ldur            w0, [x1, #7]
    // 0xeb7444: DecompressPointer r0
    //     0xeb7444: add             x0, x0, HEAP, lsl #32
    // 0xeb7448: r1 = LoadClassIdInstr(r0)
    //     0xeb7448: ldur            x1, [x0, #-1]
    //     0xeb744c: ubfx            x1, x1, #0xc, #0x14
    // 0xeb7450: mov             x16, x0
    // 0xeb7454: mov             x0, x1
    // 0xeb7458: mov             x1, x16
    // 0xeb745c: r0 = GDT[cid_x0 + -0xffb]()
    //     0xeb745c: sub             lr, x0, #0xffb
    //     0xeb7460: ldr             lr, [x21, lr, lsl #3]
    //     0xeb7464: blr             lr
    // 0xeb7468: r0 = Null
    //     0xeb7468: mov             x0, NULL
    // 0xeb746c: LeaveFrame
    //     0xeb746c: mov             SP, fp
    //     0xeb7470: ldp             fp, lr, [SP], #0x10
    // 0xeb7474: ret
    //     0xeb7474: ret             
    // 0xeb7478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7478: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb747c: b               #0xeb7440
  }
}
