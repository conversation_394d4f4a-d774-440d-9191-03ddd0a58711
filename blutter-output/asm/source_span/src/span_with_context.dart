// lib: , url: package:source_span/src/span_with_context.dart

// class id: 1051144, size: 0x8
class :: {
}

// class id: 495, size: 0x18, field offset: 0x14
class SourceSpanWithContext extends SourceSpanBase {

  _ SourceSpanWithContext(/* No info */) {
    // ** addr: 0xc19e44, size: 0x254
    // 0xc19e44: EnterFrame
    //     0xc19e44: stp             fp, lr, [SP, #-0x10]!
    //     0xc19e48: mov             fp, SP
    // 0xc19e4c: AllocStack(0x28)
    //     0xc19e4c: sub             SP, SP, #0x28
    // 0xc19e50: SetupParameters(dynamic _ /* r2 => r7, fp-0x8 */, dynamic _ /* r5 => r6, fp-0x10 */, dynamic _ /* r6 => r4, fp-0x18 */)
    //     0xc19e50: mov             x7, x2
    //     0xc19e54: mov             x4, x6
    //     0xc19e58: stur            x6, [fp, #-0x18]
    //     0xc19e5c: mov             x6, x5
    //     0xc19e60: stur            x2, [fp, #-8]
    //     0xc19e64: stur            x5, [fp, #-0x10]
    // 0xc19e68: CheckStackOverflow
    //     0xc19e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc19e6c: cmp             SP, x16
    //     0xc19e70: b.ls            #0xc1a090
    // 0xc19e74: mov             x0, x4
    // 0xc19e78: StoreField: r1->field_13 = r0
    //     0xc19e78: stur            w0, [x1, #0x13]
    //     0xc19e7c: ldurb           w16, [x1, #-1]
    //     0xc19e80: ldurb           w17, [x0, #-1]
    //     0xc19e84: and             x16, x17, x16, lsr #2
    //     0xc19e88: tst             x16, HEAP, lsr #32
    //     0xc19e8c: b.eq            #0xc19e94
    //     0xc19e90: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xc19e94: mov             x2, x7
    // 0xc19e98: mov             x5, x6
    // 0xc19e9c: r0 = SourceSpanBase()
    //     0xc19e9c: bl              #0xc1a098  ; [package:source_span/src/span.dart] SourceSpanBase::SourceSpanBase
    // 0xc19ea0: ldur            x3, [fp, #-0x18]
    // 0xc19ea4: r0 = LoadClassIdInstr(r3)
    //     0xc19ea4: ldur            x0, [x3, #-1]
    //     0xc19ea8: ubfx            x0, x0, #0xc, #0x14
    // 0xc19eac: mov             x1, x3
    // 0xc19eb0: ldur            x2, [fp, #-0x10]
    // 0xc19eb4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc19eb4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc19eb8: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc19eb8: sub             lr, x0, #0xffc
    //     0xc19ebc: ldr             lr, [x21, lr, lsl #3]
    //     0xc19ec0: blr             lr
    // 0xc19ec4: tbnz            w0, #4, #0xc19f0c
    // 0xc19ec8: ldur            x2, [fp, #-8]
    // 0xc19ecc: r0 = LoadClassIdInstr(r2)
    //     0xc19ecc: ldur            x0, [x2, #-1]
    //     0xc19ed0: ubfx            x0, x0, #0xc, #0x14
    // 0xc19ed4: mov             x1, x2
    // 0xc19ed8: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc19ed8: sub             lr, x0, #0xfff
    //     0xc19edc: ldr             lr, [x21, lr, lsl #3]
    //     0xc19ee0: blr             lr
    // 0xc19ee4: ldur            x1, [fp, #-0x18]
    // 0xc19ee8: ldur            x2, [fp, #-0x10]
    // 0xc19eec: mov             x3, x0
    // 0xc19ef0: r0 = findLineStart()
    //     0xc19ef0: bl              #0xc196ac  ; [package:source_span/src/utils.dart] ::findLineStart
    // 0xc19ef4: cmp             w0, NULL
    // 0xc19ef8: b.eq            #0xc19f80
    // 0xc19efc: r0 = Null
    //     0xc19efc: mov             x0, NULL
    // 0xc19f00: LeaveFrame
    //     0xc19f00: mov             SP, fp
    //     0xc19f04: ldp             fp, lr, [SP], #0x10
    // 0xc19f08: ret
    //     0xc19f08: ret             
    // 0xc19f0c: ldur            x3, [fp, #-0x10]
    // 0xc19f10: ldur            x0, [fp, #-0x18]
    // 0xc19f14: r1 = Null
    //     0xc19f14: mov             x1, NULL
    // 0xc19f18: r2 = 10
    //     0xc19f18: movz            x2, #0xa
    // 0xc19f1c: r0 = AllocateArray()
    //     0xc19f1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc19f20: r16 = "The context line \""
    //     0xc19f20: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c748] "The context line \""
    //     0xc19f24: ldr             x16, [x16, #0x748]
    // 0xc19f28: StoreField: r0->field_f = r16
    //     0xc19f28: stur            w16, [x0, #0xf]
    // 0xc19f2c: ldur            x3, [fp, #-0x18]
    // 0xc19f30: StoreField: r0->field_13 = r3
    //     0xc19f30: stur            w3, [x0, #0x13]
    // 0xc19f34: r16 = "\" must contain \""
    //     0xc19f34: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c750] "\" must contain \""
    //     0xc19f38: ldr             x16, [x16, #0x750]
    // 0xc19f3c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc19f3c: stur            w16, [x0, #0x17]
    // 0xc19f40: ldur            x4, [fp, #-0x10]
    // 0xc19f44: StoreField: r0->field_1b = r4
    //     0xc19f44: stur            w4, [x0, #0x1b]
    // 0xc19f48: r16 = "\"."
    //     0xc19f48: ldr             x16, [PP, #0x378]  ; [pp+0x378] "\"."
    // 0xc19f4c: StoreField: r0->field_1f = r16
    //     0xc19f4c: stur            w16, [x0, #0x1f]
    // 0xc19f50: str             x0, [SP]
    // 0xc19f54: r0 = _interpolate()
    //     0xc19f54: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc19f58: stur            x0, [fp, #-0x20]
    // 0xc19f5c: r0 = ArgumentError()
    //     0xc19f5c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc19f60: mov             x1, x0
    // 0xc19f64: ldur            x0, [fp, #-0x20]
    // 0xc19f68: ArrayStore: r1[0] = r0  ; List_4
    //     0xc19f68: stur            w0, [x1, #0x17]
    // 0xc19f6c: r0 = false
    //     0xc19f6c: add             x0, NULL, #0x30  ; false
    // 0xc19f70: StoreField: r1->field_b = r0
    //     0xc19f70: stur            w0, [x1, #0xb]
    // 0xc19f74: mov             x0, x1
    // 0xc19f78: r0 = Throw()
    //     0xc19f78: bl              #0xec04b8  ; ThrowStub
    // 0xc19f7c: brk             #0
    // 0xc19f80: ldur            x5, [fp, #-8]
    // 0xc19f84: ldur            x4, [fp, #-0x10]
    // 0xc19f88: ldur            x3, [fp, #-0x18]
    // 0xc19f8c: r0 = false
    //     0xc19f8c: add             x0, NULL, #0x30  ; false
    // 0xc19f90: r1 = Null
    //     0xc19f90: mov             x1, NULL
    // 0xc19f94: r2 = 14
    //     0xc19f94: movz            x2, #0xe
    // 0xc19f98: r0 = AllocateArray()
    //     0xc19f98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc19f9c: mov             x2, x0
    // 0xc19fa0: stur            x2, [fp, #-0x20]
    // 0xc19fa4: r16 = "The span text \""
    //     0xc19fa4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c758] "The span text \""
    //     0xc19fa8: ldr             x16, [x16, #0x758]
    // 0xc19fac: StoreField: r2->field_f = r16
    //     0xc19fac: stur            w16, [x2, #0xf]
    // 0xc19fb0: ldur            x0, [fp, #-0x10]
    // 0xc19fb4: StoreField: r2->field_13 = r0
    //     0xc19fb4: stur            w0, [x2, #0x13]
    // 0xc19fb8: r16 = "\" must start at column "
    //     0xc19fb8: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c760] "\" must start at column "
    //     0xc19fbc: ldr             x16, [x16, #0x760]
    // 0xc19fc0: ArrayStore: r2[0] = r16  ; List_4
    //     0xc19fc0: stur            w16, [x2, #0x17]
    // 0xc19fc4: ldur            x1, [fp, #-8]
    // 0xc19fc8: r0 = LoadClassIdInstr(r1)
    //     0xc19fc8: ldur            x0, [x1, #-1]
    //     0xc19fcc: ubfx            x0, x0, #0xc, #0x14
    // 0xc19fd0: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc19fd0: sub             lr, x0, #0xfff
    //     0xc19fd4: ldr             lr, [x21, lr, lsl #3]
    //     0xc19fd8: blr             lr
    // 0xc19fdc: add             x2, x0, #1
    // 0xc19fe0: r0 = BoxInt64Instr(r2)
    //     0xc19fe0: sbfiz           x0, x2, #1, #0x1f
    //     0xc19fe4: cmp             x2, x0, asr #1
    //     0xc19fe8: b.eq            #0xc19ff4
    //     0xc19fec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc19ff0: stur            x2, [x0, #7]
    // 0xc19ff4: ldur            x1, [fp, #-0x20]
    // 0xc19ff8: ArrayStore: r1[3] = r0  ; List_4
    //     0xc19ff8: add             x25, x1, #0x1b
    //     0xc19ffc: str             w0, [x25]
    //     0xc1a000: tbz             w0, #0, #0xc1a01c
    //     0xc1a004: ldurb           w16, [x1, #-1]
    //     0xc1a008: ldurb           w17, [x0, #-1]
    //     0xc1a00c: and             x16, x17, x16, lsr #2
    //     0xc1a010: tst             x16, HEAP, lsr #32
    //     0xc1a014: b.eq            #0xc1a01c
    //     0xc1a018: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc1a01c: ldur            x2, [fp, #-0x20]
    // 0xc1a020: r16 = " in a line within \""
    //     0xc1a020: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c768] " in a line within \""
    //     0xc1a024: ldr             x16, [x16, #0x768]
    // 0xc1a028: StoreField: r2->field_1f = r16
    //     0xc1a028: stur            w16, [x2, #0x1f]
    // 0xc1a02c: mov             x1, x2
    // 0xc1a030: ldur            x0, [fp, #-0x18]
    // 0xc1a034: ArrayStore: r1[5] = r0  ; List_4
    //     0xc1a034: add             x25, x1, #0x23
    //     0xc1a038: str             w0, [x25]
    //     0xc1a03c: tbz             w0, #0, #0xc1a058
    //     0xc1a040: ldurb           w16, [x1, #-1]
    //     0xc1a044: ldurb           w17, [x0, #-1]
    //     0xc1a048: and             x16, x17, x16, lsr #2
    //     0xc1a04c: tst             x16, HEAP, lsr #32
    //     0xc1a050: b.eq            #0xc1a058
    //     0xc1a054: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc1a058: r16 = "\"."
    //     0xc1a058: ldr             x16, [PP, #0x378]  ; [pp+0x378] "\"."
    // 0xc1a05c: StoreField: r2->field_27 = r16
    //     0xc1a05c: stur            w16, [x2, #0x27]
    // 0xc1a060: str             x2, [SP]
    // 0xc1a064: r0 = _interpolate()
    //     0xc1a064: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc1a068: stur            x0, [fp, #-8]
    // 0xc1a06c: r0 = ArgumentError()
    //     0xc1a06c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc1a070: mov             x1, x0
    // 0xc1a074: ldur            x0, [fp, #-8]
    // 0xc1a078: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1a078: stur            w0, [x1, #0x17]
    // 0xc1a07c: r0 = false
    //     0xc1a07c: add             x0, NULL, #0x30  ; false
    // 0xc1a080: StoreField: r1->field_b = r0
    //     0xc1a080: stur            w0, [x1, #0xb]
    // 0xc1a084: mov             x0, x1
    // 0xc1a088: r0 = Throw()
    //     0xc1a088: bl              #0xec04b8  ; ThrowStub
    // 0xc1a08c: brk             #0
    // 0xc1a090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1a090: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1a094: b               #0xc19e74
  }
}
