// lib: , url: package:source_span/src/utils.dart

// class id: 1051145, size: 0x8
class :: {

  static _ replaceFirstNull(/* No info */) {
    // ** addr: 0xc151c4, size: 0xf8
    // 0xc151c4: EnterFrame
    //     0xc151c4: stp             fp, lr, [SP, #-0x10]!
    //     0xc151c8: mov             fp, SP
    // 0xc151cc: AllocStack(0x10)
    //     0xc151cc: sub             SP, SP, #0x10
    // 0xc151d0: CheckStackOverflow
    //     0xc151d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc151d4: cmp             SP, x16
    //     0xc151d8: b.ls            #0xc152b0
    // 0xc151dc: ldr             x1, [fp, #0x18]
    // 0xc151e0: r2 = Null
    //     0xc151e0: mov             x2, NULL
    // 0xc151e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc151e4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc151e8: r0 = indexOf()
    //     0xc151e8: bl              #0x6ec5f4  ; [dart:collection] ListBase::indexOf
    // 0xc151ec: r2 = LoadInt32Instr(r0)
    //     0xc151ec: sbfx            x2, x0, #1, #0x1f
    //     0xc151f0: tbz             w0, #0, #0xc151f8
    //     0xc151f4: ldur            x2, [x0, #7]
    // 0xc151f8: tbnz            x2, #0x3f, #0xc15258
    // 0xc151fc: ldr             x3, [fp, #0x18]
    // 0xc15200: LoadField: r0 = r3->field_b
    //     0xc15200: ldur            w0, [x3, #0xb]
    // 0xc15204: r1 = LoadInt32Instr(r0)
    //     0xc15204: sbfx            x1, x0, #1, #0x1f
    // 0xc15208: mov             x0, x1
    // 0xc1520c: mov             x1, x2
    // 0xc15210: cmp             x1, x0
    // 0xc15214: b.hs            #0xc152b8
    // 0xc15218: mov             x1, x3
    // 0xc1521c: ldr             x0, [fp, #0x10]
    // 0xc15220: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc15220: add             x25, x1, x2, lsl #2
    //     0xc15224: add             x25, x25, #0xf
    //     0xc15228: str             w0, [x25]
    //     0xc1522c: tbz             w0, #0, #0xc15248
    //     0xc15230: ldurb           w16, [x1, #-1]
    //     0xc15234: ldurb           w17, [x0, #-1]
    //     0xc15238: and             x16, x17, x16, lsr #2
    //     0xc1523c: tst             x16, HEAP, lsr #32
    //     0xc15240: b.eq            #0xc15248
    //     0xc15244: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc15248: r0 = Null
    //     0xc15248: mov             x0, NULL
    // 0xc1524c: LeaveFrame
    //     0xc1524c: mov             SP, fp
    //     0xc15250: ldp             fp, lr, [SP], #0x10
    // 0xc15254: ret
    //     0xc15254: ret             
    // 0xc15258: ldr             x3, [fp, #0x18]
    // 0xc1525c: r1 = Null
    //     0xc1525c: mov             x1, NULL
    // 0xc15260: r2 = 4
    //     0xc15260: movz            x2, #0x4
    // 0xc15264: r0 = AllocateArray()
    //     0xc15264: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc15268: mov             x1, x0
    // 0xc1526c: ldr             x0, [fp, #0x18]
    // 0xc15270: StoreField: r1->field_f = r0
    //     0xc15270: stur            w0, [x1, #0xf]
    // 0xc15274: r16 = " contains no null elements."
    //     0xc15274: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c5b8] " contains no null elements."
    //     0xc15278: ldr             x16, [x16, #0x5b8]
    // 0xc1527c: StoreField: r1->field_13 = r16
    //     0xc1527c: stur            w16, [x1, #0x13]
    // 0xc15280: str             x1, [SP]
    // 0xc15284: r0 = _interpolate()
    //     0xc15284: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc15288: stur            x0, [fp, #-8]
    // 0xc1528c: r0 = ArgumentError()
    //     0xc1528c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc15290: mov             x1, x0
    // 0xc15294: ldur            x0, [fp, #-8]
    // 0xc15298: ArrayStore: r1[0] = r0  ; List_4
    //     0xc15298: stur            w0, [x1, #0x17]
    // 0xc1529c: r0 = false
    //     0xc1529c: add             x0, NULL, #0x30  ; false
    // 0xc152a0: StoreField: r1->field_b = r0
    //     0xc152a0: stur            w0, [x1, #0xb]
    // 0xc152a4: mov             x0, x1
    // 0xc152a8: r0 = Throw()
    //     0xc152a8: bl              #0xec04b8  ; ThrowStub
    // 0xc152ac: brk             #0
    // 0xc152b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc152b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc152b4: b               #0xc151dc
    // 0xc152b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc152b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ isMultiline(/* No info */) {
    // ** addr: 0xc152bc, size: 0xc4
    // 0xc152bc: EnterFrame
    //     0xc152bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc152c0: mov             fp, SP
    // 0xc152c4: AllocStack(0x10)
    //     0xc152c4: sub             SP, SP, #0x10
    // 0xc152c8: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xc152c8: mov             x2, x1
    //     0xc152cc: stur            x1, [fp, #-8]
    // 0xc152d0: CheckStackOverflow
    //     0xc152d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc152d4: cmp             SP, x16
    //     0xc152d8: b.ls            #0xc15378
    // 0xc152dc: r0 = LoadClassIdInstr(r2)
    //     0xc152dc: ldur            x0, [x2, #-1]
    //     0xc152e0: ubfx            x0, x0, #0xc, #0x14
    // 0xc152e4: mov             x1, x2
    // 0xc152e8: r0 = GDT[cid_x0 + -0xfff]()
    //     0xc152e8: sub             lr, x0, #0xfff
    //     0xc152ec: ldr             lr, [x21, lr, lsl #3]
    //     0xc152f0: blr             lr
    // 0xc152f4: r1 = LoadClassIdInstr(r0)
    //     0xc152f4: ldur            x1, [x0, #-1]
    //     0xc152f8: ubfx            x1, x1, #0xc, #0x14
    // 0xc152fc: mov             x16, x0
    // 0xc15300: mov             x0, x1
    // 0xc15304: mov             x1, x16
    // 0xc15308: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc15308: sub             lr, x0, #1, lsl #12
    //     0xc1530c: ldr             lr, [x21, lr, lsl #3]
    //     0xc15310: blr             lr
    // 0xc15314: mov             x2, x0
    // 0xc15318: ldur            x1, [fp, #-8]
    // 0xc1531c: stur            x2, [fp, #-0x10]
    // 0xc15320: r0 = LoadClassIdInstr(r1)
    //     0xc15320: ldur            x0, [x1, #-1]
    //     0xc15324: ubfx            x0, x0, #0xc, #0x14
    // 0xc15328: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc15328: sub             lr, x0, #1, lsl #12
    //     0xc1532c: ldr             lr, [x21, lr, lsl #3]
    //     0xc15330: blr             lr
    // 0xc15334: r1 = LoadClassIdInstr(r0)
    //     0xc15334: ldur            x1, [x0, #-1]
    //     0xc15338: ubfx            x1, x1, #0xc, #0x14
    // 0xc1533c: mov             x16, x0
    // 0xc15340: mov             x0, x1
    // 0xc15344: mov             x1, x16
    // 0xc15348: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc15348: sub             lr, x0, #1, lsl #12
    //     0xc1534c: ldr             lr, [x21, lr, lsl #3]
    //     0xc15350: blr             lr
    // 0xc15354: ldur            x1, [fp, #-0x10]
    // 0xc15358: cmp             x1, x0
    // 0xc1535c: r16 = true
    //     0xc1535c: add             x16, NULL, #0x20  ; true
    // 0xc15360: r17 = false
    //     0xc15360: add             x17, NULL, #0x30  ; false
    // 0xc15364: csel            x2, x16, x17, ne
    // 0xc15368: mov             x0, x2
    // 0xc1536c: LeaveFrame
    //     0xc1536c: mov             SP, fp
    //     0xc15370: ldp             fp, lr, [SP], #0x10
    // 0xc15374: ret
    //     0xc15374: ret             
    // 0xc15378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15378: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1537c: b               #0xc152dc
  }
  static _ replaceWithNull(/* No info */) {
    // ** addr: 0xc15a24, size: 0xe4
    // 0xc15a24: EnterFrame
    //     0xc15a24: stp             fp, lr, [SP, #-0x10]!
    //     0xc15a28: mov             fp, SP
    // 0xc15a2c: AllocStack(0x10)
    //     0xc15a2c: sub             SP, SP, #0x10
    // 0xc15a30: CheckStackOverflow
    //     0xc15a30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15a34: cmp             SP, x16
    //     0xc15a38: b.ls            #0xc15afc
    // 0xc15a3c: ldr             x1, [fp, #0x18]
    // 0xc15a40: ldr             x2, [fp, #0x10]
    // 0xc15a44: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc15a44: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc15a48: r0 = indexOf()
    //     0xc15a48: bl              #0x6ec5f4  ; [dart:collection] ListBase::indexOf
    // 0xc15a4c: r2 = LoadInt32Instr(r0)
    //     0xc15a4c: sbfx            x2, x0, #1, #0x1f
    //     0xc15a50: tbz             w0, #0, #0xc15a58
    //     0xc15a54: ldur            x2, [x0, #7]
    // 0xc15a58: tbnz            x2, #0x3f, #0xc15a90
    // 0xc15a5c: ldr             x3, [fp, #0x18]
    // 0xc15a60: LoadField: r0 = r3->field_b
    //     0xc15a60: ldur            w0, [x3, #0xb]
    // 0xc15a64: r1 = LoadInt32Instr(r0)
    //     0xc15a64: sbfx            x1, x0, #1, #0x1f
    // 0xc15a68: mov             x0, x1
    // 0xc15a6c: mov             x1, x2
    // 0xc15a70: cmp             x1, x0
    // 0xc15a74: b.hs            #0xc15b04
    // 0xc15a78: ArrayStore: r3[r2] = rNULL  ; Unknown_4
    //     0xc15a78: add             x0, x3, x2, lsl #2
    //     0xc15a7c: stur            NULL, [x0, #0xf]
    // 0xc15a80: r0 = Null
    //     0xc15a80: mov             x0, NULL
    // 0xc15a84: LeaveFrame
    //     0xc15a84: mov             SP, fp
    //     0xc15a88: ldp             fp, lr, [SP], #0x10
    // 0xc15a8c: ret
    //     0xc15a8c: ret             
    // 0xc15a90: ldr             x3, [fp, #0x18]
    // 0xc15a94: ldr             x0, [fp, #0x10]
    // 0xc15a98: r1 = Null
    //     0xc15a98: mov             x1, NULL
    // 0xc15a9c: r2 = 8
    //     0xc15a9c: movz            x2, #0x8
    // 0xc15aa0: r0 = AllocateArray()
    //     0xc15aa0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc15aa4: mov             x1, x0
    // 0xc15aa8: ldr             x0, [fp, #0x18]
    // 0xc15aac: StoreField: r1->field_f = r0
    //     0xc15aac: stur            w0, [x1, #0xf]
    // 0xc15ab0: r16 = " contains no elements matching "
    //     0xc15ab0: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c608] " contains no elements matching "
    //     0xc15ab4: ldr             x16, [x16, #0x608]
    // 0xc15ab8: StoreField: r1->field_13 = r16
    //     0xc15ab8: stur            w16, [x1, #0x13]
    // 0xc15abc: ldr             x0, [fp, #0x10]
    // 0xc15ac0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc15ac0: stur            w0, [x1, #0x17]
    // 0xc15ac4: r16 = "."
    //     0xc15ac4: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc15ac8: StoreField: r1->field_1b = r16
    //     0xc15ac8: stur            w16, [x1, #0x1b]
    // 0xc15acc: str             x1, [SP]
    // 0xc15ad0: r0 = _interpolate()
    //     0xc15ad0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc15ad4: stur            x0, [fp, #-8]
    // 0xc15ad8: r0 = ArgumentError()
    //     0xc15ad8: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xc15adc: mov             x1, x0
    // 0xc15ae0: ldur            x0, [fp, #-8]
    // 0xc15ae4: ArrayStore: r1[0] = r0  ; List_4
    //     0xc15ae4: stur            w0, [x1, #0x17]
    // 0xc15ae8: r0 = false
    //     0xc15ae8: add             x0, NULL, #0x30  ; false
    // 0xc15aec: StoreField: r1->field_b = r0
    //     0xc15aec: stur            w0, [x1, #0xb]
    // 0xc15af0: mov             x0, x1
    // 0xc15af4: r0 = Throw()
    //     0xc15af4: bl              #0xec04b8  ; ThrowStub
    // 0xc15af8: brk             #0
    // 0xc15afc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15afc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15b00: b               #0xc15a3c
    // 0xc15b04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc15b04: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ isAllTheSame(/* No info */) {
    // ** addr: 0xc1893c, size: 0x200
    // 0xc1893c: EnterFrame
    //     0xc1893c: stp             fp, lr, [SP, #-0x10]!
    //     0xc18940: mov             fp, SP
    // 0xc18944: AllocStack(0x40)
    //     0xc18944: sub             SP, SP, #0x40
    // 0xc18948: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc18948: mov             x0, x1
    //     0xc1894c: stur            x1, [fp, #-8]
    // 0xc18950: CheckStackOverflow
    //     0xc18950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18954: cmp             SP, x16
    //     0xc18958: b.ls            #0xc18b2c
    // 0xc1895c: mov             x1, x0
    // 0xc18960: r0 = isEmpty()
    //     0xc18960: bl              #0x8a043c  ; [dart:collection] ListBase::isEmpty
    // 0xc18964: tbnz            w0, #4, #0xc18978
    // 0xc18968: r0 = true
    //     0xc18968: add             x0, NULL, #0x20  ; true
    // 0xc1896c: LeaveFrame
    //     0xc1896c: mov             SP, fp
    //     0xc18970: ldp             fp, lr, [SP], #0x10
    // 0xc18974: ret
    //     0xc18974: ret             
    // 0xc18978: ldur            x1, [fp, #-8]
    // 0xc1897c: r0 = first()
    //     0xc1897c: bl              #0x893184  ; [dart:_internal] ListIterable::first
    // 0xc18980: ldur            x1, [fp, #-8]
    // 0xc18984: r2 = 1
    //     0xc18984: movz            x2, #0x1
    // 0xc18988: stur            x0, [fp, #-8]
    // 0xc1898c: r0 = skip()
    //     0xc1898c: bl              #0xa5a484  ; [dart:collection] ListBase::skip
    // 0xc18990: mov             x1, x0
    // 0xc18994: r0 = iterator()
    //     0xc18994: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0xc18998: mov             x1, x0
    // 0xc1899c: stur            x1, [fp, #-0x28]
    // 0xc189a0: LoadField: r2 = r1->field_b
    //     0xc189a0: ldur            w2, [x1, #0xb]
    // 0xc189a4: DecompressPointer r2
    //     0xc189a4: add             x2, x2, HEAP, lsl #32
    // 0xc189a8: stur            x2, [fp, #-0x20]
    // 0xc189ac: LoadField: r3 = r1->field_f
    //     0xc189ac: ldur            x3, [x1, #0xf]
    // 0xc189b0: stur            x3, [fp, #-0x18]
    // 0xc189b4: LoadField: r4 = r1->field_7
    //     0xc189b4: ldur            w4, [x1, #7]
    // 0xc189b8: DecompressPointer r4
    //     0xc189b8: add             x4, x4, HEAP, lsl #32
    // 0xc189bc: stur            x4, [fp, #-0x10]
    // 0xc189c0: CheckStackOverflow
    //     0xc189c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc189c4: cmp             SP, x16
    //     0xc189c8: b.ls            #0xc18b34
    // 0xc189cc: r0 = LoadClassIdInstr(r2)
    //     0xc189cc: ldur            x0, [x2, #-1]
    //     0xc189d0: ubfx            x0, x0, #0xc, #0x14
    // 0xc189d4: str             x2, [SP]
    // 0xc189d8: r0 = GDT[cid_x0 + 0xc834]()
    //     0xc189d8: movz            x17, #0xc834
    //     0xc189dc: add             lr, x0, x17
    //     0xc189e0: ldr             lr, [x21, lr, lsl #3]
    //     0xc189e4: blr             lr
    // 0xc189e8: r1 = LoadInt32Instr(r0)
    //     0xc189e8: sbfx            x1, x0, #1, #0x1f
    //     0xc189ec: tbz             w0, #0, #0xc189f4
    //     0xc189f0: ldur            x1, [x0, #7]
    // 0xc189f4: ldur            x3, [fp, #-0x18]
    // 0xc189f8: cmp             x3, x1
    // 0xc189fc: b.ne            #0xc18b0c
    // 0xc18a00: ldur            x4, [fp, #-0x28]
    // 0xc18a04: ArrayLoad: r2 = r4[0]  ; List_8
    //     0xc18a04: ldur            x2, [x4, #0x17]
    // 0xc18a08: cmp             x2, x1
    // 0xc18a0c: b.ge            #0xc18af4
    // 0xc18a10: ldur            x5, [fp, #-0x20]
    // 0xc18a14: r0 = LoadClassIdInstr(r5)
    //     0xc18a14: ldur            x0, [x5, #-1]
    //     0xc18a18: ubfx            x0, x0, #0xc, #0x14
    // 0xc18a1c: mov             x1, x5
    // 0xc18a20: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xc18a20: movz            x17, #0xd28f
    //     0xc18a24: add             lr, x0, x17
    //     0xc18a28: ldr             lr, [x21, lr, lsl #3]
    //     0xc18a2c: blr             lr
    // 0xc18a30: mov             x4, x0
    // 0xc18a34: ldur            x3, [fp, #-0x28]
    // 0xc18a38: stur            x4, [fp, #-0x30]
    // 0xc18a3c: StoreField: r3->field_1f = r0
    //     0xc18a3c: stur            w0, [x3, #0x1f]
    //     0xc18a40: tbz             w0, #0, #0xc18a5c
    //     0xc18a44: ldurb           w16, [x3, #-1]
    //     0xc18a48: ldurb           w17, [x0, #-1]
    //     0xc18a4c: and             x16, x17, x16, lsr #2
    //     0xc18a50: tst             x16, HEAP, lsr #32
    //     0xc18a54: b.eq            #0xc18a5c
    //     0xc18a58: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xc18a5c: ArrayLoad: r0 = r3[0]  ; List_8
    //     0xc18a5c: ldur            x0, [x3, #0x17]
    // 0xc18a60: add             x1, x0, #1
    // 0xc18a64: ArrayStore: r3[0] = r1  ; List_8
    //     0xc18a64: stur            x1, [x3, #0x17]
    // 0xc18a68: cmp             w4, NULL
    // 0xc18a6c: b.ne            #0xc18aa0
    // 0xc18a70: mov             x0, x4
    // 0xc18a74: ldur            x2, [fp, #-0x10]
    // 0xc18a78: r1 = Null
    //     0xc18a78: mov             x1, NULL
    // 0xc18a7c: cmp             w2, NULL
    // 0xc18a80: b.eq            #0xc18aa0
    // 0xc18a84: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xc18a84: ldur            w4, [x2, #0x17]
    // 0xc18a88: DecompressPointer r4
    //     0xc18a88: add             x4, x4, HEAP, lsl #32
    // 0xc18a8c: r8 = X0
    //     0xc18a8c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xc18a90: LoadField: r9 = r4->field_7
    //     0xc18a90: ldur            x9, [x4, #7]
    // 0xc18a94: r3 = Null
    //     0xc18a94: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c6c8] Null
    //     0xc18a98: ldr             x3, [x3, #0x6c8]
    // 0xc18a9c: blr             x9
    // 0xc18aa0: ldur            x0, [fp, #-0x30]
    // 0xc18aa4: r1 = 60
    //     0xc18aa4: movz            x1, #0x3c
    // 0xc18aa8: branchIfSmi(r0, 0xc18ab4)
    //     0xc18aa8: tbz             w0, #0, #0xc18ab4
    // 0xc18aac: r1 = LoadClassIdInstr(r0)
    //     0xc18aac: ldur            x1, [x0, #-1]
    //     0xc18ab0: ubfx            x1, x1, #0xc, #0x14
    // 0xc18ab4: ldur            x16, [fp, #-8]
    // 0xc18ab8: stp             x16, x0, [SP]
    // 0xc18abc: mov             x0, x1
    // 0xc18ac0: mov             lr, x0
    // 0xc18ac4: ldr             lr, [x21, lr, lsl #3]
    // 0xc18ac8: blr             lr
    // 0xc18acc: tbnz            w0, #4, #0xc18ae4
    // 0xc18ad0: ldur            x1, [fp, #-0x28]
    // 0xc18ad4: ldur            x4, [fp, #-0x10]
    // 0xc18ad8: ldur            x2, [fp, #-0x20]
    // 0xc18adc: ldur            x3, [fp, #-0x18]
    // 0xc18ae0: b               #0xc189c0
    // 0xc18ae4: r0 = false
    //     0xc18ae4: add             x0, NULL, #0x30  ; false
    // 0xc18ae8: LeaveFrame
    //     0xc18ae8: mov             SP, fp
    //     0xc18aec: ldp             fp, lr, [SP], #0x10
    // 0xc18af0: ret
    //     0xc18af0: ret             
    // 0xc18af4: mov             x0, x4
    // 0xc18af8: StoreField: r0->field_1f = rNULL
    //     0xc18af8: stur            NULL, [x0, #0x1f]
    // 0xc18afc: r0 = true
    //     0xc18afc: add             x0, NULL, #0x20  ; true
    // 0xc18b00: LeaveFrame
    //     0xc18b00: mov             SP, fp
    //     0xc18b04: ldp             fp, lr, [SP], #0x10
    // 0xc18b08: ret
    //     0xc18b08: ret             
    // 0xc18b0c: ldur            x0, [fp, #-0x20]
    // 0xc18b10: r0 = ConcurrentModificationError()
    //     0xc18b10: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xc18b14: mov             x1, x0
    // 0xc18b18: ldur            x0, [fp, #-0x20]
    // 0xc18b1c: StoreField: r1->field_b = r0
    //     0xc18b1c: stur            w0, [x1, #0xb]
    // 0xc18b20: mov             x0, x1
    // 0xc18b24: r0 = Throw()
    //     0xc18b24: bl              #0xec04b8  ; ThrowStub
    // 0xc18b28: brk             #0
    // 0xc18b2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18b2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18b30: b               #0xc1895c
    // 0xc18b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18b34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18b38: b               #0xc189cc
  }
  static _ findLineStart(/* No info */) {
    // ** addr: 0xc196ac, size: 0x228
    // 0xc196ac: EnterFrame
    //     0xc196ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc196b0: mov             fp, SP
    // 0xc196b4: AllocStack(0x30)
    //     0xc196b4: sub             SP, SP, #0x30
    // 0xc196b8: SetupParameters(dynamic _ /* r1 => r5, fp-0x18 */, dynamic _ /* r2 => r4, fp-0x28 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xc196b8: mov             x5, x1
    //     0xc196bc: mov             x4, x2
    //     0xc196c0: stur            x1, [fp, #-0x18]
    //     0xc196c4: stur            x3, [fp, #-0x20]
    //     0xc196c8: stur            x2, [fp, #-0x28]
    // 0xc196cc: CheckStackOverflow
    //     0xc196cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc196d0: cmp             SP, x16
    //     0xc196d4: b.ls            #0xc198bc
    // 0xc196d8: LoadField: r0 = r4->field_7
    //     0xc196d8: ldur            w0, [x4, #7]
    // 0xc196dc: cbnz            w0, #0xc197a4
    // 0xc196e0: r4 = 0
    //     0xc196e0: movz            x4, #0
    // 0xc196e4: stur            x4, [fp, #-0x10]
    // 0xc196e8: CheckStackOverflow
    //     0xc196e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc196ec: cmp             SP, x16
    //     0xc196f0: b.ls            #0xc198c4
    // 0xc196f4: r0 = BoxInt64Instr(r4)
    //     0xc196f4: sbfiz           x0, x4, #1, #0x1f
    //     0xc196f8: cmp             x4, x0, asr #1
    //     0xc196fc: b.eq            #0xc19708
    //     0xc19700: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc19704: stur            x4, [x0, #7]
    // 0xc19708: mov             x6, x0
    // 0xc1970c: stur            x6, [fp, #-8]
    // 0xc19710: r0 = LoadClassIdInstr(r5)
    //     0xc19710: ldur            x0, [x5, #-1]
    //     0xc19714: ubfx            x0, x0, #0xc, #0x14
    // 0xc19718: str             x6, [SP]
    // 0xc1971c: mov             x1, x5
    // 0xc19720: r2 = "\n"
    //     0xc19720: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc19724: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc19724: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc19728: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc19728: sub             lr, x0, #0xffa
    //     0xc1972c: ldr             lr, [x21, lr, lsl #3]
    //     0xc19730: blr             lr
    // 0xc19734: cmn             x0, #1
    // 0xc19738: b.eq            #0xc1976c
    // 0xc1973c: ldur            x3, [fp, #-0x20]
    // 0xc19740: ldur            x1, [fp, #-0x10]
    // 0xc19744: sub             x2, x0, x1
    // 0xc19748: cmp             x2, x3
    // 0xc1974c: b.ge            #0xc1975c
    // 0xc19750: add             x4, x0, #1
    // 0xc19754: ldur            x5, [fp, #-0x18]
    // 0xc19758: b               #0xc196e4
    // 0xc1975c: ldur            x0, [fp, #-8]
    // 0xc19760: LeaveFrame
    //     0xc19760: mov             SP, fp
    //     0xc19764: ldp             fp, lr, [SP], #0x10
    // 0xc19768: ret
    //     0xc19768: ret             
    // 0xc1976c: ldur            x5, [fp, #-0x18]
    // 0xc19770: ldur            x3, [fp, #-0x20]
    // 0xc19774: ldur            x1, [fp, #-0x10]
    // 0xc19778: LoadField: r0 = r5->field_7
    //     0xc19778: ldur            w0, [x5, #7]
    // 0xc1977c: r2 = LoadInt32Instr(r0)
    //     0xc1977c: sbfx            x2, x0, #1, #0x1f
    // 0xc19780: sub             x0, x2, x1
    // 0xc19784: cmp             x0, x3
    // 0xc19788: b.lt            #0xc19794
    // 0xc1978c: ldur            x0, [fp, #-8]
    // 0xc19790: b               #0xc19798
    // 0xc19794: r0 = Null
    //     0xc19794: mov             x0, NULL
    // 0xc19798: LeaveFrame
    //     0xc19798: mov             SP, fp
    //     0xc1979c: ldp             fp, lr, [SP], #0x10
    // 0xc197a0: ret
    //     0xc197a0: ret             
    // 0xc197a4: r0 = LoadClassIdInstr(r5)
    //     0xc197a4: ldur            x0, [x5, #-1]
    //     0xc197a8: ubfx            x0, x0, #0xc, #0x14
    // 0xc197ac: mov             x1, x5
    // 0xc197b0: mov             x2, x4
    // 0xc197b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc197b4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc197b8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc197b8: sub             lr, x0, #0xffa
    //     0xc197bc: ldr             lr, [x21, lr, lsl #3]
    //     0xc197c0: blr             lr
    // 0xc197c4: mov             x5, x0
    // 0xc197c8: ldur            x4, [fp, #-0x18]
    // 0xc197cc: ldur            x3, [fp, #-0x20]
    // 0xc197d0: stur            x5, [fp, #-0x10]
    // 0xc197d4: CheckStackOverflow
    //     0xc197d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc197d8: cmp             SP, x16
    //     0xc197dc: b.ls            #0xc198cc
    // 0xc197e0: cmn             x5, #1
    // 0xc197e4: b.eq            #0xc198ac
    // 0xc197e8: cbnz            x5, #0xc197f8
    // 0xc197ec: mov             x0, x5
    // 0xc197f0: r2 = 0
    //     0xc197f0: movz            x2, #0
    // 0xc197f4: b               #0xc19834
    // 0xc197f8: sub             x2, x5, #1
    // 0xc197fc: r0 = BoxInt64Instr(r2)
    //     0xc197fc: sbfiz           x0, x2, #1, #0x1f
    //     0xc19800: cmp             x2, x0, asr #1
    //     0xc19804: b.eq            #0xc19810
    //     0xc19808: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1980c: stur            x2, [x0, #7]
    // 0xc19810: str             x0, [SP]
    // 0xc19814: mov             x1, x4
    // 0xc19818: r2 = "\n"
    //     0xc19818: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc1981c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc1981c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc19820: r0 = lastIndexOf()
    //     0xc19820: bl              #0x642150  ; [dart:core] _StringBase::lastIndexOf
    // 0xc19824: add             x1, x0, #1
    // 0xc19828: mov             x2, x1
    // 0xc1982c: ldur            x3, [fp, #-0x20]
    // 0xc19830: ldur            x0, [fp, #-0x10]
    // 0xc19834: sub             x1, x0, x2
    // 0xc19838: cmp             x3, x1
    // 0xc1983c: b.eq            #0xc1988c
    // 0xc19840: ldur            x4, [fp, #-0x18]
    // 0xc19844: add             x2, x0, #1
    // 0xc19848: r0 = BoxInt64Instr(r2)
    //     0xc19848: sbfiz           x0, x2, #1, #0x1f
    //     0xc1984c: cmp             x2, x0, asr #1
    //     0xc19850: b.eq            #0xc1985c
    //     0xc19854: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc19858: stur            x2, [x0, #7]
    // 0xc1985c: r1 = LoadClassIdInstr(r4)
    //     0xc1985c: ldur            x1, [x4, #-1]
    //     0xc19860: ubfx            x1, x1, #0xc, #0x14
    // 0xc19864: str             x0, [SP]
    // 0xc19868: mov             x0, x1
    // 0xc1986c: mov             x1, x4
    // 0xc19870: ldur            x2, [fp, #-0x28]
    // 0xc19874: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc19874: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc19878: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc19878: sub             lr, x0, #0xffa
    //     0xc1987c: ldr             lr, [x21, lr, lsl #3]
    //     0xc19880: blr             lr
    // 0xc19884: mov             x5, x0
    // 0xc19888: b               #0xc197c8
    // 0xc1988c: r0 = BoxInt64Instr(r2)
    //     0xc1988c: sbfiz           x0, x2, #1, #0x1f
    //     0xc19890: cmp             x2, x0, asr #1
    //     0xc19894: b.eq            #0xc198a0
    //     0xc19898: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc1989c: stur            x2, [x0, #7]
    // 0xc198a0: LeaveFrame
    //     0xc198a0: mov             SP, fp
    //     0xc198a4: ldp             fp, lr, [SP], #0x10
    // 0xc198a8: ret
    //     0xc198a8: ret             
    // 0xc198ac: r0 = Null
    //     0xc198ac: mov             x0, NULL
    // 0xc198b0: LeaveFrame
    //     0xc198b0: mov             SP, fp
    //     0xc198b4: ldp             fp, lr, [SP], #0x10
    // 0xc198b8: ret
    //     0xc198b8: ret             
    // 0xc198bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc198bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc198c0: b               #0xc196d8
    // 0xc198c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc198c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc198c8: b               #0xc196f4
    // 0xc198cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc198cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc198d0: b               #0xc197e0
  }
  static _ countCodeUnits(/* No info */) {
    // ** addr: 0xc1b098, size: 0x8c
    // 0xc1b098: LoadField: r2 = r1->field_7
    //     0xc1b098: ldur            w2, [x1, #7]
    // 0xc1b09c: r3 = LoadInt32Instr(r2)
    //     0xc1b09c: sbfx            x3, x2, #1, #0x1f
    // 0xc1b0a0: r2 = LoadClassIdInstr(r1)
    //     0xc1b0a0: ldur            x2, [x1, #-1]
    //     0xc1b0a4: ubfx            x2, x2, #0xc, #0x14
    // 0xc1b0a8: lsl             x2, x2, #1
    // 0xc1b0ac: r5 = 0
    //     0xc1b0ac: movz            x5, #0
    // 0xc1b0b0: r4 = 0
    //     0xc1b0b0: movz            x4, #0
    // 0xc1b0b4: CheckStackOverflow
    //     0xc1b0b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1b0b8: cmp             SP, x16
    //     0xc1b0bc: b.ls            #0xc1b10c
    // 0xc1b0c0: cmp             x4, x3
    // 0xc1b0c4: b.ge            #0xc1b104
    // 0xc1b0c8: cmp             w2, #0xbc
    // 0xc1b0cc: b.ne            #0xc1b0dc
    // 0xc1b0d0: ArrayLoad: r6 = r1[r4]  ; TypedUnsigned_1
    //     0xc1b0d0: add             x16, x1, x4
    //     0xc1b0d4: ldrb            w6, [x16, #0xf]
    // 0xc1b0d8: b               #0xc1b0e4
    // 0xc1b0dc: add             x16, x1, x4, lsl #1
    // 0xc1b0e0: ldurh           w6, [x16, #0xf]
    // 0xc1b0e4: add             x0, x4, #1
    // 0xc1b0e8: lsl             x4, x6, #1
    // 0xc1b0ec: cmp             w4, #0x14
    // 0xc1b0f0: b.ne            #0xc1b0fc
    // 0xc1b0f4: add             x6, x5, #1
    // 0xc1b0f8: mov             x5, x6
    // 0xc1b0fc: mov             x4, x0
    // 0xc1b100: b               #0xc1b0b4
    // 0xc1b104: mov             x0, x5
    // 0xc1b108: ret
    //     0xc1b108: ret             
    // 0xc1b10c: EnterFrame
    //     0xc1b10c: stp             fp, lr, [SP, #-0x10]!
    //     0xc1b110: mov             fp, SP
    // 0xc1b114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1b114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1b118: LeaveFrame
    //     0xc1b118: mov             SP, fp
    //     0xc1b11c: ldp             fp, lr, [SP], #0x10
    // 0xc1b120: b               #0xc1b0c0
  }
}
