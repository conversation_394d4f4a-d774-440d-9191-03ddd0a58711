// lib: , url: package:sqflite_common/sqflite.dart

// class id: 1051147, size: 0x8
class :: {

  static _ openDatabase(/* No info */) {
    // ** addr: 0xaaed88, size: 0x54
    // 0xaaed88: EnterFrame
    //     0xaaed88: stp             fp, lr, [SP, #-0x10]!
    //     0xaaed8c: mov             fp, SP
    // 0xaaed90: AllocStack(0x10)
    //     0xaaed90: sub             SP, SP, #0x10
    // 0xaaed94: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xaaed94: mov             x0, x1
    //     0xaaed98: stur            x1, [fp, #-8]
    // 0xaaed9c: CheckStackOverflow
    //     0xaaed9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaeda0: cmp             SP, x16
    //     0xaaeda4: b.ls            #0xaaedd4
    // 0xaaeda8: r1 = Null
    //     0xaaeda8: mov             x1, NULL
    // 0xaaedac: r0 = OpenDatabaseOptions()
    //     0xaaedac: bl              #0xab6f0c  ; [package:sqflite_common/sqlite_api.dart] OpenDatabaseOptions::OpenDatabaseOptions
    // 0xaaedb0: stur            x0, [fp, #-0x10]
    // 0xaaedb4: r0 = databaseFactory()
    //     0xaaedb4: bl              #0xab6ec8  ; [package:sqflite_common/src/sqflite_database_factory.dart] ::databaseFactory
    // 0xaaedb8: mov             x1, x0
    // 0xaaedbc: ldur            x2, [fp, #-8]
    // 0xaaedc0: ldur            x3, [fp, #-0x10]
    // 0xaaedc4: r0 = openDatabase()
    //     0xaaedc4: bl              #0xaaeddc  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::openDatabase
    // 0xaaedc8: LeaveFrame
    //     0xaaedc8: mov             SP, fp
    //     0xaaedcc: ldp             fp, lr, [SP], #0x10
    // 0xaaedd0: ret
    //     0xaaedd0: ret             
    // 0xaaedd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaedd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaedd8: b               #0xaaeda8
  }
  static _ getDatabasesPath(/* No info */) {
    // ** addr: 0xab7298, size: 0x5c
    // 0xab7298: EnterFrame
    //     0xab7298: stp             fp, lr, [SP, #-0x10]!
    //     0xab729c: mov             fp, SP
    // 0xab72a0: CheckStackOverflow
    //     0xab72a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab72a4: cmp             SP, x16
    //     0xab72a8: b.ls            #0xab72ec
    // 0xab72ac: r1 = LoadStaticField(0x1758)
    //     0xab72ac: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xab72b0: ldr             x1, [x1, #0x2eb0]
    // 0xab72b4: cmp             w1, NULL
    // 0xab72b8: b.eq            #0xab72cc
    // 0xab72bc: r0 = getDatabasesPath()
    //     0xab72bc: bl              #0xab3368  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::getDatabasesPath
    // 0xab72c0: LeaveFrame
    //     0xab72c0: mov             SP, fp
    //     0xab72c4: ldp             fp, lr, [SP], #0x10
    // 0xab72c8: ret
    //     0xab72c8: ret             
    // 0xab72cc: r0 = StateError()
    //     0xab72cc: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab72d0: mov             x1, x0
    // 0xab72d4: r0 = "databaseFactory not initialized\ndatabaseFactory is only initialized when using sqflite. When using `sqflite_common_ffi`\nYou must call `databaseFactory = databaseFactoryFfi;` before using global openDatabase API\n"
    //     0xab72d4: add             x0, PP, #0x43, lsl #12  ; [pp+0x433d8] "databaseFactory not initialized\ndatabaseFactory is only initialized when using sqflite. When using `sqflite_common_ffi`\nYou must call `databaseFactory = databaseFactoryFfi;` before using global openDatabase API\n"
    //     0xab72d8: ldr             x0, [x0, #0x3d8]
    // 0xab72dc: StoreField: r1->field_b = r0
    //     0xab72dc: stur            w0, [x1, #0xb]
    // 0xab72e0: mov             x0, x1
    // 0xab72e4: r0 = Throw()
    //     0xab72e4: bl              #0xec04b8  ; ThrowStub
    // 0xab72e8: brk             #0
    // 0xab72ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab72ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab72f0: b               #0xab72ac
  }
}
