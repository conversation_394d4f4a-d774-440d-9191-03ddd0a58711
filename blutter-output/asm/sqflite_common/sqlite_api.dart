// lib: , url: package:sqflite_common/sqlite_api.dart

// class id: 1051148, size: 0x8
class :: {

  static late final (dynamic, Database, int, int) => void onDatabaseDowngradeDelete; // offset: 0x1120

  static _ SqfliteDatabaseExecutorExt.getVersion(/* No info */) {
    // ** addr: 0xab5770, size: 0x4c
    // 0xab5770: EnterFrame
    //     0xab5770: stp             fp, lr, [SP, #-0x10]!
    //     0xab5774: mov             fp, SP
    // 0xab5778: AllocStack(0x8)
    //     0xab5778: sub             SP, SP, #8
    // 0xab577c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xab577c: mov             x0, x1
    //     0xab5780: stur            x1, [fp, #-8]
    // 0xab5784: CheckStackOverflow
    //     0xab5784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5788: cmp             SP, x16
    //     0xab578c: b.ls            #0xab57b4
    // 0xab5790: mov             x1, x0
    // 0xab5794: r0 = checkNotClosed()
    //     0xab5794: bl              #0xab4e58  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::checkNotClosed
    // 0xab5798: ldur            x1, [fp, #-8]
    // 0xab579c: LoadField: r2 = r1->field_f
    //     0xab579c: ldur            w2, [x1, #0xf]
    // 0xab57a0: DecompressPointer r2
    //     0xab57a0: add             x2, x2, HEAP, lsl #32
    // 0xab57a4: r0 = SqfliteDatabaseMixinExt.txnGetVersion()
    //     0xab57a4: bl              #0xab57bc  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.txnGetVersion
    // 0xab57a8: LeaveFrame
    //     0xab57a8: mov             SP, fp
    //     0xab57ac: ldp             fp, lr, [SP], #0x10
    // 0xab57b0: ret
    //     0xab57b0: ret             
    // 0xab57b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab57b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab57b8: b               #0xab5790
  }
  static _ SqfliteDatabaseExecutorExt.setVersion(/* No info */) {
    // ** addr: 0xab67bc, size: 0x4c
    // 0xab67bc: EnterFrame
    //     0xab67bc: stp             fp, lr, [SP, #-0x10]!
    //     0xab67c0: mov             fp, SP
    // 0xab67c4: AllocStack(0x8)
    //     0xab67c4: sub             SP, SP, #8
    // 0xab67c8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xab67c8: mov             x0, x1
    //     0xab67cc: stur            x1, [fp, #-8]
    // 0xab67d0: CheckStackOverflow
    //     0xab67d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab67d4: cmp             SP, x16
    //     0xab67d8: b.ls            #0xab6800
    // 0xab67dc: mov             x1, x0
    // 0xab67e0: r0 = checkNotClosed()
    //     0xab67e0: bl              #0xab4e58  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::checkNotClosed
    // 0xab67e4: ldur            x1, [fp, #-8]
    // 0xab67e8: LoadField: r2 = r1->field_f
    //     0xab67e8: ldur            w2, [x1, #0xf]
    // 0xab67ec: DecompressPointer r2
    //     0xab67ec: add             x2, x2, HEAP, lsl #32
    // 0xab67f0: r0 = SqfliteDatabaseMixinExt.txnSetVersion()
    //     0xab67f0: bl              #0xab6808  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.txnSetVersion
    // 0xab67f4: LeaveFrame
    //     0xab67f4: mov             SP, fp
    //     0xab67f8: ldp             fp, lr, [SP], #0x10
    // 0xab67fc: ret
    //     0xab67fc: ret             
    // 0xab6800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6800: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6804: b               #0xab67dc
  }
  [closure] static Future<void> __onDatabaseDowngradeDelete(dynamic, Database, int, int) {
    // ** addr: 0xab6cec, size: 0x38
    // 0xab6cec: EnterFrame
    //     0xab6cec: stp             fp, lr, [SP, #-0x10]!
    //     0xab6cf0: mov             fp, SP
    // 0xab6cf4: CheckStackOverflow
    //     0xab6cf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6cf8: cmp             SP, x16
    //     0xab6cfc: b.ls            #0xab6d1c
    // 0xab6d00: ldr             x1, [fp, #0x20]
    // 0xab6d04: ldr             x2, [fp, #0x18]
    // 0xab6d08: ldr             x3, [fp, #0x10]
    // 0xab6d0c: r0 = onFetchFailure()
    //     0xab6d0c: bl              #0x7e5f5c  ; [package:nuonline/app/modules/ziarah/controllers/ziarah_regency_controller.dart] ZiarahRegencyController::onFetchFailure
    // 0xab6d10: LeaveFrame
    //     0xab6d10: mov             SP, fp
    //     0xab6d14: ldp             fp, lr, [SP], #0x10
    // 0xab6d18: ret
    //     0xab6d18: ret             
    // 0xab6d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6d20: b               #0xab6d00
  }
  static (dynamic, Database, int, int) => void onDatabaseDowngradeDelete() {
    // ** addr: 0xab6d24, size: 0xc
    // 0xab6d24: r0 = Closure: (Database, int, int) => Future<void> from Function '__onDatabaseDowngradeDelete@1097226320': static.
    //     0xab6d24: add             x0, PP, #0x43, lsl #12  ; [pp+0x43130] Closure: (Database, int, int) => Future<void> from Function '__onDatabaseDowngradeDelete@1097226320': static. (0x7e54fb4b6cec)
    //     0xab6d28: ldr             x0, [x0, #0x130]
    // 0xab6d2c: ret
    //     0xab6d2c: ret             
  }
}

// class id: 482, size: 0x8, field offset: 0x8
abstract class OpenDatabaseOptions extends Object {

  factory _ OpenDatabaseOptions(/* No info */) {
    // ** addr: 0xab6f0c, size: 0x4c
    // 0xab6f0c: EnterFrame
    //     0xab6f0c: stp             fp, lr, [SP, #-0x10]!
    //     0xab6f10: mov             fp, SP
    // 0xab6f14: AllocStack(0x10)
    //     0xab6f14: sub             SP, SP, #0x10
    // 0xab6f18: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xab6f18: stur            x2, [fp, #-8]
    //     0xab6f1c: stur            x3, [fp, #-0x10]
    // 0xab6f20: r0 = SqfliteOpenDatabaseOptions()
    //     0xab6f20: bl              #0xab6f58  ; AllocateSqfliteOpenDatabaseOptionsStub -> SqfliteOpenDatabaseOptions (size=0x2c)
    // 0xab6f24: r1 = 3
    //     0xab6f24: movz            x1, #0x3
    // 0xab6f28: StoreField: r0->field_7 = r1
    //     0xab6f28: stur            x1, [x0, #7]
    // 0xab6f2c: ldur            x1, [fp, #-8]
    // 0xab6f30: StoreField: r0->field_13 = r1
    //     0xab6f30: stur            w1, [x0, #0x13]
    // 0xab6f34: ldur            x1, [fp, #-0x10]
    // 0xab6f38: ArrayStore: r0[0] = r1  ; List_4
    //     0xab6f38: stur            w1, [x0, #0x17]
    // 0xab6f3c: r1 = false
    //     0xab6f3c: add             x1, NULL, #0x30  ; false
    // 0xab6f40: StoreField: r0->field_23 = r1
    //     0xab6f40: stur            w1, [x0, #0x23]
    // 0xab6f44: r1 = true
    //     0xab6f44: add             x1, NULL, #0x20  ; true
    // 0xab6f48: StoreField: r0->field_27 = r1
    //     0xab6f48: stur            w1, [x0, #0x27]
    // 0xab6f4c: LeaveFrame
    //     0xab6f4c: mov             SP, fp
    //     0xab6f50: ldp             fp, lr, [SP], #0x10
    // 0xab6f54: ret
    //     0xab6f54: ret             
  }
}

// class id: 483, size: 0x8, field offset: 0x8
abstract class Database extends Object
    implements DatabaseExecutor {
}

// class id: 484, size: 0x8, field offset: 0x8
abstract class Transaction extends Object
    implements DatabaseExecutor {
}

// class id: 485, size: 0x8, field offset: 0x8
abstract class DatabaseExecutor extends Object {
}

// class id: 486, size: 0x8, field offset: 0x8
abstract class DatabaseFactory extends Object {
}
