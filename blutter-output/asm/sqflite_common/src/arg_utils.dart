// lib: , url: package:sqflite_common/src/arg_utils.dart

// class id: 1051149, size: 0x8
class :: {

  static _ argumentsToString(/* No info */) {
    // ** addr: 0xc40f1c, size: 0xfc
    // 0xc40f1c: EnterFrame
    //     0xc40f1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc40f20: mov             fp, SP
    // 0xc40f24: AllocStack(0x28)
    //     0xc40f24: sub             SP, SP, #0x28
    // 0xc40f28: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc40f28: mov             x0, x1
    //     0xc40f2c: stur            x1, [fp, #-8]
    // 0xc40f30: CheckStackOverflow
    //     0xc40f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc40f34: cmp             SP, x16
    //     0xc40f38: b.ls            #0xc41010
    // 0xc40f3c: r1 = Null
    //     0xc40f3c: mov             x1, NULL
    // 0xc40f40: r2 = 6
    //     0xc40f40: movz            x2, #0x6
    // 0xc40f44: r0 = AllocateArray()
    //     0xc40f44: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc40f48: stur            x0, [fp, #-0x10]
    // 0xc40f4c: r16 = "["
    //     0xc40f4c: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xc40f50: StoreField: r0->field_f = r16
    //     0xc40f50: stur            w16, [x0, #0xf]
    // 0xc40f54: r1 = Function '<anonymous closure>': static.
    //     0xc40f54: add             x1, PP, #0x4c, lsl #12  ; [pp+0x4cf40] AnonymousClosure: static (0xc41018), in [package:sqflite_common/src/arg_utils.dart] ::argumentsToString (0xc40f1c)
    //     0xc40f58: ldr             x1, [x1, #0xf40]
    // 0xc40f5c: r2 = Null
    //     0xc40f5c: mov             x2, NULL
    // 0xc40f60: r0 = AllocateClosure()
    //     0xc40f60: bl              #0xec1630  ; AllocateClosureStub
    // 0xc40f64: mov             x1, x0
    // 0xc40f68: ldur            x0, [fp, #-8]
    // 0xc40f6c: r2 = LoadClassIdInstr(r0)
    //     0xc40f6c: ldur            x2, [x0, #-1]
    //     0xc40f70: ubfx            x2, x2, #0xc, #0x14
    // 0xc40f74: r16 = <String?>
    //     0xc40f74: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xc40f78: stp             x0, x16, [SP, #8]
    // 0xc40f7c: str             x1, [SP]
    // 0xc40f80: mov             x0, x2
    // 0xc40f84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc40f84: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc40f88: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xc40f88: movz            x17, #0xf28c
    //     0xc40f8c: add             lr, x0, x17
    //     0xc40f90: ldr             lr, [x21, lr, lsl #3]
    //     0xc40f94: blr             lr
    // 0xc40f98: r1 = LoadClassIdInstr(r0)
    //     0xc40f98: ldur            x1, [x0, #-1]
    //     0xc40f9c: ubfx            x1, x1, #0xc, #0x14
    // 0xc40fa0: r16 = ", "
    //     0xc40fa0: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xc40fa4: str             x16, [SP]
    // 0xc40fa8: mov             x16, x0
    // 0xc40fac: mov             x0, x1
    // 0xc40fb0: mov             x1, x16
    // 0xc40fb4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xc40fb4: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xc40fb8: r0 = GDT[cid_x0 + 0xf18c]()
    //     0xc40fb8: movz            x17, #0xf18c
    //     0xc40fbc: add             lr, x0, x17
    //     0xc40fc0: ldr             lr, [x21, lr, lsl #3]
    //     0xc40fc4: blr             lr
    // 0xc40fc8: ldur            x1, [fp, #-0x10]
    // 0xc40fcc: ArrayStore: r1[1] = r0  ; List_4
    //     0xc40fcc: add             x25, x1, #0x13
    //     0xc40fd0: str             w0, [x25]
    //     0xc40fd4: tbz             w0, #0, #0xc40ff0
    //     0xc40fd8: ldurb           w16, [x1, #-1]
    //     0xc40fdc: ldurb           w17, [x0, #-1]
    //     0xc40fe0: and             x16, x17, x16, lsr #2
    //     0xc40fe4: tst             x16, HEAP, lsr #32
    //     0xc40fe8: b.eq            #0xc40ff0
    //     0xc40fec: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc40ff0: ldur            x0, [fp, #-0x10]
    // 0xc40ff4: r16 = "]"
    //     0xc40ff4: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc40ff8: ArrayStore: r0[0] = r16  ; List_4
    //     0xc40ff8: stur            w16, [x0, #0x17]
    // 0xc40ffc: str             x0, [SP]
    // 0xc41000: r0 = _interpolate()
    //     0xc41000: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41004: LeaveFrame
    //     0xc41004: mov             SP, fp
    //     0xc41008: ldp             fp, lr, [SP], #0x10
    // 0xc4100c: ret
    //     0xc4100c: ret             
    // 0xc41010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41010: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41014: b               #0xc40f3c
  }
  [closure] static String? <anonymous closure>(dynamic, Object?) {
    // ** addr: 0xc41018, size: 0x30
    // 0xc41018: EnterFrame
    //     0xc41018: stp             fp, lr, [SP, #-0x10]!
    //     0xc4101c: mov             fp, SP
    // 0xc41020: CheckStackOverflow
    //     0xc41020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41024: cmp             SP, x16
    //     0xc41028: b.ls            #0xc41040
    // 0xc4102c: ldr             x1, [fp, #0x10]
    // 0xc41030: r0 = argumentToString()
    //     0xc41030: bl              #0xc41048  ; [package:sqflite_common/src/arg_utils.dart] ::argumentToString
    // 0xc41034: LeaveFrame
    //     0xc41034: mov             SP, fp
    //     0xc41038: ldp             fp, lr, [SP], #0x10
    // 0xc4103c: ret
    //     0xc4103c: ret             
    // 0xc41040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41044: b               #0xc4102c
  }
  static _ argumentToString(/* No info */) {
    // ** addr: 0xc41048, size: 0x98
    // 0xc41048: EnterFrame
    //     0xc41048: stp             fp, lr, [SP, #-0x10]!
    //     0xc4104c: mov             fp, SP
    // 0xc41050: AllocStack(0x10)
    //     0xc41050: sub             SP, SP, #0x10
    // 0xc41054: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc41054: mov             x0, x1
    //     0xc41058: stur            x1, [fp, #-8]
    // 0xc4105c: CheckStackOverflow
    //     0xc4105c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41060: cmp             SP, x16
    //     0xc41064: b.ls            #0xc410d8
    // 0xc41068: r1 = 60
    //     0xc41068: movz            x1, #0x3c
    // 0xc4106c: branchIfSmi(r0, 0xc41078)
    //     0xc4106c: tbz             w0, #0, #0xc41078
    // 0xc41070: r1 = LoadClassIdInstr(r0)
    //     0xc41070: ldur            x1, [x0, #-1]
    //     0xc41074: ubfx            x1, x1, #0xc, #0x14
    // 0xc41078: sub             x16, x1, #0x74
    // 0xc4107c: cmp             x16, #3
    // 0xc41080: b.hi            #0xc410c4
    // 0xc41084: r1 = Null
    //     0xc41084: mov             x1, NULL
    // 0xc41088: r2 = 6
    //     0xc41088: movz            x2, #0x6
    // 0xc4108c: r0 = AllocateArray()
    //     0xc4108c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41090: r16 = "Blob("
    //     0xc41090: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cf48] "Blob("
    //     0xc41094: ldr             x16, [x16, #0xf48]
    // 0xc41098: StoreField: r0->field_f = r16
    //     0xc41098: stur            w16, [x0, #0xf]
    // 0xc4109c: ldur            x1, [fp, #-8]
    // 0xc410a0: LoadField: r2 = r1->field_13
    //     0xc410a0: ldur            w2, [x1, #0x13]
    // 0xc410a4: StoreField: r0->field_13 = r2
    //     0xc410a4: stur            w2, [x0, #0x13]
    // 0xc410a8: r16 = ")"
    //     0xc410a8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc410ac: ArrayStore: r0[0] = r16  ; List_4
    //     0xc410ac: stur            w16, [x0, #0x17]
    // 0xc410b0: str             x0, [SP]
    // 0xc410b4: r0 = _interpolate()
    //     0xc410b4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc410b8: LeaveFrame
    //     0xc410b8: mov             SP, fp
    //     0xc410bc: ldp             fp, lr, [SP], #0x10
    // 0xc410c0: ret
    //     0xc410c0: ret             
    // 0xc410c4: mov             x1, x0
    // 0xc410c8: r0 = _argumentToStringTruncate()
    //     0xc410c8: bl              #0xc410e0  ; [package:sqflite_common/src/arg_utils.dart] ::_argumentToStringTruncate
    // 0xc410cc: LeaveFrame
    //     0xc410cc: mov             SP, fp
    //     0xc410d0: ldp             fp, lr, [SP], #0x10
    // 0xc410d4: ret
    //     0xc410d4: ret             
    // 0xc410d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc410d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc410dc: b               #0xc41068
  }
  static _ _argumentToStringTruncate(/* No info */) {
    // ** addr: 0xc410e0, size: 0xcc
    // 0xc410e0: EnterFrame
    //     0xc410e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc410e4: mov             fp, SP
    // 0xc410e8: AllocStack(0x10)
    //     0xc410e8: sub             SP, SP, #0x10
    // 0xc410ec: CheckStackOverflow
    //     0xc410ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc410f0: cmp             SP, x16
    //     0xc410f4: b.ls            #0xc411a4
    // 0xc410f8: cmp             w1, NULL
    // 0xc410fc: b.ne            #0xc41110
    // 0xc41100: r0 = Null
    //     0xc41100: mov             x0, NULL
    // 0xc41104: LeaveFrame
    //     0xc41104: mov             SP, fp
    //     0xc41108: ldp             fp, lr, [SP], #0x10
    // 0xc4110c: ret
    //     0xc4110c: ret             
    // 0xc41110: r0 = 60
    //     0xc41110: movz            x0, #0x3c
    // 0xc41114: branchIfSmi(r1, 0xc41120)
    //     0xc41114: tbz             w1, #0, #0xc41120
    // 0xc41118: r0 = LoadClassIdInstr(r1)
    //     0xc41118: ldur            x0, [x1, #-1]
    //     0xc4111c: ubfx            x0, x0, #0xc, #0x14
    // 0xc41120: str             x1, [SP]
    // 0xc41124: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc41124: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc41128: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xc41128: movz            x17, #0x2b03
    //     0xc4112c: add             lr, x0, x17
    //     0xc41130: ldr             lr, [x21, lr, lsl #3]
    //     0xc41134: blr             lr
    // 0xc41138: LoadField: r1 = r0->field_7
    //     0xc41138: ldur            w1, [x0, #7]
    // 0xc4113c: r2 = LoadInt32Instr(r1)
    //     0xc4113c: sbfx            x2, x1, #1, #0x1f
    // 0xc41140: cmp             x2, #0x32
    // 0xc41144: b.le            #0xc41198
    // 0xc41148: r16 = 100
    //     0xc41148: movz            x16, #0x64
    // 0xc4114c: str             x16, [SP]
    // 0xc41150: mov             x1, x0
    // 0xc41154: r2 = 0
    //     0xc41154: movz            x2, #0
    // 0xc41158: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc41158: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc4115c: r0 = substring()
    //     0xc4115c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xc41160: r1 = Null
    //     0xc41160: mov             x1, NULL
    // 0xc41164: r2 = 4
    //     0xc41164: movz            x2, #0x4
    // 0xc41168: stur            x0, [fp, #-8]
    // 0xc4116c: r0 = AllocateArray()
    //     0xc4116c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41170: mov             x1, x0
    // 0xc41174: ldur            x0, [fp, #-8]
    // 0xc41178: StoreField: r1->field_f = r0
    //     0xc41178: stur            w0, [x1, #0xf]
    // 0xc4117c: r16 = "..."
    //     0xc4117c: ldr             x16, [PP, #0xb00]  ; [pp+0xb00] "..."
    // 0xc41180: StoreField: r1->field_13 = r16
    //     0xc41180: stur            w16, [x1, #0x13]
    // 0xc41184: str             x1, [SP]
    // 0xc41188: r0 = _interpolate()
    //     0xc41188: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4118c: LeaveFrame
    //     0xc4118c: mov             SP, fp
    //     0xc41190: ldp             fp, lr, [SP], #0x10
    // 0xc41194: ret
    //     0xc41194: ret             
    // 0xc41198: LeaveFrame
    //     0xc41198: mov             SP, fp
    //     0xc4119c: ldp             fp, lr, [SP], #0x10
    // 0xc411a0: ret
    //     0xc411a0: ret             
    // 0xc411a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc411a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc411a8: b               #0xc410f8
  }
}
