// lib: , url: package:sqflite_common/src/collection_utils.dart

// class id: 1051150, size: 0x8
class :: {

  static _ queryResultToList(/* No info */) {
    // ** addr: 0xab5b1c, size: 0x230
    // 0xab5b1c: EnterFrame
    //     0xab5b1c: stp             fp, lr, [SP, #-0x10]!
    //     0xab5b20: mov             fp, SP
    // 0xab5b24: AllocStack(0x10)
    //     0xab5b24: sub             SP, SP, #0x10
    // 0xab5b28: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xab5b28: mov             x3, x1
    //     0xab5b2c: stur            x1, [fp, #-8]
    // 0xab5b30: CheckStackOverflow
    //     0xab5b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5b34: cmp             SP, x16
    //     0xab5b38: b.ls            #0xab5d44
    // 0xab5b3c: mov             x0, x3
    // 0xab5b40: r2 = Null
    //     0xab5b40: mov             x2, NULL
    // 0xab5b44: r1 = Null
    //     0xab5b44: mov             x1, NULL
    // 0xab5b48: cmp             w0, NULL
    // 0xab5b4c: b.eq            #0xab5be4
    // 0xab5b50: branchIfSmi(r0, 0xab5be4)
    //     0xab5b50: tbz             w0, #0, #0xab5be4
    // 0xab5b54: r3 = LoadClassIdInstr(r0)
    //     0xab5b54: ldur            x3, [x0, #-1]
    //     0xab5b58: ubfx            x3, x3, #0xc, #0x14
    // 0xab5b5c: r17 = 6717
    //     0xab5b5c: movz            x17, #0x1a3d
    // 0xab5b60: cmp             x3, x17
    // 0xab5b64: b.eq            #0xab5bec
    // 0xab5b68: r4 = LoadClassIdInstr(r0)
    //     0xab5b68: ldur            x4, [x0, #-1]
    //     0xab5b6c: ubfx            x4, x4, #0xc, #0x14
    // 0xab5b70: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xab5b74: ldr             x3, [x3, #0x18]
    // 0xab5b78: ldr             x3, [x3, x4, lsl #3]
    // 0xab5b7c: LoadField: r3 = r3->field_2b
    //     0xab5b7c: ldur            w3, [x3, #0x2b]
    // 0xab5b80: DecompressPointer r3
    //     0xab5b80: add             x3, x3, HEAP, lsl #32
    // 0xab5b84: cmp             w3, NULL
    // 0xab5b88: b.eq            #0xab5be4
    // 0xab5b8c: LoadField: r3 = r3->field_f
    //     0xab5b8c: ldur            w3, [x3, #0xf]
    // 0xab5b90: lsr             x3, x3, #3
    // 0xab5b94: r17 = 6717
    //     0xab5b94: movz            x17, #0x1a3d
    // 0xab5b98: cmp             x3, x17
    // 0xab5b9c: b.eq            #0xab5bec
    // 0xab5ba0: r3 = SubtypeTestCache
    //     0xab5ba0: add             x3, PP, #0x43, lsl #12  ; [pp+0x43190] SubtypeTestCache
    //     0xab5ba4: ldr             x3, [x3, #0x190]
    // 0xab5ba8: r30 = Subtype1TestCacheStub
    //     0xab5ba8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xab5bac: LoadField: r30 = r30->field_7
    //     0xab5bac: ldur            lr, [lr, #7]
    // 0xab5bb0: blr             lr
    // 0xab5bb4: cmp             w7, NULL
    // 0xab5bb8: b.eq            #0xab5bc4
    // 0xab5bbc: tbnz            w7, #4, #0xab5be4
    // 0xab5bc0: b               #0xab5bec
    // 0xab5bc4: r8 = Map
    //     0xab5bc4: add             x8, PP, #0x43, lsl #12  ; [pp+0x43198] Type: Map
    //     0xab5bc8: ldr             x8, [x8, #0x198]
    // 0xab5bcc: r3 = SubtypeTestCache
    //     0xab5bcc: add             x3, PP, #0x43, lsl #12  ; [pp+0x431a0] SubtypeTestCache
    //     0xab5bd0: ldr             x3, [x3, #0x1a0]
    // 0xab5bd4: r30 = InstanceOfStub
    //     0xab5bd4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xab5bd8: LoadField: r30 = r30->field_7
    //     0xab5bd8: ldur            lr, [lr, #7]
    // 0xab5bdc: blr             lr
    // 0xab5be0: b               #0xab5bf0
    // 0xab5be4: r0 = false
    //     0xab5be4: add             x0, NULL, #0x30  ; false
    // 0xab5be8: b               #0xab5bf0
    // 0xab5bec: r0 = true
    //     0xab5bec: add             x0, NULL, #0x20  ; true
    // 0xab5bf0: tbnz            w0, #4, #0xab5c08
    // 0xab5bf4: ldur            x1, [fp, #-8]
    // 0xab5bf8: r0 = queryResultSetFromMap()
    //     0xab5bf8: bl              #0xab5d58  ; [package:sqflite_common/src/collection_utils.dart] ::queryResultSetFromMap
    // 0xab5bfc: LeaveFrame
    //     0xab5bfc: mov             SP, fp
    //     0xab5c00: ldp             fp, lr, [SP], #0x10
    // 0xab5c04: ret
    //     0xab5c04: ret             
    // 0xab5c08: ldur            x0, [fp, #-8]
    // 0xab5c0c: r2 = Null
    //     0xab5c0c: mov             x2, NULL
    // 0xab5c10: r1 = Null
    //     0xab5c10: mov             x1, NULL
    // 0xab5c14: cmp             w0, NULL
    // 0xab5c18: b.eq            #0xab5cbc
    // 0xab5c1c: branchIfSmi(r0, 0xab5cbc)
    //     0xab5c1c: tbz             w0, #0, #0xab5cbc
    // 0xab5c20: r3 = LoadClassIdInstr(r0)
    //     0xab5c20: ldur            x3, [x0, #-1]
    //     0xab5c24: ubfx            x3, x3, #0xc, #0x14
    // 0xab5c28: r17 = 6718
    //     0xab5c28: movz            x17, #0x1a3e
    // 0xab5c2c: cmp             x3, x17
    // 0xab5c30: b.eq            #0xab5cc4
    // 0xab5c34: sub             x3, x3, #0x5a
    // 0xab5c38: cmp             x3, #2
    // 0xab5c3c: b.ls            #0xab5cc4
    // 0xab5c40: r4 = LoadClassIdInstr(r0)
    //     0xab5c40: ldur            x4, [x0, #-1]
    //     0xab5c44: ubfx            x4, x4, #0xc, #0x14
    // 0xab5c48: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xab5c4c: ldr             x3, [x3, #0x18]
    // 0xab5c50: ldr             x3, [x3, x4, lsl #3]
    // 0xab5c54: LoadField: r3 = r3->field_2b
    //     0xab5c54: ldur            w3, [x3, #0x2b]
    // 0xab5c58: DecompressPointer r3
    //     0xab5c58: add             x3, x3, HEAP, lsl #32
    // 0xab5c5c: cmp             w3, NULL
    // 0xab5c60: b.eq            #0xab5cbc
    // 0xab5c64: LoadField: r3 = r3->field_f
    //     0xab5c64: ldur            w3, [x3, #0xf]
    // 0xab5c68: lsr             x3, x3, #3
    // 0xab5c6c: r17 = 6718
    //     0xab5c6c: movz            x17, #0x1a3e
    // 0xab5c70: cmp             x3, x17
    // 0xab5c74: b.eq            #0xab5cc4
    // 0xab5c78: r3 = SubtypeTestCache
    //     0xab5c78: add             x3, PP, #0x43, lsl #12  ; [pp+0x431a8] SubtypeTestCache
    //     0xab5c7c: ldr             x3, [x3, #0x1a8]
    // 0xab5c80: r30 = Subtype1TestCacheStub
    //     0xab5c80: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xab5c84: LoadField: r30 = r30->field_7
    //     0xab5c84: ldur            lr, [lr, #7]
    // 0xab5c88: blr             lr
    // 0xab5c8c: cmp             w7, NULL
    // 0xab5c90: b.eq            #0xab5c9c
    // 0xab5c94: tbnz            w7, #4, #0xab5cbc
    // 0xab5c98: b               #0xab5cc4
    // 0xab5c9c: r8 = List
    //     0xab5c9c: add             x8, PP, #0x43, lsl #12  ; [pp+0x431b0] Type: List
    //     0xab5ca0: ldr             x8, [x8, #0x1b0]
    // 0xab5ca4: r3 = SubtypeTestCache
    //     0xab5ca4: add             x3, PP, #0x43, lsl #12  ; [pp+0x431b8] SubtypeTestCache
    //     0xab5ca8: ldr             x3, [x3, #0x1b8]
    // 0xab5cac: r30 = InstanceOfStub
    //     0xab5cac: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xab5cb0: LoadField: r30 = r30->field_7
    //     0xab5cb0: ldur            lr, [lr, #7]
    // 0xab5cb4: blr             lr
    // 0xab5cb8: b               #0xab5cc8
    // 0xab5cbc: r0 = false
    //     0xab5cbc: add             x0, NULL, #0x30  ; false
    // 0xab5cc0: b               #0xab5cc8
    // 0xab5cc4: r0 = true
    //     0xab5cc4: add             x0, NULL, #0x20  ; true
    // 0xab5cc8: tbnz            w0, #4, #0xab5cf8
    // 0xab5ccc: ldur            x0, [fp, #-8]
    // 0xab5cd0: r1 = <Map<String, Object?>>
    //     0xab5cd0: add             x1, PP, #0xf, lsl #12  ; [pp+0xfba0] TypeArguments: <Map<String, Object?>>
    //     0xab5cd4: ldr             x1, [x1, #0xba0]
    // 0xab5cd8: r0 = Rows()
    //     0xab5cd8: bl              #0xab5d4c  ; AllocateRowsStub -> Rows (size=0x10)
    // 0xab5cdc: mov             x1, x0
    // 0xab5ce0: ldur            x0, [fp, #-8]
    // 0xab5ce4: StoreField: r1->field_b = r0
    //     0xab5ce4: stur            w0, [x1, #0xb]
    // 0xab5ce8: mov             x0, x1
    // 0xab5cec: LeaveFrame
    //     0xab5cec: mov             SP, fp
    //     0xab5cf0: ldp             fp, lr, [SP], #0x10
    // 0xab5cf4: ret
    //     0xab5cf4: ret             
    // 0xab5cf8: ldur            x0, [fp, #-8]
    // 0xab5cfc: r1 = Null
    //     0xab5cfc: mov             x1, NULL
    // 0xab5d00: r2 = 4
    //     0xab5d00: movz            x2, #0x4
    // 0xab5d04: r0 = AllocateArray()
    //     0xab5d04: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab5d08: r16 = "Unsupported queryResult type "
    //     0xab5d08: add             x16, PP, #0x43, lsl #12  ; [pp+0x431c0] "Unsupported queryResult type "
    //     0xab5d0c: ldr             x16, [x16, #0x1c0]
    // 0xab5d10: StoreField: r0->field_f = r16
    //     0xab5d10: stur            w16, [x0, #0xf]
    // 0xab5d14: ldur            x1, [fp, #-8]
    // 0xab5d18: StoreField: r0->field_13 = r1
    //     0xab5d18: stur            w1, [x0, #0x13]
    // 0xab5d1c: str             x0, [SP]
    // 0xab5d20: r0 = _interpolate()
    //     0xab5d20: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab5d24: stur            x0, [fp, #-8]
    // 0xab5d28: r0 = UnsupportedError()
    //     0xab5d28: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0xab5d2c: mov             x1, x0
    // 0xab5d30: ldur            x0, [fp, #-8]
    // 0xab5d34: StoreField: r1->field_b = r0
    //     0xab5d34: stur            w0, [x1, #0xb]
    // 0xab5d38: mov             x0, x1
    // 0xab5d3c: r0 = Throw()
    //     0xab5d3c: bl              #0xec04b8  ; ThrowStub
    // 0xab5d40: brk             #0
    // 0xab5d44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5d44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab5d48: b               #0xab5b3c
  }
  static _ queryResultSetFromMap(/* No info */) {
    // ** addr: 0xab5d58, size: 0x118
    // 0xab5d58: EnterFrame
    //     0xab5d58: stp             fp, lr, [SP, #-0x10]!
    //     0xab5d5c: mov             fp, SP
    // 0xab5d60: AllocStack(0x10)
    //     0xab5d60: sub             SP, SP, #0x10
    // 0xab5d64: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xab5d64: mov             x3, x1
    //     0xab5d68: stur            x1, [fp, #-8]
    // 0xab5d6c: CheckStackOverflow
    //     0xab5d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5d70: cmp             SP, x16
    //     0xab5d74: b.ls            #0xab5e68
    // 0xab5d78: r0 = LoadClassIdInstr(r3)
    //     0xab5d78: ldur            x0, [x3, #-1]
    //     0xab5d7c: ubfx            x0, x0, #0xc, #0x14
    // 0xab5d80: mov             x1, x3
    // 0xab5d84: r2 = "columns"
    //     0xab5d84: add             x2, PP, #0x43, lsl #12  ; [pp+0x431c8] "columns"
    //     0xab5d88: ldr             x2, [x2, #0x1c8]
    // 0xab5d8c: r0 = GDT[cid_x0 + -0x114]()
    //     0xab5d8c: sub             lr, x0, #0x114
    //     0xab5d90: ldr             lr, [x21, lr, lsl #3]
    //     0xab5d94: blr             lr
    // 0xab5d98: mov             x3, x0
    // 0xab5d9c: r2 = Null
    //     0xab5d9c: mov             x2, NULL
    // 0xab5da0: r1 = Null
    //     0xab5da0: mov             x1, NULL
    // 0xab5da4: stur            x3, [fp, #-0x10]
    // 0xab5da8: r4 = 60
    //     0xab5da8: movz            x4, #0x3c
    // 0xab5dac: branchIfSmi(r0, 0xab5db8)
    //     0xab5dac: tbz             w0, #0, #0xab5db8
    // 0xab5db0: r4 = LoadClassIdInstr(r0)
    //     0xab5db0: ldur            x4, [x0, #-1]
    //     0xab5db4: ubfx            x4, x4, #0xc, #0x14
    // 0xab5db8: sub             x4, x4, #0x5a
    // 0xab5dbc: cmp             x4, #2
    // 0xab5dc0: b.ls            #0xab5dd8
    // 0xab5dc4: r8 = List?
    //     0xab5dc4: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xab5dc8: ldr             x8, [x8, #0x140]
    // 0xab5dcc: r3 = Null
    //     0xab5dcc: add             x3, PP, #0x43, lsl #12  ; [pp+0x431d0] Null
    //     0xab5dd0: ldr             x3, [x3, #0x1d0]
    // 0xab5dd4: r0 = List?()
    //     0xab5dd4: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xab5dd8: ldur            x1, [fp, #-8]
    // 0xab5ddc: r0 = LoadClassIdInstr(r1)
    //     0xab5ddc: ldur            x0, [x1, #-1]
    //     0xab5de0: ubfx            x0, x0, #0xc, #0x14
    // 0xab5de4: r2 = "rows"
    //     0xab5de4: add             x2, PP, #0x43, lsl #12  ; [pp+0x431e0] "rows"
    //     0xab5de8: ldr             x2, [x2, #0x1e0]
    // 0xab5dec: r0 = GDT[cid_x0 + -0x114]()
    //     0xab5dec: sub             lr, x0, #0x114
    //     0xab5df0: ldr             lr, [x21, lr, lsl #3]
    //     0xab5df4: blr             lr
    // 0xab5df8: mov             x3, x0
    // 0xab5dfc: r2 = Null
    //     0xab5dfc: mov             x2, NULL
    // 0xab5e00: r1 = Null
    //     0xab5e00: mov             x1, NULL
    // 0xab5e04: stur            x3, [fp, #-8]
    // 0xab5e08: r4 = 60
    //     0xab5e08: movz            x4, #0x3c
    // 0xab5e0c: branchIfSmi(r0, 0xab5e18)
    //     0xab5e0c: tbz             w0, #0, #0xab5e18
    // 0xab5e10: r4 = LoadClassIdInstr(r0)
    //     0xab5e10: ldur            x4, [x0, #-1]
    //     0xab5e14: ubfx            x4, x4, #0xc, #0x14
    // 0xab5e18: sub             x4, x4, #0x5a
    // 0xab5e1c: cmp             x4, #2
    // 0xab5e20: b.ls            #0xab5e38
    // 0xab5e24: r8 = List?
    //     0xab5e24: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xab5e28: ldr             x8, [x8, #0x140]
    // 0xab5e2c: r3 = Null
    //     0xab5e2c: add             x3, PP, #0x43, lsl #12  ; [pp+0x431e8] Null
    //     0xab5e30: ldr             x3, [x3, #0x1e8]
    // 0xab5e34: r0 = List?()
    //     0xab5e34: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xab5e38: r1 = <Map<String, Object?>>
    //     0xab5e38: add             x1, PP, #0xf, lsl #12  ; [pp+0xfba0] TypeArguments: <Map<String, Object?>>
    //     0xab5e3c: ldr             x1, [x1, #0xba0]
    // 0xab5e40: r0 = QueryResultSet()
    //     0xab5e40: bl              #0xab60a4  ; AllocateQueryResultSetStub -> QueryResultSet (size=0x1c)
    // 0xab5e44: mov             x1, x0
    // 0xab5e48: ldur            x2, [fp, #-0x10]
    // 0xab5e4c: ldur            x3, [fp, #-8]
    // 0xab5e50: stur            x0, [fp, #-8]
    // 0xab5e54: r0 = QueryResultSet()
    //     0xab5e54: bl              #0xab5e70  ; [package:sqflite_common/src/collection_utils.dart] QueryResultSet::QueryResultSet
    // 0xab5e58: ldur            x0, [fp, #-8]
    // 0xab5e5c: LeaveFrame
    //     0xab5e5c: mov             SP, fp
    //     0xab5e60: ldp             fp, lr, [SP], #0x10
    // 0xab5e64: ret
    //     0xab5e64: ret             
    // 0xab5e68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5e68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab5e6c: b               #0xab5d78
  }
}

// class id: 6523, size: 0x14, field offset: 0xc
class QueryRow extends MapBase<dynamic, dynamic> {

  dynamic [](QueryRow, Object?) {
    // ** addr: 0x66e1a8, size: 0x4c
    // 0x66e1a8: EnterFrame
    //     0x66e1a8: stp             fp, lr, [SP, #-0x10]!
    //     0x66e1ac: mov             fp, SP
    // 0x66e1b0: CheckStackOverflow
    //     0x66e1b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66e1b4: cmp             SP, x16
    //     0x66e1b8: b.ls            #0x66e1d4
    // 0x66e1bc: ldr             x1, [fp, #0x18]
    // 0x66e1c0: ldr             x2, [fp, #0x10]
    // 0x66e1c4: r0 = []()
    //     0x66e1c4: bl              #0xd36f74  ; [package:sqflite_common/src/collection_utils.dart] QueryRow::[]
    // 0x66e1c8: LeaveFrame
    //     0x66e1c8: mov             SP, fp
    //     0x66e1cc: ldp             fp, lr, [SP], #0x10
    // 0x66e1d0: ret
    //     0x66e1d0: ret             
    // 0x66e1d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66e1d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66e1d8: b               #0x66e1bc
  }
  dynamic remove(QueryRow, Object?) {
    // ** addr: 0xd07a38, size: 0x28
    // 0xd07a38: EnterFrame
    //     0xd07a38: stp             fp, lr, [SP, #-0x10]!
    //     0xd07a3c: mov             fp, SP
    // 0xd07a40: r0 = UnsupportedError()
    //     0xd07a40: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0xd07a44: mov             x1, x0
    // 0xd07a48: r0 = "read-only"
    //     0xd07a48: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb80] "read-only"
    //     0xd07a4c: ldr             x0, [x0, #0xb80]
    // 0xd07a50: StoreField: r1->field_b = r0
    //     0xd07a50: stur            w0, [x1, #0xb]
    // 0xd07a54: mov             x0, x1
    // 0xd07a58: r0 = Throw()
    //     0xd07a58: bl              #0xec04b8  ; ThrowStub
    // 0xd07a5c: brk             #0
  }
  get _ keys(/* No info */) {
    // ** addr: 0xd12e84, size: 0x38
    // 0xd12e84: EnterFrame
    //     0xd12e84: stp             fp, lr, [SP, #-0x10]!
    //     0xd12e88: mov             fp, SP
    // 0xd12e8c: CheckStackOverflow
    //     0xd12e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd12e90: cmp             SP, x16
    //     0xd12e94: b.ls            #0xd12eb4
    // 0xd12e98: LoadField: r0 = r1->field_b
    //     0xd12e98: ldur            w0, [x1, #0xb]
    // 0xd12e9c: DecompressPointer r0
    //     0xd12e9c: add             x0, x0, HEAP, lsl #32
    // 0xd12ea0: mov             x1, x0
    // 0xd12ea4: r0 = keys()
    //     0xd12ea4: bl              #0x6daeac  ; [package:sqflite_common/src/collection_utils.dart] QueryResultSet::keys
    // 0xd12ea8: LeaveFrame
    //     0xd12ea8: mov             SP, fp
    //     0xd12eac: ldp             fp, lr, [SP], #0x10
    // 0xd12eb0: ret
    //     0xd12eb0: ret             
    // 0xd12eb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd12eb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd12eb8: b               #0xd12e98
  }
  _ []=(/* No info */) {
    // ** addr: 0xd36a98, size: 0x68
    // 0xd36a98: EnterFrame
    //     0xd36a98: stp             fp, lr, [SP, #-0x10]!
    //     0xd36a9c: mov             fp, SP
    // 0xd36aa0: mov             x0, x2
    // 0xd36aa4: mov             x5, x1
    // 0xd36aa8: mov             x4, x2
    // 0xd36aac: r2 = Null
    //     0xd36aac: mov             x2, NULL
    // 0xd36ab0: r1 = Null
    //     0xd36ab0: mov             x1, NULL
    // 0xd36ab4: r4 = 60
    //     0xd36ab4: movz            x4, #0x3c
    // 0xd36ab8: branchIfSmi(r0, 0xd36ac4)
    //     0xd36ab8: tbz             w0, #0, #0xd36ac4
    // 0xd36abc: r4 = LoadClassIdInstr(r0)
    //     0xd36abc: ldur            x4, [x0, #-1]
    //     0xd36ac0: ubfx            x4, x4, #0xc, #0x14
    // 0xd36ac4: sub             x4, x4, #0x5e
    // 0xd36ac8: cmp             x4, #1
    // 0xd36acc: b.ls            #0xd36ae0
    // 0xd36ad0: r8 = String
    //     0xd36ad0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xd36ad4: r3 = Null
    //     0xd36ad4: add             x3, PP, #0x1f, lsl #12  ; [pp+0x1fb70] Null
    //     0xd36ad8: ldr             x3, [x3, #0xb70]
    // 0xd36adc: r0 = String()
    //     0xd36adc: bl              #0xed43b0  ; IsType_String_Stub
    // 0xd36ae0: r0 = UnsupportedError()
    //     0xd36ae0: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0xd36ae4: mov             x1, x0
    // 0xd36ae8: r0 = "read-only"
    //     0xd36ae8: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb80] "read-only"
    //     0xd36aec: ldr             x0, [x0, #0xb80]
    // 0xd36af0: StoreField: r1->field_b = r0
    //     0xd36af0: stur            w0, [x1, #0xb]
    // 0xd36af4: mov             x0, x1
    // 0xd36af8: r0 = Throw()
    //     0xd36af8: bl              #0xec04b8  ; ThrowStub
    // 0xd36afc: brk             #0
  }
  dynamic [](QueryRow, Object?) {
    // ** addr: 0xd36f74, size: 0xd0
    // 0xd36f74: EnterFrame
    //     0xd36f74: stp             fp, lr, [SP, #-0x10]!
    //     0xd36f78: mov             fp, SP
    // 0xd36f7c: AllocStack(0x20)
    //     0xd36f7c: sub             SP, SP, #0x20
    // 0xd36f80: SetupParameters(QueryRow this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd36f80: mov             x4, x1
    //     0xd36f84: mov             x3, x2
    //     0xd36f88: stur            x1, [fp, #-8]
    //     0xd36f8c: stur            x2, [fp, #-0x10]
    // 0xd36f90: CheckStackOverflow
    //     0xd36f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd36f94: cmp             SP, x16
    //     0xd36f98: b.ls            #0xd3703c
    // 0xd36f9c: mov             x0, x3
    // 0xd36fa0: r2 = Null
    //     0xd36fa0: mov             x2, NULL
    // 0xd36fa4: r1 = Null
    //     0xd36fa4: mov             x1, NULL
    // 0xd36fa8: r4 = 60
    //     0xd36fa8: movz            x4, #0x3c
    // 0xd36fac: branchIfSmi(r0, 0xd36fb8)
    //     0xd36fac: tbz             w0, #0, #0xd36fb8
    // 0xd36fb0: r4 = LoadClassIdInstr(r0)
    //     0xd36fb0: ldur            x4, [x0, #-1]
    //     0xd36fb4: ubfx            x4, x4, #0xc, #0x14
    // 0xd36fb8: sub             x4, x4, #0x5e
    // 0xd36fbc: cmp             x4, #1
    // 0xd36fc0: b.ls            #0xd36fd4
    // 0xd36fc4: r8 = String?
    //     0xd36fc4: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xd36fc8: r3 = Null
    //     0xd36fc8: add             x3, PP, #0x1f, lsl #12  ; [pp+0x1fb88] Null
    //     0xd36fcc: ldr             x3, [x3, #0xb88]
    // 0xd36fd0: r0 = String?()
    //     0xd36fd0: bl              #0x600324  ; IsType_String?_Stub
    // 0xd36fd4: ldur            x0, [fp, #-8]
    // 0xd36fd8: LoadField: r1 = r0->field_b
    //     0xd36fd8: ldur            w1, [x0, #0xb]
    // 0xd36fdc: DecompressPointer r1
    //     0xd36fdc: add             x1, x1, HEAP, lsl #32
    // 0xd36fe0: ldur            x2, [fp, #-0x10]
    // 0xd36fe4: r0 = columnIndex()
    //     0xd36fe4: bl              #0xd134a0  ; [package:sqflite_common/src/collection_utils.dart] QueryResultSet::columnIndex
    // 0xd36fe8: cmp             w0, NULL
    // 0xd36fec: b.eq            #0xd3702c
    // 0xd36ff0: ldur            x1, [fp, #-8]
    // 0xd36ff4: LoadField: r2 = r1->field_f
    //     0xd36ff4: ldur            w2, [x1, #0xf]
    // 0xd36ff8: DecompressPointer r2
    //     0xd36ff8: add             x2, x2, HEAP, lsl #32
    // 0xd36ffc: r1 = LoadClassIdInstr(r2)
    //     0xd36ffc: ldur            x1, [x2, #-1]
    //     0xd37000: ubfx            x1, x1, #0xc, #0x14
    // 0xd37004: stp             x0, x2, [SP]
    // 0xd37008: mov             x0, x1
    // 0xd3700c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd3700c: movz            x17, #0x3037
    //     0xd37010: movk            x17, #0x1, lsl #16
    //     0xd37014: add             lr, x0, x17
    //     0xd37018: ldr             lr, [x21, lr, lsl #3]
    //     0xd3701c: blr             lr
    // 0xd37020: LeaveFrame
    //     0xd37020: mov             SP, fp
    //     0xd37024: ldp             fp, lr, [SP], #0x10
    // 0xd37028: ret
    //     0xd37028: ret             
    // 0xd3702c: r0 = Null
    //     0xd3702c: mov             x0, NULL
    // 0xd37030: LeaveFrame
    //     0xd37030: mov             SP, fp
    //     0xd37034: ldp             fp, lr, [SP], #0x10
    // 0xd37038: ret
    //     0xd37038: ret             
    // 0xd3703c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3703c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd37040: b               #0xd36f9c
  }
}

// class id: 7320, size: 0x1c, field offset: 0xc
class QueryResultSet extends ListBase<dynamic> {

  late Map<String, int> _columnIndexMap; // offset: 0x18

  _ []=(/* No info */) {
    // ** addr: 0x66d550, size: 0x48
    // 0x66d550: EnterFrame
    //     0x66d550: stp             fp, lr, [SP, #-0x10]!
    //     0x66d554: mov             fp, SP
    // 0x66d558: ldr             x0, [fp, #0x10]
    // 0x66d55c: r2 = Null
    //     0x66d55c: mov             x2, NULL
    // 0x66d560: r1 = Null
    //     0x66d560: mov             x1, NULL
    // 0x66d564: r8 = Map<String, Object?>
    //     0x66d564: add             x8, PP, #0xc, lsl #12  ; [pp+0xca78] Type: Map<String, Object?>
    //     0x66d568: ldr             x8, [x8, #0xa78]
    // 0x66d56c: r3 = Null
    //     0x66d56c: add             x3, PP, #0x46, lsl #12  ; [pp+0x46cd8] Null
    //     0x66d570: ldr             x3, [x3, #0xcd8]
    // 0x66d574: r0 = Map<String, Object?>()
    //     0x66d574: bl              #0x621110  ; IsType_Map<String, Object?>_Stub
    // 0x66d578: r0 = UnsupportedError()
    //     0x66d578: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x66d57c: mov             x1, x0
    // 0x66d580: r0 = "read-only"
    //     0x66d580: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb80] "read-only"
    //     0x66d584: ldr             x0, [x0, #0xb80]
    // 0x66d588: StoreField: r1->field_b = r0
    //     0x66d588: stur            w0, [x1, #0xb]
    // 0x66d58c: mov             x0, x1
    // 0x66d590: r0 = Throw()
    //     0x66d590: bl              #0xec04b8  ; ThrowStub
    // 0x66d594: brk             #0
  }
  Map<String, Object?> [](QueryResultSet, int) {
    // ** addr: 0x66d5b0, size: 0x88
    // 0x66d5b0: EnterFrame
    //     0x66d5b0: stp             fp, lr, [SP, #-0x10]!
    //     0x66d5b4: mov             fp, SP
    // 0x66d5b8: AllocStack(0x10)
    //     0x66d5b8: sub             SP, SP, #0x10
    // 0x66d5bc: CheckStackOverflow
    //     0x66d5bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66d5c0: cmp             SP, x16
    //     0x66d5c4: b.ls            #0x66d618
    // 0x66d5c8: ldr             x0, [fp, #0x10]
    // 0x66d5cc: r2 = Null
    //     0x66d5cc: mov             x2, NULL
    // 0x66d5d0: r1 = Null
    //     0x66d5d0: mov             x1, NULL
    // 0x66d5d4: branchIfSmi(r0, 0x66d5fc)
    //     0x66d5d4: tbz             w0, #0, #0x66d5fc
    // 0x66d5d8: r4 = LoadClassIdInstr(r0)
    //     0x66d5d8: ldur            x4, [x0, #-1]
    //     0x66d5dc: ubfx            x4, x4, #0xc, #0x14
    // 0x66d5e0: sub             x4, x4, #0x3c
    // 0x66d5e4: cmp             x4, #1
    // 0x66d5e8: b.ls            #0x66d5fc
    // 0x66d5ec: r8 = int
    //     0x66d5ec: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x66d5f0: r3 = Null
    //     0x66d5f0: add             x3, PP, #0x46, lsl #12  ; [pp+0x46ce8] Null
    //     0x66d5f4: ldr             x3, [x3, #0xce8]
    // 0x66d5f8: r0 = int()
    //     0x66d5f8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x66d5fc: ldr             x16, [fp, #0x18]
    // 0x66d600: ldr             lr, [fp, #0x10]
    // 0x66d604: stp             lr, x16, [SP]
    // 0x66d608: r0 = []()
    //     0x66d608: bl              #0x66e0fc  ; [package:sqflite_common/src/collection_utils.dart] QueryResultSet::[]
    // 0x66d60c: LeaveFrame
    //     0x66d60c: mov             SP, fp
    //     0x66d610: ldp             fp, lr, [SP], #0x10
    // 0x66d614: ret
    //     0x66d614: ret             
    // 0x66d618: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66d618: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66d61c: b               #0x66d5c8
  }
  Map<String, Object?> [](QueryResultSet, int) {
    // ** addr: 0x66e0fc, size: 0x88
    // 0x66e0fc: EnterFrame
    //     0x66e0fc: stp             fp, lr, [SP, #-0x10]!
    //     0x66e100: mov             fp, SP
    // 0x66e104: AllocStack(0x18)
    //     0x66e104: sub             SP, SP, #0x18
    // 0x66e108: CheckStackOverflow
    //     0x66e108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66e10c: cmp             SP, x16
    //     0x66e110: b.ls            #0x66e178
    // 0x66e114: ldr             x1, [fp, #0x18]
    // 0x66e118: LoadField: r0 = r1->field_b
    //     0x66e118: ldur            w0, [x1, #0xb]
    // 0x66e11c: DecompressPointer r0
    //     0x66e11c: add             x0, x0, HEAP, lsl #32
    // 0x66e120: cmp             w0, NULL
    // 0x66e124: b.eq            #0x66e180
    // 0x66e128: r2 = LoadClassIdInstr(r0)
    //     0x66e128: ldur            x2, [x0, #-1]
    //     0x66e12c: ubfx            x2, x2, #0xc, #0x14
    // 0x66e130: ldr             x16, [fp, #0x10]
    // 0x66e134: stp             x16, x0, [SP]
    // 0x66e138: mov             x0, x2
    // 0x66e13c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x66e13c: movz            x17, #0x3037
    //     0x66e140: movk            x17, #0x1, lsl #16
    //     0x66e144: add             lr, x0, x17
    //     0x66e148: ldr             lr, [x21, lr, lsl #3]
    //     0x66e14c: blr             lr
    // 0x66e150: r1 = <String, dynamic>
    //     0x66e150: ldr             x1, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x66e154: stur            x0, [fp, #-8]
    // 0x66e158: r0 = QueryRow()
    //     0x66e158: bl              #0x66e184  ; AllocateQueryRowStub -> QueryRow (size=0x14)
    // 0x66e15c: ldr             x1, [fp, #0x18]
    // 0x66e160: StoreField: r0->field_b = r1
    //     0x66e160: stur            w1, [x0, #0xb]
    // 0x66e164: ldur            x1, [fp, #-8]
    // 0x66e168: StoreField: r0->field_f = r1
    //     0x66e168: stur            w1, [x0, #0xf]
    // 0x66e16c: LeaveFrame
    //     0x66e16c: mov             SP, fp
    //     0x66e170: ldp             fp, lr, [SP], #0x10
    // 0x66e174: ret
    //     0x66e174: ret             
    // 0x66e178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66e178: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66e17c: b               #0x66e114
    // 0x66e180: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x66e180: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ keys(/* No info */) {
    // ** addr: 0x6daeac, size: 0xa8
    // 0x6daeac: EnterFrame
    //     0x6daeac: stp             fp, lr, [SP, #-0x10]!
    //     0x6daeb0: mov             fp, SP
    // 0x6daeb4: AllocStack(0x8)
    //     0x6daeb4: sub             SP, SP, #8
    // 0x6daeb8: SetupParameters(QueryResultSet this /* r1 => r2, fp-0x8 */)
    //     0x6daeb8: mov             x2, x1
    //     0x6daebc: stur            x1, [fp, #-8]
    // 0x6daec0: CheckStackOverflow
    //     0x6daec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6daec4: cmp             SP, x16
    //     0x6daec8: b.ls            #0x6daf48
    // 0x6daecc: LoadField: r0 = r2->field_13
    //     0x6daecc: ldur            w0, [x2, #0x13]
    // 0x6daed0: DecompressPointer r0
    //     0x6daed0: add             x0, x0, HEAP, lsl #32
    // 0x6daed4: cmp             w0, NULL
    // 0x6daed8: b.ne            #0x6daf3c
    // 0x6daedc: LoadField: r1 = r2->field_f
    //     0x6daedc: ldur            w1, [x2, #0xf]
    // 0x6daee0: DecompressPointer r1
    //     0x6daee0: add             x1, x1, HEAP, lsl #32
    // 0x6daee4: cmp             w1, NULL
    // 0x6daee8: b.eq            #0x6daf50
    // 0x6daeec: r0 = LoadClassIdInstr(r1)
    //     0x6daeec: ldur            x0, [x1, #-1]
    //     0x6daef0: ubfx            x0, x0, #0xc, #0x14
    // 0x6daef4: r0 = GDT[cid_x0 + 0xf3fc]()
    //     0x6daef4: movz            x17, #0xf3fc
    //     0x6daef8: add             lr, x0, x17
    //     0x6daefc: ldr             lr, [x21, lr, lsl #3]
    //     0x6daf00: blr             lr
    // 0x6daf04: LoadField: r1 = r0->field_7
    //     0x6daf04: ldur            w1, [x0, #7]
    // 0x6daf08: DecompressPointer r1
    //     0x6daf08: add             x1, x1, HEAP, lsl #32
    // 0x6daf0c: mov             x2, x0
    // 0x6daf10: r0 = _List.of()
    //     0x6daf10: bl              #0x60beac  ; [dart:core] _List::_List.of
    // 0x6daf14: mov             x2, x0
    // 0x6daf18: ldur            x1, [fp, #-8]
    // 0x6daf1c: StoreField: r1->field_13 = r0
    //     0x6daf1c: stur            w0, [x1, #0x13]
    //     0x6daf20: ldurb           w16, [x1, #-1]
    //     0x6daf24: ldurb           w17, [x0, #-1]
    //     0x6daf28: and             x16, x17, x16, lsr #2
    //     0x6daf2c: tst             x16, HEAP, lsr #32
    //     0x6daf30: b.eq            #0x6daf38
    //     0x6daf34: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6daf38: mov             x0, x2
    // 0x6daf3c: LeaveFrame
    //     0x6daf3c: mov             SP, fp
    //     0x6daf40: ldp             fp, lr, [SP], #0x10
    // 0x6daf44: ret
    //     0x6daf44: ret             
    // 0x6daf48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6daf48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6daf4c: b               #0x6daecc
    // 0x6daf50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6daf50: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ length(/* No info */) {
    // ** addr: 0x913198, size: 0x9c
    // 0x913198: EnterFrame
    //     0x913198: stp             fp, lr, [SP, #-0x10]!
    //     0x91319c: mov             fp, SP
    // 0x9131a0: AllocStack(0x8)
    //     0x9131a0: sub             SP, SP, #8
    // 0x9131a4: CheckStackOverflow
    //     0x9131a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9131a8: cmp             SP, x16
    //     0x9131ac: b.ls            #0x91322c
    // 0x9131b0: ldr             x0, [fp, #0x10]
    // 0x9131b4: LoadField: r1 = r0->field_b
    //     0x9131b4: ldur            w1, [x0, #0xb]
    // 0x9131b8: DecompressPointer r1
    //     0x9131b8: add             x1, x1, HEAP, lsl #32
    // 0x9131bc: cmp             w1, NULL
    // 0x9131c0: b.ne            #0x9131cc
    // 0x9131c4: r2 = Null
    //     0x9131c4: mov             x2, NULL
    // 0x9131c8: b               #0x9131ec
    // 0x9131cc: r0 = LoadClassIdInstr(r1)
    //     0x9131cc: ldur            x0, [x1, #-1]
    //     0x9131d0: ubfx            x0, x0, #0xc, #0x14
    // 0x9131d4: str             x1, [SP]
    // 0x9131d8: r0 = GDT[cid_x0 + 0xc834]()
    //     0x9131d8: movz            x17, #0xc834
    //     0x9131dc: add             lr, x0, x17
    //     0x9131e0: ldr             lr, [x21, lr, lsl #3]
    //     0x9131e4: blr             lr
    // 0x9131e8: mov             x2, x0
    // 0x9131ec: cmp             w2, NULL
    // 0x9131f0: b.ne            #0x9131fc
    // 0x9131f4: r2 = 0
    //     0x9131f4: movz            x2, #0
    // 0x9131f8: b               #0x91320c
    // 0x9131fc: r3 = LoadInt32Instr(r2)
    //     0x9131fc: sbfx            x3, x2, #1, #0x1f
    //     0x913200: tbz             w2, #0, #0x913208
    //     0x913204: ldur            x3, [x2, #7]
    // 0x913208: mov             x2, x3
    // 0x91320c: r0 = BoxInt64Instr(r2)
    //     0x91320c: sbfiz           x0, x2, #1, #0x1f
    //     0x913210: cmp             x2, x0, asr #1
    //     0x913214: b.eq            #0x913220
    //     0x913218: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91321c: stur            x2, [x0, #7]
    // 0x913220: LeaveFrame
    //     0x913220: mov             SP, fp
    //     0x913224: ldp             fp, lr, [SP], #0x10
    // 0x913228: ret
    //     0x913228: ret             
    // 0x91322c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91322c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x913230: b               #0x9131b0
  }
  _ QueryResultSet(/* No info */) {
    // ** addr: 0xab5e70, size: 0x234
    // 0xab5e70: EnterFrame
    //     0xab5e70: stp             fp, lr, [SP, #-0x10]!
    //     0xab5e74: mov             fp, SP
    // 0xab5e78: AllocStack(0x38)
    //     0xab5e78: sub             SP, SP, #0x38
    // 0xab5e7c: r0 = Sentinel
    //     0xab5e7c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab5e80: stur            x1, [fp, #-8]
    // 0xab5e84: stur            x3, [fp, #-0x10]
    // 0xab5e88: CheckStackOverflow
    //     0xab5e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5e8c: cmp             SP, x16
    //     0xab5e90: b.ls            #0xab608c
    // 0xab5e94: ArrayStore: r1[0] = r0  ; List_4
    //     0xab5e94: stur            w0, [x1, #0x17]
    // 0xab5e98: cmp             w2, NULL
    // 0xab5e9c: b.ne            #0xab5eb0
    // 0xab5ea0: mov             x2, x1
    // 0xab5ea4: mov             x1, x3
    // 0xab5ea8: r0 = Null
    //     0xab5ea8: mov             x0, NULL
    // 0xab5eac: b               #0xab5edc
    // 0xab5eb0: r0 = LoadClassIdInstr(r2)
    //     0xab5eb0: ldur            x0, [x2, #-1]
    //     0xab5eb4: ubfx            x0, x0, #0xc, #0x14
    // 0xab5eb8: r16 = <String>
    //     0xab5eb8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xab5ebc: stp             x2, x16, [SP]
    // 0xab5ec0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab5ec0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab5ec4: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xab5ec4: movz            x17, #0xf30c
    //     0xab5ec8: add             lr, x0, x17
    //     0xab5ecc: ldr             lr, [x21, lr, lsl #3]
    //     0xab5ed0: blr             lr
    // 0xab5ed4: ldur            x2, [fp, #-8]
    // 0xab5ed8: ldur            x1, [fp, #-0x10]
    // 0xab5edc: StoreField: r2->field_f = r0
    //     0xab5edc: stur            w0, [x2, #0xf]
    //     0xab5ee0: ldurb           w16, [x2, #-1]
    //     0xab5ee4: ldurb           w17, [x0, #-1]
    //     0xab5ee8: and             x16, x17, x16, lsr #2
    //     0xab5eec: tst             x16, HEAP, lsr #32
    //     0xab5ef0: b.eq            #0xab5ef8
    //     0xab5ef4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab5ef8: cmp             w1, NULL
    // 0xab5efc: b.ne            #0xab5f0c
    // 0xab5f00: mov             x1, x2
    // 0xab5f04: r0 = Null
    //     0xab5f04: mov             x0, NULL
    // 0xab5f08: b               #0xab5f34
    // 0xab5f0c: r0 = LoadClassIdInstr(r1)
    //     0xab5f0c: ldur            x0, [x1, #-1]
    //     0xab5f10: ubfx            x0, x0, #0xc, #0x14
    // 0xab5f14: r16 = <List>
    //     0xab5f14: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0xab5f18: stp             x1, x16, [SP]
    // 0xab5f1c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab5f1c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab5f20: r0 = GDT[cid_x0 + 0xf30c]()
    //     0xab5f20: movz            x17, #0xf30c
    //     0xab5f24: add             lr, x0, x17
    //     0xab5f28: ldr             lr, [x21, lr, lsl #3]
    //     0xab5f2c: blr             lr
    // 0xab5f30: ldur            x1, [fp, #-8]
    // 0xab5f34: StoreField: r1->field_b = r0
    //     0xab5f34: stur            w0, [x1, #0xb]
    //     0xab5f38: ldurb           w16, [x1, #-1]
    //     0xab5f3c: ldurb           w17, [x0, #-1]
    //     0xab5f40: and             x16, x17, x16, lsr #2
    //     0xab5f44: tst             x16, HEAP, lsr #32
    //     0xab5f48: b.eq            #0xab5f50
    //     0xab5f4c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab5f50: LoadField: r0 = r1->field_f
    //     0xab5f50: ldur            w0, [x1, #0xf]
    // 0xab5f54: DecompressPointer r0
    //     0xab5f54: add             x0, x0, HEAP, lsl #32
    // 0xab5f58: cmp             w0, NULL
    // 0xab5f5c: b.eq            #0xab607c
    // 0xab5f60: r16 = <String, int>
    //     0xab5f60: ldr             x16, [PP, #0x910]  ; [pp+0x910] TypeArguments: <String, int>
    // 0xab5f64: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xab5f68: stp             lr, x16, [SP]
    // 0xab5f6c: r0 = Map._fromLiteral()
    //     0xab5f6c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab5f70: ldur            x1, [fp, #-8]
    // 0xab5f74: ArrayStore: r1[0] = r0  ; List_4
    //     0xab5f74: stur            w0, [x1, #0x17]
    //     0xab5f78: ldurb           w16, [x1, #-1]
    //     0xab5f7c: ldurb           w17, [x0, #-1]
    //     0xab5f80: and             x16, x17, x16, lsr #2
    //     0xab5f84: tst             x16, HEAP, lsr #32
    //     0xab5f88: b.eq            #0xab5f90
    //     0xab5f8c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab5f90: r2 = 0
    //     0xab5f90: movz            x2, #0
    // 0xab5f94: stur            x2, [fp, #-0x18]
    // 0xab5f98: CheckStackOverflow
    //     0xab5f98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5f9c: cmp             SP, x16
    //     0xab5fa0: b.ls            #0xab6094
    // 0xab5fa4: LoadField: r0 = r1->field_f
    //     0xab5fa4: ldur            w0, [x1, #0xf]
    // 0xab5fa8: DecompressPointer r0
    //     0xab5fa8: add             x0, x0, HEAP, lsl #32
    // 0xab5fac: cmp             w0, NULL
    // 0xab5fb0: b.eq            #0xab609c
    // 0xab5fb4: r3 = LoadClassIdInstr(r0)
    //     0xab5fb4: ldur            x3, [x0, #-1]
    //     0xab5fb8: ubfx            x3, x3, #0xc, #0x14
    // 0xab5fbc: str             x0, [SP]
    // 0xab5fc0: mov             x0, x3
    // 0xab5fc4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xab5fc4: movz            x17, #0xc834
    //     0xab5fc8: add             lr, x0, x17
    //     0xab5fcc: ldr             lr, [x21, lr, lsl #3]
    //     0xab5fd0: blr             lr
    // 0xab5fd4: r1 = LoadInt32Instr(r0)
    //     0xab5fd4: sbfx            x1, x0, #1, #0x1f
    //     0xab5fd8: tbz             w0, #0, #0xab5fe0
    //     0xab5fdc: ldur            x1, [x0, #7]
    // 0xab5fe0: ldur            x2, [fp, #-0x18]
    // 0xab5fe4: cmp             x2, x1
    // 0xab5fe8: b.ge            #0xab607c
    // 0xab5fec: ldur            x3, [fp, #-8]
    // 0xab5ff0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xab5ff0: ldur            w4, [x3, #0x17]
    // 0xab5ff4: DecompressPointer r4
    //     0xab5ff4: add             x4, x4, HEAP, lsl #32
    // 0xab5ff8: stur            x4, [fp, #-0x20]
    // 0xab5ffc: LoadField: r5 = r3->field_f
    //     0xab5ffc: ldur            w5, [x3, #0xf]
    // 0xab6000: DecompressPointer r5
    //     0xab6000: add             x5, x5, HEAP, lsl #32
    // 0xab6004: cmp             w5, NULL
    // 0xab6008: b.eq            #0xab60a0
    // 0xab600c: r0 = BoxInt64Instr(r2)
    //     0xab600c: sbfiz           x0, x2, #1, #0x1f
    //     0xab6010: cmp             x2, x0, asr #1
    //     0xab6014: b.eq            #0xab6020
    //     0xab6018: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab601c: stur            x2, [x0, #7]
    // 0xab6020: mov             x1, x0
    // 0xab6024: stur            x1, [fp, #-0x10]
    // 0xab6028: r0 = LoadClassIdInstr(r5)
    //     0xab6028: ldur            x0, [x5, #-1]
    //     0xab602c: ubfx            x0, x0, #0xc, #0x14
    // 0xab6030: stp             x1, x5, [SP]
    // 0xab6034: r0 = GDT[cid_x0 + 0x13037]()
    //     0xab6034: movz            x17, #0x3037
    //     0xab6038: movk            x17, #0x1, lsl #16
    //     0xab603c: add             lr, x0, x17
    //     0xab6040: ldr             lr, [x21, lr, lsl #3]
    //     0xab6044: blr             lr
    // 0xab6048: ldur            x1, [fp, #-0x20]
    // 0xab604c: mov             x2, x0
    // 0xab6050: stur            x0, [fp, #-0x28]
    // 0xab6054: r0 = _hashCode()
    //     0xab6054: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xab6058: ldur            x1, [fp, #-0x20]
    // 0xab605c: ldur            x2, [fp, #-0x28]
    // 0xab6060: ldur            x3, [fp, #-0x10]
    // 0xab6064: mov             x5, x0
    // 0xab6068: r0 = _set()
    //     0xab6068: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xab606c: ldur            x1, [fp, #-0x18]
    // 0xab6070: add             x2, x1, #1
    // 0xab6074: ldur            x1, [fp, #-8]
    // 0xab6078: b               #0xab5f94
    // 0xab607c: r0 = Null
    //     0xab607c: mov             x0, NULL
    // 0xab6080: LeaveFrame
    //     0xab6080: mov             SP, fp
    //     0xab6084: ldp             fp, lr, [SP], #0x10
    // 0xab6088: ret
    //     0xab6088: ret             
    // 0xab608c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab608c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6090: b               #0xab5e94
    // 0xab6094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6098: b               #0xab5fa4
    // 0xab609c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab609c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab60a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab60a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ columnIndex(/* No info */) {
    // ** addr: 0xd134a0, size: 0x7c
    // 0xd134a0: EnterFrame
    //     0xd134a0: stp             fp, lr, [SP, #-0x10]!
    //     0xd134a4: mov             fp, SP
    // 0xd134a8: AllocStack(0x8)
    //     0xd134a8: sub             SP, SP, #8
    // 0xd134ac: CheckStackOverflow
    //     0xd134ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd134b0: cmp             SP, x16
    //     0xd134b4: b.ls            #0xd13504
    // 0xd134b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd134b8: ldur            w0, [x1, #0x17]
    // 0xd134bc: DecompressPointer r0
    //     0xd134bc: add             x0, x0, HEAP, lsl #32
    // 0xd134c0: r16 = Sentinel
    //     0xd134c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd134c4: cmp             w0, w16
    // 0xd134c8: b.eq            #0xd1350c
    // 0xd134cc: stur            x0, [fp, #-8]
    // 0xd134d0: cmp             w2, NULL
    // 0xd134d4: b.eq            #0xd13518
    // 0xd134d8: mov             x1, x0
    // 0xd134dc: r0 = _getValueOrData()
    //     0xd134dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xd134e0: ldur            x1, [fp, #-8]
    // 0xd134e4: LoadField: r2 = r1->field_f
    //     0xd134e4: ldur            w2, [x1, #0xf]
    // 0xd134e8: DecompressPointer r2
    //     0xd134e8: add             x2, x2, HEAP, lsl #32
    // 0xd134ec: cmp             w2, w0
    // 0xd134f0: b.ne            #0xd134f8
    // 0xd134f4: r0 = Null
    //     0xd134f4: mov             x0, NULL
    // 0xd134f8: LeaveFrame
    //     0xd134f8: mov             SP, fp
    //     0xd134fc: ldp             fp, lr, [SP], #0x10
    // 0xd13500: ret
    //     0xd13500: ret             
    // 0xd13504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd13504: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd13508: b               #0xd134b8
    // 0xd1350c: r9 = _columnIndexMap
    //     0xd1350c: add             x9, PP, #0x1f, lsl #12  ; [pp+0x1fb98] Field <QueryResultSet._columnIndexMap@2714028545>: late (offset: 0x18)
    //     0xd13510: ldr             x9, [x9, #0xb98]
    // 0xd13514: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xd13514: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xd13518: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd13518: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 7321, size: 0x10, field offset: 0xc
abstract class PluginList<X0> extends ListBase<X0> {

  _ []=(/* No info */) {
    // ** addr: 0x66d4f0, size: 0x60
    // 0x66d4f0: EnterFrame
    //     0x66d4f0: stp             fp, lr, [SP, #-0x10]!
    //     0x66d4f4: mov             fp, SP
    // 0x66d4f8: ldr             x0, [fp, #0x20]
    // 0x66d4fc: LoadField: r2 = r0->field_7
    //     0x66d4fc: ldur            w2, [x0, #7]
    // 0x66d500: DecompressPointer r2
    //     0x66d500: add             x2, x2, HEAP, lsl #32
    // 0x66d504: ldr             x0, [fp, #0x10]
    // 0x66d508: r1 = Null
    //     0x66d508: mov             x1, NULL
    // 0x66d50c: cmp             w2, NULL
    // 0x66d510: b.eq            #0x66d530
    // 0x66d514: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66d514: ldur            w4, [x2, #0x17]
    // 0x66d518: DecompressPointer r4
    //     0x66d518: add             x4, x4, HEAP, lsl #32
    // 0x66d51c: r8 = X0
    //     0x66d51c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x66d520: LoadField: r9 = r4->field_7
    //     0x66d520: ldur            x9, [x4, #7]
    // 0x66d524: r3 = Null
    //     0x66d524: add             x3, PP, #0x46, lsl #12  ; [pp+0x46d18] Null
    //     0x66d528: ldr             x3, [x3, #0xd18]
    // 0x66d52c: blr             x9
    // 0x66d530: r0 = UnsupportedError()
    //     0x66d530: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x66d534: mov             x1, x0
    // 0x66d538: r0 = "read-only"
    //     0x66d538: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb80] "read-only"
    //     0x66d53c: ldr             x0, [x0, #0xb80]
    // 0x66d540: StoreField: r1->field_b = r0
    //     0x66d540: stur            w0, [x1, #0xb]
    // 0x66d544: mov             x0, x1
    // 0x66d548: r0 = Throw()
    //     0x66d548: bl              #0xec04b8  ; ThrowStub
    // 0x66d54c: brk             #0
  }
}

// class id: 7322, size: 0x10, field offset: 0x10
class Rows extends PluginList<dynamic> {

  Map<String, Object?> [](Rows, int) {
    // ** addr: 0x66dfcc, size: 0xa8
    // 0x66dfcc: EnterFrame
    //     0x66dfcc: stp             fp, lr, [SP, #-0x10]!
    //     0x66dfd0: mov             fp, SP
    // 0x66dfd4: AllocStack(0x18)
    //     0x66dfd4: sub             SP, SP, #0x18
    // 0x66dfd8: CheckStackOverflow
    //     0x66dfd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66dfdc: cmp             SP, x16
    //     0x66dfe0: b.ls            #0x66e06c
    // 0x66dfe4: ldr             x0, [fp, #0x18]
    // 0x66dfe8: LoadField: r1 = r0->field_b
    //     0x66dfe8: ldur            w1, [x0, #0xb]
    // 0x66dfec: DecompressPointer r1
    //     0x66dfec: add             x1, x1, HEAP, lsl #32
    // 0x66dff0: r0 = LoadClassIdInstr(r1)
    //     0x66dff0: ldur            x0, [x1, #-1]
    //     0x66dff4: ubfx            x0, x0, #0xc, #0x14
    // 0x66dff8: ldr             x16, [fp, #0x10]
    // 0x66dffc: stp             x16, x1, [SP]
    // 0x66e000: r0 = GDT[cid_x0 + 0x13037]()
    //     0x66e000: movz            x17, #0x3037
    //     0x66e004: movk            x17, #0x1, lsl #16
    //     0x66e008: add             lr, x0, x17
    //     0x66e00c: ldr             lr, [x21, lr, lsl #3]
    //     0x66e010: blr             lr
    // 0x66e014: mov             x3, x0
    // 0x66e018: r2 = Null
    //     0x66e018: mov             x2, NULL
    // 0x66e01c: r1 = Null
    //     0x66e01c: mov             x1, NULL
    // 0x66e020: stur            x3, [fp, #-8]
    // 0x66e024: r8 = Map
    //     0x66e024: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0x66e028: r3 = Null
    //     0x66e028: add             x3, PP, #0x46, lsl #12  ; [pp+0x46d08] Null
    //     0x66e02c: ldr             x3, [x3, #0xd08]
    // 0x66e030: r0 = Map()
    //     0x66e030: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0x66e034: ldur            x0, [fp, #-8]
    // 0x66e038: r1 = LoadClassIdInstr(r0)
    //     0x66e038: ldur            x1, [x0, #-1]
    //     0x66e03c: ubfx            x1, x1, #0xc, #0x14
    // 0x66e040: r16 = <String, Object?>
    //     0x66e040: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0x66e044: ldr             x16, [x16, #0x738]
    // 0x66e048: stp             x0, x16, [SP]
    // 0x66e04c: mov             x0, x1
    // 0x66e050: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x66e050: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x66e054: r0 = GDT[cid_x0 + 0x5f4]()
    //     0x66e054: add             lr, x0, #0x5f4
    //     0x66e058: ldr             lr, [x21, lr, lsl #3]
    //     0x66e05c: blr             lr
    // 0x66e060: LeaveFrame
    //     0x66e060: mov             SP, fp
    //     0x66e064: ldp             fp, lr, [SP], #0x10
    // 0x66e068: ret
    //     0x66e068: ret             
    // 0x66e06c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66e06c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66e070: b               #0x66dfe4
  }
  Map<String, Object?> [](Rows, int) {
    // ** addr: 0x66e08c, size: 0x88
    // 0x66e08c: EnterFrame
    //     0x66e08c: stp             fp, lr, [SP, #-0x10]!
    //     0x66e090: mov             fp, SP
    // 0x66e094: AllocStack(0x10)
    //     0x66e094: sub             SP, SP, #0x10
    // 0x66e098: CheckStackOverflow
    //     0x66e098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66e09c: cmp             SP, x16
    //     0x66e0a0: b.ls            #0x66e0f4
    // 0x66e0a4: ldr             x0, [fp, #0x10]
    // 0x66e0a8: r2 = Null
    //     0x66e0a8: mov             x2, NULL
    // 0x66e0ac: r1 = Null
    //     0x66e0ac: mov             x1, NULL
    // 0x66e0b0: branchIfSmi(r0, 0x66e0d8)
    //     0x66e0b0: tbz             w0, #0, #0x66e0d8
    // 0x66e0b4: r4 = LoadClassIdInstr(r0)
    //     0x66e0b4: ldur            x4, [x0, #-1]
    //     0x66e0b8: ubfx            x4, x4, #0xc, #0x14
    // 0x66e0bc: sub             x4, x4, #0x3c
    // 0x66e0c0: cmp             x4, #1
    // 0x66e0c4: b.ls            #0x66e0d8
    // 0x66e0c8: r8 = int
    //     0x66e0c8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x66e0cc: r3 = Null
    //     0x66e0cc: add             x3, PP, #0x46, lsl #12  ; [pp+0x46cf8] Null
    //     0x66e0d0: ldr             x3, [x3, #0xcf8]
    // 0x66e0d4: r0 = int()
    //     0x66e0d4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x66e0d8: ldr             x16, [fp, #0x18]
    // 0x66e0dc: ldr             lr, [fp, #0x10]
    // 0x66e0e0: stp             lr, x16, [SP]
    // 0x66e0e4: r0 = []()
    //     0x66e0e4: bl              #0x66dfcc  ; [package:sqflite_common/src/collection_utils.dart] Rows::[]
    // 0x66e0e8: LeaveFrame
    //     0x66e0e8: mov             SP, fp
    //     0x66e0ec: ldp             fp, lr, [SP], #0x10
    // 0x66e0f0: ret
    //     0x66e0f0: ret             
    // 0x66e0f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66e0f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66e0f8: b               #0x66e0a4
  }
}
