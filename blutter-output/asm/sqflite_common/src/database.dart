// lib: , url: package:sqflite_common/src/database.dart

// class id: 1051151, size: 0x8
class :: {
}

// class id: 479, size: 0x18, field offset: 0x8
class SqfliteDatabaseOpenHelper extends Object {

  _ openDatabase(/* No info */) async {
    // ** addr: 0xab3b14, size: 0xb4
    // 0xab3b14: EnterFrame
    //     0xab3b14: stp             fp, lr, [SP, #-0x10]!
    //     0xab3b18: mov             fp, SP
    // 0xab3b1c: AllocStack(0x20)
    //     0xab3b1c: sub             SP, SP, #0x20
    // 0xab3b20: SetupParameters(SqfliteDatabaseOpenHelper this /* r1 => r1, fp-0x10 */)
    //     0xab3b20: stur            NULL, [fp, #-8]
    //     0xab3b24: stur            x1, [fp, #-0x10]
    // 0xab3b28: CheckStackOverflow
    //     0xab3b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3b2c: cmp             SP, x16
    //     0xab3b30: b.ls            #0xab3bc0
    // 0xab3b34: InitAsync() -> Future<SqfliteDatabase>
    //     0xab3b34: add             x0, PP, #0x43, lsl #12  ; [pp+0x43118] TypeArguments: <SqfliteDatabase>
    //     0xab3b38: ldr             x0, [x0, #0x118]
    //     0xab3b3c: bl              #0x661298  ; InitAsyncStub
    // 0xab3b40: ldur            x0, [fp, #-0x10]
    // 0xab3b44: LoadField: r1 = r0->field_13
    //     0xab3b44: ldur            w1, [x0, #0x13]
    // 0xab3b48: DecompressPointer r1
    //     0xab3b48: add             x1, x1, HEAP, lsl #32
    // 0xab3b4c: cmp             w1, NULL
    // 0xab3b50: b.ne            #0xab3bb8
    // 0xab3b54: LoadField: r2 = r0->field_f
    //     0xab3b54: ldur            w2, [x0, #0xf]
    // 0xab3b58: DecompressPointer r2
    //     0xab3b58: add             x2, x2, HEAP, lsl #32
    // 0xab3b5c: mov             x1, x0
    // 0xab3b60: r0 = newDatabase()
    //     0xab3b60: bl              #0xab6d30  ; [package:sqflite_common/src/database.dart] SqfliteDatabaseOpenHelper::newDatabase
    // 0xab3b64: mov             x3, x0
    // 0xab3b68: ldur            x0, [fp, #-0x10]
    // 0xab3b6c: stur            x3, [fp, #-0x18]
    // 0xab3b70: LoadField: r2 = r0->field_b
    //     0xab3b70: ldur            w2, [x0, #0xb]
    // 0xab3b74: DecompressPointer r2
    //     0xab3b74: add             x2, x2, HEAP, lsl #32
    // 0xab3b78: mov             x1, x3
    // 0xab3b7c: r0 = doOpen()
    //     0xab3b7c: bl              #0xab3bc8  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::doOpen
    // 0xab3b80: mov             x1, x0
    // 0xab3b84: stur            x1, [fp, #-0x20]
    // 0xab3b88: r0 = Await()
    //     0xab3b88: bl              #0x661044  ; AwaitStub
    // 0xab3b8c: ldur            x0, [fp, #-0x18]
    // 0xab3b90: ldur            x2, [fp, #-0x10]
    // 0xab3b94: StoreField: r2->field_13 = r0
    //     0xab3b94: stur            w0, [x2, #0x13]
    //     0xab3b98: ldurb           w16, [x2, #-1]
    //     0xab3b9c: ldurb           w17, [x0, #-1]
    //     0xab3ba0: and             x16, x17, x16, lsr #2
    //     0xab3ba4: tst             x16, HEAP, lsr #32
    //     0xab3ba8: b.eq            #0xab3bb0
    //     0xab3bac: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab3bb0: ldur            x0, [fp, #-0x18]
    // 0xab3bb4: b               #0xab3bbc
    // 0xab3bb8: mov             x0, x1
    // 0xab3bbc: r0 = ReturnAsyncNotFuture()
    //     0xab3bbc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab3bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3bc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3bc4: b               #0xab3b34
  }
  _ newDatabase(/* No info */) {
    // ** addr: 0xab6d30, size: 0x3c
    // 0xab6d30: EnterFrame
    //     0xab6d30: stp             fp, lr, [SP, #-0x10]!
    //     0xab6d34: mov             fp, SP
    // 0xab6d38: mov             x3, x2
    // 0xab6d3c: mov             x2, x1
    // 0xab6d40: CheckStackOverflow
    //     0xab6d40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6d44: cmp             SP, x16
    //     0xab6d48: b.ls            #0xab6d64
    // 0xab6d4c: LoadField: r1 = r2->field_7
    //     0xab6d4c: ldur            w1, [x2, #7]
    // 0xab6d50: DecompressPointer r1
    //     0xab6d50: add             x1, x1, HEAP, lsl #32
    // 0xab6d54: r0 = newDatabase()
    //     0xab6d54: bl              #0xab6d6c  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::newDatabase
    // 0xab6d58: LeaveFrame
    //     0xab6d58: mov             SP, fp
    //     0xab6d5c: ldp             fp, lr, [SP], #0x10
    // 0xab6d60: ret
    //     0xab6d60: ret             
    // 0xab6d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6d64: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6d68: b               #0xab6d4c
  }
}

// class id: 480, size: 0x8, field offset: 0x8
abstract class SqfliteDatabaseExecutor extends Object
    implements DatabaseExecutor {
}

// class id: 481, size: 0x8, field offset: 0x8
abstract class SqfliteDatabase extends SqfliteDatabaseExecutor
    implements Database {
}
