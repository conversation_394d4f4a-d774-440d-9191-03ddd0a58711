// lib: , url: package:sqflite_common/src/database_mixin.dart

// class id: 1051152, size: 0x8
class :: {

  static _ SqfliteDatabaseMixinExt.readOnly(/* No info */) {
    // ** addr: 0xab4710, size: 0x34
    // 0xab4710: LoadField: r2 = r1->field_2b
    //     0xab4710: ldur            w2, [x1, #0x2b]
    // 0xab4714: DecompressPointer r2
    //     0xab4714: add             x2, x2, HEAP, lsl #32
    // 0xab4718: cmp             w2, NULL
    // 0xab471c: b.ne            #0xab4728
    // 0xab4720: r1 = Null
    //     0xab4720: mov             x1, NULL
    // 0xab4724: b               #0xab472c
    // 0xab4728: r1 = false
    //     0xab4728: add             x1, NULL, #0x30  ; false
    // 0xab472c: cmp             w1, NULL
    // 0xab4730: b.ne            #0xab473c
    // 0xab4734: r0 = false
    //     0xab4734: add             x0, NULL, #0x30  ; false
    // 0xab4738: b               #0xab4740
    // 0xab473c: mov             x0, x1
    // 0xab4740: ret
    //     0xab4740: ret             
  }
  static _ SqfliteDatabaseMixinExt.addInTransactionChangeParam(/* No info */) {
    // ** addr: 0xab4be0, size: 0x44
    // 0xab4be0: EnterFrame
    //     0xab4be0: stp             fp, lr, [SP, #-0x10]!
    //     0xab4be4: mov             fp, SP
    // 0xab4be8: mov             x3, x2
    // 0xab4bec: CheckStackOverflow
    //     0xab4bec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4bf0: cmp             SP, x16
    //     0xab4bf4: b.ls            #0xab4c1c
    // 0xab4bf8: cmp             w3, NULL
    // 0xab4bfc: b.eq            #0xab4c0c
    // 0xab4c00: r2 = "inTransaction"
    //     0xab4c00: add             x2, PP, #0x43, lsl #12  ; [pp+0x43068] "inTransaction"
    //     0xab4c04: ldr             x2, [x2, #0x68]
    // 0xab4c08: r0 = []=()
    //     0xab4c08: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab4c0c: r0 = Null
    //     0xab4c0c: mov             x0, NULL
    // 0xab4c10: LeaveFrame
    //     0xab4c10: mov             SP, fp
    //     0xab4c14: ldp             fp, lr, [SP], #0x10
    // 0xab4c18: ret
    //     0xab4c18: ret             
    // 0xab4c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4c1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4c20: b               #0xab4bf8
  }
  static _ SqfliteDatabaseMixinExt._txnGetSqlMethodArguments(/* No info */) {
    // ** addr: 0xab4c24, size: 0xa8
    // 0xab4c24: EnterFrame
    //     0xab4c24: stp             fp, lr, [SP, #-0x10]!
    //     0xab4c28: mov             fp, SP
    // 0xab4c2c: AllocStack(0x30)
    //     0xab4c2c: sub             SP, SP, #0x30
    // 0xab4c30: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */, dynamic _ /* r5 => r3, fp-0x20 */)
    //     0xab4c30: mov             x0, x3
    //     0xab4c34: stur            x3, [fp, #-0x18]
    //     0xab4c38: mov             x3, x5
    //     0xab4c3c: stur            x1, [fp, #-8]
    //     0xab4c40: stur            x2, [fp, #-0x10]
    //     0xab4c44: stur            x5, [fp, #-0x20]
    // 0xab4c48: CheckStackOverflow
    //     0xab4c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4c4c: cmp             SP, x16
    //     0xab4c50: b.ls            #0xab4cc4
    // 0xab4c54: r16 = <String, Object?>
    //     0xab4c54: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xab4c58: ldr             x16, [x16, #0x738]
    // 0xab4c5c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xab4c60: stp             lr, x16, [SP]
    // 0xab4c64: r0 = Map._fromLiteral()
    //     0xab4c64: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab4c68: mov             x1, x0
    // 0xab4c6c: ldur            x3, [fp, #-0x18]
    // 0xab4c70: r2 = "sql"
    //     0xab4c70: add             x2, PP, #0x43, lsl #12  ; [pp+0x43070] "sql"
    //     0xab4c74: ldr             x2, [x2, #0x70]
    // 0xab4c78: stur            x0, [fp, #-0x18]
    // 0xab4c7c: r0 = []=()
    //     0xab4c7c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab4c80: ldur            x3, [fp, #-0x20]
    // 0xab4c84: cmp             w3, NULL
    // 0xab4c88: b.eq            #0xab4c9c
    // 0xab4c8c: ldur            x1, [fp, #-0x18]
    // 0xab4c90: r2 = "arguments"
    //     0xab4c90: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a480] "arguments"
    //     0xab4c94: ldr             x2, [x2, #0x480]
    // 0xab4c98: r0 = []=()
    //     0xab4c98: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab4c9c: ldur            x1, [fp, #-8]
    // 0xab4ca0: ldur            x2, [fp, #-0x10]
    // 0xab4ca4: r0 = SqfliteDatabaseMixinExt.getBaseDatabaseMethodArguments()
    //     0xab4ca4: bl              #0xab4ccc  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.getBaseDatabaseMethodArguments
    // 0xab4ca8: ldur            x1, [fp, #-0x18]
    // 0xab4cac: mov             x2, x0
    // 0xab4cb0: r0 = addAll()
    //     0xab4cb0: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0xab4cb4: ldur            x0, [fp, #-0x18]
    // 0xab4cb8: LeaveFrame
    //     0xab4cb8: mov             SP, fp
    //     0xab4cbc: ldp             fp, lr, [SP], #0x10
    // 0xab4cc0: ret
    //     0xab4cc0: ret             
    // 0xab4cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4cc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4cc8: b               #0xab4c54
  }
  static _ SqfliteDatabaseMixinExt.getBaseDatabaseMethodArguments(/* No info */) {
    // ** addr: 0xab4ccc, size: 0x9c
    // 0xab4ccc: EnterFrame
    //     0xab4ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xab4cd0: mov             fp, SP
    // 0xab4cd4: AllocStack(0x28)
    //     0xab4cd4: sub             SP, SP, #0x28
    // 0xab4cd8: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xab4cd8: stur            x1, [fp, #-8]
    //     0xab4cdc: stur            x2, [fp, #-0x10]
    // 0xab4ce0: CheckStackOverflow
    //     0xab4ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4ce4: cmp             SP, x16
    //     0xab4ce8: b.ls            #0xab4d60
    // 0xab4cec: r16 = <String, Object?>
    //     0xab4cec: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xab4cf0: ldr             x16, [x16, #0x738]
    // 0xab4cf4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xab4cf8: stp             lr, x16, [SP]
    // 0xab4cfc: r0 = Map._fromLiteral()
    //     0xab4cfc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab4d00: mov             x4, x0
    // 0xab4d04: ldur            x0, [fp, #-8]
    // 0xab4d08: stur            x4, [fp, #-0x18]
    // 0xab4d0c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xab4d0c: ldur            w3, [x0, #0x17]
    // 0xab4d10: DecompressPointer r3
    //     0xab4d10: add             x3, x3, HEAP, lsl #32
    // 0xab4d14: mov             x1, x4
    // 0xab4d18: r2 = "id"
    //     0xab4d18: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xab4d1c: ldr             x2, [x2, #0x740]
    // 0xab4d20: r0 = []=()
    //     0xab4d20: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab4d24: ldur            x0, [fp, #-0x10]
    // 0xab4d28: cmp             w0, NULL
    // 0xab4d2c: b.eq            #0xab4d50
    // 0xab4d30: LoadField: r3 = r0->field_7
    //     0xab4d30: ldur            w3, [x0, #7]
    // 0xab4d34: DecompressPointer r3
    //     0xab4d34: add             x3, x3, HEAP, lsl #32
    // 0xab4d38: cmp             w3, NULL
    // 0xab4d3c: b.eq            #0xab4d50
    // 0xab4d40: ldur            x1, [fp, #-0x18]
    // 0xab4d44: r2 = "transactionId"
    //     0xab4d44: add             x2, PP, #0x43, lsl #12  ; [pp+0x43028] "transactionId"
    //     0xab4d48: ldr             x2, [x2, #0x28]
    // 0xab4d4c: r0 = []=()
    //     0xab4d4c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab4d50: ldur            x0, [fp, #-0x18]
    // 0xab4d54: LeaveFrame
    //     0xab4d54: mov             SP, fp
    //     0xab4d58: ldp             fp, lr, [SP], #0x10
    // 0xab4d5c: ret
    //     0xab4d5c: ret             
    // 0xab4d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4d60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4d64: b               #0xab4cec
  }
  static Future<Y0> SqfliteDatabaseMixinExt._txnTransaction<Y0>(SqfliteDatabase, Transaction?, (dynamic, Transaction) => Future<Y0>) async {
    // ** addr: 0xab4f28, size: 0x278
    // 0xab4f28: EnterFrame
    //     0xab4f28: stp             fp, lr, [SP, #-0x10]!
    //     0xab4f2c: mov             fp, SP
    // 0xab4f30: AllocStack(0xc0)
    //     0xab4f30: sub             SP, SP, #0xc0
    // 0xab4f34: SetupParameters(dynamic _ /* r1, fp-0x98 */, dynamic _ /* r2, fp-0x90 */, dynamic _ /* r3, fp-0x88 */)
    //     0xab4f34: stur            NULL, [fp, #-8]
    //     0xab4f38: movz            x0, #0
    //     0xab4f3c: stur            x4, [fp, #-0xa0]
    //     0xab4f40: add             x1, fp, w0, sxtw #2
    //     0xab4f44: ldr             x1, [x1, #0x20]
    //     0xab4f48: stur            x1, [fp, #-0x98]
    //     0xab4f4c: add             x2, fp, w0, sxtw #2
    //     0xab4f50: ldr             x2, [x2, #0x18]
    //     0xab4f54: stur            x2, [fp, #-0x90]
    //     0xab4f58: add             x3, fp, w0, sxtw #2
    //     0xab4f5c: ldr             x3, [x3, #0x10]
    //     0xab4f60: stur            x3, [fp, #-0x88]
    //     0xab4f64: ldur            w0, [x4, #0xf]
    //     0xab4f68: cbnz            w0, #0xab4f74
    //     0xab4f6c: mov             x5, NULL
    //     0xab4f70: b               #0xab4f80
    //     0xab4f74: ldur            w0, [x4, #0x17]
    //     0xab4f78: add             x5, fp, w0, sxtw #2
    //     0xab4f7c: ldr             x5, [x5, #0x10]
    //     0xab4f80: stur            x5, [fp, #-0x80]
    // 0xab4f84: CheckStackOverflow
    //     0xab4f84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4f88: cmp             SP, x16
    //     0xab4f8c: b.ls            #0xab5198
    // 0xab4f90: mov             x0, x5
    // 0xab4f94: r0 = InitAsync()
    //     0xab4f94: bl              #0x661298  ; InitAsyncStub
    // 0xab4f98: ldur            x0, [fp, #-0x90]
    // 0xab4f9c: cmp             w0, NULL
    // 0xab4fa0: r16 = true
    //     0xab4fa0: add             x16, NULL, #0x20  ; true
    // 0xab4fa4: r17 = false
    //     0xab4fa4: add             x17, NULL, #0x30  ; false
    // 0xab4fa8: csel            x3, x16, x17, eq
    // 0xab4fac: stur            x3, [fp, #-0xa8]
    // 0xab4fb0: tbnz            w3, #4, #0xab4fd4
    // 0xab4fb4: ldur            x1, [fp, #-0x98]
    // 0xab4fb8: r2 = true
    //     0xab4fb8: add             x2, NULL, #0x20  ; true
    // 0xab4fbc: r0 = beginTransaction()
    //     0xab4fbc: bl              #0xab5560  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::beginTransaction
    // 0xab4fc0: mov             x1, x0
    // 0xab4fc4: stur            x1, [fp, #-0xb0]
    // 0xab4fc8: r0 = Await()
    //     0xab4fc8: bl              #0x661044  ; AwaitStub
    // 0xab4fcc: mov             x1, x0
    // 0xab4fd0: b               #0xab4fd8
    // 0xab4fd4: mov             x1, x0
    // 0xab4fd8: stur            x1, [fp, #-0x90]
    // 0xab4fdc: ldur            x16, [fp, #-0x88]
    // 0xab4fe0: stp             x1, x16, [SP]
    // 0xab4fe4: ldur            x0, [fp, #-0x88]
    // 0xab4fe8: ClosureCall
    //     0xab4fe8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xab4fec: ldur            x2, [x0, #0x1f]
    //     0xab4ff0: blr             x2
    // 0xab4ff4: mov             x1, x0
    // 0xab4ff8: stur            x1, [fp, #-0x88]
    // 0xab4ffc: r0 = Await()
    //     0xab4ffc: bl              #0x661044  ; AwaitStub
    // 0xab5000: mov             x3, x0
    // 0xab5004: stur            x3, [fp, #-0x80]
    // 0xab5008: ldur            x0, [fp, #-0xa8]
    // 0xab500c: tbnz            w0, #4, #0xab506c
    // 0xab5010: ldur            x4, [fp, #-0x90]
    // 0xab5014: mov             x0, x4
    // 0xab5018: r2 = Null
    //     0xab5018: mov             x2, NULL
    // 0xab501c: r1 = Null
    //     0xab501c: mov             x1, NULL
    // 0xab5020: r4 = 60
    //     0xab5020: movz            x4, #0x3c
    // 0xab5024: branchIfSmi(r0, 0xab5030)
    //     0xab5024: tbz             w0, #0, #0xab5030
    // 0xab5028: r4 = LoadClassIdInstr(r0)
    //     0xab5028: ldur            x4, [x0, #-1]
    //     0xab502c: ubfx            x4, x4, #0xc, #0x14
    // 0xab5030: cmp             x4, #0x1cd
    // 0xab5034: b.eq            #0xab504c
    // 0xab5038: r8 = SqfliteTransaction
    //     0xab5038: add             x8, PP, #0x43, lsl #12  ; [pp+0x43158] Type: SqfliteTransaction
    //     0xab503c: ldr             x8, [x8, #0x158]
    // 0xab5040: r3 = Null
    //     0xab5040: add             x3, PP, #0x43, lsl #12  ; [pp+0x432c0] Null
    //     0xab5044: ldr             x3, [x3, #0x2c0]
    // 0xab5048: r0 = DefaultTypeTest()
    //     0xab5048: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xab504c: ldur            x2, [fp, #-0x90]
    // 0xab5050: r0 = true
    //     0xab5050: add             x0, NULL, #0x20  ; true
    // 0xab5054: StoreField: r2->field_f = r0
    //     0xab5054: stur            w0, [x2, #0xf]
    // 0xab5058: ldur            x1, [fp, #-0x98]
    // 0xab505c: r0 = endTransaction()
    //     0xab505c: bl              #0xab51a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::endTransaction
    // 0xab5060: mov             x1, x0
    // 0xab5064: stur            x1, [fp, #-0x88]
    // 0xab5068: r0 = Await()
    //     0xab5068: bl              #0x661044  ; AwaitStub
    // 0xab506c: ldur            x0, [fp, #-0x80]
    // 0xab5070: r0 = ReturnAsync()
    //     0xab5070: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab5074: sub             SP, fp, #0xc0
    // 0xab5078: mov             x3, x1
    // 0xab507c: stur            x1, [fp, #-0x88]
    // 0xab5080: ldur            x1, [fp, #-0x28]
    // 0xab5084: mov             x4, x0
    // 0xab5088: stur            x0, [fp, #-0x80]
    // 0xab508c: r2 = Null
    //     0xab508c: mov             x2, NULL
    // 0xab5090: cmp             w0, NULL
    // 0xab5094: b.eq            #0xab50e0
    // 0xab5098: branchIfSmi(r0, 0xab50e0)
    //     0xab5098: tbz             w0, #0, #0xab50e0
    // 0xab509c: r3 = SubtypeTestCache
    //     0xab509c: add             x3, PP, #0x43, lsl #12  ; [pp+0x432d0] SubtypeTestCache
    //     0xab50a0: ldr             x3, [x3, #0x2d0]
    // 0xab50a4: r30 = Subtype4TestCacheStub
    //     0xab50a4: ldr             lr, [PP, #0x20]  ; [pp+0x20] Stub: Subtype4TestCache (0x5f2a74)
    // 0xab50a8: LoadField: r30 = r30->field_7
    //     0xab50a8: ldur            lr, [lr, #7]
    // 0xab50ac: blr             lr
    // 0xab50b0: cmp             w7, NULL
    // 0xab50b4: b.eq            #0xab50c0
    // 0xab50b8: tbnz            w7, #4, #0xab50e0
    // 0xab50bc: b               #0xab50e8
    // 0xab50c0: r8 = SqfliteTransactionRollbackSuccess<Y0>
    //     0xab50c0: add             x8, PP, #0x43, lsl #12  ; [pp+0x432d8] Type: SqfliteTransactionRollbackSuccess<Y0>
    //     0xab50c4: ldr             x8, [x8, #0x2d8]
    // 0xab50c8: r3 = SubtypeTestCache
    //     0xab50c8: add             x3, PP, #0x43, lsl #12  ; [pp+0x432e0] SubtypeTestCache
    //     0xab50cc: ldr             x3, [x3, #0x2e0]
    // 0xab50d0: r30 = InstanceOfStub
    //     0xab50d0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xab50d4: LoadField: r30 = r30->field_7
    //     0xab50d4: ldur            lr, [lr, #7]
    // 0xab50d8: blr             lr
    // 0xab50dc: b               #0xab50ec
    // 0xab50e0: r0 = false
    //     0xab50e0: add             x0, NULL, #0x30  ; false
    // 0xab50e4: b               #0xab50ec
    // 0xab50e8: r0 = true
    //     0xab50e8: add             x0, NULL, #0x20  ; true
    // 0xab50ec: tbnz            w0, #4, #0xab50fc
    // 0xab50f0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0xab50f0: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0xab50f4: r0 = Throw()
    //     0xab50f4: bl              #0xec04b8  ; ThrowStub
    // 0xab50f8: brk             #0
    // 0xab50fc: ldur            x0, [fp, #-0x80]
    // 0xab5100: ldur            x1, [fp, #-0x88]
    // 0xab5104: r0 = ReThrow()
    //     0xab5104: bl              #0xec048c  ; ReThrowStub
    // 0xab5108: brk             #0
    // 0xab510c: sub             SP, fp, #0xc0
    // 0xab5110: mov             x4, x0
    // 0xab5114: mov             x3, x1
    // 0xab5118: stur            x0, [fp, #-0x80]
    // 0xab511c: ldur            x0, [fp, #-0x68]
    // 0xab5120: stur            x1, [fp, #-0x88]
    // 0xab5124: r16 = true
    //     0xab5124: add             x16, NULL, #0x20  ; true
    // 0xab5128: cmp             w0, w16
    // 0xab512c: b.ne            #0xab5188
    // 0xab5130: ldur            x5, [fp, #-0x18]
    // 0xab5134: mov             x0, x5
    // 0xab5138: r2 = Null
    //     0xab5138: mov             x2, NULL
    // 0xab513c: r1 = Null
    //     0xab513c: mov             x1, NULL
    // 0xab5140: r4 = 60
    //     0xab5140: movz            x4, #0x3c
    // 0xab5144: branchIfSmi(r0, 0xab5150)
    //     0xab5144: tbz             w0, #0, #0xab5150
    // 0xab5148: r4 = LoadClassIdInstr(r0)
    //     0xab5148: ldur            x4, [x0, #-1]
    //     0xab514c: ubfx            x4, x4, #0xc, #0x14
    // 0xab5150: cmp             x4, #0x1cd
    // 0xab5154: b.eq            #0xab516c
    // 0xab5158: r8 = SqfliteTransaction
    //     0xab5158: add             x8, PP, #0x43, lsl #12  ; [pp+0x43158] Type: SqfliteTransaction
    //     0xab515c: ldr             x8, [x8, #0x158]
    // 0xab5160: r3 = Null
    //     0xab5160: add             x3, PP, #0x43, lsl #12  ; [pp+0x432e8] Null
    //     0xab5164: ldr             x3, [x3, #0x2e8]
    // 0xab5168: r0 = DefaultTypeTest()
    //     0xab5168: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xab516c: ldur            x2, [fp, #-0x18]
    // 0xab5170: StoreField: r2->field_f = rNULL
    //     0xab5170: stur            NULL, [x2, #0xf]
    // 0xab5174: ldur            x1, [fp, #-0x10]
    // 0xab5178: r0 = endTransaction()
    //     0xab5178: bl              #0xab51a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::endTransaction
    // 0xab517c: mov             x1, x0
    // 0xab5180: stur            x1, [fp, #-0x90]
    // 0xab5184: r0 = Await()
    //     0xab5184: bl              #0x661044  ; AwaitStub
    // 0xab5188: ldur            x0, [fp, #-0x80]
    // 0xab518c: ldur            x1, [fp, #-0x88]
    // 0xab5190: r0 = ReThrow()
    //     0xab5190: bl              #0xec048c  ; ReThrowStub
    // 0xab5194: brk             #0
    // 0xab5198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab519c: b               #0xab4f90
  }
  static _ SqfliteDatabaseMixinExt.txnBeginTransaction(/* No info */) async {
    // ** addr: 0xab55c0, size: 0x198
    // 0xab55c0: EnterFrame
    //     0xab55c0: stp             fp, lr, [SP, #-0x10]!
    //     0xab55c4: mov             fp, SP
    // 0xab55c8: AllocStack(0x48)
    //     0xab55c8: sub             SP, SP, #0x48
    // 0xab55cc: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab55cc: stur            NULL, [fp, #-8]
    //     0xab55d0: stur            x1, [fp, #-0x10]
    //     0xab55d4: stur            x2, [fp, #-0x18]
    // 0xab55d8: CheckStackOverflow
    //     0xab55d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab55dc: cmp             SP, x16
    //     0xab55e0: b.ls            #0xab5750
    // 0xab55e4: InitAsync() -> Future<void?>
    //     0xab55e4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab55e8: bl              #0x661298  ; InitAsyncStub
    // 0xab55ec: ldur            x1, [fp, #-0x10]
    // 0xab55f0: r0 = SqfliteDatabaseMixinExt.readOnly()
    //     0xab55f0: bl              #0xab4710  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.readOnly
    // 0xab55f4: ldur            x16, [fp, #-0x10]
    // 0xab55f8: stp             x16, NULL, [SP, #0x20]
    // 0xab55fc: ldur            x16, [fp, #-0x18]
    // 0xab5600: r30 = "BEGIN EXCLUSIVE"
    //     0xab5600: add             lr, PP, #0x43, lsl #12  ; [pp+0x43220] "BEGIN EXCLUSIVE"
    //     0xab5604: ldr             lr, [lr, #0x220]
    // 0xab5608: stp             lr, x16, [SP, #0x10]
    // 0xab560c: r16 = true
    //     0xab560c: add             x16, NULL, #0x20  ; true
    // 0xab5610: stp             x16, NULL, [SP]
    // 0xab5614: r4 = const [0x1, 0x5, 0x5, 0x4, beginTransaction, 0x4, null]
    //     0xab5614: add             x4, PP, #0x43, lsl #12  ; [pp+0x43228] List(7) [0x1, 0x5, 0x5, 0x4, "beginTransaction", 0x4, Null]
    //     0xab5618: ldr             x4, [x4, #0x228]
    // 0xab561c: r0 = txnExecute()
    //     0xab561c: bl              #0xab52a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnExecute
    // 0xab5620: mov             x1, x0
    // 0xab5624: stur            x1, [fp, #-0x10]
    // 0xab5628: r0 = Await()
    //     0xab5628: bl              #0x661044  ; AwaitStub
    // 0xab562c: mov             x3, x0
    // 0xab5630: r2 = Null
    //     0xab5630: mov             x2, NULL
    // 0xab5634: r1 = Null
    //     0xab5634: mov             x1, NULL
    // 0xab5638: stur            x3, [fp, #-0x10]
    // 0xab563c: cmp             w0, NULL
    // 0xab5640: b.eq            #0xab56d8
    // 0xab5644: branchIfSmi(r0, 0xab56d8)
    //     0xab5644: tbz             w0, #0, #0xab56d8
    // 0xab5648: r3 = LoadClassIdInstr(r0)
    //     0xab5648: ldur            x3, [x0, #-1]
    //     0xab564c: ubfx            x3, x3, #0xc, #0x14
    // 0xab5650: r17 = 6717
    //     0xab5650: movz            x17, #0x1a3d
    // 0xab5654: cmp             x3, x17
    // 0xab5658: b.eq            #0xab56e0
    // 0xab565c: r4 = LoadClassIdInstr(r0)
    //     0xab565c: ldur            x4, [x0, #-1]
    //     0xab5660: ubfx            x4, x4, #0xc, #0x14
    // 0xab5664: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xab5668: ldr             x3, [x3, #0x18]
    // 0xab566c: ldr             x3, [x3, x4, lsl #3]
    // 0xab5670: LoadField: r3 = r3->field_2b
    //     0xab5670: ldur            w3, [x3, #0x2b]
    // 0xab5674: DecompressPointer r3
    //     0xab5674: add             x3, x3, HEAP, lsl #32
    // 0xab5678: cmp             w3, NULL
    // 0xab567c: b.eq            #0xab56d8
    // 0xab5680: LoadField: r3 = r3->field_f
    //     0xab5680: ldur            w3, [x3, #0xf]
    // 0xab5684: lsr             x3, x3, #3
    // 0xab5688: r17 = 6717
    //     0xab5688: movz            x17, #0x1a3d
    // 0xab568c: cmp             x3, x17
    // 0xab5690: b.eq            #0xab56e0
    // 0xab5694: r3 = SubtypeTestCache
    //     0xab5694: add             x3, PP, #0x43, lsl #12  ; [pp+0x43230] SubtypeTestCache
    //     0xab5698: ldr             x3, [x3, #0x230]
    // 0xab569c: r30 = Subtype1TestCacheStub
    //     0xab569c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xab56a0: LoadField: r30 = r30->field_7
    //     0xab56a0: ldur            lr, [lr, #7]
    // 0xab56a4: blr             lr
    // 0xab56a8: cmp             w7, NULL
    // 0xab56ac: b.eq            #0xab56b8
    // 0xab56b0: tbnz            w7, #4, #0xab56d8
    // 0xab56b4: b               #0xab56e0
    // 0xab56b8: r8 = Map
    //     0xab56b8: add             x8, PP, #0x43, lsl #12  ; [pp+0x43238] Type: Map
    //     0xab56bc: ldr             x8, [x8, #0x238]
    // 0xab56c0: r3 = SubtypeTestCache
    //     0xab56c0: add             x3, PP, #0x43, lsl #12  ; [pp+0x43240] SubtypeTestCache
    //     0xab56c4: ldr             x3, [x3, #0x240]
    // 0xab56c8: r30 = InstanceOfStub
    //     0xab56c8: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xab56cc: LoadField: r30 = r30->field_7
    //     0xab56cc: ldur            lr, [lr, #7]
    // 0xab56d0: blr             lr
    // 0xab56d4: b               #0xab56e4
    // 0xab56d8: r0 = false
    //     0xab56d8: add             x0, NULL, #0x30  ; false
    // 0xab56dc: b               #0xab56e4
    // 0xab56e0: r0 = true
    //     0xab56e0: add             x0, NULL, #0x20  ; true
    // 0xab56e4: tbnz            w0, #4, #0xab5748
    // 0xab56e8: ldur            x1, [fp, #-0x10]
    // 0xab56ec: r0 = LoadClassIdInstr(r1)
    //     0xab56ec: ldur            x0, [x1, #-1]
    //     0xab56f0: ubfx            x0, x0, #0xc, #0x14
    // 0xab56f4: r2 = "transactionId"
    //     0xab56f4: add             x2, PP, #0x43, lsl #12  ; [pp+0x43028] "transactionId"
    //     0xab56f8: ldr             x2, [x2, #0x28]
    // 0xab56fc: r0 = GDT[cid_x0 + -0x114]()
    //     0xab56fc: sub             lr, x0, #0x114
    //     0xab5700: ldr             lr, [x21, lr, lsl #3]
    //     0xab5704: blr             lr
    // 0xab5708: r1 = 60
    //     0xab5708: movz            x1, #0x3c
    // 0xab570c: branchIfSmi(r0, 0xab5718)
    //     0xab570c: tbz             w0, #0, #0xab5718
    // 0xab5710: r1 = LoadClassIdInstr(r0)
    //     0xab5710: ldur            x1, [x0, #-1]
    //     0xab5714: ubfx            x1, x1, #0xc, #0x14
    // 0xab5718: sub             x16, x1, #0x3c
    // 0xab571c: cmp             x16, #1
    // 0xab5720: b.hi            #0xab5748
    // 0xab5724: ldur            x1, [fp, #-0x18]
    // 0xab5728: StoreField: r1->field_7 = r0
    //     0xab5728: stur            w0, [x1, #7]
    //     0xab572c: tbz             w0, #0, #0xab5748
    //     0xab5730: ldurb           w16, [x1, #-1]
    //     0xab5734: ldurb           w17, [x0, #-1]
    //     0xab5738: and             x16, x17, x16, lsr #2
    //     0xab573c: tst             x16, HEAP, lsr #32
    //     0xab5740: b.eq            #0xab5748
    //     0xab5744: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab5748: r0 = Null
    //     0xab5748: mov             x0, NULL
    // 0xab574c: r0 = ReturnAsyncNotFuture()
    //     0xab574c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab5750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab5754: b               #0xab55e4
  }
  static _ SqfliteDatabaseMixinExt.txnGetVersion(/* No info */) async {
    // ** addr: 0xab57bc, size: 0x94
    // 0xab57bc: EnterFrame
    //     0xab57bc: stp             fp, lr, [SP, #-0x10]!
    //     0xab57c0: mov             fp, SP
    // 0xab57c4: AllocStack(0x18)
    //     0xab57c4: sub             SP, SP, #0x18
    // 0xab57c8: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab57c8: stur            NULL, [fp, #-8]
    //     0xab57cc: stur            x1, [fp, #-0x10]
    //     0xab57d0: stur            x2, [fp, #-0x18]
    // 0xab57d4: CheckStackOverflow
    //     0xab57d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab57d8: cmp             SP, x16
    //     0xab57dc: b.ls            #0xab5848
    // 0xab57e0: InitAsync() -> Future<int>
    //     0xab57e0: ldr             x0, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    //     0xab57e4: bl              #0x661298  ; InitAsyncStub
    // 0xab57e8: ldur            x1, [fp, #-0x10]
    // 0xab57ec: ldur            x2, [fp, #-0x18]
    // 0xab57f0: r3 = "PRAGMA user_version"
    //     0xab57f0: add             x3, PP, #0x43, lsl #12  ; [pp+0x43178] "PRAGMA user_version"
    //     0xab57f4: ldr             x3, [x3, #0x178]
    // 0xab57f8: r5 = Null
    //     0xab57f8: mov             x5, NULL
    // 0xab57fc: r0 = txnRawQuery()
    //     0xab57fc: bl              #0xab59dc  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnRawQuery
    // 0xab5800: mov             x1, x0
    // 0xab5804: stur            x1, [fp, #-0x10]
    // 0xab5808: r0 = Await()
    //     0xab5808: bl              #0x661044  ; AwaitStub
    // 0xab580c: mov             x1, x0
    // 0xab5810: r0 = firstIntValue()
    //     0xab5810: bl              #0xab5850  ; [package:sqflite_common/utils/utils.dart] ::firstIntValue
    // 0xab5814: cmp             w0, NULL
    // 0xab5818: b.ne            #0xab5824
    // 0xab581c: r2 = 0
    //     0xab581c: movz            x2, #0
    // 0xab5820: b               #0xab5830
    // 0xab5824: r2 = LoadInt32Instr(r0)
    //     0xab5824: sbfx            x2, x0, #1, #0x1f
    //     0xab5828: tbz             w0, #0, #0xab5830
    //     0xab582c: ldur            x2, [x0, #7]
    // 0xab5830: r0 = BoxInt64Instr(r2)
    //     0xab5830: sbfiz           x0, x2, #1, #0x1f
    //     0xab5834: cmp             x2, x0, asr #1
    //     0xab5838: b.eq            #0xab5844
    //     0xab583c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab5840: stur            x2, [x0, #7]
    // 0xab5844: r0 = ReturnAsyncNotFuture()
    //     0xab5844: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab5848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5848: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab584c: b               #0xab57e0
  }
  static _ SqfliteDatabaseMixinExt.txnSetVersion(/* No info */) async {
    // ** addr: 0xab6808, size: 0x70
    // 0xab6808: EnterFrame
    //     0xab6808: stp             fp, lr, [SP, #-0x10]!
    //     0xab680c: mov             fp, SP
    // 0xab6810: AllocStack(0x40)
    //     0xab6810: sub             SP, SP, #0x40
    // 0xab6814: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab6814: stur            NULL, [fp, #-8]
    //     0xab6818: stur            x1, [fp, #-0x10]
    //     0xab681c: stur            x2, [fp, #-0x18]
    // 0xab6820: CheckStackOverflow
    //     0xab6820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6824: cmp             SP, x16
    //     0xab6828: b.ls            #0xab6870
    // 0xab682c: InitAsync() -> Future<void?>
    //     0xab682c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab6830: bl              #0x661298  ; InitAsyncStub
    // 0xab6834: r16 = <void?>
    //     0xab6834: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xab6838: ldur            lr, [fp, #-0x10]
    // 0xab683c: stp             lr, x16, [SP, #0x18]
    // 0xab6840: ldur            x16, [fp, #-0x18]
    // 0xab6844: r30 = "PRAGMA user_version = 3"
    //     0xab6844: add             lr, PP, #0x43, lsl #12  ; [pp+0x43170] "PRAGMA user_version = 3"
    //     0xab6848: ldr             lr, [lr, #0x170]
    // 0xab684c: stp             lr, x16, [SP, #8]
    // 0xab6850: str             NULL, [SP]
    // 0xab6854: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0xab6854: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0xab6858: r0 = txnExecute()
    //     0xab6858: bl              #0xab52a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnExecute
    // 0xab685c: mov             x1, x0
    // 0xab6860: stur            x1, [fp, #-0x10]
    // 0xab6864: r0 = Await()
    //     0xab6864: bl              #0x661044  ; AwaitStub
    // 0xab6868: r0 = Null
    //     0xab6868: mov             x0, NULL
    // 0xab686c: r0 = ReturnAsyncNotFuture()
    //     0xab686c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab6870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6874: b               #0xab682c
  }
  static _ SqfliteDatabaseMixinExt._txnRawUpdateOrDelete(/* No info */) {
    // ** addr: 0xdc0e2c, size: 0x94
    // 0xdc0e2c: EnterFrame
    //     0xdc0e2c: stp             fp, lr, [SP, #-0x10]!
    //     0xdc0e30: mov             fp, SP
    // 0xdc0e34: AllocStack(0x40)
    //     0xdc0e34: sub             SP, SP, #0x40
    // 0xdc0e38: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xdc0e38: stur            x1, [fp, #-8]
    //     0xdc0e3c: stur            x2, [fp, #-0x10]
    //     0xdc0e40: stur            x3, [fp, #-0x18]
    //     0xdc0e44: stur            x5, [fp, #-0x20]
    // 0xdc0e48: CheckStackOverflow
    //     0xdc0e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc0e4c: cmp             SP, x16
    //     0xdc0e50: b.ls            #0xdc0eb8
    // 0xdc0e54: r1 = 4
    //     0xdc0e54: movz            x1, #0x4
    // 0xdc0e58: r0 = AllocateContext()
    //     0xdc0e58: bl              #0xec126c  ; AllocateContextStub
    // 0xdc0e5c: mov             x1, x0
    // 0xdc0e60: ldur            x0, [fp, #-8]
    // 0xdc0e64: StoreField: r1->field_f = r0
    //     0xdc0e64: stur            w0, [x1, #0xf]
    // 0xdc0e68: ldur            x3, [fp, #-0x10]
    // 0xdc0e6c: StoreField: r1->field_13 = r3
    //     0xdc0e6c: stur            w3, [x1, #0x13]
    // 0xdc0e70: ldur            x2, [fp, #-0x18]
    // 0xdc0e74: ArrayStore: r1[0] = r2  ; List_4
    //     0xdc0e74: stur            w2, [x1, #0x17]
    // 0xdc0e78: ldur            x2, [fp, #-0x20]
    // 0xdc0e7c: StoreField: r1->field_1b = r2
    //     0xdc0e7c: stur            w2, [x1, #0x1b]
    // 0xdc0e80: mov             x2, x1
    // 0xdc0e84: r1 = Function '<anonymous closure>': static.
    //     0xdc0e84: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d548] AnonymousClosure: static (0xdc0ec0), in [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt._txnRawUpdateOrDelete (0xdc0e2c)
    //     0xdc0e88: ldr             x1, [x1, #0x548]
    // 0xdc0e8c: r0 = AllocateClosure()
    //     0xdc0e8c: bl              #0xec1630  ; AllocateClosureStub
    // 0xdc0e90: r16 = <int>
    //     0xdc0e90: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xdc0e94: ldur            lr, [fp, #-8]
    // 0xdc0e98: stp             lr, x16, [SP, #0x10]
    // 0xdc0e9c: ldur            x16, [fp, #-0x10]
    // 0xdc0ea0: stp             x0, x16, [SP]
    // 0xdc0ea4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xdc0ea4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xdc0ea8: r0 = txnSynchronized()
    //     0xdc0ea8: bl              #0xab41e0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized
    // 0xdc0eac: LeaveFrame
    //     0xdc0eac: mov             SP, fp
    //     0xdc0eb0: ldp             fp, lr, [SP], #0x10
    // 0xdc0eb4: ret
    //     0xdc0eb4: ret             
    // 0xdc0eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0eb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0ebc: b               #0xdc0e54
  }
  [closure] static Future<int> <anonymous closure>(dynamic, Transaction?) async {
    // ** addr: 0xdc0ec0, size: 0x10c
    // 0xdc0ec0: EnterFrame
    //     0xdc0ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xdc0ec4: mov             fp, SP
    // 0xdc0ec8: AllocStack(0x40)
    //     0xdc0ec8: sub             SP, SP, #0x40
    // 0xdc0ecc: SetupParameters(dynamic _ /* r1 */)
    //     0xdc0ecc: stur            NULL, [fp, #-8]
    //     0xdc0ed0: movz            x0, #0
    //     0xdc0ed4: add             x1, fp, w0, sxtw #2
    //     0xdc0ed8: ldr             x1, [x1, #0x18]
    //     0xdc0edc: ldur            w2, [x1, #0x17]
    //     0xdc0ee0: add             x2, x2, HEAP, lsl #32
    //     0xdc0ee4: stur            x2, [fp, #-0x10]
    // 0xdc0ee8: CheckStackOverflow
    //     0xdc0ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc0eec: cmp             SP, x16
    //     0xdc0ef0: b.ls            #0xdc0fc4
    // 0xdc0ef4: InitAsync() -> Future<int>
    //     0xdc0ef4: ldr             x0, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    //     0xdc0ef8: bl              #0x661298  ; InitAsyncStub
    // 0xdc0efc: ldur            x0, [fp, #-0x10]
    // 0xdc0f00: LoadField: r3 = r0->field_f
    //     0xdc0f00: ldur            w3, [x0, #0xf]
    // 0xdc0f04: DecompressPointer r3
    //     0xdc0f04: add             x3, x3, HEAP, lsl #32
    // 0xdc0f08: stur            x3, [fp, #-0x18]
    // 0xdc0f0c: r1 = Null
    //     0xdc0f0c: mov             x1, NULL
    // 0xdc0f10: r2 = 8
    //     0xdc0f10: movz            x2, #0x8
    // 0xdc0f14: r0 = AllocateArray()
    //     0xdc0f14: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdc0f18: r16 = "sql"
    //     0xdc0f18: add             x16, PP, #0x43, lsl #12  ; [pp+0x43070] "sql"
    //     0xdc0f1c: ldr             x16, [x16, #0x70]
    // 0xdc0f20: StoreField: r0->field_f = r16
    //     0xdc0f20: stur            w16, [x0, #0xf]
    // 0xdc0f24: ldur            x1, [fp, #-0x10]
    // 0xdc0f28: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xdc0f28: ldur            w2, [x1, #0x17]
    // 0xdc0f2c: DecompressPointer r2
    //     0xdc0f2c: add             x2, x2, HEAP, lsl #32
    // 0xdc0f30: StoreField: r0->field_13 = r2
    //     0xdc0f30: stur            w2, [x0, #0x13]
    // 0xdc0f34: r16 = "arguments"
    //     0xdc0f34: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1a480] "arguments"
    //     0xdc0f38: ldr             x16, [x16, #0x480]
    // 0xdc0f3c: ArrayStore: r0[0] = r16  ; List_4
    //     0xdc0f3c: stur            w16, [x0, #0x17]
    // 0xdc0f40: LoadField: r2 = r1->field_1b
    //     0xdc0f40: ldur            w2, [x1, #0x1b]
    // 0xdc0f44: DecompressPointer r2
    //     0xdc0f44: add             x2, x2, HEAP, lsl #32
    // 0xdc0f48: StoreField: r0->field_1b = r2
    //     0xdc0f48: stur            w2, [x0, #0x1b]
    // 0xdc0f4c: r16 = <String, Object?>
    //     0xdc0f4c: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xdc0f50: ldr             x16, [x16, #0x738]
    // 0xdc0f54: stp             x0, x16, [SP]
    // 0xdc0f58: r0 = Map._fromLiteral()
    //     0xdc0f58: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xdc0f5c: mov             x3, x0
    // 0xdc0f60: ldur            x0, [fp, #-0x10]
    // 0xdc0f64: stur            x3, [fp, #-0x20]
    // 0xdc0f68: LoadField: r2 = r0->field_13
    //     0xdc0f68: ldur            w2, [x0, #0x13]
    // 0xdc0f6c: DecompressPointer r2
    //     0xdc0f6c: add             x2, x2, HEAP, lsl #32
    // 0xdc0f70: ldur            x1, [fp, #-0x18]
    // 0xdc0f74: r0 = SqfliteDatabaseMixinExt.getBaseDatabaseMethodArguments()
    //     0xdc0f74: bl              #0xab4ccc  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.getBaseDatabaseMethodArguments
    // 0xdc0f78: ldur            x1, [fp, #-0x20]
    // 0xdc0f7c: mov             x2, x0
    // 0xdc0f80: r0 = addAll()
    //     0xdc0f80: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0xdc0f84: r16 = <int?>
    //     0xdc0f84: ldr             x16, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xdc0f88: ldur            lr, [fp, #-0x18]
    // 0xdc0f8c: stp             lr, x16, [SP, #0x10]
    // 0xdc0f90: r16 = "update"
    //     0xdc0f90: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d4d8] "update"
    //     0xdc0f94: ldr             x16, [x16, #0x4d8]
    // 0xdc0f98: ldur            lr, [fp, #-0x20]
    // 0xdc0f9c: stp             lr, x16, [SP]
    // 0xdc0fa0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xdc0fa0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xdc0fa4: r0 = safeInvokeMethod()
    //     0xdc0fa4: bl              #0xab484c  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod
    // 0xdc0fa8: mov             x1, x0
    // 0xdc0fac: stur            x1, [fp, #-0x18]
    // 0xdc0fb0: r0 = Await()
    //     0xdc0fb0: bl              #0x661044  ; AwaitStub
    // 0xdc0fb4: cmp             w0, NULL
    // 0xdc0fb8: b.ne            #0xdc0fc0
    // 0xdc0fbc: r0 = 0
    //     0xdc0fbc: movz            x0, #0
    // 0xdc0fc0: r0 = ReturnAsync()
    //     0xdc0fc0: b               #0x6576a4  ; ReturnAsyncStub
    // 0xdc0fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0fc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0fc8: b               #0xdc0ef4
  }
}

// class id: 472, size: 0x8, field offset: 0x8
abstract class SqfliteDatabaseExecutorMixin extends Object
    implements SqfliteDatabaseExecutor {
}

// class id: 473, size: 0x8, field offset: 0x8
abstract class SqfliteDatabaseWithOpenHelperMixin extends Object
    implements SqfliteDatabase {
}

// class id: 474, size: 0x8, field offset: 0x8
abstract class SqfliteDatabaseMixin extends Object
    implements SqfliteDatabase {
}

// class id: 475, size: 0x28, field offset: 0x8
//   transformed mixin,
abstract class _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin extends Object
     with SqfliteDatabaseMixin {

  late String path; // offset: 0xc

  _ doOpen(/* No info */) async {
    // ** addr: 0xab3bc8, size: 0x2a8
    // 0xab3bc8: EnterFrame
    //     0xab3bc8: stp             fp, lr, [SP, #-0x10]!
    //     0xab3bcc: mov             fp, SP
    // 0xab3bd0: AllocStack(0xa8)
    //     0xab3bd0: sub             SP, SP, #0xa8
    // 0xab3bd4: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x78 */, dynamic _ /* r2 => r2, fp-0x80 */)
    //     0xab3bd4: stur            NULL, [fp, #-8]
    //     0xab3bd8: stur            x1, [fp, #-0x78]
    //     0xab3bdc: stur            x2, [fp, #-0x80]
    // 0xab3be0: CheckStackOverflow
    //     0xab3be0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3be4: cmp             SP, x16
    //     0xab3be8: b.ls            #0xab3e68
    // 0xab3bec: r1 = 3
    //     0xab3bec: movz            x1, #0x3
    // 0xab3bf0: r0 = AllocateContext()
    //     0xab3bf0: bl              #0xec126c  ; AllocateContextStub
    // 0xab3bf4: mov             x2, x0
    // 0xab3bf8: ldur            x1, [fp, #-0x78]
    // 0xab3bfc: stur            x2, [fp, #-0x88]
    // 0xab3c00: StoreField: r2->field_f = r1
    //     0xab3c00: stur            w1, [x2, #0xf]
    // 0xab3c04: ldur            x0, [fp, #-0x80]
    // 0xab3c08: StoreField: r2->field_13 = r0
    //     0xab3c08: stur            w0, [x2, #0x13]
    // 0xab3c0c: InitAsync() -> Future<SqfliteDatabase>
    //     0xab3c0c: add             x0, PP, #0x43, lsl #12  ; [pp+0x43118] TypeArguments: <SqfliteDatabase>
    //     0xab3c10: ldr             x0, [x0, #0x118]
    //     0xab3c14: bl              #0x661298  ; InitAsyncStub
    // 0xab3c18: ldur            x2, [fp, #-0x88]
    // 0xab3c1c: LoadField: r0 = r2->field_13
    //     0xab3c1c: ldur            w0, [x2, #0x13]
    // 0xab3c20: DecompressPointer r0
    //     0xab3c20: add             x0, x0, HEAP, lsl #32
    // 0xab3c24: ldur            x3, [fp, #-0x78]
    // 0xab3c28: StoreField: r3->field_2b = r0
    //     0xab3c28: stur            w0, [x3, #0x2b]
    //     0xab3c2c: ldurb           w16, [x3, #-1]
    //     0xab3c30: ldurb           w17, [x0, #-1]
    //     0xab3c34: and             x16, x17, x16, lsr #2
    //     0xab3c38: tst             x16, HEAP, lsr #32
    //     0xab3c3c: b.eq            #0xab3c44
    //     0xab3c40: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xab3c44: mov             x1, x3
    // 0xab3c48: r0 = openDatabase()
    //     0xab3c48: bl              #0xab60b0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::openDatabase
    // 0xab3c4c: mov             x1, x0
    // 0xab3c50: stur            x1, [fp, #-0x80]
    // 0xab3c54: r0 = Await()
    //     0xab3c54: bl              #0x661044  ; AwaitStub
    // 0xab3c58: ldur            x2, [fp, #-0x88]
    // 0xab3c5c: ArrayStore: r2[0] = r0  ; List_4
    //     0xab3c5c: stur            w0, [x2, #0x17]
    //     0xab3c60: tbz             w0, #0, #0xab3c7c
    //     0xab3c64: ldurb           w16, [x2, #-1]
    //     0xab3c68: ldurb           w17, [x0, #-1]
    //     0xab3c6c: and             x16, x17, x16, lsr #2
    //     0xab3c70: tst             x16, HEAP, lsr #32
    //     0xab3c74: b.eq            #0xab3c7c
    //     0xab3c78: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab3c7c: LoadField: r0 = r2->field_13
    //     0xab3c7c: ldur            w0, [x2, #0x13]
    // 0xab3c80: DecompressPointer r0
    //     0xab3c80: add             x0, x0, HEAP, lsl #32
    // 0xab3c84: LoadField: r1 = r0->field_1b
    //     0xab3c84: ldur            w1, [x0, #0x1b]
    // 0xab3c88: DecompressPointer r1
    //     0xab3c88: add             x1, x1, HEAP, lsl #32
    // 0xab3c8c: stur            x1, [fp, #-0x80]
    // 0xab3c90: r0 = InitLateStaticField(0x1120) // [package:sqflite_common/sqlite_api.dart] ::onDatabaseDowngradeDelete
    //     0xab3c90: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab3c94: ldr             x0, [x0, #0x2240]
    //     0xab3c98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab3c9c: cmp             w0, w16
    //     0xab3ca0: b.ne            #0xab3cb0
    //     0xab3ca4: add             x2, PP, #0x43, lsl #12  ; [pp+0x43128] Field <::.onDatabaseDowngradeDelete>: static late final (offset: 0x1120)
    //     0xab3ca8: ldr             x2, [x2, #0x128]
    //     0xab3cac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab3cb0: ldur            x0, [fp, #-0x80]
    // 0xab3cb4: r1 = LoadClassIdInstr(r0)
    //     0xab3cb4: ldur            x1, [x0, #-1]
    //     0xab3cb8: ubfx            x1, x1, #0xc, #0x14
    // 0xab3cbc: r16 = Closure: (Database, int, int) => Future<void> from Function '__onDatabaseDowngradeDelete@1097226320': static.
    //     0xab3cbc: add             x16, PP, #0x43, lsl #12  ; [pp+0x43130] Closure: (Database, int, int) => Future<void> from Function '__onDatabaseDowngradeDelete@1097226320': static. (0x7e54fb4b6cec)
    //     0xab3cc0: ldr             x16, [x16, #0x130]
    // 0xab3cc4: stp             x16, x0, [SP]
    // 0xab3cc8: mov             x0, x1
    // 0xab3ccc: mov             lr, x0
    // 0xab3cd0: ldr             lr, [x21, lr, lsl #3]
    // 0xab3cd4: blr             lr
    // 0xab3cd8: tbnz            w0, #4, #0xab3d1c
    // 0xab3cdc: ldur            x0, [fp, #-0x88]
    // 0xab3ce0: mov             x2, x0
    // 0xab3ce4: r1 = Function 'onDatabaseDowngradeDoDelete':.
    //     0xab3ce4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43138] AnonymousClosure: (0xab6878), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::doOpen (0xab3bc8)
    //     0xab3ce8: ldr             x1, [x1, #0x138]
    // 0xab3cec: r0 = AllocateClosure()
    //     0xab3cec: bl              #0xec1630  ; AllocateClosureStub
    // 0xab3cf0: ldur            x2, [fp, #-0x88]
    // 0xab3cf4: LoadField: r1 = r2->field_13
    //     0xab3cf4: ldur            w1, [x2, #0x13]
    // 0xab3cf8: DecompressPointer r1
    //     0xab3cf8: add             x1, x1, HEAP, lsl #32
    // 0xab3cfc: StoreField: r1->field_1b = r0
    //     0xab3cfc: stur            w0, [x1, #0x1b]
    //     0xab3d00: ldurb           w16, [x1, #-1]
    //     0xab3d04: ldurb           w17, [x0, #-1]
    //     0xab3d08: and             x16, x17, x16, lsr #2
    //     0xab3d0c: tst             x16, HEAP, lsr #32
    //     0xab3d10: b.eq            #0xab3d18
    //     0xab3d14: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab3d18: b               #0xab3d20
    // 0xab3d1c: ldur            x2, [fp, #-0x88]
    // 0xab3d20: ldur            x3, [fp, #-0x78]
    // 0xab3d24: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xab3d24: ldur            w0, [x2, #0x17]
    // 0xab3d28: DecompressPointer r0
    //     0xab3d28: add             x0, x0, HEAP, lsl #32
    // 0xab3d2c: ArrayStore: r3[0] = r0  ; List_4
    //     0xab3d2c: stur            w0, [x3, #0x17]
    //     0xab3d30: tbz             w0, #0, #0xab3d4c
    //     0xab3d34: ldurb           w16, [x3, #-1]
    //     0xab3d38: ldurb           w17, [x0, #-1]
    //     0xab3d3c: and             x16, x17, x16, lsr #2
    //     0xab3d40: tst             x16, HEAP, lsr #32
    //     0xab3d44: b.eq            #0xab3d4c
    //     0xab3d48: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xab3d4c: mov             x1, x3
    // 0xab3d50: r0 = SqfliteDatabaseExecutorExt.getVersion()
    //     0xab3d50: bl              #0xab5770  ; [package:sqflite_common/sqlite_api.dart] ::SqfliteDatabaseExecutorExt.getVersion
    // 0xab3d54: mov             x1, x0
    // 0xab3d58: stur            x1, [fp, #-0x80]
    // 0xab3d5c: r0 = Await()
    //     0xab3d5c: bl              #0x661044  ; AwaitStub
    // 0xab3d60: cmp             w0, #6
    // 0xab3d64: b.eq            #0xab3da0
    // 0xab3d68: ldur            x2, [fp, #-0x88]
    // 0xab3d6c: r1 = Function '<anonymous closure>':.
    //     0xab3d6c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43140] AnonymousClosure: (0xab64f4), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::doOpen (0xab3bc8)
    //     0xab3d70: ldr             x1, [x1, #0x140]
    // 0xab3d74: r0 = AllocateClosure()
    //     0xab3d74: bl              #0xec1630  ; AllocateClosureStub
    // 0xab3d78: r16 = <Null?>
    //     0xab3d78: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xab3d7c: ldur            lr, [fp, #-0x78]
    // 0xab3d80: stp             lr, x16, [SP, #0x10]
    // 0xab3d84: r16 = true
    //     0xab3d84: add             x16, NULL, #0x20  ; true
    // 0xab3d88: stp             x16, x0, [SP]
    // 0xab3d8c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab3d8c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab3d90: r0 = transaction()
    //     0xab3d90: bl              #0xab4d94  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::transaction
    // 0xab3d94: mov             x1, x0
    // 0xab3d98: stur            x1, [fp, #-0x80]
    // 0xab3d9c: r0 = Await()
    //     0xab3d9c: bl              #0x661044  ; AwaitStub
    // 0xab3da0: ldur            x0, [fp, #-0x78]
    // 0xab3da4: StoreField: r0->field_f = rNULL
    //     0xab3da4: stur            NULL, [x0, #0xf]
    // 0xab3da8: r0 = ReturnAsyncNotFuture()
    //     0xab3da8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab3dac: sub             SP, fp, #0xa8
    // 0xab3db0: mov             x4, x0
    // 0xab3db4: mov             x3, x1
    // 0xab3db8: ldur            x2, [fp, #-0x60]
    // 0xab3dbc: stur            x0, [fp, #-0x78]
    // 0xab3dc0: stur            x1, [fp, #-0x80]
    // 0xab3dc4: StoreField: r2->field_f = rNULL
    //     0xab3dc4: stur            NULL, [x2, #0xf]
    // 0xab3dc8: mov             x0, x4
    // 0xab3dcc: mov             x1, x3
    // 0xab3dd0: r0 = ReThrow()
    //     0xab3dd0: bl              #0xec048c  ; ReThrowStub
    // 0xab3dd4: brk             #0
    // 0xab3dd8: sub             SP, fp, #0xa8
    // 0xab3ddc: mov             x3, x0
    // 0xab3de0: stur            x0, [fp, #-0x78]
    // 0xab3de4: mov             x0, x1
    // 0xab3de8: stur            x1, [fp, #-0x80]
    // 0xab3dec: r1 = Null
    //     0xab3dec: mov             x1, NULL
    // 0xab3df0: r2 = 6
    //     0xab3df0: movz            x2, #0x6
    // 0xab3df4: r0 = AllocateArray()
    //     0xab3df4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab3df8: r16 = "error "
    //     0xab3df8: add             x16, PP, #0x43, lsl #12  ; [pp+0x43148] "error "
    //     0xab3dfc: ldr             x16, [x16, #0x148]
    // 0xab3e00: StoreField: r0->field_f = r16
    //     0xab3e00: stur            w16, [x0, #0xf]
    // 0xab3e04: ldur            x1, [fp, #-0x78]
    // 0xab3e08: StoreField: r0->field_13 = r1
    //     0xab3e08: stur            w1, [x0, #0x13]
    // 0xab3e0c: r16 = " during open, closing..."
    //     0xab3e0c: add             x16, PP, #0x43, lsl #12  ; [pp+0x43150] " during open, closing..."
    //     0xab3e10: ldr             x16, [x16, #0x150]
    // 0xab3e14: ArrayStore: r0[0] = r16  ; List_4
    //     0xab3e14: stur            w16, [x0, #0x17]
    // 0xab3e18: str             x0, [SP]
    // 0xab3e1c: r0 = _interpolate()
    //     0xab3e1c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab3e20: mov             x1, x0
    // 0xab3e24: r0 = print()
    //     0xab3e24: bl              #0x63fe38  ; [dart:core] ::print
    // 0xab3e28: ldur            x1, [fp, #-0x60]
    // 0xab3e2c: r0 = closeDatabase()
    //     0xab3e2c: bl              #0xab3e70  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::closeDatabase
    // 0xab3e30: mov             x1, x0
    // 0xab3e34: stur            x1, [fp, #-0x88]
    // 0xab3e38: r0 = Await()
    //     0xab3e38: bl              #0x661044  ; AwaitStub
    // 0xab3e3c: ldur            x0, [fp, #-0x78]
    // 0xab3e40: ldur            x1, [fp, #-0x80]
    // 0xab3e44: r0 = ReThrow()
    //     0xab3e44: bl              #0xec048c  ; ReThrowStub
    // 0xab3e48: brk             #0
    // 0xab3e4c: sub             SP, fp, #0xa8
    // 0xab3e50: mov             x2, x0
    // 0xab3e54: ldur            x0, [fp, #-0x60]
    // 0xab3e58: StoreField: r0->field_f = rNULL
    //     0xab3e58: stur            NULL, [x0, #0xf]
    // 0xab3e5c: mov             x0, x2
    // 0xab3e60: r0 = ReThrow()
    //     0xab3e60: bl              #0xec048c  ; ReThrowStub
    // 0xab3e64: brk             #0
    // 0xab3e68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3e68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3e6c: b               #0xab3bec
  }
  _ closeDatabase(/* No info */) async {
    // ** addr: 0xab3e70, size: 0x6c
    // 0xab3e70: EnterFrame
    //     0xab3e70: stp             fp, lr, [SP, #-0x10]!
    //     0xab3e74: mov             fp, SP
    // 0xab3e78: AllocStack(0x10)
    //     0xab3e78: sub             SP, SP, #0x10
    // 0xab3e7c: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x10 */)
    //     0xab3e7c: stur            NULL, [fp, #-8]
    //     0xab3e80: stur            x1, [fp, #-0x10]
    // 0xab3e84: CheckStackOverflow
    //     0xab3e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3e88: cmp             SP, x16
    //     0xab3e8c: b.ls            #0xab3ed0
    // 0xab3e90: InitAsync() -> Future<void?>
    //     0xab3e90: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab3e94: bl              #0x661298  ; InitAsyncStub
    // 0xab3e98: ldur            x1, [fp, #-0x10]
    // 0xab3e9c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xab3e9c: ldur            w0, [x1, #0x17]
    // 0xab3ea0: DecompressPointer r0
    //     0xab3ea0: add             x0, x0, HEAP, lsl #32
    // 0xab3ea4: cmp             w0, NULL
    // 0xab3ea8: b.eq            #0xab3ed8
    // 0xab3eac: r2 = LoadInt32Instr(r0)
    //     0xab3eac: sbfx            x2, x0, #1, #0x1f
    //     0xab3eb0: tbz             w0, #0, #0xab3eb8
    //     0xab3eb4: ldur            x2, [x0, #7]
    // 0xab3eb8: r0 = _closeDatabase()
    //     0xab3eb8: bl              #0xab3edc  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::_closeDatabase
    // 0xab3ebc: mov             x1, x0
    // 0xab3ec0: stur            x1, [fp, #-0x10]
    // 0xab3ec4: r0 = Await()
    //     0xab3ec4: bl              #0x661044  ; AwaitStub
    // 0xab3ec8: r0 = Null
    //     0xab3ec8: mov             x0, NULL
    // 0xab3ecc: r0 = ReturnAsyncNotFuture()
    //     0xab3ecc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab3ed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3ed0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3ed4: b               #0xab3e90
    // 0xab3ed8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab3ed8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _closeDatabase(/* No info */) async {
    // ** addr: 0xab3edc, size: 0xb4
    // 0xab3edc: EnterFrame
    //     0xab3edc: stp             fp, lr, [SP, #-0x10]!
    //     0xab3ee0: mov             fp, SP
    // 0xab3ee4: AllocStack(0x40)
    //     0xab3ee4: sub             SP, SP, #0x40
    // 0xab3ee8: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab3ee8: stur            NULL, [fp, #-8]
    //     0xab3eec: stur            x1, [fp, #-0x10]
    //     0xab3ef0: stur            x2, [fp, #-0x18]
    // 0xab3ef4: CheckStackOverflow
    //     0xab3ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3ef8: cmp             SP, x16
    //     0xab3efc: b.ls            #0xab3f88
    // 0xab3f00: r1 = 2
    //     0xab3f00: movz            x1, #0x2
    // 0xab3f04: r0 = AllocateContext()
    //     0xab3f04: bl              #0xec126c  ; AllocateContextStub
    // 0xab3f08: mov             x3, x0
    // 0xab3f0c: ldur            x2, [fp, #-0x10]
    // 0xab3f10: stur            x3, [fp, #-0x20]
    // 0xab3f14: StoreField: r3->field_f = r2
    //     0xab3f14: stur            w2, [x3, #0xf]
    // 0xab3f18: ldur            x4, [fp, #-0x18]
    // 0xab3f1c: r0 = BoxInt64Instr(r4)
    //     0xab3f1c: sbfiz           x0, x4, #1, #0x1f
    //     0xab3f20: cmp             x4, x0, asr #1
    //     0xab3f24: b.eq            #0xab3f30
    //     0xab3f28: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab3f2c: stur            x4, [x0, #7]
    // 0xab3f30: StoreField: r3->field_13 = r0
    //     0xab3f30: stur            w0, [x3, #0x13]
    // 0xab3f34: InitAsync() -> Future<void?>
    //     0xab3f34: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab3f38: bl              #0x661298  ; InitAsyncStub
    // 0xab3f3c: ldur            x0, [fp, #-0x10]
    // 0xab3f40: LoadField: r3 = r0->field_23
    //     0xab3f40: ldur            w3, [x0, #0x23]
    // 0xab3f44: DecompressPointer r3
    //     0xab3f44: add             x3, x3, HEAP, lsl #32
    // 0xab3f48: ldur            x2, [fp, #-0x20]
    // 0xab3f4c: stur            x3, [fp, #-0x28]
    // 0xab3f50: r1 = Function '<anonymous closure>':.
    //     0xab3f50: add             x1, PP, #0x43, lsl #12  ; [pp+0x43260] AnonymousClosure: (0xab3f90), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::_closeDatabase (0xab3edc)
    //     0xab3f54: ldr             x1, [x1, #0x260]
    // 0xab3f58: r0 = AllocateClosure()
    //     0xab3f58: bl              #0xec1630  ; AllocateClosureStub
    // 0xab3f5c: r16 = <Null?>
    //     0xab3f5c: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xab3f60: ldur            lr, [fp, #-0x28]
    // 0xab3f64: stp             lr, x16, [SP, #8]
    // 0xab3f68: str             x0, [SP]
    // 0xab3f6c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab3f6c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab3f70: r0 = synchronized()
    //     0xab3f70: bl              #0xaaf038  ; [package:synchronized/src/basic_lock.dart] BasicLock::synchronized
    // 0xab3f74: mov             x1, x0
    // 0xab3f78: stur            x1, [fp, #-0x10]
    // 0xab3f7c: r0 = Await()
    //     0xab3f7c: bl              #0x661044  ; AwaitStub
    // 0xab3f80: r0 = Null
    //     0xab3f80: mov             x0, NULL
    // 0xab3f84: r0 = ReturnAsyncNotFuture()
    //     0xab3f84: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab3f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3f8c: b               #0xab3f00
  }
  [closure] Future<Null> <anonymous closure>(dynamic) async {
    // ** addr: 0xab3f90, size: 0x1e0
    // 0xab3f90: EnterFrame
    //     0xab3f90: stp             fp, lr, [SP, #-0x10]!
    //     0xab3f94: mov             fp, SP
    // 0xab3f98: AllocStack(0x88)
    //     0xab3f98: sub             SP, SP, #0x88
    // 0xab3f9c: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1, fp-0x58 */)
    //     0xab3f9c: stur            NULL, [fp, #-8]
    //     0xab3fa0: movz            x0, #0
    //     0xab3fa4: add             x1, fp, w0, sxtw #2
    //     0xab3fa8: ldr             x1, [x1, #0x10]
    //     0xab3fac: stur            x1, [fp, #-0x58]
    //     0xab3fb0: ldur            w2, [x1, #0x17]
    //     0xab3fb4: add             x2, x2, HEAP, lsl #32
    //     0xab3fb8: stur            x2, [fp, #-0x50]
    // 0xab3fbc: CheckStackOverflow
    //     0xab3fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3fc0: cmp             SP, x16
    //     0xab3fc4: b.ls            #0xab4168
    // 0xab3fc8: InitAsync() -> Future<Null?>
    //     0xab3fc8: ldr             x0, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    //     0xab3fcc: bl              #0x661298  ; InitAsyncStub
    // 0xab3fd0: ldur            x2, [fp, #-0x50]
    // 0xab3fd4: LoadField: r1 = r2->field_f
    //     0xab3fd4: ldur            w1, [x2, #0xf]
    // 0xab3fd8: DecompressPointer r1
    //     0xab3fd8: add             x1, x1, HEAP, lsl #32
    // 0xab3fdc: LoadField: r0 = r1->field_7
    //     0xab3fdc: ldur            w0, [x1, #7]
    // 0xab3fe0: DecompressPointer r0
    //     0xab3fe0: add             x0, x0, HEAP, lsl #32
    // 0xab3fe4: tbz             w0, #4, #0xab4160
    // 0xab3fe8: r0 = true
    //     0xab3fe8: add             x0, NULL, #0x20  ; true
    // 0xab3fec: StoreField: r1->field_7 = r0
    //     0xab3fec: stur            w0, [x1, #7]
    // 0xab3ff0: r0 = SqfliteDatabaseMixinExt.readOnly()
    //     0xab3ff0: bl              #0xab4710  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.readOnly
    // 0xab3ff4: ldur            x0, [fp, #-0x50]
    // 0xab3ff8: LoadField: r3 = r0->field_f
    //     0xab3ff8: ldur            w3, [x0, #0xf]
    // 0xab3ffc: DecompressPointer r3
    //     0xab3ffc: add             x3, x3, HEAP, lsl #32
    // 0xab4000: stur            x3, [fp, #-0x60]
    // 0xab4004: LoadField: r1 = r3->field_1b
    //     0xab4004: ldur            w1, [x3, #0x1b]
    // 0xab4008: DecompressPointer r1
    //     0xab4008: add             x1, x1, HEAP, lsl #32
    // 0xab400c: tbnz            w1, #4, #0xab4064
    // 0xab4010: LoadField: r4 = r3->field_f
    //     0xab4010: ldur            w4, [x3, #0xf]
    // 0xab4014: DecompressPointer r4
    //     0xab4014: add             x4, x4, HEAP, lsl #32
    // 0xab4018: mov             x2, x0
    // 0xab401c: stur            x4, [fp, #-0x58]
    // 0xab4020: r1 = Function '<anonymous closure>':.
    //     0xab4020: add             x1, PP, #0x43, lsl #12  ; [pp+0x43268] AnonymousClosure: (0xab49dc), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::_closeDatabase (0xab3edc)
    //     0xab4024: ldr             x1, [x1, #0x268]
    // 0xab4028: r0 = AllocateClosure()
    //     0xab4028: bl              #0xec1630  ; AllocateClosureStub
    // 0xab402c: stur            x0, [fp, #-0x68]
    // 0xab4030: r16 = <Null?>
    //     0xab4030: ldr             x16, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    // 0xab4034: ldur            lr, [fp, #-0x60]
    // 0xab4038: stp             lr, x16, [SP, #0x10]
    // 0xab403c: ldur            x16, [fp, #-0x58]
    // 0xab4040: stp             x0, x16, [SP]
    // 0xab4044: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab4044: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab4048: r0 = txnSynchronized()
    //     0xab4048: bl              #0xab41e0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized
    // 0xab404c: mov             x1, x0
    // 0xab4050: stur            x1, [fp, #-0x58]
    // 0xab4054: r0 = Await()
    //     0xab4054: bl              #0x661044  ; AwaitStub
    // 0xab4058: ldur            x1, [fp, #-0x50]
    // 0xab405c: r0 = Null
    //     0xab405c: mov             x0, NULL
    // 0xab4060: b               #0xab40c0
    // 0xab4064: ldur            x3, [fp, #-0x50]
    // 0xab4068: r0 = Null
    //     0xab4068: mov             x0, NULL
    // 0xab406c: b               #0xab40c4
    // 0xab4070: sub             SP, fp, #0x88
    // 0xab4074: stur            x0, [fp, #-0x50]
    // 0xab4078: r1 = Null
    //     0xab4078: mov             x1, NULL
    // 0xab407c: r2 = 6
    //     0xab407c: movz            x2, #0x6
    // 0xab4080: r0 = AllocateArray()
    //     0xab4080: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab4084: r16 = "Error "
    //     0xab4084: add             x16, PP, #0x43, lsl #12  ; [pp+0x43270] "Error "
    //     0xab4088: ldr             x16, [x16, #0x270]
    // 0xab408c: StoreField: r0->field_f = r16
    //     0xab408c: stur            w16, [x0, #0xf]
    // 0xab4090: ldur            x1, [fp, #-0x50]
    // 0xab4094: StoreField: r0->field_13 = r1
    //     0xab4094: stur            w1, [x0, #0x13]
    // 0xab4098: r16 = " before rollback"
    //     0xab4098: add             x16, PP, #0x43, lsl #12  ; [pp+0x43278] " before rollback"
    //     0xab409c: ldr             x16, [x16, #0x278]
    // 0xab40a0: ArrayStore: r0[0] = r16  ; List_4
    //     0xab40a0: stur            w16, [x0, #0x17]
    // 0xab40a4: str             x0, [SP]
    // 0xab40a8: r0 = _interpolate()
    //     0xab40a8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab40ac: mov             x1, x0
    // 0xab40b0: r0 = print()
    //     0xab40b0: bl              #0x63fe38  ; [dart:core] ::print
    // 0xab40b4: ldur            x0, [fp, #-0x20]
    // 0xab40b8: mov             x1, x0
    // 0xab40bc: ldur            x0, [fp, #-0x50]
    // 0xab40c0: mov             x3, x1
    // 0xab40c4: stur            x3, [fp, #-0x58]
    // 0xab40c8: stur            x0, [fp, #-0x60]
    // 0xab40cc: LoadField: r4 = r3->field_f
    //     0xab40cc: ldur            w4, [x3, #0xf]
    // 0xab40d0: DecompressPointer r4
    //     0xab40d0: add             x4, x4, HEAP, lsl #32
    // 0xab40d4: mov             x2, x3
    // 0xab40d8: stur            x4, [fp, #-0x50]
    // 0xab40dc: r1 = Function '<anonymous closure>':.
    //     0xab40dc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43280] AnonymousClosure: (0xab4744), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::_closeDatabase (0xab3edc)
    //     0xab40e0: ldr             x1, [x1, #0x280]
    // 0xab40e4: r0 = AllocateClosure()
    //     0xab40e4: bl              #0xec1630  ; AllocateClosureStub
    // 0xab40e8: ldur            x16, [fp, #-0x50]
    // 0xab40ec: stp             x16, NULL, [SP, #8]
    // 0xab40f0: str             x0, [SP]
    // 0xab40f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab40f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab40f8: r0 = safeAction()
    //     0xab40f8: bl              #0xab4170  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeAction
    // 0xab40fc: mov             x1, x0
    // 0xab4100: stur            x1, [fp, #-0x50]
    // 0xab4104: r0 = Await()
    //     0xab4104: bl              #0x661044  ; AwaitStub
    // 0xab4108: b               #0xab4160
    // 0xab410c: sub             SP, fp, #0x88
    // 0xab4110: stur            x0, [fp, #-0x50]
    // 0xab4114: r1 = Null
    //     0xab4114: mov             x1, NULL
    // 0xab4118: r2 = 8
    //     0xab4118: movz            x2, #0x8
    // 0xab411c: r0 = AllocateArray()
    //     0xab411c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab4120: r16 = "error "
    //     0xab4120: add             x16, PP, #0x43, lsl #12  ; [pp+0x43148] "error "
    //     0xab4124: ldr             x16, [x16, #0x148]
    // 0xab4128: StoreField: r0->field_f = r16
    //     0xab4128: stur            w16, [x0, #0xf]
    // 0xab412c: ldur            x1, [fp, #-0x50]
    // 0xab4130: StoreField: r0->field_13 = r1
    //     0xab4130: stur            w1, [x0, #0x13]
    // 0xab4134: r16 = " closing database "
    //     0xab4134: add             x16, PP, #0x43, lsl #12  ; [pp+0x43288] " closing database "
    //     0xab4138: ldr             x16, [x16, #0x288]
    // 0xab413c: ArrayStore: r0[0] = r16  ; List_4
    //     0xab413c: stur            w16, [x0, #0x17]
    // 0xab4140: ldur            x1, [fp, #-0x20]
    // 0xab4144: LoadField: r2 = r1->field_13
    //     0xab4144: ldur            w2, [x1, #0x13]
    // 0xab4148: DecompressPointer r2
    //     0xab4148: add             x2, x2, HEAP, lsl #32
    // 0xab414c: StoreField: r0->field_1b = r2
    //     0xab414c: stur            w2, [x0, #0x1b]
    // 0xab4150: str             x0, [SP]
    // 0xab4154: r0 = _interpolate()
    //     0xab4154: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab4158: mov             x1, x0
    // 0xab415c: r0 = print()
    //     0xab415c: bl              #0x63fe38  ; [dart:core] ::print
    // 0xab4160: r0 = Null
    //     0xab4160: mov             x0, NULL
    // 0xab4164: r0 = ReturnAsyncNotFuture()
    //     0xab4164: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab4168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab416c: b               #0xab3fc8
  }
  _ safeAction(/* No info */) {
    // ** addr: 0xab4170, size: 0x70
    // 0xab4170: EnterFrame
    //     0xab4170: stp             fp, lr, [SP, #-0x10]!
    //     0xab4174: mov             fp, SP
    // 0xab4178: AllocStack(0x10)
    //     0xab4178: sub             SP, SP, #0x10
    // 0xab417c: SetupParameters()
    //     0xab417c: ldur            w0, [x4, #0xf]
    //     0xab4180: cbnz            w0, #0xab418c
    //     0xab4184: mov             x1, NULL
    //     0xab4188: b               #0xab4198
    //     0xab418c: ldur            w0, [x4, #0x17]
    //     0xab4190: add             x1, fp, w0, sxtw #2
    //     0xab4194: ldr             x1, [x1, #0x10]
    //     0xab4198: ldr             x0, [fp, #0x18]
    // 0xab419c: CheckStackOverflow
    //     0xab419c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab41a0: cmp             SP, x16
    //     0xab41a4: b.ls            #0xab41d4
    // 0xab41a8: LoadField: r2 = r0->field_27
    //     0xab41a8: ldur            w2, [x0, #0x27]
    // 0xab41ac: DecompressPointer r2
    //     0xab41ac: add             x2, x2, HEAP, lsl #32
    // 0xab41b0: cmp             w2, NULL
    // 0xab41b4: b.eq            #0xab41dc
    // 0xab41b8: ldr             x16, [fp, #0x10]
    // 0xab41bc: stp             x16, x1, [SP]
    // 0xab41c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab41c0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab41c4: r0 = wrapDatabaseException()
    //     0xab41c4: bl              #0xab351c  ; [package:sqflite_platform_interface/src/platform_exception.dart] ::wrapDatabaseException
    // 0xab41c8: LeaveFrame
    //     0xab41c8: mov             SP, fp
    //     0xab41cc: ldp             fp, lr, [SP], #0x10
    // 0xab41d0: ret
    //     0xab41d0: ret             
    // 0xab41d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab41d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab41d8: b               #0xab41a8
    // 0xab41dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab41dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  Future<Y0> txnSynchronized<Y0>(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin, Transaction?, (dynamic, Transaction?) => Future<Y0>) async {
    // ** addr: 0xab41e0, size: 0x308
    // 0xab41e0: EnterFrame
    //     0xab41e0: stp             fp, lr, [SP, #-0x10]!
    //     0xab41e4: mov             fp, SP
    // 0xab41e8: AllocStack(0xc8)
    //     0xab41e8: sub             SP, SP, #0xc8
    // 0xab41ec: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1, fp-0xa0 */, dynamic _ /* r2, fp-0x98 */, dynamic _ /* r3, fp-0x90 */)
    //     0xab41ec: stur            NULL, [fp, #-8]
    //     0xab41f0: movz            x0, #0
    //     0xab41f4: stur            x4, [fp, #-0xa8]
    //     0xab41f8: add             x1, fp, w0, sxtw #2
    //     0xab41fc: ldr             x1, [x1, #0x20]
    //     0xab4200: stur            x1, [fp, #-0xa0]
    //     0xab4204: add             x2, fp, w0, sxtw #2
    //     0xab4208: ldr             x2, [x2, #0x18]
    //     0xab420c: stur            x2, [fp, #-0x98]
    //     0xab4210: add             x3, fp, w0, sxtw #2
    //     0xab4214: ldr             x3, [x3, #0x10]
    //     0xab4218: stur            x3, [fp, #-0x90]
    //     0xab421c: ldur            w0, [x4, #0xf]
    //     0xab4220: cbnz            w0, #0xab422c
    //     0xab4224: mov             x0, NULL
    //     0xab4228: b               #0xab423c
    //     0xab422c: ldur            w0, [x4, #0x17]
    //     0xab4230: add             x5, fp, w0, sxtw #2
    //     0xab4234: ldr             x5, [x5, #0x10]
    //     0xab4238: mov             x0, x5
    //     0xab423c: stur            x0, [fp, #-0x88]
    // 0xab4240: CheckStackOverflow
    //     0xab4240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4244: cmp             SP, x16
    //     0xab4248: b.ls            #0xab44dc
    // 0xab424c: r1 = 4
    //     0xab424c: movz            x1, #0x4
    // 0xab4250: r0 = AllocateContext()
    //     0xab4250: bl              #0xec126c  ; AllocateContextStub
    // 0xab4254: mov             x1, x0
    // 0xab4258: ldur            x0, [fp, #-0x98]
    // 0xab425c: stur            x1, [fp, #-0xb0]
    // 0xab4260: StoreField: r1->field_f = r0
    //     0xab4260: stur            w0, [x1, #0xf]
    // 0xab4264: ldur            x0, [fp, #-0x90]
    // 0xab4268: StoreField: r1->field_13 = r0
    //     0xab4268: stur            w0, [x1, #0x13]
    // 0xab426c: ldur            x0, [fp, #-0x88]
    // 0xab4270: r0 = InitAsync()
    //     0xab4270: bl              #0x661298  ; InitAsyncStub
    // 0xab4274: ldur            x2, [fp, #-0xb0]
    // 0xab4278: LoadField: r1 = r2->field_f
    //     0xab4278: ldur            w1, [x2, #0xf]
    // 0xab427c: DecompressPointer r1
    //     0xab427c: add             x1, x1, HEAP, lsl #32
    // 0xab4280: cmp             w1, NULL
    // 0xab4284: b.eq            #0xab42c8
    // 0xab4288: r0 = TransactionPrvExt.checkNotClosed()
    //     0xab4288: bl              #0xab44e8  ; [package:sqflite_common/src/transaction.dart] ::TransactionPrvExt.checkNotClosed
    // 0xab428c: ldur            x2, [fp, #-0xb0]
    // 0xab4290: LoadField: r1 = r2->field_13
    //     0xab4290: ldur            w1, [x2, #0x13]
    // 0xab4294: DecompressPointer r1
    //     0xab4294: add             x1, x1, HEAP, lsl #32
    // 0xab4298: stur            x1, [fp, #-0x90]
    // 0xab429c: LoadField: r0 = r2->field_f
    //     0xab429c: ldur            w0, [x2, #0xf]
    // 0xab42a0: DecompressPointer r0
    //     0xab42a0: add             x0, x0, HEAP, lsl #32
    // 0xab42a4: stp             x0, x1, [SP]
    // 0xab42a8: mov             x0, x1
    // 0xab42ac: ClosureCall
    //     0xab42ac: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xab42b0: ldur            x2, [x0, #0x1f]
    //     0xab42b4: blr             x2
    // 0xab42b8: mov             x1, x0
    // 0xab42bc: stur            x1, [fp, #-0x90]
    // 0xab42c0: r0 = Await()
    //     0xab42c0: bl              #0x661044  ; AwaitStub
    // 0xab42c4: r0 = ReturnAsync()
    //     0xab42c4: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab42c8: r0 = InitLateStaticField(0x1124) // [package:sqflite_common/src/utils.dart] ::lockWarningDuration
    //     0xab42c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab42cc: ldr             x0, [x0, #0x2248]
    //     0xab42d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab42d4: cmp             w0, w16
    //     0xab42d8: b.ne            #0xab42e8
    //     0xab42dc: add             x2, PP, #0x43, lsl #12  ; [pp+0x43090] Field <::.lockWarningDuration>: static late (offset: 0x1124)
    //     0xab42e0: ldr             x2, [x2, #0x90]
    //     0xab42e4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xab42e8: cmp             w0, NULL
    // 0xab42ec: b.eq            #0xab4328
    // 0xab42f0: r0 = InitLateStaticField(0x1128) // [package:sqflite_common/src/utils.dart] ::lockWarningCallback
    //     0xab42f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab42f4: ldr             x0, [x0, #0x2250]
    //     0xab42f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab42fc: cmp             w0, w16
    //     0xab4300: b.ne            #0xab4310
    //     0xab4304: add             x2, PP, #0x43, lsl #12  ; [pp+0x43098] Field <::.lockWarningCallback>: static late (offset: 0x1128)
    //     0xab4308: ldr             x2, [x2, #0x98]
    //     0xab430c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xab4310: cmp             w0, NULL
    // 0xab4314: r16 = true
    //     0xab4314: add             x16, NULL, #0x20  ; true
    // 0xab4318: r17 = false
    //     0xab4318: add             x17, NULL, #0x30  ; false
    // 0xab431c: csel            x1, x16, x17, ne
    // 0xab4320: mov             x3, x1
    // 0xab4324: b               #0xab432c
    // 0xab4328: r3 = false
    //     0xab4328: add             x3, NULL, #0x30  ; false
    // 0xab432c: ldur            x2, [fp, #-0xb0]
    // 0xab4330: r0 = Sentinel
    //     0xab4330: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab4334: stur            x3, [fp, #-0x90]
    // 0xab4338: ArrayStore: r2[0] = r3  ; List_4
    //     0xab4338: stur            w3, [x2, #0x17]
    // 0xab433c: StoreField: r2->field_1b = r0
    //     0xab433c: stur            w0, [x2, #0x1b]
    // 0xab4340: tbnz            w3, #4, #0xab43b8
    // 0xab4344: r1 = Null
    //     0xab4344: mov             x1, NULL
    // 0xab4348: r0 = _Future()
    //     0xab4348: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xab434c: stur            x0, [fp, #-0x98]
    // 0xab4350: StoreField: r0->field_b = rZR
    //     0xab4350: stur            xzr, [x0, #0xb]
    // 0xab4354: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0xab4354: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab4358: ldr             x0, [x0, #0x7a0]
    //     0xab435c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab4360: cmp             w0, w16
    //     0xab4364: b.ne            #0xab4370
    //     0xab4368: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0xab436c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xab4370: mov             x1, x0
    // 0xab4374: ldur            x0, [fp, #-0x98]
    // 0xab4378: StoreField: r0->field_13 = r1
    //     0xab4378: stur            w1, [x0, #0x13]
    // 0xab437c: r1 = Null
    //     0xab437c: mov             x1, NULL
    // 0xab4380: r0 = _AsyncCompleter()
    //     0xab4380: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xab4384: mov             x1, x0
    // 0xab4388: ldur            x0, [fp, #-0x98]
    // 0xab438c: StoreField: r1->field_b = r0
    //     0xab438c: stur            w0, [x1, #0xb]
    // 0xab4390: mov             x0, x1
    // 0xab4394: ldur            x3, [fp, #-0xb0]
    // 0xab4398: StoreField: r3->field_1b = r0
    //     0xab4398: stur            w0, [x3, #0x1b]
    //     0xab439c: ldurb           w16, [x3, #-1]
    //     0xab43a0: ldurb           w17, [x0, #-1]
    //     0xab43a4: and             x16, x17, x16, lsr #2
    //     0xab43a8: tst             x16, HEAP, lsr #32
    //     0xab43ac: b.eq            #0xab43b4
    //     0xab43b0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xab43b4: b               #0xab43bc
    // 0xab43b8: mov             x3, x2
    // 0xab43bc: ldur            x1, [fp, #-0xa0]
    // 0xab43c0: ldur            x4, [fp, #-0x88]
    // 0xab43c4: ldur            x0, [fp, #-0x90]
    // 0xab43c8: LoadField: r5 = r1->field_13
    //     0xab43c8: ldur            w5, [x1, #0x13]
    // 0xab43cc: DecompressPointer r5
    //     0xab43cc: add             x5, x5, HEAP, lsl #32
    // 0xab43d0: mov             x2, x3
    // 0xab43d4: stur            x5, [fp, #-0x98]
    // 0xab43d8: r1 = Function '<anonymous closure>':.
    //     0xab43d8: add             x1, PP, #0x43, lsl #12  ; [pp+0x430a0] AnonymousClosure: (0xab4650), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized (0xab41e0)
    //     0xab43dc: ldr             x1, [x1, #0xa0]
    // 0xab43e0: r0 = AllocateClosure()
    //     0xab43e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xab43e4: mov             x1, x0
    // 0xab43e8: ldur            x0, [fp, #-0x88]
    // 0xab43ec: StoreField: r1->field_b = r0
    //     0xab43ec: stur            w0, [x1, #0xb]
    // 0xab43f0: ldur            x16, [fp, #-0x98]
    // 0xab43f4: stp             x16, x0, [SP, #8]
    // 0xab43f8: str             x1, [SP]
    // 0xab43fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab43fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab4400: r0 = synchronized()
    //     0xab4400: bl              #0xaaf038  ; [package:synchronized/src/basic_lock.dart] BasicLock::synchronized
    // 0xab4404: mov             x1, x0
    // 0xab4408: ldur            x0, [fp, #-0x90]
    // 0xab440c: stur            x1, [fp, #-0x98]
    // 0xab4410: tbnz            w0, #4, #0xab44a4
    // 0xab4414: ldur            x0, [fp, #-0xb0]
    // 0xab4418: LoadField: r2 = r0->field_1b
    //     0xab4418: ldur            w2, [x0, #0x1b]
    // 0xab441c: DecompressPointer r2
    //     0xab441c: add             x2, x2, HEAP, lsl #32
    // 0xab4420: r16 = Sentinel
    //     0xab4420: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab4424: cmp             w2, w16
    // 0xab4428: b.ne            #0xab443c
    // 0xab442c: r16 = "timeoutCompleter"
    //     0xab442c: add             x16, PP, #0x43, lsl #12  ; [pp+0x430a8] "timeoutCompleter"
    //     0xab4430: ldr             x16, [x16, #0xa8]
    // 0xab4434: str             x16, [SP]
    // 0xab4438: r0 = _throwLocalNotInitialized()
    //     0xab4438: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xab443c: ldur            x3, [fp, #-0x88]
    // 0xab4440: ldur            x0, [fp, #-0xb0]
    // 0xab4444: LoadField: r1 = r0->field_1b
    //     0xab4444: ldur            w1, [x0, #0x1b]
    // 0xab4448: DecompressPointer r1
    //     0xab4448: add             x1, x1, HEAP, lsl #32
    // 0xab444c: LoadField: r4 = r1->field_b
    //     0xab444c: ldur            w4, [x1, #0xb]
    // 0xab4450: DecompressPointer r4
    //     0xab4450: add             x4, x4, HEAP, lsl #32
    // 0xab4454: stur            x4, [fp, #-0xa0]
    // 0xab4458: r5 = LoadStaticField(0x1124)
    //     0xab4458: ldr             x5, [THR, #0x68]  ; THR::field_table_values
    //     0xab445c: ldr             x5, [x5, #0x2248]
    // 0xab4460: stur            x5, [fp, #-0x90]
    // 0xab4464: cmp             w5, NULL
    // 0xab4468: b.eq            #0xab44e4
    // 0xab446c: r1 = Function '<anonymous closure>':.
    //     0xab446c: add             x1, PP, #0x43, lsl #12  ; [pp+0x430b0] AnonymousClosure: (0xab4534), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized (0xab41e0)
    //     0xab4470: ldr             x1, [x1, #0xb0]
    // 0xab4474: r2 = Null
    //     0xab4474: mov             x2, NULL
    // 0xab4478: r0 = AllocateClosure()
    //     0xab4478: bl              #0xec1630  ; AllocateClosureStub
    // 0xab447c: mov             x1, x0
    // 0xab4480: ldur            x0, [fp, #-0x88]
    // 0xab4484: StoreField: r1->field_b = r0
    //     0xab4484: stur            w0, [x1, #0xb]
    // 0xab4488: ldur            x16, [fp, #-0xa0]
    // 0xab448c: ldur            lr, [fp, #-0x90]
    // 0xab4490: stp             lr, x16, [SP, #8]
    // 0xab4494: str             x1, [SP]
    // 0xab4498: r4 = const [0, 0x3, 0x3, 0x2, onTimeout, 0x2, null]
    //     0xab4498: add             x4, PP, #0xd, lsl #12  ; [pp+0xd0f8] List(7) [0, 0x3, 0x3, 0x2, "onTimeout", 0x2, Null]
    //     0xab449c: ldr             x4, [x4, #0xf8]
    // 0xab44a0: r0 = timeout()
    //     0xab44a0: bl              #0x5f9480  ; [dart:async] _Future::timeout
    // 0xab44a4: ldur            x0, [fp, #-0x98]
    // 0xab44a8: r0 = Await()
    //     0xab44a8: bl              #0x661044  ; AwaitStub
    // 0xab44ac: r0 = ReturnAsync()
    //     0xab44ac: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab44b0: sub             SP, fp, #0xc8
    // 0xab44b4: r2 = 60
    //     0xab44b4: movz            x2, #0x3c
    // 0xab44b8: branchIfSmi(r0, 0xab44c4)
    //     0xab44b8: tbz             w0, #0, #0xab44c4
    // 0xab44bc: r2 = LoadClassIdInstr(r0)
    //     0xab44bc: ldur            x2, [x0, #-1]
    //     0xab44c0: ubfx            x2, x2, #0xc, #0x14
    // 0xab44c4: cmp             x2, #0x1d7
    // 0xab44c8: b.ne            #0xab44d4
    // 0xab44cc: r0 = ReThrow()
    //     0xab44cc: bl              #0xec048c  ; ReThrowStub
    // 0xab44d0: brk             #0
    // 0xab44d4: r0 = ReThrow()
    //     0xab44d4: bl              #0xec048c  ; ReThrowStub
    // 0xab44d8: brk             #0
    // 0xab44dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab44dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab44e0: b               #0xab424c
    // 0xab44e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab44e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xab4534, size: 0x5c
    // 0xab4534: EnterFrame
    //     0xab4534: stp             fp, lr, [SP, #-0x10]!
    //     0xab4538: mov             fp, SP
    // 0xab453c: CheckStackOverflow
    //     0xab453c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4540: cmp             SP, x16
    //     0xab4544: b.ls            #0xab4584
    // 0xab4548: r0 = InitLateStaticField(0x1128) // [package:sqflite_common/src/utils.dart] ::lockWarningCallback
    //     0xab4548: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab454c: ldr             x0, [x0, #0x2250]
    //     0xab4550: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab4554: cmp             w0, w16
    //     0xab4558: b.ne            #0xab4568
    //     0xab455c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43098] Field <::.lockWarningCallback>: static late (offset: 0x1128)
    //     0xab4560: ldr             x2, [x2, #0x98]
    //     0xab4564: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xab4568: cmp             w0, NULL
    // 0xab456c: b.eq            #0xab458c
    // 0xab4570: r0 = _lockWarningCallbackDefault()
    //     0xab4570: bl              #0xab45bc  ; [package:sqflite_common/src/utils.dart] ::_lockWarningCallbackDefault
    // 0xab4574: r0 = Null
    //     0xab4574: mov             x0, NULL
    // 0xab4578: LeaveFrame
    //     0xab4578: mov             SP, fp
    //     0xab457c: ldp             fp, lr, [SP], #0x10
    // 0xab4580: ret
    //     0xab4580: ret             
    // 0xab4584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4584: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4588: b               #0xab4548
    // 0xab458c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab458c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Y0> <anonymous closure>(dynamic) {
    // ** addr: 0xab4650, size: 0xa8
    // 0xab4650: EnterFrame
    //     0xab4650: stp             fp, lr, [SP, #-0x10]!
    //     0xab4654: mov             fp, SP
    // 0xab4658: AllocStack(0x18)
    //     0xab4658: sub             SP, SP, #0x18
    // 0xab465c: SetupParameters()
    //     0xab465c: ldr             x0, [fp, #0x10]
    //     0xab4660: ldur            w1, [x0, #0x17]
    //     0xab4664: add             x1, x1, HEAP, lsl #32
    //     0xab4668: stur            x1, [fp, #-8]
    // 0xab466c: CheckStackOverflow
    //     0xab466c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4670: cmp             SP, x16
    //     0xab4674: b.ls            #0xab46f0
    // 0xab4678: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xab4678: ldur            w0, [x1, #0x17]
    // 0xab467c: DecompressPointer r0
    //     0xab467c: add             x0, x0, HEAP, lsl #32
    // 0xab4680: tbnz            w0, #4, #0xab46bc
    // 0xab4684: LoadField: r0 = r1->field_1b
    //     0xab4684: ldur            w0, [x1, #0x1b]
    // 0xab4688: DecompressPointer r0
    //     0xab4688: add             x0, x0, HEAP, lsl #32
    // 0xab468c: r16 = Sentinel
    //     0xab468c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab4690: cmp             w0, w16
    // 0xab4694: b.ne            #0xab46a8
    // 0xab4698: r16 = "timeoutCompleter"
    //     0xab4698: add             x16, PP, #0x43, lsl #12  ; [pp+0x430a8] "timeoutCompleter"
    //     0xab469c: ldr             x16, [x16, #0xa8]
    // 0xab46a0: str             x16, [SP]
    // 0xab46a4: r0 = _throwLocalNotInitialized()
    //     0xab46a4: bl              #0x6414e8  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xab46a8: ldur            x0, [fp, #-8]
    // 0xab46ac: LoadField: r1 = r0->field_1b
    //     0xab46ac: ldur            w1, [x0, #0x1b]
    // 0xab46b0: DecompressPointer r1
    //     0xab46b0: add             x1, x1, HEAP, lsl #32
    // 0xab46b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab46b4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab46b8: r0 = complete()
    //     0xab46b8: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0xab46bc: ldur            x0, [fp, #-8]
    // 0xab46c0: LoadField: r1 = r0->field_13
    //     0xab46c0: ldur            w1, [x0, #0x13]
    // 0xab46c4: DecompressPointer r1
    //     0xab46c4: add             x1, x1, HEAP, lsl #32
    // 0xab46c8: LoadField: r2 = r0->field_f
    //     0xab46c8: ldur            w2, [x0, #0xf]
    // 0xab46cc: DecompressPointer r2
    //     0xab46cc: add             x2, x2, HEAP, lsl #32
    // 0xab46d0: stp             x2, x1, [SP]
    // 0xab46d4: mov             x0, x1
    // 0xab46d8: ClosureCall
    //     0xab46d8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xab46dc: ldur            x2, [x0, #0x1f]
    //     0xab46e0: blr             x2
    // 0xab46e4: LeaveFrame
    //     0xab46e4: mov             SP, fp
    //     0xab46e8: ldp             fp, lr, [SP], #0x10
    // 0xab46ec: ret
    //     0xab46ec: ret             
    // 0xab46f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab46f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab46f4: b               #0xab4678
  }
  [closure] Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0xab4744, size: 0x5c
    // 0xab4744: EnterFrame
    //     0xab4744: stp             fp, lr, [SP, #-0x10]!
    //     0xab4748: mov             fp, SP
    // 0xab474c: ldr             x0, [fp, #0x10]
    // 0xab4750: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xab4750: ldur            w1, [x0, #0x17]
    // 0xab4754: DecompressPointer r1
    //     0xab4754: add             x1, x1, HEAP, lsl #32
    // 0xab4758: CheckStackOverflow
    //     0xab4758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab475c: cmp             SP, x16
    //     0xab4760: b.ls            #0xab4798
    // 0xab4764: LoadField: r0 = r1->field_f
    //     0xab4764: ldur            w0, [x1, #0xf]
    // 0xab4768: DecompressPointer r0
    //     0xab4768: add             x0, x0, HEAP, lsl #32
    // 0xab476c: LoadField: r2 = r1->field_13
    //     0xab476c: ldur            w2, [x1, #0x13]
    // 0xab4770: DecompressPointer r2
    //     0xab4770: add             x2, x2, HEAP, lsl #32
    // 0xab4774: r1 = LoadInt32Instr(r2)
    //     0xab4774: sbfx            x1, x2, #1, #0x1f
    //     0xab4778: tbz             w2, #0, #0xab4780
    //     0xab477c: ldur            x1, [x2, #7]
    // 0xab4780: mov             x2, x1
    // 0xab4784: mov             x1, x0
    // 0xab4788: r0 = invokeCloseDatabase()
    //     0xab4788: bl              #0xab47a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::invokeCloseDatabase
    // 0xab478c: LeaveFrame
    //     0xab478c: mov             SP, fp
    //     0xab4790: ldp             fp, lr, [SP], #0x10
    // 0xab4794: ret
    //     0xab4794: ret             
    // 0xab4798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4798: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab479c: b               #0xab4764
  }
  _ invokeCloseDatabase(/* No info */) async {
    // ** addr: 0xab47a0, size: 0xac
    // 0xab47a0: EnterFrame
    //     0xab47a0: stp             fp, lr, [SP, #-0x10]!
    //     0xab47a4: mov             fp, SP
    // 0xab47a8: AllocStack(0x38)
    //     0xab47a8: sub             SP, SP, #0x38
    // 0xab47ac: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab47ac: stur            NULL, [fp, #-8]
    //     0xab47b0: stur            x1, [fp, #-0x10]
    //     0xab47b4: stur            x2, [fp, #-0x18]
    // 0xab47b8: CheckStackOverflow
    //     0xab47b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab47bc: cmp             SP, x16
    //     0xab47c0: b.ls            #0xab4844
    // 0xab47c4: InitAsync() -> Future<void?>
    //     0xab47c4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab47c8: bl              #0x661298  ; InitAsyncStub
    // 0xab47cc: r1 = Null
    //     0xab47cc: mov             x1, NULL
    // 0xab47d0: r2 = 4
    //     0xab47d0: movz            x2, #0x4
    // 0xab47d4: r0 = AllocateArray()
    //     0xab47d4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab47d8: mov             x2, x0
    // 0xab47dc: r16 = "id"
    //     0xab47dc: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xab47e0: ldr             x16, [x16, #0x740]
    // 0xab47e4: StoreField: r2->field_f = r16
    //     0xab47e4: stur            w16, [x2, #0xf]
    // 0xab47e8: ldur            x3, [fp, #-0x18]
    // 0xab47ec: r0 = BoxInt64Instr(r3)
    //     0xab47ec: sbfiz           x0, x3, #1, #0x1f
    //     0xab47f0: cmp             x3, x0, asr #1
    //     0xab47f4: b.eq            #0xab4800
    //     0xab47f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab47fc: stur            x3, [x0, #7]
    // 0xab4800: StoreField: r2->field_13 = r0
    //     0xab4800: stur            w0, [x2, #0x13]
    // 0xab4804: r16 = <String, Object?>
    //     0xab4804: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xab4808: ldr             x16, [x16, #0x738]
    // 0xab480c: stp             x2, x16, [SP]
    // 0xab4810: r0 = Map._fromLiteral()
    //     0xab4810: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab4814: ldur            x16, [fp, #-0x10]
    // 0xab4818: stp             x16, NULL, [SP, #0x10]
    // 0xab481c: r16 = "closeDatabase"
    //     0xab481c: add             x16, PP, #0x43, lsl #12  ; [pp+0x43290] "closeDatabase"
    //     0xab4820: ldr             x16, [x16, #0x290]
    // 0xab4824: stp             x0, x16, [SP]
    // 0xab4828: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab4828: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab482c: r0 = safeInvokeMethod()
    //     0xab482c: bl              #0xab484c  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod
    // 0xab4830: mov             x1, x0
    // 0xab4834: stur            x1, [fp, #-0x10]
    // 0xab4838: r0 = Await()
    //     0xab4838: bl              #0x661044  ; AwaitStub
    // 0xab483c: r0 = Null
    //     0xab483c: mov             x0, NULL
    // 0xab4840: r0 = ReturnAsyncNotFuture()
    //     0xab4840: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab4844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4844: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4848: b               #0xab47c4
  }
  Future<Y0> safeInvokeMethod<Y0>(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin, String, Object?) {
    // ** addr: 0xab484c, size: 0xb0
    // 0xab484c: EnterFrame
    //     0xab484c: stp             fp, lr, [SP, #-0x10]!
    //     0xab4850: mov             fp, SP
    // 0xab4854: AllocStack(0x20)
    //     0xab4854: sub             SP, SP, #0x20
    // 0xab4858: SetupParameters()
    //     0xab4858: ldur            w0, [x4, #0xf]
    //     0xab485c: cbnz            w0, #0xab4868
    //     0xab4860: mov             x3, NULL
    //     0xab4864: b               #0xab4878
    //     0xab4868: ldur            w0, [x4, #0x17]
    //     0xab486c: add             x1, fp, w0, sxtw #2
    //     0xab4870: ldr             x1, [x1, #0x10]
    //     0xab4874: mov             x3, x1
    //     0xab4878: ldr             x2, [fp, #0x20]
    //     0xab487c: ldr             x1, [fp, #0x18]
    //     0xab4880: ldr             x0, [fp, #0x10]
    //     0xab4884: stur            x3, [fp, #-8]
    // 0xab4888: CheckStackOverflow
    //     0xab4888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab488c: cmp             SP, x16
    //     0xab4890: b.ls            #0xab48f4
    // 0xab4894: r1 = 3
    //     0xab4894: movz            x1, #0x3
    // 0xab4898: r0 = AllocateContext()
    //     0xab4898: bl              #0xec126c  ; AllocateContextStub
    // 0xab489c: mov             x1, x0
    // 0xab48a0: ldr             x0, [fp, #0x20]
    // 0xab48a4: StoreField: r1->field_f = r0
    //     0xab48a4: stur            w0, [x1, #0xf]
    // 0xab48a8: ldr             x2, [fp, #0x18]
    // 0xab48ac: StoreField: r1->field_13 = r2
    //     0xab48ac: stur            w2, [x1, #0x13]
    // 0xab48b0: ldr             x2, [fp, #0x10]
    // 0xab48b4: ArrayStore: r1[0] = r2  ; List_4
    //     0xab48b4: stur            w2, [x1, #0x17]
    // 0xab48b8: mov             x2, x1
    // 0xab48bc: r1 = Function '<anonymous closure>':.
    //     0xab48bc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43038] AnonymousClosure: (0xab48fc), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod (0xab484c)
    //     0xab48c0: ldr             x1, [x1, #0x38]
    // 0xab48c4: r0 = AllocateClosure()
    //     0xab48c4: bl              #0xec1630  ; AllocateClosureStub
    // 0xab48c8: mov             x1, x0
    // 0xab48cc: ldur            x0, [fp, #-8]
    // 0xab48d0: StoreField: r1->field_b = r0
    //     0xab48d0: stur            w0, [x1, #0xb]
    // 0xab48d4: ldr             x16, [fp, #0x20]
    // 0xab48d8: stp             x16, x0, [SP, #8]
    // 0xab48dc: str             x1, [SP]
    // 0xab48e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab48e0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab48e4: r0 = safeAction()
    //     0xab48e4: bl              #0xab4170  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeAction
    // 0xab48e8: LeaveFrame
    //     0xab48e8: mov             SP, fp
    //     0xab48ec: ldp             fp, lr, [SP], #0x10
    // 0xab48f0: ret
    //     0xab48f0: ret             
    // 0xab48f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab48f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab48f8: b               #0xab4894
  }
  [closure] Future<Y0> <anonymous closure>(dynamic) {
    // ** addr: 0xab48fc, size: 0x68
    // 0xab48fc: EnterFrame
    //     0xab48fc: stp             fp, lr, [SP, #-0x10]!
    //     0xab4900: mov             fp, SP
    // 0xab4904: AllocStack(0x20)
    //     0xab4904: sub             SP, SP, #0x20
    // 0xab4908: SetupParameters()
    //     0xab4908: ldr             x0, [fp, #0x10]
    //     0xab490c: ldur            w1, [x0, #0x17]
    //     0xab4910: add             x1, x1, HEAP, lsl #32
    // 0xab4914: CheckStackOverflow
    //     0xab4914: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4918: cmp             SP, x16
    //     0xab491c: b.ls            #0xab495c
    // 0xab4920: LoadField: r2 = r0->field_b
    //     0xab4920: ldur            w2, [x0, #0xb]
    // 0xab4924: DecompressPointer r2
    //     0xab4924: add             x2, x2, HEAP, lsl #32
    // 0xab4928: LoadField: r0 = r1->field_f
    //     0xab4928: ldur            w0, [x1, #0xf]
    // 0xab492c: DecompressPointer r0
    //     0xab492c: add             x0, x0, HEAP, lsl #32
    // 0xab4930: LoadField: r3 = r1->field_13
    //     0xab4930: ldur            w3, [x1, #0x13]
    // 0xab4934: DecompressPointer r3
    //     0xab4934: add             x3, x3, HEAP, lsl #32
    // 0xab4938: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xab4938: ldur            w4, [x1, #0x17]
    // 0xab493c: DecompressPointer r4
    //     0xab493c: add             x4, x4, HEAP, lsl #32
    // 0xab4940: stp             x0, x2, [SP, #0x10]
    // 0xab4944: stp             x4, x3, [SP]
    // 0xab4948: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab4948: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab494c: r0 = invokeMethod()
    //     0xab494c: bl              #0xab4964  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::invokeMethod
    // 0xab4950: LeaveFrame
    //     0xab4950: mov             SP, fp
    //     0xab4954: ldp             fp, lr, [SP], #0x10
    // 0xab4958: ret
    //     0xab4958: ret             
    // 0xab495c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab495c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4960: b               #0xab4920
  }
  Future<Y0> invokeMethod<Y0>(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin, String, Object?) {
    // ** addr: 0xab4964, size: 0x78
    // 0xab4964: EnterFrame
    //     0xab4964: stp             fp, lr, [SP, #-0x10]!
    //     0xab4968: mov             fp, SP
    // 0xab496c: AllocStack(0x18)
    //     0xab496c: sub             SP, SP, #0x18
    // 0xab4970: SetupParameters()
    //     0xab4970: ldur            w0, [x4, #0xf]
    //     0xab4974: cbnz            w0, #0xab4980
    //     0xab4978: mov             x1, NULL
    //     0xab497c: b               #0xab498c
    //     0xab4980: ldur            w0, [x4, #0x17]
    //     0xab4984: add             x1, fp, w0, sxtw #2
    //     0xab4988: ldr             x1, [x1, #0x10]
    //     0xab498c: ldr             x0, [fp, #0x20]
    // 0xab4990: CheckStackOverflow
    //     0xab4990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4994: cmp             SP, x16
    //     0xab4998: b.ls            #0xab49d0
    // 0xab499c: LoadField: r2 = r0->field_27
    //     0xab499c: ldur            w2, [x0, #0x27]
    // 0xab49a0: DecompressPointer r2
    //     0xab49a0: add             x2, x2, HEAP, lsl #32
    // 0xab49a4: cmp             w2, NULL
    // 0xab49a8: b.eq            #0xab49d8
    // 0xab49ac: ldr             x16, [fp, #0x18]
    // 0xab49b0: stp             x16, x1, [SP, #8]
    // 0xab49b4: ldr             x16, [fp, #0x10]
    // 0xab49b8: str             x16, [SP]
    // 0xab49bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab49bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab49c0: r0 = invokeMethod()
    //     0xab49c0: bl              #0xab36c8  ; [package:sqflite_platform_interface/src/sqflite_method_channel.dart] ::invokeMethod
    // 0xab49c4: LeaveFrame
    //     0xab49c4: mov             SP, fp
    //     0xab49c8: ldp             fp, lr, [SP], #0x10
    // 0xab49cc: ret
    //     0xab49cc: ret             
    // 0xab49d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab49d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab49d4: b               #0xab499c
    // 0xab49d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab49d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, Transaction?) async {
    // ** addr: 0xab49dc, size: 0xe8
    // 0xab49dc: EnterFrame
    //     0xab49dc: stp             fp, lr, [SP, #-0x10]!
    //     0xab49e0: mov             fp, SP
    // 0xab49e4: AllocStack(0x98)
    //     0xab49e4: sub             SP, SP, #0x98
    // 0xab49e8: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1, fp-0x68 */, dynamic _ /* r2, fp-0x60 */)
    //     0xab49e8: stur            NULL, [fp, #-8]
    //     0xab49ec: movz            x0, #0
    //     0xab49f0: add             x1, fp, w0, sxtw #2
    //     0xab49f4: ldr             x1, [x1, #0x18]
    //     0xab49f8: stur            x1, [fp, #-0x68]
    //     0xab49fc: add             x2, fp, w0, sxtw #2
    //     0xab4a00: ldr             x2, [x2, #0x10]
    //     0xab4a04: stur            x2, [fp, #-0x60]
    //     0xab4a08: ldur            w3, [x1, #0x17]
    //     0xab4a0c: add             x3, x3, HEAP, lsl #32
    //     0xab4a10: stur            x3, [fp, #-0x58]
    // 0xab4a14: CheckStackOverflow
    //     0xab4a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4a18: cmp             SP, x16
    //     0xab4a1c: b.ls            #0xab4abc
    // 0xab4a20: InitAsync() -> Future<Null?>
    //     0xab4a20: ldr             x0, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    //     0xab4a24: bl              #0x661298  ; InitAsyncStub
    // 0xab4a28: ldur            x3, [fp, #-0x60]
    // 0xab4a2c: ldur            x0, [fp, #-0x58]
    // 0xab4a30: LoadField: r4 = r0->field_f
    //     0xab4a30: ldur            w4, [x0, #0xf]
    // 0xab4a34: DecompressPointer r4
    //     0xab4a34: add             x4, x4, HEAP, lsl #32
    // 0xab4a38: mov             x0, x3
    // 0xab4a3c: stur            x4, [fp, #-0x68]
    // 0xab4a40: r2 = Null
    //     0xab4a40: mov             x2, NULL
    // 0xab4a44: r1 = Null
    //     0xab4a44: mov             x1, NULL
    // 0xab4a48: r4 = LoadClassIdInstr(r0)
    //     0xab4a48: ldur            x4, [x0, #-1]
    //     0xab4a4c: ubfx            x4, x4, #0xc, #0x14
    // 0xab4a50: cmp             x4, #0x1cd
    // 0xab4a54: b.eq            #0xab4a6c
    // 0xab4a58: r8 = SqfliteTransaction?
    //     0xab4a58: add             x8, PP, #0x43, lsl #12  ; [pp+0x43298] Type: SqfliteTransaction?
    //     0xab4a5c: ldr             x8, [x8, #0x298]
    // 0xab4a60: r3 = Null
    //     0xab4a60: add             x3, PP, #0x43, lsl #12  ; [pp+0x432a0] Null
    //     0xab4a64: ldr             x3, [x3, #0x2a0]
    // 0xab4a68: r0 = DefaultNullableTypeTest()
    //     0xab4a68: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0xab4a6c: ldur            x0, [fp, #-0x60]
    // 0xab4a70: cmp             w0, NULL
    // 0xab4a74: b.ne            #0xab4a7c
    // 0xab4a78: r0 = getForcedSqfliteTransaction()
    //     0xab4a78: bl              #0xab4d68  ; [package:sqflite_common/src/transaction.dart] ::getForcedSqfliteTransaction
    // 0xab4a7c: ldur            x16, [fp, #-0x68]
    // 0xab4a80: stp             x16, NULL, [SP, #0x20]
    // 0xab4a84: r16 = "ROLLBACK"
    //     0xab4a84: add             x16, PP, #0x43, lsl #12  ; [pp+0x432b0] "ROLLBACK"
    //     0xab4a88: ldr             x16, [x16, #0x2b0]
    // 0xab4a8c: stp             x16, x0, [SP, #0x10]
    // 0xab4a90: r16 = false
    //     0xab4a90: add             x16, NULL, #0x30  ; false
    // 0xab4a94: stp             x16, NULL, [SP]
    // 0xab4a98: r4 = const [0x1, 0x5, 0x5, 0x5, null]
    //     0xab4a98: ldr             x4, [PP, #0x1800]  ; [pp+0x1800] List(5) [0x1, 0x5, 0x5, 0x5, Null]
    // 0xab4a9c: r0 = invokeExecute()
    //     0xab4a9c: bl              #0xab4ac4  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::invokeExecute
    // 0xab4aa0: mov             x1, x0
    // 0xab4aa4: stur            x1, [fp, #-0x58]
    // 0xab4aa8: r0 = Await()
    //     0xab4aa8: bl              #0x661044  ; AwaitStub
    // 0xab4aac: b               #0xab4ab4
    // 0xab4ab0: sub             SP, fp, #0x98
    // 0xab4ab4: r0 = Null
    //     0xab4ab4: mov             x0, NULL
    // 0xab4ab8: r0 = ReturnAsyncNotFuture()
    //     0xab4ab8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab4abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4abc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4ac0: b               #0xab4a20
  }
  _ invokeExecute(/* No info */) {
    // ** addr: 0xab4ac4, size: 0x11c
    // 0xab4ac4: EnterFrame
    //     0xab4ac4: stp             fp, lr, [SP, #-0x10]!
    //     0xab4ac8: mov             fp, SP
    // 0xab4acc: AllocStack(0x48)
    //     0xab4acc: sub             SP, SP, #0x48
    // 0xab4ad0: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r6, fp-0x20 */, dynamic _ /* r2 */, dynamic _ /* r3 */, dynamic _ /* r7, fp-0x18 */, {dynamic beginTransaction = Null /* r0, fp-0x10 */})
    //     0xab4ad0: ldur            w0, [x4, #0x13]
    //     0xab4ad4: sub             x1, x0, #0xa
    //     0xab4ad8: add             x6, fp, w1, sxtw #2
    //     0xab4adc: ldr             x6, [x6, #0x30]
    //     0xab4ae0: stur            x6, [fp, #-0x20]
    //     0xab4ae4: add             x2, fp, w1, sxtw #2
    //     0xab4ae8: ldr             x2, [x2, #0x28]
    //     0xab4aec: add             x3, fp, w1, sxtw #2
    //     0xab4af0: ldr             x3, [x3, #0x20]
    //     0xab4af4: add             x7, fp, w1, sxtw #2
    //     0xab4af8: ldr             x7, [x7, #0x10]
    //     0xab4afc: stur            x7, [fp, #-0x18]
    //     0xab4b00: ldur            w1, [x4, #0x1f]
    //     0xab4b04: add             x1, x1, HEAP, lsl #32
    //     0xab4b08: add             x16, PP, #0x43, lsl #12  ; [pp+0x43010] "beginTransaction"
    //     0xab4b0c: ldr             x16, [x16, #0x10]
    //     0xab4b10: cmp             w1, w16
    //     0xab4b14: b.ne            #0xab4b30
    //     0xab4b18: ldur            w1, [x4, #0x23]
    //     0xab4b1c: add             x1, x1, HEAP, lsl #32
    //     0xab4b20: sub             w5, w0, w1
    //     0xab4b24: add             x0, fp, w5, sxtw #2
    //     0xab4b28: ldr             x0, [x0, #8]
    //     0xab4b2c: b               #0xab4b34
    //     0xab4b30: mov             x0, NULL
    //     0xab4b34: stur            x0, [fp, #-0x10]
    //     0xab4b38: ldur            w1, [x4, #0xf]
    //     0xab4b3c: cbnz            w1, #0xab4b48
    //     0xab4b40: mov             x4, NULL
    //     0xab4b44: b               #0xab4b54
    //     0xab4b48: ldur            w1, [x4, #0x17]
    //     0xab4b4c: add             x4, fp, w1, sxtw #2
    //     0xab4b50: ldr             x4, [x4, #0x10]
    //     0xab4b54: stur            x4, [fp, #-8]
    // 0xab4b58: CheckStackOverflow
    //     0xab4b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4b5c: cmp             SP, x16
    //     0xab4b60: b.ls            #0xab4bd8
    // 0xab4b64: mov             x1, x6
    // 0xab4b68: r5 = Null
    //     0xab4b68: mov             x5, NULL
    // 0xab4b6c: r0 = SqfliteDatabaseMixinExt._txnGetSqlMethodArguments()
    //     0xab4b6c: bl              #0xab4c24  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt._txnGetSqlMethodArguments
    // 0xab4b70: mov             x4, x0
    // 0xab4b74: ldur            x0, [fp, #-0x10]
    // 0xab4b78: stur            x4, [fp, #-0x28]
    // 0xab4b7c: r16 = true
    //     0xab4b7c: add             x16, NULL, #0x20  ; true
    // 0xab4b80: cmp             w0, w16
    // 0xab4b84: b.ne            #0xab4b9c
    // 0xab4b88: mov             x1, x4
    // 0xab4b8c: r2 = "transactionId"
    //     0xab4b8c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43028] "transactionId"
    //     0xab4b90: ldr             x2, [x2, #0x28]
    // 0xab4b94: r3 = Null
    //     0xab4b94: mov             x3, NULL
    // 0xab4b98: r0 = []=()
    //     0xab4b98: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab4b9c: ldur            x1, [fp, #-0x28]
    // 0xab4ba0: ldur            x2, [fp, #-0x18]
    // 0xab4ba4: r0 = SqfliteDatabaseMixinExt.addInTransactionChangeParam()
    //     0xab4ba4: bl              #0xab4be0  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.addInTransactionChangeParam
    // 0xab4ba8: ldur            x16, [fp, #-8]
    // 0xab4bac: ldur            lr, [fp, #-0x20]
    // 0xab4bb0: stp             lr, x16, [SP, #0x10]
    // 0xab4bb4: r16 = "execute"
    //     0xab4bb4: add             x16, PP, #0x43, lsl #12  ; [pp+0x43030] "execute"
    //     0xab4bb8: ldr             x16, [x16, #0x30]
    // 0xab4bbc: ldur            lr, [fp, #-0x28]
    // 0xab4bc0: stp             lr, x16, [SP]
    // 0xab4bc4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab4bc4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab4bc8: r0 = safeInvokeMethod()
    //     0xab4bc8: bl              #0xab484c  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod
    // 0xab4bcc: LeaveFrame
    //     0xab4bcc: mov             SP, fp
    //     0xab4bd0: ldp             fp, lr, [SP], #0x10
    // 0xab4bd4: ret
    //     0xab4bd4: ret             
    // 0xab4bd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4bd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4bdc: b               #0xab4b64
  }
  Future<Y0> transaction<Y0>(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin, (dynamic, Transaction) => Future<Y0>, bool?) {
    // ** addr: 0xab4d94, size: 0xc4
    // 0xab4d94: EnterFrame
    //     0xab4d94: stp             fp, lr, [SP, #-0x10]!
    //     0xab4d98: mov             fp, SP
    // 0xab4d9c: AllocStack(0x38)
    //     0xab4d9c: sub             SP, SP, #0x38
    // 0xab4da0: SetupParameters()
    //     0xab4da0: ldur            w0, [x4, #0xf]
    //     0xab4da4: cbnz            w0, #0xab4db0
    //     0xab4da8: mov             x2, NULL
    //     0xab4dac: b               #0xab4dc0
    //     0xab4db0: ldur            w0, [x4, #0x17]
    //     0xab4db4: add             x1, fp, w0, sxtw #2
    //     0xab4db8: ldr             x1, [x1, #0x10]
    //     0xab4dbc: mov             x2, x1
    //     0xab4dc0: ldr             x1, [fp, #0x20]
    //     0xab4dc4: ldr             x0, [fp, #0x18]
    //     0xab4dc8: stur            x2, [fp, #-8]
    // 0xab4dcc: CheckStackOverflow
    //     0xab4dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4dd0: cmp             SP, x16
    //     0xab4dd4: b.ls            #0xab4e50
    // 0xab4dd8: r1 = 2
    //     0xab4dd8: movz            x1, #0x2
    // 0xab4ddc: r0 = AllocateContext()
    //     0xab4ddc: bl              #0xec126c  ; AllocateContextStub
    // 0xab4de0: mov             x2, x0
    // 0xab4de4: ldr             x0, [fp, #0x20]
    // 0xab4de8: stur            x2, [fp, #-0x10]
    // 0xab4dec: StoreField: r2->field_f = r0
    //     0xab4dec: stur            w0, [x2, #0xf]
    // 0xab4df0: ldr             x1, [fp, #0x18]
    // 0xab4df4: StoreField: r2->field_13 = r1
    //     0xab4df4: stur            w1, [x2, #0x13]
    // 0xab4df8: mov             x1, x0
    // 0xab4dfc: r0 = checkNotClosed()
    //     0xab4dfc: bl              #0xab4e58  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::checkNotClosed
    // 0xab4e00: ldr             x0, [fp, #0x20]
    // 0xab4e04: LoadField: r3 = r0->field_f
    //     0xab4e04: ldur            w3, [x0, #0xf]
    // 0xab4e08: DecompressPointer r3
    //     0xab4e08: add             x3, x3, HEAP, lsl #32
    // 0xab4e0c: ldur            x2, [fp, #-0x10]
    // 0xab4e10: stur            x3, [fp, #-0x18]
    // 0xab4e14: r1 = Function '<anonymous closure>':.
    //     0xab4e14: add             x1, PP, #0x43, lsl #12  ; [pp+0x432b8] AnonymousClosure: (0xab4e9c), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::transaction (0xab4d94)
    //     0xab4e18: ldr             x1, [x1, #0x2b8]
    // 0xab4e1c: r0 = AllocateClosure()
    //     0xab4e1c: bl              #0xec1630  ; AllocateClosureStub
    // 0xab4e20: mov             x1, x0
    // 0xab4e24: ldur            x0, [fp, #-8]
    // 0xab4e28: StoreField: r1->field_b = r0
    //     0xab4e28: stur            w0, [x1, #0xb]
    // 0xab4e2c: ldr             x16, [fp, #0x20]
    // 0xab4e30: stp             x16, x0, [SP, #0x10]
    // 0xab4e34: ldur            x16, [fp, #-0x18]
    // 0xab4e38: stp             x1, x16, [SP]
    // 0xab4e3c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab4e3c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab4e40: r0 = txnSynchronized()
    //     0xab4e40: bl              #0xab41e0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized
    // 0xab4e44: LeaveFrame
    //     0xab4e44: mov             SP, fp
    //     0xab4e48: ldp             fp, lr, [SP], #0x10
    // 0xab4e4c: ret
    //     0xab4e4c: ret             
    // 0xab4e50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4e50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4e54: b               #0xab4dd8
  }
  _ checkNotClosed(/* No info */) {
    // ** addr: 0xab4e58, size: 0x44
    // 0xab4e58: EnterFrame
    //     0xab4e58: stp             fp, lr, [SP, #-0x10]!
    //     0xab4e5c: mov             fp, SP
    // 0xab4e60: LoadField: r0 = r1->field_7
    //     0xab4e60: ldur            w0, [x1, #7]
    // 0xab4e64: DecompressPointer r0
    //     0xab4e64: add             x0, x0, HEAP, lsl #32
    // 0xab4e68: tbz             w0, #4, #0xab4e7c
    // 0xab4e6c: r0 = Null
    //     0xab4e6c: mov             x0, NULL
    // 0xab4e70: LeaveFrame
    //     0xab4e70: mov             SP, fp
    //     0xab4e74: ldp             fp, lr, [SP], #0x10
    // 0xab4e78: ret
    //     0xab4e78: ret             
    // 0xab4e7c: r0 = SqfliteDatabaseException()
    //     0xab4e7c: bl              #0xab3434  ; AllocateSqfliteDatabaseExceptionStub -> SqfliteDatabaseException (size=0x14)
    // 0xab4e80: mov             x1, x0
    // 0xab4e84: r0 = "error database_closed"
    //     0xab4e84: add             x0, PP, #0x43, lsl #12  ; [pp+0x430f8] "error database_closed"
    //     0xab4e88: ldr             x0, [x0, #0xf8]
    // 0xab4e8c: StoreField: r1->field_7 = r0
    //     0xab4e8c: stur            w0, [x1, #7]
    // 0xab4e90: mov             x0, x1
    // 0xab4e94: r0 = Throw()
    //     0xab4e94: bl              #0xec04b8  ; ThrowStub
    // 0xab4e98: brk             #0
  }
  [closure] Future<Y0> <anonymous closure>(dynamic, Transaction?) async {
    // ** addr: 0xab4e9c, size: 0x8c
    // 0xab4e9c: EnterFrame
    //     0xab4e9c: stp             fp, lr, [SP, #-0x10]!
    //     0xab4ea0: mov             fp, SP
    // 0xab4ea4: AllocStack(0x40)
    //     0xab4ea4: sub             SP, SP, #0x40
    // 0xab4ea8: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 */, dynamic _ /* r2, fp-0x20 */)
    //     0xab4ea8: stur            NULL, [fp, #-8]
    //     0xab4eac: movz            x0, #0
    //     0xab4eb0: add             x1, fp, w0, sxtw #2
    //     0xab4eb4: ldr             x1, [x1, #0x18]
    //     0xab4eb8: add             x2, fp, w0, sxtw #2
    //     0xab4ebc: ldr             x2, [x2, #0x10]
    //     0xab4ec0: stur            x2, [fp, #-0x20]
    //     0xab4ec4: ldur            w3, [x1, #0x17]
    //     0xab4ec8: add             x3, x3, HEAP, lsl #32
    //     0xab4ecc: stur            x3, [fp, #-0x18]
    // 0xab4ed0: CheckStackOverflow
    //     0xab4ed0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab4ed4: cmp             SP, x16
    //     0xab4ed8: b.ls            #0xab4f20
    // 0xab4edc: LoadField: r4 = r1->field_b
    //     0xab4edc: ldur            w4, [x1, #0xb]
    // 0xab4ee0: DecompressPointer r4
    //     0xab4ee0: add             x4, x4, HEAP, lsl #32
    // 0xab4ee4: mov             x0, x4
    // 0xab4ee8: stur            x4, [fp, #-0x10]
    // 0xab4eec: r0 = InitAsync()
    //     0xab4eec: bl              #0x661298  ; InitAsyncStub
    // 0xab4ef0: ldur            x0, [fp, #-0x18]
    // 0xab4ef4: LoadField: r1 = r0->field_f
    //     0xab4ef4: ldur            w1, [x0, #0xf]
    // 0xab4ef8: DecompressPointer r1
    //     0xab4ef8: add             x1, x1, HEAP, lsl #32
    // 0xab4efc: LoadField: r2 = r0->field_13
    //     0xab4efc: ldur            w2, [x0, #0x13]
    // 0xab4f00: DecompressPointer r2
    //     0xab4f00: add             x2, x2, HEAP, lsl #32
    // 0xab4f04: ldur            x16, [fp, #-0x10]
    // 0xab4f08: stp             x1, x16, [SP, #0x10]
    // 0xab4f0c: ldur            x16, [fp, #-0x20]
    // 0xab4f10: stp             x2, x16, [SP]
    // 0xab4f14: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab4f14: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab4f18: r0 = SqfliteDatabaseMixinExt._txnTransaction()
    //     0xab4f18: bl              #0xab4f28  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt._txnTransaction
    // 0xab4f1c: r0 = ReturnAsync()
    //     0xab4f1c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab4f20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4f20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab4f24: b               #0xab4edc
  }
  _ endTransaction(/* No info */) async {
    // ** addr: 0xab51a0, size: 0x100
    // 0xab51a0: EnterFrame
    //     0xab51a0: stp             fp, lr, [SP, #-0x10]!
    //     0xab51a4: mov             fp, SP
    // 0xab51a8: AllocStack(0x80)
    //     0xab51a8: sub             SP, SP, #0x80
    // 0xab51ac: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x48 */, dynamic _ /* r2 => r2, fp-0x50 */)
    //     0xab51ac: stur            NULL, [fp, #-8]
    //     0xab51b0: stur            x1, [fp, #-0x48]
    //     0xab51b4: stur            x2, [fp, #-0x50]
    // 0xab51b8: CheckStackOverflow
    //     0xab51b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab51bc: cmp             SP, x16
    //     0xab51c0: b.ls            #0xab5298
    // 0xab51c4: InitAsync() -> Future<void?>
    //     0xab51c4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab51c8: bl              #0x661298  ; InitAsyncStub
    // 0xab51cc: ldur            x1, [fp, #-0x48]
    // 0xab51d0: r0 = SqfliteDatabaseMixinExt.readOnly()
    //     0xab51d0: bl              #0xab4710  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.readOnly
    // 0xab51d4: ldur            x0, [fp, #-0x50]
    // 0xab51d8: LoadField: r1 = r0->field_b
    //     0xab51d8: ldur            w1, [x0, #0xb]
    // 0xab51dc: DecompressPointer r1
    //     0xab51dc: add             x1, x1, HEAP, lsl #32
    // 0xab51e0: r16 = true
    //     0xab51e0: add             x16, NULL, #0x20  ; true
    // 0xab51e4: cmp             w1, w16
    // 0xab51e8: b.ne            #0xab51fc
    // 0xab51ec: ldur            x1, [fp, #-0x48]
    // 0xab51f0: r0 = false
    //     0xab51f0: add             x0, NULL, #0x30  ; false
    // 0xab51f4: StoreField: r1->field_1b = r0
    //     0xab51f4: stur            w0, [x1, #0x1b]
    // 0xab51f8: b               #0xab5278
    // 0xab51fc: ldur            x1, [fp, #-0x48]
    // 0xab5200: LoadField: r2 = r0->field_f
    //     0xab5200: ldur            w2, [x0, #0xf]
    // 0xab5204: DecompressPointer r2
    //     0xab5204: add             x2, x2, HEAP, lsl #32
    // 0xab5208: r16 = true
    //     0xab5208: add             x16, NULL, #0x20  ; true
    // 0xab520c: cmp             w2, w16
    // 0xab5210: b.ne            #0xab5240
    // 0xab5214: stp             x1, NULL, [SP, #0x18]
    // 0xab5218: r16 = "COMMIT"
    //     0xab5218: add             x16, PP, #0x43, lsl #12  ; [pp+0x432f8] "COMMIT"
    //     0xab521c: ldr             x16, [x16, #0x2f8]
    // 0xab5220: stp             x16, x0, [SP, #8]
    // 0xab5224: str             NULL, [SP]
    // 0xab5228: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0xab5228: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0xab522c: r0 = txnExecute()
    //     0xab522c: bl              #0xab52a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnExecute
    // 0xab5230: mov             x1, x0
    // 0xab5234: stur            x1, [fp, #-0x58]
    // 0xab5238: r0 = Await()
    //     0xab5238: bl              #0x661044  ; AwaitStub
    // 0xab523c: b               #0xab526c
    // 0xab5240: stp             x1, NULL, [SP, #0x18]
    // 0xab5244: ldur            x16, [fp, #-0x50]
    // 0xab5248: r30 = "ROLLBACK"
    //     0xab5248: add             lr, PP, #0x43, lsl #12  ; [pp+0x432b0] "ROLLBACK"
    //     0xab524c: ldr             lr, [lr, #0x2b0]
    // 0xab5250: stp             lr, x16, [SP, #8]
    // 0xab5254: str             NULL, [SP]
    // 0xab5258: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0xab5258: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0xab525c: r0 = txnExecute()
    //     0xab525c: bl              #0xab52a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnExecute
    // 0xab5260: mov             x1, x0
    // 0xab5264: stur            x1, [fp, #-0x48]
    // 0xab5268: r0 = Await()
    //     0xab5268: bl              #0x661044  ; AwaitStub
    // 0xab526c: ldur            x0, [fp, #-0x50]
    // 0xab5270: r2 = true
    //     0xab5270: add             x2, NULL, #0x20  ; true
    // 0xab5274: StoreField: r0->field_b = r2
    //     0xab5274: stur            w2, [x0, #0xb]
    // 0xab5278: r0 = Null
    //     0xab5278: mov             x0, NULL
    // 0xab527c: r0 = ReturnAsyncNotFuture()
    //     0xab527c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab5280: r2 = true
    //     0xab5280: add             x2, NULL, #0x20  ; true
    // 0xab5284: sub             SP, fp, #0x80
    // 0xab5288: ldur            x3, [fp, #-0x18]
    // 0xab528c: StoreField: r3->field_b = r2
    //     0xab528c: stur            w2, [x3, #0xb]
    // 0xab5290: r0 = ReThrow()
    //     0xab5290: bl              #0xec048c  ; ReThrowStub
    // 0xab5294: brk             #0
    // 0xab5298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5298: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab529c: b               #0xab51c4
  }
  Future<Y0> txnExecute<Y0>(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin, SqfliteTransaction?, String, List<Object?>?, {bool? beginTransaction}) {
    // ** addr: 0xab52a0, size: 0x114
    // 0xab52a0: EnterFrame
    //     0xab52a0: stp             fp, lr, [SP, #-0x10]!
    //     0xab52a4: mov             fp, SP
    // 0xab52a8: AllocStack(0x48)
    //     0xab52a8: sub             SP, SP, #0x48
    // 0xab52ac: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r2, fp-0x28 */, dynamic _ /* r3, fp-0x20 */, dynamic _ /* r5, fp-0x18 */, {dynamic beginTransaction = Null /* r0, fp-0x10 */})
    //     0xab52ac: ldur            w0, [x4, #0x13]
    //     0xab52b0: sub             x1, x0, #8
    //     0xab52b4: add             x2, fp, w1, sxtw #2
    //     0xab52b8: ldr             x2, [x2, #0x28]
    //     0xab52bc: stur            x2, [fp, #-0x28]
    //     0xab52c0: add             x3, fp, w1, sxtw #2
    //     0xab52c4: ldr             x3, [x3, #0x20]
    //     0xab52c8: stur            x3, [fp, #-0x20]
    //     0xab52cc: add             x5, fp, w1, sxtw #2
    //     0xab52d0: ldr             x5, [x5, #0x18]
    //     0xab52d4: stur            x5, [fp, #-0x18]
    //     0xab52d8: ldur            w1, [x4, #0x1f]
    //     0xab52dc: add             x1, x1, HEAP, lsl #32
    //     0xab52e0: add             x16, PP, #0x43, lsl #12  ; [pp+0x43010] "beginTransaction"
    //     0xab52e4: ldr             x16, [x16, #0x10]
    //     0xab52e8: cmp             w1, w16
    //     0xab52ec: b.ne            #0xab5308
    //     0xab52f0: ldur            w1, [x4, #0x23]
    //     0xab52f4: add             x1, x1, HEAP, lsl #32
    //     0xab52f8: sub             w6, w0, w1
    //     0xab52fc: add             x0, fp, w6, sxtw #2
    //     0xab5300: ldr             x0, [x0, #8]
    //     0xab5304: b               #0xab530c
    //     0xab5308: mov             x0, NULL
    //     0xab530c: stur            x0, [fp, #-0x10]
    //     0xab5310: ldur            w1, [x4, #0xf]
    //     0xab5314: cbnz            w1, #0xab5320
    //     0xab5318: mov             x1, NULL
    //     0xab531c: b               #0xab5330
    //     0xab5320: ldur            w1, [x4, #0x17]
    //     0xab5324: add             x4, fp, w1, sxtw #2
    //     0xab5328: ldr             x4, [x4, #0x10]
    //     0xab532c: mov             x1, x4
    //     0xab5330: stur            x1, [fp, #-8]
    // 0xab5334: CheckStackOverflow
    //     0xab5334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5338: cmp             SP, x16
    //     0xab533c: b.ls            #0xab53ac
    // 0xab5340: r1 = 5
    //     0xab5340: movz            x1, #0x5
    // 0xab5344: r0 = AllocateContext()
    //     0xab5344: bl              #0xec126c  ; AllocateContextStub
    // 0xab5348: mov             x1, x0
    // 0xab534c: ldur            x0, [fp, #-0x28]
    // 0xab5350: StoreField: r1->field_f = r0
    //     0xab5350: stur            w0, [x1, #0xf]
    // 0xab5354: ldur            x3, [fp, #-0x20]
    // 0xab5358: StoreField: r1->field_13 = r3
    //     0xab5358: stur            w3, [x1, #0x13]
    // 0xab535c: ldur            x2, [fp, #-0x18]
    // 0xab5360: ArrayStore: r1[0] = r2  ; List_4
    //     0xab5360: stur            w2, [x1, #0x17]
    // 0xab5364: ldur            x2, [fp, #-0x10]
    // 0xab5368: StoreField: r1->field_1f = r2
    //     0xab5368: stur            w2, [x1, #0x1f]
    // 0xab536c: mov             x2, x1
    // 0xab5370: r1 = Function '<anonymous closure>':.
    //     0xab5370: add             x1, PP, #0x43, lsl #12  ; [pp+0x43018] AnonymousClosure: (0xab53b4), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnExecute (0xab52a0)
    //     0xab5374: ldr             x1, [x1, #0x18]
    // 0xab5378: r0 = AllocateClosure()
    //     0xab5378: bl              #0xec1630  ; AllocateClosureStub
    // 0xab537c: mov             x1, x0
    // 0xab5380: ldur            x0, [fp, #-8]
    // 0xab5384: StoreField: r1->field_b = r0
    //     0xab5384: stur            w0, [x1, #0xb]
    // 0xab5388: ldur            x16, [fp, #-0x28]
    // 0xab538c: stp             x16, x0, [SP, #0x10]
    // 0xab5390: ldur            x16, [fp, #-0x20]
    // 0xab5394: stp             x1, x16, [SP]
    // 0xab5398: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab5398: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab539c: r0 = txnSynchronized()
    //     0xab539c: bl              #0xab41e0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized
    // 0xab53a0: LeaveFrame
    //     0xab53a0: mov             SP, fp
    //     0xab53a4: ldp             fp, lr, [SP], #0x10
    // 0xab53a8: ret
    //     0xab53a8: ret             
    // 0xab53ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab53ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab53b0: b               #0xab5340
  }
  [closure] Future<Y0> <anonymous closure>(dynamic, Transaction?) {
    // ** addr: 0xab53b4, size: 0xf0
    // 0xab53b4: EnterFrame
    //     0xab53b4: stp             fp, lr, [SP, #-0x10]!
    //     0xab53b8: mov             fp, SP
    // 0xab53bc: AllocStack(0x48)
    //     0xab53bc: sub             SP, SP, #0x48
    // 0xab53c0: SetupParameters()
    //     0xab53c0: ldr             x0, [fp, #0x18]
    //     0xab53c4: ldur            w2, [x0, #0x17]
    //     0xab53c8: add             x2, x2, HEAP, lsl #32
    //     0xab53cc: stur            x2, [fp, #-0x10]
    // 0xab53d0: CheckStackOverflow
    //     0xab53d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab53d4: cmp             SP, x16
    //     0xab53d8: b.ls            #0xab549c
    // 0xab53dc: LoadField: r3 = r0->field_b
    //     0xab53dc: ldur            w3, [x0, #0xb]
    // 0xab53e0: DecompressPointer r3
    //     0xab53e0: add             x3, x3, HEAP, lsl #32
    // 0xab53e4: stur            x3, [fp, #-8]
    // 0xab53e8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xab53e8: ldur            w1, [x2, #0x17]
    // 0xab53ec: DecompressPointer r1
    //     0xab53ec: add             x1, x1, HEAP, lsl #32
    // 0xab53f0: r0 = getSqlInTransactionArgument()
    //     0xab53f0: bl              #0xab54a4  ; [package:sqflite_common/src/utils.dart] ::getSqlInTransactionArgument
    // 0xab53f4: cmp             w0, NULL
    // 0xab53f8: b.ne            #0xab5404
    // 0xab53fc: ldur            x1, [fp, #-0x10]
    // 0xab5400: b               #0xab5428
    // 0xab5404: tbnz            w0, #4, #0xab5424
    // 0xab5408: ldur            x1, [fp, #-0x10]
    // 0xab540c: r0 = true
    //     0xab540c: add             x0, NULL, #0x20  ; true
    // 0xab5410: LoadField: r2 = r1->field_f
    //     0xab5410: ldur            w2, [x1, #0xf]
    // 0xab5414: DecompressPointer r2
    //     0xab5414: add             x2, x2, HEAP, lsl #32
    // 0xab5418: StoreField: r2->field_1b = r0
    //     0xab5418: stur            w0, [x2, #0x1b]
    // 0xab541c: r0 = true
    //     0xab541c: add             x0, NULL, #0x20  ; true
    // 0xab5420: b               #0xab5448
    // 0xab5424: ldur            x1, [fp, #-0x10]
    // 0xab5428: r16 = false
    //     0xab5428: add             x16, NULL, #0x30  ; false
    // 0xab542c: cmp             w0, w16
    // 0xab5430: b.ne            #0xab5448
    // 0xab5434: r0 = false
    //     0xab5434: add             x0, NULL, #0x30  ; false
    // 0xab5438: LoadField: r2 = r1->field_f
    //     0xab5438: ldur            w2, [x1, #0xf]
    // 0xab543c: DecompressPointer r2
    //     0xab543c: add             x2, x2, HEAP, lsl #32
    // 0xab5440: StoreField: r2->field_1b = r0
    //     0xab5440: stur            w0, [x2, #0x1b]
    // 0xab5444: r0 = false
    //     0xab5444: add             x0, NULL, #0x30  ; false
    // 0xab5448: LoadField: r2 = r1->field_f
    //     0xab5448: ldur            w2, [x1, #0xf]
    // 0xab544c: DecompressPointer r2
    //     0xab544c: add             x2, x2, HEAP, lsl #32
    // 0xab5450: LoadField: r3 = r1->field_13
    //     0xab5450: ldur            w3, [x1, #0x13]
    // 0xab5454: DecompressPointer r3
    //     0xab5454: add             x3, x3, HEAP, lsl #32
    // 0xab5458: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xab5458: ldur            w4, [x1, #0x17]
    // 0xab545c: DecompressPointer r4
    //     0xab545c: add             x4, x4, HEAP, lsl #32
    // 0xab5460: LoadField: r5 = r1->field_1b
    //     0xab5460: ldur            w5, [x1, #0x1b]
    // 0xab5464: DecompressPointer r5
    //     0xab5464: add             x5, x5, HEAP, lsl #32
    // 0xab5468: LoadField: r6 = r1->field_1f
    //     0xab5468: ldur            w6, [x1, #0x1f]
    // 0xab546c: DecompressPointer r6
    //     0xab546c: add             x6, x6, HEAP, lsl #32
    // 0xab5470: ldur            x16, [fp, #-8]
    // 0xab5474: stp             x2, x16, [SP, #0x28]
    // 0xab5478: stp             x4, x3, [SP, #0x18]
    // 0xab547c: stp             x0, x5, [SP, #8]
    // 0xab5480: str             x6, [SP]
    // 0xab5484: r4 = const [0x1, 0x6, 0x6, 0x5, beginTransaction, 0x5, null]
    //     0xab5484: add             x4, PP, #0x43, lsl #12  ; [pp+0x43020] List(7) [0x1, 0x6, 0x6, 0x5, "beginTransaction", 0x5, Null]
    //     0xab5488: ldr             x4, [x4, #0x20]
    // 0xab548c: r0 = invokeExecute()
    //     0xab548c: bl              #0xab4ac4  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::invokeExecute
    // 0xab5490: LeaveFrame
    //     0xab5490: mov             SP, fp
    //     0xab5494: ldp             fp, lr, [SP], #0x10
    // 0xab5498: ret
    //     0xab5498: ret             
    // 0xab549c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab549c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab54a0: b               #0xab53dc
  }
  _ beginTransaction(/* No info */) async {
    // ** addr: 0xab5560, size: 0x60
    // 0xab5560: EnterFrame
    //     0xab5560: stp             fp, lr, [SP, #-0x10]!
    //     0xab5564: mov             fp, SP
    // 0xab5568: AllocStack(0x18)
    //     0xab5568: sub             SP, SP, #0x18
    // 0xab556c: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x10 */)
    //     0xab556c: stur            NULL, [fp, #-8]
    //     0xab5570: stur            x1, [fp, #-0x10]
    // 0xab5574: CheckStackOverflow
    //     0xab5574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5578: cmp             SP, x16
    //     0xab557c: b.ls            #0xab55b8
    // 0xab5580: InitAsync() -> Future<SqfliteTransaction>
    //     0xab5580: add             x0, PP, #0x43, lsl #12  ; [pp+0x43218] TypeArguments: <SqfliteTransaction>
    //     0xab5584: ldr             x0, [x0, #0x218]
    //     0xab5588: bl              #0x661298  ; InitAsyncStub
    // 0xab558c: ldur            x1, [fp, #-0x10]
    // 0xab5590: r0 = newTransaction()
    //     0xab5590: bl              #0xab5758  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::newTransaction
    // 0xab5594: ldur            x1, [fp, #-0x10]
    // 0xab5598: mov             x2, x0
    // 0xab559c: stur            x0, [fp, #-0x10]
    // 0xab55a0: r0 = SqfliteDatabaseMixinExt.txnBeginTransaction()
    //     0xab55a0: bl              #0xab55c0  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.txnBeginTransaction
    // 0xab55a4: mov             x1, x0
    // 0xab55a8: stur            x1, [fp, #-0x18]
    // 0xab55ac: r0 = Await()
    //     0xab55ac: bl              #0x661044  ; AwaitStub
    // 0xab55b0: ldur            x0, [fp, #-0x10]
    // 0xab55b4: r0 = ReturnAsyncNotFuture()
    //     0xab55b4: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab55b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab55b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab55bc: b               #0xab5580
  }
  _ newTransaction(/* No info */) {
    // ** addr: 0xab5758, size: 0x18
    // 0xab5758: EnterFrame
    //     0xab5758: stp             fp, lr, [SP, #-0x10]!
    //     0xab575c: mov             fp, SP
    // 0xab5760: r0 = SqfliteTransaction()
    //     0xab5760: bl              #0xab4d88  ; AllocateSqfliteTransactionStub -> SqfliteTransaction (size=0x14)
    // 0xab5764: LeaveFrame
    //     0xab5764: mov             SP, fp
    //     0xab5768: ldp             fp, lr, [SP], #0x10
    // 0xab576c: ret
    //     0xab576c: ret             
  }
  _ txnRawQuery(/* No info */) {
    // ** addr: 0xab59dc, size: 0x98
    // 0xab59dc: EnterFrame
    //     0xab59dc: stp             fp, lr, [SP, #-0x10]!
    //     0xab59e0: mov             fp, SP
    // 0xab59e4: AllocStack(0x40)
    //     0xab59e4: sub             SP, SP, #0x40
    // 0xab59e8: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xab59e8: stur            x1, [fp, #-8]
    //     0xab59ec: stur            x2, [fp, #-0x10]
    //     0xab59f0: stur            x3, [fp, #-0x18]
    //     0xab59f4: stur            x5, [fp, #-0x20]
    // 0xab59f8: CheckStackOverflow
    //     0xab59f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab59fc: cmp             SP, x16
    //     0xab5a00: b.ls            #0xab5a6c
    // 0xab5a04: r1 = 4
    //     0xab5a04: movz            x1, #0x4
    // 0xab5a08: r0 = AllocateContext()
    //     0xab5a08: bl              #0xec126c  ; AllocateContextStub
    // 0xab5a0c: mov             x1, x0
    // 0xab5a10: ldur            x0, [fp, #-8]
    // 0xab5a14: StoreField: r1->field_f = r0
    //     0xab5a14: stur            w0, [x1, #0xf]
    // 0xab5a18: ldur            x3, [fp, #-0x10]
    // 0xab5a1c: StoreField: r1->field_13 = r3
    //     0xab5a1c: stur            w3, [x1, #0x13]
    // 0xab5a20: ldur            x2, [fp, #-0x18]
    // 0xab5a24: ArrayStore: r1[0] = r2  ; List_4
    //     0xab5a24: stur            w2, [x1, #0x17]
    // 0xab5a28: ldur            x2, [fp, #-0x20]
    // 0xab5a2c: StoreField: r1->field_1b = r2
    //     0xab5a2c: stur            w2, [x1, #0x1b]
    // 0xab5a30: mov             x2, x1
    // 0xab5a34: r1 = Function '<anonymous closure>':.
    //     0xab5a34: add             x1, PP, #0x43, lsl #12  ; [pp+0x43180] AnonymousClosure: (0xab5a74), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnRawQuery (0xab59dc)
    //     0xab5a38: ldr             x1, [x1, #0x180]
    // 0xab5a3c: r0 = AllocateClosure()
    //     0xab5a3c: bl              #0xec1630  ; AllocateClosureStub
    // 0xab5a40: r16 = <List<Map<String, Object?>>>
    //     0xab5a40: add             x16, PP, #0x43, lsl #12  ; [pp+0x43188] TypeArguments: <List<Map<String, Object?>>>
    //     0xab5a44: ldr             x16, [x16, #0x188]
    // 0xab5a48: ldur            lr, [fp, #-8]
    // 0xab5a4c: stp             lr, x16, [SP, #0x10]
    // 0xab5a50: ldur            x16, [fp, #-0x10]
    // 0xab5a54: stp             x0, x16, [SP]
    // 0xab5a58: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab5a58: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab5a5c: r0 = txnSynchronized()
    //     0xab5a5c: bl              #0xab41e0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized
    // 0xab5a60: LeaveFrame
    //     0xab5a60: mov             SP, fp
    //     0xab5a64: ldp             fp, lr, [SP], #0x10
    // 0xab5a68: ret
    //     0xab5a68: ret             
    // 0xab5a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5a6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab5a70: b               #0xab5a04
  }
  [closure] Future<List<Map<String, Object?>>> <anonymous closure>(dynamic, Transaction?) async {
    // ** addr: 0xab5a74, size: 0xa8
    // 0xab5a74: EnterFrame
    //     0xab5a74: stp             fp, lr, [SP, #-0x10]!
    //     0xab5a78: mov             fp, SP
    // 0xab5a7c: AllocStack(0x38)
    //     0xab5a7c: sub             SP, SP, #0x38
    // 0xab5a80: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 */)
    //     0xab5a80: stur            NULL, [fp, #-8]
    //     0xab5a84: movz            x0, #0
    //     0xab5a88: add             x1, fp, w0, sxtw #2
    //     0xab5a8c: ldr             x1, [x1, #0x18]
    //     0xab5a90: ldur            w2, [x1, #0x17]
    //     0xab5a94: add             x2, x2, HEAP, lsl #32
    //     0xab5a98: stur            x2, [fp, #-0x10]
    // 0xab5a9c: CheckStackOverflow
    //     0xab5a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5aa0: cmp             SP, x16
    //     0xab5aa4: b.ls            #0xab5b14
    // 0xab5aa8: InitAsync() -> Future<List<Map<String, Object?>>>
    //     0xab5aa8: add             x0, PP, #0x43, lsl #12  ; [pp+0x43188] TypeArguments: <List<Map<String, Object?>>>
    //     0xab5aac: ldr             x0, [x0, #0x188]
    //     0xab5ab0: bl              #0x661298  ; InitAsyncStub
    // 0xab5ab4: ldur            x0, [fp, #-0x10]
    // 0xab5ab8: LoadField: r4 = r0->field_f
    //     0xab5ab8: ldur            w4, [x0, #0xf]
    // 0xab5abc: DecompressPointer r4
    //     0xab5abc: add             x4, x4, HEAP, lsl #32
    // 0xab5ac0: stur            x4, [fp, #-0x18]
    // 0xab5ac4: LoadField: r2 = r0->field_13
    //     0xab5ac4: ldur            w2, [x0, #0x13]
    // 0xab5ac8: DecompressPointer r2
    //     0xab5ac8: add             x2, x2, HEAP, lsl #32
    // 0xab5acc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xab5acc: ldur            w3, [x0, #0x17]
    // 0xab5ad0: DecompressPointer r3
    //     0xab5ad0: add             x3, x3, HEAP, lsl #32
    // 0xab5ad4: LoadField: r5 = r0->field_1b
    //     0xab5ad4: ldur            w5, [x0, #0x1b]
    // 0xab5ad8: DecompressPointer r5
    //     0xab5ad8: add             x5, x5, HEAP, lsl #32
    // 0xab5adc: mov             x1, x4
    // 0xab5ae0: r0 = SqfliteDatabaseMixinExt._txnGetSqlMethodArguments()
    //     0xab5ae0: bl              #0xab4c24  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt._txnGetSqlMethodArguments
    // 0xab5ae4: ldur            x16, [fp, #-0x18]
    // 0xab5ae8: stp             x16, NULL, [SP, #0x10]
    // 0xab5aec: r16 = "query"
    //     0xab5aec: ldr             x16, [PP, #0x3650]  ; [pp+0x3650] "query"
    // 0xab5af0: stp             x0, x16, [SP]
    // 0xab5af4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab5af4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab5af8: r0 = safeInvokeMethod()
    //     0xab5af8: bl              #0xab484c  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod
    // 0xab5afc: mov             x1, x0
    // 0xab5b00: stur            x1, [fp, #-0x18]
    // 0xab5b04: r0 = Await()
    //     0xab5b04: bl              #0x661044  ; AwaitStub
    // 0xab5b08: mov             x1, x0
    // 0xab5b0c: r0 = queryResultToList()
    //     0xab5b0c: bl              #0xab5b1c  ; [package:sqflite_common/src/collection_utils.dart] ::queryResultToList
    // 0xab5b10: r0 = ReturnAsyncNotFuture()
    //     0xab5b10: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab5b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5b14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab5b18: b               #0xab5aa8
  }
  _ openDatabase(/* No info */) async {
    // ** addr: 0xab60b0, size: 0x444
    // 0xab60b0: EnterFrame
    //     0xab60b0: stp             fp, lr, [SP, #-0x10]!
    //     0xab60b4: mov             fp, SP
    // 0xab60b8: AllocStack(0xb0)
    //     0xab60b8: sub             SP, SP, #0xb0
    // 0xab60bc: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x78 */)
    //     0xab60bc: stur            NULL, [fp, #-8]
    //     0xab60c0: stur            x1, [fp, #-0x78]
    // 0xab60c4: CheckStackOverflow
    //     0xab60c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab60c8: cmp             SP, x16
    //     0xab60cc: b.ls            #0xab64dc
    // 0xab60d0: InitAsync() -> Future<int>
    //     0xab60d0: ldr             x0, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    //     0xab60d4: bl              #0x661298  ; InitAsyncStub
    // 0xab60d8: r1 = Null
    //     0xab60d8: mov             x1, NULL
    // 0xab60dc: r2 = 4
    //     0xab60dc: movz            x2, #0x4
    // 0xab60e0: r0 = AllocateArray()
    //     0xab60e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab60e4: r16 = "path"
    //     0xab60e4: ldr             x16, [PP, #0x3638]  ; [pp+0x3638] "path"
    // 0xab60e8: StoreField: r0->field_f = r16
    //     0xab60e8: stur            w16, [x0, #0xf]
    // 0xab60ec: ldur            x1, [fp, #-0x78]
    // 0xab60f0: LoadField: r2 = r1->field_b
    //     0xab60f0: ldur            w2, [x1, #0xb]
    // 0xab60f4: DecompressPointer r2
    //     0xab60f4: add             x2, x2, HEAP, lsl #32
    // 0xab60f8: r16 = Sentinel
    //     0xab60f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab60fc: cmp             w2, w16
    // 0xab6100: b.eq            #0xab64e4
    // 0xab6104: StoreField: r0->field_13 = r2
    //     0xab6104: stur            w2, [x0, #0x13]
    // 0xab6108: r16 = <String, Object?>
    //     0xab6108: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xab610c: ldr             x16, [x16, #0x738]
    // 0xab6110: stp             x0, x16, [SP]
    // 0xab6114: r0 = Map._fromLiteral()
    //     0xab6114: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab6118: ldur            x1, [fp, #-0x78]
    // 0xab611c: stur            x0, [fp, #-0x80]
    // 0xab6120: r0 = SqfliteDatabaseMixinExt.readOnly()
    //     0xab6120: bl              #0xab4710  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.readOnly
    // 0xab6124: ldur            x0, [fp, #-0x78]
    // 0xab6128: LoadField: r1 = r0->field_2b
    //     0xab6128: ldur            w1, [x0, #0x2b]
    // 0xab612c: DecompressPointer r1
    //     0xab612c: add             x1, x1, HEAP, lsl #32
    // 0xab6130: cmp             w1, NULL
    // 0xab6134: b.ne            #0xab6140
    // 0xab6138: r1 = Null
    //     0xab6138: mov             x1, NULL
    // 0xab613c: b               #0xab6144
    // 0xab6140: r1 = true
    //     0xab6140: add             x1, NULL, #0x20  ; true
    // 0xab6144: cmp             w1, NULL
    // 0xab6148: b.eq            #0xab6168
    // 0xab614c: tbnz            w1, #4, #0xab6168
    // 0xab6150: LoadField: r1 = r0->field_b
    //     0xab6150: ldur            w1, [x0, #0xb]
    // 0xab6154: DecompressPointer r1
    //     0xab6154: add             x1, x1, HEAP, lsl #32
    // 0xab6158: r0 = isInMemoryDatabasePath()
    //     0xab6158: bl              #0xab3878  ; [package:sqflite_common/src/path_utils.dart] ::isInMemoryDatabasePath
    // 0xab615c: eor             x1, x0, #0x10
    // 0xab6160: mov             x0, x1
    // 0xab6164: b               #0xab616c
    // 0xab6168: r0 = false
    //     0xab6168: add             x0, NULL, #0x30  ; false
    // 0xab616c: ldur            x1, [fp, #-0x80]
    // 0xab6170: mov             x3, x0
    // 0xab6174: stur            x0, [fp, #-0x88]
    // 0xab6178: r2 = "singleInstance"
    //     0xab6178: add             x2, PP, #0x43, lsl #12  ; [pp+0x43300] "singleInstance"
    //     0xab617c: ldr             x2, [x2, #0x300]
    // 0xab6180: r0 = []=()
    //     0xab6180: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab6184: r16 = <Object?>
    //     0xab6184: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xab6188: ldur            lr, [fp, #-0x78]
    // 0xab618c: stp             lr, x16, [SP, #0x10]
    // 0xab6190: r16 = "openDatabase"
    //     0xab6190: add             x16, PP, #0x43, lsl #12  ; [pp+0x43120] "openDatabase"
    //     0xab6194: ldr             x16, [x16, #0x120]
    // 0xab6198: ldur            lr, [fp, #-0x80]
    // 0xab619c: stp             lr, x16, [SP]
    // 0xab61a0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab61a0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab61a4: r0 = safeInvokeMethod()
    //     0xab61a4: bl              #0xab484c  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod
    // 0xab61a8: mov             x1, x0
    // 0xab61ac: stur            x1, [fp, #-0x90]
    // 0xab61b0: r0 = Await()
    //     0xab61b0: bl              #0x661044  ; AwaitStub
    // 0xab61b4: mov             x3, x0
    // 0xab61b8: stur            x3, [fp, #-0x80]
    // 0xab61bc: r0 = 60
    //     0xab61bc: movz            x0, #0x3c
    // 0xab61c0: branchIfSmi(r3, 0xab61cc)
    //     0xab61c0: tbz             w3, #0, #0xab61cc
    // 0xab61c4: r0 = LoadClassIdInstr(r3)
    //     0xab61c4: ldur            x0, [x3, #-1]
    //     0xab61c8: ubfx            x0, x0, #0xc, #0x14
    // 0xab61cc: sub             x16, x0, #0x3c
    // 0xab61d0: cmp             x16, #1
    // 0xab61d4: b.hi            #0xab61e0
    // 0xab61d8: mov             x0, x3
    // 0xab61dc: r0 = ReturnAsyncNotFuture()
    //     0xab61dc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab61e0: mov             x0, x3
    // 0xab61e4: r2 = Null
    //     0xab61e4: mov             x2, NULL
    // 0xab61e8: r1 = Null
    //     0xab61e8: mov             x1, NULL
    // 0xab61ec: cmp             w0, NULL
    // 0xab61f0: b.eq            #0xab6288
    // 0xab61f4: branchIfSmi(r0, 0xab6288)
    //     0xab61f4: tbz             w0, #0, #0xab6288
    // 0xab61f8: r3 = LoadClassIdInstr(r0)
    //     0xab61f8: ldur            x3, [x0, #-1]
    //     0xab61fc: ubfx            x3, x3, #0xc, #0x14
    // 0xab6200: r17 = 6717
    //     0xab6200: movz            x17, #0x1a3d
    // 0xab6204: cmp             x3, x17
    // 0xab6208: b.eq            #0xab6290
    // 0xab620c: r4 = LoadClassIdInstr(r0)
    //     0xab620c: ldur            x4, [x0, #-1]
    //     0xab6210: ubfx            x4, x4, #0xc, #0x14
    // 0xab6214: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xab6218: ldr             x3, [x3, #0x18]
    // 0xab621c: ldr             x3, [x3, x4, lsl #3]
    // 0xab6220: LoadField: r3 = r3->field_2b
    //     0xab6220: ldur            w3, [x3, #0x2b]
    // 0xab6224: DecompressPointer r3
    //     0xab6224: add             x3, x3, HEAP, lsl #32
    // 0xab6228: cmp             w3, NULL
    // 0xab622c: b.eq            #0xab6288
    // 0xab6230: LoadField: r3 = r3->field_f
    //     0xab6230: ldur            w3, [x3, #0xf]
    // 0xab6234: lsr             x3, x3, #3
    // 0xab6238: r17 = 6717
    //     0xab6238: movz            x17, #0x1a3d
    // 0xab623c: cmp             x3, x17
    // 0xab6240: b.eq            #0xab6290
    // 0xab6244: r3 = SubtypeTestCache
    //     0xab6244: add             x3, PP, #0x43, lsl #12  ; [pp+0x43308] SubtypeTestCache
    //     0xab6248: ldr             x3, [x3, #0x308]
    // 0xab624c: r30 = Subtype1TestCacheStub
    //     0xab624c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xab6250: LoadField: r30 = r30->field_7
    //     0xab6250: ldur            lr, [lr, #7]
    // 0xab6254: blr             lr
    // 0xab6258: cmp             w7, NULL
    // 0xab625c: b.eq            #0xab6268
    // 0xab6260: tbnz            w7, #4, #0xab6288
    // 0xab6264: b               #0xab6290
    // 0xab6268: r8 = Map
    //     0xab6268: add             x8, PP, #0x43, lsl #12  ; [pp+0x43310] Type: Map
    //     0xab626c: ldr             x8, [x8, #0x310]
    // 0xab6270: r3 = SubtypeTestCache
    //     0xab6270: add             x3, PP, #0x43, lsl #12  ; [pp+0x43318] SubtypeTestCache
    //     0xab6274: ldr             x3, [x3, #0x318]
    // 0xab6278: r30 = InstanceOfStub
    //     0xab6278: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xab627c: LoadField: r30 = r30->field_7
    //     0xab627c: ldur            lr, [lr, #7]
    // 0xab6280: blr             lr
    // 0xab6284: b               #0xab6294
    // 0xab6288: r0 = false
    //     0xab6288: add             x0, NULL, #0x30  ; false
    // 0xab628c: b               #0xab6294
    // 0xab6290: r0 = true
    //     0xab6290: add             x0, NULL, #0x20  ; true
    // 0xab6294: tbnz            w0, #4, #0xab644c
    // 0xab6298: ldur            x3, [fp, #-0x80]
    // 0xab629c: r0 = LoadClassIdInstr(r3)
    //     0xab629c: ldur            x0, [x3, #-1]
    //     0xab62a0: ubfx            x0, x0, #0xc, #0x14
    // 0xab62a4: mov             x1, x3
    // 0xab62a8: r2 = "id"
    //     0xab62a8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xab62ac: ldr             x2, [x2, #0x740]
    // 0xab62b0: r0 = GDT[cid_x0 + -0x114]()
    //     0xab62b0: sub             lr, x0, #0x114
    //     0xab62b4: ldr             lr, [x21, lr, lsl #3]
    //     0xab62b8: blr             lr
    // 0xab62bc: mov             x3, x0
    // 0xab62c0: r2 = Null
    //     0xab62c0: mov             x2, NULL
    // 0xab62c4: r1 = Null
    //     0xab62c4: mov             x1, NULL
    // 0xab62c8: stur            x3, [fp, #-0x88]
    // 0xab62cc: branchIfSmi(r0, 0xab62f4)
    //     0xab62cc: tbz             w0, #0, #0xab62f4
    // 0xab62d0: r4 = LoadClassIdInstr(r0)
    //     0xab62d0: ldur            x4, [x0, #-1]
    //     0xab62d4: ubfx            x4, x4, #0xc, #0x14
    // 0xab62d8: sub             x4, x4, #0x3c
    // 0xab62dc: cmp             x4, #1
    // 0xab62e0: b.ls            #0xab62f4
    // 0xab62e4: r8 = int?
    //     0xab62e4: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xab62e8: r3 = Null
    //     0xab62e8: add             x3, PP, #0x43, lsl #12  ; [pp+0x43320] Null
    //     0xab62ec: ldr             x3, [x3, #0x320]
    // 0xab62f0: r0 = int?()
    //     0xab62f0: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xab62f4: ldur            x0, [fp, #-0x80]
    // 0xab62f8: r1 = LoadClassIdInstr(r0)
    //     0xab62f8: ldur            x1, [x0, #-1]
    //     0xab62fc: ubfx            x1, x1, #0xc, #0x14
    // 0xab6300: mov             x16, x0
    // 0xab6304: mov             x0, x1
    // 0xab6308: mov             x1, x16
    // 0xab630c: r2 = "recoveredInTransaction"
    //     0xab630c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43330] "recoveredInTransaction"
    //     0xab6310: ldr             x2, [x2, #0x330]
    // 0xab6314: r0 = GDT[cid_x0 + -0x114]()
    //     0xab6314: sub             lr, x0, #0x114
    //     0xab6318: ldr             lr, [x21, lr, lsl #3]
    //     0xab631c: blr             lr
    // 0xab6320: r1 = 60
    //     0xab6320: movz            x1, #0x3c
    // 0xab6324: branchIfSmi(r0, 0xab6330)
    //     0xab6324: tbz             w0, #0, #0xab6330
    // 0xab6328: r1 = LoadClassIdInstr(r0)
    //     0xab6328: ldur            x1, [x0, #-1]
    //     0xab632c: ubfx            x1, x1, #0xc, #0x14
    // 0xab6330: r16 = true
    //     0xab6330: add             x16, NULL, #0x20  ; true
    // 0xab6334: stp             x16, x0, [SP]
    // 0xab6338: mov             x0, x1
    // 0xab633c: mov             lr, x0
    // 0xab6340: ldr             lr, [x21, lr, lsl #3]
    // 0xab6344: blr             lr
    // 0xab6348: tbnz            w0, #4, #0xab63fc
    // 0xab634c: ldur            x1, [fp, #-0x78]
    // 0xab6350: r0 = SqfliteDatabaseMixinExt.readOnly()
    //     0xab6350: bl              #0xab4710  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.readOnly
    // 0xab6354: ldur            x0, [fp, #-0x88]
    // 0xab6358: r1 = Null
    //     0xab6358: mov             x1, NULL
    // 0xab635c: r2 = 16
    //     0xab635c: movz            x2, #0x10
    // 0xab6360: r0 = AllocateArray()
    //     0xab6360: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab6364: r16 = "sql"
    //     0xab6364: add             x16, PP, #0x43, lsl #12  ; [pp+0x43070] "sql"
    //     0xab6368: ldr             x16, [x16, #0x70]
    // 0xab636c: StoreField: r0->field_f = r16
    //     0xab636c: stur            w16, [x0, #0xf]
    // 0xab6370: r16 = "ROLLBACK"
    //     0xab6370: add             x16, PP, #0x43, lsl #12  ; [pp+0x432b0] "ROLLBACK"
    //     0xab6374: ldr             x16, [x16, #0x2b0]
    // 0xab6378: StoreField: r0->field_13 = r16
    //     0xab6378: stur            w16, [x0, #0x13]
    // 0xab637c: r16 = "id"
    //     0xab637c: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xab6380: ldr             x16, [x16, #0x740]
    // 0xab6384: ArrayStore: r0[0] = r16  ; List_4
    //     0xab6384: stur            w16, [x0, #0x17]
    // 0xab6388: ldur            x1, [fp, #-0x88]
    // 0xab638c: StoreField: r0->field_1b = r1
    //     0xab638c: stur            w1, [x0, #0x1b]
    // 0xab6390: r16 = "transactionId"
    //     0xab6390: add             x16, PP, #0x43, lsl #12  ; [pp+0x43028] "transactionId"
    //     0xab6394: ldr             x16, [x16, #0x28]
    // 0xab6398: StoreField: r0->field_1f = r16
    //     0xab6398: stur            w16, [x0, #0x1f]
    // 0xab639c: r16 = -2
    //     0xab639c: orr             x16, xzr, #0xfffffffffffffffe
    // 0xab63a0: StoreField: r0->field_23 = r16
    //     0xab63a0: stur            w16, [x0, #0x23]
    // 0xab63a4: r16 = "inTransaction"
    //     0xab63a4: add             x16, PP, #0x43, lsl #12  ; [pp+0x43068] "inTransaction"
    //     0xab63a8: ldr             x16, [x16, #0x68]
    // 0xab63ac: StoreField: r0->field_27 = r16
    //     0xab63ac: stur            w16, [x0, #0x27]
    // 0xab63b0: r16 = false
    //     0xab63b0: add             x16, NULL, #0x30  ; false
    // 0xab63b4: StoreField: r0->field_2b = r16
    //     0xab63b4: stur            w16, [x0, #0x2b]
    // 0xab63b8: r16 = <String, Object?>
    //     0xab63b8: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xab63bc: ldr             x16, [x16, #0x738]
    // 0xab63c0: stp             x0, x16, [SP]
    // 0xab63c4: r0 = Map._fromLiteral()
    //     0xab63c4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab63c8: r16 = <Object?>
    //     0xab63c8: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xab63cc: ldur            lr, [fp, #-0x78]
    // 0xab63d0: stp             lr, x16, [SP, #0x10]
    // 0xab63d4: r16 = "execute"
    //     0xab63d4: add             x16, PP, #0x43, lsl #12  ; [pp+0x43030] "execute"
    //     0xab63d8: ldr             x16, [x16, #0x30]
    // 0xab63dc: stp             x0, x16, [SP]
    // 0xab63e0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab63e0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab63e4: r0 = safeInvokeMethod()
    //     0xab63e4: bl              #0xab484c  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod
    // 0xab63e8: mov             x1, x0
    // 0xab63ec: stur            x1, [fp, #-0x78]
    // 0xab63f0: r0 = Await()
    //     0xab63f0: bl              #0x661044  ; AwaitStub
    // 0xab63f4: ldur            x0, [fp, #-0x88]
    // 0xab63f8: b               #0xab6440
    // 0xab63fc: ldur            x0, [fp, #-0x88]
    // 0xab6400: b               #0xab6440
    // 0xab6404: sub             SP, fp, #0xb0
    // 0xab6408: stur            x0, [fp, #-0x78]
    // 0xab640c: r1 = Null
    //     0xab640c: mov             x1, NULL
    // 0xab6410: r2 = 4
    //     0xab6410: movz            x2, #0x4
    // 0xab6414: r0 = AllocateArray()
    //     0xab6414: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab6418: r16 = "ignore recovered database ROLLBACK error "
    //     0xab6418: add             x16, PP, #0x43, lsl #12  ; [pp+0x43338] "ignore recovered database ROLLBACK error "
    //     0xab641c: ldr             x16, [x16, #0x338]
    // 0xab6420: StoreField: r0->field_f = r16
    //     0xab6420: stur            w16, [x0, #0xf]
    // 0xab6424: ldur            x1, [fp, #-0x78]
    // 0xab6428: StoreField: r0->field_13 = r1
    //     0xab6428: stur            w1, [x0, #0x13]
    // 0xab642c: str             x0, [SP]
    // 0xab6430: r0 = _interpolate()
    //     0xab6430: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab6434: mov             x1, x0
    // 0xab6438: r0 = print()
    //     0xab6438: bl              #0x63fe38  ; [dart:core] ::print
    // 0xab643c: ldur            x0, [fp, #-0x60]
    // 0xab6440: cmp             w0, NULL
    // 0xab6444: b.eq            #0xab64f0
    // 0xab6448: r0 = ReturnAsyncNotFuture()
    //     0xab6448: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab644c: ldur            x0, [fp, #-0x80]
    // 0xab6450: r1 = Null
    //     0xab6450: mov             x1, NULL
    // 0xab6454: r2 = 10
    //     0xab6454: movz            x2, #0xa
    // 0xab6458: r0 = AllocateArray()
    //     0xab6458: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab645c: stur            x0, [fp, #-0x78]
    // 0xab6460: r16 = "unsupported result "
    //     0xab6460: add             x16, PP, #0x43, lsl #12  ; [pp+0x43340] "unsupported result "
    //     0xab6464: ldr             x16, [x16, #0x340]
    // 0xab6468: StoreField: r0->field_f = r16
    //     0xab6468: stur            w16, [x0, #0xf]
    // 0xab646c: ldur            x1, [fp, #-0x80]
    // 0xab6470: StoreField: r0->field_13 = r1
    //     0xab6470: stur            w1, [x0, #0x13]
    // 0xab6474: r16 = " ("
    //     0xab6474: ldr             x16, [PP, #0x980]  ; [pp+0x980] " ("
    // 0xab6478: ArrayStore: r0[0] = r16  ; List_4
    //     0xab6478: stur            w16, [x0, #0x17]
    // 0xab647c: cmp             w1, NULL
    // 0xab6480: b.ne            #0xab6490
    // 0xab6484: mov             x2, x0
    // 0xab6488: r0 = Null
    //     0xab6488: mov             x0, NULL
    // 0xab648c: b               #0xab649c
    // 0xab6490: str             x1, [SP]
    // 0xab6494: r0 = runtimeType()
    //     0xab6494: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xab6498: ldur            x2, [fp, #-0x78]
    // 0xab649c: mov             x1, x2
    // 0xab64a0: ArrayStore: r1[3] = r0  ; List_4
    //     0xab64a0: add             x25, x1, #0x1b
    //     0xab64a4: str             w0, [x25]
    //     0xab64a8: tbz             w0, #0, #0xab64c4
    //     0xab64ac: ldurb           w16, [x1, #-1]
    //     0xab64b0: ldurb           w17, [x0, #-1]
    //     0xab64b4: and             x16, x17, x16, lsr #2
    //     0xab64b8: tst             x16, HEAP, lsr #32
    //     0xab64bc: b.eq            #0xab64c4
    //     0xab64c0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xab64c4: r16 = ")"
    //     0xab64c4: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xab64c8: StoreField: r2->field_1f = r16
    //     0xab64c8: stur            w16, [x2, #0x1f]
    // 0xab64cc: str             x2, [SP]
    // 0xab64d0: r0 = _interpolate()
    //     0xab64d0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab64d4: r0 = Throw()
    //     0xab64d4: bl              #0xec04b8  ; ThrowStub
    // 0xab64d8: brk             #0
    // 0xab64dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab64dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab64e0: b               #0xab60d0
    // 0xab64e4: r9 = path
    //     0xab64e4: add             x9, PP, #0x43, lsl #12  ; [pp+0x43210] Field <_SqfliteDatabaseBase&Object&<EMAIL>>: late (offset: 0xc)
    //     0xab64e8: ldr             x9, [x9, #0x210]
    // 0xab64ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xab64ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xab64f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab64f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, Transaction) async {
    // ** addr: 0xab64f4, size: 0x2c8
    // 0xab64f4: EnterFrame
    //     0xab64f4: stp             fp, lr, [SP, #-0x10]!
    //     0xab64f8: mov             fp, SP
    // 0xab64fc: AllocStack(0x48)
    //     0xab64fc: sub             SP, SP, #0x48
    // 0xab6500: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xab6500: stur            NULL, [fp, #-8]
    //     0xab6504: movz            x0, #0
    //     0xab6508: add             x1, fp, w0, sxtw #2
    //     0xab650c: ldr             x1, [x1, #0x18]
    //     0xab6510: add             x2, fp, w0, sxtw #2
    //     0xab6514: ldr             x2, [x2, #0x10]
    //     0xab6518: stur            x2, [fp, #-0x18]
    //     0xab651c: ldur            w3, [x1, #0x17]
    //     0xab6520: add             x3, x3, HEAP, lsl #32
    //     0xab6524: stur            x3, [fp, #-0x10]
    // 0xab6528: CheckStackOverflow
    //     0xab6528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab652c: cmp             SP, x16
    //     0xab6530: b.ls            #0xab67a4
    // 0xab6534: InitAsync() -> Future<Null?>
    //     0xab6534: ldr             x0, [PP, #0x1430]  ; [pp+0x1430] TypeArguments: <Null?>
    //     0xab6538: bl              #0x661298  ; InitAsyncStub
    // 0xab653c: ldur            x0, [fp, #-0x18]
    // 0xab6540: r2 = Null
    //     0xab6540: mov             x2, NULL
    // 0xab6544: r1 = Null
    //     0xab6544: mov             x1, NULL
    // 0xab6548: r4 = LoadClassIdInstr(r0)
    //     0xab6548: ldur            x4, [x0, #-1]
    //     0xab654c: ubfx            x4, x4, #0xc, #0x14
    // 0xab6550: cmp             x4, #0x1cd
    // 0xab6554: b.eq            #0xab656c
    // 0xab6558: r8 = SqfliteTransaction
    //     0xab6558: add             x8, PP, #0x43, lsl #12  ; [pp+0x43158] Type: SqfliteTransaction
    //     0xab655c: ldr             x8, [x8, #0x158]
    // 0xab6560: r3 = Null
    //     0xab6560: add             x3, PP, #0x43, lsl #12  ; [pp+0x43160] Null
    //     0xab6564: ldr             x3, [x3, #0x160]
    // 0xab6568: r0 = DefaultTypeTest()
    //     0xab6568: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xab656c: ldur            x3, [fp, #-0x10]
    // 0xab6570: LoadField: r1 = r3->field_f
    //     0xab6570: ldur            w1, [x3, #0xf]
    // 0xab6574: DecompressPointer r1
    //     0xab6574: add             x1, x1, HEAP, lsl #32
    // 0xab6578: ldur            x0, [fp, #-0x18]
    // 0xab657c: StoreField: r1->field_f = r0
    //     0xab657c: stur            w0, [x1, #0xf]
    //     0xab6580: ldurb           w16, [x1, #-1]
    //     0xab6584: ldurb           w17, [x0, #-1]
    //     0xab6588: and             x16, x17, x16, lsr #2
    //     0xab658c: tst             x16, HEAP, lsr #32
    //     0xab6590: b.eq            #0xab6598
    //     0xab6594: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab6598: ldur            x2, [fp, #-0x18]
    // 0xab659c: r0 = SqfliteDatabaseMixinExt.txnGetVersion()
    //     0xab659c: bl              #0xab57bc  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt.txnGetVersion
    // 0xab65a0: mov             x1, x0
    // 0xab65a4: stur            x1, [fp, #-0x20]
    // 0xab65a8: r0 = Await()
    //     0xab65a8: bl              #0x661044  ; AwaitStub
    // 0xab65ac: mov             x1, x0
    // 0xab65b0: stur            x1, [fp, #-0x20]
    // 0xab65b4: cbnz            w1, #0xab660c
    // 0xab65b8: ldur            x2, [fp, #-0x10]
    // 0xab65bc: LoadField: r0 = r2->field_13
    //     0xab65bc: ldur            w0, [x2, #0x13]
    // 0xab65c0: DecompressPointer r0
    //     0xab65c0: add             x0, x0, HEAP, lsl #32
    // 0xab65c4: LoadField: r3 = r0->field_13
    //     0xab65c4: ldur            w3, [x0, #0x13]
    // 0xab65c8: DecompressPointer r3
    //     0xab65c8: add             x3, x3, HEAP, lsl #32
    // 0xab65cc: LoadField: r0 = r2->field_f
    //     0xab65cc: ldur            w0, [x2, #0xf]
    // 0xab65d0: DecompressPointer r0
    //     0xab65d0: add             x0, x0, HEAP, lsl #32
    // 0xab65d4: cmp             w3, NULL
    // 0xab65d8: b.eq            #0xab67ac
    // 0xab65dc: stp             x0, x3, [SP, #8]
    // 0xab65e0: r16 = 6
    //     0xab65e0: movz            x16, #0x6
    // 0xab65e4: str             x16, [SP]
    // 0xab65e8: mov             x0, x3
    // 0xab65ec: ClosureCall
    //     0xab65ec: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xab65f0: ldur            x2, [x0, #0x1f]
    //     0xab65f4: blr             x2
    // 0xab65f8: mov             x1, x0
    // 0xab65fc: stur            x1, [fp, #-0x28]
    // 0xab6600: r0 = Await()
    //     0xab6600: bl              #0x661044  ; AwaitStub
    // 0xab6604: ldur            x2, [fp, #-0x10]
    // 0xab6608: b               #0xab6778
    // 0xab660c: cmp             w1, NULL
    // 0xab6610: b.eq            #0xab67b0
    // 0xab6614: r0 = LoadInt32Instr(r1)
    //     0xab6614: sbfx            x0, x1, #1, #0x1f
    //     0xab6618: tbz             w1, #0, #0xab6620
    //     0xab661c: ldur            x0, [x1, #7]
    // 0xab6620: cmp             x0, #3
    // 0xab6624: b.ge            #0xab667c
    // 0xab6628: ldur            x2, [fp, #-0x10]
    // 0xab662c: LoadField: r0 = r2->field_13
    //     0xab662c: ldur            w0, [x2, #0x13]
    // 0xab6630: DecompressPointer r0
    //     0xab6630: add             x0, x0, HEAP, lsl #32
    // 0xab6634: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xab6634: ldur            w3, [x0, #0x17]
    // 0xab6638: DecompressPointer r3
    //     0xab6638: add             x3, x3, HEAP, lsl #32
    // 0xab663c: LoadField: r0 = r2->field_f
    //     0xab663c: ldur            w0, [x2, #0xf]
    // 0xab6640: DecompressPointer r0
    //     0xab6640: add             x0, x0, HEAP, lsl #32
    // 0xab6644: cmp             w3, NULL
    // 0xab6648: b.eq            #0xab67b4
    // 0xab664c: stp             x0, x3, [SP, #0x10]
    // 0xab6650: r16 = 6
    //     0xab6650: movz            x16, #0x6
    // 0xab6654: stp             x16, x1, [SP]
    // 0xab6658: mov             x0, x3
    // 0xab665c: ClosureCall
    //     0xab665c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xab6660: ldur            x2, [x0, #0x1f]
    //     0xab6664: blr             x2
    // 0xab6668: mov             x1, x0
    // 0xab666c: stur            x1, [fp, #-0x28]
    // 0xab6670: r0 = Await()
    //     0xab6670: bl              #0x661044  ; AwaitStub
    // 0xab6674: ldur            x2, [fp, #-0x10]
    // 0xab6678: b               #0xab6778
    // 0xab667c: cmp             x0, #3
    // 0xab6680: b.le            #0xab6774
    // 0xab6684: ldur            x1, [fp, #-0x10]
    // 0xab6688: LoadField: r0 = r1->field_13
    //     0xab6688: ldur            w0, [x1, #0x13]
    // 0xab668c: DecompressPointer r0
    //     0xab668c: add             x0, x0, HEAP, lsl #32
    // 0xab6690: LoadField: r2 = r0->field_1b
    //     0xab6690: ldur            w2, [x0, #0x1b]
    // 0xab6694: DecompressPointer r2
    //     0xab6694: add             x2, x2, HEAP, lsl #32
    // 0xab6698: cmp             w2, NULL
    // 0xab669c: b.eq            #0xab676c
    // 0xab66a0: ldur            x3, [fp, #-0x18]
    // 0xab66a4: LoadField: r0 = r1->field_f
    //     0xab66a4: ldur            w0, [x1, #0xf]
    // 0xab66a8: DecompressPointer r0
    //     0xab66a8: add             x0, x0, HEAP, lsl #32
    // 0xab66ac: stp             x0, x2, [SP, #0x10]
    // 0xab66b0: ldur            x16, [fp, #-0x20]
    // 0xab66b4: r30 = 6
    //     0xab66b4: movz            lr, #0x6
    // 0xab66b8: stp             lr, x16, [SP]
    // 0xab66bc: mov             x0, x2
    // 0xab66c0: ClosureCall
    //     0xab66c0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xab66c4: ldur            x2, [x0, #0x1f]
    //     0xab66c8: blr             x2
    // 0xab66cc: mov             x1, x0
    // 0xab66d0: stur            x1, [fp, #-0x28]
    // 0xab66d4: r0 = Await()
    //     0xab66d4: bl              #0x661044  ; AwaitStub
    // 0xab66d8: ldur            x2, [fp, #-0x10]
    // 0xab66dc: LoadField: r0 = r2->field_f
    //     0xab66dc: ldur            w0, [x2, #0xf]
    // 0xab66e0: DecompressPointer r0
    //     0xab66e0: add             x0, x0, HEAP, lsl #32
    // 0xab66e4: LoadField: r1 = r0->field_f
    //     0xab66e4: ldur            w1, [x0, #0xf]
    // 0xab66e8: DecompressPointer r1
    //     0xab66e8: add             x1, x1, HEAP, lsl #32
    // 0xab66ec: cmp             w1, NULL
    // 0xab66f0: b.eq            #0xab67b8
    // 0xab66f4: LoadField: r0 = r1->field_7
    //     0xab66f4: ldur            w0, [x1, #7]
    // 0xab66f8: DecompressPointer r0
    //     0xab66f8: add             x0, x0, HEAP, lsl #32
    // 0xab66fc: ldur            x1, [fp, #-0x18]
    // 0xab6700: LoadField: r3 = r1->field_7
    //     0xab6700: ldur            w3, [x1, #7]
    // 0xab6704: DecompressPointer r3
    //     0xab6704: add             x3, x3, HEAP, lsl #32
    // 0xab6708: cmp             w0, w3
    // 0xab670c: b.eq            #0xab6778
    // 0xab6710: and             w16, w0, w3
    // 0xab6714: branchIfSmi(r16, 0xab6748)
    //     0xab6714: tbz             w16, #0, #0xab6748
    // 0xab6718: r16 = LoadClassIdInstr(r0)
    //     0xab6718: ldur            x16, [x0, #-1]
    //     0xab671c: ubfx            x16, x16, #0xc, #0x14
    // 0xab6720: cmp             x16, #0x3d
    // 0xab6724: b.ne            #0xab6748
    // 0xab6728: r16 = LoadClassIdInstr(r3)
    //     0xab6728: ldur            x16, [x3, #-1]
    //     0xab672c: ubfx            x16, x16, #0xc, #0x14
    // 0xab6730: cmp             x16, #0x3d
    // 0xab6734: b.ne            #0xab6748
    // 0xab6738: LoadField: r16 = r0->field_7
    //     0xab6738: ldur            x16, [x0, #7]
    // 0xab673c: LoadField: r17 = r3->field_7
    //     0xab673c: ldur            x17, [x3, #7]
    // 0xab6740: cmp             x16, x17
    // 0xab6744: b.eq            #0xab6778
    // 0xab6748: StoreField: r1->field_7 = r0
    //     0xab6748: stur            w0, [x1, #7]
    //     0xab674c: tbz             w0, #0, #0xab6768
    //     0xab6750: ldurb           w16, [x1, #-1]
    //     0xab6754: ldurb           w17, [x0, #-1]
    //     0xab6758: and             x16, x17, x16, lsr #2
    //     0xab675c: tst             x16, HEAP, lsr #32
    //     0xab6760: b.eq            #0xab6768
    //     0xab6764: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab6768: b               #0xab6778
    // 0xab676c: mov             x2, x1
    // 0xab6770: b               #0xab6778
    // 0xab6774: ldur            x2, [fp, #-0x10]
    // 0xab6778: ldur            x0, [fp, #-0x20]
    // 0xab677c: cmp             w0, #6
    // 0xab6780: b.eq            #0xab679c
    // 0xab6784: LoadField: r1 = r2->field_f
    //     0xab6784: ldur            w1, [x2, #0xf]
    // 0xab6788: DecompressPointer r1
    //     0xab6788: add             x1, x1, HEAP, lsl #32
    // 0xab678c: r0 = SqfliteDatabaseExecutorExt.setVersion()
    //     0xab678c: bl              #0xab67bc  ; [package:sqflite_common/sqlite_api.dart] ::SqfliteDatabaseExecutorExt.setVersion
    // 0xab6790: mov             x1, x0
    // 0xab6794: stur            x1, [fp, #-0x18]
    // 0xab6798: r0 = Await()
    //     0xab6798: bl              #0x661044  ; AwaitStub
    // 0xab679c: r0 = Null
    //     0xab679c: mov             x0, NULL
    // 0xab67a0: r0 = ReturnAsyncNotFuture()
    //     0xab67a0: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab67a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab67a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab67a8: b               #0xab6534
    // 0xab67ac: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab67ac: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xab67b0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab67b0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xab67b4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab67b4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xab67b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab67b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> onDatabaseDowngradeDoDelete(dynamic, Database, int, int) async {
    // ** addr: 0xab6878, size: 0x290
    // 0xab6878: EnterFrame
    //     0xab6878: stp             fp, lr, [SP, #-0x10]!
    //     0xab687c: mov             fp, SP
    // 0xab6880: AllocStack(0xa8)
    //     0xab6880: sub             SP, SP, #0xa8
    // 0xab6884: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1, fp-0x88 */, dynamic _ /* r2, fp-0x80 */, dynamic _ /* r3, fp-0x78 */, dynamic _ /* r4, fp-0x70 */)
    //     0xab6884: stur            NULL, [fp, #-8]
    //     0xab6888: movz            x0, #0
    //     0xab688c: add             x1, fp, w0, sxtw #2
    //     0xab6890: ldr             x1, [x1, #0x28]
    //     0xab6894: stur            x1, [fp, #-0x88]
    //     0xab6898: add             x2, fp, w0, sxtw #2
    //     0xab689c: ldr             x2, [x2, #0x20]
    //     0xab68a0: stur            x2, [fp, #-0x80]
    //     0xab68a4: add             x3, fp, w0, sxtw #2
    //     0xab68a8: ldr             x3, [x3, #0x18]
    //     0xab68ac: stur            x3, [fp, #-0x78]
    //     0xab68b0: add             x4, fp, w0, sxtw #2
    //     0xab68b4: ldr             x4, [x4, #0x10]
    //     0xab68b8: stur            x4, [fp, #-0x70]
    //     0xab68bc: ldur            w5, [x1, #0x17]
    //     0xab68c0: add             x5, x5, HEAP, lsl #32
    //     0xab68c4: stur            x5, [fp, #-0x68]
    // 0xab68c8: CheckStackOverflow
    //     0xab68c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab68cc: cmp             SP, x16
    //     0xab68d0: b.ls            #0xab6aec
    // 0xab68d4: InitAsync() -> Future<void?>
    //     0xab68d4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab68d8: bl              #0x661298  ; InitAsyncStub
    // 0xab68dc: ldur            x0, [fp, #-0x80]
    // 0xab68e0: r2 = Null
    //     0xab68e0: mov             x2, NULL
    // 0xab68e4: r1 = Null
    //     0xab68e4: mov             x1, NULL
    // 0xab68e8: r4 = LoadClassIdInstr(r0)
    //     0xab68e8: ldur            x4, [x0, #-1]
    //     0xab68ec: ubfx            x4, x4, #0xc, #0x14
    // 0xab68f0: cmp             x4, #0x1de
    // 0xab68f4: b.eq            #0xab690c
    // 0xab68f8: r8 = SqfliteDatabase
    //     0xab68f8: add             x8, PP, #0x43, lsl #12  ; [pp+0x431f8] Type: SqfliteDatabase
    //     0xab68fc: ldr             x8, [x8, #0x1f8]
    // 0xab6900: r3 = Null
    //     0xab6900: add             x3, PP, #0x43, lsl #12  ; [pp+0x43200] Null
    //     0xab6904: ldr             x3, [x3, #0x200]
    // 0xab6908: r0 = DefaultTypeTest()
    //     0xab6908: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xab690c: ldur            x1, [fp, #-0x80]
    // 0xab6910: r0 = closeDatabase()
    //     0xab6910: bl              #0xab3e70  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::closeDatabase
    // 0xab6914: mov             x1, x0
    // 0xab6918: stur            x1, [fp, #-0x90]
    // 0xab691c: r0 = Await()
    //     0xab691c: bl              #0x661044  ; AwaitStub
    // 0xab6920: ldur            x0, [fp, #-0x68]
    // 0xab6924: LoadField: r1 = r0->field_f
    //     0xab6924: ldur            w1, [x0, #0xf]
    // 0xab6928: DecompressPointer r1
    //     0xab6928: add             x1, x1, HEAP, lsl #32
    // 0xab692c: r2 = false
    //     0xab692c: add             x2, NULL, #0x30  ; false
    // 0xab6930: StoreField: r1->field_7 = r2
    //     0xab6930: stur            w2, [x1, #7]
    // 0xab6934: LoadField: r2 = r1->field_27
    //     0xab6934: ldur            w2, [x1, #0x27]
    // 0xab6938: DecompressPointer r2
    //     0xab6938: add             x2, x2, HEAP, lsl #32
    // 0xab693c: cmp             w2, NULL
    // 0xab6940: b.eq            #0xab6af4
    // 0xab6944: LoadField: r1 = r2->field_7
    //     0xab6944: ldur            w1, [x2, #7]
    // 0xab6948: DecompressPointer r1
    //     0xab6948: add             x1, x1, HEAP, lsl #32
    // 0xab694c: ldur            x3, [fp, #-0x80]
    // 0xab6950: LoadField: r2 = r3->field_b
    //     0xab6950: ldur            w2, [x3, #0xb]
    // 0xab6954: DecompressPointer r2
    //     0xab6954: add             x2, x2, HEAP, lsl #32
    // 0xab6958: r16 = Sentinel
    //     0xab6958: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab695c: cmp             w2, w16
    // 0xab6960: b.eq            #0xab6af8
    // 0xab6964: r0 = deleteDatabase()
    //     0xab6964: bl              #0xab6b08  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::deleteDatabase
    // 0xab6968: mov             x1, x0
    // 0xab696c: stur            x1, [fp, #-0x90]
    // 0xab6970: r0 = Await()
    //     0xab6970: bl              #0x661044  ; AwaitStub
    // 0xab6974: ldur            x0, [fp, #-0x68]
    // 0xab6978: LoadField: r1 = r0->field_f
    //     0xab6978: ldur            w1, [x0, #0xf]
    // 0xab697c: DecompressPointer r1
    //     0xab697c: add             x1, x1, HEAP, lsl #32
    // 0xab6980: r0 = openDatabase()
    //     0xab6980: bl              #0xab60b0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::openDatabase
    // 0xab6984: mov             x1, x0
    // 0xab6988: stur            x1, [fp, #-0x90]
    // 0xab698c: r0 = Await()
    //     0xab698c: bl              #0x661044  ; AwaitStub
    // 0xab6990: mov             x1, x0
    // 0xab6994: ldur            x3, [fp, #-0x68]
    // 0xab6998: ArrayStore: r3[0] = r0  ; List_4
    //     0xab6998: stur            w0, [x3, #0x17]
    //     0xab699c: tbz             w0, #0, #0xab69b8
    //     0xab69a0: ldurb           w16, [x3, #-1]
    //     0xab69a4: ldurb           w17, [x0, #-1]
    //     0xab69a8: and             x16, x17, x16, lsr #2
    //     0xab69ac: tst             x16, HEAP, lsr #32
    //     0xab69b0: b.eq            #0xab69b8
    //     0xab69b4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xab69b8: mov             x0, x1
    // 0xab69bc: ldur            x4, [fp, #-0x80]
    // 0xab69c0: ArrayStore: r4[0] = r0  ; List_4
    //     0xab69c0: stur            w0, [x4, #0x17]
    //     0xab69c4: tbz             w0, #0, #0xab69e0
    //     0xab69c8: ldurb           w16, [x4, #-1]
    //     0xab69cc: ldurb           w17, [x0, #-1]
    //     0xab69d0: and             x16, x17, x16, lsr #2
    //     0xab69d4: tst             x16, HEAP, lsr #32
    //     0xab69d8: b.eq            #0xab69e0
    //     0xab69dc: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xab69e0: LoadField: r0 = r3->field_f
    //     0xab69e0: ldur            w0, [x3, #0xf]
    // 0xab69e4: DecompressPointer r0
    //     0xab69e4: add             x0, x0, HEAP, lsl #32
    // 0xab69e8: mov             x1, x4
    // 0xab69ec: stur            x0, [fp, #-0x70]
    // 0xab69f0: r2 = true
    //     0xab69f0: add             x2, NULL, #0x20  ; true
    // 0xab69f4: r0 = beginTransaction()
    //     0xab69f4: bl              #0xab5560  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::beginTransaction
    // 0xab69f8: mov             x1, x0
    // 0xab69fc: stur            x1, [fp, #-0x78]
    // 0xab6a00: r0 = Await()
    //     0xab6a00: bl              #0x661044  ; AwaitStub
    // 0xab6a04: ldur            x1, [fp, #-0x70]
    // 0xab6a08: StoreField: r1->field_f = r0
    //     0xab6a08: stur            w0, [x1, #0xf]
    //     0xab6a0c: ldurb           w16, [x1, #-1]
    //     0xab6a10: ldurb           w17, [x0, #-1]
    //     0xab6a14: and             x16, x17, x16, lsr #2
    //     0xab6a18: tst             x16, HEAP, lsr #32
    //     0xab6a1c: b.eq            #0xab6a24
    //     0xab6a20: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab6a24: ldur            x1, [fp, #-0x68]
    // 0xab6a28: LoadField: r0 = r1->field_13
    //     0xab6a28: ldur            w0, [x1, #0x13]
    // 0xab6a2c: DecompressPointer r0
    //     0xab6a2c: add             x0, x0, HEAP, lsl #32
    // 0xab6a30: LoadField: r2 = r0->field_13
    //     0xab6a30: ldur            w2, [x0, #0x13]
    // 0xab6a34: DecompressPointer r2
    //     0xab6a34: add             x2, x2, HEAP, lsl #32
    // 0xab6a38: cmp             w2, NULL
    // 0xab6a3c: b.eq            #0xab6b04
    // 0xab6a40: ldur            x16, [fp, #-0x80]
    // 0xab6a44: stp             x16, x2, [SP, #8]
    // 0xab6a48: r16 = 6
    //     0xab6a48: movz            x16, #0x6
    // 0xab6a4c: str             x16, [SP]
    // 0xab6a50: mov             x0, x2
    // 0xab6a54: ClosureCall
    //     0xab6a54: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xab6a58: ldur            x2, [x0, #0x1f]
    //     0xab6a5c: blr             x2
    // 0xab6a60: mov             x1, x0
    // 0xab6a64: stur            x1, [fp, #-0x70]
    // 0xab6a68: r0 = Await()
    //     0xab6a68: bl              #0x661044  ; AwaitStub
    // 0xab6a6c: r0 = Null
    //     0xab6a6c: mov             x0, NULL
    // 0xab6a70: r0 = ReturnAsyncNotFuture()
    //     0xab6a70: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab6a74: sub             SP, fp, #0xa8
    // 0xab6a78: mov             x2, x0
    // 0xab6a7c: stur            x0, [fp, #-0x68]
    // 0xab6a80: mov             x16, x1
    // 0xab6a84: mov             x1, x0
    // 0xab6a88: mov             x0, x16
    // 0xab6a8c: stur            x0, [fp, #-0x70]
    // 0xab6a90: r0 = print()
    //     0xab6a90: bl              #0x63fe38  ; [dart:core] ::print
    // 0xab6a94: ldur            x0, [fp, #-0x38]
    // 0xab6a98: LoadField: r3 = r0->field_f
    //     0xab6a98: ldur            w3, [x0, #0xf]
    // 0xab6a9c: DecompressPointer r3
    //     0xab6a9c: add             x3, x3, HEAP, lsl #32
    // 0xab6aa0: ldur            x1, [fp, #-0x58]
    // 0xab6aa4: stur            x3, [fp, #-0x78]
    // 0xab6aa8: r2 = true
    //     0xab6aa8: add             x2, NULL, #0x20  ; true
    // 0xab6aac: r0 = beginTransaction()
    //     0xab6aac: bl              #0xab5560  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::beginTransaction
    // 0xab6ab0: mov             x1, x0
    // 0xab6ab4: stur            x1, [fp, #-0x80]
    // 0xab6ab8: r0 = Await()
    //     0xab6ab8: bl              #0x661044  ; AwaitStub
    // 0xab6abc: ldur            x1, [fp, #-0x78]
    // 0xab6ac0: StoreField: r1->field_f = r0
    //     0xab6ac0: stur            w0, [x1, #0xf]
    //     0xab6ac4: ldurb           w16, [x1, #-1]
    //     0xab6ac8: ldurb           w17, [x0, #-1]
    //     0xab6acc: and             x16, x17, x16, lsr #2
    //     0xab6ad0: tst             x16, HEAP, lsr #32
    //     0xab6ad4: b.eq            #0xab6adc
    //     0xab6ad8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab6adc: ldur            x0, [fp, #-0x68]
    // 0xab6ae0: ldur            x1, [fp, #-0x70]
    // 0xab6ae4: r0 = ReThrow()
    //     0xab6ae4: bl              #0xec048c  ; ReThrowStub
    // 0xab6ae8: brk             #0
    // 0xab6aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6af0: b               #0xab68d4
    // 0xab6af4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6af4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6af8: r9 = path
    //     0xab6af8: add             x9, PP, #0x43, lsl #12  ; [pp+0x43210] Field <_SqfliteDatabaseBase&Object&<EMAIL>>: late (offset: 0xc)
    //     0xab6afc: ldr             x9, [x9, #0x210]
    // 0xab6b00: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xab6b00: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xab6b04: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab6b04: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin(/* No info */) {
    // ** addr: 0xab6e08, size: 0xa8
    // 0xab6e08: EnterFrame
    //     0xab6e08: stp             fp, lr, [SP, #-0x10]!
    //     0xab6e0c: mov             fp, SP
    // 0xab6e10: AllocStack(0x8)
    //     0xab6e10: sub             SP, SP, #8
    // 0xab6e14: r2 = false
    //     0xab6e14: add             x2, NULL, #0x30  ; false
    // 0xab6e18: r0 = Sentinel
    //     0xab6e18: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xab6e1c: mov             x3, x1
    // 0xab6e20: stur            x1, [fp, #-8]
    // 0xab6e24: CheckStackOverflow
    //     0xab6e24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6e28: cmp             SP, x16
    //     0xab6e2c: b.ls            #0xab6ea8
    // 0xab6e30: StoreField: r3->field_7 = r2
    //     0xab6e30: stur            w2, [x3, #7]
    // 0xab6e34: StoreField: r3->field_b = r0
    //     0xab6e34: stur            w0, [x3, #0xb]
    // 0xab6e38: StoreField: r3->field_1b = r2
    //     0xab6e38: stur            w2, [x3, #0x1b]
    // 0xab6e3c: StoreField: r3->field_1f = r2
    //     0xab6e3c: stur            w2, [x3, #0x1f]
    // 0xab6e40: r1 = Null
    //     0xab6e40: mov             x1, NULL
    // 0xab6e44: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab6e44: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab6e48: r0 = Lock()
    //     0xab6e48: bl              #0xab2288  ; [package:synchronized/synchronized.dart] Lock::Lock
    // 0xab6e4c: ldur            x2, [fp, #-8]
    // 0xab6e50: StoreField: r2->field_13 = r0
    //     0xab6e50: stur            w0, [x2, #0x13]
    //     0xab6e54: ldurb           w16, [x2, #-1]
    //     0xab6e58: ldurb           w17, [x0, #-1]
    //     0xab6e5c: and             x16, x17, x16, lsr #2
    //     0xab6e60: tst             x16, HEAP, lsr #32
    //     0xab6e64: b.eq            #0xab6e6c
    //     0xab6e68: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab6e6c: r1 = Null
    //     0xab6e6c: mov             x1, NULL
    // 0xab6e70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab6e70: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab6e74: r0 = Lock()
    //     0xab6e74: bl              #0xab2288  ; [package:synchronized/synchronized.dart] Lock::Lock
    // 0xab6e78: ldur            x1, [fp, #-8]
    // 0xab6e7c: StoreField: r1->field_23 = r0
    //     0xab6e7c: stur            w0, [x1, #0x23]
    //     0xab6e80: ldurb           w16, [x1, #-1]
    //     0xab6e84: ldurb           w17, [x0, #-1]
    //     0xab6e88: and             x16, x17, x16, lsr #2
    //     0xab6e8c: tst             x16, HEAP, lsr #32
    //     0xab6e90: b.eq            #0xab6e98
    //     0xab6e94: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab6e98: r0 = Null
    //     0xab6e98: mov             x0, NULL
    // 0xab6e9c: LeaveFrame
    //     0xab6e9c: mov             SP, fp
    //     0xab6ea0: ldp             fp, lr, [SP], #0x10
    // 0xab6ea4: ret
    //     0xab6ea4: ret             
    // 0xab6ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6eac: b               #0xab6e30
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4029c, size: 0x8c
    // 0xc4029c: EnterFrame
    //     0xc4029c: stp             fp, lr, [SP, #-0x10]!
    //     0xc402a0: mov             fp, SP
    // 0xc402a4: AllocStack(0x10)
    //     0xc402a4: sub             SP, SP, #0x10
    // 0xc402a8: CheckStackOverflow
    //     0xc402a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc402ac: cmp             SP, x16
    //     0xc402b0: b.ls            #0xc40314
    // 0xc402b4: ldr             x0, [fp, #0x10]
    // 0xc402b8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc402b8: ldur            w3, [x0, #0x17]
    // 0xc402bc: DecompressPointer r3
    //     0xc402bc: add             x3, x3, HEAP, lsl #32
    // 0xc402c0: stur            x3, [fp, #-8]
    // 0xc402c4: r1 = Null
    //     0xc402c4: mov             x1, NULL
    // 0xc402c8: r2 = 6
    //     0xc402c8: movz            x2, #0x6
    // 0xc402cc: r0 = AllocateArray()
    //     0xc402cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc402d0: mov             x1, x0
    // 0xc402d4: ldur            x0, [fp, #-8]
    // 0xc402d8: StoreField: r1->field_f = r0
    //     0xc402d8: stur            w0, [x1, #0xf]
    // 0xc402dc: r16 = " "
    //     0xc402dc: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc402e0: StoreField: r1->field_13 = r16
    //     0xc402e0: stur            w16, [x1, #0x13]
    // 0xc402e4: ldr             x0, [fp, #0x10]
    // 0xc402e8: LoadField: r2 = r0->field_b
    //     0xc402e8: ldur            w2, [x0, #0xb]
    // 0xc402ec: DecompressPointer r2
    //     0xc402ec: add             x2, x2, HEAP, lsl #32
    // 0xc402f0: r16 = Sentinel
    //     0xc402f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc402f4: cmp             w2, w16
    // 0xc402f8: b.eq            #0xc4031c
    // 0xc402fc: ArrayStore: r1[0] = r2  ; List_4
    //     0xc402fc: stur            w2, [x1, #0x17]
    // 0xc40300: str             x1, [SP]
    // 0xc40304: r0 = _interpolate()
    //     0xc40304: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc40308: LeaveFrame
    //     0xc40308: mov             SP, fp
    //     0xc4030c: ldp             fp, lr, [SP], #0x10
    // 0xc40310: ret
    //     0xc40310: ret             
    // 0xc40314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc40314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc40318: b               #0xc402b4
    // 0xc4031c: r9 = path
    //     0xc4031c: add             x9, PP, #0x43, lsl #12  ; [pp+0x43210] Field <_SqfliteDatabaseBase&Object&<EMAIL>>: late (offset: 0xc)
    //     0xc40320: ldr             x9, [x9, #0x210]
    // 0xc40324: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc40324: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ txnRawInsert(/* No info */) {
    // ** addr: 0xdc2228, size: 0x94
    // 0xdc2228: EnterFrame
    //     0xdc2228: stp             fp, lr, [SP, #-0x10]!
    //     0xdc222c: mov             fp, SP
    // 0xdc2230: AllocStack(0x40)
    //     0xdc2230: sub             SP, SP, #0x40
    // 0xdc2234: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xdc2234: stur            x1, [fp, #-8]
    //     0xdc2238: stur            x2, [fp, #-0x10]
    //     0xdc223c: stur            x3, [fp, #-0x18]
    //     0xdc2240: stur            x5, [fp, #-0x20]
    // 0xdc2244: CheckStackOverflow
    //     0xdc2244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc2248: cmp             SP, x16
    //     0xdc224c: b.ls            #0xdc22b4
    // 0xdc2250: r1 = 4
    //     0xdc2250: movz            x1, #0x4
    // 0xdc2254: r0 = AllocateContext()
    //     0xdc2254: bl              #0xec126c  ; AllocateContextStub
    // 0xdc2258: mov             x1, x0
    // 0xdc225c: ldur            x0, [fp, #-8]
    // 0xdc2260: StoreField: r1->field_f = r0
    //     0xdc2260: stur            w0, [x1, #0xf]
    // 0xdc2264: ldur            x3, [fp, #-0x10]
    // 0xdc2268: StoreField: r1->field_13 = r3
    //     0xdc2268: stur            w3, [x1, #0x13]
    // 0xdc226c: ldur            x2, [fp, #-0x18]
    // 0xdc2270: ArrayStore: r1[0] = r2  ; List_4
    //     0xdc2270: stur            w2, [x1, #0x17]
    // 0xdc2274: ldur            x2, [fp, #-0x20]
    // 0xdc2278: StoreField: r1->field_1b = r2
    //     0xdc2278: stur            w2, [x1, #0x1b]
    // 0xdc227c: mov             x2, x1
    // 0xdc2280: r1 = Function '<anonymous closure>':.
    //     0xdc2280: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d5a8] AnonymousClosure: (0xdc22bc), in [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnRawInsert (0xdc2228)
    //     0xdc2284: ldr             x1, [x1, #0x5a8]
    // 0xdc2288: r0 = AllocateClosure()
    //     0xdc2288: bl              #0xec1630  ; AllocateClosureStub
    // 0xdc228c: r16 = <int>
    //     0xdc228c: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xdc2290: ldur            lr, [fp, #-8]
    // 0xdc2294: stp             lr, x16, [SP, #0x10]
    // 0xdc2298: ldur            x16, [fp, #-0x10]
    // 0xdc229c: stp             x0, x16, [SP]
    // 0xdc22a0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xdc22a0: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xdc22a4: r0 = txnSynchronized()
    //     0xdc22a4: bl              #0xab41e0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnSynchronized
    // 0xdc22a8: LeaveFrame
    //     0xdc22a8: mov             SP, fp
    //     0xdc22ac: ldp             fp, lr, [SP], #0x10
    // 0xdc22b0: ret
    //     0xdc22b0: ret             
    // 0xdc22b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc22b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc22b8: b               #0xdc2250
  }
  [closure] Future<int> <anonymous closure>(dynamic, Transaction?) async {
    // ** addr: 0xdc22bc, size: 0xb0
    // 0xdc22bc: EnterFrame
    //     0xdc22bc: stp             fp, lr, [SP, #-0x10]!
    //     0xdc22c0: mov             fp, SP
    // 0xdc22c4: AllocStack(0x38)
    //     0xdc22c4: sub             SP, SP, #0x38
    // 0xdc22c8: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin this /* r1 */)
    //     0xdc22c8: stur            NULL, [fp, #-8]
    //     0xdc22cc: movz            x0, #0
    //     0xdc22d0: add             x1, fp, w0, sxtw #2
    //     0xdc22d4: ldr             x1, [x1, #0x18]
    //     0xdc22d8: ldur            w2, [x1, #0x17]
    //     0xdc22dc: add             x2, x2, HEAP, lsl #32
    //     0xdc22e0: stur            x2, [fp, #-0x10]
    // 0xdc22e4: CheckStackOverflow
    //     0xdc22e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc22e8: cmp             SP, x16
    //     0xdc22ec: b.ls            #0xdc2364
    // 0xdc22f0: InitAsync() -> Future<int>
    //     0xdc22f0: ldr             x0, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    //     0xdc22f4: bl              #0x661298  ; InitAsyncStub
    // 0xdc22f8: ldur            x0, [fp, #-0x10]
    // 0xdc22fc: LoadField: r4 = r0->field_f
    //     0xdc22fc: ldur            w4, [x0, #0xf]
    // 0xdc2300: DecompressPointer r4
    //     0xdc2300: add             x4, x4, HEAP, lsl #32
    // 0xdc2304: stur            x4, [fp, #-0x18]
    // 0xdc2308: LoadField: r2 = r0->field_13
    //     0xdc2308: ldur            w2, [x0, #0x13]
    // 0xdc230c: DecompressPointer r2
    //     0xdc230c: add             x2, x2, HEAP, lsl #32
    // 0xdc2310: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xdc2310: ldur            w3, [x0, #0x17]
    // 0xdc2314: DecompressPointer r3
    //     0xdc2314: add             x3, x3, HEAP, lsl #32
    // 0xdc2318: LoadField: r5 = r0->field_1b
    //     0xdc2318: ldur            w5, [x0, #0x1b]
    // 0xdc231c: DecompressPointer r5
    //     0xdc231c: add             x5, x5, HEAP, lsl #32
    // 0xdc2320: mov             x1, x4
    // 0xdc2324: r0 = SqfliteDatabaseMixinExt._txnGetSqlMethodArguments()
    //     0xdc2324: bl              #0xab4c24  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt._txnGetSqlMethodArguments
    // 0xdc2328: r16 = <int?>
    //     0xdc2328: ldr             x16, [PP, #0x1d68]  ; [pp+0x1d68] TypeArguments: <int?>
    // 0xdc232c: ldur            lr, [fp, #-0x18]
    // 0xdc2330: stp             lr, x16, [SP, #0x10]
    // 0xdc2334: r16 = "insert"
    //     0xdc2334: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df0] "insert"
    //     0xdc2338: ldr             x16, [x16, #0xdf0]
    // 0xdc233c: stp             x0, x16, [SP]
    // 0xdc2340: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xdc2340: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xdc2344: r0 = safeInvokeMethod()
    //     0xdc2344: bl              #0xab484c  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::safeInvokeMethod
    // 0xdc2348: mov             x1, x0
    // 0xdc234c: stur            x1, [fp, #-0x18]
    // 0xdc2350: r0 = Await()
    //     0xdc2350: bl              #0x661044  ; AwaitStub
    // 0xdc2354: cmp             w0, NULL
    // 0xdc2358: b.ne            #0xdc2360
    // 0xdc235c: r0 = 0
    //     0xdc235c: movz            x0, #0
    // 0xdc2360: r0 = ReturnAsync()
    //     0xdc2360: b               #0x6576a4  ; ReturnAsyncStub
    // 0xdc2364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc2364: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc2368: b               #0xdc22f0
  }
}

// class id: 476, size: 0x30, field offset: 0x28
//   transformed mixin,
abstract class _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin extends _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin
     with SqfliteDatabaseWithOpenHelperMixin {
}

// class id: 477, size: 0x30, field offset: 0x30
//   transformed mixin,
abstract class _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin extends _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin
     with SqfliteDatabaseExecutorMixin {

  _ execute(/* No info */) {
    // ** addr: 0xab7a28, size: 0x64
    // 0xab7a28: EnterFrame
    //     0xab7a28: stp             fp, lr, [SP, #-0x10]!
    //     0xab7a2c: mov             fp, SP
    // 0xab7a30: AllocStack(0x38)
    //     0xab7a30: sub             SP, SP, #0x38
    // 0xab7a34: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xab7a34: mov             x0, x1
    //     0xab7a38: stur            x1, [fp, #-8]
    //     0xab7a3c: stur            x2, [fp, #-0x10]
    // 0xab7a40: CheckStackOverflow
    //     0xab7a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab7a44: cmp             SP, x16
    //     0xab7a48: b.ls            #0xab7a84
    // 0xab7a4c: mov             x1, x0
    // 0xab7a50: r0 = checkNotClosed()
    //     0xab7a50: bl              #0xab4e58  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::checkNotClosed
    // 0xab7a54: ldur            x0, [fp, #-8]
    // 0xab7a58: LoadField: r1 = r0->field_f
    //     0xab7a58: ldur            w1, [x0, #0xf]
    // 0xab7a5c: DecompressPointer r1
    //     0xab7a5c: add             x1, x1, HEAP, lsl #32
    // 0xab7a60: stp             x0, NULL, [SP, #0x18]
    // 0xab7a64: ldur            x16, [fp, #-0x10]
    // 0xab7a68: stp             x16, x1, [SP, #8]
    // 0xab7a6c: str             NULL, [SP]
    // 0xab7a70: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0xab7a70: ldr             x4, [PP, #0x3e8]  ; [pp+0x3e8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0xab7a74: r0 = txnExecute()
    //     0xab7a74: bl              #0xab52a0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnExecute
    // 0xab7a78: LeaveFrame
    //     0xab7a78: mov             SP, fp
    //     0xab7a7c: ldp             fp, lr, [SP], #0x10
    // 0xab7a80: ret
    //     0xab7a80: ret             
    // 0xab7a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab7a84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab7a88: b               #0xab7a4c
  }
  _ update(/* No info */) {
    // ** addr: 0xdc0cf0, size: 0x90
    // 0xdc0cf0: EnterFrame
    //     0xdc0cf0: stp             fp, lr, [SP, #-0x10]!
    //     0xdc0cf4: mov             fp, SP
    // 0xdc0cf8: AllocStack(0x18)
    //     0xdc0cf8: sub             SP, SP, #0x18
    // 0xdc0cfc: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2, fp-0x10 */, dynamic _ /* r6 => r3, fp-0x18 */)
    //     0xdc0cfc: mov             x0, x2
    //     0xdc0d00: mov             x2, x3
    //     0xdc0d04: stur            x3, [fp, #-0x10]
    //     0xdc0d08: mov             x3, x6
    //     0xdc0d0c: stur            x1, [fp, #-8]
    //     0xdc0d10: stur            x6, [fp, #-0x18]
    // 0xdc0d14: CheckStackOverflow
    //     0xdc0d14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc0d18: cmp             SP, x16
    //     0xdc0d1c: b.ls            #0xdc0d6c
    // 0xdc0d20: r0 = SqlBuilder()
    //     0xdc0d20: bl              #0xdc1ef0  ; AllocateSqlBuilderStub -> SqlBuilder (size=0x10)
    // 0xdc0d24: mov             x1, x0
    // 0xdc0d28: ldur            x2, [fp, #-0x10]
    // 0xdc0d2c: ldur            x3, [fp, #-0x18]
    // 0xdc0d30: stur            x0, [fp, #-0x10]
    // 0xdc0d34: r0 = SqlBuilder.update()
    //     0xdc0d34: bl              #0xdc0fcc  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::SqlBuilder.update
    // 0xdc0d38: ldur            x0, [fp, #-0x10]
    // 0xdc0d3c: LoadField: r2 = r0->field_7
    //     0xdc0d3c: ldur            w2, [x0, #7]
    // 0xdc0d40: DecompressPointer r2
    //     0xdc0d40: add             x2, x2, HEAP, lsl #32
    // 0xdc0d44: r16 = Sentinel
    //     0xdc0d44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc0d48: cmp             w2, w16
    // 0xdc0d4c: b.eq            #0xdc0d74
    // 0xdc0d50: LoadField: r3 = r0->field_b
    //     0xdc0d50: ldur            w3, [x0, #0xb]
    // 0xdc0d54: DecompressPointer r3
    //     0xdc0d54: add             x3, x3, HEAP, lsl #32
    // 0xdc0d58: ldur            x1, [fp, #-8]
    // 0xdc0d5c: r0 = rawUpdate()
    //     0xdc0d5c: bl              #0xdc0d80  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin::rawUpdate
    // 0xdc0d60: LeaveFrame
    //     0xdc0d60: mov             SP, fp
    //     0xdc0d64: ldp             fp, lr, [SP], #0x10
    // 0xdc0d68: ret
    //     0xdc0d68: ret             
    // 0xdc0d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0d6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0d70: b               #0xdc0d20
    // 0xdc0d74: r9 = sql
    //     0xdc0d74: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d1b0] Field <SqlBuilder.sql>: late (offset: 0x8)
    //     0xdc0d78: ldr             x9, [x9, #0x1b0]
    // 0xdc0d7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xdc0d7c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ rawUpdate(/* No info */) {
    // ** addr: 0xdc0d80, size: 0x48
    // 0xdc0d80: EnterFrame
    //     0xdc0d80: stp             fp, lr, [SP, #-0x10]!
    //     0xdc0d84: mov             fp, SP
    // 0xdc0d88: CheckStackOverflow
    //     0xdc0d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc0d8c: cmp             SP, x16
    //     0xdc0d90: b.ls            #0xdc0dc0
    // 0xdc0d94: r0 = LoadStaticField(0x175c)
    //     0xdc0d94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc0d98: ldr             x0, [x0, #0x2eb8]
    // 0xdc0d9c: cmp             w0, NULL
    // 0xdc0da0: b.ne            #0xdc0db0
    // 0xdc0da4: r0 = true
    //     0xdc0da4: add             x0, NULL, #0x20  ; true
    // 0xdc0da8: StoreStaticField(0x175c, r0)
    //     0xdc0da8: ldr             x4, [THR, #0x68]  ; THR::field_table_values
    //     0xdc0dac: str             x0, [x4, #0x2eb8]
    // 0xdc0db0: r0 = _rawUpdate()
    //     0xdc0db0: bl              #0xdc0dc8  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin::_rawUpdate
    // 0xdc0db4: LeaveFrame
    //     0xdc0db4: mov             SP, fp
    //     0xdc0db8: ldp             fp, lr, [SP], #0x10
    // 0xdc0dbc: ret
    //     0xdc0dbc: ret             
    // 0xdc0dc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0dc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0dc4: b               #0xdc0d94
  }
  _ _rawUpdate(/* No info */) {
    // ** addr: 0xdc0dc8, size: 0x64
    // 0xdc0dc8: EnterFrame
    //     0xdc0dc8: stp             fp, lr, [SP, #-0x10]!
    //     0xdc0dcc: mov             fp, SP
    // 0xdc0dd0: AllocStack(0x18)
    //     0xdc0dd0: sub             SP, SP, #0x18
    // 0xdc0dd4: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0xdc0dd4: mov             x0, x1
    //     0xdc0dd8: mov             x5, x3
    //     0xdc0ddc: stur            x3, [fp, #-0x18]
    //     0xdc0de0: mov             x3, x2
    //     0xdc0de4: stur            x1, [fp, #-8]
    //     0xdc0de8: stur            x2, [fp, #-0x10]
    // 0xdc0dec: CheckStackOverflow
    //     0xdc0dec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc0df0: cmp             SP, x16
    //     0xdc0df4: b.ls            #0xdc0e24
    // 0xdc0df8: mov             x1, x0
    // 0xdc0dfc: r0 = checkNotClosed()
    //     0xdc0dfc: bl              #0xab4e58  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::checkNotClosed
    // 0xdc0e00: ldur            x1, [fp, #-8]
    // 0xdc0e04: LoadField: r2 = r1->field_f
    //     0xdc0e04: ldur            w2, [x1, #0xf]
    // 0xdc0e08: DecompressPointer r2
    //     0xdc0e08: add             x2, x2, HEAP, lsl #32
    // 0xdc0e0c: ldur            x3, [fp, #-0x10]
    // 0xdc0e10: ldur            x5, [fp, #-0x18]
    // 0xdc0e14: r0 = SqfliteDatabaseMixinExt._txnRawUpdateOrDelete()
    //     0xdc0e14: bl              #0xdc0e2c  ; [package:sqflite_common/src/database_mixin.dart] ::SqfliteDatabaseMixinExt._txnRawUpdateOrDelete
    // 0xdc0e18: LeaveFrame
    //     0xdc0e18: mov             SP, fp
    //     0xdc0e1c: ldp             fp, lr, [SP], #0x10
    // 0xdc0e20: ret
    //     0xdc0e20: ret             
    // 0xdc0e24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc0e24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc0e28: b               #0xdc0df8
  }
  _ insert(/* No info */) {
    // ** addr: 0xdc2140, size: 0x84
    // 0xdc2140: EnterFrame
    //     0xdc2140: stp             fp, lr, [SP, #-0x10]!
    //     0xdc2144: mov             fp, SP
    // 0xdc2148: AllocStack(0x10)
    //     0xdc2148: sub             SP, SP, #0x10
    // 0xdc214c: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xdc214c: mov             x0, x2
    //     0xdc2150: mov             x2, x3
    //     0xdc2154: stur            x1, [fp, #-8]
    //     0xdc2158: stur            x3, [fp, #-0x10]
    // 0xdc215c: CheckStackOverflow
    //     0xdc215c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc2160: cmp             SP, x16
    //     0xdc2164: b.ls            #0xdc21b0
    // 0xdc2168: r0 = SqlBuilder()
    //     0xdc2168: bl              #0xdc1ef0  ; AllocateSqlBuilderStub -> SqlBuilder (size=0x10)
    // 0xdc216c: mov             x1, x0
    // 0xdc2170: ldur            x2, [fp, #-0x10]
    // 0xdc2174: stur            x0, [fp, #-0x10]
    // 0xdc2178: r0 = SqlBuilder.insert()
    //     0xdc2178: bl              #0xdc236c  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::SqlBuilder.insert
    // 0xdc217c: ldur            x0, [fp, #-0x10]
    // 0xdc2180: LoadField: r2 = r0->field_7
    //     0xdc2180: ldur            w2, [x0, #7]
    // 0xdc2184: DecompressPointer r2
    //     0xdc2184: add             x2, x2, HEAP, lsl #32
    // 0xdc2188: r16 = Sentinel
    //     0xdc2188: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc218c: cmp             w2, w16
    // 0xdc2190: b.eq            #0xdc21b8
    // 0xdc2194: LoadField: r3 = r0->field_b
    //     0xdc2194: ldur            w3, [x0, #0xb]
    // 0xdc2198: DecompressPointer r3
    //     0xdc2198: add             x3, x3, HEAP, lsl #32
    // 0xdc219c: ldur            x1, [fp, #-8]
    // 0xdc21a0: r0 = rawInsert()
    //     0xdc21a0: bl              #0xdc21c4  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin::rawInsert
    // 0xdc21a4: LeaveFrame
    //     0xdc21a4: mov             SP, fp
    //     0xdc21a8: ldp             fp, lr, [SP], #0x10
    // 0xdc21ac: ret
    //     0xdc21ac: ret             
    // 0xdc21b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc21b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc21b4: b               #0xdc2168
    // 0xdc21b8: r9 = sql
    //     0xdc21b8: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d1b0] Field <SqlBuilder.sql>: late (offset: 0x8)
    //     0xdc21bc: ldr             x9, [x9, #0x1b0]
    // 0xdc21c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xdc21c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ rawInsert(/* No info */) {
    // ** addr: 0xdc21c4, size: 0x64
    // 0xdc21c4: EnterFrame
    //     0xdc21c4: stp             fp, lr, [SP, #-0x10]!
    //     0xdc21c8: mov             fp, SP
    // 0xdc21cc: AllocStack(0x18)
    //     0xdc21cc: sub             SP, SP, #0x18
    // 0xdc21d0: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0xdc21d0: mov             x0, x1
    //     0xdc21d4: mov             x5, x3
    //     0xdc21d8: stur            x3, [fp, #-0x18]
    //     0xdc21dc: mov             x3, x2
    //     0xdc21e0: stur            x1, [fp, #-8]
    //     0xdc21e4: stur            x2, [fp, #-0x10]
    // 0xdc21e8: CheckStackOverflow
    //     0xdc21e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc21ec: cmp             SP, x16
    //     0xdc21f0: b.ls            #0xdc2220
    // 0xdc21f4: mov             x1, x0
    // 0xdc21f8: r0 = checkNotClosed()
    //     0xdc21f8: bl              #0xab4e58  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::checkNotClosed
    // 0xdc21fc: ldur            x1, [fp, #-8]
    // 0xdc2200: LoadField: r2 = r1->field_f
    //     0xdc2200: ldur            w2, [x1, #0xf]
    // 0xdc2204: DecompressPointer r2
    //     0xdc2204: add             x2, x2, HEAP, lsl #32
    // 0xdc2208: ldur            x3, [fp, #-0x10]
    // 0xdc220c: ldur            x5, [fp, #-0x18]
    // 0xdc2210: r0 = txnRawInsert()
    //     0xdc2210: bl              #0xdc2228  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnRawInsert
    // 0xdc2214: LeaveFrame
    //     0xdc2214: mov             SP, fp
    //     0xdc2218: ldp             fp, lr, [SP], #0x10
    // 0xdc221c: ret
    //     0xdc221c: ret             
    // 0xdc2220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc2220: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc2224: b               #0xdc21f4
  }
  _ query(/* No info */) {
    // ** addr: 0xdc2e10, size: 0x190
    // 0xdc2e10: EnterFrame
    //     0xdc2e10: stp             fp, lr, [SP, #-0x10]!
    //     0xdc2e14: mov             fp, SP
    // 0xdc2e18: AllocStack(0x30)
    //     0xdc2e18: sub             SP, SP, #0x30
    // 0xdc2e1c: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r1, fp-0x20 */, dynamic _ /* r3 => r6, fp-0x28 */, dynamic _ /* r5 => r7, fp-0x30 */, {dynamic limit = Null /* r3, fp-0x18 */, dynamic offset = Null /* r5, fp-0x10 */, dynamic orderBy = Null /* r0, fp-0x8 */})
    //     0xdc2e1c: mov             x6, x3
    //     0xdc2e20: mov             x7, x5
    //     0xdc2e24: stur            x1, [fp, #-0x20]
    //     0xdc2e28: stur            x3, [fp, #-0x28]
    //     0xdc2e2c: stur            x5, [fp, #-0x30]
    //     0xdc2e30: ldur            w0, [x4, #0x13]
    //     0xdc2e34: ldur            w2, [x4, #0x1f]
    //     0xdc2e38: add             x2, x2, HEAP, lsl #32
    //     0xdc2e3c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0xdc2e40: ldr             x16, [x16, #0x50]
    //     0xdc2e44: cmp             w2, w16
    //     0xdc2e48: b.ne            #0xdc2e6c
    //     0xdc2e4c: ldur            w2, [x4, #0x23]
    //     0xdc2e50: add             x2, x2, HEAP, lsl #32
    //     0xdc2e54: sub             w3, w0, w2
    //     0xdc2e58: add             x2, fp, w3, sxtw #2
    //     0xdc2e5c: ldr             x2, [x2, #8]
    //     0xdc2e60: mov             x3, x2
    //     0xdc2e64: movz            x2, #0x1
    //     0xdc2e68: b               #0xdc2e74
    //     0xdc2e6c: mov             x3, NULL
    //     0xdc2e70: movz            x2, #0
    //     0xdc2e74: stur            x3, [fp, #-0x18]
    //     0xdc2e78: lsl             x5, x2, #1
    //     0xdc2e7c: lsl             w8, w5, #1
    //     0xdc2e80: add             w9, w8, #8
    //     0xdc2e84: add             x16, x4, w9, sxtw #1
    //     0xdc2e88: ldur            w10, [x16, #0xf]
    //     0xdc2e8c: add             x10, x10, HEAP, lsl #32
    //     0xdc2e90: add             x16, PP, #0x26, lsl #12  ; [pp+0x263b0] "offset"
    //     0xdc2e94: ldr             x16, [x16, #0x3b0]
    //     0xdc2e98: cmp             w10, w16
    //     0xdc2e9c: b.ne            #0xdc2ed0
    //     0xdc2ea0: add             w2, w8, #0xa
    //     0xdc2ea4: add             x16, x4, w2, sxtw #1
    //     0xdc2ea8: ldur            w8, [x16, #0xf]
    //     0xdc2eac: add             x8, x8, HEAP, lsl #32
    //     0xdc2eb0: sub             w2, w0, w8
    //     0xdc2eb4: add             x8, fp, w2, sxtw #2
    //     0xdc2eb8: ldr             x8, [x8, #8]
    //     0xdc2ebc: add             w2, w5, #2
    //     0xdc2ec0: sbfx            x5, x2, #1, #0x1f
    //     0xdc2ec4: mov             x2, x5
    //     0xdc2ec8: mov             x5, x8
    //     0xdc2ecc: b               #0xdc2ed4
    //     0xdc2ed0: mov             x5, NULL
    //     0xdc2ed4: stur            x5, [fp, #-0x10]
    //     0xdc2ed8: lsl             x8, x2, #1
    //     0xdc2edc: lsl             w2, w8, #1
    //     0xdc2ee0: add             w8, w2, #8
    //     0xdc2ee4: add             x16, x4, w8, sxtw #1
    //     0xdc2ee8: ldur            w9, [x16, #0xf]
    //     0xdc2eec: add             x9, x9, HEAP, lsl #32
    //     0xdc2ef0: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d1a8] "orderBy"
    //     0xdc2ef4: ldr             x16, [x16, #0x1a8]
    //     0xdc2ef8: cmp             w9, w16
    //     0xdc2efc: b.ne            #0xdc2f20
    //     0xdc2f00: add             w8, w2, #0xa
    //     0xdc2f04: add             x16, x4, w8, sxtw #1
    //     0xdc2f08: ldur            w2, [x16, #0xf]
    //     0xdc2f0c: add             x2, x2, HEAP, lsl #32
    //     0xdc2f10: sub             w4, w0, w2
    //     0xdc2f14: add             x0, fp, w4, sxtw #2
    //     0xdc2f18: ldr             x0, [x0, #8]
    //     0xdc2f1c: b               #0xdc2f24
    //     0xdc2f20: mov             x0, NULL
    //     0xdc2f24: stur            x0, [fp, #-8]
    // 0xdc2f28: CheckStackOverflow
    //     0xdc2f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc2f2c: cmp             SP, x16
    //     0xdc2f30: b.ls            #0xdc2f8c
    // 0xdc2f34: r0 = SqlBuilder()
    //     0xdc2f34: bl              #0xdc1ef0  ; AllocateSqlBuilderStub -> SqlBuilder (size=0x10)
    // 0xdc2f38: mov             x1, x0
    // 0xdc2f3c: ldur            x2, [fp, #-0x18]
    // 0xdc2f40: ldur            x3, [fp, #-0x10]
    // 0xdc2f44: ldur            x5, [fp, #-8]
    // 0xdc2f48: ldur            x6, [fp, #-0x28]
    // 0xdc2f4c: ldur            x7, [fp, #-0x30]
    // 0xdc2f50: stur            x0, [fp, #-8]
    // 0xdc2f54: r0 = SqlBuilder.query()
    //     0xdc2f54: bl              #0xdc304c  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::SqlBuilder.query
    // 0xdc2f58: ldur            x0, [fp, #-8]
    // 0xdc2f5c: LoadField: r2 = r0->field_7
    //     0xdc2f5c: ldur            w2, [x0, #7]
    // 0xdc2f60: DecompressPointer r2
    //     0xdc2f60: add             x2, x2, HEAP, lsl #32
    // 0xdc2f64: r16 = Sentinel
    //     0xdc2f64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc2f68: cmp             w2, w16
    // 0xdc2f6c: b.eq            #0xdc2f94
    // 0xdc2f70: LoadField: r3 = r0->field_b
    //     0xdc2f70: ldur            w3, [x0, #0xb]
    // 0xdc2f74: DecompressPointer r3
    //     0xdc2f74: add             x3, x3, HEAP, lsl #32
    // 0xdc2f78: ldur            x1, [fp, #-0x20]
    // 0xdc2f7c: r0 = rawQuery()
    //     0xdc2f7c: bl              #0xdc2fa0  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin::rawQuery
    // 0xdc2f80: LeaveFrame
    //     0xdc2f80: mov             SP, fp
    //     0xdc2f84: ldp             fp, lr, [SP], #0x10
    // 0xdc2f88: ret
    //     0xdc2f88: ret             
    // 0xdc2f8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc2f8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc2f90: b               #0xdc2f34
    // 0xdc2f94: r9 = sql
    //     0xdc2f94: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d1b0] Field <SqlBuilder.sql>: late (offset: 0x8)
    //     0xdc2f98: ldr             x9, [x9, #0x1b0]
    // 0xdc2f9c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xdc2f9c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ rawQuery(/* No info */) {
    // ** addr: 0xdc2fa0, size: 0x48
    // 0xdc2fa0: EnterFrame
    //     0xdc2fa0: stp             fp, lr, [SP, #-0x10]!
    //     0xdc2fa4: mov             fp, SP
    // 0xdc2fa8: CheckStackOverflow
    //     0xdc2fa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc2fac: cmp             SP, x16
    //     0xdc2fb0: b.ls            #0xdc2fe0
    // 0xdc2fb4: r0 = LoadStaticField(0x175c)
    //     0xdc2fb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc2fb8: ldr             x0, [x0, #0x2eb8]
    // 0xdc2fbc: cmp             w0, NULL
    // 0xdc2fc0: b.ne            #0xdc2fd0
    // 0xdc2fc4: r0 = true
    //     0xdc2fc4: add             x0, NULL, #0x20  ; true
    // 0xdc2fc8: StoreStaticField(0x175c, r0)
    //     0xdc2fc8: ldr             x4, [THR, #0x68]  ; THR::field_table_values
    //     0xdc2fcc: str             x0, [x4, #0x2eb8]
    // 0xdc2fd0: r0 = _rawQuery()
    //     0xdc2fd0: bl              #0xdc2fe8  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin::_rawQuery
    // 0xdc2fd4: LeaveFrame
    //     0xdc2fd4: mov             SP, fp
    //     0xdc2fd8: ldp             fp, lr, [SP], #0x10
    // 0xdc2fdc: ret
    //     0xdc2fdc: ret             
    // 0xdc2fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc2fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc2fe4: b               #0xdc2fb4
  }
  _ _rawQuery(/* No info */) {
    // ** addr: 0xdc2fe8, size: 0x64
    // 0xdc2fe8: EnterFrame
    //     0xdc2fe8: stp             fp, lr, [SP, #-0x10]!
    //     0xdc2fec: mov             fp, SP
    // 0xdc2ff0: AllocStack(0x18)
    //     0xdc2ff0: sub             SP, SP, #0x18
    // 0xdc2ff4: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0xdc2ff4: mov             x0, x1
    //     0xdc2ff8: mov             x5, x3
    //     0xdc2ffc: stur            x3, [fp, #-0x18]
    //     0xdc3000: mov             x3, x2
    //     0xdc3004: stur            x1, [fp, #-8]
    //     0xdc3008: stur            x2, [fp, #-0x10]
    // 0xdc300c: CheckStackOverflow
    //     0xdc300c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc3010: cmp             SP, x16
    //     0xdc3014: b.ls            #0xdc3044
    // 0xdc3018: mov             x1, x0
    // 0xdc301c: r0 = checkNotClosed()
    //     0xdc301c: bl              #0xab4e58  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::checkNotClosed
    // 0xdc3020: ldur            x1, [fp, #-8]
    // 0xdc3024: LoadField: r2 = r1->field_f
    //     0xdc3024: ldur            w2, [x1, #0xf]
    // 0xdc3028: DecompressPointer r2
    //     0xdc3028: add             x2, x2, HEAP, lsl #32
    // 0xdc302c: ldur            x3, [fp, #-0x10]
    // 0xdc3030: ldur            x5, [fp, #-0x18]
    // 0xdc3034: r0 = txnRawQuery()
    //     0xdc3034: bl              #0xab59dc  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::txnRawQuery
    // 0xdc3038: LeaveFrame
    //     0xdc3038: mov             SP, fp
    //     0xdc303c: ldp             fp, lr, [SP], #0x10
    // 0xdc3040: ret
    //     0xdc3040: ret             
    // 0xdc3044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc3044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc3048: b               #0xdc3018
  }
  _ delete(/* No info */) {
    // ** addr: 0xdc32d8, size: 0xc8
    // 0xdc32d8: EnterFrame
    //     0xdc32d8: stp             fp, lr, [SP, #-0x10]!
    //     0xdc32dc: mov             fp, SP
    // 0xdc32e0: AllocStack(0x18)
    //     0xdc32e0: sub             SP, SP, #0x18
    // 0xdc32e4: SetupParameters(_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin this /* r1 => r1, fp-0x10 */, dynamic _, dynamic _ /* r3 => r2, fp-0x18 */, {dynamic whereArgs = Null /* r3, fp-0x8 */})
    //     0xdc32e4: mov             x0, x2
    //     0xdc32e8: mov             x2, x3
    //     0xdc32ec: stur            x1, [fp, #-0x10]
    //     0xdc32f0: stur            x3, [fp, #-0x18]
    //     0xdc32f4: ldur            w0, [x4, #0x13]
    //     0xdc32f8: ldur            w3, [x4, #0x1f]
    //     0xdc32fc: add             x3, x3, HEAP, lsl #32
    //     0xdc3300: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d540] "whereArgs"
    //     0xdc3304: ldr             x16, [x16, #0x540]
    //     0xdc3308: cmp             w3, w16
    //     0xdc330c: b.ne            #0xdc332c
    //     0xdc3310: ldur            w3, [x4, #0x23]
    //     0xdc3314: add             x3, x3, HEAP, lsl #32
    //     0xdc3318: sub             w4, w0, w3
    //     0xdc331c: add             x0, fp, w4, sxtw #2
    //     0xdc3320: ldr             x0, [x0, #8]
    //     0xdc3324: mov             x3, x0
    //     0xdc3328: b               #0xdc3330
    //     0xdc332c: mov             x3, NULL
    //     0xdc3330: stur            x3, [fp, #-8]
    // 0xdc3334: CheckStackOverflow
    //     0xdc3334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc3338: cmp             SP, x16
    //     0xdc333c: b.ls            #0xdc338c
    // 0xdc3340: r0 = SqlBuilder()
    //     0xdc3340: bl              #0xdc1ef0  ; AllocateSqlBuilderStub -> SqlBuilder (size=0x10)
    // 0xdc3344: mov             x1, x0
    // 0xdc3348: ldur            x2, [fp, #-0x18]
    // 0xdc334c: ldur            x3, [fp, #-8]
    // 0xdc3350: stur            x0, [fp, #-8]
    // 0xdc3354: r0 = SqlBuilder.delete()
    //     0xdc3354: bl              #0xdc33a0  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::SqlBuilder.delete
    // 0xdc3358: ldur            x0, [fp, #-8]
    // 0xdc335c: LoadField: r2 = r0->field_7
    //     0xdc335c: ldur            w2, [x0, #7]
    // 0xdc3360: DecompressPointer r2
    //     0xdc3360: add             x2, x2, HEAP, lsl #32
    // 0xdc3364: r16 = Sentinel
    //     0xdc3364: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc3368: cmp             w2, w16
    // 0xdc336c: b.eq            #0xdc3394
    // 0xdc3370: LoadField: r3 = r0->field_b
    //     0xdc3370: ldur            w3, [x0, #0xb]
    // 0xdc3374: DecompressPointer r3
    //     0xdc3374: add             x3, x3, HEAP, lsl #32
    // 0xdc3378: ldur            x1, [fp, #-0x10]
    // 0xdc337c: r0 = _rawUpdate()
    //     0xdc337c: bl              #0xdc0dc8  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin::_rawUpdate
    // 0xdc3380: LeaveFrame
    //     0xdc3380: mov             SP, fp
    //     0xdc3384: ldp             fp, lr, [SP], #0x10
    // 0xdc3388: ret
    //     0xdc3388: ret             
    // 0xdc338c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc338c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc3390: b               #0xdc3340
    // 0xdc3394: r9 = sql
    //     0xdc3394: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d1b0] Field <SqlBuilder.sql>: late (offset: 0x8)
    //     0xdc3398: ldr             x9, [x9, #0x1b0]
    // 0xdc339c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xdc339c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 478, size: 0x30, field offset: 0x30
class SqfliteDatabaseBase extends _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin&SqfliteDatabaseWithOpenHelperMixin&SqfliteDatabaseExecutorMixin {
}
