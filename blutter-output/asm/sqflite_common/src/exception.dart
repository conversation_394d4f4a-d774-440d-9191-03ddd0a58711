// lib: , url: package:sqflite_common/src/exception.dart

// class id: 1051154, size: 0x8
class :: {
}

// class id: 469, size: 0xc, field offset: 0x8
abstract class SqfliteTransactionRollbackSuccess<X0> extends Object {
}

// class id: 470, size: 0xc, field offset: 0x8
abstract class DatabaseException extends Object
    implements Exception {

  _ isDuplicateColumnError(/* No info */) {
    // ** addr: 0xab79b4, size: 0x74
    // 0xab79b4: EnterFrame
    //     0xab79b4: stp             fp, lr, [SP, #-0x10]!
    //     0xab79b8: mov             fp, SP
    // 0xab79bc: AllocStack(0x18)
    //     0xab79bc: sub             SP, SP, #0x18
    // 0xab79c0: SetupParameters(DatabaseException this /* r1 => r1, fp-0x8 */)
    //     0xab79c0: stur            x1, [fp, #-8]
    // 0xab79c4: CheckStackOverflow
    //     0xab79c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab79c8: cmp             SP, x16
    //     0xab79cc: b.ls            #0xab7a20
    // 0xab79d0: r16 = "duplicate column name: "
    //     0xab79d0: add             x16, PP, #0x43, lsl #12  ; [pp+0x43008] "duplicate column name: "
    //     0xab79d4: ldr             x16, [x16, #8]
    // 0xab79d8: stp             x2, x16, [SP]
    // 0xab79dc: r0 = +()
    //     0xab79dc: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xab79e0: mov             x1, x0
    // 0xab79e4: ldur            x0, [fp, #-8]
    // 0xab79e8: LoadField: r2 = r0->field_7
    //     0xab79e8: ldur            w2, [x0, #7]
    // 0xab79ec: DecompressPointer r2
    //     0xab79ec: add             x2, x2, HEAP, lsl #32
    // 0xab79f0: r0 = LoadClassIdInstr(r2)
    //     0xab79f0: ldur            x0, [x2, #-1]
    //     0xab79f4: ubfx            x0, x0, #0xc, #0x14
    // 0xab79f8: mov             x16, x1
    // 0xab79fc: mov             x1, x2
    // 0xab7a00: mov             x2, x16
    // 0xab7a04: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab7a04: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab7a08: r0 = GDT[cid_x0 + -0xffc]()
    //     0xab7a08: sub             lr, x0, #0xffc
    //     0xab7a0c: ldr             lr, [x21, lr, lsl #3]
    //     0xab7a10: blr             lr
    // 0xab7a14: LeaveFrame
    //     0xab7a14: mov             SP, fp
    //     0xab7a18: ldp             fp, lr, [SP], #0x10
    // 0xab7a1c: ret
    //     0xab7a1c: ret             
    // 0xab7a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab7a20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab7a24: b               #0xab79d0
  }
  _ toString(/* No info */) {
    // ** addr: 0xc40eb8, size: 0x64
    // 0xc40eb8: EnterFrame
    //     0xc40eb8: stp             fp, lr, [SP, #-0x10]!
    //     0xc40ebc: mov             fp, SP
    // 0xc40ec0: AllocStack(0x8)
    //     0xc40ec0: sub             SP, SP, #8
    // 0xc40ec4: CheckStackOverflow
    //     0xc40ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc40ec8: cmp             SP, x16
    //     0xc40ecc: b.ls            #0xc40f14
    // 0xc40ed0: r1 = Null
    //     0xc40ed0: mov             x1, NULL
    // 0xc40ed4: r2 = 6
    //     0xc40ed4: movz            x2, #0x6
    // 0xc40ed8: r0 = AllocateArray()
    //     0xc40ed8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc40edc: r16 = "DatabaseException("
    //     0xc40edc: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cf08] "DatabaseException("
    //     0xc40ee0: ldr             x16, [x16, #0xf08]
    // 0xc40ee4: StoreField: r0->field_f = r16
    //     0xc40ee4: stur            w16, [x0, #0xf]
    // 0xc40ee8: ldr             x1, [fp, #0x10]
    // 0xc40eec: LoadField: r2 = r1->field_7
    //     0xc40eec: ldur            w2, [x1, #7]
    // 0xc40ef0: DecompressPointer r2
    //     0xc40ef0: add             x2, x2, HEAP, lsl #32
    // 0xc40ef4: StoreField: r0->field_13 = r2
    //     0xc40ef4: stur            w2, [x0, #0x13]
    // 0xc40ef8: r16 = ")"
    //     0xc40ef8: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc40efc: ArrayStore: r0[0] = r16  ; List_4
    //     0xc40efc: stur            w16, [x0, #0x17]
    // 0xc40f00: str             x0, [SP]
    // 0xc40f04: r0 = _interpolate()
    //     0xc40f04: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc40f08: LeaveFrame
    //     0xc40f08: mov             SP, fp
    //     0xc40f0c: ldp             fp, lr, [SP], #0x10
    // 0xc40f10: ret
    //     0xc40f10: ret             
    // 0xc40f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc40f14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc40f18: b               #0xc40ed0
  }
}

// class id: 471, size: 0x14, field offset: 0xc
class SqfliteDatabaseException extends DatabaseException {

  _ toString(/* No info */) {
    // ** addr: 0xc40a9c, size: 0x41c
    // 0xc40a9c: EnterFrame
    //     0xc40a9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc40aa0: mov             fp, SP
    // 0xc40aa4: AllocStack(0x20)
    //     0xc40aa4: sub             SP, SP, #0x20
    // 0xc40aa8: CheckStackOverflow
    //     0xc40aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc40aac: cmp             SP, x16
    //     0xc40ab0: b.ls            #0xc40eb0
    // 0xc40ab4: ldr             x3, [fp, #0x10]
    // 0xc40ab8: LoadField: r4 = r3->field_b
    //     0xc40ab8: ldur            w4, [x3, #0xb]
    // 0xc40abc: DecompressPointer r4
    //     0xc40abc: add             x4, x4, HEAP, lsl #32
    // 0xc40ac0: mov             x0, x4
    // 0xc40ac4: stur            x4, [fp, #-8]
    // 0xc40ac8: r2 = Null
    //     0xc40ac8: mov             x2, NULL
    // 0xc40acc: r1 = Null
    //     0xc40acc: mov             x1, NULL
    // 0xc40ad0: cmp             w0, NULL
    // 0xc40ad4: b.eq            #0xc40b6c
    // 0xc40ad8: branchIfSmi(r0, 0xc40b6c)
    //     0xc40ad8: tbz             w0, #0, #0xc40b6c
    // 0xc40adc: r3 = LoadClassIdInstr(r0)
    //     0xc40adc: ldur            x3, [x0, #-1]
    //     0xc40ae0: ubfx            x3, x3, #0xc, #0x14
    // 0xc40ae4: r17 = 6717
    //     0xc40ae4: movz            x17, #0x1a3d
    // 0xc40ae8: cmp             x3, x17
    // 0xc40aec: b.eq            #0xc40b74
    // 0xc40af0: r4 = LoadClassIdInstr(r0)
    //     0xc40af0: ldur            x4, [x0, #-1]
    //     0xc40af4: ubfx            x4, x4, #0xc, #0x14
    // 0xc40af8: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xc40afc: ldr             x3, [x3, #0x18]
    // 0xc40b00: ldr             x3, [x3, x4, lsl #3]
    // 0xc40b04: LoadField: r3 = r3->field_2b
    //     0xc40b04: ldur            w3, [x3, #0x2b]
    // 0xc40b08: DecompressPointer r3
    //     0xc40b08: add             x3, x3, HEAP, lsl #32
    // 0xc40b0c: cmp             w3, NULL
    // 0xc40b10: b.eq            #0xc40b6c
    // 0xc40b14: LoadField: r3 = r3->field_f
    //     0xc40b14: ldur            w3, [x3, #0xf]
    // 0xc40b18: lsr             x3, x3, #3
    // 0xc40b1c: r17 = 6717
    //     0xc40b1c: movz            x17, #0x1a3d
    // 0xc40b20: cmp             x3, x17
    // 0xc40b24: b.eq            #0xc40b74
    // 0xc40b28: r3 = SubtypeTestCache
    //     0xc40b28: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4cec8] SubtypeTestCache
    //     0xc40b2c: ldr             x3, [x3, #0xec8]
    // 0xc40b30: r30 = Subtype1TestCacheStub
    //     0xc40b30: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xc40b34: LoadField: r30 = r30->field_7
    //     0xc40b34: ldur            lr, [lr, #7]
    // 0xc40b38: blr             lr
    // 0xc40b3c: cmp             w7, NULL
    // 0xc40b40: b.eq            #0xc40b4c
    // 0xc40b44: tbnz            w7, #4, #0xc40b6c
    // 0xc40b48: b               #0xc40b74
    // 0xc40b4c: r8 = Map
    //     0xc40b4c: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4ced0] Type: Map
    //     0xc40b50: ldr             x8, [x8, #0xed0]
    // 0xc40b54: r3 = SubtypeTestCache
    //     0xc40b54: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4ced8] SubtypeTestCache
    //     0xc40b58: ldr             x3, [x3, #0xed8]
    // 0xc40b5c: r30 = InstanceOfStub
    //     0xc40b5c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xc40b60: LoadField: r30 = r30->field_7
    //     0xc40b60: ldur            lr, [lr, #7]
    // 0xc40b64: blr             lr
    // 0xc40b68: b               #0xc40b78
    // 0xc40b6c: r0 = false
    //     0xc40b6c: add             x0, NULL, #0x30  ; false
    // 0xc40b70: b               #0xc40b78
    // 0xc40b74: r0 = true
    //     0xc40b74: add             x0, NULL, #0x20  ; true
    // 0xc40b78: tbnz            w0, #4, #0xc40e98
    // 0xc40b7c: ldur            x1, [fp, #-8]
    // 0xc40b80: r0 = LoadClassIdInstr(r1)
    //     0xc40b80: ldur            x0, [x1, #-1]
    //     0xc40b84: ubfx            x0, x0, #0xc, #0x14
    // 0xc40b88: r2 = "sql"
    //     0xc40b88: add             x2, PP, #0x43, lsl #12  ; [pp+0x43070] "sql"
    //     0xc40b8c: ldr             x2, [x2, #0x70]
    // 0xc40b90: r0 = GDT[cid_x0 + -0x114]()
    //     0xc40b90: sub             lr, x0, #0x114
    //     0xc40b94: ldr             lr, [x21, lr, lsl #3]
    //     0xc40b98: blr             lr
    // 0xc40b9c: cmp             w0, NULL
    // 0xc40ba0: b.eq            #0xc40e90
    // 0xc40ba4: ldr             x3, [fp, #0x10]
    // 0xc40ba8: LoadField: r4 = r3->field_b
    //     0xc40ba8: ldur            w4, [x3, #0xb]
    // 0xc40bac: DecompressPointer r4
    //     0xc40bac: add             x4, x4, HEAP, lsl #32
    // 0xc40bb0: mov             x0, x4
    // 0xc40bb4: stur            x4, [fp, #-8]
    // 0xc40bb8: r2 = Null
    //     0xc40bb8: mov             x2, NULL
    // 0xc40bbc: r1 = Null
    //     0xc40bbc: mov             x1, NULL
    // 0xc40bc0: r8 = Map
    //     0xc40bc0: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xc40bc4: r3 = Null
    //     0xc40bc4: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4cee0] Null
    //     0xc40bc8: ldr             x3, [x3, #0xee0]
    // 0xc40bcc: r0 = Map()
    //     0xc40bcc: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xc40bd0: ldur            x1, [fp, #-8]
    // 0xc40bd4: r0 = LoadClassIdInstr(r1)
    //     0xc40bd4: ldur            x0, [x1, #-1]
    //     0xc40bd8: ubfx            x0, x0, #0xc, #0x14
    // 0xc40bdc: r2 = "arguments"
    //     0xc40bdc: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a480] "arguments"
    //     0xc40be0: ldr             x2, [x2, #0x480]
    // 0xc40be4: r0 = GDT[cid_x0 + -0x114]()
    //     0xc40be4: sub             lr, x0, #0x114
    //     0xc40be8: ldr             lr, [x21, lr, lsl #3]
    //     0xc40bec: blr             lr
    // 0xc40bf0: mov             x3, x0
    // 0xc40bf4: r2 = Null
    //     0xc40bf4: mov             x2, NULL
    // 0xc40bf8: r1 = Null
    //     0xc40bf8: mov             x1, NULL
    // 0xc40bfc: stur            x3, [fp, #-8]
    // 0xc40c00: cmp             w0, NULL
    // 0xc40c04: b.eq            #0xc40ca8
    // 0xc40c08: branchIfSmi(r0, 0xc40ca8)
    //     0xc40c08: tbz             w0, #0, #0xc40ca8
    // 0xc40c0c: r3 = LoadClassIdInstr(r0)
    //     0xc40c0c: ldur            x3, [x0, #-1]
    //     0xc40c10: ubfx            x3, x3, #0xc, #0x14
    // 0xc40c14: r17 = 6718
    //     0xc40c14: movz            x17, #0x1a3e
    // 0xc40c18: cmp             x3, x17
    // 0xc40c1c: b.eq            #0xc40cb0
    // 0xc40c20: sub             x3, x3, #0x5a
    // 0xc40c24: cmp             x3, #2
    // 0xc40c28: b.ls            #0xc40cb0
    // 0xc40c2c: r4 = LoadClassIdInstr(r0)
    //     0xc40c2c: ldur            x4, [x0, #-1]
    //     0xc40c30: ubfx            x4, x4, #0xc, #0x14
    // 0xc40c34: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xc40c38: ldr             x3, [x3, #0x18]
    // 0xc40c3c: ldr             x3, [x3, x4, lsl #3]
    // 0xc40c40: LoadField: r3 = r3->field_2b
    //     0xc40c40: ldur            w3, [x3, #0x2b]
    // 0xc40c44: DecompressPointer r3
    //     0xc40c44: add             x3, x3, HEAP, lsl #32
    // 0xc40c48: cmp             w3, NULL
    // 0xc40c4c: b.eq            #0xc40ca8
    // 0xc40c50: LoadField: r3 = r3->field_f
    //     0xc40c50: ldur            w3, [x3, #0xf]
    // 0xc40c54: lsr             x3, x3, #3
    // 0xc40c58: r17 = 6718
    //     0xc40c58: movz            x17, #0x1a3e
    // 0xc40c5c: cmp             x3, x17
    // 0xc40c60: b.eq            #0xc40cb0
    // 0xc40c64: r3 = SubtypeTestCache
    //     0xc40c64: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4cef0] SubtypeTestCache
    //     0xc40c68: ldr             x3, [x3, #0xef0]
    // 0xc40c6c: r30 = Subtype1TestCacheStub
    //     0xc40c6c: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xc40c70: LoadField: r30 = r30->field_7
    //     0xc40c70: ldur            lr, [lr, #7]
    // 0xc40c74: blr             lr
    // 0xc40c78: cmp             w7, NULL
    // 0xc40c7c: b.eq            #0xc40c88
    // 0xc40c80: tbnz            w7, #4, #0xc40ca8
    // 0xc40c84: b               #0xc40cb0
    // 0xc40c88: r8 = List
    //     0xc40c88: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4cef8] Type: List
    //     0xc40c8c: ldr             x8, [x8, #0xef8]
    // 0xc40c90: r3 = SubtypeTestCache
    //     0xc40c90: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4cf00] SubtypeTestCache
    //     0xc40c94: ldr             x3, [x3, #0xf00]
    // 0xc40c98: r30 = InstanceOfStub
    //     0xc40c98: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xc40c9c: LoadField: r30 = r30->field_7
    //     0xc40c9c: ldur            lr, [lr, #7]
    // 0xc40ca0: blr             lr
    // 0xc40ca4: b               #0xc40cb4
    // 0xc40ca8: r0 = false
    //     0xc40ca8: add             x0, NULL, #0x30  ; false
    // 0xc40cac: b               #0xc40cb4
    // 0xc40cb0: r0 = true
    //     0xc40cb0: add             x0, NULL, #0x20  ; true
    // 0xc40cb4: tbnz            w0, #4, #0xc40dc0
    // 0xc40cb8: ldr             x0, [fp, #0x10]
    // 0xc40cbc: r1 = Null
    //     0xc40cbc: mov             x1, NULL
    // 0xc40cc0: r2 = 12
    //     0xc40cc0: movz            x2, #0xc
    // 0xc40cc4: r0 = AllocateArray()
    //     0xc40cc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc40cc8: mov             x3, x0
    // 0xc40ccc: stur            x3, [fp, #-0x18]
    // 0xc40cd0: r16 = "DatabaseException("
    //     0xc40cd0: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cf08] "DatabaseException("
    //     0xc40cd4: ldr             x16, [x16, #0xf08]
    // 0xc40cd8: StoreField: r3->field_f = r16
    //     0xc40cd8: stur            w16, [x3, #0xf]
    // 0xc40cdc: ldr             x0, [fp, #0x10]
    // 0xc40ce0: LoadField: r1 = r0->field_7
    //     0xc40ce0: ldur            w1, [x0, #7]
    // 0xc40ce4: DecompressPointer r1
    //     0xc40ce4: add             x1, x1, HEAP, lsl #32
    // 0xc40ce8: StoreField: r3->field_13 = r1
    //     0xc40ce8: stur            w1, [x3, #0x13]
    // 0xc40cec: r16 = ") sql \'"
    //     0xc40cec: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cf10] ") sql \'"
    //     0xc40cf0: ldr             x16, [x16, #0xf10]
    // 0xc40cf4: ArrayStore: r3[0] = r16  ; List_4
    //     0xc40cf4: stur            w16, [x3, #0x17]
    // 0xc40cf8: LoadField: r4 = r0->field_b
    //     0xc40cf8: ldur            w4, [x0, #0xb]
    // 0xc40cfc: DecompressPointer r4
    //     0xc40cfc: add             x4, x4, HEAP, lsl #32
    // 0xc40d00: mov             x0, x4
    // 0xc40d04: stur            x4, [fp, #-0x10]
    // 0xc40d08: r2 = Null
    //     0xc40d08: mov             x2, NULL
    // 0xc40d0c: r1 = Null
    //     0xc40d0c: mov             x1, NULL
    // 0xc40d10: r8 = Map
    //     0xc40d10: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xc40d14: r3 = Null
    //     0xc40d14: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4cf18] Null
    //     0xc40d18: ldr             x3, [x3, #0xf18]
    // 0xc40d1c: r0 = Map()
    //     0xc40d1c: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xc40d20: ldur            x1, [fp, #-0x10]
    // 0xc40d24: r0 = LoadClassIdInstr(r1)
    //     0xc40d24: ldur            x0, [x1, #-1]
    //     0xc40d28: ubfx            x0, x0, #0xc, #0x14
    // 0xc40d2c: r2 = "sql"
    //     0xc40d2c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43070] "sql"
    //     0xc40d30: ldr             x2, [x2, #0x70]
    // 0xc40d34: r0 = GDT[cid_x0 + -0x114]()
    //     0xc40d34: sub             lr, x0, #0x114
    //     0xc40d38: ldr             lr, [x21, lr, lsl #3]
    //     0xc40d3c: blr             lr
    // 0xc40d40: ldur            x1, [fp, #-0x18]
    // 0xc40d44: ArrayStore: r1[3] = r0  ; List_4
    //     0xc40d44: add             x25, x1, #0x1b
    //     0xc40d48: str             w0, [x25]
    //     0xc40d4c: tbz             w0, #0, #0xc40d68
    //     0xc40d50: ldurb           w16, [x1, #-1]
    //     0xc40d54: ldurb           w17, [x0, #-1]
    //     0xc40d58: and             x16, x17, x16, lsr #2
    //     0xc40d5c: tst             x16, HEAP, lsr #32
    //     0xc40d60: b.eq            #0xc40d68
    //     0xc40d64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc40d68: ldur            x0, [fp, #-0x18]
    // 0xc40d6c: r16 = "\' args "
    //     0xc40d6c: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cf28] "\' args "
    //     0xc40d70: ldr             x16, [x16, #0xf28]
    // 0xc40d74: StoreField: r0->field_1f = r16
    //     0xc40d74: stur            w16, [x0, #0x1f]
    // 0xc40d78: ldur            x1, [fp, #-8]
    // 0xc40d7c: r0 = argumentsToString()
    //     0xc40d7c: bl              #0xc40f1c  ; [package:sqflite_common/src/arg_utils.dart] ::argumentsToString
    // 0xc40d80: ldur            x1, [fp, #-0x18]
    // 0xc40d84: ArrayStore: r1[5] = r0  ; List_4
    //     0xc40d84: add             x25, x1, #0x23
    //     0xc40d88: str             w0, [x25]
    //     0xc40d8c: tbz             w0, #0, #0xc40da8
    //     0xc40d90: ldurb           w16, [x1, #-1]
    //     0xc40d94: ldurb           w17, [x0, #-1]
    //     0xc40d98: and             x16, x17, x16, lsr #2
    //     0xc40d9c: tst             x16, HEAP, lsr #32
    //     0xc40da0: b.eq            #0xc40da8
    //     0xc40da4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc40da8: ldur            x16, [fp, #-0x18]
    // 0xc40dac: str             x16, [SP]
    // 0xc40db0: r0 = _interpolate()
    //     0xc40db0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc40db4: LeaveFrame
    //     0xc40db4: mov             SP, fp
    //     0xc40db8: ldp             fp, lr, [SP], #0x10
    // 0xc40dbc: ret
    //     0xc40dbc: ret             
    // 0xc40dc0: ldr             x0, [fp, #0x10]
    // 0xc40dc4: r1 = Null
    //     0xc40dc4: mov             x1, NULL
    // 0xc40dc8: r2 = 10
    //     0xc40dc8: movz            x2, #0xa
    // 0xc40dcc: r0 = AllocateArray()
    //     0xc40dcc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc40dd0: mov             x3, x0
    // 0xc40dd4: stur            x3, [fp, #-0x10]
    // 0xc40dd8: r16 = "DatabaseException("
    //     0xc40dd8: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cf08] "DatabaseException("
    //     0xc40ddc: ldr             x16, [x16, #0xf08]
    // 0xc40de0: StoreField: r3->field_f = r16
    //     0xc40de0: stur            w16, [x3, #0xf]
    // 0xc40de4: ldr             x0, [fp, #0x10]
    // 0xc40de8: LoadField: r1 = r0->field_7
    //     0xc40de8: ldur            w1, [x0, #7]
    // 0xc40dec: DecompressPointer r1
    //     0xc40dec: add             x1, x1, HEAP, lsl #32
    // 0xc40df0: StoreField: r3->field_13 = r1
    //     0xc40df0: stur            w1, [x3, #0x13]
    // 0xc40df4: r16 = ") sql \'"
    //     0xc40df4: add             x16, PP, #0x4c, lsl #12  ; [pp+0x4cf10] ") sql \'"
    //     0xc40df8: ldr             x16, [x16, #0xf10]
    // 0xc40dfc: ArrayStore: r3[0] = r16  ; List_4
    //     0xc40dfc: stur            w16, [x3, #0x17]
    // 0xc40e00: LoadField: r4 = r0->field_b
    //     0xc40e00: ldur            w4, [x0, #0xb]
    // 0xc40e04: DecompressPointer r4
    //     0xc40e04: add             x4, x4, HEAP, lsl #32
    // 0xc40e08: mov             x0, x4
    // 0xc40e0c: stur            x4, [fp, #-8]
    // 0xc40e10: r2 = Null
    //     0xc40e10: mov             x2, NULL
    // 0xc40e14: r1 = Null
    //     0xc40e14: mov             x1, NULL
    // 0xc40e18: r8 = Map
    //     0xc40e18: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0xc40e1c: r3 = Null
    //     0xc40e1c: add             x3, PP, #0x4c, lsl #12  ; [pp+0x4cf30] Null
    //     0xc40e20: ldr             x3, [x3, #0xf30]
    // 0xc40e24: r0 = Map()
    //     0xc40e24: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0xc40e28: ldur            x1, [fp, #-8]
    // 0xc40e2c: r0 = LoadClassIdInstr(r1)
    //     0xc40e2c: ldur            x0, [x1, #-1]
    //     0xc40e30: ubfx            x0, x0, #0xc, #0x14
    // 0xc40e34: r2 = "sql"
    //     0xc40e34: add             x2, PP, #0x43, lsl #12  ; [pp+0x43070] "sql"
    //     0xc40e38: ldr             x2, [x2, #0x70]
    // 0xc40e3c: r0 = GDT[cid_x0 + -0x114]()
    //     0xc40e3c: sub             lr, x0, #0x114
    //     0xc40e40: ldr             lr, [x21, lr, lsl #3]
    //     0xc40e44: blr             lr
    // 0xc40e48: ldur            x1, [fp, #-0x10]
    // 0xc40e4c: ArrayStore: r1[3] = r0  ; List_4
    //     0xc40e4c: add             x25, x1, #0x1b
    //     0xc40e50: str             w0, [x25]
    //     0xc40e54: tbz             w0, #0, #0xc40e70
    //     0xc40e58: ldurb           w16, [x1, #-1]
    //     0xc40e5c: ldurb           w17, [x0, #-1]
    //     0xc40e60: and             x16, x17, x16, lsr #2
    //     0xc40e64: tst             x16, HEAP, lsr #32
    //     0xc40e68: b.eq            #0xc40e70
    //     0xc40e6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc40e70: ldur            x0, [fp, #-0x10]
    // 0xc40e74: r16 = "\'"
    //     0xc40e74: ldr             x16, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xc40e78: StoreField: r0->field_1f = r16
    //     0xc40e78: stur            w16, [x0, #0x1f]
    // 0xc40e7c: str             x0, [SP]
    // 0xc40e80: r0 = _interpolate()
    //     0xc40e80: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc40e84: LeaveFrame
    //     0xc40e84: mov             SP, fp
    //     0xc40e88: ldp             fp, lr, [SP], #0x10
    // 0xc40e8c: ret
    //     0xc40e8c: ret             
    // 0xc40e90: ldr             x0, [fp, #0x10]
    // 0xc40e94: b               #0xc40e9c
    // 0xc40e98: ldr             x0, [fp, #0x10]
    // 0xc40e9c: str             x0, [SP]
    // 0xc40ea0: r0 = toString()
    //     0xc40ea0: bl              #0xc40eb8  ; [package:sqflite_common/src/exception.dart] DatabaseException::toString
    // 0xc40ea4: LeaveFrame
    //     0xc40ea4: mov             SP, fp
    //     0xc40ea8: ldp             fp, lr, [SP], #0x10
    // 0xc40eac: ret
    //     0xc40eac: ret             
    // 0xc40eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc40eb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc40eb4: b               #0xc40ab4
  }
}
