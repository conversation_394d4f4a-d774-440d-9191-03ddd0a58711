// lib: , url: package:sqflite_common/src/factory_mixin.dart

// class id: 1051156, size: 0x8
class :: {
}

// class id: 465, size: 0x8, field offset: 0x8
abstract class SqfliteDatabaseFactoryMixin extends Object
    implements SqfliteDatabaseFactory, SqfliteInvokeHandler {
}

// class id: 466, size: 0xc, field offset: 0x8
class _NamedLock extends Object {

  static late final Map<String, _NamedLock> cacheLocks; // offset: 0x111c

  factory _ _NamedLock(/* No info */) {
    // ** addr: 0xab2184, size: 0x104
    // 0xab2184: EnterFrame
    //     0xab2184: stp             fp, lr, [SP, #-0x10]!
    //     0xab2188: mov             fp, SP
    // 0xab218c: AllocStack(0x20)
    //     0xab218c: sub             SP, SP, #0x20
    // 0xab2190: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xab2190: stur            x2, [fp, #-8]
    // 0xab2194: CheckStackOverflow
    //     0xab2194: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2198: cmp             SP, x16
    //     0xab219c: b.ls            #0xab2280
    // 0xab21a0: r0 = InitLateStaticField(0x111c) // [package:sqflite_common/src/factory_mixin.dart] _NamedLock::cacheLocks
    //     0xab21a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab21a4: ldr             x0, [x0, #0x2238]
    //     0xab21a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab21ac: cmp             w0, w16
    //     0xab21b0: b.ne            #0xab21c0
    //     0xab21b4: add             x2, PP, #0x43, lsl #12  ; [pp+0x433b8] Field <<EMAIL>>: static late final (offset: 0x111c)
    //     0xab21b8: ldr             x2, [x2, #0x3b8]
    //     0xab21bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xab21c0: mov             x1, x0
    // 0xab21c4: ldur            x2, [fp, #-8]
    // 0xab21c8: stur            x0, [fp, #-0x10]
    // 0xab21cc: r0 = _getValueOrData()
    //     0xab21cc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xab21d0: ldur            x1, [fp, #-0x10]
    // 0xab21d4: LoadField: r2 = r1->field_f
    //     0xab21d4: ldur            w2, [x1, #0xf]
    // 0xab21d8: DecompressPointer r2
    //     0xab21d8: add             x2, x2, HEAP, lsl #32
    // 0xab21dc: cmp             w2, w0
    // 0xab21e0: b.ne            #0xab21e8
    // 0xab21e4: r0 = Null
    //     0xab21e4: mov             x0, NULL
    // 0xab21e8: cmp             w0, NULL
    // 0xab21ec: b.ne            #0xab2274
    // 0xab21f0: r0 = BasicLock()
    //     0xab21f0: bl              #0xaaf9f4  ; AllocateBasicLockStub -> BasicLock (size=0xc)
    // 0xab21f4: r1 = Null
    //     0xab21f4: mov             x1, NULL
    // 0xab21f8: r2 = 2
    //     0xab21f8: movz            x2, #0x2
    // 0xab21fc: stur            x0, [fp, #-0x18]
    // 0xab2200: r0 = AllocateArray()
    //     0xab2200: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab2204: mov             x2, x0
    // 0xab2208: ldur            x0, [fp, #-0x18]
    // 0xab220c: stur            x2, [fp, #-0x20]
    // 0xab2210: StoreField: r2->field_f = r0
    //     0xab2210: stur            w0, [x2, #0xf]
    // 0xab2214: r1 = <BasicLock>
    //     0xab2214: add             x1, PP, #0x43, lsl #12  ; [pp+0x43358] TypeArguments: <BasicLock>
    //     0xab2218: ldr             x1, [x1, #0x358]
    // 0xab221c: r0 = AllocateGrowableArray()
    //     0xab221c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xab2220: mov             x1, x0
    // 0xab2224: ldur            x0, [fp, #-0x20]
    // 0xab2228: stur            x1, [fp, #-0x18]
    // 0xab222c: StoreField: r1->field_f = r0
    //     0xab222c: stur            w0, [x1, #0xf]
    // 0xab2230: r0 = 2
    //     0xab2230: movz            x0, #0x2
    // 0xab2234: StoreField: r1->field_b = r0
    //     0xab2234: stur            w0, [x1, #0xb]
    // 0xab2238: r0 = ReentrantLock()
    //     0xab2238: bl              #0xab2374  ; AllocateReentrantLockStub -> ReentrantLock (size=0xc)
    // 0xab223c: mov             x1, x0
    // 0xab2240: ldur            x0, [fp, #-0x18]
    // 0xab2244: stur            x1, [fp, #-0x20]
    // 0xab2248: StoreField: r1->field_7 = r0
    //     0xab2248: stur            w0, [x1, #7]
    // 0xab224c: r0 = _NamedLock()
    //     0xab224c: bl              #0xab2368  ; Allocate_NamedLockStub -> _NamedLock (size=0xc)
    // 0xab2250: mov             x4, x0
    // 0xab2254: ldur            x0, [fp, #-0x20]
    // 0xab2258: stur            x4, [fp, #-0x18]
    // 0xab225c: StoreField: r4->field_7 = r0
    //     0xab225c: stur            w0, [x4, #7]
    // 0xab2260: ldur            x1, [fp, #-0x10]
    // 0xab2264: ldur            x2, [fp, #-8]
    // 0xab2268: mov             x3, x4
    // 0xab226c: r0 = []=()
    //     0xab226c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab2270: ldur            x0, [fp, #-0x18]
    // 0xab2274: LeaveFrame
    //     0xab2274: mov             SP, fp
    //     0xab2278: ldp             fp, lr, [SP], #0x10
    // 0xab227c: ret
    //     0xab227c: ret             
    // 0xab2280: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab2280: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2284: b               #0xab21a0
  }
  static Map<String, _NamedLock> cacheLocks() {
    // ** addr: 0xab2380, size: 0x40
    // 0xab2380: EnterFrame
    //     0xab2380: stp             fp, lr, [SP, #-0x10]!
    //     0xab2384: mov             fp, SP
    // 0xab2388: AllocStack(0x10)
    //     0xab2388: sub             SP, SP, #0x10
    // 0xab238c: CheckStackOverflow
    //     0xab238c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2390: cmp             SP, x16
    //     0xab2394: b.ls            #0xab23b8
    // 0xab2398: r16 = <String, _NamedLock>
    //     0xab2398: add             x16, PP, #0x43, lsl #12  ; [pp+0x433c0] TypeArguments: <String, _NamedLock>
    //     0xab239c: ldr             x16, [x16, #0x3c0]
    // 0xab23a0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xab23a4: stp             lr, x16, [SP]
    // 0xab23a8: r0 = Map._fromLiteral()
    //     0xab23a8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab23ac: LeaveFrame
    //     0xab23ac: mov             SP, fp
    //     0xab23b0: ldp             fp, lr, [SP], #0x10
    // 0xab23b4: ret
    //     0xab23b4: ret             
    // 0xab23b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab23b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab23bc: b               #0xab2398
  }
}
