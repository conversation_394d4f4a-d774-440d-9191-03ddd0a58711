// lib: , url: package:sqflite_common/src/open_options.dart

// class id: 1051158, size: 0x8
class :: {
}

// class id: 464, size: 0x2c, field offset: 0x8
class SqfliteOpenDatabaseOptions extends Object
    implements OpenDatabaseOptions {

  _ toString(/* No info */) {
    // ** addr: 0xc41304, size: 0x84
    // 0xc41304: EnterFrame
    //     0xc41304: stp             fp, lr, [SP, #-0x10]!
    //     0xc41308: mov             fp, SP
    // 0xc4130c: AllocStack(0x18)
    //     0xc4130c: sub             SP, SP, #0x18
    // 0xc41310: CheckStackOverflow
    //     0xc41310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41314: cmp             SP, x16
    //     0xc41318: b.ls            #0xc41380
    // 0xc4131c: r16 = <String, Object?>
    //     0xc4131c: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xc41320: ldr             x16, [x16, #0x738]
    // 0xc41324: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc41328: stp             lr, x16, [SP]
    // 0xc4132c: r0 = Map._fromLiteral()
    //     0xc4132c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xc41330: mov             x1, x0
    // 0xc41334: r2 = "version"
    //     0xc41334: add             x2, PP, #0xf, lsl #12  ; [pp+0xfeb8] "version"
    //     0xc41338: ldr             x2, [x2, #0xeb8]
    // 0xc4133c: r3 = 6
    //     0xc4133c: movz            x3, #0x6
    // 0xc41340: stur            x0, [fp, #-8]
    // 0xc41344: r0 = []=()
    //     0xc41344: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xc41348: ldur            x1, [fp, #-8]
    // 0xc4134c: r2 = "readOnly"
    //     0xc4134c: ldr             x2, [PP, #0x7a70]  ; [pp+0x7a70] "readOnly"
    // 0xc41350: r3 = false
    //     0xc41350: add             x3, NULL, #0x30  ; false
    // 0xc41354: r0 = []=()
    //     0xc41354: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xc41358: ldur            x1, [fp, #-8]
    // 0xc4135c: r2 = "singleInstance"
    //     0xc4135c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43300] "singleInstance"
    //     0xc41360: ldr             x2, [x2, #0x300]
    // 0xc41364: r3 = true
    //     0xc41364: add             x3, NULL, #0x20  ; true
    // 0xc41368: r0 = []=()
    //     0xc41368: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xc4136c: ldur            x1, [fp, #-8]
    // 0xc41370: r0 = mapToString()
    //     0xc41370: bl              #0x7275e0  ; [dart:collection] MapBase::mapToString
    // 0xc41374: LeaveFrame
    //     0xc41374: mov             SP, fp
    //     0xc41378: ldp             fp, lr, [SP], #0x10
    // 0xc4137c: ret
    //     0xc4137c: ret             
    // 0xc41380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41380: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41384: b               #0xc4131c
  }
}
