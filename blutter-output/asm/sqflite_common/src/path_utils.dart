// lib: , url: package:sqflite_common/src/path_utils.dart

// class id: 1051159, size: 0x8
class :: {

  static _ isFileUriDatabasePath(/* No info */) {
    // ** addr: 0xab3840, size: 0x38
    // 0xab3840: EnterFrame
    //     0xab3840: stp             fp, lr, [SP, #-0x10]!
    //     0xab3844: mov             fp, SP
    // 0xab3848: CheckStackOverflow
    //     0xab3848: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab384c: cmp             SP, x16
    //     0xab3850: b.ls            #0xab3870
    // 0xab3854: r2 = "file:"
    //     0xab3854: add             x2, PP, #0xc, lsl #12  ; [pp+0xccc8] "file:"
    //     0xab3858: ldr             x2, [x2, #0xcc8]
    // 0xab385c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab385c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab3860: r0 = startsWith()
    //     0xab3860: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xab3864: LeaveFrame
    //     0xab3864: mov             SP, fp
    //     0xab3868: ldp             fp, lr, [SP], #0x10
    // 0xab386c: ret
    //     0xab386c: ret             
    // 0xab3870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3870: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3874: b               #0xab3854
  }
  static _ isInMemoryDatabasePath(/* No info */) {
    // ** addr: 0xab3878, size: 0xbc
    // 0xab3878: EnterFrame
    //     0xab3878: stp             fp, lr, [SP, #-0x10]!
    //     0xab387c: mov             fp, SP
    // 0xab3880: AllocStack(0x18)
    //     0xab3880: sub             SP, SP, #0x18
    // 0xab3884: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xab3884: stur            x1, [fp, #-8]
    // 0xab3888: CheckStackOverflow
    //     0xab3888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab388c: cmp             SP, x16
    //     0xab3890: b.ls            #0xab392c
    // 0xab3894: r0 = LoadClassIdInstr(r1)
    //     0xab3894: ldur            x0, [x1, #-1]
    //     0xab3898: ubfx            x0, x0, #0xc, #0x14
    // 0xab389c: r16 = ":memory:"
    //     0xab389c: add             x16, PP, #0x43, lsl #12  ; [pp+0x43348] ":memory:"
    //     0xab38a0: ldr             x16, [x16, #0x348]
    // 0xab38a4: stp             x16, x1, [SP]
    // 0xab38a8: mov             lr, x0
    // 0xab38ac: ldr             lr, [x21, lr, lsl #3]
    // 0xab38b0: blr             lr
    // 0xab38b4: tbnz            w0, #4, #0xab38c8
    // 0xab38b8: r0 = true
    //     0xab38b8: add             x0, NULL, #0x20  ; true
    // 0xab38bc: LeaveFrame
    //     0xab38bc: mov             SP, fp
    //     0xab38c0: ldp             fp, lr, [SP], #0x10
    // 0xab38c4: ret
    //     0xab38c4: ret             
    // 0xab38c8: ldur            x1, [fp, #-8]
    // 0xab38cc: r0 = isFileUriDatabasePath()
    //     0xab38cc: bl              #0xab3840  ; [package:sqflite_common/src/path_utils.dart] ::isFileUriDatabasePath
    // 0xab38d0: tbnz            w0, #4, #0xab391c
    // 0xab38d4: ldur            x1, [fp, #-8]
    // 0xab38d8: r2 = 5
    //     0xab38d8: movz            x2, #0x5
    // 0xab38dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab38dc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab38e0: r0 = substring()
    //     0xab38e0: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xab38e4: r1 = LoadClassIdInstr(r0)
    //     0xab38e4: ldur            x1, [x0, #-1]
    //     0xab38e8: ubfx            x1, x1, #0xc, #0x14
    // 0xab38ec: r16 = ":memory:"
    //     0xab38ec: add             x16, PP, #0x43, lsl #12  ; [pp+0x43348] ":memory:"
    //     0xab38f0: ldr             x16, [x16, #0x348]
    // 0xab38f4: stp             x16, x0, [SP]
    // 0xab38f8: mov             x0, x1
    // 0xab38fc: mov             lr, x0
    // 0xab3900: ldr             lr, [x21, lr, lsl #3]
    // 0xab3904: blr             lr
    // 0xab3908: tbnz            w0, #4, #0xab391c
    // 0xab390c: r0 = true
    //     0xab390c: add             x0, NULL, #0x20  ; true
    // 0xab3910: LeaveFrame
    //     0xab3910: mov             SP, fp
    //     0xab3914: ldp             fp, lr, [SP], #0x10
    // 0xab3918: ret
    //     0xab3918: ret             
    // 0xab391c: r0 = false
    //     0xab391c: add             x0, NULL, #0x30  ; false
    // 0xab3920: LeaveFrame
    //     0xab3920: mov             SP, fp
    //     0xab3924: ldp             fp, lr, [SP], #0x10
    // 0xab3928: ret
    //     0xab3928: ret             
    // 0xab392c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab392c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3930: b               #0xab3894
  }
}
