// lib: , url: package:sqflite_common/src/sqflite_database_factory.dart

// class id: 1051160, size: 0x8
class :: {

  get _ databaseFactory(/* No info */) {
    // ** addr: 0xab6ec8, size: 0x44
    // 0xab6ec8: EnterFrame
    //     0xab6ec8: stp             fp, lr, [SP, #-0x10]!
    //     0xab6ecc: mov             fp, SP
    // 0xab6ed0: r0 = LoadStaticField(0x1758)
    //     0xab6ed0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab6ed4: ldr             x0, [x0, #0x2eb0]
    // 0xab6ed8: cmp             w0, NULL
    // 0xab6edc: b.eq            #0xab6eec
    // 0xab6ee0: LeaveFrame
    //     0xab6ee0: mov             SP, fp
    //     0xab6ee4: ldp             fp, lr, [SP], #0x10
    // 0xab6ee8: ret
    //     0xab6ee8: ret             
    // 0xab6eec: r0 = StateError()
    //     0xab6eec: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xab6ef0: mov             x1, x0
    // 0xab6ef4: r0 = "databaseFactory not initialized\ndatabaseFactory is only initialized when using sqflite. When using `sqflite_common_ffi`\nYou must call `databaseFactory = databaseFactoryFfi;` before using global openDatabase API\n"
    //     0xab6ef4: add             x0, PP, #0x43, lsl #12  ; [pp+0x433d8] "databaseFactory not initialized\ndatabaseFactory is only initialized when using sqflite. When using `sqflite_common_ffi`\nYou must call `databaseFactory = databaseFactoryFfi;` before using global openDatabase API\n"
    //     0xab6ef8: ldr             x0, [x0, #0x3d8]
    // 0xab6efc: StoreField: r1->field_b = r0
    //     0xab6efc: stur            w0, [x1, #0xb]
    // 0xab6f00: mov             x0, x1
    // 0xab6f04: r0 = Throw()
    //     0xab6f04: bl              #0xec04b8  ; ThrowStub
    // 0xab6f08: brk             #0
  }
}
