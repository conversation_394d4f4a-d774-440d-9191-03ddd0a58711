// lib: , url: package:sqflite_common/src/sql_builder.dart

// class id: 1051161, size: 0x8
class :: {

  static late final Set<String> escapeNames; // offset: 0x112c

  static _ escapeName(/* No info */) {
    // ** addr: 0xdc1450, size: 0x9c
    // 0xdc1450: EnterFrame
    //     0xdc1450: stp             fp, lr, [SP, #-0x10]!
    //     0xdc1454: mov             fp, SP
    // 0xdc1458: AllocStack(0x18)
    //     0xdc1458: sub             SP, SP, #0x18
    // 0xdc145c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xdc145c: stur            x1, [fp, #-8]
    // 0xdc1460: CheckStackOverflow
    //     0xdc1460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc1464: cmp             SP, x16
    //     0xdc1468: b.ls            #0xdc14e4
    // 0xdc146c: r0 = InitLateStaticField(0x112c) // [package:sqflite_common/src/sql_builder.dart] ::escapeNames
    //     0xdc146c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc1470: ldr             x0, [x0, #0x2258]
    //     0xdc1474: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xdc1478: cmp             w0, w16
    //     0xdc147c: b.ne            #0xdc148c
    //     0xdc1480: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d1f0] Field <::.escapeNames>: static late final (offset: 0x112c)
    //     0xdc1484: ldr             x2, [x2, #0x1f0]
    //     0xdc1488: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xdc148c: mov             x2, x0
    // 0xdc1490: ldur            x1, [fp, #-8]
    // 0xdc1494: stur            x2, [fp, #-0x10]
    // 0xdc1498: r0 = LoadClassIdInstr(r1)
    //     0xdc1498: ldur            x0, [x1, #-1]
    //     0xdc149c: ubfx            x0, x0, #0xc, #0x14
    // 0xdc14a0: str             x1, [SP]
    // 0xdc14a4: r0 = GDT[cid_x0 + -0xffe]()
    //     0xdc14a4: sub             lr, x0, #0xffe
    //     0xdc14a8: ldr             lr, [x21, lr, lsl #3]
    //     0xdc14ac: blr             lr
    // 0xdc14b0: ldur            x1, [fp, #-0x10]
    // 0xdc14b4: mov             x2, x0
    // 0xdc14b8: r0 = contains()
    //     0xdc14b8: bl              #0x86b148  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0xdc14bc: tbnz            w0, #4, #0xdc14d4
    // 0xdc14c0: ldur            x1, [fp, #-8]
    // 0xdc14c4: r0 = _doEscape()
    //     0xdc14c4: bl              #0xdc14ec  ; [package:sqflite_common/src/sql_builder.dart] ::_doEscape
    // 0xdc14c8: LeaveFrame
    //     0xdc14c8: mov             SP, fp
    //     0xdc14cc: ldp             fp, lr, [SP], #0x10
    // 0xdc14d0: ret
    //     0xdc14d0: ret             
    // 0xdc14d4: ldur            x0, [fp, #-8]
    // 0xdc14d8: LeaveFrame
    //     0xdc14d8: mov             SP, fp
    //     0xdc14dc: ldp             fp, lr, [SP], #0x10
    // 0xdc14e0: ret
    //     0xdc14e0: ret             
    // 0xdc14e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc14e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc14e8: b               #0xdc146c
  }
  static _ _doEscape(/* No info */) {
    // ** addr: 0xdc14ec, size: 0x60
    // 0xdc14ec: EnterFrame
    //     0xdc14ec: stp             fp, lr, [SP, #-0x10]!
    //     0xdc14f0: mov             fp, SP
    // 0xdc14f4: AllocStack(0x10)
    //     0xdc14f4: sub             SP, SP, #0x10
    // 0xdc14f8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xdc14f8: mov             x0, x1
    //     0xdc14fc: stur            x1, [fp, #-8]
    // 0xdc1500: CheckStackOverflow
    //     0xdc1500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc1504: cmp             SP, x16
    //     0xdc1508: b.ls            #0xdc1544
    // 0xdc150c: r1 = Null
    //     0xdc150c: mov             x1, NULL
    // 0xdc1510: r2 = 6
    //     0xdc1510: movz            x2, #0x6
    // 0xdc1514: r0 = AllocateArray()
    //     0xdc1514: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdc1518: r16 = "\""
    //     0xdc1518: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0xdc151c: StoreField: r0->field_f = r16
    //     0xdc151c: stur            w16, [x0, #0xf]
    // 0xdc1520: ldur            x1, [fp, #-8]
    // 0xdc1524: StoreField: r0->field_13 = r1
    //     0xdc1524: stur            w1, [x0, #0x13]
    // 0xdc1528: r16 = "\""
    //     0xdc1528: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0xdc152c: ArrayStore: r0[0] = r16  ; List_4
    //     0xdc152c: stur            w16, [x0, #0x17]
    // 0xdc1530: str             x0, [SP]
    // 0xdc1534: r0 = _interpolate()
    //     0xdc1534: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xdc1538: LeaveFrame
    //     0xdc1538: mov             SP, fp
    //     0xdc153c: ldp             fp, lr, [SP], #0x10
    // 0xdc1540: ret
    //     0xdc1540: ret             
    // 0xdc1544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc1544: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc1548: b               #0xdc150c
  }
  static Set<String> escapeNames() {
    // ** addr: 0xdc154c, size: 0x9a4
    // 0xdc154c: EnterFrame
    //     0xdc154c: stp             fp, lr, [SP, #-0x10]!
    //     0xdc1550: mov             fp, SP
    // 0xdc1554: AllocStack(0x10)
    //     0xdc1554: sub             SP, SP, #0x10
    // 0xdc1558: CheckStackOverflow
    //     0xdc1558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc155c: cmp             SP, x16
    //     0xdc1560: b.ls            #0xdc1ee8
    // 0xdc1564: r0 = InitLateStaticField(0x3bc) // [dart:_compact_hash] ::_uninitializedIndex
    //     0xdc1564: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc1568: ldr             x0, [x0, #0x778]
    //     0xdc156c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xdc1570: cmp             w0, w16
    //     0xdc1574: b.ne            #0xdc1580
    //     0xdc1578: ldr             x2, [PP, #0x1340]  ; [pp+0x1340] Field <::._uninitializedIndex@3099033>: static late final (offset: 0x3bc)
    //     0xdc157c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xdc1580: r1 = <String>
    //     0xdc1580: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xdc1584: stur            x0, [fp, #-8]
    // 0xdc1588: r0 = _Set()
    //     0xdc1588: bl              #0x623484  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0xdc158c: mov             x1, x0
    // 0xdc1590: ldur            x0, [fp, #-8]
    // 0xdc1594: stur            x1, [fp, #-0x10]
    // 0xdc1598: StoreField: r1->field_1b = r0
    //     0xdc1598: stur            w0, [x1, #0x1b]
    // 0xdc159c: StoreField: r1->field_b = rZR
    //     0xdc159c: stur            wzr, [x1, #0xb]
    // 0xdc15a0: r0 = InitLateStaticField(0x3c0) // [dart:_compact_hash] ::_uninitializedData
    //     0xdc15a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc15a4: ldr             x0, [x0, #0x780]
    //     0xdc15a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xdc15ac: cmp             w0, w16
    //     0xdc15b0: b.ne            #0xdc15bc
    //     0xdc15b4: ldr             x2, [PP, #0x1348]  ; [pp+0x1348] Field <::._uninitializedData@3099033>: static late final (offset: 0x3c0)
    //     0xdc15b8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xdc15bc: mov             x1, x0
    // 0xdc15c0: ldur            x0, [fp, #-0x10]
    // 0xdc15c4: StoreField: r0->field_f = r1
    //     0xdc15c4: stur            w1, [x0, #0xf]
    // 0xdc15c8: StoreField: r0->field_13 = rZR
    //     0xdc15c8: stur            wzr, [x0, #0x13]
    // 0xdc15cc: ArrayStore: r0[0] = rZR  ; List_4
    //     0xdc15cc: stur            wzr, [x0, #0x17]
    // 0xdc15d0: mov             x1, x0
    // 0xdc15d4: r2 = "abort"
    //     0xdc15d4: add             x2, PP, #0xe, lsl #12  ; [pp+0xe568] "abort"
    //     0xdc15d8: ldr             x2, [x2, #0x568]
    // 0xdc15dc: r0 = add()
    //     0xdc15dc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc15e0: ldur            x1, [fp, #-0x10]
    // 0xdc15e4: r2 = "action"
    //     0xdc15e4: ldr             x2, [PP, #0x42b0]  ; [pp+0x42b0] "action"
    // 0xdc15e8: r0 = add()
    //     0xdc15e8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc15ec: ldur            x1, [fp, #-0x10]
    // 0xdc15f0: r2 = "add"
    //     0xdc15f0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d1f8] "add"
    //     0xdc15f4: ldr             x2, [x2, #0x1f8]
    // 0xdc15f8: r0 = add()
    //     0xdc15f8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc15fc: ldur            x1, [fp, #-0x10]
    // 0xdc1600: r2 = "after"
    //     0xdc1600: add             x2, PP, #0x24, lsl #12  ; [pp+0x24aa8] "after"
    //     0xdc1604: ldr             x2, [x2, #0xaa8]
    // 0xdc1608: r0 = add()
    //     0xdc1608: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc160c: ldur            x1, [fp, #-0x10]
    // 0xdc1610: r2 = "all"
    //     0xdc1610: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d200] "all"
    //     0xdc1614: ldr             x2, [x2, #0x200]
    // 0xdc1618: r0 = add()
    //     0xdc1618: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc161c: ldur            x1, [fp, #-0x10]
    // 0xdc1620: r2 = "alter"
    //     0xdc1620: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d208] "alter"
    //     0xdc1624: ldr             x2, [x2, #0x208]
    // 0xdc1628: r0 = add()
    //     0xdc1628: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc162c: ldur            x1, [fp, #-0x10]
    // 0xdc1630: r2 = "always"
    //     0xdc1630: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d210] "always"
    //     0xdc1634: ldr             x2, [x2, #0x210]
    // 0xdc1638: r0 = add()
    //     0xdc1638: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc163c: ldur            x1, [fp, #-0x10]
    // 0xdc1640: r2 = "analyze"
    //     0xdc1640: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d218] "analyze"
    //     0xdc1644: ldr             x2, [x2, #0x218]
    // 0xdc1648: r0 = add()
    //     0xdc1648: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc164c: ldur            x1, [fp, #-0x10]
    // 0xdc1650: r2 = "and"
    //     0xdc1650: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d220] "and"
    //     0xdc1654: ldr             x2, [x2, #0x220]
    // 0xdc1658: r0 = add()
    //     0xdc1658: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc165c: ldur            x1, [fp, #-0x10]
    // 0xdc1660: r2 = "as"
    //     0xdc1660: add             x2, PP, #9, lsl #12  ; [pp+0x9b28] "as"
    //     0xdc1664: ldr             x2, [x2, #0xb28]
    // 0xdc1668: r0 = add()
    //     0xdc1668: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc166c: ldur            x1, [fp, #-0x10]
    // 0xdc1670: r2 = "asc"
    //     0xdc1670: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d228] "asc"
    //     0xdc1674: ldr             x2, [x2, #0x228]
    // 0xdc1678: r0 = add()
    //     0xdc1678: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc167c: ldur            x1, [fp, #-0x10]
    // 0xdc1680: r2 = "attach"
    //     0xdc1680: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d230] "attach"
    //     0xdc1684: ldr             x2, [x2, #0x230]
    // 0xdc1688: r0 = add()
    //     0xdc1688: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc168c: ldur            x1, [fp, #-0x10]
    // 0xdc1690: r2 = "autoincrement"
    //     0xdc1690: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d238] "autoincrement"
    //     0xdc1694: ldr             x2, [x2, #0x238]
    // 0xdc1698: r0 = add()
    //     0xdc1698: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc169c: ldur            x1, [fp, #-0x10]
    // 0xdc16a0: r2 = "before"
    //     0xdc16a0: add             x2, PP, #0x24, lsl #12  ; [pp+0x24ab0] "before"
    //     0xdc16a4: ldr             x2, [x2, #0xab0]
    // 0xdc16a8: r0 = add()
    //     0xdc16a8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc16ac: ldur            x1, [fp, #-0x10]
    // 0xdc16b0: r2 = "begin"
    //     0xdc16b0: add             x2, PP, #0x43, lsl #12  ; [pp+0x43078] "begin"
    //     0xdc16b4: ldr             x2, [x2, #0x78]
    // 0xdc16b8: r0 = add()
    //     0xdc16b8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc16bc: ldur            x1, [fp, #-0x10]
    // 0xdc16c0: r2 = "between"
    //     0xdc16c0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d240] "between"
    //     0xdc16c4: ldr             x2, [x2, #0x240]
    // 0xdc16c8: r0 = add()
    //     0xdc16c8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc16cc: ldur            x1, [fp, #-0x10]
    // 0xdc16d0: r2 = "by"
    //     0xdc16d0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d248] "by"
    //     0xdc16d4: ldr             x2, [x2, #0x248]
    // 0xdc16d8: r0 = add()
    //     0xdc16d8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc16dc: ldur            x1, [fp, #-0x10]
    // 0xdc16e0: r2 = "cascade"
    //     0xdc16e0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d250] "cascade"
    //     0xdc16e4: ldr             x2, [x2, #0x250]
    // 0xdc16e8: r0 = add()
    //     0xdc16e8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc16ec: ldur            x1, [fp, #-0x10]
    // 0xdc16f0: r2 = "case"
    //     0xdc16f0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d258] "case"
    //     0xdc16f4: ldr             x2, [x2, #0x258]
    // 0xdc16f8: r0 = add()
    //     0xdc16f8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc16fc: ldur            x1, [fp, #-0x10]
    // 0xdc1700: r2 = "cast"
    //     0xdc1700: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d260] "cast"
    //     0xdc1704: ldr             x2, [x2, #0x260]
    // 0xdc1708: r0 = add()
    //     0xdc1708: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc170c: ldur            x1, [fp, #-0x10]
    // 0xdc1710: r2 = "check"
    //     0xdc1710: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d268] "check"
    //     0xdc1714: ldr             x2, [x2, #0x268]
    // 0xdc1718: r0 = add()
    //     0xdc1718: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc171c: ldur            x1, [fp, #-0x10]
    // 0xdc1720: r2 = "collate"
    //     0xdc1720: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d270] "collate"
    //     0xdc1724: ldr             x2, [x2, #0x270]
    // 0xdc1728: r0 = add()
    //     0xdc1728: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc172c: ldur            x1, [fp, #-0x10]
    // 0xdc1730: r2 = "column"
    //     0xdc1730: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d278] "column"
    //     0xdc1734: ldr             x2, [x2, #0x278]
    // 0xdc1738: r0 = add()
    //     0xdc1738: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc173c: ldur            x1, [fp, #-0x10]
    // 0xdc1740: r2 = "commit"
    //     0xdc1740: add             x2, PP, #0x43, lsl #12  ; [pp+0x43080] "commit"
    //     0xdc1744: ldr             x2, [x2, #0x80]
    // 0xdc1748: r0 = add()
    //     0xdc1748: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc174c: ldur            x1, [fp, #-0x10]
    // 0xdc1750: r2 = "conflict"
    //     0xdc1750: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d280] "conflict"
    //     0xdc1754: ldr             x2, [x2, #0x280]
    // 0xdc1758: r0 = add()
    //     0xdc1758: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc175c: ldur            x1, [fp, #-0x10]
    // 0xdc1760: r2 = "constraint"
    //     0xdc1760: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d288] "constraint"
    //     0xdc1764: ldr             x2, [x2, #0x288]
    // 0xdc1768: r0 = add()
    //     0xdc1768: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc176c: ldur            x1, [fp, #-0x10]
    // 0xdc1770: r2 = "create"
    //     0xdc1770: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d290] "create"
    //     0xdc1774: ldr             x2, [x2, #0x290]
    // 0xdc1778: r0 = add()
    //     0xdc1778: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc177c: ldur            x1, [fp, #-0x10]
    // 0xdc1780: r2 = "cross"
    //     0xdc1780: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d298] "cross"
    //     0xdc1784: ldr             x2, [x2, #0x298]
    // 0xdc1788: r0 = add()
    //     0xdc1788: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc178c: ldur            x1, [fp, #-0x10]
    // 0xdc1790: r2 = "current"
    //     0xdc1790: add             x2, PP, #8, lsl #12  ; [pp+0x8110] "current"
    //     0xdc1794: ldr             x2, [x2, #0x110]
    // 0xdc1798: r0 = add()
    //     0xdc1798: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc179c: ldur            x1, [fp, #-0x10]
    // 0xdc17a0: r2 = "current_date"
    //     0xdc17a0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2a0] "current_date"
    //     0xdc17a4: ldr             x2, [x2, #0x2a0]
    // 0xdc17a8: r0 = add()
    //     0xdc17a8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc17ac: ldur            x1, [fp, #-0x10]
    // 0xdc17b0: r2 = "current_time"
    //     0xdc17b0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2a8] "current_time"
    //     0xdc17b4: ldr             x2, [x2, #0x2a8]
    // 0xdc17b8: r0 = add()
    //     0xdc17b8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc17bc: ldur            x1, [fp, #-0x10]
    // 0xdc17c0: r2 = "current_timestamp"
    //     0xdc17c0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2b0] "current_timestamp"
    //     0xdc17c4: ldr             x2, [x2, #0x2b0]
    // 0xdc17c8: r0 = add()
    //     0xdc17c8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc17cc: ldur            x1, [fp, #-0x10]
    // 0xdc17d0: r2 = "database"
    //     0xdc17d0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2b8] "database"
    //     0xdc17d4: ldr             x2, [x2, #0x2b8]
    // 0xdc17d8: r0 = add()
    //     0xdc17d8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc17dc: ldur            x1, [fp, #-0x10]
    // 0xdc17e0: r2 = "default"
    //     0xdc17e0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf990] "default"
    //     0xdc17e4: ldr             x2, [x2, #0x990]
    // 0xdc17e8: r0 = add()
    //     0xdc17e8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc17ec: ldur            x1, [fp, #-0x10]
    // 0xdc17f0: r2 = "deferrable"
    //     0xdc17f0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2c0] "deferrable"
    //     0xdc17f4: ldr             x2, [x2, #0x2c0]
    // 0xdc17f8: r0 = add()
    //     0xdc17f8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc17fc: ldur            x1, [fp, #-0x10]
    // 0xdc1800: r2 = "deferred"
    //     0xdc1800: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2c8] "deferred"
    //     0xdc1804: ldr             x2, [x2, #0x2c8]
    // 0xdc1808: r0 = add()
    //     0xdc1808: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc180c: ldur            x1, [fp, #-0x10]
    // 0xdc1810: r2 = "delete"
    //     0xdc1810: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2d0] "delete"
    //     0xdc1814: ldr             x2, [x2, #0x2d0]
    // 0xdc1818: r0 = add()
    //     0xdc1818: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc181c: ldur            x1, [fp, #-0x10]
    // 0xdc1820: r2 = "desc"
    //     0xdc1820: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2d8] "desc"
    //     0xdc1824: ldr             x2, [x2, #0x2d8]
    // 0xdc1828: r0 = add()
    //     0xdc1828: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc182c: ldur            x1, [fp, #-0x10]
    // 0xdc1830: r2 = "detach"
    //     0xdc1830: add             x2, PP, #0x47, lsl #12  ; [pp+0x47fd8] "detach"
    //     0xdc1834: ldr             x2, [x2, #0xfd8]
    // 0xdc1838: r0 = add()
    //     0xdc1838: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc183c: ldur            x1, [fp, #-0x10]
    // 0xdc1840: r2 = "distinct"
    //     0xdc1840: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2e0] "distinct"
    //     0xdc1844: ldr             x2, [x2, #0x2e0]
    // 0xdc1848: r0 = add()
    //     0xdc1848: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc184c: ldur            x1, [fp, #-0x10]
    // 0xdc1850: r2 = "do"
    //     0xdc1850: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2e8] "do"
    //     0xdc1854: ldr             x2, [x2, #0x2e8]
    // 0xdc1858: r0 = add()
    //     0xdc1858: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc185c: ldur            x1, [fp, #-0x10]
    // 0xdc1860: r2 = "drop"
    //     0xdc1860: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2f0] "drop"
    //     0xdc1864: ldr             x2, [x2, #0x2f0]
    // 0xdc1868: r0 = add()
    //     0xdc1868: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc186c: ldur            x1, [fp, #-0x10]
    // 0xdc1870: r2 = "each"
    //     0xdc1870: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d2f8] "each"
    //     0xdc1874: ldr             x2, [x2, #0x2f8]
    // 0xdc1878: r0 = add()
    //     0xdc1878: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc187c: ldur            x1, [fp, #-0x10]
    // 0xdc1880: r2 = "else"
    //     0xdc1880: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d300] "else"
    //     0xdc1884: ldr             x2, [x2, #0x300]
    // 0xdc1888: r0 = add()
    //     0xdc1888: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc188c: ldur            x1, [fp, #-0x10]
    // 0xdc1890: r2 = "end"
    //     0xdc1890: ldr             x2, [PP, #0x540]  ; [pp+0x540] "end"
    // 0xdc1894: r0 = add()
    //     0xdc1894: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1898: ldur            x1, [fp, #-0x10]
    // 0xdc189c: r2 = "escape"
    //     0xdc189c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d308] "escape"
    //     0xdc18a0: ldr             x2, [x2, #0x308]
    // 0xdc18a4: r0 = add()
    //     0xdc18a4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc18a8: ldur            x1, [fp, #-0x10]
    // 0xdc18ac: r2 = "except"
    //     0xdc18ac: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d310] "except"
    //     0xdc18b0: ldr             x2, [x2, #0x310]
    // 0xdc18b4: r0 = add()
    //     0xdc18b4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc18b8: ldur            x1, [fp, #-0x10]
    // 0xdc18bc: r2 = "exclude"
    //     0xdc18bc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d318] "exclude"
    //     0xdc18c0: ldr             x2, [x2, #0x318]
    // 0xdc18c4: r0 = add()
    //     0xdc18c4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc18c8: ldur            x1, [fp, #-0x10]
    // 0xdc18cc: r2 = "exclusive"
    //     0xdc18cc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d320] "exclusive"
    //     0xdc18d0: ldr             x2, [x2, #0x320]
    // 0xdc18d4: r0 = add()
    //     0xdc18d4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc18d8: ldur            x1, [fp, #-0x10]
    // 0xdc18dc: r2 = "exists"
    //     0xdc18dc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d328] "exists"
    //     0xdc18e0: ldr             x2, [x2, #0x328]
    // 0xdc18e4: r0 = add()
    //     0xdc18e4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc18e8: ldur            x1, [fp, #-0x10]
    // 0xdc18ec: r2 = "explain"
    //     0xdc18ec: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d330] "explain"
    //     0xdc18f0: ldr             x2, [x2, #0x330]
    // 0xdc18f4: r0 = add()
    //     0xdc18f4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc18f8: ldur            x1, [fp, #-0x10]
    // 0xdc18fc: r2 = "fail"
    //     0xdc18fc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d338] "fail"
    //     0xdc1900: ldr             x2, [x2, #0x338]
    // 0xdc1904: r0 = add()
    //     0xdc1904: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1908: ldur            x1, [fp, #-0x10]
    // 0xdc190c: r2 = "filter"
    //     0xdc190c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d340] "filter"
    //     0xdc1910: ldr             x2, [x2, #0x340]
    // 0xdc1914: r0 = add()
    //     0xdc1914: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1918: ldur            x1, [fp, #-0x10]
    // 0xdc191c: r2 = "first"
    //     0xdc191c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d348] "first"
    //     0xdc1920: ldr             x2, [x2, #0x348]
    // 0xdc1924: r0 = add()
    //     0xdc1924: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1928: ldur            x1, [fp, #-0x10]
    // 0xdc192c: r2 = "following"
    //     0xdc192c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d350] "following"
    //     0xdc1930: ldr             x2, [x2, #0x350]
    // 0xdc1934: r0 = add()
    //     0xdc1934: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1938: ldur            x1, [fp, #-0x10]
    // 0xdc193c: r2 = "for"
    //     0xdc193c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d358] "for"
    //     0xdc1940: ldr             x2, [x2, #0x358]
    // 0xdc1944: r0 = add()
    //     0xdc1944: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1948: ldur            x1, [fp, #-0x10]
    // 0xdc194c: r2 = "foreign"
    //     0xdc194c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d360] "foreign"
    //     0xdc1950: ldr             x2, [x2, #0x360]
    // 0xdc1954: r0 = add()
    //     0xdc1954: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1958: ldur            x1, [fp, #-0x10]
    // 0xdc195c: r2 = "from"
    //     0xdc195c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc3d0] "from"
    //     0xdc1960: ldr             x2, [x2, #0x3d0]
    // 0xdc1964: r0 = add()
    //     0xdc1964: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1968: ldur            x1, [fp, #-0x10]
    // 0xdc196c: r2 = "full"
    //     0xdc196c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19fb0] "full"
    //     0xdc1970: ldr             x2, [x2, #0xfb0]
    // 0xdc1974: r0 = add()
    //     0xdc1974: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1978: ldur            x1, [fp, #-0x10]
    // 0xdc197c: r2 = "generated"
    //     0xdc197c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d368] "generated"
    //     0xdc1980: ldr             x2, [x2, #0x368]
    // 0xdc1984: r0 = add()
    //     0xdc1984: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1988: ldur            x1, [fp, #-0x10]
    // 0xdc198c: r2 = "glob"
    //     0xdc198c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d370] "glob"
    //     0xdc1990: ldr             x2, [x2, #0x370]
    // 0xdc1994: r0 = add()
    //     0xdc1994: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1998: ldur            x1, [fp, #-0x10]
    // 0xdc199c: r2 = "group"
    //     0xdc199c: add             x2, PP, #0x19, lsl #12  ; [pp+0x19dd8] "group"
    //     0xdc19a0: ldr             x2, [x2, #0xdd8]
    // 0xdc19a4: r0 = add()
    //     0xdc19a4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc19a8: ldur            x1, [fp, #-0x10]
    // 0xdc19ac: r2 = "groups"
    //     0xdc19ac: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d378] "groups"
    //     0xdc19b0: ldr             x2, [x2, #0x378]
    // 0xdc19b4: r0 = add()
    //     0xdc19b4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc19b8: ldur            x1, [fp, #-0x10]
    // 0xdc19bc: r2 = "having"
    //     0xdc19bc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d380] "having"
    //     0xdc19c0: ldr             x2, [x2, #0x380]
    // 0xdc19c4: r0 = add()
    //     0xdc19c4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc19c8: ldur            x1, [fp, #-0x10]
    // 0xdc19cc: r2 = "if"
    //     0xdc19cc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d388] "if"
    //     0xdc19d0: ldr             x2, [x2, #0x388]
    // 0xdc19d4: r0 = add()
    //     0xdc19d4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc19d8: ldur            x1, [fp, #-0x10]
    // 0xdc19dc: r2 = "ignore"
    //     0xdc19dc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d390] "ignore"
    //     0xdc19e0: ldr             x2, [x2, #0x390]
    // 0xdc19e4: r0 = add()
    //     0xdc19e4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc19e8: ldur            x1, [fp, #-0x10]
    // 0xdc19ec: r2 = "immediate"
    //     0xdc19ec: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d398] "immediate"
    //     0xdc19f0: ldr             x2, [x2, #0x398]
    // 0xdc19f4: r0 = add()
    //     0xdc19f4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc19f8: ldur            x1, [fp, #-0x10]
    // 0xdc19fc: r2 = "in"
    //     0xdc19fc: add             x2, PP, #8, lsl #12  ; [pp+0x8c70] "in"
    //     0xdc1a00: ldr             x2, [x2, #0xc70]
    // 0xdc1a04: r0 = add()
    //     0xdc1a04: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a08: ldur            x1, [fp, #-0x10]
    // 0xdc1a0c: r2 = "index"
    //     0xdc1a0c: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c878] "index"
    //     0xdc1a10: ldr             x2, [x2, #0x878]
    // 0xdc1a14: r0 = add()
    //     0xdc1a14: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a18: ldur            x1, [fp, #-0x10]
    // 0xdc1a1c: r2 = "indexed"
    //     0xdc1a1c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3a0] "indexed"
    //     0xdc1a20: ldr             x2, [x2, #0x3a0]
    // 0xdc1a24: r0 = add()
    //     0xdc1a24: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a28: ldur            x1, [fp, #-0x10]
    // 0xdc1a2c: r2 = "initially"
    //     0xdc1a2c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3a8] "initially"
    //     0xdc1a30: ldr             x2, [x2, #0x3a8]
    // 0xdc1a34: r0 = add()
    //     0xdc1a34: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a38: ldur            x1, [fp, #-0x10]
    // 0xdc1a3c: r2 = "inner"
    //     0xdc1a3c: add             x2, PP, #0x46, lsl #12  ; [pp+0x460b8] "inner"
    //     0xdc1a40: ldr             x2, [x2, #0xb8]
    // 0xdc1a44: r0 = add()
    //     0xdc1a44: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a48: ldur            x1, [fp, #-0x10]
    // 0xdc1a4c: r2 = "insert"
    //     0xdc1a4c: add             x2, PP, #0x40, lsl #12  ; [pp+0x40df0] "insert"
    //     0xdc1a50: ldr             x2, [x2, #0xdf0]
    // 0xdc1a54: r0 = add()
    //     0xdc1a54: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a58: ldur            x1, [fp, #-0x10]
    // 0xdc1a5c: r2 = "instead"
    //     0xdc1a5c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3b0] "instead"
    //     0xdc1a60: ldr             x2, [x2, #0x3b0]
    // 0xdc1a64: r0 = add()
    //     0xdc1a64: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a68: ldur            x1, [fp, #-0x10]
    // 0xdc1a6c: r2 = "intersect"
    //     0xdc1a6c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3b8] "intersect"
    //     0xdc1a70: ldr             x2, [x2, #0x3b8]
    // 0xdc1a74: r0 = add()
    //     0xdc1a74: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a78: ldur            x1, [fp, #-0x10]
    // 0xdc1a7c: r2 = "into"
    //     0xdc1a7c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3c0] "into"
    //     0xdc1a80: ldr             x2, [x2, #0x3c0]
    // 0xdc1a84: r0 = add()
    //     0xdc1a84: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a88: ldur            x1, [fp, #-0x10]
    // 0xdc1a8c: r2 = "is"
    //     0xdc1a8c: add             x2, PP, #9, lsl #12  ; [pp+0x9778] "is"
    //     0xdc1a90: ldr             x2, [x2, #0x778]
    // 0xdc1a94: r0 = add()
    //     0xdc1a94: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1a98: ldur            x1, [fp, #-0x10]
    // 0xdc1a9c: r2 = "isnull"
    //     0xdc1a9c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3c8] "isnull"
    //     0xdc1aa0: ldr             x2, [x2, #0x3c8]
    // 0xdc1aa4: r0 = add()
    //     0xdc1aa4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1aa8: ldur            x1, [fp, #-0x10]
    // 0xdc1aac: r2 = "join"
    //     0xdc1aac: add             x2, PP, #0xb, lsl #12  ; [pp+0xbbf8] "join"
    //     0xdc1ab0: ldr             x2, [x2, #0xbf8]
    // 0xdc1ab4: r0 = add()
    //     0xdc1ab4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ab8: ldur            x1, [fp, #-0x10]
    // 0xdc1abc: r2 = "key"
    //     0xdc1abc: ldr             x2, [PP, #0xab8]  ; [pp+0xab8] "key"
    // 0xdc1ac0: r0 = add()
    //     0xdc1ac0: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ac4: ldur            x1, [fp, #-0x10]
    // 0xdc1ac8: r2 = "last"
    //     0xdc1ac8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a268] "last"
    //     0xdc1acc: ldr             x2, [x2, #0x268]
    // 0xdc1ad0: r0 = add()
    //     0xdc1ad0: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ad4: ldur            x1, [fp, #-0x10]
    // 0xdc1ad8: r2 = "left"
    //     0xdc1ad8: ldr             x2, [PP, #0x7038]  ; [pp+0x7038] "left"
    // 0xdc1adc: r0 = add()
    //     0xdc1adc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ae0: ldur            x1, [fp, #-0x10]
    // 0xdc1ae4: r2 = "like"
    //     0xdc1ae4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3d0] "like"
    //     0xdc1ae8: ldr             x2, [x2, #0x3d0]
    // 0xdc1aec: r0 = add()
    //     0xdc1aec: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1af0: ldur            x1, [fp, #-0x10]
    // 0xdc1af4: r2 = "limit"
    //     0xdc1af4: add             x2, PP, #0x2b, lsl #12  ; [pp+0x2b050] "limit"
    //     0xdc1af8: ldr             x2, [x2, #0x50]
    // 0xdc1afc: r0 = add()
    //     0xdc1afc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b00: ldur            x1, [fp, #-0x10]
    // 0xdc1b04: r2 = "match"
    //     0xdc1b04: add             x2, PP, #0xb, lsl #12  ; [pp+0xb940] "match"
    //     0xdc1b08: ldr             x2, [x2, #0x940]
    // 0xdc1b0c: r0 = add()
    //     0xdc1b0c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b10: ldur            x1, [fp, #-0x10]
    // 0xdc1b14: r2 = "materialized"
    //     0xdc1b14: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3d8] "materialized"
    //     0xdc1b18: ldr             x2, [x2, #0x3d8]
    // 0xdc1b1c: r0 = add()
    //     0xdc1b1c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b20: ldur            x1, [fp, #-0x10]
    // 0xdc1b24: r2 = "natural"
    //     0xdc1b24: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3e0] "natural"
    //     0xdc1b28: ldr             x2, [x2, #0x3e0]
    // 0xdc1b2c: r0 = add()
    //     0xdc1b2c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b30: ldur            x1, [fp, #-0x10]
    // 0xdc1b34: r2 = "no"
    //     0xdc1b34: add             x2, PP, #9, lsl #12  ; [pp+0x9900] "no"
    //     0xdc1b38: ldr             x2, [x2, #0x900]
    // 0xdc1b3c: r0 = add()
    //     0xdc1b3c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b40: ldur            x1, [fp, #-0x10]
    // 0xdc1b44: r2 = "not"
    //     0xdc1b44: add             x2, PP, #0x37, lsl #12  ; [pp+0x37150] "not"
    //     0xdc1b48: ldr             x2, [x2, #0x150]
    // 0xdc1b4c: r0 = add()
    //     0xdc1b4c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b50: ldur            x1, [fp, #-0x10]
    // 0xdc1b54: r2 = "nothing"
    //     0xdc1b54: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3e8] "nothing"
    //     0xdc1b58: ldr             x2, [x2, #0x3e8]
    // 0xdc1b5c: r0 = add()
    //     0xdc1b5c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b60: ldur            x1, [fp, #-0x10]
    // 0xdc1b64: r2 = "notnull"
    //     0xdc1b64: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3f0] "notnull"
    //     0xdc1b68: ldr             x2, [x2, #0x3f0]
    // 0xdc1b6c: r0 = add()
    //     0xdc1b6c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b70: ldur            x1, [fp, #-0x10]
    // 0xdc1b74: r2 = "null"
    //     0xdc1b74: ldr             x2, [PP, #0x4b8]  ; [pp+0x4b8] "null"
    // 0xdc1b78: r0 = add()
    //     0xdc1b78: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b7c: ldur            x1, [fp, #-0x10]
    // 0xdc1b80: r2 = "nulls"
    //     0xdc1b80: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d3f8] "nulls"
    //     0xdc1b84: ldr             x2, [x2, #0x3f8]
    // 0xdc1b88: r0 = add()
    //     0xdc1b88: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b8c: ldur            x1, [fp, #-0x10]
    // 0xdc1b90: r2 = "of"
    //     0xdc1b90: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d400] "of"
    //     0xdc1b94: ldr             x2, [x2, #0x400]
    // 0xdc1b98: r0 = add()
    //     0xdc1b98: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1b9c: ldur            x1, [fp, #-0x10]
    // 0xdc1ba0: r2 = "offset"
    //     0xdc1ba0: add             x2, PP, #0x26, lsl #12  ; [pp+0x263b0] "offset"
    //     0xdc1ba4: ldr             x2, [x2, #0x3b0]
    // 0xdc1ba8: r0 = add()
    //     0xdc1ba8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1bac: ldur            x1, [fp, #-0x10]
    // 0xdc1bb0: r2 = "on"
    //     0xdc1bb0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d408] "on"
    //     0xdc1bb4: ldr             x2, [x2, #0x408]
    // 0xdc1bb8: r0 = add()
    //     0xdc1bb8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1bbc: ldur            x1, [fp, #-0x10]
    // 0xdc1bc0: r2 = "or"
    //     0xdc1bc0: add             x2, PP, #9, lsl #12  ; [pp+0x9910] "or"
    //     0xdc1bc4: ldr             x2, [x2, #0x910]
    // 0xdc1bc8: r0 = add()
    //     0xdc1bc8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1bcc: ldur            x1, [fp, #-0x10]
    // 0xdc1bd0: r2 = "order"
    //     0xdc1bd0: add             x2, PP, #0xf, lsl #12  ; [pp+0xfb78] "order"
    //     0xdc1bd4: ldr             x2, [x2, #0xb78]
    // 0xdc1bd8: r0 = add()
    //     0xdc1bd8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1bdc: ldur            x1, [fp, #-0x10]
    // 0xdc1be0: r2 = "others"
    //     0xdc1be0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d410] "others"
    //     0xdc1be4: ldr             x2, [x2, #0x410]
    // 0xdc1be8: r0 = add()
    //     0xdc1be8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1bec: ldur            x1, [fp, #-0x10]
    // 0xdc1bf0: r2 = "outer"
    //     0xdc1bf0: add             x2, PP, #0x46, lsl #12  ; [pp+0x460b0] "outer"
    //     0xdc1bf4: ldr             x2, [x2, #0xb0]
    // 0xdc1bf8: r0 = add()
    //     0xdc1bf8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1bfc: ldur            x1, [fp, #-0x10]
    // 0xdc1c00: r2 = "over"
    //     0xdc1c00: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d418] "over"
    //     0xdc1c04: ldr             x2, [x2, #0x418]
    // 0xdc1c08: r0 = add()
    //     0xdc1c08: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c0c: ldur            x1, [fp, #-0x10]
    // 0xdc1c10: r2 = "partition"
    //     0xdc1c10: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d420] "partition"
    //     0xdc1c14: ldr             x2, [x2, #0x420]
    // 0xdc1c18: r0 = add()
    //     0xdc1c18: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c1c: ldur            x1, [fp, #-0x10]
    // 0xdc1c20: r2 = "plan"
    //     0xdc1c20: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d428] "plan"
    //     0xdc1c24: ldr             x2, [x2, #0x428]
    // 0xdc1c28: r0 = add()
    //     0xdc1c28: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c2c: ldur            x1, [fp, #-0x10]
    // 0xdc1c30: r2 = "pragma"
    //     0xdc1c30: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d430] "pragma"
    //     0xdc1c34: ldr             x2, [x2, #0x430]
    // 0xdc1c38: r0 = add()
    //     0xdc1c38: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c3c: ldur            x1, [fp, #-0x10]
    // 0xdc1c40: r2 = "preceding"
    //     0xdc1c40: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d438] "preceding"
    //     0xdc1c44: ldr             x2, [x2, #0x438]
    // 0xdc1c48: r0 = add()
    //     0xdc1c48: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c4c: ldur            x1, [fp, #-0x10]
    // 0xdc1c50: r2 = "primary"
    //     0xdc1c50: ldr             x2, [PP, #0x5120]  ; [pp+0x5120] "primary"
    // 0xdc1c54: r0 = add()
    //     0xdc1c54: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c58: ldur            x1, [fp, #-0x10]
    // 0xdc1c5c: r2 = "query"
    //     0xdc1c5c: ldr             x2, [PP, #0x3650]  ; [pp+0x3650] "query"
    // 0xdc1c60: r0 = add()
    //     0xdc1c60: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c64: ldur            x1, [fp, #-0x10]
    // 0xdc1c68: r2 = "raise"
    //     0xdc1c68: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d440] "raise"
    //     0xdc1c6c: ldr             x2, [x2, #0x440]
    // 0xdc1c70: r0 = add()
    //     0xdc1c70: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c74: ldur            x1, [fp, #-0x10]
    // 0xdc1c78: r2 = "range"
    //     0xdc1c78: add             x2, PP, #0x38, lsl #12  ; [pp+0x38488] "range"
    //     0xdc1c7c: ldr             x2, [x2, #0x488]
    // 0xdc1c80: r0 = add()
    //     0xdc1c80: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c84: ldur            x1, [fp, #-0x10]
    // 0xdc1c88: r2 = "recursive"
    //     0xdc1c88: ldr             x2, [PP, #0x7ee8]  ; [pp+0x7ee8] "recursive"
    // 0xdc1c8c: r0 = add()
    //     0xdc1c8c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1c90: ldur            x1, [fp, #-0x10]
    // 0xdc1c94: r2 = "references"
    //     0xdc1c94: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d448] "references"
    //     0xdc1c98: ldr             x2, [x2, #0x448]
    // 0xdc1c9c: r0 = add()
    //     0xdc1c9c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ca0: ldur            x1, [fp, #-0x10]
    // 0xdc1ca4: r2 = "regexp"
    //     0xdc1ca4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d450] "regexp"
    //     0xdc1ca8: ldr             x2, [x2, #0x450]
    // 0xdc1cac: r0 = add()
    //     0xdc1cac: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1cb0: ldur            x1, [fp, #-0x10]
    // 0xdc1cb4: r2 = "reindex"
    //     0xdc1cb4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d458] "reindex"
    //     0xdc1cb8: ldr             x2, [x2, #0x458]
    // 0xdc1cbc: r0 = add()
    //     0xdc1cbc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1cc0: ldur            x1, [fp, #-0x10]
    // 0xdc1cc4: r2 = "release"
    //     0xdc1cc4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3cbf8] "release"
    //     0xdc1cc8: ldr             x2, [x2, #0xbf8]
    // 0xdc1ccc: r0 = add()
    //     0xdc1ccc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1cd0: ldur            x1, [fp, #-0x10]
    // 0xdc1cd4: r2 = "rename"
    //     0xdc1cd4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d460] "rename"
    //     0xdc1cd8: ldr             x2, [x2, #0x460]
    // 0xdc1cdc: r0 = add()
    //     0xdc1cdc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ce0: ldur            x1, [fp, #-0x10]
    // 0xdc1ce4: r2 = "replace"
    //     0xdc1ce4: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a658] "replace"
    //     0xdc1ce8: ldr             x2, [x2, #0x658]
    // 0xdc1cec: r0 = add()
    //     0xdc1cec: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1cf0: ldur            x1, [fp, #-0x10]
    // 0xdc1cf4: r2 = "restrict"
    //     0xdc1cf4: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d468] "restrict"
    //     0xdc1cf8: ldr             x2, [x2, #0x468]
    // 0xdc1cfc: r0 = add()
    //     0xdc1cfc: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d00: ldur            x1, [fp, #-0x10]
    // 0xdc1d04: r2 = "returning"
    //     0xdc1d04: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d470] "returning"
    //     0xdc1d08: ldr             x2, [x2, #0x470]
    // 0xdc1d0c: r0 = add()
    //     0xdc1d0c: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d10: ldur            x1, [fp, #-0x10]
    // 0xdc1d14: r2 = "right"
    //     0xdc1d14: ldr             x2, [PP, #0x7040]  ; [pp+0x7040] "right"
    // 0xdc1d18: r0 = add()
    //     0xdc1d18: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d1c: ldur            x1, [fp, #-0x10]
    // 0xdc1d20: r2 = "rollback"
    //     0xdc1d20: add             x2, PP, #0x43, lsl #12  ; [pp+0x43088] "rollback"
    //     0xdc1d24: ldr             x2, [x2, #0x88]
    // 0xdc1d28: r0 = add()
    //     0xdc1d28: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d2c: ldur            x1, [fp, #-0x10]
    // 0xdc1d30: r2 = "row"
    //     0xdc1d30: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d478] "row"
    //     0xdc1d34: ldr             x2, [x2, #0x478]
    // 0xdc1d38: r0 = add()
    //     0xdc1d38: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d3c: ldur            x1, [fp, #-0x10]
    // 0xdc1d40: r2 = "rows"
    //     0xdc1d40: add             x2, PP, #0x43, lsl #12  ; [pp+0x431e0] "rows"
    //     0xdc1d44: ldr             x2, [x2, #0x1e0]
    // 0xdc1d48: r0 = add()
    //     0xdc1d48: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d4c: ldur            x1, [fp, #-0x10]
    // 0xdc1d50: r2 = "savepoint"
    //     0xdc1d50: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d480] "savepoint"
    //     0xdc1d54: ldr             x2, [x2, #0x480]
    // 0xdc1d58: r0 = add()
    //     0xdc1d58: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d5c: ldur            x1, [fp, #-0x10]
    // 0xdc1d60: r2 = "select"
    //     0xdc1d60: add             x2, PP, #0x4c, lsl #12  ; [pp+0x4c8f8] "select"
    //     0xdc1d64: ldr             x2, [x2, #0x8f8]
    // 0xdc1d68: r0 = add()
    //     0xdc1d68: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d6c: ldur            x1, [fp, #-0x10]
    // 0xdc1d70: r2 = "set"
    //     0xdc1d70: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d488] "set"
    //     0xdc1d74: ldr             x2, [x2, #0x488]
    // 0xdc1d78: r0 = add()
    //     0xdc1d78: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d7c: ldur            x1, [fp, #-0x10]
    // 0xdc1d80: r2 = "table"
    //     0xdc1d80: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b40] "table"
    //     0xdc1d84: ldr             x2, [x2, #0xb40]
    // 0xdc1d88: r0 = add()
    //     0xdc1d88: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d8c: ldur            x1, [fp, #-0x10]
    // 0xdc1d90: r2 = "temp"
    //     0xdc1d90: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d490] "temp"
    //     0xdc1d94: ldr             x2, [x2, #0x490]
    // 0xdc1d98: r0 = add()
    //     0xdc1d98: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1d9c: ldur            x1, [fp, #-0x10]
    // 0xdc1da0: r2 = "temporary"
    //     0xdc1da0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d498] "temporary"
    //     0xdc1da4: ldr             x2, [x2, #0x498]
    // 0xdc1da8: r0 = add()
    //     0xdc1da8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1dac: ldur            x1, [fp, #-0x10]
    // 0xdc1db0: r2 = "then"
    //     0xdc1db0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4a0] "then"
    //     0xdc1db4: ldr             x2, [x2, #0x4a0]
    // 0xdc1db8: r0 = add()
    //     0xdc1db8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1dbc: ldur            x1, [fp, #-0x10]
    // 0xdc1dc0: r2 = "ties"
    //     0xdc1dc0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4a8] "ties"
    //     0xdc1dc4: ldr             x2, [x2, #0x4a8]
    // 0xdc1dc8: r0 = add()
    //     0xdc1dc8: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1dcc: ldur            x1, [fp, #-0x10]
    // 0xdc1dd0: r2 = "to"
    //     0xdc1dd0: ldr             x2, [PP, #0x7b28]  ; [pp+0x7b28] "to"
    // 0xdc1dd4: r0 = add()
    //     0xdc1dd4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1dd8: ldur            x1, [fp, #-0x10]
    // 0xdc1ddc: r2 = "transaction"
    //     0xdc1ddc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4b0] "transaction"
    //     0xdc1de0: ldr             x2, [x2, #0x4b0]
    // 0xdc1de4: r0 = add()
    //     0xdc1de4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1de8: ldur            x1, [fp, #-0x10]
    // 0xdc1dec: r2 = "trigger"
    //     0xdc1dec: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4b8] "trigger"
    //     0xdc1df0: ldr             x2, [x2, #0x4b8]
    // 0xdc1df4: r0 = add()
    //     0xdc1df4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1df8: ldur            x1, [fp, #-0x10]
    // 0xdc1dfc: r2 = "unbounded"
    //     0xdc1dfc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4c0] "unbounded"
    //     0xdc1e00: ldr             x2, [x2, #0x4c0]
    // 0xdc1e04: r0 = add()
    //     0xdc1e04: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e08: ldur            x1, [fp, #-0x10]
    // 0xdc1e0c: r2 = "union"
    //     0xdc1e0c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4c8] "union"
    //     0xdc1e10: ldr             x2, [x2, #0x4c8]
    // 0xdc1e14: r0 = add()
    //     0xdc1e14: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e18: ldur            x1, [fp, #-0x10]
    // 0xdc1e1c: r2 = "unique"
    //     0xdc1e1c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4d0] "unique"
    //     0xdc1e20: ldr             x2, [x2, #0x4d0]
    // 0xdc1e24: r0 = add()
    //     0xdc1e24: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e28: ldur            x1, [fp, #-0x10]
    // 0xdc1e2c: r2 = "update"
    //     0xdc1e2c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4d8] "update"
    //     0xdc1e30: ldr             x2, [x2, #0x4d8]
    // 0xdc1e34: r0 = add()
    //     0xdc1e34: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e38: ldur            x1, [fp, #-0x10]
    // 0xdc1e3c: r2 = "using"
    //     0xdc1e3c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4e0] "using"
    //     0xdc1e40: ldr             x2, [x2, #0x4e0]
    // 0xdc1e44: r0 = add()
    //     0xdc1e44: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e48: ldur            x1, [fp, #-0x10]
    // 0xdc1e4c: r2 = "vacuum"
    //     0xdc1e4c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4e8] "vacuum"
    //     0xdc1e50: ldr             x2, [x2, #0x4e8]
    // 0xdc1e54: r0 = add()
    //     0xdc1e54: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e58: ldur            x1, [fp, #-0x10]
    // 0xdc1e5c: r2 = "values"
    //     0xdc1e5c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4f0] "values"
    //     0xdc1e60: ldr             x2, [x2, #0x4f0]
    // 0xdc1e64: r0 = add()
    //     0xdc1e64: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e68: ldur            x1, [fp, #-0x10]
    // 0xdc1e6c: r2 = "view"
    //     0xdc1e6c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d4f8] "view"
    //     0xdc1e70: ldr             x2, [x2, #0x4f8]
    // 0xdc1e74: r0 = add()
    //     0xdc1e74: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e78: ldur            x1, [fp, #-0x10]
    // 0xdc1e7c: r2 = "virtual"
    //     0xdc1e7c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d500] "virtual"
    //     0xdc1e80: ldr             x2, [x2, #0x500]
    // 0xdc1e84: r0 = add()
    //     0xdc1e84: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e88: ldur            x1, [fp, #-0x10]
    // 0xdc1e8c: r2 = "when"
    //     0xdc1e8c: add             x2, PP, #8, lsl #12  ; [pp+0x88e0] "when"
    //     0xdc1e90: ldr             x2, [x2, #0x8e0]
    // 0xdc1e94: r0 = add()
    //     0xdc1e94: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1e98: ldur            x1, [fp, #-0x10]
    // 0xdc1e9c: r2 = "where"
    //     0xdc1e9c: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d508] "where"
    //     0xdc1ea0: ldr             x2, [x2, #0x508]
    // 0xdc1ea4: r0 = add()
    //     0xdc1ea4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ea8: ldur            x1, [fp, #-0x10]
    // 0xdc1eac: r2 = "window"
    //     0xdc1eac: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d510] "window"
    //     0xdc1eb0: ldr             x2, [x2, #0x510]
    // 0xdc1eb4: r0 = add()
    //     0xdc1eb4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1eb8: ldur            x1, [fp, #-0x10]
    // 0xdc1ebc: r2 = "with"
    //     0xdc1ebc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d518] "with"
    //     0xdc1ec0: ldr             x2, [x2, #0x518]
    // 0xdc1ec4: r0 = add()
    //     0xdc1ec4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ec8: ldur            x1, [fp, #-0x10]
    // 0xdc1ecc: r2 = "without"
    //     0xdc1ecc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d520] "without"
    //     0xdc1ed0: ldr             x2, [x2, #0x520]
    // 0xdc1ed4: r0 = add()
    //     0xdc1ed4: bl              #0xe548f0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xdc1ed8: ldur            x0, [fp, #-0x10]
    // 0xdc1edc: LeaveFrame
    //     0xdc1edc: mov             SP, fp
    //     0xdc1ee0: ldp             fp, lr, [SP], #0x10
    // 0xdc1ee4: ret
    //     0xdc1ee4: ret             
    // 0xdc1ee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc1ee8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc1eec: b               #0xdc1564
  }
}

// class id: 463, size: 0x10, field offset: 0x8
class SqlBuilder extends Object {

  late String sql; // offset: 0x8

  _ SqlBuilder.update(/* No info */) {
    // ** addr: 0xdc0fcc, size: 0x420
    // 0xdc0fcc: EnterFrame
    //     0xdc0fcc: stp             fp, lr, [SP, #-0x10]!
    //     0xdc0fd0: mov             fp, SP
    // 0xdc0fd4: AllocStack(0x58)
    //     0xdc0fd4: sub             SP, SP, #0x58
    // 0xdc0fd8: r0 = Sentinel
    //     0xdc0fd8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc0fdc: stur            x1, [fp, #-8]
    // 0xdc0fe0: mov             x16, x2
    // 0xdc0fe4: mov             x2, x1
    // 0xdc0fe8: mov             x1, x16
    // 0xdc0fec: mov             x16, x3
    // 0xdc0ff0: mov             x3, x2
    // 0xdc0ff4: mov             x2, x16
    // 0xdc0ff8: stur            x1, [fp, #-0x10]
    // 0xdc0ffc: stur            x2, [fp, #-0x18]
    // 0xdc1000: CheckStackOverflow
    //     0xdc1000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc1004: cmp             SP, x16
    //     0xdc1008: b.ls            #0xdc13dc
    // 0xdc100c: StoreField: r3->field_7 = r0
    //     0xdc100c: stur            w0, [x3, #7]
    // 0xdc1010: LoadField: r0 = r1->field_13
    //     0xdc1010: ldur            w0, [x1, #0x13]
    // 0xdc1014: r4 = LoadInt32Instr(r0)
    //     0xdc1014: sbfx            x4, x0, #1, #0x1f
    // 0xdc1018: asr             x0, x4, #1
    // 0xdc101c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xdc101c: ldur            w4, [x1, #0x17]
    // 0xdc1020: r5 = LoadInt32Instr(r4)
    //     0xdc1020: sbfx            x5, x4, #1, #0x1f
    // 0xdc1024: sub             x4, x0, x5
    // 0xdc1028: cbz             x4, #0xdc13b4
    // 0xdc102c: r0 = LoadStaticField(0x175c)
    //     0xdc102c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc1030: ldr             x0, [x0, #0x2eb8]
    // 0xdc1034: cmp             w0, NULL
    // 0xdc1038: b.ne            #0xdc104c
    // 0xdc103c: r0 = true
    //     0xdc103c: add             x0, NULL, #0x20  ; true
    // 0xdc1040: StoreStaticField(0x175c, r0)
    //     0xdc1040: ldr             x4, [THR, #0x68]  ; THR::field_table_values
    //     0xdc1044: str             x0, [x4, #0x2eb8]
    // 0xdc1048: b               #0xdc1050
    // 0xdc104c: r0 = true
    //     0xdc104c: add             x0, NULL, #0x20  ; true
    // 0xdc1050: r0 = StringBuffer()
    //     0xdc1050: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xdc1054: mov             x1, x0
    // 0xdc1058: stur            x0, [fp, #-0x20]
    // 0xdc105c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdc105c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdc1060: r0 = StringBuffer()
    //     0xdc1060: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xdc1064: ldur            x1, [fp, #-0x20]
    // 0xdc1068: r2 = "UPDATE"
    //     0xdc1068: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d570] "UPDATE"
    //     0xdc106c: ldr             x2, [x2, #0x570]
    // 0xdc1070: r0 = write()
    //     0xdc1070: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc1074: r1 = Null
    //     0xdc1074: mov             x1, NULL
    // 0xdc1078: r2 = 4
    //     0xdc1078: movz            x2, #0x4
    // 0xdc107c: r0 = AllocateArray()
    //     0xdc107c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdc1080: stur            x0, [fp, #-0x28]
    // 0xdc1084: r16 = " "
    //     0xdc1084: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xdc1088: StoreField: r0->field_f = r16
    //     0xdc1088: stur            w16, [x0, #0xf]
    // 0xdc108c: r1 = "cacheObject"
    //     0xdc108c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d0e0] "cacheObject"
    //     0xdc1090: ldr             x1, [x1, #0xe0]
    // 0xdc1094: r0 = escapeName()
    //     0xdc1094: bl              #0xdc1450  ; [package:sqflite_common/src/sql_builder.dart] ::escapeName
    // 0xdc1098: ldur            x1, [fp, #-0x28]
    // 0xdc109c: ArrayStore: r1[1] = r0  ; List_4
    //     0xdc109c: add             x25, x1, #0x13
    //     0xdc10a0: str             w0, [x25]
    //     0xdc10a4: tbz             w0, #0, #0xdc10c0
    //     0xdc10a8: ldurb           w16, [x1, #-1]
    //     0xdc10ac: ldurb           w17, [x0, #-1]
    //     0xdc10b0: and             x16, x17, x16, lsr #2
    //     0xdc10b4: tst             x16, HEAP, lsr #32
    //     0xdc10b8: b.eq            #0xdc10c0
    //     0xdc10bc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xdc10c0: ldur            x16, [fp, #-0x28]
    // 0xdc10c4: str             x16, [SP]
    // 0xdc10c8: r0 = _interpolate()
    //     0xdc10c8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xdc10cc: ldur            x1, [fp, #-0x20]
    // 0xdc10d0: mov             x2, x0
    // 0xdc10d4: r0 = write()
    //     0xdc10d4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc10d8: ldur            x1, [fp, #-0x20]
    // 0xdc10dc: r2 = " SET "
    //     0xdc10dc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d578] " SET "
    //     0xdc10e0: ldr             x2, [x2, #0x578]
    // 0xdc10e4: r0 = write()
    //     0xdc10e4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc10e8: r1 = <Object?>
    //     0xdc10e8: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xdc10ec: r2 = 0
    //     0xdc10ec: movz            x2, #0
    // 0xdc10f0: r0 = _GrowableList()
    //     0xdc10f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xdc10f4: mov             x2, x0
    // 0xdc10f8: ldur            x0, [fp, #-0x10]
    // 0xdc10fc: stur            x2, [fp, #-0x28]
    // 0xdc1100: LoadField: r1 = r0->field_7
    //     0xdc1100: ldur            w1, [x0, #7]
    // 0xdc1104: DecompressPointer r1
    //     0xdc1104: add             x1, x1, HEAP, lsl #32
    // 0xdc1108: r0 = _CompactIterable()
    //     0xdc1108: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xdc110c: mov             x1, x0
    // 0xdc1110: ldur            x0, [fp, #-0x10]
    // 0xdc1114: StoreField: r1->field_b = r0
    //     0xdc1114: stur            w0, [x1, #0xb]
    // 0xdc1118: r2 = -2
    //     0xdc1118: orr             x2, xzr, #0xfffffffffffffffe
    // 0xdc111c: StoreField: r1->field_f = r2
    //     0xdc111c: stur            x2, [x1, #0xf]
    // 0xdc1120: r2 = 2
    //     0xdc1120: movz            x2, #0x2
    // 0xdc1124: ArrayStore: r1[0] = r2  ; List_8
    //     0xdc1124: stur            x2, [x1, #0x17]
    // 0xdc1128: r0 = iterator()
    //     0xdc1128: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0xdc112c: stur            x0, [fp, #-0x40]
    // 0xdc1130: LoadField: r2 = r0->field_7
    //     0xdc1130: ldur            w2, [x0, #7]
    // 0xdc1134: DecompressPointer r2
    //     0xdc1134: add             x2, x2, HEAP, lsl #32
    // 0xdc1138: stur            x2, [fp, #-0x38]
    // 0xdc113c: r5 = 0
    //     0xdc113c: movz            x5, #0
    // 0xdc1140: ldur            x3, [fp, #-0x10]
    // 0xdc1144: ldur            x4, [fp, #-0x28]
    // 0xdc1148: stur            x5, [fp, #-0x30]
    // 0xdc114c: CheckStackOverflow
    //     0xdc114c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc1150: cmp             SP, x16
    //     0xdc1154: b.ls            #0xdc13e4
    // 0xdc1158: mov             x1, x0
    // 0xdc115c: r0 = moveNext()
    //     0xdc115c: bl              #0x68824c  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0xdc1160: tbnz            w0, #4, #0xdc132c
    // 0xdc1164: ldur            x3, [fp, #-0x40]
    // 0xdc1168: LoadField: r4 = r3->field_33
    //     0xdc1168: ldur            w4, [x3, #0x33]
    // 0xdc116c: DecompressPointer r4
    //     0xdc116c: add             x4, x4, HEAP, lsl #32
    // 0xdc1170: stur            x4, [fp, #-0x48]
    // 0xdc1174: cmp             w4, NULL
    // 0xdc1178: b.ne            #0xdc11ac
    // 0xdc117c: mov             x0, x4
    // 0xdc1180: ldur            x2, [fp, #-0x38]
    // 0xdc1184: r1 = Null
    //     0xdc1184: mov             x1, NULL
    // 0xdc1188: cmp             w2, NULL
    // 0xdc118c: b.eq            #0xdc11ac
    // 0xdc1190: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xdc1190: ldur            w4, [x2, #0x17]
    // 0xdc1194: DecompressPointer r4
    //     0xdc1194: add             x4, x4, HEAP, lsl #32
    // 0xdc1198: r8 = X0
    //     0xdc1198: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xdc119c: LoadField: r9 = r4->field_7
    //     0xdc119c: ldur            x9, [x4, #7]
    // 0xdc11a0: r3 = Null
    //     0xdc11a0: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d580] Null
    //     0xdc11a4: ldr             x3, [x3, #0x580]
    // 0xdc11a8: blr             x9
    // 0xdc11ac: ldur            x0, [fp, #-0x30]
    // 0xdc11b0: add             x5, x0, #1
    // 0xdc11b4: stur            x5, [fp, #-0x50]
    // 0xdc11b8: cmp             x0, #0
    // 0xdc11bc: b.le            #0xdc11c8
    // 0xdc11c0: r2 = ", "
    //     0xdc11c0: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xdc11c4: b               #0xdc11cc
    // 0xdc11c8: r2 = ""
    //     0xdc11c8: ldr             x2, [PP, #0x288]  ; [pp+0x288] ""
    // 0xdc11cc: LoadField: r0 = r2->field_7
    //     0xdc11cc: ldur            w0, [x2, #7]
    // 0xdc11d0: cbz             w0, #0xdc11dc
    // 0xdc11d4: ldur            x1, [fp, #-0x20]
    // 0xdc11d8: r0 = _writeString()
    //     0xdc11d8: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xdc11dc: ldur            x1, [fp, #-0x48]
    // 0xdc11e0: r0 = escapeName()
    //     0xdc11e0: bl              #0xdc1450  ; [package:sqflite_common/src/sql_builder.dart] ::escapeName
    // 0xdc11e4: r1 = LoadClassIdInstr(r0)
    //     0xdc11e4: ldur            x1, [x0, #-1]
    //     0xdc11e8: ubfx            x1, x1, #0xc, #0x14
    // 0xdc11ec: str             x0, [SP]
    // 0xdc11f0: mov             x0, x1
    // 0xdc11f4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xdc11f4: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xdc11f8: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xdc11f8: movz            x17, #0x2b03
    //     0xdc11fc: add             lr, x0, x17
    //     0xdc1200: ldr             lr, [x21, lr, lsl #3]
    //     0xdc1204: blr             lr
    // 0xdc1208: LoadField: r1 = r0->field_7
    //     0xdc1208: ldur            w1, [x0, #7]
    // 0xdc120c: cbz             w1, #0xdc121c
    // 0xdc1210: ldur            x1, [fp, #-0x20]
    // 0xdc1214: mov             x2, x0
    // 0xdc1218: r0 = _writeString()
    //     0xdc1218: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xdc121c: ldur            x0, [fp, #-0x10]
    // 0xdc1220: mov             x1, x0
    // 0xdc1224: ldur            x2, [fp, #-0x48]
    // 0xdc1228: r0 = _getValueOrData()
    //     0xdc1228: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xdc122c: mov             x1, x0
    // 0xdc1230: ldur            x0, [fp, #-0x10]
    // 0xdc1234: LoadField: r2 = r0->field_f
    //     0xdc1234: ldur            w2, [x0, #0xf]
    // 0xdc1238: DecompressPointer r2
    //     0xdc1238: add             x2, x2, HEAP, lsl #32
    // 0xdc123c: cmp             w2, w1
    // 0xdc1240: b.ne            #0xdc124c
    // 0xdc1244: r2 = Null
    //     0xdc1244: mov             x2, NULL
    // 0xdc1248: b               #0xdc1250
    // 0xdc124c: mov             x2, x1
    // 0xdc1250: stur            x2, [fp, #-0x48]
    // 0xdc1254: cmp             w2, NULL
    // 0xdc1258: b.eq            #0xdc130c
    // 0xdc125c: r1 = LoadStaticField(0x175c)
    //     0xdc125c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xdc1260: ldr             x1, [x1, #0x2eb8]
    // 0xdc1264: cmp             w1, NULL
    // 0xdc1268: b.ne            #0xdc127c
    // 0xdc126c: r3 = true
    //     0xdc126c: add             x3, NULL, #0x20  ; true
    // 0xdc1270: StoreStaticField(0x175c, r3)
    //     0xdc1270: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xdc1274: str             x3, [x1, #0x2eb8]
    // 0xdc1278: b               #0xdc1280
    // 0xdc127c: r3 = true
    //     0xdc127c: add             x3, NULL, #0x20  ; true
    // 0xdc1280: ldur            x4, [fp, #-0x28]
    // 0xdc1284: LoadField: r1 = r4->field_b
    //     0xdc1284: ldur            w1, [x4, #0xb]
    // 0xdc1288: LoadField: r5 = r4->field_f
    //     0xdc1288: ldur            w5, [x4, #0xf]
    // 0xdc128c: DecompressPointer r5
    //     0xdc128c: add             x5, x5, HEAP, lsl #32
    // 0xdc1290: LoadField: r6 = r5->field_b
    //     0xdc1290: ldur            w6, [x5, #0xb]
    // 0xdc1294: r5 = LoadInt32Instr(r1)
    //     0xdc1294: sbfx            x5, x1, #1, #0x1f
    // 0xdc1298: stur            x5, [fp, #-0x30]
    // 0xdc129c: r1 = LoadInt32Instr(r6)
    //     0xdc129c: sbfx            x1, x6, #1, #0x1f
    // 0xdc12a0: cmp             x5, x1
    // 0xdc12a4: b.ne            #0xdc12b0
    // 0xdc12a8: mov             x1, x4
    // 0xdc12ac: r0 = _growToNextCapacity()
    //     0xdc12ac: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xdc12b0: ldur            x3, [fp, #-0x28]
    // 0xdc12b4: ldur            x2, [fp, #-0x30]
    // 0xdc12b8: add             x0, x2, #1
    // 0xdc12bc: lsl             x1, x0, #1
    // 0xdc12c0: StoreField: r3->field_b = r1
    //     0xdc12c0: stur            w1, [x3, #0xb]
    // 0xdc12c4: LoadField: r1 = r3->field_f
    //     0xdc12c4: ldur            w1, [x3, #0xf]
    // 0xdc12c8: DecompressPointer r1
    //     0xdc12c8: add             x1, x1, HEAP, lsl #32
    // 0xdc12cc: ldur            x0, [fp, #-0x48]
    // 0xdc12d0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xdc12d0: add             x25, x1, x2, lsl #2
    //     0xdc12d4: add             x25, x25, #0xf
    //     0xdc12d8: str             w0, [x25]
    //     0xdc12dc: tbz             w0, #0, #0xdc12f8
    //     0xdc12e0: ldurb           w16, [x1, #-1]
    //     0xdc12e4: ldurb           w17, [x0, #-1]
    //     0xdc12e8: and             x16, x17, x16, lsr #2
    //     0xdc12ec: tst             x16, HEAP, lsr #32
    //     0xdc12f0: b.eq            #0xdc12f8
    //     0xdc12f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xdc12f8: ldur            x1, [fp, #-0x20]
    // 0xdc12fc: r2 = " = \?"
    //     0xdc12fc: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d590] " = \?"
    //     0xdc1300: ldr             x2, [x2, #0x590]
    // 0xdc1304: r0 = _writeString()
    //     0xdc1304: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xdc1308: b               #0xdc131c
    // 0xdc130c: ldur            x1, [fp, #-0x20]
    // 0xdc1310: r2 = " = NULL"
    //     0xdc1310: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d598] " = NULL"
    //     0xdc1314: ldr             x2, [x2, #0x598]
    // 0xdc1318: r0 = _writeString()
    //     0xdc1318: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xdc131c: ldur            x5, [fp, #-0x50]
    // 0xdc1320: ldur            x0, [fp, #-0x40]
    // 0xdc1324: ldur            x2, [fp, #-0x38]
    // 0xdc1328: b               #0xdc1140
    // 0xdc132c: ldur            x0, [fp, #-8]
    // 0xdc1330: ldur            x1, [fp, #-0x28]
    // 0xdc1334: ldur            x2, [fp, #-0x18]
    // 0xdc1338: r0 = addAll()
    //     0xdc1338: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xdc133c: ldur            x1, [fp, #-8]
    // 0xdc1340: ldur            x2, [fp, #-0x20]
    // 0xdc1344: r3 = " WHERE "
    //     0xdc1344: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d1d0] " WHERE "
    //     0xdc1348: ldr             x3, [x3, #0x1d0]
    // 0xdc134c: r5 = "_id = \?"
    //     0xdc134c: add             x5, PP, #0x4d, lsl #12  ; [pp+0x4d558] "_id = \?"
    //     0xdc1350: ldr             x5, [x5, #0x558]
    // 0xdc1354: r0 = _writeClause()
    //     0xdc1354: bl              #0xdc13ec  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::_writeClause
    // 0xdc1358: ldur            x16, [fp, #-0x20]
    // 0xdc135c: str             x16, [SP]
    // 0xdc1360: r0 = toString()
    //     0xdc1360: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xdc1364: ldur            x1, [fp, #-8]
    // 0xdc1368: StoreField: r1->field_7 = r0
    //     0xdc1368: stur            w0, [x1, #7]
    //     0xdc136c: ldurb           w16, [x1, #-1]
    //     0xdc1370: ldurb           w17, [x0, #-1]
    //     0xdc1374: and             x16, x17, x16, lsr #2
    //     0xdc1378: tst             x16, HEAP, lsr #32
    //     0xdc137c: b.eq            #0xdc1384
    //     0xdc1380: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdc1384: ldur            x0, [fp, #-0x28]
    // 0xdc1388: StoreField: r1->field_b = r0
    //     0xdc1388: stur            w0, [x1, #0xb]
    //     0xdc138c: ldurb           w16, [x1, #-1]
    //     0xdc1390: ldurb           w17, [x0, #-1]
    //     0xdc1394: and             x16, x17, x16, lsr #2
    //     0xdc1398: tst             x16, HEAP, lsr #32
    //     0xdc139c: b.eq            #0xdc13a4
    //     0xdc13a0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdc13a4: r0 = Null
    //     0xdc13a4: mov             x0, NULL
    // 0xdc13a8: LeaveFrame
    //     0xdc13a8: mov             SP, fp
    //     0xdc13ac: ldp             fp, lr, [SP], #0x10
    // 0xdc13b0: ret
    //     0xdc13b0: ret             
    // 0xdc13b4: r0 = ArgumentError()
    //     0xdc13b4: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xdc13b8: mov             x1, x0
    // 0xdc13bc: r0 = "Empty values"
    //     0xdc13bc: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4d5a0] "Empty values"
    //     0xdc13c0: ldr             x0, [x0, #0x5a0]
    // 0xdc13c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xdc13c4: stur            w0, [x1, #0x17]
    // 0xdc13c8: r0 = false
    //     0xdc13c8: add             x0, NULL, #0x30  ; false
    // 0xdc13cc: StoreField: r1->field_b = r0
    //     0xdc13cc: stur            w0, [x1, #0xb]
    // 0xdc13d0: mov             x0, x1
    // 0xdc13d4: r0 = Throw()
    //     0xdc13d4: bl              #0xec04b8  ; ThrowStub
    // 0xdc13d8: brk             #0
    // 0xdc13dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc13dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc13e0: b               #0xdc100c
    // 0xdc13e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc13e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc13e8: b               #0xdc1158
  }
  _ _writeClause(/* No info */) {
    // ** addr: 0xdc13ec, size: 0x64
    // 0xdc13ec: EnterFrame
    //     0xdc13ec: stp             fp, lr, [SP, #-0x10]!
    //     0xdc13f0: mov             fp, SP
    // 0xdc13f4: AllocStack(0x10)
    //     0xdc13f4: sub             SP, SP, #0x10
    // 0xdc13f8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r2 */, dynamic _ /* r5 => r0, fp-0x10 */)
    //     0xdc13f8: stur            x2, [fp, #-8]
    //     0xdc13fc: mov             x16, x3
    //     0xdc1400: mov             x3, x2
    //     0xdc1404: mov             x2, x16
    //     0xdc1408: mov             x0, x5
    //     0xdc140c: stur            x5, [fp, #-0x10]
    // 0xdc1410: CheckStackOverflow
    //     0xdc1410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc1414: cmp             SP, x16
    //     0xdc1418: b.ls            #0xdc1448
    // 0xdc141c: cmp             w0, NULL
    // 0xdc1420: b.eq            #0xdc1438
    // 0xdc1424: mov             x1, x3
    // 0xdc1428: r0 = write()
    //     0xdc1428: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc142c: ldur            x1, [fp, #-8]
    // 0xdc1430: ldur            x2, [fp, #-0x10]
    // 0xdc1434: r0 = write()
    //     0xdc1434: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc1438: r0 = Null
    //     0xdc1438: mov             x0, NULL
    // 0xdc143c: LeaveFrame
    //     0xdc143c: mov             SP, fp
    //     0xdc1440: ldp             fp, lr, [SP], #0x10
    // 0xdc1444: ret
    //     0xdc1444: ret             
    // 0xdc1448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc1448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc144c: b               #0xdc141c
  }
  _ SqlBuilder.insert(/* No info */) {
    // ** addr: 0xdc236c, size: 0x234
    // 0xdc236c: EnterFrame
    //     0xdc236c: stp             fp, lr, [SP, #-0x10]!
    //     0xdc2370: mov             fp, SP
    // 0xdc2374: AllocStack(0x30)
    //     0xdc2374: sub             SP, SP, #0x30
    // 0xdc2378: SetupParameters(SqlBuilder this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xdc2378: mov             x0, x1
    //     0xdc237c: stur            x1, [fp, #-8]
    //     0xdc2380: mov             x1, x2
    //     0xdc2384: stur            x2, [fp, #-0x10]
    // 0xdc2388: CheckStackOverflow
    //     0xdc2388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc238c: cmp             SP, x16
    //     0xdc2390: b.ls            #0xdc2598
    // 0xdc2394: r1 = 5
    //     0xdc2394: movz            x1, #0x5
    // 0xdc2398: r0 = AllocateContext()
    //     0xdc2398: bl              #0xec126c  ; AllocateContextStub
    // 0xdc239c: mov             x1, x0
    // 0xdc23a0: ldur            x0, [fp, #-8]
    // 0xdc23a4: stur            x1, [fp, #-0x18]
    // 0xdc23a8: StoreField: r1->field_f = r0
    //     0xdc23a8: stur            w0, [x1, #0xf]
    // 0xdc23ac: r2 = Sentinel
    //     0xdc23ac: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc23b0: StoreField: r0->field_7 = r2
    //     0xdc23b0: stur            w2, [x0, #7]
    // 0xdc23b4: r0 = StringBuffer()
    //     0xdc23b4: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xdc23b8: mov             x1, x0
    // 0xdc23bc: stur            x0, [fp, #-0x20]
    // 0xdc23c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdc23c0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdc23c4: r0 = StringBuffer()
    //     0xdc23c4: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xdc23c8: ldur            x0, [fp, #-0x20]
    // 0xdc23cc: ldur            x3, [fp, #-0x18]
    // 0xdc23d0: StoreField: r3->field_13 = r0
    //     0xdc23d0: stur            w0, [x3, #0x13]
    //     0xdc23d4: ldurb           w16, [x3, #-1]
    //     0xdc23d8: ldurb           w17, [x0, #-1]
    //     0xdc23dc: and             x16, x17, x16, lsr #2
    //     0xdc23e0: tst             x16, HEAP, lsr #32
    //     0xdc23e4: b.eq            #0xdc23ec
    //     0xdc23e8: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xdc23ec: ldur            x1, [fp, #-0x20]
    // 0xdc23f0: r2 = "INSERT"
    //     0xdc23f0: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d5b0] "INSERT"
    //     0xdc23f4: ldr             x2, [x2, #0x5b0]
    // 0xdc23f8: r0 = write()
    //     0xdc23f8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc23fc: ldur            x1, [fp, #-0x20]
    // 0xdc2400: r2 = " INTO "
    //     0xdc2400: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d5b8] " INTO "
    //     0xdc2404: ldr             x2, [x2, #0x5b8]
    // 0xdc2408: r0 = write()
    //     0xdc2408: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc240c: r1 = "cacheObject"
    //     0xdc240c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d0e0] "cacheObject"
    //     0xdc2410: ldr             x1, [x1, #0xe0]
    // 0xdc2414: r0 = escapeName()
    //     0xdc2414: bl              #0xdc1450  ; [package:sqflite_common/src/sql_builder.dart] ::escapeName
    // 0xdc2418: ldur            x1, [fp, #-0x20]
    // 0xdc241c: mov             x2, x0
    // 0xdc2420: r0 = write()
    //     0xdc2420: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc2424: ldur            x1, [fp, #-0x20]
    // 0xdc2428: r2 = " ("
    //     0xdc2428: ldr             x2, [PP, #0x980]  ; [pp+0x980] " ("
    // 0xdc242c: r0 = write()
    //     0xdc242c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc2430: ldur            x2, [fp, #-0x18]
    // 0xdc2434: ArrayStore: r2[0] = rNULL  ; List_4
    //     0xdc2434: stur            NULL, [x2, #0x17]
    // 0xdc2438: ldur            x1, [fp, #-0x10]
    // 0xdc243c: LoadField: r0 = r1->field_13
    //     0xdc243c: ldur            w0, [x1, #0x13]
    // 0xdc2440: r3 = LoadInt32Instr(r0)
    //     0xdc2440: sbfx            x3, x0, #1, #0x1f
    // 0xdc2444: asr             x0, x3, #1
    // 0xdc2448: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xdc2448: ldur            w3, [x1, #0x17]
    // 0xdc244c: r4 = LoadInt32Instr(r3)
    //     0xdc244c: sbfx            x4, x3, #1, #0x1f
    // 0xdc2450: sub             x3, x0, x4
    // 0xdc2454: cmp             x3, #0
    // 0xdc2458: b.le            #0xdc2570
    // 0xdc245c: ldur            x0, [fp, #-8]
    // 0xdc2460: r0 = StringBuffer()
    //     0xdc2460: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xdc2464: stur            x0, [fp, #-0x28]
    // 0xdc2468: r16 = ") VALUES ("
    //     0xdc2468: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d5c0] ") VALUES ("
    //     0xdc246c: ldr             x16, [x16, #0x5c0]
    // 0xdc2470: str             x16, [SP]
    // 0xdc2474: mov             x1, x0
    // 0xdc2478: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xdc2478: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xdc247c: r0 = StringBuffer()
    //     0xdc247c: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xdc2480: ldur            x0, [fp, #-0x28]
    // 0xdc2484: ldur            x3, [fp, #-0x18]
    // 0xdc2488: StoreField: r3->field_1b = r0
    //     0xdc2488: stur            w0, [x3, #0x1b]
    //     0xdc248c: ldurb           w16, [x3, #-1]
    //     0xdc2490: ldurb           w17, [x0, #-1]
    //     0xdc2494: and             x16, x17, x16, lsr #2
    //     0xdc2498: tst             x16, HEAP, lsr #32
    //     0xdc249c: b.eq            #0xdc24a4
    //     0xdc24a0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xdc24a4: r1 = <Object?>
    //     0xdc24a4: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xdc24a8: r2 = 0
    //     0xdc24a8: movz            x2, #0
    // 0xdc24ac: r0 = _GrowableList()
    //     0xdc24ac: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xdc24b0: ldur            x3, [fp, #-0x18]
    // 0xdc24b4: ArrayStore: r3[0] = r0  ; List_4
    //     0xdc24b4: stur            w0, [x3, #0x17]
    //     0xdc24b8: ldurb           w16, [x3, #-1]
    //     0xdc24bc: ldurb           w17, [x0, #-1]
    //     0xdc24c0: and             x16, x17, x16, lsr #2
    //     0xdc24c4: tst             x16, HEAP, lsr #32
    //     0xdc24c8: b.eq            #0xdc24d0
    //     0xdc24cc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xdc24d0: StoreField: r3->field_1f = rZR
    //     0xdc24d0: stur            wzr, [x3, #0x1f]
    // 0xdc24d4: mov             x2, x3
    // 0xdc24d8: r1 = Function '<anonymous closure>':.
    //     0xdc24d8: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d5c8] AnonymousClosure: (0xdc25a0), in [package:sqflite_common/src/sql_builder.dart] SqlBuilder::SqlBuilder.insert (0xdc236c)
    //     0xdc24dc: ldr             x1, [x1, #0x5c8]
    // 0xdc24e0: r0 = AllocateClosure()
    //     0xdc24e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xdc24e4: ldur            x1, [fp, #-0x10]
    // 0xdc24e8: mov             x2, x0
    // 0xdc24ec: r0 = forEach()
    //     0xdc24ec: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xdc24f0: ldur            x1, [fp, #-0x20]
    // 0xdc24f4: ldur            x2, [fp, #-0x28]
    // 0xdc24f8: r0 = write()
    //     0xdc24f8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc24fc: ldur            x1, [fp, #-0x20]
    // 0xdc2500: r2 = ")"
    //     0xdc2500: ldr             x2, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xdc2504: r0 = write()
    //     0xdc2504: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc2508: ldur            x16, [fp, #-0x20]
    // 0xdc250c: str             x16, [SP]
    // 0xdc2510: r0 = toString()
    //     0xdc2510: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xdc2514: ldur            x1, [fp, #-8]
    // 0xdc2518: StoreField: r1->field_7 = r0
    //     0xdc2518: stur            w0, [x1, #7]
    //     0xdc251c: ldurb           w16, [x1, #-1]
    //     0xdc2520: ldurb           w17, [x0, #-1]
    //     0xdc2524: and             x16, x17, x16, lsr #2
    //     0xdc2528: tst             x16, HEAP, lsr #32
    //     0xdc252c: b.eq            #0xdc2534
    //     0xdc2530: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdc2534: ldur            x0, [fp, #-0x18]
    // 0xdc2538: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xdc2538: ldur            w2, [x0, #0x17]
    // 0xdc253c: DecompressPointer r2
    //     0xdc253c: add             x2, x2, HEAP, lsl #32
    // 0xdc2540: mov             x0, x2
    // 0xdc2544: StoreField: r1->field_b = r0
    //     0xdc2544: stur            w0, [x1, #0xb]
    //     0xdc2548: ldurb           w16, [x1, #-1]
    //     0xdc254c: ldurb           w17, [x0, #-1]
    //     0xdc2550: and             x16, x17, x16, lsr #2
    //     0xdc2554: tst             x16, HEAP, lsr #32
    //     0xdc2558: b.eq            #0xdc2560
    //     0xdc255c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdc2560: r0 = Null
    //     0xdc2560: mov             x0, NULL
    // 0xdc2564: LeaveFrame
    //     0xdc2564: mov             SP, fp
    //     0xdc2568: ldp             fp, lr, [SP], #0x10
    // 0xdc256c: ret
    //     0xdc256c: ret             
    // 0xdc2570: r0 = ArgumentError()
    //     0xdc2570: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xdc2574: mov             x1, x0
    // 0xdc2578: r0 = "nullColumnHack required when inserting no data"
    //     0xdc2578: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4d5d0] "nullColumnHack required when inserting no data"
    //     0xdc257c: ldr             x0, [x0, #0x5d0]
    // 0xdc2580: ArrayStore: r1[0] = r0  ; List_4
    //     0xdc2580: stur            w0, [x1, #0x17]
    // 0xdc2584: r0 = false
    //     0xdc2584: add             x0, NULL, #0x30  ; false
    // 0xdc2588: StoreField: r1->field_b = r0
    //     0xdc2588: stur            w0, [x1, #0xb]
    // 0xdc258c: mov             x0, x1
    // 0xdc2590: r0 = Throw()
    //     0xdc2590: bl              #0xec04b8  ; ThrowStub
    // 0xdc2594: brk             #0
    // 0xdc2598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc2598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc259c: b               #0xdc2394
  }
  [closure] void <anonymous closure>(dynamic, String, Object?) {
    // ** addr: 0xdc25a0, size: 0x200
    // 0xdc25a0: EnterFrame
    //     0xdc25a0: stp             fp, lr, [SP, #-0x10]!
    //     0xdc25a4: mov             fp, SP
    // 0xdc25a8: AllocStack(0x18)
    //     0xdc25a8: sub             SP, SP, #0x18
    // 0xdc25ac: SetupParameters()
    //     0xdc25ac: ldr             x0, [fp, #0x20]
    //     0xdc25b0: ldur            w3, [x0, #0x17]
    //     0xdc25b4: add             x3, x3, HEAP, lsl #32
    //     0xdc25b8: stur            x3, [fp, #-8]
    // 0xdc25bc: CheckStackOverflow
    //     0xdc25bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc25c0: cmp             SP, x16
    //     0xdc25c4: b.ls            #0xdc2794
    // 0xdc25c8: LoadField: r0 = r3->field_1f
    //     0xdc25c8: ldur            w0, [x3, #0x1f]
    // 0xdc25cc: DecompressPointer r0
    //     0xdc25cc: add             x0, x0, HEAP, lsl #32
    // 0xdc25d0: r2 = LoadInt32Instr(r0)
    //     0xdc25d0: sbfx            x2, x0, #1, #0x1f
    //     0xdc25d4: tbz             w0, #0, #0xdc25dc
    //     0xdc25d8: ldur            x2, [x0, #7]
    // 0xdc25dc: add             x4, x2, #1
    // 0xdc25e0: r0 = BoxInt64Instr(r4)
    //     0xdc25e0: sbfiz           x0, x4, #1, #0x1f
    //     0xdc25e4: cmp             x4, x0, asr #1
    //     0xdc25e8: b.eq            #0xdc25f4
    //     0xdc25ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xdc25f0: stur            x4, [x0, #7]
    // 0xdc25f4: StoreField: r3->field_1f = r0
    //     0xdc25f4: stur            w0, [x3, #0x1f]
    //     0xdc25f8: tbz             w0, #0, #0xdc2614
    //     0xdc25fc: ldurb           w16, [x3, #-1]
    //     0xdc2600: ldurb           w17, [x0, #-1]
    //     0xdc2604: and             x16, x17, x16, lsr #2
    //     0xdc2608: tst             x16, HEAP, lsr #32
    //     0xdc260c: b.eq            #0xdc2614
    //     0xdc2610: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xdc2614: cmp             x2, #0
    // 0xdc2618: b.le            #0xdc2640
    // 0xdc261c: LoadField: r1 = r3->field_13
    //     0xdc261c: ldur            w1, [x3, #0x13]
    // 0xdc2620: DecompressPointer r1
    //     0xdc2620: add             x1, x1, HEAP, lsl #32
    // 0xdc2624: r2 = ", "
    //     0xdc2624: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xdc2628: r0 = write()
    //     0xdc2628: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc262c: ldur            x0, [fp, #-8]
    // 0xdc2630: LoadField: r1 = r0->field_1b
    //     0xdc2630: ldur            w1, [x0, #0x1b]
    // 0xdc2634: DecompressPointer r1
    //     0xdc2634: add             x1, x1, HEAP, lsl #32
    // 0xdc2638: r2 = ", "
    //     0xdc2638: ldr             x2, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xdc263c: r0 = write()
    //     0xdc263c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc2640: ldr             x2, [fp, #0x10]
    // 0xdc2644: ldur            x0, [fp, #-8]
    // 0xdc2648: LoadField: r3 = r0->field_13
    //     0xdc2648: ldur            w3, [x0, #0x13]
    // 0xdc264c: DecompressPointer r3
    //     0xdc264c: add             x3, x3, HEAP, lsl #32
    // 0xdc2650: ldr             x1, [fp, #0x18]
    // 0xdc2654: stur            x3, [fp, #-0x10]
    // 0xdc2658: r0 = escapeName()
    //     0xdc2658: bl              #0xdc1450  ; [package:sqflite_common/src/sql_builder.dart] ::escapeName
    // 0xdc265c: ldur            x1, [fp, #-0x10]
    // 0xdc2660: mov             x2, x0
    // 0xdc2664: r0 = write()
    //     0xdc2664: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc2668: ldr             x3, [fp, #0x10]
    // 0xdc266c: cmp             w3, NULL
    // 0xdc2670: b.ne            #0xdc2690
    // 0xdc2674: ldur            x4, [fp, #-8]
    // 0xdc2678: LoadField: r1 = r4->field_1b
    //     0xdc2678: ldur            w1, [x4, #0x1b]
    // 0xdc267c: DecompressPointer r1
    //     0xdc267c: add             x1, x1, HEAP, lsl #32
    // 0xdc2680: r2 = "NULL"
    //     0xdc2680: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d5d8] "NULL"
    //     0xdc2684: ldr             x2, [x2, #0x5d8]
    // 0xdc2688: r0 = write()
    //     0xdc2688: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc268c: b               #0xdc2784
    // 0xdc2690: ldur            x4, [fp, #-8]
    // 0xdc2694: r0 = LoadStaticField(0x175c)
    //     0xdc2694: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc2698: ldr             x0, [x0, #0x2eb8]
    // 0xdc269c: cmp             w0, NULL
    // 0xdc26a0: b.ne            #0xdc26b0
    // 0xdc26a4: r0 = true
    //     0xdc26a4: add             x0, NULL, #0x20  ; true
    // 0xdc26a8: StoreStaticField(0x175c, r0)
    //     0xdc26a8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xdc26ac: str             x0, [x1, #0x2eb8]
    // 0xdc26b0: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xdc26b0: ldur            w5, [x4, #0x17]
    // 0xdc26b4: DecompressPointer r5
    //     0xdc26b4: add             x5, x5, HEAP, lsl #32
    // 0xdc26b8: stur            x5, [fp, #-0x10]
    // 0xdc26bc: cmp             w5, NULL
    // 0xdc26c0: b.eq            #0xdc279c
    // 0xdc26c4: LoadField: r2 = r5->field_7
    //     0xdc26c4: ldur            w2, [x5, #7]
    // 0xdc26c8: DecompressPointer r2
    //     0xdc26c8: add             x2, x2, HEAP, lsl #32
    // 0xdc26cc: mov             x0, x3
    // 0xdc26d0: r1 = Null
    //     0xdc26d0: mov             x1, NULL
    // 0xdc26d4: cmp             w2, NULL
    // 0xdc26d8: b.eq            #0xdc26f8
    // 0xdc26dc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xdc26dc: ldur            w4, [x2, #0x17]
    // 0xdc26e0: DecompressPointer r4
    //     0xdc26e0: add             x4, x4, HEAP, lsl #32
    // 0xdc26e4: r8 = X0
    //     0xdc26e4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xdc26e8: LoadField: r9 = r4->field_7
    //     0xdc26e8: ldur            x9, [x4, #7]
    // 0xdc26ec: r3 = Null
    //     0xdc26ec: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d5e0] Null
    //     0xdc26f0: ldr             x3, [x3, #0x5e0]
    // 0xdc26f4: blr             x9
    // 0xdc26f8: ldur            x0, [fp, #-0x10]
    // 0xdc26fc: LoadField: r1 = r0->field_b
    //     0xdc26fc: ldur            w1, [x0, #0xb]
    // 0xdc2700: LoadField: r2 = r0->field_f
    //     0xdc2700: ldur            w2, [x0, #0xf]
    // 0xdc2704: DecompressPointer r2
    //     0xdc2704: add             x2, x2, HEAP, lsl #32
    // 0xdc2708: LoadField: r3 = r2->field_b
    //     0xdc2708: ldur            w3, [x2, #0xb]
    // 0xdc270c: r2 = LoadInt32Instr(r1)
    //     0xdc270c: sbfx            x2, x1, #1, #0x1f
    // 0xdc2710: stur            x2, [fp, #-0x18]
    // 0xdc2714: r1 = LoadInt32Instr(r3)
    //     0xdc2714: sbfx            x1, x3, #1, #0x1f
    // 0xdc2718: cmp             x2, x1
    // 0xdc271c: b.ne            #0xdc2728
    // 0xdc2720: mov             x1, x0
    // 0xdc2724: r0 = _growToNextCapacity()
    //     0xdc2724: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xdc2728: ldur            x3, [fp, #-8]
    // 0xdc272c: ldur            x0, [fp, #-0x10]
    // 0xdc2730: ldur            x2, [fp, #-0x18]
    // 0xdc2734: add             x1, x2, #1
    // 0xdc2738: lsl             x4, x1, #1
    // 0xdc273c: StoreField: r0->field_b = r4
    //     0xdc273c: stur            w4, [x0, #0xb]
    // 0xdc2740: LoadField: r1 = r0->field_f
    //     0xdc2740: ldur            w1, [x0, #0xf]
    // 0xdc2744: DecompressPointer r1
    //     0xdc2744: add             x1, x1, HEAP, lsl #32
    // 0xdc2748: ldr             x0, [fp, #0x10]
    // 0xdc274c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xdc274c: add             x25, x1, x2, lsl #2
    //     0xdc2750: add             x25, x25, #0xf
    //     0xdc2754: str             w0, [x25]
    //     0xdc2758: tbz             w0, #0, #0xdc2774
    //     0xdc275c: ldurb           w16, [x1, #-1]
    //     0xdc2760: ldurb           w17, [x0, #-1]
    //     0xdc2764: and             x16, x17, x16, lsr #2
    //     0xdc2768: tst             x16, HEAP, lsr #32
    //     0xdc276c: b.eq            #0xdc2774
    //     0xdc2770: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xdc2774: LoadField: r1 = r3->field_1b
    //     0xdc2774: ldur            w1, [x3, #0x1b]
    // 0xdc2778: DecompressPointer r1
    //     0xdc2778: add             x1, x1, HEAP, lsl #32
    // 0xdc277c: r2 = "\?"
    //     0xdc277c: ldr             x2, [PP, #0xf90]  ; [pp+0xf90] "\?"
    // 0xdc2780: r0 = write()
    //     0xdc2780: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc2784: r0 = Null
    //     0xdc2784: mov             x0, NULL
    // 0xdc2788: LeaveFrame
    //     0xdc2788: mov             SP, fp
    //     0xdc278c: ldp             fp, lr, [SP], #0x10
    // 0xdc2790: ret
    //     0xdc2790: ret             
    // 0xdc2794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc2794: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc2798: b               #0xdc25c8
    // 0xdc279c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xdc279c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ SqlBuilder.query(/* No info */) {
    // ** addr: 0xdc304c, size: 0x1d0
    // 0xdc304c: EnterFrame
    //     0xdc304c: stp             fp, lr, [SP, #-0x10]!
    //     0xdc3050: mov             fp, SP
    // 0xdc3054: AllocStack(0x40)
    //     0xdc3054: sub             SP, SP, #0x40
    // 0xdc3058: r0 = Sentinel
    //     0xdc3058: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc305c: stur            x1, [fp, #-8]
    // 0xdc3060: mov             x16, x5
    // 0xdc3064: mov             x5, x1
    // 0xdc3068: mov             x1, x16
    // 0xdc306c: mov             x16, x6
    // 0xdc3070: mov             x6, x5
    // 0xdc3074: mov             x5, x16
    // 0xdc3078: mov             x4, x2
    // 0xdc307c: stur            x2, [fp, #-0x10]
    // 0xdc3080: mov             x2, x7
    // 0xdc3084: stur            x3, [fp, #-0x18]
    // 0xdc3088: stur            x1, [fp, #-0x20]
    // 0xdc308c: stur            x5, [fp, #-0x28]
    // 0xdc3090: stur            x7, [fp, #-0x30]
    // 0xdc3094: CheckStackOverflow
    //     0xdc3094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc3098: cmp             SP, x16
    //     0xdc309c: b.ls            #0xdc3214
    // 0xdc30a0: StoreField: r6->field_7 = r0
    //     0xdc30a0: stur            w0, [x6, #7]
    // 0xdc30a4: r0 = LoadStaticField(0x175c)
    //     0xdc30a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc30a8: ldr             x0, [x0, #0x2eb8]
    // 0xdc30ac: cmp             w0, NULL
    // 0xdc30b0: b.ne            #0xdc30c0
    // 0xdc30b4: r0 = true
    //     0xdc30b4: add             x0, NULL, #0x20  ; true
    // 0xdc30b8: StoreStaticField(0x175c, r0)
    //     0xdc30b8: ldr             x7, [THR, #0x68]  ; THR::field_table_values
    //     0xdc30bc: str             x0, [x7, #0x2eb8]
    // 0xdc30c0: r0 = StringBuffer()
    //     0xdc30c0: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xdc30c4: mov             x1, x0
    // 0xdc30c8: stur            x0, [fp, #-0x38]
    // 0xdc30cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdc30cc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdc30d0: r0 = StringBuffer()
    //     0xdc30d0: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xdc30d4: ldur            x1, [fp, #-0x38]
    // 0xdc30d8: r2 = "SELECT "
    //     0xdc30d8: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d1b8] "SELECT "
    //     0xdc30dc: ldr             x2, [x2, #0x1b8]
    // 0xdc30e0: r0 = write()
    //     0xdc30e0: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc30e4: ldur            x1, [fp, #-0x38]
    // 0xdc30e8: r2 = "* "
    //     0xdc30e8: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d1c0] "* "
    //     0xdc30ec: ldr             x2, [x2, #0x1c0]
    // 0xdc30f0: r0 = write()
    //     0xdc30f0: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc30f4: ldur            x1, [fp, #-0x38]
    // 0xdc30f8: r2 = "FROM "
    //     0xdc30f8: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d1c8] "FROM "
    //     0xdc30fc: ldr             x2, [x2, #0x1c8]
    // 0xdc3100: r0 = write()
    //     0xdc3100: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc3104: r1 = "cacheObject"
    //     0xdc3104: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d0e0] "cacheObject"
    //     0xdc3108: ldr             x1, [x1, #0xe0]
    // 0xdc310c: r0 = escapeName()
    //     0xdc310c: bl              #0xdc1450  ; [package:sqflite_common/src/sql_builder.dart] ::escapeName
    // 0xdc3110: ldur            x1, [fp, #-0x38]
    // 0xdc3114: mov             x2, x0
    // 0xdc3118: r0 = write()
    //     0xdc3118: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc311c: ldur            x1, [fp, #-8]
    // 0xdc3120: ldur            x2, [fp, #-0x38]
    // 0xdc3124: ldur            x5, [fp, #-0x28]
    // 0xdc3128: r3 = " WHERE "
    //     0xdc3128: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d1d0] " WHERE "
    //     0xdc312c: ldr             x3, [x3, #0x1d0]
    // 0xdc3130: r0 = _writeClause()
    //     0xdc3130: bl              #0xdc13ec  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::_writeClause
    // 0xdc3134: ldur            x1, [fp, #-8]
    // 0xdc3138: ldur            x2, [fp, #-0x38]
    // 0xdc313c: ldur            x5, [fp, #-0x20]
    // 0xdc3140: r3 = " ORDER BY "
    //     0xdc3140: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d1d8] " ORDER BY "
    //     0xdc3144: ldr             x3, [x3, #0x1d8]
    // 0xdc3148: r0 = _writeClause()
    //     0xdc3148: bl              #0xdc13ec  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::_writeClause
    // 0xdc314c: ldur            x0, [fp, #-0x10]
    // 0xdc3150: cmp             w0, NULL
    // 0xdc3154: b.eq            #0xdc3178
    // 0xdc3158: str             x0, [SP]
    // 0xdc315c: r0 = toString()
    //     0xdc315c: bl              #0xc460ec  ; [dart:core] _Smi::toString
    // 0xdc3160: ldur            x1, [fp, #-8]
    // 0xdc3164: ldur            x2, [fp, #-0x38]
    // 0xdc3168: mov             x5, x0
    // 0xdc316c: r3 = " LIMIT "
    //     0xdc316c: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d1e0] " LIMIT "
    //     0xdc3170: ldr             x3, [x3, #0x1e0]
    // 0xdc3174: r0 = _writeClause()
    //     0xdc3174: bl              #0xdc13ec  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::_writeClause
    // 0xdc3178: ldur            x0, [fp, #-0x18]
    // 0xdc317c: cmp             w0, NULL
    // 0xdc3180: b.eq            #0xdc31a4
    // 0xdc3184: str             x0, [SP]
    // 0xdc3188: r0 = toString()
    //     0xdc3188: bl              #0xc460ec  ; [dart:core] _Smi::toString
    // 0xdc318c: ldur            x1, [fp, #-8]
    // 0xdc3190: ldur            x2, [fp, #-0x38]
    // 0xdc3194: mov             x5, x0
    // 0xdc3198: r3 = " OFFSET "
    //     0xdc3198: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d1e8] " OFFSET "
    //     0xdc319c: ldr             x3, [x3, #0x1e8]
    // 0xdc31a0: r0 = _writeClause()
    //     0xdc31a0: bl              #0xdc13ec  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::_writeClause
    // 0xdc31a4: ldur            x0, [fp, #-8]
    // 0xdc31a8: ldur            x16, [fp, #-0x38]
    // 0xdc31ac: str             x16, [SP]
    // 0xdc31b0: r0 = toString()
    //     0xdc31b0: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xdc31b4: ldur            x3, [fp, #-8]
    // 0xdc31b8: StoreField: r3->field_7 = r0
    //     0xdc31b8: stur            w0, [x3, #7]
    //     0xdc31bc: ldurb           w16, [x3, #-1]
    //     0xdc31c0: ldurb           w17, [x0, #-1]
    //     0xdc31c4: and             x16, x17, x16, lsr #2
    //     0xdc31c8: tst             x16, HEAP, lsr #32
    //     0xdc31cc: b.eq            #0xdc31d4
    //     0xdc31d0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xdc31d4: ldur            x2, [fp, #-0x30]
    // 0xdc31d8: r1 = <Object?>
    //     0xdc31d8: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xdc31dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xdc31dc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xdc31e0: r0 = List.from()
    //     0xdc31e0: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0xdc31e4: ldur            x1, [fp, #-8]
    // 0xdc31e8: StoreField: r1->field_b = r0
    //     0xdc31e8: stur            w0, [x1, #0xb]
    //     0xdc31ec: ldurb           w16, [x1, #-1]
    //     0xdc31f0: ldurb           w17, [x0, #-1]
    //     0xdc31f4: and             x16, x17, x16, lsr #2
    //     0xdc31f8: tst             x16, HEAP, lsr #32
    //     0xdc31fc: b.eq            #0xdc3204
    //     0xdc3200: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdc3204: r0 = Null
    //     0xdc3204: mov             x0, NULL
    // 0xdc3208: LeaveFrame
    //     0xdc3208: mov             SP, fp
    //     0xdc320c: ldp             fp, lr, [SP], #0x10
    // 0xdc3210: ret
    //     0xdc3210: ret             
    // 0xdc3214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc3214: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc3218: b               #0xdc30a0
  }
  _ SqlBuilder.delete(/* No info */) {
    // ** addr: 0xdc33a0, size: 0x128
    // 0xdc33a0: EnterFrame
    //     0xdc33a0: stp             fp, lr, [SP, #-0x10]!
    //     0xdc33a4: mov             fp, SP
    // 0xdc33a8: AllocStack(0x28)
    //     0xdc33a8: sub             SP, SP, #0x28
    // 0xdc33ac: r0 = Sentinel
    //     0xdc33ac: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xdc33b0: mov             x5, x2
    // 0xdc33b4: stur            x2, [fp, #-0x10]
    // 0xdc33b8: mov             x2, x3
    // 0xdc33bc: stur            x1, [fp, #-8]
    // 0xdc33c0: stur            x3, [fp, #-0x18]
    // 0xdc33c4: CheckStackOverflow
    //     0xdc33c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc33c8: cmp             SP, x16
    //     0xdc33cc: b.ls            #0xdc34c0
    // 0xdc33d0: StoreField: r1->field_7 = r0
    //     0xdc33d0: stur            w0, [x1, #7]
    // 0xdc33d4: r0 = LoadStaticField(0x175c)
    //     0xdc33d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xdc33d8: ldr             x0, [x0, #0x2eb8]
    // 0xdc33dc: cmp             w0, NULL
    // 0xdc33e0: b.ne            #0xdc33f0
    // 0xdc33e4: r0 = true
    //     0xdc33e4: add             x0, NULL, #0x20  ; true
    // 0xdc33e8: StoreStaticField(0x175c, r0)
    //     0xdc33e8: ldr             x3, [THR, #0x68]  ; THR::field_table_values
    //     0xdc33ec: str             x0, [x3, #0x2eb8]
    // 0xdc33f0: r0 = StringBuffer()
    //     0xdc33f0: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xdc33f4: mov             x1, x0
    // 0xdc33f8: stur            x0, [fp, #-0x20]
    // 0xdc33fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xdc33fc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xdc3400: r0 = StringBuffer()
    //     0xdc3400: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xdc3404: ldur            x1, [fp, #-0x20]
    // 0xdc3408: r2 = "DELETE FROM "
    //     0xdc3408: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d550] "DELETE FROM "
    //     0xdc340c: ldr             x2, [x2, #0x550]
    // 0xdc3410: r0 = write()
    //     0xdc3410: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc3414: r1 = "cacheObject"
    //     0xdc3414: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d0e0] "cacheObject"
    //     0xdc3418: ldr             x1, [x1, #0xe0]
    // 0xdc341c: r0 = escapeName()
    //     0xdc341c: bl              #0xdc1450  ; [package:sqflite_common/src/sql_builder.dart] ::escapeName
    // 0xdc3420: ldur            x1, [fp, #-0x20]
    // 0xdc3424: mov             x2, x0
    // 0xdc3428: r0 = write()
    //     0xdc3428: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xdc342c: ldur            x1, [fp, #-8]
    // 0xdc3430: ldur            x2, [fp, #-0x20]
    // 0xdc3434: ldur            x5, [fp, #-0x10]
    // 0xdc3438: r3 = " WHERE "
    //     0xdc3438: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d1d0] " WHERE "
    //     0xdc343c: ldr             x3, [x3, #0x1d0]
    // 0xdc3440: r0 = _writeClause()
    //     0xdc3440: bl              #0xdc13ec  ; [package:sqflite_common/src/sql_builder.dart] SqlBuilder::_writeClause
    // 0xdc3444: ldur            x16, [fp, #-0x20]
    // 0xdc3448: str             x16, [SP]
    // 0xdc344c: r0 = toString()
    //     0xdc344c: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xdc3450: ldur            x3, [fp, #-8]
    // 0xdc3454: StoreField: r3->field_7 = r0
    //     0xdc3454: stur            w0, [x3, #7]
    //     0xdc3458: ldurb           w16, [x3, #-1]
    //     0xdc345c: ldurb           w17, [x0, #-1]
    //     0xdc3460: and             x16, x17, x16, lsr #2
    //     0xdc3464: tst             x16, HEAP, lsr #32
    //     0xdc3468: b.eq            #0xdc3470
    //     0xdc346c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xdc3470: ldur            x2, [fp, #-0x18]
    // 0xdc3474: cmp             w2, NULL
    // 0xdc3478: b.eq            #0xdc348c
    // 0xdc347c: r1 = <Object?>
    //     0xdc347c: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xdc3480: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xdc3480: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xdc3484: r0 = List.from()
    //     0xdc3484: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0xdc3488: b               #0xdc3490
    // 0xdc348c: r0 = Null
    //     0xdc348c: mov             x0, NULL
    // 0xdc3490: ldur            x1, [fp, #-8]
    // 0xdc3494: StoreField: r1->field_b = r0
    //     0xdc3494: stur            w0, [x1, #0xb]
    //     0xdc3498: ldurb           w16, [x1, #-1]
    //     0xdc349c: ldurb           w17, [x0, #-1]
    //     0xdc34a0: and             x16, x17, x16, lsr #2
    //     0xdc34a4: tst             x16, HEAP, lsr #32
    //     0xdc34a8: b.eq            #0xdc34b0
    //     0xdc34ac: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xdc34b0: r0 = Null
    //     0xdc34b0: mov             x0, NULL
    // 0xdc34b4: LeaveFrame
    //     0xdc34b4: mov             SP, fp
    //     0xdc34b8: ldp             fp, lr, [SP], #0x10
    // 0xdc34bc: ret
    //     0xdc34bc: ret             
    // 0xdc34c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc34c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc34c4: b               #0xdc33d0
  }
}
