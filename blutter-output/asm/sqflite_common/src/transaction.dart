// lib: , url: package:sqflite_common/src/transaction.dart

// class id: 1051162, size: 0x8
class :: {

  static _ TransactionPrvExt.checkNotClosed(/* No info */) {
    // ** addr: 0xab44e8, size: 0x4c
    // 0xab44e8: EnterFrame
    //     0xab44e8: stp             fp, lr, [SP, #-0x10]!
    //     0xab44ec: mov             fp, SP
    // 0xab44f0: LoadField: r0 = r1->field_b
    //     0xab44f0: ldur            w0, [x1, #0xb]
    // 0xab44f4: DecompressPointer r0
    //     0xab44f4: add             x0, x0, HEAP, lsl #32
    // 0xab44f8: r16 = true
    //     0xab44f8: add             x16, NULL, #0x20  ; true
    // 0xab44fc: cmp             w0, w16
    // 0xab4500: b.eq            #0xab4514
    // 0xab4504: r0 = Null
    //     0xab4504: mov             x0, NULL
    // 0xab4508: LeaveFrame
    //     0xab4508: mov             SP, fp
    //     0xab450c: ldp             fp, lr, [SP], #0x10
    // 0xab4510: ret
    //     0xab4510: ret             
    // 0xab4514: r0 = SqfliteDatabaseException()
    //     0xab4514: bl              #0xab3434  ; AllocateSqfliteDatabaseExceptionStub -> SqfliteDatabaseException (size=0x14)
    // 0xab4518: mov             x1, x0
    // 0xab451c: r0 = "error transaction_closed"
    //     0xab451c: add             x0, PP, #0x43, lsl #12  ; [pp+0x430e8] "error transaction_closed"
    //     0xab4520: ldr             x0, [x0, #0xe8]
    // 0xab4524: StoreField: r1->field_7 = r0
    //     0xab4524: stur            w0, [x1, #7]
    // 0xab4528: mov             x0, x1
    // 0xab452c: r0 = Throw()
    //     0xab452c: bl              #0xec04b8  ; ThrowStub
    // 0xab4530: brk             #0
  }
  static _ getForcedSqfliteTransaction(/* No info */) {
    // ** addr: 0xab4d68, size: 0x20
    // 0xab4d68: EnterFrame
    //     0xab4d68: stp             fp, lr, [SP, #-0x10]!
    //     0xab4d6c: mov             fp, SP
    // 0xab4d70: r0 = SqfliteTransaction()
    //     0xab4d70: bl              #0xab4d88  ; AllocateSqfliteTransactionStub -> SqfliteTransaction (size=0x14)
    // 0xab4d74: r1 = -2
    //     0xab4d74: orr             x1, xzr, #0xfffffffffffffffe
    // 0xab4d78: StoreField: r0->field_7 = r1
    //     0xab4d78: stur            w1, [x0, #7]
    // 0xab4d7c: LeaveFrame
    //     0xab4d7c: mov             SP, fp
    //     0xab4d80: ldp             fp, lr, [SP], #0x10
    // 0xab4d84: ret
    //     0xab4d84: ret             
  }
}

// class id: 459, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _SqfliteTransaction&Object&SqfliteDatabaseExecutorMixin extends Object
     with SqfliteDatabaseExecutorMixin {
}

// class id: 460, size: 0x10, field offset: 0x8
//   transformed mixin,
abstract class _SqfliteTransaction&Object&SqfliteDatabaseExecutorMixin&SqfliteTransactionMixin extends _SqfliteTransaction&Object&SqfliteDatabaseExecutorMixin
     with SqfliteTransactionMixin {
}

// class id: 461, size: 0x14, field offset: 0x10
class SqfliteTransaction extends _SqfliteTransaction&Object&SqfliteDatabaseExecutorMixin&SqfliteTransactionMixin
    implements Transaction {
}

// class id: 462, size: 0x8, field offset: 0x8
abstract class SqfliteTransactionMixin extends Object
    implements Transaction {
}
