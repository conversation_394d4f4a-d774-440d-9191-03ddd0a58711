// lib: , url: package:sqflite_common/src/utils.dart

// class id: 1051163, size: 0x8
class :: {

  static late Duration? lockWarningDuration; // offset: 0x1124
  static late ((dynamic) => void)? lockWarningCallback; // offset: 0x1128

  [closure] static void _lockWarningCallbackDefault(dynamic) {
    // ** addr: 0xab4590, size: 0x2c
    // 0xab4590: EnterFrame
    //     0xab4590: stp             fp, lr, [SP, #-0x10]!
    //     0xab4594: mov             fp, SP
    // 0xab4598: CheckStackOverflow
    //     0xab4598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab459c: cmp             SP, x16
    //     0xab45a0: b.ls            #0xab45b4
    // 0xab45a4: r0 = _lockWarningCallbackDefault()
    //     0xab45a4: bl              #0xab45bc  ; [package:sqflite_common/src/utils.dart] ::_lockWarningCallbackDefault
    // 0xab45a8: LeaveFrame
    //     0xab45a8: mov             SP, fp
    //     0xab45ac: ldp             fp, lr, [SP], #0x10
    // 0xab45b0: ret
    //     0xab45b0: ret             
    // 0xab45b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab45b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab45b8: b               #0xab45a4
  }
  static void _lockWarningCallbackDefault() {
    // ** addr: 0xab45bc, size: 0x94
    // 0xab45bc: EnterFrame
    //     0xab45bc: stp             fp, lr, [SP, #-0x10]!
    //     0xab45c0: mov             fp, SP
    // 0xab45c4: AllocStack(0x10)
    //     0xab45c4: sub             SP, SP, #0x10
    // 0xab45c8: CheckStackOverflow
    //     0xab45c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab45cc: cmp             SP, x16
    //     0xab45d0: b.ls            #0xab4648
    // 0xab45d4: r1 = Null
    //     0xab45d4: mov             x1, NULL
    // 0xab45d8: r2 = 6
    //     0xab45d8: movz            x2, #0x6
    // 0xab45dc: r0 = AllocateArray()
    //     0xab45dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab45e0: stur            x0, [fp, #-8]
    // 0xab45e4: r16 = "Warning database has been locked for "
    //     0xab45e4: add             x16, PP, #0x43, lsl #12  ; [pp+0x430b8] "Warning database has been locked for "
    //     0xab45e8: ldr             x16, [x16, #0xb8]
    // 0xab45ec: StoreField: r0->field_f = r16
    //     0xab45ec: stur            w16, [x0, #0xf]
    // 0xab45f0: r0 = InitLateStaticField(0x1124) // [package:sqflite_common/src/utils.dart] ::lockWarningDuration
    //     0xab45f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xab45f4: ldr             x0, [x0, #0x2248]
    //     0xab45f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xab45fc: cmp             w0, w16
    //     0xab4600: b.ne            #0xab4610
    //     0xab4604: add             x2, PP, #0x43, lsl #12  ; [pp+0x43090] Field <::.lockWarningDuration>: static late (offset: 0x1124)
    //     0xab4608: ldr             x2, [x2, #0x90]
    //     0xab460c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xab4610: mov             x1, x0
    // 0xab4614: ldur            x0, [fp, #-8]
    // 0xab4618: StoreField: r0->field_13 = r1
    //     0xab4618: stur            w1, [x0, #0x13]
    // 0xab461c: r16 = ". Make sure you always use the transaction object for database operations during a transaction"
    //     0xab461c: add             x16, PP, #0x43, lsl #12  ; [pp+0x430c0] ". Make sure you always use the transaction object for database operations during a transaction"
    //     0xab4620: ldr             x16, [x16, #0xc0]
    // 0xab4624: ArrayStore: r0[0] = r16  ; List_4
    //     0xab4624: stur            w16, [x0, #0x17]
    // 0xab4628: str             x0, [SP]
    // 0xab462c: r0 = _interpolate()
    //     0xab462c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xab4630: mov             x1, x0
    // 0xab4634: r0 = print()
    //     0xab4634: bl              #0x63fe38  ; [dart:core] ::print
    // 0xab4638: r0 = Null
    //     0xab4638: mov             x0, NULL
    // 0xab463c: LeaveFrame
    //     0xab463c: mov             SP, fp
    //     0xab4640: ldp             fp, lr, [SP], #0x10
    // 0xab4644: ret
    //     0xab4644: ret             
    // 0xab4648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab4648: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab464c: b               #0xab45d4
  }
  static ((dynamic) => void)? lockWarningCallback() {
    // ** addr: 0xab46f8, size: 0xc
    // 0xab46f8: r0 = Closure: () => void from Function '_lockWarningCallbackDefault@1098173424': static.
    //     0xab46f8: add             x0, PP, #0x43, lsl #12  ; [pp+0x430f0] Closure: () => void from Function '_lockWarningCallbackDefault@1098173424': static. (0x7e54fb4b4590)
    //     0xab46fc: ldr             x0, [x0, #0xf0]
    // 0xab4700: ret
    //     0xab4700: ret             
  }
  static Duration? lockWarningDuration() {
    // ** addr: 0xab4704, size: 0xc
    // 0xab4704: r0 = Instance_Duration
    //     0xab4704: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c3f8] Obj!Duration@e3a1c1
    //     0xab4708: ldr             x0, [x0, #0x3f8]
    // 0xab470c: ret
    //     0xab470c: ret             
  }
  static _ getSqlInTransactionArgument(/* No info */) {
    // ** addr: 0xab54a4, size: 0xbc
    // 0xab54a4: EnterFrame
    //     0xab54a4: stp             fp, lr, [SP, #-0x10]!
    //     0xab54a8: mov             fp, SP
    // 0xab54ac: AllocStack(0x10)
    //     0xab54ac: sub             SP, SP, #0x10
    // 0xab54b0: CheckStackOverflow
    //     0xab54b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab54b4: cmp             SP, x16
    //     0xab54b8: b.ls            #0xab5558
    // 0xab54bc: r0 = trim()
    //     0xab54bc: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xab54c0: r1 = LoadClassIdInstr(r0)
    //     0xab54c0: ldur            x1, [x0, #-1]
    //     0xab54c4: ubfx            x1, x1, #0xc, #0x14
    // 0xab54c8: str             x0, [SP]
    // 0xab54cc: mov             x0, x1
    // 0xab54d0: r0 = GDT[cid_x0 + -0xffe]()
    //     0xab54d0: sub             lr, x0, #0xffe
    //     0xab54d4: ldr             lr, [x21, lr, lsl #3]
    //     0xab54d8: blr             lr
    // 0xab54dc: mov             x1, x0
    // 0xab54e0: r2 = "begin"
    //     0xab54e0: add             x2, PP, #0x43, lsl #12  ; [pp+0x43078] "begin"
    //     0xab54e4: ldr             x2, [x2, #0x78]
    // 0xab54e8: stur            x0, [fp, #-8]
    // 0xab54ec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab54ec: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab54f0: r0 = startsWith()
    //     0xab54f0: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xab54f4: tbnz            w0, #4, #0xab5508
    // 0xab54f8: r0 = true
    //     0xab54f8: add             x0, NULL, #0x20  ; true
    // 0xab54fc: LeaveFrame
    //     0xab54fc: mov             SP, fp
    //     0xab5500: ldp             fp, lr, [SP], #0x10
    // 0xab5504: ret
    //     0xab5504: ret             
    // 0xab5508: ldur            x1, [fp, #-8]
    // 0xab550c: r2 = "commit"
    //     0xab550c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43080] "commit"
    //     0xab5510: ldr             x2, [x2, #0x80]
    // 0xab5514: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab5514: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab5518: r0 = startsWith()
    //     0xab5518: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xab551c: tbz             w0, #4, #0xab5538
    // 0xab5520: ldur            x1, [fp, #-8]
    // 0xab5524: r2 = "rollback"
    //     0xab5524: add             x2, PP, #0x43, lsl #12  ; [pp+0x43088] "rollback"
    //     0xab5528: ldr             x2, [x2, #0x88]
    // 0xab552c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xab552c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xab5530: r0 = startsWith()
    //     0xab5530: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xab5534: tbnz            w0, #4, #0xab5548
    // 0xab5538: r0 = false
    //     0xab5538: add             x0, NULL, #0x30  ; false
    // 0xab553c: LeaveFrame
    //     0xab553c: mov             SP, fp
    //     0xab5540: ldp             fp, lr, [SP], #0x10
    // 0xab5544: ret
    //     0xab5544: ret             
    // 0xab5548: r0 = Null
    //     0xab5548: mov             x0, NULL
    // 0xab554c: LeaveFrame
    //     0xab554c: mov             SP, fp
    //     0xab5550: ldp             fp, lr, [SP], #0x10
    // 0xab5554: ret
    //     0xab5554: ret             
    // 0xab5558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5558: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab555c: b               #0xab54bc
  }
  static _ parseInt(/* No info */) {
    // ** addr: 0xab5940, size: 0x9c
    // 0xab5940: EnterFrame
    //     0xab5940: stp             fp, lr, [SP, #-0x10]!
    //     0xab5944: mov             fp, SP
    // 0xab5948: AllocStack(0x38)
    //     0xab5948: sub             SP, SP, #0x38
    // 0xab594c: SetupParameters(dynamic _ /* r1 => r0 */)
    //     0xab594c: mov             x0, x1
    // 0xab5950: CheckStackOverflow
    //     0xab5950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5954: cmp             SP, x16
    //     0xab5958: b.ls            #0xab59d4
    // 0xab595c: r1 = 60
    //     0xab595c: movz            x1, #0x3c
    // 0xab5960: branchIfSmi(r0, 0xab596c)
    //     0xab5960: tbz             w0, #0, #0xab596c
    // 0xab5964: r1 = LoadClassIdInstr(r0)
    //     0xab5964: ldur            x1, [x0, #-1]
    //     0xab5968: ubfx            x1, x1, #0xc, #0x14
    // 0xab596c: sub             x16, x1, #0x3c
    // 0xab5970: cmp             x16, #1
    // 0xab5974: b.hi            #0xab5984
    // 0xab5978: LeaveFrame
    //     0xab5978: mov             SP, fp
    //     0xab597c: ldp             fp, lr, [SP], #0x10
    // 0xab5980: ret
    //     0xab5980: ret             
    // 0xab5984: sub             x16, x1, #0x5e
    // 0xab5988: cmp             x16, #1
    // 0xab598c: b.hi            #0xab59c4
    // 0xab5990: mov             x1, x0
    // 0xab5994: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab5994: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab5998: r0 = parse()
    //     0xab5998: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xab599c: mov             x2, x0
    // 0xab59a0: r0 = BoxInt64Instr(r2)
    //     0xab59a0: sbfiz           x0, x2, #1, #0x1f
    //     0xab59a4: cmp             x2, x0, asr #1
    //     0xab59a8: b.eq            #0xab59b4
    //     0xab59ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab59b0: stur            x2, [x0, #7]
    // 0xab59b4: LeaveFrame
    //     0xab59b4: mov             SP, fp
    //     0xab59b8: ldp             fp, lr, [SP], #0x10
    // 0xab59bc: ret
    //     0xab59bc: ret             
    // 0xab59c0: sub             SP, fp, #0x38
    // 0xab59c4: r0 = Null
    //     0xab59c4: mov             x0, NULL
    // 0xab59c8: LeaveFrame
    //     0xab59c8: mov             SP, fp
    //     0xab59cc: ldp             fp, lr, [SP], #0x10
    // 0xab59d0: ret
    //     0xab59d0: ret             
    // 0xab59d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab59d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab59d8: b               #0xab595c
  }
}
