// lib: , url: package:sqflite_common/utils/utils.dart

// class id: 1051165, size: 0x8
class :: {

  static _ firstIntValue(/* No info */) {
    // ** addr: 0xab5850, size: 0xf0
    // 0xab5850: EnterFrame
    //     0xab5850: stp             fp, lr, [SP, #-0x10]!
    //     0xab5854: mov             fp, SP
    // 0xab5858: AllocStack(0x8)
    //     0xab5858: sub             SP, SP, #8
    // 0xab585c: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xab585c: mov             x2, x1
    //     0xab5860: stur            x1, [fp, #-8]
    // 0xab5864: CheckStackOverflow
    //     0xab5864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab5868: cmp             SP, x16
    //     0xab586c: b.ls            #0xab5938
    // 0xab5870: r0 = LoadClassIdInstr(r2)
    //     0xab5870: ldur            x0, [x2, #-1]
    //     0xab5874: ubfx            x0, x0, #0xc, #0x14
    // 0xab5878: mov             x1, x2
    // 0xab587c: r0 = GDT[cid_x0 + 0xd488]()
    //     0xab587c: movz            x17, #0xd488
    //     0xab5880: add             lr, x0, x17
    //     0xab5884: ldr             lr, [x21, lr, lsl #3]
    //     0xab5888: blr             lr
    // 0xab588c: tbnz            w0, #4, #0xab5928
    // 0xab5890: ldur            x1, [fp, #-8]
    // 0xab5894: r0 = LoadClassIdInstr(r1)
    //     0xab5894: ldur            x0, [x1, #-1]
    //     0xab5898: ubfx            x0, x0, #0xc, #0x14
    // 0xab589c: r0 = GDT[cid_x0 + 0xd20f]()
    //     0xab589c: movz            x17, #0xd20f
    //     0xab58a0: add             lr, x0, x17
    //     0xab58a4: ldr             lr, [x21, lr, lsl #3]
    //     0xab58a8: blr             lr
    // 0xab58ac: mov             x2, x0
    // 0xab58b0: stur            x2, [fp, #-8]
    // 0xab58b4: r0 = LoadClassIdInstr(r2)
    //     0xab58b4: ldur            x0, [x2, #-1]
    //     0xab58b8: ubfx            x0, x0, #0xc, #0x14
    // 0xab58bc: mov             x1, x2
    // 0xab58c0: r0 = GDT[cid_x0 + 0x11f66]()
    //     0xab58c0: movz            x17, #0x1f66
    //     0xab58c4: movk            x17, #0x1, lsl #16
    //     0xab58c8: add             lr, x0, x17
    //     0xab58cc: ldr             lr, [x21, lr, lsl #3]
    //     0xab58d0: blr             lr
    // 0xab58d4: tbnz            w0, #4, #0xab5928
    // 0xab58d8: ldur            x1, [fp, #-8]
    // 0xab58dc: r0 = LoadClassIdInstr(r1)
    //     0xab58dc: ldur            x0, [x1, #-1]
    //     0xab58e0: ubfx            x0, x0, #0xc, #0x14
    // 0xab58e4: r0 = GDT[cid_x0 + 0x73d]()
    //     0xab58e4: add             lr, x0, #0x73d
    //     0xab58e8: ldr             lr, [x21, lr, lsl #3]
    //     0xab58ec: blr             lr
    // 0xab58f0: r1 = LoadClassIdInstr(r0)
    //     0xab58f0: ldur            x1, [x0, #-1]
    //     0xab58f4: ubfx            x1, x1, #0xc, #0x14
    // 0xab58f8: mov             x16, x0
    // 0xab58fc: mov             x0, x1
    // 0xab5900: mov             x1, x16
    // 0xab5904: r0 = GDT[cid_x0 + 0xd20f]()
    //     0xab5904: movz            x17, #0xd20f
    //     0xab5908: add             lr, x0, x17
    //     0xab590c: ldr             lr, [x21, lr, lsl #3]
    //     0xab5910: blr             lr
    // 0xab5914: mov             x1, x0
    // 0xab5918: r0 = parseInt()
    //     0xab5918: bl              #0xab5940  ; [package:sqflite_common/src/utils.dart] ::parseInt
    // 0xab591c: LeaveFrame
    //     0xab591c: mov             SP, fp
    //     0xab5920: ldp             fp, lr, [SP], #0x10
    // 0xab5924: ret
    //     0xab5924: ret             
    // 0xab5928: r0 = Null
    //     0xab5928: mov             x0, NULL
    // 0xab592c: LeaveFrame
    //     0xab592c: mov             SP, fp
    //     0xab5930: ldp             fp, lr, [SP], #0x10
    // 0xab5934: ret
    //     0xab5934: ret             
    // 0xab5938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab5938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab593c: b               #0xab5870
  }
}
