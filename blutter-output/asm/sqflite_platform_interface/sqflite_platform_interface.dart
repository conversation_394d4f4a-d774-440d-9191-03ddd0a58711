// lib: , url: package:sqflite_platform_interface/sqflite_platform_interface.dart

// class id: 1051166, size: 0x8
class :: {
}

// class id: 458, size: 0x8, field offset: 0x8
abstract class SqflitePlatform extends Object {

  static void initWithDatabaseFactoryMethodChannel() {
    // ** addr: 0xec70ec, size: 0x64
    // 0xec70ec: EnterFrame
    //     0xec70ec: stp             fp, lr, [SP, #-0x10]!
    //     0xec70f0: mov             fp, SP
    // 0xec70f4: CheckStackOverflow
    //     0xec70f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec70f8: cmp             SP, x16
    //     0xec70fc: b.ls            #0xec7148
    // 0xec7100: r0 = LoadStaticField(0x1758)
    //     0xec7100: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec7104: ldr             x0, [x0, #0x2eb0]
    // 0xec7108: cmp             w0, NULL
    // 0xec710c: b.ne            #0xec7138
    // 0xec7110: r0 = InitLateStaticField(0x1764) // [package:sqflite_platform_interface/src/factory_platform.dart] ::sqfliteDatabaseFactoryDefault
    //     0xec7110: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec7114: ldr             x0, [x0, #0x2ec8]
    //     0xec7118: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec711c: cmp             w0, w16
    //     0xec7120: b.ne            #0xec7130
    //     0xec7124: add             x2, PP, #0xc, lsl #12  ; [pp+0xc758] Field <::.sqfliteDatabaseFactoryDefault>: static late final (offset: 0x1764)
    //     0xec7128: ldr             x2, [x2, #0x758]
    //     0xec712c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec7130: StoreStaticField(0x1758, r0)
    //     0xec7130: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xec7134: str             x0, [x1, #0x2eb0]
    // 0xec7138: r0 = Null
    //     0xec7138: mov             x0, NULL
    // 0xec713c: LeaveFrame
    //     0xec713c: mov             SP, fp
    //     0xec7140: ldp             fp, lr, [SP], #0x10
    // 0xec7144: ret
    //     0xec7144: ret             
    // 0xec7148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec7148: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec714c: b               #0xec7100
  }
}
