// lib: , url: package:sqflite_platform_interface/src/factory_platform.dart

// class id: 1051167, size: 0x8
class :: {

  static late final SqfliteDatabaseFactory sqfliteDatabaseFactoryDefault; // offset: 0x1764
  static late final SqfliteDatabaseFactory _databaseFactorySqflitePlugin; // offset: 0x1760

  static SqfliteDatabaseFactory sqfliteDatabaseFactoryDefault() {
    // ** addr: 0xec7150, size: 0x48
    // 0xec7150: EnterFrame
    //     0xec7150: stp             fp, lr, [SP, #-0x10]!
    //     0xec7154: mov             fp, SP
    // 0xec7158: CheckStackOverflow
    //     0xec7158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec715c: cmp             SP, x16
    //     0xec7160: b.ls            #0xec7190
    // 0xec7164: r0 = InitLateStaticField(0x1760) // [package:sqflite_platform_interface/src/factory_platform.dart] ::_databaseFactorySqflitePlugin
    //     0xec7164: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec7168: ldr             x0, [x0, #0x2ec0]
    //     0xec716c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec7170: cmp             w0, w16
    //     0xec7174: b.ne            #0xec7184
    //     0xec7178: add             x2, PP, #0xc, lsl #12  ; [pp+0xc760] Field <::._databaseFactorySqflitePlugin@2718225172>: static late final (offset: 0x1760)
    //     0xec717c: ldr             x2, [x2, #0x760]
    //     0xec7180: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec7184: LeaveFrame
    //     0xec7184: mov             SP, fp
    //     0xec7188: ldp             fp, lr, [SP], #0x10
    // 0xec718c: ret
    //     0xec718c: ret             
    // 0xec7190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec7190: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec7194: b               #0xec7164
  }
  static SqfliteDatabaseFactory _databaseFactorySqflitePlugin() {
    // ** addr: 0xec7198, size: 0x58
    // 0xec7198: EnterFrame
    //     0xec7198: stp             fp, lr, [SP, #-0x10]!
    //     0xec719c: mov             fp, SP
    // 0xec71a0: AllocStack(0x18)
    //     0xec71a0: sub             SP, SP, #0x18
    // 0xec71a4: CheckStackOverflow
    //     0xec71a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec71a8: cmp             SP, x16
    //     0xec71ac: b.ls            #0xec71e8
    // 0xec71b0: r16 = <String, SqfliteDatabaseOpenHelper>
    //     0xec71b0: add             x16, PP, #0xc, lsl #12  ; [pp+0xc768] TypeArguments: <String, SqfliteDatabaseOpenHelper>
    //     0xec71b4: ldr             x16, [x16, #0x768]
    // 0xec71b8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xec71bc: stp             lr, x16, [SP]
    // 0xec71c0: r0 = Map._fromLiteral()
    //     0xec71c0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xec71c4: stur            x0, [fp, #-8]
    // 0xec71c8: r0 = SqfliteDatabaseFactoryImpl()
    //     0xec71c8: bl              #0xec71f0  ; AllocateSqfliteDatabaseFactoryImplStub -> SqfliteDatabaseFactoryImpl (size=0x18)
    // 0xec71cc: ldur            x1, [fp, #-8]
    // 0xec71d0: StoreField: r0->field_7 = r1
    //     0xec71d0: stur            w1, [x0, #7]
    // 0xec71d4: r1 = false
    //     0xec71d4: add             x1, NULL, #0x30  ; false
    // 0xec71d8: StoreField: r0->field_13 = r1
    //     0xec71d8: stur            w1, [x0, #0x13]
    // 0xec71dc: LeaveFrame
    //     0xec71dc: mov             SP, fp
    //     0xec71e0: ldp             fp, lr, [SP], #0x10
    // 0xec71e4: ret
    //     0xec71e4: ret             
    // 0xec71e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec71e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec71ec: b               #0xec71b0
  }
}

// class id: 456, size: 0x18, field offset: 0x8
//   transformed mixin,
abstract class _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin extends Object
     with SqfliteDatabaseFactoryMixin {

  _ openDatabase(/* No info */) async {
    // ** addr: 0xaaeddc, size: 0xec
    // 0xaaeddc: EnterFrame
    //     0xaaeddc: stp             fp, lr, [SP, #-0x10]!
    //     0xaaede0: mov             fp, SP
    // 0xaaede4: AllocStack(0x40)
    //     0xaaede4: sub             SP, SP, #0x40
    // 0xaaede8: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xaaede8: stur            NULL, [fp, #-8]
    //     0xaaedec: stur            x1, [fp, #-0x10]
    //     0xaaedf0: stur            x2, [fp, #-0x18]
    //     0xaaedf4: stur            x3, [fp, #-0x20]
    // 0xaaedf8: CheckStackOverflow
    //     0xaaedf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaedfc: cmp             SP, x16
    //     0xaaee00: b.ls            #0xaaeec0
    // 0xaaee04: r1 = 3
    //     0xaaee04: movz            x1, #0x3
    // 0xaaee08: r0 = AllocateContext()
    //     0xaaee08: bl              #0xec126c  ; AllocateContextStub
    // 0xaaee0c: mov             x2, x0
    // 0xaaee10: ldur            x1, [fp, #-0x10]
    // 0xaaee14: stur            x2, [fp, #-0x28]
    // 0xaaee18: StoreField: r2->field_f = r1
    //     0xaaee18: stur            w1, [x2, #0xf]
    // 0xaaee1c: ldur            x0, [fp, #-0x18]
    // 0xaaee20: StoreField: r2->field_13 = r0
    //     0xaaee20: stur            w0, [x2, #0x13]
    // 0xaaee24: ldur            x0, [fp, #-0x20]
    // 0xaaee28: ArrayStore: r2[0] = r0  ; List_4
    //     0xaaee28: stur            w0, [x2, #0x17]
    // 0xaaee2c: InitAsync() -> Future<Database>
    //     0xaaee2c: add             x0, PP, #0x43, lsl #12  ; [pp+0x43108] TypeArguments: <Database>
    //     0xaaee30: ldr             x0, [x0, #0x108]
    //     0xaaee34: bl              #0x661298  ; InitAsyncStub
    // 0xaaee38: ldur            x0, [fp, #-0x28]
    // 0xaaee3c: LoadField: r2 = r0->field_13
    //     0xaaee3c: ldur            w2, [x0, #0x13]
    // 0xaaee40: DecompressPointer r2
    //     0xaaee40: add             x2, x2, HEAP, lsl #32
    // 0xaaee44: ldur            x1, [fp, #-0x10]
    // 0xaaee48: r0 = fixPath()
    //     0xaaee48: bl              #0xab23c0  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::fixPath
    // 0xaaee4c: mov             x1, x0
    // 0xaaee50: stur            x1, [fp, #-0x18]
    // 0xaaee54: r0 = Await()
    //     0xaaee54: bl              #0x661044  ; AwaitStub
    // 0xaaee58: mov             x1, x0
    // 0xaaee5c: ldur            x3, [fp, #-0x28]
    // 0xaaee60: StoreField: r3->field_13 = r0
    //     0xaaee60: stur            w0, [x3, #0x13]
    //     0xaaee64: tbz             w0, #0, #0xaaee80
    //     0xaaee68: ldurb           w16, [x3, #-1]
    //     0xaaee6c: ldurb           w17, [x0, #-1]
    //     0xaaee70: and             x16, x17, x16, lsr #2
    //     0xaaee74: tst             x16, HEAP, lsr #32
    //     0xaaee78: b.eq            #0xaaee80
    //     0xaaee7c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaaee80: mov             x2, x1
    // 0xaaee84: ldur            x1, [fp, #-0x10]
    // 0xaaee88: r0 = _getDatabaseOpenLock()
    //     0xaaee88: bl              #0xab2148  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::_getDatabaseOpenLock
    // 0xaaee8c: ldur            x2, [fp, #-0x28]
    // 0xaaee90: r1 = Function '<anonymous closure>':.
    //     0xaaee90: add             x1, PP, #0x43, lsl #12  ; [pp+0x43110] AnonymousClosure: (0xab3934), in [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::openDatabase (0xaaeddc)
    //     0xaaee94: ldr             x1, [x1, #0x110]
    // 0xaaee98: stur            x0, [fp, #-0x10]
    // 0xaaee9c: r0 = AllocateClosure()
    //     0xaaee9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaaeea0: r16 = <Database>
    //     0xaaeea0: add             x16, PP, #0x43, lsl #12  ; [pp+0x43108] TypeArguments: <Database>
    //     0xaaeea4: ldr             x16, [x16, #0x108]
    // 0xaaeea8: ldur            lr, [fp, #-0x10]
    // 0xaaeeac: stp             lr, x16, [SP, #8]
    // 0xaaeeb0: str             x0, [SP]
    // 0xaaeeb4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaaeeb4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaaeeb8: r0 = synchronized()
    //     0xaaeeb8: bl              #0xaaeec8  ; [package:synchronized/src/reentrant_lock.dart] ReentrantLock::synchronized
    // 0xaaeebc: r0 = ReturnAsync()
    //     0xaaeebc: b               #0x6576a4  ; ReturnAsyncStub
    // 0xaaeec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaeec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaeec4: b               #0xaaee04
  }
  _ _getDatabaseOpenLock(/* No info */) {
    // ** addr: 0xab2148, size: 0x3c
    // 0xab2148: EnterFrame
    //     0xab2148: stp             fp, lr, [SP, #-0x10]!
    //     0xab214c: mov             fp, SP
    // 0xab2150: CheckStackOverflow
    //     0xab2150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab2154: cmp             SP, x16
    //     0xab2158: b.ls            #0xab217c
    // 0xab215c: r1 = Null
    //     0xab215c: mov             x1, NULL
    // 0xab2160: r0 = _NamedLock()
    //     0xab2160: bl              #0xab2184  ; [package:sqflite_common/src/factory_mixin.dart] _NamedLock::_NamedLock
    // 0xab2164: LoadField: r1 = r0->field_7
    //     0xab2164: ldur            w1, [x0, #7]
    // 0xab2168: DecompressPointer r1
    //     0xab2168: add             x1, x1, HEAP, lsl #32
    // 0xab216c: mov             x0, x1
    // 0xab2170: LeaveFrame
    //     0xab2170: mov             SP, fp
    //     0xab2174: ldp             fp, lr, [SP], #0x10
    // 0xab2178: ret
    //     0xab2178: ret             
    // 0xab217c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab217c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2180: b               #0xab215c
  }
  _ fixPath(/* No info */) async {
    // ** addr: 0xab23c0, size: 0xc4
    // 0xab23c0: EnterFrame
    //     0xab23c0: stp             fp, lr, [SP, #-0x10]!
    //     0xab23c4: mov             fp, SP
    // 0xab23c8: AllocStack(0x18)
    //     0xab23c8: sub             SP, SP, #0x18
    // 0xab23cc: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0xab23cc: stur            NULL, [fp, #-8]
    //     0xab23d0: stur            x1, [fp, #-0x10]
    //     0xab23d4: mov             x16, x2
    //     0xab23d8: mov             x2, x1
    //     0xab23dc: mov             x1, x16
    //     0xab23e0: stur            x1, [fp, #-0x18]
    // 0xab23e4: CheckStackOverflow
    //     0xab23e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab23e8: cmp             SP, x16
    //     0xab23ec: b.ls            #0xab247c
    // 0xab23f0: InitAsync() -> Future<String>
    //     0xab23f0: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0xab23f4: bl              #0x661298  ; InitAsyncStub
    // 0xab23f8: ldur            x1, [fp, #-0x18]
    // 0xab23fc: r0 = isInMemoryDatabasePath()
    //     0xab23fc: bl              #0xab3878  ; [package:sqflite_common/src/path_utils.dart] ::isInMemoryDatabasePath
    // 0xab2400: tbnz            w0, #4, #0xab2410
    // 0xab2404: r0 = ":memory:"
    //     0xab2404: add             x0, PP, #0x43, lsl #12  ; [pp+0x43348] ":memory:"
    //     0xab2408: ldr             x0, [x0, #0x348]
    // 0xab240c: r0 = ReturnAsyncNotFuture()
    //     0xab240c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab2410: ldur            x0, [fp, #-0x10]
    // 0xab2414: LoadField: r1 = r0->field_13
    //     0xab2414: ldur            w1, [x0, #0x13]
    // 0xab2418: DecompressPointer r1
    //     0xab2418: add             x1, x1, HEAP, lsl #32
    // 0xab241c: tbz             w1, #4, #0xab242c
    // 0xab2420: ldur            x1, [fp, #-0x18]
    // 0xab2424: r0 = isFileUriDatabasePath()
    //     0xab2424: bl              #0xab3840  ; [package:sqflite_common/src/path_utils.dart] ::isFileUriDatabasePath
    // 0xab2428: tbnz            w0, #4, #0xab2434
    // 0xab242c: ldur            x0, [fp, #-0x18]
    // 0xab2430: b               #0xab2478
    // 0xab2434: ldur            x1, [fp, #-0x18]
    // 0xab2438: r0 = isRelative()
    //     0xab2438: bl              #0xab37ac  ; [package:path/path.dart] ::isRelative
    // 0xab243c: tbnz            w0, #4, #0xab2468
    // 0xab2440: ldur            x1, [fp, #-0x10]
    // 0xab2444: r0 = getDatabasesPath()
    //     0xab2444: bl              #0xab3368  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::getDatabasesPath
    // 0xab2448: mov             x1, x0
    // 0xab244c: stur            x1, [fp, #-0x10]
    // 0xab2450: r0 = Await()
    //     0xab2450: bl              #0x661044  ; AwaitStub
    // 0xab2454: mov             x1, x0
    // 0xab2458: ldur            x2, [fp, #-0x18]
    // 0xab245c: r0 = join()
    //     0xab245c: bl              #0x831be8  ; [package:path/path.dart] ::join
    // 0xab2460: mov             x1, x0
    // 0xab2464: b               #0xab246c
    // 0xab2468: ldur            x1, [fp, #-0x18]
    // 0xab246c: r0 = normalize()
    //     0xab246c: bl              #0xab263c  ; [package:path/path.dart] ::normalize
    // 0xab2470: mov             x1, x0
    // 0xab2474: r0 = absolute()
    //     0xab2474: bl              #0xab2484  ; [package:path/path.dart] ::absolute
    // 0xab2478: r0 = ReturnAsyncNotFuture()
    //     0xab2478: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab247c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab247c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab2480: b               #0xab23f0
  }
  _ getDatabasesPath(/* No info */) async {
    // ** addr: 0xab3368, size: 0xcc
    // 0xab3368: EnterFrame
    //     0xab3368: stp             fp, lr, [SP, #-0x10]!
    //     0xab336c: mov             fp, SP
    // 0xab3370: AllocStack(0x30)
    //     0xab3370: sub             SP, SP, #0x30
    // 0xab3374: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1 => r1, fp-0x10 */)
    //     0xab3374: stur            NULL, [fp, #-8]
    //     0xab3378: stur            x1, [fp, #-0x10]
    // 0xab337c: CheckStackOverflow
    //     0xab337c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3380: cmp             SP, x16
    //     0xab3384: b.ls            #0xab342c
    // 0xab3388: InitAsync() -> Future<String>
    //     0xab3388: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0xab338c: bl              #0x661298  ; InitAsyncStub
    // 0xab3390: ldur            x0, [fp, #-0x10]
    // 0xab3394: LoadField: r1 = r0->field_f
    //     0xab3394: ldur            w1, [x0, #0xf]
    // 0xab3398: DecompressPointer r1
    //     0xab3398: add             x1, x1, HEAP, lsl #32
    // 0xab339c: cmp             w1, NULL
    // 0xab33a0: b.ne            #0xab3404
    // 0xab33a4: r16 = <String?>
    //     0xab33a4: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String?>
    // 0xab33a8: stp             x0, x16, [SP, #8]
    // 0xab33ac: r16 = "getDatabasesPath"
    //     0xab33ac: add             x16, PP, #0x43, lsl #12  ; [pp+0x433c8] "getDatabasesPath"
    //     0xab33b0: ldr             x16, [x16, #0x3c8]
    // 0xab33b4: str             x16, [SP]
    // 0xab33b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab33b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab33bc: r0 = safeInvokeMethod()
    //     0xab33bc: bl              #0xab3440  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::safeInvokeMethod
    // 0xab33c0: mov             x1, x0
    // 0xab33c4: stur            x1, [fp, #-0x18]
    // 0xab33c8: r0 = Await()
    //     0xab33c8: bl              #0x661044  ; AwaitStub
    // 0xab33cc: mov             x1, x0
    // 0xab33d0: cmp             w1, NULL
    // 0xab33d4: b.eq            #0xab340c
    // 0xab33d8: ldur            x2, [fp, #-0x10]
    // 0xab33dc: mov             x0, x1
    // 0xab33e0: StoreField: r2->field_f = r0
    //     0xab33e0: stur            w0, [x2, #0xf]
    //     0xab33e4: ldurb           w16, [x2, #-1]
    //     0xab33e8: ldurb           w17, [x0, #-1]
    //     0xab33ec: and             x16, x17, x16, lsr #2
    //     0xab33f0: tst             x16, HEAP, lsr #32
    //     0xab33f4: b.eq            #0xab33fc
    //     0xab33f8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xab33fc: mov             x0, x1
    // 0xab3400: b               #0xab3408
    // 0xab3404: mov             x0, x1
    // 0xab3408: r0 = ReturnAsyncNotFuture()
    //     0xab3408: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab340c: r0 = SqfliteDatabaseException()
    //     0xab340c: bl              #0xab3434  ; AllocateSqfliteDatabaseExceptionStub -> SqfliteDatabaseException (size=0x14)
    // 0xab3410: mov             x1, x0
    // 0xab3414: r0 = "getDatabasesPath is null"
    //     0xab3414: add             x0, PP, #0x43, lsl #12  ; [pp+0x433d0] "getDatabasesPath is null"
    //     0xab3418: ldr             x0, [x0, #0x3d0]
    // 0xab341c: StoreField: r1->field_7 = r0
    //     0xab341c: stur            w0, [x1, #7]
    // 0xab3420: mov             x0, x1
    // 0xab3424: r0 = Throw()
    //     0xab3424: bl              #0xec04b8  ; ThrowStub
    // 0xab3428: brk             #0
    // 0xab342c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab342c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3430: b               #0xab3388
  }
  Future<Y0> safeInvokeMethod<Y0>(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin, String, [Object?]) {
    // ** addr: 0xab3440, size: 0xdc
    // 0xab3440: EnterFrame
    //     0xab3440: stp             fp, lr, [SP, #-0x10]!
    //     0xab3444: mov             fp, SP
    // 0xab3448: AllocStack(0x30)
    //     0xab3448: sub             SP, SP, #0x30
    // 0xab344c: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r0, fp-0x20 */, dynamic _ /* r2, fp-0x18 */, [dynamic _ = Null /* r1, fp-0x10 */])
    //     0xab344c: ldur            w0, [x4, #0x13]
    //     0xab3450: sub             x1, x0, #4
    //     0xab3454: add             x0, fp, w1, sxtw #2
    //     0xab3458: ldr             x0, [x0, #0x18]
    //     0xab345c: stur            x0, [fp, #-0x20]
    //     0xab3460: add             x2, fp, w1, sxtw #2
    //     0xab3464: ldr             x2, [x2, #0x10]
    //     0xab3468: stur            x2, [fp, #-0x18]
    //     0xab346c: cmp             w1, #2
    //     0xab3470: b.lt            #0xab3484
    //     0xab3474: add             x3, fp, w1, sxtw #2
    //     0xab3478: ldr             x3, [x3, #8]
    //     0xab347c: mov             x1, x3
    //     0xab3480: b               #0xab3488
    //     0xab3484: mov             x1, NULL
    //     0xab3488: stur            x1, [fp, #-0x10]
    //     0xab348c: ldur            w3, [x4, #0xf]
    //     0xab3490: cbnz            w3, #0xab349c
    //     0xab3494: mov             x3, NULL
    //     0xab3498: b               #0xab34ac
    //     0xab349c: ldur            w3, [x4, #0x17]
    //     0xab34a0: add             x4, fp, w3, sxtw #2
    //     0xab34a4: ldr             x4, [x4, #0x10]
    //     0xab34a8: mov             x3, x4
    //     0xab34ac: stur            x3, [fp, #-8]
    // 0xab34b0: CheckStackOverflow
    //     0xab34b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab34b4: cmp             SP, x16
    //     0xab34b8: b.ls            #0xab3514
    // 0xab34bc: r1 = 3
    //     0xab34bc: movz            x1, #0x3
    // 0xab34c0: r0 = AllocateContext()
    //     0xab34c0: bl              #0xec126c  ; AllocateContextStub
    // 0xab34c4: mov             x1, x0
    // 0xab34c8: ldur            x0, [fp, #-0x20]
    // 0xab34cc: StoreField: r1->field_f = r0
    //     0xab34cc: stur            w0, [x1, #0xf]
    // 0xab34d0: ldur            x0, [fp, #-0x18]
    // 0xab34d4: StoreField: r1->field_13 = r0
    //     0xab34d4: stur            w0, [x1, #0x13]
    // 0xab34d8: ldur            x0, [fp, #-0x10]
    // 0xab34dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xab34dc: stur            w0, [x1, #0x17]
    // 0xab34e0: mov             x2, x1
    // 0xab34e4: r1 = Function '<anonymous closure>':.
    //     0xab34e4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43258] AnonymousClosure: (0xab3668), in [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::safeInvokeMethod (0xab3440)
    //     0xab34e8: ldr             x1, [x1, #0x258]
    // 0xab34ec: r0 = AllocateClosure()
    //     0xab34ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xab34f0: mov             x1, x0
    // 0xab34f4: ldur            x0, [fp, #-8]
    // 0xab34f8: StoreField: r1->field_b = r0
    //     0xab34f8: stur            w0, [x1, #0xb]
    // 0xab34fc: stp             x1, x0, [SP]
    // 0xab3500: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab3500: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab3504: r0 = wrapDatabaseException()
    //     0xab3504: bl              #0xab351c  ; [package:sqflite_platform_interface/src/platform_exception.dart] ::wrapDatabaseException
    // 0xab3508: LeaveFrame
    //     0xab3508: mov             SP, fp
    //     0xab350c: ldp             fp, lr, [SP], #0x10
    // 0xab3510: ret
    //     0xab3510: ret             
    // 0xab3514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3514: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3518: b               #0xab34bc
  }
  [closure] Future<Y0> <anonymous closure>(dynamic) {
    // ** addr: 0xab3668, size: 0x60
    // 0xab3668: EnterFrame
    //     0xab3668: stp             fp, lr, [SP, #-0x10]!
    //     0xab366c: mov             fp, SP
    // 0xab3670: AllocStack(0x18)
    //     0xab3670: sub             SP, SP, #0x18
    // 0xab3674: SetupParameters()
    //     0xab3674: ldr             x0, [fp, #0x10]
    //     0xab3678: ldur            w1, [x0, #0x17]
    //     0xab367c: add             x1, x1, HEAP, lsl #32
    // 0xab3680: CheckStackOverflow
    //     0xab3680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3684: cmp             SP, x16
    //     0xab3688: b.ls            #0xab36c0
    // 0xab368c: LoadField: r2 = r0->field_b
    //     0xab368c: ldur            w2, [x0, #0xb]
    // 0xab3690: DecompressPointer r2
    //     0xab3690: add             x2, x2, HEAP, lsl #32
    // 0xab3694: LoadField: r0 = r1->field_13
    //     0xab3694: ldur            w0, [x1, #0x13]
    // 0xab3698: DecompressPointer r0
    //     0xab3698: add             x0, x0, HEAP, lsl #32
    // 0xab369c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xab369c: ldur            w3, [x1, #0x17]
    // 0xab36a0: DecompressPointer r3
    //     0xab36a0: add             x3, x3, HEAP, lsl #32
    // 0xab36a4: stp             x0, x2, [SP, #8]
    // 0xab36a8: str             x3, [SP]
    // 0xab36ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab36ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab36b0: r0 = invokeMethod()
    //     0xab36b0: bl              #0xab36c8  ; [package:sqflite_platform_interface/src/sqflite_method_channel.dart] ::invokeMethod
    // 0xab36b4: LeaveFrame
    //     0xab36b4: mov             SP, fp
    //     0xab36b8: ldp             fp, lr, [SP], #0x10
    // 0xab36bc: ret
    //     0xab36bc: ret             
    // 0xab36c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab36c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab36c4: b               #0xab368c
  }
  [closure] Future<SqfliteDatabase> <anonymous closure>(dynamic) async {
    // ** addr: 0xab3934, size: 0x184
    // 0xab3934: EnterFrame
    //     0xab3934: stp             fp, lr, [SP, #-0x10]!
    //     0xab3938: mov             fp, SP
    // 0xab393c: AllocStack(0x90)
    //     0xab393c: sub             SP, SP, #0x90
    // 0xab3940: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1, fp-0x70 */)
    //     0xab3940: stur            NULL, [fp, #-8]
    //     0xab3944: movz            x0, #0
    //     0xab3948: add             x1, fp, w0, sxtw #2
    //     0xab394c: ldr             x1, [x1, #0x10]
    //     0xab3950: stur            x1, [fp, #-0x70]
    //     0xab3954: ldur            w2, [x1, #0x17]
    //     0xab3958: add             x2, x2, HEAP, lsl #32
    //     0xab395c: stur            x2, [fp, #-0x68]
    // 0xab3960: CheckStackOverflow
    //     0xab3960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3964: cmp             SP, x16
    //     0xab3968: b.ls            #0xab3aac
    // 0xab396c: InitAsync() -> Future<SqfliteDatabase>
    //     0xab396c: add             x0, PP, #0x43, lsl #12  ; [pp+0x43118] TypeArguments: <SqfliteDatabase>
    //     0xab3970: ldr             x0, [x0, #0x118]
    //     0xab3974: bl              #0x661298  ; InitAsyncStub
    // 0xab3978: ldur            x0, [fp, #-0x68]
    // 0xab397c: LoadField: r2 = r0->field_13
    //     0xab397c: ldur            w2, [x0, #0x13]
    // 0xab3980: DecompressPointer r2
    //     0xab3980: add             x2, x2, HEAP, lsl #32
    // 0xab3984: LoadField: r1 = r0->field_f
    //     0xab3984: ldur            w1, [x0, #0xf]
    // 0xab3988: DecompressPointer r1
    //     0xab3988: add             x1, x1, HEAP, lsl #32
    // 0xab398c: LoadField: r3 = r1->field_7
    //     0xab398c: ldur            w3, [x1, #7]
    // 0xab3990: DecompressPointer r3
    //     0xab3990: add             x3, x3, HEAP, lsl #32
    // 0xab3994: mov             x1, x3
    // 0xab3998: stur            x3, [fp, #-0x70]
    // 0xab399c: r0 = _getValueOrData()
    //     0xab399c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xab39a0: mov             x1, x0
    // 0xab39a4: ldur            x0, [fp, #-0x70]
    // 0xab39a8: LoadField: r2 = r0->field_f
    //     0xab39a8: ldur            w2, [x0, #0xf]
    // 0xab39ac: DecompressPointer r2
    //     0xab39ac: add             x2, x2, HEAP, lsl #32
    // 0xab39b0: cmp             w2, w1
    // 0xab39b4: b.ne            #0xab39c0
    // 0xab39b8: r0 = Null
    //     0xab39b8: mov             x0, NULL
    // 0xab39bc: b               #0xab39c4
    // 0xab39c0: mov             x0, x1
    // 0xab39c4: cmp             w0, NULL
    // 0xab39c8: r16 = true
    //     0xab39c8: add             x16, NULL, #0x20  ; true
    // 0xab39cc: r17 = false
    //     0xab39cc: add             x17, NULL, #0x30  ; false
    // 0xab39d0: csel            x1, x16, x17, eq
    // 0xab39d4: stur            x1, [fp, #-0x88]
    // 0xab39d8: tbnz            w1, #4, #0xab3a40
    // 0xab39dc: ldur            x0, [fp, #-0x68]
    // 0xab39e0: LoadField: r2 = r0->field_f
    //     0xab39e0: ldur            w2, [x0, #0xf]
    // 0xab39e4: DecompressPointer r2
    //     0xab39e4: add             x2, x2, HEAP, lsl #32
    // 0xab39e8: stur            x2, [fp, #-0x80]
    // 0xab39ec: LoadField: r3 = r0->field_13
    //     0xab39ec: ldur            w3, [x0, #0x13]
    // 0xab39f0: DecompressPointer r3
    //     0xab39f0: add             x3, x3, HEAP, lsl #32
    // 0xab39f4: stur            x3, [fp, #-0x78]
    // 0xab39f8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xab39f8: ldur            w4, [x0, #0x17]
    // 0xab39fc: DecompressPointer r4
    //     0xab39fc: add             x4, x4, HEAP, lsl #32
    // 0xab3a00: stur            x4, [fp, #-0x70]
    // 0xab3a04: r0 = SqfliteDatabaseOpenHelper()
    //     0xab3a04: bl              #0xab6ebc  ; AllocateSqfliteDatabaseOpenHelperStub -> SqfliteDatabaseOpenHelper (size=0x18)
    // 0xab3a08: mov             x4, x0
    // 0xab3a0c: ldur            x0, [fp, #-0x80]
    // 0xab3a10: stur            x4, [fp, #-0x90]
    // 0xab3a14: StoreField: r4->field_7 = r0
    //     0xab3a14: stur            w0, [x4, #7]
    // 0xab3a18: ldur            x2, [fp, #-0x78]
    // 0xab3a1c: StoreField: r4->field_f = r2
    //     0xab3a1c: stur            w2, [x4, #0xf]
    // 0xab3a20: ldur            x1, [fp, #-0x70]
    // 0xab3a24: StoreField: r4->field_b = r1
    //     0xab3a24: stur            w1, [x4, #0xb]
    // 0xab3a28: LoadField: r1 = r0->field_7
    //     0xab3a28: ldur            w1, [x0, #7]
    // 0xab3a2c: DecompressPointer r1
    //     0xab3a2c: add             x1, x1, HEAP, lsl #32
    // 0xab3a30: mov             x3, x4
    // 0xab3a34: r0 = []=()
    //     0xab3a34: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xab3a38: ldur            x1, [fp, #-0x90]
    // 0xab3a3c: b               #0xab3a44
    // 0xab3a40: mov             x1, x0
    // 0xab3a44: cmp             w1, NULL
    // 0xab3a48: b.eq            #0xab3ab4
    // 0xab3a4c: r0 = openDatabase()
    //     0xab3a4c: bl              #0xab3b14  ; [package:sqflite_common/src/database.dart] SqfliteDatabaseOpenHelper::openDatabase
    // 0xab3a50: mov             x1, x0
    // 0xab3a54: stur            x1, [fp, #-0x70]
    // 0xab3a58: r0 = Await()
    //     0xab3a58: bl              #0x661044  ; AwaitStub
    // 0xab3a5c: r0 = ReturnAsync()
    //     0xab3a5c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab3a60: sub             SP, fp, #0x90
    // 0xab3a64: mov             x4, x0
    // 0xab3a68: mov             x3, x1
    // 0xab3a6c: stur            x0, [fp, #-0x68]
    // 0xab3a70: ldur            x0, [fp, #-0x58]
    // 0xab3a74: stur            x1, [fp, #-0x70]
    // 0xab3a78: r16 = true
    //     0xab3a78: add             x16, NULL, #0x20  ; true
    // 0xab3a7c: cmp             w0, w16
    // 0xab3a80: b.ne            #0xab3a9c
    // 0xab3a84: ldur            x0, [fp, #-0x20]
    // 0xab3a88: LoadField: r1 = r0->field_f
    //     0xab3a88: ldur            w1, [x0, #0xf]
    // 0xab3a8c: DecompressPointer r1
    //     0xab3a8c: add             x1, x1, HEAP, lsl #32
    // 0xab3a90: LoadField: r2 = r0->field_13
    //     0xab3a90: ldur            w2, [x0, #0x13]
    // 0xab3a94: DecompressPointer r2
    //     0xab3a94: add             x2, x2, HEAP, lsl #32
    // 0xab3a98: r0 = removeDatabaseOpenHelper()
    //     0xab3a98: bl              #0xab3ad8  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::removeDatabaseOpenHelper
    // 0xab3a9c: ldur            x0, [fp, #-0x68]
    // 0xab3aa0: ldur            x1, [fp, #-0x70]
    // 0xab3aa4: r0 = ReThrow()
    //     0xab3aa4: bl              #0xec048c  ; ReThrowStub
    // 0xab3aa8: brk             #0
    // 0xab3aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3aac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3ab0: b               #0xab396c
    // 0xab3ab4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xab3ab4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ removeDatabaseOpenHelper(/* No info */) {
    // ** addr: 0xab3ad8, size: 0x3c
    // 0xab3ad8: EnterFrame
    //     0xab3ad8: stp             fp, lr, [SP, #-0x10]!
    //     0xab3adc: mov             fp, SP
    // 0xab3ae0: CheckStackOverflow
    //     0xab3ae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3ae4: cmp             SP, x16
    //     0xab3ae8: b.ls            #0xab3b0c
    // 0xab3aec: LoadField: r0 = r1->field_7
    //     0xab3aec: ldur            w0, [x1, #7]
    // 0xab3af0: DecompressPointer r0
    //     0xab3af0: add             x0, x0, HEAP, lsl #32
    // 0xab3af4: mov             x1, x0
    // 0xab3af8: r0 = remove()
    //     0xab3af8: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xab3afc: r0 = Null
    //     0xab3afc: mov             x0, NULL
    // 0xab3b00: LeaveFrame
    //     0xab3b00: mov             SP, fp
    //     0xab3b04: ldp             fp, lr, [SP], #0x10
    // 0xab3b08: ret
    //     0xab3b08: ret             
    // 0xab3b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab3b0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3b10: b               #0xab3aec
  }
  _ deleteDatabase(/* No info */) async {
    // ** addr: 0xab6b08, size: 0xd8
    // 0xab6b08: EnterFrame
    //     0xab6b08: stp             fp, lr, [SP, #-0x10]!
    //     0xab6b0c: mov             fp, SP
    // 0xab6b10: AllocStack(0x38)
    //     0xab6b10: sub             SP, SP, #0x38
    // 0xab6b14: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab6b14: stur            NULL, [fp, #-8]
    //     0xab6b18: stur            x1, [fp, #-0x10]
    //     0xab6b1c: stur            x2, [fp, #-0x18]
    // 0xab6b20: CheckStackOverflow
    //     0xab6b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6b24: cmp             SP, x16
    //     0xab6b28: b.ls            #0xab6bd8
    // 0xab6b2c: r1 = 2
    //     0xab6b2c: movz            x1, #0x2
    // 0xab6b30: r0 = AllocateContext()
    //     0xab6b30: bl              #0xec126c  ; AllocateContextStub
    // 0xab6b34: mov             x2, x0
    // 0xab6b38: ldur            x1, [fp, #-0x10]
    // 0xab6b3c: stur            x2, [fp, #-0x20]
    // 0xab6b40: StoreField: r2->field_f = r1
    //     0xab6b40: stur            w1, [x2, #0xf]
    // 0xab6b44: ldur            x0, [fp, #-0x18]
    // 0xab6b48: StoreField: r2->field_13 = r0
    //     0xab6b48: stur            w0, [x2, #0x13]
    // 0xab6b4c: InitAsync() -> Future<void?>
    //     0xab6b4c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab6b50: bl              #0x661298  ; InitAsyncStub
    // 0xab6b54: ldur            x0, [fp, #-0x20]
    // 0xab6b58: LoadField: r2 = r0->field_13
    //     0xab6b58: ldur            w2, [x0, #0x13]
    // 0xab6b5c: DecompressPointer r2
    //     0xab6b5c: add             x2, x2, HEAP, lsl #32
    // 0xab6b60: ldur            x1, [fp, #-0x10]
    // 0xab6b64: r0 = fixPath()
    //     0xab6b64: bl              #0xab23c0  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::fixPath
    // 0xab6b68: mov             x1, x0
    // 0xab6b6c: stur            x1, [fp, #-0x18]
    // 0xab6b70: r0 = Await()
    //     0xab6b70: bl              #0x661044  ; AwaitStub
    // 0xab6b74: mov             x1, x0
    // 0xab6b78: ldur            x3, [fp, #-0x20]
    // 0xab6b7c: StoreField: r3->field_13 = r0
    //     0xab6b7c: stur            w0, [x3, #0x13]
    //     0xab6b80: tbz             w0, #0, #0xab6b9c
    //     0xab6b84: ldurb           w16, [x3, #-1]
    //     0xab6b88: ldurb           w17, [x0, #-1]
    //     0xab6b8c: and             x16, x17, x16, lsr #2
    //     0xab6b90: tst             x16, HEAP, lsr #32
    //     0xab6b94: b.eq            #0xab6b9c
    //     0xab6b98: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xab6b9c: mov             x2, x1
    // 0xab6ba0: ldur            x1, [fp, #-0x10]
    // 0xab6ba4: r0 = _getDatabaseOpenLock()
    //     0xab6ba4: bl              #0xab2148  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::_getDatabaseOpenLock
    // 0xab6ba8: ldur            x2, [fp, #-0x20]
    // 0xab6bac: r1 = Function '<anonymous closure>':.
    //     0xab6bac: add             x1, PP, #0x43, lsl #12  ; [pp+0x43248] AnonymousClosure: (0xab6be0), in [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::deleteDatabase (0xab6b08)
    //     0xab6bb0: ldr             x1, [x1, #0x248]
    // 0xab6bb4: stur            x0, [fp, #-0x10]
    // 0xab6bb8: r0 = AllocateClosure()
    //     0xab6bb8: bl              #0xec1630  ; AllocateClosureStub
    // 0xab6bbc: r16 = <void?>
    //     0xab6bbc: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xab6bc0: ldur            lr, [fp, #-0x10]
    // 0xab6bc4: stp             lr, x16, [SP, #8]
    // 0xab6bc8: str             x0, [SP]
    // 0xab6bcc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xab6bcc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xab6bd0: r0 = synchronized()
    //     0xab6bd0: bl              #0xaaeec8  ; [package:synchronized/src/reentrant_lock.dart] ReentrantLock::synchronized
    // 0xab6bd4: r0 = ReturnAsync()
    //     0xab6bd4: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab6bd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6bd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6bdc: b               #0xab6b2c
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xab6be0, size: 0x88
    // 0xab6be0: EnterFrame
    //     0xab6be0: stp             fp, lr, [SP, #-0x10]!
    //     0xab6be4: mov             fp, SP
    // 0xab6be8: AllocStack(0x18)
    //     0xab6be8: sub             SP, SP, #0x18
    // 0xab6bec: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1 */)
    //     0xab6bec: stur            NULL, [fp, #-8]
    //     0xab6bf0: movz            x0, #0
    //     0xab6bf4: add             x1, fp, w0, sxtw #2
    //     0xab6bf8: ldr             x1, [x1, #0x10]
    //     0xab6bfc: ldur            w2, [x1, #0x17]
    //     0xab6c00: add             x2, x2, HEAP, lsl #32
    //     0xab6c04: stur            x2, [fp, #-0x10]
    // 0xab6c08: CheckStackOverflow
    //     0xab6c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6c0c: cmp             SP, x16
    //     0xab6c10: b.ls            #0xab6c60
    // 0xab6c14: InitAsync() -> Future<void?>
    //     0xab6c14: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab6c18: bl              #0x661298  ; InitAsyncStub
    // 0xab6c1c: ldur            x0, [fp, #-0x10]
    // 0xab6c20: LoadField: r1 = r0->field_f
    //     0xab6c20: ldur            w1, [x0, #0xf]
    // 0xab6c24: DecompressPointer r1
    //     0xab6c24: add             x1, x1, HEAP, lsl #32
    // 0xab6c28: LoadField: r2 = r0->field_13
    //     0xab6c28: ldur            w2, [x0, #0x13]
    // 0xab6c2c: DecompressPointer r2
    //     0xab6c2c: add             x2, x2, HEAP, lsl #32
    // 0xab6c30: r0 = removeDatabaseOpenHelper()
    //     0xab6c30: bl              #0xab3ad8  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::removeDatabaseOpenHelper
    // 0xab6c34: ldur            x0, [fp, #-0x10]
    // 0xab6c38: LoadField: r1 = r0->field_f
    //     0xab6c38: ldur            w1, [x0, #0xf]
    // 0xab6c3c: DecompressPointer r1
    //     0xab6c3c: add             x1, x1, HEAP, lsl #32
    // 0xab6c40: LoadField: r2 = r0->field_13
    //     0xab6c40: ldur            w2, [x0, #0x13]
    // 0xab6c44: DecompressPointer r2
    //     0xab6c44: add             x2, x2, HEAP, lsl #32
    // 0xab6c48: r0 = invokeDeleteDatabase()
    //     0xab6c48: bl              #0xab6c68  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::invokeDeleteDatabase
    // 0xab6c4c: mov             x1, x0
    // 0xab6c50: stur            x1, [fp, #-0x18]
    // 0xab6c54: r0 = Await()
    //     0xab6c54: bl              #0x661044  ; AwaitStub
    // 0xab6c58: r0 = Null
    //     0xab6c58: mov             x0, NULL
    // 0xab6c5c: r0 = ReturnAsyncNotFuture()
    //     0xab6c5c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xab6c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6c60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6c64: b               #0xab6c14
  }
  _ invokeDeleteDatabase(/* No info */) async {
    // ** addr: 0xab6c68, size: 0x84
    // 0xab6c68: EnterFrame
    //     0xab6c68: stp             fp, lr, [SP, #-0x10]!
    //     0xab6c6c: mov             fp, SP
    // 0xab6c70: AllocStack(0x38)
    //     0xab6c70: sub             SP, SP, #0x38
    // 0xab6c74: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xab6c74: stur            NULL, [fp, #-8]
    //     0xab6c78: stur            x1, [fp, #-0x10]
    //     0xab6c7c: stur            x2, [fp, #-0x18]
    // 0xab6c80: CheckStackOverflow
    //     0xab6c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6c84: cmp             SP, x16
    //     0xab6c88: b.ls            #0xab6ce4
    // 0xab6c8c: InitAsync() -> Future<void?>
    //     0xab6c8c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xab6c90: bl              #0x661298  ; InitAsyncStub
    // 0xab6c94: r1 = Null
    //     0xab6c94: mov             x1, NULL
    // 0xab6c98: r2 = 4
    //     0xab6c98: movz            x2, #0x4
    // 0xab6c9c: r0 = AllocateArray()
    //     0xab6c9c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab6ca0: r16 = "path"
    //     0xab6ca0: ldr             x16, [PP, #0x3638]  ; [pp+0x3638] "path"
    // 0xab6ca4: StoreField: r0->field_f = r16
    //     0xab6ca4: stur            w16, [x0, #0xf]
    // 0xab6ca8: ldur            x1, [fp, #-0x18]
    // 0xab6cac: StoreField: r0->field_13 = r1
    //     0xab6cac: stur            w1, [x0, #0x13]
    // 0xab6cb0: r16 = <String, Object?>
    //     0xab6cb0: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0xab6cb4: ldr             x16, [x16, #0x738]
    // 0xab6cb8: stp             x0, x16, [SP]
    // 0xab6cbc: r0 = Map._fromLiteral()
    //     0xab6cbc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xab6cc0: r16 = <void?>
    //     0xab6cc0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xab6cc4: ldur            lr, [fp, #-0x10]
    // 0xab6cc8: stp             lr, x16, [SP, #0x10]
    // 0xab6ccc: r16 = "deleteDatabase"
    //     0xab6ccc: add             x16, PP, #0x43, lsl #12  ; [pp+0x43250] "deleteDatabase"
    //     0xab6cd0: ldr             x16, [x16, #0x250]
    // 0xab6cd4: stp             x0, x16, [SP]
    // 0xab6cd8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab6cd8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab6cdc: r0 = safeInvokeMethod()
    //     0xab6cdc: bl              #0xab3440  ; [package:sqflite_platform_interface/src/factory_platform.dart] _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin::safeInvokeMethod
    // 0xab6ce0: r0 = ReturnAsync()
    //     0xab6ce0: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab6ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6ce8: b               #0xab6c8c
  }
  _ newDatabase(/* No info */) {
    // ** addr: 0xab6d6c, size: 0x9c
    // 0xab6d6c: EnterFrame
    //     0xab6d6c: stp             fp, lr, [SP, #-0x10]!
    //     0xab6d70: mov             fp, SP
    // 0xab6d74: AllocStack(0x18)
    //     0xab6d74: sub             SP, SP, #0x18
    // 0xab6d78: SetupParameters(_SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin this /* r1 => r2 */, dynamic _ /* r2 => r1, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xab6d78: mov             x16, x2
    //     0xab6d7c: mov             x2, x1
    //     0xab6d80: mov             x1, x16
    //     0xab6d84: mov             x0, x3
    //     0xab6d88: stur            x1, [fp, #-8]
    //     0xab6d8c: stur            x3, [fp, #-0x10]
    // 0xab6d90: CheckStackOverflow
    //     0xab6d90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6d94: cmp             SP, x16
    //     0xab6d98: b.ls            #0xab6e00
    // 0xab6d9c: r0 = SqfliteDatabaseBase()
    //     0xab6d9c: bl              #0xab6eb0  ; AllocateSqfliteDatabaseBaseStub -> SqfliteDatabaseBase (size=0x30)
    // 0xab6da0: mov             x1, x0
    // 0xab6da4: stur            x0, [fp, #-0x18]
    // 0xab6da8: r0 = _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin()
    //     0xab6da8: bl              #0xab6e08  ; [package:sqflite_common/src/database_mixin.dart] _SqfliteDatabaseBase&Object&SqfliteDatabaseMixin::_SqfliteDatabaseBase&Object&SqfliteDatabaseMixin
    // 0xab6dac: ldur            x0, [fp, #-8]
    // 0xab6db0: ldur            x1, [fp, #-0x18]
    // 0xab6db4: StoreField: r1->field_27 = r0
    //     0xab6db4: stur            w0, [x1, #0x27]
    //     0xab6db8: ldurb           w16, [x1, #-1]
    //     0xab6dbc: ldurb           w17, [x0, #-1]
    //     0xab6dc0: and             x16, x17, x16, lsr #2
    //     0xab6dc4: tst             x16, HEAP, lsr #32
    //     0xab6dc8: b.eq            #0xab6dd0
    //     0xab6dcc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab6dd0: ldur            x0, [fp, #-0x10]
    // 0xab6dd4: StoreField: r1->field_b = r0
    //     0xab6dd4: stur            w0, [x1, #0xb]
    //     0xab6dd8: ldurb           w16, [x1, #-1]
    //     0xab6ddc: ldurb           w17, [x0, #-1]
    //     0xab6de0: and             x16, x17, x16, lsr #2
    //     0xab6de4: tst             x16, HEAP, lsr #32
    //     0xab6de8: b.eq            #0xab6df0
    //     0xab6dec: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xab6df0: mov             x0, x1
    // 0xab6df4: LeaveFrame
    //     0xab6df4: mov             SP, fp
    //     0xab6df8: ldp             fp, lr, [SP], #0x10
    // 0xab6dfc: ret
    //     0xab6dfc: ret             
    // 0xab6e00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6e00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6e04: b               #0xab6d9c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc41388, size: 0xc
    // 0xc41388: r0 = "SqfliteDatabaseFactory(sqflite)"
    //     0xc41388: add             x0, PP, #0x12, lsl #12  ; [pp+0x12780] "SqfliteDatabaseFactory(sqflite)"
    //     0xc4138c: ldr             x0, [x0, #0x780]
    // 0xc41390: ret
    //     0xc41390: ret             
  }
}

// class id: 457, size: 0x18, field offset: 0x18
class SqfliteDatabaseFactoryImpl extends _SqfliteDatabaseFactoryImpl&Object&SqfliteDatabaseFactoryMixin {
}
