// lib: , url: package:sqflite_platform_interface/src/platform_exception.dart

// class id: 1051168, size: 0x8
class :: {

  static _ wrapDatabaseException(/* No info */) async {
    // ** addr: 0xab351c, size: 0x14c
    // 0xab351c: EnterFrame
    //     0xab351c: stp             fp, lr, [SP, #-0x10]!
    //     0xab3520: mov             fp, SP
    // 0xab3524: AllocStack(0x78)
    //     0xab3524: sub             SP, SP, #0x78
    // 0xab3528: SetupParameters(dynamic _ /* r1, fp-0x58 */)
    //     0xab3528: stur            NULL, [fp, #-8]
    //     0xab352c: movz            x0, #0
    //     0xab3530: stur            x4, [fp, #-0x60]
    //     0xab3534: add             x1, fp, w0, sxtw #2
    //     0xab3538: ldr             x1, [x1, #0x10]
    //     0xab353c: stur            x1, [fp, #-0x58]
    //     0xab3540: ldur            w0, [x4, #0xf]
    //     0xab3544: cbnz            w0, #0xab3550
    //     0xab3548: mov             x2, NULL
    //     0xab354c: b               #0xab355c
    //     0xab3550: ldur            w0, [x4, #0x17]
    //     0xab3554: add             x2, fp, w0, sxtw #2
    //     0xab3558: ldr             x2, [x2, #0x10]
    //     0xab355c: stur            x2, [fp, #-0x50]
    // 0xab3560: CheckStackOverflow
    //     0xab3560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3564: cmp             SP, x16
    //     0xab3568: b.ls            #0xab365c
    // 0xab356c: mov             x0, x2
    // 0xab3570: r0 = InitAsync()
    //     0xab3570: bl              #0x661298  ; InitAsyncStub
    // 0xab3574: ldur            x16, [fp, #-0x58]
    // 0xab3578: str             x16, [SP]
    // 0xab357c: ldur            x0, [fp, #-0x58]
    // 0xab3580: ClosureCall
    //     0xab3580: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xab3584: ldur            x2, [x0, #0x1f]
    //     0xab3588: blr             x2
    // 0xab358c: mov             x1, x0
    // 0xab3590: stur            x1, [fp, #-0x50]
    // 0xab3594: r0 = Await()
    //     0xab3594: bl              #0x661044  ; AwaitStub
    // 0xab3598: r0 = ReturnAsync()
    //     0xab3598: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab359c: sub             SP, fp, #0x78
    // 0xab35a0: mov             x2, x0
    // 0xab35a4: stur            x0, [fp, #-0x50]
    // 0xab35a8: stur            x1, [fp, #-0x58]
    // 0xab35ac: r0 = 60
    //     0xab35ac: movz            x0, #0x3c
    // 0xab35b0: branchIfSmi(r2, 0xab35bc)
    //     0xab35b0: tbz             w2, #0, #0xab35bc
    // 0xab35b4: r0 = LoadClassIdInstr(r2)
    //     0xab35b4: ldur            x0, [x2, #-1]
    //     0xab35b8: ubfx            x0, x0, #0xc, #0x14
    // 0xab35bc: cmp             x0, #0xaf3
    // 0xab35c0: b.ne            #0xab364c
    // 0xab35c4: LoadField: r0 = r2->field_7
    //     0xab35c4: ldur            w0, [x2, #7]
    // 0xab35c8: DecompressPointer r0
    //     0xab35c8: add             x0, x0, HEAP, lsl #32
    // 0xab35cc: r3 = LoadClassIdInstr(r0)
    //     0xab35cc: ldur            x3, [x0, #-1]
    //     0xab35d0: ubfx            x3, x3, #0xc, #0x14
    // 0xab35d4: r16 = "sqlite_error"
    //     0xab35d4: add             x16, PP, #0x43, lsl #12  ; [pp+0x43060] "sqlite_error"
    //     0xab35d8: ldr             x16, [x16, #0x60]
    // 0xab35dc: stp             x16, x0, [SP]
    // 0xab35e0: mov             x0, x3
    // 0xab35e4: mov             lr, x0
    // 0xab35e8: ldr             lr, [x21, lr, lsl #3]
    // 0xab35ec: blr             lr
    // 0xab35f0: tbnz            w0, #4, #0xab363c
    // 0xab35f4: ldur            x0, [fp, #-0x50]
    // 0xab35f8: LoadField: r1 = r0->field_b
    //     0xab35f8: ldur            w1, [x0, #0xb]
    // 0xab35fc: DecompressPointer r1
    //     0xab35fc: add             x1, x1, HEAP, lsl #32
    // 0xab3600: stur            x1, [fp, #-0x68]
    // 0xab3604: cmp             w1, NULL
    // 0xab3608: b.eq            #0xab3664
    // 0xab360c: LoadField: r2 = r0->field_f
    //     0xab360c: ldur            w2, [x0, #0xf]
    // 0xab3610: DecompressPointer r2
    //     0xab3610: add             x2, x2, HEAP, lsl #32
    // 0xab3614: stur            x2, [fp, #-0x60]
    // 0xab3618: r0 = SqfliteDatabaseException()
    //     0xab3618: bl              #0xab3434  ; AllocateSqfliteDatabaseExceptionStub -> SqfliteDatabaseException (size=0x14)
    // 0xab361c: mov             x1, x0
    // 0xab3620: ldur            x0, [fp, #-0x60]
    // 0xab3624: StoreField: r1->field_b = r0
    //     0xab3624: stur            w0, [x1, #0xb]
    // 0xab3628: ldur            x0, [fp, #-0x68]
    // 0xab362c: StoreField: r1->field_7 = r0
    //     0xab362c: stur            w0, [x1, #7]
    // 0xab3630: mov             x0, x1
    // 0xab3634: r0 = Throw()
    //     0xab3634: bl              #0xec04b8  ; ThrowStub
    // 0xab3638: brk             #0
    // 0xab363c: ldur            x0, [fp, #-0x50]
    // 0xab3640: ldur            x1, [fp, #-0x58]
    // 0xab3644: r0 = ReThrow()
    //     0xab3644: bl              #0xec048c  ; ReThrowStub
    // 0xab3648: brk             #0
    // 0xab364c: mov             x0, x2
    // 0xab3650: ldur            x1, [fp, #-0x58]
    // 0xab3654: r0 = ReThrow()
    //     0xab3654: bl              #0xec048c  ; ReThrowStub
    // 0xab3658: brk             #0
    // 0xab365c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab365c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab3660: b               #0xab356c
    // 0xab3664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab3664: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
