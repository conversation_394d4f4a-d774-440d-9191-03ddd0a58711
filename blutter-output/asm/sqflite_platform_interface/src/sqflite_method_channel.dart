// lib: , url: package:sqflite_platform_interface/src/sqflite_method_channel.dart

// class id: 1051169, size: 0x8
class :: {

  static Future<Y0> invokeMethod<Y0>(String, Object?) async {
    // ** addr: 0xab36c8, size: 0xe4
    // 0xab36c8: EnterFrame
    //     0xab36c8: stp             fp, lr, [SP, #-0x10]!
    //     0xab36cc: mov             fp, SP
    // 0xab36d0: AllocStack(0x40)
    //     0xab36d0: sub             SP, SP, #0x40
    // 0xab36d4: SetupParameters(dynamic _ /* r1, fp-0x20 */, dynamic _ /* r2, fp-0x18 */)
    //     0xab36d4: stur            NULL, [fp, #-8]
    //     0xab36d8: movz            x0, #0
    //     0xab36dc: add             x1, fp, w0, sxtw #2
    //     0xab36e0: ldr             x1, [x1, #0x18]
    //     0xab36e4: stur            x1, [fp, #-0x20]
    //     0xab36e8: add             x2, fp, w0, sxtw #2
    //     0xab36ec: ldr             x2, [x2, #0x10]
    //     0xab36f0: stur            x2, [fp, #-0x18]
    // 0xab36f4: LoadField: r0 = r4->field_f
    //     0xab36f4: ldur            w0, [x4, #0xf]
    // 0xab36f8: cbnz            w0, #0xab3704
    // 0xab36fc: r3 = Null
    //     0xab36fc: mov             x3, NULL
    // 0xab3700: b               #0xab3710
    // 0xab3704: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xab3704: ldur            w0, [x4, #0x17]
    // 0xab3708: add             x3, fp, w0, sxtw #2
    // 0xab370c: ldr             x3, [x3, #0x10]
    // 0xab3710: stur            x3, [fp, #-0x10]
    // 0xab3714: CheckStackOverflow
    //     0xab3714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab3718: cmp             SP, x16
    //     0xab371c: b.ls            #0xab37a4
    // 0xab3720: mov             x0, x3
    // 0xab3724: r0 = InitAsync()
    //     0xab3724: bl              #0x661298  ; InitAsyncStub
    // 0xab3728: ldur            x16, [fp, #-0x10]
    // 0xab372c: r30 = Instance_MethodChannel
    //     0xab372c: add             lr, PP, #0x43, lsl #12  ; [pp+0x43040] Obj!MethodChannel@e112d1
    //     0xab3730: ldr             lr, [lr, #0x40]
    // 0xab3734: stp             lr, x16, [SP, #0x10]
    // 0xab3738: ldur            x16, [fp, #-0x20]
    // 0xab373c: ldur            lr, [fp, #-0x18]
    // 0xab3740: stp             lr, x16, [SP]
    // 0xab3744: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xab3744: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xab3748: r0 = invokeMethod()
    //     0xab3748: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xab374c: mov             x1, x0
    // 0xab3750: stur            x1, [fp, #-0x18]
    // 0xab3754: r0 = Await()
    //     0xab3754: bl              #0x661044  ; AwaitStub
    // 0xab3758: mov             x3, x0
    // 0xab375c: stur            x3, [fp, #-0x18]
    // 0xab3760: cmp             w3, NULL
    // 0xab3764: b.ne            #0xab379c
    // 0xab3768: mov             x0, x3
    // 0xab376c: ldur            x1, [fp, #-0x10]
    // 0xab3770: r2 = Null
    //     0xab3770: mov             x2, NULL
    // 0xab3774: cmp             w1, NULL
    // 0xab3778: b.eq            #0xab379c
    // 0xab377c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xab377c: ldur            w4, [x1, #0x17]
    // 0xab3780: DecompressPointer r4
    //     0xab3780: add             x4, x4, HEAP, lsl #32
    // 0xab3784: r8 = Y0
    //     0xab3784: add             x8, PP, #0x43, lsl #12  ; [pp+0x43048] TypeParameter: Y0
    //     0xab3788: ldr             x8, [x8, #0x48]
    // 0xab378c: LoadField: r9 = r4->field_7
    //     0xab378c: ldur            x9, [x4, #7]
    // 0xab3790: r3 = Null
    //     0xab3790: add             x3, PP, #0x43, lsl #12  ; [pp+0x43050] Null
    //     0xab3794: ldr             x3, [x3, #0x50]
    // 0xab3798: blr             x9
    // 0xab379c: ldur            x0, [fp, #-0x18]
    // 0xab37a0: r0 = ReturnAsync()
    //     0xab37a0: b               #0x6576a4  ; ReturnAsyncStub
    // 0xab37a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab37a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab37a8: b               #0xab3720
  }
}
