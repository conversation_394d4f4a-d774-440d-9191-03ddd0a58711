// lib: , url: package:stack_trace/src/frame.dart

// class id: 1051170, size: 0x8
class :: {

  static late final RegExp _vmFrame; // offset: 0xfb8
  static late final RegExp _asyncBody; // offset: 0xfbc

  static RegExp _asyncBody() {
    // ** addr: 0xec8d78, size: 0x58
    // 0xec8d78: EnterFrame
    //     0xec8d78: stp             fp, lr, [SP, #-0x10]!
    //     0xec8d7c: mov             fp, SP
    // 0xec8d80: AllocStack(0x30)
    //     0xec8d80: sub             SP, SP, #0x30
    // 0xec8d84: CheckStackOverflow
    //     0xec8d84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec8d88: cmp             SP, x16
    //     0xec8d8c: b.ls            #0xec8dc8
    // 0xec8d90: r16 = "<(<anonymous closure>|[^>]+)_async_body>"
    //     0xec8d90: add             x16, PP, #0xd, lsl #12  ; [pp+0xd610] "<(<anonymous closure>|[^>]+)_async_body>"
    //     0xec8d94: ldr             x16, [x16, #0x610]
    // 0xec8d98: stp             x16, NULL, [SP, #0x20]
    // 0xec8d9c: r16 = false
    //     0xec8d9c: add             x16, NULL, #0x30  ; false
    // 0xec8da0: r30 = true
    //     0xec8da0: add             lr, NULL, #0x20  ; true
    // 0xec8da4: stp             lr, x16, [SP, #0x10]
    // 0xec8da8: r16 = false
    //     0xec8da8: add             x16, NULL, #0x30  ; false
    // 0xec8dac: r30 = false
    //     0xec8dac: add             lr, NULL, #0x30  ; false
    // 0xec8db0: stp             lr, x16, [SP]
    // 0xec8db4: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xec8db4: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xec8db8: r0 = _RegExp()
    //     0xec8db8: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xec8dbc: LeaveFrame
    //     0xec8dbc: mov             SP, fp
    //     0xec8dc0: ldp             fp, lr, [SP], #0x10
    // 0xec8dc4: ret
    //     0xec8dc4: ret             
    // 0xec8dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec8dc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec8dcc: b               #0xec8d90
  }
  static RegExp _vmFrame() {
    // ** addr: 0xec8dd0, size: 0x58
    // 0xec8dd0: EnterFrame
    //     0xec8dd0: stp             fp, lr, [SP, #-0x10]!
    //     0xec8dd4: mov             fp, SP
    // 0xec8dd8: AllocStack(0x30)
    //     0xec8dd8: sub             SP, SP, #0x30
    // 0xec8ddc: CheckStackOverflow
    //     0xec8ddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec8de0: cmp             SP, x16
    //     0xec8de4: b.ls            #0xec8e20
    // 0xec8de8: r16 = "^#\\d+\\s+(\\S.*) \\((.+\?)((\?::\\d+){0,2})\\)$"
    //     0xec8de8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd618] "^#\\d+\\s+(\\S.*) \\((.+\?)((\?::\\d+){0,2})\\)$"
    //     0xec8dec: ldr             x16, [x16, #0x618]
    // 0xec8df0: stp             x16, NULL, [SP, #0x20]
    // 0xec8df4: r16 = false
    //     0xec8df4: add             x16, NULL, #0x30  ; false
    // 0xec8df8: r30 = true
    //     0xec8df8: add             lr, NULL, #0x20  ; true
    // 0xec8dfc: stp             lr, x16, [SP, #0x10]
    // 0xec8e00: r16 = false
    //     0xec8e00: add             x16, NULL, #0x30  ; false
    // 0xec8e04: r30 = false
    //     0xec8e04: add             lr, NULL, #0x30  ; false
    // 0xec8e08: stp             lr, x16, [SP]
    // 0xec8e0c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xec8e0c: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xec8e10: r0 = _RegExp()
    //     0xec8e10: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xec8e14: LeaveFrame
    //     0xec8e14: mov             SP, fp
    //     0xec8e18: ldp             fp, lr, [SP], #0x10
    // 0xec8e1c: ret
    //     0xec8e1c: ret             
    // 0xec8e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec8e20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec8e24: b               #0xec8de8
  }
}

// class id: 455, size: 0x18, field offset: 0x8
class Frame extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc41394, size: 0x74
    // 0xc41394: EnterFrame
    //     0xc41394: stp             fp, lr, [SP, #-0x10]!
    //     0xc41398: mov             fp, SP
    // 0xc4139c: AllocStack(0x10)
    //     0xc4139c: sub             SP, SP, #0x10
    // 0xc413a0: CheckStackOverflow
    //     0xc413a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc413a4: cmp             SP, x16
    //     0xc413a8: b.ls            #0xc41400
    // 0xc413ac: ldr             x1, [fp, #0x10]
    // 0xc413b0: r0 = location()
    //     0xc413b0: bl              #0xeb85e4  ; [package:stack_trace/src/frame.dart] Frame::location
    // 0xc413b4: r1 = Null
    //     0xc413b4: mov             x1, NULL
    // 0xc413b8: r2 = 6
    //     0xc413b8: movz            x2, #0x6
    // 0xc413bc: stur            x0, [fp, #-8]
    // 0xc413c0: r0 = AllocateArray()
    //     0xc413c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc413c4: mov             x1, x0
    // 0xc413c8: ldur            x0, [fp, #-8]
    // 0xc413cc: StoreField: r1->field_f = r0
    //     0xc413cc: stur            w0, [x1, #0xf]
    // 0xc413d0: r16 = " in "
    //     0xc413d0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bb10] " in "
    //     0xc413d4: ldr             x16, [x16, #0xb10]
    // 0xc413d8: StoreField: r1->field_13 = r16
    //     0xc413d8: stur            w16, [x1, #0x13]
    // 0xc413dc: ldr             x0, [fp, #0x10]
    // 0xc413e0: LoadField: r2 = r0->field_13
    //     0xc413e0: ldur            w2, [x0, #0x13]
    // 0xc413e4: DecompressPointer r2
    //     0xc413e4: add             x2, x2, HEAP, lsl #32
    // 0xc413e8: ArrayStore: r1[0] = r2  ; List_4
    //     0xc413e8: stur            w2, [x1, #0x17]
    // 0xc413ec: str             x1, [SP]
    // 0xc413f0: r0 = _interpolate()
    //     0xc413f0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc413f4: LeaveFrame
    //     0xc413f4: mov             SP, fp
    //     0xc413f8: ldp             fp, lr, [SP], #0x10
    // 0xc413fc: ret
    //     0xc413fc: ret             
    // 0xc41400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41404: b               #0xc413ac
  }
  get _ isCore(/* No info */) {
    // ** addr: 0xeb83a4, size: 0x74
    // 0xeb83a4: EnterFrame
    //     0xeb83a4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb83a8: mov             fp, SP
    // 0xeb83ac: AllocStack(0x10)
    //     0xeb83ac: sub             SP, SP, #0x10
    // 0xeb83b0: CheckStackOverflow
    //     0xeb83b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb83b4: cmp             SP, x16
    //     0xeb83b8: b.ls            #0xeb8410
    // 0xeb83bc: LoadField: r0 = r1->field_7
    //     0xeb83bc: ldur            w0, [x1, #7]
    // 0xeb83c0: DecompressPointer r0
    //     0xeb83c0: add             x0, x0, HEAP, lsl #32
    // 0xeb83c4: r1 = LoadClassIdInstr(r0)
    //     0xeb83c4: ldur            x1, [x0, #-1]
    //     0xeb83c8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb83cc: mov             x16, x0
    // 0xeb83d0: mov             x0, x1
    // 0xeb83d4: mov             x1, x16
    // 0xeb83d8: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xeb83d8: sub             lr, x0, #0xfcb
    //     0xeb83dc: ldr             lr, [x21, lr, lsl #3]
    //     0xeb83e0: blr             lr
    // 0xeb83e4: r1 = LoadClassIdInstr(r0)
    //     0xeb83e4: ldur            x1, [x0, #-1]
    //     0xeb83e8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb83ec: r16 = "dart"
    //     0xeb83ec: ldr             x16, [PP, #0xb38]  ; [pp+0xb38] "dart"
    // 0xeb83f0: stp             x16, x0, [SP]
    // 0xeb83f4: mov             x0, x1
    // 0xeb83f8: mov             lr, x0
    // 0xeb83fc: ldr             lr, [x21, lr, lsl #3]
    // 0xeb8400: blr             lr
    // 0xeb8404: LeaveFrame
    //     0xeb8404: mov             SP, fp
    //     0xeb8408: ldp             fp, lr, [SP], #0x10
    // 0xeb840c: ret
    //     0xeb840c: ret             
    // 0xeb8410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb8410: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb8414: b               #0xeb83bc
  }
  get _ package(/* No info */) {
    // ** addr: 0xeb8424, size: 0xc8
    // 0xeb8424: EnterFrame
    //     0xeb8424: stp             fp, lr, [SP, #-0x10]!
    //     0xeb8428: mov             fp, SP
    // 0xeb842c: AllocStack(0x18)
    //     0xeb842c: sub             SP, SP, #0x18
    // 0xeb8430: CheckStackOverflow
    //     0xeb8430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8434: cmp             SP, x16
    //     0xeb8438: b.ls            #0xeb84e4
    // 0xeb843c: LoadField: r2 = r1->field_7
    //     0xeb843c: ldur            w2, [x1, #7]
    // 0xeb8440: DecompressPointer r2
    //     0xeb8440: add             x2, x2, HEAP, lsl #32
    // 0xeb8444: stur            x2, [fp, #-8]
    // 0xeb8448: r0 = LoadClassIdInstr(r2)
    //     0xeb8448: ldur            x0, [x2, #-1]
    //     0xeb844c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb8450: mov             x1, x2
    // 0xeb8454: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xeb8454: sub             lr, x0, #0xfcb
    //     0xeb8458: ldr             lr, [x21, lr, lsl #3]
    //     0xeb845c: blr             lr
    // 0xeb8460: r1 = LoadClassIdInstr(r0)
    //     0xeb8460: ldur            x1, [x0, #-1]
    //     0xeb8464: ubfx            x1, x1, #0xc, #0x14
    // 0xeb8468: r16 = "package"
    //     0xeb8468: ldr             x16, [PP, #0xb40]  ; [pp+0xb40] "package"
    // 0xeb846c: stp             x16, x0, [SP]
    // 0xeb8470: mov             x0, x1
    // 0xeb8474: mov             lr, x0
    // 0xeb8478: ldr             lr, [x21, lr, lsl #3]
    // 0xeb847c: blr             lr
    // 0xeb8480: tbz             w0, #4, #0xeb8494
    // 0xeb8484: r0 = Null
    //     0xeb8484: mov             x0, NULL
    // 0xeb8488: LeaveFrame
    //     0xeb8488: mov             SP, fp
    //     0xeb848c: ldp             fp, lr, [SP], #0x10
    // 0xeb8490: ret
    //     0xeb8490: ret             
    // 0xeb8494: ldur            x1, [fp, #-8]
    // 0xeb8498: r0 = LoadClassIdInstr(r1)
    //     0xeb8498: ldur            x0, [x1, #-1]
    //     0xeb849c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb84a0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xeb84a0: sub             lr, x0, #0xffa
    //     0xeb84a4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb84a8: blr             lr
    // 0xeb84ac: r1 = LoadClassIdInstr(r0)
    //     0xeb84ac: ldur            x1, [x0, #-1]
    //     0xeb84b0: ubfx            x1, x1, #0xc, #0x14
    // 0xeb84b4: mov             x16, x0
    // 0xeb84b8: mov             x0, x1
    // 0xeb84bc: mov             x1, x16
    // 0xeb84c0: r2 = "/"
    //     0xeb84c0: ldr             x2, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xeb84c4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xeb84c4: sub             lr, x0, #1, lsl #12
    //     0xeb84c8: ldr             lr, [x21, lr, lsl #3]
    //     0xeb84cc: blr             lr
    // 0xeb84d0: mov             x1, x0
    // 0xeb84d4: r0 = first()
    //     0xeb84d4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xeb84d8: LeaveFrame
    //     0xeb84d8: mov             SP, fp
    //     0xeb84dc: ldp             fp, lr, [SP], #0x10
    // 0xeb84e0: ret
    //     0xeb84e0: ret             
    // 0xeb84e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb84e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb84e8: b               #0xeb843c
  }
  get _ library(/* No info */) {
    // ** addr: 0xeb8554, size: 0x90
    // 0xeb8554: EnterFrame
    //     0xeb8554: stp             fp, lr, [SP, #-0x10]!
    //     0xeb8558: mov             fp, SP
    // 0xeb855c: AllocStack(0x18)
    //     0xeb855c: sub             SP, SP, #0x18
    // 0xeb8560: CheckStackOverflow
    //     0xeb8560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8564: cmp             SP, x16
    //     0xeb8568: b.ls            #0xeb85dc
    // 0xeb856c: LoadField: r2 = r1->field_7
    //     0xeb856c: ldur            w2, [x1, #7]
    // 0xeb8570: DecompressPointer r2
    //     0xeb8570: add             x2, x2, HEAP, lsl #32
    // 0xeb8574: stur            x2, [fp, #-8]
    // 0xeb8578: r0 = LoadClassIdInstr(r2)
    //     0xeb8578: ldur            x0, [x2, #-1]
    //     0xeb857c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb8580: mov             x1, x2
    // 0xeb8584: r0 = GDT[cid_x0 + -0xfcb]()
    //     0xeb8584: sub             lr, x0, #0xfcb
    //     0xeb8588: ldr             lr, [x21, lr, lsl #3]
    //     0xeb858c: blr             lr
    // 0xeb8590: r1 = LoadClassIdInstr(r0)
    //     0xeb8590: ldur            x1, [x0, #-1]
    //     0xeb8594: ubfx            x1, x1, #0xc, #0x14
    // 0xeb8598: r16 = "data"
    //     0xeb8598: ldr             x16, [PP, #0xfb0]  ; [pp+0xfb0] "data"
    // 0xeb859c: stp             x16, x0, [SP]
    // 0xeb85a0: mov             x0, x1
    // 0xeb85a4: mov             lr, x0
    // 0xeb85a8: ldr             lr, [x21, lr, lsl #3]
    // 0xeb85ac: blr             lr
    // 0xeb85b0: tbnz            w0, #4, #0xeb85c8
    // 0xeb85b4: r0 = "data:..."
    //     0xeb85b4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1bb18] "data:..."
    //     0xeb85b8: ldr             x0, [x0, #0xb18]
    // 0xeb85bc: LeaveFrame
    //     0xeb85bc: mov             SP, fp
    //     0xeb85c0: ldp             fp, lr, [SP], #0x10
    // 0xeb85c4: ret
    //     0xeb85c4: ret             
    // 0xeb85c8: ldur            x1, [fp, #-8]
    // 0xeb85cc: r0 = prettyUri()
    //     0xeb85cc: bl              #0xc1738c  ; [package:path/path.dart] ::prettyUri
    // 0xeb85d0: LeaveFrame
    //     0xeb85d0: mov             SP, fp
    //     0xeb85d4: ldp             fp, lr, [SP], #0x10
    // 0xeb85d8: ret
    //     0xeb85d8: ret             
    // 0xeb85dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb85dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb85e0: b               #0xeb856c
  }
  get _ location(/* No info */) {
    // ** addr: 0xeb85e4, size: 0xf0
    // 0xeb85e4: EnterFrame
    //     0xeb85e4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb85e8: mov             fp, SP
    // 0xeb85ec: AllocStack(0x20)
    //     0xeb85ec: sub             SP, SP, #0x20
    // 0xeb85f0: CheckStackOverflow
    //     0xeb85f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb85f4: cmp             SP, x16
    //     0xeb85f8: b.ls            #0xeb86cc
    // 0xeb85fc: LoadField: r0 = r1->field_b
    //     0xeb85fc: ldur            w0, [x1, #0xb]
    // 0xeb8600: DecompressPointer r0
    //     0xeb8600: add             x0, x0, HEAP, lsl #32
    // 0xeb8604: stur            x0, [fp, #-8]
    // 0xeb8608: cmp             w0, NULL
    // 0xeb860c: b.ne            #0xeb8620
    // 0xeb8610: r0 = library()
    //     0xeb8610: bl              #0xeb8554  ; [package:stack_trace/src/frame.dart] Frame::library
    // 0xeb8614: LeaveFrame
    //     0xeb8614: mov             SP, fp
    //     0xeb8618: ldp             fp, lr, [SP], #0x10
    // 0xeb861c: ret
    //     0xeb861c: ret             
    // 0xeb8620: LoadField: r2 = r1->field_f
    //     0xeb8620: ldur            w2, [x1, #0xf]
    // 0xeb8624: DecompressPointer r2
    //     0xeb8624: add             x2, x2, HEAP, lsl #32
    // 0xeb8628: stur            x2, [fp, #-0x18]
    // 0xeb862c: cmp             w2, NULL
    // 0xeb8630: b.ne            #0xeb8678
    // 0xeb8634: r0 = library()
    //     0xeb8634: bl              #0xeb8554  ; [package:stack_trace/src/frame.dart] Frame::library
    // 0xeb8638: r1 = Null
    //     0xeb8638: mov             x1, NULL
    // 0xeb863c: r2 = 6
    //     0xeb863c: movz            x2, #0x6
    // 0xeb8640: stur            x0, [fp, #-0x10]
    // 0xeb8644: r0 = AllocateArray()
    //     0xeb8644: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb8648: mov             x1, x0
    // 0xeb864c: ldur            x0, [fp, #-0x10]
    // 0xeb8650: StoreField: r1->field_f = r0
    //     0xeb8650: stur            w0, [x1, #0xf]
    // 0xeb8654: r16 = " "
    //     0xeb8654: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xeb8658: StoreField: r1->field_13 = r16
    //     0xeb8658: stur            w16, [x1, #0x13]
    // 0xeb865c: ldur            x0, [fp, #-8]
    // 0xeb8660: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb8660: stur            w0, [x1, #0x17]
    // 0xeb8664: str             x1, [SP]
    // 0xeb8668: r0 = _interpolate()
    //     0xeb8668: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb866c: LeaveFrame
    //     0xeb866c: mov             SP, fp
    //     0xeb8670: ldp             fp, lr, [SP], #0x10
    // 0xeb8674: ret
    //     0xeb8674: ret             
    // 0xeb8678: r0 = library()
    //     0xeb8678: bl              #0xeb8554  ; [package:stack_trace/src/frame.dart] Frame::library
    // 0xeb867c: r1 = Null
    //     0xeb867c: mov             x1, NULL
    // 0xeb8680: r2 = 10
    //     0xeb8680: movz            x2, #0xa
    // 0xeb8684: stur            x0, [fp, #-0x10]
    // 0xeb8688: r0 = AllocateArray()
    //     0xeb8688: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb868c: mov             x1, x0
    // 0xeb8690: ldur            x0, [fp, #-0x10]
    // 0xeb8694: StoreField: r1->field_f = r0
    //     0xeb8694: stur            w0, [x1, #0xf]
    // 0xeb8698: r16 = " "
    //     0xeb8698: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xeb869c: StoreField: r1->field_13 = r16
    //     0xeb869c: stur            w16, [x1, #0x13]
    // 0xeb86a0: ldur            x0, [fp, #-8]
    // 0xeb86a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb86a4: stur            w0, [x1, #0x17]
    // 0xeb86a8: r16 = ":"
    //     0xeb86a8: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xeb86ac: StoreField: r1->field_1b = r16
    //     0xeb86ac: stur            w16, [x1, #0x1b]
    // 0xeb86b0: ldur            x0, [fp, #-0x18]
    // 0xeb86b4: StoreField: r1->field_1f = r0
    //     0xeb86b4: stur            w0, [x1, #0x1f]
    // 0xeb86b8: str             x1, [SP]
    // 0xeb86bc: r0 = _interpolate()
    //     0xeb86bc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb86c0: LeaveFrame
    //     0xeb86c0: mov             SP, fp
    //     0xeb86c4: ldp             fp, lr, [SP], #0x10
    // 0xeb86c8: ret
    //     0xeb86c8: ret             
    // 0xeb86cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb86cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb86d0: b               #0xeb85fc
  }
  const get _ member(/* No info */) {
    // ** addr: 0xeb86e0, size: 0xc
    // 0xeb86e0: LoadField: r0 = r1->field_13
    //     0xeb86e0: ldur            w0, [x1, #0x13]
    // 0xeb86e4: DecompressPointer r0
    //     0xeb86e4: add             x0, x0, HEAP, lsl #32
    // 0xeb86e8: ret
    //     0xeb86e8: ret             
  }
  factory Frame Frame.parseVM(dynamic, String) {
    // ** addr: 0xec83e8, size: 0x68
    // 0xec83e8: EnterFrame
    //     0xec83e8: stp             fp, lr, [SP, #-0x10]!
    //     0xec83ec: mov             fp, SP
    // 0xec83f0: AllocStack(0x8)
    //     0xec83f0: sub             SP, SP, #8
    // 0xec83f4: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0xec83f4: mov             x0, x1
    //     0xec83f8: mov             x1, x2
    //     0xec83fc: stur            x2, [fp, #-8]
    // 0xec8400: CheckStackOverflow
    //     0xec8400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec8404: cmp             SP, x16
    //     0xec8408: b.ls            #0xec8448
    // 0xec840c: r1 = 1
    //     0xec840c: movz            x1, #0x1
    // 0xec8410: r0 = AllocateContext()
    //     0xec8410: bl              #0xec126c  ; AllocateContextStub
    // 0xec8414: mov             x1, x0
    // 0xec8418: ldur            x0, [fp, #-8]
    // 0xec841c: StoreField: r1->field_f = r0
    //     0xec841c: stur            w0, [x1, #0xf]
    // 0xec8420: mov             x2, x1
    // 0xec8424: r1 = Function '<anonymous closure>': static.
    //     0xec8424: add             x1, PP, #0xd, lsl #12  ; [pp+0xd5c8] AnonymousClosure: static (0xec8630), in [package:stack_trace/src/frame.dart] Frame::Frame.parseVM (0xec83e8)
    //     0xec8428: ldr             x1, [x1, #0x5c8]
    // 0xec842c: r0 = AllocateClosure()
    //     0xec842c: bl              #0xec1630  ; AllocateClosureStub
    // 0xec8430: ldur            x1, [fp, #-8]
    // 0xec8434: mov             x2, x0
    // 0xec8438: r0 = _catchFormatException()
    //     0xec8438: bl              #0xec8484  ; [package:stack_trace/src/frame.dart] Frame::_catchFormatException
    // 0xec843c: LeaveFrame
    //     0xec843c: mov             SP, fp
    //     0xec8440: ldp             fp, lr, [SP], #0x10
    // 0xec8444: ret
    //     0xec8444: ret             
    // 0xec8448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec8448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec844c: b               #0xec840c
  }
  [closure] static Frame Frame.parseVM(dynamic, String) {
    // ** addr: 0xec8450, size: 0x34
    // 0xec8450: EnterFrame
    //     0xec8450: stp             fp, lr, [SP, #-0x10]!
    //     0xec8454: mov             fp, SP
    // 0xec8458: CheckStackOverflow
    //     0xec8458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec845c: cmp             SP, x16
    //     0xec8460: b.ls            #0xec847c
    // 0xec8464: ldr             x2, [fp, #0x10]
    // 0xec8468: r1 = Null
    //     0xec8468: mov             x1, NULL
    // 0xec846c: r0 = Frame.parseVM()
    //     0xec846c: bl              #0xec83e8  ; [package:stack_trace/src/frame.dart] Frame::Frame.parseVM
    // 0xec8470: LeaveFrame
    //     0xec8470: mov             SP, fp
    //     0xec8474: ldp             fp, lr, [SP], #0x10
    // 0xec8478: ret
    //     0xec8478: ret             
    // 0xec847c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec847c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec8480: b               #0xec8464
  }
  static _ _catchFormatException(/* No info */) {
    // ** addr: 0xec8484, size: 0xf4
    // 0xec8484: EnterFrame
    //     0xec8484: stp             fp, lr, [SP, #-0x10]!
    //     0xec8488: mov             fp, SP
    // 0xec848c: AllocStack(0x58)
    //     0xec848c: sub             SP, SP, #0x58
    // 0xec8490: SetupParameters(dynamic _ /* r1 => r2, fp-0x40 */, dynamic _ /* r2 => r1, fp-0x48 */)
    //     0xec8490: stur            x1, [fp, #-0x40]
    //     0xec8494: mov             x16, x2
    //     0xec8498: mov             x2, x1
    //     0xec849c: mov             x1, x16
    //     0xec84a0: stur            x1, [fp, #-0x48]
    // 0xec84a4: CheckStackOverflow
    //     0xec84a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec84a8: cmp             SP, x16
    //     0xec84ac: b.ls            #0xec8570
    // 0xec84b0: str             x1, [SP]
    // 0xec84b4: mov             x0, x1
    // 0xec84b8: ClosureCall
    //     0xec84b8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xec84bc: ldur            x2, [x0, #0x1f]
    //     0xec84c0: blr             x2
    // 0xec84c4: LeaveFrame
    //     0xec84c4: mov             SP, fp
    //     0xec84c8: ldp             fp, lr, [SP], #0x10
    // 0xec84cc: ret
    //     0xec84cc: ret             
    // 0xec84d0: sub             SP, fp, #0x58
    // 0xec84d4: mov             x4, x0
    // 0xec84d8: mov             x3, x1
    // 0xec84dc: stur            x0, [fp, #-0x40]
    // 0xec84e0: stur            x1, [fp, #-0x48]
    // 0xec84e4: r2 = Null
    //     0xec84e4: mov             x2, NULL
    // 0xec84e8: r1 = Null
    //     0xec84e8: mov             x1, NULL
    // 0xec84ec: cmp             w0, NULL
    // 0xec84f0: b.eq            #0xec852c
    // 0xec84f4: branchIfSmi(r0, 0xec852c)
    //     0xec84f4: tbz             w0, #0, #0xec852c
    // 0xec84f8: r3 = LoadClassIdInstr(r0)
    //     0xec84f8: ldur            x3, [x0, #-1]
    //     0xec84fc: ubfx            x3, x3, #0xc, #0x14
    // 0xec8500: sub             x3, x3, #0x10c
    // 0xec8504: cmp             x3, #1
    // 0xec8508: b.ls            #0xec8534
    // 0xec850c: cmp             x3, #0x1e4
    // 0xec8510: b.eq            #0xec8534
    // 0xec8514: sub             x3, x3, #0x4fe
    // 0xec8518: cmp             x3, #1
    // 0xec851c: b.ls            #0xec8534
    // 0xec8520: r17 = 5176
    //     0xec8520: movz            x17, #0x1438
    // 0xec8524: cmp             x3, x17
    // 0xec8528: b.eq            #0xec8534
    // 0xec852c: r0 = false
    //     0xec852c: add             x0, NULL, #0x30  ; false
    // 0xec8530: b               #0xec8538
    // 0xec8534: r0 = true
    //     0xec8534: add             x0, NULL, #0x20  ; true
    // 0xec8538: tbnz            w0, #4, #0xec8560
    // 0xec853c: r0 = UnparsedFrame()
    //     0xec853c: bl              #0xec8624  ; AllocateUnparsedFrameStub -> UnparsedFrame (size=0x28)
    // 0xec8540: mov             x1, x0
    // 0xec8544: ldur            x2, [fp, #-0x30]
    // 0xec8548: stur            x0, [fp, #-0x50]
    // 0xec854c: r0 = UnparsedFrame()
    //     0xec854c: bl              #0xec8578  ; [package:stack_trace/src/unparsed_frame.dart] UnparsedFrame::UnparsedFrame
    // 0xec8550: ldur            x0, [fp, #-0x50]
    // 0xec8554: LeaveFrame
    //     0xec8554: mov             SP, fp
    //     0xec8558: ldp             fp, lr, [SP], #0x10
    // 0xec855c: ret
    //     0xec855c: ret             
    // 0xec8560: ldur            x0, [fp, #-0x40]
    // 0xec8564: ldur            x1, [fp, #-0x48]
    // 0xec8568: r0 = ReThrow()
    //     0xec8568: bl              #0xec048c  ; ReThrowStub
    // 0xec856c: brk             #0
    // 0xec8570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec8570: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec8574: b               #0xec84b0
  }
  [closure] static Frame <anonymous closure>(dynamic) {
    // ** addr: 0xec8630, size: 0x328
    // 0xec8630: EnterFrame
    //     0xec8630: stp             fp, lr, [SP, #-0x10]!
    //     0xec8634: mov             fp, SP
    // 0xec8638: AllocStack(0x30)
    //     0xec8638: sub             SP, SP, #0x30
    // 0xec863c: SetupParameters()
    //     0xec863c: ldr             x0, [fp, #0x10]
    //     0xec8640: ldur            w1, [x0, #0x17]
    //     0xec8644: add             x1, x1, HEAP, lsl #32
    //     0xec8648: stur            x1, [fp, #-8]
    // 0xec864c: CheckStackOverflow
    //     0xec864c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec8650: cmp             SP, x16
    //     0xec8654: b.ls            #0xec8938
    // 0xec8658: LoadField: r0 = r1->field_f
    //     0xec8658: ldur            w0, [x1, #0xf]
    // 0xec865c: DecompressPointer r0
    //     0xec865c: add             x0, x0, HEAP, lsl #32
    // 0xec8660: r2 = LoadClassIdInstr(r0)
    //     0xec8660: ldur            x2, [x0, #-1]
    //     0xec8664: ubfx            x2, x2, #0xc, #0x14
    // 0xec8668: r16 = "..."
    //     0xec8668: ldr             x16, [PP, #0xb00]  ; [pp+0xb00] "..."
    // 0xec866c: stp             x16, x0, [SP]
    // 0xec8670: mov             x0, x2
    // 0xec8674: mov             lr, x0
    // 0xec8678: ldr             lr, [x21, lr, lsl #3]
    // 0xec867c: blr             lr
    // 0xec8680: tbnz            w0, #4, #0xec86bc
    // 0xec8684: r1 = Null
    //     0xec8684: mov             x1, NULL
    // 0xec8688: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xec8688: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xec868c: r0 = _Uri()
    //     0xec868c: bl              #0x5ff47c  ; [dart:core] _Uri::_Uri
    // 0xec8690: stur            x0, [fp, #-0x10]
    // 0xec8694: r0 = Frame()
    //     0xec8694: bl              #0xebdb94  ; AllocateFrameStub -> Frame (size=0x18)
    // 0xec8698: mov             x1, x0
    // 0xec869c: ldur            x0, [fp, #-0x10]
    // 0xec86a0: StoreField: r1->field_7 = r0
    //     0xec86a0: stur            w0, [x1, #7]
    // 0xec86a4: r0 = "..."
    //     0xec86a4: ldr             x0, [PP, #0xb00]  ; [pp+0xb00] "..."
    // 0xec86a8: StoreField: r1->field_13 = r0
    //     0xec86a8: stur            w0, [x1, #0x13]
    // 0xec86ac: mov             x0, x1
    // 0xec86b0: LeaveFrame
    //     0xec86b0: mov             SP, fp
    //     0xec86b4: ldp             fp, lr, [SP], #0x10
    // 0xec86b8: ret
    //     0xec86b8: ret             
    // 0xec86bc: ldur            x0, [fp, #-8]
    // 0xec86c0: r0 = InitLateStaticField(0xfb8) // [package:stack_trace/src/frame.dart] ::_vmFrame
    //     0xec86c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec86c4: ldr             x0, [x0, #0x1f70]
    //     0xec86c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec86cc: cmp             w0, w16
    //     0xec86d0: b.ne            #0xec86e0
    //     0xec86d4: add             x2, PP, #0xd, lsl #12  ; [pp+0xd5d0] Field <::._vmFrame@1034140773>: static late final (offset: 0xfb8)
    //     0xec86d8: ldr             x2, [x2, #0x5d0]
    //     0xec86dc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec86e0: mov             x1, x0
    // 0xec86e4: ldur            x0, [fp, #-8]
    // 0xec86e8: LoadField: r2 = r0->field_f
    //     0xec86e8: ldur            w2, [x0, #0xf]
    // 0xec86ec: DecompressPointer r2
    //     0xec86ec: add             x2, x2, HEAP, lsl #32
    // 0xec86f0: r0 = firstMatch()
    //     0xec86f0: bl              #0x644328  ; [dart:core] _RegExp::firstMatch
    // 0xec86f4: stur            x0, [fp, #-0x18]
    // 0xec86f8: cmp             w0, NULL
    // 0xec86fc: b.ne            #0xec8734
    // 0xec8700: ldur            x0, [fp, #-8]
    // 0xec8704: LoadField: r2 = r0->field_f
    //     0xec8704: ldur            w2, [x0, #0xf]
    // 0xec8708: DecompressPointer r2
    //     0xec8708: add             x2, x2, HEAP, lsl #32
    // 0xec870c: stur            x2, [fp, #-0x10]
    // 0xec8710: r0 = UnparsedFrame()
    //     0xec8710: bl              #0xec8624  ; AllocateUnparsedFrameStub -> UnparsedFrame (size=0x28)
    // 0xec8714: mov             x1, x0
    // 0xec8718: ldur            x2, [fp, #-0x10]
    // 0xec871c: stur            x0, [fp, #-8]
    // 0xec8720: r0 = UnparsedFrame()
    //     0xec8720: bl              #0xec8578  ; [package:stack_trace/src/unparsed_frame.dart] UnparsedFrame::UnparsedFrame
    // 0xec8724: ldur            x0, [fp, #-8]
    // 0xec8728: LeaveFrame
    //     0xec8728: mov             SP, fp
    //     0xec872c: ldp             fp, lr, [SP], #0x10
    // 0xec8730: ret
    //     0xec8730: ret             
    // 0xec8734: mov             x1, x0
    // 0xec8738: r2 = 1
    //     0xec8738: movz            x2, #0x1
    // 0xec873c: r0 = group()
    //     0xec873c: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xec8740: stur            x0, [fp, #-8]
    // 0xec8744: cmp             w0, NULL
    // 0xec8748: b.eq            #0xec8940
    // 0xec874c: r0 = InitLateStaticField(0xfbc) // [package:stack_trace/src/frame.dart] ::_asyncBody
    //     0xec874c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec8750: ldr             x0, [x0, #0x1f78]
    //     0xec8754: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec8758: cmp             w0, w16
    //     0xec875c: b.ne            #0xec876c
    //     0xec8760: add             x2, PP, #0xd, lsl #12  ; [pp+0xd5d8] Field <::._asyncBody@1034140773>: static late final (offset: 0xfbc)
    //     0xec8764: ldr             x2, [x2, #0x5d8]
    //     0xec8768: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec876c: ldur            x1, [fp, #-8]
    // 0xec8770: mov             x2, x0
    // 0xec8774: r3 = "<async>"
    //     0xec8774: add             x3, PP, #0xd, lsl #12  ; [pp+0xd590] "<async>"
    //     0xec8778: ldr             x3, [x3, #0x590]
    // 0xec877c: r0 = replaceAll()
    //     0xec877c: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xec8780: mov             x1, x0
    // 0xec8784: r2 = "<anonymous closure>"
    //     0xec8784: add             x2, PP, #0xd, lsl #12  ; [pp+0xd5e0] "<anonymous closure>"
    //     0xec8788: ldr             x2, [x2, #0x5e0]
    // 0xec878c: r3 = "<fn>"
    //     0xec878c: add             x3, PP, #0xd, lsl #12  ; [pp+0xd5e8] "<fn>"
    //     0xec8790: ldr             x3, [x3, #0x5e8]
    // 0xec8794: r0 = replaceAll()
    //     0xec8794: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xec8798: ldur            x1, [fp, #-0x18]
    // 0xec879c: r2 = 2
    //     0xec879c: movz            x2, #0x2
    // 0xec87a0: stur            x0, [fp, #-8]
    // 0xec87a4: r0 = group()
    //     0xec87a4: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xec87a8: cmp             w0, NULL
    // 0xec87ac: b.eq            #0xec8944
    // 0xec87b0: mov             x1, x0
    // 0xec87b4: r2 = "<data:"
    //     0xec87b4: add             x2, PP, #0xd, lsl #12  ; [pp+0xd5f0] "<data:"
    //     0xec87b8: ldr             x2, [x2, #0x5f0]
    // 0xec87bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xec87bc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xec87c0: r0 = startsWith()
    //     0xec87c0: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xec87c4: tbnz            w0, #4, #0xec87d4
    // 0xec87c8: r1 = Null
    //     0xec87c8: mov             x1, NULL
    // 0xec87cc: r0 = Uri.dataFromString()
    //     0xec87cc: bl              #0xec8958  ; [dart:core] Uri::Uri.dataFromString
    // 0xec87d0: b               #0xec87f4
    // 0xec87d4: ldur            x1, [fp, #-0x18]
    // 0xec87d8: r2 = 2
    //     0xec87d8: movz            x2, #0x2
    // 0xec87dc: r0 = group()
    //     0xec87dc: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xec87e0: cmp             w0, NULL
    // 0xec87e4: b.eq            #0xec8948
    // 0xec87e8: mov             x1, x0
    // 0xec87ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xec87ec: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xec87f0: r0 = parse()
    //     0xec87f0: bl              #0x60d170  ; [dart:core] Uri::parse
    // 0xec87f4: ldur            x1, [fp, #-0x18]
    // 0xec87f8: stur            x0, [fp, #-0x10]
    // 0xec87fc: r2 = 3
    //     0xec87fc: movz            x2, #0x3
    // 0xec8800: r0 = group()
    //     0xec8800: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xec8804: cmp             w0, NULL
    // 0xec8808: b.eq            #0xec894c
    // 0xec880c: r1 = LoadClassIdInstr(r0)
    //     0xec880c: ldur            x1, [x0, #-1]
    //     0xec8810: ubfx            x1, x1, #0xc, #0x14
    // 0xec8814: mov             x16, x0
    // 0xec8818: mov             x0, x1
    // 0xec881c: mov             x1, x16
    // 0xec8820: r2 = ":"
    //     0xec8820: ldr             x2, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xec8824: r0 = GDT[cid_x0 + -0x1000]()
    //     0xec8824: sub             lr, x0, #1, lsl #12
    //     0xec8828: ldr             lr, [x21, lr, lsl #3]
    //     0xec882c: blr             lr
    // 0xec8830: mov             x2, x0
    // 0xec8834: stur            x2, [fp, #-0x18]
    // 0xec8838: LoadField: r0 = r2->field_b
    //     0xec8838: ldur            w0, [x2, #0xb]
    // 0xec883c: r1 = LoadInt32Instr(r0)
    //     0xec883c: sbfx            x1, x0, #1, #0x1f
    // 0xec8840: cmp             x1, #1
    // 0xec8844: b.le            #0xec8890
    // 0xec8848: mov             x0, x1
    // 0xec884c: r1 = 1
    //     0xec884c: movz            x1, #0x1
    // 0xec8850: cmp             x1, x0
    // 0xec8854: b.hs            #0xec8950
    // 0xec8858: LoadField: r0 = r2->field_f
    //     0xec8858: ldur            w0, [x2, #0xf]
    // 0xec885c: DecompressPointer r0
    //     0xec885c: add             x0, x0, HEAP, lsl #32
    // 0xec8860: LoadField: r1 = r0->field_13
    //     0xec8860: ldur            w1, [x0, #0x13]
    // 0xec8864: DecompressPointer r1
    //     0xec8864: add             x1, x1, HEAP, lsl #32
    // 0xec8868: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xec8868: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xec886c: r0 = parse()
    //     0xec886c: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xec8870: mov             x2, x0
    // 0xec8874: r0 = BoxInt64Instr(r2)
    //     0xec8874: sbfiz           x0, x2, #1, #0x1f
    //     0xec8878: cmp             x2, x0, asr #1
    //     0xec887c: b.eq            #0xec8888
    //     0xec8880: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xec8884: stur            x2, [x0, #7]
    // 0xec8888: mov             x3, x0
    // 0xec888c: b               #0xec8894
    // 0xec8890: r3 = Null
    //     0xec8890: mov             x3, NULL
    // 0xec8894: ldur            x2, [fp, #-0x18]
    // 0xec8898: stur            x3, [fp, #-0x20]
    // 0xec889c: LoadField: r0 = r2->field_b
    //     0xec889c: ldur            w0, [x2, #0xb]
    // 0xec88a0: r1 = LoadInt32Instr(r0)
    //     0xec88a0: sbfx            x1, x0, #1, #0x1f
    // 0xec88a4: cmp             x1, #2
    // 0xec88a8: b.le            #0xec88f4
    // 0xec88ac: mov             x0, x1
    // 0xec88b0: r1 = 2
    //     0xec88b0: movz            x1, #0x2
    // 0xec88b4: cmp             x1, x0
    // 0xec88b8: b.hs            #0xec8954
    // 0xec88bc: LoadField: r0 = r2->field_f
    //     0xec88bc: ldur            w0, [x2, #0xf]
    // 0xec88c0: DecompressPointer r0
    //     0xec88c0: add             x0, x0, HEAP, lsl #32
    // 0xec88c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xec88c4: ldur            w1, [x0, #0x17]
    // 0xec88c8: DecompressPointer r1
    //     0xec88c8: add             x1, x1, HEAP, lsl #32
    // 0xec88cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xec88cc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xec88d0: r0 = parse()
    //     0xec88d0: bl              #0x6062cc  ; [dart:core] int::parse
    // 0xec88d4: mov             x2, x0
    // 0xec88d8: r0 = BoxInt64Instr(r2)
    //     0xec88d8: sbfiz           x0, x2, #1, #0x1f
    //     0xec88dc: cmp             x2, x0, asr #1
    //     0xec88e0: b.eq            #0xec88ec
    //     0xec88e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xec88e8: stur            x2, [x0, #7]
    // 0xec88ec: mov             x3, x0
    // 0xec88f0: b               #0xec88f8
    // 0xec88f4: r3 = Null
    //     0xec88f4: mov             x3, NULL
    // 0xec88f8: ldur            x2, [fp, #-8]
    // 0xec88fc: ldur            x1, [fp, #-0x10]
    // 0xec8900: ldur            x0, [fp, #-0x20]
    // 0xec8904: stur            x3, [fp, #-0x18]
    // 0xec8908: r0 = Frame()
    //     0xec8908: bl              #0xebdb94  ; AllocateFrameStub -> Frame (size=0x18)
    // 0xec890c: ldur            x1, [fp, #-0x10]
    // 0xec8910: StoreField: r0->field_7 = r1
    //     0xec8910: stur            w1, [x0, #7]
    // 0xec8914: ldur            x1, [fp, #-0x20]
    // 0xec8918: StoreField: r0->field_b = r1
    //     0xec8918: stur            w1, [x0, #0xb]
    // 0xec891c: ldur            x1, [fp, #-0x18]
    // 0xec8920: StoreField: r0->field_f = r1
    //     0xec8920: stur            w1, [x0, #0xf]
    // 0xec8924: ldur            x1, [fp, #-8]
    // 0xec8928: StoreField: r0->field_13 = r1
    //     0xec8928: stur            w1, [x0, #0x13]
    // 0xec892c: LeaveFrame
    //     0xec892c: mov             SP, fp
    //     0xec8930: ldp             fp, lr, [SP], #0x10
    // 0xec8934: ret
    //     0xec8934: ret             
    // 0xec8938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec8938: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec893c: b               #0xec8658
    // 0xec8940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xec8940: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xec8944: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xec8944: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xec8948: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xec8948: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xec894c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xec894c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xec8950: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xec8950: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xec8954: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xec8954: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
