// lib: , url: package:stack_trace/src/trace.dart

// class id: 1051171, size: 0x8
class :: {

  static late final RegExp _terseRegExp; // offset: 0xfc0

  static RegExp _terseRegExp() {
    // ** addr: 0xebdcb4, size: 0x58
    // 0xebdcb4: EnterFrame
    //     0xebdcb4: stp             fp, lr, [SP, #-0x10]!
    //     0xebdcb8: mov             fp, SP
    // 0xebdcbc: AllocStack(0x30)
    //     0xebdcbc: sub             SP, SP, #0x30
    // 0xebdcc0: CheckStackOverflow
    //     0xebdcc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebdcc4: cmp             SP, x16
    //     0xebdcc8: b.ls            #0xebdd04
    // 0xebdccc: r16 = "(-patch)\?([/\\\\].*)\?$"
    //     0xebdccc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd580] "(-patch)\?([/\\\\].*)\?$"
    //     0xebdcd0: ldr             x16, [x16, #0x580]
    // 0xebdcd4: stp             x16, NULL, [SP, #0x20]
    // 0xebdcd8: r16 = false
    //     0xebdcd8: add             x16, NULL, #0x30  ; false
    // 0xebdcdc: r30 = true
    //     0xebdcdc: add             lr, NULL, #0x20  ; true
    // 0xebdce0: stp             lr, x16, [SP, #0x10]
    // 0xebdce4: r16 = false
    //     0xebdce4: add             x16, NULL, #0x30  ; false
    // 0xebdce8: r30 = false
    //     0xebdce8: add             lr, NULL, #0x30  ; false
    // 0xebdcec: stp             lr, x16, [SP]
    // 0xebdcf0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xebdcf0: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xebdcf4: r0 = _RegExp()
    //     0xebdcf4: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xebdcf8: LeaveFrame
    //     0xebdcf8: mov             SP, fp
    //     0xebdcfc: ldp             fp, lr, [SP], #0x10
    // 0xebdd00: ret
    //     0xebdd00: ret             
    // 0xebdd04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebdd04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebdd08: b               #0xebdccc
  }
}

// class id: 454, size: 0x10, field offset: 0x8
class Trace extends Object
    implements StackTrace {

  _ toString(/* No info */) {
    // ** addr: 0xc41430, size: 0xcc
    // 0xc41430: EnterFrame
    //     0xc41430: stp             fp, lr, [SP, #-0x10]!
    //     0xc41434: mov             fp, SP
    // 0xc41438: AllocStack(0x30)
    //     0xc41438: sub             SP, SP, #0x30
    // 0xc4143c: CheckStackOverflow
    //     0xc4143c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41440: cmp             SP, x16
    //     0xc41444: b.ls            #0xc414f4
    // 0xc41448: ldr             x0, [fp, #0x10]
    // 0xc4144c: LoadField: r3 = r0->field_7
    //     0xc4144c: ldur            w3, [x0, #7]
    // 0xc41450: DecompressPointer r3
    //     0xc41450: add             x3, x3, HEAP, lsl #32
    // 0xc41454: stur            x3, [fp, #-8]
    // 0xc41458: r1 = Function '<anonymous closure>':.
    //     0xc41458: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1bae0] AnonymousClosure: (0xc41654), in [package:stack_trace/src/trace.dart] Trace::toString (0xc41430)
    //     0xc4145c: ldr             x1, [x1, #0xae0]
    // 0xc41460: r2 = Null
    //     0xc41460: mov             x2, NULL
    // 0xc41464: r0 = AllocateClosure()
    //     0xc41464: bl              #0xec1630  ; AllocateClosureStub
    // 0xc41468: r16 = <int>
    //     0xc41468: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc4146c: ldur            lr, [fp, #-8]
    // 0xc41470: stp             lr, x16, [SP, #8]
    // 0xc41474: str             x0, [SP]
    // 0xc41478: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc41478: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc4147c: r0 = map()
    //     0xc4147c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xc41480: r16 = <int>
    //     0xc41480: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xc41484: stp             x0, x16, [SP, #0x10]
    // 0xc41488: r16 = Closure: (int, int) => int from Function 'max': static.
    //     0xc41488: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bae8] Closure: (int, int) => int from Function 'max': static. (0x7e54fb034a64)
    //     0xc4148c: ldr             x16, [x16, #0xae8]
    // 0xc41490: stp             x16, xzr, [SP]
    // 0xc41494: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc41494: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc41498: r0 = fold()
    //     0xc41498: bl              #0x7d8444  ; [dart:_internal] ListIterable::fold
    // 0xc4149c: stur            x0, [fp, #-0x10]
    // 0xc414a0: r1 = 1
    //     0xc414a0: movz            x1, #0x1
    // 0xc414a4: r0 = AllocateContext()
    //     0xc414a4: bl              #0xec126c  ; AllocateContextStub
    // 0xc414a8: mov             x1, x0
    // 0xc414ac: ldur            x0, [fp, #-0x10]
    // 0xc414b0: StoreField: r1->field_f = r0
    //     0xc414b0: stur            w0, [x1, #0xf]
    // 0xc414b4: mov             x2, x1
    // 0xc414b8: r1 = Function '<anonymous closure>':.
    //     0xc414b8: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1baf0] AnonymousClosure: (0xc414fc), in [package:stack_trace/src/trace.dart] Trace::toString (0xc41430)
    //     0xc414bc: ldr             x1, [x1, #0xaf0]
    // 0xc414c0: r0 = AllocateClosure()
    //     0xc414c0: bl              #0xec1630  ; AllocateClosureStub
    // 0xc414c4: r16 = <String>
    //     0xc414c4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xc414c8: ldur            lr, [fp, #-8]
    // 0xc414cc: stp             lr, x16, [SP, #8]
    // 0xc414d0: str             x0, [SP]
    // 0xc414d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc414d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc414d8: r0 = map()
    //     0xc414d8: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xc414dc: mov             x1, x0
    // 0xc414e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc414e0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc414e4: r0 = join()
    //     0xc414e4: bl              #0x7adcb0  ; [dart:_internal] ListIterable::join
    // 0xc414e8: LeaveFrame
    //     0xc414e8: mov             SP, fp
    //     0xc414ec: ldp             fp, lr, [SP], #0x10
    // 0xc414f0: ret
    //     0xc414f0: ret             
    // 0xc414f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc414f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc414f8: b               #0xc41448
  }
  [closure] String <anonymous closure>(dynamic, Frame) {
    // ** addr: 0xc414fc, size: 0x158
    // 0xc414fc: EnterFrame
    //     0xc414fc: stp             fp, lr, [SP, #-0x10]!
    //     0xc41500: mov             fp, SP
    // 0xc41504: AllocStack(0x18)
    //     0xc41504: sub             SP, SP, #0x18
    // 0xc41508: SetupParameters()
    //     0xc41508: ldr             x0, [fp, #0x18]
    //     0xc4150c: ldur            w2, [x0, #0x17]
    //     0xc41510: add             x2, x2, HEAP, lsl #32
    //     0xc41514: stur            x2, [fp, #-8]
    // 0xc41518: CheckStackOverflow
    //     0xc41518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4151c: cmp             SP, x16
    //     0xc41520: b.ls            #0xc4164c
    // 0xc41524: ldr             x0, [fp, #0x10]
    // 0xc41528: r1 = LoadClassIdInstr(r0)
    //     0xc41528: ldur            x1, [x0, #-1]
    //     0xc4152c: ubfx            x1, x1, #0xc, #0x14
    // 0xc41530: cmp             x1, #0x1c5
    // 0xc41534: b.ne            #0xc41568
    // 0xc41538: r1 = Null
    //     0xc41538: mov             x1, NULL
    // 0xc4153c: r2 = 4
    //     0xc4153c: movz            x2, #0x4
    // 0xc41540: r0 = AllocateArray()
    //     0xc41540: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41544: ldr             x3, [fp, #0x10]
    // 0xc41548: StoreField: r0->field_f = r3
    //     0xc41548: stur            w3, [x0, #0xf]
    // 0xc4154c: r16 = "\n"
    //     0xc4154c: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc41550: StoreField: r0->field_13 = r16
    //     0xc41550: stur            w16, [x0, #0x13]
    // 0xc41554: str             x0, [SP]
    // 0xc41558: r0 = _interpolate()
    //     0xc41558: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4155c: LeaveFrame
    //     0xc4155c: mov             SP, fp
    //     0xc41560: ldp             fp, lr, [SP], #0x10
    // 0xc41564: ret
    //     0xc41564: ret             
    // 0xc41568: mov             x3, x0
    // 0xc4156c: r0 = LoadClassIdInstr(r3)
    //     0xc4156c: ldur            x0, [x3, #-1]
    //     0xc41570: ubfx            x0, x0, #0xc, #0x14
    // 0xc41574: mov             x1, x3
    // 0xc41578: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc41578: sub             lr, x0, #0xffc
    //     0xc4157c: ldr             lr, [x21, lr, lsl #3]
    //     0xc41580: blr             lr
    // 0xc41584: mov             x1, x0
    // 0xc41588: ldur            x0, [fp, #-8]
    // 0xc4158c: LoadField: r2 = r0->field_f
    //     0xc4158c: ldur            w2, [x0, #0xf]
    // 0xc41590: DecompressPointer r2
    //     0xc41590: add             x2, x2, HEAP, lsl #32
    // 0xc41594: r0 = LoadInt32Instr(r2)
    //     0xc41594: sbfx            x0, x2, #1, #0x1f
    //     0xc41598: tbz             w2, #0, #0xc415a0
    //     0xc4159c: ldur            x0, [x2, #7]
    // 0xc415a0: r2 = LoadClassIdInstr(r1)
    //     0xc415a0: ldur            x2, [x1, #-1]
    //     0xc415a4: ubfx            x2, x2, #0xc, #0x14
    // 0xc415a8: mov             x16, x0
    // 0xc415ac: mov             x0, x2
    // 0xc415b0: mov             x2, x16
    // 0xc415b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc415b4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc415b8: r0 = GDT[cid_x0 + -0xfe8]()
    //     0xc415b8: sub             lr, x0, #0xfe8
    //     0xc415bc: ldr             lr, [x21, lr, lsl #3]
    //     0xc415c0: blr             lr
    // 0xc415c4: r1 = Null
    //     0xc415c4: mov             x1, NULL
    // 0xc415c8: r2 = 8
    //     0xc415c8: movz            x2, #0x8
    // 0xc415cc: stur            x0, [fp, #-8]
    // 0xc415d0: r0 = AllocateArray()
    //     0xc415d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc415d4: mov             x2, x0
    // 0xc415d8: ldur            x0, [fp, #-8]
    // 0xc415dc: stur            x2, [fp, #-0x10]
    // 0xc415e0: StoreField: r2->field_f = r0
    //     0xc415e0: stur            w0, [x2, #0xf]
    // 0xc415e4: r16 = "  "
    //     0xc415e4: ldr             x16, [PP, #0x830]  ; [pp+0x830] "  "
    // 0xc415e8: StoreField: r2->field_13 = r16
    //     0xc415e8: stur            w16, [x2, #0x13]
    // 0xc415ec: ldr             x1, [fp, #0x10]
    // 0xc415f0: r0 = LoadClassIdInstr(r1)
    //     0xc415f0: ldur            x0, [x1, #-1]
    //     0xc415f4: ubfx            x0, x0, #0xc, #0x14
    // 0xc415f8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc415f8: sub             lr, x0, #1, lsl #12
    //     0xc415fc: ldr             lr, [x21, lr, lsl #3]
    //     0xc41600: blr             lr
    // 0xc41604: ldur            x1, [fp, #-0x10]
    // 0xc41608: ArrayStore: r1[2] = r0  ; List_4
    //     0xc41608: add             x25, x1, #0x17
    //     0xc4160c: str             w0, [x25]
    //     0xc41610: tbz             w0, #0, #0xc4162c
    //     0xc41614: ldurb           w16, [x1, #-1]
    //     0xc41618: ldurb           w17, [x0, #-1]
    //     0xc4161c: and             x16, x17, x16, lsr #2
    //     0xc41620: tst             x16, HEAP, lsr #32
    //     0xc41624: b.eq            #0xc4162c
    //     0xc41628: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc4162c: ldur            x0, [fp, #-0x10]
    // 0xc41630: r16 = "\n"
    //     0xc41630: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc41634: StoreField: r0->field_1b = r16
    //     0xc41634: stur            w16, [x0, #0x1b]
    // 0xc41638: str             x0, [SP]
    // 0xc4163c: r0 = _interpolate()
    //     0xc4163c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41640: LeaveFrame
    //     0xc41640: mov             SP, fp
    //     0xc41644: ldp             fp, lr, [SP], #0x10
    // 0xc41648: ret
    //     0xc41648: ret             
    // 0xc4164c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4164c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41650: b               #0xc41524
  }
  [closure] int <anonymous closure>(dynamic, Frame) {
    // ** addr: 0xc41654, size: 0x48
    // 0xc41654: EnterFrame
    //     0xc41654: stp             fp, lr, [SP, #-0x10]!
    //     0xc41658: mov             fp, SP
    // 0xc4165c: CheckStackOverflow
    //     0xc4165c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41660: cmp             SP, x16
    //     0xc41664: b.ls            #0xc41694
    // 0xc41668: ldr             x1, [fp, #0x10]
    // 0xc4166c: r0 = LoadClassIdInstr(r1)
    //     0xc4166c: ldur            x0, [x1, #-1]
    //     0xc41670: ubfx            x0, x0, #0xc, #0x14
    // 0xc41674: r0 = GDT[cid_x0 + -0xffc]()
    //     0xc41674: sub             lr, x0, #0xffc
    //     0xc41678: ldr             lr, [x21, lr, lsl #3]
    //     0xc4167c: blr             lr
    // 0xc41680: LoadField: r1 = r0->field_7
    //     0xc41680: ldur            w1, [x0, #7]
    // 0xc41684: mov             x0, x1
    // 0xc41688: LeaveFrame
    //     0xc41688: mov             SP, fp
    //     0xc4168c: ldp             fp, lr, [SP], #0x10
    // 0xc41690: ret
    //     0xc41690: ret             
    // 0xc41694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41694: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41698: b               #0xc41668
  }
  get _ terse(/* No info */) {
    // ** addr: 0xebd580, size: 0x50
    // 0xebd580: EnterFrame
    //     0xebd580: stp             fp, lr, [SP, #-0x10]!
    //     0xebd584: mov             fp, SP
    // 0xebd588: AllocStack(0x8)
    //     0xebd588: sub             SP, SP, #8
    // 0xebd58c: SetupParameters(Trace this /* r1 => r0, fp-0x8 */)
    //     0xebd58c: mov             x0, x1
    //     0xebd590: stur            x1, [fp, #-8]
    // 0xebd594: CheckStackOverflow
    //     0xebd594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebd598: cmp             SP, x16
    //     0xebd59c: b.ls            #0xebd5c8
    // 0xebd5a0: r1 = Function '<anonymous closure>':.
    //     0xebd5a0: add             x1, PP, #0xd, lsl #12  ; [pp+0xd550] Function: [dart:core] Object::_simpleInstanceOfFalse (0xebd578)
    //     0xebd5a4: ldr             x1, [x1, #0x550]
    // 0xebd5a8: r2 = Null
    //     0xebd5a8: mov             x2, NULL
    // 0xebd5ac: r0 = AllocateClosure()
    //     0xebd5ac: bl              #0xec1630  ; AllocateClosureStub
    // 0xebd5b0: ldur            x1, [fp, #-8]
    // 0xebd5b4: mov             x2, x0
    // 0xebd5b8: r0 = foldFrames()
    //     0xebd5b8: bl              #0xebd5d0  ; [package:stack_trace/src/trace.dart] Trace::foldFrames
    // 0xebd5bc: LeaveFrame
    //     0xebd5bc: mov             SP, fp
    //     0xebd5c0: ldp             fp, lr, [SP], #0x10
    // 0xebd5c4: ret
    //     0xebd5c4: ret             
    // 0xebd5c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebd5c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebd5cc: b               #0xebd5a0
  }
  _ foldFrames(/* No info */) {
    // ** addr: 0xebd5d0, size: 0x5b8
    // 0xebd5d0: EnterFrame
    //     0xebd5d0: stp             fp, lr, [SP, #-0x10]!
    //     0xebd5d4: mov             fp, SP
    // 0xebd5d8: AllocStack(0x80)
    //     0xebd5d8: sub             SP, SP, #0x80
    // 0xebd5dc: SetupParameters(Trace this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xebd5dc: stur            x1, [fp, #-8]
    //     0xebd5e0: stur            x2, [fp, #-0x10]
    // 0xebd5e4: CheckStackOverflow
    //     0xebd5e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebd5e8: cmp             SP, x16
    //     0xebd5ec: b.ls            #0xebdb74
    // 0xebd5f0: r1 = 2
    //     0xebd5f0: movz            x1, #0x2
    // 0xebd5f4: r0 = AllocateContext()
    //     0xebd5f4: bl              #0xec126c  ; AllocateContextStub
    // 0xebd5f8: mov             x3, x0
    // 0xebd5fc: ldur            x0, [fp, #-0x10]
    // 0xebd600: stur            x3, [fp, #-0x18]
    // 0xebd604: StoreField: r3->field_f = r0
    //     0xebd604: stur            w0, [x3, #0xf]
    // 0xebd608: StoreField: r3->field_13 = r0
    //     0xebd608: stur            w0, [x3, #0x13]
    // 0xebd60c: mov             x2, x3
    // 0xebd610: r1 = Function '<anonymous closure>':.
    //     0xebd610: add             x1, PP, #0xd, lsl #12  ; [pp+0xd558] AnonymousClosure: (0xebdd0c), in [package:stack_trace/src/trace.dart] Trace::foldFrames (0xebd5d0)
    //     0xebd614: ldr             x1, [x1, #0x558]
    // 0xebd618: r0 = AllocateClosure()
    //     0xebd618: bl              #0xec1630  ; AllocateClosureStub
    // 0xebd61c: mov             x1, x0
    // 0xebd620: ldur            x0, [fp, #-0x18]
    // 0xebd624: StoreField: r0->field_f = r1
    //     0xebd624: stur            w1, [x0, #0xf]
    // 0xebd628: r1 = <Frame>
    //     0xebd628: add             x1, PP, #0xd, lsl #12  ; [pp+0xd528] TypeArguments: <Frame>
    //     0xebd62c: ldr             x1, [x1, #0x528]
    // 0xebd630: r2 = 0
    //     0xebd630: movz            x2, #0
    // 0xebd634: r0 = _GrowableList()
    //     0xebd634: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xebd638: mov             x2, x0
    // 0xebd63c: ldur            x0, [fp, #-8]
    // 0xebd640: stur            x2, [fp, #-0x20]
    // 0xebd644: LoadField: r3 = r0->field_7
    //     0xebd644: ldur            w3, [x0, #7]
    // 0xebd648: DecompressPointer r3
    //     0xebd648: add             x3, x3, HEAP, lsl #32
    // 0xebd64c: stur            x3, [fp, #-0x10]
    // 0xebd650: LoadField: r1 = r3->field_7
    //     0xebd650: ldur            w1, [x3, #7]
    // 0xebd654: DecompressPointer r1
    //     0xebd654: add             x1, x1, HEAP, lsl #32
    // 0xebd658: r0 = ReversedListIterable()
    //     0xebd658: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xebd65c: mov             x1, x0
    // 0xebd660: ldur            x0, [fp, #-0x10]
    // 0xebd664: StoreField: r1->field_b = r0
    //     0xebd664: stur            w0, [x1, #0xb]
    // 0xebd668: r0 = iterator()
    //     0xebd668: bl              #0x8873a8  ; [dart:_internal] ListIterable::iterator
    // 0xebd66c: mov             x1, x0
    // 0xebd670: stur            x1, [fp, #-0x38]
    // 0xebd674: LoadField: r2 = r1->field_b
    //     0xebd674: ldur            w2, [x1, #0xb]
    // 0xebd678: DecompressPointer r2
    //     0xebd678: add             x2, x2, HEAP, lsl #32
    // 0xebd67c: stur            x2, [fp, #-0x30]
    // 0xebd680: LoadField: r3 = r1->field_f
    //     0xebd680: ldur            x3, [x1, #0xf]
    // 0xebd684: stur            x3, [fp, #-0x28]
    // 0xebd688: LoadField: r4 = r1->field_7
    //     0xebd688: ldur            w4, [x1, #7]
    // 0xebd68c: DecompressPointer r4
    //     0xebd68c: add             x4, x4, HEAP, lsl #32
    // 0xebd690: stur            x4, [fp, #-0x10]
    // 0xebd694: ldur            x5, [fp, #-0x20]
    // 0xebd698: ldur            x6, [fp, #-0x18]
    // 0xebd69c: CheckStackOverflow
    //     0xebd69c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebd6a0: cmp             SP, x16
    //     0xebd6a4: b.ls            #0xebdb7c
    // 0xebd6a8: r0 = LoadClassIdInstr(r2)
    //     0xebd6a8: ldur            x0, [x2, #-1]
    //     0xebd6ac: ubfx            x0, x0, #0xc, #0x14
    // 0xebd6b0: str             x2, [SP]
    // 0xebd6b4: r0 = GDT[cid_x0 + 0xc834]()
    //     0xebd6b4: movz            x17, #0xc834
    //     0xebd6b8: add             lr, x0, x17
    //     0xebd6bc: ldr             lr, [x21, lr, lsl #3]
    //     0xebd6c0: blr             lr
    // 0xebd6c4: r1 = LoadInt32Instr(r0)
    //     0xebd6c4: sbfx            x1, x0, #1, #0x1f
    //     0xebd6c8: tbz             w0, #0, #0xebd6d0
    //     0xebd6cc: ldur            x1, [x0, #7]
    // 0xebd6d0: ldur            x3, [fp, #-0x28]
    // 0xebd6d4: cmp             x3, x1
    // 0xebd6d8: b.ne            #0xebdb54
    // 0xebd6dc: ldur            x4, [fp, #-0x38]
    // 0xebd6e0: ArrayLoad: r2 = r4[0]  ; List_8
    //     0xebd6e0: ldur            x2, [x4, #0x17]
    // 0xebd6e4: cmp             x2, x1
    // 0xebd6e8: b.ge            #0xebd9fc
    // 0xebd6ec: ldur            x5, [fp, #-0x30]
    // 0xebd6f0: r0 = LoadClassIdInstr(r5)
    //     0xebd6f0: ldur            x0, [x5, #-1]
    //     0xebd6f4: ubfx            x0, x0, #0xc, #0x14
    // 0xebd6f8: mov             x1, x5
    // 0xebd6fc: r0 = GDT[cid_x0 + 0xd28f]()
    //     0xebd6fc: movz            x17, #0xd28f
    //     0xebd700: add             lr, x0, x17
    //     0xebd704: ldr             lr, [x21, lr, lsl #3]
    //     0xebd708: blr             lr
    // 0xebd70c: mov             x4, x0
    // 0xebd710: ldur            x3, [fp, #-0x38]
    // 0xebd714: stur            x4, [fp, #-0x40]
    // 0xebd718: StoreField: r3->field_1f = r0
    //     0xebd718: stur            w0, [x3, #0x1f]
    //     0xebd71c: tbz             w0, #0, #0xebd738
    //     0xebd720: ldurb           w16, [x3, #-1]
    //     0xebd724: ldurb           w17, [x0, #-1]
    //     0xebd728: and             x16, x17, x16, lsr #2
    //     0xebd72c: tst             x16, HEAP, lsr #32
    //     0xebd730: b.eq            #0xebd738
    //     0xebd734: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xebd738: ArrayLoad: r0 = r3[0]  ; List_8
    //     0xebd738: ldur            x0, [x3, #0x17]
    // 0xebd73c: add             x1, x0, #1
    // 0xebd740: ArrayStore: r3[0] = r1  ; List_8
    //     0xebd740: stur            x1, [x3, #0x17]
    // 0xebd744: cmp             w4, NULL
    // 0xebd748: b.ne            #0xebd77c
    // 0xebd74c: mov             x0, x4
    // 0xebd750: ldur            x2, [fp, #-0x10]
    // 0xebd754: r1 = Null
    //     0xebd754: mov             x1, NULL
    // 0xebd758: cmp             w2, NULL
    // 0xebd75c: b.eq            #0xebd77c
    // 0xebd760: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xebd760: ldur            w4, [x2, #0x17]
    // 0xebd764: DecompressPointer r4
    //     0xebd764: add             x4, x4, HEAP, lsl #32
    // 0xebd768: r8 = X0
    //     0xebd768: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xebd76c: LoadField: r9 = r4->field_7
    //     0xebd76c: ldur            x9, [x4, #7]
    // 0xebd770: r3 = Null
    //     0xebd770: add             x3, PP, #0xd, lsl #12  ; [pp+0xd560] Null
    //     0xebd774: ldr             x3, [x3, #0x560]
    // 0xebd778: blr             x9
    // 0xebd77c: ldur            x1, [fp, #-0x40]
    // 0xebd780: r0 = 60
    //     0xebd780: movz            x0, #0x3c
    // 0xebd784: branchIfSmi(r1, 0xebd790)
    //     0xebd784: tbz             w1, #0, #0xebd790
    // 0xebd788: r0 = LoadClassIdInstr(r1)
    //     0xebd788: ldur            x0, [x1, #-1]
    //     0xebd78c: ubfx            x0, x0, #0xc, #0x14
    // 0xebd790: cmp             x0, #0x1c5
    // 0xebd794: b.eq            #0xebd7c0
    // 0xebd798: ldur            x2, [fp, #-0x18]
    // 0xebd79c: LoadField: r0 = r2->field_f
    //     0xebd79c: ldur            w0, [x2, #0xf]
    // 0xebd7a0: DecompressPointer r0
    //     0xebd7a0: add             x0, x0, HEAP, lsl #32
    // 0xebd7a4: stp             x1, x0, [SP]
    // 0xebd7a8: ClosureCall
    //     0xebd7a8: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xebd7ac: ldur            x2, [x0, #0x1f]
    //     0xebd7b0: blr             x2
    // 0xebd7b4: r16 = true
    //     0xebd7b4: add             x16, NULL, #0x20  ; true
    // 0xebd7b8: cmp             w0, w16
    // 0xebd7bc: b.eq            #0xebd840
    // 0xebd7c0: ldur            x0, [fp, #-0x20]
    // 0xebd7c4: LoadField: r1 = r0->field_b
    //     0xebd7c4: ldur            w1, [x0, #0xb]
    // 0xebd7c8: LoadField: r2 = r0->field_f
    //     0xebd7c8: ldur            w2, [x0, #0xf]
    // 0xebd7cc: DecompressPointer r2
    //     0xebd7cc: add             x2, x2, HEAP, lsl #32
    // 0xebd7d0: LoadField: r3 = r2->field_b
    //     0xebd7d0: ldur            w3, [x2, #0xb]
    // 0xebd7d4: r2 = LoadInt32Instr(r1)
    //     0xebd7d4: sbfx            x2, x1, #1, #0x1f
    // 0xebd7d8: stur            x2, [fp, #-0x48]
    // 0xebd7dc: r1 = LoadInt32Instr(r3)
    //     0xebd7dc: sbfx            x1, x3, #1, #0x1f
    // 0xebd7e0: cmp             x2, x1
    // 0xebd7e4: b.ne            #0xebd7f0
    // 0xebd7e8: mov             x1, x0
    // 0xebd7ec: r0 = _growToNextCapacity()
    //     0xebd7ec: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xebd7f0: ldur            x2, [fp, #-0x20]
    // 0xebd7f4: ldur            x3, [fp, #-0x48]
    // 0xebd7f8: add             x0, x3, #1
    // 0xebd7fc: lsl             x1, x0, #1
    // 0xebd800: StoreField: r2->field_b = r1
    //     0xebd800: stur            w1, [x2, #0xb]
    // 0xebd804: LoadField: r1 = r2->field_f
    //     0xebd804: ldur            w1, [x2, #0xf]
    // 0xebd808: DecompressPointer r1
    //     0xebd808: add             x1, x1, HEAP, lsl #32
    // 0xebd80c: ldur            x0, [fp, #-0x40]
    // 0xebd810: ArrayStore: r1[r3] = r0  ; List_4
    //     0xebd810: add             x25, x1, x3, lsl #2
    //     0xebd814: add             x25, x25, #0xf
    //     0xebd818: str             w0, [x25]
    //     0xebd81c: tbz             w0, #0, #0xebd838
    //     0xebd820: ldurb           w16, [x1, #-1]
    //     0xebd824: ldurb           w17, [x0, #-1]
    //     0xebd828: and             x16, x17, x16, lsr #2
    //     0xebd82c: tst             x16, HEAP, lsr #32
    //     0xebd830: b.eq            #0xebd838
    //     0xebd834: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xebd838: mov             x3, x2
    // 0xebd83c: b               #0xebd9e4
    // 0xebd840: ldur            x2, [fp, #-0x20]
    // 0xebd844: LoadField: r0 = r2->field_b
    //     0xebd844: ldur            w0, [x2, #0xb]
    // 0xebd848: r1 = LoadInt32Instr(r0)
    //     0xebd848: sbfx            x1, x0, #1, #0x1f
    // 0xebd84c: cbz             x1, #0xebd8b0
    // 0xebd850: ldur            x3, [fp, #-0x18]
    // 0xebd854: LoadField: r4 = r3->field_f
    //     0xebd854: ldur            w4, [x3, #0xf]
    // 0xebd858: DecompressPointer r4
    //     0xebd858: add             x4, x4, HEAP, lsl #32
    // 0xebd85c: cmp             x1, #0
    // 0xebd860: b.le            #0xebdb48
    // 0xebd864: sub             x5, x1, #1
    // 0xebd868: mov             x0, x1
    // 0xebd86c: mov             x1, x5
    // 0xebd870: cmp             x1, x0
    // 0xebd874: b.hs            #0xebdb84
    // 0xebd878: LoadField: r0 = r2->field_f
    //     0xebd878: ldur            w0, [x2, #0xf]
    // 0xebd87c: DecompressPointer r0
    //     0xebd87c: add             x0, x0, HEAP, lsl #32
    // 0xebd880: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xebd880: add             x16, x0, x5, lsl #2
    //     0xebd884: ldur            w1, [x16, #0xf]
    // 0xebd888: DecompressPointer r1
    //     0xebd888: add             x1, x1, HEAP, lsl #32
    // 0xebd88c: stp             x1, x4, [SP]
    // 0xebd890: mov             x0, x4
    // 0xebd894: ClosureCall
    //     0xebd894: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xebd898: ldur            x2, [x0, #0x1f]
    //     0xebd89c: blr             x2
    // 0xebd8a0: r16 = true
    //     0xebd8a0: add             x16, NULL, #0x20  ; true
    // 0xebd8a4: cmp             w0, w16
    // 0xebd8a8: b.eq            #0xebd9e0
    // 0xebd8ac: ldur            x2, [fp, #-0x20]
    // 0xebd8b0: ldur            x3, [fp, #-0x40]
    // 0xebd8b4: r0 = LoadClassIdInstr(r3)
    //     0xebd8b4: ldur            x0, [x3, #-1]
    //     0xebd8b8: ubfx            x0, x0, #0xc, #0x14
    // 0xebd8bc: mov             x1, x3
    // 0xebd8c0: r0 = GDT[cid_x0 + -0xff0]()
    //     0xebd8c0: sub             lr, x0, #0xff0
    //     0xebd8c4: ldr             lr, [x21, lr, lsl #3]
    //     0xebd8c8: blr             lr
    // 0xebd8cc: mov             x3, x0
    // 0xebd8d0: ldur            x2, [fp, #-0x40]
    // 0xebd8d4: stur            x3, [fp, #-0x50]
    // 0xebd8d8: r0 = LoadClassIdInstr(r2)
    //     0xebd8d8: ldur            x0, [x2, #-1]
    //     0xebd8dc: ubfx            x0, x0, #0xc, #0x14
    // 0xebd8e0: mov             x1, x2
    // 0xebd8e4: r0 = GDT[cid_x0 + -0xfff]()
    //     0xebd8e4: sub             lr, x0, #0xfff
    //     0xebd8e8: ldr             lr, [x21, lr, lsl #3]
    //     0xebd8ec: blr             lr
    // 0xebd8f0: mov             x3, x0
    // 0xebd8f4: ldur            x2, [fp, #-0x40]
    // 0xebd8f8: stur            x3, [fp, #-0x58]
    // 0xebd8fc: r0 = LoadClassIdInstr(r2)
    //     0xebd8fc: ldur            x0, [x2, #-1]
    //     0xebd900: ubfx            x0, x0, #0xc, #0x14
    // 0xebd904: mov             x1, x2
    // 0xebd908: r0 = GDT[cid_x0 + -0xff3]()
    //     0xebd908: sub             lr, x0, #0xff3
    //     0xebd90c: ldr             lr, [x21, lr, lsl #3]
    //     0xebd910: blr             lr
    // 0xebd914: mov             x2, x0
    // 0xebd918: ldur            x1, [fp, #-0x40]
    // 0xebd91c: stur            x2, [fp, #-0x60]
    // 0xebd920: r0 = LoadClassIdInstr(r1)
    //     0xebd920: ldur            x0, [x1, #-1]
    //     0xebd924: ubfx            x0, x0, #0xc, #0x14
    // 0xebd928: r0 = GDT[cid_x0 + -0x1000]()
    //     0xebd928: sub             lr, x0, #1, lsl #12
    //     0xebd92c: ldr             lr, [x21, lr, lsl #3]
    //     0xebd930: blr             lr
    // 0xebd934: stur            x0, [fp, #-0x40]
    // 0xebd938: r0 = Frame()
    //     0xebd938: bl              #0xebdb94  ; AllocateFrameStub -> Frame (size=0x18)
    // 0xebd93c: mov             x2, x0
    // 0xebd940: ldur            x0, [fp, #-0x50]
    // 0xebd944: stur            x2, [fp, #-0x68]
    // 0xebd948: StoreField: r2->field_7 = r0
    //     0xebd948: stur            w0, [x2, #7]
    // 0xebd94c: ldur            x0, [fp, #-0x58]
    // 0xebd950: StoreField: r2->field_b = r0
    //     0xebd950: stur            w0, [x2, #0xb]
    // 0xebd954: ldur            x0, [fp, #-0x60]
    // 0xebd958: StoreField: r2->field_f = r0
    //     0xebd958: stur            w0, [x2, #0xf]
    // 0xebd95c: ldur            x0, [fp, #-0x40]
    // 0xebd960: StoreField: r2->field_13 = r0
    //     0xebd960: stur            w0, [x2, #0x13]
    // 0xebd964: ldur            x0, [fp, #-0x20]
    // 0xebd968: LoadField: r1 = r0->field_b
    //     0xebd968: ldur            w1, [x0, #0xb]
    // 0xebd96c: LoadField: r3 = r0->field_f
    //     0xebd96c: ldur            w3, [x0, #0xf]
    // 0xebd970: DecompressPointer r3
    //     0xebd970: add             x3, x3, HEAP, lsl #32
    // 0xebd974: LoadField: r4 = r3->field_b
    //     0xebd974: ldur            w4, [x3, #0xb]
    // 0xebd978: r3 = LoadInt32Instr(r1)
    //     0xebd978: sbfx            x3, x1, #1, #0x1f
    // 0xebd97c: stur            x3, [fp, #-0x48]
    // 0xebd980: r1 = LoadInt32Instr(r4)
    //     0xebd980: sbfx            x1, x4, #1, #0x1f
    // 0xebd984: cmp             x3, x1
    // 0xebd988: b.ne            #0xebd994
    // 0xebd98c: mov             x1, x0
    // 0xebd990: r0 = _growToNextCapacity()
    //     0xebd990: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xebd994: ldur            x3, [fp, #-0x20]
    // 0xebd998: ldur            x2, [fp, #-0x48]
    // 0xebd99c: add             x0, x2, #1
    // 0xebd9a0: lsl             x1, x0, #1
    // 0xebd9a4: StoreField: r3->field_b = r1
    //     0xebd9a4: stur            w1, [x3, #0xb]
    // 0xebd9a8: LoadField: r1 = r3->field_f
    //     0xebd9a8: ldur            w1, [x3, #0xf]
    // 0xebd9ac: DecompressPointer r1
    //     0xebd9ac: add             x1, x1, HEAP, lsl #32
    // 0xebd9b0: ldur            x0, [fp, #-0x68]
    // 0xebd9b4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xebd9b4: add             x25, x1, x2, lsl #2
    //     0xebd9b8: add             x25, x25, #0xf
    //     0xebd9bc: str             w0, [x25]
    //     0xebd9c0: tbz             w0, #0, #0xebd9dc
    //     0xebd9c4: ldurb           w16, [x1, #-1]
    //     0xebd9c8: ldurb           w17, [x0, #-1]
    //     0xebd9cc: and             x16, x17, x16, lsr #2
    //     0xebd9d0: tst             x16, HEAP, lsr #32
    //     0xebd9d4: b.eq            #0xebd9dc
    //     0xebd9d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xebd9dc: b               #0xebd9e4
    // 0xebd9e0: ldur            x3, [fp, #-0x20]
    // 0xebd9e4: mov             x5, x3
    // 0xebd9e8: ldur            x1, [fp, #-0x38]
    // 0xebd9ec: ldur            x4, [fp, #-0x10]
    // 0xebd9f0: ldur            x2, [fp, #-0x30]
    // 0xebd9f4: ldur            x3, [fp, #-0x28]
    // 0xebd9f8: b               #0xebd698
    // 0xebd9fc: ldur            x3, [fp, #-0x20]
    // 0xebda00: mov             x0, x4
    // 0xebda04: StoreField: r0->field_1f = rNULL
    //     0xebda04: stur            NULL, [x0, #0x1f]
    // 0xebda08: ldur            x2, [fp, #-0x18]
    // 0xebda0c: r1 = Function '<anonymous closure>':.
    //     0xebda0c: add             x1, PP, #0xd, lsl #12  ; [pp+0xd570] AnonymousClosure: (0xebdba0), in [package:stack_trace/src/trace.dart] Trace::foldFrames (0xebd5d0)
    //     0xebda10: ldr             x1, [x1, #0x570]
    // 0xebda14: r0 = AllocateClosure()
    //     0xebda14: bl              #0xec1630  ; AllocateClosureStub
    // 0xebda18: r16 = <Frame>
    //     0xebda18: add             x16, PP, #0xd, lsl #12  ; [pp+0xd528] TypeArguments: <Frame>
    //     0xebda1c: ldr             x16, [x16, #0x528]
    // 0xebda20: ldur            lr, [fp, #-0x20]
    // 0xebda24: stp             lr, x16, [SP, #8]
    // 0xebda28: str             x0, [SP]
    // 0xebda2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xebda2c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xebda30: r0 = map()
    //     0xebda30: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xebda34: LoadField: r1 = r0->field_7
    //     0xebda34: ldur            w1, [x0, #7]
    // 0xebda38: DecompressPointer r1
    //     0xebda38: add             x1, x1, HEAP, lsl #32
    // 0xebda3c: mov             x2, x0
    // 0xebda40: r0 = _GrowableList.of()
    //     0xebda40: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xebda44: stur            x0, [fp, #-0x20]
    // 0xebda48: LoadField: r1 = r0->field_b
    //     0xebda48: ldur            w1, [x0, #0xb]
    // 0xebda4c: r2 = LoadInt32Instr(r1)
    //     0xebda4c: sbfx            x2, x1, #1, #0x1f
    // 0xebda50: cmp             x2, #1
    // 0xebda54: b.le            #0xebdaa0
    // 0xebda58: ldur            x1, [fp, #-0x18]
    // 0xebda5c: LoadField: r2 = r1->field_f
    //     0xebda5c: ldur            w2, [x1, #0xf]
    // 0xebda60: DecompressPointer r2
    //     0xebda60: add             x2, x2, HEAP, lsl #32
    // 0xebda64: mov             x1, x0
    // 0xebda68: stur            x2, [fp, #-0x10]
    // 0xebda6c: r0 = first()
    //     0xebda6c: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0xebda70: ldur            x16, [fp, #-0x10]
    // 0xebda74: stp             x0, x16, [SP]
    // 0xebda78: ldur            x0, [fp, #-0x10]
    // 0xebda7c: ClosureCall
    //     0xebda7c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xebda80: ldur            x2, [x0, #0x1f]
    //     0xebda84: blr             x2
    // 0xebda88: r16 = true
    //     0xebda88: add             x16, NULL, #0x20  ; true
    // 0xebda8c: cmp             w0, w16
    // 0xebda90: b.ne            #0xebdaa0
    // 0xebda94: ldur            x1, [fp, #-0x20]
    // 0xebda98: r2 = 0
    //     0xebda98: movz            x2, #0
    // 0xebda9c: r0 = removeAt()
    //     0xebda9c: bl              #0xa8d758  ; [dart:core] _GrowableList::removeAt
    // 0xebdaa0: ldur            x2, [fp, #-8]
    // 0xebdaa4: ldur            x0, [fp, #-0x20]
    // 0xebdaa8: LoadField: r1 = r0->field_7
    //     0xebdaa8: ldur            w1, [x0, #7]
    // 0xebdaac: DecompressPointer r1
    //     0xebdaac: add             x1, x1, HEAP, lsl #32
    // 0xebdab0: r0 = ReversedListIterable()
    //     0xebdab0: bl              #0x668634  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0xebdab4: mov             x1, x0
    // 0xebdab8: ldur            x0, [fp, #-0x20]
    // 0xebdabc: StoreField: r1->field_b = r0
    //     0xebdabc: stur            w0, [x1, #0xb]
    // 0xebdac0: ldur            x0, [fp, #-8]
    // 0xebdac4: LoadField: r2 = r0->field_b
    //     0xebdac4: ldur            w2, [x0, #0xb]
    // 0xebdac8: DecompressPointer r2
    //     0xebdac8: add             x2, x2, HEAP, lsl #32
    // 0xebdacc: LoadField: r0 = r2->field_7
    //     0xebdacc: ldur            w0, [x2, #7]
    // 0xebdad0: DecompressPointer r0
    //     0xebdad0: add             x0, x0, HEAP, lsl #32
    // 0xebdad4: stur            x0, [fp, #-8]
    // 0xebdad8: r16 = false
    //     0xebdad8: add             x16, NULL, #0x30  ; false
    // 0xebdadc: str             x16, [SP]
    // 0xebdae0: mov             x2, x1
    // 0xebdae4: r1 = <Frame>
    //     0xebdae4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd528] TypeArguments: <Frame>
    //     0xebdae8: ldr             x1, [x1, #0x528]
    // 0xebdaec: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0xebdaec: add             x4, PP, #0xb, lsl #12  ; [pp+0xbcc0] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0xebdaf0: ldr             x4, [x4, #0xcc0]
    // 0xebdaf4: r0 = List.from()
    //     0xebdaf4: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0xebdaf8: r16 = <Frame>
    //     0xebdaf8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd528] TypeArguments: <Frame>
    //     0xebdafc: ldr             x16, [x16, #0x528]
    // 0xebdb00: stp             x0, x16, [SP]
    // 0xebdb04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xebdb04: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xebdb08: r0 = makeFixedListUnmodifiable()
    //     0xebdb08: bl              #0x8b7f28  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0xebdb0c: stur            x0, [fp, #-0x10]
    // 0xebdb10: r0 = Trace()
    //     0xebdb10: bl              #0xebdb88  ; AllocateTraceStub -> Trace (size=0x10)
    // 0xebdb14: mov             x1, x0
    // 0xebdb18: ldur            x0, [fp, #-0x10]
    // 0xebdb1c: stur            x1, [fp, #-0x18]
    // 0xebdb20: StoreField: r1->field_7 = r0
    //     0xebdb20: stur            w0, [x1, #7]
    // 0xebdb24: r0 = _StringStackTrace()
    //     0xebdb24: bl              #0x697f3c  ; Allocate_StringStackTraceStub -> _StringStackTrace (size=0xc)
    // 0xebdb28: mov             x1, x0
    // 0xebdb2c: ldur            x0, [fp, #-8]
    // 0xebdb30: StoreField: r1->field_7 = r0
    //     0xebdb30: stur            w0, [x1, #7]
    // 0xebdb34: ldur            x0, [fp, #-0x18]
    // 0xebdb38: StoreField: r0->field_b = r1
    //     0xebdb38: stur            w1, [x0, #0xb]
    // 0xebdb3c: LeaveFrame
    //     0xebdb3c: mov             SP, fp
    //     0xebdb40: ldp             fp, lr, [SP], #0x10
    // 0xebdb44: ret
    //     0xebdb44: ret             
    // 0xebdb48: r0 = noElement()
    //     0xebdb48: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0xebdb4c: r0 = Throw()
    //     0xebdb4c: bl              #0xec04b8  ; ThrowStub
    // 0xebdb50: brk             #0
    // 0xebdb54: ldur            x0, [fp, #-0x30]
    // 0xebdb58: r0 = ConcurrentModificationError()
    //     0xebdb58: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xebdb5c: mov             x1, x0
    // 0xebdb60: ldur            x0, [fp, #-0x30]
    // 0xebdb64: StoreField: r1->field_b = r0
    //     0xebdb64: stur            w0, [x1, #0xb]
    // 0xebdb68: mov             x0, x1
    // 0xebdb6c: r0 = Throw()
    //     0xebdb6c: bl              #0xec04b8  ; ThrowStub
    // 0xebdb70: brk             #0
    // 0xebdb74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebdb74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebdb78: b               #0xebd5f0
    // 0xebdb7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebdb7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebdb80: b               #0xebd6a8
    // 0xebdb84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xebdb84: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Frame <anonymous closure>(dynamic, Frame) {
    // ** addr: 0xebdba0, size: 0x114
    // 0xebdba0: EnterFrame
    //     0xebdba0: stp             fp, lr, [SP, #-0x10]!
    //     0xebdba4: mov             fp, SP
    // 0xebdba8: AllocStack(0x20)
    //     0xebdba8: sub             SP, SP, #0x20
    // 0xebdbac: SetupParameters()
    //     0xebdbac: ldr             x0, [fp, #0x18]
    //     0xebdbb0: ldur            w1, [x0, #0x17]
    //     0xebdbb4: add             x1, x1, HEAP, lsl #32
    // 0xebdbb8: CheckStackOverflow
    //     0xebdbb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebdbbc: cmp             SP, x16
    //     0xebdbc0: b.ls            #0xebdcac
    // 0xebdbc4: ldr             x2, [fp, #0x10]
    // 0xebdbc8: r0 = LoadClassIdInstr(r2)
    //     0xebdbc8: ldur            x0, [x2, #-1]
    //     0xebdbcc: ubfx            x0, x0, #0xc, #0x14
    // 0xebdbd0: cmp             x0, #0x1c5
    // 0xebdbd4: b.eq            #0xebdbfc
    // 0xebdbd8: LoadField: r0 = r1->field_f
    //     0xebdbd8: ldur            w0, [x1, #0xf]
    // 0xebdbdc: DecompressPointer r0
    //     0xebdbdc: add             x0, x0, HEAP, lsl #32
    // 0xebdbe0: stp             x2, x0, [SP]
    // 0xebdbe4: ClosureCall
    //     0xebdbe4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xebdbe8: ldur            x2, [x0, #0x1f]
    //     0xebdbec: blr             x2
    // 0xebdbf0: r16 = true
    //     0xebdbf0: add             x16, NULL, #0x20  ; true
    // 0xebdbf4: cmp             w0, w16
    // 0xebdbf8: b.eq            #0xebdc0c
    // 0xebdbfc: ldr             x0, [fp, #0x10]
    // 0xebdc00: LeaveFrame
    //     0xebdc00: mov             SP, fp
    //     0xebdc04: ldp             fp, lr, [SP], #0x10
    // 0xebdc08: ret
    //     0xebdc08: ret             
    // 0xebdc0c: ldr             x2, [fp, #0x10]
    // 0xebdc10: r0 = LoadClassIdInstr(r2)
    //     0xebdc10: ldur            x0, [x2, #-1]
    //     0xebdc14: ubfx            x0, x0, #0xc, #0x14
    // 0xebdc18: mov             x1, x2
    // 0xebdc1c: r0 = GDT[cid_x0 + -0xffb]()
    //     0xebdc1c: sub             lr, x0, #0xffb
    //     0xebdc20: ldr             lr, [x21, lr, lsl #3]
    //     0xebdc24: blr             lr
    // 0xebdc28: stur            x0, [fp, #-8]
    // 0xebdc2c: r0 = InitLateStaticField(0xfc0) // [package:stack_trace/src/trace.dart] ::_terseRegExp
    //     0xebdc2c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xebdc30: ldr             x0, [x0, #0x1f80]
    //     0xebdc34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xebdc38: cmp             w0, w16
    //     0xebdc3c: b.ne            #0xebdc4c
    //     0xebdc40: add             x2, PP, #0xd, lsl #12  ; [pp+0xd578] Field <::._terseRegExp@1035486701>: static late final (offset: 0xfc0)
    //     0xebdc44: ldr             x2, [x2, #0x578]
    //     0xebdc48: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xebdc4c: ldur            x1, [fp, #-8]
    // 0xebdc50: mov             x2, x0
    // 0xebdc54: r3 = ""
    //     0xebdc54: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xebdc58: r0 = replaceAll()
    //     0xebdc58: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xebdc5c: mov             x1, x0
    // 0xebdc60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xebdc60: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xebdc64: r0 = parse()
    //     0xebdc64: bl              #0x60d170  ; [dart:core] Uri::parse
    // 0xebdc68: mov             x2, x0
    // 0xebdc6c: ldr             x1, [fp, #0x10]
    // 0xebdc70: stur            x2, [fp, #-8]
    // 0xebdc74: r0 = LoadClassIdInstr(r1)
    //     0xebdc74: ldur            x0, [x1, #-1]
    //     0xebdc78: ubfx            x0, x0, #0xc, #0x14
    // 0xebdc7c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xebdc7c: sub             lr, x0, #1, lsl #12
    //     0xebdc80: ldr             lr, [x21, lr, lsl #3]
    //     0xebdc84: blr             lr
    // 0xebdc88: stur            x0, [fp, #-0x10]
    // 0xebdc8c: r0 = Frame()
    //     0xebdc8c: bl              #0xebdb94  ; AllocateFrameStub -> Frame (size=0x18)
    // 0xebdc90: ldur            x1, [fp, #-8]
    // 0xebdc94: StoreField: r0->field_7 = r1
    //     0xebdc94: stur            w1, [x0, #7]
    // 0xebdc98: ldur            x1, [fp, #-0x10]
    // 0xebdc9c: StoreField: r0->field_13 = r1
    //     0xebdc9c: stur            w1, [x0, #0x13]
    // 0xebdca0: LeaveFrame
    //     0xebdca0: mov             SP, fp
    //     0xebdca4: ldp             fp, lr, [SP], #0x10
    // 0xebdca8: ret
    //     0xebdca8: ret             
    // 0xebdcac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebdcac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebdcb0: b               #0xebdbc4
  }
  [closure] bool <anonymous closure>(dynamic, Frame) {
    // ** addr: 0xebdd0c, size: 0x17c
    // 0xebdd0c: EnterFrame
    //     0xebdd0c: stp             fp, lr, [SP, #-0x10]!
    //     0xebdd10: mov             fp, SP
    // 0xebdd14: AllocStack(0x10)
    //     0xebdd14: sub             SP, SP, #0x10
    // 0xebdd18: SetupParameters()
    //     0xebdd18: ldr             x0, [fp, #0x18]
    //     0xebdd1c: ldur            w1, [x0, #0x17]
    //     0xebdd20: add             x1, x1, HEAP, lsl #32
    // 0xebdd24: CheckStackOverflow
    //     0xebdd24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebdd28: cmp             SP, x16
    //     0xebdd2c: b.ls            #0xebde80
    // 0xebdd30: LoadField: r0 = r1->field_13
    //     0xebdd30: ldur            w0, [x1, #0x13]
    // 0xebdd34: DecompressPointer r0
    //     0xebdd34: add             x0, x0, HEAP, lsl #32
    // 0xebdd38: ldr             x16, [fp, #0x10]
    // 0xebdd3c: stp             x16, x0, [SP]
    // 0xebdd40: ClosureCall
    //     0xebdd40: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xebdd44: ldur            x2, [x0, #0x1f]
    //     0xebdd48: blr             x2
    // 0xebdd4c: r16 = true
    //     0xebdd4c: add             x16, NULL, #0x20  ; true
    // 0xebdd50: cmp             w0, w16
    // 0xebdd54: b.ne            #0xebdd68
    // 0xebdd58: r0 = true
    //     0xebdd58: add             x0, NULL, #0x20  ; true
    // 0xebdd5c: LeaveFrame
    //     0xebdd5c: mov             SP, fp
    //     0xebdd60: ldp             fp, lr, [SP], #0x10
    // 0xebdd64: ret
    //     0xebdd64: ret             
    // 0xebdd68: ldr             x2, [fp, #0x10]
    // 0xebdd6c: r0 = LoadClassIdInstr(r2)
    //     0xebdd6c: ldur            x0, [x2, #-1]
    //     0xebdd70: ubfx            x0, x0, #0xc, #0x14
    // 0xebdd74: mov             x1, x2
    // 0xebdd78: r0 = GDT[cid_x0 + -0xff4]()
    //     0xebdd78: sub             lr, x0, #0xff4
    //     0xebdd7c: ldr             lr, [x21, lr, lsl #3]
    //     0xebdd80: blr             lr
    // 0xebdd84: tbnz            w0, #4, #0xebdd98
    // 0xebdd88: r0 = true
    //     0xebdd88: add             x0, NULL, #0x20  ; true
    // 0xebdd8c: LeaveFrame
    //     0xebdd8c: mov             SP, fp
    //     0xebdd90: ldp             fp, lr, [SP], #0x10
    // 0xebdd94: ret
    //     0xebdd94: ret             
    // 0xebdd98: ldr             x2, [fp, #0x10]
    // 0xebdd9c: r0 = LoadClassIdInstr(r2)
    //     0xebdd9c: ldur            x0, [x2, #-1]
    //     0xebdda0: ubfx            x0, x0, #0xc, #0x14
    // 0xebdda4: mov             x1, x2
    // 0xebdda8: r0 = GDT[cid_x0 + -0xff7]()
    //     0xebdda8: sub             lr, x0, #0xff7
    //     0xebddac: ldr             lr, [x21, lr, lsl #3]
    //     0xebddb0: blr             lr
    // 0xebddb4: r1 = LoadClassIdInstr(r0)
    //     0xebddb4: ldur            x1, [x0, #-1]
    //     0xebddb8: ubfx            x1, x1, #0xc, #0x14
    // 0xebddbc: r16 = "stack_trace"
    //     0xebddbc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd588] "stack_trace"
    //     0xebddc0: ldr             x16, [x16, #0x588]
    // 0xebddc4: stp             x16, x0, [SP]
    // 0xebddc8: mov             x0, x1
    // 0xebddcc: mov             lr, x0
    // 0xebddd0: ldr             lr, [x21, lr, lsl #3]
    // 0xebddd4: blr             lr
    // 0xebddd8: tbnz            w0, #4, #0xebddec
    // 0xebdddc: r0 = true
    //     0xebdddc: add             x0, NULL, #0x20  ; true
    // 0xebdde0: LeaveFrame
    //     0xebdde0: mov             SP, fp
    //     0xebdde4: ldp             fp, lr, [SP], #0x10
    // 0xebdde8: ret
    //     0xebdde8: ret             
    // 0xebddec: ldr             x2, [fp, #0x10]
    // 0xebddf0: r0 = LoadClassIdInstr(r2)
    //     0xebddf0: ldur            x0, [x2, #-1]
    //     0xebddf4: ubfx            x0, x0, #0xc, #0x14
    // 0xebddf8: mov             x1, x2
    // 0xebddfc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xebddfc: sub             lr, x0, #1, lsl #12
    //     0xebde00: ldr             lr, [x21, lr, lsl #3]
    //     0xebde04: blr             lr
    // 0xebde08: r1 = LoadClassIdInstr(r0)
    //     0xebde08: ldur            x1, [x0, #-1]
    //     0xebde0c: ubfx            x1, x1, #0xc, #0x14
    // 0xebde10: mov             x16, x0
    // 0xebde14: mov             x0, x1
    // 0xebde18: mov             x1, x16
    // 0xebde1c: r2 = "<async>"
    //     0xebde1c: add             x2, PP, #0xd, lsl #12  ; [pp+0xd590] "<async>"
    //     0xebde20: ldr             x2, [x2, #0x590]
    // 0xebde24: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xebde24: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xebde28: r0 = GDT[cid_x0 + -0xffc]()
    //     0xebde28: sub             lr, x0, #0xffc
    //     0xebde2c: ldr             lr, [x21, lr, lsl #3]
    //     0xebde30: blr             lr
    // 0xebde34: tbz             w0, #4, #0xebde48
    // 0xebde38: r0 = false
    //     0xebde38: add             x0, NULL, #0x30  ; false
    // 0xebde3c: LeaveFrame
    //     0xebde3c: mov             SP, fp
    //     0xebde40: ldp             fp, lr, [SP], #0x10
    // 0xebde44: ret
    //     0xebde44: ret             
    // 0xebde48: ldr             x1, [fp, #0x10]
    // 0xebde4c: r0 = LoadClassIdInstr(r1)
    //     0xebde4c: ldur            x0, [x1, #-1]
    //     0xebde50: ubfx            x0, x0, #0xc, #0x14
    // 0xebde54: r0 = GDT[cid_x0 + -0xfff]()
    //     0xebde54: sub             lr, x0, #0xfff
    //     0xebde58: ldr             lr, [x21, lr, lsl #3]
    //     0xebde5c: blr             lr
    // 0xebde60: cmp             w0, NULL
    // 0xebde64: r16 = true
    //     0xebde64: add             x16, NULL, #0x20  ; true
    // 0xebde68: r17 = false
    //     0xebde68: add             x17, NULL, #0x30  ; false
    // 0xebde6c: csel            x1, x16, x17, eq
    // 0xebde70: mov             x0, x1
    // 0xebde74: LeaveFrame
    //     0xebde74: mov             SP, fp
    //     0xebde78: ldp             fp, lr, [SP], #0x10
    // 0xebde7c: ret
    //     0xebde7c: ret             
    // 0xebde80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebde80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebde84: b               #0xebdd30
  }
  static _ _parseVM(/* No info */) {
    // ** addr: 0xec8184, size: 0x264
    // 0xec8184: EnterFrame
    //     0xec8184: stp             fp, lr, [SP, #-0x10]!
    //     0xec8188: mov             fp, SP
    // 0xec818c: AllocStack(0x30)
    //     0xec818c: sub             SP, SP, #0x30
    // 0xec8190: CheckStackOverflow
    //     0xec8190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec8194: cmp             SP, x16
    //     0xec8198: b.ls            #0xec83e0
    // 0xec819c: r0 = trim()
    //     0xec819c: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xec81a0: stur            x0, [fp, #-8]
    // 0xec81a4: r0 = InitLateStaticField(0xfc4) // [package:stack_trace/src/utils.dart] ::vmChainGap
    //     0xec81a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec81a8: ldr             x0, [x0, #0x1f88]
    //     0xec81ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec81b0: cmp             w0, w16
    //     0xec81b4: b.ne            #0xec81c4
    //     0xec81b8: add             x2, PP, #0xd, lsl #12  ; [pp+0xd598] Field <::.vmChainGap>: static late final (offset: 0xfc4)
    //     0xec81bc: ldr             x2, [x2, #0x598]
    //     0xec81c0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec81c4: ldur            x1, [fp, #-8]
    // 0xec81c8: mov             x2, x0
    // 0xec81cc: r3 = ""
    //     0xec81cc: ldr             x3, [PP, #0x288]  ; [pp+0x288] ""
    // 0xec81d0: r0 = replaceAll()
    //     0xec81d0: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0xec81d4: r1 = LoadClassIdInstr(r0)
    //     0xec81d4: ldur            x1, [x0, #-1]
    //     0xec81d8: ubfx            x1, x1, #0xc, #0x14
    // 0xec81dc: mov             x16, x0
    // 0xec81e0: mov             x0, x1
    // 0xec81e4: mov             x1, x16
    // 0xec81e8: r2 = "\n"
    //     0xec81e8: ldr             x2, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xec81ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xec81ec: sub             lr, x0, #1, lsl #12
    //     0xec81f0: ldr             lr, [x21, lr, lsl #3]
    //     0xec81f4: blr             lr
    // 0xec81f8: r1 = Function '<anonymous closure>': static.
    //     0xec81f8: add             x1, PP, #0xd, lsl #12  ; [pp+0xd5a0] AnonymousClosure: static (0x6446b8), in [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackString (0x643988)
    //     0xec81fc: ldr             x1, [x1, #0x5a0]
    // 0xec8200: r2 = Null
    //     0xec8200: mov             x2, NULL
    // 0xec8204: stur            x0, [fp, #-8]
    // 0xec8208: r0 = AllocateClosure()
    //     0xec8208: bl              #0xec1630  ; AllocateClosureStub
    // 0xec820c: ldur            x1, [fp, #-8]
    // 0xec8210: mov             x2, x0
    // 0xec8214: r0 = where()
    //     0xec8214: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xec8218: mov             x1, x0
    // 0xec821c: stur            x0, [fp, #-8]
    // 0xec8220: r0 = iterator()
    //     0xec8220: bl              #0x887e0c  ; [dart:_internal] WhereIterable::iterator
    // 0xec8224: r1 = LoadClassIdInstr(r0)
    //     0xec8224: ldur            x1, [x0, #-1]
    //     0xec8228: ubfx            x1, x1, #0xc, #0x14
    // 0xec822c: mov             x16, x0
    // 0xec8230: mov             x0, x1
    // 0xec8234: mov             x1, x16
    // 0xec8238: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xec8238: movz            x17, #0x292d
    //     0xec823c: movk            x17, #0x1, lsl #16
    //     0xec8240: add             lr, x0, x17
    //     0xec8244: ldr             lr, [x21, lr, lsl #3]
    //     0xec8248: blr             lr
    // 0xec824c: eor             x1, x0, #0x10
    // 0xec8250: tbnz            w1, #4, #0xec8270
    // 0xec8254: r1 = <Frame>
    //     0xec8254: add             x1, PP, #0xd, lsl #12  ; [pp+0xd528] TypeArguments: <Frame>
    //     0xec8258: ldr             x1, [x1, #0x528]
    // 0xec825c: r2 = 0
    //     0xec825c: movz            x2, #0
    // 0xec8260: r0 = _GrowableList()
    //     0xec8260: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xec8264: LeaveFrame
    //     0xec8264: mov             SP, fp
    //     0xec8268: ldp             fp, lr, [SP], #0x10
    // 0xec826c: ret
    //     0xec826c: ret             
    // 0xec8270: ldur            x16, [fp, #-8]
    // 0xec8274: str             x16, [SP]
    // 0xec8278: r0 = length()
    //     0xec8278: bl              #0x913c28  ; [dart:core] Iterable::length
    // 0xec827c: r1 = LoadInt32Instr(r0)
    //     0xec827c: sbfx            x1, x0, #1, #0x1f
    //     0xec8280: tbz             w0, #0, #0xec8288
    //     0xec8284: ldur            x1, [x0, #7]
    // 0xec8288: sub             x2, x1, #1
    // 0xec828c: ldur            x1, [fp, #-8]
    // 0xec8290: r0 = take()
    //     0xec8290: bl              #0x7a99b0  ; [dart:core] Iterable::take
    // 0xec8294: r16 = <Frame>
    //     0xec8294: add             x16, PP, #0xd, lsl #12  ; [pp+0xd528] TypeArguments: <Frame>
    //     0xec8298: ldr             x16, [x16, #0x528]
    // 0xec829c: stp             x0, x16, [SP, #8]
    // 0xec82a0: r16 = Closure: (String) => Frame from Function 'Frame.parseVM': static.
    //     0xec82a0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5a8] Closure: (String) => Frame from Function 'Frame.parseVM': static. (0x7e54fb8c8450)
    //     0xec82a4: ldr             x16, [x16, #0x5a8]
    // 0xec82a8: str             x16, [SP]
    // 0xec82ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xec82ac: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xec82b0: r0 = map()
    //     0xec82b0: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xec82b4: LoadField: r1 = r0->field_7
    //     0xec82b4: ldur            w1, [x0, #7]
    // 0xec82b8: DecompressPointer r1
    //     0xec82b8: add             x1, x1, HEAP, lsl #32
    // 0xec82bc: mov             x2, x0
    // 0xec82c0: r0 = _GrowableList.of()
    //     0xec82c0: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xec82c4: ldur            x1, [fp, #-8]
    // 0xec82c8: stur            x0, [fp, #-0x10]
    // 0xec82cc: r0 = last()
    //     0xec82cc: bl              #0x7a963c  ; [dart:core] Iterable::last
    // 0xec82d0: LoadField: r1 = r0->field_7
    //     0xec82d0: ldur            w1, [x0, #7]
    // 0xec82d4: r2 = LoadInt32Instr(r1)
    //     0xec82d4: sbfx            x2, x1, #1, #0x1f
    // 0xec82d8: sub             x1, x2, #3
    // 0xec82dc: lsl             x2, x1, #1
    // 0xec82e0: stp             x2, x0, [SP, #8]
    // 0xec82e4: r16 = ".da"
    //     0xec82e4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5b0] ".da"
    //     0xec82e8: ldr             x16, [x16, #0x5b0]
    // 0xec82ec: str             x16, [SP]
    // 0xec82f0: r0 = _substringMatches()
    //     0xec82f0: bl              #0x6085f8  ; [dart:core] _StringBase::_substringMatches
    // 0xec82f4: tbz             w0, #4, #0xec83cc
    // 0xec82f8: ldur            x0, [fp, #-0x10]
    // 0xec82fc: ldur            x1, [fp, #-8]
    // 0xec8300: r0 = last()
    //     0xec8300: bl              #0x7a963c  ; [dart:core] Iterable::last
    // 0xec8304: mov             x2, x0
    // 0xec8308: r1 = Null
    //     0xec8308: mov             x1, NULL
    // 0xec830c: r0 = Frame.parseVM()
    //     0xec830c: bl              #0xec83e8  ; [package:stack_trace/src/frame.dart] Frame::Frame.parseVM
    // 0xec8310: mov             x4, x0
    // 0xec8314: ldur            x3, [fp, #-0x10]
    // 0xec8318: stur            x4, [fp, #-8]
    // 0xec831c: LoadField: r2 = r3->field_7
    //     0xec831c: ldur            w2, [x3, #7]
    // 0xec8320: DecompressPointer r2
    //     0xec8320: add             x2, x2, HEAP, lsl #32
    // 0xec8324: mov             x0, x4
    // 0xec8328: r1 = Null
    //     0xec8328: mov             x1, NULL
    // 0xec832c: cmp             w2, NULL
    // 0xec8330: b.eq            #0xec8350
    // 0xec8334: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xec8334: ldur            w4, [x2, #0x17]
    // 0xec8338: DecompressPointer r4
    //     0xec8338: add             x4, x4, HEAP, lsl #32
    // 0xec833c: r8 = X0
    //     0xec833c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xec8340: LoadField: r9 = r4->field_7
    //     0xec8340: ldur            x9, [x4, #7]
    // 0xec8344: r3 = Null
    //     0xec8344: add             x3, PP, #0xd, lsl #12  ; [pp+0xd5b8] Null
    //     0xec8348: ldr             x3, [x3, #0x5b8]
    // 0xec834c: blr             x9
    // 0xec8350: ldur            x0, [fp, #-0x10]
    // 0xec8354: LoadField: r1 = r0->field_b
    //     0xec8354: ldur            w1, [x0, #0xb]
    // 0xec8358: LoadField: r2 = r0->field_f
    //     0xec8358: ldur            w2, [x0, #0xf]
    // 0xec835c: DecompressPointer r2
    //     0xec835c: add             x2, x2, HEAP, lsl #32
    // 0xec8360: LoadField: r3 = r2->field_b
    //     0xec8360: ldur            w3, [x2, #0xb]
    // 0xec8364: r2 = LoadInt32Instr(r1)
    //     0xec8364: sbfx            x2, x1, #1, #0x1f
    // 0xec8368: stur            x2, [fp, #-0x18]
    // 0xec836c: r1 = LoadInt32Instr(r3)
    //     0xec836c: sbfx            x1, x3, #1, #0x1f
    // 0xec8370: cmp             x2, x1
    // 0xec8374: b.ne            #0xec8380
    // 0xec8378: mov             x1, x0
    // 0xec837c: r0 = _growToNextCapacity()
    //     0xec837c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xec8380: ldur            x2, [fp, #-0x10]
    // 0xec8384: ldur            x3, [fp, #-0x18]
    // 0xec8388: add             x4, x3, #1
    // 0xec838c: lsl             x5, x4, #1
    // 0xec8390: StoreField: r2->field_b = r5
    //     0xec8390: stur            w5, [x2, #0xb]
    // 0xec8394: LoadField: r1 = r2->field_f
    //     0xec8394: ldur            w1, [x2, #0xf]
    // 0xec8398: DecompressPointer r1
    //     0xec8398: add             x1, x1, HEAP, lsl #32
    // 0xec839c: ldur            x0, [fp, #-8]
    // 0xec83a0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xec83a0: add             x25, x1, x3, lsl #2
    //     0xec83a4: add             x25, x25, #0xf
    //     0xec83a8: str             w0, [x25]
    //     0xec83ac: tbz             w0, #0, #0xec83c8
    //     0xec83b0: ldurb           w16, [x1, #-1]
    //     0xec83b4: ldurb           w17, [x0, #-1]
    //     0xec83b8: and             x16, x17, x16, lsr #2
    //     0xec83bc: tst             x16, HEAP, lsr #32
    //     0xec83c0: b.eq            #0xec83c8
    //     0xec83c4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xec83c8: b               #0xec83d0
    // 0xec83cc: ldur            x2, [fp, #-0x10]
    // 0xec83d0: mov             x0, x2
    // 0xec83d4: LeaveFrame
    //     0xec83d4: mov             SP, fp
    //     0xec83d8: ldp             fp, lr, [SP], #0x10
    // 0xec83dc: ret
    //     0xec83dc: ret             
    // 0xec83e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec83e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec83e4: b               #0xec819c
  }
}
