// lib: , url: package:stack_trace/src/unparsed_frame.dart

// class id: 1051172, size: 0x8
class :: {
}

// class id: 453, size: 0x28, field offset: 0x8
class UnparsedFrame extends Object
    implements Frame {

  const get _ column(/* No info */) {
    // ** addr: 0xeb8418, size: 0xc
    // 0xeb8418: LoadField: r0 = r1->field_f
    //     0xeb8418: ldur            w0, [x1, #0xf]
    // 0xeb841c: DecompressPointer r0
    //     0xeb841c: add             x0, x0, HEAP, lsl #32
    // 0xeb8420: ret
    //     0xeb8420: ret             
  }
  const get _ location(/* No info */) {
    // ** addr: 0xeb86d4, size: 0xc
    // 0xeb86d4: LoadField: r0 = r1->field_1f
    //     0xeb86d4: ldur            w0, [x1, #0x1f]
    // 0xeb86d8: DecompressPointer r0
    //     0xeb86d8: add             x0, x0, HEAP, lsl #32
    // 0xeb86dc: ret
    //     0xeb86dc: ret             
  }
  const get _ member(/* No info */) {
    // ** addr: 0xeb86ec, size: 0xc
    // 0xeb86ec: LoadField: r0 = r1->field_23
    //     0xeb86ec: ldur            w0, [x1, #0x23]
    // 0xeb86f0: DecompressPointer r0
    //     0xeb86f0: add             x0, x0, HEAP, lsl #32
    // 0xeb86f4: ret
    //     0xeb86f4: ret             
  }
  _ UnparsedFrame(/* No info */) {
    // ** addr: 0xec8578, size: 0xac
    // 0xec8578: EnterFrame
    //     0xec8578: stp             fp, lr, [SP, #-0x10]!
    //     0xec857c: mov             fp, SP
    // 0xec8580: AllocStack(0x18)
    //     0xec8580: sub             SP, SP, #0x18
    // 0xec8584: r3 = false
    //     0xec8584: add             x3, NULL, #0x30  ; false
    // 0xec8588: r0 = "unparsed"
    //     0xec8588: add             x0, PP, #0xd, lsl #12  ; [pp+0xd608] "unparsed"
    //     0xec858c: ldr             x0, [x0, #0x608]
    // 0xec8590: mov             x4, x1
    // 0xec8594: stur            x1, [fp, #-8]
    // 0xec8598: stur            x2, [fp, #-0x10]
    // 0xec859c: CheckStackOverflow
    //     0xec859c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec85a0: cmp             SP, x16
    //     0xec85a4: b.ls            #0xec861c
    // 0xec85a8: StoreField: r4->field_13 = r3
    //     0xec85a8: stur            w3, [x4, #0x13]
    // 0xec85ac: ArrayStore: r4[0] = r0  ; List_4
    //     0xec85ac: stur            w0, [x4, #0x17]
    // 0xec85b0: StoreField: r4->field_1f = r0
    //     0xec85b0: stur            w0, [x4, #0x1f]
    // 0xec85b4: r16 = "unparsed"
    //     0xec85b4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd608] "unparsed"
    //     0xec85b8: ldr             x16, [x16, #0x608]
    // 0xec85bc: str             x16, [SP]
    // 0xec85c0: r1 = Null
    //     0xec85c0: mov             x1, NULL
    // 0xec85c4: r4 = const [0, 0x2, 0x1, 0x1, path, 0x1, null]
    //     0xec85c4: ldr             x4, [PP, #0x35f8]  ; [pp+0x35f8] List(7) [0, 0x2, 0x1, 0x1, "path", 0x1, Null]
    // 0xec85c8: r0 = _Uri()
    //     0xec85c8: bl              #0x5ff47c  ; [dart:core] _Uri::_Uri
    // 0xec85cc: ldur            x1, [fp, #-8]
    // 0xec85d0: StoreField: r1->field_7 = r0
    //     0xec85d0: stur            w0, [x1, #7]
    //     0xec85d4: ldurb           w16, [x1, #-1]
    //     0xec85d8: ldurb           w17, [x0, #-1]
    //     0xec85dc: and             x16, x17, x16, lsr #2
    //     0xec85e0: tst             x16, HEAP, lsr #32
    //     0xec85e4: b.eq            #0xec85ec
    //     0xec85e8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xec85ec: ldur            x0, [fp, #-0x10]
    // 0xec85f0: StoreField: r1->field_23 = r0
    //     0xec85f0: stur            w0, [x1, #0x23]
    //     0xec85f4: ldurb           w16, [x1, #-1]
    //     0xec85f8: ldurb           w17, [x0, #-1]
    //     0xec85fc: and             x16, x17, x16, lsr #2
    //     0xec8600: tst             x16, HEAP, lsr #32
    //     0xec8604: b.eq            #0xec860c
    //     0xec8608: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xec860c: r0 = Null
    //     0xec860c: mov             x0, NULL
    // 0xec8610: LeaveFrame
    //     0xec8610: mov             SP, fp
    //     0xec8614: ldp             fp, lr, [SP], #0x10
    // 0xec8618: ret
    //     0xec8618: ret             
    // 0xec861c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec861c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec8620: b               #0xec85a8
  }
}
