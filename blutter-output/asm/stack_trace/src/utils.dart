// lib: , url: package:stack_trace/src/utils.dart

// class id: 1051173, size: 0x8
class :: {

  static late final RegExp vmChainGap; // offset: 0xfc4

  static RegExp vmChainGap() {
    // ** addr: 0xec8e28, size: 0x58
    // 0xec8e28: EnterFrame
    //     0xec8e28: stp             fp, lr, [SP, #-0x10]!
    //     0xec8e2c: mov             fp, SP
    // 0xec8e30: AllocStack(0x30)
    //     0xec8e30: sub             SP, SP, #0x30
    // 0xec8e34: CheckStackOverflow
    //     0xec8e34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec8e38: cmp             SP, x16
    //     0xec8e3c: b.ls            #0xec8e78
    // 0xec8e40: r16 = "^<asynchronous suspension>\\n\?$"
    //     0xec8e40: add             x16, PP, #0xd, lsl #12  ; [pp+0xd640] "^<asynchronous suspension>\\n\?$"
    //     0xec8e44: ldr             x16, [x16, #0x640]
    // 0xec8e48: stp             x16, NULL, [SP, #0x20]
    // 0xec8e4c: r16 = true
    //     0xec8e4c: add             x16, NULL, #0x20  ; true
    // 0xec8e50: r30 = true
    //     0xec8e50: add             lr, NULL, #0x20  ; true
    // 0xec8e54: stp             lr, x16, [SP, #0x10]
    // 0xec8e58: r16 = false
    //     0xec8e58: add             x16, NULL, #0x30  ; false
    // 0xec8e5c: r30 = false
    //     0xec8e5c: add             lr, NULL, #0x30  ; false
    // 0xec8e60: stp             lr, x16, [SP]
    // 0xec8e64: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xec8e64: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xec8e68: r0 = _RegExp()
    //     0xec8e68: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xec8e6c: LeaveFrame
    //     0xec8e6c: mov             SP, fp
    //     0xec8e70: ldp             fp, lr, [SP], #0x10
    // 0xec8e74: ret
    //     0xec8e74: ret             
    // 0xec8e78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec8e78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec8e7c: b               #0xec8e40
  }
}
