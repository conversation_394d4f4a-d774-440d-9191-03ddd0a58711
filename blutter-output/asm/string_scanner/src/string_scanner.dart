// lib: , url: package:string_scanner/src/string_scanner.dart

// class id: 1051175, size: 0x8
class :: {
}

// class id: 452, size: 0x20, field offset: 0x8
class StringScanner extends Object {

  _ expectDone(/* No info */) {
    // ** addr: 0x7056b8, size: 0x64
    // 0x7056b8: EnterFrame
    //     0x7056b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7056bc: mov             fp, SP
    // 0x7056c0: AllocStack(0x8)
    //     0x7056c0: sub             SP, SP, #8
    // 0x7056c4: SetupParameters(StringScanner this /* r1 => r0, fp-0x8 */)
    //     0x7056c4: mov             x0, x1
    //     0x7056c8: stur            x1, [fp, #-8]
    // 0x7056cc: CheckStackOverflow
    //     0x7056cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7056d0: cmp             SP, x16
    //     0x7056d4: b.ls            #0x705714
    // 0x7056d8: mov             x1, x0
    // 0x7056dc: r0 = isDone()
    //     0x7056dc: bl              #0x70571c  ; [package:string_scanner/src/string_scanner.dart] StringScanner::isDone
    // 0x7056e0: tbnz            w0, #4, #0x7056f4
    // 0x7056e4: r0 = Null
    //     0x7056e4: mov             x0, NULL
    // 0x7056e8: LeaveFrame
    //     0x7056e8: mov             SP, fp
    //     0x7056ec: ldp             fp, lr, [SP], #0x10
    // 0x7056f0: ret
    //     0x7056f0: ret             
    // 0x7056f4: ldur            x1, [fp, #-8]
    // 0x7056f8: r2 = "no more input"
    //     0x7056f8: add             x2, PP, #0x10, lsl #12  ; [pp+0x10898] "no more input"
    //     0x7056fc: ldr             x2, [x2, #0x898]
    // 0x705700: r0 = _fail()
    //     0x705700: bl              #0x705964  ; [package:string_scanner/src/string_scanner.dart] StringScanner::_fail
    // 0x705704: r0 = Null
    //     0x705704: mov             x0, NULL
    // 0x705708: LeaveFrame
    //     0x705708: mov             SP, fp
    //     0x70570c: ldp             fp, lr, [SP], #0x10
    // 0x705710: ret
    //     0x705710: ret             
    // 0x705714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x705714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x705718: b               #0x7056d8
  }
  get _ isDone(/* No info */) {
    // ** addr: 0x70571c, size: 0x28
    // 0x70571c: LoadField: r2 = r1->field_f
    //     0x70571c: ldur            x2, [x1, #0xf]
    // 0x705720: LoadField: r3 = r1->field_b
    //     0x705720: ldur            w3, [x1, #0xb]
    // 0x705724: DecompressPointer r3
    //     0x705724: add             x3, x3, HEAP, lsl #32
    // 0x705728: LoadField: r1 = r3->field_7
    //     0x705728: ldur            w1, [x3, #7]
    // 0x70572c: r3 = LoadInt32Instr(r1)
    //     0x70572c: sbfx            x3, x1, #1, #0x1f
    // 0x705730: cmp             x2, x3
    // 0x705734: r16 = true
    //     0x705734: add             x16, NULL, #0x20  ; true
    // 0x705738: r17 = false
    //     0x705738: add             x17, NULL, #0x30  ; false
    // 0x70573c: csel            x0, x16, x17, eq
    // 0x705740: ret
    //     0x705740: ret             
  }
  _ _fail(/* No info */) {
    // ** addr: 0x705964, size: 0x84
    // 0x705964: EnterFrame
    //     0x705964: stp             fp, lr, [SP, #-0x10]!
    //     0x705968: mov             fp, SP
    // 0x70596c: AllocStack(0x18)
    //     0x70596c: sub             SP, SP, #0x18
    // 0x705970: SetupParameters(StringScanner this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x705970: mov             x3, x1
    //     0x705974: mov             x0, x2
    //     0x705978: stur            x1, [fp, #-8]
    //     0x70597c: stur            x2, [fp, #-0x10]
    // 0x705980: CheckStackOverflow
    //     0x705980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x705984: cmp             SP, x16
    //     0x705988: b.ls            #0x7059e0
    // 0x70598c: r1 = Null
    //     0x70598c: mov             x1, NULL
    // 0x705990: r2 = 6
    //     0x705990: movz            x2, #0x6
    // 0x705994: r0 = AllocateArray()
    //     0x705994: bl              #0xec22fc  ; AllocateArrayStub
    // 0x705998: r16 = "expected "
    //     0x705998: add             x16, PP, #0x10, lsl #12  ; [pp+0x10918] "expected "
    //     0x70599c: ldr             x16, [x16, #0x918]
    // 0x7059a0: StoreField: r0->field_f = r16
    //     0x7059a0: stur            w16, [x0, #0xf]
    // 0x7059a4: ldur            x1, [fp, #-0x10]
    // 0x7059a8: StoreField: r0->field_13 = r1
    //     0x7059a8: stur            w1, [x0, #0x13]
    // 0x7059ac: r16 = "."
    //     0x7059ac: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x7059b0: ArrayStore: r0[0] = r16  ; List_4
    //     0x7059b0: stur            w16, [x0, #0x17]
    // 0x7059b4: str             x0, [SP]
    // 0x7059b8: r0 = _interpolate()
    //     0x7059b8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7059bc: ldur            x1, [fp, #-8]
    // 0x7059c0: LoadField: r5 = r1->field_f
    //     0x7059c0: ldur            x5, [x1, #0xf]
    // 0x7059c4: mov             x2, x0
    // 0x7059c8: r3 = 0
    //     0x7059c8: movz            x3, #0
    // 0x7059cc: r0 = error()
    //     0x7059cc: bl              #0x7059e8  ; [package:string_scanner/src/string_scanner.dart] StringScanner::error
    // 0x7059d0: r0 = Null
    //     0x7059d0: mov             x0, NULL
    // 0x7059d4: LeaveFrame
    //     0x7059d4: mov             SP, fp
    //     0x7059d8: ldp             fp, lr, [SP], #0x10
    // 0x7059dc: ret
    //     0x7059dc: ret             
    // 0x7059e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7059e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7059e4: b               #0x70598c
  }
  _ error(/* No info */) {
    // ** addr: 0x7059e8, size: 0xb4
    // 0x7059e8: EnterFrame
    //     0x7059e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7059ec: mov             fp, SP
    // 0x7059f0: AllocStack(0x20)
    //     0x7059f0: sub             SP, SP, #0x20
    // 0x7059f4: SetupParameters(dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r5 => r0, fp-0x18 */)
    //     0x7059f4: mov             x4, x2
    //     0x7059f8: mov             x0, x5
    //     0x7059fc: stur            x2, [fp, #-0x10]
    //     0x705a00: stur            x5, [fp, #-0x18]
    // 0x705a04: CheckStackOverflow
    //     0x705a04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x705a08: cmp             SP, x16
    //     0x705a0c: b.ls            #0x705a94
    // 0x705a10: LoadField: r3 = r1->field_b
    //     0x705a10: ldur            w3, [x1, #0xb]
    // 0x705a14: DecompressPointer r3
    //     0x705a14: add             x3, x3, HEAP, lsl #32
    // 0x705a18: mov             x1, x3
    // 0x705a1c: mov             x2, x0
    // 0x705a20: stur            x3, [fp, #-8]
    // 0x705a24: r0 = validateErrorArgs()
    //     0x705a24: bl              #0x705f7c  ; [package:string_scanner/src/utils.dart] ::validateErrorArgs
    // 0x705a28: r1 = <int>
    //     0x705a28: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x705a2c: r0 = CodeUnits()
    //     0x705a2c: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0x705a30: mov             x1, x0
    // 0x705a34: ldur            x0, [fp, #-8]
    // 0x705a38: stur            x1, [fp, #-0x20]
    // 0x705a3c: StoreField: r1->field_b = r0
    //     0x705a3c: stur            w0, [x1, #0xb]
    // 0x705a40: r0 = SourceFile()
    //     0x705a40: bl              #0x705f64  ; AllocateSourceFileStub -> SourceFile (size=0x18)
    // 0x705a44: mov             x1, x0
    // 0x705a48: ldur            x2, [fp, #-0x20]
    // 0x705a4c: stur            x0, [fp, #-0x20]
    // 0x705a50: r0 = SourceFile.decoded()
    //     0x705a50: bl              #0x705d44  ; [package:source_span/src/file.dart] SourceFile::SourceFile.decoded
    // 0x705a54: ldur            x1, [fp, #-0x20]
    // 0x705a58: ldur            x2, [fp, #-0x18]
    // 0x705a5c: ldur            x3, [fp, #-0x18]
    // 0x705a60: r0 = span()
    //     0x705a60: bl              #0x705aa8  ; [package:source_span/src/file.dart] SourceFile::span
    // 0x705a64: stur            x0, [fp, #-0x20]
    // 0x705a68: r0 = StringScannerException()
    //     0x705a68: bl              #0x705a9c  ; AllocateStringScannerExceptionStub -> StringScannerException (size=0x14)
    // 0x705a6c: mov             x1, x0
    // 0x705a70: ldur            x0, [fp, #-8]
    // 0x705a74: StoreField: r1->field_f = r0
    //     0x705a74: stur            w0, [x1, #0xf]
    // 0x705a78: ldur            x0, [fp, #-0x10]
    // 0x705a7c: StoreField: r1->field_7 = r0
    //     0x705a7c: stur            w0, [x1, #7]
    // 0x705a80: ldur            x0, [fp, #-0x20]
    // 0x705a84: StoreField: r1->field_b = r0
    //     0x705a84: stur            w0, [x1, #0xb]
    // 0x705a88: mov             x0, x1
    // 0x705a8c: r0 = Throw()
    //     0x705a8c: bl              #0xec04b8  ; ThrowStub
    // 0x705a90: brk             #0
    // 0x705a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x705a94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x705a98: b               #0x705a10
  }
  _ matches(/* No info */) {
    // ** addr: 0x706080, size: 0xe8
    // 0x706080: EnterFrame
    //     0x706080: stp             fp, lr, [SP, #-0x10]!
    //     0x706084: mov             fp, SP
    // 0x706088: AllocStack(0x10)
    //     0x706088: sub             SP, SP, #0x10
    // 0x70608c: SetupParameters(StringScanner this /* r1 => r3, fp-0x8 */)
    //     0x70608c: mov             x3, x1
    //     0x706090: stur            x1, [fp, #-8]
    // 0x706094: CheckStackOverflow
    //     0x706094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x706098: cmp             SP, x16
    //     0x70609c: b.ls            #0x706160
    // 0x7060a0: LoadField: r4 = r3->field_b
    //     0x7060a0: ldur            w4, [x3, #0xb]
    // 0x7060a4: DecompressPointer r4
    //     0x7060a4: add             x4, x4, HEAP, lsl #32
    // 0x7060a8: LoadField: r5 = r3->field_f
    //     0x7060a8: ldur            x5, [x3, #0xf]
    // 0x7060ac: r0 = BoxInt64Instr(r5)
    //     0x7060ac: sbfiz           x0, x5, #1, #0x1f
    //     0x7060b0: cmp             x5, x0, asr #1
    //     0x7060b4: b.eq            #0x7060c0
    //     0x7060b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7060bc: stur            x5, [x0, #7]
    // 0x7060c0: r1 = LoadClassIdInstr(r2)
    //     0x7060c0: ldur            x1, [x2, #-1]
    //     0x7060c4: ubfx            x1, x1, #0xc, #0x14
    // 0x7060c8: str             x0, [SP]
    // 0x7060cc: mov             x0, x1
    // 0x7060d0: mov             x1, x2
    // 0x7060d4: mov             x2, x4
    // 0x7060d8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x7060d8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x7060dc: r0 = GDT[cid_x0 + -0xfd4]()
    //     0x7060dc: sub             lr, x0, #0xfd4
    //     0x7060e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7060e4: blr             lr
    // 0x7060e8: mov             x3, x0
    // 0x7060ec: ldur            x2, [fp, #-8]
    // 0x7060f0: ArrayStore: r2[0] = r0  ; List_4
    //     0x7060f0: stur            w0, [x2, #0x17]
    //     0x7060f4: ldurb           w16, [x2, #-1]
    //     0x7060f8: ldurb           w17, [x0, #-1]
    //     0x7060fc: and             x16, x17, x16, lsr #2
    //     0x706100: tst             x16, HEAP, lsr #32
    //     0x706104: b.eq            #0x70610c
    //     0x706108: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x70610c: LoadField: r4 = r2->field_f
    //     0x70610c: ldur            x4, [x2, #0xf]
    // 0x706110: r0 = BoxInt64Instr(r4)
    //     0x706110: sbfiz           x0, x4, #1, #0x1f
    //     0x706114: cmp             x4, x0, asr #1
    //     0x706118: b.eq            #0x706124
    //     0x70611c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x706120: stur            x4, [x0, #7]
    // 0x706124: StoreField: r2->field_1b = r0
    //     0x706124: stur            w0, [x2, #0x1b]
    //     0x706128: tbz             w0, #0, #0x706144
    //     0x70612c: ldurb           w16, [x2, #-1]
    //     0x706130: ldurb           w17, [x0, #-1]
    //     0x706134: and             x16, x17, x16, lsr #2
    //     0x706138: tst             x16, HEAP, lsr #32
    //     0x70613c: b.eq            #0x706144
    //     0x706140: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x706144: cmp             w3, NULL
    // 0x706148: r16 = true
    //     0x706148: add             x16, NULL, #0x20  ; true
    // 0x70614c: r17 = false
    //     0x70614c: add             x17, NULL, #0x30  ; false
    // 0x706150: csel            x0, x16, x17, ne
    // 0x706154: LeaveFrame
    //     0x706154: mov             SP, fp
    //     0x706158: ldp             fp, lr, [SP], #0x10
    // 0x70615c: ret
    //     0x70615c: ret             
    // 0x706160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x706160: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x706164: b               #0x7060a0
  }
  get _ lastMatch(/* No info */) {
    // ** addr: 0x706168, size: 0x84
    // 0x706168: mov             x2, x1
    // 0x70616c: LoadField: r3 = r2->field_f
    //     0x70616c: ldur            x3, [x2, #0xf]
    // 0x706170: LoadField: r4 = r2->field_1b
    //     0x706170: ldur            w4, [x2, #0x1b]
    // 0x706174: DecompressPointer r4
    //     0x706174: add             x4, x4, HEAP, lsl #32
    // 0x706178: r0 = BoxInt64Instr(r3)
    //     0x706178: sbfiz           x0, x3, #1, #0x1f
    //     0x70617c: cmp             x3, x0, asr #1
    //     0x706180: b.eq            #0x70619c
    //     0x706184: stp             fp, lr, [SP, #-0x10]!
    //     0x706188: mov             fp, SP
    //     0x70618c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x706190: mov             SP, fp
    //     0x706194: ldp             fp, lr, [SP], #0x10
    //     0x706198: stur            x3, [x0, #7]
    // 0x70619c: cmp             w0, w4
    // 0x7061a0: b.eq            #0x7061e0
    // 0x7061a4: and             w16, w0, w4
    // 0x7061a8: branchIfSmi(r16, 0x7061dc)
    //     0x7061a8: tbz             w16, #0, #0x7061dc
    // 0x7061ac: r16 = LoadClassIdInstr(r0)
    //     0x7061ac: ldur            x16, [x0, #-1]
    //     0x7061b0: ubfx            x16, x16, #0xc, #0x14
    // 0x7061b4: cmp             x16, #0x3d
    // 0x7061b8: b.ne            #0x7061dc
    // 0x7061bc: r16 = LoadClassIdInstr(r4)
    //     0x7061bc: ldur            x16, [x4, #-1]
    //     0x7061c0: ubfx            x16, x16, #0xc, #0x14
    // 0x7061c4: cmp             x16, #0x3d
    // 0x7061c8: b.ne            #0x7061dc
    // 0x7061cc: LoadField: r16 = r0->field_7
    //     0x7061cc: ldur            x16, [x0, #7]
    // 0x7061d0: LoadField: r17 = r4->field_7
    //     0x7061d0: ldur            x17, [x4, #7]
    // 0x7061d4: cmp             x16, x17
    // 0x7061d8: b.eq            #0x7061e0
    // 0x7061dc: ArrayStore: r2[0] = rNULL  ; List_4
    //     0x7061dc: stur            NULL, [x2, #0x17]
    // 0x7061e0: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x7061e0: ldur            w0, [x2, #0x17]
    // 0x7061e4: DecompressPointer r0
    //     0x7061e4: add             x0, x0, HEAP, lsl #32
    // 0x7061e8: ret
    //     0x7061e8: ret             
  }
  _ expect(/* No info */) {
    // ** addr: 0x7061ec, size: 0x188
    // 0x7061ec: EnterFrame
    //     0x7061ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7061f0: mov             fp, SP
    // 0x7061f4: AllocStack(0x28)
    //     0x7061f4: sub             SP, SP, #0x28
    // 0x7061f8: SetupParameters(StringScanner this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, {dynamic name = Null /* r4, fp-0x8 */})
    //     0x7061f8: mov             x3, x1
    //     0x7061fc: mov             x0, x2
    //     0x706200: stur            x1, [fp, #-0x10]
    //     0x706204: stur            x2, [fp, #-0x18]
    //     0x706208: ldur            w1, [x4, #0x13]
    //     0x70620c: ldur            w2, [x4, #0x1f]
    //     0x706210: add             x2, x2, HEAP, lsl #32
    //     0x706214: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "name"
    //     0x706218: cmp             w2, w16
    //     0x70621c: b.ne            #0x70623c
    //     0x706220: ldur            w2, [x4, #0x23]
    //     0x706224: add             x2, x2, HEAP, lsl #32
    //     0x706228: sub             w4, w1, w2
    //     0x70622c: add             x1, fp, w4, sxtw #2
    //     0x706230: ldr             x1, [x1, #8]
    //     0x706234: mov             x4, x1
    //     0x706238: b               #0x706240
    //     0x70623c: mov             x4, NULL
    //     0x706240: stur            x4, [fp, #-8]
    // 0x706244: CheckStackOverflow
    //     0x706244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x706248: cmp             SP, x16
    //     0x70624c: b.ls            #0x70636c
    // 0x706250: mov             x1, x3
    // 0x706254: mov             x2, x0
    // 0x706258: r0 = scan()
    //     0x706258: bl              #0x706374  ; [package:string_scanner/src/string_scanner.dart] StringScanner::scan
    // 0x70625c: tbnz            w0, #4, #0x706270
    // 0x706260: r0 = Null
    //     0x706260: mov             x0, NULL
    // 0x706264: LeaveFrame
    //     0x706264: mov             SP, fp
    //     0x706268: ldp             fp, lr, [SP], #0x10
    // 0x70626c: ret
    //     0x70626c: ret             
    // 0x706270: ldur            x0, [fp, #-8]
    // 0x706274: cmp             w0, NULL
    // 0x706278: b.ne            #0x706350
    // 0x70627c: ldur            x0, [fp, #-0x18]
    // 0x706280: r1 = LoadClassIdInstr(r0)
    //     0x706280: ldur            x1, [x0, #-1]
    //     0x706284: ubfx            x1, x1, #0xc, #0x14
    // 0x706288: cmp             x1, #0x4f
    // 0x70628c: b.ne            #0x7062cc
    // 0x706290: str             x0, [SP]
    // 0x706294: r0 = pattern()
    //     0x706294: bl              #0x705ffc  ; [dart:core] _RegExp::pattern
    // 0x706298: r1 = Null
    //     0x706298: mov             x1, NULL
    // 0x70629c: r2 = 6
    //     0x70629c: movz            x2, #0x6
    // 0x7062a0: stur            x0, [fp, #-0x20]
    // 0x7062a4: r0 = AllocateArray()
    //     0x7062a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7062a8: r16 = "/"
    //     0x7062a8: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7062ac: StoreField: r0->field_f = r16
    //     0x7062ac: stur            w16, [x0, #0xf]
    // 0x7062b0: ldur            x1, [fp, #-0x20]
    // 0x7062b4: StoreField: r0->field_13 = r1
    //     0x7062b4: stur            w1, [x0, #0x13]
    // 0x7062b8: r16 = "/"
    //     0x7062b8: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0x7062bc: ArrayStore: r0[0] = r16  ; List_4
    //     0x7062bc: stur            w16, [x0, #0x17]
    // 0x7062c0: str             x0, [SP]
    // 0x7062c4: r0 = _interpolate()
    //     0x7062c4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x7062c8: b               #0x706348
    // 0x7062cc: r1 = LoadClassIdInstr(r0)
    //     0x7062cc: ldur            x1, [x0, #-1]
    //     0x7062d0: ubfx            x1, x1, #0xc, #0x14
    // 0x7062d4: str             x0, [SP]
    // 0x7062d8: mov             x0, x1
    // 0x7062dc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x7062dc: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x7062e0: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x7062e0: movz            x17, #0x2b03
    //     0x7062e4: add             lr, x0, x17
    //     0x7062e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7062ec: blr             lr
    // 0x7062f0: mov             x1, x0
    // 0x7062f4: r2 = "\\"
    //     0x7062f4: ldr             x2, [PP, #0xc28]  ; [pp+0xc28] "\\"
    // 0x7062f8: r3 = "\\\\"
    //     0x7062f8: add             x3, PP, #0x10, lsl #12  ; [pp+0x10800] "\\\\"
    //     0x7062fc: ldr             x3, [x3, #0x800]
    // 0x706300: r0 = replaceAll()
    //     0x706300: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0x706304: mov             x1, x0
    // 0x706308: r2 = "\""
    //     0x706308: ldr             x2, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x70630c: r3 = "\\\""
    //     0x70630c: add             x3, PP, #0x10, lsl #12  ; [pp+0x10808] "\\\""
    //     0x706310: ldr             x3, [x3, #0x808]
    // 0x706314: r0 = replaceAll()
    //     0x706314: bl              #0x6097d0  ; [dart:core] _StringBase::replaceAll
    // 0x706318: r1 = Null
    //     0x706318: mov             x1, NULL
    // 0x70631c: r2 = 6
    //     0x70631c: movz            x2, #0x6
    // 0x706320: stur            x0, [fp, #-0x18]
    // 0x706324: r0 = AllocateArray()
    //     0x706324: bl              #0xec22fc  ; AllocateArrayStub
    // 0x706328: r16 = "\""
    //     0x706328: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x70632c: StoreField: r0->field_f = r16
    //     0x70632c: stur            w16, [x0, #0xf]
    // 0x706330: ldur            x1, [fp, #-0x18]
    // 0x706334: StoreField: r0->field_13 = r1
    //     0x706334: stur            w1, [x0, #0x13]
    // 0x706338: r16 = "\""
    //     0x706338: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x70633c: ArrayStore: r0[0] = r16  ; List_4
    //     0x70633c: stur            w16, [x0, #0x17]
    // 0x706340: str             x0, [SP]
    // 0x706344: r0 = _interpolate()
    //     0x706344: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x706348: mov             x2, x0
    // 0x70634c: b               #0x706354
    // 0x706350: mov             x2, x0
    // 0x706354: ldur            x1, [fp, #-0x10]
    // 0x706358: r0 = _fail()
    //     0x706358: bl              #0x705964  ; [package:string_scanner/src/string_scanner.dart] StringScanner::_fail
    // 0x70635c: r0 = Null
    //     0x70635c: mov             x0, NULL
    // 0x706360: LeaveFrame
    //     0x706360: mov             SP, fp
    //     0x706364: ldp             fp, lr, [SP], #0x10
    // 0x706368: ret
    //     0x706368: ret             
    // 0x70636c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x70636c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x706370: b               #0x706250
  }
  _ scan(/* No info */) {
    // ** addr: 0x706374, size: 0xb8
    // 0x706374: EnterFrame
    //     0x706374: stp             fp, lr, [SP, #-0x10]!
    //     0x706378: mov             fp, SP
    // 0x70637c: AllocStack(0x10)
    //     0x70637c: sub             SP, SP, #0x10
    // 0x706380: SetupParameters(StringScanner this /* r1 => r0, fp-0x8 */)
    //     0x706380: mov             x0, x1
    //     0x706384: stur            x1, [fp, #-8]
    // 0x706388: CheckStackOverflow
    //     0x706388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70638c: cmp             SP, x16
    //     0x706390: b.ls            #0x706420
    // 0x706394: mov             x1, x0
    // 0x706398: r0 = matches()
    //     0x706398: bl              #0x706080  ; [package:string_scanner/src/string_scanner.dart] StringScanner::matches
    // 0x70639c: mov             x2, x0
    // 0x7063a0: stur            x2, [fp, #-0x10]
    // 0x7063a4: tbnz            w2, #4, #0x706410
    // 0x7063a8: ldur            x3, [fp, #-8]
    // 0x7063ac: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x7063ac: ldur            w1, [x3, #0x17]
    // 0x7063b0: DecompressPointer r1
    //     0x7063b0: add             x1, x1, HEAP, lsl #32
    // 0x7063b4: cmp             w1, NULL
    // 0x7063b8: b.eq            #0x706428
    // 0x7063bc: r0 = LoadClassIdInstr(r1)
    //     0x7063bc: ldur            x0, [x1, #-1]
    //     0x7063c0: ubfx            x0, x0, #0xc, #0x14
    // 0x7063c4: r0 = GDT[cid_x0 + -0xfc2]()
    //     0x7063c4: sub             lr, x0, #0xfc2
    //     0x7063c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7063cc: blr             lr
    // 0x7063d0: mov             x3, x0
    // 0x7063d4: ldur            x2, [fp, #-8]
    // 0x7063d8: StoreField: r2->field_f = r3
    //     0x7063d8: stur            x3, [x2, #0xf]
    // 0x7063dc: r0 = BoxInt64Instr(r3)
    //     0x7063dc: sbfiz           x0, x3, #1, #0x1f
    //     0x7063e0: cmp             x3, x0, asr #1
    //     0x7063e4: b.eq            #0x7063f0
    //     0x7063e8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x7063ec: stur            x3, [x0, #7]
    // 0x7063f0: StoreField: r2->field_1b = r0
    //     0x7063f0: stur            w0, [x2, #0x1b]
    //     0x7063f4: tbz             w0, #0, #0x706410
    //     0x7063f8: ldurb           w16, [x2, #-1]
    //     0x7063fc: ldurb           w17, [x0, #-1]
    //     0x706400: and             x16, x17, x16, lsr #2
    //     0x706404: tst             x16, HEAP, lsr #32
    //     0x706408: b.eq            #0x706410
    //     0x70640c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x706410: ldur            x0, [fp, #-0x10]
    // 0x706414: LeaveFrame
    //     0x706414: mov             SP, fp
    //     0x706418: ldp             fp, lr, [SP], #0x10
    // 0x70641c: ret
    //     0x70641c: ret             
    // 0x706420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x706420: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x706424: b               #0x706394
    // 0x706428: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x706428: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
