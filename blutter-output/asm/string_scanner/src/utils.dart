// lib: , url: package:string_scanner/src/utils.dart

// class id: 1051176, size: 0x8
class :: {

  static _ validateErrorArgs(/* No info */) {
    // ** addr: 0x705f7c, size: 0x80
    // 0x705f7c: EnterFrame
    //     0x705f7c: stp             fp, lr, [SP, #-0x10]!
    //     0x705f80: mov             fp, SP
    // 0x705f84: tbnz            x2, #0x3f, #0x705fa8
    // 0x705f88: LoadField: r0 = r1->field_7
    //     0x705f88: ldur            w0, [x1, #7]
    // 0x705f8c: r1 = LoadInt32Instr(r0)
    //     0x705f8c: sbfx            x1, x0, #1, #0x1f
    // 0x705f90: cmp             x2, x1
    // 0x705f94: b.gt            #0x705fd0
    // 0x705f98: r0 = Null
    //     0x705f98: mov             x0, NULL
    // 0x705f9c: LeaveFrame
    //     0x705f9c: mov             SP, fp
    //     0x705fa0: ldp             fp, lr, [SP], #0x10
    // 0x705fa4: ret
    //     0x705fa4: ret             
    // 0x705fa8: r0 = RangeError()
    //     0x705fa8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x705fac: mov             x1, x0
    // 0x705fb0: r0 = "position must be greater than or equal to 0."
    //     0x705fb0: add             x0, PP, #0x10, lsl #12  ; [pp+0x10988] "position must be greater than or equal to 0."
    //     0x705fb4: ldr             x0, [x0, #0x988]
    // 0x705fb8: ArrayStore: r1[0] = r0  ; List_4
    //     0x705fb8: stur            w0, [x1, #0x17]
    // 0x705fbc: r0 = false
    //     0x705fbc: add             x0, NULL, #0x30  ; false
    // 0x705fc0: StoreField: r1->field_b = r0
    //     0x705fc0: stur            w0, [x1, #0xb]
    // 0x705fc4: mov             x0, x1
    // 0x705fc8: r0 = Throw()
    //     0x705fc8: bl              #0xec04b8  ; ThrowStub
    // 0x705fcc: brk             #0
    // 0x705fd0: r0 = false
    //     0x705fd0: add             x0, NULL, #0x30  ; false
    // 0x705fd4: r0 = RangeError()
    //     0x705fd4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x705fd8: mov             x1, x0
    // 0x705fdc: r0 = "position must be less than or equal to the string length."
    //     0x705fdc: add             x0, PP, #0x10, lsl #12  ; [pp+0x10990] "position must be less than or equal to the string length."
    //     0x705fe0: ldr             x0, [x0, #0x990]
    // 0x705fe4: ArrayStore: r1[0] = r0  ; List_4
    //     0x705fe4: stur            w0, [x1, #0x17]
    // 0x705fe8: r0 = false
    //     0x705fe8: add             x0, NULL, #0x30  ; false
    // 0x705fec: StoreField: r1->field_b = r0
    //     0x705fec: stur            w0, [x1, #0xb]
    // 0x705ff0: mov             x0, x1
    // 0x705ff4: r0 = Throw()
    //     0x705ff4: bl              #0xec04b8  ; ThrowStub
    // 0x705ff8: brk             #0
  }
}
