// lib: substring_highlight, url: package:substring_highlight/substring_highlight.dart

// class id: 1051177, size: 0x8
class :: {

  static late final int __int64MaxValue; // offset: 0x15e0

  static int __int64MaxValue() {
    // ** addr: 0xbba650, size: 0x54
    // 0xbba650: EnterFrame
    //     0xbba650: stp             fp, lr, [SP, #-0x10]!
    //     0xbba654: mov             fp, SP
    // 0xbba658: d0 = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368.000000
    //     0xbba658: add             x17, PP, #0x26, lsl #12  ; [pp+0x26270] IMM: double(1.7976931348623157e+308) from 0x7fefffffffffffff
    //     0xbba65c: ldr             d0, [x17, #0x270]
    // 0xbba660: fcmp            d0, d0
    // 0xbba664: b.vs            #0xbba688
    // 0xbba668: fcvtzs          x0, d0
    // 0xbba66c: asr             x16, x0, #0x1e
    // 0xbba670: cmp             x16, x0, asr #63
    // 0xbba674: b.ne            #0xbba688
    // 0xbba678: lsl             x0, x0, #1
    // 0xbba67c: LeaveFrame
    //     0xbba67c: mov             SP, fp
    //     0xbba680: ldp             fp, lr, [SP], #0x10
    // 0xbba684: ret
    //     0xbba684: ret             
    // 0xbba688: SaveReg d0
    //     0xbba688: str             q0, [SP, #-0x10]!
    // 0xbba68c: r0 = 74
    //     0xbba68c: movz            x0, #0x4a
    // 0xbba690: r30 = DoubleToIntegerStub
    //     0xbba690: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xbba694: LoadField: r30 = r30->field_7
    //     0xbba694: ldur            lr, [lr, #7]
    // 0xbba698: blr             lr
    // 0xbba69c: RestoreReg d0
    //     0xbba69c: ldr             q0, [SP], #0x10
    // 0xbba6a0: b               #0xbba67c
  }
}

// class id: 4913, size: 0x34, field offset: 0xc
class SubstringHighlight extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbb9ea8, size: 0x7a8
    // 0xbb9ea8: EnterFrame
    //     0xbb9ea8: stp             fp, lr, [SP, #-0x10]!
    //     0xbb9eac: mov             fp, SP
    // 0xbb9eb0: AllocStack(0xb0)
    //     0xbb9eb0: sub             SP, SP, #0xb0
    // 0xbb9eb4: SetupParameters(SubstringHighlight this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbb9eb4: mov             x0, x1
    //     0xbb9eb8: stur            x1, [fp, #-8]
    //     0xbb9ebc: mov             x1, x2
    //     0xbb9ec0: stur            x2, [fp, #-0x10]
    // 0xbb9ec4: CheckStackOverflow
    //     0xbb9ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb9ec8: cmp             SP, x16
    //     0xbb9ecc: b.ls            #0xbba634
    // 0xbb9ed0: r1 = 3
    //     0xbb9ed0: movz            x1, #0x3
    // 0xbb9ed4: r0 = AllocateContext()
    //     0xbb9ed4: bl              #0xec126c  ; AllocateContextStub
    // 0xbb9ed8: mov             x2, x0
    // 0xbb9edc: ldur            x1, [fp, #-8]
    // 0xbb9ee0: stur            x2, [fp, #-0x20]
    // 0xbb9ee4: StoreField: r2->field_f = r1
    //     0xbb9ee4: stur            w1, [x2, #0xf]
    // 0xbb9ee8: LoadField: r3 = r1->field_1f
    //     0xbb9ee8: ldur            w3, [x1, #0x1f]
    // 0xbb9eec: DecompressPointer r3
    //     0xbb9eec: add             x3, x3, HEAP, lsl #32
    // 0xbb9ef0: stur            x3, [fp, #-0x18]
    // 0xbb9ef4: r0 = LoadClassIdInstr(r3)
    //     0xbb9ef4: ldur            x0, [x3, #-1]
    //     0xbb9ef8: ubfx            x0, x0, #0xc, #0x14
    // 0xbb9efc: str             x3, [SP]
    // 0xbb9f00: r0 = GDT[cid_x0 + -0xffe]()
    //     0xbb9f00: sub             lr, x0, #0xffe
    //     0xbb9f04: ldr             lr, [x21, lr, lsl #3]
    //     0xbb9f08: blr             lr
    // 0xbb9f0c: r1 = Null
    //     0xbb9f0c: mov             x1, NULL
    // 0xbb9f10: r2 = 2
    //     0xbb9f10: movz            x2, #0x2
    // 0xbb9f14: stur            x0, [fp, #-0x28]
    // 0xbb9f18: r0 = AllocateArray()
    //     0xbb9f18: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb9f1c: stur            x0, [fp, #-0x30]
    // 0xbb9f20: r16 = ""
    //     0xbb9f20: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0xbb9f24: StoreField: r0->field_f = r16
    //     0xbb9f24: stur            w16, [x0, #0xf]
    // 0xbb9f28: r1 = <String>
    //     0xbb9f28: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbb9f2c: r0 = AllocateGrowableArray()
    //     0xbb9f2c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb9f30: mov             x3, x0
    // 0xbb9f34: ldur            x0, [fp, #-0x30]
    // 0xbb9f38: stur            x3, [fp, #-0x38]
    // 0xbb9f3c: StoreField: r3->field_f = r0
    //     0xbb9f3c: stur            w0, [x3, #0xf]
    // 0xbb9f40: r0 = 2
    //     0xbb9f40: movz            x0, #0x2
    // 0xbb9f44: StoreField: r3->field_b = r0
    //     0xbb9f44: stur            w0, [x3, #0xb]
    // 0xbb9f48: ldur            x0, [fp, #-8]
    // 0xbb9f4c: LoadField: r2 = r0->field_1b
    //     0xbb9f4c: ldur            w2, [x0, #0x1b]
    // 0xbb9f50: DecompressPointer r2
    //     0xbb9f50: add             x2, x2, HEAP, lsl #32
    // 0xbb9f54: mov             x1, x3
    // 0xbb9f58: r0 = addAll()
    //     0xbb9f58: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xbb9f5c: r1 = Function '<anonymous closure>':.
    //     0xbb9f5c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35bf8] AnonymousClosure: static (0x6446b8), in [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackString (0x643988)
    //     0xbb9f60: ldr             x1, [x1, #0xbf8]
    // 0xbb9f64: r2 = Null
    //     0xbb9f64: mov             x2, NULL
    // 0xbb9f68: r0 = AllocateClosure()
    //     0xbb9f68: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb9f6c: ldur            x1, [fp, #-0x38]
    // 0xbb9f70: mov             x2, x0
    // 0xbb9f74: r0 = where()
    //     0xbb9f74: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xbb9f78: ldur            x2, [fp, #-0x20]
    // 0xbb9f7c: r1 = Function '<anonymous closure>':.
    //     0xbb9f7c: add             x1, PP, #0x35, lsl #12  ; [pp+0x35c00] AnonymousClosure: (0xbba6a4), in [package:substring_highlight/substring_highlight.dart] SubstringHighlight::build (0xbb9ea8)
    //     0xbb9f80: ldr             x1, [x1, #0xc00]
    // 0xbb9f84: stur            x0, [fp, #-0x30]
    // 0xbb9f88: r0 = AllocateClosure()
    //     0xbb9f88: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb9f8c: r16 = <String>
    //     0xbb9f8c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xbb9f90: ldur            lr, [fp, #-0x30]
    // 0xbb9f94: stp             lr, x16, [SP, #8]
    // 0xbb9f98: str             x0, [SP]
    // 0xbb9f9c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbb9f9c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbb9fa0: r0 = map()
    //     0xbb9fa0: bl              #0x7abfa0  ; [dart:_internal] WhereIterable::map
    // 0xbb9fa4: mov             x1, x0
    // 0xbb9fa8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb9fa8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb9fac: r0 = toList()
    //     0xbb9fac: bl              #0xa532a8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::toList
    // 0xbb9fb0: r1 = <InlineSpan>
    //     0xbb9fb0: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b5f0] TypeArguments: <InlineSpan>
    //     0xbb9fb4: ldr             x1, [x1, #0x5f0]
    // 0xbb9fb8: r2 = 0
    //     0xbb9fb8: movz            x2, #0
    // 0xbb9fbc: stur            x0, [fp, #-0x30]
    // 0xbb9fc0: r0 = _GrowableList()
    //     0xbb9fc0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbb9fc4: ldur            x1, [fp, #-0x20]
    // 0xbb9fc8: StoreField: r1->field_13 = r0
    //     0xbb9fc8: stur            w0, [x1, #0x13]
    //     0xbb9fcc: ldurb           w16, [x1, #-1]
    //     0xbb9fd0: ldurb           w17, [x0, #-1]
    //     0xbb9fd4: and             x16, x17, x16, lsr #2
    //     0xbb9fd8: tst             x16, HEAP, lsr #32
    //     0xbb9fdc: b.eq            #0xbb9fe4
    //     0xbb9fe0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbb9fe4: ArrayStore: r1[0] = rZR  ; List_4
    //     0xbb9fe4: stur            wzr, [x1, #0x17]
    // 0xbb9fe8: ldur            x0, [fp, #-0x28]
    // 0xbb9fec: LoadField: r2 = r0->field_7
    //     0xbb9fec: ldur            w2, [x0, #7]
    // 0xbb9ff0: stur            x2, [fp, #-0x58]
    // 0xbb9ff4: r3 = LoadInt32Instr(r2)
    //     0xbb9ff4: sbfx            x3, x2, #1, #0x1f
    // 0xbb9ff8: ldur            x4, [fp, #-0x18]
    // 0xbb9ffc: stur            x3, [fp, #-0x50]
    // 0xbba000: LoadField: r5 = r4->field_7
    //     0xbba000: ldur            w5, [x4, #7]
    // 0xbba004: r6 = LoadInt32Instr(r5)
    //     0xbba004: sbfx            x6, x5, #1, #0x1f
    // 0xbba008: ldur            x5, [fp, #-8]
    // 0xbba00c: stur            x6, [fp, #-0x48]
    // 0xbba010: LoadField: r7 = r5->field_2b
    //     0xbba010: ldur            w7, [x5, #0x2b]
    // 0xbba014: DecompressPointer r7
    //     0xbba014: add             x7, x7, HEAP, lsl #32
    // 0xbba018: stur            x7, [fp, #-0x38]
    // 0xbba01c: r9 = 0
    //     0xbba01c: movz            x9, #0
    // 0xbba020: ldur            x8, [fp, #-0x30]
    // 0xbba024: stur            x9, [fp, #-0x40]
    // 0xbba028: CheckStackOverflow
    //     0xbba028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbba02c: cmp             SP, x16
    //     0xbba030: b.ls            #0xbba63c
    // 0xbba034: cmp             x9, x3
    // 0xbba038: b.ge            #0xbba540
    // 0xbba03c: r0 = InitLateStaticField(0x15e0) // [package:substring_highlight/substring_highlight.dart] ::__int64MaxValue
    //     0xbba03c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbba040: ldr             x0, [x0, #0x2bc0]
    //     0xbba044: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbba048: cmp             w0, w16
    //     0xbba04c: b.ne            #0xbba05c
    //     0xbba050: add             x2, PP, #0x35, lsl #12  ; [pp+0x35c08] Field <::.__int64MaxValue@1951292948>: static late final (offset: 0x15e0)
    //     0xbba054: ldr             x2, [x2, #0xc08]
    //     0xbba058: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xbba05c: r2 = LoadInt32Instr(r0)
    //     0xbba05c: sbfx            x2, x0, #1, #0x1f
    //     0xbba060: tbz             w0, #0, #0xbba068
    //     0xbba064: ldur            x2, [x0, #7]
    // 0xbba068: ldur            x3, [fp, #-0x40]
    // 0xbba06c: r0 = BoxInt64Instr(r3)
    //     0xbba06c: sbfiz           x0, x3, #1, #0x1f
    //     0xbba070: cmp             x3, x0, asr #1
    //     0xbba074: b.eq            #0xbba080
    //     0xbba078: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbba07c: stur            x3, [x0, #7]
    // 0xbba080: mov             x3, x0
    // 0xbba084: stur            x3, [fp, #-0x70]
    // 0xbba088: mov             x7, x2
    // 0xbba08c: r8 = -1
    //     0xbba08c: movn            x8, #0
    // 0xbba090: r6 = 0
    //     0xbba090: movz            x6, #0
    // 0xbba094: ldur            x4, [fp, #-0x28]
    // 0xbba098: ldur            x5, [fp, #-0x30]
    // 0xbba09c: stur            x8, [fp, #-0x40]
    // 0xbba0a0: stur            x7, [fp, #-0x60]
    // 0xbba0a4: stur            x6, [fp, #-0x68]
    // 0xbba0a8: CheckStackOverflow
    //     0xbba0a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbba0ac: cmp             SP, x16
    //     0xbba0b0: b.ls            #0xbba644
    // 0xbba0b4: LoadField: r0 = r5->field_b
    //     0xbba0b4: ldur            w0, [x5, #0xb]
    // 0xbba0b8: r1 = LoadInt32Instr(r0)
    //     0xbba0b8: sbfx            x1, x0, #1, #0x1f
    // 0xbba0bc: cmp             x6, x1
    // 0xbba0c0: b.ge            #0xbba140
    // 0xbba0c4: LoadField: r0 = r5->field_f
    //     0xbba0c4: ldur            w0, [x5, #0xf]
    // 0xbba0c8: DecompressPointer r0
    //     0xbba0c8: add             x0, x0, HEAP, lsl #32
    // 0xbba0cc: ArrayLoad: r2 = r0[r6]  ; Unknown_4
    //     0xbba0cc: add             x16, x0, x6, lsl #2
    //     0xbba0d0: ldur            w2, [x16, #0xf]
    // 0xbba0d4: DecompressPointer r2
    //     0xbba0d4: add             x2, x2, HEAP, lsl #32
    // 0xbba0d8: r0 = LoadClassIdInstr(r4)
    //     0xbba0d8: ldur            x0, [x4, #-1]
    //     0xbba0dc: ubfx            x0, x0, #0xc, #0x14
    // 0xbba0e0: str             x3, [SP]
    // 0xbba0e4: mov             x1, x4
    // 0xbba0e8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbba0e8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbba0ec: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbba0ec: sub             lr, x0, #0xffa
    //     0xbba0f0: ldr             lr, [x21, lr, lsl #3]
    //     0xbba0f4: blr             lr
    // 0xbba0f8: tbnz            x0, #0x3f, #0xbba124
    // 0xbba0fc: ldur            x3, [fp, #-0x60]
    // 0xbba100: cmp             x0, x3
    // 0xbba104: b.ge            #0xbba110
    // 0xbba108: ldur            x1, [fp, #-0x68]
    // 0xbba10c: b               #0xbba118
    // 0xbba110: ldur            x1, [fp, #-0x40]
    // 0xbba114: mov             x0, x3
    // 0xbba118: mov             x8, x1
    // 0xbba11c: mov             x7, x0
    // 0xbba120: b               #0xbba130
    // 0xbba124: ldur            x3, [fp, #-0x60]
    // 0xbba128: ldur            x8, [fp, #-0x40]
    // 0xbba12c: mov             x7, x3
    // 0xbba130: ldur            x0, [fp, #-0x68]
    // 0xbba134: add             x6, x0, #1
    // 0xbba138: ldur            x3, [fp, #-0x70]
    // 0xbba13c: b               #0xbba094
    // 0xbba140: mov             x4, x8
    // 0xbba144: mov             x3, x7
    // 0xbba148: tbnz            x4, #0x3f, #0xbba438
    // 0xbba14c: ldur            x5, [fp, #-0x20]
    // 0xbba150: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xbba150: ldur            w0, [x5, #0x17]
    // 0xbba154: DecompressPointer r0
    //     0xbba154: add             x0, x0, HEAP, lsl #32
    // 0xbba158: r2 = LoadInt32Instr(r0)
    //     0xbba158: sbfx            x2, x0, #1, #0x1f
    //     0xbba15c: tbz             w0, #0, #0xbba164
    //     0xbba160: ldur            x2, [x0, #7]
    // 0xbba164: cmp             x2, x3
    // 0xbba168: b.ge            #0xbba29c
    // 0xbba16c: LoadField: r6 = r5->field_13
    //     0xbba16c: ldur            w6, [x5, #0x13]
    // 0xbba170: DecompressPointer r6
    //     0xbba170: add             x6, x6, HEAP, lsl #32
    // 0xbba174: stur            x6, [fp, #-0x78]
    // 0xbba178: LoadField: r0 = r5->field_f
    //     0xbba178: ldur            w0, [x5, #0xf]
    // 0xbba17c: DecompressPointer r0
    //     0xbba17c: add             x0, x0, HEAP, lsl #32
    // 0xbba180: LoadField: r7 = r0->field_1f
    //     0xbba180: ldur            w7, [x0, #0x1f]
    // 0xbba184: DecompressPointer r7
    //     0xbba184: add             x7, x7, HEAP, lsl #32
    // 0xbba188: r0 = BoxInt64Instr(r3)
    //     0xbba188: sbfiz           x0, x3, #1, #0x1f
    //     0xbba18c: cmp             x3, x0, asr #1
    //     0xbba190: b.eq            #0xbba19c
    //     0xbba194: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbba198: stur            x3, [x0, #7]
    // 0xbba19c: stur            x0, [fp, #-0x70]
    // 0xbba1a0: str             x0, [SP]
    // 0xbba1a4: mov             x1, x7
    // 0xbba1a8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbba1a8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbba1ac: r0 = substring()
    //     0xbba1ac: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xbba1b0: mov             x1, x0
    // 0xbba1b4: ldur            x0, [fp, #-0x20]
    // 0xbba1b8: stur            x1, [fp, #-0x88]
    // 0xbba1bc: LoadField: r2 = r0->field_f
    //     0xbba1bc: ldur            w2, [x0, #0xf]
    // 0xbba1c0: DecompressPointer r2
    //     0xbba1c0: add             x2, x2, HEAP, lsl #32
    // 0xbba1c4: LoadField: r3 = r2->field_27
    //     0xbba1c4: ldur            w3, [x2, #0x27]
    // 0xbba1c8: DecompressPointer r3
    //     0xbba1c8: add             x3, x3, HEAP, lsl #32
    // 0xbba1cc: stur            x3, [fp, #-0x80]
    // 0xbba1d0: r0 = TextSpan()
    //     0xbba1d0: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xbba1d4: mov             x2, x0
    // 0xbba1d8: ldur            x0, [fp, #-0x88]
    // 0xbba1dc: stur            x2, [fp, #-0x90]
    // 0xbba1e0: StoreField: r2->field_b = r0
    //     0xbba1e0: stur            w0, [x2, #0xb]
    // 0xbba1e4: r0 = Instance__DeferringMouseCursor
    //     0xbba1e4: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xbba1e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbba1e8: stur            w0, [x2, #0x17]
    // 0xbba1ec: ldur            x1, [fp, #-0x80]
    // 0xbba1f0: StoreField: r2->field_7 = r1
    //     0xbba1f0: stur            w1, [x2, #7]
    // 0xbba1f4: ldur            x3, [fp, #-0x78]
    // 0xbba1f8: LoadField: r1 = r3->field_b
    //     0xbba1f8: ldur            w1, [x3, #0xb]
    // 0xbba1fc: LoadField: r4 = r3->field_f
    //     0xbba1fc: ldur            w4, [x3, #0xf]
    // 0xbba200: DecompressPointer r4
    //     0xbba200: add             x4, x4, HEAP, lsl #32
    // 0xbba204: LoadField: r5 = r4->field_b
    //     0xbba204: ldur            w5, [x4, #0xb]
    // 0xbba208: r4 = LoadInt32Instr(r1)
    //     0xbba208: sbfx            x4, x1, #1, #0x1f
    // 0xbba20c: stur            x4, [fp, #-0x68]
    // 0xbba210: r1 = LoadInt32Instr(r5)
    //     0xbba210: sbfx            x1, x5, #1, #0x1f
    // 0xbba214: cmp             x4, x1
    // 0xbba218: b.ne            #0xbba224
    // 0xbba21c: mov             x1, x3
    // 0xbba220: r0 = _growToNextCapacity()
    //     0xbba220: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbba224: ldur            x4, [fp, #-0x20]
    // 0xbba228: ldur            x0, [fp, #-0x78]
    // 0xbba22c: ldur            x2, [fp, #-0x68]
    // 0xbba230: add             x1, x2, #1
    // 0xbba234: lsl             x3, x1, #1
    // 0xbba238: StoreField: r0->field_b = r3
    //     0xbba238: stur            w3, [x0, #0xb]
    // 0xbba23c: LoadField: r1 = r0->field_f
    //     0xbba23c: ldur            w1, [x0, #0xf]
    // 0xbba240: DecompressPointer r1
    //     0xbba240: add             x1, x1, HEAP, lsl #32
    // 0xbba244: ldur            x0, [fp, #-0x90]
    // 0xbba248: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbba248: add             x25, x1, x2, lsl #2
    //     0xbba24c: add             x25, x25, #0xf
    //     0xbba250: str             w0, [x25]
    //     0xbba254: tbz             w0, #0, #0xbba270
    //     0xbba258: ldurb           w16, [x1, #-1]
    //     0xbba25c: ldurb           w17, [x0, #-1]
    //     0xbba260: and             x16, x17, x16, lsr #2
    //     0xbba264: tst             x16, HEAP, lsr #32
    //     0xbba268: b.eq            #0xbba270
    //     0xbba26c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbba270: ldur            x0, [fp, #-0x70]
    // 0xbba274: ArrayStore: r4[0] = r0  ; List_4
    //     0xbba274: stur            w0, [x4, #0x17]
    //     0xbba278: tbz             w0, #0, #0xbba294
    //     0xbba27c: ldurb           w16, [x4, #-1]
    //     0xbba280: ldurb           w17, [x0, #-1]
    //     0xbba284: and             x16, x17, x16, lsr #2
    //     0xbba288: tst             x16, HEAP, lsr #32
    //     0xbba28c: b.eq            #0xbba294
    //     0xbba290: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0xbba294: ldur            x7, [fp, #-0x60]
    // 0xbba298: b               #0xbba2b0
    // 0xbba29c: mov             x4, x5
    // 0xbba2a0: r1 = LoadInt32Instr(r0)
    //     0xbba2a0: sbfx            x1, x0, #1, #0x1f
    //     0xbba2a4: tbz             w0, #0, #0xbba2ac
    //     0xbba2a8: ldur            x1, [x0, #7]
    // 0xbba2ac: mov             x7, x1
    // 0xbba2b0: ldur            x5, [fp, #-0x30]
    // 0xbba2b4: ldur            x3, [fp, #-0x40]
    // 0xbba2b8: ldur            x2, [fp, #-0x60]
    // 0xbba2bc: ldur            x6, [fp, #-0x38]
    // 0xbba2c0: stur            x7, [fp, #-0x68]
    // 0xbba2c4: LoadField: r0 = r5->field_b
    //     0xbba2c4: ldur            w0, [x5, #0xb]
    // 0xbba2c8: r1 = LoadInt32Instr(r0)
    //     0xbba2c8: sbfx            x1, x0, #1, #0x1f
    // 0xbba2cc: mov             x0, x1
    // 0xbba2d0: mov             x1, x3
    // 0xbba2d4: cmp             x1, x0
    // 0xbba2d8: b.hs            #0xbba64c
    // 0xbba2dc: LoadField: r0 = r5->field_f
    //     0xbba2dc: ldur            w0, [x5, #0xf]
    // 0xbba2e0: DecompressPointer r0
    //     0xbba2e0: add             x0, x0, HEAP, lsl #32
    // 0xbba2e4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbba2e4: add             x16, x0, x3, lsl #2
    //     0xbba2e8: ldur            w1, [x16, #0xf]
    // 0xbba2ec: DecompressPointer r1
    //     0xbba2ec: add             x1, x1, HEAP, lsl #32
    // 0xbba2f0: LoadField: r0 = r1->field_7
    //     0xbba2f0: ldur            w0, [x1, #7]
    // 0xbba2f4: LoadField: r8 = r4->field_13
    //     0xbba2f4: ldur            w8, [x4, #0x13]
    // 0xbba2f8: DecompressPointer r8
    //     0xbba2f8: add             x8, x8, HEAP, lsl #32
    // 0xbba2fc: stur            x8, [fp, #-0x78]
    // 0xbba300: r1 = LoadInt32Instr(r0)
    //     0xbba300: sbfx            x1, x0, #1, #0x1f
    // 0xbba304: add             x9, x2, x1
    // 0xbba308: stur            x9, [fp, #-0x40]
    // 0xbba30c: r0 = BoxInt64Instr(r9)
    //     0xbba30c: sbfiz           x0, x9, #1, #0x1f
    //     0xbba310: cmp             x9, x0, asr #1
    //     0xbba314: b.eq            #0xbba320
    //     0xbba318: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbba31c: stur            x9, [x0, #7]
    // 0xbba320: mov             x1, x7
    // 0xbba324: mov             x2, x0
    // 0xbba328: ldur            x3, [fp, #-0x48]
    // 0xbba32c: stur            x0, [fp, #-0x70]
    // 0xbba330: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xbba330: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xbba334: r0 = checkValidRange()
    //     0xbba334: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0xbba338: ldur            x1, [fp, #-0x18]
    // 0xbba33c: ldur            x2, [fp, #-0x68]
    // 0xbba340: mov             x3, x0
    // 0xbba344: r0 = _substringUnchecked()
    //     0xbba344: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0xbba348: stur            x0, [fp, #-0x80]
    // 0xbba34c: r0 = TextSpan()
    //     0xbba34c: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xbba350: mov             x2, x0
    // 0xbba354: ldur            x0, [fp, #-0x80]
    // 0xbba358: stur            x2, [fp, #-0x88]
    // 0xbba35c: StoreField: r2->field_b = r0
    //     0xbba35c: stur            w0, [x2, #0xb]
    // 0xbba360: r0 = Instance__DeferringMouseCursor
    //     0xbba360: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xbba364: ArrayStore: r2[0] = r0  ; List_4
    //     0xbba364: stur            w0, [x2, #0x17]
    // 0xbba368: ldur            x3, [fp, #-0x38]
    // 0xbba36c: StoreField: r2->field_7 = r3
    //     0xbba36c: stur            w3, [x2, #7]
    // 0xbba370: ldur            x4, [fp, #-0x78]
    // 0xbba374: LoadField: r1 = r4->field_b
    //     0xbba374: ldur            w1, [x4, #0xb]
    // 0xbba378: LoadField: r5 = r4->field_f
    //     0xbba378: ldur            w5, [x4, #0xf]
    // 0xbba37c: DecompressPointer r5
    //     0xbba37c: add             x5, x5, HEAP, lsl #32
    // 0xbba380: LoadField: r6 = r5->field_b
    //     0xbba380: ldur            w6, [x5, #0xb]
    // 0xbba384: r5 = LoadInt32Instr(r1)
    //     0xbba384: sbfx            x5, x1, #1, #0x1f
    // 0xbba388: stur            x5, [fp, #-0x60]
    // 0xbba38c: r1 = LoadInt32Instr(r6)
    //     0xbba38c: sbfx            x1, x6, #1, #0x1f
    // 0xbba390: cmp             x5, x1
    // 0xbba394: b.ne            #0xbba3a0
    // 0xbba398: mov             x1, x4
    // 0xbba39c: r0 = _growToNextCapacity()
    //     0xbba39c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbba3a0: ldur            x3, [fp, #-0x20]
    // 0xbba3a4: ldur            x0, [fp, #-0x78]
    // 0xbba3a8: ldur            x2, [fp, #-0x60]
    // 0xbba3ac: add             x1, x2, #1
    // 0xbba3b0: lsl             x4, x1, #1
    // 0xbba3b4: StoreField: r0->field_b = r4
    //     0xbba3b4: stur            w4, [x0, #0xb]
    // 0xbba3b8: LoadField: r1 = r0->field_f
    //     0xbba3b8: ldur            w1, [x0, #0xf]
    // 0xbba3bc: DecompressPointer r1
    //     0xbba3bc: add             x1, x1, HEAP, lsl #32
    // 0xbba3c0: ldur            x0, [fp, #-0x88]
    // 0xbba3c4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbba3c4: add             x25, x1, x2, lsl #2
    //     0xbba3c8: add             x25, x25, #0xf
    //     0xbba3cc: str             w0, [x25]
    //     0xbba3d0: tbz             w0, #0, #0xbba3ec
    //     0xbba3d4: ldurb           w16, [x1, #-1]
    //     0xbba3d8: ldurb           w17, [x0, #-1]
    //     0xbba3dc: and             x16, x17, x16, lsr #2
    //     0xbba3e0: tst             x16, HEAP, lsr #32
    //     0xbba3e4: b.eq            #0xbba3ec
    //     0xbba3e8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbba3ec: ldur            x0, [fp, #-0x70]
    // 0xbba3f0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbba3f0: stur            w0, [x3, #0x17]
    //     0xbba3f4: tbz             w0, #0, #0xbba410
    //     0xbba3f8: ldurb           w16, [x3, #-1]
    //     0xbba3fc: ldurb           w17, [x0, #-1]
    //     0xbba400: and             x16, x17, x16, lsr #2
    //     0xbba404: tst             x16, HEAP, lsr #32
    //     0xbba408: b.eq            #0xbba410
    //     0xbba40c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xbba410: ldur            x9, [fp, #-0x40]
    // 0xbba414: ldur            x5, [fp, #-8]
    // 0xbba418: mov             x1, x3
    // 0xbba41c: ldur            x4, [fp, #-0x18]
    // 0xbba420: ldur            x0, [fp, #-0x28]
    // 0xbba424: ldur            x7, [fp, #-0x38]
    // 0xbba428: ldur            x2, [fp, #-0x58]
    // 0xbba42c: ldur            x3, [fp, #-0x50]
    // 0xbba430: ldur            x6, [fp, #-0x48]
    // 0xbba434: b               #0xbba020
    // 0xbba438: ldur            x3, [fp, #-0x20]
    // 0xbba43c: LoadField: r0 = r3->field_13
    //     0xbba43c: ldur            w0, [x3, #0x13]
    // 0xbba440: DecompressPointer r0
    //     0xbba440: add             x0, x0, HEAP, lsl #32
    // 0xbba444: stur            x0, [fp, #-0x18]
    // 0xbba448: LoadField: r1 = r3->field_f
    //     0xbba448: ldur            w1, [x3, #0xf]
    // 0xbba44c: DecompressPointer r1
    //     0xbba44c: add             x1, x1, HEAP, lsl #32
    // 0xbba450: LoadField: r2 = r1->field_1f
    //     0xbba450: ldur            w2, [x1, #0x1f]
    // 0xbba454: DecompressPointer r2
    //     0xbba454: add             x2, x2, HEAP, lsl #32
    // 0xbba458: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xbba458: ldur            w1, [x3, #0x17]
    // 0xbba45c: DecompressPointer r1
    //     0xbba45c: add             x1, x1, HEAP, lsl #32
    // 0xbba460: r4 = LoadInt32Instr(r1)
    //     0xbba460: sbfx            x4, x1, #1, #0x1f
    //     0xbba464: tbz             w1, #0, #0xbba46c
    //     0xbba468: ldur            x4, [x1, #7]
    // 0xbba46c: ldur            x16, [fp, #-0x58]
    // 0xbba470: str             x16, [SP]
    // 0xbba474: mov             x1, x2
    // 0xbba478: mov             x2, x4
    // 0xbba47c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbba47c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbba480: r0 = substring()
    //     0xbba480: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xbba484: mov             x1, x0
    // 0xbba488: ldur            x0, [fp, #-0x20]
    // 0xbba48c: stur            x1, [fp, #-0x30]
    // 0xbba490: LoadField: r2 = r0->field_f
    //     0xbba490: ldur            w2, [x0, #0xf]
    // 0xbba494: DecompressPointer r2
    //     0xbba494: add             x2, x2, HEAP, lsl #32
    // 0xbba498: LoadField: r3 = r2->field_27
    //     0xbba498: ldur            w3, [x2, #0x27]
    // 0xbba49c: DecompressPointer r3
    //     0xbba49c: add             x3, x3, HEAP, lsl #32
    // 0xbba4a0: stur            x3, [fp, #-0x28]
    // 0xbba4a4: r0 = TextSpan()
    //     0xbba4a4: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xbba4a8: mov             x2, x0
    // 0xbba4ac: ldur            x0, [fp, #-0x30]
    // 0xbba4b0: stur            x2, [fp, #-0x38]
    // 0xbba4b4: StoreField: r2->field_b = r0
    //     0xbba4b4: stur            w0, [x2, #0xb]
    // 0xbba4b8: r0 = Instance__DeferringMouseCursor
    //     0xbba4b8: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xbba4bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xbba4bc: stur            w0, [x2, #0x17]
    // 0xbba4c0: ldur            x1, [fp, #-0x28]
    // 0xbba4c4: StoreField: r2->field_7 = r1
    //     0xbba4c4: stur            w1, [x2, #7]
    // 0xbba4c8: ldur            x3, [fp, #-0x18]
    // 0xbba4cc: LoadField: r1 = r3->field_b
    //     0xbba4cc: ldur            w1, [x3, #0xb]
    // 0xbba4d0: LoadField: r4 = r3->field_f
    //     0xbba4d0: ldur            w4, [x3, #0xf]
    // 0xbba4d4: DecompressPointer r4
    //     0xbba4d4: add             x4, x4, HEAP, lsl #32
    // 0xbba4d8: LoadField: r5 = r4->field_b
    //     0xbba4d8: ldur            w5, [x4, #0xb]
    // 0xbba4dc: r4 = LoadInt32Instr(r1)
    //     0xbba4dc: sbfx            x4, x1, #1, #0x1f
    // 0xbba4e0: stur            x4, [fp, #-0x40]
    // 0xbba4e4: r1 = LoadInt32Instr(r5)
    //     0xbba4e4: sbfx            x1, x5, #1, #0x1f
    // 0xbba4e8: cmp             x4, x1
    // 0xbba4ec: b.ne            #0xbba4f8
    // 0xbba4f0: mov             x1, x3
    // 0xbba4f4: r0 = _growToNextCapacity()
    //     0xbba4f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbba4f8: ldur            x0, [fp, #-0x18]
    // 0xbba4fc: ldur            x2, [fp, #-0x40]
    // 0xbba500: add             x1, x2, #1
    // 0xbba504: lsl             x3, x1, #1
    // 0xbba508: StoreField: r0->field_b = r3
    //     0xbba508: stur            w3, [x0, #0xb]
    // 0xbba50c: LoadField: r1 = r0->field_f
    //     0xbba50c: ldur            w1, [x0, #0xf]
    // 0xbba510: DecompressPointer r1
    //     0xbba510: add             x1, x1, HEAP, lsl #32
    // 0xbba514: ldur            x0, [fp, #-0x38]
    // 0xbba518: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbba518: add             x25, x1, x2, lsl #2
    //     0xbba51c: add             x25, x25, #0xf
    //     0xbba520: str             w0, [x25]
    //     0xbba524: tbz             w0, #0, #0xbba540
    //     0xbba528: ldurb           w16, [x1, #-1]
    //     0xbba52c: ldurb           w17, [x0, #-1]
    //     0xbba530: and             x16, x17, x16, lsr #2
    //     0xbba534: tst             x16, HEAP, lsr #32
    //     0xbba538: b.eq            #0xbba540
    //     0xbba53c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbba540: ldur            x1, [fp, #-8]
    // 0xbba544: ldur            x0, [fp, #-0x20]
    // 0xbba548: LoadField: r2 = r0->field_13
    //     0xbba548: ldur            w2, [x0, #0x13]
    // 0xbba54c: DecompressPointer r2
    //     0xbba54c: add             x2, x2, HEAP, lsl #32
    // 0xbba550: stur            x2, [fp, #-0x28]
    // 0xbba554: LoadField: r0 = r1->field_27
    //     0xbba554: ldur            w0, [x1, #0x27]
    // 0xbba558: DecompressPointer r0
    //     0xbba558: add             x0, x0, HEAP, lsl #32
    // 0xbba55c: stur            x0, [fp, #-0x18]
    // 0xbba560: r0 = TextSpan()
    //     0xbba560: bl              #0x773ac8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xbba564: mov             x2, x0
    // 0xbba568: ldur            x0, [fp, #-0x28]
    // 0xbba56c: stur            x2, [fp, #-0x20]
    // 0xbba570: StoreField: r2->field_f = r0
    //     0xbba570: stur            w0, [x2, #0xf]
    // 0xbba574: r0 = Instance__DeferringMouseCursor
    //     0xbba574: ldr             x0, [PP, #0x26f8]  ; [pp+0x26f8] Obj!_DeferringMouseCursor@e1cf31
    // 0xbba578: ArrayStore: r2[0] = r0  ; List_4
    //     0xbba578: stur            w0, [x2, #0x17]
    // 0xbba57c: ldur            x0, [fp, #-0x18]
    // 0xbba580: StoreField: r2->field_7 = r0
    //     0xbba580: stur            w0, [x2, #7]
    // 0xbba584: ldur            x0, [fp, #-8]
    // 0xbba588: LoadField: r3 = r0->field_23
    //     0xbba588: ldur            w3, [x0, #0x23]
    // 0xbba58c: DecompressPointer r3
    //     0xbba58c: add             x3, x3, HEAP, lsl #32
    // 0xbba590: ldur            x1, [fp, #-0x10]
    // 0xbba594: stur            x3, [fp, #-0x18]
    // 0xbba598: r0 = of()
    //     0xbba598: bl              #0x980794  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::of
    // 0xbba59c: LoadField: r1 = r0->field_1b
    //     0xbba59c: ldur            w1, [x0, #0x1b]
    // 0xbba5a0: DecompressPointer r1
    //     0xbba5a0: add             x1, x1, HEAP, lsl #32
    // 0xbba5a4: r16 = Instance__UnspecifiedTextScaler
    //     0xbba5a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12760] Obj!_UnspecifiedTextScaler@e0fdd1
    //     0xbba5a8: ldr             x16, [x16, #0x760]
    // 0xbba5ac: cmp             w1, w16
    // 0xbba5b0: b.ne            #0xbba5d8
    // 0xbba5b4: d0 = 1.000000
    //     0xbba5b4: fmov            d0, #1.00000000
    // 0xbba5b8: fcmp            d0, d0
    // 0xbba5bc: b.ne            #0xbba5c8
    // 0xbba5c0: r0 = Instance__LinearTextScaler
    //     0xbba5c0: ldr             x0, [PP, #0x4708]  ; [pp+0x4708] Obj!_LinearTextScaler@e11ae1
    // 0xbba5c4: b               #0xbba5d4
    // 0xbba5c8: r0 = _LinearTextScaler()
    //     0xbba5c8: bl              #0x6880c0  ; Allocate_LinearTextScalerStub -> _LinearTextScaler (size=0x10)
    // 0xbba5cc: d0 = 1.000000
    //     0xbba5cc: fmov            d0, #1.00000000
    // 0xbba5d0: StoreField: r0->field_7 = d0
    //     0xbba5d0: stur            d0, [x0, #7]
    // 0xbba5d4: mov             x1, x0
    // 0xbba5d8: r0 = LoadClassIdInstr(r1)
    //     0xbba5d8: ldur            x0, [x1, #-1]
    //     0xbba5dc: ubfx            x0, x0, #0xc, #0x14
    // 0xbba5e0: r0 = GDT[cid_x0 + -0xfe4]()
    //     0xbba5e0: sub             lr, x0, #0xfe4
    //     0xbba5e4: ldr             lr, [x21, lr, lsl #3]
    //     0xbba5e8: blr             lr
    // 0xbba5ec: stur            x0, [fp, #-8]
    // 0xbba5f0: r0 = RichText()
    //     0xbba5f0: bl              #0xaa3ce4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbba5f4: stur            x0, [fp, #-0x10]
    // 0xbba5f8: r16 = Instance_TextOverflow
    //     0xbba5f8: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ac60] Obj!TextOverflow@e35ca1
    //     0xbba5fc: ldr             x16, [x16, #0xc60]
    // 0xbba600: stp             x16, NULL, [SP, #0x10]
    // 0xbba604: ldur            x16, [fp, #-0x18]
    // 0xbba608: ldur            lr, [fp, #-8]
    // 0xbba60c: stp             lr, x16, [SP]
    // 0xbba610: mov             x1, x0
    // 0xbba614: ldur            x2, [fp, #-0x20]
    // 0xbba618: r4 = const [0, 0x6, 0x4, 0x2, maxLines, 0x2, overflow, 0x3, textAlign, 0x4, textScaleFactor, 0x5, null]
    //     0xbba618: add             x4, PP, #0x35, lsl #12  ; [pp+0x35c10] List(13) [0, 0x6, 0x4, 0x2, "maxLines", 0x2, "overflow", 0x3, "textAlign", 0x4, "textScaleFactor", 0x5, Null]
    //     0xbba61c: ldr             x4, [x4, #0xc10]
    // 0xbba620: r0 = RichText()
    //     0xbba620: bl              #0xaa3744  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbba624: ldur            x0, [fp, #-0x10]
    // 0xbba628: LeaveFrame
    //     0xbba628: mov             SP, fp
    //     0xbba62c: ldp             fp, lr, [SP], #0x10
    // 0xbba630: ret
    //     0xbba630: ret             
    // 0xbba634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba634: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba638: b               #0xbb9ed0
    // 0xbba63c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba63c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba640: b               #0xbba034
    // 0xbba644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba644: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba648: b               #0xbba0b4
    // 0xbba64c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbba64c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] String <anonymous closure>(dynamic, String) {
    // ** addr: 0xbba6a4, size: 0x4c
    // 0xbba6a4: EnterFrame
    //     0xbba6a4: stp             fp, lr, [SP, #-0x10]!
    //     0xbba6a8: mov             fp, SP
    // 0xbba6ac: AllocStack(0x8)
    //     0xbba6ac: sub             SP, SP, #8
    // 0xbba6b0: CheckStackOverflow
    //     0xbba6b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbba6b4: cmp             SP, x16
    //     0xbba6b8: b.ls            #0xbba6e8
    // 0xbba6bc: ldr             x0, [fp, #0x10]
    // 0xbba6c0: r1 = LoadClassIdInstr(r0)
    //     0xbba6c0: ldur            x1, [x0, #-1]
    //     0xbba6c4: ubfx            x1, x1, #0xc, #0x14
    // 0xbba6c8: str             x0, [SP]
    // 0xbba6cc: mov             x0, x1
    // 0xbba6d0: r0 = GDT[cid_x0 + -0xffe]()
    //     0xbba6d0: sub             lr, x0, #0xffe
    //     0xbba6d4: ldr             lr, [x21, lr, lsl #3]
    //     0xbba6d8: blr             lr
    // 0xbba6dc: LeaveFrame
    //     0xbba6dc: mov             SP, fp
    //     0xbba6e0: ldp             fp, lr, [SP], #0x10
    // 0xbba6e4: ret
    //     0xbba6e4: ret             
    // 0xbba6e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba6e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba6ec: b               #0xbba6bc
  }
}
