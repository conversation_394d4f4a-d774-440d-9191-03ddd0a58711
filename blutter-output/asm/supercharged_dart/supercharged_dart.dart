// lib: supercharged_dart, url: package:supercharged_dart/supercharged_dart.dart

// class id: 1051178, size: 0x8
class :: {

  [closure] static Y0 <anonymous closure>(dynamic, Y0, Y0) {
    // ** addr: 0x739074, size: 0x90
    // 0x739074: EnterFrame
    //     0x739074: stp             fp, lr, [SP, #-0x10]!
    //     0x739078: mov             fp, SP
    // 0x73907c: AllocStack(0x18)
    //     0x73907c: sub             SP, SP, #0x18
    // 0x739080: SetupParameters()
    //     0x739080: ldr             x0, [fp, #0x20]
    //     0x739084: ldur            w1, [x0, #0x17]
    //     0x739088: add             x1, x1, HEAP, lsl #32
    // 0x73908c: CheckStackOverflow
    //     0x73908c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x739090: cmp             SP, x16
    //     0x739094: b.ls            #0x7390f8
    // 0x739098: LoadField: r0 = r1->field_f
    //     0x739098: ldur            w0, [x1, #0xf]
    // 0x73909c: DecompressPointer r0
    //     0x73909c: add             x0, x0, HEAP, lsl #32
    // 0x7390a0: ldr             x16, [fp, #0x18]
    // 0x7390a4: stp             x16, x0, [SP, #8]
    // 0x7390a8: ldr             x16, [fp, #0x10]
    // 0x7390ac: str             x16, [SP]
    // 0x7390b0: ClosureCall
    //     0x7390b0: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x7390b4: ldur            x2, [x0, #0x1f]
    //     0x7390b8: blr             x2
    // 0x7390bc: cmp             w0, NULL
    // 0x7390c0: b.eq            #0x739100
    // 0x7390c4: r1 = LoadInt32Instr(r0)
    //     0x7390c4: sbfx            x1, x0, #1, #0x1f
    //     0x7390c8: tbz             w0, #0, #0x7390d0
    //     0x7390cc: ldur            x1, [x0, #7]
    // 0x7390d0: cmp             x1, #0
    // 0x7390d4: b.le            #0x7390e4
    // 0x7390d8: ldr             x1, [fp, #0x18]
    // 0x7390dc: mov             x0, x1
    // 0x7390e0: b               #0x7390ec
    // 0x7390e4: ldr             x1, [fp, #0x10]
    // 0x7390e8: mov             x0, x1
    // 0x7390ec: LeaveFrame
    //     0x7390ec: mov             SP, fp
    //     0x7390f0: ldp             fp, lr, [SP], #0x10
    // 0x7390f4: ret
    //     0x7390f4: ret             
    // 0x7390f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7390f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7390fc: b               #0x739098
    // 0x739100: r0 = NullErrorSharedWithoutFPURegs()
    //     0x739100: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  static _ IterableSC.withoutLast(/* No info */) {
    // ** addr: 0x8bf80c, size: 0x17c
    // 0x8bf80c: EnterFrame
    //     0x8bf80c: stp             fp, lr, [SP, #-0x10]!
    //     0x8bf810: mov             fp, SP
    // 0x8bf814: AllocStack(0x18)
    //     0x8bf814: sub             SP, SP, #0x18
    // 0x8bf818: SetupParameters(dynamic _ /* r2, fp-0x18 */)
    //     0x8bf818: stur            NULL, [fp, #-8]
    //     0x8bf81c: movz            x1, #0
    //     0x8bf820: add             x2, fp, w1, sxtw #2
    //     0x8bf824: ldr             x2, [x2, #0x10]
    //     0x8bf828: stur            x2, [fp, #-0x18]
    // 0x8bf82c: LoadField: r0 = r4->field_f
    //     0x8bf82c: ldur            w0, [x4, #0xf]
    // 0x8bf830: cbnz            w0, #0x8bf83c
    // 0x8bf834: r3 = Null
    //     0x8bf834: mov             x3, NULL
    // 0x8bf838: b               #0x8bf848
    // 0x8bf83c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x8bf83c: ldur            w0, [x4, #0x17]
    // 0x8bf840: add             x3, fp, w0, sxtw #2
    // 0x8bf844: ldr             x3, [x3, #0x10]
    // 0x8bf848: stur            x3, [fp, #-0x10]
    // 0x8bf84c: CheckStackOverflow
    //     0x8bf84c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bf850: cmp             SP, x16
    //     0x8bf854: b.ls            #0x8bf978
    // 0x8bf858: mov             x0, x3
    // 0x8bf85c: r0 = InitAsync()
    //     0x8bf85c: bl              #0x7348c0  ; InitAsyncStub
    // 0x8bf860: r0 = Null
    //     0x8bf860: mov             x0, NULL
    // 0x8bf864: r0 = SuspendSyncStarAtStart()
    //     0x8bf864: bl              #0x734738  ; SuspendSyncStarAtStartStub
    // 0x8bf868: ldur            x1, [fp, #-0x18]
    // 0x8bf86c: r0 = LoadClassIdInstr(r1)
    //     0x8bf86c: ldur            x0, [x1, #-1]
    //     0x8bf870: ubfx            x0, x0, #0xc, #0x14
    // 0x8bf874: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x8bf874: movz            x17, #0xd35d
    //     0x8bf878: add             lr, x0, x17
    //     0x8bf87c: ldr             lr, [x21, lr, lsl #3]
    //     0x8bf880: blr             lr
    // 0x8bf884: mov             x2, x0
    // 0x8bf888: stur            x2, [fp, #-0x10]
    // 0x8bf88c: r0 = LoadClassIdInstr(r2)
    //     0x8bf88c: ldur            x0, [x2, #-1]
    //     0x8bf890: ubfx            x0, x0, #0xc, #0x14
    // 0x8bf894: mov             x1, x2
    // 0x8bf898: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x8bf898: movz            x17, #0x292d
    //     0x8bf89c: movk            x17, #0x1, lsl #16
    //     0x8bf8a0: add             lr, x0, x17
    //     0x8bf8a4: ldr             lr, [x21, lr, lsl #3]
    //     0x8bf8a8: blr             lr
    // 0x8bf8ac: tbz             w0, #4, #0x8bf8c0
    // 0x8bf8b0: r0 = false
    //     0x8bf8b0: add             x0, NULL, #0x30  ; false
    // 0x8bf8b4: LeaveFrame
    //     0x8bf8b4: mov             SP, fp
    //     0x8bf8b8: ldp             fp, lr, [SP], #0x10
    // 0x8bf8bc: ret
    //     0x8bf8bc: ret             
    // 0x8bf8c0: ldur            x2, [fp, #-0x10]
    // 0x8bf8c4: CheckStackOverflow
    //     0x8bf8c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8bf8c8: cmp             SP, x16
    //     0x8bf8cc: b.ls            #0x8bf980
    // 0x8bf8d0: r0 = LoadClassIdInstr(r2)
    //     0x8bf8d0: ldur            x0, [x2, #-1]
    //     0x8bf8d4: ubfx            x0, x0, #0xc, #0x14
    // 0x8bf8d8: mov             x1, x2
    // 0x8bf8dc: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x8bf8dc: movz            x17, #0x384d
    //     0x8bf8e0: movk            x17, #0x1, lsl #16
    //     0x8bf8e4: add             lr, x0, x17
    //     0x8bf8e8: ldr             lr, [x21, lr, lsl #3]
    //     0x8bf8ec: blr             lr
    // 0x8bf8f0: mov             x3, x0
    // 0x8bf8f4: ldur            x2, [fp, #-0x10]
    // 0x8bf8f8: stur            x3, [fp, #-0x18]
    // 0x8bf8fc: r0 = LoadClassIdInstr(r2)
    //     0x8bf8fc: ldur            x0, [x2, #-1]
    //     0x8bf900: ubfx            x0, x0, #0xc, #0x14
    // 0x8bf904: mov             x1, x2
    // 0x8bf908: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x8bf908: movz            x17, #0x292d
    //     0x8bf90c: movk            x17, #0x1, lsl #16
    //     0x8bf910: add             lr, x0, x17
    //     0x8bf914: ldr             lr, [x21, lr, lsl #3]
    //     0x8bf918: blr             lr
    // 0x8bf91c: eor             x1, x0, #0x10
    // 0x8bf920: tbz             w1, #4, #0x8bf968
    // 0x8bf924: r1 = 0
    //     0x8bf924: movz            x1, #0
    // 0x8bf928: add             x0, fp, w1, sxtw #2
    // 0x8bf92c: LoadField: r0 = r0->field_fffffff8
    //     0x8bf92c: ldur            x0, [x0, #-8]
    // 0x8bf930: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8bf930: ldur            w2, [x0, #0x17]
    // 0x8bf934: DecompressPointer r2
    //     0x8bf934: add             x2, x2, HEAP, lsl #32
    // 0x8bf938: ldur            x0, [fp, #-0x18]
    // 0x8bf93c: ArrayStore: r2[0] = r0  ; List_4
    //     0x8bf93c: stur            w0, [x2, #0x17]
    //     0x8bf940: tbz             w0, #0, #0x8bf95c
    //     0x8bf944: ldurb           w16, [x2, #-1]
    //     0x8bf948: ldurb           w17, [x0, #-1]
    //     0x8bf94c: and             x16, x17, x16, lsr #2
    //     0x8bf950: tst             x16, HEAP, lsr #32
    //     0x8bf954: b.eq            #0x8bf95c
    //     0x8bf958: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8bf95c: r0 = true
    //     0x8bf95c: add             x0, NULL, #0x20  ; true
    // 0x8bf960: r0 = SuspendSyncStarAtYield()
    //     0x8bf960: bl              #0x7345b4  ; SuspendSyncStarAtYieldStub
    // 0x8bf964: b               #0x8bf8c0
    // 0x8bf968: r0 = false
    //     0x8bf968: add             x0, NULL, #0x30  ; false
    // 0x8bf96c: LeaveFrame
    //     0x8bf96c: mov             SP, fp
    //     0x8bf970: ldp             fp, lr, [SP], #0x10
    // 0x8bf974: ret
    //     0x8bf974: ret             
    // 0x8bf978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bf978: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bf97c: b               #0x8bf858
    // 0x8bf980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8bf980: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8bf984: b               #0x8bf8d0
  }
  static _ IterableSC.sumBy(/* No info */) {
    // ** addr: 0xaec92c, size: 0xb4
    // 0xaec92c: EnterFrame
    //     0xaec92c: stp             fp, lr, [SP, #-0x10]!
    //     0xaec930: mov             fp, SP
    // 0xaec934: AllocStack(0x30)
    //     0xaec934: sub             SP, SP, #0x30
    // 0xaec938: SetupParameters()
    //     0xaec938: ldur            w0, [x4, #0xf]
    //     0xaec93c: cbnz            w0, #0xaec948
    //     0xaec940: mov             x0, NULL
    //     0xaec944: b               #0xaec958
    //     0xaec948: ldur            w0, [x4, #0x17]
    //     0xaec94c: add             x1, fp, w0, sxtw #2
    //     0xaec950: ldr             x1, [x1, #0x10]
    //     0xaec954: mov             x0, x1
    //     0xaec958: stur            x0, [fp, #-8]
    // 0xaec95c: CheckStackOverflow
    //     0xaec95c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaec960: cmp             SP, x16
    //     0xaec964: b.ls            #0xaec9d8
    // 0xaec968: r16 = <int>
    //     0xaec968: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaec96c: ldr             lr, [fp, #0x18]
    // 0xaec970: stp             lr, x16, [SP, #8]
    // 0xaec974: ldr             x16, [fp, #0x10]
    // 0xaec978: str             x16, [SP]
    // 0xaec97c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaec97c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaec980: r0 = map()
    //     0xaec980: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xaec984: r1 = Function '<anonymous closure>': static.
    //     0xaec984: add             x1, PP, #0x34, lsl #12  ; [pp+0x34cb8] AnonymousClosure: static (0xaec9e0), in [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.sumBy (0xaec92c)
    //     0xaec988: ldr             x1, [x1, #0xcb8]
    // 0xaec98c: r2 = Null
    //     0xaec98c: mov             x2, NULL
    // 0xaec990: stur            x0, [fp, #-0x10]
    // 0xaec994: r0 = AllocateClosure()
    //     0xaec994: bl              #0xec1630  ; AllocateClosureStub
    // 0xaec998: mov             x1, x0
    // 0xaec99c: ldur            x0, [fp, #-8]
    // 0xaec9a0: StoreField: r1->field_b = r0
    //     0xaec9a0: stur            w0, [x1, #0xb]
    // 0xaec9a4: r16 = <int>
    //     0xaec9a4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaec9a8: ldur            lr, [fp, #-0x10]
    // 0xaec9ac: stp             lr, x16, [SP, #0x10]
    // 0xaec9b0: stp             x1, xzr, [SP]
    // 0xaec9b4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xaec9b4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xaec9b8: r0 = fold()
    //     0xaec9b8: bl              #0x7d8444  ; [dart:_internal] ListIterable::fold
    // 0xaec9bc: r1 = LoadInt32Instr(r0)
    //     0xaec9bc: sbfx            x1, x0, #1, #0x1f
    //     0xaec9c0: tbz             w0, #0, #0xaec9c8
    //     0xaec9c4: ldur            x1, [x0, #7]
    // 0xaec9c8: mov             x0, x1
    // 0xaec9cc: LeaveFrame
    //     0xaec9cc: mov             SP, fp
    //     0xaec9d0: ldp             fp, lr, [SP], #0x10
    // 0xaec9d4: ret
    //     0xaec9d4: ret             
    // 0xaec9d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaec9d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaec9dc: b               #0xaec968
  }
  [closure] static int <anonymous closure>(dynamic, int, int) {
    // ** addr: 0xaec9e0, size: 0x4c
    // 0xaec9e0: ldr             x2, [SP, #8]
    // 0xaec9e4: r3 = LoadInt32Instr(r2)
    //     0xaec9e4: sbfx            x3, x2, #1, #0x1f
    //     0xaec9e8: tbz             w2, #0, #0xaec9f0
    //     0xaec9ec: ldur            x3, [x2, #7]
    // 0xaec9f0: ldr             x2, [SP]
    // 0xaec9f4: r4 = LoadInt32Instr(r2)
    //     0xaec9f4: sbfx            x4, x2, #1, #0x1f
    //     0xaec9f8: tbz             w2, #0, #0xaeca00
    //     0xaec9fc: ldur            x4, [x2, #7]
    // 0xaeca00: add             x2, x3, x4
    // 0xaeca04: r0 = BoxInt64Instr(r2)
    //     0xaeca04: sbfiz           x0, x2, #1, #0x1f
    //     0xaeca08: cmp             x2, x0, asr #1
    //     0xaeca0c: b.eq            #0xaeca28
    //     0xaeca10: stp             fp, lr, [SP, #-0x10]!
    //     0xaeca14: mov             fp, SP
    //     0xaeca18: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaeca1c: mov             SP, fp
    //     0xaeca20: ldp             fp, lr, [SP], #0x10
    //     0xaeca24: stur            x2, [x0, #7]
    // 0xaeca28: ret
    //     0xaeca28: ret             
  }
  static _ IterableSC.averageBy(/* No info */) {
    // ** addr: 0xb3fe68, size: 0xc4
    // 0xb3fe68: EnterFrame
    //     0xb3fe68: stp             fp, lr, [SP, #-0x10]!
    //     0xb3fe6c: mov             fp, SP
    // 0xb3fe70: AllocStack(0x18)
    //     0xb3fe70: sub             SP, SP, #0x18
    // 0xb3fe74: SetupParameters()
    //     0xb3fe74: ldur            w0, [x4, #0xf]
    //     0xb3fe78: cbnz            w0, #0xb3fe84
    //     0xb3fe7c: mov             x1, NULL
    //     0xb3fe80: b               #0xb3fe90
    //     0xb3fe84: ldur            w0, [x4, #0x17]
    //     0xb3fe88: add             x1, fp, w0, sxtw #2
    //     0xb3fe8c: ldr             x1, [x1, #0x10]
    //     0xb3fe90: ldr             x0, [fp, #0x18]
    // 0xb3fe94: CheckStackOverflow
    //     0xb3fe94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3fe98: cmp             SP, x16
    //     0xb3fe9c: b.ls            #0xb3ff14
    // 0xb3fea0: LoadField: r2 = r0->field_b
    //     0xb3fea0: ldur            w2, [x0, #0xb]
    // 0xb3fea4: cbnz            w2, #0xb3feb8
    // 0xb3fea8: r0 = Null
    //     0xb3fea8: mov             x0, NULL
    // 0xb3feac: LeaveFrame
    //     0xb3feac: mov             SP, fp
    //     0xb3feb0: ldp             fp, lr, [SP], #0x10
    // 0xb3feb4: ret
    //     0xb3feb4: ret             
    // 0xb3feb8: stp             x0, x1, [SP, #8]
    // 0xb3febc: ldr             x16, [fp, #0x10]
    // 0xb3fec0: str             x16, [SP]
    // 0xb3fec4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb3fec4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb3fec8: r0 = IterableSC.sumByDouble()
    //     0xb3fec8: bl              #0xb3ff2c  ; [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.sumByDouble
    // 0xb3fecc: ldr             x1, [fp, #0x18]
    // 0xb3fed0: LoadField: r2 = r1->field_b
    //     0xb3fed0: ldur            w2, [x1, #0xb]
    // 0xb3fed4: r16 = LoadInt32Instr(r2)
    //     0xb3fed4: sbfx            x16, x2, #1, #0x1f
    // 0xb3fed8: scvtf           d1, w16
    // 0xb3fedc: fdiv            d2, d0, d1
    // 0xb3fee0: r0 = inline_Allocate_Double()
    //     0xb3fee0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb3fee4: add             x0, x0, #0x10
    //     0xb3fee8: cmp             x1, x0
    //     0xb3feec: b.ls            #0xb3ff1c
    //     0xb3fef0: str             x0, [THR, #0x50]  ; THR::top
    //     0xb3fef4: sub             x0, x0, #0xf
    //     0xb3fef8: movz            x1, #0xe15c
    //     0xb3fefc: movk            x1, #0x3, lsl #16
    //     0xb3ff00: stur            x1, [x0, #-1]
    // 0xb3ff04: StoreField: r0->field_7 = d2
    //     0xb3ff04: stur            d2, [x0, #7]
    // 0xb3ff08: LeaveFrame
    //     0xb3ff08: mov             SP, fp
    //     0xb3ff0c: ldp             fp, lr, [SP], #0x10
    // 0xb3ff10: ret
    //     0xb3ff10: ret             
    // 0xb3ff14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3ff14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3ff18: b               #0xb3fea0
    // 0xb3ff1c: SaveReg d2
    //     0xb3ff1c: str             q2, [SP, #-0x10]!
    // 0xb3ff20: r0 = AllocateDouble()
    //     0xb3ff20: bl              #0xec2254  ; AllocateDoubleStub
    // 0xb3ff24: RestoreReg d2
    //     0xb3ff24: ldr             q2, [SP], #0x10
    // 0xb3ff28: b               #0xb3ff04
  }
  static _ IterableSC.sumByDouble(/* No info */) {
    // ** addr: 0xb3ff2c, size: 0xac
    // 0xb3ff2c: EnterFrame
    //     0xb3ff2c: stp             fp, lr, [SP, #-0x10]!
    //     0xb3ff30: mov             fp, SP
    // 0xb3ff34: AllocStack(0x30)
    //     0xb3ff34: sub             SP, SP, #0x30
    // 0xb3ff38: SetupParameters()
    //     0xb3ff38: ldur            w0, [x4, #0xf]
    //     0xb3ff3c: cbnz            w0, #0xb3ff48
    //     0xb3ff40: mov             x0, NULL
    //     0xb3ff44: b               #0xb3ff58
    //     0xb3ff48: ldur            w0, [x4, #0x17]
    //     0xb3ff4c: add             x1, fp, w0, sxtw #2
    //     0xb3ff50: ldr             x1, [x1, #0x10]
    //     0xb3ff54: mov             x0, x1
    //     0xb3ff58: stur            x0, [fp, #-8]
    // 0xb3ff5c: CheckStackOverflow
    //     0xb3ff5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3ff60: cmp             SP, x16
    //     0xb3ff64: b.ls            #0xb3ffd0
    // 0xb3ff68: r16 = <num>
    //     0xb3ff68: ldr             x16, [PP, #0x4148]  ; [pp+0x4148] TypeArguments: <num>
    // 0xb3ff6c: ldr             lr, [fp, #0x18]
    // 0xb3ff70: stp             lr, x16, [SP, #8]
    // 0xb3ff74: ldr             x16, [fp, #0x10]
    // 0xb3ff78: str             x16, [SP]
    // 0xb3ff7c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb3ff7c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb3ff80: r0 = map()
    //     0xb3ff80: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0xb3ff84: r1 = Function '<anonymous closure>': static.
    //     0xb3ff84: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2acf0] AnonymousClosure: static (0xb3ffd8), in [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.sumByDouble (0xb3ff2c)
    //     0xb3ff88: ldr             x1, [x1, #0xcf0]
    // 0xb3ff8c: r2 = Null
    //     0xb3ff8c: mov             x2, NULL
    // 0xb3ff90: stur            x0, [fp, #-0x10]
    // 0xb3ff94: r0 = AllocateClosure()
    //     0xb3ff94: bl              #0xec1630  ; AllocateClosureStub
    // 0xb3ff98: mov             x1, x0
    // 0xb3ff9c: ldur            x0, [fp, #-8]
    // 0xb3ffa0: StoreField: r1->field_b = r0
    //     0xb3ffa0: stur            w0, [x1, #0xb]
    // 0xb3ffa4: r16 = <double>
    //     0xb3ffa4: ldr             x16, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xb3ffa8: ldur            lr, [fp, #-0x10]
    // 0xb3ffac: stp             lr, x16, [SP, #0x10]
    // 0xb3ffb0: r16 = 0.000000
    //     0xb3ffb0: ldr             x16, [PP, #0x4850]  ; [pp+0x4850] 0
    // 0xb3ffb4: stp             x1, x16, [SP]
    // 0xb3ffb8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xb3ffb8: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xb3ffbc: r0 = fold()
    //     0xb3ffbc: bl              #0x7d8444  ; [dart:_internal] ListIterable::fold
    // 0xb3ffc0: LoadField: d0 = r0->field_7
    //     0xb3ffc0: ldur            d0, [x0, #7]
    // 0xb3ffc4: LeaveFrame
    //     0xb3ffc4: mov             SP, fp
    //     0xb3ffc8: ldp             fp, lr, [SP], #0x10
    // 0xb3ffcc: ret
    //     0xb3ffcc: ret             
    // 0xb3ffd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3ffd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3ffd4: b               #0xb3ff68
  }
  [closure] static double <anonymous closure>(dynamic, double, num) {
    // ** addr: 0xb3ffd8, size: 0x3c
    // 0xb3ffd8: EnterFrame
    //     0xb3ffd8: stp             fp, lr, [SP, #-0x10]!
    //     0xb3ffdc: mov             fp, SP
    // 0xb3ffe0: AllocStack(0x10)
    //     0xb3ffe0: sub             SP, SP, #0x10
    // 0xb3ffe4: CheckStackOverflow
    //     0xb3ffe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3ffe8: cmp             SP, x16
    //     0xb3ffec: b.ls            #0xb4000c
    // 0xb3fff0: ldr             x16, [fp, #0x18]
    // 0xb3fff4: ldr             lr, [fp, #0x10]
    // 0xb3fff8: stp             lr, x16, [SP]
    // 0xb3fffc: r0 = +()
    //     0xb3fffc: bl              #0xebf900  ; [dart:core] _Double::+
    // 0xb40000: LeaveFrame
    //     0xb40000: mov             SP, fp
    //     0xb40004: ldp             fp, lr, [SP], #0x10
    // 0xb40008: ret
    //     0xb40008: ret             
    // 0xb4000c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4000c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb40010: b               #0xb3fff0
  }
  static Map<Y1, List<Y2>> IterableSC.groupBy<Y0, Y1, Y2>(Iterable<Y0>, (dynamic, Y0) => Y1) {
    // ** addr: 0xba7e60, size: 0x138
    // 0xba7e60: EnterFrame
    //     0xba7e60: stp             fp, lr, [SP, #-0x10]!
    //     0xba7e64: mov             fp, SP
    // 0xba7e68: AllocStack(0x20)
    //     0xba7e68: sub             SP, SP, #0x20
    // 0xba7e6c: SetupParameters()
    //     0xba7e6c: ldur            w0, [x4, #0xf]
    //     0xba7e70: cbnz            w0, #0xba7e7c
    //     0xba7e74: mov             x2, NULL
    //     0xba7e78: b               #0xba7e8c
    //     0xba7e7c: ldur            w0, [x4, #0x17]
    //     0xba7e80: add             x1, fp, w0, sxtw #2
    //     0xba7e84: ldr             x1, [x1, #0x10]
    //     0xba7e88: mov             x2, x1
    //     0xba7e8c: ldr             x1, [fp, #0x18]
    //     0xba7e90: ldr             x0, [fp, #0x10]
    //     0xba7e94: stur            x2, [fp, #-8]
    // 0xba7e98: CheckStackOverflow
    //     0xba7e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba7e9c: cmp             SP, x16
    //     0xba7ea0: b.ls            #0xba7f90
    // 0xba7ea4: r1 = 3
    //     0xba7ea4: movz            x1, #0x3
    // 0xba7ea8: r0 = AllocateContext()
    //     0xba7ea8: bl              #0xec126c  ; AllocateContextStub
    // 0xba7eac: mov             x3, x0
    // 0xba7eb0: ldr             x0, [fp, #0x10]
    // 0xba7eb4: stur            x3, [fp, #-0x10]
    // 0xba7eb8: StoreField: r3->field_f = r0
    //     0xba7eb8: stur            w0, [x3, #0xf]
    // 0xba7ebc: r1 = Function '<anonymous closure>': static.
    //     0xba7ebc: add             x1, PP, #0x33, lsl #12  ; [pp+0x33190] AnonymousClosure: static (0xba8114), in [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.groupBy (0xba7e60)
    //     0xba7ec0: ldr             x1, [x1, #0x190]
    // 0xba7ec4: r2 = Null
    //     0xba7ec4: mov             x2, NULL
    // 0xba7ec8: r0 = AllocateClosure()
    //     0xba7ec8: bl              #0xec1630  ; AllocateClosureStub
    // 0xba7ecc: mov             x1, x0
    // 0xba7ed0: ldur            x0, [fp, #-8]
    // 0xba7ed4: StoreField: r1->field_b = r0
    //     0xba7ed4: stur            w0, [x1, #0xb]
    // 0xba7ed8: ldur            x4, [fp, #-0x10]
    // 0xba7edc: StoreField: r4->field_13 = r1
    //     0xba7edc: stur            w1, [x4, #0x13]
    // 0xba7ee0: mov             x1, x0
    // 0xba7ee4: r2 = Null
    //     0xba7ee4: mov             x2, NULL
    // 0xba7ee8: r3 = <Y1, List<Y2>>
    //     0xba7ee8: add             x3, PP, #0x33, lsl #12  ; [pp+0x33198] TypeArguments: <Y1, List<Y2>>
    //     0xba7eec: ldr             x3, [x3, #0x198]
    // 0xba7ef0: r30 = InstantiateTypeArgumentsStub
    //     0xba7ef0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xba7ef4: LoadField: r30 = r30->field_7
    //     0xba7ef4: ldur            lr, [lr, #7]
    // 0xba7ef8: blr             lr
    // 0xba7efc: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0xba7f00: stp             x16, x0, [SP]
    // 0xba7f04: r0 = Map._fromLiteral()
    //     0xba7f04: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xba7f08: ldur            x3, [fp, #-0x10]
    // 0xba7f0c: ArrayStore: r3[0] = r0  ; List_4
    //     0xba7f0c: stur            w0, [x3, #0x17]
    //     0xba7f10: ldurb           w16, [x3, #-1]
    //     0xba7f14: ldurb           w17, [x0, #-1]
    //     0xba7f18: and             x16, x17, x16, lsr #2
    //     0xba7f1c: tst             x16, HEAP, lsr #32
    //     0xba7f20: b.eq            #0xba7f28
    //     0xba7f24: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xba7f28: mov             x2, x3
    // 0xba7f2c: r1 = Function '<anonymous closure>': static.
    //     0xba7f2c: add             x1, PP, #0x33, lsl #12  ; [pp+0x331a0] AnonymousClosure: static (0xba7f98), in [package:supercharged_dart/supercharged_dart.dart] ::IterableSC.groupBy (0xba7e60)
    //     0xba7f30: ldr             x1, [x1, #0x1a0]
    // 0xba7f34: r0 = AllocateClosure()
    //     0xba7f34: bl              #0xec1630  ; AllocateClosureStub
    // 0xba7f38: mov             x1, x0
    // 0xba7f3c: ldur            x0, [fp, #-8]
    // 0xba7f40: StoreField: r1->field_b = r0
    //     0xba7f40: stur            w0, [x1, #0xb]
    // 0xba7f44: ldr             x0, [fp, #0x18]
    // 0xba7f48: r2 = LoadClassIdInstr(r0)
    //     0xba7f48: ldur            x2, [x0, #-1]
    //     0xba7f4c: ubfx            x2, x2, #0xc, #0x14
    // 0xba7f50: mov             x16, x1
    // 0xba7f54: mov             x1, x2
    // 0xba7f58: mov             x2, x16
    // 0xba7f5c: mov             x16, x0
    // 0xba7f60: mov             x0, x1
    // 0xba7f64: mov             x1, x16
    // 0xba7f68: r0 = GDT[cid_x0 + 0xeb9b]()
    //     0xba7f68: movz            x17, #0xeb9b
    //     0xba7f6c: add             lr, x0, x17
    //     0xba7f70: ldr             lr, [x21, lr, lsl #3]
    //     0xba7f74: blr             lr
    // 0xba7f78: ldur            x1, [fp, #-0x10]
    // 0xba7f7c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xba7f7c: ldur            w0, [x1, #0x17]
    // 0xba7f80: DecompressPointer r0
    //     0xba7f80: add             x0, x0, HEAP, lsl #32
    // 0xba7f84: LeaveFrame
    //     0xba7f84: mov             SP, fp
    //     0xba7f88: ldp             fp, lr, [SP], #0x10
    // 0xba7f8c: ret
    //     0xba7f8c: ret             
    // 0xba7f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba7f90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba7f94: b               #0xba7ea4
  }
  [closure] static void <anonymous closure>(dynamic, Y0) {
    // ** addr: 0xba7f98, size: 0x17c
    // 0xba7f98: EnterFrame
    //     0xba7f98: stp             fp, lr, [SP, #-0x10]!
    //     0xba7f9c: mov             fp, SP
    // 0xba7fa0: AllocStack(0x30)
    //     0xba7fa0: sub             SP, SP, #0x30
    // 0xba7fa4: SetupParameters()
    //     0xba7fa4: ldr             x0, [fp, #0x18]
    //     0xba7fa8: ldur            w1, [x0, #0x17]
    //     0xba7fac: add             x1, x1, HEAP, lsl #32
    //     0xba7fb0: stur            x1, [fp, #-0x10]
    // 0xba7fb4: CheckStackOverflow
    //     0xba7fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba7fb8: cmp             SP, x16
    //     0xba7fbc: b.ls            #0xba8108
    // 0xba7fc0: LoadField: r2 = r0->field_b
    //     0xba7fc0: ldur            w2, [x0, #0xb]
    // 0xba7fc4: DecompressPointer r2
    //     0xba7fc4: add             x2, x2, HEAP, lsl #32
    // 0xba7fc8: stur            x2, [fp, #-8]
    // 0xba7fcc: LoadField: r0 = r1->field_f
    //     0xba7fcc: ldur            w0, [x1, #0xf]
    // 0xba7fd0: DecompressPointer r0
    //     0xba7fd0: add             x0, x0, HEAP, lsl #32
    // 0xba7fd4: ldr             x16, [fp, #0x10]
    // 0xba7fd8: stp             x16, x0, [SP]
    // 0xba7fdc: ClosureCall
    //     0xba7fdc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xba7fe0: ldur            x2, [x0, #0x1f]
    //     0xba7fe4: blr             x2
    // 0xba7fe8: mov             x3, x0
    // 0xba7fec: ldur            x0, [fp, #-0x10]
    // 0xba7ff0: stur            x3, [fp, #-0x18]
    // 0xba7ff4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xba7ff4: ldur            w1, [x0, #0x17]
    // 0xba7ff8: DecompressPointer r1
    //     0xba7ff8: add             x1, x1, HEAP, lsl #32
    // 0xba7ffc: mov             x2, x3
    // 0xba8000: r0 = containsKey()
    //     0xba8000: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xba8004: tbz             w0, #4, #0xba8064
    // 0xba8008: ldur            x0, [fp, #-0x10]
    // 0xba800c: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xba800c: ldur            w4, [x0, #0x17]
    // 0xba8010: DecompressPointer r4
    //     0xba8010: add             x4, x4, HEAP, lsl #32
    // 0xba8014: ldur            x1, [fp, #-8]
    // 0xba8018: stur            x4, [fp, #-0x20]
    // 0xba801c: r2 = Null
    //     0xba801c: mov             x2, NULL
    // 0xba8020: r3 = <Y2>
    //     0xba8020: add             x3, PP, #0x33, lsl #12  ; [pp+0x331a8] TypeArguments: <Y2>
    //     0xba8024: ldr             x3, [x3, #0x1a8]
    // 0xba8028: r0 = Null
    //     0xba8028: mov             x0, NULL
    // 0xba802c: cmp             x2, x0
    // 0xba8030: b.ne            #0xba803c
    // 0xba8034: cmp             x1, x0
    // 0xba8038: b.eq            #0xba8048
    // 0xba803c: r30 = InstantiateTypeArgumentsStub
    //     0xba803c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xba8040: LoadField: r30 = r30->field_7
    //     0xba8040: ldur            lr, [lr, #7]
    // 0xba8044: blr             lr
    // 0xba8048: mov             x1, x0
    // 0xba804c: r2 = 0
    //     0xba804c: movz            x2, #0
    // 0xba8050: r0 = _GrowableList()
    //     0xba8050: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xba8054: ldur            x1, [fp, #-0x20]
    // 0xba8058: ldur            x2, [fp, #-0x18]
    // 0xba805c: mov             x3, x0
    // 0xba8060: r0 = []=()
    //     0xba8060: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xba8064: ldur            x0, [fp, #-0x10]
    // 0xba8068: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xba8068: ldur            w3, [x0, #0x17]
    // 0xba806c: DecompressPointer r3
    //     0xba806c: add             x3, x3, HEAP, lsl #32
    // 0xba8070: mov             x1, x3
    // 0xba8074: ldur            x2, [fp, #-0x18]
    // 0xba8078: stur            x3, [fp, #-8]
    // 0xba807c: r0 = _getValueOrData()
    //     0xba807c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xba8080: mov             x1, x0
    // 0xba8084: ldur            x0, [fp, #-8]
    // 0xba8088: LoadField: r2 = r0->field_f
    //     0xba8088: ldur            w2, [x0, #0xf]
    // 0xba808c: DecompressPointer r2
    //     0xba808c: add             x2, x2, HEAP, lsl #32
    // 0xba8090: cmp             w2, w1
    // 0xba8094: b.ne            #0xba809c
    // 0xba8098: r1 = Null
    //     0xba8098: mov             x1, NULL
    // 0xba809c: ldur            x0, [fp, #-0x10]
    // 0xba80a0: stur            x1, [fp, #-8]
    // 0xba80a4: cmp             w1, NULL
    // 0xba80a8: b.eq            #0xba8110
    // 0xba80ac: LoadField: r2 = r0->field_13
    //     0xba80ac: ldur            w2, [x0, #0x13]
    // 0xba80b0: DecompressPointer r2
    //     0xba80b0: add             x2, x2, HEAP, lsl #32
    // 0xba80b4: ldr             x16, [fp, #0x10]
    // 0xba80b8: stp             x16, x2, [SP]
    // 0xba80bc: mov             x0, x2
    // 0xba80c0: ClosureCall
    //     0xba80c0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xba80c4: ldur            x2, [x0, #0x1f]
    //     0xba80c8: blr             x2
    // 0xba80cc: mov             x1, x0
    // 0xba80d0: ldur            x0, [fp, #-8]
    // 0xba80d4: r2 = LoadClassIdInstr(r0)
    //     0xba80d4: ldur            x2, [x0, #-1]
    //     0xba80d8: ubfx            x2, x2, #0xc, #0x14
    // 0xba80dc: stp             x1, x0, [SP]
    // 0xba80e0: mov             x0, x2
    // 0xba80e4: r0 = GDT[cid_x0 + 0x13254]()
    //     0xba80e4: movz            x17, #0x3254
    //     0xba80e8: movk            x17, #0x1, lsl #16
    //     0xba80ec: add             lr, x0, x17
    //     0xba80f0: ldr             lr, [x21, lr, lsl #3]
    //     0xba80f4: blr             lr
    // 0xba80f8: r0 = Null
    //     0xba80f8: mov             x0, NULL
    // 0xba80fc: LeaveFrame
    //     0xba80fc: mov             SP, fp
    //     0xba8100: ldp             fp, lr, [SP], #0x10
    // 0xba8104: ret
    //     0xba8104: ret             
    // 0xba8108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba8108: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba810c: b               #0xba7fc0
    // 0xba8110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba8110: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Y2 <anonymous closure>(dynamic, Y0) {
    // ** addr: 0xba8114, size: 0x54
    // 0xba8114: EnterFrame
    //     0xba8114: stp             fp, lr, [SP, #-0x10]!
    //     0xba8118: mov             fp, SP
    // 0xba811c: ldr             x0, [fp, #0x18]
    // 0xba8120: LoadField: r1 = r0->field_b
    //     0xba8120: ldur            w1, [x0, #0xb]
    // 0xba8124: DecompressPointer r1
    //     0xba8124: add             x1, x1, HEAP, lsl #32
    // 0xba8128: ldr             x0, [fp, #0x10]
    // 0xba812c: r2 = Null
    //     0xba812c: mov             x2, NULL
    // 0xba8130: cmp             w1, NULL
    // 0xba8134: b.eq            #0xba8158
    // 0xba8138: LoadField: r4 = r1->field_1f
    //     0xba8138: ldur            w4, [x1, #0x1f]
    // 0xba813c: DecompressPointer r4
    //     0xba813c: add             x4, x4, HEAP, lsl #32
    // 0xba8140: r8 = Y2
    //     0xba8140: add             x8, PP, #0x33, lsl #12  ; [pp+0x331b0] TypeParameter: Y2
    //     0xba8144: ldr             x8, [x8, #0x1b0]
    // 0xba8148: LoadField: r9 = r4->field_7
    //     0xba8148: ldur            x9, [x4, #7]
    // 0xba814c: r3 = Null
    //     0xba814c: add             x3, PP, #0x33, lsl #12  ; [pp+0x331b8] Null
    //     0xba8150: ldr             x3, [x3, #0x1b8]
    // 0xba8154: blr             x9
    // 0xba8158: ldr             x0, [fp, #0x10]
    // 0xba815c: LeaveFrame
    //     0xba815c: mov             SP, fp
    //     0xba8160: ldp             fp, lr, [SP], #0x10
    // 0xba8164: ret
    //     0xba8164: ret             
  }
}
