// lib: , url: package:synchronized/src/basic_lock.dart

// class id: 1051179, size: 0x8
class :: {
}

// class id: 451, size: 0xc, field offset: 0x8
class BasicLock extends Object
    implements Lock {

  _ synchronized(/* No info */) async {
    // ** addr: 0xaaf038, size: 0x390
    // 0xaaf038: EnterFrame
    //     0xaaf038: stp             fp, lr, [SP, #-0x10]!
    //     0xaaf03c: mov             fp, SP
    // 0xaaf040: AllocStack(0xa8)
    //     0xaaf040: sub             SP, SP, #0xa8
    // 0xaaf044: SetupParameters(BasicLock this /* r1, fp-0x90 */, dynamic _ /* r2, fp-0x88 */)
    //     0xaaf044: stur            NULL, [fp, #-8]
    //     0xaaf048: movz            x0, #0
    //     0xaaf04c: stur            x4, [fp, #-0x98]
    //     0xaaf050: add             x1, fp, w0, sxtw #2
    //     0xaaf054: ldr             x1, [x1, #0x18]
    //     0xaaf058: stur            x1, [fp, #-0x90]
    //     0xaaf05c: add             x2, fp, w0, sxtw #2
    //     0xaaf060: ldr             x2, [x2, #0x10]
    //     0xaaf064: stur            x2, [fp, #-0x88]
    //     0xaaf068: ldur            w0, [x4, #0xf]
    //     0xaaf06c: cbnz            w0, #0xaaf078
    //     0xaaf070: mov             x0, NULL
    //     0xaaf074: b               #0xaaf088
    //     0xaaf078: ldur            w0, [x4, #0x17]
    //     0xaaf07c: add             x3, fp, w0, sxtw #2
    //     0xaaf080: ldr             x3, [x3, #0x10]
    //     0xaaf084: mov             x0, x3
    //     0xaaf088: stur            x0, [fp, #-0x80]
    // 0xaaf08c: CheckStackOverflow
    //     0xaaf08c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaf090: cmp             SP, x16
    //     0xaaf094: b.ls            #0xaaf3c0
    // 0xaaf098: r1 = 3
    //     0xaaf098: movz            x1, #0x3
    // 0xaaf09c: r0 = AllocateContext()
    //     0xaaf09c: bl              #0xec126c  ; AllocateContextStub
    // 0xaaf0a0: mov             x2, x0
    // 0xaaf0a4: ldur            x1, [fp, #-0x90]
    // 0xaaf0a8: stur            x2, [fp, #-0xa0]
    // 0xaaf0ac: StoreField: r2->field_f = r1
    //     0xaaf0ac: stur            w1, [x2, #0xf]
    // 0xaaf0b0: ldur            x0, [fp, #-0x80]
    // 0xaaf0b4: r0 = InitAsync()
    //     0xaaf0b4: bl              #0x661298  ; InitAsyncStub
    // 0xaaf0b8: ldur            x0, [fp, #-0x90]
    // 0xaaf0bc: LoadField: r2 = r0->field_7
    //     0xaaf0bc: ldur            w2, [x0, #7]
    // 0xaaf0c0: DecompressPointer r2
    //     0xaaf0c0: add             x2, x2, HEAP, lsl #32
    // 0xaaf0c4: stur            x2, [fp, #-0x98]
    // 0xaaf0c8: r1 = <void?>
    //     0xaaf0c8: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xaaf0cc: r0 = Completer.sync()
    //     0xaaf0cc: bl              #0x6972a8  ; [dart:async] Completer::Completer.sync
    // 0xaaf0d0: mov             x1, x0
    // 0xaaf0d4: ldur            x2, [fp, #-0xa0]
    // 0xaaf0d8: StoreField: r2->field_13 = r0
    //     0xaaf0d8: stur            w0, [x2, #0x13]
    //     0xaaf0dc: ldurb           w16, [x2, #-1]
    //     0xaaf0e0: ldurb           w17, [x0, #-1]
    //     0xaaf0e4: and             x16, x17, x16, lsr #2
    //     0xaaf0e8: tst             x16, HEAP, lsr #32
    //     0xaaf0ec: b.eq            #0xaaf0f4
    //     0xaaf0f0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xaaf0f4: LoadField: r0 = r1->field_b
    //     0xaaf0f4: ldur            w0, [x1, #0xb]
    // 0xaaf0f8: DecompressPointer r0
    //     0xaaf0f8: add             x0, x0, HEAP, lsl #32
    // 0xaaf0fc: ldur            x1, [fp, #-0x90]
    // 0xaaf100: StoreField: r1->field_7 = r0
    //     0xaaf100: stur            w0, [x1, #7]
    //     0xaaf104: ldurb           w16, [x1, #-1]
    //     0xaaf108: ldurb           w17, [x0, #-1]
    //     0xaaf10c: and             x16, x17, x16, lsr #2
    //     0xaaf110: tst             x16, HEAP, lsr #32
    //     0xaaf114: b.eq            #0xaaf11c
    //     0xaaf118: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaaf11c: ldur            x1, [fp, #-0x98]
    // 0xaaf120: cmp             w1, NULL
    // 0xaaf124: b.eq            #0xaaf130
    // 0xaaf128: mov             x0, x1
    // 0xaaf12c: r0 = Await()
    //     0xaaf12c: bl              #0x661044  ; AwaitStub
    // 0xaaf130: ldur            x16, [fp, #-0x88]
    // 0xaaf134: str             x16, [SP]
    // 0xaaf138: ldur            x0, [fp, #-0x88]
    // 0xaaf13c: ClosureCall
    //     0xaaf13c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xaaf140: ldur            x2, [x0, #0x1f]
    //     0xaaf144: blr             x2
    // 0xaaf148: mov             x3, x0
    // 0xaaf14c: r2 = Null
    //     0xaaf14c: mov             x2, NULL
    // 0xaaf150: r1 = Null
    //     0xaaf150: mov             x1, NULL
    // 0xaaf154: stur            x3, [fp, #-0x88]
    // 0xaaf158: cmp             w0, NULL
    // 0xaaf15c: b.eq            #0xaaf1f4
    // 0xaaf160: branchIfSmi(r0, 0xaaf1f4)
    //     0xaaf160: tbz             w0, #0, #0xaaf1f4
    // 0xaaf164: r3 = LoadClassIdInstr(r0)
    //     0xaaf164: ldur            x3, [x0, #-1]
    //     0xaaf168: ubfx            x3, x3, #0xc, #0x14
    // 0xaaf16c: r17 = 6686
    //     0xaaf16c: movz            x17, #0x1a1e
    // 0xaaf170: cmp             x3, x17
    // 0xaaf174: b.eq            #0xaaf1fc
    // 0xaaf178: r4 = LoadClassIdInstr(r0)
    //     0xaaf178: ldur            x4, [x0, #-1]
    //     0xaaf17c: ubfx            x4, x4, #0xc, #0x14
    // 0xaaf180: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xaaf184: ldr             x3, [x3, #0x18]
    // 0xaaf188: ldr             x3, [x3, x4, lsl #3]
    // 0xaaf18c: LoadField: r3 = r3->field_2b
    //     0xaaf18c: ldur            w3, [x3, #0x2b]
    // 0xaaf190: DecompressPointer r3
    //     0xaaf190: add             x3, x3, HEAP, lsl #32
    // 0xaaf194: cmp             w3, NULL
    // 0xaaf198: b.eq            #0xaaf1f4
    // 0xaaf19c: LoadField: r3 = r3->field_f
    //     0xaaf19c: ldur            w3, [x3, #0xf]
    // 0xaaf1a0: lsr             x3, x3, #3
    // 0xaaf1a4: r17 = 6686
    //     0xaaf1a4: movz            x17, #0x1a1e
    // 0xaaf1a8: cmp             x3, x17
    // 0xaaf1ac: b.eq            #0xaaf1fc
    // 0xaaf1b0: r3 = SubtypeTestCache
    //     0xaaf1b0: add             x3, PP, #0x43, lsl #12  ; [pp+0x430c8] SubtypeTestCache
    //     0xaaf1b4: ldr             x3, [x3, #0xc8]
    // 0xaaf1b8: r30 = Subtype1TestCacheStub
    //     0xaaf1b8: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xaaf1bc: LoadField: r30 = r30->field_7
    //     0xaaf1bc: ldur            lr, [lr, #7]
    // 0xaaf1c0: blr             lr
    // 0xaaf1c4: cmp             w7, NULL
    // 0xaaf1c8: b.eq            #0xaaf1d4
    // 0xaaf1cc: tbnz            w7, #4, #0xaaf1f4
    // 0xaaf1d0: b               #0xaaf1fc
    // 0xaaf1d4: r8 = Future
    //     0xaaf1d4: add             x8, PP, #0x43, lsl #12  ; [pp+0x430d0] Type: Future
    //     0xaaf1d8: ldr             x8, [x8, #0xd0]
    // 0xaaf1dc: r3 = SubtypeTestCache
    //     0xaaf1dc: add             x3, PP, #0x43, lsl #12  ; [pp+0x430d8] SubtypeTestCache
    //     0xaaf1e0: ldr             x3, [x3, #0xd8]
    // 0xaaf1e4: r30 = InstanceOfStub
    //     0xaaf1e4: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xaaf1e8: LoadField: r30 = r30->field_7
    //     0xaaf1e8: ldur            lr, [lr, #7]
    // 0xaaf1ec: blr             lr
    // 0xaaf1f0: b               #0xaaf200
    // 0xaaf1f4: r0 = false
    //     0xaaf1f4: add             x0, NULL, #0x30  ; false
    // 0xaaf1f8: b               #0xaaf200
    // 0xaaf1fc: r0 = true
    //     0xaaf1fc: add             x0, NULL, #0x20  ; true
    // 0xaaf200: tbnz            w0, #4, #0xaaf29c
    // 0xaaf204: ldur            x0, [fp, #-0x88]
    // 0xaaf208: ldur            x1, [fp, #-0x80]
    // 0xaaf20c: r0 = AwaitWithTypeCheck()
    //     0xaaf20c: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xaaf210: stur            x0, [fp, #-0x90]
    // 0xaaf214: ldur            x4, [fp, #-0x80]
    // 0xaaf218: ldur            x3, [fp, #-0xa0]
    // 0xaaf21c: mov             x2, x3
    // 0xaaf220: r1 = Function 'complete':.
    //     0xaaf220: add             x1, PP, #0x43, lsl #12  ; [pp+0x430e0] AnonymousClosure: (0xaaf3e8), in [package:synchronized/src/basic_lock.dart] BasicLock::synchronized (0xaaf038)
    //     0xaaf224: ldr             x1, [x1, #0xe0]
    // 0xaaf228: r0 = AllocateClosure()
    //     0xaaf228: bl              #0xec1630  ; AllocateClosureStub
    // 0xaaf22c: mov             x1, x0
    // 0xaaf230: ldur            x0, [fp, #-0x80]
    // 0xaaf234: StoreField: r1->field_b = r0
    //     0xaaf234: stur            w0, [x1, #0xb]
    // 0xaaf238: mov             x0, x1
    // 0xaaf23c: ldur            x3, [fp, #-0xa0]
    // 0xaaf240: ArrayStore: r3[0] = r0  ; List_4
    //     0xaaf240: stur            w0, [x3, #0x17]
    //     0xaaf244: ldurb           w16, [x3, #-1]
    //     0xaaf248: ldurb           w17, [x0, #-1]
    //     0xaaf24c: and             x16, x17, x16, lsr #2
    //     0xaaf250: tst             x16, HEAP, lsr #32
    //     0xaaf254: b.eq            #0xaaf25c
    //     0xaaf258: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaaf25c: LoadField: r0 = r3->field_f
    //     0xaaf25c: ldur            w0, [x3, #0xf]
    // 0xaaf260: DecompressPointer r0
    //     0xaaf260: add             x0, x0, HEAP, lsl #32
    // 0xaaf264: LoadField: r1 = r0->field_7
    //     0xaaf264: ldur            w1, [x0, #7]
    // 0xaaf268: DecompressPointer r1
    //     0xaaf268: add             x1, x1, HEAP, lsl #32
    // 0xaaf26c: LoadField: r2 = r3->field_13
    //     0xaaf26c: ldur            w2, [x3, #0x13]
    // 0xaaf270: DecompressPointer r2
    //     0xaaf270: add             x2, x2, HEAP, lsl #32
    // 0xaaf274: LoadField: r3 = r2->field_b
    //     0xaaf274: ldur            w3, [x2, #0xb]
    // 0xaaf278: DecompressPointer r3
    //     0xaaf278: add             x3, x3, HEAP, lsl #32
    // 0xaaf27c: cmp             w1, w3
    // 0xaaf280: b.ne            #0xaaf288
    // 0xaaf284: StoreField: r0->field_7 = rNULL
    //     0xaaf284: stur            NULL, [x0, #7]
    // 0xaaf288: mov             x1, x2
    // 0xaaf28c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaaf28c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaaf290: r0 = complete()
    //     0xaaf290: bl              #0xd68d4c  ; [dart:async] _SyncCompleter::complete
    // 0xaaf294: ldur            x0, [fp, #-0x90]
    // 0xaaf298: r0 = ReturnAsync()
    //     0xaaf298: b               #0x6576a4  ; ReturnAsyncStub
    // 0xaaf29c: ldur            x0, [fp, #-0x80]
    // 0xaaf2a0: ldur            x3, [fp, #-0xa0]
    // 0xaaf2a4: mov             x2, x3
    // 0xaaf2a8: r1 = Function 'complete':.
    //     0xaaf2a8: add             x1, PP, #0x43, lsl #12  ; [pp+0x430e0] AnonymousClosure: (0xaaf3e8), in [package:synchronized/src/basic_lock.dart] BasicLock::synchronized (0xaaf038)
    //     0xaaf2ac: ldr             x1, [x1, #0xe0]
    // 0xaaf2b0: r0 = AllocateClosure()
    //     0xaaf2b0: bl              #0xec1630  ; AllocateClosureStub
    // 0xaaf2b4: mov             x1, x0
    // 0xaaf2b8: ldur            x0, [fp, #-0x80]
    // 0xaaf2bc: StoreField: r1->field_b = r0
    //     0xaaf2bc: stur            w0, [x1, #0xb]
    // 0xaaf2c0: mov             x0, x1
    // 0xaaf2c4: ldur            x1, [fp, #-0xa0]
    // 0xaaf2c8: ArrayStore: r1[0] = r0  ; List_4
    //     0xaaf2c8: stur            w0, [x1, #0x17]
    //     0xaaf2cc: ldurb           w16, [x1, #-1]
    //     0xaaf2d0: ldurb           w17, [x0, #-1]
    //     0xaaf2d4: and             x16, x17, x16, lsr #2
    //     0xaaf2d8: tst             x16, HEAP, lsr #32
    //     0xaaf2dc: b.eq            #0xaaf2e4
    //     0xaaf2e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaaf2e4: LoadField: r0 = r1->field_f
    //     0xaaf2e4: ldur            w0, [x1, #0xf]
    // 0xaaf2e8: DecompressPointer r0
    //     0xaaf2e8: add             x0, x0, HEAP, lsl #32
    // 0xaaf2ec: LoadField: r2 = r0->field_7
    //     0xaaf2ec: ldur            w2, [x0, #7]
    // 0xaaf2f0: DecompressPointer r2
    //     0xaaf2f0: add             x2, x2, HEAP, lsl #32
    // 0xaaf2f4: LoadField: r3 = r1->field_13
    //     0xaaf2f4: ldur            w3, [x1, #0x13]
    // 0xaaf2f8: DecompressPointer r3
    //     0xaaf2f8: add             x3, x3, HEAP, lsl #32
    // 0xaaf2fc: LoadField: r1 = r3->field_b
    //     0xaaf2fc: ldur            w1, [x3, #0xb]
    // 0xaaf300: DecompressPointer r1
    //     0xaaf300: add             x1, x1, HEAP, lsl #32
    // 0xaaf304: cmp             w2, w1
    // 0xaaf308: b.ne            #0xaaf310
    // 0xaaf30c: StoreField: r0->field_7 = rNULL
    //     0xaaf30c: stur            NULL, [x0, #7]
    // 0xaaf310: mov             x1, x3
    // 0xaaf314: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaaf314: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaaf318: r0 = complete()
    //     0xaaf318: bl              #0xd68d4c  ; [dart:async] _SyncCompleter::complete
    // 0xaaf31c: ldur            x0, [fp, #-0x88]
    // 0xaaf320: r0 = ReturnAsync()
    //     0xaaf320: b               #0x6576a4  ; ReturnAsyncStub
    // 0xaaf324: sub             SP, fp, #0xa8
    // 0xaaf328: ldur            x2, [fp, #-0x38]
    // 0xaaf32c: mov             x3, x0
    // 0xaaf330: stur            x0, [fp, #-0x80]
    // 0xaaf334: mov             x0, x1
    // 0xaaf338: stur            x1, [fp, #-0x88]
    // 0xaaf33c: r1 = Function 'complete':.
    //     0xaaf33c: add             x1, PP, #0x43, lsl #12  ; [pp+0x430e0] AnonymousClosure: (0xaaf3e8), in [package:synchronized/src/basic_lock.dart] BasicLock::synchronized (0xaaf038)
    //     0xaaf340: ldr             x1, [x1, #0xe0]
    // 0xaaf344: r0 = AllocateClosure()
    //     0xaaf344: bl              #0xec1630  ; AllocateClosureStub
    // 0xaaf348: mov             x1, x0
    // 0xaaf34c: ldur            x0, [fp, #-0x20]
    // 0xaaf350: StoreField: r1->field_b = r0
    //     0xaaf350: stur            w0, [x1, #0xb]
    // 0xaaf354: mov             x0, x1
    // 0xaaf358: ldur            x1, [fp, #-0x38]
    // 0xaaf35c: ArrayStore: r1[0] = r0  ; List_4
    //     0xaaf35c: stur            w0, [x1, #0x17]
    //     0xaaf360: ldurb           w16, [x1, #-1]
    //     0xaaf364: ldurb           w17, [x0, #-1]
    //     0xaaf368: and             x16, x17, x16, lsr #2
    //     0xaaf36c: tst             x16, HEAP, lsr #32
    //     0xaaf370: b.eq            #0xaaf378
    //     0xaaf374: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xaaf378: LoadField: r0 = r1->field_f
    //     0xaaf378: ldur            w0, [x1, #0xf]
    // 0xaaf37c: DecompressPointer r0
    //     0xaaf37c: add             x0, x0, HEAP, lsl #32
    // 0xaaf380: LoadField: r2 = r0->field_7
    //     0xaaf380: ldur            w2, [x0, #7]
    // 0xaaf384: DecompressPointer r2
    //     0xaaf384: add             x2, x2, HEAP, lsl #32
    // 0xaaf388: LoadField: r3 = r1->field_13
    //     0xaaf388: ldur            w3, [x1, #0x13]
    // 0xaaf38c: DecompressPointer r3
    //     0xaaf38c: add             x3, x3, HEAP, lsl #32
    // 0xaaf390: LoadField: r1 = r3->field_b
    //     0xaaf390: ldur            w1, [x3, #0xb]
    // 0xaaf394: DecompressPointer r1
    //     0xaaf394: add             x1, x1, HEAP, lsl #32
    // 0xaaf398: cmp             w2, w1
    // 0xaaf39c: b.ne            #0xaaf3a4
    // 0xaaf3a0: StoreField: r0->field_7 = rNULL
    //     0xaaf3a0: stur            NULL, [x0, #7]
    // 0xaaf3a4: mov             x1, x3
    // 0xaaf3a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaaf3a8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaaf3ac: r0 = complete()
    //     0xaaf3ac: bl              #0xd68d4c  ; [dart:async] _SyncCompleter::complete
    // 0xaaf3b0: ldur            x0, [fp, #-0x80]
    // 0xaaf3b4: ldur            x1, [fp, #-0x88]
    // 0xaaf3b8: r0 = ReThrow()
    //     0xaaf3b8: bl              #0xec048c  ; ReThrowStub
    // 0xaaf3bc: brk             #0
    // 0xaaf3c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaf3c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaf3c4: b               #0xaaf098
  }
  [closure] void complete(dynamic) {
    // ** addr: 0xaaf3e8, size: 0x70
    // 0xaaf3e8: EnterFrame
    //     0xaaf3e8: stp             fp, lr, [SP, #-0x10]!
    //     0xaaf3ec: mov             fp, SP
    // 0xaaf3f0: ldr             x0, [fp, #0x10]
    // 0xaaf3f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaaf3f4: ldur            w1, [x0, #0x17]
    // 0xaaf3f8: DecompressPointer r1
    //     0xaaf3f8: add             x1, x1, HEAP, lsl #32
    // 0xaaf3fc: CheckStackOverflow
    //     0xaaf3fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaf400: cmp             SP, x16
    //     0xaaf404: b.ls            #0xaaf450
    // 0xaaf408: LoadField: r0 = r1->field_f
    //     0xaaf408: ldur            w0, [x1, #0xf]
    // 0xaaf40c: DecompressPointer r0
    //     0xaaf40c: add             x0, x0, HEAP, lsl #32
    // 0xaaf410: LoadField: r2 = r0->field_7
    //     0xaaf410: ldur            w2, [x0, #7]
    // 0xaaf414: DecompressPointer r2
    //     0xaaf414: add             x2, x2, HEAP, lsl #32
    // 0xaaf418: LoadField: r3 = r1->field_13
    //     0xaaf418: ldur            w3, [x1, #0x13]
    // 0xaaf41c: DecompressPointer r3
    //     0xaaf41c: add             x3, x3, HEAP, lsl #32
    // 0xaaf420: LoadField: r1 = r3->field_b
    //     0xaaf420: ldur            w1, [x3, #0xb]
    // 0xaaf424: DecompressPointer r1
    //     0xaaf424: add             x1, x1, HEAP, lsl #32
    // 0xaaf428: cmp             w2, w1
    // 0xaaf42c: b.ne            #0xaaf434
    // 0xaaf430: StoreField: r0->field_7 = rNULL
    //     0xaaf430: stur            NULL, [x0, #7]
    // 0xaaf434: mov             x1, x3
    // 0xaaf438: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaaf438: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaaf43c: r0 = complete()
    //     0xaaf43c: bl              #0xd68d4c  ; [dart:async] _SyncCompleter::complete
    // 0xaaf440: r0 = Null
    //     0xaaf440: mov             x0, NULL
    // 0xaaf444: LeaveFrame
    //     0xaaf444: mov             SP, fp
    //     0xaaf448: ldp             fp, lr, [SP], #0x10
    // 0xaaf44c: ret
    //     0xaaf44c: ret             
    // 0xaaf450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaf450: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaf454: b               #0xaaf408
  }
  _ toString(/* No info */) {
    // ** addr: 0xc416ac, size: 0x70
    // 0xc416ac: EnterFrame
    //     0xc416ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc416b0: mov             fp, SP
    // 0xc416b4: AllocStack(0x10)
    //     0xc416b4: sub             SP, SP, #0x10
    // 0xc416b8: CheckStackOverflow
    //     0xc416b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc416bc: cmp             SP, x16
    //     0xc416c0: b.ls            #0xc41714
    // 0xc416c4: r1 = Null
    //     0xc416c4: mov             x1, NULL
    // 0xc416c8: r2 = 6
    //     0xc416c8: movz            x2, #0x6
    // 0xc416cc: r0 = AllocateArray()
    //     0xc416cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc416d0: stur            x0, [fp, #-8]
    // 0xc416d4: r16 = "Lock["
    //     0xc416d4: add             x16, PP, #0x46, lsl #12  ; [pp+0x46cd0] "Lock["
    //     0xc416d8: ldr             x16, [x16, #0xcd0]
    // 0xc416dc: StoreField: r0->field_f = r16
    //     0xc416dc: stur            w16, [x0, #0xf]
    // 0xc416e0: ldr             x16, [fp, #0x10]
    // 0xc416e4: str             x16, [SP]
    // 0xc416e8: r0 = _getHash()
    //     0xc416e8: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0xc416ec: mov             x1, x0
    // 0xc416f0: ldur            x0, [fp, #-8]
    // 0xc416f4: StoreField: r0->field_13 = r1
    //     0xc416f4: stur            w1, [x0, #0x13]
    // 0xc416f8: r16 = "]"
    //     0xc416f8: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc416fc: ArrayStore: r0[0] = r16  ; List_4
    //     0xc416fc: stur            w16, [x0, #0x17]
    // 0xc41700: str             x0, [SP]
    // 0xc41704: r0 = _interpolate()
    //     0xc41704: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41708: LeaveFrame
    //     0xc41708: mov             SP, fp
    //     0xc4170c: ldp             fp, lr, [SP], #0x10
    // 0xc41710: ret
    //     0xc41710: ret             
    // 0xc41714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41714: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41718: b               #0xc416c4
  }
}
