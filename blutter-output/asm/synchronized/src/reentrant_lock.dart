// lib: , url: package:synchronized/src/reentrant_lock.dart

// class id: 1051180, size: 0x8
class :: {
}

// class id: 449, size: 0xc, field offset: 0x8
class ReentrantLock extends Object
    implements Lock {

  Future<Y0> synchronized<Y0>(ReentrantLock, (dynamic) => FutureOr<Y0>) async {
    // ** addr: 0xaaeec8, size: 0x170
    // 0xaaeec8: EnterFrame
    //     0xaaeec8: stp             fp, lr, [SP, #-0x10]!
    //     0xaaeecc: mov             fp, SP
    // 0xaaeed0: AllocStack(0x40)
    //     0xaaeed0: sub             SP, SP, #0x40
    // 0xaaeed4: SetupParameters(ReentrantLock this /* r1, fp-0x20 */, dynamic _ /* r2, fp-0x18 */)
    //     0xaaeed4: stur            NULL, [fp, #-8]
    //     0xaaeed8: movz            x0, #0
    //     0xaaeedc: add             x1, fp, w0, sxtw #2
    //     0xaaeee0: ldr             x1, [x1, #0x18]
    //     0xaaeee4: stur            x1, [fp, #-0x20]
    //     0xaaeee8: add             x2, fp, w0, sxtw #2
    //     0xaaeeec: ldr             x2, [x2, #0x10]
    //     0xaaeef0: stur            x2, [fp, #-0x18]
    // 0xaaeef4: LoadField: r0 = r4->field_f
    //     0xaaeef4: ldur            w0, [x4, #0xf]
    // 0xaaeef8: cbnz            w0, #0xaaef04
    // 0xaaeefc: r0 = Null
    //     0xaaeefc: mov             x0, NULL
    // 0xaaef00: b               #0xaaef14
    // 0xaaef04: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xaaef04: ldur            w0, [x4, #0x17]
    // 0xaaef08: add             x3, fp, w0, sxtw #2
    // 0xaaef0c: ldr             x3, [x3, #0x10]
    // 0xaaef10: mov             x0, x3
    // 0xaaef14: stur            x0, [fp, #-0x10]
    // 0xaaef18: CheckStackOverflow
    //     0xaaef18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaef1c: cmp             SP, x16
    //     0xaaef20: b.ls            #0xaaf02c
    // 0xaaef24: r1 = 3
    //     0xaaef24: movz            x1, #0x3
    // 0xaaef28: r0 = AllocateContext()
    //     0xaaef28: bl              #0xec126c  ; AllocateContextStub
    // 0xaaef2c: mov             x2, x0
    // 0xaaef30: ldur            x1, [fp, #-0x20]
    // 0xaaef34: stur            x2, [fp, #-0x28]
    // 0xaaef38: StoreField: r2->field_f = r1
    //     0xaaef38: stur            w1, [x2, #0xf]
    // 0xaaef3c: ldur            x0, [fp, #-0x18]
    // 0xaaef40: StoreField: r2->field_13 = r0
    //     0xaaef40: stur            w0, [x2, #0x13]
    // 0xaaef44: ldur            x0, [fp, #-0x10]
    // 0xaaef48: r0 = InitAsync()
    //     0xaaef48: bl              #0x661298  ; InitAsyncStub
    // 0xaaef4c: ldur            x1, [fp, #-0x20]
    // 0xaaef50: r0 = innerLevel()
    //     0xaaef50: bl              #0xaaf458  ; [package:synchronized/src/reentrant_lock.dart] ReentrantLock::innerLevel
    // 0xaaef54: mov             x2, x0
    // 0xaaef58: r0 = BoxInt64Instr(r2)
    //     0xaaef58: sbfiz           x0, x2, #1, #0x1f
    //     0xaaef5c: cmp             x2, x0, asr #1
    //     0xaaef60: b.eq            #0xaaef6c
    //     0xaaef64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaaef68: stur            x2, [x0, #7]
    // 0xaaef6c: ldur            x3, [fp, #-0x28]
    // 0xaaef70: ArrayStore: r3[0] = r0  ; List_4
    //     0xaaef70: stur            w0, [x3, #0x17]
    //     0xaaef74: tbz             w0, #0, #0xaaef90
    //     0xaaef78: ldurb           w16, [x3, #-1]
    //     0xaaef7c: ldurb           w17, [x0, #-1]
    //     0xaaef80: and             x16, x17, x16, lsr #2
    //     0xaaef84: tst             x16, HEAP, lsr #32
    //     0xaaef88: b.eq            #0xaaef90
    //     0xaaef8c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xaaef90: ldur            x0, [fp, #-0x20]
    // 0xaaef94: LoadField: r4 = r0->field_7
    //     0xaaef94: ldur            w4, [x0, #7]
    // 0xaaef98: DecompressPointer r4
    //     0xaaef98: add             x4, x4, HEAP, lsl #32
    // 0xaaef9c: LoadField: r0 = r4->field_b
    //     0xaaef9c: ldur            w0, [x4, #0xb]
    // 0xaaefa0: r1 = LoadInt32Instr(r0)
    //     0xaaefa0: sbfx            x1, x0, #1, #0x1f
    // 0xaaefa4: cmp             x2, x1
    // 0xaaefa8: b.ge            #0xaaf00c
    // 0xaaefac: ldur            x5, [fp, #-0x10]
    // 0xaaefb0: mov             x0, x1
    // 0xaaefb4: mov             x1, x2
    // 0xaaefb8: cmp             x1, x0
    // 0xaaefbc: b.hs            #0xaaf034
    // 0xaaefc0: LoadField: r0 = r4->field_f
    //     0xaaefc0: ldur            w0, [x4, #0xf]
    // 0xaaefc4: DecompressPointer r0
    //     0xaaefc4: add             x0, x0, HEAP, lsl #32
    // 0xaaefc8: ArrayLoad: r4 = r0[r2]  ; Unknown_4
    //     0xaaefc8: add             x16, x0, x2, lsl #2
    //     0xaaefcc: ldur            w4, [x16, #0xf]
    // 0xaaefd0: DecompressPointer r4
    //     0xaaefd0: add             x4, x4, HEAP, lsl #32
    // 0xaaefd4: mov             x2, x3
    // 0xaaefd8: stur            x4, [fp, #-0x18]
    // 0xaaefdc: r1 = Function '<anonymous closure>':.
    //     0xaaefdc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43360] AnonymousClosure: (0xaaf528), in [package:synchronized/src/reentrant_lock.dart] ReentrantLock::synchronized (0xaaeec8)
    //     0xaaefe0: ldr             x1, [x1, #0x360]
    // 0xaaefe4: r0 = AllocateClosure()
    //     0xaaefe4: bl              #0xec1630  ; AllocateClosureStub
    // 0xaaefe8: mov             x1, x0
    // 0xaaefec: ldur            x0, [fp, #-0x10]
    // 0xaaeff0: StoreField: r1->field_b = r0
    //     0xaaeff0: stur            w0, [x1, #0xb]
    // 0xaaeff4: ldur            x16, [fp, #-0x18]
    // 0xaaeff8: stp             x16, x0, [SP, #8]
    // 0xaaeffc: str             x1, [SP]
    // 0xaaf000: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaaf000: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaaf004: r0 = synchronized()
    //     0xaaf004: bl              #0xaaf038  ; [package:synchronized/src/basic_lock.dart] BasicLock::synchronized
    // 0xaaf008: r0 = ReturnAsync()
    //     0xaaf008: b               #0x6576a4  ; ReturnAsyncStub
    // 0xaaf00c: r0 = StateError()
    //     0xaaf00c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xaaf010: mov             x1, x0
    // 0xaaf014: r0 = "This can happen if an inner synchronized block is spawned outside the block it was started from. Make sure the inner synchronized blocks are properly awaited"
    //     0xaaf014: add             x0, PP, #0x43, lsl #12  ; [pp+0x43368] "This can happen if an inner synchronized block is spawned outside the block it was started from. Make sure the inner synchronized blocks are properly awaited"
    //     0xaaf018: ldr             x0, [x0, #0x368]
    // 0xaaf01c: StoreField: r1->field_b = r0
    //     0xaaf01c: stur            w0, [x1, #0xb]
    // 0xaaf020: mov             x0, x1
    // 0xaaf024: r0 = Throw()
    //     0xaaf024: bl              #0xec04b8  ; ThrowStub
    // 0xaaf028: brk             #0
    // 0xaaf02c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaf02c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaf030: b               #0xaaef24
    // 0xaaf034: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaaf034: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ innerLevel(/* No info */) {
    // ** addr: 0xaaf458, size: 0xd0
    // 0xaaf458: EnterFrame
    //     0xaaf458: stp             fp, lr, [SP, #-0x10]!
    //     0xaaf45c: mov             fp, SP
    // 0xaaf460: AllocStack(0x8)
    //     0xaaf460: sub             SP, SP, #8
    // 0xaaf464: SetupParameters(ReentrantLock this /* r1 => r2, fp-0x8 */)
    //     0xaaf464: mov             x2, x1
    //     0xaaf468: stur            x1, [fp, #-8]
    // 0xaaf46c: CheckStackOverflow
    //     0xaaf46c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaf470: cmp             SP, x16
    //     0xaaf474: b.ls            #0xaaf520
    // 0xaaf478: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0xaaf478: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaaf47c: ldr             x0, [x0, #0x7a0]
    //     0xaaf480: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaaf484: cmp             w0, w16
    //     0xaaf488: b.ne            #0xaaf494
    //     0xaaf48c: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0xaaf490: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xaaf494: r1 = LoadClassIdInstr(r0)
    //     0xaaf494: ldur            x1, [x0, #-1]
    //     0xaaf498: ubfx            x1, x1, #0xc, #0x14
    // 0xaaf49c: mov             x16, x0
    // 0xaaf4a0: mov             x0, x1
    // 0xaaf4a4: mov             x1, x16
    // 0xaaf4a8: ldur            x2, [fp, #-8]
    // 0xaaf4ac: r0 = GDT[cid_x0 + -0xfef]()
    //     0xaaf4ac: sub             lr, x0, #0xfef
    //     0xaaf4b0: ldr             lr, [x21, lr, lsl #3]
    //     0xaaf4b4: blr             lr
    // 0xaaf4b8: mov             x3, x0
    // 0xaaf4bc: r2 = Null
    //     0xaaf4bc: mov             x2, NULL
    // 0xaaf4c0: r1 = Null
    //     0xaaf4c0: mov             x1, NULL
    // 0xaaf4c4: stur            x3, [fp, #-8]
    // 0xaaf4c8: branchIfSmi(r0, 0xaaf4f0)
    //     0xaaf4c8: tbz             w0, #0, #0xaaf4f0
    // 0xaaf4cc: r4 = LoadClassIdInstr(r0)
    //     0xaaf4cc: ldur            x4, [x0, #-1]
    //     0xaaf4d0: ubfx            x4, x4, #0xc, #0x14
    // 0xaaf4d4: sub             x4, x4, #0x3c
    // 0xaaf4d8: cmp             x4, #1
    // 0xaaf4dc: b.ls            #0xaaf4f0
    // 0xaaf4e0: r8 = int?
    //     0xaaf4e0: ldr             x8, [PP, #0x39d8]  ; [pp+0x39d8] Type: int?
    // 0xaaf4e4: r3 = Null
    //     0xaaf4e4: add             x3, PP, #0x43, lsl #12  ; [pp+0x433a8] Null
    //     0xaaf4e8: ldr             x3, [x3, #0x3a8]
    // 0xaaf4ec: r0 = int?()
    //     0xaaf4ec: bl              #0xed4d88  ; IsType_int?_Stub
    // 0xaaf4f0: ldur            x1, [fp, #-8]
    // 0xaaf4f4: cmp             w1, NULL
    // 0xaaf4f8: b.ne            #0xaaf504
    // 0xaaf4fc: r0 = 0
    //     0xaaf4fc: movz            x0, #0
    // 0xaaf500: b               #0xaaf514
    // 0xaaf504: r2 = LoadInt32Instr(r1)
    //     0xaaf504: sbfx            x2, x1, #1, #0x1f
    //     0xaaf508: tbz             w1, #0, #0xaaf510
    //     0xaaf50c: ldur            x2, [x1, #7]
    // 0xaaf510: mov             x0, x2
    // 0xaaf514: LeaveFrame
    //     0xaaf514: mov             SP, fp
    //     0xaaf518: ldp             fp, lr, [SP], #0x10
    // 0xaaf51c: ret
    //     0xaaf51c: ret             
    // 0xaaf520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaf520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaf524: b               #0xaaf478
  }
  [closure] Future<Y0> <anonymous closure>(dynamic) async {
    // ** addr: 0xaaf528, size: 0x3a8
    // 0xaaf528: EnterFrame
    //     0xaaf528: stp             fp, lr, [SP, #-0x10]!
    //     0xaaf52c: mov             fp, SP
    // 0xaaf530: AllocStack(0xa8)
    //     0xaaf530: sub             SP, SP, #0xa8
    // 0xaaf534: SetupParameters(ReentrantLock this /* r1, fp-0x80 */)
    //     0xaaf534: stur            NULL, [fp, #-8]
    //     0xaaf538: movz            x0, #0
    //     0xaaf53c: add             x1, fp, w0, sxtw #2
    //     0xaaf540: ldr             x1, [x1, #0x10]
    //     0xaaf544: stur            x1, [fp, #-0x80]
    //     0xaaf548: ldur            w2, [x1, #0x17]
    //     0xaaf54c: add             x2, x2, HEAP, lsl #32
    //     0xaaf550: stur            x2, [fp, #-0x78]
    // 0xaaf554: CheckStackOverflow
    //     0xaaf554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaf558: cmp             SP, x16
    //     0xaaf55c: b.ls            #0xaaf8bc
    // 0xaaf560: LoadField: r3 = r1->field_b
    //     0xaaf560: ldur            w3, [x1, #0xb]
    // 0xaaf564: DecompressPointer r3
    //     0xaaf564: add             x3, x3, HEAP, lsl #32
    // 0xaaf568: mov             x0, x3
    // 0xaaf56c: stur            x3, [fp, #-0x70]
    // 0xaaf570: r0 = InitAsync()
    //     0xaaf570: bl              #0x661298  ; InitAsyncStub
    // 0xaaf574: ldur            x2, [fp, #-0x78]
    // 0xaaf578: LoadField: r0 = r2->field_f
    //     0xaaf578: ldur            w0, [x2, #0xf]
    // 0xaaf57c: DecompressPointer r0
    //     0xaaf57c: add             x0, x0, HEAP, lsl #32
    // 0xaaf580: LoadField: r1 = r0->field_7
    //     0xaaf580: ldur            w1, [x0, #7]
    // 0xaaf584: DecompressPointer r1
    //     0xaaf584: add             x1, x1, HEAP, lsl #32
    // 0xaaf588: stur            x1, [fp, #-0x88]
    // 0xaaf58c: LoadField: r0 = r1->field_7
    //     0xaaf58c: ldur            w0, [x1, #7]
    // 0xaaf590: DecompressPointer r0
    //     0xaaf590: add             x0, x0, HEAP, lsl #32
    // 0xaaf594: stur            x0, [fp, #-0x80]
    // 0xaaf598: r0 = BasicLock()
    //     0xaaf598: bl              #0xaaf9f4  ; AllocateBasicLockStub -> BasicLock (size=0xc)
    // 0xaaf59c: ldur            x2, [fp, #-0x80]
    // 0xaaf5a0: mov             x3, x0
    // 0xaaf5a4: r1 = Null
    //     0xaaf5a4: mov             x1, NULL
    // 0xaaf5a8: stur            x3, [fp, #-0x80]
    // 0xaaf5ac: cmp             w2, NULL
    // 0xaaf5b0: b.eq            #0xaaf5d0
    // 0xaaf5b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaaf5b4: ldur            w4, [x2, #0x17]
    // 0xaaf5b8: DecompressPointer r4
    //     0xaaf5b8: add             x4, x4, HEAP, lsl #32
    // 0xaaf5bc: r8 = X0
    //     0xaaf5bc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xaaf5c0: LoadField: r9 = r4->field_7
    //     0xaaf5c0: ldur            x9, [x4, #7]
    // 0xaaf5c4: r3 = Null
    //     0xaaf5c4: add             x3, PP, #0x43, lsl #12  ; [pp+0x43370] Null
    //     0xaaf5c8: ldr             x3, [x3, #0x370]
    // 0xaaf5cc: blr             x9
    // 0xaaf5d0: ldur            x0, [fp, #-0x88]
    // 0xaaf5d4: LoadField: r1 = r0->field_b
    //     0xaaf5d4: ldur            w1, [x0, #0xb]
    // 0xaaf5d8: LoadField: r2 = r0->field_f
    //     0xaaf5d8: ldur            w2, [x0, #0xf]
    // 0xaaf5dc: DecompressPointer r2
    //     0xaaf5dc: add             x2, x2, HEAP, lsl #32
    // 0xaaf5e0: LoadField: r3 = r2->field_b
    //     0xaaf5e0: ldur            w3, [x2, #0xb]
    // 0xaaf5e4: r2 = LoadInt32Instr(r1)
    //     0xaaf5e4: sbfx            x2, x1, #1, #0x1f
    // 0xaaf5e8: stur            x2, [fp, #-0x90]
    // 0xaaf5ec: r1 = LoadInt32Instr(r3)
    //     0xaaf5ec: sbfx            x1, x3, #1, #0x1f
    // 0xaaf5f0: cmp             x2, x1
    // 0xaaf5f4: b.ne            #0xaaf600
    // 0xaaf5f8: mov             x1, x0
    // 0xaaf5fc: r0 = _growToNextCapacity()
    //     0xaaf5fc: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaaf600: ldur            x0, [fp, #-0x88]
    // 0xaaf604: ldur            x2, [fp, #-0x90]
    // 0xaaf608: add             x1, x2, #1
    // 0xaaf60c: lsl             x3, x1, #1
    // 0xaaf610: StoreField: r0->field_b = r3
    //     0xaaf610: stur            w3, [x0, #0xb]
    // 0xaaf614: LoadField: r1 = r0->field_f
    //     0xaaf614: ldur            w1, [x0, #0xf]
    // 0xaaf618: DecompressPointer r1
    //     0xaaf618: add             x1, x1, HEAP, lsl #32
    // 0xaaf61c: ldur            x0, [fp, #-0x80]
    // 0xaaf620: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaaf620: add             x25, x1, x2, lsl #2
    //     0xaaf624: add             x25, x25, #0xf
    //     0xaaf628: str             w0, [x25]
    //     0xaaf62c: tbz             w0, #0, #0xaaf648
    //     0xaaf630: ldurb           w16, [x1, #-1]
    //     0xaaf634: ldurb           w17, [x0, #-1]
    //     0xaaf638: and             x16, x17, x16, lsr #2
    //     0xaaf63c: tst             x16, HEAP, lsr #32
    //     0xaaf640: b.eq            #0xaaf648
    //     0xaaf644: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xaaf648: ldur            x0, [fp, #-0x78]
    // 0xaaf64c: ldur            x3, [fp, #-0x70]
    // 0xaaf650: mov             x2, x0
    // 0xaaf654: r1 = Function '<anonymous closure>':.
    //     0xaaf654: add             x1, PP, #0x43, lsl #12  ; [pp+0x43380] AnonymousClosure: static (0xaafa00), in [package:rxdart/src/utils/forwarding_stream.dart] ::_forward (0xaafa50)
    //     0xaaf658: ldr             x1, [x1, #0x380]
    // 0xaaf65c: r0 = AllocateClosure()
    //     0xaaf65c: bl              #0xec1630  ; AllocateClosureStub
    // 0xaaf660: mov             x3, x0
    // 0xaaf664: ldur            x0, [fp, #-0x70]
    // 0xaaf668: stur            x3, [fp, #-0x80]
    // 0xaaf66c: StoreField: r3->field_b = r0
    //     0xaaf66c: stur            w0, [x3, #0xb]
    // 0xaaf670: r1 = Null
    //     0xaaf670: mov             x1, NULL
    // 0xaaf674: r2 = 4
    //     0xaaf674: movz            x2, #0x4
    // 0xaaf678: r0 = AllocateArray()
    //     0xaaf678: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaaf67c: mov             x3, x0
    // 0xaaf680: ldur            x2, [fp, #-0x78]
    // 0xaaf684: LoadField: r0 = r2->field_f
    //     0xaaf684: ldur            w0, [x2, #0xf]
    // 0xaaf688: DecompressPointer r0
    //     0xaaf688: add             x0, x0, HEAP, lsl #32
    // 0xaaf68c: StoreField: r3->field_f = r0
    //     0xaaf68c: stur            w0, [x3, #0xf]
    // 0xaaf690: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xaaf690: ldur            w0, [x2, #0x17]
    // 0xaaf694: DecompressPointer r0
    //     0xaaf694: add             x0, x0, HEAP, lsl #32
    // 0xaaf698: r1 = LoadInt32Instr(r0)
    //     0xaaf698: sbfx            x1, x0, #1, #0x1f
    //     0xaaf69c: tbz             w0, #0, #0xaaf6a4
    //     0xaaf6a0: ldur            x1, [x0, #7]
    // 0xaaf6a4: add             x4, x1, #1
    // 0xaaf6a8: r0 = BoxInt64Instr(r4)
    //     0xaaf6a8: sbfiz           x0, x4, #1, #0x1f
    //     0xaaf6ac: cmp             x4, x0, asr #1
    //     0xaaf6b0: b.eq            #0xaaf6bc
    //     0xaaf6b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaaf6b8: stur            x4, [x0, #7]
    // 0xaaf6bc: StoreField: r3->field_13 = r0
    //     0xaaf6bc: stur            w0, [x3, #0x13]
    // 0xaaf6c0: r16 = <Object?, Object?>
    //     0xaaf6c0: ldr             x16, [PP, #0x3f28]  ; [pp+0x3f28] TypeArguments: <Object?, Object?>
    // 0xaaf6c4: stp             x3, x16, [SP]
    // 0xaaf6c8: r0 = Map._fromLiteral()
    //     0xaaf6c8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaaf6cc: ldur            x1, [fp, #-0x70]
    // 0xaaf6d0: r2 = Null
    //     0xaaf6d0: mov             x2, NULL
    // 0xaaf6d4: r3 = <FutureOr<Y0>>
    //     0xaaf6d4: add             x3, PP, #0x43, lsl #12  ; [pp+0x43388] TypeArguments: <FutureOr<Y0>>
    //     0xaaf6d8: ldr             x3, [x3, #0x388]
    // 0xaaf6dc: stur            x0, [fp, #-0x88]
    // 0xaaf6e0: r30 = InstantiateTypeArgumentsStub
    //     0xaaf6e0: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xaaf6e4: LoadField: r30 = r30->field_7
    //     0xaaf6e4: ldur            lr, [lr, #7]
    // 0xaaf6e8: blr             lr
    // 0xaaf6ec: ldur            x16, [fp, #-0x80]
    // 0xaaf6f0: stp             x16, x0, [SP, #8]
    // 0xaaf6f4: ldur            x16, [fp, #-0x88]
    // 0xaaf6f8: str             x16, [SP]
    // 0xaaf6fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaaf6fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaaf700: r0 = runZoned()
    //     0xaaf700: bl              #0xaaf8d0  ; [dart:async] ::runZoned
    // 0xaaf704: mov             x3, x0
    // 0xaaf708: r2 = Null
    //     0xaaf708: mov             x2, NULL
    // 0xaaf70c: r1 = Null
    //     0xaaf70c: mov             x1, NULL
    // 0xaaf710: stur            x3, [fp, #-0x80]
    // 0xaaf714: cmp             w0, NULL
    // 0xaaf718: b.eq            #0xaaf7b0
    // 0xaaf71c: branchIfSmi(r0, 0xaaf7b0)
    //     0xaaf71c: tbz             w0, #0, #0xaaf7b0
    // 0xaaf720: r3 = LoadClassIdInstr(r0)
    //     0xaaf720: ldur            x3, [x0, #-1]
    //     0xaaf724: ubfx            x3, x3, #0xc, #0x14
    // 0xaaf728: r17 = 6686
    //     0xaaf728: movz            x17, #0x1a1e
    // 0xaaf72c: cmp             x3, x17
    // 0xaaf730: b.eq            #0xaaf7b8
    // 0xaaf734: r4 = LoadClassIdInstr(r0)
    //     0xaaf734: ldur            x4, [x0, #-1]
    //     0xaaf738: ubfx            x4, x4, #0xc, #0x14
    // 0xaaf73c: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0xaaf740: ldr             x3, [x3, #0x18]
    // 0xaaf744: ldr             x3, [x3, x4, lsl #3]
    // 0xaaf748: LoadField: r3 = r3->field_2b
    //     0xaaf748: ldur            w3, [x3, #0x2b]
    // 0xaaf74c: DecompressPointer r3
    //     0xaaf74c: add             x3, x3, HEAP, lsl #32
    // 0xaaf750: cmp             w3, NULL
    // 0xaaf754: b.eq            #0xaaf7b0
    // 0xaaf758: LoadField: r3 = r3->field_f
    //     0xaaf758: ldur            w3, [x3, #0xf]
    // 0xaaf75c: lsr             x3, x3, #3
    // 0xaaf760: r17 = 6686
    //     0xaaf760: movz            x17, #0x1a1e
    // 0xaaf764: cmp             x3, x17
    // 0xaaf768: b.eq            #0xaaf7b8
    // 0xaaf76c: r3 = SubtypeTestCache
    //     0xaaf76c: add             x3, PP, #0x43, lsl #12  ; [pp+0x43390] SubtypeTestCache
    //     0xaaf770: ldr             x3, [x3, #0x390]
    // 0xaaf774: r30 = Subtype1TestCacheStub
    //     0xaaf774: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0xaaf778: LoadField: r30 = r30->field_7
    //     0xaaf778: ldur            lr, [lr, #7]
    // 0xaaf77c: blr             lr
    // 0xaaf780: cmp             w7, NULL
    // 0xaaf784: b.eq            #0xaaf790
    // 0xaaf788: tbnz            w7, #4, #0xaaf7b0
    // 0xaaf78c: b               #0xaaf7b8
    // 0xaaf790: r8 = Future
    //     0xaaf790: add             x8, PP, #0x43, lsl #12  ; [pp+0x43398] Type: Future
    //     0xaaf794: ldr             x8, [x8, #0x398]
    // 0xaaf798: r3 = SubtypeTestCache
    //     0xaaf798: add             x3, PP, #0x43, lsl #12  ; [pp+0x433a0] SubtypeTestCache
    //     0xaaf79c: ldr             x3, [x3, #0x3a0]
    // 0xaaf7a0: r30 = InstanceOfStub
    //     0xaaf7a0: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0xaaf7a4: LoadField: r30 = r30->field_7
    //     0xaaf7a4: ldur            lr, [lr, #7]
    // 0xaaf7a8: blr             lr
    // 0xaaf7ac: b               #0xaaf7bc
    // 0xaaf7b0: r0 = false
    //     0xaaf7b0: add             x0, NULL, #0x30  ; false
    // 0xaaf7b4: b               #0xaaf7bc
    // 0xaaf7b8: r0 = true
    //     0xaaf7b8: add             x0, NULL, #0x20  ; true
    // 0xaaf7bc: tbnz            w0, #4, #0xaaf818
    // 0xaaf7c0: ldur            x0, [fp, #-0x80]
    // 0xaaf7c4: ldur            x1, [fp, #-0x70]
    // 0xaaf7c8: r0 = AwaitWithTypeCheck()
    //     0xaaf7c8: bl              #0x6576d0  ; AwaitWithTypeCheckStub
    // 0xaaf7cc: mov             x3, x0
    // 0xaaf7d0: stur            x3, [fp, #-0x70]
    // 0xaaf7d4: ldur            x0, [fp, #-0x78]
    // 0xaaf7d8: LoadField: r1 = r0->field_f
    //     0xaaf7d8: ldur            w1, [x0, #0xf]
    // 0xaaf7dc: DecompressPointer r1
    //     0xaaf7dc: add             x1, x1, HEAP, lsl #32
    // 0xaaf7e0: LoadField: r2 = r1->field_7
    //     0xaaf7e0: ldur            w2, [x1, #7]
    // 0xaaf7e4: DecompressPointer r2
    //     0xaaf7e4: add             x2, x2, HEAP, lsl #32
    // 0xaaf7e8: LoadField: r0 = r2->field_b
    //     0xaaf7e8: ldur            w0, [x2, #0xb]
    // 0xaaf7ec: r1 = LoadInt32Instr(r0)
    //     0xaaf7ec: sbfx            x1, x0, #1, #0x1f
    // 0xaaf7f0: sub             x4, x1, #1
    // 0xaaf7f4: mov             x0, x1
    // 0xaaf7f8: mov             x1, x4
    // 0xaaf7fc: cmp             x1, x0
    // 0xaaf800: b.hs            #0xaaf8c4
    // 0xaaf804: mov             x1, x2
    // 0xaaf808: mov             x2, x4
    // 0xaaf80c: r0 = length=()
    //     0xaaf80c: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xaaf810: ldur            x0, [fp, #-0x70]
    // 0xaaf814: r0 = ReturnAsync()
    //     0xaaf814: b               #0x6576a4  ; ReturnAsyncStub
    // 0xaaf818: ldur            x0, [fp, #-0x78]
    // 0xaaf81c: LoadField: r1 = r0->field_f
    //     0xaaf81c: ldur            w1, [x0, #0xf]
    // 0xaaf820: DecompressPointer r1
    //     0xaaf820: add             x1, x1, HEAP, lsl #32
    // 0xaaf824: LoadField: r2 = r1->field_7
    //     0xaaf824: ldur            w2, [x1, #7]
    // 0xaaf828: DecompressPointer r2
    //     0xaaf828: add             x2, x2, HEAP, lsl #32
    // 0xaaf82c: LoadField: r0 = r2->field_b
    //     0xaaf82c: ldur            w0, [x2, #0xb]
    // 0xaaf830: r1 = LoadInt32Instr(r0)
    //     0xaaf830: sbfx            x1, x0, #1, #0x1f
    // 0xaaf834: sub             x3, x1, #1
    // 0xaaf838: mov             x0, x1
    // 0xaaf83c: mov             x1, x3
    // 0xaaf840: cmp             x1, x0
    // 0xaaf844: b.hs            #0xaaf8c8
    // 0xaaf848: mov             x1, x2
    // 0xaaf84c: mov             x2, x3
    // 0xaaf850: r0 = length=()
    //     0xaaf850: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xaaf854: ldur            x0, [fp, #-0x80]
    // 0xaaf858: r0 = ReturnAsync()
    //     0xaaf858: b               #0x6576a4  ; ReturnAsyncStub
    // 0xaaf85c: sub             SP, fp, #0xa8
    // 0xaaf860: mov             x4, x0
    // 0xaaf864: stur            x0, [fp, #-0x70]
    // 0xaaf868: ldur            x0, [fp, #-0x28]
    // 0xaaf86c: mov             x3, x1
    // 0xaaf870: stur            x1, [fp, #-0x78]
    // 0xaaf874: LoadField: r1 = r0->field_f
    //     0xaaf874: ldur            w1, [x0, #0xf]
    // 0xaaf878: DecompressPointer r1
    //     0xaaf878: add             x1, x1, HEAP, lsl #32
    // 0xaaf87c: LoadField: r2 = r1->field_7
    //     0xaaf87c: ldur            w2, [x1, #7]
    // 0xaaf880: DecompressPointer r2
    //     0xaaf880: add             x2, x2, HEAP, lsl #32
    // 0xaaf884: LoadField: r0 = r2->field_b
    //     0xaaf884: ldur            w0, [x2, #0xb]
    // 0xaaf888: r1 = LoadInt32Instr(r0)
    //     0xaaf888: sbfx            x1, x0, #1, #0x1f
    // 0xaaf88c: sub             x5, x1, #1
    // 0xaaf890: mov             x0, x1
    // 0xaaf894: mov             x1, x5
    // 0xaaf898: cmp             x1, x0
    // 0xaaf89c: b.hs            #0xaaf8cc
    // 0xaaf8a0: mov             x1, x2
    // 0xaaf8a4: mov             x2, x5
    // 0xaaf8a8: r0 = length=()
    //     0xaaf8a8: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0xaaf8ac: ldur            x0, [fp, #-0x70]
    // 0xaaf8b0: ldur            x1, [fp, #-0x78]
    // 0xaaf8b4: r0 = ReThrow()
    //     0xaaf8b4: bl              #0xec048c  ; ReThrowStub
    // 0xaaf8b8: brk             #0
    // 0xaaf8bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaf8bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaf8c0: b               #0xaaf560
    // 0xaaf8c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaaf8c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaaf8c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaaf8c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaaf8cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaaf8cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4171c, size: 0x70
    // 0xc4171c: EnterFrame
    //     0xc4171c: stp             fp, lr, [SP, #-0x10]!
    //     0xc41720: mov             fp, SP
    // 0xc41724: AllocStack(0x10)
    //     0xc41724: sub             SP, SP, #0x10
    // 0xc41728: CheckStackOverflow
    //     0xc41728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4172c: cmp             SP, x16
    //     0xc41730: b.ls            #0xc41784
    // 0xc41734: r1 = Null
    //     0xc41734: mov             x1, NULL
    // 0xc41738: r2 = 6
    //     0xc41738: movz            x2, #0x6
    // 0xc4173c: r0 = AllocateArray()
    //     0xc4173c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41740: stur            x0, [fp, #-8]
    // 0xc41744: r16 = "ReentrantLock["
    //     0xc41744: add             x16, PP, #0x46, lsl #12  ; [pp+0x46cc8] "ReentrantLock["
    //     0xc41748: ldr             x16, [x16, #0xcc8]
    // 0xc4174c: StoreField: r0->field_f = r16
    //     0xc4174c: stur            w16, [x0, #0xf]
    // 0xc41750: ldr             x16, [fp, #0x10]
    // 0xc41754: str             x16, [SP]
    // 0xc41758: r0 = _getHash()
    //     0xc41758: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0xc4175c: mov             x1, x0
    // 0xc41760: ldur            x0, [fp, #-8]
    // 0xc41764: StoreField: r0->field_13 = r1
    //     0xc41764: stur            w1, [x0, #0x13]
    // 0xc41768: r16 = "]"
    //     0xc41768: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc4176c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc4176c: stur            w16, [x0, #0x17]
    // 0xc41770: str             x0, [SP]
    // 0xc41774: r0 = _interpolate()
    //     0xc41774: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41778: LeaveFrame
    //     0xc41778: mov             SP, fp
    //     0xc4177c: ldp             fp, lr, [SP], #0x10
    // 0xc41780: ret
    //     0xc41780: ret             
    // 0xc41784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41788: b               #0xc41734
  }
}
