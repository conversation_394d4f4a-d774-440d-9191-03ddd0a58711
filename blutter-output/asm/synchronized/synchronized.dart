// lib: , url: package:synchronized/synchronized.dart

// class id: 1051181, size: 0x8
class :: {
}

// class id: 450, size: 0x8, field offset: 0x8
abstract class Lock extends Object {

  factory _ Lock(/* No info */) {
    // ** addr: 0xab2288, size: 0xc0
    // 0xab2288: EnterFrame
    //     0xab2288: stp             fp, lr, [SP, #-0x10]!
    //     0xab228c: mov             fp, SP
    // 0xab2290: AllocStack(0x10)
    //     0xab2290: sub             SP, SP, #0x10
    // 0xab2294: SetupParameters({dynamic reentrant = false /* r0 */})
    //     0xab2294: ldur            w0, [x4, #0x13]
    //     0xab2298: ldur            w1, [x4, #0x1f]
    //     0xab229c: add             x1, x1, HEAP, lsl #32
    //     0xab22a0: add             x16, PP, #0x43, lsl #12  ; [pp+0x43350] "reentrant"
    //     0xab22a4: ldr             x16, [x16, #0x350]
    //     0xab22a8: cmp             w1, w16
    //     0xab22ac: b.ne            #0xab22c8
    //     0xab22b0: ldur            w1, [x4, #0x23]
    //     0xab22b4: add             x1, x1, HEAP, lsl #32
    //     0xab22b8: sub             w2, w0, w1
    //     0xab22bc: add             x0, fp, w2, sxtw #2
    //     0xab22c0: ldr             x0, [x0, #8]
    //     0xab22c4: b               #0xab22cc
    //     0xab22c8: add             x0, NULL, #0x30  ; false
    // 0xab22cc: tbnz            w0, #4, #0xab2338
    // 0xab22d0: r0 = BasicLock()
    //     0xab22d0: bl              #0xaaf9f4  ; AllocateBasicLockStub -> BasicLock (size=0xc)
    // 0xab22d4: r1 = Null
    //     0xab22d4: mov             x1, NULL
    // 0xab22d8: r2 = 2
    //     0xab22d8: movz            x2, #0x2
    // 0xab22dc: stur            x0, [fp, #-8]
    // 0xab22e0: r0 = AllocateArray()
    //     0xab22e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xab22e4: mov             x2, x0
    // 0xab22e8: ldur            x0, [fp, #-8]
    // 0xab22ec: stur            x2, [fp, #-0x10]
    // 0xab22f0: StoreField: r2->field_f = r0
    //     0xab22f0: stur            w0, [x2, #0xf]
    // 0xab22f4: r1 = <BasicLock>
    //     0xab22f4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43358] TypeArguments: <BasicLock>
    //     0xab22f8: ldr             x1, [x1, #0x358]
    // 0xab22fc: r0 = AllocateGrowableArray()
    //     0xab22fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xab2300: mov             x1, x0
    // 0xab2304: ldur            x0, [fp, #-0x10]
    // 0xab2308: stur            x1, [fp, #-8]
    // 0xab230c: StoreField: r1->field_f = r0
    //     0xab230c: stur            w0, [x1, #0xf]
    // 0xab2310: r0 = 2
    //     0xab2310: movz            x0, #0x2
    // 0xab2314: StoreField: r1->field_b = r0
    //     0xab2314: stur            w0, [x1, #0xb]
    // 0xab2318: r0 = ReentrantLock()
    //     0xab2318: bl              #0xab2374  ; AllocateReentrantLockStub -> ReentrantLock (size=0xc)
    // 0xab231c: mov             x1, x0
    // 0xab2320: ldur            x0, [fp, #-8]
    // 0xab2324: StoreField: r1->field_7 = r0
    //     0xab2324: stur            w0, [x1, #7]
    // 0xab2328: mov             x0, x1
    // 0xab232c: LeaveFrame
    //     0xab232c: mov             SP, fp
    //     0xab2330: ldp             fp, lr, [SP], #0x10
    // 0xab2334: ret
    //     0xab2334: ret             
    // 0xab2338: r0 = BasicLock()
    //     0xab2338: bl              #0xaaf9f4  ; AllocateBasicLockStub -> BasicLock (size=0xc)
    // 0xab233c: LeaveFrame
    //     0xab233c: mov             SP, fp
    //     0xab2340: ldp             fp, lr, [SP], #0x10
    // 0xab2344: ret
    //     0xab2344: ret             
  }
}
