// lib: , url: package:table_calendar/src/customization/calendar_style.dart

// class id: 1051183, size: 0x8
class :: {
}

// class id: 446, size: 0x18, field offset: 0x8
//   const constructor, 
class PositionedOffset extends Object {
}

// class id: 447, size: 0xc0, field offset: 0x8
//   const constructor, 
class CalendarStyle extends Object {

  _Mint field_8;
  bool field_10;
  bool field_14;
  _Double field_18;
  _Double field_24;
  PositionedOffset field_2c;
  Alignment field_30;
  BoxDecoration field_34;
  EdgeInsets field_38;
  EdgeInsets field_3c;
  EdgeInsets field_40;
  Alignment field_44;
  _Double field_48;
  Color field_50;
  bool field_54;
  bool field_58;
  TextStyle field_5c;
  BoxDecoration field_60;
  TextStyle field_64;
  BoxDecoration field_68;
  TextStyle field_6c;
  BoxDecoration field_70;
  TextStyle field_74;
  BoxDecoration field_78;
  TextStyle field_7c;
  BoxDecoration field_80;
  TextStyle field_84;
  BoxDecoration field_88;
  TextStyle field_8c;
  BoxDecoration field_90;
  TextStyle field_94;
  BoxDecoration field_98;
  TextStyle field_9c;
  BoxDecoration field_a0;
  TextStyle field_a4;
  TextStyle field_a8;
  BoxDecoration field_ac;
  BoxDecoration field_b0;
  TableBorder field_b4;
  EdgeInsets field_b8;
}
