// lib: , url: package:table_calendar/src/customization/header_style.dart

// class id: 1051185, size: 0x8
class :: {
}

// class id: 444, size: 0x54, field offset: 0x8
//   const constructor, 
class HeaderStyle extends Object {

  bool field_8;
  bool field_c;
  bool field_10;
  TextStyle field_18;
  TextStyle field_1c;
  BoxDecoration field_20;
  EdgeInsets field_24;
  EdgeInsets field_28;
  EdgeInsets field_2c;
  EdgeInsets field_30;
  EdgeInsets field_34;
  EdgeInsets field_38;
  EdgeInsets field_3c;
  Icon field_40;
  Icon field_44;
  bool field_48;
  bool field_4c;
  BoxDecoration field_50;
}
