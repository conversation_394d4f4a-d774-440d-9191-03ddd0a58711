// lib: , url: package:table_calendar/src/shared/utils.dart

// class id: 1051186, size: 0x8
class :: {

  static int getWeekdayNumber() {
    // ** addr: 0x981768, size: 0x74
    // 0x981768: r2 = 0
    //     0x981768: movz            x2, #0
    // 0x98176c: r1 = const [Instance of 'StartingDayOfWeek', Instance of 'StartingDayOfWeek', Instance of 'StartingDayOfWeek', Instance of 'StartingDayOfWeek', Instance of 'StartingDayOfWeek', Instance of 'StartingDayOfWeek', Instance of 'StartingDayOfWeek']
    //     0x98176c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e68] List<StartingDayOfWeek>(7)
    //     0x981770: ldr             x1, [x1, #0xe68]
    // 0x981774: CheckStackOverflow
    //     0x981774: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981778: cmp             SP, x16
    //     0x98177c: b.ls            #0x9817c4
    // 0x981780: cmp             x2, #7
    // 0x981784: b.ge            #0x9817b8
    // 0x981788: ArrayLoad: r3 = r1[r2]  ; Unknown_4
    //     0x981788: add             x16, x1, x2, lsl #2
    //     0x98178c: ldur            w3, [x16, #0xf]
    // 0x981790: DecompressPointer r3
    //     0x981790: add             x3, x3, HEAP, lsl #32
    // 0x981794: r16 = Instance_StartingDayOfWeek
    //     0x981794: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ec8] Obj!StartingDayOfWeek@e2e021
    //     0x981798: ldr             x16, [x16, #0xec8]
    // 0x98179c: cmp             w3, w16
    // 0x9817a0: b.eq            #0x9817b0
    // 0x9817a4: add             x0, x2, #1
    // 0x9817a8: mov             x2, x0
    // 0x9817ac: b               #0x981774
    // 0x9817b0: mov             x1, x2
    // 0x9817b4: b               #0x9817bc
    // 0x9817b8: r1 = -1
    //     0x9817b8: movn            x1, #0
    // 0x9817bc: add             x0, x1, #1
    // 0x9817c0: ret
    //     0x9817c0: ret             
    // 0x9817c4: EnterFrame
    //     0x9817c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9817c8: mov             fp, SP
    // 0x9817cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9817cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9817d0: LeaveFrame
    //     0x9817d0: mov             SP, fp
    //     0x9817d4: ldp             fp, lr, [SP], #0x10
    // 0x9817d8: b               #0x981780
  }
  static _ isSameDay(/* No info */) {
    // ** addr: 0xa4fa74, size: 0x14c
    // 0xa4fa74: EnterFrame
    //     0xa4fa74: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fa78: mov             fp, SP
    // 0xa4fa7c: AllocStack(0x18)
    //     0xa4fa7c: sub             SP, SP, #0x18
    // 0xa4fa80: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa4fa80: mov             x3, x1
    //     0xa4fa84: stur            x1, [fp, #-8]
    //     0xa4fa88: stur            x2, [fp, #-0x10]
    // 0xa4fa8c: CheckStackOverflow
    //     0xa4fa8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fa90: cmp             SP, x16
    //     0xa4fa94: b.ls            #0xa4fbb8
    // 0xa4fa98: cmp             w2, NULL
    // 0xa4fa9c: b.ne            #0xa4fab0
    // 0xa4faa0: r0 = false
    //     0xa4faa0: add             x0, NULL, #0x30  ; false
    // 0xa4faa4: LeaveFrame
    //     0xa4faa4: mov             SP, fp
    //     0xa4faa8: ldp             fp, lr, [SP], #0x10
    // 0xa4faac: ret
    //     0xa4faac: ret             
    // 0xa4fab0: r0 = LoadClassIdInstr(r3)
    //     0xa4fab0: ldur            x0, [x3, #-1]
    //     0xa4fab4: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fab8: mov             x1, x3
    // 0xa4fabc: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa4fabc: sub             lr, x0, #0xff6
    //     0xa4fac0: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fac4: blr             lr
    // 0xa4fac8: mov             x3, x0
    // 0xa4facc: ldur            x2, [fp, #-0x10]
    // 0xa4fad0: stur            x3, [fp, #-0x18]
    // 0xa4fad4: r0 = LoadClassIdInstr(r2)
    //     0xa4fad4: ldur            x0, [x2, #-1]
    //     0xa4fad8: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fadc: mov             x1, x2
    // 0xa4fae0: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa4fae0: sub             lr, x0, #0xff6
    //     0xa4fae4: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fae8: blr             lr
    // 0xa4faec: mov             x1, x0
    // 0xa4faf0: ldur            x0, [fp, #-0x18]
    // 0xa4faf4: cmp             x0, x1
    // 0xa4faf8: b.ne            #0xa4fba8
    // 0xa4fafc: ldur            x3, [fp, #-8]
    // 0xa4fb00: ldur            x2, [fp, #-0x10]
    // 0xa4fb04: r0 = LoadClassIdInstr(r3)
    //     0xa4fb04: ldur            x0, [x3, #-1]
    //     0xa4fb08: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fb0c: mov             x1, x3
    // 0xa4fb10: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4fb10: sub             lr, x0, #0xfff
    //     0xa4fb14: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fb18: blr             lr
    // 0xa4fb1c: mov             x3, x0
    // 0xa4fb20: ldur            x2, [fp, #-0x10]
    // 0xa4fb24: stur            x3, [fp, #-0x18]
    // 0xa4fb28: r0 = LoadClassIdInstr(r2)
    //     0xa4fb28: ldur            x0, [x2, #-1]
    //     0xa4fb2c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fb30: mov             x1, x2
    // 0xa4fb34: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4fb34: sub             lr, x0, #0xfff
    //     0xa4fb38: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fb3c: blr             lr
    // 0xa4fb40: mov             x1, x0
    // 0xa4fb44: ldur            x0, [fp, #-0x18]
    // 0xa4fb48: cmp             x0, x1
    // 0xa4fb4c: b.ne            #0xa4fba8
    // 0xa4fb50: ldur            x1, [fp, #-8]
    // 0xa4fb54: ldur            x2, [fp, #-0x10]
    // 0xa4fb58: r0 = LoadClassIdInstr(r1)
    //     0xa4fb58: ldur            x0, [x1, #-1]
    //     0xa4fb5c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fb60: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xa4fb60: sub             lr, x0, #0xfdf
    //     0xa4fb64: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fb68: blr             lr
    // 0xa4fb6c: mov             x2, x0
    // 0xa4fb70: ldur            x1, [fp, #-0x10]
    // 0xa4fb74: stur            x2, [fp, #-0x18]
    // 0xa4fb78: r0 = LoadClassIdInstr(r1)
    //     0xa4fb78: ldur            x0, [x1, #-1]
    //     0xa4fb7c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fb80: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xa4fb80: sub             lr, x0, #0xfdf
    //     0xa4fb84: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fb88: blr             lr
    // 0xa4fb8c: ldur            x1, [fp, #-0x18]
    // 0xa4fb90: cmp             x1, x0
    // 0xa4fb94: r16 = true
    //     0xa4fb94: add             x16, NULL, #0x20  ; true
    // 0xa4fb98: r17 = false
    //     0xa4fb98: add             x17, NULL, #0x30  ; false
    // 0xa4fb9c: csel            x2, x16, x17, eq
    // 0xa4fba0: mov             x0, x2
    // 0xa4fba4: b               #0xa4fbac
    // 0xa4fba8: r0 = false
    //     0xa4fba8: add             x0, NULL, #0x30  ; false
    // 0xa4fbac: LeaveFrame
    //     0xa4fbac: mov             SP, fp
    //     0xa4fbb0: ldp             fp, lr, [SP], #0x10
    // 0xa4fbb4: ret
    //     0xa4fbb4: ret             
    // 0xa4fbb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4fbb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4fbbc: b               #0xa4fa98
  }
  static _ normalizeDate(/* No info */) {
    // ** addr: 0xa50a10, size: 0xf8
    // 0xa50a10: EnterFrame
    //     0xa50a10: stp             fp, lr, [SP, #-0x10]!
    //     0xa50a14: mov             fp, SP
    // 0xa50a18: AllocStack(0x38)
    //     0xa50a18: sub             SP, SP, #0x38
    // 0xa50a1c: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0xa50a1c: mov             x2, x1
    //     0xa50a20: stur            x1, [fp, #-8]
    // 0xa50a24: CheckStackOverflow
    //     0xa50a24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50a28: cmp             SP, x16
    //     0xa50a2c: b.ls            #0xa50b00
    // 0xa50a30: r0 = LoadClassIdInstr(r2)
    //     0xa50a30: ldur            x0, [x2, #-1]
    //     0xa50a34: ubfx            x0, x0, #0xc, #0x14
    // 0xa50a38: mov             x1, x2
    // 0xa50a3c: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa50a3c: sub             lr, x0, #0xff6
    //     0xa50a40: ldr             lr, [x21, lr, lsl #3]
    //     0xa50a44: blr             lr
    // 0xa50a48: mov             x3, x0
    // 0xa50a4c: ldur            x2, [fp, #-8]
    // 0xa50a50: stur            x3, [fp, #-0x10]
    // 0xa50a54: r0 = LoadClassIdInstr(r2)
    //     0xa50a54: ldur            x0, [x2, #-1]
    //     0xa50a58: ubfx            x0, x0, #0xc, #0x14
    // 0xa50a5c: mov             x1, x2
    // 0xa50a60: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa50a60: sub             lr, x0, #0xfff
    //     0xa50a64: ldr             lr, [x21, lr, lsl #3]
    //     0xa50a68: blr             lr
    // 0xa50a6c: mov             x2, x0
    // 0xa50a70: ldur            x1, [fp, #-8]
    // 0xa50a74: stur            x2, [fp, #-0x18]
    // 0xa50a78: r0 = LoadClassIdInstr(r1)
    //     0xa50a78: ldur            x0, [x1, #-1]
    //     0xa50a7c: ubfx            x0, x0, #0xc, #0x14
    // 0xa50a80: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xa50a80: sub             lr, x0, #0xfdf
    //     0xa50a84: ldr             lr, [x21, lr, lsl #3]
    //     0xa50a88: blr             lr
    // 0xa50a8c: mov             x3, x0
    // 0xa50a90: ldur            x2, [fp, #-0x18]
    // 0xa50a94: r0 = BoxInt64Instr(r2)
    //     0xa50a94: sbfiz           x0, x2, #1, #0x1f
    //     0xa50a98: cmp             x2, x0, asr #1
    //     0xa50a9c: b.eq            #0xa50aa8
    //     0xa50aa0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa50aa4: stur            x2, [x0, #7]
    // 0xa50aa8: mov             x2, x0
    // 0xa50aac: stur            x2, [fp, #-0x20]
    // 0xa50ab0: r0 = BoxInt64Instr(r3)
    //     0xa50ab0: sbfiz           x0, x3, #1, #0x1f
    //     0xa50ab4: cmp             x3, x0, asr #1
    //     0xa50ab8: b.eq            #0xa50ac4
    //     0xa50abc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa50ac0: stur            x3, [x0, #7]
    // 0xa50ac4: stur            x0, [fp, #-8]
    // 0xa50ac8: r0 = DateTime()
    //     0xa50ac8: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xa50acc: stur            x0, [fp, #-0x28]
    // 0xa50ad0: ldur            x16, [fp, #-0x20]
    // 0xa50ad4: ldur            lr, [fp, #-8]
    // 0xa50ad8: stp             lr, x16, [SP]
    // 0xa50adc: mov             x1, x0
    // 0xa50ae0: ldur            x2, [fp, #-0x10]
    // 0xa50ae4: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xa50ae4: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xa50ae8: ldr             x4, [x4, #0xe00]
    // 0xa50aec: r0 = DateTime.utc()
    //     0xa50aec: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xa50af0: ldur            x0, [fp, #-0x28]
    // 0xa50af4: LeaveFrame
    //     0xa50af4: mov             SP, fp
    //     0xa50af8: ldp             fp, lr, [SP], #0x10
    // 0xa50afc: ret
    //     0xa50afc: ret             
    // 0xa50b00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50b00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50b04: b               #0xa50a30
  }
}

// class id: 6767, size: 0x14, field offset: 0x14
enum StartingDayOfWeek extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4eae0, size: 0x64
    // 0xc4eae0: EnterFrame
    //     0xc4eae0: stp             fp, lr, [SP, #-0x10]!
    //     0xc4eae4: mov             fp, SP
    // 0xc4eae8: AllocStack(0x10)
    //     0xc4eae8: sub             SP, SP, #0x10
    // 0xc4eaec: SetupParameters(StartingDayOfWeek this /* r1 => r0, fp-0x8 */)
    //     0xc4eaec: mov             x0, x1
    //     0xc4eaf0: stur            x1, [fp, #-8]
    // 0xc4eaf4: CheckStackOverflow
    //     0xc4eaf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4eaf8: cmp             SP, x16
    //     0xc4eafc: b.ls            #0xc4eb3c
    // 0xc4eb00: r1 = Null
    //     0xc4eb00: mov             x1, NULL
    // 0xc4eb04: r2 = 4
    //     0xc4eb04: movz            x2, #0x4
    // 0xc4eb08: r0 = AllocateArray()
    //     0xc4eb08: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4eb0c: r16 = "StartingDayOfWeek."
    //     0xc4eb0c: add             x16, PP, #0x40, lsl #12  ; [pp+0x406b0] "StartingDayOfWeek."
    //     0xc4eb10: ldr             x16, [x16, #0x6b0]
    // 0xc4eb14: StoreField: r0->field_f = r16
    //     0xc4eb14: stur            w16, [x0, #0xf]
    // 0xc4eb18: ldur            x1, [fp, #-8]
    // 0xc4eb1c: LoadField: r2 = r1->field_f
    //     0xc4eb1c: ldur            w2, [x1, #0xf]
    // 0xc4eb20: DecompressPointer r2
    //     0xc4eb20: add             x2, x2, HEAP, lsl #32
    // 0xc4eb24: StoreField: r0->field_13 = r2
    //     0xc4eb24: stur            w2, [x0, #0x13]
    // 0xc4eb28: str             x0, [SP]
    // 0xc4eb2c: r0 = _interpolate()
    //     0xc4eb2c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4eb30: LeaveFrame
    //     0xc4eb30: mov             SP, fp
    //     0xc4eb34: ldp             fp, lr, [SP], #0x10
    // 0xc4eb38: ret
    //     0xc4eb38: ret             
    // 0xc4eb3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4eb3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4eb40: b               #0xc4eb00
  }
}

// class id: 6768, size: 0x14, field offset: 0x14
enum CalendarFormat extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ea7c, size: 0x64
    // 0xc4ea7c: EnterFrame
    //     0xc4ea7c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ea80: mov             fp, SP
    // 0xc4ea84: AllocStack(0x10)
    //     0xc4ea84: sub             SP, SP, #0x10
    // 0xc4ea88: SetupParameters(CalendarFormat this /* r1 => r0, fp-0x8 */)
    //     0xc4ea88: mov             x0, x1
    //     0xc4ea8c: stur            x1, [fp, #-8]
    // 0xc4ea90: CheckStackOverflow
    //     0xc4ea90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ea94: cmp             SP, x16
    //     0xc4ea98: b.ls            #0xc4ead8
    // 0xc4ea9c: r1 = Null
    //     0xc4ea9c: mov             x1, NULL
    // 0xc4eaa0: r2 = 4
    //     0xc4eaa0: movz            x2, #0x4
    // 0xc4eaa4: r0 = AllocateArray()
    //     0xc4eaa4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4eaa8: r16 = "CalendarFormat."
    //     0xc4eaa8: add             x16, PP, #0x40, lsl #12  ; [pp+0x406a0] "CalendarFormat."
    //     0xc4eaac: ldr             x16, [x16, #0x6a0]
    // 0xc4eab0: StoreField: r0->field_f = r16
    //     0xc4eab0: stur            w16, [x0, #0xf]
    // 0xc4eab4: ldur            x1, [fp, #-8]
    // 0xc4eab8: LoadField: r2 = r1->field_f
    //     0xc4eab8: ldur            w2, [x1, #0xf]
    // 0xc4eabc: DecompressPointer r2
    //     0xc4eabc: add             x2, x2, HEAP, lsl #32
    // 0xc4eac0: StoreField: r0->field_13 = r2
    //     0xc4eac0: stur            w2, [x0, #0x13]
    // 0xc4eac4: str             x0, [SP]
    // 0xc4eac8: r0 = _interpolate()
    //     0xc4eac8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4eacc: LeaveFrame
    //     0xc4eacc: mov             SP, fp
    //     0xc4ead0: ldp             fp, lr, [SP], #0x10
    // 0xc4ead4: ret
    //     0xc4ead4: ret             
    // 0xc4ead8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ead8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4eadc: b               #0xc4ea9c
  }
}

// class id: 6769, size: 0x14, field offset: 0x14
enum AvailableGestures extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ea18, size: 0x64
    // 0xc4ea18: EnterFrame
    //     0xc4ea18: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ea1c: mov             fp, SP
    // 0xc4ea20: AllocStack(0x10)
    //     0xc4ea20: sub             SP, SP, #0x10
    // 0xc4ea24: SetupParameters(AvailableGestures this /* r1 => r0, fp-0x8 */)
    //     0xc4ea24: mov             x0, x1
    //     0xc4ea28: stur            x1, [fp, #-8]
    // 0xc4ea2c: CheckStackOverflow
    //     0xc4ea2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ea30: cmp             SP, x16
    //     0xc4ea34: b.ls            #0xc4ea74
    // 0xc4ea38: r1 = Null
    //     0xc4ea38: mov             x1, NULL
    // 0xc4ea3c: r2 = 4
    //     0xc4ea3c: movz            x2, #0x4
    // 0xc4ea40: r0 = AllocateArray()
    //     0xc4ea40: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ea44: r16 = "AvailableGestures."
    //     0xc4ea44: add             x16, PP, #0x40, lsl #12  ; [pp+0x406a8] "AvailableGestures."
    //     0xc4ea48: ldr             x16, [x16, #0x6a8]
    // 0xc4ea4c: StoreField: r0->field_f = r16
    //     0xc4ea4c: stur            w16, [x0, #0xf]
    // 0xc4ea50: ldur            x1, [fp, #-8]
    // 0xc4ea54: LoadField: r2 = r1->field_f
    //     0xc4ea54: ldur            w2, [x1, #0xf]
    // 0xc4ea58: DecompressPointer r2
    //     0xc4ea58: add             x2, x2, HEAP, lsl #32
    // 0xc4ea5c: StoreField: r0->field_13 = r2
    //     0xc4ea5c: stur            w2, [x0, #0x13]
    // 0xc4ea60: str             x0, [SP]
    // 0xc4ea64: r0 = _interpolate()
    //     0xc4ea64: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ea68: LeaveFrame
    //     0xc4ea68: mov             SP, fp
    //     0xc4ea6c: ldp             fp, lr, [SP], #0x10
    // 0xc4ea70: ret
    //     0xc4ea70: ret             
    // 0xc4ea74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ea74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ea78: b               #0xc4ea38
  }
}
