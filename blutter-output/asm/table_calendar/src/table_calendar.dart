// lib: , url: package:table_calendar/src/table_calendar.dart

// class id: 1051187, size: 0x8
class :: {
}

// class id: 4081, size: 0x24, field offset: 0x14
class _TableCalendarState<C1X0> extends State<C1X0> {

  late final ValueNotifier<DateTime> _focusedDay; // offset: 0x18
  late RangeSelectionMode _rangeSelectionMode; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x980f24, size: 0x114
    // 0x980f24: EnterFrame
    //     0x980f24: stp             fp, lr, [SP, #-0x10]!
    //     0x980f28: mov             fp, SP
    // 0x980f2c: AllocStack(0x20)
    //     0x980f2c: sub             SP, SP, #0x20
    // 0x980f30: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r0, fp-0x10 */)
    //     0x980f30: mov             x0, x1
    //     0x980f34: stur            x1, [fp, #-0x10]
    // 0x980f38: CheckStackOverflow
    //     0x980f38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x980f3c: cmp             SP, x16
    //     0x980f40: b.ls            #0x981028
    // 0x980f44: LoadField: r1 = r0->field_b
    //     0x980f44: ldur            w1, [x0, #0xb]
    // 0x980f48: DecompressPointer r1
    //     0x980f48: add             x1, x1, HEAP, lsl #32
    // 0x980f4c: cmp             w1, NULL
    // 0x980f50: b.eq            #0x981030
    // 0x980f54: LoadField: r2 = r1->field_1b
    //     0x980f54: ldur            w2, [x1, #0x1b]
    // 0x980f58: DecompressPointer r2
    //     0x980f58: add             x2, x2, HEAP, lsl #32
    // 0x980f5c: stur            x2, [fp, #-8]
    // 0x980f60: r1 = <DateTime>
    //     0x980f60: add             x1, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0x980f64: ldr             x1, [x1, #0xdd8]
    // 0x980f68: r0 = ValueNotifier()
    //     0x980f68: bl              #0x65a810  ; AllocateValueNotifierStub -> ValueNotifier<X0> (size=0x2c)
    // 0x980f6c: mov             x1, x0
    // 0x980f70: ldur            x0, [fp, #-8]
    // 0x980f74: stur            x1, [fp, #-0x18]
    // 0x980f78: StoreField: r1->field_27 = r0
    //     0x980f78: stur            w0, [x1, #0x27]
    // 0x980f7c: StoreField: r1->field_7 = rZR
    //     0x980f7c: stur            xzr, [x1, #7]
    // 0x980f80: StoreField: r1->field_13 = rZR
    //     0x980f80: stur            xzr, [x1, #0x13]
    // 0x980f84: StoreField: r1->field_1b = rZR
    //     0x980f84: stur            xzr, [x1, #0x1b]
    // 0x980f88: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x980f88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x980f8c: ldr             x0, [x0, #0xca8]
    //     0x980f90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x980f94: cmp             w0, w16
    //     0x980f98: b.ne            #0x980fa4
    //     0x980f9c: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x980fa0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x980fa4: mov             x1, x0
    // 0x980fa8: ldur            x0, [fp, #-0x18]
    // 0x980fac: StoreField: r0->field_f = r1
    //     0x980fac: stur            w1, [x0, #0xf]
    // 0x980fb0: ldur            x1, [fp, #-0x10]
    // 0x980fb4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x980fb4: ldur            w2, [x1, #0x17]
    // 0x980fb8: DecompressPointer r2
    //     0x980fb8: add             x2, x2, HEAP, lsl #32
    // 0x980fbc: r16 = Sentinel
    //     0x980fbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x980fc0: cmp             w2, w16
    // 0x980fc4: b.eq            #0x980fdc
    // 0x980fc8: r16 = "_focusedDay@1928116939"
    //     0x980fc8: add             x16, PP, #0x47, lsl #12  ; [pp+0x47ed0] "_focusedDay@1928116939"
    //     0x980fcc: ldr             x16, [x16, #0xed0]
    // 0x980fd0: str             x16, [SP]
    // 0x980fd4: r0 = _throwFieldAlreadyInitialized()
    //     0x980fd4: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x980fd8: ldur            x1, [fp, #-0x10]
    // 0x980fdc: r2 = Instance_RangeSelectionMode
    //     0x980fdc: add             x2, PP, #0x35, lsl #12  ; [pp+0x35ef8] Obj!RangeSelectionMode@e2df41
    //     0x980fe0: ldr             x2, [x2, #0xef8]
    // 0x980fe4: ldur            x0, [fp, #-0x18]
    // 0x980fe8: ArrayStore: r1[0] = r0  ; List_4
    //     0x980fe8: stur            w0, [x1, #0x17]
    //     0x980fec: ldurb           w16, [x1, #-1]
    //     0x980ff0: ldurb           w17, [x0, #-1]
    //     0x980ff4: and             x16, x17, x16, lsr #2
    //     0x980ff8: tst             x16, HEAP, lsr #32
    //     0x980ffc: b.eq            #0x981004
    //     0x981000: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x981004: LoadField: r3 = r1->field_b
    //     0x981004: ldur            w3, [x1, #0xb]
    // 0x981008: DecompressPointer r3
    //     0x981008: add             x3, x3, HEAP, lsl #32
    // 0x98100c: cmp             w3, NULL
    // 0x981010: b.eq            #0x981034
    // 0x981014: StoreField: r1->field_1b = r2
    //     0x981014: stur            w2, [x1, #0x1b]
    // 0x981018: r0 = Null
    //     0x981018: mov             x0, NULL
    // 0x98101c: LeaveFrame
    //     0x98101c: mov             SP, fp
    //     0x981020: ldp             fp, lr, [SP], #0x10
    // 0x981024: ret
    //     0x981024: ret             
    // 0x981028: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981028: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98102c: b               #0x980f44
    // 0x981030: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x981030: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x981034: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x981034: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _lastDayOfMonth(/* No info */) {
    // ** addr: 0x9817dc, size: 0x118
    // 0x9817dc: EnterFrame
    //     0x9817dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9817e0: mov             fp, SP
    // 0x9817e4: AllocStack(0x28)
    //     0x9817e4: sub             SP, SP, #0x28
    // 0x9817e8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x9817e8: stur            x2, [fp, #-8]
    // 0x9817ec: CheckStackOverflow
    //     0x9817ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9817f0: cmp             SP, x16
    //     0x9817f4: b.ls            #0x9818ec
    // 0x9817f8: r0 = LoadClassIdInstr(r2)
    //     0x9817f8: ldur            x0, [x2, #-1]
    //     0x9817fc: ubfx            x0, x0, #0xc, #0x14
    // 0x981800: mov             x1, x2
    // 0x981804: r0 = GDT[cid_x0 + -0xfff]()
    //     0x981804: sub             lr, x0, #0xfff
    //     0x981808: ldr             lr, [x21, lr, lsl #3]
    //     0x98180c: blr             lr
    // 0x981810: cmp             x0, #0xc
    // 0x981814: b.ge            #0x981898
    // 0x981818: ldur            x2, [fp, #-8]
    // 0x98181c: r0 = LoadClassIdInstr(r2)
    //     0x98181c: ldur            x0, [x2, #-1]
    //     0x981820: ubfx            x0, x0, #0xc, #0x14
    // 0x981824: mov             x1, x2
    // 0x981828: r0 = GDT[cid_x0 + -0xff6]()
    //     0x981828: sub             lr, x0, #0xff6
    //     0x98182c: ldr             lr, [x21, lr, lsl #3]
    //     0x981830: blr             lr
    // 0x981834: mov             x2, x0
    // 0x981838: ldur            x1, [fp, #-8]
    // 0x98183c: stur            x2, [fp, #-0x10]
    // 0x981840: r0 = LoadClassIdInstr(r1)
    //     0x981840: ldur            x0, [x1, #-1]
    //     0x981844: ubfx            x0, x0, #0xc, #0x14
    // 0x981848: r0 = GDT[cid_x0 + -0xfff]()
    //     0x981848: sub             lr, x0, #0xfff
    //     0x98184c: ldr             lr, [x21, lr, lsl #3]
    //     0x981850: blr             lr
    // 0x981854: add             x2, x0, #1
    // 0x981858: r0 = BoxInt64Instr(r2)
    //     0x981858: sbfiz           x0, x2, #1, #0x1f
    //     0x98185c: cmp             x2, x0, asr #1
    //     0x981860: b.eq            #0x98186c
    //     0x981864: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x981868: stur            x2, [x0, #7]
    // 0x98186c: stur            x0, [fp, #-0x18]
    // 0x981870: r0 = DateTime()
    //     0x981870: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x981874: stur            x0, [fp, #-0x20]
    // 0x981878: ldur            x16, [fp, #-0x18]
    // 0x98187c: str             x16, [SP]
    // 0x981880: mov             x1, x0
    // 0x981884: ldur            x2, [fp, #-0x10]
    // 0x981888: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x981888: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x98188c: r0 = DateTime.utc()
    //     0x98188c: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0x981890: ldur            x1, [fp, #-0x20]
    // 0x981894: b               #0x9818d4
    // 0x981898: ldur            x1, [fp, #-8]
    // 0x98189c: r0 = LoadClassIdInstr(r1)
    //     0x98189c: ldur            x0, [x1, #-1]
    //     0x9818a0: ubfx            x0, x0, #0xc, #0x14
    // 0x9818a4: r0 = GDT[cid_x0 + -0xff6]()
    //     0x9818a4: sub             lr, x0, #0xff6
    //     0x9818a8: ldr             lr, [x21, lr, lsl #3]
    //     0x9818ac: blr             lr
    // 0x9818b0: add             x2, x0, #1
    // 0x9818b4: stur            x2, [fp, #-0x10]
    // 0x9818b8: r0 = DateTime()
    //     0x9818b8: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x9818bc: mov             x1, x0
    // 0x9818c0: ldur            x2, [fp, #-0x10]
    // 0x9818c4: stur            x0, [fp, #-8]
    // 0x9818c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9818c8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9818cc: r0 = DateTime.utc()
    //     0x9818cc: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0x9818d0: ldur            x1, [fp, #-8]
    // 0x9818d4: r2 = Instance_Duration
    //     0x9818d4: add             x2, PP, #9, lsl #12  ; [pp+0x92d8] Obj!Duration@e3a141
    //     0x9818d8: ldr             x2, [x2, #0x2d8]
    // 0x9818dc: r0 = subtract()
    //     0x9818dc: bl              #0x819a38  ; [dart:core] DateTime::subtract
    // 0x9818e0: LeaveFrame
    //     0x9818e0: mov             SP, fp
    //     0x9818e4: ldp             fp, lr, [SP], #0x10
    // 0x9818e8: ret
    //     0x9818e8: ret             
    // 0x9818ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9818ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9818f0: b               #0x9817f8
  }
  _ _firstDayOfMonth(/* No info */) {
    // ** addr: 0x9819ac, size: 0xa8
    // 0x9819ac: EnterFrame
    //     0x9819ac: stp             fp, lr, [SP, #-0x10]!
    //     0x9819b0: mov             fp, SP
    // 0x9819b4: AllocStack(0x20)
    //     0x9819b4: sub             SP, SP, #0x20
    // 0x9819b8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x9819b8: stur            x2, [fp, #-8]
    // 0x9819bc: CheckStackOverflow
    //     0x9819bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9819c0: cmp             SP, x16
    //     0x9819c4: b.ls            #0x981a4c
    // 0x9819c8: r0 = LoadClassIdInstr(r2)
    //     0x9819c8: ldur            x0, [x2, #-1]
    //     0x9819cc: ubfx            x0, x0, #0xc, #0x14
    // 0x9819d0: mov             x1, x2
    // 0x9819d4: r0 = GDT[cid_x0 + -0xff6]()
    //     0x9819d4: sub             lr, x0, #0xff6
    //     0x9819d8: ldr             lr, [x21, lr, lsl #3]
    //     0x9819dc: blr             lr
    // 0x9819e0: mov             x2, x0
    // 0x9819e4: ldur            x1, [fp, #-8]
    // 0x9819e8: stur            x2, [fp, #-0x10]
    // 0x9819ec: r0 = LoadClassIdInstr(r1)
    //     0x9819ec: ldur            x0, [x1, #-1]
    //     0x9819f0: ubfx            x0, x0, #0xc, #0x14
    // 0x9819f4: r0 = GDT[cid_x0 + -0xfff]()
    //     0x9819f4: sub             lr, x0, #0xfff
    //     0x9819f8: ldr             lr, [x21, lr, lsl #3]
    //     0x9819fc: blr             lr
    // 0x981a00: mov             x2, x0
    // 0x981a04: r0 = BoxInt64Instr(r2)
    //     0x981a04: sbfiz           x0, x2, #1, #0x1f
    //     0x981a08: cmp             x2, x0, asr #1
    //     0x981a0c: b.eq            #0x981a18
    //     0x981a10: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x981a14: stur            x2, [x0, #7]
    // 0x981a18: stur            x0, [fp, #-8]
    // 0x981a1c: r0 = DateTime()
    //     0x981a1c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x981a20: stur            x0, [fp, #-0x18]
    // 0x981a24: ldur            x16, [fp, #-8]
    // 0x981a28: str             x16, [SP]
    // 0x981a2c: mov             x1, x0
    // 0x981a30: ldur            x2, [fp, #-0x10]
    // 0x981a34: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x981a34: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x981a38: r0 = DateTime.utc()
    //     0x981a38: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0x981a3c: ldur            x0, [fp, #-0x18]
    // 0x981a40: LeaveFrame
    //     0x981a40: mov             SP, fp
    //     0x981a44: ldp             fp, lr, [SP], #0x10
    // 0x981a48: ret
    //     0x981a48: ret             
    // 0x981a4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981a4c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981a50: b               #0x9819c8
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a2130, size: 0x174
    // 0x9a2130: EnterFrame
    //     0x9a2130: stp             fp, lr, [SP, #-0x10]!
    //     0x9a2134: mov             fp, SP
    // 0x9a2138: AllocStack(0x28)
    //     0x9a2138: sub             SP, SP, #0x28
    // 0x9a213c: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x9a213c: mov             x4, x1
    //     0x9a2140: mov             x3, x2
    //     0x9a2144: stur            x1, [fp, #-0x10]
    //     0x9a2148: stur            x2, [fp, #-0x18]
    // 0x9a214c: CheckStackOverflow
    //     0x9a214c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a2150: cmp             SP, x16
    //     0x9a2154: b.ls            #0x9a2278
    // 0x9a2158: LoadField: r5 = r4->field_7
    //     0x9a2158: ldur            w5, [x4, #7]
    // 0x9a215c: DecompressPointer r5
    //     0x9a215c: add             x5, x5, HEAP, lsl #32
    // 0x9a2160: mov             x0, x3
    // 0x9a2164: mov             x2, x5
    // 0x9a2168: stur            x5, [fp, #-8]
    // 0x9a216c: r1 = Null
    //     0x9a216c: mov             x1, NULL
    // 0x9a2170: r8 = TableCalendar<C1X0>
    //     0x9a2170: add             x8, PP, #0x47, lsl #12  ; [pp+0x47ea8] Type: TableCalendar<C1X0>
    //     0x9a2174: ldr             x8, [x8, #0xea8]
    // 0x9a2178: LoadField: r9 = r8->field_7
    //     0x9a2178: ldur            x9, [x8, #7]
    // 0x9a217c: r3 = Null
    //     0x9a217c: add             x3, PP, #0x47, lsl #12  ; [pp+0x47eb0] Null
    //     0x9a2180: ldr             x3, [x3, #0xeb0]
    // 0x9a2184: blr             x9
    // 0x9a2188: ldur            x0, [fp, #-0x18]
    // 0x9a218c: ldur            x2, [fp, #-8]
    // 0x9a2190: r1 = Null
    //     0x9a2190: mov             x1, NULL
    // 0x9a2194: cmp             w2, NULL
    // 0x9a2198: b.eq            #0x9a21bc
    // 0x9a219c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a219c: ldur            w4, [x2, #0x17]
    // 0x9a21a0: DecompressPointer r4
    //     0x9a21a0: add             x4, x4, HEAP, lsl #32
    // 0x9a21a4: r8 = X0 bound StatefulWidget
    //     0x9a21a4: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a21a8: ldr             x8, [x8, #0x7f8]
    // 0x9a21ac: LoadField: r9 = r4->field_7
    //     0x9a21ac: ldur            x9, [x4, #7]
    // 0x9a21b0: r3 = Null
    //     0x9a21b0: add             x3, PP, #0x47, lsl #12  ; [pp+0x47ec0] Null
    //     0x9a21b4: ldr             x3, [x3, #0xec0]
    // 0x9a21b8: blr             x9
    // 0x9a21bc: ldur            x1, [fp, #-0x10]
    // 0x9a21c0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9a21c0: ldur            w0, [x1, #0x17]
    // 0x9a21c4: DecompressPointer r0
    //     0x9a21c4: add             x0, x0, HEAP, lsl #32
    // 0x9a21c8: r16 = Sentinel
    //     0x9a21c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a21cc: cmp             w0, w16
    // 0x9a21d0: b.eq            #0x9a2280
    // 0x9a21d4: LoadField: r2 = r0->field_27
    //     0x9a21d4: ldur            w2, [x0, #0x27]
    // 0x9a21d8: DecompressPointer r2
    //     0x9a21d8: add             x2, x2, HEAP, lsl #32
    // 0x9a21dc: LoadField: r0 = r1->field_b
    //     0x9a21dc: ldur            w0, [x1, #0xb]
    // 0x9a21e0: DecompressPointer r0
    //     0x9a21e0: add             x0, x0, HEAP, lsl #32
    // 0x9a21e4: cmp             w0, NULL
    // 0x9a21e8: b.eq            #0x9a228c
    // 0x9a21ec: LoadField: r3 = r0->field_1b
    //     0x9a21ec: ldur            w3, [x0, #0x1b]
    // 0x9a21f0: DecompressPointer r3
    //     0x9a21f0: add             x3, x3, HEAP, lsl #32
    // 0x9a21f4: r0 = LoadClassIdInstr(r2)
    //     0x9a21f4: ldur            x0, [x2, #-1]
    //     0x9a21f8: ubfx            x0, x0, #0xc, #0x14
    // 0x9a21fc: stp             x3, x2, [SP]
    // 0x9a2200: mov             lr, x0
    // 0x9a2204: ldr             lr, [x21, lr, lsl #3]
    // 0x9a2208: blr             lr
    // 0x9a220c: tbz             w0, #4, #0x9a223c
    // 0x9a2210: ldur            x0, [fp, #-0x10]
    // 0x9a2214: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9a2214: ldur            w1, [x0, #0x17]
    // 0x9a2218: DecompressPointer r1
    //     0x9a2218: add             x1, x1, HEAP, lsl #32
    // 0x9a221c: LoadField: r2 = r0->field_b
    //     0x9a221c: ldur            w2, [x0, #0xb]
    // 0x9a2220: DecompressPointer r2
    //     0x9a2220: add             x2, x2, HEAP, lsl #32
    // 0x9a2224: cmp             w2, NULL
    // 0x9a2228: b.eq            #0x9a2290
    // 0x9a222c: LoadField: r3 = r2->field_1b
    //     0x9a222c: ldur            w3, [x2, #0x1b]
    // 0x9a2230: DecompressPointer r3
    //     0x9a2230: add             x3, x3, HEAP, lsl #32
    // 0x9a2234: mov             x2, x3
    // 0x9a2238: r0 = value=()
    //     0x9a2238: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9a223c: ldur            x1, [fp, #-0x10]
    // 0x9a2240: LoadField: r2 = r1->field_1b
    //     0x9a2240: ldur            w2, [x1, #0x1b]
    // 0x9a2244: DecompressPointer r2
    //     0x9a2244: add             x2, x2, HEAP, lsl #32
    // 0x9a2248: r16 = Sentinel
    //     0x9a2248: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a224c: cmp             w2, w16
    // 0x9a2250: b.eq            #0x9a2294
    // 0x9a2254: LoadField: r2 = r1->field_b
    //     0x9a2254: ldur            w2, [x1, #0xb]
    // 0x9a2258: DecompressPointer r2
    //     0x9a2258: add             x2, x2, HEAP, lsl #32
    // 0x9a225c: cmp             w2, NULL
    // 0x9a2260: b.eq            #0x9a22a0
    // 0x9a2264: StoreField: r1->field_1f = rNULL
    //     0x9a2264: stur            NULL, [x1, #0x1f]
    // 0x9a2268: r0 = Null
    //     0x9a2268: mov             x0, NULL
    // 0x9a226c: LeaveFrame
    //     0x9a226c: mov             SP, fp
    //     0x9a2270: ldp             fp, lr, [SP], #0x10
    // 0x9a2274: ret
    //     0x9a2274: ret             
    // 0x9a2278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a2278: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a227c: b               #0x9a2158
    // 0x9a2280: r9 = _focusedDay
    //     0x9a2280: add             x9, PP, #0x47, lsl #12  ; [pp+0x47de8] Field <_TableCalendarState@1928116939._focusedDay@1928116939>: late final (offset: 0x18)
    //     0x9a2284: ldr             x9, [x9, #0xde8]
    // 0x9a2288: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a2288: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a228c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a228c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a2290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a2290: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a2294: r9 = _rangeSelectionMode
    //     0x9a2294: add             x9, PP, #0x47, lsl #12  ; [pp+0x47e08] Field <_TableCalendarState@1928116939._rangeSelectionMode@1928116939>: late (offset: 0x1c)
    //     0x9a2298: ldr             x9, [x9, #0xe08]
    // 0x9a229c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a229c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a22a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a22a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _swipeCalendarFormat(dynamic, SwipeDirection) {
    // ** addr: 0x9f3534, size: 0x30
    // 0x9f3534: ldr             x1, [SP, #8]
    // 0x9f3538: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9f3538: ldur            w2, [x1, #0x17]
    // 0x9f353c: DecompressPointer r2
    //     0x9f353c: add             x2, x2, HEAP, lsl #32
    // 0x9f3540: LoadField: r1 = r2->field_b
    //     0x9f3540: ldur            w1, [x2, #0xb]
    // 0x9f3544: DecompressPointer r1
    //     0x9f3544: add             x1, x1, HEAP, lsl #32
    // 0x9f3548: cmp             w1, NULL
    // 0x9f354c: b.eq            #0x9f3558
    // 0x9f3550: r0 = Null
    //     0x9f3550: mov             x0, NULL
    // 0x9f3554: ret
    //     0x9f3554: ret             
    // 0x9f3558: EnterFrame
    //     0x9f3558: stp             fp, lr, [SP, #-0x10]!
    //     0x9f355c: mov             fp, SP
    // 0x9f3560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f3560: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa4eac8, size: 0x264
    // 0xa4eac8: EnterFrame
    //     0xa4eac8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4eacc: mov             fp, SP
    // 0xa4ead0: AllocStack(0x80)
    //     0xa4ead0: sub             SP, SP, #0x80
    // 0xa4ead4: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r0, fp-0x8 */)
    //     0xa4ead4: mov             x0, x1
    //     0xa4ead8: stur            x1, [fp, #-8]
    // 0xa4eadc: CheckStackOverflow
    //     0xa4eadc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4eae0: cmp             SP, x16
    //     0xa4eae4: b.ls            #0xa4ed14
    // 0xa4eae8: r1 = 1
    //     0xa4eae8: movz            x1, #0x1
    // 0xa4eaec: r0 = AllocateContext()
    //     0xa4eaec: bl              #0xec126c  ; AllocateContextStub
    // 0xa4eaf0: mov             x3, x0
    // 0xa4eaf4: ldur            x0, [fp, #-8]
    // 0xa4eaf8: stur            x3, [fp, #-0x10]
    // 0xa4eafc: StoreField: r3->field_f = r0
    //     0xa4eafc: stur            w0, [x3, #0xf]
    // 0xa4eb00: r1 = <Widget>
    //     0xa4eb00: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4eb04: r2 = 0
    //     0xa4eb04: movz            x2, #0
    // 0xa4eb08: r0 = _GrowableList()
    //     0xa4eb08: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa4eb0c: mov             x3, x0
    // 0xa4eb10: ldur            x0, [fp, #-8]
    // 0xa4eb14: stur            x3, [fp, #-0x30]
    // 0xa4eb18: LoadField: r1 = r0->field_b
    //     0xa4eb18: ldur            w1, [x0, #0xb]
    // 0xa4eb1c: DecompressPointer r1
    //     0xa4eb1c: add             x1, x1, HEAP, lsl #32
    // 0xa4eb20: cmp             w1, NULL
    // 0xa4eb24: b.eq            #0xa4ed1c
    // 0xa4eb28: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa4eb28: ldur            w2, [x0, #0x17]
    // 0xa4eb2c: DecompressPointer r2
    //     0xa4eb2c: add             x2, x2, HEAP, lsl #32
    // 0xa4eb30: r16 = Sentinel
    //     0xa4eb30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4eb34: cmp             w2, w16
    // 0xa4eb38: b.eq            #0xa4ed20
    // 0xa4eb3c: LoadField: r6 = r2->field_27
    //     0xa4eb3c: ldur            w6, [x2, #0x27]
    // 0xa4eb40: DecompressPointer r6
    //     0xa4eb40: add             x6, x6, HEAP, lsl #32
    // 0xa4eb44: stur            x6, [fp, #-0x28]
    // 0xa4eb48: LoadField: r5 = r1->field_1f
    //     0xa4eb48: ldur            w5, [x1, #0x1f]
    // 0xa4eb4c: DecompressPointer r5
    //     0xa4eb4c: add             x5, x5, HEAP, lsl #32
    // 0xa4eb50: stur            x5, [fp, #-0x20]
    // 0xa4eb54: LoadField: r7 = r1->field_23
    //     0xa4eb54: ldur            w7, [x1, #0x23]
    // 0xa4eb58: DecompressPointer r7
    //     0xa4eb58: add             x7, x7, HEAP, lsl #32
    // 0xa4eb5c: ldur            x2, [fp, #-0x10]
    // 0xa4eb60: stur            x7, [fp, #-0x18]
    // 0xa4eb64: r1 = Function '<anonymous closure>':.
    //     0xa4eb64: add             x1, PP, #0x47, lsl #12  ; [pp+0x47db8] AnonymousClosure: (0xa50c68), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::build (0xa4eac8)
    //     0xa4eb68: ldr             x1, [x1, #0xdb8]
    // 0xa4eb6c: r0 = AllocateClosure()
    //     0xa4eb6c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4eb70: ldur            x2, [fp, #-8]
    // 0xa4eb74: r1 = Function '_swipeCalendarFormat@1928116939':.
    //     0xa4eb74: add             x1, PP, #0x47, lsl #12  ; [pp+0x47dc0] AnonymousClosure: (0x9f3534), of [package:table_calendar/src/table_calendar.dart] _TableCalendarState<C1X0>
    //     0xa4eb78: ldr             x1, [x1, #0xdc0]
    // 0xa4eb7c: stur            x0, [fp, #-8]
    // 0xa4eb80: r0 = AllocateClosure()
    //     0xa4eb80: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4eb84: ldur            x2, [fp, #-0x10]
    // 0xa4eb88: r1 = Function '<anonymous closure>':.
    //     0xa4eb88: add             x1, PP, #0x47, lsl #12  ; [pp+0x47dc8] AnonymousClosure: (0xa50b08), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::build (0xa4eac8)
    //     0xa4eb8c: ldr             x1, [x1, #0xdc8]
    // 0xa4eb90: stur            x0, [fp, #-0x38]
    // 0xa4eb94: r0 = AllocateClosure()
    //     0xa4eb94: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4eb98: ldur            x2, [fp, #-0x10]
    // 0xa4eb9c: r1 = Function '<anonymous closure>':.
    //     0xa4eb9c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47dd0] AnonymousClosure: (0xa50788), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::build (0xa4eac8)
    //     0xa4eba0: ldr             x1, [x1, #0xdd0]
    // 0xa4eba4: stur            x0, [fp, #-0x40]
    // 0xa4eba8: r0 = AllocateClosure()
    //     0xa4eba8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4ebac: ldur            x2, [fp, #-0x10]
    // 0xa4ebb0: r1 = Function '<anonymous closure>':.
    //     0xa4ebb0: add             x1, PP, #0x47, lsl #12  ; [pp+0x47dd8] AnonymousClosure: (0xa5060c), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::build (0xa4eac8)
    //     0xa4ebb4: ldr             x1, [x1, #0xdd8]
    // 0xa4ebb8: stur            x0, [fp, #-0x48]
    // 0xa4ebbc: r0 = AllocateClosure()
    //     0xa4ebbc: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4ebc0: ldur            x2, [fp, #-0x10]
    // 0xa4ebc4: r1 = Function '<anonymous closure>':.
    //     0xa4ebc4: add             x1, PP, #0x47, lsl #12  ; [pp+0x47de0] AnonymousClosure: (0xa4ef34), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::build (0xa4eac8)
    //     0xa4ebc8: ldr             x1, [x1, #0xde0]
    // 0xa4ebcc: stur            x0, [fp, #-0x10]
    // 0xa4ebd0: r0 = AllocateClosure()
    //     0xa4ebd0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4ebd4: stur            x0, [fp, #-0x50]
    // 0xa4ebd8: r0 = TableCalendarBase()
    //     0xa4ebd8: bl              #0xa4ef28  ; AllocateTableCalendarBaseStub -> TableCalendarBase (size=0x80)
    // 0xa4ebdc: stur            x0, [fp, #-0x58]
    // 0xa4ebe0: ldur            x16, [fp, #-8]
    // 0xa4ebe4: ldur            lr, [fp, #-0x40]
    // 0xa4ebe8: stp             lr, x16, [SP, #0x10]
    // 0xa4ebec: ldur            x16, [fp, #-0x38]
    // 0xa4ebf0: ldur            lr, [fp, #-0x48]
    // 0xa4ebf4: stp             lr, x16, [SP]
    // 0xa4ebf8: mov             x1, x0
    // 0xa4ebfc: ldur            x2, [fp, #-0x50]
    // 0xa4ec00: ldur            x3, [fp, #-0x10]
    // 0xa4ec04: ldur            x5, [fp, #-0x20]
    // 0xa4ec08: ldur            x6, [fp, #-0x28]
    // 0xa4ec0c: ldur            x7, [fp, #-0x18]
    // 0xa4ec10: r0 = TableCalendarBase()
    //     0xa4ec10: bl              #0xa4ed2c  ; [package:table_calendar/src/table_calendar_base.dart] TableCalendarBase::TableCalendarBase
    // 0xa4ec14: r1 = <FlexParentData>
    //     0xa4ec14: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xa4ec18: ldr             x1, [x1, #0x720]
    // 0xa4ec1c: r0 = Flexible()
    //     0xa4ec1c: bl              #0x9e6a68  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xa4ec20: stur            x0, [fp, #-8]
    // 0xa4ec24: StoreField: r0->field_13 = rZR
    //     0xa4ec24: stur            xzr, [x0, #0x13]
    // 0xa4ec28: r1 = Instance_FlexFit
    //     0xa4ec28: add             x1, PP, #0x29, lsl #12  ; [pp+0x29d68] Obj!FlexFit@e35b61
    //     0xa4ec2c: ldr             x1, [x1, #0xd68]
    // 0xa4ec30: StoreField: r0->field_1b = r1
    //     0xa4ec30: stur            w1, [x0, #0x1b]
    // 0xa4ec34: ldur            x1, [fp, #-0x58]
    // 0xa4ec38: StoreField: r0->field_b = r1
    //     0xa4ec38: stur            w1, [x0, #0xb]
    // 0xa4ec3c: ldur            x2, [fp, #-0x30]
    // 0xa4ec40: LoadField: r1 = r2->field_b
    //     0xa4ec40: ldur            w1, [x2, #0xb]
    // 0xa4ec44: LoadField: r3 = r2->field_f
    //     0xa4ec44: ldur            w3, [x2, #0xf]
    // 0xa4ec48: DecompressPointer r3
    //     0xa4ec48: add             x3, x3, HEAP, lsl #32
    // 0xa4ec4c: LoadField: r4 = r3->field_b
    //     0xa4ec4c: ldur            w4, [x3, #0xb]
    // 0xa4ec50: r3 = LoadInt32Instr(r1)
    //     0xa4ec50: sbfx            x3, x1, #1, #0x1f
    // 0xa4ec54: stur            x3, [fp, #-0x60]
    // 0xa4ec58: r1 = LoadInt32Instr(r4)
    //     0xa4ec58: sbfx            x1, x4, #1, #0x1f
    // 0xa4ec5c: cmp             x3, x1
    // 0xa4ec60: b.ne            #0xa4ec6c
    // 0xa4ec64: mov             x1, x2
    // 0xa4ec68: r0 = _growToNextCapacity()
    //     0xa4ec68: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4ec6c: ldur            x2, [fp, #-0x30]
    // 0xa4ec70: ldur            x3, [fp, #-0x60]
    // 0xa4ec74: add             x0, x3, #1
    // 0xa4ec78: lsl             x1, x0, #1
    // 0xa4ec7c: StoreField: r2->field_b = r1
    //     0xa4ec7c: stur            w1, [x2, #0xb]
    // 0xa4ec80: LoadField: r1 = r2->field_f
    //     0xa4ec80: ldur            w1, [x2, #0xf]
    // 0xa4ec84: DecompressPointer r1
    //     0xa4ec84: add             x1, x1, HEAP, lsl #32
    // 0xa4ec88: ldur            x0, [fp, #-8]
    // 0xa4ec8c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4ec8c: add             x25, x1, x3, lsl #2
    //     0xa4ec90: add             x25, x25, #0xf
    //     0xa4ec94: str             w0, [x25]
    //     0xa4ec98: tbz             w0, #0, #0xa4ecb4
    //     0xa4ec9c: ldurb           w16, [x1, #-1]
    //     0xa4eca0: ldurb           w17, [x0, #-1]
    //     0xa4eca4: and             x16, x17, x16, lsr #2
    //     0xa4eca8: tst             x16, HEAP, lsr #32
    //     0xa4ecac: b.eq            #0xa4ecb4
    //     0xa4ecb0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4ecb4: r0 = Column()
    //     0xa4ecb4: bl              #0x9e6a38  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa4ecb8: r1 = Instance_Axis
    //     0xa4ecb8: ldr             x1, [PP, #0x7578]  ; [pp+0x7578] Obj!Axis@e35f21
    // 0xa4ecbc: StoreField: r0->field_f = r1
    //     0xa4ecbc: stur            w1, [x0, #0xf]
    // 0xa4ecc0: r1 = Instance_MainAxisAlignment
    //     0xa4ecc0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa4ecc4: ldr             x1, [x1, #0x730]
    // 0xa4ecc8: StoreField: r0->field_13 = r1
    //     0xa4ecc8: stur            w1, [x0, #0x13]
    // 0xa4eccc: r1 = Instance_MainAxisSize
    //     0xa4eccc: add             x1, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xa4ecd0: ldr             x1, [x1, #0x738]
    // 0xa4ecd4: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4ecd4: stur            w1, [x0, #0x17]
    // 0xa4ecd8: r1 = Instance_CrossAxisAlignment
    //     0xa4ecd8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa4ecdc: ldr             x1, [x1, #0x740]
    // 0xa4ece0: StoreField: r0->field_1b = r1
    //     0xa4ece0: stur            w1, [x0, #0x1b]
    // 0xa4ece4: r1 = Instance_VerticalDirection
    //     0xa4ece4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa4ece8: ldr             x1, [x1, #0x748]
    // 0xa4ecec: StoreField: r0->field_23 = r1
    //     0xa4ecec: stur            w1, [x0, #0x23]
    // 0xa4ecf0: r1 = Instance_Clip
    //     0xa4ecf0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa4ecf4: ldr             x1, [x1, #0x750]
    // 0xa4ecf8: StoreField: r0->field_2b = r1
    //     0xa4ecf8: stur            w1, [x0, #0x2b]
    // 0xa4ecfc: StoreField: r0->field_2f = rZR
    //     0xa4ecfc: stur            xzr, [x0, #0x2f]
    // 0xa4ed00: ldur            x1, [fp, #-0x30]
    // 0xa4ed04: StoreField: r0->field_b = r1
    //     0xa4ed04: stur            w1, [x0, #0xb]
    // 0xa4ed08: LeaveFrame
    //     0xa4ed08: mov             SP, fp
    //     0xa4ed0c: ldp             fp, lr, [SP], #0x10
    // 0xa4ed10: ret
    //     0xa4ed10: ret             
    // 0xa4ed14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ed14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ed18: b               #0xa4eae8
    // 0xa4ed1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ed1c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ed20: r9 = _focusedDay
    //     0xa4ed20: add             x9, PP, #0x47, lsl #12  ; [pp+0x47de8] Field <_TableCalendarState@1928116939._focusedDay@1928116939>: late final (offset: 0x18)
    //     0xa4ed24: ldr             x9, [x9, #0xde8]
    // 0xa4ed28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4ed28: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, DateTime, DateTime) {
    // ** addr: 0xa4ef34, size: 0xdc
    // 0xa4ef34: EnterFrame
    //     0xa4ef34: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ef38: mov             fp, SP
    // 0xa4ef3c: AllocStack(0x38)
    //     0xa4ef3c: sub             SP, SP, #0x38
    // 0xa4ef40: SetupParameters()
    //     0xa4ef40: ldr             x0, [fp, #0x28]
    //     0xa4ef44: ldur            w1, [x0, #0x17]
    //     0xa4ef48: add             x1, x1, HEAP, lsl #32
    //     0xa4ef4c: stur            x1, [fp, #-8]
    // 0xa4ef50: CheckStackOverflow
    //     0xa4ef50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ef54: cmp             SP, x16
    //     0xa4ef58: b.ls            #0xa4f004
    // 0xa4ef5c: r1 = 1
    //     0xa4ef5c: movz            x1, #0x1
    // 0xa4ef60: r0 = AllocateContext()
    //     0xa4ef60: bl              #0xec126c  ; AllocateContextStub
    // 0xa4ef64: mov             x4, x0
    // 0xa4ef68: ldur            x0, [fp, #-8]
    // 0xa4ef6c: stur            x4, [fp, #-0x10]
    // 0xa4ef70: StoreField: r4->field_b = r0
    //     0xa4ef70: stur            w0, [x4, #0xb]
    // 0xa4ef74: ldr             x2, [fp, #0x18]
    // 0xa4ef78: StoreField: r4->field_f = r2
    //     0xa4ef78: stur            w2, [x4, #0xf]
    // 0xa4ef7c: LoadField: r1 = r0->field_f
    //     0xa4ef7c: ldur            w1, [x0, #0xf]
    // 0xa4ef80: DecompressPointer r1
    //     0xa4ef80: add             x1, x1, HEAP, lsl #32
    // 0xa4ef84: LoadField: r0 = r1->field_b
    //     0xa4ef84: ldur            w0, [x1, #0xb]
    // 0xa4ef88: DecompressPointer r0
    //     0xa4ef88: add             x0, x0, HEAP, lsl #32
    // 0xa4ef8c: cmp             w0, NULL
    // 0xa4ef90: b.eq            #0xa4f00c
    // 0xa4ef94: ldr             x3, [fp, #0x10]
    // 0xa4ef98: r0 = _buildCell()
    //     0xa4ef98: bl              #0xa4f010  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_buildCell
    // 0xa4ef9c: stur            x0, [fp, #-8]
    // 0xa4efa0: r0 = GestureDetector()
    //     0xa4efa0: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa4efa4: ldur            x2, [fp, #-0x10]
    // 0xa4efa8: r1 = Function '<anonymous closure>':.
    //     0xa4efa8: add             x1, PP, #0x47, lsl #12  ; [pp+0x47df0] AnonymousClosure: (0xa4fe7c), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::build (0xa4eac8)
    //     0xa4efac: ldr             x1, [x1, #0xdf0]
    // 0xa4efb0: stur            x0, [fp, #-0x18]
    // 0xa4efb4: r0 = AllocateClosure()
    //     0xa4efb4: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4efb8: ldur            x2, [fp, #-0x10]
    // 0xa4efbc: r1 = Function '<anonymous closure>':.
    //     0xa4efbc: add             x1, PP, #0x47, lsl #12  ; [pp+0x47df8] AnonymousClosure: (0xa4fd04), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::build (0xa4eac8)
    //     0xa4efc0: ldr             x1, [x1, #0xdf8]
    // 0xa4efc4: stur            x0, [fp, #-0x10]
    // 0xa4efc8: r0 = AllocateClosure()
    //     0xa4efc8: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4efcc: r16 = Instance_HitTestBehavior
    //     0xa4efcc: add             x16, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xa4efd0: ldr             x16, [x16, #0x1c8]
    // 0xa4efd4: ldur            lr, [fp, #-0x10]
    // 0xa4efd8: stp             lr, x16, [SP, #0x10]
    // 0xa4efdc: ldur            x16, [fp, #-8]
    // 0xa4efe0: stp             x16, x0, [SP]
    // 0xa4efe4: ldur            x1, [fp, #-0x18]
    // 0xa4efe8: r4 = const [0, 0x5, 0x4, 0x1, behavior, 0x1, child, 0x4, onLongPress, 0x3, onTap, 0x2, null]
    //     0xa4efe8: add             x4, PP, #0x47, lsl #12  ; [pp+0x47e00] List(13) [0, 0x5, 0x4, 0x1, "behavior", 0x1, "child", 0x4, "onLongPress", 0x3, "onTap", 0x2, Null]
    //     0xa4efec: ldr             x4, [x4, #0xe00]
    // 0xa4eff0: r0 = GestureDetector()
    //     0xa4eff0: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa4eff4: ldur            x0, [fp, #-0x18]
    // 0xa4eff8: LeaveFrame
    //     0xa4eff8: mov             SP, fp
    //     0xa4effc: ldp             fp, lr, [SP], #0x10
    // 0xa4f000: ret
    //     0xa4f000: ret             
    // 0xa4f004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4f004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4f008: b               #0xa4ef5c
    // 0xa4f00c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f00c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCell(/* No info */) {
    // ** addr: 0xa4f010, size: 0x104
    // 0xa4f010: EnterFrame
    //     0xa4f010: stp             fp, lr, [SP, #-0x10]!
    //     0xa4f014: mov             fp, SP
    // 0xa4f018: AllocStack(0x28)
    //     0xa4f018: sub             SP, SP, #0x28
    // 0xa4f01c: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xa4f01c: mov             x0, x2
    //     0xa4f020: stur            x2, [fp, #-0x10]
    //     0xa4f024: mov             x2, x1
    //     0xa4f028: stur            x1, [fp, #-8]
    //     0xa4f02c: mov             x1, x3
    //     0xa4f030: stur            x3, [fp, #-0x18]
    // 0xa4f034: CheckStackOverflow
    //     0xa4f034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4f038: cmp             SP, x16
    //     0xa4f03c: b.ls            #0xa4f108
    // 0xa4f040: r1 = 4
    //     0xa4f040: movz            x1, #0x4
    // 0xa4f044: r0 = AllocateContext()
    //     0xa4f044: bl              #0xec126c  ; AllocateContextStub
    // 0xa4f048: mov             x3, x0
    // 0xa4f04c: ldur            x2, [fp, #-8]
    // 0xa4f050: stur            x3, [fp, #-0x20]
    // 0xa4f054: StoreField: r3->field_f = r2
    //     0xa4f054: stur            w2, [x3, #0xf]
    // 0xa4f058: ldur            x1, [fp, #-0x10]
    // 0xa4f05c: StoreField: r3->field_13 = r1
    //     0xa4f05c: stur            w1, [x3, #0x13]
    // 0xa4f060: ldur            x4, [fp, #-0x18]
    // 0xa4f064: ArrayStore: r3[0] = r4  ; List_4
    //     0xa4f064: stur            w4, [x3, #0x17]
    // 0xa4f068: r0 = LoadClassIdInstr(r1)
    //     0xa4f068: ldur            x0, [x1, #-1]
    //     0xa4f06c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f070: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4f070: sub             lr, x0, #0xfff
    //     0xa4f074: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f078: blr             lr
    // 0xa4f07c: mov             x2, x0
    // 0xa4f080: ldur            x1, [fp, #-0x18]
    // 0xa4f084: stur            x2, [fp, #-0x28]
    // 0xa4f088: r0 = LoadClassIdInstr(r1)
    //     0xa4f088: ldur            x0, [x1, #-1]
    //     0xa4f08c: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f090: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4f090: sub             lr, x0, #0xfff
    //     0xa4f094: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f098: blr             lr
    // 0xa4f09c: mov             x1, x0
    // 0xa4f0a0: ldur            x0, [fp, #-0x28]
    // 0xa4f0a4: cmp             x0, x1
    // 0xa4f0a8: r16 = true
    //     0xa4f0a8: add             x16, NULL, #0x20  ; true
    // 0xa4f0ac: r17 = false
    //     0xa4f0ac: add             x17, NULL, #0x30  ; false
    // 0xa4f0b0: csel            x2, x16, x17, ne
    // 0xa4f0b4: ldur            x0, [fp, #-0x20]
    // 0xa4f0b8: StoreField: r0->field_1b = r2
    //     0xa4f0b8: stur            w2, [x0, #0x1b]
    // 0xa4f0bc: tbnz            w2, #4, #0xa4f0d4
    // 0xa4f0c0: ldur            x1, [fp, #-8]
    // 0xa4f0c4: LoadField: r2 = r1->field_b
    //     0xa4f0c4: ldur            w2, [x1, #0xb]
    // 0xa4f0c8: DecompressPointer r2
    //     0xa4f0c8: add             x2, x2, HEAP, lsl #32
    // 0xa4f0cc: cmp             w2, NULL
    // 0xa4f0d0: b.eq            #0xa4f110
    // 0xa4f0d4: mov             x2, x0
    // 0xa4f0d8: r1 = Function '<anonymous closure>':.
    //     0xa4f0d8: add             x1, PP, #0x47, lsl #12  ; [pp+0x47e10] AnonymousClosure: (0xa4f114), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_buildCell (0xa4f010)
    //     0xa4f0dc: ldr             x1, [x1, #0xe10]
    // 0xa4f0e0: r0 = AllocateClosure()
    //     0xa4f0e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xa4f0e4: r1 = <BoxConstraints>
    //     0xa4f0e4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xa4f0e8: ldr             x1, [x1, #0xfa8]
    // 0xa4f0ec: stur            x0, [fp, #-8]
    // 0xa4f0f0: r0 = LayoutBuilder()
    //     0xa4f0f0: bl              #0x9f1460  ; AllocateLayoutBuilderStub -> LayoutBuilder (size=0x14)
    // 0xa4f0f4: ldur            x1, [fp, #-8]
    // 0xa4f0f8: StoreField: r0->field_f = r1
    //     0xa4f0f8: stur            w1, [x0, #0xf]
    // 0xa4f0fc: LeaveFrame
    //     0xa4f0fc: mov             SP, fp
    //     0xa4f100: ldp             fp, lr, [SP], #0x10
    // 0xa4f104: ret
    //     0xa4f104: ret             
    // 0xa4f108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4f108: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4f10c: b               #0xa4f040
    // 0xa4f110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f110: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, BoxConstraints) {
    // ** addr: 0xa4f114, size: 0x814
    // 0xa4f114: EnterFrame
    //     0xa4f114: stp             fp, lr, [SP, #-0x10]!
    //     0xa4f118: mov             fp, SP
    // 0xa4f11c: AllocStack(0xa0)
    //     0xa4f11c: sub             SP, SP, #0xa0
    // 0xa4f120: SetupParameters()
    //     0xa4f120: ldr             x0, [fp, #0x20]
    //     0xa4f124: ldur            w3, [x0, #0x17]
    //     0xa4f128: add             x3, x3, HEAP, lsl #32
    //     0xa4f12c: stur            x3, [fp, #-8]
    // 0xa4f130: CheckStackOverflow
    //     0xa4f130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4f134: cmp             SP, x16
    //     0xa4f138: b.ls            #0xa4f8d8
    // 0xa4f13c: ldr             x0, [fp, #0x10]
    // 0xa4f140: LoadField: d0 = r0->field_1f
    //     0xa4f140: ldur            d0, [x0, #0x1f]
    // 0xa4f144: stur            d0, [fp, #-0x80]
    // 0xa4f148: LoadField: d1 = r0->field_f
    //     0xa4f148: ldur            d1, [x0, #0xf]
    // 0xa4f14c: fcmp            d0, d1
    // 0xa4f150: b.gt            #0xa4f158
    // 0xa4f154: mov             v1.16b, v0.16b
    // 0xa4f158: stur            d1, [fp, #-0x78]
    // 0xa4f15c: r1 = <Widget>
    //     0xa4f15c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4f160: r2 = 0
    //     0xa4f160: movz            x2, #0
    // 0xa4f164: r0 = _GrowableList()
    //     0xa4f164: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa4f168: mov             x3, x0
    // 0xa4f16c: ldur            x0, [fp, #-8]
    // 0xa4f170: stur            x3, [fp, #-0x10]
    // 0xa4f174: LoadField: r1 = r0->field_f
    //     0xa4f174: ldur            w1, [x0, #0xf]
    // 0xa4f178: DecompressPointer r1
    //     0xa4f178: add             x1, x1, HEAP, lsl #32
    // 0xa4f17c: LoadField: r2 = r1->field_b
    //     0xa4f17c: ldur            w2, [x1, #0xb]
    // 0xa4f180: DecompressPointer r2
    //     0xa4f180: add             x2, x2, HEAP, lsl #32
    // 0xa4f184: cmp             w2, NULL
    // 0xa4f188: b.eq            #0xa4f8e0
    // 0xa4f18c: LoadField: r1 = r0->field_13
    //     0xa4f18c: ldur            w1, [x0, #0x13]
    // 0xa4f190: DecompressPointer r1
    //     0xa4f190: add             x1, x1, HEAP, lsl #32
    // 0xa4f194: LoadField: r4 = r2->field_27
    //     0xa4f194: ldur            w4, [x2, #0x27]
    // 0xa4f198: DecompressPointer r4
    //     0xa4f198: add             x4, x4, HEAP, lsl #32
    // 0xa4f19c: mov             x2, x4
    // 0xa4f1a0: r0 = isSameDay()
    //     0xa4f1a0: bl              #0xa4fa74  ; [package:table_calendar/src/shared/utils.dart] ::isSameDay
    // 0xa4f1a4: mov             x3, x0
    // 0xa4f1a8: ldur            x0, [fp, #-8]
    // 0xa4f1ac: stur            x3, [fp, #-0x18]
    // 0xa4f1b0: LoadField: r1 = r0->field_f
    //     0xa4f1b0: ldur            w1, [x0, #0xf]
    // 0xa4f1b4: DecompressPointer r1
    //     0xa4f1b4: add             x1, x1, HEAP, lsl #32
    // 0xa4f1b8: LoadField: r2 = r0->field_13
    //     0xa4f1b8: ldur            w2, [x0, #0x13]
    // 0xa4f1bc: DecompressPointer r2
    //     0xa4f1bc: add             x2, x2, HEAP, lsl #32
    // 0xa4f1c0: r0 = _isDayDisabled()
    //     0xa4f1c0: bl              #0xa4f9a0  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isDayDisabled
    // 0xa4f1c4: mov             x3, x0
    // 0xa4f1c8: ldur            x0, [fp, #-8]
    // 0xa4f1cc: stur            x3, [fp, #-0x20]
    // 0xa4f1d0: LoadField: r2 = r0->field_13
    //     0xa4f1d0: ldur            w2, [x0, #0x13]
    // 0xa4f1d4: DecompressPointer r2
    //     0xa4f1d4: add             x2, x2, HEAP, lsl #32
    // 0xa4f1d8: LoadField: r1 = r0->field_f
    //     0xa4f1d8: ldur            w1, [x0, #0xf]
    // 0xa4f1dc: DecompressPointer r1
    //     0xa4f1dc: add             x1, x1, HEAP, lsl #32
    // 0xa4f1e0: LoadField: r4 = r1->field_b
    //     0xa4f1e0: ldur            w4, [x1, #0xb]
    // 0xa4f1e4: DecompressPointer r4
    //     0xa4f1e4: add             x4, x4, HEAP, lsl #32
    // 0xa4f1e8: cmp             w4, NULL
    // 0xa4f1ec: b.eq            #0xa4f8e4
    // 0xa4f1f0: r0 = _isWeekend()
    //     0xa4f1f0: bl              #0xa4f934  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isWeekend
    // 0xa4f1f4: r1 = Null
    //     0xa4f1f4: mov             x1, NULL
    // 0xa4f1f8: r2 = 12
    //     0xa4f1f8: movz            x2, #0xc
    // 0xa4f1fc: stur            x0, [fp, #-0x28]
    // 0xa4f200: r0 = AllocateArray()
    //     0xa4f200: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa4f204: mov             x2, x0
    // 0xa4f208: stur            x2, [fp, #-0x30]
    // 0xa4f20c: r16 = "CellContent-"
    //     0xa4f20c: add             x16, PP, #0x47, lsl #12  ; [pp+0x47e18] "CellContent-"
    //     0xa4f210: ldr             x16, [x16, #0xe18]
    // 0xa4f214: StoreField: r2->field_f = r16
    //     0xa4f214: stur            w16, [x2, #0xf]
    // 0xa4f218: ldur            x3, [fp, #-8]
    // 0xa4f21c: LoadField: r1 = r3->field_13
    //     0xa4f21c: ldur            w1, [x3, #0x13]
    // 0xa4f220: DecompressPointer r1
    //     0xa4f220: add             x1, x1, HEAP, lsl #32
    // 0xa4f224: r0 = LoadClassIdInstr(r1)
    //     0xa4f224: ldur            x0, [x1, #-1]
    //     0xa4f228: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f22c: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa4f22c: sub             lr, x0, #0xff6
    //     0xa4f230: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f234: blr             lr
    // 0xa4f238: mov             x2, x0
    // 0xa4f23c: r0 = BoxInt64Instr(r2)
    //     0xa4f23c: sbfiz           x0, x2, #1, #0x1f
    //     0xa4f240: cmp             x2, x0, asr #1
    //     0xa4f244: b.eq            #0xa4f250
    //     0xa4f248: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4f24c: stur            x2, [x0, #7]
    // 0xa4f250: ldur            x1, [fp, #-0x30]
    // 0xa4f254: ArrayStore: r1[1] = r0  ; List_4
    //     0xa4f254: add             x25, x1, #0x13
    //     0xa4f258: str             w0, [x25]
    //     0xa4f25c: tbz             w0, #0, #0xa4f278
    //     0xa4f260: ldurb           w16, [x1, #-1]
    //     0xa4f264: ldurb           w17, [x0, #-1]
    //     0xa4f268: and             x16, x17, x16, lsr #2
    //     0xa4f26c: tst             x16, HEAP, lsr #32
    //     0xa4f270: b.eq            #0xa4f278
    //     0xa4f274: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4f278: ldur            x2, [fp, #-0x30]
    // 0xa4f27c: r16 = "-"
    //     0xa4f27c: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xa4f280: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4f280: stur            w16, [x2, #0x17]
    // 0xa4f284: ldur            x3, [fp, #-8]
    // 0xa4f288: LoadField: r1 = r3->field_13
    //     0xa4f288: ldur            w1, [x3, #0x13]
    // 0xa4f28c: DecompressPointer r1
    //     0xa4f28c: add             x1, x1, HEAP, lsl #32
    // 0xa4f290: r0 = LoadClassIdInstr(r1)
    //     0xa4f290: ldur            x0, [x1, #-1]
    //     0xa4f294: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f298: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4f298: sub             lr, x0, #0xfff
    //     0xa4f29c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f2a0: blr             lr
    // 0xa4f2a4: mov             x2, x0
    // 0xa4f2a8: r0 = BoxInt64Instr(r2)
    //     0xa4f2a8: sbfiz           x0, x2, #1, #0x1f
    //     0xa4f2ac: cmp             x2, x0, asr #1
    //     0xa4f2b0: b.eq            #0xa4f2bc
    //     0xa4f2b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4f2b8: stur            x2, [x0, #7]
    // 0xa4f2bc: ldur            x1, [fp, #-0x30]
    // 0xa4f2c0: ArrayStore: r1[3] = r0  ; List_4
    //     0xa4f2c0: add             x25, x1, #0x1b
    //     0xa4f2c4: str             w0, [x25]
    //     0xa4f2c8: tbz             w0, #0, #0xa4f2e4
    //     0xa4f2cc: ldurb           w16, [x1, #-1]
    //     0xa4f2d0: ldurb           w17, [x0, #-1]
    //     0xa4f2d4: and             x16, x17, x16, lsr #2
    //     0xa4f2d8: tst             x16, HEAP, lsr #32
    //     0xa4f2dc: b.eq            #0xa4f2e4
    //     0xa4f2e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4f2e4: ldur            x2, [fp, #-0x30]
    // 0xa4f2e8: r16 = "-"
    //     0xa4f2e8: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xa4f2ec: StoreField: r2->field_1f = r16
    //     0xa4f2ec: stur            w16, [x2, #0x1f]
    // 0xa4f2f0: ldur            x3, [fp, #-8]
    // 0xa4f2f4: LoadField: r1 = r3->field_13
    //     0xa4f2f4: ldur            w1, [x3, #0x13]
    // 0xa4f2f8: DecompressPointer r1
    //     0xa4f2f8: add             x1, x1, HEAP, lsl #32
    // 0xa4f2fc: r0 = LoadClassIdInstr(r1)
    //     0xa4f2fc: ldur            x0, [x1, #-1]
    //     0xa4f300: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f304: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xa4f304: sub             lr, x0, #0xfdf
    //     0xa4f308: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f30c: blr             lr
    // 0xa4f310: mov             x2, x0
    // 0xa4f314: r0 = BoxInt64Instr(r2)
    //     0xa4f314: sbfiz           x0, x2, #1, #0x1f
    //     0xa4f318: cmp             x2, x0, asr #1
    //     0xa4f31c: b.eq            #0xa4f328
    //     0xa4f320: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4f324: stur            x2, [x0, #7]
    // 0xa4f328: ldur            x1, [fp, #-0x30]
    // 0xa4f32c: ArrayStore: r1[5] = r0  ; List_4
    //     0xa4f32c: add             x25, x1, #0x23
    //     0xa4f330: str             w0, [x25]
    //     0xa4f334: tbz             w0, #0, #0xa4f350
    //     0xa4f338: ldurb           w16, [x1, #-1]
    //     0xa4f33c: ldurb           w17, [x0, #-1]
    //     0xa4f340: and             x16, x17, x16, lsr #2
    //     0xa4f344: tst             x16, HEAP, lsr #32
    //     0xa4f348: b.eq            #0xa4f350
    //     0xa4f34c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4f350: ldur            x16, [fp, #-0x30]
    // 0xa4f354: str             x16, [SP]
    // 0xa4f358: r0 = _interpolate()
    //     0xa4f358: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa4f35c: r1 = <String>
    //     0xa4f35c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xa4f360: stur            x0, [fp, #-0x30]
    // 0xa4f364: r0 = ValueKey()
    //     0xa4f364: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xa4f368: mov             x1, x0
    // 0xa4f36c: ldur            x0, [fp, #-0x30]
    // 0xa4f370: stur            x1, [fp, #-0x48]
    // 0xa4f374: StoreField: r1->field_b = r0
    //     0xa4f374: stur            w0, [x1, #0xb]
    // 0xa4f378: ldur            x2, [fp, #-8]
    // 0xa4f37c: LoadField: r3 = r2->field_13
    //     0xa4f37c: ldur            w3, [x2, #0x13]
    // 0xa4f380: DecompressPointer r3
    //     0xa4f380: add             x3, x3, HEAP, lsl #32
    // 0xa4f384: stur            x3, [fp, #-0x40]
    // 0xa4f388: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa4f388: ldur            w4, [x2, #0x17]
    // 0xa4f38c: DecompressPointer r4
    //     0xa4f38c: add             x4, x4, HEAP, lsl #32
    // 0xa4f390: stur            x4, [fp, #-0x38]
    // 0xa4f394: LoadField: r0 = r2->field_f
    //     0xa4f394: ldur            w0, [x2, #0xf]
    // 0xa4f398: DecompressPointer r0
    //     0xa4f398: add             x0, x0, HEAP, lsl #32
    // 0xa4f39c: LoadField: r5 = r0->field_b
    //     0xa4f39c: ldur            w5, [x0, #0xb]
    // 0xa4f3a0: DecompressPointer r5
    //     0xa4f3a0: add             x5, x5, HEAP, lsl #32
    // 0xa4f3a4: cmp             w5, NULL
    // 0xa4f3a8: b.eq            #0xa4f8e8
    // 0xa4f3ac: LoadField: r6 = r5->field_8f
    //     0xa4f3ac: ldur            w6, [x5, #0x8f]
    // 0xa4f3b0: DecompressPointer r6
    //     0xa4f3b0: add             x6, x6, HEAP, lsl #32
    // 0xa4f3b4: stur            x6, [fp, #-0x30]
    // 0xa4f3b8: LoadField: r0 = r5->field_a3
    //     0xa4f3b8: ldur            w0, [x5, #0xa3]
    // 0xa4f3bc: DecompressPointer r0
    //     0xa4f3bc: add             x0, x0, HEAP, lsl #32
    // 0xa4f3c0: cmp             w0, NULL
    // 0xa4f3c4: b.eq            #0xa4f8ec
    // 0xa4f3c8: stp             x3, x0, [SP]
    // 0xa4f3cc: ClosureCall
    //     0xa4f3cc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa4f3d0: ldur            x2, [x0, #0x1f]
    //     0xa4f3d4: blr             x2
    // 0xa4f3d8: mov             x2, x0
    // 0xa4f3dc: ldur            x1, [fp, #-8]
    // 0xa4f3e0: stur            x2, [fp, #-0x50]
    // 0xa4f3e4: LoadField: r0 = r1->field_f
    //     0xa4f3e4: ldur            w0, [x1, #0xf]
    // 0xa4f3e8: DecompressPointer r0
    //     0xa4f3e8: add             x0, x0, HEAP, lsl #32
    // 0xa4f3ec: LoadField: r3 = r0->field_b
    //     0xa4f3ec: ldur            w3, [x0, #0xb]
    // 0xa4f3f0: DecompressPointer r3
    //     0xa4f3f0: add             x3, x3, HEAP, lsl #32
    // 0xa4f3f4: cmp             w3, NULL
    // 0xa4f3f8: b.eq            #0xa4f8f0
    // 0xa4f3fc: LoadField: r0 = r3->field_a7
    //     0xa4f3fc: ldur            w0, [x3, #0xa7]
    // 0xa4f400: DecompressPointer r0
    //     0xa4f400: add             x0, x0, HEAP, lsl #32
    // 0xa4f404: LoadField: r3 = r1->field_13
    //     0xa4f404: ldur            w3, [x1, #0x13]
    // 0xa4f408: DecompressPointer r3
    //     0xa4f408: add             x3, x3, HEAP, lsl #32
    // 0xa4f40c: cmp             w0, NULL
    // 0xa4f410: b.eq            #0xa4f8f4
    // 0xa4f414: stp             x3, x0, [SP]
    // 0xa4f418: ClosureCall
    //     0xa4f418: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa4f41c: ldur            x2, [x0, #0x1f]
    //     0xa4f420: blr             x2
    // 0xa4f424: mov             x1, x0
    // 0xa4f428: ldur            x0, [fp, #-8]
    // 0xa4f42c: stur            x1, [fp, #-0x60]
    // 0xa4f430: LoadField: r2 = r0->field_f
    //     0xa4f430: ldur            w2, [x0, #0xf]
    // 0xa4f434: DecompressPointer r2
    //     0xa4f434: add             x2, x2, HEAP, lsl #32
    // 0xa4f438: LoadField: r3 = r2->field_b
    //     0xa4f438: ldur            w3, [x2, #0xb]
    // 0xa4f43c: DecompressPointer r3
    //     0xa4f43c: add             x3, x3, HEAP, lsl #32
    // 0xa4f440: cmp             w3, NULL
    // 0xa4f444: b.eq            #0xa4f8f8
    // 0xa4f448: LoadField: r2 = r0->field_1b
    //     0xa4f448: ldur            w2, [x0, #0x1b]
    // 0xa4f44c: DecompressPointer r2
    //     0xa4f44c: add             x2, x2, HEAP, lsl #32
    // 0xa4f450: stur            x2, [fp, #-0x58]
    // 0xa4f454: r0 = CellContent()
    //     0xa4f454: bl              #0xa4f928  ; AllocateCellContentStub -> CellContent (size=0x48)
    // 0xa4f458: mov             x2, x0
    // 0xa4f45c: ldur            x0, [fp, #-0x40]
    // 0xa4f460: stur            x2, [fp, #-0x70]
    // 0xa4f464: StoreField: r2->field_b = r0
    //     0xa4f464: stur            w0, [x2, #0xb]
    // 0xa4f468: ldur            x0, [fp, #-0x38]
    // 0xa4f46c: StoreField: r2->field_f = r0
    //     0xa4f46c: stur            w0, [x2, #0xf]
    // 0xa4f470: r0 = Instance_CalendarStyle
    //     0xa4f470: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ef0] Obj!CalendarStyle@e0c001
    //     0xa4f474: ldr             x0, [x0, #0xef0]
    // 0xa4f478: StoreField: r2->field_3f = r0
    //     0xa4f478: stur            w0, [x2, #0x3f]
    // 0xa4f47c: ldur            x0, [fp, #-0x30]
    // 0xa4f480: StoreField: r2->field_43 = r0
    //     0xa4f480: stur            w0, [x2, #0x43]
    // 0xa4f484: r0 = true
    //     0xa4f484: add             x0, NULL, #0x20  ; true
    // 0xa4f488: ArrayStore: r2[0] = r0  ; List_4
    //     0xa4f488: stur            w0, [x2, #0x17]
    // 0xa4f48c: ldur            x0, [fp, #-0x18]
    // 0xa4f490: StoreField: r2->field_1b = r0
    //     0xa4f490: stur            w0, [x2, #0x1b]
    // 0xa4f494: ldur            x0, [fp, #-0x50]
    // 0xa4f498: StoreField: r2->field_1f = r0
    //     0xa4f498: stur            w0, [x2, #0x1f]
    // 0xa4f49c: r0 = false
    //     0xa4f49c: add             x0, NULL, #0x30  ; false
    // 0xa4f4a0: StoreField: r2->field_23 = r0
    //     0xa4f4a0: stur            w0, [x2, #0x23]
    // 0xa4f4a4: StoreField: r2->field_27 = r0
    //     0xa4f4a4: stur            w0, [x2, #0x27]
    // 0xa4f4a8: StoreField: r2->field_2b = r0
    //     0xa4f4a8: stur            w0, [x2, #0x2b]
    // 0xa4f4ac: ldur            x0, [fp, #-0x58]
    // 0xa4f4b0: StoreField: r2->field_2f = r0
    //     0xa4f4b0: stur            w0, [x2, #0x2f]
    // 0xa4f4b4: ldur            x0, [fp, #-0x20]
    // 0xa4f4b8: StoreField: r2->field_33 = r0
    //     0xa4f4b8: stur            w0, [x2, #0x33]
    // 0xa4f4bc: ldur            x1, [fp, #-0x60]
    // 0xa4f4c0: StoreField: r2->field_37 = r1
    //     0xa4f4c0: stur            w1, [x2, #0x37]
    // 0xa4f4c4: ldur            x1, [fp, #-0x28]
    // 0xa4f4c8: StoreField: r2->field_3b = r1
    //     0xa4f4c8: stur            w1, [x2, #0x3b]
    // 0xa4f4cc: ldur            x1, [fp, #-0x48]
    // 0xa4f4d0: StoreField: r2->field_7 = r1
    //     0xa4f4d0: stur            w1, [x2, #7]
    // 0xa4f4d4: ldur            x3, [fp, #-0x10]
    // 0xa4f4d8: LoadField: r1 = r3->field_b
    //     0xa4f4d8: ldur            w1, [x3, #0xb]
    // 0xa4f4dc: LoadField: r4 = r3->field_f
    //     0xa4f4dc: ldur            w4, [x3, #0xf]
    // 0xa4f4e0: DecompressPointer r4
    //     0xa4f4e0: add             x4, x4, HEAP, lsl #32
    // 0xa4f4e4: LoadField: r5 = r4->field_b
    //     0xa4f4e4: ldur            w5, [x4, #0xb]
    // 0xa4f4e8: r4 = LoadInt32Instr(r1)
    //     0xa4f4e8: sbfx            x4, x1, #1, #0x1f
    // 0xa4f4ec: stur            x4, [fp, #-0x68]
    // 0xa4f4f0: r1 = LoadInt32Instr(r5)
    //     0xa4f4f0: sbfx            x1, x5, #1, #0x1f
    // 0xa4f4f4: cmp             x4, x1
    // 0xa4f4f8: b.ne            #0xa4f504
    // 0xa4f4fc: mov             x1, x3
    // 0xa4f500: r0 = _growToNextCapacity()
    //     0xa4f500: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4f504: ldur            x5, [fp, #-8]
    // 0xa4f508: ldur            x4, [fp, #-0x10]
    // 0xa4f50c: ldur            x2, [fp, #-0x20]
    // 0xa4f510: ldur            x3, [fp, #-0x68]
    // 0xa4f514: add             x0, x3, #1
    // 0xa4f518: lsl             x1, x0, #1
    // 0xa4f51c: StoreField: r4->field_b = r1
    //     0xa4f51c: stur            w1, [x4, #0xb]
    // 0xa4f520: LoadField: r1 = r4->field_f
    //     0xa4f520: ldur            w1, [x4, #0xf]
    // 0xa4f524: DecompressPointer r1
    //     0xa4f524: add             x1, x1, HEAP, lsl #32
    // 0xa4f528: ldur            x0, [fp, #-0x70]
    // 0xa4f52c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4f52c: add             x25, x1, x3, lsl #2
    //     0xa4f530: add             x25, x25, #0xf
    //     0xa4f534: str             w0, [x25]
    //     0xa4f538: tbz             w0, #0, #0xa4f554
    //     0xa4f53c: ldurb           w16, [x1, #-1]
    //     0xa4f540: ldurb           w17, [x0, #-1]
    //     0xa4f544: and             x16, x17, x16, lsr #2
    //     0xa4f548: tst             x16, HEAP, lsr #32
    //     0xa4f54c: b.eq            #0xa4f554
    //     0xa4f550: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4f554: LoadField: r0 = r5->field_f
    //     0xa4f554: ldur            w0, [x5, #0xf]
    // 0xa4f558: DecompressPointer r0
    //     0xa4f558: add             x0, x0, HEAP, lsl #32
    // 0xa4f55c: LoadField: r1 = r0->field_b
    //     0xa4f55c: ldur            w1, [x0, #0xb]
    // 0xa4f560: DecompressPointer r1
    //     0xa4f560: add             x1, x1, HEAP, lsl #32
    // 0xa4f564: cmp             w1, NULL
    // 0xa4f568: b.eq            #0xa4f8fc
    // 0xa4f56c: tbz             w2, #4, #0xa4f87c
    // 0xa4f570: LoadField: r2 = r0->field_7
    //     0xa4f570: ldur            w2, [x0, #7]
    // 0xa4f574: DecompressPointer r2
    //     0xa4f574: add             x2, x2, HEAP, lsl #32
    // 0xa4f578: r1 = Null
    //     0xa4f578: mov             x1, NULL
    // 0xa4f57c: r3 = <C1X0>
    //     0xa4f57c: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2af98] TypeArguments: <C1X0>
    //     0xa4f580: ldr             x3, [x3, #0xf98]
    // 0xa4f584: r0 = Null
    //     0xa4f584: mov             x0, NULL
    // 0xa4f588: cmp             x2, x0
    // 0xa4f58c: b.eq            #0xa4f59c
    // 0xa4f590: r30 = InstantiateTypeArgumentsStub
    //     0xa4f590: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xa4f594: LoadField: r30 = r30->field_7
    //     0xa4f594: ldur            lr, [lr, #7]
    // 0xa4f598: blr             lr
    // 0xa4f59c: mov             x1, x0
    // 0xa4f5a0: r2 = 0
    //     0xa4f5a0: movz            x2, #0
    // 0xa4f5a4: r0 = _GrowableList()
    //     0xa4f5a4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xa4f5a8: mov             x4, x0
    // 0xa4f5ac: ldur            x3, [fp, #-8]
    // 0xa4f5b0: stur            x4, [fp, #-0x18]
    // 0xa4f5b4: LoadField: r0 = r3->field_f
    //     0xa4f5b4: ldur            w0, [x3, #0xf]
    // 0xa4f5b8: DecompressPointer r0
    //     0xa4f5b8: add             x0, x0, HEAP, lsl #32
    // 0xa4f5bc: LoadField: r1 = r0->field_b
    //     0xa4f5bc: ldur            w1, [x0, #0xb]
    // 0xa4f5c0: DecompressPointer r1
    //     0xa4f5c0: add             x1, x1, HEAP, lsl #32
    // 0xa4f5c4: cmp             w1, NULL
    // 0xa4f5c8: b.eq            #0xa4f900
    // 0xa4f5cc: LoadField: r2 = r0->field_7
    //     0xa4f5cc: ldur            w2, [x0, #7]
    // 0xa4f5d0: DecompressPointer r2
    //     0xa4f5d0: add             x2, x2, HEAP, lsl #32
    // 0xa4f5d4: r0 = Null
    //     0xa4f5d4: mov             x0, NULL
    // 0xa4f5d8: r1 = Null
    //     0xa4f5d8: mov             x1, NULL
    // 0xa4f5dc: r8 = ((dynamic this, BuildContext, DateTime, List<C1X0>) => Widget?)?
    //     0xa4f5dc: add             x8, PP, #0x47, lsl #12  ; [pp+0x47e20] FunctionType: ((dynamic this, BuildContext, DateTime, List<C1X0>) => Widget?)?
    //     0xa4f5e0: ldr             x8, [x8, #0xe20]
    // 0xa4f5e4: LoadField: r9 = r8->field_7
    //     0xa4f5e4: ldur            x9, [x8, #7]
    // 0xa4f5e8: r3 = Null
    //     0xa4f5e8: add             x3, PP, #0x47, lsl #12  ; [pp+0x47e28] Null
    //     0xa4f5ec: ldr             x3, [x3, #0xe28]
    // 0xa4f5f0: blr             x9
    // 0xa4f5f4: ldur            x1, [fp, #-0x18]
    // 0xa4f5f8: LoadField: r0 = r1->field_b
    //     0xa4f5f8: ldur            w0, [x1, #0xb]
    // 0xa4f5fc: cbz             w0, #0xa4f7e8
    // 0xa4f600: ldur            x0, [fp, #-8]
    // 0xa4f604: ldur            d0, [fp, #-0x80]
    // 0xa4f608: ldur            d1, [fp, #-0x78]
    // 0xa4f60c: r1 = 1
    //     0xa4f60c: movz            x1, #0x1
    // 0xa4f610: r0 = AllocateContext()
    //     0xa4f610: bl              #0xec126c  ; AllocateContextStub
    // 0xa4f614: mov             x2, x0
    // 0xa4f618: ldur            x0, [fp, #-8]
    // 0xa4f61c: stur            x2, [fp, #-0x20]
    // 0xa4f620: StoreField: r2->field_b = r0
    //     0xa4f620: stur            w0, [x2, #0xb]
    // 0xa4f624: ldur            d0, [fp, #-0x80]
    // 0xa4f628: d1 = 2.000000
    //     0xa4f628: fmov            d1, #2.00000000
    // 0xa4f62c: fdiv            d2, d0, d1
    // 0xa4f630: stur            d2, [fp, #-0x88]
    // 0xa4f634: r1 = Instance_EdgeInsets
    //     0xa4f634: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4f638: r0 = vertical()
    //     0xa4f638: bl              #0x72e244  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::vertical
    // 0xa4f63c: mov             v1.16b, v0.16b
    // 0xa4f640: ldur            d0, [fp, #-0x78]
    // 0xa4f644: fsub            d2, d0, d1
    // 0xa4f648: ldur            x2, [fp, #-8]
    // 0xa4f64c: LoadField: r0 = r2->field_f
    //     0xa4f64c: ldur            w0, [x2, #0xf]
    // 0xa4f650: DecompressPointer r0
    //     0xa4f650: add             x0, x0, HEAP, lsl #32
    // 0xa4f654: LoadField: r1 = r0->field_b
    //     0xa4f654: ldur            w1, [x0, #0xb]
    // 0xa4f658: DecompressPointer r1
    //     0xa4f658: add             x1, x1, HEAP, lsl #32
    // 0xa4f65c: cmp             w1, NULL
    // 0xa4f660: b.eq            #0xa4f904
    // 0xa4f664: d1 = 0.200000
    //     0xa4f664: ldr             d1, [PP, #0x5b18]  ; [pp+0x5b18] IMM: double(0.2) from 0x3fc999999999999a
    // 0xa4f668: fmul            d3, d2, d1
    // 0xa4f66c: stur            d3, [fp, #-0x80]
    // 0xa4f670: r0 = inline_Allocate_Double()
    //     0xa4f670: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa4f674: add             x0, x0, #0x10
    //     0xa4f678: cmp             x1, x0
    //     0xa4f67c: b.ls            #0xa4f908
    //     0xa4f680: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4f684: sub             x0, x0, #0xf
    //     0xa4f688: movz            x1, #0xe15c
    //     0xa4f68c: movk            x1, #0x3, lsl #16
    //     0xa4f690: stur            x1, [x0, #-1]
    // 0xa4f694: StoreField: r0->field_7 = d3
    //     0xa4f694: stur            d3, [x0, #7]
    // 0xa4f698: ldur            x3, [fp, #-0x20]
    // 0xa4f69c: StoreField: r3->field_f = r0
    //     0xa4f69c: stur            w0, [x3, #0xf]
    //     0xa4f6a0: ldurb           w16, [x3, #-1]
    //     0xa4f6a4: ldurb           w17, [x0, #-1]
    //     0xa4f6a8: and             x16, x17, x16, lsr #2
    //     0xa4f6ac: tst             x16, HEAP, lsr #32
    //     0xa4f6b0: b.eq            #0xa4f6b8
    //     0xa4f6b4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xa4f6b8: r1 = Instance_EdgeInsets
    //     0xa4f6b8: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4f6bc: r0 = vertical()
    //     0xa4f6bc: bl              #0x72e244  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::vertical
    // 0xa4f6c0: mov             v1.16b, v0.16b
    // 0xa4f6c4: ldur            d0, [fp, #-0x78]
    // 0xa4f6c8: fsub            d2, d0, d1
    // 0xa4f6cc: d0 = 2.000000
    //     0xa4f6cc: fmov            d0, #2.00000000
    // 0xa4f6d0: fdiv            d1, d2, d0
    // 0xa4f6d4: ldur            d0, [fp, #-0x88]
    // 0xa4f6d8: fadd            d2, d0, d1
    // 0xa4f6dc: ldur            x0, [fp, #-8]
    // 0xa4f6e0: LoadField: r1 = r0->field_f
    //     0xa4f6e0: ldur            w1, [x0, #0xf]
    // 0xa4f6e4: DecompressPointer r1
    //     0xa4f6e4: add             x1, x1, HEAP, lsl #32
    // 0xa4f6e8: LoadField: r2 = r1->field_b
    //     0xa4f6e8: ldur            w2, [x1, #0xb]
    // 0xa4f6ec: DecompressPointer r2
    //     0xa4f6ec: add             x2, x2, HEAP, lsl #32
    // 0xa4f6f0: cmp             w2, NULL
    // 0xa4f6f4: b.eq            #0xa4f920
    // 0xa4f6f8: ldur            d0, [fp, #-0x80]
    // 0xa4f6fc: d1 = 0.700000
    //     0xa4f6fc: add             x17, PP, #0x34, lsl #12  ; [pp+0x34120] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa4f700: ldr             d1, [x17, #0x120]
    // 0xa4f704: fmul            d3, d0, d1
    // 0xa4f708: fsub            d0, d2, d3
    // 0xa4f70c: ldur            x1, [fp, #-0x18]
    // 0xa4f710: stur            d0, [fp, #-0x78]
    // 0xa4f714: r2 = 4
    //     0xa4f714: movz            x2, #0x4
    // 0xa4f718: r0 = take()
    //     0xa4f718: bl              #0x8630a0  ; [dart:collection] ListBase::take
    // 0xa4f71c: mov             x4, x0
    // 0xa4f720: ldur            x0, [fp, #-8]
    // 0xa4f724: stur            x4, [fp, #-0x18]
    // 0xa4f728: LoadField: r1 = r0->field_f
    //     0xa4f728: ldur            w1, [x0, #0xf]
    // 0xa4f72c: DecompressPointer r1
    //     0xa4f72c: add             x1, x1, HEAP, lsl #32
    // 0xa4f730: LoadField: r3 = r1->field_7
    //     0xa4f730: ldur            w3, [x1, #7]
    // 0xa4f734: DecompressPointer r3
    //     0xa4f734: add             x3, x3, HEAP, lsl #32
    // 0xa4f738: ldur            x2, [fp, #-0x20]
    // 0xa4f73c: r1 = Function '<anonymous closure>':.
    //     0xa4f73c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47e38] AnonymousClosure: (0xa4fbc0), in [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_buildCell (0xa4f010)
    //     0xa4f740: ldr             x1, [x1, #0xe38]
    // 0xa4f744: r0 = AllocateClosureTA()
    //     0xa4f744: bl              #0xec1474  ; AllocateClosureTAStub
    // 0xa4f748: r16 = <Widget>
    //     0xa4f748: ldr             x16, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xa4f74c: ldur            lr, [fp, #-0x18]
    // 0xa4f750: stp             lr, x16, [SP, #8]
    // 0xa4f754: str             x0, [SP]
    // 0xa4f758: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa4f758: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa4f75c: r0 = map()
    //     0xa4f75c: bl              #0x7abe64  ; [dart:_internal] ListIterable::map
    // 0xa4f760: LoadField: r1 = r0->field_7
    //     0xa4f760: ldur            w1, [x0, #7]
    // 0xa4f764: DecompressPointer r1
    //     0xa4f764: add             x1, x1, HEAP, lsl #32
    // 0xa4f768: mov             x2, x0
    // 0xa4f76c: r0 = _GrowableList.of()
    //     0xa4f76c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xa4f770: stur            x0, [fp, #-0x18]
    // 0xa4f774: r0 = Row()
    //     0xa4f774: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa4f778: mov             x1, x0
    // 0xa4f77c: r0 = Instance_Axis
    //     0xa4f77c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xa4f780: stur            x1, [fp, #-0x20]
    // 0xa4f784: StoreField: r1->field_f = r0
    //     0xa4f784: stur            w0, [x1, #0xf]
    // 0xa4f788: r0 = Instance_MainAxisAlignment
    //     0xa4f788: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xa4f78c: ldr             x0, [x0, #0x730]
    // 0xa4f790: StoreField: r1->field_13 = r0
    //     0xa4f790: stur            w0, [x1, #0x13]
    // 0xa4f794: r0 = Instance_MainAxisSize
    //     0xa4f794: add             x0, PP, #0x29, lsl #12  ; [pp+0x29e88] Obj!MainAxisSize@e35b01
    //     0xa4f798: ldr             x0, [x0, #0xe88]
    // 0xa4f79c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa4f79c: stur            w0, [x1, #0x17]
    // 0xa4f7a0: r0 = Instance_CrossAxisAlignment
    //     0xa4f7a0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xa4f7a4: ldr             x0, [x0, #0x740]
    // 0xa4f7a8: StoreField: r1->field_1b = r0
    //     0xa4f7a8: stur            w0, [x1, #0x1b]
    // 0xa4f7ac: r0 = Instance_VerticalDirection
    //     0xa4f7ac: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xa4f7b0: ldr             x0, [x0, #0x748]
    // 0xa4f7b4: StoreField: r1->field_23 = r0
    //     0xa4f7b4: stur            w0, [x1, #0x23]
    // 0xa4f7b8: r0 = Instance_Clip
    //     0xa4f7b8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa4f7bc: ldr             x0, [x0, #0x750]
    // 0xa4f7c0: StoreField: r1->field_2b = r0
    //     0xa4f7c0: stur            w0, [x1, #0x2b]
    // 0xa4f7c4: StoreField: r1->field_2f = rZR
    //     0xa4f7c4: stur            xzr, [x1, #0x2f]
    // 0xa4f7c8: ldur            x2, [fp, #-0x18]
    // 0xa4f7cc: StoreField: r1->field_b = r2
    //     0xa4f7cc: stur            w2, [x1, #0xb]
    // 0xa4f7d0: r0 = PositionedDirectional()
    //     0xa4f7d0: bl              #0x9daaa4  ; AllocatePositionedDirectionalStub -> PositionedDirectional (size=0x2c)
    // 0xa4f7d4: ldur            d0, [fp, #-0x78]
    // 0xa4f7d8: StoreField: r0->field_f = d0
    //     0xa4f7d8: stur            d0, [x0, #0xf]
    // 0xa4f7dc: ldur            x1, [fp, #-0x20]
    // 0xa4f7e0: StoreField: r0->field_27 = r1
    //     0xa4f7e0: stur            w1, [x0, #0x27]
    // 0xa4f7e4: b               #0xa4f7ec
    // 0xa4f7e8: r0 = Null
    //     0xa4f7e8: mov             x0, NULL
    // 0xa4f7ec: stur            x0, [fp, #-0x18]
    // 0xa4f7f0: cmp             w0, NULL
    // 0xa4f7f4: b.eq            #0xa4f874
    // 0xa4f7f8: ldur            x2, [fp, #-0x10]
    // 0xa4f7fc: LoadField: r1 = r2->field_b
    //     0xa4f7fc: ldur            w1, [x2, #0xb]
    // 0xa4f800: LoadField: r3 = r2->field_f
    //     0xa4f800: ldur            w3, [x2, #0xf]
    // 0xa4f804: DecompressPointer r3
    //     0xa4f804: add             x3, x3, HEAP, lsl #32
    // 0xa4f808: LoadField: r4 = r3->field_b
    //     0xa4f808: ldur            w4, [x3, #0xb]
    // 0xa4f80c: r3 = LoadInt32Instr(r1)
    //     0xa4f80c: sbfx            x3, x1, #1, #0x1f
    // 0xa4f810: stur            x3, [fp, #-0x68]
    // 0xa4f814: r1 = LoadInt32Instr(r4)
    //     0xa4f814: sbfx            x1, x4, #1, #0x1f
    // 0xa4f818: cmp             x3, x1
    // 0xa4f81c: b.ne            #0xa4f828
    // 0xa4f820: mov             x1, x2
    // 0xa4f824: r0 = _growToNextCapacity()
    //     0xa4f824: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4f828: ldur            x2, [fp, #-0x10]
    // 0xa4f82c: ldur            x3, [fp, #-0x68]
    // 0xa4f830: add             x0, x3, #1
    // 0xa4f834: lsl             x1, x0, #1
    // 0xa4f838: StoreField: r2->field_b = r1
    //     0xa4f838: stur            w1, [x2, #0xb]
    // 0xa4f83c: LoadField: r1 = r2->field_f
    //     0xa4f83c: ldur            w1, [x2, #0xf]
    // 0xa4f840: DecompressPointer r1
    //     0xa4f840: add             x1, x1, HEAP, lsl #32
    // 0xa4f844: ldur            x0, [fp, #-0x18]
    // 0xa4f848: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4f848: add             x25, x1, x3, lsl #2
    //     0xa4f84c: add             x25, x25, #0xf
    //     0xa4f850: str             w0, [x25]
    //     0xa4f854: tbz             w0, #0, #0xa4f870
    //     0xa4f858: ldurb           w16, [x1, #-1]
    //     0xa4f85c: ldurb           w17, [x0, #-1]
    //     0xa4f860: and             x16, x17, x16, lsr #2
    //     0xa4f864: tst             x16, HEAP, lsr #32
    //     0xa4f868: b.eq            #0xa4f870
    //     0xa4f86c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xa4f870: b               #0xa4f880
    // 0xa4f874: ldur            x2, [fp, #-0x10]
    // 0xa4f878: b               #0xa4f880
    // 0xa4f87c: mov             x2, x4
    // 0xa4f880: ldur            x0, [fp, #-8]
    // 0xa4f884: LoadField: r1 = r0->field_f
    //     0xa4f884: ldur            w1, [x0, #0xf]
    // 0xa4f888: DecompressPointer r1
    //     0xa4f888: add             x1, x1, HEAP, lsl #32
    // 0xa4f88c: LoadField: r0 = r1->field_b
    //     0xa4f88c: ldur            w0, [x1, #0xb]
    // 0xa4f890: DecompressPointer r0
    //     0xa4f890: add             x0, x0, HEAP, lsl #32
    // 0xa4f894: cmp             w0, NULL
    // 0xa4f898: b.eq            #0xa4f924
    // 0xa4f89c: r0 = Stack()
    //     0xa4f89c: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa4f8a0: r1 = Instance_Alignment
    //     0xa4f8a0: add             x1, PP, #0x27, lsl #12  ; [pp+0x270b0] Obj!Alignment@e13e91
    //     0xa4f8a4: ldr             x1, [x1, #0xb0]
    // 0xa4f8a8: StoreField: r0->field_f = r1
    //     0xa4f8a8: stur            w1, [x0, #0xf]
    // 0xa4f8ac: r1 = Instance_StackFit
    //     0xa4f8ac: add             x1, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xa4f8b0: ldr             x1, [x1, #0x7b8]
    // 0xa4f8b4: ArrayStore: r0[0] = r1  ; List_4
    //     0xa4f8b4: stur            w1, [x0, #0x17]
    // 0xa4f8b8: r1 = Instance_Clip
    //     0xa4f8b8: add             x1, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xa4f8bc: ldr             x1, [x1, #0x750]
    // 0xa4f8c0: StoreField: r0->field_1b = r1
    //     0xa4f8c0: stur            w1, [x0, #0x1b]
    // 0xa4f8c4: ldur            x1, [fp, #-0x10]
    // 0xa4f8c8: StoreField: r0->field_b = r1
    //     0xa4f8c8: stur            w1, [x0, #0xb]
    // 0xa4f8cc: LeaveFrame
    //     0xa4f8cc: mov             SP, fp
    //     0xa4f8d0: ldp             fp, lr, [SP], #0x10
    // 0xa4f8d4: ret
    //     0xa4f8d4: ret             
    // 0xa4f8d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4f8d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4f8dc: b               #0xa4f13c
    // 0xa4f8e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f8e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f8e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f8e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f8e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f8e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f8ec: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa4f8ec: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xa4f8f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f8f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f8f4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa4f8f4: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0xa4f8f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f8f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f8fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f8fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f900: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f900: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4f904: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4f904: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4f908: stp             q0, q3, [SP, #-0x20]!
    // 0xa4f90c: SaveReg r2
    //     0xa4f90c: str             x2, [SP, #-8]!
    // 0xa4f910: r0 = AllocateDouble()
    //     0xa4f910: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4f914: RestoreReg r2
    //     0xa4f914: ldr             x2, [SP], #8
    // 0xa4f918: ldp             q0, q3, [SP], #0x20
    // 0xa4f91c: b               #0xa4f694
    // 0xa4f920: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4f920: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4f924: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4f924: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _isWeekend(/* No info */) {
    // ** addr: 0xa4f934, size: 0x6c
    // 0xa4f934: EnterFrame
    //     0xa4f934: stp             fp, lr, [SP, #-0x10]!
    //     0xa4f938: mov             fp, SP
    // 0xa4f93c: mov             x0, x1
    // 0xa4f940: mov             x1, x2
    // 0xa4f944: CheckStackOverflow
    //     0xa4f944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4f948: cmp             SP, x16
    //     0xa4f94c: b.ls            #0xa4f998
    // 0xa4f950: r0 = LoadClassIdInstr(r1)
    //     0xa4f950: ldur            x0, [x1, #-1]
    //     0xa4f954: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f958: r0 = GDT[cid_x0 + -0xfad]()
    //     0xa4f958: sub             lr, x0, #0xfad
    //     0xa4f95c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f960: blr             lr
    // 0xa4f964: mov             x2, x0
    // 0xa4f968: r0 = BoxInt64Instr(r2)
    //     0xa4f968: sbfiz           x0, x2, #1, #0x1f
    //     0xa4f96c: cmp             x2, x0, asr #1
    //     0xa4f970: b.eq            #0xa4f97c
    //     0xa4f974: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4f978: stur            x2, [x0, #7]
    // 0xa4f97c: mov             x2, x0
    // 0xa4f980: r1 = const [0x6, 0x7]
    //     0xa4f980: add             x1, PP, #0x35, lsl #12  ; [pp+0x35eb0] List<int>(2)
    //     0xa4f984: ldr             x1, [x1, #0xeb0]
    // 0xa4f988: r0 = contains()
    //     0xa4f988: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0xa4f98c: LeaveFrame
    //     0xa4f98c: mov             SP, fp
    //     0xa4f990: ldp             fp, lr, [SP], #0x10
    // 0xa4f994: ret
    //     0xa4f994: ret             
    // 0xa4f998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4f998: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4f99c: b               #0xa4f950
  }
  _ _isDayDisabled(/* No info */) {
    // ** addr: 0xa4f9a0, size: 0xd4
    // 0xa4f9a0: EnterFrame
    //     0xa4f9a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa4f9a4: mov             fp, SP
    // 0xa4f9a8: AllocStack(0x10)
    //     0xa4f9a8: sub             SP, SP, #0x10
    // 0xa4f9ac: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xa4f9ac: mov             x4, x1
    //     0xa4f9b0: mov             x3, x2
    //     0xa4f9b4: stur            x1, [fp, #-8]
    //     0xa4f9b8: stur            x2, [fp, #-0x10]
    // 0xa4f9bc: CheckStackOverflow
    //     0xa4f9bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4f9c0: cmp             SP, x16
    //     0xa4f9c4: b.ls            #0xa4fa60
    // 0xa4f9c8: LoadField: r0 = r4->field_b
    //     0xa4f9c8: ldur            w0, [x4, #0xb]
    // 0xa4f9cc: DecompressPointer r0
    //     0xa4f9cc: add             x0, x0, HEAP, lsl #32
    // 0xa4f9d0: cmp             w0, NULL
    // 0xa4f9d4: b.eq            #0xa4fa68
    // 0xa4f9d8: LoadField: r2 = r0->field_1f
    //     0xa4f9d8: ldur            w2, [x0, #0x1f]
    // 0xa4f9dc: DecompressPointer r2
    //     0xa4f9dc: add             x2, x2, HEAP, lsl #32
    // 0xa4f9e0: r0 = LoadClassIdInstr(r3)
    //     0xa4f9e0: ldur            x0, [x3, #-1]
    //     0xa4f9e4: ubfx            x0, x0, #0xc, #0x14
    // 0xa4f9e8: mov             x1, x3
    // 0xa4f9ec: r0 = GDT[cid_x0 + -0xf0c]()
    //     0xa4f9ec: sub             lr, x0, #0xf0c
    //     0xa4f9f0: ldr             lr, [x21, lr, lsl #3]
    //     0xa4f9f4: blr             lr
    // 0xa4f9f8: tbz             w0, #4, #0xa4fa34
    // 0xa4f9fc: ldur            x3, [fp, #-8]
    // 0xa4fa00: ldur            x1, [fp, #-0x10]
    // 0xa4fa04: LoadField: r0 = r3->field_b
    //     0xa4fa04: ldur            w0, [x3, #0xb]
    // 0xa4fa08: DecompressPointer r0
    //     0xa4fa08: add             x0, x0, HEAP, lsl #32
    // 0xa4fa0c: cmp             w0, NULL
    // 0xa4fa10: b.eq            #0xa4fa6c
    // 0xa4fa14: LoadField: r2 = r0->field_23
    //     0xa4fa14: ldur            w2, [x0, #0x23]
    // 0xa4fa18: DecompressPointer r2
    //     0xa4fa18: add             x2, x2, HEAP, lsl #32
    // 0xa4fa1c: r0 = LoadClassIdInstr(r1)
    //     0xa4fa1c: ldur            x0, [x1, #-1]
    //     0xa4fa20: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fa24: r0 = GDT[cid_x0 + -0xf8d]()
    //     0xa4fa24: sub             lr, x0, #0xf8d
    //     0xa4fa28: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fa2c: blr             lr
    // 0xa4fa30: tbnz            w0, #4, #0xa4fa3c
    // 0xa4fa34: r0 = true
    //     0xa4fa34: add             x0, NULL, #0x20  ; true
    // 0xa4fa38: b               #0xa4fa54
    // 0xa4fa3c: ldur            x1, [fp, #-8]
    // 0xa4fa40: LoadField: r2 = r1->field_b
    //     0xa4fa40: ldur            w2, [x1, #0xb]
    // 0xa4fa44: DecompressPointer r2
    //     0xa4fa44: add             x2, x2, HEAP, lsl #32
    // 0xa4fa48: cmp             w2, NULL
    // 0xa4fa4c: b.eq            #0xa4fa70
    // 0xa4fa50: r0 = false
    //     0xa4fa50: add             x0, NULL, #0x30  ; false
    // 0xa4fa54: LeaveFrame
    //     0xa4fa54: mov             SP, fp
    //     0xa4fa58: ldp             fp, lr, [SP], #0x10
    // 0xa4fa5c: ret
    //     0xa4fa5c: ret             
    // 0xa4fa60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4fa60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4fa64: b               #0xa4f9c8
    // 0xa4fa68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fa68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fa6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fa6c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fa70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fa70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, C1X0) {
    // ** addr: 0xa4fbc0, size: 0x5c
    // 0xa4fbc0: EnterFrame
    //     0xa4fbc0: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fbc4: mov             fp, SP
    // 0xa4fbc8: ldr             x0, [fp, #0x18]
    // 0xa4fbcc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4fbcc: ldur            w1, [x0, #0x17]
    // 0xa4fbd0: DecompressPointer r1
    //     0xa4fbd0: add             x1, x1, HEAP, lsl #32
    // 0xa4fbd4: CheckStackOverflow
    //     0xa4fbd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fbd8: cmp             SP, x16
    //     0xa4fbdc: b.ls            #0xa4fc14
    // 0xa4fbe0: LoadField: r0 = r1->field_b
    //     0xa4fbe0: ldur            w0, [x1, #0xb]
    // 0xa4fbe4: DecompressPointer r0
    //     0xa4fbe4: add             x0, x0, HEAP, lsl #32
    // 0xa4fbe8: LoadField: r2 = r0->field_f
    //     0xa4fbe8: ldur            w2, [x0, #0xf]
    // 0xa4fbec: DecompressPointer r2
    //     0xa4fbec: add             x2, x2, HEAP, lsl #32
    // 0xa4fbf0: LoadField: r0 = r1->field_f
    //     0xa4fbf0: ldur            w0, [x1, #0xf]
    // 0xa4fbf4: DecompressPointer r0
    //     0xa4fbf4: add             x0, x0, HEAP, lsl #32
    // 0xa4fbf8: LoadField: d0 = r0->field_7
    //     0xa4fbf8: ldur            d0, [x0, #7]
    // 0xa4fbfc: mov             x1, x2
    // 0xa4fc00: ldr             x2, [fp, #0x10]
    // 0xa4fc04: r0 = _buildSingleMarker()
    //     0xa4fc04: bl              #0xa4fc1c  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_buildSingleMarker
    // 0xa4fc08: LeaveFrame
    //     0xa4fc08: mov             SP, fp
    //     0xa4fc0c: ldp             fp, lr, [SP], #0x10
    // 0xa4fc10: ret
    //     0xa4fc10: ret             
    // 0xa4fc14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4fc14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4fc18: b               #0xa4fbe0
  }
  _ _buildSingleMarker(/* No info */) {
    // ** addr: 0xa4fc1c, size: 0xe8
    // 0xa4fc1c: EnterFrame
    //     0xa4fc1c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fc20: mov             fp, SP
    // 0xa4fc24: AllocStack(0x38)
    //     0xa4fc24: sub             SP, SP, #0x38
    // 0xa4fc28: SetupParameters(dynamic _ /* d0 => d0, fp-0x18 */)
    //     0xa4fc28: stur            d0, [fp, #-0x18]
    // 0xa4fc2c: CheckStackOverflow
    //     0xa4fc2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fc30: cmp             SP, x16
    //     0xa4fc34: b.ls            #0xa4fce8
    // 0xa4fc38: LoadField: r0 = r1->field_b
    //     0xa4fc38: ldur            w0, [x1, #0xb]
    // 0xa4fc3c: DecompressPointer r0
    //     0xa4fc3c: add             x0, x0, HEAP, lsl #32
    // 0xa4fc40: cmp             w0, NULL
    // 0xa4fc44: b.eq            #0xa4fcf0
    // 0xa4fc48: LoadField: r2 = r1->field_7
    //     0xa4fc48: ldur            w2, [x1, #7]
    // 0xa4fc4c: DecompressPointer r2
    //     0xa4fc4c: add             x2, x2, HEAP, lsl #32
    // 0xa4fc50: r0 = Null
    //     0xa4fc50: mov             x0, NULL
    // 0xa4fc54: r1 = Null
    //     0xa4fc54: mov             x1, NULL
    // 0xa4fc58: r8 = ((dynamic this, BuildContext, DateTime, C1X0) => Widget?)?
    //     0xa4fc58: add             x8, PP, #0x47, lsl #12  ; [pp+0x47e40] FunctionType: ((dynamic this, BuildContext, DateTime, C1X0) => Widget?)?
    //     0xa4fc5c: ldr             x8, [x8, #0xe40]
    // 0xa4fc60: LoadField: r9 = r8->field_7
    //     0xa4fc60: ldur            x9, [x8, #7]
    // 0xa4fc64: r3 = Null
    //     0xa4fc64: add             x3, PP, #0x47, lsl #12  ; [pp+0x47e48] Null
    //     0xa4fc68: ldr             x3, [x3, #0xe48]
    // 0xa4fc6c: blr             x9
    // 0xa4fc70: ldur            d0, [fp, #-0x18]
    // 0xa4fc74: r0 = inline_Allocate_Double()
    //     0xa4fc74: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa4fc78: add             x0, x0, #0x10
    //     0xa4fc7c: cmp             x1, x0
    //     0xa4fc80: b.ls            #0xa4fcf4
    //     0xa4fc84: str             x0, [THR, #0x50]  ; THR::top
    //     0xa4fc88: sub             x0, x0, #0xf
    //     0xa4fc8c: movz            x1, #0xe15c
    //     0xa4fc90: movk            x1, #0x3, lsl #16
    //     0xa4fc94: stur            x1, [x0, #-1]
    // 0xa4fc98: StoreField: r0->field_7 = d0
    //     0xa4fc98: stur            d0, [x0, #7]
    // 0xa4fc9c: stur            x0, [fp, #-8]
    // 0xa4fca0: r0 = Container()
    //     0xa4fca0: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa4fca4: stur            x0, [fp, #-0x10]
    // 0xa4fca8: ldur            x16, [fp, #-8]
    // 0xa4fcac: ldur            lr, [fp, #-8]
    // 0xa4fcb0: stp             lr, x16, [SP, #0x10]
    // 0xa4fcb4: r16 = Instance_EdgeInsets
    //     0xa4fcb4: add             x16, PP, #0x47, lsl #12  ; [pp+0x47e58] Obj!EdgeInsets@e12431
    //     0xa4fcb8: ldr             x16, [x16, #0xe58]
    // 0xa4fcbc: r30 = Instance_BoxDecoration
    //     0xa4fcbc: add             lr, PP, #0x47, lsl #12  ; [pp+0x47e60] Obj!BoxDecoration@e1d211
    //     0xa4fcc0: ldr             lr, [lr, #0xe60]
    // 0xa4fcc4: stp             lr, x16, [SP]
    // 0xa4fcc8: mov             x1, x0
    // 0xa4fccc: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x2, margin, 0x3, width, 0x1, null]
    //     0xa4fccc: add             x4, PP, #0x47, lsl #12  ; [pp+0x47e68] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x2, "margin", 0x3, "width", 0x1, Null]
    //     0xa4fcd0: ldr             x4, [x4, #0xe68]
    // 0xa4fcd4: r0 = Container()
    //     0xa4fcd4: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa4fcd8: ldur            x0, [fp, #-0x10]
    // 0xa4fcdc: LeaveFrame
    //     0xa4fcdc: mov             SP, fp
    //     0xa4fce0: ldp             fp, lr, [SP], #0x10
    // 0xa4fce4: ret
    //     0xa4fce4: ret             
    // 0xa4fce8: r0 = StackOverflowSharedWithFPURegs()
    //     0xa4fce8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa4fcec: b               #0xa4fc38
    // 0xa4fcf0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa4fcf0: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa4fcf4: SaveReg d0
    //     0xa4fcf4: str             q0, [SP, #-0x10]!
    // 0xa4fcf8: r0 = AllocateDouble()
    //     0xa4fcf8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa4fcfc: RestoreReg d0
    //     0xa4fcfc: ldr             q0, [SP], #0x10
    // 0xa4fd00: b               #0xa4fc98
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa4fd04, size: 0x5c
    // 0xa4fd04: EnterFrame
    //     0xa4fd04: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fd08: mov             fp, SP
    // 0xa4fd0c: ldr             x0, [fp, #0x10]
    // 0xa4fd10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4fd10: ldur            w1, [x0, #0x17]
    // 0xa4fd14: DecompressPointer r1
    //     0xa4fd14: add             x1, x1, HEAP, lsl #32
    // 0xa4fd18: CheckStackOverflow
    //     0xa4fd18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fd1c: cmp             SP, x16
    //     0xa4fd20: b.ls            #0xa4fd58
    // 0xa4fd24: LoadField: r0 = r1->field_b
    //     0xa4fd24: ldur            w0, [x1, #0xb]
    // 0xa4fd28: DecompressPointer r0
    //     0xa4fd28: add             x0, x0, HEAP, lsl #32
    // 0xa4fd2c: LoadField: r2 = r0->field_f
    //     0xa4fd2c: ldur            w2, [x0, #0xf]
    // 0xa4fd30: DecompressPointer r2
    //     0xa4fd30: add             x2, x2, HEAP, lsl #32
    // 0xa4fd34: LoadField: r0 = r1->field_f
    //     0xa4fd34: ldur            w0, [x1, #0xf]
    // 0xa4fd38: DecompressPointer r0
    //     0xa4fd38: add             x0, x0, HEAP, lsl #32
    // 0xa4fd3c: mov             x1, x2
    // 0xa4fd40: mov             x2, x0
    // 0xa4fd44: r0 = _onDayLongPressed()
    //     0xa4fd44: bl              #0xa4fd60  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_onDayLongPressed
    // 0xa4fd48: r0 = Null
    //     0xa4fd48: mov             x0, NULL
    // 0xa4fd4c: LeaveFrame
    //     0xa4fd4c: mov             SP, fp
    //     0xa4fd50: ldp             fp, lr, [SP], #0x10
    // 0xa4fd54: ret
    //     0xa4fd54: ret             
    // 0xa4fd58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4fd58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4fd5c: b               #0xa4fd24
  }
  _ _onDayLongPressed(/* No info */) {
    // ** addr: 0xa4fd60, size: 0x11c
    // 0xa4fd60: EnterFrame
    //     0xa4fd60: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fd64: mov             fp, SP
    // 0xa4fd68: AllocStack(0x18)
    //     0xa4fd68: sub             SP, SP, #0x18
    // 0xa4fd6c: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa4fd6c: mov             x3, x1
    //     0xa4fd70: stur            x1, [fp, #-8]
    //     0xa4fd74: stur            x2, [fp, #-0x10]
    // 0xa4fd78: CheckStackOverflow
    //     0xa4fd78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fd7c: cmp             SP, x16
    //     0xa4fd80: b.ls            #0xa4fe5c
    // 0xa4fd84: r0 = LoadClassIdInstr(r2)
    //     0xa4fd84: ldur            x0, [x2, #-1]
    //     0xa4fd88: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fd8c: mov             x1, x2
    // 0xa4fd90: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4fd90: sub             lr, x0, #0xfff
    //     0xa4fd94: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fd98: blr             lr
    // 0xa4fd9c: mov             x3, x0
    // 0xa4fda0: ldur            x2, [fp, #-8]
    // 0xa4fda4: stur            x3, [fp, #-0x18]
    // 0xa4fda8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa4fda8: ldur            w0, [x2, #0x17]
    // 0xa4fdac: DecompressPointer r0
    //     0xa4fdac: add             x0, x0, HEAP, lsl #32
    // 0xa4fdb0: r16 = Sentinel
    //     0xa4fdb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4fdb4: cmp             w0, w16
    // 0xa4fdb8: b.eq            #0xa4fe64
    // 0xa4fdbc: LoadField: r1 = r0->field_27
    //     0xa4fdbc: ldur            w1, [x0, #0x27]
    // 0xa4fdc0: DecompressPointer r1
    //     0xa4fdc0: add             x1, x1, HEAP, lsl #32
    // 0xa4fdc4: r0 = LoadClassIdInstr(r1)
    //     0xa4fdc4: ldur            x0, [x1, #-1]
    //     0xa4fdc8: ubfx            x0, x0, #0xc, #0x14
    // 0xa4fdcc: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4fdcc: sub             lr, x0, #0xfff
    //     0xa4fdd0: ldr             lr, [x21, lr, lsl #3]
    //     0xa4fdd4: blr             lr
    // 0xa4fdd8: mov             x1, x0
    // 0xa4fddc: ldur            x0, [fp, #-0x18]
    // 0xa4fde0: cmp             x0, x1
    // 0xa4fde4: b.eq            #0xa4fe00
    // 0xa4fde8: ldur            x0, [fp, #-8]
    // 0xa4fdec: LoadField: r1 = r0->field_b
    //     0xa4fdec: ldur            w1, [x0, #0xb]
    // 0xa4fdf0: DecompressPointer r1
    //     0xa4fdf0: add             x1, x1, HEAP, lsl #32
    // 0xa4fdf4: cmp             w1, NULL
    // 0xa4fdf8: b.eq            #0xa4fe70
    // 0xa4fdfc: b               #0xa4fe04
    // 0xa4fe00: ldur            x0, [fp, #-8]
    // 0xa4fe04: mov             x1, x0
    // 0xa4fe08: ldur            x2, [fp, #-0x10]
    // 0xa4fe0c: r0 = _isDayDisabled()
    //     0xa4fe0c: bl              #0xa4f9a0  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isDayDisabled
    // 0xa4fe10: tbnz            w0, #4, #0xa4fe38
    // 0xa4fe14: ldur            x1, [fp, #-8]
    // 0xa4fe18: LoadField: r2 = r1->field_b
    //     0xa4fe18: ldur            w2, [x1, #0xb]
    // 0xa4fe1c: DecompressPointer r2
    //     0xa4fe1c: add             x2, x2, HEAP, lsl #32
    // 0xa4fe20: cmp             w2, NULL
    // 0xa4fe24: b.eq            #0xa4fe74
    // 0xa4fe28: r0 = Null
    //     0xa4fe28: mov             x0, NULL
    // 0xa4fe2c: LeaveFrame
    //     0xa4fe2c: mov             SP, fp
    //     0xa4fe30: ldp             fp, lr, [SP], #0x10
    // 0xa4fe34: ret
    //     0xa4fe34: ret             
    // 0xa4fe38: ldur            x1, [fp, #-8]
    // 0xa4fe3c: LoadField: r2 = r1->field_b
    //     0xa4fe3c: ldur            w2, [x1, #0xb]
    // 0xa4fe40: DecompressPointer r2
    //     0xa4fe40: add             x2, x2, HEAP, lsl #32
    // 0xa4fe44: cmp             w2, NULL
    // 0xa4fe48: b.eq            #0xa4fe78
    // 0xa4fe4c: r0 = Null
    //     0xa4fe4c: mov             x0, NULL
    // 0xa4fe50: LeaveFrame
    //     0xa4fe50: mov             SP, fp
    //     0xa4fe54: ldp             fp, lr, [SP], #0x10
    // 0xa4fe58: ret
    //     0xa4fe58: ret             
    // 0xa4fe5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4fe5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4fe60: b               #0xa4fd84
    // 0xa4fe64: r9 = _focusedDay
    //     0xa4fe64: add             x9, PP, #0x47, lsl #12  ; [pp+0x47de8] Field <_TableCalendarState@1928116939._focusedDay@1928116939>: late final (offset: 0x18)
    //     0xa4fe68: ldr             x9, [x9, #0xde8]
    // 0xa4fe6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa4fe6c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa4fe70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fe70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fe74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fe74: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4fe78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4fe78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa4fe7c, size: 0x5c
    // 0xa4fe7c: EnterFrame
    //     0xa4fe7c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fe80: mov             fp, SP
    // 0xa4fe84: ldr             x0, [fp, #0x10]
    // 0xa4fe88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa4fe88: ldur            w1, [x0, #0x17]
    // 0xa4fe8c: DecompressPointer r1
    //     0xa4fe8c: add             x1, x1, HEAP, lsl #32
    // 0xa4fe90: CheckStackOverflow
    //     0xa4fe90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fe94: cmp             SP, x16
    //     0xa4fe98: b.ls            #0xa4fed0
    // 0xa4fe9c: LoadField: r0 = r1->field_b
    //     0xa4fe9c: ldur            w0, [x1, #0xb]
    // 0xa4fea0: DecompressPointer r0
    //     0xa4fea0: add             x0, x0, HEAP, lsl #32
    // 0xa4fea4: LoadField: r2 = r0->field_f
    //     0xa4fea4: ldur            w2, [x0, #0xf]
    // 0xa4fea8: DecompressPointer r2
    //     0xa4fea8: add             x2, x2, HEAP, lsl #32
    // 0xa4feac: LoadField: r0 = r1->field_f
    //     0xa4feac: ldur            w0, [x1, #0xf]
    // 0xa4feb0: DecompressPointer r0
    //     0xa4feb0: add             x0, x0, HEAP, lsl #32
    // 0xa4feb4: mov             x1, x2
    // 0xa4feb8: mov             x2, x0
    // 0xa4febc: r0 = _onDayTapped()
    //     0xa4febc: bl              #0xa4fed8  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_onDayTapped
    // 0xa4fec0: r0 = Null
    //     0xa4fec0: mov             x0, NULL
    // 0xa4fec4: LeaveFrame
    //     0xa4fec4: mov             SP, fp
    //     0xa4fec8: ldp             fp, lr, [SP], #0x10
    // 0xa4fecc: ret
    //     0xa4fecc: ret             
    // 0xa4fed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4fed0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4fed4: b               #0xa4fe9c
  }
  _ _onDayTapped(/* No info */) {
    // ** addr: 0xa4fed8, size: 0x188
    // 0xa4fed8: EnterFrame
    //     0xa4fed8: stp             fp, lr, [SP, #-0x10]!
    //     0xa4fedc: mov             fp, SP
    // 0xa4fee0: AllocStack(0x18)
    //     0xa4fee0: sub             SP, SP, #0x18
    // 0xa4fee4: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa4fee4: mov             x3, x1
    //     0xa4fee8: stur            x1, [fp, #-8]
    //     0xa4feec: stur            x2, [fp, #-0x10]
    // 0xa4fef0: CheckStackOverflow
    //     0xa4fef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4fef4: cmp             SP, x16
    //     0xa4fef8: b.ls            #0xa50038
    // 0xa4fefc: r0 = LoadClassIdInstr(r2)
    //     0xa4fefc: ldur            x0, [x2, #-1]
    //     0xa4ff00: ubfx            x0, x0, #0xc, #0x14
    // 0xa4ff04: mov             x1, x2
    // 0xa4ff08: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4ff08: sub             lr, x0, #0xfff
    //     0xa4ff0c: ldr             lr, [x21, lr, lsl #3]
    //     0xa4ff10: blr             lr
    // 0xa4ff14: mov             x3, x0
    // 0xa4ff18: ldur            x2, [fp, #-8]
    // 0xa4ff1c: stur            x3, [fp, #-0x18]
    // 0xa4ff20: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa4ff20: ldur            w0, [x2, #0x17]
    // 0xa4ff24: DecompressPointer r0
    //     0xa4ff24: add             x0, x0, HEAP, lsl #32
    // 0xa4ff28: r16 = Sentinel
    //     0xa4ff28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4ff2c: cmp             w0, w16
    // 0xa4ff30: b.eq            #0xa50040
    // 0xa4ff34: LoadField: r1 = r0->field_27
    //     0xa4ff34: ldur            w1, [x0, #0x27]
    // 0xa4ff38: DecompressPointer r1
    //     0xa4ff38: add             x1, x1, HEAP, lsl #32
    // 0xa4ff3c: r0 = LoadClassIdInstr(r1)
    //     0xa4ff3c: ldur            x0, [x1, #-1]
    //     0xa4ff40: ubfx            x0, x0, #0xc, #0x14
    // 0xa4ff44: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa4ff44: sub             lr, x0, #0xfff
    //     0xa4ff48: ldr             lr, [x21, lr, lsl #3]
    //     0xa4ff4c: blr             lr
    // 0xa4ff50: mov             x1, x0
    // 0xa4ff54: ldur            x0, [fp, #-0x18]
    // 0xa4ff58: cmp             x0, x1
    // 0xa4ff5c: b.eq            #0xa4ff78
    // 0xa4ff60: ldur            x0, [fp, #-8]
    // 0xa4ff64: LoadField: r1 = r0->field_b
    //     0xa4ff64: ldur            w1, [x0, #0xb]
    // 0xa4ff68: DecompressPointer r1
    //     0xa4ff68: add             x1, x1, HEAP, lsl #32
    // 0xa4ff6c: cmp             w1, NULL
    // 0xa4ff70: b.eq            #0xa5004c
    // 0xa4ff74: b               #0xa4ff7c
    // 0xa4ff78: ldur            x0, [fp, #-8]
    // 0xa4ff7c: mov             x1, x0
    // 0xa4ff80: ldur            x2, [fp, #-0x10]
    // 0xa4ff84: r0 = _isDayDisabled()
    //     0xa4ff84: bl              #0xa4f9a0  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isDayDisabled
    // 0xa4ff88: tbnz            w0, #4, #0xa4ffb0
    // 0xa4ff8c: ldur            x0, [fp, #-8]
    // 0xa4ff90: LoadField: r1 = r0->field_b
    //     0xa4ff90: ldur            w1, [x0, #0xb]
    // 0xa4ff94: DecompressPointer r1
    //     0xa4ff94: add             x1, x1, HEAP, lsl #32
    // 0xa4ff98: cmp             w1, NULL
    // 0xa4ff9c: b.eq            #0xa50050
    // 0xa4ffa0: r0 = Null
    //     0xa4ffa0: mov             x0, NULL
    // 0xa4ffa4: LeaveFrame
    //     0xa4ffa4: mov             SP, fp
    //     0xa4ffa8: ldp             fp, lr, [SP], #0x10
    // 0xa4ffac: ret
    //     0xa4ffac: ret             
    // 0xa4ffb0: ldur            x0, [fp, #-8]
    // 0xa4ffb4: mov             x1, x0
    // 0xa4ffb8: ldur            x2, [fp, #-0x10]
    // 0xa4ffbc: r0 = _updateFocusOnTap()
    //     0xa4ffbc: bl              #0xa502a0  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_updateFocusOnTap
    // 0xa4ffc0: ldur            x1, [fp, #-8]
    // 0xa4ffc4: r0 = _isRangeSelectionOn()
    //     0xa4ffc4: bl              #0xa50270  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isRangeSelectionOn
    // 0xa4ffc8: tbnz            w0, #4, #0xa4ffe4
    // 0xa4ffcc: ldur            x0, [fp, #-8]
    // 0xa4ffd0: LoadField: r1 = r0->field_b
    //     0xa4ffd0: ldur            w1, [x0, #0xb]
    // 0xa4ffd4: DecompressPointer r1
    //     0xa4ffd4: add             x1, x1, HEAP, lsl #32
    // 0xa4ffd8: cmp             w1, NULL
    // 0xa4ffdc: b.eq            #0xa50054
    // 0xa4ffe0: b               #0xa4ffe8
    // 0xa4ffe4: ldur            x0, [fp, #-8]
    // 0xa4ffe8: LoadField: r1 = r0->field_b
    //     0xa4ffe8: ldur            w1, [x0, #0xb]
    // 0xa4ffec: DecompressPointer r1
    //     0xa4ffec: add             x1, x1, HEAP, lsl #32
    // 0xa4fff0: cmp             w1, NULL
    // 0xa4fff4: b.eq            #0xa50058
    // 0xa4fff8: LoadField: r2 = r1->field_af
    //     0xa4fff8: ldur            w2, [x1, #0xaf]
    // 0xa4fffc: DecompressPointer r2
    //     0xa4fffc: add             x2, x2, HEAP, lsl #32
    // 0xa50000: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa50000: ldur            w1, [x0, #0x17]
    // 0xa50004: DecompressPointer r1
    //     0xa50004: add             x1, x1, HEAP, lsl #32
    // 0xa50008: LoadField: r3 = r1->field_27
    //     0xa50008: ldur            w3, [x1, #0x27]
    // 0xa5000c: DecompressPointer r3
    //     0xa5000c: add             x3, x3, HEAP, lsl #32
    // 0xa50010: cmp             w2, NULL
    // 0xa50014: b.eq            #0xa5005c
    // 0xa50018: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa50018: ldur            w1, [x2, #0x17]
    // 0xa5001c: DecompressPointer r1
    //     0xa5001c: add             x1, x1, HEAP, lsl #32
    // 0xa50020: ldur            x2, [fp, #-0x10]
    // 0xa50024: r0 = onDaySelected()
    //     0xa50024: bl              #0xa500a0  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onDaySelected
    // 0xa50028: r0 = Null
    //     0xa50028: mov             x0, NULL
    // 0xa5002c: LeaveFrame
    //     0xa5002c: mov             SP, fp
    //     0xa50030: ldp             fp, lr, [SP], #0x10
    // 0xa50034: ret
    //     0xa50034: ret             
    // 0xa50038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50038: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5003c: b               #0xa4fefc
    // 0xa50040: r9 = _focusedDay
    //     0xa50040: add             x9, PP, #0x47, lsl #12  ; [pp+0x47de8] Field <_TableCalendarState@1928116939._focusedDay@1928116939>: late final (offset: 0x18)
    //     0xa50044: ldr             x9, [x9, #0xde8]
    // 0xa50048: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa50048: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa5004c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5004c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50050: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50054: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50058: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50058: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa5005c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa5005c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  get _ _isRangeSelectionOn(/* No info */) {
    // ** addr: 0xa50270, size: 0x30
    // 0xa50270: LoadField: r2 = r1->field_1b
    //     0xa50270: ldur            w2, [x1, #0x1b]
    // 0xa50274: DecompressPointer r2
    //     0xa50274: add             x2, x2, HEAP, lsl #32
    // 0xa50278: r16 = Sentinel
    //     0xa50278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa5027c: cmp             w2, w16
    // 0xa50280: b.eq            #0xa5028c
    // 0xa50284: r0 = false
    //     0xa50284: add             x0, NULL, #0x30  ; false
    // 0xa50288: ret
    //     0xa50288: ret             
    // 0xa5028c: EnterFrame
    //     0xa5028c: stp             fp, lr, [SP, #-0x10]!
    //     0xa50290: mov             fp, SP
    // 0xa50294: r9 = _rangeSelectionMode
    //     0xa50294: add             x9, PP, #0x47, lsl #12  ; [pp+0x47e08] Field <_TableCalendarState@1928116939._rangeSelectionMode@1928116939>: late (offset: 0x1c)
    //     0xa50298: ldr             x9, [x9, #0xe08]
    // 0xa5029c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa5029c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _updateFocusOnTap(/* No info */) {
    // ** addr: 0xa502a0, size: 0x124
    // 0xa502a0: EnterFrame
    //     0xa502a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa502a4: mov             fp, SP
    // 0xa502a8: AllocStack(0x18)
    //     0xa502a8: sub             SP, SP, #0x18
    // 0xa502ac: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa502ac: mov             x4, x1
    //     0xa502b0: mov             x0, x2
    //     0xa502b4: stur            x1, [fp, #-8]
    //     0xa502b8: stur            x2, [fp, #-0x10]
    // 0xa502bc: CheckStackOverflow
    //     0xa502bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa502c0: cmp             SP, x16
    //     0xa502c4: b.ls            #0xa503ac
    // 0xa502c8: LoadField: r1 = r4->field_b
    //     0xa502c8: ldur            w1, [x4, #0xb]
    // 0xa502cc: DecompressPointer r1
    //     0xa502cc: add             x1, x1, HEAP, lsl #32
    // 0xa502d0: cmp             w1, NULL
    // 0xa502d4: b.eq            #0xa503b4
    // 0xa502d8: ArrayLoad: r1 = r4[0]  ; List_4
    //     0xa502d8: ldur            w1, [x4, #0x17]
    // 0xa502dc: DecompressPointer r1
    //     0xa502dc: add             x1, x1, HEAP, lsl #32
    // 0xa502e0: r16 = Sentinel
    //     0xa502e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa502e4: cmp             w1, w16
    // 0xa502e8: b.eq            #0xa503b8
    // 0xa502ec: LoadField: r3 = r1->field_27
    //     0xa502ec: ldur            w3, [x1, #0x27]
    // 0xa502f0: DecompressPointer r3
    //     0xa502f0: add             x3, x3, HEAP, lsl #32
    // 0xa502f4: mov             x1, x4
    // 0xa502f8: mov             x2, x0
    // 0xa502fc: r0 = _isBeforeMonth()
    //     0xa502fc: bl              #0xa504e8  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isBeforeMonth
    // 0xa50300: tbnz            w0, #4, #0xa50334
    // 0xa50304: ldur            x0, [fp, #-8]
    // 0xa50308: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa50308: ldur            w3, [x0, #0x17]
    // 0xa5030c: DecompressPointer r3
    //     0xa5030c: add             x3, x3, HEAP, lsl #32
    // 0xa50310: stur            x3, [fp, #-0x18]
    // 0xa50314: LoadField: r2 = r3->field_27
    //     0xa50314: ldur            w2, [x3, #0x27]
    // 0xa50318: DecompressPointer r2
    //     0xa50318: add             x2, x2, HEAP, lsl #32
    // 0xa5031c: mov             x1, x0
    // 0xa50320: r0 = _firstDayOfMonth()
    //     0xa50320: bl              #0x9819ac  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_firstDayOfMonth
    // 0xa50324: ldur            x1, [fp, #-0x18]
    // 0xa50328: mov             x2, x0
    // 0xa5032c: r0 = value=()
    //     0xa5032c: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xa50330: b               #0xa5039c
    // 0xa50334: ldur            x0, [fp, #-8]
    // 0xa50338: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa50338: ldur            w1, [x0, #0x17]
    // 0xa5033c: DecompressPointer r1
    //     0xa5033c: add             x1, x1, HEAP, lsl #32
    // 0xa50340: LoadField: r3 = r1->field_27
    //     0xa50340: ldur            w3, [x1, #0x27]
    // 0xa50344: DecompressPointer r3
    //     0xa50344: add             x3, x3, HEAP, lsl #32
    // 0xa50348: mov             x1, x0
    // 0xa5034c: ldur            x2, [fp, #-0x10]
    // 0xa50350: r0 = _isAfterMonth()
    //     0xa50350: bl              #0xa503c4  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isAfterMonth
    // 0xa50354: tbnz            w0, #4, #0xa50384
    // 0xa50358: ldur            x1, [fp, #-8]
    // 0xa5035c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa5035c: ldur            w0, [x1, #0x17]
    // 0xa50360: DecompressPointer r0
    //     0xa50360: add             x0, x0, HEAP, lsl #32
    // 0xa50364: stur            x0, [fp, #-0x18]
    // 0xa50368: LoadField: r2 = r0->field_27
    //     0xa50368: ldur            w2, [x0, #0x27]
    // 0xa5036c: DecompressPointer r2
    //     0xa5036c: add             x2, x2, HEAP, lsl #32
    // 0xa50370: r0 = _lastDayOfMonth()
    //     0xa50370: bl              #0x9817dc  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_lastDayOfMonth
    // 0xa50374: ldur            x1, [fp, #-0x18]
    // 0xa50378: mov             x2, x0
    // 0xa5037c: r0 = value=()
    //     0xa5037c: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xa50380: b               #0xa5039c
    // 0xa50384: ldur            x1, [fp, #-8]
    // 0xa50388: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa50388: ldur            w0, [x1, #0x17]
    // 0xa5038c: DecompressPointer r0
    //     0xa5038c: add             x0, x0, HEAP, lsl #32
    // 0xa50390: mov             x1, x0
    // 0xa50394: ldur            x2, [fp, #-0x10]
    // 0xa50398: r0 = value=()
    //     0xa50398: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xa5039c: r0 = Null
    //     0xa5039c: mov             x0, NULL
    // 0xa503a0: LeaveFrame
    //     0xa503a0: mov             SP, fp
    //     0xa503a4: ldp             fp, lr, [SP], #0x10
    // 0xa503a8: ret
    //     0xa503a8: ret             
    // 0xa503ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa503ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa503b0: b               #0xa502c8
    // 0xa503b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa503b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa503b8: r9 = _focusedDay
    //     0xa503b8: add             x9, PP, #0x47, lsl #12  ; [pp+0x47de8] Field <_TableCalendarState@1928116939._focusedDay@1928116939>: late final (offset: 0x18)
    //     0xa503bc: ldr             x9, [x9, #0xde8]
    // 0xa503c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa503c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _isAfterMonth(/* No info */) {
    // ** addr: 0xa503c4, size: 0x124
    // 0xa503c4: EnterFrame
    //     0xa503c4: stp             fp, lr, [SP, #-0x10]!
    //     0xa503c8: mov             fp, SP
    // 0xa503cc: AllocStack(0x18)
    //     0xa503cc: sub             SP, SP, #0x18
    // 0xa503d0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xa503d0: stur            x2, [fp, #-8]
    //     0xa503d4: mov             x16, x3
    //     0xa503d8: mov             x3, x2
    //     0xa503dc: mov             x2, x16
    //     0xa503e0: stur            x2, [fp, #-0x10]
    // 0xa503e4: CheckStackOverflow
    //     0xa503e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa503e8: cmp             SP, x16
    //     0xa503ec: b.ls            #0xa504e0
    // 0xa503f0: r0 = LoadClassIdInstr(r3)
    //     0xa503f0: ldur            x0, [x3, #-1]
    //     0xa503f4: ubfx            x0, x0, #0xc, #0x14
    // 0xa503f8: mov             x1, x3
    // 0xa503fc: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa503fc: sub             lr, x0, #0xff6
    //     0xa50400: ldr             lr, [x21, lr, lsl #3]
    //     0xa50404: blr             lr
    // 0xa50408: mov             x3, x0
    // 0xa5040c: ldur            x2, [fp, #-0x10]
    // 0xa50410: stur            x3, [fp, #-0x18]
    // 0xa50414: r0 = LoadClassIdInstr(r2)
    //     0xa50414: ldur            x0, [x2, #-1]
    //     0xa50418: ubfx            x0, x0, #0xc, #0x14
    // 0xa5041c: mov             x1, x2
    // 0xa50420: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa50420: sub             lr, x0, #0xff6
    //     0xa50424: ldr             lr, [x21, lr, lsl #3]
    //     0xa50428: blr             lr
    // 0xa5042c: mov             x1, x0
    // 0xa50430: ldur            x0, [fp, #-0x18]
    // 0xa50434: cmp             x0, x1
    // 0xa50438: b.ne            #0xa504ac
    // 0xa5043c: ldur            x1, [fp, #-8]
    // 0xa50440: ldur            x2, [fp, #-0x10]
    // 0xa50444: r0 = LoadClassIdInstr(r1)
    //     0xa50444: ldur            x0, [x1, #-1]
    //     0xa50448: ubfx            x0, x0, #0xc, #0x14
    // 0xa5044c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa5044c: sub             lr, x0, #0xfff
    //     0xa50450: ldr             lr, [x21, lr, lsl #3]
    //     0xa50454: blr             lr
    // 0xa50458: mov             x2, x0
    // 0xa5045c: ldur            x0, [fp, #-0x10]
    // 0xa50460: stur            x2, [fp, #-0x18]
    // 0xa50464: r1 = LoadClassIdInstr(r0)
    //     0xa50464: ldur            x1, [x0, #-1]
    //     0xa50468: ubfx            x1, x1, #0xc, #0x14
    // 0xa5046c: mov             x16, x0
    // 0xa50470: mov             x0, x1
    // 0xa50474: mov             x1, x16
    // 0xa50478: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa50478: sub             lr, x0, #0xfff
    //     0xa5047c: ldr             lr, [x21, lr, lsl #3]
    //     0xa50480: blr             lr
    // 0xa50484: mov             x1, x0
    // 0xa50488: ldur            x0, [fp, #-0x18]
    // 0xa5048c: cmp             x0, x1
    // 0xa50490: r16 = true
    //     0xa50490: add             x16, NULL, #0x20  ; true
    // 0xa50494: r17 = false
    //     0xa50494: add             x17, NULL, #0x30  ; false
    // 0xa50498: csel            x2, x16, x17, gt
    // 0xa5049c: mov             x0, x2
    // 0xa504a0: LeaveFrame
    //     0xa504a0: mov             SP, fp
    //     0xa504a4: ldp             fp, lr, [SP], #0x10
    // 0xa504a8: ret
    //     0xa504a8: ret             
    // 0xa504ac: ldur            x1, [fp, #-8]
    // 0xa504b0: ldur            x0, [fp, #-0x10]
    // 0xa504b4: r2 = LoadClassIdInstr(r1)
    //     0xa504b4: ldur            x2, [x1, #-1]
    //     0xa504b8: ubfx            x2, x2, #0xc, #0x14
    // 0xa504bc: mov             x16, x0
    // 0xa504c0: mov             x0, x2
    // 0xa504c4: mov             x2, x16
    // 0xa504c8: r0 = GDT[cid_x0 + -0xf8d]()
    //     0xa504c8: sub             lr, x0, #0xf8d
    //     0xa504cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa504d0: blr             lr
    // 0xa504d4: LeaveFrame
    //     0xa504d4: mov             SP, fp
    //     0xa504d8: ldp             fp, lr, [SP], #0x10
    // 0xa504dc: ret
    //     0xa504dc: ret             
    // 0xa504e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa504e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa504e4: b               #0xa503f0
  }
  _ _isBeforeMonth(/* No info */) {
    // ** addr: 0xa504e8, size: 0x124
    // 0xa504e8: EnterFrame
    //     0xa504e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa504ec: mov             fp, SP
    // 0xa504f0: AllocStack(0x18)
    //     0xa504f0: sub             SP, SP, #0x18
    // 0xa504f4: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0xa504f4: stur            x2, [fp, #-8]
    //     0xa504f8: mov             x16, x3
    //     0xa504fc: mov             x3, x2
    //     0xa50500: mov             x2, x16
    //     0xa50504: stur            x2, [fp, #-0x10]
    // 0xa50508: CheckStackOverflow
    //     0xa50508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5050c: cmp             SP, x16
    //     0xa50510: b.ls            #0xa50604
    // 0xa50514: r0 = LoadClassIdInstr(r3)
    //     0xa50514: ldur            x0, [x3, #-1]
    //     0xa50518: ubfx            x0, x0, #0xc, #0x14
    // 0xa5051c: mov             x1, x3
    // 0xa50520: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa50520: sub             lr, x0, #0xff6
    //     0xa50524: ldr             lr, [x21, lr, lsl #3]
    //     0xa50528: blr             lr
    // 0xa5052c: mov             x3, x0
    // 0xa50530: ldur            x2, [fp, #-0x10]
    // 0xa50534: stur            x3, [fp, #-0x18]
    // 0xa50538: r0 = LoadClassIdInstr(r2)
    //     0xa50538: ldur            x0, [x2, #-1]
    //     0xa5053c: ubfx            x0, x0, #0xc, #0x14
    // 0xa50540: mov             x1, x2
    // 0xa50544: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa50544: sub             lr, x0, #0xff6
    //     0xa50548: ldr             lr, [x21, lr, lsl #3]
    //     0xa5054c: blr             lr
    // 0xa50550: mov             x1, x0
    // 0xa50554: ldur            x0, [fp, #-0x18]
    // 0xa50558: cmp             x0, x1
    // 0xa5055c: b.ne            #0xa505d0
    // 0xa50560: ldur            x1, [fp, #-8]
    // 0xa50564: ldur            x2, [fp, #-0x10]
    // 0xa50568: r0 = LoadClassIdInstr(r1)
    //     0xa50568: ldur            x0, [x1, #-1]
    //     0xa5056c: ubfx            x0, x0, #0xc, #0x14
    // 0xa50570: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa50570: sub             lr, x0, #0xfff
    //     0xa50574: ldr             lr, [x21, lr, lsl #3]
    //     0xa50578: blr             lr
    // 0xa5057c: mov             x2, x0
    // 0xa50580: ldur            x0, [fp, #-0x10]
    // 0xa50584: stur            x2, [fp, #-0x18]
    // 0xa50588: r1 = LoadClassIdInstr(r0)
    //     0xa50588: ldur            x1, [x0, #-1]
    //     0xa5058c: ubfx            x1, x1, #0xc, #0x14
    // 0xa50590: mov             x16, x0
    // 0xa50594: mov             x0, x1
    // 0xa50598: mov             x1, x16
    // 0xa5059c: r0 = GDT[cid_x0 + -0xfff]()
    //     0xa5059c: sub             lr, x0, #0xfff
    //     0xa505a0: ldr             lr, [x21, lr, lsl #3]
    //     0xa505a4: blr             lr
    // 0xa505a8: mov             x1, x0
    // 0xa505ac: ldur            x0, [fp, #-0x18]
    // 0xa505b0: cmp             x0, x1
    // 0xa505b4: r16 = true
    //     0xa505b4: add             x16, NULL, #0x20  ; true
    // 0xa505b8: r17 = false
    //     0xa505b8: add             x17, NULL, #0x30  ; false
    // 0xa505bc: csel            x2, x16, x17, lt
    // 0xa505c0: mov             x0, x2
    // 0xa505c4: LeaveFrame
    //     0xa505c4: mov             SP, fp
    //     0xa505c8: ldp             fp, lr, [SP], #0x10
    // 0xa505cc: ret
    //     0xa505cc: ret             
    // 0xa505d0: ldur            x1, [fp, #-8]
    // 0xa505d4: ldur            x0, [fp, #-0x10]
    // 0xa505d8: r2 = LoadClassIdInstr(r1)
    //     0xa505d8: ldur            x2, [x1, #-1]
    //     0xa505dc: ubfx            x2, x2, #0xc, #0x14
    // 0xa505e0: mov             x16, x0
    // 0xa505e4: mov             x0, x2
    // 0xa505e8: mov             x2, x16
    // 0xa505ec: r0 = GDT[cid_x0 + -0xf0c]()
    //     0xa505ec: sub             lr, x0, #0xf0c
    //     0xa505f0: ldr             lr, [x21, lr, lsl #3]
    //     0xa505f4: blr             lr
    // 0xa505f8: LeaveFrame
    //     0xa505f8: mov             SP, fp
    //     0xa505fc: ldp             fp, lr, [SP], #0x10
    // 0xa50600: ret
    //     0xa50600: ret             
    // 0xa50604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50604: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50608: b               #0xa50514
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, DateTime) {
    // ** addr: 0xa5060c, size: 0x17c
    // 0xa5060c: EnterFrame
    //     0xa5060c: stp             fp, lr, [SP, #-0x10]!
    //     0xa50610: mov             fp, SP
    // 0xa50614: AllocStack(0x18)
    //     0xa50614: sub             SP, SP, #0x18
    // 0xa50618: SetupParameters()
    //     0xa50618: ldr             x0, [fp, #0x20]
    //     0xa5061c: ldur            w3, [x0, #0x17]
    //     0xa50620: add             x3, x3, HEAP, lsl #32
    //     0xa50624: stur            x3, [fp, #-8]
    // 0xa50628: CheckStackOverflow
    //     0xa50628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5062c: cmp             SP, x16
    //     0xa50630: b.ls            #0xa50770
    // 0xa50634: LoadField: r0 = r3->field_f
    //     0xa50634: ldur            w0, [x3, #0xf]
    // 0xa50638: DecompressPointer r0
    //     0xa50638: add             x0, x0, HEAP, lsl #32
    // 0xa5063c: LoadField: r1 = r0->field_b
    //     0xa5063c: ldur            w1, [x0, #0xb]
    // 0xa50640: DecompressPointer r1
    //     0xa50640: add             x1, x1, HEAP, lsl #32
    // 0xa50644: cmp             w1, NULL
    // 0xa50648: b.eq            #0xa50778
    // 0xa5064c: r1 = Null
    //     0xa5064c: mov             x1, NULL
    // 0xa50650: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xa50650: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xa50654: ldr             x2, [x2, #0xad0]
    // 0xa50658: r0 = verifiedLocale()
    //     0xa50658: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xa5065c: stur            x0, [fp, #-0x10]
    // 0xa50660: r0 = DateFormat()
    //     0xa50660: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xa50664: mov             x3, x0
    // 0xa50668: ldur            x0, [fp, #-0x10]
    // 0xa5066c: stur            x3, [fp, #-0x18]
    // 0xa50670: StoreField: r3->field_7 = r0
    //     0xa50670: stur            w0, [x3, #7]
    // 0xa50674: mov             x1, x3
    // 0xa50678: r2 = "E"
    //     0xa50678: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b7d0] "E"
    //     0xa5067c: ldr             x2, [x2, #0x7d0]
    // 0xa50680: r0 = addPattern()
    //     0xa50680: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xa50684: ldur            x1, [fp, #-0x18]
    // 0xa50688: ldr             x2, [fp, #0x10]
    // 0xa5068c: r0 = format()
    //     0xa5068c: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xa50690: mov             x3, x0
    // 0xa50694: ldur            x0, [fp, #-8]
    // 0xa50698: stur            x3, [fp, #-0x10]
    // 0xa5069c: LoadField: r1 = r0->field_f
    //     0xa5069c: ldur            w1, [x0, #0xf]
    // 0xa506a0: DecompressPointer r1
    //     0xa506a0: add             x1, x1, HEAP, lsl #32
    // 0xa506a4: LoadField: r2 = r1->field_b
    //     0xa506a4: ldur            w2, [x1, #0xb]
    // 0xa506a8: DecompressPointer r2
    //     0xa506a8: add             x2, x2, HEAP, lsl #32
    // 0xa506ac: cmp             w2, NULL
    // 0xa506b0: b.eq            #0xa5077c
    // 0xa506b4: ldr             x2, [fp, #0x10]
    // 0xa506b8: r0 = _isWeekend()
    //     0xa506b8: bl              #0xa4f934  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_isWeekend
    // 0xa506bc: tbnz            w0, #4, #0xa506e8
    // 0xa506c0: ldur            x0, [fp, #-8]
    // 0xa506c4: LoadField: r1 = r0->field_f
    //     0xa506c4: ldur            w1, [x0, #0xf]
    // 0xa506c8: DecompressPointer r1
    //     0xa506c8: add             x1, x1, HEAP, lsl #32
    // 0xa506cc: LoadField: r0 = r1->field_b
    //     0xa506cc: ldur            w0, [x1, #0xb]
    // 0xa506d0: DecompressPointer r0
    //     0xa506d0: add             x0, x0, HEAP, lsl #32
    // 0xa506d4: cmp             w0, NULL
    // 0xa506d8: b.eq            #0xa50780
    // 0xa506dc: r1 = Instance_TextStyle
    //     0xa506dc: add             x1, PP, #0x47, lsl #12  ; [pp+0x47e70] Obj!TextStyle@e1b501
    //     0xa506e0: ldr             x1, [x1, #0xe70]
    // 0xa506e4: b               #0xa5070c
    // 0xa506e8: ldur            x0, [fp, #-8]
    // 0xa506ec: LoadField: r1 = r0->field_f
    //     0xa506ec: ldur            w1, [x0, #0xf]
    // 0xa506f0: DecompressPointer r1
    //     0xa506f0: add             x1, x1, HEAP, lsl #32
    // 0xa506f4: LoadField: r0 = r1->field_b
    //     0xa506f4: ldur            w0, [x1, #0xb]
    // 0xa506f8: DecompressPointer r0
    //     0xa506f8: add             x0, x0, HEAP, lsl #32
    // 0xa506fc: cmp             w0, NULL
    // 0xa50700: b.eq            #0xa50784
    // 0xa50704: r1 = Instance_TextStyle
    //     0xa50704: add             x1, PP, #0x47, lsl #12  ; [pp+0x47e78] Obj!TextStyle@e1b491
    //     0xa50708: ldr             x1, [x1, #0xe78]
    // 0xa5070c: ldur            x0, [fp, #-0x10]
    // 0xa50710: stur            x1, [fp, #-8]
    // 0xa50714: r0 = Text()
    //     0xa50714: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa50718: mov             x1, x0
    // 0xa5071c: ldur            x0, [fp, #-0x10]
    // 0xa50720: stur            x1, [fp, #-0x18]
    // 0xa50724: StoreField: r1->field_b = r0
    //     0xa50724: stur            w0, [x1, #0xb]
    // 0xa50728: ldur            x0, [fp, #-8]
    // 0xa5072c: StoreField: r1->field_13 = r0
    //     0xa5072c: stur            w0, [x1, #0x13]
    // 0xa50730: r0 = ExcludeSemantics()
    //     0xa50730: bl              #0x6ac8e0  ; AllocateExcludeSemanticsStub -> ExcludeSemantics (size=0x14)
    // 0xa50734: mov             x1, x0
    // 0xa50738: r0 = true
    //     0xa50738: add             x0, NULL, #0x20  ; true
    // 0xa5073c: stur            x1, [fp, #-8]
    // 0xa50740: StoreField: r1->field_f = r0
    //     0xa50740: stur            w0, [x1, #0xf]
    // 0xa50744: ldur            x0, [fp, #-0x18]
    // 0xa50748: StoreField: r1->field_b = r0
    //     0xa50748: stur            w0, [x1, #0xb]
    // 0xa5074c: r0 = Center()
    //     0xa5074c: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa50750: r1 = Instance_Alignment
    //     0xa50750: add             x1, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa50754: ldr             x1, [x1, #0x898]
    // 0xa50758: StoreField: r0->field_f = r1
    //     0xa50758: stur            w1, [x0, #0xf]
    // 0xa5075c: ldur            x1, [fp, #-8]
    // 0xa50760: StoreField: r0->field_b = r1
    //     0xa50760: stur            w1, [x0, #0xb]
    // 0xa50764: LeaveFrame
    //     0xa50764: mov             SP, fp
    //     0xa50768: ldp             fp, lr, [SP], #0x10
    // 0xa5076c: ret
    //     0xa5076c: ret             
    // 0xa50770: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50770: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50774: b               #0xa50634
    // 0xa50778: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50778: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa5077c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5077c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50780: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50784: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, DateTime) {
    // ** addr: 0xa50788, size: 0x130
    // 0xa50788: EnterFrame
    //     0xa50788: stp             fp, lr, [SP, #-0x10]!
    //     0xa5078c: mov             fp, SP
    // 0xa50790: AllocStack(0x18)
    //     0xa50790: sub             SP, SP, #0x18
    // 0xa50794: SetupParameters()
    //     0xa50794: ldr             x0, [fp, #0x20]
    //     0xa50798: ldur            w3, [x0, #0x17]
    //     0xa5079c: add             x3, x3, HEAP, lsl #32
    //     0xa507a0: stur            x3, [fp, #-8]
    // 0xa507a4: CheckStackOverflow
    //     0xa507a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa507a8: cmp             SP, x16
    //     0xa507ac: b.ls            #0xa508a8
    // 0xa507b0: LoadField: r1 = r3->field_f
    //     0xa507b0: ldur            w1, [x3, #0xf]
    // 0xa507b4: DecompressPointer r1
    //     0xa507b4: add             x1, x1, HEAP, lsl #32
    // 0xa507b8: ldr             x2, [fp, #0x10]
    // 0xa507bc: r0 = _calculateWeekNumber()
    //     0xa507bc: bl              #0xa508b8  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_calculateWeekNumber
    // 0xa507c0: mov             x3, x0
    // 0xa507c4: ldur            x2, [fp, #-8]
    // 0xa507c8: LoadField: r0 = r2->field_f
    //     0xa507c8: ldur            w0, [x2, #0xf]
    // 0xa507cc: DecompressPointer r0
    //     0xa507cc: add             x0, x0, HEAP, lsl #32
    // 0xa507d0: LoadField: r1 = r0->field_b
    //     0xa507d0: ldur            w1, [x0, #0xb]
    // 0xa507d4: DecompressPointer r1
    //     0xa507d4: add             x1, x1, HEAP, lsl #32
    // 0xa507d8: cmp             w1, NULL
    // 0xa507dc: b.eq            #0xa508b0
    // 0xa507e0: r0 = BoxInt64Instr(r3)
    //     0xa507e0: sbfiz           x0, x3, #1, #0x1f
    //     0xa507e4: cmp             x3, x0, asr #1
    //     0xa507e8: b.eq            #0xa507f4
    //     0xa507ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa507f0: stur            x3, [x0, #7]
    // 0xa507f4: r1 = 60
    //     0xa507f4: movz            x1, #0x3c
    // 0xa507f8: branchIfSmi(r0, 0xa50804)
    //     0xa507f8: tbz             w0, #0, #0xa50804
    // 0xa507fc: r1 = LoadClassIdInstr(r0)
    //     0xa507fc: ldur            x1, [x0, #-1]
    //     0xa50800: ubfx            x1, x1, #0xc, #0x14
    // 0xa50804: str             x0, [SP]
    // 0xa50808: mov             x0, x1
    // 0xa5080c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa5080c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa50810: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xa50810: movz            x17, #0x2b03
    //     0xa50814: add             lr, x0, x17
    //     0xa50818: ldr             lr, [x21, lr, lsl #3]
    //     0xa5081c: blr             lr
    // 0xa50820: mov             x1, x0
    // 0xa50824: ldur            x0, [fp, #-8]
    // 0xa50828: stur            x1, [fp, #-0x10]
    // 0xa5082c: LoadField: r2 = r0->field_f
    //     0xa5082c: ldur            w2, [x0, #0xf]
    // 0xa50830: DecompressPointer r2
    //     0xa50830: add             x2, x2, HEAP, lsl #32
    // 0xa50834: LoadField: r0 = r2->field_b
    //     0xa50834: ldur            w0, [x2, #0xb]
    // 0xa50838: DecompressPointer r0
    //     0xa50838: add             x0, x0, HEAP, lsl #32
    // 0xa5083c: cmp             w0, NULL
    // 0xa50840: b.eq            #0xa508b4
    // 0xa50844: r0 = Text()
    //     0xa50844: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xa50848: mov             x1, x0
    // 0xa5084c: ldur            x0, [fp, #-0x10]
    // 0xa50850: stur            x1, [fp, #-8]
    // 0xa50854: StoreField: r1->field_b = r0
    //     0xa50854: stur            w0, [x1, #0xb]
    // 0xa50858: r0 = Instance_TextStyle
    //     0xa50858: add             x0, PP, #0x47, lsl #12  ; [pp+0x47e80] Obj!TextStyle@e1b1f1
    //     0xa5085c: ldr             x0, [x0, #0xe80]
    // 0xa50860: StoreField: r1->field_13 = r0
    //     0xa50860: stur            w0, [x1, #0x13]
    // 0xa50864: r0 = Center()
    //     0xa50864: bl              #0x9d3a28  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa50868: mov             x1, x0
    // 0xa5086c: r0 = Instance_Alignment
    //     0xa5086c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xa50870: ldr             x0, [x0, #0x898]
    // 0xa50874: stur            x1, [fp, #-0x10]
    // 0xa50878: StoreField: r1->field_f = r0
    //     0xa50878: stur            w0, [x1, #0xf]
    // 0xa5087c: ldur            x0, [fp, #-8]
    // 0xa50880: StoreField: r1->field_b = r0
    //     0xa50880: stur            w0, [x1, #0xb]
    // 0xa50884: r0 = Padding()
    //     0xa50884: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa50888: r1 = Instance_EdgeInsets
    //     0xa50888: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2abf0] Obj!EdgeInsets@e12461
    //     0xa5088c: ldr             x1, [x1, #0xbf0]
    // 0xa50890: StoreField: r0->field_f = r1
    //     0xa50890: stur            w1, [x0, #0xf]
    // 0xa50894: ldur            x1, [fp, #-0x10]
    // 0xa50898: StoreField: r0->field_b = r1
    //     0xa50898: stur            w1, [x0, #0xb]
    // 0xa5089c: LeaveFrame
    //     0xa5089c: mov             SP, fp
    //     0xa508a0: ldp             fp, lr, [SP], #0x10
    // 0xa508a4: ret
    //     0xa508a4: ret             
    // 0xa508a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa508a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa508ac: b               #0xa507b0
    // 0xa508b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa508b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa508b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa508b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateWeekNumber(/* No info */) {
    // ** addr: 0xa508b8, size: 0xc0
    // 0xa508b8: EnterFrame
    //     0xa508b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa508bc: mov             fp, SP
    // 0xa508c0: AllocStack(0x8)
    //     0xa508c0: sub             SP, SP, #8
    // 0xa508c4: SetupParameters(_TableCalendarState<C1X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xa508c4: mov             x3, x1
    //     0xa508c8: stur            x1, [fp, #-8]
    //     0xa508cc: mov             x1, x2
    // 0xa508d0: CheckStackOverflow
    //     0xa508d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa508d4: cmp             SP, x16
    //     0xa508d8: b.ls            #0xa5094c
    // 0xa508dc: r0 = LoadClassIdInstr(r1)
    //     0xa508dc: ldur            x0, [x1, #-1]
    //     0xa508e0: ubfx            x0, x0, #0xc, #0x14
    // 0xa508e4: r2 = Instance_Duration
    //     0xa508e4: add             x2, PP, #0x47, lsl #12  ; [pp+0x47e88] Obj!Duration@e3a2c1
    //     0xa508e8: ldr             x2, [x2, #0xe88]
    // 0xa508ec: r0 = GDT[cid_x0 + -0xf8f]()
    //     0xa508ec: sub             lr, x0, #0xf8f
    //     0xa508f0: ldr             lr, [x21, lr, lsl #3]
    //     0xa508f4: blr             lr
    // 0xa508f8: ldur            x1, [fp, #-8]
    // 0xa508fc: mov             x2, x0
    // 0xa50900: r0 = _dayOfYear()
    //     0xa50900: bl              #0xa50978  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_dayOfYear
    // 0xa50904: sub             x1, x0, #1
    // 0xa50908: scvtf           d0, x1
    // 0xa5090c: d1 = 7.000000
    //     0xa5090c: fmov            d1, #7.00000000
    // 0xa50910: fdiv            d2, d0, d1
    // 0xa50914: fcmp            d2, d2
    // 0xa50918: b.vs            #0xa50954
    // 0xa5091c: fcvtms          x1, d2
    // 0xa50920: asr             x16, x1, #0x1e
    // 0xa50924: cmp             x16, x1, asr #63
    // 0xa50928: b.ne            #0xa50954
    // 0xa5092c: lsl             x1, x1, #1
    // 0xa50930: r2 = LoadInt32Instr(r1)
    //     0xa50930: sbfx            x2, x1, #1, #0x1f
    //     0xa50934: tbz             w1, #0, #0xa5093c
    //     0xa50938: ldur            x2, [x1, #7]
    // 0xa5093c: add             x0, x2, #1
    // 0xa50940: LeaveFrame
    //     0xa50940: mov             SP, fp
    //     0xa50944: ldp             fp, lr, [SP], #0x10
    // 0xa50948: ret
    //     0xa50948: ret             
    // 0xa5094c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5094c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50950: b               #0xa508dc
    // 0xa50954: SaveReg d2
    //     0xa50954: str             q2, [SP, #-0x10]!
    // 0xa50958: d0 = 0.000000
    //     0xa50958: fmov            d0, d2
    // 0xa5095c: r0 = 68
    //     0xa5095c: movz            x0, #0x44
    // 0xa50960: r30 = DoubleToIntegerStub
    //     0xa50960: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xa50964: LoadField: r30 = r30->field_7
    //     0xa50964: ldur            lr, [lr, #7]
    // 0xa50968: blr             lr
    // 0xa5096c: mov             x1, x0
    // 0xa50970: RestoreReg d2
    //     0xa50970: ldr             q2, [SP], #0x10
    // 0xa50974: b               #0xa50930
  }
  _ _dayOfYear(/* No info */) {
    // ** addr: 0xa50978, size: 0x98
    // 0xa50978: EnterFrame
    //     0xa50978: stp             fp, lr, [SP, #-0x10]!
    //     0xa5097c: mov             fp, SP
    // 0xa50980: AllocStack(0x18)
    //     0xa50980: sub             SP, SP, #0x18
    // 0xa50984: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xa50984: mov             x0, x2
    //     0xa50988: stur            x2, [fp, #-8]
    // 0xa5098c: CheckStackOverflow
    //     0xa5098c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50990: cmp             SP, x16
    //     0xa50994: b.ls            #0xa50a08
    // 0xa50998: mov             x1, x0
    // 0xa5099c: r0 = normalizeDate()
    //     0xa5099c: bl              #0xa50a10  ; [package:table_calendar/src/shared/utils.dart] ::normalizeDate
    // 0xa509a0: mov             x2, x0
    // 0xa509a4: ldur            x1, [fp, #-8]
    // 0xa509a8: stur            x2, [fp, #-0x10]
    // 0xa509ac: r0 = LoadClassIdInstr(r1)
    //     0xa509ac: ldur            x0, [x1, #-1]
    //     0xa509b0: ubfx            x0, x0, #0xc, #0x14
    // 0xa509b4: r0 = GDT[cid_x0 + -0xff6]()
    //     0xa509b4: sub             lr, x0, #0xff6
    //     0xa509b8: ldr             lr, [x21, lr, lsl #3]
    //     0xa509bc: blr             lr
    // 0xa509c0: stur            x0, [fp, #-0x18]
    // 0xa509c4: r0 = DateTime()
    //     0xa509c4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xa509c8: mov             x1, x0
    // 0xa509cc: ldur            x2, [fp, #-0x18]
    // 0xa509d0: stur            x0, [fp, #-8]
    // 0xa509d4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa509d4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa509d8: r0 = DateTime.utc()
    //     0xa509d8: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xa509dc: ldur            x1, [fp, #-0x10]
    // 0xa509e0: ldur            x2, [fp, #-8]
    // 0xa509e4: r0 = difference()
    //     0xa509e4: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0xa509e8: LoadField: r1 = r0->field_7
    //     0xa509e8: ldur            x1, [x0, #7]
    // 0xa509ec: r2 = 86400000000
    //     0xa509ec: add             x2, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xa509f0: ldr             x2, [x2, #0x268]
    // 0xa509f4: sdiv            x3, x1, x2
    // 0xa509f8: add             x0, x3, #1
    // 0xa509fc: LeaveFrame
    //     0xa509fc: mov             SP, fp
    //     0xa50a00: ldp             fp, lr, [SP], #0x10
    // 0xa50a04: ret
    //     0xa50a04: ret             
    // 0xa50a08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50a08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50a0c: b               #0xa50998
  }
  [closure] void <anonymous closure>(dynamic, DateTime) {
    // ** addr: 0xa50b08, size: 0xb8
    // 0xa50b08: EnterFrame
    //     0xa50b08: stp             fp, lr, [SP, #-0x10]!
    //     0xa50b0c: mov             fp, SP
    // 0xa50b10: AllocStack(0x8)
    //     0xa50b10: sub             SP, SP, #8
    // 0xa50b14: SetupParameters()
    //     0xa50b14: ldr             x0, [fp, #0x18]
    //     0xa50b18: ldur            w3, [x0, #0x17]
    //     0xa50b1c: add             x3, x3, HEAP, lsl #32
    //     0xa50b20: stur            x3, [fp, #-8]
    // 0xa50b24: CheckStackOverflow
    //     0xa50b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50b28: cmp             SP, x16
    //     0xa50b2c: b.ls            #0xa50ba4
    // 0xa50b30: LoadField: r0 = r3->field_f
    //     0xa50b30: ldur            w0, [x3, #0xf]
    // 0xa50b34: DecompressPointer r0
    //     0xa50b34: add             x0, x0, HEAP, lsl #32
    // 0xa50b38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa50b38: ldur            w1, [x0, #0x17]
    // 0xa50b3c: DecompressPointer r1
    //     0xa50b3c: add             x1, x1, HEAP, lsl #32
    // 0xa50b40: r16 = Sentinel
    //     0xa50b40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa50b44: cmp             w1, w16
    // 0xa50b48: b.eq            #0xa50bac
    // 0xa50b4c: ldr             x2, [fp, #0x10]
    // 0xa50b50: r0 = value=()
    //     0xa50b50: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xa50b54: ldur            x0, [fp, #-8]
    // 0xa50b58: LoadField: r1 = r0->field_f
    //     0xa50b58: ldur            w1, [x0, #0xf]
    // 0xa50b5c: DecompressPointer r1
    //     0xa50b5c: add             x1, x1, HEAP, lsl #32
    // 0xa50b60: LoadField: r0 = r1->field_b
    //     0xa50b60: ldur            w0, [x1, #0xb]
    // 0xa50b64: DecompressPointer r0
    //     0xa50b64: add             x0, x0, HEAP, lsl #32
    // 0xa50b68: cmp             w0, NULL
    // 0xa50b6c: b.eq            #0xa50bb8
    // 0xa50b70: LoadField: r1 = r0->field_c7
    //     0xa50b70: ldur            w1, [x0, #0xc7]
    // 0xa50b74: DecompressPointer r1
    //     0xa50b74: add             x1, x1, HEAP, lsl #32
    // 0xa50b78: cmp             w1, NULL
    // 0xa50b7c: b.eq            #0xa50bbc
    // 0xa50b80: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa50b80: ldur            w0, [x1, #0x17]
    // 0xa50b84: DecompressPointer r0
    //     0xa50b84: add             x0, x0, HEAP, lsl #32
    // 0xa50b88: mov             x1, x0
    // 0xa50b8c: ldr             x2, [fp, #0x10]
    // 0xa50b90: r0 = onPageChanged()
    //     0xa50b90: bl              #0xa50bfc  ; [package:nuonline/app/modules/calendar/controllers/calendar_controller.dart] CalendarController::onPageChanged
    // 0xa50b94: r0 = Null
    //     0xa50b94: mov             x0, NULL
    // 0xa50b98: LeaveFrame
    //     0xa50b98: mov             SP, fp
    //     0xa50b9c: ldp             fp, lr, [SP], #0x10
    // 0xa50ba0: ret
    //     0xa50ba0: ret             
    // 0xa50ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50ba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50ba8: b               #0xa50b30
    // 0xa50bac: r9 = _focusedDay
    //     0xa50bac: add             x9, PP, #0x47, lsl #12  ; [pp+0x47de8] Field <_TableCalendarState@1928116939._focusedDay@1928116939>: late final (offset: 0x18)
    //     0xa50bb0: ldr             x9, [x9, #0xde8]
    // 0xa50bb4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa50bb4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa50bb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50bb8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50bbc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa50bbc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, PageController) {
    // ** addr: 0xa50c68, size: 0xf8
    // 0xa50c68: EnterFrame
    //     0xa50c68: stp             fp, lr, [SP, #-0x10]!
    //     0xa50c6c: mov             fp, SP
    // 0xa50c70: AllocStack(0x18)
    //     0xa50c70: sub             SP, SP, #0x18
    // 0xa50c74: SetupParameters()
    //     0xa50c74: ldr             x0, [fp, #0x18]
    //     0xa50c78: ldur            w1, [x0, #0x17]
    //     0xa50c7c: add             x1, x1, HEAP, lsl #32
    //     0xa50c80: stur            x1, [fp, #-0x10]
    // 0xa50c84: CheckStackOverflow
    //     0xa50c84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50c88: cmp             SP, x16
    //     0xa50c8c: b.ls            #0xa50d50
    // 0xa50c90: LoadField: r0 = r1->field_f
    //     0xa50c90: ldur            w0, [x1, #0xf]
    // 0xa50c94: DecompressPointer r0
    //     0xa50c94: add             x0, x0, HEAP, lsl #32
    // 0xa50c98: stur            x0, [fp, #-8]
    // 0xa50c9c: LoadField: r2 = r0->field_13
    //     0xa50c9c: ldur            w2, [x0, #0x13]
    // 0xa50ca0: DecompressPointer r2
    //     0xa50ca0: add             x2, x2, HEAP, lsl #32
    // 0xa50ca4: r16 = Sentinel
    //     0xa50ca4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa50ca8: cmp             w2, w16
    // 0xa50cac: b.ne            #0xa50cb8
    // 0xa50cb0: mov             x2, x0
    // 0xa50cb4: b               #0xa50cd0
    // 0xa50cb8: r16 = "_pageController@1928116939"
    //     0xa50cb8: add             x16, PP, #0x47, lsl #12  ; [pp+0x47e90] "_pageController@1928116939"
    //     0xa50cbc: ldr             x16, [x16, #0xe90]
    // 0xa50cc0: str             x16, [SP]
    // 0xa50cc4: r0 = _throwFieldAlreadyInitialized()
    //     0xa50cc4: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xa50cc8: ldur            x1, [fp, #-0x10]
    // 0xa50ccc: ldur            x2, [fp, #-8]
    // 0xa50cd0: ldr             x0, [fp, #0x10]
    // 0xa50cd4: StoreField: r2->field_13 = r0
    //     0xa50cd4: stur            w0, [x2, #0x13]
    //     0xa50cd8: ldurb           w16, [x2, #-1]
    //     0xa50cdc: ldurb           w17, [x0, #-1]
    //     0xa50ce0: and             x16, x17, x16, lsr #2
    //     0xa50ce4: tst             x16, HEAP, lsr #32
    //     0xa50ce8: b.eq            #0xa50cf0
    //     0xa50cec: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa50cf0: LoadField: r2 = r1->field_f
    //     0xa50cf0: ldur            w2, [x1, #0xf]
    // 0xa50cf4: DecompressPointer r2
    //     0xa50cf4: add             x2, x2, HEAP, lsl #32
    // 0xa50cf8: LoadField: r1 = r2->field_b
    //     0xa50cf8: ldur            w1, [x2, #0xb]
    // 0xa50cfc: DecompressPointer r1
    //     0xa50cfc: add             x1, x1, HEAP, lsl #32
    // 0xa50d00: cmp             w1, NULL
    // 0xa50d04: b.eq            #0xa50d58
    // 0xa50d08: LoadField: r2 = r1->field_cf
    //     0xa50d08: ldur            w2, [x1, #0xcf]
    // 0xa50d0c: DecompressPointer r2
    //     0xa50d0c: add             x2, x2, HEAP, lsl #32
    // 0xa50d10: cmp             w2, NULL
    // 0xa50d14: b.eq            #0xa50d5c
    // 0xa50d18: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa50d18: ldur            w1, [x2, #0x17]
    // 0xa50d1c: DecompressPointer r1
    //     0xa50d1c: add             x1, x1, HEAP, lsl #32
    // 0xa50d20: ldr             x0, [fp, #0x10]
    // 0xa50d24: StoreField: r1->field_57 = r0
    //     0xa50d24: stur            w0, [x1, #0x57]
    //     0xa50d28: ldurb           w16, [x1, #-1]
    //     0xa50d2c: ldurb           w17, [x0, #-1]
    //     0xa50d30: and             x16, x17, x16, lsr #2
    //     0xa50d34: tst             x16, HEAP, lsr #32
    //     0xa50d38: b.eq            #0xa50d40
    //     0xa50d3c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa50d40: r0 = Null
    //     0xa50d40: mov             x0, NULL
    // 0xa50d44: LeaveFrame
    //     0xa50d44: mov             SP, fp
    //     0xa50d48: ldp             fp, lr, [SP], #0x10
    // 0xa50d4c: ret
    //     0xa50d4c: ret             
    // 0xa50d50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa50d50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa50d54: b               #0xa50c90
    // 0xa50d58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa50d58: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa50d5c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa50d5c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa84200, size: 0x54
    // 0xa84200: EnterFrame
    //     0xa84200: stp             fp, lr, [SP, #-0x10]!
    //     0xa84204: mov             fp, SP
    // 0xa84208: CheckStackOverflow
    //     0xa84208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8420c: cmp             SP, x16
    //     0xa84210: b.ls            #0xa84240
    // 0xa84214: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa84214: ldur            w0, [x1, #0x17]
    // 0xa84218: DecompressPointer r0
    //     0xa84218: add             x0, x0, HEAP, lsl #32
    // 0xa8421c: r16 = Sentinel
    //     0xa8421c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa84220: cmp             w0, w16
    // 0xa84224: b.eq            #0xa84248
    // 0xa84228: mov             x1, x0
    // 0xa8422c: r0 = dispose()
    //     0xa8422c: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xa84230: r0 = Null
    //     0xa84230: mov             x0, NULL
    // 0xa84234: LeaveFrame
    //     0xa84234: mov             SP, fp
    //     0xa84238: ldp             fp, lr, [SP], #0x10
    // 0xa8423c: ret
    //     0xa8423c: ret             
    // 0xa84240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa84240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa84244: b               #0xa84214
    // 0xa84248: r9 = _focusedDay
    //     0xa84248: add             x9, PP, #0x47, lsl #12  ; [pp+0x47de8] Field <_TableCalendarState@1928116939._focusedDay@1928116939>: late final (offset: 0x18)
    //     0xa8424c: ldr             x9, [x9, #0xde8]
    // 0xa84250: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa84250: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4687, size: 0xd4, field offset: 0xc
class TableCalendar<X0> extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa95518, size: 0x4c
    // 0xa95518: EnterFrame
    //     0xa95518: stp             fp, lr, [SP, #-0x10]!
    //     0xa9551c: mov             fp, SP
    // 0xa95520: LoadField: r2 = r1->field_b
    //     0xa95520: ldur            w2, [x1, #0xb]
    // 0xa95524: DecompressPointer r2
    //     0xa95524: add             x2, x2, HEAP, lsl #32
    // 0xa95528: r1 = Null
    //     0xa95528: mov             x1, NULL
    // 0xa9552c: r3 = <TableCalendar<X0>, X0>
    //     0xa9552c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40698] TypeArguments: <TableCalendar<X0>, X0>
    //     0xa95530: ldr             x3, [x3, #0x698]
    // 0xa95534: r30 = InstantiateTypeArgumentsStub
    //     0xa95534: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xa95538: LoadField: r30 = r30->field_7
    //     0xa95538: ldur            lr, [lr, #7]
    // 0xa9553c: blr             lr
    // 0xa95540: mov             x1, x0
    // 0xa95544: r0 = _TableCalendarState()
    //     0xa95544: bl              #0xa95564  ; Allocate_TableCalendarStateStub -> _TableCalendarState<C1X0> (size=0x24)
    // 0xa95548: r1 = Sentinel
    //     0xa95548: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9554c: StoreField: r0->field_13 = r1
    //     0xa9554c: stur            w1, [x0, #0x13]
    // 0xa95550: ArrayStore: r0[0] = r1  ; List_4
    //     0xa95550: stur            w1, [x0, #0x17]
    // 0xa95554: StoreField: r0->field_1b = r1
    //     0xa95554: stur            w1, [x0, #0x1b]
    // 0xa95558: LeaveFrame
    //     0xa95558: mov             SP, fp
    //     0xa9555c: ldp             fp, lr, [SP], #0x10
    // 0xa95560: ret
    //     0xa95560: ret             
  }
  _ TableCalendar(/* No info */) {
    // ** addr: 0xb89008, size: 0x2dc
    // 0xb89008: EnterFrame
    //     0xb89008: stp             fp, lr, [SP, #-0x10]!
    //     0xb8900c: mov             fp, SP
    // 0xb89010: AllocStack(0x18)
    //     0xb89010: sub             SP, SP, #0x18
    // 0xb89014: r8 = const [0x6, 0x7]
    //     0xb89014: add             x8, PP, #0x35, lsl #12  ; [pp+0x35eb0] List<int>(2)
    //     0xb89018: ldr             x8, [x8, #0xeb0]
    // 0xb8901c: r0 = Instance_CalendarFormat
    //     0xb8901c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35eb8] Obj!CalendarFormat@e2e041
    //     0xb89020: ldr             x0, [x0, #0xeb8]
    // 0xb89024: r4 = _ConstMap len:3
    //     0xb89024: add             x4, PP, #0x35, lsl #12  ; [pp+0x35ec0] Map<CalendarFormat, String>(3)
    //     0xb89028: ldr             x4, [x4, #0xec0]
    // 0xb8902c: r25 = false
    //     0xb8902c: add             x25, NULL, #0x30  ; false
    // 0xb89030: r24 = true
    //     0xb89030: add             x24, NULL, #0x20  ; true
    // 0xb89034: r23 = Instance_Duration
    //     0xb89034: add             x23, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xb89038: ldr             x23, [x23, #0x368]
    // 0xb8903c: r20 = Instance__Linear
    //     0xb8903c: ldr             x20, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xb89040: r19 = Instance_Duration
    //     0xb89040: add             x19, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xb89044: ldr             x19, [x19, #0x9c0]
    // 0xb89048: r14 = Instance_Cubic
    //     0xb89048: add             x14, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xb8904c: ldr             x14, [x14, #0xb28]
    // 0xb89050: r13 = Instance_StartingDayOfWeek
    //     0xb89050: add             x13, PP, #0x35, lsl #12  ; [pp+0x35ec8] Obj!StartingDayOfWeek@e2e021
    //     0xb89054: ldr             x13, [x13, #0xec8]
    // 0xb89058: r12 = Instance_HitTestBehavior
    //     0xb89058: add             x12, PP, #0x25, lsl #12  ; [pp+0x251c8] Obj!HitTestBehavior@e358c1
    //     0xb8905c: ldr             x12, [x12, #0x1c8]
    // 0xb89060: r11 = Instance_AvailableGestures
    //     0xb89060: add             x11, PP, #0x35, lsl #12  ; [pp+0x35ed0] Obj!AvailableGestures@e2e0a1
    //     0xb89064: ldr             x11, [x11, #0xed0]
    // 0xb89068: r10 = Instance_SimpleSwipeConfig
    //     0xb89068: add             x10, PP, #0x35, lsl #12  ; [pp+0x35ed8] Obj!SimpleSwipeConfig@e0c0c1
    //     0xb8906c: ldr             x10, [x10, #0xed8]
    // 0xb89070: r9 = Instance_HeaderStyle
    //     0xb89070: add             x9, PP, #0x35, lsl #12  ; [pp+0x35ee0] Obj!HeaderStyle@e0bf61
    //     0xb89074: ldr             x9, [x9, #0xee0]
    // 0xb89078: d1 = 64.000000
    //     0xb89078: add             x17, PP, #0x25, lsl #12  ; [pp+0x25238] IMM: double(64) from 0x4050000000000000
    //     0xb8907c: ldr             d1, [x17, #0x238]
    // 0xb89080: d0 = 16.000000
    //     0xb89080: fmov            d0, #16.00000000
    // 0xb89084: stur            x1, [fp, #-0x10]
    // 0xb89088: mov             x16, x6
    // 0xb8908c: mov             x6, x1
    // 0xb89090: mov             x1, x16
    // 0xb89094: mov             x16, x2
    // 0xb89098: mov             x2, x6
    // 0xb8909c: mov             x6, x16
    // 0xb890a0: mov             x16, x7
    // 0xb890a4: mov             x7, x2
    // 0xb890a8: mov             x2, x16
    // 0xb890ac: stur            x3, [fp, #-0x18]
    // 0xb890b0: mov             x16, x5
    // 0xb890b4: mov             x5, x3
    // 0xb890b8: mov             x3, x16
    // 0xb890bc: stur            x2, [fp, #-8]
    // 0xb890c0: CheckStackOverflow
    //     0xb890c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb890c4: cmp             SP, x16
    //     0xb890c8: b.ls            #0xb892dc
    // 0xb890cc: StoreField: r7->field_2b = r8
    //     0xb890cc: stur            w8, [x7, #0x2b]
    // 0xb890d0: StoreField: r7->field_2f = r0
    //     0xb890d0: stur            w0, [x7, #0x2f]
    // 0xb890d4: StoreField: r7->field_33 = r4
    //     0xb890d4: stur            w4, [x7, #0x33]
    // 0xb890d8: StoreField: r7->field_37 = r25
    //     0xb890d8: stur            w25, [x7, #0x37]
    // 0xb890dc: StoreField: r7->field_3b = r25
    //     0xb890dc: stur            w25, [x7, #0x3b]
    // 0xb890e0: StoreField: r7->field_3f = r25
    //     0xb890e0: stur            w25, [x7, #0x3f]
    // 0xb890e4: StoreField: r7->field_43 = r24
    //     0xb890e4: stur            w24, [x7, #0x43]
    // 0xb890e8: StoreField: r7->field_47 = r25
    //     0xb890e8: stur            w25, [x7, #0x47]
    // 0xb890ec: StoreField: r7->field_4b = r25
    //     0xb890ec: stur            w25, [x7, #0x4b]
    // 0xb890f0: StoreField: r7->field_4f = r25
    //     0xb890f0: stur            w25, [x7, #0x4f]
    // 0xb890f4: StoreField: r7->field_53 = d1
    //     0xb890f4: stur            d1, [x7, #0x53]
    // 0xb890f8: StoreField: r7->field_5b = d0
    //     0xb890f8: stur            d0, [x7, #0x5b]
    // 0xb890fc: StoreField: r7->field_63 = r23
    //     0xb890fc: stur            w23, [x7, #0x63]
    // 0xb89100: StoreField: r7->field_67 = r20
    //     0xb89100: stur            w20, [x7, #0x67]
    // 0xb89104: StoreField: r7->field_6b = r19
    //     0xb89104: stur            w19, [x7, #0x6b]
    // 0xb89108: StoreField: r7->field_6f = r14
    //     0xb89108: stur            w14, [x7, #0x6f]
    // 0xb8910c: StoreField: r7->field_73 = r13
    //     0xb8910c: stur            w13, [x7, #0x73]
    // 0xb89110: StoreField: r7->field_77 = r12
    //     0xb89110: stur            w12, [x7, #0x77]
    // 0xb89114: StoreField: r7->field_7b = r11
    //     0xb89114: stur            w11, [x7, #0x7b]
    // 0xb89118: StoreField: r7->field_7f = r10
    //     0xb89118: stur            w10, [x7, #0x7f]
    // 0xb8911c: StoreField: r7->field_83 = r9
    //     0xb8911c: stur            w9, [x7, #0x83]
    // 0xb89120: r0 = Instance_DaysOfWeekStyle
    //     0xb89120: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ee8] Obj!DaysOfWeekStyle@e0bfc1
    //     0xb89124: ldr             x0, [x0, #0xee8]
    // 0xb89128: StoreField: r7->field_87 = r0
    //     0xb89128: stur            w0, [x7, #0x87]
    // 0xb8912c: r0 = Instance_CalendarStyle
    //     0xb8912c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ef0] Obj!CalendarStyle@e0c001
    //     0xb89130: ldr             x0, [x0, #0xef0]
    // 0xb89134: StoreField: r7->field_8b = r0
    //     0xb89134: stur            w0, [x7, #0x8b]
    // 0xb89138: mov             x0, x6
    // 0xb8913c: StoreField: r7->field_8f = r0
    //     0xb8913c: stur            w0, [x7, #0x8f]
    //     0xb89140: ldurb           w16, [x7, #-1]
    //     0xb89144: ldurb           w17, [x0, #-1]
    //     0xb89148: and             x16, x17, x16, lsr #2
    //     0xb8914c: tst             x16, HEAP, lsr #32
    //     0xb89150: b.eq            #0xb89158
    //     0xb89154: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb89158: r0 = Instance_RangeSelectionMode
    //     0xb89158: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ef8] Obj!RangeSelectionMode@e2df41
    //     0xb8915c: ldr             x0, [x0, #0xef8]
    // 0xb89160: StoreField: r7->field_93 = r0
    //     0xb89160: stur            w0, [x7, #0x93]
    // 0xb89164: StoreField: r7->field_97 = r25
    //     0xb89164: stur            w25, [x7, #0x97]
    // 0xb89168: ldr             x0, [fp, #0x10]
    // 0xb8916c: StoreField: r7->field_a3 = r0
    //     0xb8916c: stur            w0, [x7, #0xa3]
    //     0xb89170: ldurb           w16, [x7, #-1]
    //     0xb89174: ldurb           w17, [x0, #-1]
    //     0xb89178: and             x16, x17, x16, lsr #2
    //     0xb8917c: tst             x16, HEAP, lsr #32
    //     0xb89180: b.eq            #0xb89188
    //     0xb89184: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb89188: mov             x0, x1
    // 0xb8918c: StoreField: r7->field_a7 = r0
    //     0xb8918c: stur            w0, [x7, #0xa7]
    //     0xb89190: ldurb           w16, [x7, #-1]
    //     0xb89194: ldurb           w17, [x0, #-1]
    //     0xb89198: and             x16, x17, x16, lsr #2
    //     0xb8919c: tst             x16, HEAP, lsr #32
    //     0xb891a0: b.eq            #0xb891a8
    //     0xb891a4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb891a8: ldr             x0, [fp, #0x20]
    // 0xb891ac: StoreField: r7->field_af = r0
    //     0xb891ac: stur            w0, [x7, #0xaf]
    //     0xb891b0: ldurb           w16, [x7, #-1]
    //     0xb891b4: ldurb           w17, [x0, #-1]
    //     0xb891b8: and             x16, x17, x16, lsr #2
    //     0xb891bc: tst             x16, HEAP, lsr #32
    //     0xb891c0: b.eq            #0xb891c8
    //     0xb891c4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb891c8: ldr             x0, [fp, #0x18]
    // 0xb891cc: StoreField: r7->field_c7 = r0
    //     0xb891cc: stur            w0, [x7, #0xc7]
    //     0xb891d0: ldurb           w16, [x7, #-1]
    //     0xb891d4: ldurb           w17, [x0, #-1]
    //     0xb891d8: and             x16, x17, x16, lsr #2
    //     0xb891dc: tst             x16, HEAP, lsr #32
    //     0xb891e0: b.eq            #0xb891e8
    //     0xb891e4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb891e8: ldr             x0, [fp, #0x28]
    // 0xb891ec: StoreField: r7->field_cf = r0
    //     0xb891ec: stur            w0, [x7, #0xcf]
    //     0xb891f0: ldurb           w16, [x7, #-1]
    //     0xb891f4: ldurb           w17, [x0, #-1]
    //     0xb891f8: and             x16, x17, x16, lsr #2
    //     0xb891fc: tst             x16, HEAP, lsr #32
    //     0xb89200: b.eq            #0xb89208
    //     0xb89204: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xb89208: mov             x1, x3
    // 0xb8920c: r0 = normalizeDate()
    //     0xb8920c: bl              #0xa50a10  ; [package:table_calendar/src/shared/utils.dart] ::normalizeDate
    // 0xb89210: ldur            x2, [fp, #-0x10]
    // 0xb89214: StoreField: r2->field_1b = r0
    //     0xb89214: stur            w0, [x2, #0x1b]
    //     0xb89218: ldurb           w16, [x2, #-1]
    //     0xb8921c: ldurb           w17, [x0, #-1]
    //     0xb89220: and             x16, x17, x16, lsr #2
    //     0xb89224: tst             x16, HEAP, lsr #32
    //     0xb89228: b.eq            #0xb89230
    //     0xb8922c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb89230: ldur            x1, [fp, #-0x18]
    // 0xb89234: r0 = normalizeDate()
    //     0xb89234: bl              #0xa50a10  ; [package:table_calendar/src/shared/utils.dart] ::normalizeDate
    // 0xb89238: ldur            x2, [fp, #-0x10]
    // 0xb8923c: StoreField: r2->field_1f = r0
    //     0xb8923c: stur            w0, [x2, #0x1f]
    //     0xb89240: ldurb           w16, [x2, #-1]
    //     0xb89244: ldurb           w17, [x0, #-1]
    //     0xb89248: and             x16, x17, x16, lsr #2
    //     0xb8924c: tst             x16, HEAP, lsr #32
    //     0xb89250: b.eq            #0xb89258
    //     0xb89254: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb89258: ldur            x1, [fp, #-8]
    // 0xb8925c: r0 = normalizeDate()
    //     0xb8925c: bl              #0xa50a10  ; [package:table_calendar/src/shared/utils.dart] ::normalizeDate
    // 0xb89260: ldur            x1, [fp, #-0x10]
    // 0xb89264: StoreField: r1->field_23 = r0
    //     0xb89264: stur            w0, [x1, #0x23]
    //     0xb89268: ldurb           w16, [x1, #-1]
    //     0xb8926c: ldurb           w17, [x0, #-1]
    //     0xb89270: and             x16, x17, x16, lsr #2
    //     0xb89274: tst             x16, HEAP, lsr #32
    //     0xb89278: b.eq            #0xb89280
    //     0xb8927c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb89280: r0 = DateTime()
    //     0xb89280: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xb89284: mov             x1, x0
    // 0xb89288: r0 = false
    //     0xb89288: add             x0, NULL, #0x30  ; false
    // 0xb8928c: stur            x1, [fp, #-8]
    // 0xb89290: StoreField: r1->field_13 = r0
    //     0xb89290: stur            w0, [x1, #0x13]
    // 0xb89294: r0 = _getCurrentMicros()
    //     0xb89294: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xb89298: r1 = LoadInt32Instr(r0)
    //     0xb89298: sbfx            x1, x0, #1, #0x1f
    //     0xb8929c: tbz             w0, #0, #0xb892a4
    //     0xb892a0: ldur            x1, [x0, #7]
    // 0xb892a4: ldur            x0, [fp, #-8]
    // 0xb892a8: StoreField: r0->field_7 = r1
    //     0xb892a8: stur            x1, [x0, #7]
    // 0xb892ac: ldur            x1, [fp, #-0x10]
    // 0xb892b0: StoreField: r1->field_27 = r0
    //     0xb892b0: stur            w0, [x1, #0x27]
    //     0xb892b4: ldurb           w16, [x1, #-1]
    //     0xb892b8: ldurb           w17, [x0, #-1]
    //     0xb892bc: and             x16, x17, x16, lsr #2
    //     0xb892c0: tst             x16, HEAP, lsr #32
    //     0xb892c4: b.eq            #0xb892cc
    //     0xb892c8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb892cc: r0 = Null
    //     0xb892cc: mov             x0, NULL
    // 0xb892d0: LeaveFrame
    //     0xb892d0: mov             SP, fp
    //     0xb892d4: ldp             fp, lr, [SP], #0x10
    // 0xb892d8: ret
    //     0xb892d8: ret             
    // 0xb892dc: r0 = StackOverflowSharedWithFPURegs()
    //     0xb892dc: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xb892e0: b               #0xb890cc
  }
}

// class id: 6766, size: 0x14, field offset: 0x14
enum RangeSelectionMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4eb44, size: 0x64
    // 0xc4eb44: EnterFrame
    //     0xc4eb44: stp             fp, lr, [SP, #-0x10]!
    //     0xc4eb48: mov             fp, SP
    // 0xc4eb4c: AllocStack(0x10)
    //     0xc4eb4c: sub             SP, SP, #0x10
    // 0xc4eb50: SetupParameters(RangeSelectionMode this /* r1 => r0, fp-0x8 */)
    //     0xc4eb50: mov             x0, x1
    //     0xc4eb54: stur            x1, [fp, #-8]
    // 0xc4eb58: CheckStackOverflow
    //     0xc4eb58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4eb5c: cmp             SP, x16
    //     0xc4eb60: b.ls            #0xc4eba0
    // 0xc4eb64: r1 = Null
    //     0xc4eb64: mov             x1, NULL
    // 0xc4eb68: r2 = 4
    //     0xc4eb68: movz            x2, #0x4
    // 0xc4eb6c: r0 = AllocateArray()
    //     0xc4eb6c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4eb70: r16 = "RangeSelectionMode."
    //     0xc4eb70: add             x16, PP, #0x40, lsl #12  ; [pp+0x40690] "RangeSelectionMode."
    //     0xc4eb74: ldr             x16, [x16, #0x690]
    // 0xc4eb78: StoreField: r0->field_f = r16
    //     0xc4eb78: stur            w16, [x0, #0xf]
    // 0xc4eb7c: ldur            x1, [fp, #-8]
    // 0xc4eb80: LoadField: r2 = r1->field_f
    //     0xc4eb80: ldur            w2, [x1, #0xf]
    // 0xc4eb84: DecompressPointer r2
    //     0xc4eb84: add             x2, x2, HEAP, lsl #32
    // 0xc4eb88: StoreField: r0->field_13 = r2
    //     0xc4eb88: stur            w2, [x0, #0x13]
    // 0xc4eb8c: str             x0, [SP]
    // 0xc4eb90: r0 = _interpolate()
    //     0xc4eb90: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4eb94: LeaveFrame
    //     0xc4eb94: mov             SP, fp
    //     0xc4eb98: ldp             fp, lr, [SP], #0x10
    // 0xc4eb9c: ret
    //     0xc4eb9c: ret             
    // 0xc4eba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4eba0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4eba4: b               #0xc4eb64
  }
}
