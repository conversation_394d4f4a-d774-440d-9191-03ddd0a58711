// lib: , url: package:table_calendar/src/table_calendar_base.dart

// class id: 1051188, size: 0x8
class :: {
}

// class id: 4080, size: 0x28, field offset: 0x14
class _TableCalendarBaseState extends State<dynamic> {

  late final ValueNotifier<double> _pageHeight; // offset: 0x14
  late final PageController _pageController; // offset: 0x18
  late int _previousIndex; // offset: 0x20
  late DateTime _focusedDay; // offset: 0x1c
  late bool _pageCallbackDisabled; // offset: 0x24

  _ initState(/* No info */) {
    // ** addr: 0x98111c, size: 0x2a4
    // 0x98111c: EnterFrame
    //     0x98111c: stp             fp, lr, [SP, #-0x10]!
    //     0x981120: mov             fp, SP
    // 0x981124: AllocStack(0x30)
    //     0x981124: sub             SP, SP, #0x30
    // 0x981128: SetupParameters(_TableCalendarBaseState this /* r1 => r3, fp-0x8 */)
    //     0x981128: mov             x3, x1
    //     0x98112c: stur            x1, [fp, #-8]
    // 0x981130: CheckStackOverflow
    //     0x981130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981134: cmp             SP, x16
    //     0x981138: b.ls            #0x981398
    // 0x98113c: LoadField: r0 = r3->field_b
    //     0x98113c: ldur            w0, [x3, #0xb]
    // 0x981140: DecompressPointer r0
    //     0x981140: add             x0, x0, HEAP, lsl #32
    // 0x981144: cmp             w0, NULL
    // 0x981148: b.eq            #0x9813a0
    // 0x98114c: LoadField: r1 = r0->field_13
    //     0x98114c: ldur            w1, [x0, #0x13]
    // 0x981150: DecompressPointer r1
    //     0x981150: add             x1, x1, HEAP, lsl #32
    // 0x981154: mov             x0, x1
    // 0x981158: StoreField: r3->field_1b = r0
    //     0x981158: stur            w0, [x3, #0x1b]
    //     0x98115c: ldurb           w16, [x3, #-1]
    //     0x981160: ldurb           w17, [x0, #-1]
    //     0x981164: and             x16, x17, x16, lsr #2
    //     0x981168: tst             x16, HEAP, lsr #32
    //     0x98116c: b.eq            #0x981174
    //     0x981170: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x981174: mov             x2, x1
    // 0x981178: mov             x1, x3
    // 0x98117c: r0 = _getRowCount()
    //     0x98117c: bl              #0x981584  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getRowCount
    // 0x981180: ldur            x1, [fp, #-8]
    // 0x981184: mov             x2, x0
    // 0x981188: r0 = _getPageHeight()
    //     0x981188: bl              #0x9814f4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getPageHeight
    // 0x98118c: r0 = inline_Allocate_Double()
    //     0x98118c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x981190: add             x0, x0, #0x10
    //     0x981194: cmp             x1, x0
    //     0x981198: b.ls            #0x9813a4
    //     0x98119c: str             x0, [THR, #0x50]  ; THR::top
    //     0x9811a0: sub             x0, x0, #0xf
    //     0x9811a4: movz            x1, #0xe15c
    //     0x9811a8: movk            x1, #0x3, lsl #16
    //     0x9811ac: stur            x1, [x0, #-1]
    // 0x9811b0: StoreField: r0->field_7 = d0
    //     0x9811b0: stur            d0, [x0, #7]
    // 0x9811b4: stur            x0, [fp, #-0x10]
    // 0x9811b8: r1 = <double>
    //     0x9811b8: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0x9811bc: r0 = ValueNotifier()
    //     0x9811bc: bl              #0x65a810  ; AllocateValueNotifierStub -> ValueNotifier<X0> (size=0x2c)
    // 0x9811c0: mov             x1, x0
    // 0x9811c4: ldur            x0, [fp, #-0x10]
    // 0x9811c8: stur            x1, [fp, #-0x18]
    // 0x9811cc: StoreField: r1->field_27 = r0
    //     0x9811cc: stur            w0, [x1, #0x27]
    // 0x9811d0: StoreField: r1->field_7 = rZR
    //     0x9811d0: stur            xzr, [x1, #7]
    // 0x9811d4: StoreField: r1->field_13 = rZR
    //     0x9811d4: stur            xzr, [x1, #0x13]
    // 0x9811d8: StoreField: r1->field_1b = rZR
    //     0x9811d8: stur            xzr, [x1, #0x1b]
    // 0x9811dc: r0 = InitLateStaticField(0x654) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x9811dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9811e0: ldr             x0, [x0, #0xca8]
    //     0x9811e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9811e8: cmp             w0, w16
    //     0x9811ec: b.ne            #0x9811f8
    //     0x9811f0: ldr             x2, [PP, #0x1d70]  ; [pp+0x1d70] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x654)
    //     0x9811f4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9811f8: mov             x1, x0
    // 0x9811fc: ldur            x0, [fp, #-0x18]
    // 0x981200: StoreField: r0->field_f = r1
    //     0x981200: stur            w1, [x0, #0xf]
    // 0x981204: ldur            x1, [fp, #-8]
    // 0x981208: LoadField: r2 = r1->field_13
    //     0x981208: ldur            w2, [x1, #0x13]
    // 0x98120c: DecompressPointer r2
    //     0x98120c: add             x2, x2, HEAP, lsl #32
    // 0x981210: r16 = Sentinel
    //     0x981210: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x981214: cmp             w2, w16
    // 0x981218: b.ne            #0x981224
    // 0x98121c: mov             x4, x1
    // 0x981220: b               #0x981238
    // 0x981224: r16 = "_pageHeight@1929380592"
    //     0x981224: add             x16, PP, #0x57, lsl #12  ; [pp+0x57e98] "_pageHeight@1929380592"
    //     0x981228: ldr             x16, [x16, #0xe98]
    // 0x98122c: str             x16, [SP]
    // 0x981230: r0 = _throwFieldAlreadyInitialized()
    //     0x981230: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x981234: ldur            x4, [fp, #-8]
    // 0x981238: ldur            x0, [fp, #-0x18]
    // 0x98123c: StoreField: r4->field_13 = r0
    //     0x98123c: stur            w0, [x4, #0x13]
    //     0x981240: ldurb           w16, [x4, #-1]
    //     0x981244: ldurb           w17, [x0, #-1]
    //     0x981248: and             x16, x17, x16, lsr #2
    //     0x98124c: tst             x16, HEAP, lsr #32
    //     0x981250: b.eq            #0x981258
    //     0x981254: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x981258: LoadField: r0 = r4->field_b
    //     0x981258: ldur            w0, [x4, #0xb]
    // 0x98125c: DecompressPointer r0
    //     0x98125c: add             x0, x0, HEAP, lsl #32
    // 0x981260: cmp             w0, NULL
    // 0x981264: b.eq            #0x9813b4
    // 0x981268: LoadField: r2 = r0->field_b
    //     0x981268: ldur            w2, [x0, #0xb]
    // 0x98126c: DecompressPointer r2
    //     0x98126c: add             x2, x2, HEAP, lsl #32
    // 0x981270: LoadField: r3 = r4->field_1b
    //     0x981270: ldur            w3, [x4, #0x1b]
    // 0x981274: DecompressPointer r3
    //     0x981274: add             x3, x3, HEAP, lsl #32
    // 0x981278: mov             x1, x4
    // 0x98127c: r0 = _getMonthCount()
    //     0x98127c: bl              #0x9813e4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getMonthCount
    // 0x981280: stur            x0, [fp, #-0x20]
    // 0x981284: r0 = PageController()
    //     0x981284: bl              #0x80f964  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x981288: mov             x2, x0
    // 0x98128c: ldur            x0, [fp, #-0x20]
    // 0x981290: stur            x2, [fp, #-0x10]
    // 0x981294: StoreField: r2->field_3f = r0
    //     0x981294: stur            x0, [x2, #0x3f]
    // 0x981298: r1 = true
    //     0x981298: add             x1, NULL, #0x20  ; true
    // 0x98129c: StoreField: r2->field_47 = r1
    //     0x98129c: stur            w1, [x2, #0x47]
    // 0x9812a0: d0 = 1.000000
    //     0x9812a0: fmov            d0, #1.00000000
    // 0x9812a4: StoreField: r2->field_4b = d0
    //     0x9812a4: stur            d0, [x2, #0x4b]
    // 0x9812a8: mov             x1, x2
    // 0x9812ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9812ac: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9812b0: r0 = ScrollController()
    //     0x9812b0: bl              #0x685160  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x9812b4: ldur            x0, [fp, #-8]
    // 0x9812b8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9812b8: ldur            w1, [x0, #0x17]
    // 0x9812bc: DecompressPointer r1
    //     0x9812bc: add             x1, x1, HEAP, lsl #32
    // 0x9812c0: r16 = Sentinel
    //     0x9812c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9812c4: cmp             w1, w16
    // 0x9812c8: b.ne            #0x9812d4
    // 0x9812cc: mov             x1, x0
    // 0x9812d0: b               #0x9812e8
    // 0x9812d4: r16 = "_pageController@1929380592"
    //     0x9812d4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ea0] "_pageController@1929380592"
    //     0x9812d8: ldr             x16, [x16, #0xea0]
    // 0x9812dc: str             x16, [SP]
    // 0x9812e0: r0 = _throwFieldAlreadyInitialized()
    //     0x9812e0: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x9812e4: ldur            x1, [fp, #-8]
    // 0x9812e8: ldur            x2, [fp, #-0x20]
    // 0x9812ec: ldur            x0, [fp, #-0x10]
    // 0x9812f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x9812f0: stur            w0, [x1, #0x17]
    //     0x9812f4: ldurb           w16, [x1, #-1]
    //     0x9812f8: ldurb           w17, [x0, #-1]
    //     0x9812fc: and             x16, x17, x16, lsr #2
    //     0x981300: tst             x16, HEAP, lsr #32
    //     0x981304: b.eq            #0x98130c
    //     0x981308: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x98130c: LoadField: r0 = r1->field_b
    //     0x98130c: ldur            w0, [x1, #0xb]
    // 0x981310: DecompressPointer r0
    //     0x981310: add             x0, x0, HEAP, lsl #32
    // 0x981314: cmp             w0, NULL
    // 0x981318: b.eq            #0x9813b8
    // 0x98131c: LoadField: r3 = r0->field_7b
    //     0x98131c: ldur            w3, [x0, #0x7b]
    // 0x981320: DecompressPointer r3
    //     0x981320: add             x3, x3, HEAP, lsl #32
    // 0x981324: cmp             w3, NULL
    // 0x981328: b.eq            #0x9813bc
    // 0x98132c: ldur            x16, [fp, #-0x10]
    // 0x981330: stp             x16, x3, [SP]
    // 0x981334: mov             x0, x3
    // 0x981338: ClosureCall
    //     0x981338: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x98133c: ldur            x2, [x0, #0x1f]
    //     0x981340: blr             x2
    // 0x981344: ldur            x2, [fp, #-0x20]
    // 0x981348: r0 = BoxInt64Instr(r2)
    //     0x981348: sbfiz           x0, x2, #1, #0x1f
    //     0x98134c: cmp             x2, x0, asr #1
    //     0x981350: b.eq            #0x98135c
    //     0x981354: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x981358: stur            x2, [x0, #7]
    // 0x98135c: ldur            x1, [fp, #-8]
    // 0x981360: StoreField: r1->field_1f = r0
    //     0x981360: stur            w0, [x1, #0x1f]
    //     0x981364: tbz             w0, #0, #0x981380
    //     0x981368: ldurb           w16, [x1, #-1]
    //     0x98136c: ldurb           w17, [x0, #-1]
    //     0x981370: and             x16, x17, x16, lsr #2
    //     0x981374: tst             x16, HEAP, lsr #32
    //     0x981378: b.eq            #0x981380
    //     0x98137c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x981380: r2 = false
    //     0x981380: add             x2, NULL, #0x30  ; false
    // 0x981384: StoreField: r1->field_23 = r2
    //     0x981384: stur            w2, [x1, #0x23]
    // 0x981388: r0 = Null
    //     0x981388: mov             x0, NULL
    // 0x98138c: LeaveFrame
    //     0x98138c: mov             SP, fp
    //     0x981390: ldp             fp, lr, [SP], #0x10
    // 0x981394: ret
    //     0x981394: ret             
    // 0x981398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98139c: b               #0x98113c
    // 0x9813a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9813a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9813a4: SaveReg d0
    //     0x9813a4: str             q0, [SP, #-0x10]!
    // 0x9813a8: r0 = AllocateDouble()
    //     0x9813a8: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9813ac: RestoreReg d0
    //     0x9813ac: ldr             q0, [SP], #0x10
    // 0x9813b0: b               #0x9811b0
    // 0x9813b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9813b4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9813b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9813b8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9813bc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x9813bc: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _getMonthCount(/* No info */) {
    // ** addr: 0x9813e4, size: 0x110
    // 0x9813e4: EnterFrame
    //     0x9813e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9813e8: mov             fp, SP
    // 0x9813ec: AllocStack(0x20)
    //     0x9813ec: sub             SP, SP, #0x20
    // 0x9813f0: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r2, fp-0x10 */)
    //     0x9813f0: stur            x2, [fp, #-8]
    //     0x9813f4: mov             x16, x3
    //     0x9813f8: mov             x3, x2
    //     0x9813fc: mov             x2, x16
    //     0x981400: stur            x2, [fp, #-0x10]
    // 0x981404: CheckStackOverflow
    //     0x981404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981408: cmp             SP, x16
    //     0x98140c: b.ls            #0x9814e4
    // 0x981410: r0 = LoadClassIdInstr(r2)
    //     0x981410: ldur            x0, [x2, #-1]
    //     0x981414: ubfx            x0, x0, #0xc, #0x14
    // 0x981418: mov             x1, x2
    // 0x98141c: r0 = GDT[cid_x0 + -0xff6]()
    //     0x98141c: sub             lr, x0, #0xff6
    //     0x981420: ldr             lr, [x21, lr, lsl #3]
    //     0x981424: blr             lr
    // 0x981428: ldur            x1, [fp, #-8]
    // 0x98142c: stur            x0, [fp, #-0x18]
    // 0x981430: r0 = _parts()
    //     0x981430: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x981434: mov             x2, x0
    // 0x981438: LoadField: r0 = r2->field_b
    //     0x981438: ldur            w0, [x2, #0xb]
    // 0x98143c: r1 = LoadInt32Instr(r0)
    //     0x98143c: sbfx            x1, x0, #1, #0x1f
    // 0x981440: mov             x0, x1
    // 0x981444: r1 = 8
    //     0x981444: movz            x1, #0x8
    // 0x981448: cmp             x1, x0
    // 0x98144c: b.hs            #0x9814ec
    // 0x981450: LoadField: r0 = r2->field_2f
    //     0x981450: ldur            w0, [x2, #0x2f]
    // 0x981454: DecompressPointer r0
    //     0x981454: add             x0, x0, HEAP, lsl #32
    // 0x981458: r1 = LoadInt32Instr(r0)
    //     0x981458: sbfx            x1, x0, #1, #0x1f
    //     0x98145c: tbz             w0, #0, #0x981464
    //     0x981460: ldur            x1, [x0, #7]
    // 0x981464: ldur            x0, [fp, #-0x18]
    // 0x981468: sub             x2, x0, x1
    // 0x98146c: ldur            x1, [fp, #-0x10]
    // 0x981470: stur            x2, [fp, #-0x20]
    // 0x981474: r0 = LoadClassIdInstr(r1)
    //     0x981474: ldur            x0, [x1, #-1]
    //     0x981478: ubfx            x0, x0, #0xc, #0x14
    // 0x98147c: r0 = GDT[cid_x0 + -0xfff]()
    //     0x98147c: sub             lr, x0, #0xfff
    //     0x981480: ldr             lr, [x21, lr, lsl #3]
    //     0x981484: blr             lr
    // 0x981488: ldur            x1, [fp, #-8]
    // 0x98148c: stur            x0, [fp, #-0x18]
    // 0x981490: r0 = _parts()
    //     0x981490: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x981494: mov             x2, x0
    // 0x981498: LoadField: r3 = r2->field_b
    //     0x981498: ldur            w3, [x2, #0xb]
    // 0x98149c: r0 = LoadInt32Instr(r3)
    //     0x98149c: sbfx            x0, x3, #1, #0x1f
    // 0x9814a0: r1 = 7
    //     0x9814a0: movz            x1, #0x7
    // 0x9814a4: cmp             x1, x0
    // 0x9814a8: b.hs            #0x9814f0
    // 0x9814ac: LoadField: r1 = r2->field_2b
    //     0x9814ac: ldur            w1, [x2, #0x2b]
    // 0x9814b0: DecompressPointer r1
    //     0x9814b0: add             x1, x1, HEAP, lsl #32
    // 0x9814b4: r2 = LoadInt32Instr(r1)
    //     0x9814b4: sbfx            x2, x1, #1, #0x1f
    //     0x9814b8: tbz             w1, #0, #0x9814c0
    //     0x9814bc: ldur            x2, [x1, #7]
    // 0x9814c0: ldur            x1, [fp, #-0x18]
    // 0x9814c4: sub             x3, x1, x2
    // 0x9814c8: ldur            x1, [fp, #-0x20]
    // 0x9814cc: r16 = 12
    //     0x9814cc: movz            x16, #0xc
    // 0x9814d0: mul             x2, x1, x16
    // 0x9814d4: add             x0, x2, x3
    // 0x9814d8: LeaveFrame
    //     0x9814d8: mov             SP, fp
    //     0x9814dc: ldp             fp, lr, [SP], #0x10
    // 0x9814e0: ret
    //     0x9814e0: ret             
    // 0x9814e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9814e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9814e8: b               #0x981410
    // 0x9814ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9814ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9814f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9814f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _getPageHeight(/* No info */) {
    // ** addr: 0x9814f4, size: 0x90
    // 0x9814f4: EnterFrame
    //     0x9814f4: stp             fp, lr, [SP, #-0x10]!
    //     0x9814f8: mov             fp, SP
    // 0x9814fc: AllocStack(0x10)
    //     0x9814fc: sub             SP, SP, #0x10
    // 0x981500: SetupParameters(_TableCalendarBaseState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x981500: mov             x0, x1
    //     0x981504: stur            x1, [fp, #-8]
    //     0x981508: stur            x2, [fp, #-0x10]
    // 0x98150c: CheckStackOverflow
    //     0x98150c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981510: cmp             SP, x16
    //     0x981514: b.ls            #0x981574
    // 0x981518: LoadField: r1 = r0->field_b
    //     0x981518: ldur            w1, [x0, #0xb]
    // 0x98151c: DecompressPointer r1
    //     0x98151c: add             x1, x1, HEAP, lsl #32
    // 0x981520: cmp             w1, NULL
    // 0x981524: b.eq            #0x98157c
    // 0x981528: r1 = Instance_EdgeInsets
    //     0x981528: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0x98152c: r0 = vertical()
    //     0x98152c: bl              #0x72e244  ; [package:flutter/src/painting/edge_insets.dart] EdgeInsetsGeometry::vertical
    // 0x981530: ldur            x0, [fp, #-8]
    // 0x981534: LoadField: r1 = r0->field_b
    //     0x981534: ldur            w1, [x0, #0xb]
    // 0x981538: DecompressPointer r1
    //     0x981538: add             x1, x1, HEAP, lsl #32
    // 0x98153c: cmp             w1, NULL
    // 0x981540: b.eq            #0x981580
    // 0x981544: ldur            x0, [fp, #-0x10]
    // 0x981548: scvtf           d1, x0
    // 0x98154c: d2 = 64.000000
    //     0x98154c: add             x17, PP, #0x25, lsl #12  ; [pp+0x25238] IMM: double(64) from 0x4050000000000000
    //     0x981550: ldr             d2, [x17, #0x238]
    // 0x981554: fmul            d3, d1, d2
    // 0x981558: d1 = 0.000000
    //     0x981558: eor             v1.16b, v1.16b, v1.16b
    // 0x98155c: fadd            d2, d3, d1
    // 0x981560: fadd            d1, d2, d0
    // 0x981564: mov             v0.16b, v1.16b
    // 0x981568: LeaveFrame
    //     0x981568: mov             SP, fp
    //     0x98156c: ldp             fp, lr, [SP], #0x10
    // 0x981570: ret
    //     0x981570: ret             
    // 0x981574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981578: b               #0x981518
    // 0x98157c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98157c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x981580: r0 = NullCastErrorSharedWithFPURegs()
    //     0x981580: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _getRowCount(/* No info */) {
    // ** addr: 0x981584, size: 0x10c
    // 0x981584: EnterFrame
    //     0x981584: stp             fp, lr, [SP, #-0x10]!
    //     0x981588: mov             fp, SP
    // 0x98158c: AllocStack(0x20)
    //     0x98158c: sub             SP, SP, #0x20
    // 0x981590: SetupParameters(_TableCalendarBaseState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x981590: mov             x3, x1
    //     0x981594: mov             x0, x2
    //     0x981598: stur            x1, [fp, #-8]
    //     0x98159c: stur            x2, [fp, #-0x10]
    // 0x9815a0: CheckStackOverflow
    //     0x9815a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9815a4: cmp             SP, x16
    //     0x9815a8: b.ls            #0x981684
    // 0x9815ac: LoadField: r1 = r3->field_b
    //     0x9815ac: ldur            w1, [x3, #0xb]
    // 0x9815b0: DecompressPointer r1
    //     0x9815b0: add             x1, x1, HEAP, lsl #32
    // 0x9815b4: cmp             w1, NULL
    // 0x9815b8: b.eq            #0x98168c
    // 0x9815bc: mov             x1, x3
    // 0x9815c0: mov             x2, x0
    // 0x9815c4: r0 = _firstDayOfMonth()
    //     0x9815c4: bl              #0x9819ac  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_firstDayOfMonth
    // 0x9815c8: ldur            x1, [fp, #-8]
    // 0x9815cc: mov             x2, x0
    // 0x9815d0: stur            x0, [fp, #-0x18]
    // 0x9815d4: r0 = _getDaysBefore()
    //     0x9815d4: bl              #0x9818f4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getDaysBefore
    // 0x9815d8: r16 = 86400000000
    //     0x9815d8: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x9815dc: ldr             x16, [x16, #0x268]
    // 0x9815e0: mul             x1, x0, x16
    // 0x9815e4: stur            x1, [fp, #-0x20]
    // 0x9815e8: r0 = Duration()
    //     0x9815e8: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x9815ec: mov             x1, x0
    // 0x9815f0: ldur            x0, [fp, #-0x20]
    // 0x9815f4: StoreField: r1->field_7 = r0
    //     0x9815f4: stur            x0, [x1, #7]
    // 0x9815f8: mov             x2, x1
    // 0x9815fc: ldur            x1, [fp, #-0x18]
    // 0x981600: r0 = subtract()
    //     0x981600: bl              #0x819a38  ; [dart:core] DateTime::subtract
    // 0x981604: ldur            x1, [fp, #-8]
    // 0x981608: ldur            x2, [fp, #-0x10]
    // 0x98160c: stur            x0, [fp, #-0x10]
    // 0x981610: r0 = _lastDayOfMonth()
    //     0x981610: bl              #0x9817dc  ; [package:table_calendar/src/table_calendar.dart] _TableCalendarState::_lastDayOfMonth
    // 0x981614: ldur            x1, [fp, #-8]
    // 0x981618: mov             x2, x0
    // 0x98161c: stur            x0, [fp, #-8]
    // 0x981620: r0 = _getDaysAfter()
    //     0x981620: bl              #0x981690  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getDaysAfter
    // 0x981624: r16 = 86400000000
    //     0x981624: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x981628: ldr             x16, [x16, #0x268]
    // 0x98162c: mul             x1, x0, x16
    // 0x981630: stur            x1, [fp, #-0x20]
    // 0x981634: r0 = Duration()
    //     0x981634: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x981638: mov             x1, x0
    // 0x98163c: ldur            x0, [fp, #-0x20]
    // 0x981640: StoreField: r1->field_7 = r0
    //     0x981640: stur            x0, [x1, #7]
    // 0x981644: mov             x2, x1
    // 0x981648: ldur            x1, [fp, #-8]
    // 0x98164c: r0 = add()
    //     0x98164c: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0x981650: mov             x1, x0
    // 0x981654: ldur            x2, [fp, #-0x10]
    // 0x981658: r0 = difference()
    //     0x981658: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0x98165c: LoadField: r1 = r0->field_7
    //     0x98165c: ldur            x1, [x0, #7]
    // 0x981660: r2 = 86400000000
    //     0x981660: add             x2, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0x981664: ldr             x2, [x2, #0x268]
    // 0x981668: sdiv            x3, x1, x2
    // 0x98166c: add             x1, x3, #1
    // 0x981670: r2 = 7
    //     0x981670: movz            x2, #0x7
    // 0x981674: sdiv            x0, x1, x2
    // 0x981678: LeaveFrame
    //     0x981678: mov             SP, fp
    //     0x98167c: ldp             fp, lr, [SP], #0x10
    // 0x981680: ret
    //     0x981680: ret             
    // 0x981684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981688: b               #0x9815ac
    // 0x98168c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98168c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getDaysAfter(/* No info */) {
    // ** addr: 0x981690, size: 0xd8
    // 0x981690: EnterFrame
    //     0x981690: stp             fp, lr, [SP, #-0x10]!
    //     0x981694: mov             fp, SP
    // 0x981698: AllocStack(0x10)
    //     0x981698: sub             SP, SP, #0x10
    // 0x98169c: SetupParameters(_TableCalendarBaseState this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x98169c: mov             x0, x1
    //     0x9816a0: mov             x1, x2
    //     0x9816a4: stur            x2, [fp, #-8]
    // 0x9816a8: CheckStackOverflow
    //     0x9816a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9816ac: cmp             SP, x16
    //     0x9816b0: b.ls            #0x981750
    // 0x9816b4: LoadField: r2 = r0->field_b
    //     0x9816b4: ldur            w2, [x0, #0xb]
    // 0x9816b8: DecompressPointer r2
    //     0x9816b8: add             x2, x2, HEAP, lsl #32
    // 0x9816bc: cmp             w2, NULL
    // 0x9816c0: b.eq            #0x981758
    // 0x9816c4: r0 = getWeekdayNumber()
    //     0x9816c4: bl              #0x981768  ; [package:table_calendar/src/shared/utils.dart] ::getWeekdayNumber
    // 0x9816c8: mov             x1, x0
    // 0x9816cc: r0 = 8
    //     0x9816cc: movz            x0, #0x8
    // 0x9816d0: sub             x2, x0, x1
    // 0x9816d4: ldur            x1, [fp, #-8]
    // 0x9816d8: stur            x2, [fp, #-0x10]
    // 0x9816dc: r0 = _parts()
    //     0x9816dc: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x9816e0: mov             x2, x0
    // 0x9816e4: LoadField: r3 = r2->field_b
    //     0x9816e4: ldur            w3, [x2, #0xb]
    // 0x9816e8: r0 = LoadInt32Instr(r3)
    //     0x9816e8: sbfx            x0, x3, #1, #0x1f
    // 0x9816ec: r1 = 6
    //     0x9816ec: movz            x1, #0x6
    // 0x9816f0: cmp             x1, x0
    // 0x9816f4: b.hs            #0x98175c
    // 0x9816f8: LoadField: r1 = r2->field_27
    //     0x9816f8: ldur            w1, [x2, #0x27]
    // 0x9816fc: DecompressPointer r1
    //     0x9816fc: add             x1, x1, HEAP, lsl #32
    // 0x981700: r2 = LoadInt32Instr(r1)
    //     0x981700: sbfx            x2, x1, #1, #0x1f
    //     0x981704: tbz             w1, #0, #0x98170c
    //     0x981708: ldur            x2, [x1, #7]
    // 0x98170c: ldur            x1, [fp, #-0x10]
    // 0x981710: add             x3, x2, x1
    // 0x981714: r1 = 7
    //     0x981714: movz            x1, #0x7
    // 0x981718: sdiv            x4, x3, x1
    // 0x98171c: msub            x2, x4, x1, x3
    // 0x981720: cmp             x2, xzr
    // 0x981724: b.lt            #0x981760
    // 0x981728: sub             x0, x1, x2
    // 0x98172c: cmp             x0, #7
    // 0x981730: b.ne            #0x981744
    // 0x981734: r0 = 0
    //     0x981734: movz            x0, #0
    // 0x981738: LeaveFrame
    //     0x981738: mov             SP, fp
    //     0x98173c: ldp             fp, lr, [SP], #0x10
    // 0x981740: ret
    //     0x981740: ret             
    // 0x981744: LeaveFrame
    //     0x981744: mov             SP, fp
    //     0x981748: ldp             fp, lr, [SP], #0x10
    // 0x98174c: ret
    //     0x98174c: ret             
    // 0x981750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981754: b               #0x9816b4
    // 0x981758: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x981758: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98175c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x98175c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x981760: add             x2, x2, x1
    // 0x981764: b               #0x981728
  }
  _ _getDaysBefore(/* No info */) {
    // ** addr: 0x9818f4, size: 0xb8
    // 0x9818f4: EnterFrame
    //     0x9818f4: stp             fp, lr, [SP, #-0x10]!
    //     0x9818f8: mov             fp, SP
    // 0x9818fc: AllocStack(0x10)
    //     0x9818fc: sub             SP, SP, #0x10
    // 0x981900: SetupParameters(_TableCalendarBaseState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x981900: mov             x0, x1
    //     0x981904: stur            x1, [fp, #-8]
    //     0x981908: mov             x1, x2
    // 0x98190c: CheckStackOverflow
    //     0x98190c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981910: cmp             SP, x16
    //     0x981914: b.ls            #0x981994
    // 0x981918: r0 = _parts()
    //     0x981918: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0x98191c: mov             x2, x0
    // 0x981920: LoadField: r0 = r2->field_b
    //     0x981920: ldur            w0, [x2, #0xb]
    // 0x981924: r1 = LoadInt32Instr(r0)
    //     0x981924: sbfx            x1, x0, #1, #0x1f
    // 0x981928: mov             x0, x1
    // 0x98192c: r1 = 6
    //     0x98192c: movz            x1, #0x6
    // 0x981930: cmp             x1, x0
    // 0x981934: b.hs            #0x98199c
    // 0x981938: LoadField: r0 = r2->field_27
    //     0x981938: ldur            w0, [x2, #0x27]
    // 0x98193c: DecompressPointer r0
    //     0x98193c: add             x0, x0, HEAP, lsl #32
    // 0x981940: r1 = LoadInt32Instr(r0)
    //     0x981940: sbfx            x1, x0, #1, #0x1f
    //     0x981944: tbz             w0, #0, #0x98194c
    //     0x981948: ldur            x1, [x0, #7]
    // 0x98194c: add             x0, x1, #7
    // 0x981950: ldur            x1, [fp, #-8]
    // 0x981954: stur            x0, [fp, #-0x10]
    // 0x981958: LoadField: r2 = r1->field_b
    //     0x981958: ldur            w2, [x1, #0xb]
    // 0x98195c: DecompressPointer r2
    //     0x98195c: add             x2, x2, HEAP, lsl #32
    // 0x981960: cmp             w2, NULL
    // 0x981964: b.eq            #0x9819a0
    // 0x981968: r0 = getWeekdayNumber()
    //     0x981968: bl              #0x981768  ; [package:table_calendar/src/shared/utils.dart] ::getWeekdayNumber
    // 0x98196c: ldur            x1, [fp, #-0x10]
    // 0x981970: sub             x2, x1, x0
    // 0x981974: r1 = 7
    //     0x981974: movz            x1, #0x7
    // 0x981978: sdiv            x3, x2, x1
    // 0x98197c: msub            x0, x3, x1, x2
    // 0x981980: cmp             x0, xzr
    // 0x981984: b.lt            #0x9819a4
    // 0x981988: LeaveFrame
    //     0x981988: mov             SP, fp
    //     0x98198c: ldp             fp, lr, [SP], #0x10
    // 0x981990: ret
    //     0x981990: ret             
    // 0x981994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981994: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981998: b               #0x981918
    // 0x98199c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x98199c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9819a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9819a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9819a4: add             x0, x0, x1
    // 0x9819a8: b               #0x981988
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a22a4, size: 0x27c
    // 0x9a22a4: EnterFrame
    //     0x9a22a4: stp             fp, lr, [SP, #-0x10]!
    //     0x9a22a8: mov             fp, SP
    // 0x9a22ac: AllocStack(0x20)
    //     0x9a22ac: sub             SP, SP, #0x20
    // 0x9a22b0: SetupParameters(_TableCalendarBaseState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9a22b0: mov             x4, x1
    //     0x9a22b4: mov             x3, x2
    //     0x9a22b8: stur            x1, [fp, #-8]
    //     0x9a22bc: stur            x2, [fp, #-0x10]
    // 0x9a22c0: CheckStackOverflow
    //     0x9a22c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a22c4: cmp             SP, x16
    //     0x9a22c8: b.ls            #0x9a24d8
    // 0x9a22cc: mov             x0, x3
    // 0x9a22d0: r2 = Null
    //     0x9a22d0: mov             x2, NULL
    // 0x9a22d4: r1 = Null
    //     0x9a22d4: mov             x1, NULL
    // 0x9a22d8: r4 = 60
    //     0x9a22d8: movz            x4, #0x3c
    // 0x9a22dc: branchIfSmi(r0, 0x9a22e8)
    //     0x9a22dc: tbz             w0, #0, #0x9a22e8
    // 0x9a22e0: r4 = LoadClassIdInstr(r0)
    //     0x9a22e0: ldur            x4, [x0, #-1]
    //     0x9a22e4: ubfx            x4, x4, #0xc, #0x14
    // 0x9a22e8: r17 = 4686
    //     0x9a22e8: movz            x17, #0x124e
    // 0x9a22ec: cmp             x4, x17
    // 0x9a22f0: b.eq            #0x9a2308
    // 0x9a22f4: r8 = TableCalendarBase
    //     0x9a22f4: add             x8, PP, #0x57, lsl #12  ; [pp+0x57e70] Type: TableCalendarBase
    //     0x9a22f8: ldr             x8, [x8, #0xe70]
    // 0x9a22fc: r3 = Null
    //     0x9a22fc: add             x3, PP, #0x57, lsl #12  ; [pp+0x57e78] Null
    //     0x9a2300: ldr             x3, [x3, #0xe78]
    // 0x9a2304: r0 = TableCalendarBase()
    //     0x9a2304: bl              #0x9813c0  ; IsType_TableCalendarBase_Stub
    // 0x9a2308: ldur            x3, [fp, #-8]
    // 0x9a230c: LoadField: r2 = r3->field_7
    //     0x9a230c: ldur            w2, [x3, #7]
    // 0x9a2310: DecompressPointer r2
    //     0x9a2310: add             x2, x2, HEAP, lsl #32
    // 0x9a2314: ldur            x0, [fp, #-0x10]
    // 0x9a2318: r1 = Null
    //     0x9a2318: mov             x1, NULL
    // 0x9a231c: cmp             w2, NULL
    // 0x9a2320: b.eq            #0x9a2344
    // 0x9a2324: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a2324: ldur            w4, [x2, #0x17]
    // 0x9a2328: DecompressPointer r4
    //     0x9a2328: add             x4, x4, HEAP, lsl #32
    // 0x9a232c: r8 = X0 bound StatefulWidget
    //     0x9a232c: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a2330: ldr             x8, [x8, #0x7f8]
    // 0x9a2334: LoadField: r9 = r4->field_7
    //     0x9a2334: ldur            x9, [x4, #7]
    // 0x9a2338: r3 = Null
    //     0x9a2338: add             x3, PP, #0x57, lsl #12  ; [pp+0x57e88] Null
    //     0x9a233c: ldr             x3, [x3, #0xe88]
    // 0x9a2340: blr             x9
    // 0x9a2344: ldur            x1, [fp, #-8]
    // 0x9a2348: LoadField: r0 = r1->field_1b
    //     0x9a2348: ldur            w0, [x1, #0x1b]
    // 0x9a234c: DecompressPointer r0
    //     0x9a234c: add             x0, x0, HEAP, lsl #32
    // 0x9a2350: r16 = Sentinel
    //     0x9a2350: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a2354: cmp             w0, w16
    // 0x9a2358: b.eq            #0x9a24e0
    // 0x9a235c: LoadField: r2 = r1->field_b
    //     0x9a235c: ldur            w2, [x1, #0xb]
    // 0x9a2360: DecompressPointer r2
    //     0x9a2360: add             x2, x2, HEAP, lsl #32
    // 0x9a2364: cmp             w2, NULL
    // 0x9a2368: b.eq            #0x9a24ec
    // 0x9a236c: LoadField: r3 = r2->field_13
    //     0x9a236c: ldur            w3, [x2, #0x13]
    // 0x9a2370: DecompressPointer r3
    //     0x9a2370: add             x3, x3, HEAP, lsl #32
    // 0x9a2374: r2 = LoadClassIdInstr(r0)
    //     0x9a2374: ldur            x2, [x0, #-1]
    //     0x9a2378: ubfx            x2, x2, #0xc, #0x14
    // 0x9a237c: stp             x3, x0, [SP]
    // 0x9a2380: mov             x0, x2
    // 0x9a2384: mov             lr, x0
    // 0x9a2388: ldr             lr, [x21, lr, lsl #3]
    // 0x9a238c: blr             lr
    // 0x9a2390: tbz             w0, #4, #0x9a2424
    // 0x9a2394: ldur            x1, [fp, #-8]
    // 0x9a2398: LoadField: r0 = r1->field_1b
    //     0x9a2398: ldur            w0, [x1, #0x1b]
    // 0x9a239c: DecompressPointer r0
    //     0x9a239c: add             x0, x0, HEAP, lsl #32
    // 0x9a23a0: LoadField: r2 = r1->field_b
    //     0x9a23a0: ldur            w2, [x1, #0xb]
    // 0x9a23a4: DecompressPointer r2
    //     0x9a23a4: add             x2, x2, HEAP, lsl #32
    // 0x9a23a8: cmp             w2, NULL
    // 0x9a23ac: b.eq            #0x9a24f0
    // 0x9a23b0: LoadField: r3 = r2->field_13
    //     0x9a23b0: ldur            w3, [x2, #0x13]
    // 0x9a23b4: DecompressPointer r3
    //     0x9a23b4: add             x3, x3, HEAP, lsl #32
    // 0x9a23b8: r2 = LoadClassIdInstr(r0)
    //     0x9a23b8: ldur            x2, [x0, #-1]
    //     0x9a23bc: ubfx            x2, x2, #0xc, #0x14
    // 0x9a23c0: stp             x3, x0, [SP]
    // 0x9a23c4: mov             x0, x2
    // 0x9a23c8: mov             lr, x0
    // 0x9a23cc: ldr             lr, [x21, lr, lsl #3]
    // 0x9a23d0: blr             lr
    // 0x9a23d4: eor             x2, x0, #0x10
    // 0x9a23d8: ldur            x3, [fp, #-8]
    // 0x9a23dc: LoadField: r0 = r3->field_b
    //     0x9a23dc: ldur            w0, [x3, #0xb]
    // 0x9a23e0: DecompressPointer r0
    //     0x9a23e0: add             x0, x0, HEAP, lsl #32
    // 0x9a23e4: cmp             w0, NULL
    // 0x9a23e8: b.eq            #0x9a24f4
    // 0x9a23ec: LoadField: r1 = r0->field_13
    //     0x9a23ec: ldur            w1, [x0, #0x13]
    // 0x9a23f0: DecompressPointer r1
    //     0x9a23f0: add             x1, x1, HEAP, lsl #32
    // 0x9a23f4: mov             x0, x1
    // 0x9a23f8: StoreField: r3->field_1b = r0
    //     0x9a23f8: stur            w0, [x3, #0x1b]
    //     0x9a23fc: ldurb           w16, [x3, #-1]
    //     0x9a2400: ldurb           w17, [x0, #-1]
    //     0x9a2404: and             x16, x17, x16, lsr #2
    //     0x9a2408: tst             x16, HEAP, lsr #32
    //     0x9a240c: b.eq            #0x9a2414
    //     0x9a2410: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9a2414: mov             x1, x3
    // 0x9a2418: r0 = _updatePage()
    //     0x9a2418: bl              #0x9a2520  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_updatePage
    // 0x9a241c: ldur            x0, [fp, #-8]
    // 0x9a2420: b               #0x9a2438
    // 0x9a2424: ldur            x0, [fp, #-8]
    // 0x9a2428: LoadField: r1 = r0->field_b
    //     0x9a2428: ldur            w1, [x0, #0xb]
    // 0x9a242c: DecompressPointer r1
    //     0x9a242c: add             x1, x1, HEAP, lsl #32
    // 0x9a2430: cmp             w1, NULL
    // 0x9a2434: b.eq            #0x9a24f8
    // 0x9a2438: d0 = 64.000000
    //     0x9a2438: add             x17, PP, #0x25, lsl #12  ; [pp+0x25238] IMM: double(64) from 0x4050000000000000
    //     0x9a243c: ldr             d0, [x17, #0x238]
    // 0x9a2440: LoadField: r1 = r0->field_b
    //     0x9a2440: ldur            w1, [x0, #0xb]
    // 0x9a2444: DecompressPointer r1
    //     0x9a2444: add             x1, x1, HEAP, lsl #32
    // 0x9a2448: cmp             w1, NULL
    // 0x9a244c: b.eq            #0x9a24fc
    // 0x9a2450: fcmp            d0, d0
    // 0x9a2454: b.ne            #0x9a2464
    // 0x9a2458: d0 = 16.000000
    //     0x9a2458: fmov            d0, #16.00000000
    // 0x9a245c: fcmp            d0, d0
    // 0x9a2460: b.eq            #0x9a24c8
    // 0x9a2464: LoadField: r2 = r0->field_1b
    //     0x9a2464: ldur            w2, [x0, #0x1b]
    // 0x9a2468: DecompressPointer r2
    //     0x9a2468: add             x2, x2, HEAP, lsl #32
    // 0x9a246c: mov             x1, x0
    // 0x9a2470: r0 = _getRowCount()
    //     0x9a2470: bl              #0x981584  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getRowCount
    // 0x9a2474: ldur            x1, [fp, #-8]
    // 0x9a2478: LoadField: r3 = r1->field_13
    //     0x9a2478: ldur            w3, [x1, #0x13]
    // 0x9a247c: DecompressPointer r3
    //     0x9a247c: add             x3, x3, HEAP, lsl #32
    // 0x9a2480: r16 = Sentinel
    //     0x9a2480: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a2484: cmp             w3, w16
    // 0x9a2488: b.eq            #0x9a2500
    // 0x9a248c: mov             x2, x0
    // 0x9a2490: stur            x3, [fp, #-0x10]
    // 0x9a2494: r0 = _getPageHeight()
    //     0x9a2494: bl              #0x9814f4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getPageHeight
    // 0x9a2498: r2 = inline_Allocate_Double()
    //     0x9a2498: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x9a249c: add             x2, x2, #0x10
    //     0x9a24a0: cmp             x0, x2
    //     0x9a24a4: b.ls            #0x9a250c
    //     0x9a24a8: str             x2, [THR, #0x50]  ; THR::top
    //     0x9a24ac: sub             x2, x2, #0xf
    //     0x9a24b0: movz            x0, #0xe15c
    //     0x9a24b4: movk            x0, #0x3, lsl #16
    //     0x9a24b8: stur            x0, [x2, #-1]
    // 0x9a24bc: StoreField: r2->field_7 = d0
    //     0x9a24bc: stur            d0, [x2, #7]
    // 0x9a24c0: ldur            x1, [fp, #-0x10]
    // 0x9a24c4: r0 = value=()
    //     0x9a24c4: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9a24c8: r0 = Null
    //     0x9a24c8: mov             x0, NULL
    // 0x9a24cc: LeaveFrame
    //     0x9a24cc: mov             SP, fp
    //     0x9a24d0: ldp             fp, lr, [SP], #0x10
    // 0x9a24d4: ret
    //     0x9a24d4: ret             
    // 0x9a24d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a24d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a24dc: b               #0x9a22cc
    // 0x9a24e0: r9 = _focusedDay
    //     0x9a24e0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e58] Field <_TableCalendarBaseState@1929380592._focusedDay@1929380592>: late (offset: 0x1c)
    //     0x9a24e4: ldr             x9, [x9, #0xe58]
    // 0x9a24e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a24e8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a24ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a24ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a24f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a24f0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a24f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a24f4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a24f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a24f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a24fc: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9a24fc: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x9a2500: r9 = _pageHeight
    //     0x9a2500: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e40] Field <_TableCalendarBaseState@1929380592._pageHeight@1929380592>: late final (offset: 0x14)
    //     0x9a2504: ldr             x9, [x9, #0xe40]
    // 0x9a2508: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a2508: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a250c: SaveReg d0
    //     0x9a250c: str             q0, [SP, #-0x10]!
    // 0x9a2510: r0 = AllocateDouble()
    //     0x9a2510: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9a2514: mov             x2, x0
    // 0x9a2518: RestoreReg d0
    //     0x9a2518: ldr             q0, [SP], #0x10
    // 0x9a251c: b               #0x9a24bc
  }
  _ _updatePage(/* No info */) {
    // ** addr: 0x9a2520, size: 0x2f0
    // 0x9a2520: EnterFrame
    //     0x9a2520: stp             fp, lr, [SP, #-0x10]!
    //     0x9a2524: mov             fp, SP
    // 0x9a2528: AllocStack(0x18)
    //     0x9a2528: sub             SP, SP, #0x18
    // 0x9a252c: SetupParameters(_TableCalendarBaseState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9a252c: mov             x4, x1
    //     0x9a2530: mov             x0, x2
    //     0x9a2534: stur            x1, [fp, #-8]
    //     0x9a2538: stur            x2, [fp, #-0x10]
    // 0x9a253c: CheckStackOverflow
    //     0x9a253c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a2540: cmp             SP, x16
    //     0x9a2544: b.ls            #0x9a2798
    // 0x9a2548: LoadField: r1 = r4->field_b
    //     0x9a2548: ldur            w1, [x4, #0xb]
    // 0x9a254c: DecompressPointer r1
    //     0x9a254c: add             x1, x1, HEAP, lsl #32
    // 0x9a2550: cmp             w1, NULL
    // 0x9a2554: b.eq            #0x9a27a0
    // 0x9a2558: LoadField: r2 = r1->field_b
    //     0x9a2558: ldur            w2, [x1, #0xb]
    // 0x9a255c: DecompressPointer r2
    //     0x9a255c: add             x2, x2, HEAP, lsl #32
    // 0x9a2560: LoadField: r3 = r4->field_1b
    //     0x9a2560: ldur            w3, [x4, #0x1b]
    // 0x9a2564: DecompressPointer r3
    //     0x9a2564: add             x3, x3, HEAP, lsl #32
    // 0x9a2568: r16 = Sentinel
    //     0x9a2568: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a256c: cmp             w3, w16
    // 0x9a2570: b.eq            #0x9a27a4
    // 0x9a2574: mov             x1, x4
    // 0x9a2578: r0 = _getMonthCount()
    //     0x9a2578: bl              #0x9813e4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getMonthCount
    // 0x9a257c: mov             x4, x0
    // 0x9a2580: ldur            x0, [fp, #-8]
    // 0x9a2584: stur            x4, [fp, #-0x18]
    // 0x9a2588: LoadField: r1 = r0->field_b
    //     0x9a2588: ldur            w1, [x0, #0xb]
    // 0x9a258c: DecompressPointer r1
    //     0x9a258c: add             x1, x1, HEAP, lsl #32
    // 0x9a2590: cmp             w1, NULL
    // 0x9a2594: b.eq            #0x9a27b0
    // 0x9a2598: LoadField: r2 = r1->field_b
    //     0x9a2598: ldur            w2, [x1, #0xb]
    // 0x9a259c: DecompressPointer r2
    //     0x9a259c: add             x2, x2, HEAP, lsl #32
    // 0x9a25a0: LoadField: r3 = r1->field_f
    //     0x9a25a0: ldur            w3, [x1, #0xf]
    // 0x9a25a4: DecompressPointer r3
    //     0x9a25a4: add             x3, x3, HEAP, lsl #32
    // 0x9a25a8: mov             x1, x0
    // 0x9a25ac: r0 = _getMonthCount()
    //     0x9a25ac: bl              #0x9813e4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getMonthCount
    // 0x9a25b0: mov             x1, x0
    // 0x9a25b4: ldur            x0, [fp, #-8]
    // 0x9a25b8: LoadField: r2 = r0->field_1f
    //     0x9a25b8: ldur            w2, [x0, #0x1f]
    // 0x9a25bc: DecompressPointer r2
    //     0x9a25bc: add             x2, x2, HEAP, lsl #32
    // 0x9a25c0: r16 = Sentinel
    //     0x9a25c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a25c4: cmp             w2, w16
    // 0x9a25c8: b.eq            #0x9a27b4
    // 0x9a25cc: r3 = LoadInt32Instr(r2)
    //     0x9a25cc: sbfx            x3, x2, #1, #0x1f
    //     0x9a25d0: tbz             w2, #0, #0x9a25d8
    //     0x9a25d4: ldur            x3, [x2, #7]
    // 0x9a25d8: ldur            x4, [fp, #-0x18]
    // 0x9a25dc: cmp             x4, x3
    // 0x9a25e0: b.ne            #0x9a25f0
    // 0x9a25e4: cbz             x4, #0x9a25f0
    // 0x9a25e8: cmp             x4, x1
    // 0x9a25ec: b.ne            #0x9a25f8
    // 0x9a25f0: r1 = true
    //     0x9a25f0: add             x1, NULL, #0x20  ; true
    // 0x9a25f4: StoreField: r0->field_23 = r1
    //     0x9a25f4: stur            w1, [x0, #0x23]
    // 0x9a25f8: ldur            x1, [fp, #-0x10]
    // 0x9a25fc: tbnz            w1, #4, #0x9a26a8
    // 0x9a2600: LoadField: r1 = r0->field_b
    //     0x9a2600: ldur            w1, [x0, #0xb]
    // 0x9a2604: DecompressPointer r1
    //     0x9a2604: add             x1, x1, HEAP, lsl #32
    // 0x9a2608: cmp             w1, NULL
    // 0x9a260c: b.eq            #0x9a27c0
    // 0x9a2610: sub             x1, x4, x3
    // 0x9a2614: tbz             x1, #0x3f, #0x9a2628
    // 0x9a2618: neg             x2, x1
    // 0x9a261c: cmp             x2, #1
    // 0x9a2620: b.le            #0x9a2664
    // 0x9a2624: b               #0x9a2630
    // 0x9a2628: cmp             x1, #1
    // 0x9a262c: b.le            #0x9a2664
    // 0x9a2630: cmp             x4, x3
    // 0x9a2634: b.le            #0x9a2644
    // 0x9a2638: sub             x1, x4, #1
    // 0x9a263c: mov             x2, x1
    // 0x9a2640: b               #0x9a264c
    // 0x9a2644: add             x1, x4, #1
    // 0x9a2648: mov             x2, x1
    // 0x9a264c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9a264c: ldur            w1, [x0, #0x17]
    // 0x9a2650: DecompressPointer r1
    //     0x9a2650: add             x1, x1, HEAP, lsl #32
    // 0x9a2654: r16 = Sentinel
    //     0x9a2654: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a2658: cmp             w1, w16
    // 0x9a265c: b.eq            #0x9a27c4
    // 0x9a2660: r0 = jumpToPage()
    //     0x9a2660: bl              #0x8e9ab4  ; [package:flutter/src/widgets/page_view.dart] PageController::jumpToPage
    // 0x9a2664: ldur            x0, [fp, #-8]
    // 0x9a2668: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9a2668: ldur            w1, [x0, #0x17]
    // 0x9a266c: DecompressPointer r1
    //     0x9a266c: add             x1, x1, HEAP, lsl #32
    // 0x9a2670: r16 = Sentinel
    //     0x9a2670: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a2674: cmp             w1, w16
    // 0x9a2678: b.eq            #0x9a27d0
    // 0x9a267c: LoadField: r2 = r0->field_b
    //     0x9a267c: ldur            w2, [x0, #0xb]
    // 0x9a2680: DecompressPointer r2
    //     0x9a2680: add             x2, x2, HEAP, lsl #32
    // 0x9a2684: cmp             w2, NULL
    // 0x9a2688: b.eq            #0x9a27dc
    // 0x9a268c: ldur            x2, [fp, #-0x18]
    // 0x9a2690: r3 = Instance_Cubic
    //     0x9a2690: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0x9a2694: ldr             x3, [x3, #0xb28]
    // 0x9a2698: r5 = Instance_Duration
    //     0x9a2698: add             x5, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0x9a269c: ldr             x5, [x5, #0x9c0]
    // 0x9a26a0: r0 = animateToPage()
    //     0x9a26a0: bl              #0x8f11bc  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0x9a26a4: b               #0x9a26c4
    // 0x9a26a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9a26a8: ldur            w1, [x0, #0x17]
    // 0x9a26ac: DecompressPointer r1
    //     0x9a26ac: add             x1, x1, HEAP, lsl #32
    // 0x9a26b0: r16 = Sentinel
    //     0x9a26b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a26b4: cmp             w1, w16
    // 0x9a26b8: b.eq            #0x9a27e0
    // 0x9a26bc: ldur            x2, [fp, #-0x18]
    // 0x9a26c0: r0 = jumpToPage()
    //     0x9a26c0: bl              #0x8e9ab4  ; [package:flutter/src/widgets/page_view.dart] PageController::jumpToPage
    // 0x9a26c4: ldur            x3, [fp, #-8]
    // 0x9a26c8: ldur            x2, [fp, #-0x18]
    // 0x9a26cc: r0 = BoxInt64Instr(r2)
    //     0x9a26cc: sbfiz           x0, x2, #1, #0x1f
    //     0x9a26d0: cmp             x2, x0, asr #1
    //     0x9a26d4: b.eq            #0x9a26e0
    //     0x9a26d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9a26dc: stur            x2, [x0, #7]
    // 0x9a26e0: StoreField: r3->field_1f = r0
    //     0x9a26e0: stur            w0, [x3, #0x1f]
    //     0x9a26e4: tbz             w0, #0, #0x9a2700
    //     0x9a26e8: ldurb           w16, [x3, #-1]
    //     0x9a26ec: ldurb           w17, [x0, #-1]
    //     0x9a26f0: and             x16, x17, x16, lsr #2
    //     0x9a26f4: tst             x16, HEAP, lsr #32
    //     0x9a26f8: b.eq            #0x9a2700
    //     0x9a26fc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x9a2700: LoadField: r0 = r3->field_b
    //     0x9a2700: ldur            w0, [x3, #0xb]
    // 0x9a2704: DecompressPointer r0
    //     0x9a2704: add             x0, x0, HEAP, lsl #32
    // 0x9a2708: cmp             w0, NULL
    // 0x9a270c: b.eq            #0x9a27ec
    // 0x9a2710: LoadField: r2 = r3->field_1b
    //     0x9a2710: ldur            w2, [x3, #0x1b]
    // 0x9a2714: DecompressPointer r2
    //     0x9a2714: add             x2, x2, HEAP, lsl #32
    // 0x9a2718: mov             x1, x3
    // 0x9a271c: r0 = _getRowCount()
    //     0x9a271c: bl              #0x981584  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getRowCount
    // 0x9a2720: mov             x1, x0
    // 0x9a2724: ldur            x0, [fp, #-8]
    // 0x9a2728: LoadField: r3 = r0->field_13
    //     0x9a2728: ldur            w3, [x0, #0x13]
    // 0x9a272c: DecompressPointer r3
    //     0x9a272c: add             x3, x3, HEAP, lsl #32
    // 0x9a2730: r16 = Sentinel
    //     0x9a2730: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9a2734: cmp             w3, w16
    // 0x9a2738: b.eq            #0x9a27f0
    // 0x9a273c: mov             x2, x1
    // 0x9a2740: mov             x1, x0
    // 0x9a2744: stur            x3, [fp, #-0x10]
    // 0x9a2748: r0 = _getPageHeight()
    //     0x9a2748: bl              #0x9814f4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getPageHeight
    // 0x9a274c: r2 = inline_Allocate_Double()
    //     0x9a274c: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0x9a2750: add             x2, x2, #0x10
    //     0x9a2754: cmp             x0, x2
    //     0x9a2758: b.ls            #0x9a27fc
    //     0x9a275c: str             x2, [THR, #0x50]  ; THR::top
    //     0x9a2760: sub             x2, x2, #0xf
    //     0x9a2764: movz            x0, #0xe15c
    //     0x9a2768: movk            x0, #0x3, lsl #16
    //     0x9a276c: stur            x0, [x2, #-1]
    // 0x9a2770: StoreField: r2->field_7 = d0
    //     0x9a2770: stur            d0, [x2, #7]
    // 0x9a2774: ldur            x1, [fp, #-0x10]
    // 0x9a2778: r0 = value=()
    //     0x9a2778: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x9a277c: ldur            x1, [fp, #-8]
    // 0x9a2780: r2 = false
    //     0x9a2780: add             x2, NULL, #0x30  ; false
    // 0x9a2784: StoreField: r1->field_23 = r2
    //     0x9a2784: stur            w2, [x1, #0x23]
    // 0x9a2788: r0 = Null
    //     0x9a2788: mov             x0, NULL
    // 0x9a278c: LeaveFrame
    //     0x9a278c: mov             SP, fp
    //     0x9a2790: ldp             fp, lr, [SP], #0x10
    // 0x9a2794: ret
    //     0x9a2794: ret             
    // 0x9a2798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a2798: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a279c: b               #0x9a2548
    // 0x9a27a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a27a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a27a4: r9 = _focusedDay
    //     0x9a27a4: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e58] Field <_TableCalendarBaseState@1929380592._focusedDay@1929380592>: late (offset: 0x1c)
    //     0x9a27a8: ldr             x9, [x9, #0xe58]
    // 0x9a27ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a27ac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a27b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a27b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a27b4: r9 = _previousIndex
    //     0x9a27b4: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e50] Field <_TableCalendarBaseState@1929380592._previousIndex@1929380592>: late (offset: 0x20)
    //     0x9a27b8: ldr             x9, [x9, #0xe50]
    // 0x9a27bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a27bc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a27c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a27c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a27c4: r9 = _pageController
    //     0x9a27c4: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e48] Field <_TableCalendarBaseState@1929380592._pageController@1929380592>: late final (offset: 0x18)
    //     0x9a27c8: ldr             x9, [x9, #0xe48]
    // 0x9a27cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a27cc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a27d0: r9 = _pageController
    //     0x9a27d0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e48] Field <_TableCalendarBaseState@1929380592._pageController@1929380592>: late final (offset: 0x18)
    //     0x9a27d4: ldr             x9, [x9, #0xe48]
    // 0x9a27d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a27d8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a27dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a27dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a27e0: r9 = _pageController
    //     0x9a27e0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e48] Field <_TableCalendarBaseState@1929380592._pageController@1929380592>: late final (offset: 0x18)
    //     0x9a27e4: ldr             x9, [x9, #0xe48]
    // 0x9a27e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a27e8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a27ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a27ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9a27f0: r9 = _pageHeight
    //     0x9a27f0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e40] Field <_TableCalendarBaseState@1929380592._pageHeight@1929380592>: late final (offset: 0x14)
    //     0x9a27f4: ldr             x9, [x9, #0xe40]
    // 0x9a27f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9a27f8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9a27fc: SaveReg d0
    //     0x9a27fc: str             q0, [SP, #-0x10]!
    // 0x9a2800: r0 = AllocateDouble()
    //     0x9a2800: bl              #0xec2254  ; AllocateDoubleStub
    // 0x9a2804: mov             x2, x0
    // 0x9a2808: RestoreReg d0
    //     0x9a2808: ldr             q0, [SP], #0x10
    // 0x9a280c: b               #0x9a2770
  }
  _ build(/* No info */) {
    // ** addr: 0xa50d60, size: 0x58
    // 0xa50d60: EnterFrame
    //     0xa50d60: stp             fp, lr, [SP, #-0x10]!
    //     0xa50d64: mov             fp, SP
    // 0xa50d68: AllocStack(0x8)
    //     0xa50d68: sub             SP, SP, #8
    // 0xa50d6c: SetupParameters(_TableCalendarBaseState this /* r1 => r1, fp-0x8 */)
    //     0xa50d6c: stur            x1, [fp, #-8]
    // 0xa50d70: r1 = 1
    //     0xa50d70: movz            x1, #0x1
    // 0xa50d74: r0 = AllocateContext()
    //     0xa50d74: bl              #0xec126c  ; AllocateContextStub
    // 0xa50d78: mov             x1, x0
    // 0xa50d7c: ldur            x0, [fp, #-8]
    // 0xa50d80: StoreField: r1->field_f = r0
    //     0xa50d80: stur            w0, [x1, #0xf]
    // 0xa50d84: mov             x2, x1
    // 0xa50d88: r1 = Function '<anonymous closure>':.
    //     0xa50d88: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e28] AnonymousClosure: (0xa50db8), in [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::build (0xa50d60)
    //     0xa50d8c: ldr             x1, [x1, #0xe28]
    // 0xa50d90: r0 = AllocateClosure()
    //     0xa50d90: bl              #0xec1630  ; AllocateClosureStub
    // 0xa50d94: r1 = <BoxConstraints>
    //     0xa50d94: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xa50d98: ldr             x1, [x1, #0xfa8]
    // 0xa50d9c: stur            x0, [fp, #-8]
    // 0xa50da0: r0 = LayoutBuilder()
    //     0xa50da0: bl              #0x9f1460  ; AllocateLayoutBuilderStub -> LayoutBuilder (size=0x14)
    // 0xa50da4: ldur            x1, [fp, #-8]
    // 0xa50da8: StoreField: r0->field_f = r1
    //     0xa50da8: stur            w1, [x0, #0xf]
    // 0xa50dac: LeaveFrame
    //     0xa50dac: mov             SP, fp
    //     0xa50db0: ldp             fp, lr, [SP], #0x10
    // 0xa50db4: ret
    //     0xa50db4: ret             
  }
  [closure] SimpleGestureDetector <anonymous closure>(dynamic, BuildContext, BoxConstraints) {
    // ** addr: 0xa50db8, size: 0x328
    // 0xa50db8: EnterFrame
    //     0xa50db8: stp             fp, lr, [SP, #-0x10]!
    //     0xa50dbc: mov             fp, SP
    // 0xa50dc0: AllocStack(0x68)
    //     0xa50dc0: sub             SP, SP, #0x68
    // 0xa50dc4: SetupParameters()
    //     0xa50dc4: ldr             x0, [fp, #0x20]
    //     0xa50dc8: ldur            w1, [x0, #0x17]
    //     0xa50dcc: add             x1, x1, HEAP, lsl #32
    //     0xa50dd0: stur            x1, [fp, #-8]
    // 0xa50dd4: CheckStackOverflow
    //     0xa50dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa50dd8: cmp             SP, x16
    //     0xa50ddc: b.ls            #0xa5109c
    // 0xa50de0: r1 = 1
    //     0xa50de0: movz            x1, #0x1
    // 0xa50de4: r0 = AllocateContext()
    //     0xa50de4: bl              #0xec126c  ; AllocateContextStub
    // 0xa50de8: mov             x2, x0
    // 0xa50dec: ldur            x0, [fp, #-8]
    // 0xa50df0: stur            x2, [fp, #-0x10]
    // 0xa50df4: StoreField: r2->field_b = r0
    //     0xa50df4: stur            w0, [x2, #0xb]
    // 0xa50df8: ldr             x3, [fp, #0x10]
    // 0xa50dfc: StoreField: r2->field_f = r3
    //     0xa50dfc: stur            w3, [x2, #0xf]
    // 0xa50e00: LoadField: r1 = r0->field_f
    //     0xa50e00: ldur            w1, [x0, #0xf]
    // 0xa50e04: DecompressPointer r1
    //     0xa50e04: add             x1, x1, HEAP, lsl #32
    // 0xa50e08: r0 = _usesRouter()
    //     0xa50e08: bl              #0x9e43ec  ; [package:flutter/src/material/app.dart] _MaterialAppState::_usesRouter
    // 0xa50e0c: tbnz            w0, #4, #0xa50e3c
    // 0xa50e10: ldur            x0, [fp, #-8]
    // 0xa50e14: LoadField: r1 = r0->field_f
    //     0xa50e14: ldur            w1, [x0, #0xf]
    // 0xa50e18: DecompressPointer r1
    //     0xa50e18: add             x1, x1, HEAP, lsl #32
    // 0xa50e1c: LoadField: r2 = r1->field_b
    //     0xa50e1c: ldur            w2, [x1, #0xb]
    // 0xa50e20: DecompressPointer r2
    //     0xa50e20: add             x2, x2, HEAP, lsl #32
    // 0xa50e24: cmp             w2, NULL
    // 0xa50e28: b.eq            #0xa510a4
    // 0xa50e2c: LoadField: r1 = r2->field_73
    //     0xa50e2c: ldur            w1, [x2, #0x73]
    // 0xa50e30: DecompressPointer r1
    //     0xa50e30: add             x1, x1, HEAP, lsl #32
    // 0xa50e34: mov             x2, x1
    // 0xa50e38: b               #0xa50e44
    // 0xa50e3c: ldur            x0, [fp, #-8]
    // 0xa50e40: r2 = Null
    //     0xa50e40: mov             x2, NULL
    // 0xa50e44: stur            x2, [fp, #-0x28]
    // 0xa50e48: LoadField: r1 = r0->field_f
    //     0xa50e48: ldur            w1, [x0, #0xf]
    // 0xa50e4c: DecompressPointer r1
    //     0xa50e4c: add             x1, x1, HEAP, lsl #32
    // 0xa50e50: LoadField: r3 = r1->field_b
    //     0xa50e50: ldur            w3, [x1, #0xb]
    // 0xa50e54: DecompressPointer r3
    //     0xa50e54: add             x3, x3, HEAP, lsl #32
    // 0xa50e58: cmp             w3, NULL
    // 0xa50e5c: b.eq            #0xa510a8
    // 0xa50e60: LoadField: r3 = r1->field_13
    //     0xa50e60: ldur            w3, [x1, #0x13]
    // 0xa50e64: DecompressPointer r3
    //     0xa50e64: add             x3, x3, HEAP, lsl #32
    // 0xa50e68: r16 = Sentinel
    //     0xa50e68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa50e6c: cmp             w3, w16
    // 0xa50e70: b.eq            #0xa510ac
    // 0xa50e74: stur            x3, [fp, #-0x20]
    // 0xa50e78: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa50e78: ldur            w4, [x1, #0x17]
    // 0xa50e7c: DecompressPointer r4
    //     0xa50e7c: add             x4, x4, HEAP, lsl #32
    // 0xa50e80: r16 = Sentinel
    //     0xa50e80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa50e84: cmp             w4, w16
    // 0xa50e88: b.eq            #0xa510b8
    // 0xa50e8c: stur            x4, [fp, #-0x18]
    // 0xa50e90: r0 = _usesNavigator()
    //     0xa50e90: bl              #0xa10d90  ; [package:flutter/src/widgets/app.dart] _WidgetsAppState::_usesNavigator
    // 0xa50e94: tbnz            w0, #4, #0xa50ea4
    // 0xa50e98: r5 = Instance_PageScrollPhysics
    //     0xa50e98: add             x5, PP, #0x43, lsl #12  ; [pp+0x43c00] Obj!PageScrollPhysics@e0fd91
    //     0xa50e9c: ldr             x5, [x5, #0xc00]
    // 0xa50ea0: b               #0xa50eac
    // 0xa50ea4: r5 = Instance_NeverScrollableScrollPhysics
    //     0xa50ea4: add             x5, PP, #0x28, lsl #12  ; [pp+0x28290] Obj!NeverScrollableScrollPhysics@e0fd41
    //     0xa50ea8: ldr             x5, [x5, #0x290]
    // 0xa50eac: ldr             x4, [fp, #0x10]
    // 0xa50eb0: ldur            x0, [fp, #-8]
    // 0xa50eb4: ldur            x1, [fp, #-0x28]
    // 0xa50eb8: ldur            x2, [fp, #-0x20]
    // 0xa50ebc: ldur            x3, [fp, #-0x18]
    // 0xa50ec0: stur            x5, [fp, #-0x60]
    // 0xa50ec4: LoadField: r6 = r0->field_f
    //     0xa50ec4: ldur            w6, [x0, #0xf]
    // 0xa50ec8: DecompressPointer r6
    //     0xa50ec8: add             x6, x6, HEAP, lsl #32
    // 0xa50ecc: LoadField: r0 = r6->field_b
    //     0xa50ecc: ldur            w0, [x6, #0xb]
    // 0xa50ed0: DecompressPointer r0
    //     0xa50ed0: add             x0, x0, HEAP, lsl #32
    // 0xa50ed4: cmp             w0, NULL
    // 0xa50ed8: b.eq            #0xa510c4
    // 0xa50edc: LoadField: r7 = r0->field_b
    //     0xa50edc: ldur            w7, [x0, #0xb]
    // 0xa50ee0: DecompressPointer r7
    //     0xa50ee0: add             x7, x7, HEAP, lsl #32
    // 0xa50ee4: stur            x7, [fp, #-0x58]
    // 0xa50ee8: LoadField: r8 = r0->field_f
    //     0xa50ee8: ldur            w8, [x0, #0xf]
    // 0xa50eec: DecompressPointer r8
    //     0xa50eec: add             x8, x8, HEAP, lsl #32
    // 0xa50ef0: stur            x8, [fp, #-0x50]
    // 0xa50ef4: LoadField: r10 = r6->field_1f
    //     0xa50ef4: ldur            w10, [x6, #0x1f]
    // 0xa50ef8: DecompressPointer r10
    //     0xa50ef8: add             x10, x10, HEAP, lsl #32
    // 0xa50efc: r16 = Sentinel
    //     0xa50efc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa50f00: cmp             w10, w16
    // 0xa50f04: b.eq            #0xa510c8
    // 0xa50f08: stur            x10, [fp, #-0x48]
    // 0xa50f0c: LoadField: r11 = r6->field_1b
    //     0xa50f0c: ldur            w11, [x6, #0x1b]
    // 0xa50f10: DecompressPointer r11
    //     0xa50f10: add             x11, x11, HEAP, lsl #32
    // 0xa50f14: r16 = Sentinel
    //     0xa50f14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa50f18: cmp             w11, w16
    // 0xa50f1c: b.eq            #0xa510d4
    // 0xa50f20: stur            x11, [fp, #-0x40]
    // 0xa50f24: LoadField: r6 = r0->field_1f
    //     0xa50f24: ldur            w6, [x0, #0x1f]
    // 0xa50f28: DecompressPointer r6
    //     0xa50f28: add             x6, x6, HEAP, lsl #32
    // 0xa50f2c: stur            x6, [fp, #-0x38]
    // 0xa50f30: LoadField: r9 = r0->field_1b
    //     0xa50f30: ldur            w9, [x0, #0x1b]
    // 0xa50f34: DecompressPointer r9
    //     0xa50f34: add             x9, x9, HEAP, lsl #32
    // 0xa50f38: stur            x9, [fp, #-0x30]
    // 0xa50f3c: LoadField: r12 = r0->field_23
    //     0xa50f3c: ldur            w12, [x0, #0x23]
    // 0xa50f40: DecompressPointer r12
    //     0xa50f40: add             x12, x12, HEAP, lsl #32
    // 0xa50f44: stur            x12, [fp, #-8]
    // 0xa50f48: r0 = CalendarCore()
    //     0xa50f48: bl              #0xa510ec  ; AllocateCalendarCoreStub -> CalendarCore (size=0x70)
    // 0xa50f4c: mov             x3, x0
    // 0xa50f50: ldur            x0, [fp, #-0x30]
    // 0xa50f54: stur            x3, [fp, #-0x68]
    // 0xa50f58: StoreField: r3->field_1b = r0
    //     0xa50f58: stur            w0, [x3, #0x1b]
    // 0xa50f5c: ldur            x0, [fp, #-8]
    // 0xa50f60: StoreField: r3->field_23 = r0
    //     0xa50f60: stur            w0, [x3, #0x23]
    // 0xa50f64: ldur            x2, [fp, #-0x10]
    // 0xa50f68: r1 = Function '<anonymous closure>':.
    //     0xa50f68: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e30] AnonymousClosure: (0xa51208), in [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::build (0xa50d60)
    //     0xa50f6c: ldr             x1, [x1, #0xe30]
    // 0xa50f70: r0 = AllocateClosure()
    //     0xa50f70: bl              #0xec1630  ; AllocateClosureStub
    // 0xa50f74: mov             x1, x0
    // 0xa50f78: ldur            x0, [fp, #-0x68]
    // 0xa50f7c: StoreField: r0->field_6b = r1
    //     0xa50f7c: stur            w1, [x0, #0x6b]
    // 0xa50f80: ldur            x1, [fp, #-0x58]
    // 0xa50f84: StoreField: r0->field_f = r1
    //     0xa50f84: stur            w1, [x0, #0xf]
    // 0xa50f88: ldur            x1, [fp, #-0x50]
    // 0xa50f8c: StoreField: r0->field_13 = r1
    //     0xa50f8c: stur            w1, [x0, #0x13]
    // 0xa50f90: ldr             x1, [fp, #0x10]
    // 0xa50f94: StoreField: r0->field_53 = r1
    //     0xa50f94: stur            w1, [x0, #0x53]
    // 0xa50f98: d0 = 16.000000
    //     0xa50f98: fmov            d0, #16.00000000
    // 0xa50f9c: StoreField: r0->field_43 = d0
    //     0xa50f9c: stur            d0, [x0, #0x43]
    // 0xa50fa0: d0 = 64.000000
    //     0xa50fa0: add             x17, PP, #0x25, lsl #12  ; [pp+0x25238] IMM: double(64) from 0x4050000000000000
    //     0xa50fa4: ldr             d0, [x17, #0x238]
    // 0xa50fa8: StoreField: r0->field_4b = d0
    //     0xa50fa8: stur            d0, [x0, #0x4b]
    // 0xa50fac: r1 = Instance_StartingDayOfWeek
    //     0xa50fac: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ec8] Obj!StartingDayOfWeek@e2e021
    //     0xa50fb0: ldr             x1, [x1, #0xec8]
    // 0xa50fb4: StoreField: r0->field_5f = r1
    //     0xa50fb4: stur            w1, [x0, #0x5f]
    // 0xa50fb8: r1 = Instance_CalendarFormat
    //     0xa50fb8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35eb8] Obj!CalendarFormat@e2e041
    //     0xa50fbc: ldr             x1, [x1, #0xeb8]
    // 0xa50fc0: ArrayStore: r0[0] = r1  ; List_4
    //     0xa50fc0: stur            w1, [x0, #0x17]
    // 0xa50fc4: ldur            x1, [fp, #-0x18]
    // 0xa50fc8: StoreField: r0->field_63 = r1
    //     0xa50fc8: stur            w1, [x0, #0x63]
    // 0xa50fcc: ldur            x1, [fp, #-0x40]
    // 0xa50fd0: StoreField: r0->field_b = r1
    //     0xa50fd0: stur            w1, [x0, #0xb]
    // 0xa50fd4: ldur            x1, [fp, #-0x48]
    // 0xa50fd8: r2 = LoadInt32Instr(r1)
    //     0xa50fd8: sbfx            x2, x1, #1, #0x1f
    //     0xa50fdc: tbz             w1, #0, #0xa50fe4
    //     0xa50fe0: ldur            x2, [x1, #7]
    // 0xa50fe4: StoreField: r0->field_57 = r2
    //     0xa50fe4: stur            x2, [x0, #0x57]
    // 0xa50fe8: r1 = false
    //     0xa50fe8: add             x1, NULL, #0x30  ; false
    // 0xa50fec: StoreField: r0->field_27 = r1
    //     0xa50fec: stur            w1, [x0, #0x27]
    // 0xa50ff0: StoreField: r0->field_2b = r1
    //     0xa50ff0: stur            w1, [x0, #0x2b]
    // 0xa50ff4: ldur            x2, [fp, #-0x38]
    // 0xa50ff8: StoreField: r0->field_1f = r2
    //     0xa50ff8: stur            w2, [x0, #0x1f]
    // 0xa50ffc: StoreField: r0->field_2f = r1
    //     0xa50ffc: stur            w1, [x0, #0x2f]
    // 0xa51000: r1 = Instance_BoxDecoration
    //     0xa51000: add             x1, PP, #0x47, lsl #12  ; [pp+0x47e98] Obj!BoxDecoration@e1d1e1
    //     0xa51004: ldr             x1, [x1, #0xe98]
    // 0xa51008: StoreField: r0->field_33 = r1
    //     0xa51008: stur            w1, [x0, #0x33]
    // 0xa5100c: StoreField: r0->field_37 = r1
    //     0xa5100c: stur            w1, [x0, #0x37]
    // 0xa51010: r1 = Instance_TableBorder
    //     0xa51010: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ea0] Obj!TableBorder@e116b1
    //     0xa51014: ldr             x1, [x1, #0xea0]
    // 0xa51018: StoreField: r0->field_3b = r1
    //     0xa51018: stur            w1, [x0, #0x3b]
    // 0xa5101c: r1 = Instance_EdgeInsets
    //     0xa5101c: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa51020: StoreField: r0->field_3f = r1
    //     0xa51020: stur            w1, [x0, #0x3f]
    // 0xa51024: ldur            x1, [fp, #-0x60]
    // 0xa51028: StoreField: r0->field_67 = r1
    //     0xa51028: stur            w1, [x0, #0x67]
    // 0xa5102c: r1 = <double>
    //     0xa5102c: ldr             x1, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    // 0xa51030: r0 = ValueListenableBuilder()
    //     0xa51030: bl              #0xa3091c  ; AllocateValueListenableBuilderStub -> ValueListenableBuilder<X0> (size=0x1c)
    // 0xa51034: mov             x3, x0
    // 0xa51038: ldur            x0, [fp, #-0x20]
    // 0xa5103c: stur            x3, [fp, #-8]
    // 0xa51040: StoreField: r3->field_f = r0
    //     0xa51040: stur            w0, [x3, #0xf]
    // 0xa51044: ldur            x2, [fp, #-0x10]
    // 0xa51048: r1 = Function '<anonymous closure>':.
    //     0xa51048: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e38] AnonymousClosure: (0xa510f8), in [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::build (0xa50d60)
    //     0xa5104c: ldr             x1, [x1, #0xe38]
    // 0xa51050: r0 = AllocateClosure()
    //     0xa51050: bl              #0xec1630  ; AllocateClosureStub
    // 0xa51054: mov             x1, x0
    // 0xa51058: ldur            x0, [fp, #-8]
    // 0xa5105c: StoreField: r0->field_13 = r1
    //     0xa5105c: stur            w1, [x0, #0x13]
    // 0xa51060: ldur            x1, [fp, #-0x68]
    // 0xa51064: ArrayStore: r0[0] = r1  ; List_4
    //     0xa51064: stur            w1, [x0, #0x17]
    // 0xa51068: r0 = SimpleGestureDetector()
    //     0xa51068: bl              #0xa510e0  ; AllocateSimpleGestureDetectorStub -> SimpleGestureDetector (size=0x2c)
    // 0xa5106c: ldur            x1, [fp, #-8]
    // 0xa51070: StoreField: r0->field_b = r1
    //     0xa51070: stur            w1, [x0, #0xb]
    // 0xa51074: r1 = Instance_SimpleSwipeConfig
    //     0xa51074: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ed8] Obj!SimpleSwipeConfig@e0c0c1
    //     0xa51078: ldr             x1, [x1, #0xed8]
    // 0xa5107c: StoreField: r0->field_f = r1
    //     0xa5107c: stur            w1, [x0, #0xf]
    // 0xa51080: r1 = Instance_HitTestBehavior
    //     0xa51080: ldr             x1, [PP, #0x6c40]  ; [pp+0x6c40] Obj!HitTestBehavior@e358a1
    // 0xa51084: StoreField: r0->field_13 = r1
    //     0xa51084: stur            w1, [x0, #0x13]
    // 0xa51088: ldur            x1, [fp, #-0x28]
    // 0xa5108c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa5108c: stur            w1, [x0, #0x17]
    // 0xa51090: LeaveFrame
    //     0xa51090: mov             SP, fp
    //     0xa51094: ldp             fp, lr, [SP], #0x10
    // 0xa51098: ret
    //     0xa51098: ret             
    // 0xa5109c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5109c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa510a0: b               #0xa50de0
    // 0xa510a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa510a4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa510a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa510a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa510ac: r9 = _pageHeight
    //     0xa510ac: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e40] Field <_TableCalendarBaseState@1929380592._pageHeight@1929380592>: late final (offset: 0x14)
    //     0xa510b0: ldr             x9, [x9, #0xe40]
    // 0xa510b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa510b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa510b8: r9 = _pageController
    //     0xa510b8: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e48] Field <_TableCalendarBaseState@1929380592._pageController@1929380592>: late final (offset: 0x18)
    //     0xa510bc: ldr             x9, [x9, #0xe48]
    // 0xa510c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa510c0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa510c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa510c4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa510c8: r9 = _previousIndex
    //     0xa510c8: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e50] Field <_TableCalendarBaseState@1929380592._previousIndex@1929380592>: late (offset: 0x20)
    //     0xa510cc: ldr             x9, [x9, #0xe50]
    // 0xa510d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa510d0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa510d4: r9 = _focusedDay
    //     0xa510d4: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e58] Field <_TableCalendarBaseState@1929380592._focusedDay@1929380592>: late (offset: 0x1c)
    //     0xa510d8: ldr             x9, [x9, #0xe58]
    // 0xa510dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa510dc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedSize <anonymous closure>(dynamic, BuildContext, double, Widget?) {
    // ** addr: 0xa510f8, size: 0x110
    // 0xa510f8: EnterFrame
    //     0xa510f8: stp             fp, lr, [SP, #-0x10]!
    //     0xa510fc: mov             fp, SP
    // 0xa51100: AllocStack(0x10)
    //     0xa51100: sub             SP, SP, #0x10
    // 0xa51104: SetupParameters()
    //     0xa51104: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    //     0xa51108: ldr             x0, [fp, #0x28]
    //     0xa5110c: ldur            w1, [x0, #0x17]
    //     0xa51110: add             x1, x1, HEAP, lsl #32
    // 0xa51104: d0 = inf
    // 0xa51114: LoadField: r0 = r1->field_f
    //     0xa51114: ldur            w0, [x1, #0xf]
    // 0xa51118: DecompressPointer r0
    //     0xa51118: add             x0, x0, HEAP, lsl #32
    // 0xa5111c: LoadField: d1 = r0->field_1f
    //     0xa5111c: ldur            d1, [x0, #0x1f]
    // 0xa51120: fcmp            d0, d1
    // 0xa51124: b.le            #0xa51130
    // 0xa51128: mov             v0.16b, v1.16b
    // 0xa5112c: b               #0xa51138
    // 0xa51130: ldr             x0, [fp, #0x18]
    // 0xa51134: LoadField: d0 = r0->field_7
    //     0xa51134: ldur            d0, [x0, #7]
    // 0xa51138: ldr             x0, [fp, #0x10]
    // 0xa5113c: LoadField: r2 = r1->field_b
    //     0xa5113c: ldur            w2, [x1, #0xb]
    // 0xa51140: DecompressPointer r2
    //     0xa51140: add             x2, x2, HEAP, lsl #32
    // 0xa51144: LoadField: r1 = r2->field_f
    //     0xa51144: ldur            w1, [x2, #0xf]
    // 0xa51148: DecompressPointer r1
    //     0xa51148: add             x1, x1, HEAP, lsl #32
    // 0xa5114c: LoadField: r2 = r1->field_b
    //     0xa5114c: ldur            w2, [x1, #0xb]
    // 0xa51150: DecompressPointer r2
    //     0xa51150: add             x2, x2, HEAP, lsl #32
    // 0xa51154: cmp             w2, NULL
    // 0xa51158: b.eq            #0xa511e8
    // 0xa5115c: r1 = inline_Allocate_Double()
    //     0xa5115c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xa51160: add             x1, x1, #0x10
    //     0xa51164: cmp             x2, x1
    //     0xa51168: b.ls            #0xa511ec
    //     0xa5116c: str             x1, [THR, #0x50]  ; THR::top
    //     0xa51170: sub             x1, x1, #0xf
    //     0xa51174: movz            x2, #0xe15c
    //     0xa51178: movk            x2, #0x3, lsl #16
    //     0xa5117c: stur            x2, [x1, #-1]
    // 0xa51180: StoreField: r1->field_7 = d0
    //     0xa51180: stur            d0, [x1, #7]
    // 0xa51184: stur            x1, [fp, #-8]
    // 0xa51188: r0 = SizedBox()
    //     0xa51188: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa5118c: mov             x1, x0
    // 0xa51190: ldur            x0, [fp, #-8]
    // 0xa51194: stur            x1, [fp, #-0x10]
    // 0xa51198: StoreField: r1->field_13 = r0
    //     0xa51198: stur            w0, [x1, #0x13]
    // 0xa5119c: ldr             x0, [fp, #0x10]
    // 0xa511a0: StoreField: r1->field_b = r0
    //     0xa511a0: stur            w0, [x1, #0xb]
    // 0xa511a4: r0 = AnimatedSize()
    //     0xa511a4: bl              #0x9e30c4  ; AllocateAnimatedSizeStub -> AnimatedSize (size=0x28)
    // 0xa511a8: ldur            x1, [fp, #-0x10]
    // 0xa511ac: StoreField: r0->field_b = r1
    //     0xa511ac: stur            w1, [x0, #0xb]
    // 0xa511b0: r1 = Instance_Alignment
    //     0xa511b0: add             x1, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!Alignment@e13eb1
    //     0xa511b4: ldr             x1, [x1, #0xe0]
    // 0xa511b8: StoreField: r0->field_f = r1
    //     0xa511b8: stur            w1, [x0, #0xf]
    // 0xa511bc: r1 = Instance__Linear
    //     0xa511bc: ldr             x1, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa511c0: StoreField: r0->field_13 = r1
    //     0xa511c0: stur            w1, [x0, #0x13]
    // 0xa511c4: r1 = Instance_Duration
    //     0xa511c4: add             x1, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa511c8: ldr             x1, [x1, #0x368]
    // 0xa511cc: ArrayStore: r0[0] = r1  ; List_4
    //     0xa511cc: stur            w1, [x0, #0x17]
    // 0xa511d0: r1 = Instance_Clip
    //     0xa511d0: add             x1, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xa511d4: ldr             x1, [x1, #0x7c0]
    // 0xa511d8: StoreField: r0->field_1f = r1
    //     0xa511d8: stur            w1, [x0, #0x1f]
    // 0xa511dc: LeaveFrame
    //     0xa511dc: mov             SP, fp
    //     0xa511e0: ldp             fp, lr, [SP], #0x10
    // 0xa511e4: ret
    //     0xa511e4: ret             
    // 0xa511e8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa511e8: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa511ec: SaveReg d0
    //     0xa511ec: str             q0, [SP, #-0x10]!
    // 0xa511f0: SaveReg r0
    //     0xa511f0: str             x0, [SP, #-8]!
    // 0xa511f4: r0 = AllocateDouble()
    //     0xa511f4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa511f8: mov             x1, x0
    // 0xa511fc: RestoreReg r0
    //     0xa511fc: ldr             x0, [SP], #8
    // 0xa51200: RestoreReg d0
    //     0xa51200: ldr             q0, [SP], #0x10
    // 0xa51204: b               #0xa51180
  }
  [closure] void <anonymous closure>(dynamic, int, DateTime) {
    // ** addr: 0xa51208, size: 0x234
    // 0xa51208: EnterFrame
    //     0xa51208: stp             fp, lr, [SP, #-0x10]!
    //     0xa5120c: mov             fp, SP
    // 0xa51210: AllocStack(0x20)
    //     0xa51210: sub             SP, SP, #0x20
    // 0xa51214: SetupParameters()
    //     0xa51214: ldr             x0, [fp, #0x20]
    //     0xa51218: ldur            w3, [x0, #0x17]
    //     0xa5121c: add             x3, x3, HEAP, lsl #32
    //     0xa51220: stur            x3, [fp, #-0x10]
    // 0xa51224: CheckStackOverflow
    //     0xa51224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa51228: cmp             SP, x16
    //     0xa5122c: b.ls            #0xa513f0
    // 0xa51230: LoadField: r0 = r3->field_b
    //     0xa51230: ldur            w0, [x3, #0xb]
    // 0xa51234: DecompressPointer r0
    //     0xa51234: add             x0, x0, HEAP, lsl #32
    // 0xa51238: stur            x0, [fp, #-8]
    // 0xa5123c: LoadField: r1 = r0->field_f
    //     0xa5123c: ldur            w1, [x0, #0xf]
    // 0xa51240: DecompressPointer r1
    //     0xa51240: add             x1, x1, HEAP, lsl #32
    // 0xa51244: LoadField: r2 = r1->field_23
    //     0xa51244: ldur            w2, [x1, #0x23]
    // 0xa51248: DecompressPointer r2
    //     0xa51248: add             x2, x2, HEAP, lsl #32
    // 0xa5124c: r16 = Sentinel
    //     0xa5124c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa51250: cmp             w2, w16
    // 0xa51254: b.eq            #0xa513f8
    // 0xa51258: tbz             w2, #4, #0xa513cc
    // 0xa5125c: LoadField: r2 = r1->field_1b
    //     0xa5125c: ldur            w2, [x1, #0x1b]
    // 0xa51260: DecompressPointer r2
    //     0xa51260: add             x2, x2, HEAP, lsl #32
    // 0xa51264: r16 = Sentinel
    //     0xa51264: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa51268: cmp             w2, w16
    // 0xa5126c: b.eq            #0xa51404
    // 0xa51270: mov             x1, x2
    // 0xa51274: ldr             x2, [fp, #0x10]
    // 0xa51278: r0 = isSameDay()
    //     0xa51278: bl              #0xa4fa74  ; [package:table_calendar/src/shared/utils.dart] ::isSameDay
    // 0xa5127c: tbz             w0, #4, #0xa512b0
    // 0xa51280: ldur            x3, [fp, #-8]
    // 0xa51284: LoadField: r1 = r3->field_f
    //     0xa51284: ldur            w1, [x3, #0xf]
    // 0xa51288: DecompressPointer r1
    //     0xa51288: add             x1, x1, HEAP, lsl #32
    // 0xa5128c: ldr             x0, [fp, #0x10]
    // 0xa51290: StoreField: r1->field_1b = r0
    //     0xa51290: stur            w0, [x1, #0x1b]
    //     0xa51294: ldurb           w16, [x1, #-1]
    //     0xa51298: ldurb           w17, [x0, #-1]
    //     0xa5129c: and             x16, x17, x16, lsr #2
    //     0xa512a0: tst             x16, HEAP, lsr #32
    //     0xa512a4: b.eq            #0xa512ac
    //     0xa512a8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa512ac: b               #0xa512b4
    // 0xa512b0: ldur            x3, [fp, #-8]
    // 0xa512b4: ldur            x0, [fp, #-0x10]
    // 0xa512b8: d0 = inf
    //     0xa512b8: ldr             d0, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xa512bc: LoadField: r1 = r3->field_f
    //     0xa512bc: ldur            w1, [x3, #0xf]
    // 0xa512c0: DecompressPointer r1
    //     0xa512c0: add             x1, x1, HEAP, lsl #32
    // 0xa512c4: LoadField: r2 = r1->field_b
    //     0xa512c4: ldur            w2, [x1, #0xb]
    // 0xa512c8: DecompressPointer r2
    //     0xa512c8: add             x2, x2, HEAP, lsl #32
    // 0xa512cc: cmp             w2, NULL
    // 0xa512d0: b.eq            #0xa51410
    // 0xa512d4: LoadField: r2 = r0->field_f
    //     0xa512d4: ldur            w2, [x0, #0xf]
    // 0xa512d8: DecompressPointer r2
    //     0xa512d8: add             x2, x2, HEAP, lsl #32
    // 0xa512dc: LoadField: d1 = r2->field_1f
    //     0xa512dc: ldur            d1, [x2, #0x1f]
    // 0xa512e0: fcmp            d0, d1
    // 0xa512e4: r16 = true
    //     0xa512e4: add             x16, NULL, #0x20  ; true
    // 0xa512e8: r17 = false
    //     0xa512e8: add             x17, NULL, #0x30  ; false
    // 0xa512ec: csel            x0, x16, x17, gt
    // 0xa512f0: tbz             w0, #4, #0xa51364
    // 0xa512f4: ldr             x2, [fp, #0x10]
    // 0xa512f8: r0 = _getRowCount()
    //     0xa512f8: bl              #0x981584  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getRowCount
    // 0xa512fc: mov             x1, x0
    // 0xa51300: ldur            x0, [fp, #-8]
    // 0xa51304: LoadField: r2 = r0->field_f
    //     0xa51304: ldur            w2, [x0, #0xf]
    // 0xa51308: DecompressPointer r2
    //     0xa51308: add             x2, x2, HEAP, lsl #32
    // 0xa5130c: LoadField: r3 = r2->field_13
    //     0xa5130c: ldur            w3, [x2, #0x13]
    // 0xa51310: DecompressPointer r3
    //     0xa51310: add             x3, x3, HEAP, lsl #32
    // 0xa51314: r16 = Sentinel
    //     0xa51314: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa51318: cmp             w3, w16
    // 0xa5131c: b.eq            #0xa51414
    // 0xa51320: mov             x16, x1
    // 0xa51324: mov             x1, x2
    // 0xa51328: mov             x2, x16
    // 0xa5132c: stur            x3, [fp, #-0x10]
    // 0xa51330: r0 = _getPageHeight()
    //     0xa51330: bl              #0x9814f4  ; [package:table_calendar/src/table_calendar_base.dart] _TableCalendarBaseState::_getPageHeight
    // 0xa51334: r2 = inline_Allocate_Double()
    //     0xa51334: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xa51338: add             x2, x2, #0x10
    //     0xa5133c: cmp             x0, x2
    //     0xa51340: b.ls            #0xa51420
    //     0xa51344: str             x2, [THR, #0x50]  ; THR::top
    //     0xa51348: sub             x2, x2, #0xf
    //     0xa5134c: movz            x0, #0xe15c
    //     0xa51350: movk            x0, #0x3, lsl #16
    //     0xa51354: stur            x0, [x2, #-1]
    // 0xa51358: StoreField: r2->field_7 = d0
    //     0xa51358: stur            d0, [x2, #7]
    // 0xa5135c: ldur            x1, [fp, #-0x10]
    // 0xa51360: r0 = value=()
    //     0xa51360: bl              #0x64d1c4  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xa51364: ldur            x1, [fp, #-8]
    // 0xa51368: LoadField: r2 = r1->field_f
    //     0xa51368: ldur            w2, [x1, #0xf]
    // 0xa5136c: DecompressPointer r2
    //     0xa5136c: add             x2, x2, HEAP, lsl #32
    // 0xa51370: ldr             x0, [fp, #0x18]
    // 0xa51374: StoreField: r2->field_1f = r0
    //     0xa51374: stur            w0, [x2, #0x1f]
    //     0xa51378: tbz             w0, #0, #0xa51394
    //     0xa5137c: ldurb           w16, [x2, #-1]
    //     0xa51380: ldurb           w17, [x0, #-1]
    //     0xa51384: and             x16, x17, x16, lsr #2
    //     0xa51388: tst             x16, HEAP, lsr #32
    //     0xa5138c: b.eq            #0xa51394
    //     0xa51390: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa51394: LoadField: r0 = r2->field_b
    //     0xa51394: ldur            w0, [x2, #0xb]
    // 0xa51398: DecompressPointer r0
    //     0xa51398: add             x0, x0, HEAP, lsl #32
    // 0xa5139c: cmp             w0, NULL
    // 0xa513a0: b.eq            #0xa51434
    // 0xa513a4: LoadField: r2 = r0->field_77
    //     0xa513a4: ldur            w2, [x0, #0x77]
    // 0xa513a8: DecompressPointer r2
    //     0xa513a8: add             x2, x2, HEAP, lsl #32
    // 0xa513ac: cmp             w2, NULL
    // 0xa513b0: b.eq            #0xa51438
    // 0xa513b4: ldr             x16, [fp, #0x10]
    // 0xa513b8: stp             x16, x2, [SP]
    // 0xa513bc: mov             x0, x2
    // 0xa513c0: ClosureCall
    //     0xa513c0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa513c4: ldur            x2, [x0, #0x1f]
    //     0xa513c8: blr             x2
    // 0xa513cc: ldur            x1, [fp, #-8]
    // 0xa513d0: r2 = false
    //     0xa513d0: add             x2, NULL, #0x30  ; false
    // 0xa513d4: LoadField: r3 = r1->field_f
    //     0xa513d4: ldur            w3, [x1, #0xf]
    // 0xa513d8: DecompressPointer r3
    //     0xa513d8: add             x3, x3, HEAP, lsl #32
    // 0xa513dc: StoreField: r3->field_23 = r2
    //     0xa513dc: stur            w2, [x3, #0x23]
    // 0xa513e0: r0 = Null
    //     0xa513e0: mov             x0, NULL
    // 0xa513e4: LeaveFrame
    //     0xa513e4: mov             SP, fp
    //     0xa513e8: ldp             fp, lr, [SP], #0x10
    // 0xa513ec: ret
    //     0xa513ec: ret             
    // 0xa513f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa513f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa513f4: b               #0xa51230
    // 0xa513f8: r9 = _pageCallbackDisabled
    //     0xa513f8: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e60] Field <_TableCalendarBaseState@1929380592._pageCallbackDisabled@1929380592>: late (offset: 0x24)
    //     0xa513fc: ldr             x9, [x9, #0xe60]
    // 0xa51400: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa51400: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa51404: r9 = _focusedDay
    //     0xa51404: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e58] Field <_TableCalendarBaseState@1929380592._focusedDay@1929380592>: late (offset: 0x1c)
    //     0xa51408: ldr             x9, [x9, #0xe58]
    // 0xa5140c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa5140c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa51410: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa51410: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0xa51414: r9 = _pageHeight
    //     0xa51414: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e40] Field <_TableCalendarBaseState@1929380592._pageHeight@1929380592>: late final (offset: 0x14)
    //     0xa51418: ldr             x9, [x9, #0xe40]
    // 0xa5141c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa5141c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa51420: SaveReg d0
    //     0xa51420: str             q0, [SP, #-0x10]!
    // 0xa51424: r0 = AllocateDouble()
    //     0xa51424: bl              #0xec2254  ; AllocateDoubleStub
    // 0xa51428: mov             x2, x0
    // 0xa5142c: RestoreReg d0
    //     0xa5142c: ldr             q0, [SP], #0x10
    // 0xa51430: b               #0xa51358
    // 0xa51434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51434: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa51438: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa51438: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xa84254, size: 0x84
    // 0xa84254: EnterFrame
    //     0xa84254: stp             fp, lr, [SP, #-0x10]!
    //     0xa84258: mov             fp, SP
    // 0xa8425c: AllocStack(0x8)
    //     0xa8425c: sub             SP, SP, #8
    // 0xa84260: SetupParameters(_TableCalendarBaseState this /* r1 => r0, fp-0x8 */)
    //     0xa84260: mov             x0, x1
    //     0xa84264: stur            x1, [fp, #-8]
    // 0xa84268: CheckStackOverflow
    //     0xa84268: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8426c: cmp             SP, x16
    //     0xa84270: b.ls            #0xa842b8
    // 0xa84274: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa84274: ldur            w1, [x0, #0x17]
    // 0xa84278: DecompressPointer r1
    //     0xa84278: add             x1, x1, HEAP, lsl #32
    // 0xa8427c: r16 = Sentinel
    //     0xa8427c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa84280: cmp             w1, w16
    // 0xa84284: b.eq            #0xa842c0
    // 0xa84288: r0 = dispose()
    //     0xa84288: bl              #0xa876d8  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xa8428c: ldur            x0, [fp, #-8]
    // 0xa84290: LoadField: r1 = r0->field_13
    //     0xa84290: ldur            w1, [x0, #0x13]
    // 0xa84294: DecompressPointer r1
    //     0xa84294: add             x1, x1, HEAP, lsl #32
    // 0xa84298: r16 = Sentinel
    //     0xa84298: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8429c: cmp             w1, w16
    // 0xa842a0: b.eq            #0xa842cc
    // 0xa842a4: r0 = dispose()
    //     0xa842a4: bl              #0xa8d6e4  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xa842a8: r0 = Null
    //     0xa842a8: mov             x0, NULL
    // 0xa842ac: LeaveFrame
    //     0xa842ac: mov             SP, fp
    //     0xa842b0: ldp             fp, lr, [SP], #0x10
    // 0xa842b4: ret
    //     0xa842b4: ret             
    // 0xa842b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa842b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa842bc: b               #0xa84274
    // 0xa842c0: r9 = _pageController
    //     0xa842c0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e48] Field <_TableCalendarBaseState@1929380592._pageController@1929380592>: late final (offset: 0x18)
    //     0xa842c4: ldr             x9, [x9, #0xe48]
    // 0xa842c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa842c8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa842cc: r9 = _pageHeight
    //     0xa842cc: add             x9, PP, #0x57, lsl #12  ; [pp+0x57e40] Field <_TableCalendarBaseState@1929380592._pageHeight@1929380592>: late final (offset: 0x14)
    //     0xa842d0: ldr             x9, [x9, #0xe40]
    // 0xa842d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa842d4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4686, size: 0x80, field offset: 0xc
class TableCalendarBase extends StatefulWidget {

  _ TableCalendarBase(/* No info */) {
    // ** addr: 0xa4ed2c, size: 0x1fc
    // 0xa4ed2c: EnterFrame
    //     0xa4ed2c: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ed30: mov             fp, SP
    // 0xa4ed34: r25 = Instance_CalendarFormat
    //     0xa4ed34: add             x25, PP, #0x35, lsl #12  ; [pp+0x35eb8] Obj!CalendarFormat@e2e041
    //     0xa4ed38: ldr             x25, [x25, #0xeb8]
    // 0xa4ed3c: r24 = false
    //     0xa4ed3c: add             x24, NULL, #0x30  ; false
    // 0xa4ed40: r23 = Instance_BoxDecoration
    //     0xa4ed40: add             x23, PP, #0x47, lsl #12  ; [pp+0x47e98] Obj!BoxDecoration@e1d1e1
    //     0xa4ed44: ldr             x23, [x23, #0xe98]
    // 0xa4ed48: r20 = Instance_TableBorder
    //     0xa4ed48: add             x20, PP, #0x47, lsl #12  ; [pp+0x47ea0] Obj!TableBorder@e116b1
    //     0xa4ed4c: ldr             x20, [x20, #0xea0]
    // 0xa4ed50: r19 = Instance_EdgeInsets
    //     0xa4ed50: ldr             x19, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xa4ed54: r14 = Instance_Duration
    //     0xa4ed54: add             x14, PP, #0x22, lsl #12  ; [pp+0x22368] Obj!Duration@e3a091
    //     0xa4ed58: ldr             x14, [x14, #0x368]
    // 0xa4ed5c: r13 = Instance__Linear
    //     0xa4ed5c: ldr             x13, [PP, #0x4e60]  ; [pp+0x4e60] Obj!_Linear@e15161
    // 0xa4ed60: r12 = true
    //     0xa4ed60: add             x12, NULL, #0x20  ; true
    // 0xa4ed64: r11 = Instance_Duration
    //     0xa4ed64: add             x11, PP, #0x1a, lsl #12  ; [pp+0x1a9c0] Obj!Duration@e3a061
    //     0xa4ed68: ldr             x11, [x11, #0x9c0]
    // 0xa4ed6c: r10 = Instance_Cubic
    //     0xa4ed6c: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!Cubic@e14e61
    //     0xa4ed70: ldr             x10, [x10, #0xb28]
    // 0xa4ed74: r9 = Instance_StartingDayOfWeek
    //     0xa4ed74: add             x9, PP, #0x35, lsl #12  ; [pp+0x35ec8] Obj!StartingDayOfWeek@e2e021
    //     0xa4ed78: ldr             x9, [x9, #0xec8]
    // 0xa4ed7c: r8 = Instance_AvailableGestures
    //     0xa4ed7c: add             x8, PP, #0x35, lsl #12  ; [pp+0x35ed0] Obj!AvailableGestures@e2e0a1
    //     0xa4ed80: ldr             x8, [x8, #0xed0]
    // 0xa4ed84: r4 = Instance_SimpleSwipeConfig
    //     0xa4ed84: add             x4, PP, #0x35, lsl #12  ; [pp+0x35ed8] Obj!SimpleSwipeConfig@e0c0c1
    //     0xa4ed88: ldr             x4, [x4, #0xed8]
    // 0xa4ed8c: d1 = 16.000000
    //     0xa4ed8c: fmov            d1, #16.00000000
    // 0xa4ed90: d0 = 64.000000
    //     0xa4ed90: add             x17, PP, #0x25, lsl #12  ; [pp+0x25238] IMM: double(64) from 0x4050000000000000
    //     0xa4ed94: ldr             d0, [x17, #0x238]
    // 0xa4ed98: mov             x0, x5
    // 0xa4ed9c: mov             x16, x7
    // 0xa4eda0: mov             x7, x1
    // 0xa4eda4: mov             x1, x16
    // 0xa4eda8: mov             x16, x6
    // 0xa4edac: mov             x6, x2
    // 0xa4edb0: mov             x2, x16
    // 0xa4edb4: StoreField: r7->field_b = r0
    //     0xa4edb4: stur            w0, [x7, #0xb]
    //     0xa4edb8: ldurb           w16, [x7, #-1]
    //     0xa4edbc: ldurb           w17, [x0, #-1]
    //     0xa4edc0: and             x16, x17, x16, lsr #2
    //     0xa4edc4: tst             x16, HEAP, lsr #32
    //     0xa4edc8: b.eq            #0xa4edd0
    //     0xa4edcc: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4edd0: mov             x0, x1
    // 0xa4edd4: StoreField: r7->field_f = r0
    //     0xa4edd4: stur            w0, [x7, #0xf]
    //     0xa4edd8: ldurb           w16, [x7, #-1]
    //     0xa4eddc: ldurb           w17, [x0, #-1]
    //     0xa4ede0: and             x16, x17, x16, lsr #2
    //     0xa4ede4: tst             x16, HEAP, lsr #32
    //     0xa4ede8: b.eq            #0xa4edf0
    //     0xa4edec: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4edf0: mov             x0, x2
    // 0xa4edf4: StoreField: r7->field_13 = r0
    //     0xa4edf4: stur            w0, [x7, #0x13]
    //     0xa4edf8: ldurb           w16, [x7, #-1]
    //     0xa4edfc: ldurb           w17, [x0, #-1]
    //     0xa4ee00: and             x16, x17, x16, lsr #2
    //     0xa4ee04: tst             x16, HEAP, lsr #32
    //     0xa4ee08: b.eq            #0xa4ee10
    //     0xa4ee0c: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4ee10: ArrayStore: r7[0] = r25  ; List_4
    //     0xa4ee10: stur            w25, [x7, #0x17]
    // 0xa4ee14: mov             x0, x3
    // 0xa4ee18: StoreField: r7->field_1b = r0
    //     0xa4ee18: stur            w0, [x7, #0x1b]
    //     0xa4ee1c: ldurb           w16, [x7, #-1]
    //     0xa4ee20: ldurb           w17, [x0, #-1]
    //     0xa4ee24: and             x16, x17, x16, lsr #2
    //     0xa4ee28: tst             x16, HEAP, lsr #32
    //     0xa4ee2c: b.eq            #0xa4ee34
    //     0xa4ee30: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4ee34: mov             x0, x6
    // 0xa4ee38: StoreField: r7->field_23 = r0
    //     0xa4ee38: stur            w0, [x7, #0x23]
    //     0xa4ee3c: ldurb           w16, [x7, #-1]
    //     0xa4ee40: ldurb           w17, [x0, #-1]
    //     0xa4ee44: and             x16, x17, x16, lsr #2
    //     0xa4ee48: tst             x16, HEAP, lsr #32
    //     0xa4ee4c: b.eq            #0xa4ee54
    //     0xa4ee50: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4ee54: StoreField: r7->field_27 = d1
    //     0xa4ee54: stur            d1, [x7, #0x27]
    // 0xa4ee58: StoreField: r7->field_2f = d0
    //     0xa4ee58: stur            d0, [x7, #0x2f]
    // 0xa4ee5c: StoreField: r7->field_37 = r24
    //     0xa4ee5c: stur            w24, [x7, #0x37]
    // 0xa4ee60: StoreField: r7->field_3b = r24
    //     0xa4ee60: stur            w24, [x7, #0x3b]
    // 0xa4ee64: ldr             x0, [fp, #0x10]
    // 0xa4ee68: StoreField: r7->field_1f = r0
    //     0xa4ee68: stur            w0, [x7, #0x1f]
    //     0xa4ee6c: ldurb           w16, [x7, #-1]
    //     0xa4ee70: ldurb           w17, [x0, #-1]
    //     0xa4ee74: and             x16, x17, x16, lsr #2
    //     0xa4ee78: tst             x16, HEAP, lsr #32
    //     0xa4ee7c: b.eq            #0xa4ee84
    //     0xa4ee80: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4ee84: StoreField: r7->field_3f = r24
    //     0xa4ee84: stur            w24, [x7, #0x3f]
    // 0xa4ee88: StoreField: r7->field_43 = r23
    //     0xa4ee88: stur            w23, [x7, #0x43]
    // 0xa4ee8c: StoreField: r7->field_47 = r23
    //     0xa4ee8c: stur            w23, [x7, #0x47]
    // 0xa4ee90: StoreField: r7->field_4b = r20
    //     0xa4ee90: stur            w20, [x7, #0x4b]
    // 0xa4ee94: StoreField: r7->field_4f = r19
    //     0xa4ee94: stur            w19, [x7, #0x4f]
    // 0xa4ee98: StoreField: r7->field_53 = r14
    //     0xa4ee98: stur            w14, [x7, #0x53]
    // 0xa4ee9c: StoreField: r7->field_57 = r13
    //     0xa4ee9c: stur            w13, [x7, #0x57]
    // 0xa4eea0: StoreField: r7->field_5b = r12
    //     0xa4eea0: stur            w12, [x7, #0x5b]
    // 0xa4eea4: StoreField: r7->field_5f = r11
    //     0xa4eea4: stur            w11, [x7, #0x5f]
    // 0xa4eea8: StoreField: r7->field_63 = r10
    //     0xa4eea8: stur            w10, [x7, #0x63]
    // 0xa4eeac: StoreField: r7->field_67 = r9
    //     0xa4eeac: stur            w9, [x7, #0x67]
    // 0xa4eeb0: StoreField: r7->field_6b = r8
    //     0xa4eeb0: stur            w8, [x7, #0x6b]
    // 0xa4eeb4: StoreField: r7->field_6f = r4
    //     0xa4eeb4: stur            w4, [x7, #0x6f]
    // 0xa4eeb8: ldr             x0, [fp, #0x18]
    // 0xa4eebc: StoreField: r7->field_73 = r0
    //     0xa4eebc: stur            w0, [x7, #0x73]
    //     0xa4eec0: ldurb           w16, [x7, #-1]
    //     0xa4eec4: ldurb           w17, [x0, #-1]
    //     0xa4eec8: and             x16, x17, x16, lsr #2
    //     0xa4eecc: tst             x16, HEAP, lsr #32
    //     0xa4eed0: b.eq            #0xa4eed8
    //     0xa4eed4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4eed8: ldr             x0, [fp, #0x20]
    // 0xa4eedc: StoreField: r7->field_77 = r0
    //     0xa4eedc: stur            w0, [x7, #0x77]
    //     0xa4eee0: ldurb           w16, [x7, #-1]
    //     0xa4eee4: ldurb           w17, [x0, #-1]
    //     0xa4eee8: and             x16, x17, x16, lsr #2
    //     0xa4eeec: tst             x16, HEAP, lsr #32
    //     0xa4eef0: b.eq            #0xa4eef8
    //     0xa4eef4: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4eef8: ldr             x0, [fp, #0x28]
    // 0xa4eefc: StoreField: r7->field_7b = r0
    //     0xa4eefc: stur            w0, [x7, #0x7b]
    //     0xa4ef00: ldurb           w16, [x7, #-1]
    //     0xa4ef04: ldurb           w17, [x0, #-1]
    //     0xa4ef08: and             x16, x17, x16, lsr #2
    //     0xa4ef0c: tst             x16, HEAP, lsr #32
    //     0xa4ef10: b.eq            #0xa4ef18
    //     0xa4ef14: bl              #0xec0ae8  ; WriteBarrierWrappersStub
    // 0xa4ef18: r0 = Null
    //     0xa4ef18: mov             x0, NULL
    // 0xa4ef1c: LeaveFrame
    //     0xa4ef1c: mov             SP, fp
    //     0xa4ef20: ldp             fp, lr, [SP], #0x10
    // 0xa4ef24: ret
    //     0xa4ef24: ret             
  }
  _ createState(/* No info */) {
    // ** addr: 0xa95570, size: 0x3c
    // 0xa95570: EnterFrame
    //     0xa95570: stp             fp, lr, [SP, #-0x10]!
    //     0xa95574: mov             fp, SP
    // 0xa95578: mov             x0, x1
    // 0xa9557c: r1 = <TableCalendarBase>
    //     0xa9557c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51550] TypeArguments: <TableCalendarBase>
    //     0xa95580: ldr             x1, [x1, #0x550]
    // 0xa95584: r0 = _TableCalendarBaseState()
    //     0xa95584: bl              #0xa955ac  ; Allocate_TableCalendarBaseStateStub -> _TableCalendarBaseState (size=0x28)
    // 0xa95588: r1 = Sentinel
    //     0xa95588: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa9558c: StoreField: r0->field_13 = r1
    //     0xa9558c: stur            w1, [x0, #0x13]
    // 0xa95590: ArrayStore: r0[0] = r1  ; List_4
    //     0xa95590: stur            w1, [x0, #0x17]
    // 0xa95594: StoreField: r0->field_1b = r1
    //     0xa95594: stur            w1, [x0, #0x1b]
    // 0xa95598: StoreField: r0->field_1f = r1
    //     0xa95598: stur            w1, [x0, #0x1f]
    // 0xa9559c: StoreField: r0->field_23 = r1
    //     0xa9559c: stur            w1, [x0, #0x23]
    // 0xa955a0: LeaveFrame
    //     0xa955a0: mov             SP, fp
    //     0xa955a4: ldp             fp, lr, [SP], #0x10
    // 0xa955a8: ret
    //     0xa955a8: ret             
  }
}
