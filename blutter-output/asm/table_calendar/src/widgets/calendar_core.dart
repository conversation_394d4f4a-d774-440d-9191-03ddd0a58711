// lib: , url: package:table_calendar/src/widgets/calendar_core.dart

// class id: 1051189, size: 0x8
class :: {
}

// class id: 4912, size: 0x70, field offset: 0xc
//   const constructor, 
class CalendarCore extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbba6f0, size: 0xe8
    // 0xbba6f0: EnterFrame
    //     0xbba6f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbba6f4: mov             fp, SP
    // 0xbba6f8: AllocStack(0x40)
    //     0xbba6f8: sub             SP, SP, #0x40
    // 0xbba6fc: SetupParameters(CalendarCore this /* r1 => r1, fp-0x8 */)
    //     0xbba6fc: stur            x1, [fp, #-8]
    // 0xbba700: CheckStackOverflow
    //     0xbba700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbba704: cmp             SP, x16
    //     0xbba708: b.ls            #0xbba7d0
    // 0xbba70c: r1 = 1
    //     0xbba70c: movz            x1, #0x1
    // 0xbba710: r0 = AllocateContext()
    //     0xbba710: bl              #0xec126c  ; AllocateContextStub
    // 0xbba714: ldur            x1, [fp, #-8]
    // 0xbba718: stur            x0, [fp, #-0x20]
    // 0xbba71c: StoreField: r0->field_f = r1
    //     0xbba71c: stur            w1, [x0, #0xf]
    // 0xbba720: LoadField: r4 = r1->field_63
    //     0xbba720: ldur            w4, [x1, #0x63]
    // 0xbba724: DecompressPointer r4
    //     0xbba724: add             x4, x4, HEAP, lsl #32
    // 0xbba728: stur            x4, [fp, #-0x18]
    // 0xbba72c: LoadField: r5 = r1->field_67
    //     0xbba72c: ldur            w5, [x1, #0x67]
    // 0xbba730: DecompressPointer r5
    //     0xbba730: add             x5, x5, HEAP, lsl #32
    // 0xbba734: stur            x5, [fp, #-0x10]
    // 0xbba738: LoadField: r2 = r1->field_f
    //     0xbba738: ldur            w2, [x1, #0xf]
    // 0xbba73c: DecompressPointer r2
    //     0xbba73c: add             x2, x2, HEAP, lsl #32
    // 0xbba740: LoadField: r3 = r1->field_13
    //     0xbba740: ldur            w3, [x1, #0x13]
    // 0xbba744: DecompressPointer r3
    //     0xbba744: add             x3, x3, HEAP, lsl #32
    // 0xbba748: r0 = _getPageCount()
    //     0xbba748: bl              #0xbba7d8  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getPageCount
    // 0xbba74c: mov             x2, x0
    // 0xbba750: r0 = BoxInt64Instr(r2)
    //     0xbba750: sbfiz           x0, x2, #1, #0x1f
    //     0xbba754: cmp             x2, x0, asr #1
    //     0xbba758: b.eq            #0xbba764
    //     0xbba75c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbba760: stur            x2, [x0, #7]
    // 0xbba764: ldur            x2, [fp, #-0x20]
    // 0xbba768: r1 = Function '<anonymous closure>':.
    //     0xbba768: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ae38] AnonymousClosure: (0xbbab20), in [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::build (0xbba6f0)
    //     0xbba76c: ldr             x1, [x1, #0xe38]
    // 0xbba770: stur            x0, [fp, #-8]
    // 0xbba774: r0 = AllocateClosure()
    //     0xbba774: bl              #0xec1630  ; AllocateClosureStub
    // 0xbba778: ldur            x2, [fp, #-0x20]
    // 0xbba77c: r1 = Function '<anonymous closure>':.
    //     0xbba77c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ae40] AnonymousClosure: (0xbba95c), in [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::build (0xbba6f0)
    //     0xbba780: ldr             x1, [x1, #0xe40]
    // 0xbba784: stur            x0, [fp, #-0x20]
    // 0xbba788: r0 = AllocateClosure()
    //     0xbba788: bl              #0xec1630  ; AllocateClosureStub
    // 0xbba78c: stur            x0, [fp, #-0x28]
    // 0xbba790: r0 = PageView()
    //     0xbba790: bl              #0x9d332c  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbba794: stur            x0, [fp, #-0x30]
    // 0xbba798: ldur            x16, [fp, #-0x10]
    // 0xbba79c: ldur            lr, [fp, #-8]
    // 0xbba7a0: stp             lr, x16, [SP]
    // 0xbba7a4: mov             x1, x0
    // 0xbba7a8: ldur            x2, [fp, #-0x18]
    // 0xbba7ac: ldur            x3, [fp, #-0x20]
    // 0xbba7b0: ldur            x5, [fp, #-0x28]
    // 0xbba7b4: r4 = const [0, 0x6, 0x2, 0x4, itemCount, 0x5, physics, 0x4, null]
    //     0xbba7b4: add             x4, PP, #0x5a, lsl #12  ; [pp+0x5ae48] List(9) [0, 0x6, 0x2, 0x4, "itemCount", 0x5, "physics", 0x4, Null]
    //     0xbba7b8: ldr             x4, [x4, #0xe48]
    // 0xbba7bc: r0 = PageView.builder()
    //     0xbba7bc: bl              #0x9d3014  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbba7c0: ldur            x0, [fp, #-0x30]
    // 0xbba7c4: LeaveFrame
    //     0xbba7c4: mov             SP, fp
    //     0xbba7c8: ldp             fp, lr, [SP], #0x10
    // 0xbba7cc: ret
    //     0xbba7cc: ret             
    // 0xbba7d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba7d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba7d4: b               #0xbba70c
  }
  _ _getPageCount(/* No info */) {
    // ** addr: 0xbba7d8, size: 0x34
    // 0xbba7d8: EnterFrame
    //     0xbba7d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbba7dc: mov             fp, SP
    // 0xbba7e0: CheckStackOverflow
    //     0xbba7e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbba7e4: cmp             SP, x16
    //     0xbba7e8: b.ls            #0xbba804
    // 0xbba7ec: r0 = _getMonthCount()
    //     0xbba7ec: bl              #0xbba80c  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getMonthCount
    // 0xbba7f0: add             x1, x0, #1
    // 0xbba7f4: mov             x0, x1
    // 0xbba7f8: LeaveFrame
    //     0xbba7f8: mov             SP, fp
    //     0xbba7fc: ldp             fp, lr, [SP], #0x10
    // 0xbba800: ret
    //     0xbba800: ret             
    // 0xbba804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba804: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba808: b               #0xbba7ec
  }
  _ _getMonthCount(/* No info */) {
    // ** addr: 0xbba80c, size: 0x150
    // 0xbba80c: EnterFrame
    //     0xbba80c: stp             fp, lr, [SP, #-0x10]!
    //     0xbba810: mov             fp, SP
    // 0xbba814: AllocStack(0x20)
    //     0xbba814: sub             SP, SP, #0x20
    // 0xbba818: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xbba818: mov             x0, x3
    //     0xbba81c: stur            x2, [fp, #-8]
    //     0xbba820: stur            x3, [fp, #-0x10]
    // 0xbba824: CheckStackOverflow
    //     0xbba824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbba828: cmp             SP, x16
    //     0xbba82c: b.ls            #0xbba944
    // 0xbba830: mov             x1, x0
    // 0xbba834: r0 = _parts()
    //     0xbba834: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbba838: mov             x2, x0
    // 0xbba83c: LoadField: r0 = r2->field_b
    //     0xbba83c: ldur            w0, [x2, #0xb]
    // 0xbba840: r1 = LoadInt32Instr(r0)
    //     0xbba840: sbfx            x1, x0, #1, #0x1f
    // 0xbba844: mov             x0, x1
    // 0xbba848: r1 = 8
    //     0xbba848: movz            x1, #0x8
    // 0xbba84c: cmp             x1, x0
    // 0xbba850: b.hs            #0xbba94c
    // 0xbba854: LoadField: r0 = r2->field_2f
    //     0xbba854: ldur            w0, [x2, #0x2f]
    // 0xbba858: DecompressPointer r0
    //     0xbba858: add             x0, x0, HEAP, lsl #32
    // 0xbba85c: ldur            x1, [fp, #-8]
    // 0xbba860: stur            x0, [fp, #-0x18]
    // 0xbba864: r0 = _parts()
    //     0xbba864: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbba868: mov             x2, x0
    // 0xbba86c: LoadField: r0 = r2->field_b
    //     0xbba86c: ldur            w0, [x2, #0xb]
    // 0xbba870: r1 = LoadInt32Instr(r0)
    //     0xbba870: sbfx            x1, x0, #1, #0x1f
    // 0xbba874: mov             x0, x1
    // 0xbba878: r1 = 8
    //     0xbba878: movz            x1, #0x8
    // 0xbba87c: cmp             x1, x0
    // 0xbba880: b.hs            #0xbba950
    // 0xbba884: LoadField: r0 = r2->field_2f
    //     0xbba884: ldur            w0, [x2, #0x2f]
    // 0xbba888: DecompressPointer r0
    //     0xbba888: add             x0, x0, HEAP, lsl #32
    // 0xbba88c: ldur            x1, [fp, #-0x18]
    // 0xbba890: r2 = LoadInt32Instr(r1)
    //     0xbba890: sbfx            x2, x1, #1, #0x1f
    //     0xbba894: tbz             w1, #0, #0xbba89c
    //     0xbba898: ldur            x2, [x1, #7]
    // 0xbba89c: r1 = LoadInt32Instr(r0)
    //     0xbba89c: sbfx            x1, x0, #1, #0x1f
    //     0xbba8a0: tbz             w0, #0, #0xbba8a8
    //     0xbba8a4: ldur            x1, [x0, #7]
    // 0xbba8a8: sub             x0, x2, x1
    // 0xbba8ac: ldur            x1, [fp, #-0x10]
    // 0xbba8b0: stur            x0, [fp, #-0x20]
    // 0xbba8b4: r0 = _parts()
    //     0xbba8b4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbba8b8: mov             x2, x0
    // 0xbba8bc: LoadField: r0 = r2->field_b
    //     0xbba8bc: ldur            w0, [x2, #0xb]
    // 0xbba8c0: r1 = LoadInt32Instr(r0)
    //     0xbba8c0: sbfx            x1, x0, #1, #0x1f
    // 0xbba8c4: mov             x0, x1
    // 0xbba8c8: r1 = 7
    //     0xbba8c8: movz            x1, #0x7
    // 0xbba8cc: cmp             x1, x0
    // 0xbba8d0: b.hs            #0xbba954
    // 0xbba8d4: LoadField: r0 = r2->field_2b
    //     0xbba8d4: ldur            w0, [x2, #0x2b]
    // 0xbba8d8: DecompressPointer r0
    //     0xbba8d8: add             x0, x0, HEAP, lsl #32
    // 0xbba8dc: ldur            x1, [fp, #-8]
    // 0xbba8e0: stur            x0, [fp, #-0x10]
    // 0xbba8e4: r0 = _parts()
    //     0xbba8e4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbba8e8: mov             x2, x0
    // 0xbba8ec: LoadField: r3 = r2->field_b
    //     0xbba8ec: ldur            w3, [x2, #0xb]
    // 0xbba8f0: r0 = LoadInt32Instr(r3)
    //     0xbba8f0: sbfx            x0, x3, #1, #0x1f
    // 0xbba8f4: r1 = 7
    //     0xbba8f4: movz            x1, #0x7
    // 0xbba8f8: cmp             x1, x0
    // 0xbba8fc: b.hs            #0xbba958
    // 0xbba900: LoadField: r1 = r2->field_2b
    //     0xbba900: ldur            w1, [x2, #0x2b]
    // 0xbba904: DecompressPointer r1
    //     0xbba904: add             x1, x1, HEAP, lsl #32
    // 0xbba908: ldur            x2, [fp, #-0x10]
    // 0xbba90c: r3 = LoadInt32Instr(r2)
    //     0xbba90c: sbfx            x3, x2, #1, #0x1f
    //     0xbba910: tbz             w2, #0, #0xbba918
    //     0xbba914: ldur            x3, [x2, #7]
    // 0xbba918: r2 = LoadInt32Instr(r1)
    //     0xbba918: sbfx            x2, x1, #1, #0x1f
    //     0xbba91c: tbz             w1, #0, #0xbba924
    //     0xbba920: ldur            x2, [x1, #7]
    // 0xbba924: sub             x1, x3, x2
    // 0xbba928: ldur            x2, [fp, #-0x20]
    // 0xbba92c: r16 = 12
    //     0xbba92c: movz            x16, #0xc
    // 0xbba930: mul             x3, x2, x16
    // 0xbba934: add             x0, x3, x1
    // 0xbba938: LeaveFrame
    //     0xbba938: mov             SP, fp
    //     0xbba93c: ldp             fp, lr, [SP], #0x10
    // 0xbba940: ret
    //     0xbba940: ret             
    // 0xbba944: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba944: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba948: b               #0xbba830
    // 0xbba94c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbba94c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbba950: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbba950: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbba954: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbba954: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbba958: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbba958: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbba95c, size: 0x94
    // 0xbba95c: EnterFrame
    //     0xbba95c: stp             fp, lr, [SP, #-0x10]!
    //     0xbba960: mov             fp, SP
    // 0xbba964: AllocStack(0x20)
    //     0xbba964: sub             SP, SP, #0x20
    // 0xbba968: SetupParameters()
    //     0xbba968: ldr             x0, [fp, #0x18]
    //     0xbba96c: ldur            w4, [x0, #0x17]
    //     0xbba970: add             x4, x4, HEAP, lsl #32
    //     0xbba974: stur            x4, [fp, #-8]
    // 0xbba978: CheckStackOverflow
    //     0xbba978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbba97c: cmp             SP, x16
    //     0xbba980: b.ls            #0xbba9e8
    // 0xbba984: LoadField: r1 = r4->field_f
    //     0xbba984: ldur            w1, [x4, #0xf]
    // 0xbba988: DecompressPointer r1
    //     0xbba988: add             x1, x1, HEAP, lsl #32
    // 0xbba98c: LoadField: r2 = r1->field_b
    //     0xbba98c: ldur            w2, [x1, #0xb]
    // 0xbba990: DecompressPointer r2
    //     0xbba990: add             x2, x2, HEAP, lsl #32
    // 0xbba994: ldr             x0, [fp, #0x10]
    // 0xbba998: r3 = LoadInt32Instr(r0)
    //     0xbba998: sbfx            x3, x0, #1, #0x1f
    //     0xbba99c: tbz             w0, #0, #0xbba9a4
    //     0xbba9a0: ldur            x3, [x0, #7]
    // 0xbba9a4: r0 = _getFocusedDay()
    //     0xbba9a4: bl              #0xbba9f0  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getFocusedDay
    // 0xbba9a8: mov             x1, x0
    // 0xbba9ac: ldur            x0, [fp, #-8]
    // 0xbba9b0: LoadField: r2 = r0->field_f
    //     0xbba9b0: ldur            w2, [x0, #0xf]
    // 0xbba9b4: DecompressPointer r2
    //     0xbba9b4: add             x2, x2, HEAP, lsl #32
    // 0xbba9b8: LoadField: r0 = r2->field_6b
    //     0xbba9b8: ldur            w0, [x2, #0x6b]
    // 0xbba9bc: DecompressPointer r0
    //     0xbba9bc: add             x0, x0, HEAP, lsl #32
    // 0xbba9c0: ldr             x16, [fp, #0x10]
    // 0xbba9c4: stp             x16, x0, [SP, #8]
    // 0xbba9c8: str             x1, [SP]
    // 0xbba9cc: ClosureCall
    //     0xbba9cc: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xbba9d0: ldur            x2, [x0, #0x1f]
    //     0xbba9d4: blr             x2
    // 0xbba9d8: r0 = Null
    //     0xbba9d8: mov             x0, NULL
    // 0xbba9dc: LeaveFrame
    //     0xbba9dc: mov             SP, fp
    //     0xbba9e0: ldp             fp, lr, [SP], #0x10
    // 0xbba9e4: ret
    //     0xbba9e4: ret             
    // 0xbba9e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbba9e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbba9ec: b               #0xbba984
  }
  _ _getFocusedDay(/* No info */) {
    // ** addr: 0xbba9f0, size: 0x130
    // 0xbba9f0: EnterFrame
    //     0xbba9f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbba9f4: mov             fp, SP
    // 0xbba9f8: AllocStack(0x30)
    //     0xbba9f8: sub             SP, SP, #0x30
    // 0xbba9fc: SetupParameters(CalendarCore this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbba9fc: mov             x4, x1
    //     0xbbaa00: stur            x1, [fp, #-0x10]
    //     0xbbaa04: stur            x2, [fp, #-0x18]
    // 0xbbaa08: CheckStackOverflow
    //     0xbbaa08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbaa0c: cmp             SP, x16
    //     0xbbaa10: b.ls            #0xbbab18
    // 0xbbaa14: LoadField: r0 = r4->field_57
    //     0xbbaa14: ldur            x0, [x4, #0x57]
    // 0xbbaa18: cmp             x3, x0
    // 0xbbaa1c: b.ne            #0xbbaa30
    // 0xbbaa20: mov             x0, x2
    // 0xbbaa24: LeaveFrame
    //     0xbbaa24: mov             SP, fp
    //     0xbbaa28: ldp             fp, lr, [SP], #0x10
    // 0xbbaa2c: ret
    //     0xbbaa2c: ret             
    // 0xbbaa30: sub             x5, x3, x0
    // 0xbbaa34: stur            x5, [fp, #-8]
    // 0xbbaa38: r0 = LoadClassIdInstr(r2)
    //     0xbbaa38: ldur            x0, [x2, #-1]
    //     0xbbaa3c: ubfx            x0, x0, #0xc, #0x14
    // 0xbbaa40: mov             x1, x2
    // 0xbbaa44: r0 = GDT[cid_x0 + -0xff6]()
    //     0xbbaa44: sub             lr, x0, #0xff6
    //     0xbbaa48: ldr             lr, [x21, lr, lsl #3]
    //     0xbbaa4c: blr             lr
    // 0xbbaa50: mov             x2, x0
    // 0xbbaa54: ldur            x1, [fp, #-0x18]
    // 0xbbaa58: stur            x2, [fp, #-0x20]
    // 0xbbaa5c: r0 = LoadClassIdInstr(r1)
    //     0xbbaa5c: ldur            x0, [x1, #-1]
    //     0xbbaa60: ubfx            x0, x0, #0xc, #0x14
    // 0xbbaa64: r0 = GDT[cid_x0 + -0xfff]()
    //     0xbbaa64: sub             lr, x0, #0xfff
    //     0xbbaa68: ldr             lr, [x21, lr, lsl #3]
    //     0xbbaa6c: blr             lr
    // 0xbbaa70: mov             x1, x0
    // 0xbbaa74: ldur            x0, [fp, #-8]
    // 0xbbaa78: add             x2, x1, x0
    // 0xbbaa7c: r0 = BoxInt64Instr(r2)
    //     0xbbaa7c: sbfiz           x0, x2, #1, #0x1f
    //     0xbbaa80: cmp             x2, x0, asr #1
    //     0xbbaa84: b.eq            #0xbbaa90
    //     0xbbaa88: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbaa8c: stur            x2, [x0, #7]
    // 0xbbaa90: stur            x0, [fp, #-0x18]
    // 0xbbaa94: r0 = DateTime()
    //     0xbbaa94: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbbaa98: stur            x0, [fp, #-0x28]
    // 0xbbaa9c: ldur            x16, [fp, #-0x18]
    // 0xbbaaa0: str             x16, [SP]
    // 0xbbaaa4: mov             x1, x0
    // 0xbbaaa8: ldur            x2, [fp, #-0x20]
    // 0xbbaaac: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbbaaac: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbbaab0: r0 = DateTime.utc()
    //     0xbbaab0: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xbbaab4: ldur            x0, [fp, #-0x10]
    // 0xbbaab8: LoadField: r3 = r0->field_f
    //     0xbbaab8: ldur            w3, [x0, #0xf]
    // 0xbbaabc: DecompressPointer r3
    //     0xbbaabc: add             x3, x3, HEAP, lsl #32
    // 0xbbaac0: ldur            x1, [fp, #-0x28]
    // 0xbbaac4: mov             x2, x3
    // 0xbbaac8: stur            x3, [fp, #-0x18]
    // 0xbbaacc: r0 = isBefore()
    //     0xbbaacc: bl              #0xd5ddd0  ; [dart:core] DateTime::isBefore
    // 0xbbaad0: tbnz            w0, #4, #0xbbaadc
    // 0xbbaad4: ldur            x0, [fp, #-0x18]
    // 0xbbaad8: b               #0xbbab0c
    // 0xbbaadc: ldur            x0, [fp, #-0x10]
    // 0xbbaae0: LoadField: r3 = r0->field_13
    //     0xbbaae0: ldur            w3, [x0, #0x13]
    // 0xbbaae4: DecompressPointer r3
    //     0xbbaae4: add             x3, x3, HEAP, lsl #32
    // 0xbbaae8: ldur            x1, [fp, #-0x28]
    // 0xbbaaec: mov             x2, x3
    // 0xbbaaf0: stur            x3, [fp, #-0x18]
    // 0xbbaaf4: r0 = isAfter()
    //     0xbbaaf4: bl              #0xd61fd4  ; [dart:core] DateTime::isAfter
    // 0xbbaaf8: tbnz            w0, #4, #0xbbab04
    // 0xbbaafc: ldur            x1, [fp, #-0x18]
    // 0xbbab00: b               #0xbbab08
    // 0xbbab04: ldur            x1, [fp, #-0x28]
    // 0xbbab08: mov             x0, x1
    // 0xbbab0c: LeaveFrame
    //     0xbbab0c: mov             SP, fp
    //     0xbbab10: ldp             fp, lr, [SP], #0x10
    // 0xbbab14: ret
    //     0xbbab14: ret             
    // 0xbbab18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbab18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbab1c: b               #0xbbaa14
  }
  [closure] CalendarPage <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbbab20, size: 0x1d4
    // 0xbbab20: EnterFrame
    //     0xbbab20: stp             fp, lr, [SP, #-0x10]!
    //     0xbbab24: mov             fp, SP
    // 0xbbab28: AllocStack(0x28)
    //     0xbbab28: sub             SP, SP, #0x28
    // 0xbbab2c: SetupParameters()
    //     0xbbab2c: ldr             x0, [fp, #0x20]
    //     0xbbab30: ldur            w1, [x0, #0x17]
    //     0xbbab34: add             x1, x1, HEAP, lsl #32
    //     0xbbab38: stur            x1, [fp, #-8]
    // 0xbbab3c: CheckStackOverflow
    //     0xbbab3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbab40: cmp             SP, x16
    //     0xbbab44: b.ls            #0xbbacdc
    // 0xbbab48: r1 = 2
    //     0xbbab48: movz            x1, #0x2
    // 0xbbab4c: r0 = AllocateContext()
    //     0xbbab4c: bl              #0xec126c  ; AllocateContextStub
    // 0xbbab50: mov             x3, x0
    // 0xbbab54: ldur            x0, [fp, #-8]
    // 0xbbab58: stur            x3, [fp, #-0x10]
    // 0xbbab5c: StoreField: r3->field_b = r0
    //     0xbbab5c: stur            w0, [x3, #0xb]
    // 0xbbab60: ldr             x1, [fp, #0x10]
    // 0xbbab64: StoreField: r3->field_f = r1
    //     0xbbab64: stur            w1, [x3, #0xf]
    // 0xbbab68: LoadField: r2 = r0->field_f
    //     0xbbab68: ldur            w2, [x0, #0xf]
    // 0xbbab6c: DecompressPointer r2
    //     0xbbab6c: add             x2, x2, HEAP, lsl #32
    // 0xbbab70: r4 = LoadInt32Instr(r1)
    //     0xbbab70: sbfx            x4, x1, #1, #0x1f
    //     0xbbab74: tbz             w1, #0, #0xbbab7c
    //     0xbbab78: ldur            x4, [x1, #7]
    // 0xbbab7c: mov             x1, x2
    // 0xbbab80: mov             x2, x4
    // 0xbbab84: r0 = _getBaseDay()
    //     0xbbab84: bl              #0xbbb4c0  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getBaseDay
    // 0xbbab88: mov             x3, x0
    // 0xbbab8c: ldur            x0, [fp, #-8]
    // 0xbbab90: stur            x3, [fp, #-0x18]
    // 0xbbab94: LoadField: r1 = r0->field_f
    //     0xbbab94: ldur            w1, [x0, #0xf]
    // 0xbbab98: DecompressPointer r1
    //     0xbbab98: add             x1, x1, HEAP, lsl #32
    // 0xbbab9c: mov             x2, x3
    // 0xbbaba0: r0 = _daysInMonth()
    //     0xbbaba0: bl              #0xbbb3cc  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_daysInMonth
    // 0xbbaba4: mov             x1, x0
    // 0xbbaba8: ldur            x0, [fp, #-8]
    // 0xbbabac: LoadField: r2 = r0->field_f
    //     0xbbabac: ldur            w2, [x0, #0xf]
    // 0xbbabb0: DecompressPointer r2
    //     0xbbabb0: add             x2, x2, HEAP, lsl #32
    // 0xbbabb4: LoadField: r3 = r1->field_7
    //     0xbbabb4: ldur            w3, [x1, #7]
    // 0xbbabb8: DecompressPointer r3
    //     0xbbabb8: add             x3, x3, HEAP, lsl #32
    // 0xbbabbc: LoadField: r4 = r1->field_b
    //     0xbbabbc: ldur            w4, [x1, #0xb]
    // 0xbbabc0: DecompressPointer r4
    //     0xbbabc0: add             x4, x4, HEAP, lsl #32
    // 0xbbabc4: mov             x1, x2
    // 0xbbabc8: mov             x2, x3
    // 0xbbabcc: mov             x3, x4
    // 0xbbabd0: r0 = _daysInRange()
    //     0xbbabd0: bl              #0xbbb1d8  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_daysInRange
    // 0xbbabd4: mov             x3, x0
    // 0xbbabd8: ldur            x0, [fp, #-8]
    // 0xbbabdc: stur            x3, [fp, #-0x20]
    // 0xbbabe0: LoadField: r1 = r0->field_f
    //     0xbbabe0: ldur            w1, [x0, #0xf]
    // 0xbbabe4: DecompressPointer r1
    //     0xbbabe4: add             x1, x1, HEAP, lsl #32
    // 0xbbabe8: LoadField: r0 = r1->field_53
    //     0xbbabe8: ldur            w0, [x1, #0x53]
    // 0xbbabec: DecompressPointer r0
    //     0xbbabec: add             x0, x0, HEAP, lsl #32
    // 0xbbabf0: LoadField: d0 = r0->field_1f
    //     0xbbabf0: ldur            d0, [x0, #0x1f]
    // 0xbbabf4: d1 = inf
    //     0xbbabf4: ldr             d1, [PP, #0x12f0]  ; [pp+0x12f0] IMM: double(inf) from 0x7ff0000000000000
    // 0xbbabf8: fcmp            d1, d0
    // 0xbbabfc: b.le            #0xbbac4c
    // 0xbbac00: d1 = 0.000000
    //     0xbbac00: eor             v1.16b, v1.16b, v1.16b
    // 0xbbac04: fsub            d2, d0, d1
    // 0xbbac08: ldur            x2, [fp, #-0x18]
    // 0xbbac0c: stur            d2, [fp, #-0x28]
    // 0xbbac10: r0 = _getRowCount()
    //     0xbbac10: bl              #0xbbad00  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getRowCount
    // 0xbbac14: scvtf           d0, x0
    // 0xbbac18: ldur            d1, [fp, #-0x28]
    // 0xbbac1c: fdiv            d2, d1, d0
    // 0xbbac20: r0 = inline_Allocate_Double()
    //     0xbbac20: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbbac24: add             x0, x0, #0x10
    //     0xbbac28: cmp             x1, x0
    //     0xbbac2c: b.ls            #0xbbace4
    //     0xbbac30: str             x0, [THR, #0x50]  ; THR::top
    //     0xbbac34: sub             x0, x0, #0xf
    //     0xbbac38: movz            x1, #0xe15c
    //     0xbbac3c: movk            x1, #0x3, lsl #16
    //     0xbbac40: stur            x1, [x0, #-1]
    // 0xbbac44: StoreField: r0->field_7 = d2
    //     0xbbac44: stur            d2, [x0, #7]
    // 0xbbac48: b               #0xbbac50
    // 0xbbac4c: r0 = Null
    //     0xbbac4c: mov             x0, NULL
    // 0xbbac50: ldur            x2, [fp, #-0x10]
    // 0xbbac54: ldur            x1, [fp, #-0x20]
    // 0xbbac58: StoreField: r2->field_13 = r0
    //     0xbbac58: stur            w0, [x2, #0x13]
    //     0xbbac5c: ldurb           w16, [x2, #-1]
    //     0xbbac60: ldurb           w17, [x0, #-1]
    //     0xbbac64: and             x16, x17, x16, lsr #2
    //     0xbbac68: tst             x16, HEAP, lsr #32
    //     0xbbac6c: b.eq            #0xbbac74
    //     0xbbac70: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbbac74: r0 = CalendarPage()
    //     0xbbac74: bl              #0xbbacf4  ; AllocateCalendarPageStub -> CalendarPage (size=0x28)
    // 0xbbac78: mov             x3, x0
    // 0xbbac7c: ldur            x0, [fp, #-0x20]
    // 0xbbac80: stur            x3, [fp, #-8]
    // 0xbbac84: StoreField: r3->field_f = r0
    //     0xbbac84: stur            w0, [x3, #0xf]
    // 0xbbac88: ldur            x2, [fp, #-0x10]
    // 0xbbac8c: r1 = Function '<anonymous closure>':.
    //     0xbbac8c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ae50] AnonymousClosure: (0xbbb610), in [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::build (0xbba6f0)
    //     0xbbac90: ldr             x1, [x1, #0xe50]
    // 0xbbac94: r0 = AllocateClosure()
    //     0xbbac94: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbac98: mov             x1, x0
    // 0xbbac9c: ldur            x0, [fp, #-8]
    // 0xbbaca0: StoreField: r0->field_b = r1
    //     0xbbaca0: stur            w1, [x0, #0xb]
    // 0xbbaca4: r1 = Instance_BoxDecoration
    //     0xbbaca4: add             x1, PP, #0x47, lsl #12  ; [pp+0x47e98] Obj!BoxDecoration@e1d1e1
    //     0xbbaca8: ldr             x1, [x1, #0xe98]
    // 0xbbacac: StoreField: r0->field_13 = r1
    //     0xbbacac: stur            w1, [x0, #0x13]
    // 0xbbacb0: r1 = Instance_TableBorder
    //     0xbbacb0: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ea0] Obj!TableBorder@e116b1
    //     0xbbacb4: ldr             x1, [x1, #0xea0]
    // 0xbbacb8: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbacb8: stur            w1, [x0, #0x17]
    // 0xbbacbc: r1 = Instance_EdgeInsets
    //     0xbbacbc: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbacc0: StoreField: r0->field_1b = r1
    //     0xbbacc0: stur            w1, [x0, #0x1b]
    // 0xbbacc4: r1 = false
    //     0xbbacc4: add             x1, NULL, #0x30  ; false
    // 0xbbacc8: StoreField: r0->field_1f = r1
    //     0xbbacc8: stur            w1, [x0, #0x1f]
    // 0xbbaccc: StoreField: r0->field_23 = r1
    //     0xbbaccc: stur            w1, [x0, #0x23]
    // 0xbbacd0: LeaveFrame
    //     0xbbacd0: mov             SP, fp
    //     0xbbacd4: ldp             fp, lr, [SP], #0x10
    // 0xbbacd8: ret
    //     0xbbacd8: ret             
    // 0xbbacdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbacdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbace0: b               #0xbbab48
    // 0xbbace4: SaveReg d2
    //     0xbbace4: str             q2, [SP, #-0x10]!
    // 0xbbace8: r0 = AllocateDouble()
    //     0xbbace8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbbacec: RestoreReg d2
    //     0xbbacec: ldr             q2, [SP], #0x10
    // 0xbbacf0: b               #0xbbac44
  }
  _ _getRowCount(/* No info */) {
    // ** addr: 0xbbad00, size: 0xf8
    // 0xbbad00: EnterFrame
    //     0xbbad00: stp             fp, lr, [SP, #-0x10]!
    //     0xbbad04: mov             fp, SP
    // 0xbbad08: AllocStack(0x20)
    //     0xbbad08: sub             SP, SP, #0x20
    // 0xbbad0c: SetupParameters(CalendarCore this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbbad0c: mov             x3, x1
    //     0xbbad10: mov             x0, x2
    //     0xbbad14: stur            x1, [fp, #-8]
    //     0xbbad18: stur            x2, [fp, #-0x10]
    // 0xbbad1c: CheckStackOverflow
    //     0xbbad1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbad20: cmp             SP, x16
    //     0xbbad24: b.ls            #0xbbadf0
    // 0xbbad28: mov             x1, x3
    // 0xbbad2c: mov             x2, x0
    // 0xbbad30: r0 = _firstDayOfMonth()
    //     0xbbad30: bl              #0xbbb104  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_firstDayOfMonth
    // 0xbbad34: ldur            x1, [fp, #-8]
    // 0xbbad38: mov             x2, x0
    // 0xbbad3c: stur            x0, [fp, #-0x18]
    // 0xbbad40: r0 = _getDaysBefore()
    //     0xbbad40: bl              #0xbbb068  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getDaysBefore
    // 0xbbad44: r16 = 86400000000
    //     0xbbad44: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xbbad48: ldr             x16, [x16, #0x268]
    // 0xbbad4c: mul             x1, x0, x16
    // 0xbbad50: stur            x1, [fp, #-0x20]
    // 0xbbad54: r0 = Duration()
    //     0xbbad54: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xbbad58: mov             x1, x0
    // 0xbbad5c: ldur            x0, [fp, #-0x20]
    // 0xbbad60: StoreField: r1->field_7 = r0
    //     0xbbad60: stur            x0, [x1, #7]
    // 0xbbad64: mov             x2, x1
    // 0xbbad68: ldur            x1, [fp, #-0x18]
    // 0xbbad6c: r0 = subtract()
    //     0xbbad6c: bl              #0x819a38  ; [dart:core] DateTime::subtract
    // 0xbbad70: ldur            x1, [fp, #-8]
    // 0xbbad74: ldur            x2, [fp, #-0x10]
    // 0xbbad78: stur            x0, [fp, #-0x10]
    // 0xbbad7c: r0 = _lastDayOfMonth()
    //     0xbbad7c: bl              #0xbbaebc  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_lastDayOfMonth
    // 0xbbad80: ldur            x1, [fp, #-8]
    // 0xbbad84: mov             x2, x0
    // 0xbbad88: stur            x0, [fp, #-8]
    // 0xbbad8c: r0 = _getDaysAfter()
    //     0xbbad8c: bl              #0xbbadf8  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getDaysAfter
    // 0xbbad90: r16 = 86400000000
    //     0xbbad90: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xbbad94: ldr             x16, [x16, #0x268]
    // 0xbbad98: mul             x1, x0, x16
    // 0xbbad9c: stur            x1, [fp, #-0x20]
    // 0xbbada0: r0 = Duration()
    //     0xbbada0: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xbbada4: mov             x1, x0
    // 0xbbada8: ldur            x0, [fp, #-0x20]
    // 0xbbadac: StoreField: r1->field_7 = r0
    //     0xbbadac: stur            x0, [x1, #7]
    // 0xbbadb0: mov             x2, x1
    // 0xbbadb4: ldur            x1, [fp, #-8]
    // 0xbbadb8: r0 = add()
    //     0xbbadb8: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0xbbadbc: mov             x1, x0
    // 0xbbadc0: ldur            x2, [fp, #-0x10]
    // 0xbbadc4: r0 = difference()
    //     0xbbadc4: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0xbbadc8: LoadField: r1 = r0->field_7
    //     0xbbadc8: ldur            x1, [x0, #7]
    // 0xbbadcc: r2 = 86400000000
    //     0xbbadcc: add             x2, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xbbadd0: ldr             x2, [x2, #0x268]
    // 0xbbadd4: sdiv            x3, x1, x2
    // 0xbbadd8: add             x1, x3, #1
    // 0xbbaddc: r2 = 7
    //     0xbbaddc: movz            x2, #0x7
    // 0xbbade0: sdiv            x0, x1, x2
    // 0xbbade4: LeaveFrame
    //     0xbbade4: mov             SP, fp
    //     0xbbade8: ldp             fp, lr, [SP], #0x10
    // 0xbbadec: ret
    //     0xbbadec: ret             
    // 0xbbadf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbadf0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbadf4: b               #0xbbad28
  }
  _ _getDaysAfter(/* No info */) {
    // ** addr: 0xbbadf8, size: 0xc4
    // 0xbbadf8: EnterFrame
    //     0xbbadf8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbadfc: mov             fp, SP
    // 0xbbae00: AllocStack(0x10)
    //     0xbbae00: sub             SP, SP, #0x10
    // 0xbbae04: SetupParameters(CalendarCore this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0xbbae04: mov             x0, x1
    //     0xbbae08: mov             x1, x2
    //     0xbbae0c: stur            x2, [fp, #-8]
    // 0xbbae10: CheckStackOverflow
    //     0xbbae10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbae14: cmp             SP, x16
    //     0xbbae18: b.ls            #0xbbaea8
    // 0xbbae1c: r0 = getWeekdayNumber()
    //     0xbbae1c: bl              #0x981768  ; [package:table_calendar/src/shared/utils.dart] ::getWeekdayNumber
    // 0xbbae20: mov             x1, x0
    // 0xbbae24: r0 = 8
    //     0xbbae24: movz            x0, #0x8
    // 0xbbae28: sub             x2, x0, x1
    // 0xbbae2c: ldur            x1, [fp, #-8]
    // 0xbbae30: stur            x2, [fp, #-0x10]
    // 0xbbae34: r0 = _parts()
    //     0xbbae34: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbae38: mov             x2, x0
    // 0xbbae3c: LoadField: r3 = r2->field_b
    //     0xbbae3c: ldur            w3, [x2, #0xb]
    // 0xbbae40: r0 = LoadInt32Instr(r3)
    //     0xbbae40: sbfx            x0, x3, #1, #0x1f
    // 0xbbae44: r1 = 6
    //     0xbbae44: movz            x1, #0x6
    // 0xbbae48: cmp             x1, x0
    // 0xbbae4c: b.hs            #0xbbaeb0
    // 0xbbae50: LoadField: r1 = r2->field_27
    //     0xbbae50: ldur            w1, [x2, #0x27]
    // 0xbbae54: DecompressPointer r1
    //     0xbbae54: add             x1, x1, HEAP, lsl #32
    // 0xbbae58: r2 = LoadInt32Instr(r1)
    //     0xbbae58: sbfx            x2, x1, #1, #0x1f
    //     0xbbae5c: tbz             w1, #0, #0xbbae64
    //     0xbbae60: ldur            x2, [x1, #7]
    // 0xbbae64: ldur            x1, [fp, #-0x10]
    // 0xbbae68: add             x3, x2, x1
    // 0xbbae6c: r1 = 7
    //     0xbbae6c: movz            x1, #0x7
    // 0xbbae70: sdiv            x4, x3, x1
    // 0xbbae74: msub            x2, x4, x1, x3
    // 0xbbae78: cmp             x2, xzr
    // 0xbbae7c: b.lt            #0xbbaeb4
    // 0xbbae80: sub             x0, x1, x2
    // 0xbbae84: cmp             x0, #7
    // 0xbbae88: b.ne            #0xbbae9c
    // 0xbbae8c: r0 = 0
    //     0xbbae8c: movz            x0, #0
    // 0xbbae90: LeaveFrame
    //     0xbbae90: mov             SP, fp
    //     0xbbae94: ldp             fp, lr, [SP], #0x10
    // 0xbbae98: ret
    //     0xbbae98: ret             
    // 0xbbae9c: LeaveFrame
    //     0xbbae9c: mov             SP, fp
    //     0xbbaea0: ldp             fp, lr, [SP], #0x10
    // 0xbbaea4: ret
    //     0xbbaea4: ret             
    // 0xbbaea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbaea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbaeac: b               #0xbbae1c
    // 0xbbaeb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbaeb0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbaeb4: add             x2, x2, x1
    // 0xbbaeb8: b               #0xbbae80
  }
  _ _lastDayOfMonth(/* No info */) {
    // ** addr: 0xbbaebc, size: 0x1ac
    // 0xbbaebc: EnterFrame
    //     0xbbaebc: stp             fp, lr, [SP, #-0x10]!
    //     0xbbaec0: mov             fp, SP
    // 0xbbaec4: AllocStack(0x28)
    //     0xbbaec4: sub             SP, SP, #0x28
    // 0xbbaec8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xbbaec8: mov             x0, x2
    //     0xbbaecc: stur            x2, [fp, #-8]
    // 0xbbaed0: CheckStackOverflow
    //     0xbbaed0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbaed4: cmp             SP, x16
    //     0xbbaed8: b.ls            #0xbbb050
    // 0xbbaedc: mov             x1, x0
    // 0xbbaee0: r0 = _parts()
    //     0xbbaee0: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbaee4: mov             x2, x0
    // 0xbbaee8: LoadField: r0 = r2->field_b
    //     0xbbaee8: ldur            w0, [x2, #0xb]
    // 0xbbaeec: r1 = LoadInt32Instr(r0)
    //     0xbbaeec: sbfx            x1, x0, #1, #0x1f
    // 0xbbaef0: mov             x0, x1
    // 0xbbaef4: r1 = 7
    //     0xbbaef4: movz            x1, #0x7
    // 0xbbaef8: cmp             x1, x0
    // 0xbbaefc: b.hs            #0xbbb058
    // 0xbbaf00: LoadField: r0 = r2->field_2b
    //     0xbbaf00: ldur            w0, [x2, #0x2b]
    // 0xbbaf04: DecompressPointer r0
    //     0xbbaf04: add             x0, x0, HEAP, lsl #32
    // 0xbbaf08: r1 = LoadInt32Instr(r0)
    //     0xbbaf08: sbfx            x1, x0, #1, #0x1f
    //     0xbbaf0c: tbz             w0, #0, #0xbbaf14
    //     0xbbaf10: ldur            x1, [x0, #7]
    // 0xbbaf14: cmp             x1, #0xc
    // 0xbbaf18: b.ge            #0xbbafdc
    // 0xbbaf1c: ldur            x1, [fp, #-8]
    // 0xbbaf20: r0 = _parts()
    //     0xbbaf20: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbaf24: mov             x2, x0
    // 0xbbaf28: LoadField: r0 = r2->field_b
    //     0xbbaf28: ldur            w0, [x2, #0xb]
    // 0xbbaf2c: r1 = LoadInt32Instr(r0)
    //     0xbbaf2c: sbfx            x1, x0, #1, #0x1f
    // 0xbbaf30: mov             x0, x1
    // 0xbbaf34: r1 = 8
    //     0xbbaf34: movz            x1, #0x8
    // 0xbbaf38: cmp             x1, x0
    // 0xbbaf3c: b.hs            #0xbbb05c
    // 0xbbaf40: LoadField: r0 = r2->field_2f
    //     0xbbaf40: ldur            w0, [x2, #0x2f]
    // 0xbbaf44: DecompressPointer r0
    //     0xbbaf44: add             x0, x0, HEAP, lsl #32
    // 0xbbaf48: ldur            x1, [fp, #-8]
    // 0xbbaf4c: stur            x0, [fp, #-0x10]
    // 0xbbaf50: r0 = _parts()
    //     0xbbaf50: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbaf54: mov             x2, x0
    // 0xbbaf58: LoadField: r0 = r2->field_b
    //     0xbbaf58: ldur            w0, [x2, #0xb]
    // 0xbbaf5c: r1 = LoadInt32Instr(r0)
    //     0xbbaf5c: sbfx            x1, x0, #1, #0x1f
    // 0xbbaf60: mov             x0, x1
    // 0xbbaf64: r1 = 7
    //     0xbbaf64: movz            x1, #0x7
    // 0xbbaf68: cmp             x1, x0
    // 0xbbaf6c: b.hs            #0xbbb060
    // 0xbbaf70: LoadField: r0 = r2->field_2b
    //     0xbbaf70: ldur            w0, [x2, #0x2b]
    // 0xbbaf74: DecompressPointer r0
    //     0xbbaf74: add             x0, x0, HEAP, lsl #32
    // 0xbbaf78: r1 = LoadInt32Instr(r0)
    //     0xbbaf78: sbfx            x1, x0, #1, #0x1f
    //     0xbbaf7c: tbz             w0, #0, #0xbbaf84
    //     0xbbaf80: ldur            x1, [x0, #7]
    // 0xbbaf84: add             x2, x1, #1
    // 0xbbaf88: ldur            x0, [fp, #-0x10]
    // 0xbbaf8c: r3 = LoadInt32Instr(r0)
    //     0xbbaf8c: sbfx            x3, x0, #1, #0x1f
    //     0xbbaf90: tbz             w0, #0, #0xbbaf98
    //     0xbbaf94: ldur            x3, [x0, #7]
    // 0xbbaf98: stur            x3, [fp, #-0x18]
    // 0xbbaf9c: r0 = BoxInt64Instr(r2)
    //     0xbbaf9c: sbfiz           x0, x2, #1, #0x1f
    //     0xbbafa0: cmp             x2, x0, asr #1
    //     0xbbafa4: b.eq            #0xbbafb0
    //     0xbbafa8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbafac: stur            x2, [x0, #7]
    // 0xbbafb0: stur            x0, [fp, #-0x10]
    // 0xbbafb4: r0 = DateTime()
    //     0xbbafb4: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbbafb8: stur            x0, [fp, #-0x20]
    // 0xbbafbc: ldur            x16, [fp, #-0x10]
    // 0xbbafc0: str             x16, [SP]
    // 0xbbafc4: mov             x1, x0
    // 0xbbafc8: ldur            x2, [fp, #-0x18]
    // 0xbbafcc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbbafcc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbbafd0: r0 = DateTime.utc()
    //     0xbbafd0: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xbbafd4: ldur            x1, [fp, #-0x20]
    // 0xbbafd8: b               #0xbbb038
    // 0xbbafdc: ldur            x1, [fp, #-8]
    // 0xbbafe0: r0 = _parts()
    //     0xbbafe0: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbafe4: mov             x2, x0
    // 0xbbafe8: LoadField: r0 = r2->field_b
    //     0xbbafe8: ldur            w0, [x2, #0xb]
    // 0xbbafec: r1 = LoadInt32Instr(r0)
    //     0xbbafec: sbfx            x1, x0, #1, #0x1f
    // 0xbbaff0: mov             x0, x1
    // 0xbbaff4: r1 = 8
    //     0xbbaff4: movz            x1, #0x8
    // 0xbbaff8: cmp             x1, x0
    // 0xbbaffc: b.hs            #0xbbb064
    // 0xbbb000: LoadField: r0 = r2->field_2f
    //     0xbbb000: ldur            w0, [x2, #0x2f]
    // 0xbbb004: DecompressPointer r0
    //     0xbbb004: add             x0, x0, HEAP, lsl #32
    // 0xbbb008: r1 = LoadInt32Instr(r0)
    //     0xbbb008: sbfx            x1, x0, #1, #0x1f
    //     0xbbb00c: tbz             w0, #0, #0xbbb014
    //     0xbbb010: ldur            x1, [x0, #7]
    // 0xbbb014: add             x2, x1, #1
    // 0xbbb018: stur            x2, [fp, #-0x18]
    // 0xbbb01c: r0 = DateTime()
    //     0xbbb01c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbbb020: mov             x1, x0
    // 0xbbb024: ldur            x2, [fp, #-0x18]
    // 0xbbb028: stur            x0, [fp, #-8]
    // 0xbbb02c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbb02c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbb030: r0 = DateTime.utc()
    //     0xbbb030: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xbbb034: ldur            x1, [fp, #-8]
    // 0xbbb038: r2 = Instance_Duration
    //     0xbbb038: add             x2, PP, #9, lsl #12  ; [pp+0x92d8] Obj!Duration@e3a141
    //     0xbbb03c: ldr             x2, [x2, #0x2d8]
    // 0xbbb040: r0 = subtract()
    //     0xbbb040: bl              #0x819a38  ; [dart:core] DateTime::subtract
    // 0xbbb044: LeaveFrame
    //     0xbbb044: mov             SP, fp
    //     0xbbb048: ldp             fp, lr, [SP], #0x10
    // 0xbbb04c: ret
    //     0xbbb04c: ret             
    // 0xbbb050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb054: b               #0xbbaedc
    // 0xbbb058: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb058: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb05c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb05c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb060: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb060: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb064: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb064: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _getDaysBefore(/* No info */) {
    // ** addr: 0xbbb068, size: 0x9c
    // 0xbbb068: EnterFrame
    //     0xbbb068: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb06c: mov             fp, SP
    // 0xbbb070: AllocStack(0x8)
    //     0xbbb070: sub             SP, SP, #8
    // 0xbbb074: SetupParameters(CalendarCore this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xbbb074: mov             x0, x1
    //     0xbbb078: mov             x1, x2
    // 0xbbb07c: CheckStackOverflow
    //     0xbbb07c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb080: cmp             SP, x16
    //     0xbbb084: b.ls            #0xbbb0f0
    // 0xbbb088: r0 = _parts()
    //     0xbbb088: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb08c: mov             x2, x0
    // 0xbbb090: LoadField: r0 = r2->field_b
    //     0xbbb090: ldur            w0, [x2, #0xb]
    // 0xbbb094: r1 = LoadInt32Instr(r0)
    //     0xbbb094: sbfx            x1, x0, #1, #0x1f
    // 0xbbb098: mov             x0, x1
    // 0xbbb09c: r1 = 6
    //     0xbbb09c: movz            x1, #0x6
    // 0xbbb0a0: cmp             x1, x0
    // 0xbbb0a4: b.hs            #0xbbb0f8
    // 0xbbb0a8: LoadField: r0 = r2->field_27
    //     0xbbb0a8: ldur            w0, [x2, #0x27]
    // 0xbbb0ac: DecompressPointer r0
    //     0xbbb0ac: add             x0, x0, HEAP, lsl #32
    // 0xbbb0b0: r1 = LoadInt32Instr(r0)
    //     0xbbb0b0: sbfx            x1, x0, #1, #0x1f
    //     0xbbb0b4: tbz             w0, #0, #0xbbb0bc
    //     0xbbb0b8: ldur            x1, [x0, #7]
    // 0xbbb0bc: add             x0, x1, #7
    // 0xbbb0c0: stur            x0, [fp, #-8]
    // 0xbbb0c4: r0 = getWeekdayNumber()
    //     0xbbb0c4: bl              #0x981768  ; [package:table_calendar/src/shared/utils.dart] ::getWeekdayNumber
    // 0xbbb0c8: ldur            x1, [fp, #-8]
    // 0xbbb0cc: sub             x2, x1, x0
    // 0xbbb0d0: r1 = 7
    //     0xbbb0d0: movz            x1, #0x7
    // 0xbbb0d4: sdiv            x3, x2, x1
    // 0xbbb0d8: msub            x0, x3, x1, x2
    // 0xbbb0dc: cmp             x0, xzr
    // 0xbbb0e0: b.lt            #0xbbb0fc
    // 0xbbb0e4: LeaveFrame
    //     0xbbb0e4: mov             SP, fp
    //     0xbbb0e8: ldp             fp, lr, [SP], #0x10
    // 0xbbb0ec: ret
    //     0xbbb0ec: ret             
    // 0xbbb0f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb0f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb0f4: b               #0xbbb088
    // 0xbbb0f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb0f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb0fc: add             x0, x0, x1
    // 0xbbb100: b               #0xbbb0e4
  }
  _ _firstDayOfMonth(/* No info */) {
    // ** addr: 0xbbb104, size: 0xd4
    // 0xbbb104: EnterFrame
    //     0xbbb104: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb108: mov             fp, SP
    // 0xbbb10c: AllocStack(0x20)
    //     0xbbb10c: sub             SP, SP, #0x20
    // 0xbbb110: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xbbb110: mov             x0, x2
    //     0xbbb114: stur            x2, [fp, #-8]
    // 0xbbb118: CheckStackOverflow
    //     0xbbb118: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb11c: cmp             SP, x16
    //     0xbbb120: b.ls            #0xbbb1c8
    // 0xbbb124: mov             x1, x0
    // 0xbbb128: r0 = _parts()
    //     0xbbb128: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb12c: mov             x2, x0
    // 0xbbb130: LoadField: r0 = r2->field_b
    //     0xbbb130: ldur            w0, [x2, #0xb]
    // 0xbbb134: r1 = LoadInt32Instr(r0)
    //     0xbbb134: sbfx            x1, x0, #1, #0x1f
    // 0xbbb138: mov             x0, x1
    // 0xbbb13c: r1 = 8
    //     0xbbb13c: movz            x1, #0x8
    // 0xbbb140: cmp             x1, x0
    // 0xbbb144: b.hs            #0xbbb1d0
    // 0xbbb148: LoadField: r0 = r2->field_2f
    //     0xbbb148: ldur            w0, [x2, #0x2f]
    // 0xbbb14c: DecompressPointer r0
    //     0xbbb14c: add             x0, x0, HEAP, lsl #32
    // 0xbbb150: ldur            x1, [fp, #-8]
    // 0xbbb154: stur            x0, [fp, #-0x10]
    // 0xbbb158: r0 = _parts()
    //     0xbbb158: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb15c: mov             x2, x0
    // 0xbbb160: LoadField: r0 = r2->field_b
    //     0xbbb160: ldur            w0, [x2, #0xb]
    // 0xbbb164: r1 = LoadInt32Instr(r0)
    //     0xbbb164: sbfx            x1, x0, #1, #0x1f
    // 0xbbb168: mov             x0, x1
    // 0xbbb16c: r1 = 7
    //     0xbbb16c: movz            x1, #0x7
    // 0xbbb170: cmp             x1, x0
    // 0xbbb174: b.hs            #0xbbb1d4
    // 0xbbb178: LoadField: r0 = r2->field_2b
    //     0xbbb178: ldur            w0, [x2, #0x2b]
    // 0xbbb17c: DecompressPointer r0
    //     0xbbb17c: add             x0, x0, HEAP, lsl #32
    // 0xbbb180: ldur            x1, [fp, #-0x10]
    // 0xbbb184: stur            x0, [fp, #-8]
    // 0xbbb188: r2 = LoadInt32Instr(r1)
    //     0xbbb188: sbfx            x2, x1, #1, #0x1f
    //     0xbbb18c: tbz             w1, #0, #0xbbb194
    //     0xbbb190: ldur            x2, [x1, #7]
    // 0xbbb194: stur            x2, [fp, #-0x18]
    // 0xbbb198: r0 = DateTime()
    //     0xbbb198: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbbb19c: stur            x0, [fp, #-0x10]
    // 0xbbb1a0: ldur            x16, [fp, #-8]
    // 0xbbb1a4: str             x16, [SP]
    // 0xbbb1a8: mov             x1, x0
    // 0xbbb1ac: ldur            x2, [fp, #-0x18]
    // 0xbbb1b0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbbb1b0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbbb1b4: r0 = DateTime.utc()
    //     0xbbb1b4: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xbbb1b8: ldur            x0, [fp, #-0x10]
    // 0xbbb1bc: LeaveFrame
    //     0xbbb1bc: mov             SP, fp
    //     0xbbb1c0: ldp             fp, lr, [SP], #0x10
    // 0xbbb1c4: ret
    //     0xbbb1c4: ret             
    // 0xbbb1c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb1c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb1cc: b               #0xbbb124
    // 0xbbb1d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb1d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb1d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb1d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _daysInRange(/* No info */) {
    // ** addr: 0xbbb1d8, size: 0x1f4
    // 0xbbb1d8: EnterFrame
    //     0xbbb1d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb1dc: mov             fp, SP
    // 0xbbb1e0: AllocStack(0x58)
    //     0xbbb1e0: sub             SP, SP, #0x58
    // 0xbbb1e4: SetupParameters(CalendarCore this /* r1 => r2 */, dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r1 */)
    //     0xbbb1e4: mov             x0, x2
    //     0xbbb1e8: stur            x2, [fp, #-8]
    //     0xbbb1ec: mov             x2, x1
    //     0xbbb1f0: mov             x1, x3
    // 0xbbb1f4: CheckStackOverflow
    //     0xbbb1f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb1f8: cmp             SP, x16
    //     0xbbb1fc: b.ls            #0xbbb3b0
    // 0xbbb200: mov             x2, x0
    // 0xbbb204: r0 = difference()
    //     0xbbb204: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0xbbb208: LoadField: r1 = r0->field_7
    //     0xbbb208: ldur            x1, [x0, #7]
    // 0xbbb20c: r0 = 86400000000
    //     0xbbb20c: add             x0, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xbbb210: ldr             x0, [x0, #0x268]
    // 0xbbb214: sdiv            x2, x1, x0
    // 0xbbb218: add             x0, x2, #1
    // 0xbbb21c: mov             x2, x0
    // 0xbbb220: r1 = <DateTime>
    //     0xbbb220: add             x1, PP, #0xb, lsl #12  ; [pp+0xbdd8] TypeArguments: <DateTime>
    //     0xbbb224: ldr             x1, [x1, #0xdd8]
    // 0xbbb228: r0 = _GrowableList()
    //     0xbbb228: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbbb22c: stur            x0, [fp, #-0x28]
    // 0xbbb230: LoadField: r1 = r0->field_b
    //     0xbbb230: ldur            w1, [x0, #0xb]
    // 0xbbb234: r2 = LoadInt32Instr(r1)
    //     0xbbb234: sbfx            x2, x1, #1, #0x1f
    // 0xbbb238: stur            x2, [fp, #-0x20]
    // 0xbbb23c: LoadField: r3 = r0->field_f
    //     0xbbb23c: ldur            w3, [x0, #0xf]
    // 0xbbb240: DecompressPointer r3
    //     0xbbb240: add             x3, x3, HEAP, lsl #32
    // 0xbbb244: stur            x3, [fp, #-0x18]
    // 0xbbb248: r4 = 0
    //     0xbbb248: movz            x4, #0
    // 0xbbb24c: stur            x4, [fp, #-0x10]
    // 0xbbb250: CheckStackOverflow
    //     0xbbb250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb254: cmp             SP, x16
    //     0xbbb258: b.ls            #0xbbb3b8
    // 0xbbb25c: cmp             x4, x2
    // 0xbbb260: b.ge            #0xbbb3a0
    // 0xbbb264: ldur            x1, [fp, #-8]
    // 0xbbb268: r0 = _parts()
    //     0xbbb268: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb26c: mov             x2, x0
    // 0xbbb270: LoadField: r0 = r2->field_b
    //     0xbbb270: ldur            w0, [x2, #0xb]
    // 0xbbb274: r1 = LoadInt32Instr(r0)
    //     0xbbb274: sbfx            x1, x0, #1, #0x1f
    // 0xbbb278: mov             x0, x1
    // 0xbbb27c: r1 = 8
    //     0xbbb27c: movz            x1, #0x8
    // 0xbbb280: cmp             x1, x0
    // 0xbbb284: b.hs            #0xbbb3c0
    // 0xbbb288: LoadField: r0 = r2->field_2f
    //     0xbbb288: ldur            w0, [x2, #0x2f]
    // 0xbbb28c: DecompressPointer r0
    //     0xbbb28c: add             x0, x0, HEAP, lsl #32
    // 0xbbb290: ldur            x1, [fp, #-8]
    // 0xbbb294: stur            x0, [fp, #-0x30]
    // 0xbbb298: r0 = _parts()
    //     0xbbb298: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb29c: mov             x2, x0
    // 0xbbb2a0: LoadField: r0 = r2->field_b
    //     0xbbb2a0: ldur            w0, [x2, #0xb]
    // 0xbbb2a4: r1 = LoadInt32Instr(r0)
    //     0xbbb2a4: sbfx            x1, x0, #1, #0x1f
    // 0xbbb2a8: mov             x0, x1
    // 0xbbb2ac: r1 = 7
    //     0xbbb2ac: movz            x1, #0x7
    // 0xbbb2b0: cmp             x1, x0
    // 0xbbb2b4: b.hs            #0xbbb3c4
    // 0xbbb2b8: LoadField: r0 = r2->field_2b
    //     0xbbb2b8: ldur            w0, [x2, #0x2b]
    // 0xbbb2bc: DecompressPointer r0
    //     0xbbb2bc: add             x0, x0, HEAP, lsl #32
    // 0xbbb2c0: ldur            x1, [fp, #-8]
    // 0xbbb2c4: stur            x0, [fp, #-0x38]
    // 0xbbb2c8: r0 = _parts()
    //     0xbbb2c8: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb2cc: mov             x2, x0
    // 0xbbb2d0: LoadField: r0 = r2->field_b
    //     0xbbb2d0: ldur            w0, [x2, #0xb]
    // 0xbbb2d4: r1 = LoadInt32Instr(r0)
    //     0xbbb2d4: sbfx            x1, x0, #1, #0x1f
    // 0xbbb2d8: mov             x0, x1
    // 0xbbb2dc: r1 = 5
    //     0xbbb2dc: movz            x1, #0x5
    // 0xbbb2e0: cmp             x1, x0
    // 0xbbb2e4: b.hs            #0xbbb3c8
    // 0xbbb2e8: LoadField: r0 = r2->field_23
    //     0xbbb2e8: ldur            w0, [x2, #0x23]
    // 0xbbb2ec: DecompressPointer r0
    //     0xbbb2ec: add             x0, x0, HEAP, lsl #32
    // 0xbbb2f0: r1 = LoadInt32Instr(r0)
    //     0xbbb2f0: sbfx            x1, x0, #1, #0x1f
    //     0xbbb2f4: tbz             w0, #0, #0xbbb2fc
    //     0xbbb2f8: ldur            x1, [x0, #7]
    // 0xbbb2fc: ldur            x2, [fp, #-0x10]
    // 0xbbb300: add             x3, x1, x2
    // 0xbbb304: ldur            x0, [fp, #-0x30]
    // 0xbbb308: r4 = LoadInt32Instr(r0)
    //     0xbbb308: sbfx            x4, x0, #1, #0x1f
    //     0xbbb30c: tbz             w0, #0, #0xbbb314
    //     0xbbb310: ldur            x4, [x0, #7]
    // 0xbbb314: stur            x4, [fp, #-0x40]
    // 0xbbb318: r0 = BoxInt64Instr(r3)
    //     0xbbb318: sbfiz           x0, x3, #1, #0x1f
    //     0xbbb31c: cmp             x3, x0, asr #1
    //     0xbbb320: b.eq            #0xbbb32c
    //     0xbbb324: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbb328: stur            x3, [x0, #7]
    // 0xbbb32c: stur            x0, [fp, #-0x30]
    // 0xbbb330: r0 = DateTime()
    //     0xbbb330: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbbb334: stur            x0, [fp, #-0x48]
    // 0xbbb338: ldur            x16, [fp, #-0x38]
    // 0xbbb33c: ldur            lr, [fp, #-0x30]
    // 0xbbb340: stp             lr, x16, [SP]
    // 0xbbb344: mov             x1, x0
    // 0xbbb348: ldur            x2, [fp, #-0x40]
    // 0xbbb34c: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xbbb34c: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xbbb350: ldr             x4, [x4, #0xe00]
    // 0xbbb354: r0 = DateTime.utc()
    //     0xbbb354: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xbbb358: ldur            x1, [fp, #-0x18]
    // 0xbbb35c: ldur            x0, [fp, #-0x48]
    // 0xbbb360: ldur            x2, [fp, #-0x10]
    // 0xbbb364: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbbb364: add             x25, x1, x2, lsl #2
    //     0xbbb368: add             x25, x25, #0xf
    //     0xbbb36c: str             w0, [x25]
    //     0xbbb370: tbz             w0, #0, #0xbbb38c
    //     0xbbb374: ldurb           w16, [x1, #-1]
    //     0xbbb378: ldurb           w17, [x0, #-1]
    //     0xbbb37c: and             x16, x17, x16, lsr #2
    //     0xbbb380: tst             x16, HEAP, lsr #32
    //     0xbbb384: b.eq            #0xbbb38c
    //     0xbbb388: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbb38c: add             x4, x2, #1
    // 0xbbb390: ldur            x0, [fp, #-0x28]
    // 0xbbb394: ldur            x3, [fp, #-0x18]
    // 0xbbb398: ldur            x2, [fp, #-0x20]
    // 0xbbb39c: b               #0xbbb24c
    // 0xbbb3a0: ldur            x0, [fp, #-0x28]
    // 0xbbb3a4: LeaveFrame
    //     0xbbb3a4: mov             SP, fp
    //     0xbbb3a8: ldp             fp, lr, [SP], #0x10
    // 0xbbb3ac: ret
    //     0xbbb3ac: ret             
    // 0xbbb3b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb3b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb3b4: b               #0xbbb200
    // 0xbbb3b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb3b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb3bc: b               #0xbbb25c
    // 0xbbb3c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb3c0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb3c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb3c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb3c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb3c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _daysInMonth(/* No info */) {
    // ** addr: 0xbbb3cc, size: 0xe8
    // 0xbbb3cc: EnterFrame
    //     0xbbb3cc: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb3d0: mov             fp, SP
    // 0xbbb3d4: AllocStack(0x20)
    //     0xbbb3d4: sub             SP, SP, #0x20
    // 0xbbb3d8: SetupParameters(CalendarCore this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbbb3d8: mov             x3, x1
    //     0xbbb3dc: mov             x0, x2
    //     0xbbb3e0: stur            x1, [fp, #-8]
    //     0xbbb3e4: stur            x2, [fp, #-0x10]
    // 0xbbb3e8: CheckStackOverflow
    //     0xbbb3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb3ec: cmp             SP, x16
    //     0xbbb3f0: b.ls            #0xbbb4ac
    // 0xbbb3f4: mov             x1, x3
    // 0xbbb3f8: mov             x2, x0
    // 0xbbb3fc: r0 = _firstDayOfMonth()
    //     0xbbb3fc: bl              #0xbbb104  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_firstDayOfMonth
    // 0xbbb400: ldur            x1, [fp, #-8]
    // 0xbbb404: mov             x2, x0
    // 0xbbb408: stur            x0, [fp, #-0x18]
    // 0xbbb40c: r0 = _getDaysBefore()
    //     0xbbb40c: bl              #0xbbb068  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getDaysBefore
    // 0xbbb410: r16 = 86400000000
    //     0xbbb410: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xbbb414: ldr             x16, [x16, #0x268]
    // 0xbbb418: mul             x1, x0, x16
    // 0xbbb41c: stur            x1, [fp, #-0x20]
    // 0xbbb420: r0 = Duration()
    //     0xbbb420: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xbbb424: mov             x1, x0
    // 0xbbb428: ldur            x0, [fp, #-0x20]
    // 0xbbb42c: StoreField: r1->field_7 = r0
    //     0xbbb42c: stur            x0, [x1, #7]
    // 0xbbb430: mov             x2, x1
    // 0xbbb434: ldur            x1, [fp, #-0x18]
    // 0xbbb438: r0 = subtract()
    //     0xbbb438: bl              #0x819a38  ; [dart:core] DateTime::subtract
    // 0xbbb43c: ldur            x1, [fp, #-8]
    // 0xbbb440: ldur            x2, [fp, #-0x10]
    // 0xbbb444: stur            x0, [fp, #-0x10]
    // 0xbbb448: r0 = _lastDayOfMonth()
    //     0xbbb448: bl              #0xbbaebc  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_lastDayOfMonth
    // 0xbbb44c: ldur            x1, [fp, #-8]
    // 0xbbb450: mov             x2, x0
    // 0xbbb454: stur            x0, [fp, #-8]
    // 0xbbb458: r0 = _getDaysAfter()
    //     0xbbb458: bl              #0xbbadf8  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getDaysAfter
    // 0xbbb45c: r16 = 86400000000
    //     0xbbb45c: add             x16, PP, #8, lsl #12  ; [pp+0x8268] IMM: 0x141dd76000
    //     0xbbb460: ldr             x16, [x16, #0x268]
    // 0xbbb464: mul             x1, x0, x16
    // 0xbbb468: stur            x1, [fp, #-0x20]
    // 0xbbb46c: r0 = Duration()
    //     0xbbb46c: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xbbb470: mov             x1, x0
    // 0xbbb474: ldur            x0, [fp, #-0x20]
    // 0xbbb478: StoreField: r1->field_7 = r0
    //     0xbbb478: stur            x0, [x1, #7]
    // 0xbbb47c: mov             x2, x1
    // 0xbbb480: ldur            x1, [fp, #-8]
    // 0xbbb484: r0 = add()
    //     0xbbb484: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0xbbb488: stur            x0, [fp, #-8]
    // 0xbbb48c: r0 = DateTimeRange()
    //     0xbbb48c: bl              #0xbbb4b4  ; AllocateDateTimeRangeStub -> DateTimeRange (size=0x10)
    // 0xbbb490: ldur            x1, [fp, #-0x10]
    // 0xbbb494: StoreField: r0->field_7 = r1
    //     0xbbb494: stur            w1, [x0, #7]
    // 0xbbb498: ldur            x1, [fp, #-8]
    // 0xbbb49c: StoreField: r0->field_b = r1
    //     0xbbb49c: stur            w1, [x0, #0xb]
    // 0xbbb4a0: LeaveFrame
    //     0xbbb4a0: mov             SP, fp
    //     0xbbb4a4: ldp             fp, lr, [SP], #0x10
    // 0xbbb4a8: ret
    //     0xbbb4a8: ret             
    // 0xbbb4ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb4ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb4b0: b               #0xbbb3f4
  }
  _ _getBaseDay(/* No info */) {
    // ** addr: 0xbbb4c0, size: 0x150
    // 0xbbb4c0: EnterFrame
    //     0xbbb4c0: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb4c4: mov             fp, SP
    // 0xbbb4c8: AllocStack(0x30)
    //     0xbbb4c8: sub             SP, SP, #0x30
    // 0xbbb4cc: SetupParameters(CalendarCore this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xbbb4cc: mov             x0, x1
    //     0xbbb4d0: stur            x1, [fp, #-0x10]
    //     0xbbb4d4: stur            x2, [fp, #-0x18]
    // 0xbbb4d8: CheckStackOverflow
    //     0xbbb4d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb4dc: cmp             SP, x16
    //     0xbbb4e0: b.ls            #0xbbb600
    // 0xbbb4e4: LoadField: r3 = r0->field_f
    //     0xbbb4e4: ldur            w3, [x0, #0xf]
    // 0xbbb4e8: DecompressPointer r3
    //     0xbbb4e8: add             x3, x3, HEAP, lsl #32
    // 0xbbb4ec: mov             x1, x3
    // 0xbbb4f0: stur            x3, [fp, #-8]
    // 0xbbb4f4: r0 = _parts()
    //     0xbbb4f4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb4f8: mov             x2, x0
    // 0xbbb4fc: LoadField: r0 = r2->field_b
    //     0xbbb4fc: ldur            w0, [x2, #0xb]
    // 0xbbb500: r1 = LoadInt32Instr(r0)
    //     0xbbb500: sbfx            x1, x0, #1, #0x1f
    // 0xbbb504: mov             x0, x1
    // 0xbbb508: r1 = 8
    //     0xbbb508: movz            x1, #0x8
    // 0xbbb50c: cmp             x1, x0
    // 0xbbb510: b.hs            #0xbbb608
    // 0xbbb514: LoadField: r0 = r2->field_2f
    //     0xbbb514: ldur            w0, [x2, #0x2f]
    // 0xbbb518: DecompressPointer r0
    //     0xbbb518: add             x0, x0, HEAP, lsl #32
    // 0xbbb51c: ldur            x1, [fp, #-8]
    // 0xbbb520: stur            x0, [fp, #-0x20]
    // 0xbbb524: r0 = _parts()
    //     0xbbb524: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xbbb528: mov             x2, x0
    // 0xbbb52c: LoadField: r0 = r2->field_b
    //     0xbbb52c: ldur            w0, [x2, #0xb]
    // 0xbbb530: r1 = LoadInt32Instr(r0)
    //     0xbbb530: sbfx            x1, x0, #1, #0x1f
    // 0xbbb534: mov             x0, x1
    // 0xbbb538: r1 = 7
    //     0xbbb538: movz            x1, #0x7
    // 0xbbb53c: cmp             x1, x0
    // 0xbbb540: b.hs            #0xbbb60c
    // 0xbbb544: LoadField: r0 = r2->field_2b
    //     0xbbb544: ldur            w0, [x2, #0x2b]
    // 0xbbb548: DecompressPointer r0
    //     0xbbb548: add             x0, x0, HEAP, lsl #32
    // 0xbbb54c: r1 = LoadInt32Instr(r0)
    //     0xbbb54c: sbfx            x1, x0, #1, #0x1f
    //     0xbbb550: tbz             w0, #0, #0xbbb558
    //     0xbbb554: ldur            x1, [x0, #7]
    // 0xbbb558: ldur            x0, [fp, #-0x18]
    // 0xbbb55c: add             x2, x1, x0
    // 0xbbb560: ldur            x0, [fp, #-0x20]
    // 0xbbb564: r3 = LoadInt32Instr(r0)
    //     0xbbb564: sbfx            x3, x0, #1, #0x1f
    //     0xbbb568: tbz             w0, #0, #0xbbb570
    //     0xbbb56c: ldur            x3, [x0, #7]
    // 0xbbb570: stur            x3, [fp, #-0x18]
    // 0xbbb574: r0 = BoxInt64Instr(r2)
    //     0xbbb574: sbfiz           x0, x2, #1, #0x1f
    //     0xbbb578: cmp             x2, x0, asr #1
    //     0xbbb57c: b.eq            #0xbbb588
    //     0xbbb580: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbb584: stur            x2, [x0, #7]
    // 0xbbb588: stur            x0, [fp, #-0x20]
    // 0xbbb58c: r0 = DateTime()
    //     0xbbb58c: bl              #0x6fe134  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xbbb590: stur            x0, [fp, #-0x28]
    // 0xbbb594: ldur            x16, [fp, #-0x20]
    // 0xbbb598: str             x16, [SP]
    // 0xbbb59c: mov             x1, x0
    // 0xbbb5a0: ldur            x2, [fp, #-0x18]
    // 0xbbb5a4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbbb5a4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbbb5a8: r0 = DateTime.utc()
    //     0xbbb5a8: bl              #0x82c174  ; [dart:core] DateTime::DateTime.utc
    // 0xbbb5ac: ldur            x1, [fp, #-0x28]
    // 0xbbb5b0: ldur            x2, [fp, #-8]
    // 0xbbb5b4: r0 = isBefore()
    //     0xbbb5b4: bl              #0xd5ddd0  ; [dart:core] DateTime::isBefore
    // 0xbbb5b8: tbnz            w0, #4, #0xbbb5c4
    // 0xbbb5bc: ldur            x0, [fp, #-8]
    // 0xbbb5c0: b               #0xbbb5f4
    // 0xbbb5c4: ldur            x0, [fp, #-0x10]
    // 0xbbb5c8: LoadField: r3 = r0->field_13
    //     0xbbb5c8: ldur            w3, [x0, #0x13]
    // 0xbbb5cc: DecompressPointer r3
    //     0xbbb5cc: add             x3, x3, HEAP, lsl #32
    // 0xbbb5d0: ldur            x1, [fp, #-0x28]
    // 0xbbb5d4: mov             x2, x3
    // 0xbbb5d8: stur            x3, [fp, #-8]
    // 0xbbb5dc: r0 = isAfter()
    //     0xbbb5dc: bl              #0xd61fd4  ; [dart:core] DateTime::isAfter
    // 0xbbb5e0: tbnz            w0, #4, #0xbbb5ec
    // 0xbbb5e4: ldur            x1, [fp, #-8]
    // 0xbbb5e8: b               #0xbbb5f0
    // 0xbbb5ec: ldur            x1, [fp, #-0x28]
    // 0xbbb5f0: mov             x0, x1
    // 0xbbb5f4: LeaveFrame
    //     0xbbb5f4: mov             SP, fp
    //     0xbbb5f8: ldp             fp, lr, [SP], #0x10
    // 0xbbb5fc: ret
    //     0xbbb5fc: ret             
    // 0xbbb600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb600: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb604: b               #0xbbb4e4
    // 0xbbb608: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb608: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb60c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb60c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] SizedBox <anonymous closure>(dynamic, BuildContext, DateTime) {
    // ** addr: 0xbbb610, size: 0x134
    // 0xbbb610: EnterFrame
    //     0xbbb610: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb614: mov             fp, SP
    // 0xbbb618: AllocStack(0x38)
    //     0xbbb618: sub             SP, SP, #0x38
    // 0xbbb61c: SetupParameters()
    //     0xbbb61c: ldr             x0, [fp, #0x20]
    //     0xbbb620: ldur            w4, [x0, #0x17]
    //     0xbbb624: add             x4, x4, HEAP, lsl #32
    //     0xbbb628: stur            x4, [fp, #-0x10]
    // 0xbbb62c: CheckStackOverflow
    //     0xbbb62c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb630: cmp             SP, x16
    //     0xbbb634: b.ls            #0xbbb720
    // 0xbbb638: LoadField: r0 = r4->field_b
    //     0xbbb638: ldur            w0, [x4, #0xb]
    // 0xbbb63c: DecompressPointer r0
    //     0xbbb63c: add             x0, x0, HEAP, lsl #32
    // 0xbbb640: stur            x0, [fp, #-8]
    // 0xbbb644: LoadField: r1 = r0->field_f
    //     0xbbb644: ldur            w1, [x0, #0xf]
    // 0xbbb648: DecompressPointer r1
    //     0xbbb648: add             x1, x1, HEAP, lsl #32
    // 0xbbb64c: LoadField: r2 = r1->field_b
    //     0xbbb64c: ldur            w2, [x1, #0xb]
    // 0xbbb650: DecompressPointer r2
    //     0xbbb650: add             x2, x2, HEAP, lsl #32
    // 0xbbb654: LoadField: r3 = r4->field_f
    //     0xbbb654: ldur            w3, [x4, #0xf]
    // 0xbbb658: DecompressPointer r3
    //     0xbbb658: add             x3, x3, HEAP, lsl #32
    // 0xbbb65c: r5 = LoadInt32Instr(r3)
    //     0xbbb65c: sbfx            x5, x3, #1, #0x1f
    //     0xbbb660: tbz             w3, #0, #0xbbb668
    //     0xbbb664: ldur            x5, [x3, #7]
    // 0xbbb668: mov             x3, x5
    // 0xbbb66c: r0 = _getFocusedDay()
    //     0xbbb66c: bl              #0xbba9f0  ; [package:table_calendar/src/widgets/calendar_core.dart] CalendarCore::_getFocusedDay
    // 0xbbb670: mov             x1, x0
    // 0xbbb674: ldur            x0, [fp, #-0x10]
    // 0xbbb678: LoadField: r2 = r0->field_13
    //     0xbbb678: ldur            w2, [x0, #0x13]
    // 0xbbb67c: DecompressPointer r2
    //     0xbbb67c: add             x2, x2, HEAP, lsl #32
    // 0xbbb680: cmp             w2, NULL
    // 0xbbb684: b.ne            #0xbbb694
    // 0xbbb688: d0 = 64.000000
    //     0xbbb688: add             x17, PP, #0x25, lsl #12  ; [pp+0x25238] IMM: double(64) from 0x4050000000000000
    //     0xbbb68c: ldr             d0, [x17, #0x238]
    // 0xbbb690: b               #0xbbb698
    // 0xbbb694: LoadField: d0 = r2->field_7
    //     0xbbb694: ldur            d0, [x2, #7]
    // 0xbbb698: ldur            x0, [fp, #-8]
    // 0xbbb69c: stur            d0, [fp, #-0x18]
    // 0xbbb6a0: LoadField: r2 = r0->field_f
    //     0xbbb6a0: ldur            w2, [x0, #0xf]
    // 0xbbb6a4: DecompressPointer r2
    //     0xbbb6a4: add             x2, x2, HEAP, lsl #32
    // 0xbbb6a8: LoadField: r0 = r2->field_23
    //     0xbbb6a8: ldur            w0, [x2, #0x23]
    // 0xbbb6ac: DecompressPointer r0
    //     0xbbb6ac: add             x0, x0, HEAP, lsl #32
    // 0xbbb6b0: ldr             x16, [fp, #0x18]
    // 0xbbb6b4: stp             x16, x0, [SP, #0x10]
    // 0xbbb6b8: ldr             x16, [fp, #0x10]
    // 0xbbb6bc: stp             x1, x16, [SP]
    // 0xbbb6c0: ClosureCall
    //     0xbbb6c0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbbb6c4: ldur            x2, [x0, #0x1f]
    //     0xbbb6c8: blr             x2
    // 0xbbb6cc: ldur            d0, [fp, #-0x18]
    // 0xbbb6d0: stur            x0, [fp, #-0x10]
    // 0xbbb6d4: r1 = inline_Allocate_Double()
    //     0xbbb6d4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbbb6d8: add             x1, x1, #0x10
    //     0xbbb6dc: cmp             x2, x1
    //     0xbbb6e0: b.ls            #0xbbb728
    //     0xbbb6e4: str             x1, [THR, #0x50]  ; THR::top
    //     0xbbb6e8: sub             x1, x1, #0xf
    //     0xbbb6ec: movz            x2, #0xe15c
    //     0xbbb6f0: movk            x2, #0x3, lsl #16
    //     0xbbb6f4: stur            x2, [x1, #-1]
    // 0xbbb6f8: StoreField: r1->field_7 = d0
    //     0xbbb6f8: stur            d0, [x1, #7]
    // 0xbbb6fc: stur            x1, [fp, #-8]
    // 0xbbb700: r0 = SizedBox()
    //     0xbbb700: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbbb704: ldur            x1, [fp, #-8]
    // 0xbbb708: StoreField: r0->field_13 = r1
    //     0xbbb708: stur            w1, [x0, #0x13]
    // 0xbbb70c: ldur            x1, [fp, #-0x10]
    // 0xbbb710: StoreField: r0->field_b = r1
    //     0xbbb710: stur            w1, [x0, #0xb]
    // 0xbbb714: LeaveFrame
    //     0xbbb714: mov             SP, fp
    //     0xbbb718: ldp             fp, lr, [SP], #0x10
    // 0xbbb71c: ret
    //     0xbbb71c: ret             
    // 0xbbb720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb724: b               #0xbbb638
    // 0xbbb728: SaveReg d0
    //     0xbbb728: str             q0, [SP, #-0x10]!
    // 0xbbb72c: SaveReg r0
    //     0xbbb72c: str             x0, [SP, #-8]!
    // 0xbbb730: r0 = AllocateDouble()
    //     0xbbb730: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbbb734: mov             x1, x0
    // 0xbbb738: RestoreReg r0
    //     0xbbb738: ldr             x0, [SP], #8
    // 0xbbb73c: RestoreReg d0
    //     0xbbb73c: ldr             q0, [SP], #0x10
    // 0xbbb740: b               #0xbbb6f8
  }
}
