// lib: , url: package:table_calendar/src/widgets/calendar_page.dart

// class id: 1051191, size: 0x8
class :: {
}

// class id: 4910, size: 0x28, field offset: 0xc
//   const constructor, 
class CalendarPage extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbbb744, size: 0x3f8
    // 0xbbb744: EnterFrame
    //     0xbbb744: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb748: mov             fp, SP
    // 0xbbb74c: AllocStack(0x68)
    //     0xbbb74c: sub             SP, SP, #0x68
    // 0xbbb750: SetupParameters(CalendarPage this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbbb750: mov             x3, x1
    //     0xbbb754: mov             x0, x2
    //     0xbbb758: stur            x1, [fp, #-8]
    //     0xbbb75c: stur            x2, [fp, #-0x10]
    // 0xbbb760: CheckStackOverflow
    //     0xbbb760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb764: cmp             SP, x16
    //     0xbbb768: b.ls            #0xbbbb18
    // 0xbbb76c: r1 = <Widget>
    //     0xbbb76c: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbb770: r2 = 0
    //     0xbbb770: movz            x2, #0
    // 0xbbb774: r0 = _GrowableList()
    //     0xbbb774: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbbb778: r1 = <TableRow>
    //     0xbbb778: add             x1, PP, #0x31, lsl #12  ; [pp+0x31aa8] TypeArguments: <TableRow>
    //     0xbbb77c: ldr             x1, [x1, #0xaa8]
    // 0xbbb780: r2 = 0
    //     0xbbb780: movz            x2, #0
    // 0xbbb784: stur            x0, [fp, #-0x18]
    // 0xbbb788: r0 = _GrowableList()
    //     0xbbb788: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbbb78c: mov             x3, x0
    // 0xbbb790: ldur            x0, [fp, #-8]
    // 0xbbb794: stur            x3, [fp, #-0x20]
    // 0xbbb798: LoadField: r1 = r0->field_f
    //     0xbbb798: ldur            w1, [x0, #0xf]
    // 0xbbb79c: DecompressPointer r1
    //     0xbbb79c: add             x1, x1, HEAP, lsl #32
    // 0xbbb7a0: LoadField: r2 = r1->field_b
    //     0xbbb7a0: ldur            w2, [x1, #0xb]
    // 0xbbb7a4: r1 = LoadInt32Instr(r2)
    //     0xbbb7a4: sbfx            x1, x2, #1, #0x1f
    // 0xbbb7a8: r4 = 7
    //     0xbbb7a8: movz            x4, #0x7
    // 0xbbb7ac: sdiv            x2, x1, x4
    // 0xbbb7b0: r1 = <TableRow>
    //     0xbbb7b0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31aa8] TypeArguments: <TableRow>
    //     0xbbb7b4: ldr             x1, [x1, #0xaa8]
    // 0xbbb7b8: r0 = _GrowableList()
    //     0xbbb7b8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbbb7bc: stur            x0, [fp, #-0x30]
    // 0xbbb7c0: r4 = 0
    //     0xbbb7c0: movz            x4, #0
    // 0xbbb7c4: ldur            x3, [fp, #-8]
    // 0xbbb7c8: stur            x4, [fp, #-0x28]
    // 0xbbb7cc: CheckStackOverflow
    //     0xbbb7cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb7d0: cmp             SP, x16
    //     0xbbb7d4: b.ls            #0xbbbb20
    // 0xbbb7d8: LoadField: r1 = r0->field_b
    //     0xbbb7d8: ldur            w1, [x0, #0xb]
    // 0xbbb7dc: r2 = LoadInt32Instr(r1)
    //     0xbbb7dc: sbfx            x2, x1, #1, #0x1f
    // 0xbbb7e0: cmp             x4, x2
    // 0xbbb7e4: b.ge            #0xbbb9c0
    // 0xbbb7e8: r1 = <Widget>
    //     0xbbb7e8: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbb7ec: r2 = 7
    //     0xbbb7ec: movz            x2, #0x7
    // 0xbbb7f0: r0 = _GrowableList()
    //     0xbbb7f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbbb7f4: mov             x3, x0
    // 0xbbb7f8: ldur            x2, [fp, #-0x28]
    // 0xbbb7fc: stur            x3, [fp, #-0x48]
    // 0xbbb800: r16 = 7
    //     0xbbb800: movz            x16, #0x7
    // 0xbbb804: mul             x4, x2, x16
    // 0xbbb808: stur            x4, [fp, #-0x40]
    // 0xbbb80c: r6 = 0
    //     0xbbb80c: movz            x6, #0
    // 0xbbb810: ldur            x5, [fp, #-8]
    // 0xbbb814: stur            x6, [fp, #-0x38]
    // 0xbbb818: CheckStackOverflow
    //     0xbbb818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb81c: cmp             SP, x16
    //     0xbbb820: b.ls            #0xbbbb28
    // 0xbbb824: LoadField: r0 = r3->field_b
    //     0xbbb824: ldur            w0, [x3, #0xb]
    // 0xbbb828: r1 = LoadInt32Instr(r0)
    //     0xbbb828: sbfx            x1, x0, #1, #0x1f
    // 0xbbb82c: cmp             x6, x1
    // 0xbbb830: b.ge            #0xbbb938
    // 0xbbb834: LoadField: r7 = r5->field_f
    //     0xbbb834: ldur            w7, [x5, #0xf]
    // 0xbbb838: DecompressPointer r7
    //     0xbbb838: add             x7, x7, HEAP, lsl #32
    // 0xbbb83c: add             x8, x4, x6
    // 0xbbb840: LoadField: r0 = r7->field_b
    //     0xbbb840: ldur            w0, [x7, #0xb]
    // 0xbbb844: r1 = LoadInt32Instr(r0)
    //     0xbbb844: sbfx            x1, x0, #1, #0x1f
    // 0xbbb848: mov             x0, x1
    // 0xbbb84c: mov             x1, x8
    // 0xbbb850: cmp             x1, x0
    // 0xbbb854: b.hs            #0xbbbb30
    // 0xbbb858: LoadField: r0 = r7->field_f
    //     0xbbb858: ldur            w0, [x7, #0xf]
    // 0xbbb85c: DecompressPointer r0
    //     0xbbb85c: add             x0, x0, HEAP, lsl #32
    // 0xbbb860: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbbb860: add             x16, x0, x8, lsl #2
    //     0xbbb864: ldur            w1, [x16, #0xf]
    // 0xbbb868: DecompressPointer r1
    //     0xbbb868: add             x1, x1, HEAP, lsl #32
    // 0xbbb86c: LoadField: r0 = r5->field_b
    //     0xbbb86c: ldur            w0, [x5, #0xb]
    // 0xbbb870: DecompressPointer r0
    //     0xbbb870: add             x0, x0, HEAP, lsl #32
    // 0xbbb874: ldur            x16, [fp, #-0x10]
    // 0xbbb878: stp             x16, x0, [SP, #8]
    // 0xbbb87c: str             x1, [SP]
    // 0xbbb880: ClosureCall
    //     0xbbb880: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xbbb884: ldur            x2, [x0, #0x1f]
    //     0xbbb888: blr             x2
    // 0xbbb88c: mov             x3, x0
    // 0xbbb890: r2 = Null
    //     0xbbb890: mov             x2, NULL
    // 0xbbb894: r1 = Null
    //     0xbbb894: mov             x1, NULL
    // 0xbbb898: stur            x3, [fp, #-0x50]
    // 0xbbb89c: r4 = 60
    //     0xbbb89c: movz            x4, #0x3c
    // 0xbbb8a0: branchIfSmi(r0, 0xbbb8ac)
    //     0xbbb8a0: tbz             w0, #0, #0xbbb8ac
    // 0xbbb8a4: r4 = LoadClassIdInstr(r0)
    //     0xbbb8a4: ldur            x4, [x0, #-1]
    //     0xbbb8a8: ubfx            x4, x4, #0xc, #0x14
    // 0xbbb8ac: r17 = -4434
    //     0xbbb8ac: movn            x17, #0x1151
    // 0xbbb8b0: add             x4, x4, x17
    // 0xbbb8b4: cmp             x4, #0x3dc
    // 0xbbb8b8: b.ls            #0xbbb8d0
    // 0xbbb8bc: r8 = Widget
    //     0xbbb8bc: add             x8, PP, #0x41, lsl #12  ; [pp+0x41550] Type: Widget
    //     0xbbb8c0: ldr             x8, [x8, #0x550]
    // 0xbbb8c4: r3 = Null
    //     0xbbb8c4: add             x3, PP, #0x5d, lsl #12  ; [pp+0x5d2e0] Null
    //     0xbbb8c8: ldr             x3, [x3, #0x2e0]
    // 0xbbb8cc: r0 = Widget()
    //     0xbbb8cc: bl              #0x5fbab4  ; IsType_Widget_Stub
    // 0xbbb8d0: ldur            x2, [fp, #-0x48]
    // 0xbbb8d4: LoadField: r0 = r2->field_b
    //     0xbbb8d4: ldur            w0, [x2, #0xb]
    // 0xbbb8d8: r1 = LoadInt32Instr(r0)
    //     0xbbb8d8: sbfx            x1, x0, #1, #0x1f
    // 0xbbb8dc: mov             x0, x1
    // 0xbbb8e0: ldur            x1, [fp, #-0x38]
    // 0xbbb8e4: cmp             x1, x0
    // 0xbbb8e8: b.hs            #0xbbbb34
    // 0xbbb8ec: LoadField: r1 = r2->field_f
    //     0xbbb8ec: ldur            w1, [x2, #0xf]
    // 0xbbb8f0: DecompressPointer r1
    //     0xbbb8f0: add             x1, x1, HEAP, lsl #32
    // 0xbbb8f4: ldur            x0, [fp, #-0x50]
    // 0xbbb8f8: ldur            x3, [fp, #-0x38]
    // 0xbbb8fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbb8fc: add             x25, x1, x3, lsl #2
    //     0xbbb900: add             x25, x25, #0xf
    //     0xbbb904: str             w0, [x25]
    //     0xbbb908: tbz             w0, #0, #0xbbb924
    //     0xbbb90c: ldurb           w16, [x1, #-1]
    //     0xbbb910: ldurb           w17, [x0, #-1]
    //     0xbbb914: and             x16, x17, x16, lsr #2
    //     0xbbb918: tst             x16, HEAP, lsr #32
    //     0xbbb91c: b.eq            #0xbbb924
    //     0xbbb920: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbb924: add             x6, x3, #1
    // 0xbbb928: mov             x3, x2
    // 0xbbb92c: ldur            x2, [fp, #-0x28]
    // 0xbbb930: ldur            x4, [fp, #-0x40]
    // 0xbbb934: b               #0xbbb810
    // 0xbbb938: ldur            x0, [fp, #-0x30]
    // 0xbbb93c: mov             x1, x2
    // 0xbbb940: mov             x2, x3
    // 0xbbb944: r0 = TableRow()
    //     0xbbb944: bl              #0xbb1ac0  ; AllocateTableRowStub -> TableRow (size=0x14)
    // 0xbbb948: mov             x3, x0
    // 0xbbb94c: r2 = Instance_BoxDecoration
    //     0xbbb94c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47e98] Obj!BoxDecoration@e1d1e1
    //     0xbbb950: ldr             x2, [x2, #0xe98]
    // 0xbbb954: StoreField: r3->field_b = r2
    //     0xbbb954: stur            w2, [x3, #0xb]
    // 0xbbb958: ldur            x0, [fp, #-0x48]
    // 0xbbb95c: StoreField: r3->field_f = r0
    //     0xbbb95c: stur            w0, [x3, #0xf]
    // 0xbbb960: ldur            x5, [fp, #-0x30]
    // 0xbbb964: LoadField: r0 = r5->field_b
    //     0xbbb964: ldur            w0, [x5, #0xb]
    // 0xbbb968: r1 = LoadInt32Instr(r0)
    //     0xbbb968: sbfx            x1, x0, #1, #0x1f
    // 0xbbb96c: mov             x0, x1
    // 0xbbb970: ldur            x1, [fp, #-0x28]
    // 0xbbb974: cmp             x1, x0
    // 0xbbb978: b.hs            #0xbbbb38
    // 0xbbb97c: LoadField: r1 = r5->field_f
    //     0xbbb97c: ldur            w1, [x5, #0xf]
    // 0xbbb980: DecompressPointer r1
    //     0xbbb980: add             x1, x1, HEAP, lsl #32
    // 0xbbb984: mov             x0, x3
    // 0xbbb988: ldur            x3, [fp, #-0x28]
    // 0xbbb98c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbb98c: add             x25, x1, x3, lsl #2
    //     0xbbb990: add             x25, x25, #0xf
    //     0xbbb994: str             w0, [x25]
    //     0xbbb998: tbz             w0, #0, #0xbbb9b4
    //     0xbbb99c: ldurb           w16, [x1, #-1]
    //     0xbbb9a0: ldurb           w17, [x0, #-1]
    //     0xbbb9a4: and             x16, x17, x16, lsr #2
    //     0xbbb9a8: tst             x16, HEAP, lsr #32
    //     0xbbb9ac: b.eq            #0xbbb9b4
    //     0xbbb9b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbb9b4: add             x4, x3, #1
    // 0xbbb9b8: mov             x0, x5
    // 0xbbb9bc: b               #0xbbb7c4
    // 0xbbb9c0: mov             x5, x0
    // 0xbbb9c4: ldur            x0, [fp, #-0x18]
    // 0xbbb9c8: ldur            x1, [fp, #-0x20]
    // 0xbbb9cc: mov             x2, x5
    // 0xbbb9d0: r0 = addAll()
    //     0xbbb9d0: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0xbbb9d4: r0 = Table()
    //     0xbbb9d4: bl              #0xbaf944  ; AllocateTableStub -> Table (size=0x2c)
    // 0xbbb9d8: mov             x1, x0
    // 0xbbb9dc: ldur            x3, [fp, #-0x20]
    // 0xbbb9e0: r2 = Instance_TableBorder
    //     0xbbb9e0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47ea0] Obj!TableBorder@e116b1
    //     0xbbb9e4: ldr             x2, [x2, #0xea0]
    // 0xbbb9e8: stur            x0, [fp, #-8]
    // 0xbbb9ec: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xbbb9ec: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xbbb9f0: r0 = Table()
    //     0xbbb9f0: bl              #0xbaf7ac  ; [package:flutter/src/widgets/table.dart] Table::Table
    // 0xbbb9f4: r1 = <FlexParentData>
    //     0xbbb9f4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbbb9f8: ldr             x1, [x1, #0x720]
    // 0xbbb9fc: r0 = Expanded()
    //     0xbbb9fc: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbbba00: mov             x2, x0
    // 0xbbba04: r0 = 1
    //     0xbbba04: movz            x0, #0x1
    // 0xbbba08: stur            x2, [fp, #-0x10]
    // 0xbbba0c: StoreField: r2->field_13 = r0
    //     0xbbba0c: stur            x0, [x2, #0x13]
    // 0xbbba10: r0 = Instance_FlexFit
    //     0xbbba10: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbbba14: ldr             x0, [x0, #0x728]
    // 0xbbba18: StoreField: r2->field_1b = r0
    //     0xbbba18: stur            w0, [x2, #0x1b]
    // 0xbbba1c: ldur            x0, [fp, #-8]
    // 0xbbba20: StoreField: r2->field_b = r0
    //     0xbbba20: stur            w0, [x2, #0xb]
    // 0xbbba24: ldur            x0, [fp, #-0x18]
    // 0xbbba28: LoadField: r1 = r0->field_b
    //     0xbbba28: ldur            w1, [x0, #0xb]
    // 0xbbba2c: LoadField: r3 = r0->field_f
    //     0xbbba2c: ldur            w3, [x0, #0xf]
    // 0xbbba30: DecompressPointer r3
    //     0xbbba30: add             x3, x3, HEAP, lsl #32
    // 0xbbba34: LoadField: r4 = r3->field_b
    //     0xbbba34: ldur            w4, [x3, #0xb]
    // 0xbbba38: r3 = LoadInt32Instr(r1)
    //     0xbbba38: sbfx            x3, x1, #1, #0x1f
    // 0xbbba3c: stur            x3, [fp, #-0x28]
    // 0xbbba40: r1 = LoadInt32Instr(r4)
    //     0xbbba40: sbfx            x1, x4, #1, #0x1f
    // 0xbbba44: cmp             x3, x1
    // 0xbbba48: b.ne            #0xbbba54
    // 0xbbba4c: mov             x1, x0
    // 0xbbba50: r0 = _growToNextCapacity()
    //     0xbbba50: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbba54: ldur            x2, [fp, #-0x18]
    // 0xbbba58: ldur            x3, [fp, #-0x28]
    // 0xbbba5c: add             x0, x3, #1
    // 0xbbba60: lsl             x1, x0, #1
    // 0xbbba64: StoreField: r2->field_b = r1
    //     0xbbba64: stur            w1, [x2, #0xb]
    // 0xbbba68: LoadField: r1 = r2->field_f
    //     0xbbba68: ldur            w1, [x2, #0xf]
    // 0xbbba6c: DecompressPointer r1
    //     0xbbba6c: add             x1, x1, HEAP, lsl #32
    // 0xbbba70: ldur            x0, [fp, #-0x10]
    // 0xbbba74: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbba74: add             x25, x1, x3, lsl #2
    //     0xbbba78: add             x25, x25, #0xf
    //     0xbbba7c: str             w0, [x25]
    //     0xbbba80: tbz             w0, #0, #0xbbba9c
    //     0xbbba84: ldurb           w16, [x1, #-1]
    //     0xbbba88: ldurb           w17, [x0, #-1]
    //     0xbbba8c: and             x16, x17, x16, lsr #2
    //     0xbbba90: tst             x16, HEAP, lsr #32
    //     0xbbba94: b.eq            #0xbbba9c
    //     0xbbba98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbba9c: r0 = Row()
    //     0xbbba9c: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbbaa0: mov             x1, x0
    // 0xbbbaa4: r0 = Instance_Axis
    //     0xbbbaa4: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xbbbaa8: stur            x1, [fp, #-8]
    // 0xbbbaac: StoreField: r1->field_f = r0
    //     0xbbbaac: stur            w0, [x1, #0xf]
    // 0xbbbab0: r0 = Instance_MainAxisAlignment
    //     0xbbbab0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbbbab4: ldr             x0, [x0, #0x730]
    // 0xbbbab8: StoreField: r1->field_13 = r0
    //     0xbbbab8: stur            w0, [x1, #0x13]
    // 0xbbbabc: r0 = Instance_MainAxisSize
    //     0xbbbabc: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbbbac0: ldr             x0, [x0, #0x738]
    // 0xbbbac4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbbac4: stur            w0, [x1, #0x17]
    // 0xbbbac8: r0 = Instance_CrossAxisAlignment
    //     0xbbbac8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Obj!CrossAxisAlignment@e35a21
    //     0xbbbacc: ldr             x0, [x0, #0xf50]
    // 0xbbbad0: StoreField: r1->field_1b = r0
    //     0xbbbad0: stur            w0, [x1, #0x1b]
    // 0xbbbad4: r0 = Instance_VerticalDirection
    //     0xbbbad4: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbbbad8: ldr             x0, [x0, #0x748]
    // 0xbbbadc: StoreField: r1->field_23 = r0
    //     0xbbbadc: stur            w0, [x1, #0x23]
    // 0xbbbae0: r0 = Instance_Clip
    //     0xbbbae0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbbbae4: ldr             x0, [x0, #0x750]
    // 0xbbbae8: StoreField: r1->field_2b = r0
    //     0xbbbae8: stur            w0, [x1, #0x2b]
    // 0xbbbaec: StoreField: r1->field_2f = rZR
    //     0xbbbaec: stur            xzr, [x1, #0x2f]
    // 0xbbbaf0: ldur            x0, [fp, #-0x18]
    // 0xbbbaf4: StoreField: r1->field_b = r0
    //     0xbbbaf4: stur            w0, [x1, #0xb]
    // 0xbbbaf8: r0 = Padding()
    //     0xbbbaf8: bl              #0x9d401c  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbbafc: r1 = Instance_EdgeInsets
    //     0xbbbafc: ldr             x1, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbb00: StoreField: r0->field_f = r1
    //     0xbbbb00: stur            w1, [x0, #0xf]
    // 0xbbbb04: ldur            x1, [fp, #-8]
    // 0xbbbb08: StoreField: r0->field_b = r1
    //     0xbbbb08: stur            w1, [x0, #0xb]
    // 0xbbbb0c: LeaveFrame
    //     0xbbbb0c: mov             SP, fp
    //     0xbbbb10: ldp             fp, lr, [SP], #0x10
    // 0xbbbb14: ret
    //     0xbbbb14: ret             
    // 0xbbbb18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbbb18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbbb1c: b               #0xbbb76c
    // 0xbbbb20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbbb20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbbb24: b               #0xbbb7d8
    // 0xbbbb28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbbb28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbbb2c: b               #0xbbb824
    // 0xbbbb30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbbb30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbbb34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbbb34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbbb38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbbb38: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
