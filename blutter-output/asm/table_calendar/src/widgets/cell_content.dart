// lib: , url: package:table_calendar/src/widgets/cell_content.dart

// class id: 1051192, size: 0x8
class :: {
}

// class id: 4909, size: 0x48, field offset: 0xc
//   const constructor, 
class CellContent extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbbbb3c, size: 0x6d8
    // 0xbbbb3c: EnterFrame
    //     0xbbbb3c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbbb40: mov             fp, SP
    // 0xbbbb44: AllocStack(0x58)
    //     0xbbbb44: sub             SP, SP, #0x58
    // 0xbbbb48: SetupParameters(CellContent this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbbbb48: mov             x3, x1
    //     0xbbbb4c: mov             x0, x2
    //     0xbbbb50: stur            x1, [fp, #-8]
    //     0xbbbb54: stur            x2, [fp, #-0x10]
    // 0xbbbb58: CheckStackOverflow
    //     0xbbbb58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbbb5c: cmp             SP, x16
    //     0xbbbb60: b.ls            #0xbbc20c
    // 0xbbbb64: r1 = Null
    //     0xbbbb64: mov             x1, NULL
    // 0xbbbb68: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xbbbb68: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xbbbb6c: ldr             x2, [x2, #0xad0]
    // 0xbbbb70: r0 = verifiedLocale()
    //     0xbbbb70: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xbbbb74: stur            x0, [fp, #-0x18]
    // 0xbbbb78: r0 = DateFormat()
    //     0xbbbb78: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xbbbb7c: mov             x3, x0
    // 0xbbbb80: ldur            x0, [fp, #-0x18]
    // 0xbbbb84: stur            x3, [fp, #-0x20]
    // 0xbbbb88: StoreField: r3->field_7 = r0
    //     0xbbbb88: stur            w0, [x3, #7]
    // 0xbbbb8c: mov             x1, x3
    // 0xbbbb90: r2 = "EEEE"
    //     0xbbbb90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e188] "EEEE"
    //     0xbbbb94: ldr             x2, [x2, #0x188]
    // 0xbbbb98: r0 = addPattern()
    //     0xbbbb98: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xbbbb9c: ldur            x0, [fp, #-8]
    // 0xbbbba0: LoadField: r3 = r0->field_b
    //     0xbbbba0: ldur            w3, [x0, #0xb]
    // 0xbbbba4: DecompressPointer r3
    //     0xbbbba4: add             x3, x3, HEAP, lsl #32
    // 0xbbbba8: ldur            x1, [fp, #-0x20]
    // 0xbbbbac: mov             x2, x3
    // 0xbbbbb0: stur            x3, [fp, #-0x18]
    // 0xbbbbb4: r0 = format()
    //     0xbbbbb4: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xbbbbb8: r1 = Null
    //     0xbbbbb8: mov             x1, NULL
    // 0xbbbbbc: r2 = Closure: (String?) => bool from Function 'localeExists': static.
    //     0xbbbbbc: add             x2, PP, #8, lsl #12  ; [pp+0x8ad0] Closure: (String?) => bool from Function 'localeExists': static. (0x7e54fb21d880)
    //     0xbbbbc0: ldr             x2, [x2, #0xad0]
    // 0xbbbbc4: stur            x0, [fp, #-0x20]
    // 0xbbbbc8: r0 = verifiedLocale()
    //     0xbbbbc8: bl              #0x81d418  ; [package:intl/src/intl_helpers.dart] ::verifiedLocale
    // 0xbbbbcc: stur            x0, [fp, #-0x28]
    // 0xbbbbd0: r0 = DateFormat()
    //     0xbbbbd0: bl              #0x81d40c  ; AllocateDateFormatStub -> DateFormat (size=0x20)
    // 0xbbbbd4: mov             x3, x0
    // 0xbbbbd8: ldur            x0, [fp, #-0x28]
    // 0xbbbbdc: stur            x3, [fp, #-0x30]
    // 0xbbbbe0: StoreField: r3->field_7 = r0
    //     0xbbbbe0: stur            w0, [x3, #7]
    // 0xbbbbe4: mov             x1, x3
    // 0xbbbbe8: r2 = "yMMMMd"
    //     0xbbbbe8: add             x2, PP, #8, lsl #12  ; [pp+0x8bd8] "yMMMMd"
    //     0xbbbbec: ldr             x2, [x2, #0xbd8]
    // 0xbbbbf0: r0 = addPattern()
    //     0xbbbbf0: bl              #0x81cbbc  ; [package:intl/src/intl/date_format.dart] DateFormat::addPattern
    // 0xbbbbf4: ldur            x1, [fp, #-0x30]
    // 0xbbbbf8: ldur            x2, [fp, #-0x18]
    // 0xbbbbfc: r0 = format()
    //     0xbbbbfc: bl              #0x81c1a8  ; [package:intl/src/intl/date_format.dart] DateFormat::format
    // 0xbbbc00: r1 = Null
    //     0xbbbc00: mov             x1, NULL
    // 0xbbbc04: r2 = 6
    //     0xbbbc04: movz            x2, #0x6
    // 0xbbbc08: stur            x0, [fp, #-0x28]
    // 0xbbbc0c: r0 = AllocateArray()
    //     0xbbbc0c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbbc10: mov             x1, x0
    // 0xbbbc14: ldur            x0, [fp, #-0x20]
    // 0xbbbc18: StoreField: r1->field_f = r0
    //     0xbbbc18: stur            w0, [x1, #0xf]
    // 0xbbbc1c: r16 = ", "
    //     0xbbbc1c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0xbbbc20: StoreField: r1->field_13 = r16
    //     0xbbbc20: stur            w16, [x1, #0x13]
    // 0xbbbc24: ldur            x0, [fp, #-0x28]
    // 0xbbbc28: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbbc28: stur            w0, [x1, #0x17]
    // 0xbbbc2c: str             x1, [SP]
    // 0xbbbc30: r0 = _interpolate()
    //     0xbbbc30: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xbbbc34: mov             x3, x0
    // 0xbbbc38: ldur            x2, [fp, #-0x18]
    // 0xbbbc3c: stur            x3, [fp, #-0x20]
    // 0xbbbc40: r0 = LoadClassIdInstr(r2)
    //     0xbbbc40: ldur            x0, [x2, #-1]
    //     0xbbbc44: ubfx            x0, x0, #0xc, #0x14
    // 0xbbbc48: mov             x1, x2
    // 0xbbbc4c: r0 = GDT[cid_x0 + -0xfdf]()
    //     0xbbbc4c: sub             lr, x0, #0xfdf
    //     0xbbbc50: ldr             lr, [x21, lr, lsl #3]
    //     0xbbbc54: blr             lr
    // 0xbbbc58: mov             x2, x0
    // 0xbbbc5c: r0 = BoxInt64Instr(r2)
    //     0xbbbc5c: sbfiz           x0, x2, #1, #0x1f
    //     0xbbbc60: cmp             x2, x0, asr #1
    //     0xbbbc64: b.eq            #0xbbbc70
    //     0xbbbc68: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbbc6c: stur            x2, [x0, #7]
    // 0xbbbc70: str             x0, [SP]
    // 0xbbbc74: r0 = _interpolateSingle()
    //     0xbbbc74: bl              #0x5fff0c  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbbbc78: mov             x2, x0
    // 0xbbbc7c: ldur            x1, [fp, #-8]
    // 0xbbbc80: stur            x2, [fp, #-0x28]
    // 0xbbbc84: LoadField: r0 = r1->field_33
    //     0xbbbc84: ldur            w0, [x1, #0x33]
    // 0xbbbc88: DecompressPointer r0
    //     0xbbbc88: add             x0, x0, HEAP, lsl #32
    // 0xbbbc8c: tbnz            w0, #4, #0xbbbd4c
    // 0xbbbc90: LoadField: r0 = r1->field_43
    //     0xbbbc90: ldur            w0, [x1, #0x43]
    // 0xbbbc94: DecompressPointer r0
    //     0xbbbc94: add             x0, x0, HEAP, lsl #32
    // 0xbbbc98: LoadField: r3 = r0->field_27
    //     0xbbbc98: ldur            w3, [x0, #0x27]
    // 0xbbbc9c: DecompressPointer r3
    //     0xbbbc9c: add             x3, x3, HEAP, lsl #32
    // 0xbbbca0: cmp             w3, NULL
    // 0xbbbca4: b.ne            #0xbbbcb0
    // 0xbbbca8: r0 = Null
    //     0xbbbca8: mov             x0, NULL
    // 0xbbbcac: b               #0xbbbcd8
    // 0xbbbcb0: LoadField: r0 = r1->field_f
    //     0xbbbcb0: ldur            w0, [x1, #0xf]
    // 0xbbbcb4: DecompressPointer r0
    //     0xbbbcb4: add             x0, x0, HEAP, lsl #32
    // 0xbbbcb8: ldur            x16, [fp, #-0x10]
    // 0xbbbcbc: stp             x16, x3, [SP, #0x10]
    // 0xbbbcc0: ldur            x16, [fp, #-0x18]
    // 0xbbbcc4: stp             x0, x16, [SP]
    // 0xbbbcc8: mov             x0, x3
    // 0xbbbccc: ClosureCall
    //     0xbbbccc: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbbbcd0: ldur            x2, [x0, #0x1f]
    //     0xbbbcd4: blr             x2
    // 0xbbbcd8: cmp             w0, NULL
    // 0xbbbcdc: b.ne            #0xbbc1cc
    // 0xbbbce0: ldur            x0, [fp, #-0x28]
    // 0xbbbce4: r0 = Text()
    //     0xbbbce4: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbbce8: ldur            x2, [fp, #-0x28]
    // 0xbbbcec: stur            x0, [fp, #-0x30]
    // 0xbbbcf0: StoreField: r0->field_b = r2
    //     0xbbbcf0: stur            w2, [x0, #0xb]
    // 0xbbbcf4: r1 = Instance_TextStyle
    //     0xbbbcf4: add             x1, PP, #0x51, lsl #12  ; [pp+0x510c0] Obj!TextStyle@e1b341
    //     0xbbbcf8: ldr             x1, [x1, #0xc0]
    // 0xbbbcfc: StoreField: r0->field_13 = r1
    //     0xbbbcfc: stur            w1, [x0, #0x13]
    // 0xbbbd00: r0 = AnimatedContainer()
    //     0xbbbd00: bl              #0xa9b78c  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbbbd04: stur            x0, [fp, #-0x38]
    // 0xbbbd08: r16 = Instance_EdgeInsets
    //     0xbbbd08: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbd0c: r30 = Instance_EdgeInsets
    //     0xbbbd0c: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbd10: stp             lr, x16, [SP, #8]
    // 0xbbbd14: r16 = Instance_Alignment
    //     0xbbbd14: add             x16, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xbbbd18: ldr             x16, [x16, #0x898]
    // 0xbbbd1c: str             x16, [SP]
    // 0xbbbd20: mov             x1, x0
    // 0xbbbd24: ldur            x2, [fp, #-0x30]
    // 0xbbbd28: r3 = Instance_BoxDecoration
    //     0xbbbd28: add             x3, PP, #0x51, lsl #12  ; [pp+0x510c8] Obj!BoxDecoration@e1d271
    //     0xbbbd2c: ldr             x3, [x3, #0xc8]
    // 0xbbbd30: r5 = Instance_Duration
    //     0xbbbd30: add             x5, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbbbd34: ldr             x5, [x5, #0xd90]
    // 0xbbbd38: r4 = const [0, 0x7, 0x3, 0x4, alignment, 0x6, margin, 0x4, padding, 0x5, null]
    //     0xbbbd38: add             x4, PP, #0x51, lsl #12  ; [pp+0x510d0] List(11) [0, 0x7, 0x3, 0x4, "alignment", 0x6, "margin", 0x4, "padding", 0x5, Null]
    //     0xbbbd3c: ldr             x4, [x4, #0xd0]
    // 0xbbbd40: r0 = AnimatedContainer()
    //     0xbbbd40: bl              #0xa9b524  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbbbd44: ldur            x0, [fp, #-0x38]
    // 0xbbbd48: b               #0xbbc1cc
    // 0xbbbd4c: LoadField: r0 = r1->field_1f
    //     0xbbbd4c: ldur            w0, [x1, #0x1f]
    // 0xbbbd50: DecompressPointer r0
    //     0xbbbd50: add             x0, x0, HEAP, lsl #32
    // 0xbbbd54: tbnz            w0, #4, #0xbbbe14
    // 0xbbbd58: LoadField: r0 = r1->field_43
    //     0xbbbd58: ldur            w0, [x1, #0x43]
    // 0xbbbd5c: DecompressPointer r0
    //     0xbbbd5c: add             x0, x0, HEAP, lsl #32
    // 0xbbbd60: LoadField: r3 = r0->field_13
    //     0xbbbd60: ldur            w3, [x0, #0x13]
    // 0xbbbd64: DecompressPointer r3
    //     0xbbbd64: add             x3, x3, HEAP, lsl #32
    // 0xbbbd68: cmp             w3, NULL
    // 0xbbbd6c: b.ne            #0xbbbd78
    // 0xbbbd70: r0 = Null
    //     0xbbbd70: mov             x0, NULL
    // 0xbbbd74: b               #0xbbbda0
    // 0xbbbd78: LoadField: r0 = r1->field_f
    //     0xbbbd78: ldur            w0, [x1, #0xf]
    // 0xbbbd7c: DecompressPointer r0
    //     0xbbbd7c: add             x0, x0, HEAP, lsl #32
    // 0xbbbd80: ldur            x16, [fp, #-0x10]
    // 0xbbbd84: stp             x16, x3, [SP, #0x10]
    // 0xbbbd88: ldur            x16, [fp, #-0x18]
    // 0xbbbd8c: stp             x0, x16, [SP]
    // 0xbbbd90: mov             x0, x3
    // 0xbbbd94: ClosureCall
    //     0xbbbd94: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbbbd98: ldur            x2, [x0, #0x1f]
    //     0xbbbd9c: blr             x2
    // 0xbbbda0: cmp             w0, NULL
    // 0xbbbda4: b.ne            #0xbbc1cc
    // 0xbbbda8: ldur            x0, [fp, #-0x28]
    // 0xbbbdac: r0 = Text()
    //     0xbbbdac: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbbdb0: ldur            x2, [fp, #-0x28]
    // 0xbbbdb4: stur            x0, [fp, #-0x30]
    // 0xbbbdb8: StoreField: r0->field_b = r2
    //     0xbbbdb8: stur            w2, [x0, #0xb]
    // 0xbbbdbc: r3 = Instance_TextStyle
    //     0xbbbdbc: add             x3, PP, #0x51, lsl #12  ; [pp+0x510d8] Obj!TextStyle@e1b421
    //     0xbbbdc0: ldr             x3, [x3, #0xd8]
    // 0xbbbdc4: StoreField: r0->field_13 = r3
    //     0xbbbdc4: stur            w3, [x0, #0x13]
    // 0xbbbdc8: r0 = AnimatedContainer()
    //     0xbbbdc8: bl              #0xa9b78c  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbbbdcc: stur            x0, [fp, #-0x38]
    // 0xbbbdd0: r16 = Instance_EdgeInsets
    //     0xbbbdd0: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbdd4: r30 = Instance_EdgeInsets
    //     0xbbbdd4: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbdd8: stp             lr, x16, [SP, #8]
    // 0xbbbddc: r16 = Instance_Alignment
    //     0xbbbddc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xbbbde0: ldr             x16, [x16, #0x898]
    // 0xbbbde4: str             x16, [SP]
    // 0xbbbde8: mov             x1, x0
    // 0xbbbdec: ldur            x2, [fp, #-0x30]
    // 0xbbbdf0: r3 = Instance_BoxDecoration
    //     0xbbbdf0: add             x3, PP, #0x51, lsl #12  ; [pp+0x510e0] Obj!BoxDecoration@e1d2d1
    //     0xbbbdf4: ldr             x3, [x3, #0xe0]
    // 0xbbbdf8: r5 = Instance_Duration
    //     0xbbbdf8: add             x5, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbbbdfc: ldr             x5, [x5, #0xd90]
    // 0xbbbe00: r4 = const [0, 0x7, 0x3, 0x4, alignment, 0x6, margin, 0x4, padding, 0x5, null]
    //     0xbbbe00: add             x4, PP, #0x51, lsl #12  ; [pp+0x510d0] List(11) [0, 0x7, 0x3, 0x4, "alignment", 0x6, "margin", 0x4, "padding", 0x5, Null]
    //     0xbbbe04: ldr             x4, [x4, #0xd0]
    // 0xbbbe08: r0 = AnimatedContainer()
    //     0xbbbe08: bl              #0xa9b524  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbbbe0c: ldur            x0, [fp, #-0x38]
    // 0xbbbe10: b               #0xbbc1cc
    // 0xbbbe14: r3 = Instance_TextStyle
    //     0xbbbe14: add             x3, PP, #0x51, lsl #12  ; [pp+0x510d8] Obj!TextStyle@e1b421
    //     0xbbbe18: ldr             x3, [x3, #0xd8]
    // 0xbbbe1c: LoadField: r0 = r1->field_1b
    //     0xbbbe1c: ldur            w0, [x1, #0x1b]
    // 0xbbbe20: DecompressPointer r0
    //     0xbbbe20: add             x0, x0, HEAP, lsl #32
    // 0xbbbe24: tbnz            w0, #4, #0xbbbee4
    // 0xbbbe28: LoadField: r0 = r1->field_43
    //     0xbbbe28: ldur            w0, [x1, #0x43]
    // 0xbbbe2c: DecompressPointer r0
    //     0xbbbe2c: add             x0, x0, HEAP, lsl #32
    // 0xbbbe30: LoadField: r4 = r0->field_f
    //     0xbbbe30: ldur            w4, [x0, #0xf]
    // 0xbbbe34: DecompressPointer r4
    //     0xbbbe34: add             x4, x4, HEAP, lsl #32
    // 0xbbbe38: cmp             w4, NULL
    // 0xbbbe3c: b.ne            #0xbbbe48
    // 0xbbbe40: r0 = Null
    //     0xbbbe40: mov             x0, NULL
    // 0xbbbe44: b               #0xbbbe70
    // 0xbbbe48: LoadField: r0 = r1->field_f
    //     0xbbbe48: ldur            w0, [x1, #0xf]
    // 0xbbbe4c: DecompressPointer r0
    //     0xbbbe4c: add             x0, x0, HEAP, lsl #32
    // 0xbbbe50: ldur            x16, [fp, #-0x10]
    // 0xbbbe54: stp             x16, x4, [SP, #0x10]
    // 0xbbbe58: ldur            x16, [fp, #-0x18]
    // 0xbbbe5c: stp             x0, x16, [SP]
    // 0xbbbe60: mov             x0, x4
    // 0xbbbe64: ClosureCall
    //     0xbbbe64: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbbbe68: ldur            x2, [x0, #0x1f]
    //     0xbbbe6c: blr             x2
    // 0xbbbe70: cmp             w0, NULL
    // 0xbbbe74: b.ne            #0xbbc1cc
    // 0xbbbe78: ldur            x0, [fp, #-0x28]
    // 0xbbbe7c: r0 = Text()
    //     0xbbbe7c: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbbe80: ldur            x2, [fp, #-0x28]
    // 0xbbbe84: stur            x0, [fp, #-0x30]
    // 0xbbbe88: StoreField: r0->field_b = r2
    //     0xbbbe88: stur            w2, [x0, #0xb]
    // 0xbbbe8c: r1 = Instance_TextStyle
    //     0xbbbe8c: add             x1, PP, #0x51, lsl #12  ; [pp+0x510d8] Obj!TextStyle@e1b421
    //     0xbbbe90: ldr             x1, [x1, #0xd8]
    // 0xbbbe94: StoreField: r0->field_13 = r1
    //     0xbbbe94: stur            w1, [x0, #0x13]
    // 0xbbbe98: r0 = AnimatedContainer()
    //     0xbbbe98: bl              #0xa9b78c  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbbbe9c: stur            x0, [fp, #-0x38]
    // 0xbbbea0: r16 = Instance_EdgeInsets
    //     0xbbbea0: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbea4: r30 = Instance_EdgeInsets
    //     0xbbbea4: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbea8: stp             lr, x16, [SP, #8]
    // 0xbbbeac: r16 = Instance_Alignment
    //     0xbbbeac: add             x16, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xbbbeb0: ldr             x16, [x16, #0x898]
    // 0xbbbeb4: str             x16, [SP]
    // 0xbbbeb8: mov             x1, x0
    // 0xbbbebc: ldur            x2, [fp, #-0x30]
    // 0xbbbec0: r3 = Instance_BoxDecoration
    //     0xbbbec0: add             x3, PP, #0x51, lsl #12  ; [pp+0x510e8] Obj!BoxDecoration@e1d301
    //     0xbbbec4: ldr             x3, [x3, #0xe8]
    // 0xbbbec8: r5 = Instance_Duration
    //     0xbbbec8: add             x5, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbbbecc: ldr             x5, [x5, #0xd90]
    // 0xbbbed0: r4 = const [0, 0x7, 0x3, 0x4, alignment, 0x6, margin, 0x4, padding, 0x5, null]
    //     0xbbbed0: add             x4, PP, #0x51, lsl #12  ; [pp+0x510d0] List(11) [0, 0x7, 0x3, 0x4, "alignment", 0x6, "margin", 0x4, "padding", 0x5, Null]
    //     0xbbbed4: ldr             x4, [x4, #0xd0]
    // 0xbbbed8: r0 = AnimatedContainer()
    //     0xbbbed8: bl              #0xa9b524  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbbbedc: ldur            x0, [fp, #-0x38]
    // 0xbbbee0: b               #0xbbc1cc
    // 0xbbbee4: LoadField: r0 = r1->field_37
    //     0xbbbee4: ldur            w0, [x1, #0x37]
    // 0xbbbee8: DecompressPointer r0
    //     0xbbbee8: add             x0, x0, HEAP, lsl #32
    // 0xbbbeec: tbnz            w0, #4, #0xbbbfb0
    // 0xbbbef0: LoadField: r0 = r1->field_43
    //     0xbbbef0: ldur            w0, [x1, #0x43]
    // 0xbbbef4: DecompressPointer r0
    //     0xbbbef4: add             x0, x0, HEAP, lsl #32
    // 0xbbbef8: LoadField: r3 = r0->field_2b
    //     0xbbbef8: ldur            w3, [x0, #0x2b]
    // 0xbbbefc: DecompressPointer r3
    //     0xbbbefc: add             x3, x3, HEAP, lsl #32
    // 0xbbbf00: cmp             w3, NULL
    // 0xbbbf04: b.ne            #0xbbbf10
    // 0xbbbf08: r0 = Null
    //     0xbbbf08: mov             x0, NULL
    // 0xbbbf0c: b               #0xbbbf38
    // 0xbbbf10: LoadField: r0 = r1->field_f
    //     0xbbbf10: ldur            w0, [x1, #0xf]
    // 0xbbbf14: DecompressPointer r0
    //     0xbbbf14: add             x0, x0, HEAP, lsl #32
    // 0xbbbf18: ldur            x16, [fp, #-0x10]
    // 0xbbbf1c: stp             x16, x3, [SP, #0x10]
    // 0xbbbf20: ldur            x16, [fp, #-0x18]
    // 0xbbbf24: stp             x0, x16, [SP]
    // 0xbbbf28: mov             x0, x3
    // 0xbbbf2c: ClosureCall
    //     0xbbbf2c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbbbf30: ldur            x2, [x0, #0x1f]
    //     0xbbbf34: blr             x2
    // 0xbbbf38: cmp             w0, NULL
    // 0xbbbf3c: b.ne            #0xbbc1cc
    // 0xbbbf40: ldur            x0, [fp, #-0x28]
    // 0xbbbf44: r0 = Text()
    //     0xbbbf44: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbbf48: mov             x1, x0
    // 0xbbbf4c: ldur            x0, [fp, #-0x28]
    // 0xbbbf50: stur            x1, [fp, #-0x30]
    // 0xbbbf54: StoreField: r1->field_b = r0
    //     0xbbbf54: stur            w0, [x1, #0xb]
    // 0xbbbf58: r0 = Instance_TextStyle
    //     0xbbbf58: add             x0, PP, #0x51, lsl #12  ; [pp+0x510f0] Obj!TextStyle@e1b2d1
    //     0xbbbf5c: ldr             x0, [x0, #0xf0]
    // 0xbbbf60: StoreField: r1->field_13 = r0
    //     0xbbbf60: stur            w0, [x1, #0x13]
    // 0xbbbf64: r0 = AnimatedContainer()
    //     0xbbbf64: bl              #0xa9b78c  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbbbf68: stur            x0, [fp, #-0x38]
    // 0xbbbf6c: r16 = Instance_EdgeInsets
    //     0xbbbf6c: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbf70: r30 = Instance_EdgeInsets
    //     0xbbbf70: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbf74: stp             lr, x16, [SP, #8]
    // 0xbbbf78: r16 = Instance_Alignment
    //     0xbbbf78: add             x16, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xbbbf7c: ldr             x16, [x16, #0x898]
    // 0xbbbf80: str             x16, [SP]
    // 0xbbbf84: mov             x1, x0
    // 0xbbbf88: ldur            x2, [fp, #-0x30]
    // 0xbbbf8c: r3 = Instance_BoxDecoration
    //     0xbbbf8c: add             x3, PP, #0x51, lsl #12  ; [pp+0x510f8] Obj!BoxDecoration@e1d241
    //     0xbbbf90: ldr             x3, [x3, #0xf8]
    // 0xbbbf94: r5 = Instance_Duration
    //     0xbbbf94: add             x5, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbbbf98: ldr             x5, [x5, #0xd90]
    // 0xbbbf9c: r4 = const [0, 0x7, 0x3, 0x4, alignment, 0x6, margin, 0x4, padding, 0x5, null]
    //     0xbbbf9c: add             x4, PP, #0x51, lsl #12  ; [pp+0x510d0] List(11) [0, 0x7, 0x3, 0x4, "alignment", 0x6, "margin", 0x4, "padding", 0x5, Null]
    //     0xbbbfa0: ldr             x4, [x4, #0xd0]
    // 0xbbbfa4: r0 = AnimatedContainer()
    //     0xbbbfa4: bl              #0xa9b524  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbbbfa8: ldur            x0, [fp, #-0x38]
    // 0xbbbfac: b               #0xbbc1cc
    // 0xbbbfb0: mov             x0, x2
    // 0xbbbfb4: LoadField: r2 = r1->field_2b
    //     0xbbbfb4: ldur            w2, [x1, #0x2b]
    // 0xbbbfb8: DecompressPointer r2
    //     0xbbbfb8: add             x2, x2, HEAP, lsl #32
    // 0xbbbfbc: tbnz            w2, #4, #0xbbc024
    // 0xbbbfc0: r0 = Text()
    //     0xbbbfc0: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbbfc4: ldur            x2, [fp, #-0x28]
    // 0xbbbfc8: stur            x0, [fp, #-0x30]
    // 0xbbbfcc: StoreField: r0->field_b = r2
    //     0xbbbfcc: stur            w2, [x0, #0xb]
    // 0xbbbfd0: r1 = Instance_TextStyle
    //     0xbbbfd0: ldr             x1, [PP, #0x47e0]  ; [pp+0x47e0] Obj!TextStyle@e1ad91
    // 0xbbbfd4: StoreField: r0->field_13 = r1
    //     0xbbbfd4: stur            w1, [x0, #0x13]
    // 0xbbbfd8: r0 = AnimatedContainer()
    //     0xbbbfd8: bl              #0xa9b78c  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbbbfdc: stur            x0, [fp, #-0x38]
    // 0xbbbfe0: r16 = Instance_EdgeInsets
    //     0xbbbfe0: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbfe4: r30 = Instance_EdgeInsets
    //     0xbbbfe4: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbbfe8: stp             lr, x16, [SP, #8]
    // 0xbbbfec: r16 = Instance_Alignment
    //     0xbbbfec: add             x16, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xbbbff0: ldr             x16, [x16, #0x898]
    // 0xbbbff4: str             x16, [SP]
    // 0xbbbff8: mov             x1, x0
    // 0xbbbffc: ldur            x2, [fp, #-0x30]
    // 0xbbc000: r3 = Instance_BoxDecoration
    //     0xbbc000: add             x3, PP, #0x51, lsl #12  ; [pp+0x510c8] Obj!BoxDecoration@e1d271
    //     0xbbc004: ldr             x3, [x3, #0xc8]
    // 0xbbc008: r5 = Instance_Duration
    //     0xbbc008: add             x5, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbbc00c: ldr             x5, [x5, #0xd90]
    // 0xbbc010: r4 = const [0, 0x7, 0x3, 0x4, alignment, 0x6, margin, 0x4, padding, 0x5, null]
    //     0xbbc010: add             x4, PP, #0x51, lsl #12  ; [pp+0x510d0] List(11) [0, 0x7, 0x3, 0x4, "alignment", 0x6, "margin", 0x4, "padding", 0x5, Null]
    //     0xbbc014: ldr             x4, [x4, #0xd0]
    // 0xbbc018: r0 = AnimatedContainer()
    //     0xbbc018: bl              #0xa9b524  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbbc01c: ldur            x0, [fp, #-0x38]
    // 0xbbc020: b               #0xbbc1cc
    // 0xbbc024: mov             x2, x0
    // 0xbbc028: LoadField: r0 = r1->field_2f
    //     0xbbc028: ldur            w0, [x1, #0x2f]
    // 0xbbc02c: DecompressPointer r0
    //     0xbbc02c: add             x0, x0, HEAP, lsl #32
    // 0xbbc030: tbnz            w0, #4, #0xbbc0f0
    // 0xbbc034: LoadField: r0 = r1->field_43
    //     0xbbc034: ldur            w0, [x1, #0x43]
    // 0xbbc038: DecompressPointer r0
    //     0xbbc038: add             x0, x0, HEAP, lsl #32
    // 0xbbc03c: LoadField: r3 = r0->field_23
    //     0xbbc03c: ldur            w3, [x0, #0x23]
    // 0xbbc040: DecompressPointer r3
    //     0xbbc040: add             x3, x3, HEAP, lsl #32
    // 0xbbc044: cmp             w3, NULL
    // 0xbbc048: b.ne            #0xbbc054
    // 0xbbc04c: r0 = Null
    //     0xbbc04c: mov             x0, NULL
    // 0xbbc050: b               #0xbbc07c
    // 0xbbc054: LoadField: r0 = r1->field_f
    //     0xbbc054: ldur            w0, [x1, #0xf]
    // 0xbbc058: DecompressPointer r0
    //     0xbbc058: add             x0, x0, HEAP, lsl #32
    // 0xbbc05c: ldur            x16, [fp, #-0x10]
    // 0xbbc060: stp             x16, x3, [SP, #0x10]
    // 0xbbc064: ldur            x16, [fp, #-0x18]
    // 0xbbc068: stp             x0, x16, [SP]
    // 0xbbc06c: mov             x0, x3
    // 0xbbc070: ClosureCall
    //     0xbbc070: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbbc074: ldur            x2, [x0, #0x1f]
    //     0xbbc078: blr             x2
    // 0xbbc07c: cmp             w0, NULL
    // 0xbbc080: b.ne            #0xbbc1cc
    // 0xbbc084: ldur            x0, [fp, #-0x28]
    // 0xbbc088: r0 = Text()
    //     0xbbc088: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbc08c: ldur            x2, [fp, #-0x28]
    // 0xbbc090: stur            x0, [fp, #-0x30]
    // 0xbbc094: StoreField: r0->field_b = r2
    //     0xbbc094: stur            w2, [x0, #0xb]
    // 0xbbc098: r1 = Instance_TextStyle
    //     0xbbc098: add             x1, PP, #0x51, lsl #12  ; [pp+0x51100] Obj!TextStyle@e1b3b1
    //     0xbbc09c: ldr             x1, [x1, #0x100]
    // 0xbbc0a0: StoreField: r0->field_13 = r1
    //     0xbbc0a0: stur            w1, [x0, #0x13]
    // 0xbbc0a4: r0 = AnimatedContainer()
    //     0xbbc0a4: bl              #0xa9b78c  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbbc0a8: stur            x0, [fp, #-0x38]
    // 0xbbc0ac: r16 = Instance_EdgeInsets
    //     0xbbc0ac: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbc0b0: r30 = Instance_EdgeInsets
    //     0xbbc0b0: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbc0b4: stp             lr, x16, [SP, #8]
    // 0xbbc0b8: r16 = Instance_Alignment
    //     0xbbc0b8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xbbc0bc: ldr             x16, [x16, #0x898]
    // 0xbbc0c0: str             x16, [SP]
    // 0xbbc0c4: mov             x1, x0
    // 0xbbc0c8: ldur            x2, [fp, #-0x30]
    // 0xbbc0cc: r3 = Instance_BoxDecoration
    //     0xbbc0cc: add             x3, PP, #0x51, lsl #12  ; [pp+0x510c8] Obj!BoxDecoration@e1d271
    //     0xbbc0d0: ldr             x3, [x3, #0xc8]
    // 0xbbc0d4: r5 = Instance_Duration
    //     0xbbc0d4: add             x5, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbbc0d8: ldr             x5, [x5, #0xd90]
    // 0xbbc0dc: r4 = const [0, 0x7, 0x3, 0x4, alignment, 0x6, margin, 0x4, padding, 0x5, null]
    //     0xbbc0dc: add             x4, PP, #0x51, lsl #12  ; [pp+0x510d0] List(11) [0, 0x7, 0x3, 0x4, "alignment", 0x6, "margin", 0x4, "padding", 0x5, Null]
    //     0xbbc0e0: ldr             x4, [x4, #0xd0]
    // 0xbbc0e4: r0 = AnimatedContainer()
    //     0xbbc0e4: bl              #0xa9b524  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbbc0e8: ldur            x0, [fp, #-0x38]
    // 0xbbc0ec: b               #0xbbc1cc
    // 0xbbc0f0: LoadField: r0 = r1->field_43
    //     0xbbc0f0: ldur            w0, [x1, #0x43]
    // 0xbbc0f4: DecompressPointer r0
    //     0xbbc0f4: add             x0, x0, HEAP, lsl #32
    // 0xbbc0f8: LoadField: r3 = r0->field_2f
    //     0xbbc0f8: ldur            w3, [x0, #0x2f]
    // 0xbbc0fc: DecompressPointer r3
    //     0xbbc0fc: add             x3, x3, HEAP, lsl #32
    // 0xbbc100: cmp             w3, NULL
    // 0xbbc104: b.ne            #0xbbc110
    // 0xbbc108: r0 = Null
    //     0xbbc108: mov             x0, NULL
    // 0xbbc10c: b               #0xbbc138
    // 0xbbc110: LoadField: r0 = r1->field_f
    //     0xbbc110: ldur            w0, [x1, #0xf]
    // 0xbbc114: DecompressPointer r0
    //     0xbbc114: add             x0, x0, HEAP, lsl #32
    // 0xbbc118: ldur            x16, [fp, #-0x10]
    // 0xbbc11c: stp             x16, x3, [SP, #0x10]
    // 0xbbc120: ldur            x16, [fp, #-0x18]
    // 0xbbc124: stp             x0, x16, [SP]
    // 0xbbc128: mov             x0, x3
    // 0xbbc12c: ClosureCall
    //     0xbbc12c: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbbc130: ldur            x2, [x0, #0x1f]
    //     0xbbc134: blr             x2
    // 0xbbc138: cmp             w0, NULL
    // 0xbbc13c: b.ne            #0xbbc1cc
    // 0xbbc140: ldur            x0, [fp, #-8]
    // 0xbbc144: LoadField: r1 = r0->field_3b
    //     0xbbc144: ldur            w1, [x0, #0x3b]
    // 0xbbc148: DecompressPointer r1
    //     0xbbc148: add             x1, x1, HEAP, lsl #32
    // 0xbbc14c: tbnz            w1, #4, #0xbbc15c
    // 0xbbc150: r1 = Instance_TextStyle
    //     0xbbc150: add             x1, PP, #0x51, lsl #12  ; [pp+0x51108] Obj!TextStyle@e1b261
    //     0xbbc154: ldr             x1, [x1, #0x108]
    // 0xbbc158: b               #0xbbc160
    // 0xbbc15c: r1 = Instance_TextStyle
    //     0xbbc15c: ldr             x1, [PP, #0x47e0]  ; [pp+0x47e0] Obj!TextStyle@e1ad91
    // 0xbbc160: ldur            x0, [fp, #-0x28]
    // 0xbbc164: stur            x1, [fp, #-8]
    // 0xbbc168: r0 = Text()
    //     0xbbc168: bl              #0x65dc4c  ; AllocateTextStub -> Text (size=0x4c)
    // 0xbbc16c: mov             x1, x0
    // 0xbbc170: ldur            x0, [fp, #-0x28]
    // 0xbbc174: stur            x1, [fp, #-0x10]
    // 0xbbc178: StoreField: r1->field_b = r0
    //     0xbbc178: stur            w0, [x1, #0xb]
    // 0xbbc17c: ldur            x0, [fp, #-8]
    // 0xbbc180: StoreField: r1->field_13 = r0
    //     0xbbc180: stur            w0, [x1, #0x13]
    // 0xbbc184: r0 = AnimatedContainer()
    //     0xbbc184: bl              #0xa9b78c  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbbc188: stur            x0, [fp, #-8]
    // 0xbbc18c: r16 = Instance_EdgeInsets
    //     0xbbc18c: ldr             x16, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbc190: r30 = Instance_EdgeInsets
    //     0xbbc190: ldr             lr, [PP, #0x5000]  ; [pp+0x5000] Obj!EdgeInsets@e11f81
    // 0xbbc194: stp             lr, x16, [SP, #8]
    // 0xbbc198: r16 = Instance_Alignment
    //     0xbbc198: add             x16, PP, #0x25, lsl #12  ; [pp+0x25898] Obj!Alignment@e13df1
    //     0xbbc19c: ldr             x16, [x16, #0x898]
    // 0xbbc1a0: str             x16, [SP]
    // 0xbbc1a4: mov             x1, x0
    // 0xbbc1a8: ldur            x2, [fp, #-0x10]
    // 0xbbc1ac: r3 = Instance_BoxDecoration
    //     0xbbc1ac: add             x3, PP, #0x51, lsl #12  ; [pp+0x510c8] Obj!BoxDecoration@e1d271
    //     0xbbc1b0: ldr             x3, [x3, #0xc8]
    // 0xbbc1b4: r5 = Instance_Duration
    //     0xbbc1b4: add             x5, PP, #0x11, lsl #12  ; [pp+0x11d90] Obj!Duration@e3a0e1
    //     0xbbc1b8: ldr             x5, [x5, #0xd90]
    // 0xbbc1bc: r4 = const [0, 0x7, 0x3, 0x4, alignment, 0x6, margin, 0x4, padding, 0x5, null]
    //     0xbbc1bc: add             x4, PP, #0x51, lsl #12  ; [pp+0x510d0] List(11) [0, 0x7, 0x3, 0x4, "alignment", 0x6, "margin", 0x4, "padding", 0x5, Null]
    //     0xbbc1c0: ldr             x4, [x4, #0xd0]
    // 0xbbc1c4: r0 = AnimatedContainer()
    //     0xbbc1c4: bl              #0xa9b524  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbbc1c8: ldur            x0, [fp, #-8]
    // 0xbbc1cc: stur            x0, [fp, #-8]
    // 0xbbc1d0: r0 = Semantics()
    //     0xbbc1d0: bl              #0x7e4d0c  ; AllocateSemanticsStub -> Semantics (size=0x24)
    // 0xbbc1d4: stur            x0, [fp, #-0x10]
    // 0xbbc1d8: ldur            x16, [fp, #-0x20]
    // 0xbbc1dc: r30 = true
    //     0xbbc1dc: add             lr, NULL, #0x20  ; true
    // 0xbbc1e0: stp             lr, x16, [SP, #8]
    // 0xbbc1e4: ldur            x16, [fp, #-8]
    // 0xbbc1e8: str             x16, [SP]
    // 0xbbc1ec: mov             x1, x0
    // 0xbbc1f0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, excludeSemantics, 0x2, label, 0x1, null]
    //     0xbbc1f0: add             x4, PP, #0x51, lsl #12  ; [pp+0x51110] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "excludeSemantics", 0x2, "label", 0x1, Null]
    //     0xbbc1f4: ldr             x4, [x4, #0x110]
    // 0xbbc1f8: r0 = Semantics()
    //     0xbbc1f8: bl              #0x7e3d8c  ; [package:flutter/src/widgets/basic.dart] Semantics::Semantics
    // 0xbbc1fc: ldur            x0, [fp, #-0x10]
    // 0xbbc200: LeaveFrame
    //     0xbbc200: mov             SP, fp
    //     0xbbc204: ldp             fp, lr, [SP], #0x10
    // 0xbbc208: ret
    //     0xbbc208: ret             
    // 0xbbc20c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbc20c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbc210: b               #0xbbbb64
  }
}
