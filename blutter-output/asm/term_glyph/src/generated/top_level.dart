// lib: , url: package:term_glyph/src/generated/top_level.dart

// class id: 1051196, size: 0x8
class :: {

  String upEnd() {
    // ** addr: 0xc15578, size: 0x50
    // 0xc15578: EnterFrame
    //     0xc15578: stp             fp, lr, [SP, #-0x10]!
    //     0xc1557c: mov             fp, SP
    // 0xc15580: CheckStackOverflow
    //     0xc15580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15584: cmp             SP, x16
    //     0xc15588: b.ls            #0xc155c0
    // 0xc1558c: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc1558c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc15590: ldr             x0, [x0, #0x2ea8]
    //     0xc15594: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc15598: cmp             w0, w16
    //     0xc1559c: b.ne            #0xc155ac
    //     0xc155a0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc155a4: ldr             x2, [x2, #0x580]
    //     0xc155a8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc155ac: r0 = "╵"
    //     0xc155ac: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c588] "╵"
    //     0xc155b0: ldr             x0, [x0, #0x588]
    // 0xc155b4: LeaveFrame
    //     0xc155b4: mov             SP, fp
    //     0xc155b8: ldp             fp, lr, [SP], #0x10
    // 0xc155bc: ret
    //     0xc155bc: ret             
    // 0xc155c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc155c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc155c4: b               #0xc1558c
  }
  String horizontalLine() {
    // ** addr: 0xc15ec0, size: 0x50
    // 0xc15ec0: EnterFrame
    //     0xc15ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xc15ec4: mov             fp, SP
    // 0xc15ec8: CheckStackOverflow
    //     0xc15ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc15ecc: cmp             SP, x16
    //     0xc15ed0: b.ls            #0xc15f08
    // 0xc15ed4: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc15ed4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc15ed8: ldr             x0, [x0, #0x2ea8]
    //     0xc15edc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc15ee0: cmp             w0, w16
    //     0xc15ee4: b.ne            #0xc15ef4
    //     0xc15ee8: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc15eec: ldr             x2, [x2, #0x580]
    //     0xc15ef0: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc15ef4: r0 = "─"
    //     0xc15ef4: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c5f0] "─"
    //     0xc15ef8: ldr             x0, [x0, #0x5f0]
    // 0xc15efc: LeaveFrame
    //     0xc15efc: mov             SP, fp
    //     0xc15f00: ldp             fp, lr, [SP], #0x10
    // 0xc15f04: ret
    //     0xc15f04: ret             
    // 0xc15f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc15f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc15f0c: b               #0xc15ed4
  }
  String cross() {
    // ** addr: 0xc16cec, size: 0x50
    // 0xc16cec: EnterFrame
    //     0xc16cec: stp             fp, lr, [SP, #-0x10]!
    //     0xc16cf0: mov             fp, SP
    // 0xc16cf4: CheckStackOverflow
    //     0xc16cf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16cf8: cmp             SP, x16
    //     0xc16cfc: b.ls            #0xc16d34
    // 0xc16d00: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc16d00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc16d04: ldr             x0, [x0, #0x2ea8]
    //     0xc16d08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc16d0c: cmp             w0, w16
    //     0xc16d10: b.ne            #0xc16d20
    //     0xc16d14: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc16d18: ldr             x2, [x2, #0x580]
    //     0xc16d1c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc16d20: r0 = "┼"
    //     0xc16d20: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c648] "┼"
    //     0xc16d24: ldr             x0, [x0, #0x648]
    // 0xc16d28: LeaveFrame
    //     0xc16d28: mov             SP, fp
    //     0xc16d2c: ldp             fp, lr, [SP], #0x10
    // 0xc16d30: ret
    //     0xc16d30: ret             
    // 0xc16d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16d34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16d38: b               #0xc16d00
  }
  String bottomLeftCorner() {
    // ** addr: 0xc17068, size: 0x50
    // 0xc17068: EnterFrame
    //     0xc17068: stp             fp, lr, [SP, #-0x10]!
    //     0xc1706c: mov             fp, SP
    // 0xc17070: CheckStackOverflow
    //     0xc17070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17074: cmp             SP, x16
    //     0xc17078: b.ls            #0xc170b0
    // 0xc1707c: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc1707c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc17080: ldr             x0, [x0, #0x2ea8]
    //     0xc17084: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc17088: cmp             w0, w16
    //     0xc1708c: b.ne            #0xc1709c
    //     0xc17090: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc17094: ldr             x2, [x2, #0x580]
    //     0xc17098: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc1709c: r0 = "└"
    //     0xc1709c: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c658] "└"
    //     0xc170a0: ldr             x0, [x0, #0x658]
    // 0xc170a4: LeaveFrame
    //     0xc170a4: mov             SP, fp
    //     0xc170a8: ldp             fp, lr, [SP], #0x10
    // 0xc170ac: ret
    //     0xc170ac: ret             
    // 0xc170b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc170b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc170b4: b               #0xc1707c
  }
  String topLeftCorner() {
    // ** addr: 0xc170b8, size: 0x50
    // 0xc170b8: EnterFrame
    //     0xc170b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc170bc: mov             fp, SP
    // 0xc170c0: CheckStackOverflow
    //     0xc170c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc170c4: cmp             SP, x16
    //     0xc170c8: b.ls            #0xc17100
    // 0xc170cc: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc170cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc170d0: ldr             x0, [x0, #0x2ea8]
    //     0xc170d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc170d8: cmp             w0, w16
    //     0xc170dc: b.ne            #0xc170ec
    //     0xc170e0: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc170e4: ldr             x2, [x2, #0x580]
    //     0xc170e8: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc170ec: r0 = "┌"
    //     0xc170ec: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c688] "┌"
    //     0xc170f0: ldr             x0, [x0, #0x688]
    // 0xc170f4: LeaveFrame
    //     0xc170f4: mov             SP, fp
    //     0xc170f8: ldp             fp, lr, [SP], #0x10
    // 0xc170fc: ret
    //     0xc170fc: ret             
    // 0xc17100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17104: b               #0xc170cc
  }
  String downEnd() {
    // ** addr: 0xc18448, size: 0x50
    // 0xc18448: EnterFrame
    //     0xc18448: stp             fp, lr, [SP, #-0x10]!
    //     0xc1844c: mov             fp, SP
    // 0xc18450: CheckStackOverflow
    //     0xc18450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18454: cmp             SP, x16
    //     0xc18458: b.ls            #0xc18490
    // 0xc1845c: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc1845c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc18460: ldr             x0, [x0, #0x2ea8]
    //     0xc18464: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc18468: cmp             w0, w16
    //     0xc1846c: b.ne            #0xc1847c
    //     0xc18470: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc18474: ldr             x2, [x2, #0x580]
    //     0xc18478: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc1847c: r0 = "╷"
    //     0xc1847c: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c698] "╷"
    //     0xc18480: ldr             x0, [x0, #0x698]
    // 0xc18484: LeaveFrame
    //     0xc18484: mov             SP, fp
    //     0xc18488: ldp             fp, lr, [SP], #0x10
    // 0xc1848c: ret
    //     0xc1848c: ret             
    // 0xc18490: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18490: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18494: b               #0xc1845c
  }
  String verticalLine() {
    // ** addr: 0xc18610, size: 0x50
    // 0xc18610: EnterFrame
    //     0xc18610: stp             fp, lr, [SP, #-0x10]!
    //     0xc18614: mov             fp, SP
    // 0xc18618: CheckStackOverflow
    //     0xc18618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1861c: cmp             SP, x16
    //     0xc18620: b.ls            #0xc18658
    // 0xc18624: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc18624: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc18628: ldr             x0, [x0, #0x2ea8]
    //     0xc1862c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc18630: cmp             w0, w16
    //     0xc18634: b.ne            #0xc18644
    //     0xc18638: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc1863c: ldr             x2, [x2, #0x580]
    //     0xc18640: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc18644: r0 = "│"
    //     0xc18644: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c5c0] "│"
    //     0xc18648: ldr             x0, [x0, #0x5c0]
    // 0xc1864c: LeaveFrame
    //     0xc1864c: mov             SP, fp
    //     0xc18650: ldp             fp, lr, [SP], #0x10
    // 0xc18654: ret
    //     0xc18654: ret             
    // 0xc18658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1865c: b               #0xc18624
  }
}
