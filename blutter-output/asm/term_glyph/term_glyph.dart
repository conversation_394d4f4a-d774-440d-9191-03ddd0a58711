// lib: , url: package:term_glyph/term_glyph.dart

// class id: 1051198, size: 0x8
class :: {

  static late GlyphSet _glyphs; // offset: 0x1754

  static _ glyphOrAscii(/* No info */) {
    // ** addr: 0xc16c94, size: 0x58
    // 0xc16c94: EnterFrame
    //     0xc16c94: stp             fp, lr, [SP, #-0x10]!
    //     0xc16c98: mov             fp, SP
    // 0xc16c9c: AllocStack(0x8)
    //     0xc16c9c: sub             SP, SP, #8
    // 0xc16ca0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc16ca0: mov             x0, x1
    //     0xc16ca4: stur            x1, [fp, #-8]
    // 0xc16ca8: CheckStackOverflow
    //     0xc16ca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16cac: cmp             SP, x16
    //     0xc16cb0: b.ls            #0xc16ce4
    // 0xc16cb4: r0 = InitLateStaticField(0x1754) // [package:term_glyph/term_glyph.dart] ::_glyphs
    //     0xc16cb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc16cb8: ldr             x0, [x0, #0x2ea8]
    //     0xc16cbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc16cc0: cmp             w0, w16
    //     0xc16cc4: b.ne            #0xc16cd4
    //     0xc16cc8: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c580] Field <::._glyphs@2707078287>: static late (offset: 0x1754)
    //     0xc16ccc: ldr             x2, [x2, #0x580]
    //     0xc16cd0: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc16cd4: ldur            x0, [fp, #-8]
    // 0xc16cd8: LeaveFrame
    //     0xc16cd8: mov             SP, fp
    //     0xc16cdc: ldp             fp, lr, [SP], #0x10
    // 0xc16ce0: ret
    //     0xc16ce0: ret             
    // 0xc16ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16ce4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16ce8: b               #0xc16cb4
  }
  static GlyphSet _glyphs() {
    // ** addr: 0xc18660, size: 0xc
    // 0xc18660: r0 = Instance_UnicodeGlyphSet
    //     0xc18660: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c6a8] Obj!UnicodeGlyphSet@e0bf51
    //     0xc18664: ldr             x0, [x0, #0x6a8]
    // 0xc18668: ret
    //     0xc18668: ret             
  }
}
