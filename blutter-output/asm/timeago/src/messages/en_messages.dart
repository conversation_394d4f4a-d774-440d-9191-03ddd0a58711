// lib: , url: package:timeago/src/messages/en_messages.dart

// class id: 1051199, size: 0x8
class :: {
}

// class id: 439, size: 0x8, field offset: 0x8
class EnShortMessages extends Object
    implements LookupMessages {

  _ lessThanOneMinute(/* No info */) {
    // ** addr: 0xe63574, size: 0xc
    // 0xe63574: r0 = "now"
    //     0xe63574: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ee8] "now"
    //     0xe63578: ldr             x0, [x0, #0xee8]
    // 0xe6357c: ret
    //     0xe6357c: ret             
  }
  _ aboutAMinute(/* No info */) {
    // ** addr: 0xeabde8, size: 0xc
    // 0xeabde8: r0 = "1m"
    //     0xeabde8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ee0] "1m"
    //     0xeabdec: ldr             x0, [x0, #0xee0]
    // 0xeabdf0: ret
    //     0xeabdf0: ret             
  }
  _ minutes(/* No info */) {
    // ** addr: 0xeaf2b4, size: 0x70
    // 0xeaf2b4: EnterFrame
    //     0xeaf2b4: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf2b8: mov             fp, SP
    // 0xeaf2bc: AllocStack(0x10)
    //     0xeaf2bc: sub             SP, SP, #0x10
    // 0xeaf2c0: CheckStackOverflow
    //     0xeaf2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf2c4: cmp             SP, x16
    //     0xeaf2c8: b.ls            #0xeaf31c
    // 0xeaf2cc: r0 = BoxInt64Instr(r2)
    //     0xeaf2cc: sbfiz           x0, x2, #1, #0x1f
    //     0xeaf2d0: cmp             x2, x0, asr #1
    //     0xeaf2d4: b.eq            #0xeaf2e0
    //     0xeaf2d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaf2dc: stur            x2, [x0, #7]
    // 0xeaf2e0: r1 = Null
    //     0xeaf2e0: mov             x1, NULL
    // 0xeaf2e4: r2 = 4
    //     0xeaf2e4: movz            x2, #0x4
    // 0xeaf2e8: stur            x0, [fp, #-8]
    // 0xeaf2ec: r0 = AllocateArray()
    //     0xeaf2ec: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeaf2f0: mov             x1, x0
    // 0xeaf2f4: ldur            x0, [fp, #-8]
    // 0xeaf2f8: StoreField: r1->field_f = r0
    //     0xeaf2f8: stur            w0, [x1, #0xf]
    // 0xeaf2fc: r16 = "m"
    //     0xeaf2fc: add             x16, PP, #8, lsl #12  ; [pp+0x8eb8] "m"
    //     0xeaf300: ldr             x16, [x16, #0xeb8]
    // 0xeaf304: StoreField: r1->field_13 = r16
    //     0xeaf304: stur            w16, [x1, #0x13]
    // 0xeaf308: str             x1, [SP]
    // 0xeaf30c: r0 = _interpolate()
    //     0xeaf30c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeaf310: LeaveFrame
    //     0xeaf310: mov             SP, fp
    //     0xeaf314: ldp             fp, lr, [SP], #0x10
    // 0xeaf318: ret
    //     0xeaf318: ret             
    // 0xeaf31c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf31c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf320: b               #0xeaf2cc
  }
  _ aboutAnHour(/* No info */) {
    // ** addr: 0xeb5078, size: 0xc
    // 0xeb5078: r0 = "~1h"
    //     0xeb5078: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ed8] "~1h"
    //     0xeb507c: ldr             x0, [x0, #0xed8]
    // 0xeb5080: ret
    //     0xeb5080: ret             
  }
  _ hours(/* No info */) {
    // ** addr: 0xeb5118, size: 0x70
    // 0xeb5118: EnterFrame
    //     0xeb5118: stp             fp, lr, [SP, #-0x10]!
    //     0xeb511c: mov             fp, SP
    // 0xeb5120: AllocStack(0x10)
    //     0xeb5120: sub             SP, SP, #0x10
    // 0xeb5124: CheckStackOverflow
    //     0xeb5124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5128: cmp             SP, x16
    //     0xeb512c: b.ls            #0xeb5180
    // 0xeb5130: r0 = BoxInt64Instr(r2)
    //     0xeb5130: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5134: cmp             x2, x0, asr #1
    //     0xeb5138: b.eq            #0xeb5144
    //     0xeb513c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5140: stur            x2, [x0, #7]
    // 0xeb5144: r1 = Null
    //     0xeb5144: mov             x1, NULL
    // 0xeb5148: r2 = 4
    //     0xeb5148: movz            x2, #0x4
    // 0xeb514c: stur            x0, [fp, #-8]
    // 0xeb5150: r0 = AllocateArray()
    //     0xeb5150: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb5154: mov             x1, x0
    // 0xeb5158: ldur            x0, [fp, #-8]
    // 0xeb515c: StoreField: r1->field_f = r0
    //     0xeb515c: stur            w0, [x1, #0xf]
    // 0xeb5160: r16 = "h"
    //     0xeb5160: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7e0] "h"
    //     0xeb5164: ldr             x16, [x16, #0x7e0]
    // 0xeb5168: StoreField: r1->field_13 = r16
    //     0xeb5168: stur            w16, [x1, #0x13]
    // 0xeb516c: str             x1, [SP]
    // 0xeb5170: r0 = _interpolate()
    //     0xeb5170: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb5174: LeaveFrame
    //     0xeb5174: mov             SP, fp
    //     0xeb5178: ldp             fp, lr, [SP], #0x10
    // 0xeb517c: ret
    //     0xeb517c: ret             
    // 0xeb5180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5180: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5184: b               #0xeb5130
  }
  _ aDay(/* No info */) {
    // ** addr: 0xeb5390, size: 0xc
    // 0xeb5390: r0 = "~1d"
    //     0xeb5390: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ed0] "~1d"
    //     0xeb5394: ldr             x0, [x0, #0xed0]
    // 0xeb5398: ret
    //     0xeb5398: ret             
  }
  _ days(/* No info */) {
    // ** addr: 0xeb5430, size: 0x70
    // 0xeb5430: EnterFrame
    //     0xeb5430: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5434: mov             fp, SP
    // 0xeb5438: AllocStack(0x10)
    //     0xeb5438: sub             SP, SP, #0x10
    // 0xeb543c: CheckStackOverflow
    //     0xeb543c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5440: cmp             SP, x16
    //     0xeb5444: b.ls            #0xeb5498
    // 0xeb5448: r0 = BoxInt64Instr(r2)
    //     0xeb5448: sbfiz           x0, x2, #1, #0x1f
    //     0xeb544c: cmp             x2, x0, asr #1
    //     0xeb5450: b.eq            #0xeb545c
    //     0xeb5454: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5458: stur            x2, [x0, #7]
    // 0xeb545c: r1 = Null
    //     0xeb545c: mov             x1, NULL
    // 0xeb5460: r2 = 4
    //     0xeb5460: movz            x2, #0x4
    // 0xeb5464: stur            x0, [fp, #-8]
    // 0xeb5468: r0 = AllocateArray()
    //     0xeb5468: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb546c: mov             x1, x0
    // 0xeb5470: ldur            x0, [fp, #-8]
    // 0xeb5474: StoreField: r1->field_f = r0
    //     0xeb5474: stur            w0, [x1, #0xf]
    // 0xeb5478: r16 = "d"
    //     0xeb5478: add             x16, PP, #8, lsl #12  ; [pp+0x8e90] "d"
    //     0xeb547c: ldr             x16, [x16, #0xe90]
    // 0xeb5480: StoreField: r1->field_13 = r16
    //     0xeb5480: stur            w16, [x1, #0x13]
    // 0xeb5484: str             x1, [SP]
    // 0xeb5488: r0 = _interpolate()
    //     0xeb5488: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb548c: LeaveFrame
    //     0xeb548c: mov             SP, fp
    //     0xeb5490: ldp             fp, lr, [SP], #0x10
    // 0xeb5494: ret
    //     0xeb5494: ret             
    // 0xeb5498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5498: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb549c: b               #0xeb5448
  }
  _ aboutAMonth(/* No info */) {
    // ** addr: 0xeb5b00, size: 0xc
    // 0xeb5b00: r0 = "~1mo"
    //     0xeb5b00: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ec8] "~1mo"
    //     0xeb5b04: ldr             x0, [x0, #0xec8]
    // 0xeb5b08: ret
    //     0xeb5b08: ret             
  }
  _ months(/* No info */) {
    // ** addr: 0xeb5ba0, size: 0x70
    // 0xeb5ba0: EnterFrame
    //     0xeb5ba0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5ba4: mov             fp, SP
    // 0xeb5ba8: AllocStack(0x10)
    //     0xeb5ba8: sub             SP, SP, #0x10
    // 0xeb5bac: CheckStackOverflow
    //     0xeb5bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5bb0: cmp             SP, x16
    //     0xeb5bb4: b.ls            #0xeb5c08
    // 0xeb5bb8: r0 = BoxInt64Instr(r2)
    //     0xeb5bb8: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5bbc: cmp             x2, x0, asr #1
    //     0xeb5bc0: b.eq            #0xeb5bcc
    //     0xeb5bc4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5bc8: stur            x2, [x0, #7]
    // 0xeb5bcc: r1 = Null
    //     0xeb5bcc: mov             x1, NULL
    // 0xeb5bd0: r2 = 4
    //     0xeb5bd0: movz            x2, #0x4
    // 0xeb5bd4: stur            x0, [fp, #-8]
    // 0xeb5bd8: r0 = AllocateArray()
    //     0xeb5bd8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb5bdc: mov             x1, x0
    // 0xeb5be0: ldur            x0, [fp, #-8]
    // 0xeb5be4: StoreField: r1->field_f = r0
    //     0xeb5be4: stur            w0, [x1, #0xf]
    // 0xeb5be8: r16 = "mo"
    //     0xeb5be8: add             x16, PP, #9, lsl #12  ; [pp+0x9880] "mo"
    //     0xeb5bec: ldr             x16, [x16, #0x880]
    // 0xeb5bf0: StoreField: r1->field_13 = r16
    //     0xeb5bf0: stur            w16, [x1, #0x13]
    // 0xeb5bf4: str             x1, [SP]
    // 0xeb5bf8: r0 = _interpolate()
    //     0xeb5bf8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb5bfc: LeaveFrame
    //     0xeb5bfc: mov             SP, fp
    //     0xeb5c00: ldp             fp, lr, [SP], #0x10
    // 0xeb5c04: ret
    //     0xeb5c04: ret             
    // 0xeb5c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5c08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5c0c: b               #0xeb5bb8
  }
  _ aboutAYear(/* No info */) {
    // ** addr: 0xeb5d7c, size: 0xc
    // 0xeb5d7c: r0 = "~1y"
    //     0xeb5d7c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ec0] "~1y"
    //     0xeb5d80: ldr             x0, [x0, #0xec0]
    // 0xeb5d84: ret
    //     0xeb5d84: ret             
  }
  _ years(/* No info */) {
    // ** addr: 0xeb6f08, size: 0x6c
    // 0xeb6f08: EnterFrame
    //     0xeb6f08: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6f0c: mov             fp, SP
    // 0xeb6f10: AllocStack(0x10)
    //     0xeb6f10: sub             SP, SP, #0x10
    // 0xeb6f14: CheckStackOverflow
    //     0xeb6f14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6f18: cmp             SP, x16
    //     0xeb6f1c: b.ls            #0xeb6f6c
    // 0xeb6f20: r0 = BoxInt64Instr(r2)
    //     0xeb6f20: sbfiz           x0, x2, #1, #0x1f
    //     0xeb6f24: cmp             x2, x0, asr #1
    //     0xeb6f28: b.eq            #0xeb6f34
    //     0xeb6f2c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb6f30: stur            x2, [x0, #7]
    // 0xeb6f34: r1 = Null
    //     0xeb6f34: mov             x1, NULL
    // 0xeb6f38: r2 = 4
    //     0xeb6f38: movz            x2, #0x4
    // 0xeb6f3c: stur            x0, [fp, #-8]
    // 0xeb6f40: r0 = AllocateArray()
    //     0xeb6f40: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb6f44: mov             x1, x0
    // 0xeb6f48: ldur            x0, [fp, #-8]
    // 0xeb6f4c: StoreField: r1->field_f = r0
    //     0xeb6f4c: stur            w0, [x1, #0xf]
    // 0xeb6f50: r16 = "y"
    //     0xeb6f50: ldr             x16, [PP, #0x71a8]  ; [pp+0x71a8] "y"
    // 0xeb6f54: StoreField: r1->field_13 = r16
    //     0xeb6f54: stur            w16, [x1, #0x13]
    // 0xeb6f58: str             x1, [SP]
    // 0xeb6f5c: r0 = _interpolate()
    //     0xeb6f5c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb6f60: LeaveFrame
    //     0xeb6f60: mov             SP, fp
    //     0xeb6f64: ldp             fp, lr, [SP], #0x10
    // 0xeb6f68: ret
    //     0xeb6f68: ret             
    // 0xeb6f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6f6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6f70: b               #0xeb6f20
  }
}

// class id: 441, size: 0x8, field offset: 0x8
class EnMessages extends Object
    implements LookupMessages {

  _ suffixAgo(/* No info */) {
    // ** addr: 0xe62178, size: 0xc
    // 0xe62178: r0 = "ago"
    //     0xe62178: add             x0, PP, #0x34, lsl #12  ; [pp+0x34f48] "ago"
    //     0xe6217c: ldr             x0, [x0, #0xf48]
    // 0xe62180: ret
    //     0xe62180: ret             
  }
  _ lessThanOneMinute(/* No info */) {
    // ** addr: 0xe63568, size: 0xc
    // 0xe63568: r0 = "a moment"
    //     0xe63568: add             x0, PP, #0x34, lsl #12  ; [pp+0x34f40] "a moment"
    //     0xe6356c: ldr             x0, [x0, #0xf40]
    // 0xe63570: ret
    //     0xe63570: ret             
  }
  _ aboutAMinute(/* No info */) {
    // ** addr: 0xeabddc, size: 0xc
    // 0xeabddc: r0 = "a minute"
    //     0xeabddc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34f38] "a minute"
    //     0xeabde0: ldr             x0, [x0, #0xf38]
    // 0xeabde4: ret
    //     0xeabde4: ret             
  }
  _ minutes(/* No info */) {
    // ** addr: 0xeaf244, size: 0x70
    // 0xeaf244: EnterFrame
    //     0xeaf244: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf248: mov             fp, SP
    // 0xeaf24c: AllocStack(0x10)
    //     0xeaf24c: sub             SP, SP, #0x10
    // 0xeaf250: CheckStackOverflow
    //     0xeaf250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf254: cmp             SP, x16
    //     0xeaf258: b.ls            #0xeaf2ac
    // 0xeaf25c: r0 = BoxInt64Instr(r2)
    //     0xeaf25c: sbfiz           x0, x2, #1, #0x1f
    //     0xeaf260: cmp             x2, x0, asr #1
    //     0xeaf264: b.eq            #0xeaf270
    //     0xeaf268: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaf26c: stur            x2, [x0, #7]
    // 0xeaf270: r1 = Null
    //     0xeaf270: mov             x1, NULL
    // 0xeaf274: r2 = 4
    //     0xeaf274: movz            x2, #0x4
    // 0xeaf278: stur            x0, [fp, #-8]
    // 0xeaf27c: r0 = AllocateArray()
    //     0xeaf27c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeaf280: mov             x1, x0
    // 0xeaf284: ldur            x0, [fp, #-8]
    // 0xeaf288: StoreField: r1->field_f = r0
    //     0xeaf288: stur            w0, [x1, #0xf]
    // 0xeaf28c: r16 = " minutes"
    //     0xeaf28c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f30] " minutes"
    //     0xeaf290: ldr             x16, [x16, #0xf30]
    // 0xeaf294: StoreField: r1->field_13 = r16
    //     0xeaf294: stur            w16, [x1, #0x13]
    // 0xeaf298: str             x1, [SP]
    // 0xeaf29c: r0 = _interpolate()
    //     0xeaf29c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeaf2a0: LeaveFrame
    //     0xeaf2a0: mov             SP, fp
    //     0xeaf2a4: ldp             fp, lr, [SP], #0x10
    // 0xeaf2a8: ret
    //     0xeaf2a8: ret             
    // 0xeaf2ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf2ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf2b0: b               #0xeaf25c
  }
  _ aboutAnHour(/* No info */) {
    // ** addr: 0xeb506c, size: 0xc
    // 0xeb506c: r0 = "about an hour"
    //     0xeb506c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34f28] "about an hour"
    //     0xeb5070: ldr             x0, [x0, #0xf28]
    // 0xeb5074: ret
    //     0xeb5074: ret             
  }
  _ hours(/* No info */) {
    // ** addr: 0xeb50a8, size: 0x70
    // 0xeb50a8: EnterFrame
    //     0xeb50a8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb50ac: mov             fp, SP
    // 0xeb50b0: AllocStack(0x10)
    //     0xeb50b0: sub             SP, SP, #0x10
    // 0xeb50b4: CheckStackOverflow
    //     0xeb50b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb50b8: cmp             SP, x16
    //     0xeb50bc: b.ls            #0xeb5110
    // 0xeb50c0: r0 = BoxInt64Instr(r2)
    //     0xeb50c0: sbfiz           x0, x2, #1, #0x1f
    //     0xeb50c4: cmp             x2, x0, asr #1
    //     0xeb50c8: b.eq            #0xeb50d4
    //     0xeb50cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb50d0: stur            x2, [x0, #7]
    // 0xeb50d4: r1 = Null
    //     0xeb50d4: mov             x1, NULL
    // 0xeb50d8: r2 = 4
    //     0xeb50d8: movz            x2, #0x4
    // 0xeb50dc: stur            x0, [fp, #-8]
    // 0xeb50e0: r0 = AllocateArray()
    //     0xeb50e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb50e4: mov             x1, x0
    // 0xeb50e8: ldur            x0, [fp, #-8]
    // 0xeb50ec: StoreField: r1->field_f = r0
    //     0xeb50ec: stur            w0, [x1, #0xf]
    // 0xeb50f0: r16 = " hours"
    //     0xeb50f0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f20] " hours"
    //     0xeb50f4: ldr             x16, [x16, #0xf20]
    // 0xeb50f8: StoreField: r1->field_13 = r16
    //     0xeb50f8: stur            w16, [x1, #0x13]
    // 0xeb50fc: str             x1, [SP]
    // 0xeb5100: r0 = _interpolate()
    //     0xeb5100: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb5104: LeaveFrame
    //     0xeb5104: mov             SP, fp
    //     0xeb5108: ldp             fp, lr, [SP], #0x10
    // 0xeb510c: ret
    //     0xeb510c: ret             
    // 0xeb5110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5110: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5114: b               #0xeb50c0
  }
  _ aDay(/* No info */) {
    // ** addr: 0xeb5384, size: 0xc
    // 0xeb5384: r0 = "a day"
    //     0xeb5384: add             x0, PP, #0x34, lsl #12  ; [pp+0x34f18] "a day"
    //     0xeb5388: ldr             x0, [x0, #0xf18]
    // 0xeb538c: ret
    //     0xeb538c: ret             
  }
  _ days(/* No info */) {
    // ** addr: 0xeb53c0, size: 0x70
    // 0xeb53c0: EnterFrame
    //     0xeb53c0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb53c4: mov             fp, SP
    // 0xeb53c8: AllocStack(0x10)
    //     0xeb53c8: sub             SP, SP, #0x10
    // 0xeb53cc: CheckStackOverflow
    //     0xeb53cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb53d0: cmp             SP, x16
    //     0xeb53d4: b.ls            #0xeb5428
    // 0xeb53d8: r0 = BoxInt64Instr(r2)
    //     0xeb53d8: sbfiz           x0, x2, #1, #0x1f
    //     0xeb53dc: cmp             x2, x0, asr #1
    //     0xeb53e0: b.eq            #0xeb53ec
    //     0xeb53e4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb53e8: stur            x2, [x0, #7]
    // 0xeb53ec: r1 = Null
    //     0xeb53ec: mov             x1, NULL
    // 0xeb53f0: r2 = 4
    //     0xeb53f0: movz            x2, #0x4
    // 0xeb53f4: stur            x0, [fp, #-8]
    // 0xeb53f8: r0 = AllocateArray()
    //     0xeb53f8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb53fc: mov             x1, x0
    // 0xeb5400: ldur            x0, [fp, #-8]
    // 0xeb5404: StoreField: r1->field_f = r0
    //     0xeb5404: stur            w0, [x1, #0xf]
    // 0xeb5408: r16 = " days"
    //     0xeb5408: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f10] " days"
    //     0xeb540c: ldr             x16, [x16, #0xf10]
    // 0xeb5410: StoreField: r1->field_13 = r16
    //     0xeb5410: stur            w16, [x1, #0x13]
    // 0xeb5414: str             x1, [SP]
    // 0xeb5418: r0 = _interpolate()
    //     0xeb5418: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb541c: LeaveFrame
    //     0xeb541c: mov             SP, fp
    //     0xeb5420: ldp             fp, lr, [SP], #0x10
    // 0xeb5424: ret
    //     0xeb5424: ret             
    // 0xeb5428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5428: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb542c: b               #0xeb53d8
  }
  _ aboutAMonth(/* No info */) {
    // ** addr: 0xeb5af4, size: 0xc
    // 0xeb5af4: r0 = "about a month"
    //     0xeb5af4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34f08] "about a month"
    //     0xeb5af8: ldr             x0, [x0, #0xf08]
    // 0xeb5afc: ret
    //     0xeb5afc: ret             
  }
  _ months(/* No info */) {
    // ** addr: 0xeb5b30, size: 0x70
    // 0xeb5b30: EnterFrame
    //     0xeb5b30: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5b34: mov             fp, SP
    // 0xeb5b38: AllocStack(0x10)
    //     0xeb5b38: sub             SP, SP, #0x10
    // 0xeb5b3c: CheckStackOverflow
    //     0xeb5b3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5b40: cmp             SP, x16
    //     0xeb5b44: b.ls            #0xeb5b98
    // 0xeb5b48: r0 = BoxInt64Instr(r2)
    //     0xeb5b48: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5b4c: cmp             x2, x0, asr #1
    //     0xeb5b50: b.eq            #0xeb5b5c
    //     0xeb5b54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5b58: stur            x2, [x0, #7]
    // 0xeb5b5c: r1 = Null
    //     0xeb5b5c: mov             x1, NULL
    // 0xeb5b60: r2 = 4
    //     0xeb5b60: movz            x2, #0x4
    // 0xeb5b64: stur            x0, [fp, #-8]
    // 0xeb5b68: r0 = AllocateArray()
    //     0xeb5b68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb5b6c: mov             x1, x0
    // 0xeb5b70: ldur            x0, [fp, #-8]
    // 0xeb5b74: StoreField: r1->field_f = r0
    //     0xeb5b74: stur            w0, [x1, #0xf]
    // 0xeb5b78: r16 = " months"
    //     0xeb5b78: add             x16, PP, #0x34, lsl #12  ; [pp+0x34f00] " months"
    //     0xeb5b7c: ldr             x16, [x16, #0xf00]
    // 0xeb5b80: StoreField: r1->field_13 = r16
    //     0xeb5b80: stur            w16, [x1, #0x13]
    // 0xeb5b84: str             x1, [SP]
    // 0xeb5b88: r0 = _interpolate()
    //     0xeb5b88: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb5b8c: LeaveFrame
    //     0xeb5b8c: mov             SP, fp
    //     0xeb5b90: ldp             fp, lr, [SP], #0x10
    // 0xeb5b94: ret
    //     0xeb5b94: ret             
    // 0xeb5b98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5b98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5b9c: b               #0xeb5b48
  }
  _ aboutAYear(/* No info */) {
    // ** addr: 0xeb5d70, size: 0xc
    // 0xeb5d70: r0 = "about a year"
    //     0xeb5d70: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ef8] "about a year"
    //     0xeb5d74: ldr             x0, [x0, #0xef8]
    // 0xeb5d78: ret
    //     0xeb5d78: ret             
  }
  _ years(/* No info */) {
    // ** addr: 0xeb6e98, size: 0x70
    // 0xeb6e98: EnterFrame
    //     0xeb6e98: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6e9c: mov             fp, SP
    // 0xeb6ea0: AllocStack(0x10)
    //     0xeb6ea0: sub             SP, SP, #0x10
    // 0xeb6ea4: CheckStackOverflow
    //     0xeb6ea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6ea8: cmp             SP, x16
    //     0xeb6eac: b.ls            #0xeb6f00
    // 0xeb6eb0: r0 = BoxInt64Instr(r2)
    //     0xeb6eb0: sbfiz           x0, x2, #1, #0x1f
    //     0xeb6eb4: cmp             x2, x0, asr #1
    //     0xeb6eb8: b.eq            #0xeb6ec4
    //     0xeb6ebc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb6ec0: stur            x2, [x0, #7]
    // 0xeb6ec4: r1 = Null
    //     0xeb6ec4: mov             x1, NULL
    // 0xeb6ec8: r2 = 4
    //     0xeb6ec8: movz            x2, #0x4
    // 0xeb6ecc: stur            x0, [fp, #-8]
    // 0xeb6ed0: r0 = AllocateArray()
    //     0xeb6ed0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb6ed4: mov             x1, x0
    // 0xeb6ed8: ldur            x0, [fp, #-8]
    // 0xeb6edc: StoreField: r1->field_f = r0
    //     0xeb6edc: stur            w0, [x1, #0xf]
    // 0xeb6ee0: r16 = " years"
    //     0xeb6ee0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34ef0] " years"
    //     0xeb6ee4: ldr             x16, [x16, #0xef0]
    // 0xeb6ee8: StoreField: r1->field_13 = r16
    //     0xeb6ee8: stur            w16, [x1, #0x13]
    // 0xeb6eec: str             x1, [SP]
    // 0xeb6ef0: r0 = _interpolate()
    //     0xeb6ef0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb6ef4: LeaveFrame
    //     0xeb6ef4: mov             SP, fp
    //     0xeb6ef8: ldp             fp, lr, [SP], #0x10
    // 0xeb6efc: ret
    //     0xeb6efc: ret             
    // 0xeb6f00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6f00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6f04: b               #0xeb6eb0
  }
}
