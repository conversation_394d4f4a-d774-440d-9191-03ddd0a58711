// lib: , url: package:timeago/src/messages/es_messages.dart

// class id: 1051200, size: 0x8
class :: {
}

// class id: 437, size: 0x8, field offset: 0x8
class EsShortMessages extends Object
    implements LookupMessages {

  _ lessThanOneMinute(/* No info */) {
    // ** addr: 0xe6358c, size: 0xc
    // 0xe6358c: r0 = "ahora"
    //     0xe6358c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34eb8] "ahora"
    //     0xe63590: ldr             x0, [x0, #0xeb8]
    // 0xe63594: ret
    //     0xe63594: ret             
  }
  _ aboutAMinute(/* No info */) {
    // ** addr: 0xeabe00, size: 0xc
    // 0xeabe00: r0 = "1 min"
    //     0xeabe00: add             x0, PP, #0x34, lsl #12  ; [pp+0x34eb0] "1 min"
    //     0xeabe04: ldr             x0, [x0, #0xeb0]
    // 0xeabe08: ret
    //     0xeabe08: ret             
  }
  _ minutes(/* No info */) {
    // ** addr: 0xeaf394, size: 0x70
    // 0xeaf394: EnterFrame
    //     0xeaf394: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf398: mov             fp, SP
    // 0xeaf39c: AllocStack(0x10)
    //     0xeaf39c: sub             SP, SP, #0x10
    // 0xeaf3a0: CheckStackOverflow
    //     0xeaf3a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf3a4: cmp             SP, x16
    //     0xeaf3a8: b.ls            #0xeaf3fc
    // 0xeaf3ac: r0 = BoxInt64Instr(r2)
    //     0xeaf3ac: sbfiz           x0, x2, #1, #0x1f
    //     0xeaf3b0: cmp             x2, x0, asr #1
    //     0xeaf3b4: b.eq            #0xeaf3c0
    //     0xeaf3b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaf3bc: stur            x2, [x0, #7]
    // 0xeaf3c0: r1 = Null
    //     0xeaf3c0: mov             x1, NULL
    // 0xeaf3c4: r2 = 4
    //     0xeaf3c4: movz            x2, #0x4
    // 0xeaf3c8: stur            x0, [fp, #-8]
    // 0xeaf3cc: r0 = AllocateArray()
    //     0xeaf3cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeaf3d0: mov             x1, x0
    // 0xeaf3d4: ldur            x0, [fp, #-8]
    // 0xeaf3d8: StoreField: r1->field_f = r0
    //     0xeaf3d8: stur            w0, [x1, #0xf]
    // 0xeaf3dc: r16 = " min"
    //     0xeaf3dc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34ea8] " min"
    //     0xeaf3e0: ldr             x16, [x16, #0xea8]
    // 0xeaf3e4: StoreField: r1->field_13 = r16
    //     0xeaf3e4: stur            w16, [x1, #0x13]
    // 0xeaf3e8: str             x1, [SP]
    // 0xeaf3ec: r0 = _interpolate()
    //     0xeaf3ec: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeaf3f0: LeaveFrame
    //     0xeaf3f0: mov             SP, fp
    //     0xeaf3f4: ldp             fp, lr, [SP], #0x10
    // 0xeaf3f8: ret
    //     0xeaf3f8: ret             
    // 0xeaf3fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf3fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf400: b               #0xeaf3ac
  }
  _ aboutAnHour(/* No info */) {
    // ** addr: 0xeb5090, size: 0xc
    // 0xeb5090: r0 = "~1 hr"
    //     0xeb5090: add             x0, PP, #0x34, lsl #12  ; [pp+0x34ea0] "~1 hr"
    //     0xeb5094: ldr             x0, [x0, #0xea0]
    // 0xeb5098: ret
    //     0xeb5098: ret             
  }
  _ hours(/* No info */) {
    // ** addr: 0xeb51f8, size: 0x70
    // 0xeb51f8: EnterFrame
    //     0xeb51f8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb51fc: mov             fp, SP
    // 0xeb5200: AllocStack(0x10)
    //     0xeb5200: sub             SP, SP, #0x10
    // 0xeb5204: CheckStackOverflow
    //     0xeb5204: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5208: cmp             SP, x16
    //     0xeb520c: b.ls            #0xeb5260
    // 0xeb5210: r0 = BoxInt64Instr(r2)
    //     0xeb5210: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5214: cmp             x2, x0, asr #1
    //     0xeb5218: b.eq            #0xeb5224
    //     0xeb521c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5220: stur            x2, [x0, #7]
    // 0xeb5224: r1 = Null
    //     0xeb5224: mov             x1, NULL
    // 0xeb5228: r2 = 4
    //     0xeb5228: movz            x2, #0x4
    // 0xeb522c: stur            x0, [fp, #-8]
    // 0xeb5230: r0 = AllocateArray()
    //     0xeb5230: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb5234: mov             x1, x0
    // 0xeb5238: ldur            x0, [fp, #-8]
    // 0xeb523c: StoreField: r1->field_f = r0
    //     0xeb523c: stur            w0, [x1, #0xf]
    // 0xeb5240: r16 = " hr"
    //     0xeb5240: add             x16, PP, #0x34, lsl #12  ; [pp+0x34e98] " hr"
    //     0xeb5244: ldr             x16, [x16, #0xe98]
    // 0xeb5248: StoreField: r1->field_13 = r16
    //     0xeb5248: stur            w16, [x1, #0x13]
    // 0xeb524c: str             x1, [SP]
    // 0xeb5250: r0 = _interpolate()
    //     0xeb5250: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb5254: LeaveFrame
    //     0xeb5254: mov             SP, fp
    //     0xeb5258: ldp             fp, lr, [SP], #0x10
    // 0xeb525c: ret
    //     0xeb525c: ret             
    // 0xeb5260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5260: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5264: b               #0xeb5210
  }
  _ aDay(/* No info */) {
    // ** addr: 0xeb53a8, size: 0xc
    // 0xeb53a8: r0 = "~1 día"
    //     0xeb53a8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e90] "~1 día"
    //     0xeb53ac: ldr             x0, [x0, #0xe90]
    // 0xeb53b0: ret
    //     0xeb53b0: ret             
  }
  _ days(/* No info */) {
    // ** addr: 0xeb54a0, size: 0x70
    // 0xeb54a0: EnterFrame
    //     0xeb54a0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb54a4: mov             fp, SP
    // 0xeb54a8: AllocStack(0x10)
    //     0xeb54a8: sub             SP, SP, #0x10
    // 0xeb54ac: CheckStackOverflow
    //     0xeb54ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb54b0: cmp             SP, x16
    //     0xeb54b4: b.ls            #0xeb5508
    // 0xeb54b8: r0 = BoxInt64Instr(r2)
    //     0xeb54b8: sbfiz           x0, x2, #1, #0x1f
    //     0xeb54bc: cmp             x2, x0, asr #1
    //     0xeb54c0: b.eq            #0xeb54cc
    //     0xeb54c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb54c8: stur            x2, [x0, #7]
    // 0xeb54cc: r1 = Null
    //     0xeb54cc: mov             x1, NULL
    // 0xeb54d0: r2 = 4
    //     0xeb54d0: movz            x2, #0x4
    // 0xeb54d4: stur            x0, [fp, #-8]
    // 0xeb54d8: r0 = AllocateArray()
    //     0xeb54d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb54dc: mov             x1, x0
    // 0xeb54e0: ldur            x0, [fp, #-8]
    // 0xeb54e4: StoreField: r1->field_f = r0
    //     0xeb54e4: stur            w0, [x1, #0xf]
    // 0xeb54e8: r16 = " días"
    //     0xeb54e8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34e40] " días"
    //     0xeb54ec: ldr             x16, [x16, #0xe40]
    // 0xeb54f0: StoreField: r1->field_13 = r16
    //     0xeb54f0: stur            w16, [x1, #0x13]
    // 0xeb54f4: str             x1, [SP]
    // 0xeb54f8: r0 = _interpolate()
    //     0xeb54f8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb54fc: LeaveFrame
    //     0xeb54fc: mov             SP, fp
    //     0xeb5500: ldp             fp, lr, [SP], #0x10
    // 0xeb5504: ret
    //     0xeb5504: ret             
    // 0xeb5508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb550c: b               #0xeb54b8
  }
  _ aboutAMonth(/* No info */) {
    // ** addr: 0xeb5b18, size: 0xc
    // 0xeb5b18: r0 = "~1 mes"
    //     0xeb5b18: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e88] "~1 mes"
    //     0xeb5b1c: ldr             x0, [x0, #0xe88]
    // 0xeb5b20: ret
    //     0xeb5b20: ret             
  }
  _ months(/* No info */) {
    // ** addr: 0xeb5c10, size: 0x70
    // 0xeb5c10: EnterFrame
    //     0xeb5c10: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5c14: mov             fp, SP
    // 0xeb5c18: AllocStack(0x10)
    //     0xeb5c18: sub             SP, SP, #0x10
    // 0xeb5c1c: CheckStackOverflow
    //     0xeb5c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5c20: cmp             SP, x16
    //     0xeb5c24: b.ls            #0xeb5c78
    // 0xeb5c28: r0 = BoxInt64Instr(r2)
    //     0xeb5c28: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5c2c: cmp             x2, x0, asr #1
    //     0xeb5c30: b.eq            #0xeb5c3c
    //     0xeb5c34: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5c38: stur            x2, [x0, #7]
    // 0xeb5c3c: r1 = Null
    //     0xeb5c3c: mov             x1, NULL
    // 0xeb5c40: r2 = 4
    //     0xeb5c40: movz            x2, #0x4
    // 0xeb5c44: stur            x0, [fp, #-8]
    // 0xeb5c48: r0 = AllocateArray()
    //     0xeb5c48: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb5c4c: mov             x1, x0
    // 0xeb5c50: ldur            x0, [fp, #-8]
    // 0xeb5c54: StoreField: r1->field_f = r0
    //     0xeb5c54: stur            w0, [x1, #0xf]
    // 0xeb5c58: r16 = " meses"
    //     0xeb5c58: add             x16, PP, #0x34, lsl #12  ; [pp+0x34e30] " meses"
    //     0xeb5c5c: ldr             x16, [x16, #0xe30]
    // 0xeb5c60: StoreField: r1->field_13 = r16
    //     0xeb5c60: stur            w16, [x1, #0x13]
    // 0xeb5c64: str             x1, [SP]
    // 0xeb5c68: r0 = _interpolate()
    //     0xeb5c68: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb5c6c: LeaveFrame
    //     0xeb5c6c: mov             SP, fp
    //     0xeb5c70: ldp             fp, lr, [SP], #0x10
    // 0xeb5c74: ret
    //     0xeb5c74: ret             
    // 0xeb5c78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5c78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5c7c: b               #0xeb5c28
  }
  _ aboutAYear(/* No info */) {
    // ** addr: 0xeb5d94, size: 0xc
    // 0xeb5d94: r0 = "~1 año"
    //     0xeb5d94: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e80] "~1 año"
    //     0xeb5d98: ldr             x0, [x0, #0xe80]
    // 0xeb5d9c: ret
    //     0xeb5d9c: ret             
  }
  _ years(/* No info */) {
    // ** addr: 0xeb6f74, size: 0x70
    // 0xeb6f74: EnterFrame
    //     0xeb6f74: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6f78: mov             fp, SP
    // 0xeb6f7c: AllocStack(0x10)
    //     0xeb6f7c: sub             SP, SP, #0x10
    // 0xeb6f80: CheckStackOverflow
    //     0xeb6f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6f84: cmp             SP, x16
    //     0xeb6f88: b.ls            #0xeb6fdc
    // 0xeb6f8c: r0 = BoxInt64Instr(r2)
    //     0xeb6f8c: sbfiz           x0, x2, #1, #0x1f
    //     0xeb6f90: cmp             x2, x0, asr #1
    //     0xeb6f94: b.eq            #0xeb6fa0
    //     0xeb6f98: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb6f9c: stur            x2, [x0, #7]
    // 0xeb6fa0: r1 = Null
    //     0xeb6fa0: mov             x1, NULL
    // 0xeb6fa4: r2 = 4
    //     0xeb6fa4: movz            x2, #0x4
    // 0xeb6fa8: stur            x0, [fp, #-8]
    // 0xeb6fac: r0 = AllocateArray()
    //     0xeb6fac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb6fb0: mov             x1, x0
    // 0xeb6fb4: ldur            x0, [fp, #-8]
    // 0xeb6fb8: StoreField: r1->field_f = r0
    //     0xeb6fb8: stur            w0, [x1, #0xf]
    // 0xeb6fbc: r16 = " años"
    //     0xeb6fbc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34e20] " años"
    //     0xeb6fc0: ldr             x16, [x16, #0xe20]
    // 0xeb6fc4: StoreField: r1->field_13 = r16
    //     0xeb6fc4: stur            w16, [x1, #0x13]
    // 0xeb6fc8: str             x1, [SP]
    // 0xeb6fcc: r0 = _interpolate()
    //     0xeb6fcc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb6fd0: LeaveFrame
    //     0xeb6fd0: mov             SP, fp
    //     0xeb6fd4: ldp             fp, lr, [SP], #0x10
    // 0xeb6fd8: ret
    //     0xeb6fd8: ret             
    // 0xeb6fdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb6fdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb6fe0: b               #0xeb6f8c
  }
}

// class id: 438, size: 0x8, field offset: 0x8
class EsMessages extends Object
    implements LookupMessages {

  _ prefixAgo(/* No info */) {
    // ** addr: 0xe61c64, size: 0xc
    // 0xe61c64: r0 = "hace"
    //     0xe61c64: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e78] "hace"
    //     0xe61c68: ldr             x0, [x0, #0xe78]
    // 0xe61c6c: ret
    //     0xe61c6c: ret             
  }
  _ lessThanOneMinute(/* No info */) {
    // ** addr: 0xe63580, size: 0xc
    // 0xe63580: r0 = "un momento"
    //     0xe63580: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e70] "un momento"
    //     0xe63584: ldr             x0, [x0, #0xe70]
    // 0xe63588: ret
    //     0xe63588: ret             
  }
  _ aboutAMinute(/* No info */) {
    // ** addr: 0xeabdf4, size: 0xc
    // 0xeabdf4: r0 = "un minuto"
    //     0xeabdf4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e68] "un minuto"
    //     0xeabdf8: ldr             x0, [x0, #0xe68]
    // 0xeabdfc: ret
    //     0xeabdfc: ret             
  }
  _ minutes(/* No info */) {
    // ** addr: 0xeaf324, size: 0x70
    // 0xeaf324: EnterFrame
    //     0xeaf324: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf328: mov             fp, SP
    // 0xeaf32c: AllocStack(0x10)
    //     0xeaf32c: sub             SP, SP, #0x10
    // 0xeaf330: CheckStackOverflow
    //     0xeaf330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf334: cmp             SP, x16
    //     0xeaf338: b.ls            #0xeaf38c
    // 0xeaf33c: r0 = BoxInt64Instr(r2)
    //     0xeaf33c: sbfiz           x0, x2, #1, #0x1f
    //     0xeaf340: cmp             x2, x0, asr #1
    //     0xeaf344: b.eq            #0xeaf350
    //     0xeaf348: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaf34c: stur            x2, [x0, #7]
    // 0xeaf350: r1 = Null
    //     0xeaf350: mov             x1, NULL
    // 0xeaf354: r2 = 4
    //     0xeaf354: movz            x2, #0x4
    // 0xeaf358: stur            x0, [fp, #-8]
    // 0xeaf35c: r0 = AllocateArray()
    //     0xeaf35c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeaf360: mov             x1, x0
    // 0xeaf364: ldur            x0, [fp, #-8]
    // 0xeaf368: StoreField: r1->field_f = r0
    //     0xeaf368: stur            w0, [x1, #0xf]
    // 0xeaf36c: r16 = " minutos"
    //     0xeaf36c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34e60] " minutos"
    //     0xeaf370: ldr             x16, [x16, #0xe60]
    // 0xeaf374: StoreField: r1->field_13 = r16
    //     0xeaf374: stur            w16, [x1, #0x13]
    // 0xeaf378: str             x1, [SP]
    // 0xeaf37c: r0 = _interpolate()
    //     0xeaf37c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeaf380: LeaveFrame
    //     0xeaf380: mov             SP, fp
    //     0xeaf384: ldp             fp, lr, [SP], #0x10
    // 0xeaf388: ret
    //     0xeaf388: ret             
    // 0xeaf38c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf38c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf390: b               #0xeaf33c
  }
  _ aboutAnHour(/* No info */) {
    // ** addr: 0xeb5084, size: 0xc
    // 0xeb5084: r0 = "una hora"
    //     0xeb5084: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e58] "una hora"
    //     0xeb5088: ldr             x0, [x0, #0xe58]
    // 0xeb508c: ret
    //     0xeb508c: ret             
  }
  _ hours(/* No info */) {
    // ** addr: 0xeb5188, size: 0x70
    // 0xeb5188: EnterFrame
    //     0xeb5188: stp             fp, lr, [SP, #-0x10]!
    //     0xeb518c: mov             fp, SP
    // 0xeb5190: AllocStack(0x10)
    //     0xeb5190: sub             SP, SP, #0x10
    // 0xeb5194: CheckStackOverflow
    //     0xeb5194: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5198: cmp             SP, x16
    //     0xeb519c: b.ls            #0xeb51f0
    // 0xeb51a0: r0 = BoxInt64Instr(r2)
    //     0xeb51a0: sbfiz           x0, x2, #1, #0x1f
    //     0xeb51a4: cmp             x2, x0, asr #1
    //     0xeb51a8: b.eq            #0xeb51b4
    //     0xeb51ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb51b0: stur            x2, [x0, #7]
    // 0xeb51b4: r1 = Null
    //     0xeb51b4: mov             x1, NULL
    // 0xeb51b8: r2 = 4
    //     0xeb51b8: movz            x2, #0x4
    // 0xeb51bc: stur            x0, [fp, #-8]
    // 0xeb51c0: r0 = AllocateArray()
    //     0xeb51c0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb51c4: mov             x1, x0
    // 0xeb51c8: ldur            x0, [fp, #-8]
    // 0xeb51cc: StoreField: r1->field_f = r0
    //     0xeb51cc: stur            w0, [x1, #0xf]
    // 0xeb51d0: r16 = " horas"
    //     0xeb51d0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34e50] " horas"
    //     0xeb51d4: ldr             x16, [x16, #0xe50]
    // 0xeb51d8: StoreField: r1->field_13 = r16
    //     0xeb51d8: stur            w16, [x1, #0x13]
    // 0xeb51dc: str             x1, [SP]
    // 0xeb51e0: r0 = _interpolate()
    //     0xeb51e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb51e4: LeaveFrame
    //     0xeb51e4: mov             SP, fp
    //     0xeb51e8: ldp             fp, lr, [SP], #0x10
    // 0xeb51ec: ret
    //     0xeb51ec: ret             
    // 0xeb51f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb51f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb51f4: b               #0xeb51a0
  }
  _ aDay(/* No info */) {
    // ** addr: 0xeb539c, size: 0xc
    // 0xeb539c: r0 = "un día"
    //     0xeb539c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e48] "un día"
    //     0xeb53a0: ldr             x0, [x0, #0xe48]
    // 0xeb53a4: ret
    //     0xeb53a4: ret             
  }
  _ aboutAMonth(/* No info */) {
    // ** addr: 0xeb5b0c, size: 0xc
    // 0xeb5b0c: r0 = "un mes"
    //     0xeb5b0c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e38] "un mes"
    //     0xeb5b10: ldr             x0, [x0, #0xe38]
    // 0xeb5b14: ret
    //     0xeb5b14: ret             
  }
  _ aboutAYear(/* No info */) {
    // ** addr: 0xeb5d88, size: 0xc
    // 0xeb5d88: r0 = "un año"
    //     0xeb5d88: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e28] "un año"
    //     0xeb5d8c: ldr             x0, [x0, #0xe28]
    // 0xeb5d90: ret
    //     0xeb5d90: ret             
  }
}
