// lib: , url: package:timeago/src/messages/id_messages.dart

// class id: 1051201, size: 0x8
class :: {
}

// class id: 436, size: 0x8, field offset: 0x8
class IdMessages extends Object
    implements LookupMessages {

  _ suffixAgo(/* No info */) {
    // ** addr: 0xe6218c, size: 0xc
    // 0xe6218c: r0 = "yang lalu"
    //     0xe6218c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e18] "yang lalu"
    //     0xe62190: ldr             x0, [x0, #0xe18]
    // 0xe62194: ret
    //     0xe62194: ret             
  }
  _ lessThanOneMinute(/* No info */) {
    // ** addr: 0xe63598, size: 0xc
    // 0xe63598: r0 = "kurang dari semenit"
    //     0xe63598: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] "kurang dari semenit"
    //     0xe6359c: ldr             x0, [x0, #0xe10]
    // 0xe635a0: ret
    //     0xe635a0: ret             
  }
  _ aboutAMinute(/* No info */) {
    // ** addr: 0xeabe0c, size: 0xc
    // 0xeabe0c: r0 = "semenit"
    //     0xeabe0c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e08] "semenit"
    //     0xeabe10: ldr             x0, [x0, #0xe08]
    // 0xeabe14: ret
    //     0xeabe14: ret             
  }
  _ minutes(/* No info */) {
    // ** addr: 0xeaf404, size: 0x70
    // 0xeaf404: EnterFrame
    //     0xeaf404: stp             fp, lr, [SP, #-0x10]!
    //     0xeaf408: mov             fp, SP
    // 0xeaf40c: AllocStack(0x10)
    //     0xeaf40c: sub             SP, SP, #0x10
    // 0xeaf410: CheckStackOverflow
    //     0xeaf410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaf414: cmp             SP, x16
    //     0xeaf418: b.ls            #0xeaf46c
    // 0xeaf41c: r0 = BoxInt64Instr(r2)
    //     0xeaf41c: sbfiz           x0, x2, #1, #0x1f
    //     0xeaf420: cmp             x2, x0, asr #1
    //     0xeaf424: b.eq            #0xeaf430
    //     0xeaf428: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaf42c: stur            x2, [x0, #7]
    // 0xeaf430: r1 = Null
    //     0xeaf430: mov             x1, NULL
    // 0xeaf434: r2 = 4
    //     0xeaf434: movz            x2, #0x4
    // 0xeaf438: stur            x0, [fp, #-8]
    // 0xeaf43c: r0 = AllocateArray()
    //     0xeaf43c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeaf440: mov             x1, x0
    // 0xeaf444: ldur            x0, [fp, #-8]
    // 0xeaf448: StoreField: r1->field_f = r0
    //     0xeaf448: stur            w0, [x1, #0xf]
    // 0xeaf44c: r16 = " menit"
    //     0xeaf44c: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ab00] " menit"
    //     0xeaf450: ldr             x16, [x16, #0xb00]
    // 0xeaf454: StoreField: r1->field_13 = r16
    //     0xeaf454: stur            w16, [x1, #0x13]
    // 0xeaf458: str             x1, [SP]
    // 0xeaf45c: r0 = _interpolate()
    //     0xeaf45c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeaf460: LeaveFrame
    //     0xeaf460: mov             SP, fp
    //     0xeaf464: ldp             fp, lr, [SP], #0x10
    // 0xeaf468: ret
    //     0xeaf468: ret             
    // 0xeaf46c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaf46c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaf470: b               #0xeaf41c
  }
  _ aboutAnHour(/* No info */) {
    // ** addr: 0xeb509c, size: 0xc
    // 0xeb509c: r0 = "sekitar sejam"
    //     0xeb509c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e00] "sekitar sejam"
    //     0xeb50a0: ldr             x0, [x0, #0xe00]
    // 0xeb50a4: ret
    //     0xeb50a4: ret             
  }
  _ hours(/* No info */) {
    // ** addr: 0xeb5268, size: 0x70
    // 0xeb5268: EnterFrame
    //     0xeb5268: stp             fp, lr, [SP, #-0x10]!
    //     0xeb526c: mov             fp, SP
    // 0xeb5270: AllocStack(0x10)
    //     0xeb5270: sub             SP, SP, #0x10
    // 0xeb5274: CheckStackOverflow
    //     0xeb5274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5278: cmp             SP, x16
    //     0xeb527c: b.ls            #0xeb52d0
    // 0xeb5280: r0 = BoxInt64Instr(r2)
    //     0xeb5280: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5284: cmp             x2, x0, asr #1
    //     0xeb5288: b.eq            #0xeb5294
    //     0xeb528c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5290: stur            x2, [x0, #7]
    // 0xeb5294: r1 = Null
    //     0xeb5294: mov             x1, NULL
    // 0xeb5298: r2 = 4
    //     0xeb5298: movz            x2, #0x4
    // 0xeb529c: stur            x0, [fp, #-8]
    // 0xeb52a0: r0 = AllocateArray()
    //     0xeb52a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb52a4: mov             x1, x0
    // 0xeb52a8: ldur            x0, [fp, #-8]
    // 0xeb52ac: StoreField: r1->field_f = r0
    //     0xeb52ac: stur            w0, [x1, #0xf]
    // 0xeb52b0: r16 = " jam"
    //     0xeb52b0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df8] " jam"
    //     0xeb52b4: ldr             x16, [x16, #0xdf8]
    // 0xeb52b8: StoreField: r1->field_13 = r16
    //     0xeb52b8: stur            w16, [x1, #0x13]
    // 0xeb52bc: str             x1, [SP]
    // 0xeb52c0: r0 = _interpolate()
    //     0xeb52c0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb52c4: LeaveFrame
    //     0xeb52c4: mov             SP, fp
    //     0xeb52c8: ldp             fp, lr, [SP], #0x10
    // 0xeb52cc: ret
    //     0xeb52cc: ret             
    // 0xeb52d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb52d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb52d4: b               #0xeb5280
  }
  _ aDay(/* No info */) {
    // ** addr: 0xeb53b4, size: 0xc
    // 0xeb53b4: r0 = "sehari"
    //     0xeb53b4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34df0] "sehari"
    //     0xeb53b8: ldr             x0, [x0, #0xdf0]
    // 0xeb53bc: ret
    //     0xeb53bc: ret             
  }
  _ days(/* No info */) {
    // ** addr: 0xeb5510, size: 0x70
    // 0xeb5510: EnterFrame
    //     0xeb5510: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5514: mov             fp, SP
    // 0xeb5518: AllocStack(0x10)
    //     0xeb5518: sub             SP, SP, #0x10
    // 0xeb551c: CheckStackOverflow
    //     0xeb551c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5520: cmp             SP, x16
    //     0xeb5524: b.ls            #0xeb5578
    // 0xeb5528: r0 = BoxInt64Instr(r2)
    //     0xeb5528: sbfiz           x0, x2, #1, #0x1f
    //     0xeb552c: cmp             x2, x0, asr #1
    //     0xeb5530: b.eq            #0xeb553c
    //     0xeb5534: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5538: stur            x2, [x0, #7]
    // 0xeb553c: r1 = Null
    //     0xeb553c: mov             x1, NULL
    // 0xeb5540: r2 = 4
    //     0xeb5540: movz            x2, #0x4
    // 0xeb5544: stur            x0, [fp, #-8]
    // 0xeb5548: r0 = AllocateArray()
    //     0xeb5548: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb554c: mov             x1, x0
    // 0xeb5550: ldur            x0, [fp, #-8]
    // 0xeb5554: StoreField: r1->field_f = r0
    //     0xeb5554: stur            w0, [x1, #0xf]
    // 0xeb5558: r16 = " hari"
    //     0xeb5558: add             x16, PP, #0x34, lsl #12  ; [pp+0x34de8] " hari"
    //     0xeb555c: ldr             x16, [x16, #0xde8]
    // 0xeb5560: StoreField: r1->field_13 = r16
    //     0xeb5560: stur            w16, [x1, #0x13]
    // 0xeb5564: str             x1, [SP]
    // 0xeb5568: r0 = _interpolate()
    //     0xeb5568: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb556c: LeaveFrame
    //     0xeb556c: mov             SP, fp
    //     0xeb5570: ldp             fp, lr, [SP], #0x10
    // 0xeb5574: ret
    //     0xeb5574: ret             
    // 0xeb5578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5578: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb557c: b               #0xeb5528
  }
  _ aboutAMonth(/* No info */) {
    // ** addr: 0xeb5b24, size: 0xc
    // 0xeb5b24: r0 = "sekitar sebulan"
    //     0xeb5b24: add             x0, PP, #0x34, lsl #12  ; [pp+0x34de0] "sekitar sebulan"
    //     0xeb5b28: ldr             x0, [x0, #0xde0]
    // 0xeb5b2c: ret
    //     0xeb5b2c: ret             
  }
  _ months(/* No info */) {
    // ** addr: 0xeb5c80, size: 0x70
    // 0xeb5c80: EnterFrame
    //     0xeb5c80: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5c84: mov             fp, SP
    // 0xeb5c88: AllocStack(0x10)
    //     0xeb5c88: sub             SP, SP, #0x10
    // 0xeb5c8c: CheckStackOverflow
    //     0xeb5c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5c90: cmp             SP, x16
    //     0xeb5c94: b.ls            #0xeb5ce8
    // 0xeb5c98: r0 = BoxInt64Instr(r2)
    //     0xeb5c98: sbfiz           x0, x2, #1, #0x1f
    //     0xeb5c9c: cmp             x2, x0, asr #1
    //     0xeb5ca0: b.eq            #0xeb5cac
    //     0xeb5ca4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb5ca8: stur            x2, [x0, #7]
    // 0xeb5cac: r1 = Null
    //     0xeb5cac: mov             x1, NULL
    // 0xeb5cb0: r2 = 4
    //     0xeb5cb0: movz            x2, #0x4
    // 0xeb5cb4: stur            x0, [fp, #-8]
    // 0xeb5cb8: r0 = AllocateArray()
    //     0xeb5cb8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb5cbc: mov             x1, x0
    // 0xeb5cc0: ldur            x0, [fp, #-8]
    // 0xeb5cc4: StoreField: r1->field_f = r0
    //     0xeb5cc4: stur            w0, [x1, #0xf]
    // 0xeb5cc8: r16 = " bulan"
    //     0xeb5cc8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34dd8] " bulan"
    //     0xeb5ccc: ldr             x16, [x16, #0xdd8]
    // 0xeb5cd0: StoreField: r1->field_13 = r16
    //     0xeb5cd0: stur            w16, [x1, #0x13]
    // 0xeb5cd4: str             x1, [SP]
    // 0xeb5cd8: r0 = _interpolate()
    //     0xeb5cd8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb5cdc: LeaveFrame
    //     0xeb5cdc: mov             SP, fp
    //     0xeb5ce0: ldp             fp, lr, [SP], #0x10
    // 0xeb5ce4: ret
    //     0xeb5ce4: ret             
    // 0xeb5ce8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5ce8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5cec: b               #0xeb5c98
  }
  _ aboutAYear(/* No info */) {
    // ** addr: 0xeb5da0, size: 0xc
    // 0xeb5da0: r0 = "sekitar setahun"
    //     0xeb5da0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34dd0] "sekitar setahun"
    //     0xeb5da4: ldr             x0, [x0, #0xdd0]
    // 0xeb5da8: ret
    //     0xeb5da8: ret             
  }
  _ years(/* No info */) {
    // ** addr: 0xeb6fe4, size: 0x70
    // 0xeb6fe4: EnterFrame
    //     0xeb6fe4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb6fe8: mov             fp, SP
    // 0xeb6fec: AllocStack(0x10)
    //     0xeb6fec: sub             SP, SP, #0x10
    // 0xeb6ff0: CheckStackOverflow
    //     0xeb6ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb6ff4: cmp             SP, x16
    //     0xeb6ff8: b.ls            #0xeb704c
    // 0xeb6ffc: r0 = BoxInt64Instr(r2)
    //     0xeb6ffc: sbfiz           x0, x2, #1, #0x1f
    //     0xeb7000: cmp             x2, x0, asr #1
    //     0xeb7004: b.eq            #0xeb7010
    //     0xeb7008: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb700c: stur            x2, [x0, #7]
    // 0xeb7010: r1 = Null
    //     0xeb7010: mov             x1, NULL
    // 0xeb7014: r2 = 4
    //     0xeb7014: movz            x2, #0x4
    // 0xeb7018: stur            x0, [fp, #-8]
    // 0xeb701c: r0 = AllocateArray()
    //     0xeb701c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb7020: mov             x1, x0
    // 0xeb7024: ldur            x0, [fp, #-8]
    // 0xeb7028: StoreField: r1->field_f = r0
    //     0xeb7028: stur            w0, [x1, #0xf]
    // 0xeb702c: r16 = " tahun"
    //     0xeb702c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34dc8] " tahun"
    //     0xeb7030: ldr             x16, [x16, #0xdc8]
    // 0xeb7034: StoreField: r1->field_13 = r16
    //     0xeb7034: stur            w16, [x1, #0x13]
    // 0xeb7038: str             x1, [SP]
    // 0xeb703c: r0 = _interpolate()
    //     0xeb703c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb7040: LeaveFrame
    //     0xeb7040: mov             SP, fp
    //     0xeb7044: ldp             fp, lr, [SP], #0x10
    // 0xeb7048: ret
    //     0xeb7048: ret             
    // 0xeb704c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb704c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7050: b               #0xeb6ffc
  }
  _ wordSeparator(/* No info */) {
    // ** addr: 0xeb7a88, size: 0x8
    // 0xeb7a88: r0 = " "
    //     0xeb7a88: ldr             x0, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xeb7a8c: ret
    //     0xeb7a8c: ret             
  }
}
