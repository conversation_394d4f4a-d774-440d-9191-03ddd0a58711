// lib: , url: package:timeago/src/timeago.dart

// class id: 1051203, size: 0x8
class :: {

  static late Map<String, LookupMessages> _lookupMessagesMap; // offset: 0x15e8

  static _ format(/* No info */) {
    // ** addr: 0xb36788, size: 0x9d4
    // 0xb36788: EnterFrame
    //     0xb36788: stp             fp, lr, [SP, #-0x10]!
    //     0xb3678c: mov             fp, SP
    // 0xb36790: AllocStack(0x40)
    //     0xb36790: sub             SP, SP, #0x40
    // 0xb36794: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xb36794: stur            x1, [fp, #-8]
    // 0xb36798: CheckStackOverflow
    //     0xb36798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3679c: cmp             SP, x16
    //     0xb367a0: b.ls            #0xb37020
    // 0xb367a4: r0 = InitLateStaticField(0x15e8) // [package:timeago/src/timeago.dart] ::_lookupMessagesMap
    //     0xb367a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb367a8: ldr             x0, [x0, #0x2bd0]
    //     0xb367ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb367b0: cmp             w0, w16
    //     0xb367b4: b.ne            #0xb367c4
    //     0xb367b8: add             x2, PP, #0xd, lsl #12  ; [pp+0xda90] Field <::._lookupMessagesMap@2027518848>: static late (offset: 0x15e8)
    //     0xb367bc: ldr             x2, [x2, #0xa90]
    //     0xb367c0: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xb367c4: mov             x1, x0
    // 0xb367c8: r2 = "id"
    //     0xb367c8: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb367cc: ldr             x2, [x2, #0x740]
    // 0xb367d0: stur            x0, [fp, #-0x10]
    // 0xb367d4: r0 = _getValueOrData()
    //     0xb367d4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb367d8: mov             x1, x0
    // 0xb367dc: ldur            x0, [fp, #-0x10]
    // 0xb367e0: LoadField: r2 = r0->field_f
    //     0xb367e0: ldur            w2, [x0, #0xf]
    // 0xb367e4: DecompressPointer r2
    //     0xb367e4: add             x2, x2, HEAP, lsl #32
    // 0xb367e8: cmp             w2, w1
    // 0xb367ec: b.eq            #0xb367f8
    // 0xb367f0: cmp             w1, NULL
    // 0xb367f4: b.ne            #0xb36850
    // 0xb367f8: r1 = Null
    //     0xb367f8: mov             x1, NULL
    // 0xb367fc: r2 = 10
    //     0xb367fc: movz            x2, #0xa
    // 0xb36800: r0 = AllocateArray()
    //     0xb36800: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb36804: r16 = "Locale ["
    //     0xb36804: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b538] "Locale ["
    //     0xb36808: ldr             x16, [x16, #0x538]
    // 0xb3680c: StoreField: r0->field_f = r16
    //     0xb3680c: stur            w16, [x0, #0xf]
    // 0xb36810: r16 = "id"
    //     0xb36810: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb36814: ldr             x16, [x16, #0x740]
    // 0xb36818: StoreField: r0->field_13 = r16
    //     0xb36818: stur            w16, [x0, #0x13]
    // 0xb3681c: r16 = "] has not been added, using ["
    //     0xb3681c: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b540] "] has not been added, using ["
    //     0xb36820: ldr             x16, [x16, #0x540]
    // 0xb36824: ArrayStore: r0[0] = r16  ; List_4
    //     0xb36824: stur            w16, [x0, #0x17]
    // 0xb36828: r1 = LoadStaticField(0x15e4)
    //     0xb36828: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xb3682c: ldr             x1, [x1, #0x2bc8]
    // 0xb36830: StoreField: r0->field_1b = r1
    //     0xb36830: stur            w1, [x0, #0x1b]
    // 0xb36834: r16 = "] as fallback. To add a locale use [setLocaleMessages]"
    //     0xb36834: add             x16, PP, #0x2b, lsl #12  ; [pp+0x2b548] "] as fallback. To add a locale use [setLocaleMessages]"
    //     0xb36838: ldr             x16, [x16, #0x548]
    // 0xb3683c: StoreField: r0->field_1f = r16
    //     0xb3683c: stur            w16, [x0, #0x1f]
    // 0xb36840: str             x0, [SP]
    // 0xb36844: r0 = _interpolate()
    //     0xb36844: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xb36848: mov             x1, x0
    // 0xb3684c: r0 = print()
    //     0xb3684c: bl              #0x63fe38  ; [dart:core] ::print
    // 0xb36850: r0 = LoadStaticField(0x15e8)
    //     0xb36850: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb36854: ldr             x0, [x0, #0x2bd0]
    // 0xb36858: mov             x1, x0
    // 0xb3685c: stur            x0, [fp, #-0x10]
    // 0xb36860: r2 = "id"
    //     0xb36860: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xb36864: ldr             x2, [x2, #0x740]
    // 0xb36868: r0 = _getValueOrData()
    //     0xb36868: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb3686c: mov             x1, x0
    // 0xb36870: ldur            x0, [fp, #-0x10]
    // 0xb36874: LoadField: r2 = r0->field_f
    //     0xb36874: ldur            w2, [x0, #0xf]
    // 0xb36878: DecompressPointer r2
    //     0xb36878: add             x2, x2, HEAP, lsl #32
    // 0xb3687c: cmp             w2, w1
    // 0xb36880: b.ne            #0xb3688c
    // 0xb36884: r0 = Null
    //     0xb36884: mov             x0, NULL
    // 0xb36888: b               #0xb36890
    // 0xb3688c: mov             x0, x1
    // 0xb36890: cmp             w0, NULL
    // 0xb36894: b.ne            #0xb368a4
    // 0xb36898: r0 = EnMessages()
    //     0xb36898: bl              #0xb3715c  ; AllocateEnMessagesStub -> EnMessages (size=0x8)
    // 0xb3689c: mov             x1, x0
    // 0xb368a0: b               #0xb368a8
    // 0xb368a4: mov             x1, x0
    // 0xb368a8: stur            x1, [fp, #-0x10]
    // 0xb368ac: r0 = _getCurrentMicros()
    //     0xb368ac: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xb368b0: r1 = LoadInt32Instr(r0)
    //     0xb368b0: sbfx            x1, x0, #1, #0x1f
    //     0xb368b4: tbz             w0, #0, #0xb368bc
    //     0xb368b8: ldur            x1, [x0, #7]
    // 0xb368bc: tbz             x1, #0x3f, #0xb368c8
    // 0xb368c0: r4 = 999
    //     0xb368c0: movz            x4, #0x3e7
    // 0xb368c4: b               #0xb368cc
    // 0xb368c8: r4 = 0
    //     0xb368c8: movz            x4, #0
    // 0xb368cc: ldur            x0, [fp, #-8]
    // 0xb368d0: ldur            x2, [fp, #-0x10]
    // 0xb368d4: r3 = 1000
    //     0xb368d4: movz            x3, #0x3e8
    // 0xb368d8: sub             x5, x1, x4
    // 0xb368dc: sdiv            x4, x5, x3
    // 0xb368e0: stur            x4, [fp, #-0x18]
    // 0xb368e4: r1 = LoadClassIdInstr(r0)
    //     0xb368e4: ldur            x1, [x0, #-1]
    //     0xb368e8: ubfx            x1, x1, #0xc, #0x14
    // 0xb368ec: mov             x16, x0
    // 0xb368f0: mov             x0, x1
    // 0xb368f4: mov             x1, x16
    // 0xb368f8: r0 = GDT[cid_x0 + -0xecb]()
    //     0xb368f8: sub             lr, x0, #0xecb
    //     0xb368fc: ldr             lr, [x21, lr, lsl #3]
    //     0xb36900: blr             lr
    // 0xb36904: mov             x1, x0
    // 0xb36908: ldur            x0, [fp, #-0x18]
    // 0xb3690c: sub             x2, x0, x1
    // 0xb36910: ldur            x3, [fp, #-0x10]
    // 0xb36914: stur            x2, [fp, #-0x20]
    // 0xb36918: r0 = LoadClassIdInstr(r3)
    //     0xb36918: ldur            x0, [x3, #-1]
    //     0xb3691c: ubfx            x0, x0, #0xc, #0x14
    // 0xb36920: mov             x1, x3
    // 0xb36924: r0 = GDT[cid_x0 + -0xcc7]()
    //     0xb36924: sub             lr, x0, #0xcc7
    //     0xb36928: ldr             lr, [x21, lr, lsl #3]
    //     0xb3692c: blr             lr
    // 0xb36930: mov             x3, x0
    // 0xb36934: ldur            x2, [fp, #-0x10]
    // 0xb36938: stur            x3, [fp, #-8]
    // 0xb3693c: r0 = LoadClassIdInstr(r2)
    //     0xb3693c: ldur            x0, [x2, #-1]
    //     0xb36940: ubfx            x0, x0, #0xc, #0x14
    // 0xb36944: mov             x1, x2
    // 0xb36948: r0 = GDT[cid_x0 + -0xcfc]()
    //     0xb36948: sub             lr, x0, #0xcfc
    //     0xb3694c: ldr             lr, [x21, lr, lsl #3]
    //     0xb36950: blr             lr
    // 0xb36954: mov             x1, x0
    // 0xb36958: ldur            x0, [fp, #-0x20]
    // 0xb3695c: stur            x1, [fp, #-0x28]
    // 0xb36960: scvtf           d0, x0
    // 0xb36964: d1 = 1000.000000
    //     0xb36964: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0xb36968: ldr             d1, [x17, #0x238]
    // 0xb3696c: fdiv            d2, d0, d1
    // 0xb36970: d0 = 60.000000
    //     0xb36970: ldr             d0, [PP, #0x64b8]  ; [pp+0x64b8] IMM: double(60) from 0x404e000000000000
    // 0xb36974: fdiv            d1, d2, d0
    // 0xb36978: fdiv            d3, d1, d0
    // 0xb3697c: d4 = 24.000000
    //     0xb3697c: fmov            d4, #24.00000000
    // 0xb36980: fdiv            d5, d3, d4
    // 0xb36984: d6 = 30.000000
    //     0xb36984: fmov            d6, #30.00000000
    // 0xb36988: fdiv            d7, d5, d6
    // 0xb3698c: d8 = 365.000000
    //     0xb3698c: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2b550] IMM: double(365) from 0x4076d00000000000
    //     0xb36990: ldr             d8, [x17, #0x550]
    // 0xb36994: fdiv            d9, d5, d8
    // 0xb36998: d10 = 45.000000
    //     0xb36998: add             x17, PP, #0x2b, lsl #12  ; [pp+0x2b558] IMM: double(45) from 0x4046800000000000
    //     0xb3699c: ldr             d10, [x17, #0x558]
    // 0xb369a0: fcmp            d10, d2
    // 0xb369a4: b.le            #0xb36a24
    // 0xb369a8: ldur            x0, [fp, #-0x10]
    // 0xb369ac: mov             v0.16b, v2.16b
    // 0xb369b0: stp             fp, lr, [SP, #-0x10]!
    // 0xb369b4: mov             fp, SP
    // 0xb369b8: CallRuntime_LibcRound(double) -> double
    //     0xb369b8: and             SP, SP, #0xfffffffffffffff0
    //     0xb369bc: mov             sp, SP
    //     0xb369c0: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb369c4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb369c8: blr             x16
    //     0xb369cc: movz            x16, #0x8
    //     0xb369d0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb369d4: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb369d8: sub             sp, x16, #1, lsl #12
    //     0xb369dc: mov             SP, fp
    //     0xb369e0: ldp             fp, lr, [SP], #0x10
    // 0xb369e4: fcmp            d0, d0
    // 0xb369e8: b.vs            #0xb37028
    // 0xb369ec: fcvtzs          x0, d0
    // 0xb369f0: asr             x16, x0, #0x1e
    // 0xb369f4: cmp             x16, x0, asr #63
    // 0xb369f8: b.ne            #0xb37028
    // 0xb369fc: lsl             x0, x0, #1
    // 0xb36a00: ldur            x2, [fp, #-0x10]
    // 0xb36a04: r0 = LoadClassIdInstr(r2)
    //     0xb36a04: ldur            x0, [x2, #-1]
    //     0xb36a08: ubfx            x0, x0, #0xc, #0x14
    // 0xb36a0c: mov             x1, x2
    // 0xb36a10: r0 = GDT[cid_x0 + -0xd05]()
    //     0xb36a10: sub             lr, x0, #0xd05
    //     0xb36a14: ldr             lr, [x21, lr, lsl #3]
    //     0xb36a18: blr             lr
    // 0xb36a1c: mov             x6, x0
    // 0xb36a20: b               #0xb36f64
    // 0xb36a24: d11 = 90.000000
    //     0xb36a24: ldr             d11, [PP, #0x6520]  ; [pp+0x6520] IMM: double(90) from 0x4056800000000000
    // 0xb36a28: fcmp            d11, d2
    // 0xb36a2c: b.le            #0xb36aa8
    // 0xb36a30: ldur            x1, [fp, #-0x10]
    // 0xb36a34: mov             v0.16b, v1.16b
    // 0xb36a38: stp             fp, lr, [SP, #-0x10]!
    // 0xb36a3c: mov             fp, SP
    // 0xb36a40: CallRuntime_LibcRound(double) -> double
    //     0xb36a40: and             SP, SP, #0xfffffffffffffff0
    //     0xb36a44: mov             sp, SP
    //     0xb36a48: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36a4c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36a50: blr             x16
    //     0xb36a54: movz            x16, #0x8
    //     0xb36a58: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36a5c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36a60: sub             sp, x16, #1, lsl #12
    //     0xb36a64: mov             SP, fp
    //     0xb36a68: ldp             fp, lr, [SP], #0x10
    // 0xb36a6c: fcmp            d0, d0
    // 0xb36a70: b.vs            #0xb37044
    // 0xb36a74: fcvtzs          x0, d0
    // 0xb36a78: asr             x16, x0, #0x1e
    // 0xb36a7c: cmp             x16, x0, asr #63
    // 0xb36a80: b.ne            #0xb37044
    // 0xb36a84: lsl             x0, x0, #1
    // 0xb36a88: ldur            x2, [fp, #-0x10]
    // 0xb36a8c: r0 = LoadClassIdInstr(r2)
    //     0xb36a8c: ldur            x0, [x2, #-1]
    //     0xb36a90: ubfx            x0, x0, #0xc, #0x14
    // 0xb36a94: mov             x1, x2
    // 0xb36a98: r0 = GDT[cid_x0 + -0xe6f]()
    //     0xb36a98: sub             lr, x0, #0xe6f
    //     0xb36a9c: ldr             lr, [x21, lr, lsl #3]
    //     0xb36aa0: blr             lr
    // 0xb36aa4: b               #0xb36f60
    // 0xb36aa8: fcmp            d10, d1
    // 0xb36aac: b.le            #0xb36b34
    // 0xb36ab0: ldur            x1, [fp, #-0x10]
    // 0xb36ab4: mov             v0.16b, v1.16b
    // 0xb36ab8: stp             fp, lr, [SP, #-0x10]!
    // 0xb36abc: mov             fp, SP
    // 0xb36ac0: CallRuntime_LibcRound(double) -> double
    //     0xb36ac0: and             SP, SP, #0xfffffffffffffff0
    //     0xb36ac4: mov             sp, SP
    //     0xb36ac8: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36acc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36ad0: blr             x16
    //     0xb36ad4: movz            x16, #0x8
    //     0xb36ad8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36adc: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36ae0: sub             sp, x16, #1, lsl #12
    //     0xb36ae4: mov             SP, fp
    //     0xb36ae8: ldp             fp, lr, [SP], #0x10
    // 0xb36aec: fcmp            d0, d0
    // 0xb36af0: b.vs            #0xb37060
    // 0xb36af4: fcvtzs          x0, d0
    // 0xb36af8: asr             x16, x0, #0x1e
    // 0xb36afc: cmp             x16, x0, asr #63
    // 0xb36b00: b.ne            #0xb37060
    // 0xb36b04: lsl             x0, x0, #1
    // 0xb36b08: r2 = LoadInt32Instr(r0)
    //     0xb36b08: sbfx            x2, x0, #1, #0x1f
    //     0xb36b0c: tbz             w0, #0, #0xb36b14
    //     0xb36b10: ldur            x2, [x0, #7]
    // 0xb36b14: ldur            x3, [fp, #-0x10]
    // 0xb36b18: r0 = LoadClassIdInstr(r3)
    //     0xb36b18: ldur            x0, [x3, #-1]
    //     0xb36b1c: ubfx            x0, x0, #0xc, #0x14
    // 0xb36b20: mov             x1, x3
    // 0xb36b24: r0 = GDT[cid_x0 + -0xe88]()
    //     0xb36b24: sub             lr, x0, #0xe88
    //     0xb36b28: ldr             lr, [x21, lr, lsl #3]
    //     0xb36b2c: blr             lr
    // 0xb36b30: b               #0xb36f60
    // 0xb36b34: fcmp            d11, d1
    // 0xb36b38: b.le            #0xb36bb4
    // 0xb36b3c: ldur            x1, [fp, #-0x10]
    // 0xb36b40: mov             v0.16b, v1.16b
    // 0xb36b44: stp             fp, lr, [SP, #-0x10]!
    // 0xb36b48: mov             fp, SP
    // 0xb36b4c: CallRuntime_LibcRound(double) -> double
    //     0xb36b4c: and             SP, SP, #0xfffffffffffffff0
    //     0xb36b50: mov             sp, SP
    //     0xb36b54: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36b58: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36b5c: blr             x16
    //     0xb36b60: movz            x16, #0x8
    //     0xb36b64: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36b68: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36b6c: sub             sp, x16, #1, lsl #12
    //     0xb36b70: mov             SP, fp
    //     0xb36b74: ldp             fp, lr, [SP], #0x10
    // 0xb36b78: fcmp            d0, d0
    // 0xb36b7c: b.vs            #0xb3707c
    // 0xb36b80: fcvtzs          x0, d0
    // 0xb36b84: asr             x16, x0, #0x1e
    // 0xb36b88: cmp             x16, x0, asr #63
    // 0xb36b8c: b.ne            #0xb3707c
    // 0xb36b90: lsl             x0, x0, #1
    // 0xb36b94: ldur            x2, [fp, #-0x10]
    // 0xb36b98: r0 = LoadClassIdInstr(r2)
    //     0xb36b98: ldur            x0, [x2, #-1]
    //     0xb36b9c: ubfx            x0, x0, #0xc, #0x14
    // 0xb36ba0: mov             x1, x2
    // 0xb36ba4: r0 = GDT[cid_x0 + -0xf0b]()
    //     0xb36ba4: sub             lr, x0, #0xf0b
    //     0xb36ba8: ldr             lr, [x21, lr, lsl #3]
    //     0xb36bac: blr             lr
    // 0xb36bb0: b               #0xb36f60
    // 0xb36bb4: fcmp            d4, d3
    // 0xb36bb8: b.le            #0xb36c40
    // 0xb36bbc: ldur            x1, [fp, #-0x10]
    // 0xb36bc0: mov             v0.16b, v3.16b
    // 0xb36bc4: stp             fp, lr, [SP, #-0x10]!
    // 0xb36bc8: mov             fp, SP
    // 0xb36bcc: CallRuntime_LibcRound(double) -> double
    //     0xb36bcc: and             SP, SP, #0xfffffffffffffff0
    //     0xb36bd0: mov             sp, SP
    //     0xb36bd4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36bd8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36bdc: blr             x16
    //     0xb36be0: movz            x16, #0x8
    //     0xb36be4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36be8: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36bec: sub             sp, x16, #1, lsl #12
    //     0xb36bf0: mov             SP, fp
    //     0xb36bf4: ldp             fp, lr, [SP], #0x10
    // 0xb36bf8: fcmp            d0, d0
    // 0xb36bfc: b.vs            #0xb37098
    // 0xb36c00: fcvtzs          x0, d0
    // 0xb36c04: asr             x16, x0, #0x1e
    // 0xb36c08: cmp             x16, x0, asr #63
    // 0xb36c0c: b.ne            #0xb37098
    // 0xb36c10: lsl             x0, x0, #1
    // 0xb36c14: r2 = LoadInt32Instr(r0)
    //     0xb36c14: sbfx            x2, x0, #1, #0x1f
    //     0xb36c18: tbz             w0, #0, #0xb36c20
    //     0xb36c1c: ldur            x2, [x0, #7]
    // 0xb36c20: ldur            x3, [fp, #-0x10]
    // 0xb36c24: r0 = LoadClassIdInstr(r3)
    //     0xb36c24: ldur            x0, [x3, #-1]
    //     0xb36c28: ubfx            x0, x0, #0xc, #0x14
    // 0xb36c2c: mov             x1, x3
    // 0xb36c30: r0 = GDT[cid_x0 + -0xf11]()
    //     0xb36c30: sub             lr, x0, #0xf11
    //     0xb36c34: ldr             lr, [x21, lr, lsl #3]
    //     0xb36c38: blr             lr
    // 0xb36c3c: b               #0xb36f60
    // 0xb36c40: d1 = 48.000000
    //     0xb36c40: ldr             d1, [PP, #0x6e10]  ; [pp+0x6e10] IMM: double(48) from 0x4048000000000000
    // 0xb36c44: fcmp            d1, d3
    // 0xb36c48: b.le            #0xb36cc4
    // 0xb36c4c: ldur            x1, [fp, #-0x10]
    // 0xb36c50: mov             v0.16b, v3.16b
    // 0xb36c54: stp             fp, lr, [SP, #-0x10]!
    // 0xb36c58: mov             fp, SP
    // 0xb36c5c: CallRuntime_LibcRound(double) -> double
    //     0xb36c5c: and             SP, SP, #0xfffffffffffffff0
    //     0xb36c60: mov             sp, SP
    //     0xb36c64: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36c68: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36c6c: blr             x16
    //     0xb36c70: movz            x16, #0x8
    //     0xb36c74: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36c78: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36c7c: sub             sp, x16, #1, lsl #12
    //     0xb36c80: mov             SP, fp
    //     0xb36c84: ldp             fp, lr, [SP], #0x10
    // 0xb36c88: fcmp            d0, d0
    // 0xb36c8c: b.vs            #0xb370b4
    // 0xb36c90: fcvtzs          x0, d0
    // 0xb36c94: asr             x16, x0, #0x1e
    // 0xb36c98: cmp             x16, x0, asr #63
    // 0xb36c9c: b.ne            #0xb370b4
    // 0xb36ca0: lsl             x0, x0, #1
    // 0xb36ca4: ldur            x2, [fp, #-0x10]
    // 0xb36ca8: r0 = LoadClassIdInstr(r2)
    //     0xb36ca8: ldur            x0, [x2, #-1]
    //     0xb36cac: ubfx            x0, x0, #0xc, #0x14
    // 0xb36cb0: mov             x1, x2
    // 0xb36cb4: r0 = GDT[cid_x0 + -0xf21]()
    //     0xb36cb4: sub             lr, x0, #0xf21
    //     0xb36cb8: ldr             lr, [x21, lr, lsl #3]
    //     0xb36cbc: blr             lr
    // 0xb36cc0: b               #0xb36f60
    // 0xb36cc4: fcmp            d6, d5
    // 0xb36cc8: b.le            #0xb36d50
    // 0xb36ccc: ldur            x1, [fp, #-0x10]
    // 0xb36cd0: mov             v0.16b, v5.16b
    // 0xb36cd4: stp             fp, lr, [SP, #-0x10]!
    // 0xb36cd8: mov             fp, SP
    // 0xb36cdc: CallRuntime_LibcRound(double) -> double
    //     0xb36cdc: and             SP, SP, #0xfffffffffffffff0
    //     0xb36ce0: mov             sp, SP
    //     0xb36ce4: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36ce8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36cec: blr             x16
    //     0xb36cf0: movz            x16, #0x8
    //     0xb36cf4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36cf8: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36cfc: sub             sp, x16, #1, lsl #12
    //     0xb36d00: mov             SP, fp
    //     0xb36d04: ldp             fp, lr, [SP], #0x10
    // 0xb36d08: fcmp            d0, d0
    // 0xb36d0c: b.vs            #0xb370d0
    // 0xb36d10: fcvtzs          x0, d0
    // 0xb36d14: asr             x16, x0, #0x1e
    // 0xb36d18: cmp             x16, x0, asr #63
    // 0xb36d1c: b.ne            #0xb370d0
    // 0xb36d20: lsl             x0, x0, #1
    // 0xb36d24: r2 = LoadInt32Instr(r0)
    //     0xb36d24: sbfx            x2, x0, #1, #0x1f
    //     0xb36d28: tbz             w0, #0, #0xb36d30
    //     0xb36d2c: ldur            x2, [x0, #7]
    // 0xb36d30: ldur            x3, [fp, #-0x10]
    // 0xb36d34: r0 = LoadClassIdInstr(r3)
    //     0xb36d34: ldur            x0, [x3, #-1]
    //     0xb36d38: ubfx            x0, x0, #0xc, #0x14
    // 0xb36d3c: mov             x1, x3
    // 0xb36d40: r0 = GDT[cid_x0 + -0xf27]()
    //     0xb36d40: sub             lr, x0, #0xf27
    //     0xb36d44: ldr             lr, [x21, lr, lsl #3]
    //     0xb36d48: blr             lr
    // 0xb36d4c: b               #0xb36f60
    // 0xb36d50: fcmp            d0, d5
    // 0xb36d54: b.le            #0xb36dd0
    // 0xb36d58: ldur            x1, [fp, #-0x10]
    // 0xb36d5c: mov             v0.16b, v5.16b
    // 0xb36d60: stp             fp, lr, [SP, #-0x10]!
    // 0xb36d64: mov             fp, SP
    // 0xb36d68: CallRuntime_LibcRound(double) -> double
    //     0xb36d68: and             SP, SP, #0xfffffffffffffff0
    //     0xb36d6c: mov             sp, SP
    //     0xb36d70: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36d74: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36d78: blr             x16
    //     0xb36d7c: movz            x16, #0x8
    //     0xb36d80: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36d84: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36d88: sub             sp, x16, #1, lsl #12
    //     0xb36d8c: mov             SP, fp
    //     0xb36d90: ldp             fp, lr, [SP], #0x10
    // 0xb36d94: fcmp            d0, d0
    // 0xb36d98: b.vs            #0xb370ec
    // 0xb36d9c: fcvtzs          x0, d0
    // 0xb36da0: asr             x16, x0, #0x1e
    // 0xb36da4: cmp             x16, x0, asr #63
    // 0xb36da8: b.ne            #0xb370ec
    // 0xb36dac: lsl             x0, x0, #1
    // 0xb36db0: ldur            x2, [fp, #-0x10]
    // 0xb36db4: r0 = LoadClassIdInstr(r2)
    //     0xb36db4: ldur            x0, [x2, #-1]
    //     0xb36db8: ubfx            x0, x0, #0xc, #0x14
    // 0xb36dbc: mov             x1, x2
    // 0xb36dc0: r0 = GDT[cid_x0 + -0xf36]()
    //     0xb36dc0: sub             lr, x0, #0xf36
    //     0xb36dc4: ldr             lr, [x21, lr, lsl #3]
    //     0xb36dc8: blr             lr
    // 0xb36dcc: b               #0xb36f60
    // 0xb36dd0: fcmp            d8, d5
    // 0xb36dd4: b.le            #0xb36e5c
    // 0xb36dd8: ldur            x1, [fp, #-0x10]
    // 0xb36ddc: mov             v0.16b, v7.16b
    // 0xb36de0: stp             fp, lr, [SP, #-0x10]!
    // 0xb36de4: mov             fp, SP
    // 0xb36de8: CallRuntime_LibcRound(double) -> double
    //     0xb36de8: and             SP, SP, #0xfffffffffffffff0
    //     0xb36dec: mov             sp, SP
    //     0xb36df0: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36df4: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36df8: blr             x16
    //     0xb36dfc: movz            x16, #0x8
    //     0xb36e00: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36e04: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36e08: sub             sp, x16, #1, lsl #12
    //     0xb36e0c: mov             SP, fp
    //     0xb36e10: ldp             fp, lr, [SP], #0x10
    // 0xb36e14: fcmp            d0, d0
    // 0xb36e18: b.vs            #0xb37108
    // 0xb36e1c: fcvtzs          x0, d0
    // 0xb36e20: asr             x16, x0, #0x1e
    // 0xb36e24: cmp             x16, x0, asr #63
    // 0xb36e28: b.ne            #0xb37108
    // 0xb36e2c: lsl             x0, x0, #1
    // 0xb36e30: r2 = LoadInt32Instr(r0)
    //     0xb36e30: sbfx            x2, x0, #1, #0x1f
    //     0xb36e34: tbz             w0, #0, #0xb36e3c
    //     0xb36e38: ldur            x2, [x0, #7]
    // 0xb36e3c: ldur            x3, [fp, #-0x10]
    // 0xb36e40: r0 = LoadClassIdInstr(r3)
    //     0xb36e40: ldur            x0, [x3, #-1]
    //     0xb36e44: ubfx            x0, x0, #0xc, #0x14
    // 0xb36e48: mov             x1, x3
    // 0xb36e4c: r0 = GDT[cid_x0 + -0xf3c]()
    //     0xb36e4c: sub             lr, x0, #0xf3c
    //     0xb36e50: ldr             lr, [x21, lr, lsl #3]
    //     0xb36e54: blr             lr
    // 0xb36e58: b               #0xb36f60
    // 0xb36e5c: d0 = 2.000000
    //     0xb36e5c: fmov            d0, #2.00000000
    // 0xb36e60: fcmp            d0, d9
    // 0xb36e64: b.le            #0xb36ee0
    // 0xb36e68: ldur            x1, [fp, #-0x10]
    // 0xb36e6c: mov             v0.16b, v7.16b
    // 0xb36e70: stp             fp, lr, [SP, #-0x10]!
    // 0xb36e74: mov             fp, SP
    // 0xb36e78: CallRuntime_LibcRound(double) -> double
    //     0xb36e78: and             SP, SP, #0xfffffffffffffff0
    //     0xb36e7c: mov             sp, SP
    //     0xb36e80: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36e84: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36e88: blr             x16
    //     0xb36e8c: movz            x16, #0x8
    //     0xb36e90: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36e94: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36e98: sub             sp, x16, #1, lsl #12
    //     0xb36e9c: mov             SP, fp
    //     0xb36ea0: ldp             fp, lr, [SP], #0x10
    // 0xb36ea4: fcmp            d0, d0
    // 0xb36ea8: b.vs            #0xb37124
    // 0xb36eac: fcvtzs          x0, d0
    // 0xb36eb0: asr             x16, x0, #0x1e
    // 0xb36eb4: cmp             x16, x0, asr #63
    // 0xb36eb8: b.ne            #0xb37124
    // 0xb36ebc: lsl             x0, x0, #1
    // 0xb36ec0: ldur            x2, [fp, #-0x10]
    // 0xb36ec4: r0 = LoadClassIdInstr(r2)
    //     0xb36ec4: ldur            x0, [x2, #-1]
    //     0xb36ec8: ubfx            x0, x0, #0xc, #0x14
    // 0xb36ecc: mov             x1, x2
    // 0xb36ed0: r0 = GDT[cid_x0 + -0xf47]()
    //     0xb36ed0: sub             lr, x0, #0xf47
    //     0xb36ed4: ldr             lr, [x21, lr, lsl #3]
    //     0xb36ed8: blr             lr
    // 0xb36edc: b               #0xb36f60
    // 0xb36ee0: ldur            x1, [fp, #-0x10]
    // 0xb36ee4: mov             v0.16b, v9.16b
    // 0xb36ee8: stp             fp, lr, [SP, #-0x10]!
    // 0xb36eec: mov             fp, SP
    // 0xb36ef0: CallRuntime_LibcRound(double) -> double
    //     0xb36ef0: and             SP, SP, #0xfffffffffffffff0
    //     0xb36ef4: mov             sp, SP
    //     0xb36ef8: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0xb36efc: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36f00: blr             x16
    //     0xb36f04: movz            x16, #0x8
    //     0xb36f08: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xb36f0c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xb36f10: sub             sp, x16, #1, lsl #12
    //     0xb36f14: mov             SP, fp
    //     0xb36f18: ldp             fp, lr, [SP], #0x10
    // 0xb36f1c: fcmp            d0, d0
    // 0xb36f20: b.vs            #0xb37140
    // 0xb36f24: fcvtzs          x0, d0
    // 0xb36f28: asr             x16, x0, #0x1e
    // 0xb36f2c: cmp             x16, x0, asr #63
    // 0xb36f30: b.ne            #0xb37140
    // 0xb36f34: lsl             x0, x0, #1
    // 0xb36f38: r2 = LoadInt32Instr(r0)
    //     0xb36f38: sbfx            x2, x0, #1, #0x1f
    //     0xb36f3c: tbz             w0, #0, #0xb36f44
    //     0xb36f40: ldur            x2, [x0, #7]
    // 0xb36f44: ldur            x3, [fp, #-0x10]
    // 0xb36f48: r0 = LoadClassIdInstr(r3)
    //     0xb36f48: ldur            x0, [x3, #-1]
    //     0xb36f4c: ubfx            x0, x0, #0xc, #0x14
    // 0xb36f50: mov             x1, x3
    // 0xb36f54: r0 = GDT[cid_x0 + -0xf84]()
    //     0xb36f54: sub             lr, x0, #0xf84
    //     0xb36f58: ldr             lr, [x21, lr, lsl #3]
    //     0xb36f5c: blr             lr
    // 0xb36f60: mov             x6, x0
    // 0xb36f64: ldur            x0, [fp, #-0x10]
    // 0xb36f68: ldur            x4, [fp, #-8]
    // 0xb36f6c: ldur            x3, [fp, #-0x28]
    // 0xb36f70: r5 = 6
    //     0xb36f70: movz            x5, #0x6
    // 0xb36f74: mov             x2, x5
    // 0xb36f78: stur            x6, [fp, #-0x30]
    // 0xb36f7c: r1 = Null
    //     0xb36f7c: mov             x1, NULL
    // 0xb36f80: r0 = AllocateArray()
    //     0xb36f80: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb36f84: mov             x2, x0
    // 0xb36f88: ldur            x0, [fp, #-8]
    // 0xb36f8c: stur            x2, [fp, #-0x38]
    // 0xb36f90: StoreField: r2->field_f = r0
    //     0xb36f90: stur            w0, [x2, #0xf]
    // 0xb36f94: ldur            x0, [fp, #-0x30]
    // 0xb36f98: StoreField: r2->field_13 = r0
    //     0xb36f98: stur            w0, [x2, #0x13]
    // 0xb36f9c: ldur            x0, [fp, #-0x28]
    // 0xb36fa0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb36fa0: stur            w0, [x2, #0x17]
    // 0xb36fa4: r1 = <String>
    //     0xb36fa4: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xb36fa8: r0 = AllocateGrowableArray()
    //     0xb36fa8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xb36fac: mov             x3, x0
    // 0xb36fb0: ldur            x0, [fp, #-0x38]
    // 0xb36fb4: stur            x3, [fp, #-8]
    // 0xb36fb8: StoreField: r3->field_f = r0
    //     0xb36fb8: stur            w0, [x3, #0xf]
    // 0xb36fbc: r0 = 6
    //     0xb36fbc: movz            x0, #0x6
    // 0xb36fc0: StoreField: r3->field_b = r0
    //     0xb36fc0: stur            w0, [x3, #0xb]
    // 0xb36fc4: r1 = Function '<anonymous closure>': static.
    //     0xb36fc4: add             x1, PP, #0x2b, lsl #12  ; [pp+0x2b560] AnonymousClosure: static (0x6446b8), in [package:flutter/src/foundation/stack_frame.dart] StackFrame::fromStackString (0x643988)
    //     0xb36fc8: ldr             x1, [x1, #0x560]
    // 0xb36fcc: r2 = Null
    //     0xb36fcc: mov             x2, NULL
    // 0xb36fd0: r0 = AllocateClosure()
    //     0xb36fd0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb36fd4: ldur            x1, [fp, #-8]
    // 0xb36fd8: mov             x2, x0
    // 0xb36fdc: r0 = where()
    //     0xb36fdc: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xb36fe0: mov             x2, x0
    // 0xb36fe4: ldur            x1, [fp, #-0x10]
    // 0xb36fe8: stur            x2, [fp, #-8]
    // 0xb36fec: r0 = LoadClassIdInstr(r1)
    //     0xb36fec: ldur            x0, [x1, #-1]
    //     0xb36ff0: ubfx            x0, x0, #0xc, #0x14
    // 0xb36ff4: r0 = GDT[cid_x0 + -0xfce]()
    //     0xb36ff4: sub             lr, x0, #0xfce
    //     0xb36ff8: ldr             lr, [x21, lr, lsl #3]
    //     0xb36ffc: blr             lr
    // 0xb37000: r16 = " "
    //     0xb37000: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xb37004: str             x16, [SP]
    // 0xb37008: ldur            x1, [fp, #-8]
    // 0xb3700c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb3700c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb37010: r0 = join()
    //     0xb37010: bl              #0x7ae598  ; [dart:core] Iterable::join
    // 0xb37014: LeaveFrame
    //     0xb37014: mov             SP, fp
    //     0xb37018: ldp             fp, lr, [SP], #0x10
    // 0xb3701c: ret
    //     0xb3701c: ret             
    // 0xb37020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb37020: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb37024: b               #0xb367a4
    // 0xb37028: SaveReg d0
    //     0xb37028: str             q0, [SP, #-0x10]!
    // 0xb3702c: r0 = 74
    //     0xb3702c: movz            x0, #0x4a
    // 0xb37030: r30 = DoubleToIntegerStub
    //     0xb37030: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb37034: LoadField: r30 = r30->field_7
    //     0xb37034: ldur            lr, [lr, #7]
    // 0xb37038: blr             lr
    // 0xb3703c: RestoreReg d0
    //     0xb3703c: ldr             q0, [SP], #0x10
    // 0xb37040: b               #0xb36a00
    // 0xb37044: SaveReg d0
    //     0xb37044: str             q0, [SP, #-0x10]!
    // 0xb37048: r0 = 74
    //     0xb37048: movz            x0, #0x4a
    // 0xb3704c: r30 = DoubleToIntegerStub
    //     0xb3704c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb37050: LoadField: r30 = r30->field_7
    //     0xb37050: ldur            lr, [lr, #7]
    // 0xb37054: blr             lr
    // 0xb37058: RestoreReg d0
    //     0xb37058: ldr             q0, [SP], #0x10
    // 0xb3705c: b               #0xb36a88
    // 0xb37060: SaveReg d0
    //     0xb37060: str             q0, [SP, #-0x10]!
    // 0xb37064: r0 = 74
    //     0xb37064: movz            x0, #0x4a
    // 0xb37068: r30 = DoubleToIntegerStub
    //     0xb37068: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb3706c: LoadField: r30 = r30->field_7
    //     0xb3706c: ldur            lr, [lr, #7]
    // 0xb37070: blr             lr
    // 0xb37074: RestoreReg d0
    //     0xb37074: ldr             q0, [SP], #0x10
    // 0xb37078: b               #0xb36b08
    // 0xb3707c: SaveReg d0
    //     0xb3707c: str             q0, [SP, #-0x10]!
    // 0xb37080: r0 = 74
    //     0xb37080: movz            x0, #0x4a
    // 0xb37084: r30 = DoubleToIntegerStub
    //     0xb37084: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb37088: LoadField: r30 = r30->field_7
    //     0xb37088: ldur            lr, [lr, #7]
    // 0xb3708c: blr             lr
    // 0xb37090: RestoreReg d0
    //     0xb37090: ldr             q0, [SP], #0x10
    // 0xb37094: b               #0xb36b94
    // 0xb37098: SaveReg d0
    //     0xb37098: str             q0, [SP, #-0x10]!
    // 0xb3709c: r0 = 74
    //     0xb3709c: movz            x0, #0x4a
    // 0xb370a0: r30 = DoubleToIntegerStub
    //     0xb370a0: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb370a4: LoadField: r30 = r30->field_7
    //     0xb370a4: ldur            lr, [lr, #7]
    // 0xb370a8: blr             lr
    // 0xb370ac: RestoreReg d0
    //     0xb370ac: ldr             q0, [SP], #0x10
    // 0xb370b0: b               #0xb36c14
    // 0xb370b4: SaveReg d0
    //     0xb370b4: str             q0, [SP, #-0x10]!
    // 0xb370b8: r0 = 74
    //     0xb370b8: movz            x0, #0x4a
    // 0xb370bc: r30 = DoubleToIntegerStub
    //     0xb370bc: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb370c0: LoadField: r30 = r30->field_7
    //     0xb370c0: ldur            lr, [lr, #7]
    // 0xb370c4: blr             lr
    // 0xb370c8: RestoreReg d0
    //     0xb370c8: ldr             q0, [SP], #0x10
    // 0xb370cc: b               #0xb36ca4
    // 0xb370d0: SaveReg d0
    //     0xb370d0: str             q0, [SP, #-0x10]!
    // 0xb370d4: r0 = 74
    //     0xb370d4: movz            x0, #0x4a
    // 0xb370d8: r30 = DoubleToIntegerStub
    //     0xb370d8: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb370dc: LoadField: r30 = r30->field_7
    //     0xb370dc: ldur            lr, [lr, #7]
    // 0xb370e0: blr             lr
    // 0xb370e4: RestoreReg d0
    //     0xb370e4: ldr             q0, [SP], #0x10
    // 0xb370e8: b               #0xb36d24
    // 0xb370ec: SaveReg d0
    //     0xb370ec: str             q0, [SP, #-0x10]!
    // 0xb370f0: r0 = 74
    //     0xb370f0: movz            x0, #0x4a
    // 0xb370f4: r30 = DoubleToIntegerStub
    //     0xb370f4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb370f8: LoadField: r30 = r30->field_7
    //     0xb370f8: ldur            lr, [lr, #7]
    // 0xb370fc: blr             lr
    // 0xb37100: RestoreReg d0
    //     0xb37100: ldr             q0, [SP], #0x10
    // 0xb37104: b               #0xb36db0
    // 0xb37108: SaveReg d0
    //     0xb37108: str             q0, [SP, #-0x10]!
    // 0xb3710c: r0 = 74
    //     0xb3710c: movz            x0, #0x4a
    // 0xb37110: r30 = DoubleToIntegerStub
    //     0xb37110: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb37114: LoadField: r30 = r30->field_7
    //     0xb37114: ldur            lr, [lr, #7]
    // 0xb37118: blr             lr
    // 0xb3711c: RestoreReg d0
    //     0xb3711c: ldr             q0, [SP], #0x10
    // 0xb37120: b               #0xb36e30
    // 0xb37124: SaveReg d0
    //     0xb37124: str             q0, [SP, #-0x10]!
    // 0xb37128: r0 = 74
    //     0xb37128: movz            x0, #0x4a
    // 0xb3712c: r30 = DoubleToIntegerStub
    //     0xb3712c: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb37130: LoadField: r30 = r30->field_7
    //     0xb37130: ldur            lr, [lr, #7]
    // 0xb37134: blr             lr
    // 0xb37138: RestoreReg d0
    //     0xb37138: ldr             q0, [SP], #0x10
    // 0xb3713c: b               #0xb36ec0
    // 0xb37140: SaveReg d0
    //     0xb37140: str             q0, [SP, #-0x10]!
    // 0xb37144: r0 = 74
    //     0xb37144: movz            x0, #0x4a
    // 0xb37148: r30 = DoubleToIntegerStub
    //     0xb37148: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xb3714c: LoadField: r30 = r30->field_7
    //     0xb3714c: ldur            lr, [lr, #7]
    // 0xb37150: blr             lr
    // 0xb37154: RestoreReg d0
    //     0xb37154: ldr             q0, [SP], #0x10
    // 0xb37158: b               #0xb36f38
  }
  static Map<String, LookupMessages> _lookupMessagesMap() {
    // ** addr: 0xb3718c, size: 0xbc
    // 0xb3718c: EnterFrame
    //     0xb3718c: stp             fp, lr, [SP, #-0x10]!
    //     0xb37190: mov             fp, SP
    // 0xb37194: AllocStack(0x18)
    //     0xb37194: sub             SP, SP, #0x18
    // 0xb37198: CheckStackOverflow
    //     0xb37198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3719c: cmp             SP, x16
    //     0xb371a0: b.ls            #0xb37240
    // 0xb371a4: r1 = Null
    //     0xb371a4: mov             x1, NULL
    // 0xb371a8: r2 = 16
    //     0xb371a8: movz            x2, #0x10
    // 0xb371ac: r0 = AllocateArray()
    //     0xb371ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb371b0: stur            x0, [fp, #-8]
    // 0xb371b4: r16 = "en"
    //     0xb371b4: add             x16, PP, #8, lsl #12  ; [pp+0x8e60] "en"
    //     0xb371b8: ldr             x16, [x16, #0xe60]
    // 0xb371bc: StoreField: r0->field_f = r16
    //     0xb371bc: stur            w16, [x0, #0xf]
    // 0xb371c0: r0 = EnMessages()
    //     0xb371c0: bl              #0xb3715c  ; AllocateEnMessagesStub -> EnMessages (size=0x8)
    // 0xb371c4: mov             x1, x0
    // 0xb371c8: ldur            x0, [fp, #-8]
    // 0xb371cc: StoreField: r0->field_13 = r1
    //     0xb371cc: stur            w1, [x0, #0x13]
    // 0xb371d0: r16 = "en_short"
    //     0xb371d0: add             x16, PP, #0xd, lsl #12  ; [pp+0xda98] "en_short"
    //     0xb371d4: ldr             x16, [x16, #0xa98]
    // 0xb371d8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb371d8: stur            w16, [x0, #0x17]
    // 0xb371dc: r0 = EnShortMessages()
    //     0xb371dc: bl              #0xb37260  ; AllocateEnShortMessagesStub -> EnShortMessages (size=0x8)
    // 0xb371e0: mov             x1, x0
    // 0xb371e4: ldur            x0, [fp, #-8]
    // 0xb371e8: StoreField: r0->field_1b = r1
    //     0xb371e8: stur            w1, [x0, #0x1b]
    // 0xb371ec: r16 = "es"
    //     0xb371ec: add             x16, PP, #9, lsl #12  ; [pp+0x9620] "es"
    //     0xb371f0: ldr             x16, [x16, #0x620]
    // 0xb371f4: StoreField: r0->field_1f = r16
    //     0xb371f4: stur            w16, [x0, #0x1f]
    // 0xb371f8: r0 = EsMessages()
    //     0xb371f8: bl              #0xb37254  ; AllocateEsMessagesStub -> EsMessages (size=0x8)
    // 0xb371fc: mov             x1, x0
    // 0xb37200: ldur            x0, [fp, #-8]
    // 0xb37204: StoreField: r0->field_23 = r1
    //     0xb37204: stur            w1, [x0, #0x23]
    // 0xb37208: r16 = "es_short"
    //     0xb37208: add             x16, PP, #0xd, lsl #12  ; [pp+0xdaa0] "es_short"
    //     0xb3720c: ldr             x16, [x16, #0xaa0]
    // 0xb37210: StoreField: r0->field_27 = r16
    //     0xb37210: stur            w16, [x0, #0x27]
    // 0xb37214: r0 = EsShortMessages()
    //     0xb37214: bl              #0xb37248  ; AllocateEsShortMessagesStub -> EsShortMessages (size=0x8)
    // 0xb37218: mov             x1, x0
    // 0xb3721c: ldur            x0, [fp, #-8]
    // 0xb37220: StoreField: r0->field_2b = r1
    //     0xb37220: stur            w1, [x0, #0x2b]
    // 0xb37224: r16 = <String, LookupMessages>
    //     0xb37224: add             x16, PP, #0xd, lsl #12  ; [pp+0xdaa8] TypeArguments: <String, LookupMessages>
    //     0xb37228: ldr             x16, [x16, #0xaa8]
    // 0xb3722c: stp             x0, x16, [SP]
    // 0xb37230: r0 = Map._fromLiteral()
    //     0xb37230: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb37234: LeaveFrame
    //     0xb37234: mov             SP, fp
    //     0xb37238: ldp             fp, lr, [SP], #0x10
    // 0xb3723c: ret
    //     0xb3723c: ret             
    // 0xb37240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb37240: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb37244: b               #0xb371a4
  }
  static _ setLocaleMessages(/* No info */) {
    // ** addr: 0xeca524, size: 0x8c
    // 0xeca524: EnterFrame
    //     0xeca524: stp             fp, lr, [SP, #-0x10]!
    //     0xeca528: mov             fp, SP
    // 0xeca52c: AllocStack(0x18)
    //     0xeca52c: sub             SP, SP, #0x18
    // 0xeca530: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xeca530: mov             x3, x1
    //     0xeca534: stur            x1, [fp, #-8]
    // 0xeca538: CheckStackOverflow
    //     0xeca538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeca53c: cmp             SP, x16
    //     0xeca540: b.ls            #0xeca5a8
    // 0xeca544: r0 = InitLateStaticField(0x15e8) // [package:timeago/src/timeago.dart] ::_lookupMessagesMap
    //     0xeca544: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeca548: ldr             x0, [x0, #0x2bd0]
    //     0xeca54c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeca550: cmp             w0, w16
    //     0xeca554: b.ne            #0xeca564
    //     0xeca558: add             x2, PP, #0xd, lsl #12  ; [pp+0xda90] Field <::._lookupMessagesMap@2027518848>: static late (offset: 0x15e8)
    //     0xeca55c: ldr             x2, [x2, #0xa90]
    //     0xeca560: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xeca564: stur            x0, [fp, #-0x10]
    // 0xeca568: r16 = "id"
    //     0xeca568: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xeca56c: ldr             x16, [x16, #0x740]
    // 0xeca570: str             x16, [SP]
    // 0xeca574: r0 = hashCode()
    //     0xeca574: bl              #0xbf4794  ; [dart:core] _OneByteString::hashCode
    // 0xeca578: r5 = LoadInt32Instr(r0)
    //     0xeca578: sbfx            x5, x0, #1, #0x1f
    //     0xeca57c: tbz             w0, #0, #0xeca584
    //     0xeca580: ldur            x5, [x0, #7]
    // 0xeca584: ldur            x1, [fp, #-0x10]
    // 0xeca588: ldur            x3, [fp, #-8]
    // 0xeca58c: r2 = "id"
    //     0xeca58c: add             x2, PP, #8, lsl #12  ; [pp+0x8740] "id"
    //     0xeca590: ldr             x2, [x2, #0x740]
    // 0xeca594: r0 = _set()
    //     0xeca594: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xeca598: r0 = Null
    //     0xeca598: mov             x0, NULL
    // 0xeca59c: LeaveFrame
    //     0xeca59c: mov             SP, fp
    //     0xeca5a0: ldp             fp, lr, [SP], #0x10
    // 0xeca5a4: ret
    //     0xeca5a4: ret             
    // 0xeca5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeca5a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeca5ac: b               #0xeca544
  }
}
