// lib: , url: package:timeline_tile/src/axis.dart

// class id: 1051204, size: 0x8
class :: {

  static _ calculateAxisPositioning(/* No info */) {
    // ** addr: 0x7d6214, size: 0x1ac
    // 0x7d6214: EnterFrame
    //     0x7d6214: stp             fp, lr, [SP, #-0x10]!
    //     0x7d6218: mov             fp, SP
    // 0x7d621c: AllocStack(0x48)
    //     0x7d621c: sub             SP, SP, #0x48
    // 0x7d6220: d3 = 0.000000
    //     0x7d6220: eor             v3.16b, v3.16b, v3.16b
    // 0x7d6224: mov             v31.16b, v1.16b
    // 0x7d6228: mov             v1.16b, v0.16b
    // 0x7d622c: mov             v0.16b, v31.16b
    // 0x7d6230: mov             v31.16b, v2.16b
    // 0x7d6234: mov             v2.16b, v1.16b
    // 0x7d6238: mov             v1.16b, v31.16b
    // 0x7d623c: stur            d0, [fp, #-0x28]
    // 0x7d6240: stur            d1, [fp, #-0x30]
    // 0x7d6244: CheckStackOverflow
    //     0x7d6244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d6248: cmp             SP, x16
    //     0x7d624c: b.ls            #0x7d63b8
    // 0x7d6250: fcmp            d3, d2
    // 0x7d6254: b.gt            #0x7d6398
    // 0x7d6258: d4 = 1.000000
    //     0x7d6258: fmov            d4, #1.00000000
    // 0x7d625c: fcmp            d2, d4
    // 0x7d6260: b.gt            #0x7d6398
    // 0x7d6264: fcmp            d0, d1
    // 0x7d6268: b.lt            #0x7d6290
    // 0x7d626c: r16 = true
    //     0x7d626c: add             x16, NULL, #0x20  ; true
    // 0x7d6270: r30 = true
    //     0x7d6270: add             lr, NULL, #0x20  ; true
    // 0x7d6274: stp             lr, x16, [SP]
    // 0x7d6278: r4 = const [0, 0x4, 0x2, 0x2, alignEnd, 0x2, alignStart, 0x3, null]
    //     0x7d6278: add             x4, PP, #0x57, lsl #12  ; [pp+0x57ca8] List(9) [0, 0x4, 0x2, 0x2, "alignEnd", 0x2, "alignStart", 0x3, Null]
    //     0x7d627c: ldr             x4, [x4, #0xca8]
    // 0x7d6280: r0 = _alignObject()
    //     0x7d6280: bl              #0x7d63d8  ; [package:timeline_tile/src/axis.dart] ::_alignObject
    // 0x7d6284: LeaveFrame
    //     0x7d6284: mov             SP, fp
    //     0x7d6288: ldp             fp, lr, [SP], #0x10
    // 0x7d628c: ret
    //     0x7d628c: ret             
    // 0x7d6290: d4 = 2.000000
    //     0x7d6290: fmov            d4, #2.00000000
    // 0x7d6294: fmul            d5, d1, d2
    // 0x7d6298: fdiv            d2, d0, d4
    // 0x7d629c: fsub            d4, d5, d2
    // 0x7d62a0: stur            d4, [fp, #-0x20]
    // 0x7d62a4: fcmp            d3, d4
    // 0x7d62a8: b.le            #0x7d62cc
    // 0x7d62ac: r16 = true
    //     0x7d62ac: add             x16, NULL, #0x20  ; true
    // 0x7d62b0: str             x16, [SP]
    // 0x7d62b4: r4 = const [0, 0x3, 0x1, 0x2, alignStart, 0x2, null]
    //     0x7d62b4: add             x4, PP, #0x57, lsl #12  ; [pp+0x57cb0] List(7) [0, 0x3, 0x1, 0x2, "alignStart", 0x2, Null]
    //     0x7d62b8: ldr             x4, [x4, #0xcb0]
    // 0x7d62bc: r0 = _alignObject()
    //     0x7d62bc: bl              #0x7d63d8  ; [package:timeline_tile/src/axis.dart] ::_alignObject
    // 0x7d62c0: LeaveFrame
    //     0x7d62c0: mov             SP, fp
    //     0x7d62c4: ldp             fp, lr, [SP], #0x10
    // 0x7d62c8: ret
    //     0x7d62c8: ret             
    // 0x7d62cc: fsub            d6, d1, d5
    // 0x7d62d0: fsub            d5, d6, d2
    // 0x7d62d4: fcmp            d3, d5
    // 0x7d62d8: b.le            #0x7d62fc
    // 0x7d62dc: r16 = true
    //     0x7d62dc: add             x16, NULL, #0x20  ; true
    // 0x7d62e0: str             x16, [SP]
    // 0x7d62e4: r4 = const [0, 0x3, 0x1, 0x2, alignEnd, 0x2, null]
    //     0x7d62e4: add             x4, PP, #0x57, lsl #12  ; [pp+0x57cb8] List(7) [0, 0x3, 0x1, 0x2, "alignEnd", 0x2, Null]
    //     0x7d62e8: ldr             x4, [x4, #0xcb8]
    // 0x7d62ec: r0 = _alignObject()
    //     0x7d62ec: bl              #0x7d63d8  ; [package:timeline_tile/src/axis.dart] ::_alignObject
    // 0x7d62f0: LeaveFrame
    //     0x7d62f0: mov             SP, fp
    //     0x7d62f4: ldp             fp, lr, [SP], #0x10
    // 0x7d62f8: ret
    //     0x7d62f8: ret             
    // 0x7d62fc: r0 = AxisCoordinates()
    //     0x7d62fc: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d6300: stur            x0, [fp, #-8]
    // 0x7d6304: StoreField: r0->field_7 = rZR
    //     0x7d6304: stur            xzr, [x0, #7]
    // 0x7d6308: ldur            d0, [fp, #-0x20]
    // 0x7d630c: StoreField: r0->field_f = d0
    //     0x7d630c: stur            d0, [x0, #0xf]
    // 0x7d6310: d1 = 0.000000
    //     0x7d6310: eor             v1.16b, v1.16b, v1.16b
    // 0x7d6314: fsub            d2, d0, d1
    // 0x7d6318: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d6318: stur            d2, [x0, #0x17]
    // 0x7d631c: ldur            d1, [fp, #-0x28]
    // 0x7d6320: fadd            d2, d0, d1
    // 0x7d6324: stur            d2, [fp, #-0x38]
    // 0x7d6328: r0 = AxisCoordinates()
    //     0x7d6328: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d632c: ldur            d0, [fp, #-0x20]
    // 0x7d6330: stur            x0, [fp, #-0x10]
    // 0x7d6334: StoreField: r0->field_7 = d0
    //     0x7d6334: stur            d0, [x0, #7]
    // 0x7d6338: ldur            d1, [fp, #-0x38]
    // 0x7d633c: StoreField: r0->field_f = d1
    //     0x7d633c: stur            d1, [x0, #0xf]
    // 0x7d6340: fsub            d2, d1, d0
    // 0x7d6344: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d6344: stur            d2, [x0, #0x17]
    // 0x7d6348: r0 = AxisCoordinates()
    //     0x7d6348: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d634c: ldur            d0, [fp, #-0x38]
    // 0x7d6350: stur            x0, [fp, #-0x18]
    // 0x7d6354: StoreField: r0->field_7 = d0
    //     0x7d6354: stur            d0, [x0, #7]
    // 0x7d6358: ldur            d1, [fp, #-0x30]
    // 0x7d635c: StoreField: r0->field_f = d1
    //     0x7d635c: stur            d1, [x0, #0xf]
    // 0x7d6360: fsub            d2, d1, d0
    // 0x7d6364: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d6364: stur            d2, [x0, #0x17]
    // 0x7d6368: r0 = AxisPosition()
    //     0x7d6368: bl              #0x7d63c0  ; AllocateAxisPositionStub -> AxisPosition (size=0x14)
    // 0x7d636c: mov             x1, x0
    // 0x7d6370: ldur            x0, [fp, #-8]
    // 0x7d6374: StoreField: r1->field_7 = r0
    //     0x7d6374: stur            w0, [x1, #7]
    // 0x7d6378: ldur            x0, [fp, #-0x10]
    // 0x7d637c: StoreField: r1->field_b = r0
    //     0x7d637c: stur            w0, [x1, #0xb]
    // 0x7d6380: ldur            x0, [fp, #-0x18]
    // 0x7d6384: StoreField: r1->field_f = r0
    //     0x7d6384: stur            w0, [x1, #0xf]
    // 0x7d6388: mov             x0, x1
    // 0x7d638c: LeaveFrame
    //     0x7d638c: mov             SP, fp
    //     0x7d6390: ldp             fp, lr, [SP], #0x10
    // 0x7d6394: ret
    //     0x7d6394: ret             
    // 0x7d6398: r0 = AssertionError()
    //     0x7d6398: bl              #0x6bcfb4  ; AllocateAssertionErrorStub -> AssertionError (size=0x10)
    // 0x7d639c: mov             x1, x0
    // 0x7d63a0: r0 = "The axisPosition must be provided and must be a value between 0.0 and 1.0 inclusive"
    //     0x7d63a0: add             x0, PP, #0x57, lsl #12  ; [pp+0x57cc0] "The axisPosition must be provided and must be a value between 0.0 and 1.0 inclusive"
    //     0x7d63a4: ldr             x0, [x0, #0xcc0]
    // 0x7d63a8: StoreField: r1->field_b = r0
    //     0x7d63a8: stur            w0, [x1, #0xb]
    // 0x7d63ac: mov             x0, x1
    // 0x7d63b0: r0 = Throw()
    //     0x7d63b0: bl              #0xec04b8  ; ThrowStub
    // 0x7d63b4: brk             #0
    // 0x7d63b8: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d63b8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d63bc: b               #0x7d6250
  }
  static _ _alignObject(/* No info */) {
    // ** addr: 0x7d63d8, size: 0x26c
    // 0x7d63d8: EnterFrame
    //     0x7d63d8: stp             fp, lr, [SP, #-0x10]!
    //     0x7d63dc: mov             fp, SP
    // 0x7d63e0: AllocStack(0x30)
    //     0x7d63e0: sub             SP, SP, #0x30
    // 0x7d63e4: SetupParameters(dynamic _ /* d0 => d0, fp-0x30 */, dynamic _ /* d1 => d1, fp-0x20 */, {dynamic alignEnd = false /* r2, fp-0x18 */, dynamic alignStart = false /* r0, fp-0x10 */})
    //     0x7d63e4: stur            d1, [fp, #-0x20]
    //     0x7d63e8: stur            d0, [fp, #-0x30]
    //     0x7d63ec: ldur            w0, [x4, #0x13]
    //     0x7d63f0: ldur            w1, [x4, #0x1f]
    //     0x7d63f4: add             x1, x1, HEAP, lsl #32
    //     0x7d63f8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57cc8] "alignEnd"
    //     0x7d63fc: ldr             x16, [x16, #0xcc8]
    //     0x7d6400: cmp             w1, w16
    //     0x7d6404: b.ne            #0x7d6428
    //     0x7d6408: ldur            w1, [x4, #0x23]
    //     0x7d640c: add             x1, x1, HEAP, lsl #32
    //     0x7d6410: sub             w2, w0, w1
    //     0x7d6414: add             x1, fp, w2, sxtw #2
    //     0x7d6418: ldr             x1, [x1, #8]
    //     0x7d641c: mov             x2, x1
    //     0x7d6420: movz            x1, #0x1
    //     0x7d6424: b               #0x7d6430
    //     0x7d6428: add             x2, NULL, #0x30  ; false
    //     0x7d642c: movz            x1, #0
    //     0x7d6430: stur            x2, [fp, #-0x18]
    //     0x7d6434: lsl             x3, x1, #1
    //     0x7d6438: lsl             w1, w3, #1
    //     0x7d643c: add             w3, w1, #8
    //     0x7d6440: add             x16, x4, w3, sxtw #1
    //     0x7d6444: ldur            w5, [x16, #0xf]
    //     0x7d6448: add             x5, x5, HEAP, lsl #32
    //     0x7d644c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57cd0] "alignStart"
    //     0x7d6450: ldr             x16, [x16, #0xcd0]
    //     0x7d6454: cmp             w5, w16
    //     0x7d6458: b.ne            #0x7d647c
    //     0x7d645c: add             w3, w1, #0xa
    //     0x7d6460: add             x16, x4, w3, sxtw #1
    //     0x7d6464: ldur            w1, [x16, #0xf]
    //     0x7d6468: add             x1, x1, HEAP, lsl #32
    //     0x7d646c: sub             w3, w0, w1
    //     0x7d6470: add             x0, fp, w3, sxtw #2
    //     0x7d6474: ldr             x0, [x0, #8]
    //     0x7d6478: b               #0x7d6480
    //     0x7d647c: add             x0, NULL, #0x30  ; false
    //     0x7d6480: stur            x0, [fp, #-0x10]
    // 0x7d6484: tbz             w0, #4, #0x7d648c
    // 0x7d6488: tbnz            w2, #4, #0x7d6624
    // 0x7d648c: tbnz            w0, #4, #0x7d64ec
    // 0x7d6490: tbnz            w2, #4, #0x7d64e4
    // 0x7d6494: r0 = AxisCoordinates()
    //     0x7d6494: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d6498: stur            x0, [fp, #-8]
    // 0x7d649c: StoreField: r0->field_7 = rZR
    //     0x7d649c: stur            xzr, [x0, #7]
    // 0x7d64a0: ldur            d1, [fp, #-0x20]
    // 0x7d64a4: StoreField: r0->field_f = d1
    //     0x7d64a4: stur            d1, [x0, #0xf]
    // 0x7d64a8: d2 = 0.000000
    //     0x7d64a8: eor             v2.16b, v2.16b, v2.16b
    // 0x7d64ac: fsub            d0, d1, d2
    // 0x7d64b0: ArrayStore: r0[0] = d0  ; List_8
    //     0x7d64b0: stur            d0, [x0, #0x17]
    // 0x7d64b4: r0 = AxisPosition()
    //     0x7d64b4: bl              #0x7d63c0  ; AllocateAxisPositionStub -> AxisPosition (size=0x14)
    // 0x7d64b8: mov             x1, x0
    // 0x7d64bc: r0 = Instance_AxisCoordinates
    //     0x7d64bc: add             x0, PP, #0x57, lsl #12  ; [pp+0x57cd8] Obj!AxisCoordinates@e0bf31
    //     0x7d64c0: ldr             x0, [x0, #0xcd8]
    // 0x7d64c4: StoreField: r1->field_7 = r0
    //     0x7d64c4: stur            w0, [x1, #7]
    // 0x7d64c8: ldur            x2, [fp, #-8]
    // 0x7d64cc: StoreField: r1->field_b = r2
    //     0x7d64cc: stur            w2, [x1, #0xb]
    // 0x7d64d0: StoreField: r1->field_f = r0
    //     0x7d64d0: stur            w0, [x1, #0xf]
    // 0x7d64d4: mov             x0, x1
    // 0x7d64d8: LeaveFrame
    //     0x7d64d8: mov             SP, fp
    //     0x7d64dc: ldp             fp, lr, [SP], #0x10
    // 0x7d64e0: ret
    //     0x7d64e0: ret             
    // 0x7d64e4: d2 = 0.000000
    //     0x7d64e4: eor             v2.16b, v2.16b, v2.16b
    // 0x7d64e8: b               #0x7d64f0
    // 0x7d64ec: d2 = 0.000000
    //     0x7d64ec: eor             v2.16b, v2.16b, v2.16b
    // 0x7d64f0: tbnz            w0, #4, #0x7d6504
    // 0x7d64f4: mov             v1.16b, v2.16b
    // 0x7d64f8: r1 = Instance_AxisCoordinates
    //     0x7d64f8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57cd8] Obj!AxisCoordinates@e0bf31
    //     0x7d64fc: ldr             x1, [x1, #0xcd8]
    // 0x7d6500: b               #0x7d6530
    // 0x7d6504: fsub            d3, d1, d0
    // 0x7d6508: stur            d3, [fp, #-0x28]
    // 0x7d650c: r0 = AxisCoordinates()
    //     0x7d650c: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d6510: StoreField: r0->field_7 = rZR
    //     0x7d6510: stur            xzr, [x0, #7]
    // 0x7d6514: ldur            d0, [fp, #-0x28]
    // 0x7d6518: StoreField: r0->field_f = d0
    //     0x7d6518: stur            d0, [x0, #0xf]
    // 0x7d651c: d1 = 0.000000
    //     0x7d651c: eor             v1.16b, v1.16b, v1.16b
    // 0x7d6520: fsub            d2, d0, d1
    // 0x7d6524: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d6524: stur            d2, [x0, #0x17]
    // 0x7d6528: mov             x1, x0
    // 0x7d652c: ldur            x0, [fp, #-0x10]
    // 0x7d6530: stur            x1, [fp, #-8]
    // 0x7d6534: tbnz            w0, #4, #0x7d6564
    // 0x7d6538: ldur            d0, [fp, #-0x30]
    // 0x7d653c: r0 = AxisCoordinates()
    //     0x7d653c: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d6540: StoreField: r0->field_7 = rZR
    //     0x7d6540: stur            xzr, [x0, #7]
    // 0x7d6544: ldur            d0, [fp, #-0x30]
    // 0x7d6548: StoreField: r0->field_f = d0
    //     0x7d6548: stur            d0, [x0, #0xf]
    // 0x7d654c: d1 = 0.000000
    //     0x7d654c: eor             v1.16b, v1.16b, v1.16b
    // 0x7d6550: fsub            d2, d0, d1
    // 0x7d6554: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d6554: stur            d2, [x0, #0x17]
    // 0x7d6558: mov             x1, x0
    // 0x7d655c: ldur            d1, [fp, #-0x20]
    // 0x7d6560: b               #0x7d6594
    // 0x7d6564: ldur            d0, [fp, #-0x30]
    // 0x7d6568: ldur            d1, [fp, #-0x20]
    // 0x7d656c: fsub            d2, d1, d0
    // 0x7d6570: stur            d2, [fp, #-0x28]
    // 0x7d6574: r0 = AxisCoordinates()
    //     0x7d6574: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d6578: ldur            d0, [fp, #-0x28]
    // 0x7d657c: StoreField: r0->field_7 = d0
    //     0x7d657c: stur            d0, [x0, #7]
    // 0x7d6580: ldur            d1, [fp, #-0x20]
    // 0x7d6584: StoreField: r0->field_f = d1
    //     0x7d6584: stur            d1, [x0, #0xf]
    // 0x7d6588: fsub            d2, d1, d0
    // 0x7d658c: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d658c: stur            d2, [x0, #0x17]
    // 0x7d6590: mov             x1, x0
    // 0x7d6594: ldur            x0, [fp, #-0x18]
    // 0x7d6598: stur            x1, [fp, #-0x10]
    // 0x7d659c: tbnz            w0, #4, #0x7d65c0
    // 0x7d65a0: r0 = AxisCoordinates()
    //     0x7d65a0: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d65a4: ldur            d0, [fp, #-0x20]
    // 0x7d65a8: StoreField: r0->field_7 = d0
    //     0x7d65a8: stur            d0, [x0, #7]
    // 0x7d65ac: StoreField: r0->field_f = d0
    //     0x7d65ac: stur            d0, [x0, #0xf]
    // 0x7d65b0: fsub            d1, d0, d0
    // 0x7d65b4: ArrayStore: r0[0] = d1  ; List_8
    //     0x7d65b4: stur            d1, [x0, #0x17]
    // 0x7d65b8: mov             x2, x0
    // 0x7d65bc: b               #0x7d65e8
    // 0x7d65c0: mov             v0.16b, v1.16b
    // 0x7d65c4: ldur            d1, [fp, #-0x30]
    // 0x7d65c8: r0 = AxisCoordinates()
    //     0x7d65c8: bl              #0x7d63cc  ; AllocateAxisCoordinatesStub -> AxisCoordinates (size=0x20)
    // 0x7d65cc: ldur            d0, [fp, #-0x30]
    // 0x7d65d0: StoreField: r0->field_7 = d0
    //     0x7d65d0: stur            d0, [x0, #7]
    // 0x7d65d4: ldur            d1, [fp, #-0x20]
    // 0x7d65d8: StoreField: r0->field_f = d1
    //     0x7d65d8: stur            d1, [x0, #0xf]
    // 0x7d65dc: fsub            d2, d1, d0
    // 0x7d65e0: ArrayStore: r0[0] = d2  ; List_8
    //     0x7d65e0: stur            d2, [x0, #0x17]
    // 0x7d65e4: mov             x2, x0
    // 0x7d65e8: ldur            x1, [fp, #-8]
    // 0x7d65ec: ldur            x0, [fp, #-0x10]
    // 0x7d65f0: stur            x2, [fp, #-0x18]
    // 0x7d65f4: r0 = AxisPosition()
    //     0x7d65f4: bl              #0x7d63c0  ; AllocateAxisPositionStub -> AxisPosition (size=0x14)
    // 0x7d65f8: mov             x1, x0
    // 0x7d65fc: ldur            x0, [fp, #-8]
    // 0x7d6600: StoreField: r1->field_7 = r0
    //     0x7d6600: stur            w0, [x1, #7]
    // 0x7d6604: ldur            x0, [fp, #-0x10]
    // 0x7d6608: StoreField: r1->field_b = r0
    //     0x7d6608: stur            w0, [x1, #0xb]
    // 0x7d660c: ldur            x0, [fp, #-0x18]
    // 0x7d6610: StoreField: r1->field_f = r0
    //     0x7d6610: stur            w0, [x1, #0xf]
    // 0x7d6614: mov             x0, x1
    // 0x7d6618: LeaveFrame
    //     0x7d6618: mov             SP, fp
    //     0x7d661c: ldp             fp, lr, [SP], #0x10
    // 0x7d6620: ret
    //     0x7d6620: ret             
    // 0x7d6624: r0 = AssertionError()
    //     0x7d6624: bl              #0x6bcfb4  ; AllocateAssertionErrorStub -> AssertionError (size=0x10)
    // 0x7d6628: mov             x1, x0
    // 0x7d662c: r0 = "Either alignTop or alignBottom must be true"
    //     0x7d662c: add             x0, PP, #0x57, lsl #12  ; [pp+0x57ce0] "Either alignTop or alignBottom must be true"
    //     0x7d6630: ldr             x0, [x0, #0xce0]
    // 0x7d6634: StoreField: r1->field_b = r0
    //     0x7d6634: stur            w0, [x1, #0xb]
    // 0x7d6638: mov             x0, x1
    // 0x7d663c: r0 = Throw()
    //     0x7d663c: bl              #0xec04b8  ; ThrowStub
    // 0x7d6640: brk             #0
  }
}

// class id: 434, size: 0x20, field offset: 0x8
//   const constructor, 
class AxisCoordinates extends Object {

  _Mint field_8;
  _Mint field_10;
  _Mint field_18;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3aa0, size: 0x104
    // 0xbf3aa0: ldr             x1, [SP]
    // 0xbf3aa4: LoadField: d0 = r1->field_7
    //     0xbf3aa4: ldur            d0, [x1, #7]
    // 0xbf3aa8: mov             x16, v0.d[0]
    // 0xbf3aac: and             x16, x16, #0x7ff0000000000000
    // 0xbf3ab0: r17 = 9218868437227405312
    //     0xbf3ab0: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf3ab4: cmp             x16, x17
    // 0xbf3ab8: b.eq            #0xbf3ae8
    // 0xbf3abc: fcvtzs          x16, d0
    // 0xbf3ac0: scvtf           d1, x16
    // 0xbf3ac4: fcmp            d1, d0
    // 0xbf3ac8: b.ne            #0xbf3ae8
    // 0xbf3acc: r17 = 11601
    //     0xbf3acc: movz            x17, #0x2d51
    // 0xbf3ad0: mul             x2, x16, x17
    // 0xbf3ad4: umulh           x16, x16, x17
    // 0xbf3ad8: eor             x2, x2, x16
    // 0xbf3adc: r2 = 0
    //     0xbf3adc: eor             x2, x2, x2, lsr #32
    // 0xbf3ae0: and             x2, x2, #0x3fffffff
    // 0xbf3ae4: b               #0xbf3af4
    // 0xbf3ae8: r2 = 0.000000
    //     0xbf3ae8: fmov            x2, d0
    // 0xbf3aec: r2 = 0
    //     0xbf3aec: eor             x2, x2, x2, lsr #32
    // 0xbf3af0: and             x2, x2, #0x3fffffff
    // 0xbf3af4: LoadField: d0 = r1->field_f
    //     0xbf3af4: ldur            d0, [x1, #0xf]
    // 0xbf3af8: mov             x16, v0.d[0]
    // 0xbf3afc: and             x16, x16, #0x7ff0000000000000
    // 0xbf3b00: r17 = 9218868437227405312
    //     0xbf3b00: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf3b04: cmp             x16, x17
    // 0xbf3b08: b.eq            #0xbf3b38
    // 0xbf3b0c: fcvtzs          x16, d0
    // 0xbf3b10: scvtf           d1, x16
    // 0xbf3b14: fcmp            d1, d0
    // 0xbf3b18: b.ne            #0xbf3b38
    // 0xbf3b1c: r17 = 11601
    //     0xbf3b1c: movz            x17, #0x2d51
    // 0xbf3b20: mul             x3, x16, x17
    // 0xbf3b24: umulh           x16, x16, x17
    // 0xbf3b28: eor             x3, x3, x16
    // 0xbf3b2c: r3 = 0
    //     0xbf3b2c: eor             x3, x3, x3, lsr #32
    // 0xbf3b30: and             x3, x3, #0x3fffffff
    // 0xbf3b34: b               #0xbf3b44
    // 0xbf3b38: r3 = 0.000000
    //     0xbf3b38: fmov            x3, d0
    // 0xbf3b3c: r3 = 0
    //     0xbf3b3c: eor             x3, x3, x3, lsr #32
    // 0xbf3b40: and             x3, x3, #0x3fffffff
    // 0xbf3b44: eor             x4, x2, x3
    // 0xbf3b48: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xbf3b48: ldur            d0, [x1, #0x17]
    // 0xbf3b4c: mov             x16, v0.d[0]
    // 0xbf3b50: and             x16, x16, #0x7ff0000000000000
    // 0xbf3b54: r17 = 9218868437227405312
    //     0xbf3b54: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf3b58: cmp             x16, x17
    // 0xbf3b5c: b.eq            #0xbf3b8c
    // 0xbf3b60: fcvtzs          x16, d0
    // 0xbf3b64: scvtf           d1, x16
    // 0xbf3b68: fcmp            d1, d0
    // 0xbf3b6c: b.ne            #0xbf3b8c
    // 0xbf3b70: r17 = 11601
    //     0xbf3b70: movz            x17, #0x2d51
    // 0xbf3b74: mul             x1, x16, x17
    // 0xbf3b78: umulh           x16, x16, x17
    // 0xbf3b7c: eor             x1, x1, x16
    // 0xbf3b80: r1 = 0
    //     0xbf3b80: eor             x1, x1, x1, lsr #32
    // 0xbf3b84: and             x1, x1, #0x3fffffff
    // 0xbf3b88: b               #0xbf3b98
    // 0xbf3b8c: r1 = 0.000000
    //     0xbf3b8c: fmov            x1, d0
    // 0xbf3b90: r1 = 0
    //     0xbf3b90: eor             x1, x1, x1, lsr #32
    // 0xbf3b94: and             x1, x1, #0x3fffffff
    // 0xbf3b98: eor             x2, x4, x1
    // 0xbf3b9c: lsl             x0, x2, #1
    // 0xbf3ba0: ret
    //     0xbf3ba0: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc41824, size: 0x158
    // 0xc41824: EnterFrame
    //     0xc41824: stp             fp, lr, [SP, #-0x10]!
    //     0xc41828: mov             fp, SP
    // 0xc4182c: AllocStack(0x8)
    //     0xc4182c: sub             SP, SP, #8
    // 0xc41830: CheckStackOverflow
    //     0xc41830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41834: cmp             SP, x16
    //     0xc41838: b.ls            #0xc41920
    // 0xc4183c: r1 = Null
    //     0xc4183c: mov             x1, NULL
    // 0xc41840: r2 = 14
    //     0xc41840: movz            x2, #0xe
    // 0xc41844: r0 = AllocateArray()
    //     0xc41844: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41848: r16 = "AxisCoordinates{start: "
    //     0xc41848: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae00] "AxisCoordinates{start: "
    //     0xc4184c: ldr             x16, [x16, #0xe00]
    // 0xc41850: StoreField: r0->field_f = r16
    //     0xc41850: stur            w16, [x0, #0xf]
    // 0xc41854: ldr             x1, [fp, #0x10]
    // 0xc41858: LoadField: d0 = r1->field_7
    //     0xc41858: ldur            d0, [x1, #7]
    // 0xc4185c: r2 = inline_Allocate_Double()
    //     0xc4185c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xc41860: add             x2, x2, #0x10
    //     0xc41864: cmp             x3, x2
    //     0xc41868: b.ls            #0xc41928
    //     0xc4186c: str             x2, [THR, #0x50]  ; THR::top
    //     0xc41870: sub             x2, x2, #0xf
    //     0xc41874: movz            x3, #0xe15c
    //     0xc41878: movk            x3, #0x3, lsl #16
    //     0xc4187c: stur            x3, [x2, #-1]
    // 0xc41880: StoreField: r2->field_7 = d0
    //     0xc41880: stur            d0, [x2, #7]
    // 0xc41884: StoreField: r0->field_13 = r2
    //     0xc41884: stur            w2, [x0, #0x13]
    // 0xc41888: r16 = ", end: "
    //     0xc41888: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae08] ", end: "
    //     0xc4188c: ldr             x16, [x16, #0xe08]
    // 0xc41890: ArrayStore: r0[0] = r16  ; List_4
    //     0xc41890: stur            w16, [x0, #0x17]
    // 0xc41894: LoadField: d0 = r1->field_f
    //     0xc41894: ldur            d0, [x1, #0xf]
    // 0xc41898: r2 = inline_Allocate_Double()
    //     0xc41898: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xc4189c: add             x2, x2, #0x10
    //     0xc418a0: cmp             x3, x2
    //     0xc418a4: b.ls            #0xc41944
    //     0xc418a8: str             x2, [THR, #0x50]  ; THR::top
    //     0xc418ac: sub             x2, x2, #0xf
    //     0xc418b0: movz            x3, #0xe15c
    //     0xc418b4: movk            x3, #0x3, lsl #16
    //     0xc418b8: stur            x3, [x2, #-1]
    // 0xc418bc: StoreField: r2->field_7 = d0
    //     0xc418bc: stur            d0, [x2, #7]
    // 0xc418c0: StoreField: r0->field_1b = r2
    //     0xc418c0: stur            w2, [x0, #0x1b]
    // 0xc418c4: r16 = ", size: "
    //     0xc418c4: add             x16, PP, #0x50, lsl #12  ; [pp+0x50c70] ", size: "
    //     0xc418c8: ldr             x16, [x16, #0xc70]
    // 0xc418cc: StoreField: r0->field_1f = r16
    //     0xc418cc: stur            w16, [x0, #0x1f]
    // 0xc418d0: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xc418d0: ldur            d0, [x1, #0x17]
    // 0xc418d4: r1 = inline_Allocate_Double()
    //     0xc418d4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xc418d8: add             x1, x1, #0x10
    //     0xc418dc: cmp             x2, x1
    //     0xc418e0: b.ls            #0xc41960
    //     0xc418e4: str             x1, [THR, #0x50]  ; THR::top
    //     0xc418e8: sub             x1, x1, #0xf
    //     0xc418ec: movz            x2, #0xe15c
    //     0xc418f0: movk            x2, #0x3, lsl #16
    //     0xc418f4: stur            x2, [x1, #-1]
    // 0xc418f8: StoreField: r1->field_7 = d0
    //     0xc418f8: stur            d0, [x1, #7]
    // 0xc418fc: StoreField: r0->field_23 = r1
    //     0xc418fc: stur            w1, [x0, #0x23]
    // 0xc41900: r16 = "}"
    //     0xc41900: add             x16, PP, #0x12, lsl #12  ; [pp+0x12240] "}"
    //     0xc41904: ldr             x16, [x16, #0x240]
    // 0xc41908: StoreField: r0->field_27 = r16
    //     0xc41908: stur            w16, [x0, #0x27]
    // 0xc4190c: str             x0, [SP]
    // 0xc41910: r0 = _interpolate()
    //     0xc41910: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41914: LeaveFrame
    //     0xc41914: mov             SP, fp
    //     0xc41918: ldp             fp, lr, [SP], #0x10
    // 0xc4191c: ret
    //     0xc4191c: ret             
    // 0xc41920: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41920: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41924: b               #0xc4183c
    // 0xc41928: SaveReg d0
    //     0xc41928: str             q0, [SP, #-0x10]!
    // 0xc4192c: stp             x0, x1, [SP, #-0x10]!
    // 0xc41930: r0 = AllocateDouble()
    //     0xc41930: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc41934: mov             x2, x0
    // 0xc41938: ldp             x0, x1, [SP], #0x10
    // 0xc4193c: RestoreReg d0
    //     0xc4193c: ldr             q0, [SP], #0x10
    // 0xc41940: b               #0xc41880
    // 0xc41944: SaveReg d0
    //     0xc41944: str             q0, [SP, #-0x10]!
    // 0xc41948: stp             x0, x1, [SP, #-0x10]!
    // 0xc4194c: r0 = AllocateDouble()
    //     0xc4194c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc41950: mov             x2, x0
    // 0xc41954: ldp             x0, x1, [SP], #0x10
    // 0xc41958: RestoreReg d0
    //     0xc41958: ldr             q0, [SP], #0x10
    // 0xc4195c: b               #0xc418bc
    // 0xc41960: SaveReg d0
    //     0xc41960: str             q0, [SP, #-0x10]!
    // 0xc41964: SaveReg r0
    //     0xc41964: str             x0, [SP, #-8]!
    // 0xc41968: r0 = AllocateDouble()
    //     0xc41968: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc4196c: mov             x1, x0
    // 0xc41970: RestoreReg r0
    //     0xc41970: ldr             x0, [SP], #8
    // 0xc41974: RestoreReg d0
    //     0xc41974: ldr             q0, [SP], #0x10
    // 0xc41978: b               #0xc418f8
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7e074, size: 0xdc
    // 0xd7e074: EnterFrame
    //     0xd7e074: stp             fp, lr, [SP, #-0x10]!
    //     0xd7e078: mov             fp, SP
    // 0xd7e07c: AllocStack(0x10)
    //     0xd7e07c: sub             SP, SP, #0x10
    // 0xd7e080: CheckStackOverflow
    //     0xd7e080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7e084: cmp             SP, x16
    //     0xd7e088: b.ls            #0xd7e148
    // 0xd7e08c: ldr             x0, [fp, #0x10]
    // 0xd7e090: cmp             w0, NULL
    // 0xd7e094: b.ne            #0xd7e0a8
    // 0xd7e098: r0 = false
    //     0xd7e098: add             x0, NULL, #0x30  ; false
    // 0xd7e09c: LeaveFrame
    //     0xd7e09c: mov             SP, fp
    //     0xd7e0a0: ldp             fp, lr, [SP], #0x10
    // 0xd7e0a4: ret
    //     0xd7e0a4: ret             
    // 0xd7e0a8: ldr             x1, [fp, #0x18]
    // 0xd7e0ac: cmp             w1, w0
    // 0xd7e0b0: b.ne            #0xd7e0bc
    // 0xd7e0b4: r0 = true
    //     0xd7e0b4: add             x0, NULL, #0x20  ; true
    // 0xd7e0b8: b               #0xd7e13c
    // 0xd7e0bc: r2 = 60
    //     0xd7e0bc: movz            x2, #0x3c
    // 0xd7e0c0: branchIfSmi(r0, 0xd7e0cc)
    //     0xd7e0c0: tbz             w0, #0, #0xd7e0cc
    // 0xd7e0c4: r2 = LoadClassIdInstr(r0)
    //     0xd7e0c4: ldur            x2, [x0, #-1]
    //     0xd7e0c8: ubfx            x2, x2, #0xc, #0x14
    // 0xd7e0cc: cmp             x2, #0x1b2
    // 0xd7e0d0: b.ne            #0xd7e138
    // 0xd7e0d4: r16 = AxisCoordinates
    //     0xd7e0d4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7e0d8: ldr             x16, [x16, #0xe10]
    // 0xd7e0dc: r30 = AxisCoordinates
    //     0xd7e0dc: add             lr, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7e0e0: ldr             lr, [lr, #0xe10]
    // 0xd7e0e4: stp             lr, x16, [SP]
    // 0xd7e0e8: r0 = ==()
    //     0xd7e0e8: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7e0ec: tbnz            w0, #4, #0xd7e138
    // 0xd7e0f0: ldr             x2, [fp, #0x18]
    // 0xd7e0f4: ldr             x1, [fp, #0x10]
    // 0xd7e0f8: LoadField: d0 = r2->field_7
    //     0xd7e0f8: ldur            d0, [x2, #7]
    // 0xd7e0fc: LoadField: d1 = r1->field_7
    //     0xd7e0fc: ldur            d1, [x1, #7]
    // 0xd7e100: fcmp            d0, d1
    // 0xd7e104: b.ne            #0xd7e138
    // 0xd7e108: LoadField: d0 = r2->field_f
    //     0xd7e108: ldur            d0, [x2, #0xf]
    // 0xd7e10c: LoadField: d1 = r1->field_f
    //     0xd7e10c: ldur            d1, [x1, #0xf]
    // 0xd7e110: fcmp            d0, d1
    // 0xd7e114: b.ne            #0xd7e138
    // 0xd7e118: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xd7e118: ldur            d0, [x2, #0x17]
    // 0xd7e11c: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xd7e11c: ldur            d1, [x1, #0x17]
    // 0xd7e120: fcmp            d0, d1
    // 0xd7e124: r16 = true
    //     0xd7e124: add             x16, NULL, #0x20  ; true
    // 0xd7e128: r17 = false
    //     0xd7e128: add             x17, NULL, #0x30  ; false
    // 0xd7e12c: csel            x1, x16, x17, eq
    // 0xd7e130: mov             x0, x1
    // 0xd7e134: b               #0xd7e13c
    // 0xd7e138: r0 = false
    //     0xd7e138: add             x0, NULL, #0x30  ; false
    // 0xd7e13c: LeaveFrame
    //     0xd7e13c: mov             SP, fp
    //     0xd7e140: ldp             fp, lr, [SP], #0x10
    // 0xd7e144: ret
    //     0xd7e144: ret             
    // 0xd7e148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7e148: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7e14c: b               #0xd7e08c
  }
}

// class id: 435, size: 0x14, field offset: 0x8
//   const constructor, 
class AxisPosition extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf378c, size: 0x314
    // 0xbf378c: ldr             x1, [SP]
    // 0xbf3790: LoadField: r2 = r1->field_7
    //     0xbf3790: ldur            w2, [x1, #7]
    // 0xbf3794: DecompressPointer r2
    //     0xbf3794: add             x2, x2, HEAP, lsl #32
    // 0xbf3798: LoadField: d0 = r2->field_7
    //     0xbf3798: ldur            d0, [x2, #7]
    // 0xbf379c: mov             x16, v0.d[0]
    // 0xbf37a0: and             x16, x16, #0x7ff0000000000000
    // 0xbf37a4: r17 = 9218868437227405312
    //     0xbf37a4: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf37a8: cmp             x16, x17
    // 0xbf37ac: b.eq            #0xbf37dc
    // 0xbf37b0: fcvtzs          x16, d0
    // 0xbf37b4: scvtf           d1, x16
    // 0xbf37b8: fcmp            d1, d0
    // 0xbf37bc: b.ne            #0xbf37dc
    // 0xbf37c0: r17 = 11601
    //     0xbf37c0: movz            x17, #0x2d51
    // 0xbf37c4: mul             x3, x16, x17
    // 0xbf37c8: umulh           x16, x16, x17
    // 0xbf37cc: eor             x3, x3, x16
    // 0xbf37d0: r3 = 0
    //     0xbf37d0: eor             x3, x3, x3, lsr #32
    // 0xbf37d4: and             x3, x3, #0x3fffffff
    // 0xbf37d8: b               #0xbf37e8
    // 0xbf37dc: r3 = 0.000000
    //     0xbf37dc: fmov            x3, d0
    // 0xbf37e0: r3 = 0
    //     0xbf37e0: eor             x3, x3, x3, lsr #32
    // 0xbf37e4: and             x3, x3, #0x3fffffff
    // 0xbf37e8: LoadField: d0 = r2->field_f
    //     0xbf37e8: ldur            d0, [x2, #0xf]
    // 0xbf37ec: mov             x16, v0.d[0]
    // 0xbf37f0: and             x16, x16, #0x7ff0000000000000
    // 0xbf37f4: r17 = 9218868437227405312
    //     0xbf37f4: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf37f8: cmp             x16, x17
    // 0xbf37fc: b.eq            #0xbf382c
    // 0xbf3800: fcvtzs          x16, d0
    // 0xbf3804: scvtf           d1, x16
    // 0xbf3808: fcmp            d1, d0
    // 0xbf380c: b.ne            #0xbf382c
    // 0xbf3810: r17 = 11601
    //     0xbf3810: movz            x17, #0x2d51
    // 0xbf3814: mul             x4, x16, x17
    // 0xbf3818: umulh           x16, x16, x17
    // 0xbf381c: eor             x4, x4, x16
    // 0xbf3820: r4 = 0
    //     0xbf3820: eor             x4, x4, x4, lsr #32
    // 0xbf3824: and             x4, x4, #0x3fffffff
    // 0xbf3828: b               #0xbf3838
    // 0xbf382c: r4 = 0.000000
    //     0xbf382c: fmov            x4, d0
    // 0xbf3830: r4 = 0
    //     0xbf3830: eor             x4, x4, x4, lsr #32
    // 0xbf3834: and             x4, x4, #0x3fffffff
    // 0xbf3838: eor             x5, x3, x4
    // 0xbf383c: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xbf383c: ldur            d0, [x2, #0x17]
    // 0xbf3840: mov             x16, v0.d[0]
    // 0xbf3844: and             x16, x16, #0x7ff0000000000000
    // 0xbf3848: r17 = 9218868437227405312
    //     0xbf3848: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf384c: cmp             x16, x17
    // 0xbf3850: b.eq            #0xbf3880
    // 0xbf3854: fcvtzs          x16, d0
    // 0xbf3858: scvtf           d1, x16
    // 0xbf385c: fcmp            d1, d0
    // 0xbf3860: b.ne            #0xbf3880
    // 0xbf3864: r17 = 11601
    //     0xbf3864: movz            x17, #0x2d51
    // 0xbf3868: mul             x2, x16, x17
    // 0xbf386c: umulh           x16, x16, x17
    // 0xbf3870: eor             x2, x2, x16
    // 0xbf3874: r2 = 0
    //     0xbf3874: eor             x2, x2, x2, lsr #32
    // 0xbf3878: and             x2, x2, #0x3fffffff
    // 0xbf387c: b               #0xbf388c
    // 0xbf3880: r2 = 0.000000
    //     0xbf3880: fmov            x2, d0
    // 0xbf3884: r2 = 0
    //     0xbf3884: eor             x2, x2, x2, lsr #32
    // 0xbf3888: and             x2, x2, #0x3fffffff
    // 0xbf388c: eor             x3, x5, x2
    // 0xbf3890: LoadField: r2 = r1->field_b
    //     0xbf3890: ldur            w2, [x1, #0xb]
    // 0xbf3894: DecompressPointer r2
    //     0xbf3894: add             x2, x2, HEAP, lsl #32
    // 0xbf3898: LoadField: d0 = r2->field_7
    //     0xbf3898: ldur            d0, [x2, #7]
    // 0xbf389c: mov             x16, v0.d[0]
    // 0xbf38a0: and             x16, x16, #0x7ff0000000000000
    // 0xbf38a4: r17 = 9218868437227405312
    //     0xbf38a4: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf38a8: cmp             x16, x17
    // 0xbf38ac: b.eq            #0xbf38dc
    // 0xbf38b0: fcvtzs          x16, d0
    // 0xbf38b4: scvtf           d1, x16
    // 0xbf38b8: fcmp            d1, d0
    // 0xbf38bc: b.ne            #0xbf38dc
    // 0xbf38c0: r17 = 11601
    //     0xbf38c0: movz            x17, #0x2d51
    // 0xbf38c4: mul             x4, x16, x17
    // 0xbf38c8: umulh           x16, x16, x17
    // 0xbf38cc: eor             x4, x4, x16
    // 0xbf38d0: r4 = 0
    //     0xbf38d0: eor             x4, x4, x4, lsr #32
    // 0xbf38d4: and             x4, x4, #0x3fffffff
    // 0xbf38d8: b               #0xbf38e8
    // 0xbf38dc: r4 = 0.000000
    //     0xbf38dc: fmov            x4, d0
    // 0xbf38e0: r4 = 0
    //     0xbf38e0: eor             x4, x4, x4, lsr #32
    // 0xbf38e4: and             x4, x4, #0x3fffffff
    // 0xbf38e8: LoadField: d0 = r2->field_f
    //     0xbf38e8: ldur            d0, [x2, #0xf]
    // 0xbf38ec: mov             x16, v0.d[0]
    // 0xbf38f0: and             x16, x16, #0x7ff0000000000000
    // 0xbf38f4: r17 = 9218868437227405312
    //     0xbf38f4: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf38f8: cmp             x16, x17
    // 0xbf38fc: b.eq            #0xbf392c
    // 0xbf3900: fcvtzs          x16, d0
    // 0xbf3904: scvtf           d1, x16
    // 0xbf3908: fcmp            d1, d0
    // 0xbf390c: b.ne            #0xbf392c
    // 0xbf3910: r17 = 11601
    //     0xbf3910: movz            x17, #0x2d51
    // 0xbf3914: mul             x5, x16, x17
    // 0xbf3918: umulh           x16, x16, x17
    // 0xbf391c: eor             x5, x5, x16
    // 0xbf3920: r5 = 0
    //     0xbf3920: eor             x5, x5, x5, lsr #32
    // 0xbf3924: and             x5, x5, #0x3fffffff
    // 0xbf3928: b               #0xbf3938
    // 0xbf392c: r5 = 0.000000
    //     0xbf392c: fmov            x5, d0
    // 0xbf3930: r5 = 0
    //     0xbf3930: eor             x5, x5, x5, lsr #32
    // 0xbf3934: and             x5, x5, #0x3fffffff
    // 0xbf3938: eor             x6, x4, x5
    // 0xbf393c: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xbf393c: ldur            d0, [x2, #0x17]
    // 0xbf3940: mov             x16, v0.d[0]
    // 0xbf3944: and             x16, x16, #0x7ff0000000000000
    // 0xbf3948: r17 = 9218868437227405312
    //     0xbf3948: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf394c: cmp             x16, x17
    // 0xbf3950: b.eq            #0xbf3980
    // 0xbf3954: fcvtzs          x16, d0
    // 0xbf3958: scvtf           d1, x16
    // 0xbf395c: fcmp            d1, d0
    // 0xbf3960: b.ne            #0xbf3980
    // 0xbf3964: r17 = 11601
    //     0xbf3964: movz            x17, #0x2d51
    // 0xbf3968: mul             x2, x16, x17
    // 0xbf396c: umulh           x16, x16, x17
    // 0xbf3970: eor             x2, x2, x16
    // 0xbf3974: r2 = 0
    //     0xbf3974: eor             x2, x2, x2, lsr #32
    // 0xbf3978: and             x2, x2, #0x3fffffff
    // 0xbf397c: b               #0xbf398c
    // 0xbf3980: r2 = 0.000000
    //     0xbf3980: fmov            x2, d0
    // 0xbf3984: r2 = 0
    //     0xbf3984: eor             x2, x2, x2, lsr #32
    // 0xbf3988: and             x2, x2, #0x3fffffff
    // 0xbf398c: eor             x4, x6, x2
    // 0xbf3990: eor             x2, x3, x4
    // 0xbf3994: LoadField: r3 = r1->field_f
    //     0xbf3994: ldur            w3, [x1, #0xf]
    // 0xbf3998: DecompressPointer r3
    //     0xbf3998: add             x3, x3, HEAP, lsl #32
    // 0xbf399c: LoadField: d0 = r3->field_7
    //     0xbf399c: ldur            d0, [x3, #7]
    // 0xbf39a0: mov             x16, v0.d[0]
    // 0xbf39a4: and             x16, x16, #0x7ff0000000000000
    // 0xbf39a8: r17 = 9218868437227405312
    //     0xbf39a8: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf39ac: cmp             x16, x17
    // 0xbf39b0: b.eq            #0xbf39e0
    // 0xbf39b4: fcvtzs          x16, d0
    // 0xbf39b8: scvtf           d1, x16
    // 0xbf39bc: fcmp            d1, d0
    // 0xbf39c0: b.ne            #0xbf39e0
    // 0xbf39c4: r17 = 11601
    //     0xbf39c4: movz            x17, #0x2d51
    // 0xbf39c8: mul             x1, x16, x17
    // 0xbf39cc: umulh           x16, x16, x17
    // 0xbf39d0: eor             x1, x1, x16
    // 0xbf39d4: r1 = 0
    //     0xbf39d4: eor             x1, x1, x1, lsr #32
    // 0xbf39d8: and             x1, x1, #0x3fffffff
    // 0xbf39dc: b               #0xbf39ec
    // 0xbf39e0: r1 = 0.000000
    //     0xbf39e0: fmov            x1, d0
    // 0xbf39e4: r1 = 0
    //     0xbf39e4: eor             x1, x1, x1, lsr #32
    // 0xbf39e8: and             x1, x1, #0x3fffffff
    // 0xbf39ec: LoadField: d0 = r3->field_f
    //     0xbf39ec: ldur            d0, [x3, #0xf]
    // 0xbf39f0: mov             x16, v0.d[0]
    // 0xbf39f4: and             x16, x16, #0x7ff0000000000000
    // 0xbf39f8: r17 = 9218868437227405312
    //     0xbf39f8: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf39fc: cmp             x16, x17
    // 0xbf3a00: b.eq            #0xbf3a30
    // 0xbf3a04: fcvtzs          x16, d0
    // 0xbf3a08: scvtf           d1, x16
    // 0xbf3a0c: fcmp            d1, d0
    // 0xbf3a10: b.ne            #0xbf3a30
    // 0xbf3a14: r17 = 11601
    //     0xbf3a14: movz            x17, #0x2d51
    // 0xbf3a18: mul             x4, x16, x17
    // 0xbf3a1c: umulh           x16, x16, x17
    // 0xbf3a20: eor             x4, x4, x16
    // 0xbf3a24: r4 = 0
    //     0xbf3a24: eor             x4, x4, x4, lsr #32
    // 0xbf3a28: and             x4, x4, #0x3fffffff
    // 0xbf3a2c: b               #0xbf3a3c
    // 0xbf3a30: r4 = 0.000000
    //     0xbf3a30: fmov            x4, d0
    // 0xbf3a34: r4 = 0
    //     0xbf3a34: eor             x4, x4, x4, lsr #32
    // 0xbf3a38: and             x4, x4, #0x3fffffff
    // 0xbf3a3c: eor             x5, x1, x4
    // 0xbf3a40: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xbf3a40: ldur            d0, [x3, #0x17]
    // 0xbf3a44: mov             x16, v0.d[0]
    // 0xbf3a48: and             x16, x16, #0x7ff0000000000000
    // 0xbf3a4c: r17 = 9218868437227405312
    //     0xbf3a4c: orr             x17, xzr, #0x7ff0000000000000
    // 0xbf3a50: cmp             x16, x17
    // 0xbf3a54: b.eq            #0xbf3a84
    // 0xbf3a58: fcvtzs          x16, d0
    // 0xbf3a5c: scvtf           d1, x16
    // 0xbf3a60: fcmp            d1, d0
    // 0xbf3a64: b.ne            #0xbf3a84
    // 0xbf3a68: r17 = 11601
    //     0xbf3a68: movz            x17, #0x2d51
    // 0xbf3a6c: mul             x1, x16, x17
    // 0xbf3a70: umulh           x16, x16, x17
    // 0xbf3a74: eor             x1, x1, x16
    // 0xbf3a78: r1 = 0
    //     0xbf3a78: eor             x1, x1, x1, lsr #32
    // 0xbf3a7c: and             x1, x1, #0x3fffffff
    // 0xbf3a80: b               #0xbf3a90
    // 0xbf3a84: r1 = 0.000000
    //     0xbf3a84: fmov            x1, d0
    // 0xbf3a88: r1 = 0
    //     0xbf3a88: eor             x1, x1, x1, lsr #32
    // 0xbf3a8c: and             x1, x1, #0x3fffffff
    // 0xbf3a90: eor             x3, x5, x1
    // 0xbf3a94: eor             x1, x2, x3
    // 0xbf3a98: lsl             x0, x1, #1
    // 0xbf3a9c: ret
    //     0xbf3a9c: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4178c, size: 0x98
    // 0xc4178c: EnterFrame
    //     0xc4178c: stp             fp, lr, [SP, #-0x10]!
    //     0xc41790: mov             fp, SP
    // 0xc41794: AllocStack(0x8)
    //     0xc41794: sub             SP, SP, #8
    // 0xc41798: CheckStackOverflow
    //     0xc41798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4179c: cmp             SP, x16
    //     0xc417a0: b.ls            #0xc4181c
    // 0xc417a4: r1 = Null
    //     0xc417a4: mov             x1, NULL
    // 0xc417a8: r2 = 14
    //     0xc417a8: movz            x2, #0xe
    // 0xc417ac: r0 = AllocateArray()
    //     0xc417ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc417b0: r16 = "AxisPosition{firstSpace: "
    //     0xc417b0: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae18] "AxisPosition{firstSpace: "
    //     0xc417b4: ldr             x16, [x16, #0xe18]
    // 0xc417b8: StoreField: r0->field_f = r16
    //     0xc417b8: stur            w16, [x0, #0xf]
    // 0xc417bc: ldr             x1, [fp, #0x10]
    // 0xc417c0: LoadField: r2 = r1->field_7
    //     0xc417c0: ldur            w2, [x1, #7]
    // 0xc417c4: DecompressPointer r2
    //     0xc417c4: add             x2, x2, HEAP, lsl #32
    // 0xc417c8: StoreField: r0->field_13 = r2
    //     0xc417c8: stur            w2, [x0, #0x13]
    // 0xc417cc: r16 = ", objectSpace: "
    //     0xc417cc: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae20] ", objectSpace: "
    //     0xc417d0: ldr             x16, [x16, #0xe20]
    // 0xc417d4: ArrayStore: r0[0] = r16  ; List_4
    //     0xc417d4: stur            w16, [x0, #0x17]
    // 0xc417d8: LoadField: r2 = r1->field_b
    //     0xc417d8: ldur            w2, [x1, #0xb]
    // 0xc417dc: DecompressPointer r2
    //     0xc417dc: add             x2, x2, HEAP, lsl #32
    // 0xc417e0: StoreField: r0->field_1b = r2
    //     0xc417e0: stur            w2, [x0, #0x1b]
    // 0xc417e4: r16 = ", secondSpace: "
    //     0xc417e4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae28] ", secondSpace: "
    //     0xc417e8: ldr             x16, [x16, #0xe28]
    // 0xc417ec: StoreField: r0->field_1f = r16
    //     0xc417ec: stur            w16, [x0, #0x1f]
    // 0xc417f0: LoadField: r2 = r1->field_f
    //     0xc417f0: ldur            w2, [x1, #0xf]
    // 0xc417f4: DecompressPointer r2
    //     0xc417f4: add             x2, x2, HEAP, lsl #32
    // 0xc417f8: StoreField: r0->field_23 = r2
    //     0xc417f8: stur            w2, [x0, #0x23]
    // 0xc417fc: r16 = "}"
    //     0xc417fc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12240] "}"
    //     0xc41800: ldr             x16, [x16, #0x240]
    // 0xc41804: StoreField: r0->field_27 = r16
    //     0xc41804: stur            w16, [x0, #0x27]
    // 0xc41808: str             x0, [SP]
    // 0xc4180c: r0 = _interpolate()
    //     0xc4180c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41810: LeaveFrame
    //     0xc41810: mov             SP, fp
    //     0xc41814: ldp             fp, lr, [SP], #0x10
    // 0xc41818: ret
    //     0xc41818: ret             
    // 0xc4181c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4181c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41820: b               #0xc417a4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7de4c, size: 0x228
    // 0xd7de4c: EnterFrame
    //     0xd7de4c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7de50: mov             fp, SP
    // 0xd7de54: AllocStack(0x20)
    //     0xd7de54: sub             SP, SP, #0x20
    // 0xd7de58: CheckStackOverflow
    //     0xd7de58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7de5c: cmp             SP, x16
    //     0xd7de60: b.ls            #0xd7e06c
    // 0xd7de64: ldr             x0, [fp, #0x10]
    // 0xd7de68: cmp             w0, NULL
    // 0xd7de6c: b.ne            #0xd7de80
    // 0xd7de70: r0 = false
    //     0xd7de70: add             x0, NULL, #0x30  ; false
    // 0xd7de74: LeaveFrame
    //     0xd7de74: mov             SP, fp
    //     0xd7de78: ldp             fp, lr, [SP], #0x10
    // 0xd7de7c: ret
    //     0xd7de7c: ret             
    // 0xd7de80: ldr             x1, [fp, #0x18]
    // 0xd7de84: cmp             w1, w0
    // 0xd7de88: b.ne            #0xd7de94
    // 0xd7de8c: r0 = true
    //     0xd7de8c: add             x0, NULL, #0x20  ; true
    // 0xd7de90: b               #0xd7e060
    // 0xd7de94: r2 = 60
    //     0xd7de94: movz            x2, #0x3c
    // 0xd7de98: branchIfSmi(r0, 0xd7dea4)
    //     0xd7de98: tbz             w0, #0, #0xd7dea4
    // 0xd7de9c: r2 = LoadClassIdInstr(r0)
    //     0xd7de9c: ldur            x2, [x0, #-1]
    //     0xd7dea0: ubfx            x2, x2, #0xc, #0x14
    // 0xd7dea4: cmp             x2, #0x1b3
    // 0xd7dea8: b.ne            #0xd7e05c
    // 0xd7deac: r16 = AxisPosition
    //     0xd7deac: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae30] Type: AxisPosition
    //     0xd7deb0: ldr             x16, [x16, #0xe30]
    // 0xd7deb4: r30 = AxisPosition
    //     0xd7deb4: add             lr, PP, #0x5a, lsl #12  ; [pp+0x5ae30] Type: AxisPosition
    //     0xd7deb8: ldr             lr, [lr, #0xe30]
    // 0xd7debc: stp             lr, x16, [SP]
    // 0xd7dec0: r0 = ==()
    //     0xd7dec0: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7dec4: tbnz            w0, #4, #0xd7e05c
    // 0xd7dec8: ldr             x1, [fp, #0x18]
    // 0xd7decc: ldr             x0, [fp, #0x10]
    // 0xd7ded0: LoadField: r2 = r1->field_7
    //     0xd7ded0: ldur            w2, [x1, #7]
    // 0xd7ded4: DecompressPointer r2
    //     0xd7ded4: add             x2, x2, HEAP, lsl #32
    // 0xd7ded8: stur            x2, [fp, #-0x10]
    // 0xd7dedc: LoadField: r3 = r0->field_7
    //     0xd7dedc: ldur            w3, [x0, #7]
    // 0xd7dee0: DecompressPointer r3
    //     0xd7dee0: add             x3, x3, HEAP, lsl #32
    // 0xd7dee4: stur            x3, [fp, #-8]
    // 0xd7dee8: cmp             w2, w3
    // 0xd7deec: b.eq            #0xd7df4c
    // 0xd7def0: r16 = AxisCoordinates
    //     0xd7def0: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7def4: ldr             x16, [x16, #0xe10]
    // 0xd7def8: r30 = AxisCoordinates
    //     0xd7def8: add             lr, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7defc: ldr             lr, [lr, #0xe10]
    // 0xd7df00: stp             lr, x16, [SP]
    // 0xd7df04: r0 = ==()
    //     0xd7df04: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7df08: tbnz            w0, #4, #0xd7e05c
    // 0xd7df0c: ldur            x0, [fp, #-0x10]
    // 0xd7df10: ldur            x1, [fp, #-8]
    // 0xd7df14: LoadField: d0 = r0->field_7
    //     0xd7df14: ldur            d0, [x0, #7]
    // 0xd7df18: LoadField: d1 = r1->field_7
    //     0xd7df18: ldur            d1, [x1, #7]
    // 0xd7df1c: fcmp            d0, d1
    // 0xd7df20: b.ne            #0xd7e05c
    // 0xd7df24: LoadField: d0 = r0->field_f
    //     0xd7df24: ldur            d0, [x0, #0xf]
    // 0xd7df28: LoadField: d1 = r1->field_f
    //     0xd7df28: ldur            d1, [x1, #0xf]
    // 0xd7df2c: fcmp            d0, d1
    // 0xd7df30: b.ne            #0xd7e05c
    // 0xd7df34: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xd7df34: ldur            d0, [x0, #0x17]
    // 0xd7df38: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xd7df38: ldur            d1, [x1, #0x17]
    // 0xd7df3c: fcmp            d0, d1
    // 0xd7df40: b.ne            #0xd7e05c
    // 0xd7df44: ldr             x1, [fp, #0x18]
    // 0xd7df48: ldr             x0, [fp, #0x10]
    // 0xd7df4c: LoadField: r2 = r1->field_b
    //     0xd7df4c: ldur            w2, [x1, #0xb]
    // 0xd7df50: DecompressPointer r2
    //     0xd7df50: add             x2, x2, HEAP, lsl #32
    // 0xd7df54: stur            x2, [fp, #-0x10]
    // 0xd7df58: LoadField: r3 = r0->field_b
    //     0xd7df58: ldur            w3, [x0, #0xb]
    // 0xd7df5c: DecompressPointer r3
    //     0xd7df5c: add             x3, x3, HEAP, lsl #32
    // 0xd7df60: stur            x3, [fp, #-8]
    // 0xd7df64: cmp             w2, w3
    // 0xd7df68: b.eq            #0xd7dfc8
    // 0xd7df6c: r16 = AxisCoordinates
    //     0xd7df6c: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7df70: ldr             x16, [x16, #0xe10]
    // 0xd7df74: r30 = AxisCoordinates
    //     0xd7df74: add             lr, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7df78: ldr             lr, [lr, #0xe10]
    // 0xd7df7c: stp             lr, x16, [SP]
    // 0xd7df80: r0 = ==()
    //     0xd7df80: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7df84: tbnz            w0, #4, #0xd7e05c
    // 0xd7df88: ldur            x0, [fp, #-0x10]
    // 0xd7df8c: ldur            x1, [fp, #-8]
    // 0xd7df90: LoadField: d0 = r0->field_7
    //     0xd7df90: ldur            d0, [x0, #7]
    // 0xd7df94: LoadField: d1 = r1->field_7
    //     0xd7df94: ldur            d1, [x1, #7]
    // 0xd7df98: fcmp            d0, d1
    // 0xd7df9c: b.ne            #0xd7e05c
    // 0xd7dfa0: LoadField: d0 = r0->field_f
    //     0xd7dfa0: ldur            d0, [x0, #0xf]
    // 0xd7dfa4: LoadField: d1 = r1->field_f
    //     0xd7dfa4: ldur            d1, [x1, #0xf]
    // 0xd7dfa8: fcmp            d0, d1
    // 0xd7dfac: b.ne            #0xd7e05c
    // 0xd7dfb0: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xd7dfb0: ldur            d0, [x0, #0x17]
    // 0xd7dfb4: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xd7dfb4: ldur            d1, [x1, #0x17]
    // 0xd7dfb8: fcmp            d0, d1
    // 0xd7dfbc: b.ne            #0xd7e05c
    // 0xd7dfc0: ldr             x1, [fp, #0x18]
    // 0xd7dfc4: ldr             x0, [fp, #0x10]
    // 0xd7dfc8: LoadField: r2 = r1->field_f
    //     0xd7dfc8: ldur            w2, [x1, #0xf]
    // 0xd7dfcc: DecompressPointer r2
    //     0xd7dfcc: add             x2, x2, HEAP, lsl #32
    // 0xd7dfd0: stur            x2, [fp, #-0x10]
    // 0xd7dfd4: LoadField: r1 = r0->field_f
    //     0xd7dfd4: ldur            w1, [x0, #0xf]
    // 0xd7dfd8: DecompressPointer r1
    //     0xd7dfd8: add             x1, x1, HEAP, lsl #32
    // 0xd7dfdc: stur            x1, [fp, #-8]
    // 0xd7dfe0: cmp             w2, w1
    // 0xd7dfe4: b.ne            #0xd7dff0
    // 0xd7dfe8: r1 = true
    //     0xd7dfe8: add             x1, NULL, #0x20  ; true
    // 0xd7dfec: b               #0xd7e054
    // 0xd7dff0: r16 = AxisCoordinates
    //     0xd7dff0: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7dff4: ldr             x16, [x16, #0xe10]
    // 0xd7dff8: r30 = AxisCoordinates
    //     0xd7dff8: add             lr, PP, #0x5a, lsl #12  ; [pp+0x5ae10] Type: AxisCoordinates
    //     0xd7dffc: ldr             lr, [lr, #0xe10]
    // 0xd7e000: stp             lr, x16, [SP]
    // 0xd7e004: r0 = ==()
    //     0xd7e004: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7e008: tbnz            w0, #4, #0xd7e050
    // 0xd7e00c: ldur            x1, [fp, #-0x10]
    // 0xd7e010: ldur            x2, [fp, #-8]
    // 0xd7e014: LoadField: d0 = r1->field_7
    //     0xd7e014: ldur            d0, [x1, #7]
    // 0xd7e018: LoadField: d1 = r2->field_7
    //     0xd7e018: ldur            d1, [x2, #7]
    // 0xd7e01c: fcmp            d0, d1
    // 0xd7e020: b.ne            #0xd7e050
    // 0xd7e024: LoadField: d0 = r1->field_f
    //     0xd7e024: ldur            d0, [x1, #0xf]
    // 0xd7e028: LoadField: d1 = r2->field_f
    //     0xd7e028: ldur            d1, [x2, #0xf]
    // 0xd7e02c: fcmp            d0, d1
    // 0xd7e030: b.ne            #0xd7e050
    // 0xd7e034: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xd7e034: ldur            d0, [x1, #0x17]
    // 0xd7e038: ArrayLoad: d1 = r2[0]  ; List_8
    //     0xd7e038: ldur            d1, [x2, #0x17]
    // 0xd7e03c: fcmp            d0, d1
    // 0xd7e040: r16 = true
    //     0xd7e040: add             x16, NULL, #0x20  ; true
    // 0xd7e044: r17 = false
    //     0xd7e044: add             x17, NULL, #0x30  ; false
    // 0xd7e048: csel            x1, x16, x17, eq
    // 0xd7e04c: b               #0xd7e054
    // 0xd7e050: r1 = false
    //     0xd7e050: add             x1, NULL, #0x30  ; false
    // 0xd7e054: mov             x0, x1
    // 0xd7e058: b               #0xd7e060
    // 0xd7e05c: r0 = false
    //     0xd7e05c: add             x0, NULL, #0x30  ; false
    // 0xd7e060: LeaveFrame
    //     0xd7e060: mov             SP, fp
    //     0xd7e064: ldp             fp, lr, [SP], #0x10
    // 0xd7e068: ret
    //     0xd7e068: ret             
    // 0xd7e06c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7e06c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7e070: b               #0xd7de64
  }
}
