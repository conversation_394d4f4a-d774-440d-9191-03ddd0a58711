// lib: , url: package:timeline_tile/src/tile.dart

// class id: 1051206, size: 0x8
class :: {
}

// class id: 4905, size: 0x28, field offset: 0xc
//   const constructor, 
class _Indicator extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbbc708, size: 0x18c
    // 0xbbc708: EnterFrame
    //     0xbbc708: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc70c: mov             fp, SP
    // 0xbbc710: AllocStack(0x30)
    //     0xbbc710: sub             SP, SP, #0x30
    // 0xbbc714: SetupParameters(_Indicator this /* r1 => r1, fp-0x18 */)
    //     0xbbc714: stur            x1, [fp, #-0x18]
    // 0xbbc718: CheckStackOverflow
    //     0xbbc718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbc71c: cmp             SP, x16
    //     0xbbc720: b.ls            #0xbbc874
    // 0xbbc724: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xbbc724: ldur            w5, [x1, #0x17]
    // 0xbbc728: DecompressPointer r5
    //     0xbbc728: add             x5, x5, HEAP, lsl #32
    // 0xbbc72c: stur            x5, [fp, #-0x10]
    // 0xbbc730: LoadField: d0 = r5->field_7
    //     0xbbc730: ldur            d0, [x5, #7]
    // 0xbbc734: r0 = inline_Allocate_Double()
    //     0xbbc734: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbbc738: add             x0, x0, #0x10
    //     0xbbc73c: cmp             x2, x0
    //     0xbbc740: b.ls            #0xbbc87c
    //     0xbbc744: str             x0, [THR, #0x50]  ; THR::top
    //     0xbbc748: sub             x0, x0, #0xf
    //     0xbbc74c: movz            x2, #0xe15c
    //     0xbbc750: movk            x2, #0x3, lsl #16
    //     0xbbc754: stur            x2, [x0, #-1]
    // 0xbbc758: StoreField: r0->field_7 = d0
    //     0xbbc758: stur            d0, [x0, #7]
    // 0xbbc75c: stur            x0, [fp, #-8]
    // 0xbbc760: r0 = SizedBox()
    //     0xbbc760: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbbc764: mov             x3, x0
    // 0xbbc768: ldur            x0, [fp, #-8]
    // 0xbbc76c: stur            x3, [fp, #-0x20]
    // 0xbbc770: StoreField: r3->field_f = r0
    //     0xbbc770: stur            w0, [x3, #0xf]
    // 0xbbc774: r0 = inf
    //     0xbbc774: ldr             x0, [PP, #0x49e0]  ; [pp+0x49e0] inf
    // 0xbbc778: StoreField: r3->field_13 = r0
    //     0xbbc778: stur            w0, [x3, #0x13]
    // 0xbbc77c: r1 = Null
    //     0xbbc77c: mov             x1, NULL
    // 0xbbc780: r2 = 2
    //     0xbbc780: movz            x2, #0x2
    // 0xbbc784: r0 = AllocateArray()
    //     0xbbc784: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbbc788: mov             x2, x0
    // 0xbbc78c: ldur            x0, [fp, #-0x20]
    // 0xbbc790: stur            x2, [fp, #-8]
    // 0xbbc794: StoreField: r2->field_f = r0
    //     0xbbc794: stur            w0, [x2, #0xf]
    // 0xbbc798: r1 = <Widget>
    //     0xbbc798: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbc79c: r0 = AllocateGrowableArray()
    //     0xbbc79c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbbc7a0: mov             x1, x0
    // 0xbbc7a4: ldur            x0, [fp, #-8]
    // 0xbbc7a8: stur            x1, [fp, #-0x30]
    // 0xbbc7ac: StoreField: r1->field_f = r0
    //     0xbbc7ac: stur            w0, [x1, #0xf]
    // 0xbbc7b0: r0 = 2
    //     0xbbc7b0: movz            x0, #0x2
    // 0xbbc7b4: StoreField: r1->field_b = r0
    //     0xbbc7b4: stur            w0, [x1, #0xb]
    // 0xbbc7b8: ldur            x0, [fp, #-0x18]
    // 0xbbc7bc: LoadField: r3 = r0->field_f
    //     0xbbc7bc: ldur            w3, [x0, #0xf]
    // 0xbbc7c0: DecompressPointer r3
    //     0xbbc7c0: add             x3, x3, HEAP, lsl #32
    // 0xbbc7c4: stur            x3, [fp, #-0x28]
    // 0xbbc7c8: LoadField: r2 = r0->field_13
    //     0xbbc7c8: ldur            w2, [x0, #0x13]
    // 0xbbc7cc: DecompressPointer r2
    //     0xbbc7cc: add             x2, x2, HEAP, lsl #32
    // 0xbbc7d0: stur            x2, [fp, #-0x20]
    // 0xbbc7d4: LoadField: r6 = r0->field_23
    //     0xbbc7d4: ldur            w6, [x0, #0x23]
    // 0xbbc7d8: DecompressPointer r6
    //     0xbbc7d8: add             x6, x6, HEAP, lsl #32
    // 0xbbc7dc: stur            x6, [fp, #-8]
    // 0xbbc7e0: r0 = _TimelinePainter()
    //     0xbbc7e0: bl              #0xbbcaa8  ; Allocate_TimelinePainterStub -> _TimelinePainter (size=0x50)
    // 0xbbc7e4: mov             x1, x0
    // 0xbbc7e8: ldur            x2, [fp, #-0x20]
    // 0xbbc7ec: ldur            x3, [fp, #-0x28]
    // 0xbbc7f0: ldur            x5, [fp, #-0x10]
    // 0xbbc7f4: ldur            x6, [fp, #-8]
    // 0xbbc7f8: r7 = true
    //     0xbbc7f8: add             x7, NULL, #0x20  ; true
    // 0xbbc7fc: stur            x0, [fp, #-8]
    // 0xbbc800: r0 = _TimelinePainter()
    //     0xbbc800: bl              #0xbbc894  ; [package:timeline_tile/src/tile.dart] _TimelinePainter::_TimelinePainter
    // 0xbbc804: r0 = Stack()
    //     0xbbc804: bl              #0x9daa98  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbbc808: mov             x1, x0
    // 0xbbc80c: r0 = Instance_AlignmentDirectional
    //     0xbbc80c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b0] Obj!AlignmentDirectional@e13d31
    //     0xbbc810: ldr             x0, [x0, #0x7b0]
    // 0xbbc814: stur            x1, [fp, #-0x10]
    // 0xbbc818: StoreField: r1->field_f = r0
    //     0xbbc818: stur            w0, [x1, #0xf]
    // 0xbbc81c: r0 = Instance_StackFit
    //     0xbbc81c: add             x0, PP, #0x25, lsl #12  ; [pp+0x257b8] Obj!StackFit@e35461
    //     0xbbc820: ldr             x0, [x0, #0x7b8]
    // 0xbbc824: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbc824: stur            w0, [x1, #0x17]
    // 0xbbc828: r0 = Instance_Clip
    //     0xbbc828: add             x0, PP, #0x25, lsl #12  ; [pp+0x257c0] Obj!Clip@e39ae1
    //     0xbbc82c: ldr             x0, [x0, #0x7c0]
    // 0xbbc830: StoreField: r1->field_1b = r0
    //     0xbbc830: stur            w0, [x1, #0x1b]
    // 0xbbc834: ldur            x0, [fp, #-0x30]
    // 0xbbc838: StoreField: r1->field_b = r0
    //     0xbbc838: stur            w0, [x1, #0xb]
    // 0xbbc83c: r0 = CustomPaint()
    //     0xbbc83c: bl              #0x9d4558  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0xbbc840: ldur            x1, [fp, #-8]
    // 0xbbc844: StoreField: r0->field_f = r1
    //     0xbbc844: stur            w1, [x0, #0xf]
    // 0xbbc848: r1 = Instance_Size
    //     0xbbc848: add             x1, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!Size@e2c001
    //     0xbbc84c: ldr             x1, [x1, #0xa20]
    // 0xbbc850: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbc850: stur            w1, [x0, #0x17]
    // 0xbbc854: r1 = false
    //     0xbbc854: add             x1, NULL, #0x30  ; false
    // 0xbbc858: StoreField: r0->field_1b = r1
    //     0xbbc858: stur            w1, [x0, #0x1b]
    // 0xbbc85c: StoreField: r0->field_1f = r1
    //     0xbbc85c: stur            w1, [x0, #0x1f]
    // 0xbbc860: ldur            x1, [fp, #-0x10]
    // 0xbbc864: StoreField: r0->field_b = r1
    //     0xbbc864: stur            w1, [x0, #0xb]
    // 0xbbc868: LeaveFrame
    //     0xbbc868: mov             SP, fp
    //     0xbbc86c: ldp             fp, lr, [SP], #0x10
    // 0xbbc870: ret
    //     0xbbc870: ret             
    // 0xbbc874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbc874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbc878: b               #0xbbc724
    // 0xbbc87c: SaveReg d0
    //     0xbbc87c: str             q0, [SP, #-0x10]!
    // 0xbbc880: stp             x1, x5, [SP, #-0x10]!
    // 0xbbc884: r0 = AllocateDouble()
    //     0xbbc884: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbbc888: ldp             x1, x5, [SP], #0x10
    // 0xbbc88c: RestoreReg d0
    //     0xbbc88c: ldr             q0, [SP], #0x10
    // 0xbbc890: b               #0xbbc758
  }
}

// class id: 4906, size: 0x38, field offset: 0xc
//   const constructor, 
class TimelineTile extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbbc214, size: 0x58
    // 0xbbc214: EnterFrame
    //     0xbbc214: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc218: mov             fp, SP
    // 0xbbc21c: AllocStack(0x8)
    //     0xbbc21c: sub             SP, SP, #8
    // 0xbbc220: SetupParameters(TimelineTile this /* r1 => r1, fp-0x8 */)
    //     0xbbc220: stur            x1, [fp, #-8]
    // 0xbbc224: r1 = 1
    //     0xbbc224: movz            x1, #0x1
    // 0xbbc228: r0 = AllocateContext()
    //     0xbbc228: bl              #0xec126c  ; AllocateContextStub
    // 0xbbc22c: mov             x1, x0
    // 0xbbc230: ldur            x0, [fp, #-8]
    // 0xbbc234: StoreField: r1->field_f = r0
    //     0xbbc234: stur            w0, [x1, #0xf]
    // 0xbbc238: mov             x2, x1
    // 0xbbc23c: r1 = Function '<anonymous closure>':.
    //     0xbbc23c: add             x1, PP, #0x47, lsl #12  ; [pp+0x47ac0] AnonymousClosure: (0xbbc26c), in [package:timeline_tile/src/tile.dart] TimelineTile::build (0xbbc214)
    //     0xbbc240: ldr             x1, [x1, #0xac0]
    // 0xbbc244: r0 = AllocateClosure()
    //     0xbbc244: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbc248: r1 = <BoxConstraints>
    //     0xbbc248: add             x1, PP, #0x37, lsl #12  ; [pp+0x37fa8] TypeArguments: <BoxConstraints>
    //     0xbbc24c: ldr             x1, [x1, #0xfa8]
    // 0xbbc250: stur            x0, [fp, #-8]
    // 0xbbc254: r0 = LayoutBuilder()
    //     0xbbc254: bl              #0x9f1460  ; AllocateLayoutBuilderStub -> LayoutBuilder (size=0x14)
    // 0xbbc258: ldur            x1, [fp, #-8]
    // 0xbbc25c: StoreField: r0->field_f = r1
    //     0xbbc25c: stur            w1, [x0, #0xf]
    // 0xbbc260: LeaveFrame
    //     0xbbc260: mov             SP, fp
    //     0xbbc264: ldp             fp, lr, [SP], #0x10
    // 0xbbc268: ret
    //     0xbbc268: ret             
  }
  [closure] SingleChildRenderObjectWidget <anonymous closure>(dynamic, BuildContext, BoxConstraints) {
    // ** addr: 0xbbc26c, size: 0x490
    // 0xbbc26c: EnterFrame
    //     0xbbc26c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc270: mov             fp, SP
    // 0xbbc274: AllocStack(0x60)
    //     0xbbc274: sub             SP, SP, #0x60
    // 0xbbc278: SetupParameters()
    //     0xbbc278: ldr             x0, [fp, #0x20]
    //     0xbbc27c: ldur            w3, [x0, #0x17]
    //     0xbbc280: add             x3, x3, HEAP, lsl #32
    //     0xbbc284: stur            x3, [fp, #-8]
    // 0xbbc288: CheckStackOverflow
    //     0xbbc288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbc28c: cmp             SP, x16
    //     0xbbc290: b.ls            #0xbbc6b8
    // 0xbbc294: LoadField: r0 = r3->field_f
    //     0xbbc294: ldur            w0, [x3, #0xf]
    // 0xbbc298: DecompressPointer r0
    //     0xbbc298: add             x0, x0, HEAP, lsl #32
    // 0xbbc29c: LoadField: r1 = r0->field_2b
    //     0xbbc29c: ldur            w1, [x0, #0x2b]
    // 0xbbc2a0: DecompressPointer r1
    //     0xbbc2a0: add             x1, x1, HEAP, lsl #32
    // 0xbbc2a4: LoadField: r0 = r1->field_1b
    //     0xbbc2a4: ldur            w0, [x1, #0x1b]
    // 0xbbc2a8: DecompressPointer r0
    //     0xbbc2a8: add             x0, x0, HEAP, lsl #32
    // 0xbbc2ac: LoadField: d0 = r0->field_7
    //     0xbbc2ac: ldur            d0, [x0, #7]
    // 0xbbc2b0: stur            d0, [fp, #-0x58]
    // 0xbbc2b4: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xbbc2b4: ldur            d1, [x0, #0x17]
    // 0xbbc2b8: stur            d1, [fp, #-0x50]
    // 0xbbc2bc: r1 = <Widget>
    //     0xbbc2bc: ldr             x1, [PP, #0x6f88]  ; [pp+0x6f88] TypeArguments: <Widget>
    // 0xbbc2c0: r2 = 0
    //     0xbbc2c0: movz            x2, #0
    // 0xbbc2c4: r0 = _GrowableList()
    //     0xbbc2c4: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xbbc2c8: ldur            d1, [fp, #-0x58]
    // 0xbbc2cc: d0 = 0.000000
    //     0xbbc2cc: eor             v0.16b, v0.16b, v0.16b
    // 0xbbc2d0: stur            x0, [fp, #-0x18]
    // 0xbbc2d4: fcmp            d1, d0
    // 0xbbc2d8: b.le            #0xbbc398
    // 0xbbc2dc: r1 = inline_Allocate_Double()
    //     0xbbc2dc: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbbc2e0: add             x1, x1, #0x10
    //     0xbbc2e4: cmp             x2, x1
    //     0xbbc2e8: b.ls            #0xbbc6c0
    //     0xbbc2ec: str             x1, [THR, #0x50]  ; THR::top
    //     0xbbc2f0: sub             x1, x1, #0xf
    //     0xbbc2f4: movz            x2, #0xe15c
    //     0xbbc2f8: movk            x2, #0x3, lsl #16
    //     0xbbc2fc: stur            x2, [x1, #-1]
    // 0xbbc300: StoreField: r1->field_7 = d1
    //     0xbbc300: stur            d1, [x1, #7]
    // 0xbbc304: stur            x1, [fp, #-0x10]
    // 0xbbc308: r0 = SizedBox()
    //     0xbbc308: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbbc30c: mov             x2, x0
    // 0xbbc310: ldur            x0, [fp, #-0x10]
    // 0xbbc314: stur            x2, [fp, #-0x28]
    // 0xbbc318: StoreField: r2->field_f = r0
    //     0xbbc318: stur            w0, [x2, #0xf]
    // 0xbbc31c: ldur            x0, [fp, #-0x18]
    // 0xbbc320: LoadField: r1 = r0->field_b
    //     0xbbc320: ldur            w1, [x0, #0xb]
    // 0xbbc324: LoadField: r3 = r0->field_f
    //     0xbbc324: ldur            w3, [x0, #0xf]
    // 0xbbc328: DecompressPointer r3
    //     0xbbc328: add             x3, x3, HEAP, lsl #32
    // 0xbbc32c: LoadField: r4 = r3->field_b
    //     0xbbc32c: ldur            w4, [x3, #0xb]
    // 0xbbc330: r3 = LoadInt32Instr(r1)
    //     0xbbc330: sbfx            x3, x1, #1, #0x1f
    // 0xbbc334: stur            x3, [fp, #-0x20]
    // 0xbbc338: r1 = LoadInt32Instr(r4)
    //     0xbbc338: sbfx            x1, x4, #1, #0x1f
    // 0xbbc33c: cmp             x3, x1
    // 0xbbc340: b.ne            #0xbbc34c
    // 0xbbc344: mov             x1, x0
    // 0xbbc348: r0 = _growToNextCapacity()
    //     0xbbc348: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbc34c: ldur            x2, [fp, #-0x18]
    // 0xbbc350: ldur            x3, [fp, #-0x20]
    // 0xbbc354: add             x0, x3, #1
    // 0xbbc358: lsl             x1, x0, #1
    // 0xbbc35c: StoreField: r2->field_b = r1
    //     0xbbc35c: stur            w1, [x2, #0xb]
    // 0xbbc360: LoadField: r1 = r2->field_f
    //     0xbbc360: ldur            w1, [x2, #0xf]
    // 0xbbc364: DecompressPointer r1
    //     0xbbc364: add             x1, x1, HEAP, lsl #32
    // 0xbbc368: ldur            x0, [fp, #-0x28]
    // 0xbbc36c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbc36c: add             x25, x1, x3, lsl #2
    //     0xbbc370: add             x25, x25, #0xf
    //     0xbbc374: str             w0, [x25]
    //     0xbbc378: tbz             w0, #0, #0xbbc394
    //     0xbbc37c: ldurb           w16, [x1, #-1]
    //     0xbbc380: ldurb           w17, [x0, #-1]
    //     0xbbc384: and             x16, x17, x16, lsr #2
    //     0xbbc388: tst             x16, HEAP, lsr #32
    //     0xbbc38c: b.eq            #0xbbc394
    //     0xbbc390: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbc394: b               #0xbbc39c
    // 0xbbc398: mov             x2, x0
    // 0xbbc39c: ldur            x0, [fp, #-8]
    // 0xbbc3a0: LoadField: r1 = r0->field_f
    //     0xbbc3a0: ldur            w1, [x0, #0xf]
    // 0xbbc3a4: DecompressPointer r1
    //     0xbbc3a4: add             x1, x1, HEAP, lsl #32
    // 0xbbc3a8: LoadField: r3 = r1->field_2f
    //     0xbbc3a8: ldur            w3, [x1, #0x2f]
    // 0xbbc3ac: DecompressPointer r3
    //     0xbbc3ac: add             x3, x3, HEAP, lsl #32
    // 0xbbc3b0: stur            x3, [fp, #-0x38]
    // 0xbbc3b4: LoadField: r4 = r1->field_33
    //     0xbbc3b4: ldur            w4, [x1, #0x33]
    // 0xbbc3b8: DecompressPointer r4
    //     0xbbc3b8: add             x4, x4, HEAP, lsl #32
    // 0xbbc3bc: stur            x4, [fp, #-0x30]
    // 0xbbc3c0: LoadField: r5 = r1->field_2b
    //     0xbbc3c0: ldur            w5, [x1, #0x2b]
    // 0xbbc3c4: DecompressPointer r5
    //     0xbbc3c4: add             x5, x5, HEAP, lsl #32
    // 0xbbc3c8: stur            x5, [fp, #-0x28]
    // 0xbbc3cc: LoadField: r6 = r1->field_27
    //     0xbbc3cc: ldur            w6, [x1, #0x27]
    // 0xbbc3d0: DecompressPointer r6
    //     0xbbc3d0: add             x6, x6, HEAP, lsl #32
    // 0xbbc3d4: stur            x6, [fp, #-0x10]
    // 0xbbc3d8: r0 = _Indicator()
    //     0xbbc3d8: bl              #0xbbc6fc  ; Allocate_IndicatorStub -> _Indicator (size=0x28)
    // 0xbbc3dc: mov             x2, x0
    // 0xbbc3e0: r0 = Instance_TimelineAxis
    //     0xbbc3e0: add             x0, PP, #0x40, lsl #12  ; [pp+0x40320] Obj!TimelineAxis@e2df21
    //     0xbbc3e4: ldr             x0, [x0, #0x320]
    // 0xbbc3e8: stur            x2, [fp, #-0x40]
    // 0xbbc3ec: StoreField: r2->field_b = r0
    //     0xbbc3ec: stur            w0, [x2, #0xb]
    // 0xbbc3f0: ldur            x0, [fp, #-0x38]
    // 0xbbc3f4: StoreField: r2->field_f = r0
    //     0xbbc3f4: stur            w0, [x2, #0xf]
    // 0xbbc3f8: ldur            x0, [fp, #-0x30]
    // 0xbbc3fc: StoreField: r2->field_13 = r0
    //     0xbbc3fc: stur            w0, [x2, #0x13]
    // 0xbbc400: ldur            x0, [fp, #-0x28]
    // 0xbbc404: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbc404: stur            w0, [x2, #0x17]
    // 0xbbc408: r0 = true
    //     0xbbc408: add             x0, NULL, #0x20  ; true
    // 0xbbc40c: StoreField: r2->field_1b = r0
    //     0xbbc40c: stur            w0, [x2, #0x1b]
    // 0xbbc410: r0 = false
    //     0xbbc410: add             x0, NULL, #0x30  ; false
    // 0xbbc414: StoreField: r2->field_1f = r0
    //     0xbbc414: stur            w0, [x2, #0x1f]
    // 0xbbc418: ldur            x0, [fp, #-0x10]
    // 0xbbc41c: StoreField: r2->field_23 = r0
    //     0xbbc41c: stur            w0, [x2, #0x23]
    // 0xbbc420: ldur            x0, [fp, #-0x18]
    // 0xbbc424: LoadField: r1 = r0->field_b
    //     0xbbc424: ldur            w1, [x0, #0xb]
    // 0xbbc428: LoadField: r3 = r0->field_f
    //     0xbbc428: ldur            w3, [x0, #0xf]
    // 0xbbc42c: DecompressPointer r3
    //     0xbbc42c: add             x3, x3, HEAP, lsl #32
    // 0xbbc430: LoadField: r4 = r3->field_b
    //     0xbbc430: ldur            w4, [x3, #0xb]
    // 0xbbc434: r3 = LoadInt32Instr(r1)
    //     0xbbc434: sbfx            x3, x1, #1, #0x1f
    // 0xbbc438: stur            x3, [fp, #-0x20]
    // 0xbbc43c: r1 = LoadInt32Instr(r4)
    //     0xbbc43c: sbfx            x1, x4, #1, #0x1f
    // 0xbbc440: cmp             x3, x1
    // 0xbbc444: b.ne            #0xbbc450
    // 0xbbc448: mov             x1, x0
    // 0xbbc44c: r0 = _growToNextCapacity()
    //     0xbbc44c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbc450: ldur            x2, [fp, #-0x18]
    // 0xbbc454: ldur            d1, [fp, #-0x50]
    // 0xbbc458: ldur            x3, [fp, #-0x20]
    // 0xbbc45c: d0 = 0.000000
    //     0xbbc45c: eor             v0.16b, v0.16b, v0.16b
    // 0xbbc460: add             x4, x3, #1
    // 0xbbc464: stur            x4, [fp, #-0x48]
    // 0xbbc468: lsl             x0, x4, #1
    // 0xbbc46c: StoreField: r2->field_b = r0
    //     0xbbc46c: stur            w0, [x2, #0xb]
    // 0xbbc470: LoadField: r5 = r2->field_f
    //     0xbbc470: ldur            w5, [x2, #0xf]
    // 0xbbc474: DecompressPointer r5
    //     0xbbc474: add             x5, x5, HEAP, lsl #32
    // 0xbbc478: mov             x1, x5
    // 0xbbc47c: ldur            x0, [fp, #-0x40]
    // 0xbbc480: stur            x5, [fp, #-0x28]
    // 0xbbc484: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbc484: add             x25, x1, x3, lsl #2
    //     0xbbc488: add             x25, x25, #0xf
    //     0xbbc48c: str             w0, [x25]
    //     0xbbc490: tbz             w0, #0, #0xbbc4ac
    //     0xbbc494: ldurb           w16, [x1, #-1]
    //     0xbbc498: ldurb           w17, [x0, #-1]
    //     0xbbc49c: and             x16, x17, x16, lsr #2
    //     0xbbc4a0: tst             x16, HEAP, lsr #32
    //     0xbbc4a4: b.eq            #0xbbc4ac
    //     0xbbc4a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbc4ac: fcmp            d1, d0
    // 0xbbc4b0: b.le            #0xbbc560
    // 0xbbc4b4: r0 = inline_Allocate_Double()
    //     0xbbc4b4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbbc4b8: add             x0, x0, #0x10
    //     0xbbc4bc: cmp             x1, x0
    //     0xbbc4c0: b.ls            #0xbbc6dc
    //     0xbbc4c4: str             x0, [THR, #0x50]  ; THR::top
    //     0xbbc4c8: sub             x0, x0, #0xf
    //     0xbbc4cc: movz            x1, #0xe15c
    //     0xbbc4d0: movk            x1, #0x3, lsl #16
    //     0xbbc4d4: stur            x1, [x0, #-1]
    // 0xbbc4d8: StoreField: r0->field_7 = d1
    //     0xbbc4d8: stur            d1, [x0, #7]
    // 0xbbc4dc: stur            x0, [fp, #-0x10]
    // 0xbbc4e0: r0 = SizedBox()
    //     0xbbc4e0: bl              #0x925f00  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbbc4e4: mov             x2, x0
    // 0xbbc4e8: ldur            x0, [fp, #-0x10]
    // 0xbbc4ec: stur            x2, [fp, #-0x30]
    // 0xbbc4f0: StoreField: r2->field_f = r0
    //     0xbbc4f0: stur            w0, [x2, #0xf]
    // 0xbbc4f4: ldur            x0, [fp, #-0x28]
    // 0xbbc4f8: LoadField: r1 = r0->field_b
    //     0xbbc4f8: ldur            w1, [x0, #0xb]
    // 0xbbc4fc: r0 = LoadInt32Instr(r1)
    //     0xbbc4fc: sbfx            x0, x1, #1, #0x1f
    // 0xbbc500: ldur            x3, [fp, #-0x48]
    // 0xbbc504: cmp             x3, x0
    // 0xbbc508: b.ne            #0xbbc514
    // 0xbbc50c: ldur            x1, [fp, #-0x18]
    // 0xbbc510: r0 = _growToNextCapacity()
    //     0xbbc510: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbc514: ldur            x3, [fp, #-0x18]
    // 0xbbc518: ldur            x2, [fp, #-0x48]
    // 0xbbc51c: add             x0, x2, #1
    // 0xbbc520: lsl             x1, x0, #1
    // 0xbbc524: StoreField: r3->field_b = r1
    //     0xbbc524: stur            w1, [x3, #0xb]
    // 0xbbc528: LoadField: r1 = r3->field_f
    //     0xbbc528: ldur            w1, [x3, #0xf]
    // 0xbbc52c: DecompressPointer r1
    //     0xbbc52c: add             x1, x1, HEAP, lsl #32
    // 0xbbc530: ldur            x0, [fp, #-0x30]
    // 0xbbc534: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbbc534: add             x25, x1, x2, lsl #2
    //     0xbbc538: add             x25, x25, #0xf
    //     0xbbc53c: str             w0, [x25]
    //     0xbbc540: tbz             w0, #0, #0xbbc55c
    //     0xbbc544: ldurb           w16, [x1, #-1]
    //     0xbbc548: ldurb           w17, [x0, #-1]
    //     0xbbc54c: and             x16, x17, x16, lsr #2
    //     0xbbc550: tst             x16, HEAP, lsr #32
    //     0xbbc554: b.eq            #0xbbc55c
    //     0xbbc558: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbc55c: b               #0xbbc564
    // 0xbbc560: mov             x3, x2
    // 0xbbc564: ldur            x0, [fp, #-8]
    // 0xbbc568: r0 = Container()
    //     0xbbc568: bl              #0x65dc40  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbc56c: r16 = 100.000000
    //     0xbbc56c: ldr             x16, [PP, #0x5b40]  ; [pp+0x5b40] 100
    // 0xbbc570: str             x16, [SP]
    // 0xbbc574: mov             x1, x0
    // 0xbbc578: r4 = const [0, 0x2, 0x1, 0x1, height, 0x1, null]
    //     0xbbc578: add             x4, PP, #0x35, lsl #12  ; [pp+0x35538] List(7) [0, 0x2, 0x1, 0x1, "height", 0x1, Null]
    //     0xbbc57c: ldr             x4, [x4, #0x538]
    // 0xbbc580: r0 = Container()
    //     0xbbc580: bl              #0x65d67c  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbc584: ldur            x0, [fp, #-8]
    // 0xbbc588: LoadField: r1 = r0->field_f
    //     0xbbc588: ldur            w1, [x0, #0xf]
    // 0xbbc58c: DecompressPointer r1
    //     0xbbc58c: add             x1, x1, HEAP, lsl #32
    // 0xbbc590: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbbc590: ldur            w0, [x1, #0x17]
    // 0xbbc594: DecompressPointer r0
    //     0xbbc594: add             x0, x0, HEAP, lsl #32
    // 0xbbc598: stur            x0, [fp, #-8]
    // 0xbbc59c: r1 = <FlexParentData>
    //     0xbbc59c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25720] TypeArguments: <FlexParentData>
    //     0xbbc5a0: ldr             x1, [x1, #0x720]
    // 0xbbc5a4: r0 = Expanded()
    //     0xbbc5a4: bl              #0xa06d1c  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbbc5a8: mov             x2, x0
    // 0xbbc5ac: r0 = 1
    //     0xbbc5ac: movz            x0, #0x1
    // 0xbbc5b0: stur            x2, [fp, #-0x10]
    // 0xbbc5b4: StoreField: r2->field_13 = r0
    //     0xbbc5b4: stur            x0, [x2, #0x13]
    // 0xbbc5b8: r0 = Instance_FlexFit
    //     0xbbc5b8: add             x0, PP, #0x25, lsl #12  ; [pp+0x25728] Obj!FlexFit@e35b41
    //     0xbbc5bc: ldr             x0, [x0, #0x728]
    // 0xbbc5c0: StoreField: r2->field_1b = r0
    //     0xbbc5c0: stur            w0, [x2, #0x1b]
    // 0xbbc5c4: ldur            x0, [fp, #-8]
    // 0xbbc5c8: StoreField: r2->field_b = r0
    //     0xbbc5c8: stur            w0, [x2, #0xb]
    // 0xbbc5cc: ldur            x0, [fp, #-0x18]
    // 0xbbc5d0: LoadField: r1 = r0->field_b
    //     0xbbc5d0: ldur            w1, [x0, #0xb]
    // 0xbbc5d4: LoadField: r3 = r0->field_f
    //     0xbbc5d4: ldur            w3, [x0, #0xf]
    // 0xbbc5d8: DecompressPointer r3
    //     0xbbc5d8: add             x3, x3, HEAP, lsl #32
    // 0xbbc5dc: LoadField: r4 = r3->field_b
    //     0xbbc5dc: ldur            w4, [x3, #0xb]
    // 0xbbc5e0: r3 = LoadInt32Instr(r1)
    //     0xbbc5e0: sbfx            x3, x1, #1, #0x1f
    // 0xbbc5e4: stur            x3, [fp, #-0x20]
    // 0xbbc5e8: r1 = LoadInt32Instr(r4)
    //     0xbbc5e8: sbfx            x1, x4, #1, #0x1f
    // 0xbbc5ec: cmp             x3, x1
    // 0xbbc5f0: b.ne            #0xbbc5fc
    // 0xbbc5f4: mov             x1, x0
    // 0xbbc5f8: r0 = _growToNextCapacity()
    //     0xbbc5f8: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbc5fc: ldur            x2, [fp, #-0x18]
    // 0xbbc600: ldur            x3, [fp, #-0x20]
    // 0xbbc604: add             x0, x3, #1
    // 0xbbc608: lsl             x1, x0, #1
    // 0xbbc60c: StoreField: r2->field_b = r1
    //     0xbbc60c: stur            w1, [x2, #0xb]
    // 0xbbc610: LoadField: r1 = r2->field_f
    //     0xbbc610: ldur            w1, [x2, #0xf]
    // 0xbbc614: DecompressPointer r1
    //     0xbbc614: add             x1, x1, HEAP, lsl #32
    // 0xbbc618: ldur            x0, [fp, #-0x10]
    // 0xbbc61c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbc61c: add             x25, x1, x3, lsl #2
    //     0xbbc620: add             x25, x25, #0xf
    //     0xbbc624: str             w0, [x25]
    //     0xbbc628: tbz             w0, #0, #0xbbc644
    //     0xbbc62c: ldurb           w16, [x1, #-1]
    //     0xbbc630: ldurb           w17, [x0, #-1]
    //     0xbbc634: and             x16, x17, x16, lsr #2
    //     0xbbc638: tst             x16, HEAP, lsr #32
    //     0xbbc63c: b.eq            #0xbbc644
    //     0xbbc640: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbc644: r0 = Row()
    //     0xbbc644: bl              #0x9e6d6c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbc648: mov             x1, x0
    // 0xbbc64c: r0 = Instance_Axis
    //     0xbbc64c: ldr             x0, [PP, #0x4678]  ; [pp+0x4678] Obj!Axis@e35f41
    // 0xbbc650: stur            x1, [fp, #-8]
    // 0xbbc654: StoreField: r1->field_f = r0
    //     0xbbc654: stur            w0, [x1, #0xf]
    // 0xbbc658: r0 = Instance_MainAxisAlignment
    //     0xbbc658: add             x0, PP, #0x25, lsl #12  ; [pp+0x25730] Obj!MainAxisAlignment@e35ae1
    //     0xbbc65c: ldr             x0, [x0, #0x730]
    // 0xbbc660: StoreField: r1->field_13 = r0
    //     0xbbc660: stur            w0, [x1, #0x13]
    // 0xbbc664: r0 = Instance_MainAxisSize
    //     0xbbc664: add             x0, PP, #0x25, lsl #12  ; [pp+0x25738] Obj!MainAxisSize@e35b21
    //     0xbbc668: ldr             x0, [x0, #0x738]
    // 0xbbc66c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbc66c: stur            w0, [x1, #0x17]
    // 0xbbc670: r0 = Instance_CrossAxisAlignment
    //     0xbbc670: add             x0, PP, #0x25, lsl #12  ; [pp+0x25740] Obj!CrossAxisAlignment@e359e1
    //     0xbbc674: ldr             x0, [x0, #0x740]
    // 0xbbc678: StoreField: r1->field_1b = r0
    //     0xbbc678: stur            w0, [x1, #0x1b]
    // 0xbbc67c: r0 = Instance_VerticalDirection
    //     0xbbc67c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25748] Obj!VerticalDirection@e35f01
    //     0xbbc680: ldr             x0, [x0, #0x748]
    // 0xbbc684: StoreField: r1->field_23 = r0
    //     0xbbc684: stur            w0, [x1, #0x23]
    // 0xbbc688: r0 = Instance_Clip
    //     0xbbc688: add             x0, PP, #0x25, lsl #12  ; [pp+0x25750] Obj!Clip@e39ac1
    //     0xbbc68c: ldr             x0, [x0, #0x750]
    // 0xbbc690: StoreField: r1->field_2b = r0
    //     0xbbc690: stur            w0, [x1, #0x2b]
    // 0xbbc694: StoreField: r1->field_2f = rZR
    //     0xbbc694: stur            xzr, [x1, #0x2f]
    // 0xbbc698: ldur            x0, [fp, #-0x18]
    // 0xbbc69c: StoreField: r1->field_b = r0
    //     0xbbc69c: stur            w0, [x1, #0xb]
    // 0xbbc6a0: r0 = IntrinsicHeight()
    //     0xbbc6a0: bl              #0xa02d14  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xbbc6a4: ldur            x1, [fp, #-8]
    // 0xbbc6a8: StoreField: r0->field_b = r1
    //     0xbbc6a8: stur            w1, [x0, #0xb]
    // 0xbbc6ac: LeaveFrame
    //     0xbbc6ac: mov             SP, fp
    //     0xbbc6b0: ldp             fp, lr, [SP], #0x10
    // 0xbbc6b4: ret
    //     0xbbc6b4: ret             
    // 0xbbc6b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbc6b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbc6bc: b               #0xbbc294
    // 0xbbc6c0: stp             q0, q1, [SP, #-0x20]!
    // 0xbbc6c4: SaveReg r0
    //     0xbbc6c4: str             x0, [SP, #-8]!
    // 0xbbc6c8: r0 = AllocateDouble()
    //     0xbbc6c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbbc6cc: mov             x1, x0
    // 0xbbc6d0: RestoreReg r0
    //     0xbbc6d0: ldr             x0, [SP], #8
    // 0xbbc6d4: ldp             q0, q1, [SP], #0x20
    // 0xbbc6d8: b               #0xbbc300
    // 0xbbc6dc: SaveReg d1
    //     0xbbc6dc: str             q1, [SP, #-0x10]!
    // 0xbbc6e0: stp             x4, x5, [SP, #-0x10]!
    // 0xbbc6e4: SaveReg r2
    //     0xbbc6e4: str             x2, [SP, #-8]!
    // 0xbbc6e8: r0 = AllocateDouble()
    //     0xbbc6e8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xbbc6ec: RestoreReg r2
    //     0xbbc6ec: ldr             x2, [SP], #8
    // 0xbbc6f0: ldp             x4, x5, [SP], #0x10
    // 0xbbc6f4: RestoreReg d1
    //     0xbbc6f4: ldr             q1, [SP], #0x10
    // 0xbbc6f8: b               #0xbbc4d8
  }
}

// class id: 5452, size: 0x50, field offset: 0xc
class _TimelinePainter extends CustomPainter {

  _ paint(/* No info */) {
    // ** addr: 0x7d5dec, size: 0x194
    // 0x7d5dec: EnterFrame
    //     0x7d5dec: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5df0: mov             fp, SP
    // 0x7d5df4: AllocStack(0x40)
    //     0x7d5df4: sub             SP, SP, #0x40
    // 0x7d5df8: d0 = 0.000000
    //     0x7d5df8: eor             v0.16b, v0.16b, v0.16b
    // 0x7d5dfc: stur            x1, [fp, #-0x10]
    // 0x7d5e00: stur            x2, [fp, #-0x18]
    // 0x7d5e04: CheckStackOverflow
    //     0x7d5e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d5e08: cmp             SP, x16
    //     0x7d5e0c: b.ls            #0x7d5f74
    // 0x7d5e10: ArrayLoad: d3 = r1[0]  ; List_8
    //     0x7d5e10: ldur            d3, [x1, #0x17]
    // 0x7d5e14: stur            d3, [fp, #-0x38]
    // 0x7d5e18: fcmp            d3, d0
    // 0x7d5e1c: b.gt            #0x7d5e2c
    // 0x7d5e20: LoadField: d1 = r1->field_1f
    //     0x7d5e20: ldur            d1, [x1, #0x1f]
    // 0x7d5e24: fcmp            d1, d0
    // 0x7d5e28: b.le            #0x7d5e34
    // 0x7d5e2c: r0 = true
    //     0x7d5e2c: add             x0, NULL, #0x20  ; true
    // 0x7d5e30: b               #0x7d5e38
    // 0x7d5e34: r0 = false
    //     0x7d5e34: add             x0, NULL, #0x30  ; false
    // 0x7d5e38: d4 = 2.000000
    //     0x7d5e38: fmov            d4, #2.00000000
    // 0x7d5e3c: stur            x0, [fp, #-8]
    // 0x7d5e40: LoadField: d0 = r3->field_7
    //     0x7d5e40: ldur            d0, [x3, #7]
    // 0x7d5e44: fdiv            d5, d0, d4
    // 0x7d5e48: stur            d5, [fp, #-0x30]
    // 0x7d5e4c: LoadField: d0 = r1->field_27
    //     0x7d5e4c: ldur            d0, [x1, #0x27]
    // 0x7d5e50: LoadField: d6 = r1->field_1f
    //     0x7d5e50: ldur            d6, [x1, #0x1f]
    // 0x7d5e54: stur            d6, [fp, #-0x28]
    // 0x7d5e58: fadd            d1, d0, d6
    // 0x7d5e5c: fadd            d0, d1, d3
    // 0x7d5e60: LoadField: d2 = r3->field_f
    //     0x7d5e60: ldur            d2, [x3, #0xf]
    // 0x7d5e64: LoadField: d1 = r1->field_f
    //     0x7d5e64: ldur            d1, [x1, #0xf]
    // 0x7d5e68: mov             v31.16b, v0.16b
    // 0x7d5e6c: mov             v0.16b, v1.16b
    // 0x7d5e70: mov             v1.16b, v31.16b
    // 0x7d5e74: r0 = calculateAxisPositioning()
    //     0x7d5e74: bl              #0x7d6214  ; [package:timeline_tile/src/axis.dart] ::calculateAxisPositioning
    // 0x7d5e78: mov             x4, x0
    // 0x7d5e7c: ldur            x0, [fp, #-8]
    // 0x7d5e80: stur            x4, [fp, #-0x20]
    // 0x7d5e84: tbz             w0, #4, #0x7d5ea0
    // 0x7d5e88: ldur            x1, [fp, #-0x10]
    // 0x7d5e8c: ldur            x2, [fp, #-0x18]
    // 0x7d5e90: ldur            d0, [fp, #-0x30]
    // 0x7d5e94: mov             x3, x4
    // 0x7d5e98: r0 = _drawSingleLine()
    //     0x7d5e98: bl              #0x7d60ec  ; [package:timeline_tile/src/tile.dart] _TimelinePainter::_drawSingleLine
    // 0x7d5e9c: b               #0x7d5edc
    // 0x7d5ea0: ldur            x0, [fp, #-0x10]
    // 0x7d5ea4: mov             x1, x0
    // 0x7d5ea8: ldur            x2, [fp, #-0x18]
    // 0x7d5eac: ldur            d0, [fp, #-0x30]
    // 0x7d5eb0: ldur            x3, [fp, #-0x20]
    // 0x7d5eb4: r0 = _drawBeforeLine()
    //     0x7d5eb4: bl              #0x7d6040  ; [package:timeline_tile/src/tile.dart] _TimelinePainter::_drawBeforeLine
    // 0x7d5eb8: ldur            x0, [fp, #-0x10]
    // 0x7d5ebc: LoadField: r1 = r0->field_43
    //     0x7d5ebc: ldur            w1, [x0, #0x43]
    // 0x7d5ec0: DecompressPointer r1
    //     0x7d5ec0: add             x1, x1, HEAP, lsl #32
    // 0x7d5ec4: tbz             w1, #4, #0x7d5edc
    // 0x7d5ec8: mov             x1, x0
    // 0x7d5ecc: ldur            x2, [fp, #-0x18]
    // 0x7d5ed0: ldur            d0, [fp, #-0x30]
    // 0x7d5ed4: ldur            x3, [fp, #-0x20]
    // 0x7d5ed8: r0 = _drawAfterLine()
    //     0x7d5ed8: bl              #0x7d5f80  ; [package:timeline_tile/src/tile.dart] _TimelinePainter::_drawAfterLine
    // 0x7d5edc: ldur            x0, [fp, #-0x10]
    // 0x7d5ee0: LoadField: r1 = r0->field_3b
    //     0x7d5ee0: ldur            w1, [x0, #0x3b]
    // 0x7d5ee4: DecompressPointer r1
    //     0x7d5ee4: add             x1, x1, HEAP, lsl #32
    // 0x7d5ee8: tbnz            w1, #4, #0x7d5f64
    // 0x7d5eec: ldur            d0, [fp, #-0x38]
    // 0x7d5ef0: ldur            d3, [fp, #-0x28]
    // 0x7d5ef4: ldur            x1, [fp, #-0x20]
    // 0x7d5ef8: ldur            d2, [fp, #-0x30]
    // 0x7d5efc: d1 = 2.000000
    //     0x7d5efc: fmov            d1, #2.00000000
    // 0x7d5f00: LoadField: r2 = r1->field_b
    //     0x7d5f00: ldur            w2, [x1, #0xb]
    // 0x7d5f04: DecompressPointer r2
    //     0x7d5f04: add             x2, x2, HEAP, lsl #32
    // 0x7d5f08: ArrayLoad: d4 = r2[0]  ; List_8
    //     0x7d5f08: ldur            d4, [x2, #0x17]
    // 0x7d5f0c: fsub            d5, d4, d0
    // 0x7d5f10: fsub            d4, d5, d3
    // 0x7d5f14: fdiv            d3, d4, d1
    // 0x7d5f18: stur            d3, [fp, #-0x40]
    // 0x7d5f1c: LoadField: d1 = r2->field_7
    //     0x7d5f1c: ldur            d1, [x2, #7]
    // 0x7d5f20: fadd            d4, d1, d0
    // 0x7d5f24: fadd            d0, d4, d3
    // 0x7d5f28: stur            d0, [fp, #-0x28]
    // 0x7d5f2c: r0 = Offset()
    //     0x7d5f2c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d5f30: ldur            d0, [fp, #-0x30]
    // 0x7d5f34: StoreField: r0->field_7 = d0
    //     0x7d5f34: stur            d0, [x0, #7]
    // 0x7d5f38: ldur            d0, [fp, #-0x28]
    // 0x7d5f3c: StoreField: r0->field_f = d0
    //     0x7d5f3c: stur            d0, [x0, #0xf]
    // 0x7d5f40: ldur            x1, [fp, #-0x10]
    // 0x7d5f44: LoadField: r3 = r1->field_37
    //     0x7d5f44: ldur            w3, [x1, #0x37]
    // 0x7d5f48: DecompressPointer r3
    //     0x7d5f48: add             x3, x3, HEAP, lsl #32
    // 0x7d5f4c: cmp             w3, NULL
    // 0x7d5f50: b.eq            #0x7d5f7c
    // 0x7d5f54: ldur            x1, [fp, #-0x18]
    // 0x7d5f58: mov             x2, x0
    // 0x7d5f5c: ldur            d0, [fp, #-0x40]
    // 0x7d5f60: r0 = drawCircle()
    //     0x7d5f60: bl              #0x7d3ac4  ; [dart:ui] _NativeCanvas::drawCircle
    // 0x7d5f64: r0 = Null
    //     0x7d5f64: mov             x0, NULL
    // 0x7d5f68: LeaveFrame
    //     0x7d5f68: mov             SP, fp
    //     0x7d5f6c: ldp             fp, lr, [SP], #0x10
    // 0x7d5f70: ret
    //     0x7d5f70: ret             
    // 0x7d5f74: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d5f74: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d5f78: b               #0x7d5e10
    // 0x7d5f7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7d5f7c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _drawAfterLine(/* No info */) {
    // ** addr: 0x7d5f80, size: 0xc0
    // 0x7d5f80: EnterFrame
    //     0x7d5f80: stp             fp, lr, [SP, #-0x10]!
    //     0x7d5f84: mov             fp, SP
    // 0x7d5f88: AllocStack(0x38)
    //     0x7d5f88: sub             SP, SP, #0x38
    // 0x7d5f8c: SetupParameters(_TimelinePainter this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x30 */)
    //     0x7d5f8c: mov             x0, x1
    //     0x7d5f90: stur            x1, [fp, #-0x10]
    //     0x7d5f94: mov             x1, x2
    //     0x7d5f98: stur            x2, [fp, #-0x18]
    //     0x7d5f9c: stur            d0, [fp, #-0x30]
    // 0x7d5fa0: CheckStackOverflow
    //     0x7d5fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d5fa4: cmp             SP, x16
    //     0x7d5fa8: b.ls            #0x7d6038
    // 0x7d5fac: LoadField: r2 = r3->field_f
    //     0x7d5fac: ldur            w2, [x3, #0xf]
    // 0x7d5fb0: DecompressPointer r2
    //     0x7d5fb0: add             x2, x2, HEAP, lsl #32
    // 0x7d5fb4: stur            x2, [fp, #-8]
    // 0x7d5fb8: LoadField: d1 = r2->field_7
    //     0x7d5fb8: ldur            d1, [x2, #7]
    // 0x7d5fbc: stur            d1, [fp, #-0x28]
    // 0x7d5fc0: r0 = Offset()
    //     0x7d5fc0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d5fc4: ldur            d0, [fp, #-0x30]
    // 0x7d5fc8: stur            x0, [fp, #-0x20]
    // 0x7d5fcc: StoreField: r0->field_7 = d0
    //     0x7d5fcc: stur            d0, [x0, #7]
    // 0x7d5fd0: ldur            d1, [fp, #-0x28]
    // 0x7d5fd4: StoreField: r0->field_f = d1
    //     0x7d5fd4: stur            d1, [x0, #0xf]
    // 0x7d5fd8: ldur            x1, [fp, #-8]
    // 0x7d5fdc: LoadField: d2 = r1->field_f
    //     0x7d5fdc: ldur            d2, [x1, #0xf]
    // 0x7d5fe0: stur            d2, [fp, #-0x38]
    // 0x7d5fe4: r0 = Offset()
    //     0x7d5fe4: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d5fe8: ldur            d0, [fp, #-0x30]
    // 0x7d5fec: StoreField: r0->field_7 = d0
    //     0x7d5fec: stur            d0, [x0, #7]
    // 0x7d5ff0: ldur            d0, [fp, #-0x38]
    // 0x7d5ff4: StoreField: r0->field_f = d0
    //     0x7d5ff4: stur            d0, [x0, #0xf]
    // 0x7d5ff8: ldur            d1, [fp, #-0x28]
    // 0x7d5ffc: fsub            d2, d0, d1
    // 0x7d6000: d0 = 0.000000
    //     0x7d6000: eor             v0.16b, v0.16b, v0.16b
    // 0x7d6004: fcmp            d2, d0
    // 0x7d6008: b.le            #0x7d6028
    // 0x7d600c: ldur            x1, [fp, #-0x10]
    // 0x7d6010: LoadField: r5 = r1->field_33
    //     0x7d6010: ldur            w5, [x1, #0x33]
    // 0x7d6014: DecompressPointer r5
    //     0x7d6014: add             x5, x5, HEAP, lsl #32
    // 0x7d6018: ldur            x1, [fp, #-0x18]
    // 0x7d601c: ldur            x2, [fp, #-0x20]
    // 0x7d6020: mov             x3, x0
    // 0x7d6024: r0 = drawLine()
    //     0x7d6024: bl              #0x79712c  ; [dart:ui] _NativeCanvas::drawLine
    // 0x7d6028: r0 = Null
    //     0x7d6028: mov             x0, NULL
    // 0x7d602c: LeaveFrame
    //     0x7d602c: mov             SP, fp
    //     0x7d6030: ldp             fp, lr, [SP], #0x10
    // 0x7d6034: ret
    //     0x7d6034: ret             
    // 0x7d6038: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d6038: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d603c: b               #0x7d5fac
  }
  _ _drawBeforeLine(/* No info */) {
    // ** addr: 0x7d6040, size: 0xac
    // 0x7d6040: EnterFrame
    //     0x7d6040: stp             fp, lr, [SP, #-0x10]!
    //     0x7d6044: mov             fp, SP
    // 0x7d6048: AllocStack(0x30)
    //     0x7d6048: sub             SP, SP, #0x30
    // 0x7d604c: SetupParameters(_TimelinePainter this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x28 */)
    //     0x7d604c: mov             x0, x1
    //     0x7d6050: stur            x1, [fp, #-8]
    //     0x7d6054: mov             x1, x2
    //     0x7d6058: stur            x2, [fp, #-0x10]
    //     0x7d605c: stur            x3, [fp, #-0x18]
    //     0x7d6060: stur            d0, [fp, #-0x28]
    // 0x7d6064: CheckStackOverflow
    //     0x7d6064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d6068: cmp             SP, x16
    //     0x7d606c: b.ls            #0x7d60e4
    // 0x7d6070: r0 = Offset()
    //     0x7d6070: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d6074: ldur            d0, [fp, #-0x28]
    // 0x7d6078: stur            x0, [fp, #-0x20]
    // 0x7d607c: StoreField: r0->field_7 = d0
    //     0x7d607c: stur            d0, [x0, #7]
    // 0x7d6080: StoreField: r0->field_f = rZR
    //     0x7d6080: stur            xzr, [x0, #0xf]
    // 0x7d6084: ldur            x1, [fp, #-0x18]
    // 0x7d6088: LoadField: r2 = r1->field_7
    //     0x7d6088: ldur            w2, [x1, #7]
    // 0x7d608c: DecompressPointer r2
    //     0x7d608c: add             x2, x2, HEAP, lsl #32
    // 0x7d6090: LoadField: d1 = r2->field_f
    //     0x7d6090: ldur            d1, [x2, #0xf]
    // 0x7d6094: stur            d1, [fp, #-0x30]
    // 0x7d6098: r0 = Offset()
    //     0x7d6098: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d609c: ldur            d0, [fp, #-0x28]
    // 0x7d60a0: StoreField: r0->field_7 = d0
    //     0x7d60a0: stur            d0, [x0, #7]
    // 0x7d60a4: ldur            d0, [fp, #-0x30]
    // 0x7d60a8: StoreField: r0->field_f = d0
    //     0x7d60a8: stur            d0, [x0, #0xf]
    // 0x7d60ac: d1 = 0.000000
    //     0x7d60ac: eor             v1.16b, v1.16b, v1.16b
    // 0x7d60b0: fcmp            d0, d1
    // 0x7d60b4: b.le            #0x7d60d4
    // 0x7d60b8: ldur            x1, [fp, #-8]
    // 0x7d60bc: LoadField: r5 = r1->field_2f
    //     0x7d60bc: ldur            w5, [x1, #0x2f]
    // 0x7d60c0: DecompressPointer r5
    //     0x7d60c0: add             x5, x5, HEAP, lsl #32
    // 0x7d60c4: ldur            x1, [fp, #-0x10]
    // 0x7d60c8: ldur            x2, [fp, #-0x20]
    // 0x7d60cc: mov             x3, x0
    // 0x7d60d0: r0 = drawLine()
    //     0x7d60d0: bl              #0x79712c  ; [dart:ui] _NativeCanvas::drawLine
    // 0x7d60d4: r0 = Null
    //     0x7d60d4: mov             x0, NULL
    // 0x7d60d8: LeaveFrame
    //     0x7d60d8: mov             SP, fp
    //     0x7d60dc: ldp             fp, lr, [SP], #0x10
    // 0x7d60e0: ret
    //     0x7d60e0: ret             
    // 0x7d60e4: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d60e4: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d60e8: b               #0x7d6070
  }
  _ _drawSingleLine(/* No info */) {
    // ** addr: 0x7d60ec, size: 0x128
    // 0x7d60ec: EnterFrame
    //     0x7d60ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7d60f0: mov             fp, SP
    // 0x7d60f4: AllocStack(0x30)
    //     0x7d60f4: sub             SP, SP, #0x30
    // 0x7d60f8: SetupParameters(_TimelinePainter this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x28 */)
    //     0x7d60f8: mov             x0, x1
    //     0x7d60fc: stur            x1, [fp, #-8]
    //     0x7d6100: mov             x1, x2
    //     0x7d6104: stur            x2, [fp, #-0x10]
    //     0x7d6108: stur            x3, [fp, #-0x18]
    //     0x7d610c: stur            d0, [fp, #-0x28]
    // 0x7d6110: CheckStackOverflow
    //     0x7d6110: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d6114: cmp             SP, x16
    //     0x7d6118: b.ls            #0x7d620c
    // 0x7d611c: r0 = Offset()
    //     0x7d611c: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d6120: ldur            d0, [fp, #-0x28]
    // 0x7d6124: stur            x0, [fp, #-0x20]
    // 0x7d6128: StoreField: r0->field_7 = d0
    //     0x7d6128: stur            d0, [x0, #7]
    // 0x7d612c: StoreField: r0->field_f = rZR
    //     0x7d612c: stur            xzr, [x0, #0xf]
    // 0x7d6130: ldur            x1, [fp, #-0x18]
    // 0x7d6134: LoadField: r2 = r1->field_b
    //     0x7d6134: ldur            w2, [x1, #0xb]
    // 0x7d6138: DecompressPointer r2
    //     0x7d6138: add             x2, x2, HEAP, lsl #32
    // 0x7d613c: LoadField: d1 = r2->field_7
    //     0x7d613c: ldur            d1, [x2, #7]
    // 0x7d6140: ArrayLoad: d2 = r2[0]  ; List_8
    //     0x7d6140: ldur            d2, [x2, #0x17]
    // 0x7d6144: d3 = 2.000000
    //     0x7d6144: fmov            d3, #2.00000000
    // 0x7d6148: fdiv            d4, d2, d3
    // 0x7d614c: fadd            d2, d1, d4
    // 0x7d6150: stur            d2, [fp, #-0x30]
    // 0x7d6154: r0 = Offset()
    //     0x7d6154: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d6158: ldur            d0, [fp, #-0x28]
    // 0x7d615c: StoreField: r0->field_7 = d0
    //     0x7d615c: stur            d0, [x0, #7]
    // 0x7d6160: ldur            d1, [fp, #-0x30]
    // 0x7d6164: StoreField: r0->field_f = d1
    //     0x7d6164: stur            d1, [x0, #0xf]
    // 0x7d6168: ldur            x4, [fp, #-8]
    // 0x7d616c: LoadField: r5 = r4->field_2f
    //     0x7d616c: ldur            w5, [x4, #0x2f]
    // 0x7d6170: DecompressPointer r5
    //     0x7d6170: add             x5, x5, HEAP, lsl #32
    // 0x7d6174: ldur            x1, [fp, #-0x10]
    // 0x7d6178: ldur            x2, [fp, #-0x20]
    // 0x7d617c: mov             x3, x0
    // 0x7d6180: r0 = drawLine()
    //     0x7d6180: bl              #0x79712c  ; [dart:ui] _NativeCanvas::drawLine
    // 0x7d6184: ldur            x0, [fp, #-8]
    // 0x7d6188: LoadField: r1 = r0->field_43
    //     0x7d6188: ldur            w1, [x0, #0x43]
    // 0x7d618c: DecompressPointer r1
    //     0x7d618c: add             x1, x1, HEAP, lsl #32
    // 0x7d6190: tbz             w1, #4, #0x7d61fc
    // 0x7d6194: ldur            d0, [fp, #-0x28]
    // 0x7d6198: ldur            x1, [fp, #-0x18]
    // 0x7d619c: ldur            d1, [fp, #-0x30]
    // 0x7d61a0: r0 = Offset()
    //     0x7d61a0: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d61a4: ldur            d0, [fp, #-0x28]
    // 0x7d61a8: stur            x0, [fp, #-0x20]
    // 0x7d61ac: StoreField: r0->field_7 = d0
    //     0x7d61ac: stur            d0, [x0, #7]
    // 0x7d61b0: ldur            d1, [fp, #-0x30]
    // 0x7d61b4: StoreField: r0->field_f = d1
    //     0x7d61b4: stur            d1, [x0, #0xf]
    // 0x7d61b8: ldur            x1, [fp, #-0x18]
    // 0x7d61bc: LoadField: r2 = r1->field_f
    //     0x7d61bc: ldur            w2, [x1, #0xf]
    // 0x7d61c0: DecompressPointer r2
    //     0x7d61c0: add             x2, x2, HEAP, lsl #32
    // 0x7d61c4: LoadField: d1 = r2->field_f
    //     0x7d61c4: ldur            d1, [x2, #0xf]
    // 0x7d61c8: stur            d1, [fp, #-0x30]
    // 0x7d61cc: r0 = Offset()
    //     0x7d61cc: bl              #0x6188ec  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7d61d0: ldur            d0, [fp, #-0x28]
    // 0x7d61d4: StoreField: r0->field_7 = d0
    //     0x7d61d4: stur            d0, [x0, #7]
    // 0x7d61d8: ldur            d0, [fp, #-0x30]
    // 0x7d61dc: StoreField: r0->field_f = d0
    //     0x7d61dc: stur            d0, [x0, #0xf]
    // 0x7d61e0: ldur            x1, [fp, #-8]
    // 0x7d61e4: LoadField: r5 = r1->field_33
    //     0x7d61e4: ldur            w5, [x1, #0x33]
    // 0x7d61e8: DecompressPointer r5
    //     0x7d61e8: add             x5, x5, HEAP, lsl #32
    // 0x7d61ec: ldur            x1, [fp, #-0x10]
    // 0x7d61f0: ldur            x2, [fp, #-0x20]
    // 0x7d61f4: mov             x3, x0
    // 0x7d61f8: r0 = drawLine()
    //     0x7d61f8: bl              #0x79712c  ; [dart:ui] _NativeCanvas::drawLine
    // 0x7d61fc: r0 = Null
    //     0x7d61fc: mov             x0, NULL
    // 0x7d6200: LeaveFrame
    //     0x7d6200: mov             SP, fp
    //     0x7d6204: ldp             fp, lr, [SP], #0x10
    // 0x7d6208: ret
    //     0x7d6208: ret             
    // 0x7d620c: r0 = StackOverflowSharedWithFPURegs()
    //     0x7d620c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x7d6210: b               #0x7d611c
  }
  _ _TimelinePainter(/* No info */) {
    // ** addr: 0xbbc894, size: 0x214
    // 0xbbc894: EnterFrame
    //     0xbbc894: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc898: mov             fp, SP
    // 0xbbc89c: AllocStack(0x48)
    //     0xbbc89c: sub             SP, SP, #0x48
    // 0xbbc8a0: r4 = Instance_TimelineAxis
    //     0xbbc8a0: add             x4, PP, #0x40, lsl #12  ; [pp+0x40320] Obj!TimelineAxis@e2df21
    //     0xbbc8a4: ldr             x4, [x4, #0x320]
    // 0xbbc8a8: r0 = false
    //     0xbbc8a8: add             x0, NULL, #0x30  ; false
    // 0xbbc8ac: stur            x1, [fp, #-8]
    // 0xbbc8b0: stur            x2, [fp, #-0x10]
    // 0xbbc8b4: stur            x3, [fp, #-0x18]
    // 0xbbc8b8: stur            x5, [fp, #-0x20]
    // 0xbbc8bc: stur            x7, [fp, #-0x28]
    // 0xbbc8c0: CheckStackOverflow
    //     0xbbc8c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbc8c4: cmp             SP, x16
    //     0xbbc8c8: b.ls            #0xbbcaa0
    // 0xbbc8cc: StoreField: r1->field_b = r4
    //     0xbbc8cc: stur            w4, [x1, #0xb]
    // 0xbbc8d0: StoreField: r1->field_3b = r7
    //     0xbbc8d0: stur            w7, [x1, #0x3b]
    // 0xbbc8d4: StoreField: r1->field_3f = r0
    //     0xbbc8d4: stur            w0, [x1, #0x3f]
    // 0xbbc8d8: StoreField: r1->field_43 = r6
    //     0xbbc8d8: stur            w6, [x1, #0x43]
    // 0xbbc8dc: r16 = 136
    //     0xbbc8dc: movz            x16, #0x88
    // 0xbbc8e0: stp             x16, NULL, [SP]
    // 0xbbc8e4: r0 = ByteData()
    //     0xbbc8e4: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xbbc8e8: stur            x0, [fp, #-0x30]
    // 0xbbc8ec: r0 = Paint()
    //     0xbbc8ec: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xbbc8f0: mov             x3, x0
    // 0xbbc8f4: ldur            x0, [fp, #-0x30]
    // 0xbbc8f8: stur            x3, [fp, #-0x38]
    // 0xbbc8fc: StoreField: r3->field_7 = r0
    //     0xbbc8fc: stur            w0, [x3, #7]
    // 0xbbc900: ldur            x4, [fp, #-0x18]
    // 0xbbc904: LoadField: r2 = r4->field_7
    //     0xbbc904: ldur            w2, [x4, #7]
    // 0xbbc908: DecompressPointer r2
    //     0xbbc908: add             x2, x2, HEAP, lsl #32
    // 0xbbc90c: mov             x1, x3
    // 0xbbc910: r0 = color=()
    //     0xbbc910: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0xbbc914: ldur            x0, [fp, #-0x18]
    // 0xbbc918: LoadField: d0 = r0->field_b
    //     0xbbc918: ldur            d0, [x0, #0xb]
    // 0xbbc91c: ldur            x0, [fp, #-0x30]
    // 0xbbc920: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbc920: ldur            w1, [x0, #0x17]
    // 0xbbc924: DecompressPointer r1
    //     0xbbc924: add             x1, x1, HEAP, lsl #32
    // 0xbbc928: fcvt            s1, d0
    // 0xbbc92c: LoadField: r0 = r1->field_7
    //     0xbbc92c: ldur            x0, [x1, #7]
    // 0xbbc930: str             s1, [x0, #0x20]
    // 0xbbc934: ldur            x0, [fp, #-0x38]
    // 0xbbc938: ldur            x1, [fp, #-8]
    // 0xbbc93c: StoreField: r1->field_2f = r0
    //     0xbbc93c: stur            w0, [x1, #0x2f]
    //     0xbbc940: ldurb           w16, [x1, #-1]
    //     0xbbc944: ldurb           w17, [x0, #-1]
    //     0xbbc948: and             x16, x17, x16, lsr #2
    //     0xbbc94c: tst             x16, HEAP, lsr #32
    //     0xbbc950: b.eq            #0xbbc958
    //     0xbbc954: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbbc958: r16 = 136
    //     0xbbc958: movz            x16, #0x88
    // 0xbbc95c: stp             x16, NULL, [SP]
    // 0xbbc960: r0 = ByteData()
    //     0xbbc960: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xbbc964: stur            x0, [fp, #-0x18]
    // 0xbbc968: r0 = Paint()
    //     0xbbc968: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xbbc96c: mov             x3, x0
    // 0xbbc970: ldur            x0, [fp, #-0x18]
    // 0xbbc974: stur            x3, [fp, #-0x30]
    // 0xbbc978: StoreField: r3->field_7 = r0
    //     0xbbc978: stur            w0, [x3, #7]
    // 0xbbc97c: ldur            x4, [fp, #-0x10]
    // 0xbbc980: LoadField: r2 = r4->field_7
    //     0xbbc980: ldur            w2, [x4, #7]
    // 0xbbc984: DecompressPointer r2
    //     0xbbc984: add             x2, x2, HEAP, lsl #32
    // 0xbbc988: mov             x1, x3
    // 0xbbc98c: r0 = color=()
    //     0xbbc98c: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0xbbc990: ldur            x0, [fp, #-0x10]
    // 0xbbc994: LoadField: d0 = r0->field_b
    //     0xbbc994: ldur            d0, [x0, #0xb]
    // 0xbbc998: ldur            x0, [fp, #-0x18]
    // 0xbbc99c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbc99c: ldur            w1, [x0, #0x17]
    // 0xbbc9a0: DecompressPointer r1
    //     0xbbc9a0: add             x1, x1, HEAP, lsl #32
    // 0xbbc9a4: fcvt            s1, d0
    // 0xbbc9a8: LoadField: r0 = r1->field_7
    //     0xbbc9a8: ldur            x0, [x1, #7]
    // 0xbbc9ac: str             s1, [x0, #0x20]
    // 0xbbc9b0: ldur            x0, [fp, #-0x30]
    // 0xbbc9b4: ldur            x1, [fp, #-8]
    // 0xbbc9b8: StoreField: r1->field_33 = r0
    //     0xbbc9b8: stur            w0, [x1, #0x33]
    //     0xbbc9bc: ldurb           w16, [x1, #-1]
    //     0xbbc9c0: ldurb           w17, [x0, #-1]
    //     0xbbc9c4: and             x16, x17, x16, lsr #2
    //     0xbbc9c8: tst             x16, HEAP, lsr #32
    //     0xbbc9cc: b.eq            #0xbbc9d4
    //     0xbbc9d0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbbc9d4: ldur            x0, [fp, #-0x28]
    // 0xbbc9d8: tbz             w0, #4, #0xbbc9ec
    // 0xbbc9dc: mov             x2, x1
    // 0xbbc9e0: mov             x3, x0
    // 0xbbc9e4: r0 = Null
    //     0xbbc9e4: mov             x0, NULL
    // 0xbbc9e8: b               #0xbbca34
    // 0xbbc9ec: ldur            x2, [fp, #-0x20]
    // 0xbbc9f0: r16 = 136
    //     0xbbc9f0: movz            x16, #0x88
    // 0xbbc9f4: stp             x16, NULL, [SP]
    // 0xbbc9f8: r0 = ByteData()
    //     0xbbc9f8: bl              #0x617768  ; [dart:typed_data] ByteData::ByteData
    // 0xbbc9fc: stur            x0, [fp, #-0x10]
    // 0xbbca00: r0 = Paint()
    //     0xbbca00: bl              #0x68330c  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xbbca04: mov             x3, x0
    // 0xbbca08: ldur            x0, [fp, #-0x10]
    // 0xbbca0c: stur            x3, [fp, #-0x18]
    // 0xbbca10: StoreField: r3->field_7 = r0
    //     0xbbca10: stur            w0, [x3, #7]
    // 0xbbca14: ldur            x0, [fp, #-0x20]
    // 0xbbca18: LoadField: r2 = r0->field_1f
    //     0xbbca18: ldur            w2, [x0, #0x1f]
    // 0xbbca1c: DecompressPointer r2
    //     0xbbca1c: add             x2, x2, HEAP, lsl #32
    // 0xbbca20: mov             x1, x3
    // 0xbbca24: r0 = color=()
    //     0xbbca24: bl              #0x683114  ; [dart:ui] Paint::color=
    // 0xbbca28: ldur            x0, [fp, #-0x18]
    // 0xbbca2c: ldur            x2, [fp, #-8]
    // 0xbbca30: ldur            x3, [fp, #-0x28]
    // 0xbbca34: ldur            x1, [fp, #-0x20]
    // 0xbbca38: StoreField: r2->field_37 = r0
    //     0xbbca38: stur            w0, [x2, #0x37]
    //     0xbbca3c: ldurb           w16, [x2, #-1]
    //     0xbbca40: ldurb           w17, [x0, #-1]
    //     0xbbca44: and             x16, x17, x16, lsr #2
    //     0xbbca48: tst             x16, HEAP, lsr #32
    //     0xbbca4c: b.eq            #0xbbca54
    //     0xbbca50: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xbbca54: LoadField: d0 = r1->field_27
    //     0xbbca54: ldur            d0, [x1, #0x27]
    // 0xbbca58: StoreField: r2->field_f = d0
    //     0xbbca58: stur            d0, [x2, #0xf]
    // 0xbbca5c: tbnz            w3, #4, #0xbbca68
    // 0xbbca60: LoadField: d0 = r1->field_7
    //     0xbbca60: ldur            d0, [x1, #7]
    // 0xbbca64: b               #0xbbca6c
    // 0xbbca68: d0 = 0.000000
    //     0xbbca68: eor             v0.16b, v0.16b, v0.16b
    // 0xbbca6c: r3 = false
    //     0xbbca6c: add             x3, NULL, #0x30  ; false
    // 0xbbca70: StoreField: r2->field_27 = d0
    //     0xbbca70: stur            d0, [x2, #0x27]
    // 0xbbca74: LoadField: r4 = r1->field_1b
    //     0xbbca74: ldur            w4, [x1, #0x1b]
    // 0xbbca78: DecompressPointer r4
    //     0xbbca78: add             x4, x4, HEAP, lsl #32
    // 0xbbca7c: LoadField: d0 = r4->field_f
    //     0xbbca7c: ldur            d0, [x4, #0xf]
    // 0xbbca80: ArrayStore: r2[0] = d0  ; List_8
    //     0xbbca80: stur            d0, [x2, #0x17]
    // 0xbbca84: LoadField: d0 = r4->field_1f
    //     0xbbca84: ldur            d0, [x4, #0x1f]
    // 0xbbca88: StoreField: r2->field_1f = d0
    //     0xbbca88: stur            d0, [x2, #0x1f]
    // 0xbbca8c: StoreField: r2->field_47 = r3
    //     0xbbca8c: stur            w3, [x2, #0x47]
    // 0xbbca90: r0 = Null
    //     0xbbca90: mov             x0, NULL
    // 0xbbca94: LeaveFrame
    //     0xbbca94: mov             SP, fp
    //     0xbbca98: ldp             fp, lr, [SP], #0x10
    // 0xbbca9c: ret
    //     0xbbca9c: ret             
    // 0xbbcaa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbcaa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbcaa4: b               #0xbbc8cc
  }
}

// class id: 6764, size: 0x14, field offset: 0x14
enum TimelineAlign extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ec0c, size: 0x64
    // 0xc4ec0c: EnterFrame
    //     0xc4ec0c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ec10: mov             fp, SP
    // 0xc4ec14: AllocStack(0x10)
    //     0xc4ec14: sub             SP, SP, #0x10
    // 0xc4ec18: SetupParameters(TimelineAlign this /* r1 => r0, fp-0x8 */)
    //     0xc4ec18: mov             x0, x1
    //     0xc4ec1c: stur            x1, [fp, #-8]
    // 0xc4ec20: CheckStackOverflow
    //     0xc4ec20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ec24: cmp             SP, x16
    //     0xc4ec28: b.ls            #0xc4ec68
    // 0xc4ec2c: r1 = Null
    //     0xc4ec2c: mov             x1, NULL
    // 0xc4ec30: r2 = 4
    //     0xc4ec30: movz            x2, #0x4
    // 0xc4ec34: r0 = AllocateArray()
    //     0xc4ec34: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ec38: r16 = "TimelineAlign."
    //     0xc4ec38: add             x16, PP, #0x47, lsl #12  ; [pp+0x47ac8] "TimelineAlign."
    //     0xc4ec3c: ldr             x16, [x16, #0xac8]
    // 0xc4ec40: StoreField: r0->field_f = r16
    //     0xc4ec40: stur            w16, [x0, #0xf]
    // 0xc4ec44: ldur            x1, [fp, #-8]
    // 0xc4ec48: LoadField: r2 = r1->field_f
    //     0xc4ec48: ldur            w2, [x1, #0xf]
    // 0xc4ec4c: DecompressPointer r2
    //     0xc4ec4c: add             x2, x2, HEAP, lsl #32
    // 0xc4ec50: StoreField: r0->field_13 = r2
    //     0xc4ec50: stur            w2, [x0, #0x13]
    // 0xc4ec54: str             x0, [SP]
    // 0xc4ec58: r0 = _interpolate()
    //     0xc4ec58: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ec5c: LeaveFrame
    //     0xc4ec5c: mov             SP, fp
    //     0xc4ec60: ldp             fp, lr, [SP], #0x10
    // 0xc4ec64: ret
    //     0xc4ec64: ret             
    // 0xc4ec68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ec68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ec6c: b               #0xc4ec2c
  }
}

// class id: 6765, size: 0x14, field offset: 0x14
enum TimelineAxis extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4eba8, size: 0x64
    // 0xc4eba8: EnterFrame
    //     0xc4eba8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ebac: mov             fp, SP
    // 0xc4ebb0: AllocStack(0x10)
    //     0xc4ebb0: sub             SP, SP, #0x10
    // 0xc4ebb4: SetupParameters(TimelineAxis this /* r1 => r0, fp-0x8 */)
    //     0xc4ebb4: mov             x0, x1
    //     0xc4ebb8: stur            x1, [fp, #-8]
    // 0xc4ebbc: CheckStackOverflow
    //     0xc4ebbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ebc0: cmp             SP, x16
    //     0xc4ebc4: b.ls            #0xc4ec04
    // 0xc4ebc8: r1 = Null
    //     0xc4ebc8: mov             x1, NULL
    // 0xc4ebcc: r2 = 4
    //     0xc4ebcc: movz            x2, #0x4
    // 0xc4ebd0: r0 = AllocateArray()
    //     0xc4ebd0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ebd4: r16 = "TimelineAxis."
    //     0xc4ebd4: add             x16, PP, #0x47, lsl #12  ; [pp+0x47ad0] "TimelineAxis."
    //     0xc4ebd8: ldr             x16, [x16, #0xad0]
    // 0xc4ebdc: StoreField: r0->field_f = r16
    //     0xc4ebdc: stur            w16, [x0, #0xf]
    // 0xc4ebe0: ldur            x1, [fp, #-8]
    // 0xc4ebe4: LoadField: r2 = r1->field_f
    //     0xc4ebe4: ldur            w2, [x1, #0xf]
    // 0xc4ebe8: DecompressPointer r2
    //     0xc4ebe8: add             x2, x2, HEAP, lsl #32
    // 0xc4ebec: StoreField: r0->field_13 = r2
    //     0xc4ebec: stur            w2, [x0, #0x13]
    // 0xc4ebf0: str             x0, [SP]
    // 0xc4ebf4: r0 = _interpolate()
    //     0xc4ebf4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ebf8: LeaveFrame
    //     0xc4ebf8: mov             SP, fp
    //     0xc4ebfc: ldp             fp, lr, [SP], #0x10
    // 0xc4ec00: ret
    //     0xc4ec00: ret             
    // 0xc4ec04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ec04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ec08: b               #0xc4ebc8
  }
}
