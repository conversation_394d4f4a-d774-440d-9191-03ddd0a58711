// lib: , url: package:timezone/data/latest.dart

// class id: 1051207, size: 0x8
class :: {

  static void initializeTimeZones() {
    // ** addr: 0x9110c8, size: 0xc8
    // 0x9110c8: EnterFrame
    //     0x9110c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9110cc: mov             fp, SP
    // 0x9110d0: AllocStack(0x38)
    //     0x9110d0: sub             SP, SP, #0x38
    // 0x9110d4: CheckStackOverflow
    //     0x9110d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9110d8: cmp             SP, x16
    //     0x9110dc: b.ls            #0x911188
    // 0x9110e0: r1 = <int>
    //     0x9110e0: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x9110e4: r0 = CodeUnits()
    //     0x9110e4: bl              #0x705f70  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0x9110e8: mov             x1, x0
    // 0x9110ec: r0 = ""
    //     0x9110ec: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ad80] ""
    //     0x9110f0: ldr             x0, [x0, #0xd80]
    // 0x9110f4: StoreField: r1->field_b = r0
    //     0x9110f4: stur            w0, [x1, #0xb]
    // 0x9110f8: mov             x2, x1
    // 0x9110fc: r1 = Null
    //     0x9110fc: mov             x1, NULL
    // 0x911100: r0 = Uint16List.fromList()
    //     0x911100: bl              #0x912668  ; [dart:typed_data] Uint16List::Uint16List.fromList
    // 0x911104: stur            x0, [fp, #-0x30]
    // 0x911108: r0 = _ByteBuffer()
    //     0x911108: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x91110c: mov             x1, x0
    // 0x911110: ldur            x0, [fp, #-0x30]
    // 0x911114: StoreField: r1->field_7 = r0
    //     0x911114: stur            w0, [x1, #7]
    // 0x911118: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x911118: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91111c: r0 = asUint8List()
    //     0x91111c: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0x911120: mov             x1, x0
    // 0x911124: r0 = initializeDatabase()
    //     0x911124: bl              #0x91119c  ; [package:timezone/src/env.dart] ::initializeDatabase
    // 0x911128: r0 = Null
    //     0x911128: mov             x0, NULL
    // 0x91112c: LeaveFrame
    //     0x91112c: mov             SP, fp
    //     0x911130: ldp             fp, lr, [SP], #0x10
    // 0x911134: ret
    //     0x911134: ret             
    // 0x911138: sub             SP, fp, #0x38
    // 0x91113c: r1 = 60
    //     0x91113c: movz            x1, #0x3c
    // 0x911140: branchIfSmi(r0, 0x91114c)
    //     0x911140: tbz             w0, #0, #0x91114c
    // 0x911144: r1 = LoadClassIdInstr(r0)
    //     0x911144: ldur            x1, [x0, #-1]
    //     0x911148: ubfx            x1, x1, #0xc, #0x14
    // 0x91114c: str             x0, [SP]
    // 0x911150: mov             x0, x1
    // 0x911154: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x911154: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x911158: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x911158: movz            x17, #0x2b03
    //     0x91115c: add             lr, x0, x17
    //     0x911160: ldr             lr, [x21, lr, lsl #3]
    //     0x911164: blr             lr
    // 0x911168: stur            x0, [fp, #-0x30]
    // 0x91116c: r0 = TimeZoneInitException()
    //     0x91116c: bl              #0x911190  ; AllocateTimeZoneInitExceptionStub -> TimeZoneInitException (size=0xc)
    // 0x911170: mov             x1, x0
    // 0x911174: ldur            x0, [fp, #-0x30]
    // 0x911178: StoreField: r1->field_7 = r0
    //     0x911178: stur            w0, [x1, #7]
    // 0x91117c: mov             x0, x1
    // 0x911180: r0 = Throw()
    //     0x911180: bl              #0xec04b8  ; ThrowStub
    // 0x911184: brk             #0
    // 0x911188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x911188: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91118c: b               #0x9110e0
  }
}
