// lib: , url: package:timezone/src/date_time.dart

// class id: 1051208, size: 0x8
class :: {
}

// class id: 430, size: 0x18, field offset: 0x8
class TZDateTime extends Object
    implements DateTime {

  _ compareTo(/* No info */) {
    // ** addr: 0x6d655c, size: 0x54
    // 0x6d655c: EnterFrame
    //     0x6d655c: stp             fp, lr, [SP, #-0x10]!
    //     0x6d6560: mov             fp, SP
    // 0x6d6564: CheckStackOverflow
    //     0x6d6564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6d6568: cmp             SP, x16
    //     0x6d656c: b.ls            #0x6d65a8
    // 0x6d6570: LoadField: r0 = r1->field_b
    //     0x6d6570: ldur            w0, [x1, #0xb]
    // 0x6d6574: DecompressPointer r0
    //     0x6d6574: add             x0, x0, HEAP, lsl #32
    // 0x6d6578: r1 = LoadClassIdInstr(r2)
    //     0x6d6578: ldur            x1, [x2, #-1]
    //     0x6d657c: ubfx            x1, x1, #0xc, #0x14
    // 0x6d6580: cmp             x1, #0x1ae
    // 0x6d6584: b.ne            #0x6d6594
    // 0x6d6588: LoadField: r1 = r2->field_b
    //     0x6d6588: ldur            w1, [x2, #0xb]
    // 0x6d658c: DecompressPointer r1
    //     0x6d658c: add             x1, x1, HEAP, lsl #32
    // 0x6d6590: mov             x2, x1
    // 0x6d6594: mov             x1, x0
    // 0x6d6598: r0 = compareTo()
    //     0x6d6598: bl              #0x665dbc  ; [dart:core] DateTime::compareTo
    // 0x6d659c: LeaveFrame
    //     0x6d659c: mov             SP, fp
    //     0x6d65a0: ldp             fp, lr, [SP], #0x10
    // 0x6d65a4: ret
    //     0x6d65a4: ret             
    // 0x6d65a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6d65a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6d65ac: b               #0x6d6570
  }
  _ TZDateTime.from(/* No info */) {
    // ** addr: 0x6fd2cc, size: 0xcc
    // 0x6fd2cc: EnterFrame
    //     0x6fd2cc: stp             fp, lr, [SP, #-0x10]!
    //     0x6fd2d0: mov             fp, SP
    // 0x6fd2d4: AllocStack(0x20)
    //     0x6fd2d4: sub             SP, SP, #0x20
    // 0x6fd2d8: SetupParameters(TZDateTime this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x6fd2d8: mov             x0, x3
    //     0x6fd2dc: stur            x3, [fp, #-0x18]
    //     0x6fd2e0: mov             x3, x1
    //     0x6fd2e4: stur            x1, [fp, #-8]
    //     0x6fd2e8: stur            x2, [fp, #-0x10]
    // 0x6fd2ec: CheckStackOverflow
    //     0x6fd2ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd2f0: cmp             SP, x16
    //     0x6fd2f4: b.ls            #0x6fd390
    // 0x6fd2f8: mov             x1, x2
    // 0x6fd2fc: r0 = toUtc()
    //     0x6fd2fc: bl              #0xd5e984  ; [dart:core] DateTime::toUtc
    // 0x6fd300: stur            x0, [fp, #-0x20]
    // 0x6fd304: r0 = InitLateStaticField(0x129c) // [package:timezone/src/env.dart] ::_UTC
    //     0x6fd304: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fd308: ldr             x0, [x0, #0x2538]
    //     0x6fd30c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fd310: cmp             w0, w16
    //     0x6fd314: b.ne            #0x6fd324
    //     0x6fd318: add             x2, PP, #8, lsl #12  ; [pp+0x87a0] Field <::._UTC@1194310200>: static late final (offset: 0x129c)
    //     0x6fd31c: ldr             x2, [x2, #0x7a0]
    //     0x6fd320: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fd324: mov             x1, x0
    // 0x6fd328: ldur            x0, [fp, #-0x18]
    // 0x6fd32c: cmp             w0, w1
    // 0x6fd330: b.ne            #0x6fd340
    // 0x6fd334: r5 = Instance_TimeZone
    //     0x6fd334: add             x5, PP, #8, lsl #12  ; [pp+0x87b0] Obj!TimeZone@e0bf11
    //     0x6fd338: ldr             x5, [x5, #0x7b0]
    // 0x6fd33c: b               #0x6fd370
    // 0x6fd340: ldur            x1, [fp, #-0x10]
    // 0x6fd344: LoadField: r2 = r1->field_7
    //     0x6fd344: ldur            x2, [x1, #7]
    // 0x6fd348: tbz             x2, #0x3f, #0x6fd354
    // 0x6fd34c: r3 = 999
    //     0x6fd34c: movz            x3, #0x3e7
    // 0x6fd350: b               #0x6fd358
    // 0x6fd354: r3 = 0
    //     0x6fd354: movz            x3, #0
    // 0x6fd358: r1 = 1000
    //     0x6fd358: movz            x1, #0x3e8
    // 0x6fd35c: sub             x4, x2, x3
    // 0x6fd360: sdiv            x2, x4, x1
    // 0x6fd364: mov             x1, x0
    // 0x6fd368: r0 = timeZone()
    //     0x6fd368: bl              #0x6fd4ec  ; [package:timezone/src/location.dart] Location::timeZone
    // 0x6fd36c: mov             x5, x0
    // 0x6fd370: ldur            x1, [fp, #-8]
    // 0x6fd374: ldur            x2, [fp, #-0x20]
    // 0x6fd378: ldur            x3, [fp, #-0x18]
    // 0x6fd37c: r0 = TZDateTime._()
    //     0x6fd37c: bl              #0x6fd398  ; [package:timezone/src/date_time.dart] TZDateTime::TZDateTime._
    // 0x6fd380: r0 = Null
    //     0x6fd380: mov             x0, NULL
    // 0x6fd384: LeaveFrame
    //     0x6fd384: mov             SP, fp
    //     0x6fd388: ldp             fp, lr, [SP], #0x10
    // 0x6fd38c: ret
    //     0x6fd38c: ret             
    // 0x6fd390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fd390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fd394: b               #0x6fd2f8
  }
  _ TZDateTime._(/* No info */) {
    // ** addr: 0x6fd398, size: 0x120
    // 0x6fd398: EnterFrame
    //     0x6fd398: stp             fp, lr, [SP, #-0x10]!
    //     0x6fd39c: mov             fp, SP
    // 0x6fd3a0: AllocStack(0x20)
    //     0x6fd3a0: sub             SP, SP, #0x20
    // 0x6fd3a4: SetupParameters(TZDateTime this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0x6fd3a4: mov             x4, x1
    //     0x6fd3a8: stur            x2, [fp, #-0x10]
    //     0x6fd3ac: mov             x16, x3
    //     0x6fd3b0: mov             x3, x2
    //     0x6fd3b4: mov             x2, x16
    //     0x6fd3b8: stur            x1, [fp, #-8]
    //     0x6fd3bc: mov             x1, x5
    //     0x6fd3c0: stur            x2, [fp, #-0x18]
    //     0x6fd3c4: stur            x5, [fp, #-0x20]
    // 0x6fd3c8: CheckStackOverflow
    //     0x6fd3c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd3cc: cmp             SP, x16
    //     0x6fd3d0: b.ls            #0x6fd4b0
    // 0x6fd3d4: mov             x0, x2
    // 0x6fd3d8: StoreField: r4->field_f = r0
    //     0x6fd3d8: stur            w0, [x4, #0xf]
    //     0x6fd3dc: ldurb           w16, [x4, #-1]
    //     0x6fd3e0: ldurb           w17, [x0, #-1]
    //     0x6fd3e4: and             x16, x17, x16, lsr #2
    //     0x6fd3e8: tst             x16, HEAP, lsr #32
    //     0x6fd3ec: b.eq            #0x6fd3f4
    //     0x6fd3f0: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x6fd3f4: mov             x0, x1
    // 0x6fd3f8: StoreField: r4->field_13 = r0
    //     0x6fd3f8: stur            w0, [x4, #0x13]
    //     0x6fd3fc: ldurb           w16, [x4, #-1]
    //     0x6fd400: ldurb           w17, [x0, #-1]
    //     0x6fd404: and             x16, x17, x16, lsr #2
    //     0x6fd408: tst             x16, HEAP, lsr #32
    //     0x6fd40c: b.eq            #0x6fd414
    //     0x6fd410: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x6fd414: mov             x0, x3
    // 0x6fd418: StoreField: r4->field_b = r0
    //     0x6fd418: stur            w0, [x4, #0xb]
    //     0x6fd41c: ldurb           w16, [x4, #-1]
    //     0x6fd420: ldurb           w17, [x0, #-1]
    //     0x6fd424: and             x16, x17, x16, lsr #2
    //     0x6fd428: tst             x16, HEAP, lsr #32
    //     0x6fd42c: b.eq            #0x6fd434
    //     0x6fd430: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x6fd434: r0 = InitLateStaticField(0x129c) // [package:timezone/src/env.dart] ::_UTC
    //     0x6fd434: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fd438: ldr             x0, [x0, #0x2538]
    //     0x6fd43c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fd440: cmp             w0, w16
    //     0x6fd444: b.ne            #0x6fd454
    //     0x6fd448: add             x2, PP, #8, lsl #12  ; [pp+0x87a0] Field <::._UTC@1194310200>: static late final (offset: 0x129c)
    //     0x6fd44c: ldr             x2, [x2, #0x7a0]
    //     0x6fd450: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fd454: mov             x1, x0
    // 0x6fd458: ldur            x0, [fp, #-0x18]
    // 0x6fd45c: cmp             w0, w1
    // 0x6fd460: b.ne            #0x6fd46c
    // 0x6fd464: ldur            x0, [fp, #-0x10]
    // 0x6fd468: b               #0x6fd480
    // 0x6fd46c: ldur            x1, [fp, #-0x20]
    // 0x6fd470: r0 = _timeZoneOffset()
    //     0x6fd470: bl              #0x6fd4b8  ; [package:timezone/src/date_time.dart] TZDateTime::_timeZoneOffset
    // 0x6fd474: ldur            x1, [fp, #-0x10]
    // 0x6fd478: mov             x2, x0
    // 0x6fd47c: r0 = add()
    //     0x6fd47c: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0x6fd480: ldur            x1, [fp, #-8]
    // 0x6fd484: StoreField: r1->field_7 = r0
    //     0x6fd484: stur            w0, [x1, #7]
    //     0x6fd488: ldurb           w16, [x1, #-1]
    //     0x6fd48c: ldurb           w17, [x0, #-1]
    //     0x6fd490: and             x16, x17, x16, lsr #2
    //     0x6fd494: tst             x16, HEAP, lsr #32
    //     0x6fd498: b.eq            #0x6fd4a0
    //     0x6fd49c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x6fd4a0: r0 = Null
    //     0x6fd4a0: mov             x0, NULL
    // 0x6fd4a4: LeaveFrame
    //     0x6fd4a4: mov             SP, fp
    //     0x6fd4a8: ldp             fp, lr, [SP], #0x10
    // 0x6fd4ac: ret
    //     0x6fd4ac: ret             
    // 0x6fd4b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fd4b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fd4b4: b               #0x6fd3d4
  }
  static _ _timeZoneOffset(/* No info */) {
    // ** addr: 0x6fd4b8, size: 0x34
    // 0x6fd4b8: EnterFrame
    //     0x6fd4b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6fd4bc: mov             fp, SP
    // 0x6fd4c0: AllocStack(0x8)
    //     0x6fd4c0: sub             SP, SP, #8
    // 0x6fd4c4: LoadField: r0 = r1->field_7
    //     0x6fd4c4: ldur            x0, [x1, #7]
    // 0x6fd4c8: r16 = 1000
    //     0x6fd4c8: movz            x16, #0x3e8
    // 0x6fd4cc: mul             x1, x0, x16
    // 0x6fd4d0: stur            x1, [fp, #-8]
    // 0x6fd4d4: r0 = Duration()
    //     0x6fd4d4: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6fd4d8: ldur            x1, [fp, #-8]
    // 0x6fd4dc: StoreField: r0->field_7 = r1
    //     0x6fd4dc: stur            x1, [x0, #7]
    // 0x6fd4e0: LeaveFrame
    //     0x6fd4e0: mov             SP, fp
    //     0x6fd4e4: ldp             fp, lr, [SP], #0x10
    // 0x6fd4e8: ret
    //     0x6fd4e8: ret             
  }
  static _ parse(/* No info */) {
    // ** addr: 0x7ea0c0, size: 0x5c
    // 0x7ea0c0: EnterFrame
    //     0x7ea0c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7ea0c4: mov             fp, SP
    // 0x7ea0c8: AllocStack(0x10)
    //     0x7ea0c8: sub             SP, SP, #0x10
    // 0x7ea0cc: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x7ea0cc: mov             x3, x1
    //     0x7ea0d0: stur            x1, [fp, #-8]
    //     0x7ea0d4: mov             x1, x2
    // 0x7ea0d8: CheckStackOverflow
    //     0x7ea0d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea0dc: cmp             SP, x16
    //     0x7ea0e0: b.ls            #0x7ea114
    // 0x7ea0e4: r0 = parse()
    //     0x7ea0e4: bl              #0x6fe1fc  ; [dart:core] DateTime::parse
    // 0x7ea0e8: stur            x0, [fp, #-0x10]
    // 0x7ea0ec: r0 = TZDateTime()
    //     0x7ea0ec: bl              #0x6fdf18  ; AllocateTZDateTimeStub -> TZDateTime (size=0x18)
    // 0x7ea0f0: mov             x1, x0
    // 0x7ea0f4: ldur            x2, [fp, #-0x10]
    // 0x7ea0f8: ldur            x3, [fp, #-8]
    // 0x7ea0fc: stur            x0, [fp, #-8]
    // 0x7ea100: r0 = TZDateTime.from()
    //     0x7ea100: bl              #0x6fd2cc  ; [package:timezone/src/date_time.dart] TZDateTime::TZDateTime.from
    // 0x7ea104: ldur            x0, [fp, #-8]
    // 0x7ea108: LeaveFrame
    //     0x7ea108: mov             SP, fp
    //     0x7ea10c: ldp             fp, lr, [SP], #0x10
    // 0x7ea110: ret
    //     0x7ea110: ret             
    // 0x7ea114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ea114: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ea118: b               #0x7ea0e4
  }
  get _ timeZoneOffset(/* No info */) {
    // ** addr: 0x81abf0, size: 0x38
    // 0x81abf0: EnterFrame
    //     0x81abf0: stp             fp, lr, [SP, #-0x10]!
    //     0x81abf4: mov             fp, SP
    // 0x81abf8: CheckStackOverflow
    //     0x81abf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x81abfc: cmp             SP, x16
    //     0x81ac00: b.ls            #0x81ac20
    // 0x81ac04: LoadField: r0 = r1->field_13
    //     0x81ac04: ldur            w0, [x1, #0x13]
    // 0x81ac08: DecompressPointer r0
    //     0x81ac08: add             x0, x0, HEAP, lsl #32
    // 0x81ac0c: mov             x1, x0
    // 0x81ac10: r0 = _timeZoneOffset()
    //     0x81ac10: bl              #0x6fd4b8  ; [package:timezone/src/date_time.dart] TZDateTime::_timeZoneOffset
    // 0x81ac14: LeaveFrame
    //     0x81ac14: mov             SP, fp
    //     0x81ac18: ldp             fp, lr, [SP], #0x10
    // 0x81ac1c: ret
    //     0x81ac1c: ret             
    // 0x81ac20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81ac20: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x81ac24: b               #0x81ac04
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3ba4, size: 0x40
    // 0xbf3ba4: EnterFrame
    //     0xbf3ba4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3ba8: mov             fp, SP
    // 0xbf3bac: r1 = 1073741823
    //     0xbf3bac: orr             x1, xzr, #0x3fffffff
    // 0xbf3bb0: ldr             x2, [fp, #0x10]
    // 0xbf3bb4: LoadField: r3 = r2->field_b
    //     0xbf3bb4: ldur            w3, [x2, #0xb]
    // 0xbf3bb8: DecompressPointer r3
    //     0xbf3bb8: add             x3, x3, HEAP, lsl #32
    // 0xbf3bbc: LoadField: r2 = r3->field_7
    //     0xbf3bbc: ldur            x2, [x3, #7]
    // 0xbf3bc0: asr             x3, x2, #0x1e
    // 0xbf3bc4: ubfx            x2, x2, #0, #0x20
    // 0xbf3bc8: ubfx            x3, x3, #0, #0x20
    // 0xbf3bcc: eor             x4, x2, x3
    // 0xbf3bd0: and             x2, x4, x1
    // 0xbf3bd4: lsl             w0, w2, #1
    // 0xbf3bd8: LeaveFrame
    //     0xbf3bd8: mov             SP, fp
    //     0xbf3bdc: ldp             fp, lr, [SP], #0x10
    // 0xbf3be0: ret
    //     0xbf3be0: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4197c, size: 0x34
    // 0xc4197c: EnterFrame
    //     0xc4197c: stp             fp, lr, [SP, #-0x10]!
    //     0xc41980: mov             fp, SP
    // 0xc41984: CheckStackOverflow
    //     0xc41984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41988: cmp             SP, x16
    //     0xc4198c: b.ls            #0xc419a8
    // 0xc41990: ldr             x1, [fp, #0x10]
    // 0xc41994: r2 = false
    //     0xc41994: add             x2, NULL, #0x30  ; false
    // 0xc41998: r0 = _toString()
    //     0xc41998: bl              #0xc419b0  ; [package:timezone/src/date_time.dart] TZDateTime::_toString
    // 0xc4199c: LeaveFrame
    //     0xc4199c: mov             SP, fp
    //     0xc419a0: ldp             fp, lr, [SP], #0x10
    // 0xc419a4: ret
    //     0xc419a4: ret             
    // 0xc419a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc419a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc419ac: b               #0xc41990
  }
  _ _toString(/* No info */) {
    // ** addr: 0xc419b0, size: 0x370
    // 0xc419b0: EnterFrame
    //     0xc419b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc419b4: mov             fp, SP
    // 0xc419b8: AllocStack(0x70)
    //     0xc419b8: sub             SP, SP, #0x70
    // 0xc419bc: SetupParameters(TZDateTime this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xc419bc: mov             x0, x1
    //     0xc419c0: stur            x1, [fp, #-0x10]
    //     0xc419c4: stur            x2, [fp, #-0x18]
    // 0xc419c8: CheckStackOverflow
    //     0xc419c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc419cc: cmp             SP, x16
    //     0xc419d0: b.ls            #0xc41d0c
    // 0xc419d4: LoadField: r1 = r0->field_13
    //     0xc419d4: ldur            w1, [x0, #0x13]
    // 0xc419d8: DecompressPointer r1
    //     0xc419d8: add             x1, x1, HEAP, lsl #32
    // 0xc419dc: LoadField: r3 = r1->field_7
    //     0xc419dc: ldur            x3, [x1, #7]
    // 0xc419e0: mov             x1, x0
    // 0xc419e4: stur            x3, [fp, #-8]
    // 0xc419e8: r0 = year()
    //     0xc419e8: bl              #0xeb8818  ; [package:timezone/src/date_time.dart] TZDateTime::year
    // 0xc419ec: mov             x1, x0
    // 0xc419f0: r0 = _fourDigits()
    //     0xc419f0: bl              #0xbfffe4  ; [dart:core] DateTime::_fourDigits
    // 0xc419f4: ldur            x1, [fp, #-0x10]
    // 0xc419f8: stur            x0, [fp, #-0x20]
    // 0xc419fc: r0 = month()
    //     0xc419fc: bl              #0xeb888c  ; [package:timezone/src/date_time.dart] TZDateTime::month
    // 0xc41a00: mov             x1, x0
    // 0xc41a04: r0 = _twoDigits()
    //     0xc41a04: bl              #0xbfff44  ; [dart:core] DateTime::_twoDigits
    // 0xc41a08: ldur            x1, [fp, #-0x10]
    // 0xc41a0c: stur            x0, [fp, #-0x28]
    // 0xc41a10: r0 = day()
    //     0xc41a10: bl              #0xeb84ec  ; [package:timezone/src/date_time.dart] TZDateTime::day
    // 0xc41a14: mov             x1, x0
    // 0xc41a18: r0 = _twoDigits()
    //     0xc41a18: bl              #0xbfff44  ; [dart:core] DateTime::_twoDigits
    // 0xc41a1c: mov             x2, x0
    // 0xc41a20: ldur            x0, [fp, #-0x18]
    // 0xc41a24: stur            x2, [fp, #-0x30]
    // 0xc41a28: tbnz            w0, #4, #0xc41a38
    // 0xc41a2c: r3 = "T"
    //     0xc41a2c: add             x3, PP, #8, lsl #12  ; [pp+0x8798] "T"
    //     0xc41a30: ldr             x3, [x3, #0x798]
    // 0xc41a34: b               #0xc41a3c
    // 0xc41a38: r3 = " "
    //     0xc41a38: ldr             x3, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc41a3c: ldur            x0, [fp, #-0x10]
    // 0xc41a40: mov             x1, x0
    // 0xc41a44: stur            x3, [fp, #-0x18]
    // 0xc41a48: r0 = hour()
    //     0xc41a48: bl              #0xeb7128  ; [package:timezone/src/date_time.dart] TZDateTime::hour
    // 0xc41a4c: mov             x1, x0
    // 0xc41a50: r0 = _twoDigits()
    //     0xc41a50: bl              #0xbfff44  ; [dart:core] DateTime::_twoDigits
    // 0xc41a54: ldur            x1, [fp, #-0x10]
    // 0xc41a58: stur            x0, [fp, #-0x38]
    // 0xc41a5c: r0 = minute()
    //     0xc41a5c: bl              #0xeb5034  ; [package:timezone/src/date_time.dart] TZDateTime::minute
    // 0xc41a60: mov             x1, x0
    // 0xc41a64: r0 = _twoDigits()
    //     0xc41a64: bl              #0xbfff44  ; [dart:core] DateTime::_twoDigits
    // 0xc41a68: ldur            x1, [fp, #-0x10]
    // 0xc41a6c: stur            x0, [fp, #-0x40]
    // 0xc41a70: r0 = second()
    //     0xc41a70: bl              #0xeb4fe4  ; [package:timezone/src/date_time.dart] TZDateTime::second
    // 0xc41a74: mov             x1, x0
    // 0xc41a78: r0 = _twoDigits()
    //     0xc41a78: bl              #0xbfff44  ; [dart:core] DateTime::_twoDigits
    // 0xc41a7c: ldur            x1, [fp, #-0x10]
    // 0xc41a80: stur            x0, [fp, #-0x48]
    // 0xc41a84: r0 = millisecond()
    //     0xc41a84: bl              #0xe78f80  ; [package:timezone/src/date_time.dart] TZDateTime::millisecond
    // 0xc41a88: mov             x1, x0
    // 0xc41a8c: r0 = _threeDigits()
    //     0xc41a8c: bl              #0xbffe50  ; [dart:core] DateTime::_threeDigits
    // 0xc41a90: mov             x2, x0
    // 0xc41a94: ldur            x0, [fp, #-0x10]
    // 0xc41a98: stur            x2, [fp, #-0x50]
    // 0xc41a9c: LoadField: r1 = r0->field_7
    //     0xc41a9c: ldur            w1, [x0, #7]
    // 0xc41aa0: DecompressPointer r1
    //     0xc41aa0: add             x1, x1, HEAP, lsl #32
    // 0xc41aa4: r0 = _parts()
    //     0xc41aa4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xc41aa8: mov             x2, x0
    // 0xc41aac: LoadField: r0 = r2->field_b
    //     0xc41aac: ldur            w0, [x2, #0xb]
    // 0xc41ab0: r1 = LoadInt32Instr(r0)
    //     0xc41ab0: sbfx            x1, x0, #1, #0x1f
    // 0xc41ab4: mov             x0, x1
    // 0xc41ab8: r1 = 0
    //     0xc41ab8: movz            x1, #0
    // 0xc41abc: cmp             x1, x0
    // 0xc41ac0: b.hs            #0xc41d14
    // 0xc41ac4: LoadField: r0 = r2->field_f
    //     0xc41ac4: ldur            w0, [x2, #0xf]
    // 0xc41ac8: DecompressPointer r0
    //     0xc41ac8: add             x0, x0, HEAP, lsl #32
    // 0xc41acc: cbnz            w0, #0xc41ad8
    // 0xc41ad0: r0 = ""
    //     0xc41ad0: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc41ad4: b               #0xc41ae8
    // 0xc41ad8: ldur            x1, [fp, #-0x10]
    // 0xc41adc: r0 = microsecond()
    //     0xc41adc: bl              #0xe877ec  ; [package:timezone/src/date_time.dart] TZDateTime::microsecond
    // 0xc41ae0: mov             x1, x0
    // 0xc41ae4: r0 = _threeDigits()
    //     0xc41ae4: bl              #0xbffe50  ; [dart:core] DateTime::_threeDigits
    // 0xc41ae8: ldur            x1, [fp, #-0x10]
    // 0xc41aec: stur            x0, [fp, #-0x58]
    // 0xc41af0: r0 = isUtc()
    //     0xc41af0: bl              #0xeb5cf0  ; [package:timezone/src/date_time.dart] TZDateTime::isUtc
    // 0xc41af4: tbnz            w0, #4, #0xc41bbc
    // 0xc41af8: ldur            x10, [fp, #-0x20]
    // 0xc41afc: ldur            x9, [fp, #-0x28]
    // 0xc41b00: ldur            x7, [fp, #-0x30]
    // 0xc41b04: ldur            x8, [fp, #-0x18]
    // 0xc41b08: ldur            x6, [fp, #-0x38]
    // 0xc41b0c: ldur            x5, [fp, #-0x40]
    // 0xc41b10: ldur            x4, [fp, #-0x48]
    // 0xc41b14: ldur            x3, [fp, #-0x50]
    // 0xc41b18: ldur            x0, [fp, #-0x58]
    // 0xc41b1c: r1 = Null
    //     0xc41b1c: mov             x1, NULL
    // 0xc41b20: r2 = 30
    //     0xc41b20: movz            x2, #0x1e
    // 0xc41b24: r0 = AllocateArray()
    //     0xc41b24: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41b28: mov             x1, x0
    // 0xc41b2c: ldur            x0, [fp, #-0x20]
    // 0xc41b30: StoreField: r1->field_f = r0
    //     0xc41b30: stur            w0, [x1, #0xf]
    // 0xc41b34: r16 = "-"
    //     0xc41b34: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc41b38: StoreField: r1->field_13 = r16
    //     0xc41b38: stur            w16, [x1, #0x13]
    // 0xc41b3c: ldur            x2, [fp, #-0x28]
    // 0xc41b40: ArrayStore: r1[0] = r2  ; List_4
    //     0xc41b40: stur            w2, [x1, #0x17]
    // 0xc41b44: r16 = "-"
    //     0xc41b44: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc41b48: StoreField: r1->field_1b = r16
    //     0xc41b48: stur            w16, [x1, #0x1b]
    // 0xc41b4c: ldur            x3, [fp, #-0x30]
    // 0xc41b50: StoreField: r1->field_1f = r3
    //     0xc41b50: stur            w3, [x1, #0x1f]
    // 0xc41b54: ldur            x4, [fp, #-0x18]
    // 0xc41b58: StoreField: r1->field_23 = r4
    //     0xc41b58: stur            w4, [x1, #0x23]
    // 0xc41b5c: ldur            x5, [fp, #-0x38]
    // 0xc41b60: StoreField: r1->field_27 = r5
    //     0xc41b60: stur            w5, [x1, #0x27]
    // 0xc41b64: r16 = ":"
    //     0xc41b64: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc41b68: StoreField: r1->field_2b = r16
    //     0xc41b68: stur            w16, [x1, #0x2b]
    // 0xc41b6c: ldur            x6, [fp, #-0x40]
    // 0xc41b70: StoreField: r1->field_2f = r6
    //     0xc41b70: stur            w6, [x1, #0x2f]
    // 0xc41b74: r16 = ":"
    //     0xc41b74: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc41b78: StoreField: r1->field_33 = r16
    //     0xc41b78: stur            w16, [x1, #0x33]
    // 0xc41b7c: ldur            x7, [fp, #-0x48]
    // 0xc41b80: StoreField: r1->field_37 = r7
    //     0xc41b80: stur            w7, [x1, #0x37]
    // 0xc41b84: r16 = "."
    //     0xc41b84: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc41b88: StoreField: r1->field_3b = r16
    //     0xc41b88: stur            w16, [x1, #0x3b]
    // 0xc41b8c: ldur            x8, [fp, #-0x50]
    // 0xc41b90: StoreField: r1->field_3f = r8
    //     0xc41b90: stur            w8, [x1, #0x3f]
    // 0xc41b94: ldur            x9, [fp, #-0x58]
    // 0xc41b98: StoreField: r1->field_43 = r9
    //     0xc41b98: stur            w9, [x1, #0x43]
    // 0xc41b9c: r16 = "Z"
    //     0xc41b9c: add             x16, PP, #8, lsl #12  ; [pp+0x8770] "Z"
    //     0xc41ba0: ldr             x16, [x16, #0x770]
    // 0xc41ba4: StoreField: r1->field_47 = r16
    //     0xc41ba4: stur            w16, [x1, #0x47]
    // 0xc41ba8: str             x1, [SP]
    // 0xc41bac: r0 = _interpolate()
    //     0xc41bac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41bb0: LeaveFrame
    //     0xc41bb0: mov             SP, fp
    //     0xc41bb4: ldp             fp, lr, [SP], #0x10
    // 0xc41bb8: ret
    //     0xc41bb8: ret             
    // 0xc41bbc: ldur            x1, [fp, #-8]
    // 0xc41bc0: ldur            x0, [fp, #-0x20]
    // 0xc41bc4: ldur            x2, [fp, #-0x28]
    // 0xc41bc8: ldur            x3, [fp, #-0x30]
    // 0xc41bcc: ldur            x4, [fp, #-0x18]
    // 0xc41bd0: ldur            x5, [fp, #-0x38]
    // 0xc41bd4: ldur            x6, [fp, #-0x40]
    // 0xc41bd8: ldur            x7, [fp, #-0x48]
    // 0xc41bdc: ldur            x8, [fp, #-0x50]
    // 0xc41be0: ldur            x9, [fp, #-0x58]
    // 0xc41be4: asr             x10, x1, #0x3f
    // 0xc41be8: neg             x11, x1
    // 0xc41bec: lsr             x12, x11, #0x3f
    // 0xc41bf0: orr             x13, x10, x12
    // 0xc41bf4: tbnz            x13, #0x3f, #0xc41c00
    // 0xc41bf8: r10 = "+"
    //     0xc41bf8: ldr             x10, [PP, #0x3158]  ; [pp+0x3158] "+"
    // 0xc41bfc: b               #0xc41c04
    // 0xc41c00: r10 = "-"
    //     0xc41c00: ldr             x10, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc41c04: stur            x10, [fp, #-0x10]
    // 0xc41c08: tbz             x1, #0x3f, #0xc41c14
    // 0xc41c0c: mov             x12, x11
    // 0xc41c10: b               #0xc41c18
    // 0xc41c14: mov             x12, x1
    // 0xc41c18: r1 = 1000
    //     0xc41c18: movz            x1, #0x3e8
    // 0xc41c1c: r11 = 3600
    //     0xc41c1c: movz            x11, #0xe10
    // 0xc41c20: sdiv            x13, x12, x1
    // 0xc41c24: stur            x13, [fp, #-8]
    // 0xc41c28: sdiv            x1, x13, x11
    // 0xc41c2c: r0 = _twoDigits()
    //     0xc41c2c: bl              #0xbfff44  ; [dart:core] DateTime::_twoDigits
    // 0xc41c30: mov             x2, x0
    // 0xc41c34: ldur            x1, [fp, #-8]
    // 0xc41c38: r0 = 3600
    //     0xc41c38: movz            x0, #0xe10
    // 0xc41c3c: stur            x2, [fp, #-0x60]
    // 0xc41c40: sdiv            x4, x1, x0
    // 0xc41c44: msub            x3, x4, x0, x1
    // 0xc41c48: cmp             x3, xzr
    // 0xc41c4c: b.lt            #0xc41d18
    // 0xc41c50: r0 = 60
    //     0xc41c50: movz            x0, #0x3c
    // 0xc41c54: sdiv            x1, x3, x0
    // 0xc41c58: r0 = _twoDigits()
    //     0xc41c58: bl              #0xbfff44  ; [dart:core] DateTime::_twoDigits
    // 0xc41c5c: r1 = Null
    //     0xc41c5c: mov             x1, NULL
    // 0xc41c60: r2 = 34
    //     0xc41c60: movz            x2, #0x22
    // 0xc41c64: stur            x0, [fp, #-0x68]
    // 0xc41c68: r0 = AllocateArray()
    //     0xc41c68: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41c6c: mov             x1, x0
    // 0xc41c70: ldur            x0, [fp, #-0x20]
    // 0xc41c74: StoreField: r1->field_f = r0
    //     0xc41c74: stur            w0, [x1, #0xf]
    // 0xc41c78: r16 = "-"
    //     0xc41c78: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc41c7c: StoreField: r1->field_13 = r16
    //     0xc41c7c: stur            w16, [x1, #0x13]
    // 0xc41c80: ldur            x0, [fp, #-0x28]
    // 0xc41c84: ArrayStore: r1[0] = r0  ; List_4
    //     0xc41c84: stur            w0, [x1, #0x17]
    // 0xc41c88: r16 = "-"
    //     0xc41c88: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0xc41c8c: StoreField: r1->field_1b = r16
    //     0xc41c8c: stur            w16, [x1, #0x1b]
    // 0xc41c90: ldur            x0, [fp, #-0x30]
    // 0xc41c94: StoreField: r1->field_1f = r0
    //     0xc41c94: stur            w0, [x1, #0x1f]
    // 0xc41c98: ldur            x0, [fp, #-0x18]
    // 0xc41c9c: StoreField: r1->field_23 = r0
    //     0xc41c9c: stur            w0, [x1, #0x23]
    // 0xc41ca0: ldur            x0, [fp, #-0x38]
    // 0xc41ca4: StoreField: r1->field_27 = r0
    //     0xc41ca4: stur            w0, [x1, #0x27]
    // 0xc41ca8: r16 = ":"
    //     0xc41ca8: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc41cac: StoreField: r1->field_2b = r16
    //     0xc41cac: stur            w16, [x1, #0x2b]
    // 0xc41cb0: ldur            x0, [fp, #-0x40]
    // 0xc41cb4: StoreField: r1->field_2f = r0
    //     0xc41cb4: stur            w0, [x1, #0x2f]
    // 0xc41cb8: r16 = ":"
    //     0xc41cb8: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc41cbc: StoreField: r1->field_33 = r16
    //     0xc41cbc: stur            w16, [x1, #0x33]
    // 0xc41cc0: ldur            x0, [fp, #-0x48]
    // 0xc41cc4: StoreField: r1->field_37 = r0
    //     0xc41cc4: stur            w0, [x1, #0x37]
    // 0xc41cc8: r16 = "."
    //     0xc41cc8: ldr             x16, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0xc41ccc: StoreField: r1->field_3b = r16
    //     0xc41ccc: stur            w16, [x1, #0x3b]
    // 0xc41cd0: ldur            x0, [fp, #-0x50]
    // 0xc41cd4: StoreField: r1->field_3f = r0
    //     0xc41cd4: stur            w0, [x1, #0x3f]
    // 0xc41cd8: ldur            x0, [fp, #-0x58]
    // 0xc41cdc: StoreField: r1->field_43 = r0
    //     0xc41cdc: stur            w0, [x1, #0x43]
    // 0xc41ce0: ldur            x0, [fp, #-0x10]
    // 0xc41ce4: StoreField: r1->field_47 = r0
    //     0xc41ce4: stur            w0, [x1, #0x47]
    // 0xc41ce8: ldur            x0, [fp, #-0x60]
    // 0xc41cec: StoreField: r1->field_4b = r0
    //     0xc41cec: stur            w0, [x1, #0x4b]
    // 0xc41cf0: ldur            x0, [fp, #-0x68]
    // 0xc41cf4: StoreField: r1->field_4f = r0
    //     0xc41cf4: stur            w0, [x1, #0x4f]
    // 0xc41cf8: str             x1, [SP]
    // 0xc41cfc: r0 = _interpolate()
    //     0xc41cfc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41d00: LeaveFrame
    //     0xc41d00: mov             SP, fp
    //     0xc41d04: ldp             fp, lr, [SP], #0x10
    // 0xc41d08: ret
    //     0xc41d08: ret             
    // 0xc41d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41d0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41d10: b               #0xc419d4
    // 0xc41d14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc41d14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc41d18: add             x3, x3, x0
    // 0xc41d1c: b               #0xc41c50
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7e150, size: 0xb4
    // 0xd7e150: EnterFrame
    //     0xd7e150: stp             fp, lr, [SP, #-0x10]!
    //     0xd7e154: mov             fp, SP
    // 0xd7e158: AllocStack(0x10)
    //     0xd7e158: sub             SP, SP, #0x10
    // 0xd7e15c: CheckStackOverflow
    //     0xd7e15c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7e160: cmp             SP, x16
    //     0xd7e164: b.ls            #0xd7e1fc
    // 0xd7e168: ldr             x0, [fp, #0x10]
    // 0xd7e16c: cmp             w0, NULL
    // 0xd7e170: b.ne            #0xd7e184
    // 0xd7e174: r0 = false
    //     0xd7e174: add             x0, NULL, #0x30  ; false
    // 0xd7e178: LeaveFrame
    //     0xd7e178: mov             SP, fp
    //     0xd7e17c: ldp             fp, lr, [SP], #0x10
    // 0xd7e180: ret
    //     0xd7e180: ret             
    // 0xd7e184: ldr             x3, [fp, #0x18]
    // 0xd7e188: cmp             w3, w0
    // 0xd7e18c: b.ne            #0xd7e198
    // 0xd7e190: r0 = true
    //     0xd7e190: add             x0, NULL, #0x20  ; true
    // 0xd7e194: b               #0xd7e1f0
    // 0xd7e198: r1 = 60
    //     0xd7e198: movz            x1, #0x3c
    // 0xd7e19c: branchIfSmi(r0, 0xd7e1a8)
    //     0xd7e19c: tbz             w0, #0, #0xd7e1a8
    // 0xd7e1a0: r1 = LoadClassIdInstr(r0)
    //     0xd7e1a0: ldur            x1, [x0, #-1]
    //     0xd7e1a4: ubfx            x1, x1, #0xc, #0x14
    // 0xd7e1a8: cmp             x1, #0x1ae
    // 0xd7e1ac: b.ne            #0xd7e1ec
    // 0xd7e1b0: LoadField: r1 = r3->field_b
    //     0xd7e1b0: ldur            w1, [x3, #0xb]
    // 0xd7e1b4: DecompressPointer r1
    //     0xd7e1b4: add             x1, x1, HEAP, lsl #32
    // 0xd7e1b8: LoadField: r2 = r0->field_b
    //     0xd7e1b8: ldur            w2, [x0, #0xb]
    // 0xd7e1bc: DecompressPointer r2
    //     0xd7e1bc: add             x2, x2, HEAP, lsl #32
    // 0xd7e1c0: r0 = isAtSameMomentAs()
    //     0xd7e1c0: bl              #0xd7e204  ; [dart:core] DateTime::isAtSameMomentAs
    // 0xd7e1c4: tbnz            w0, #4, #0xd7e1ec
    // 0xd7e1c8: ldr             x1, [fp, #0x18]
    // 0xd7e1cc: ldr             x0, [fp, #0x10]
    // 0xd7e1d0: LoadField: r2 = r1->field_f
    //     0xd7e1d0: ldur            w2, [x1, #0xf]
    // 0xd7e1d4: DecompressPointer r2
    //     0xd7e1d4: add             x2, x2, HEAP, lsl #32
    // 0xd7e1d8: LoadField: r1 = r0->field_f
    //     0xd7e1d8: ldur            w1, [x0, #0xf]
    // 0xd7e1dc: DecompressPointer r1
    //     0xd7e1dc: add             x1, x1, HEAP, lsl #32
    // 0xd7e1e0: stp             x1, x2, [SP]
    // 0xd7e1e4: r0 = ==()
    //     0xd7e1e4: bl              #0xd7e220  ; [package:timezone/src/location.dart] Location::==
    // 0xd7e1e8: b               #0xd7e1f0
    // 0xd7e1ec: r0 = false
    //     0xd7e1ec: add             x0, NULL, #0x30  ; false
    // 0xd7e1f0: LeaveFrame
    //     0xd7e1f0: mov             SP, fp
    //     0xd7e1f4: ldp             fp, lr, [SP], #0x10
    // 0xd7e1f8: ret
    //     0xd7e1f8: ret             
    // 0xd7e1fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7e1fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7e200: b               #0xd7e168
  }
  _ toIso8601String(/* No info */) {
    // ** addr: 0xd838b8, size: 0x30
    // 0xd838b8: EnterFrame
    //     0xd838b8: stp             fp, lr, [SP, #-0x10]!
    //     0xd838bc: mov             fp, SP
    // 0xd838c0: CheckStackOverflow
    //     0xd838c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd838c4: cmp             SP, x16
    //     0xd838c8: b.ls            #0xd838e0
    // 0xd838cc: r2 = true
    //     0xd838cc: add             x2, NULL, #0x20  ; true
    // 0xd838d0: r0 = _toString()
    //     0xd838d0: bl              #0xc419b0  ; [package:timezone/src/date_time.dart] TZDateTime::_toString
    // 0xd838d4: LeaveFrame
    //     0xd838d4: mov             SP, fp
    //     0xd838d8: ldp             fp, lr, [SP], #0x10
    // 0xd838dc: ret
    //     0xd838dc: ret             
    // 0xd838e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd838e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd838e4: b               #0xd838cc
  }
  get _ millisecond(/* No info */) {
    // ** addr: 0xe78f80, size: 0x38
    // 0xe78f80: EnterFrame
    //     0xe78f80: stp             fp, lr, [SP, #-0x10]!
    //     0xe78f84: mov             fp, SP
    // 0xe78f88: CheckStackOverflow
    //     0xe78f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78f8c: cmp             SP, x16
    //     0xe78f90: b.ls            #0xe78fb0
    // 0xe78f94: LoadField: r0 = r1->field_7
    //     0xe78f94: ldur            w0, [x1, #7]
    // 0xe78f98: DecompressPointer r0
    //     0xe78f98: add             x0, x0, HEAP, lsl #32
    // 0xe78f9c: mov             x1, x0
    // 0xe78fa0: r0 = millisecond()
    //     0xe78fa0: bl              #0xd5a008  ; [dart:core] DateTime::millisecond
    // 0xe78fa4: LeaveFrame
    //     0xe78fa4: mov             SP, fp
    //     0xe78fa8: ldp             fp, lr, [SP], #0x10
    // 0xe78fac: ret
    //     0xe78fac: ret             
    // 0xe78fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78fb4: b               #0xe78f94
  }
  get _ microsecond(/* No info */) {
    // ** addr: 0xe877ec, size: 0x68
    // 0xe877ec: EnterFrame
    //     0xe877ec: stp             fp, lr, [SP, #-0x10]!
    //     0xe877f0: mov             fp, SP
    // 0xe877f4: CheckStackOverflow
    //     0xe877f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe877f8: cmp             SP, x16
    //     0xe877fc: b.ls            #0xe87848
    // 0xe87800: LoadField: r0 = r1->field_7
    //     0xe87800: ldur            w0, [x1, #7]
    // 0xe87804: DecompressPointer r0
    //     0xe87804: add             x0, x0, HEAP, lsl #32
    // 0xe87808: mov             x1, x0
    // 0xe8780c: r0 = _parts()
    //     0xe8780c: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xe87810: mov             x2, x0
    // 0xe87814: LoadField: r3 = r2->field_b
    //     0xe87814: ldur            w3, [x2, #0xb]
    // 0xe87818: r0 = LoadInt32Instr(r3)
    //     0xe87818: sbfx            x0, x3, #1, #0x1f
    // 0xe8781c: r1 = 0
    //     0xe8781c: movz            x1, #0
    // 0xe87820: cmp             x1, x0
    // 0xe87824: b.hs            #0xe87850
    // 0xe87828: LoadField: r1 = r2->field_f
    //     0xe87828: ldur            w1, [x2, #0xf]
    // 0xe8782c: DecompressPointer r1
    //     0xe8782c: add             x1, x1, HEAP, lsl #32
    // 0xe87830: r0 = LoadInt32Instr(r1)
    //     0xe87830: sbfx            x0, x1, #1, #0x1f
    //     0xe87834: tbz             w1, #0, #0xe8783c
    //     0xe87838: ldur            x0, [x1, #7]
    // 0xe8783c: LeaveFrame
    //     0xe8783c: mov             SP, fp
    //     0xe87840: ldp             fp, lr, [SP], #0x10
    // 0xe87844: ret
    //     0xe87844: ret             
    // 0xe87848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe87848: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8784c: b               #0xe87800
    // 0xe87850: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe87850: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ millisecondsSinceEpoch(/* No info */) {
    // ** addr: 0xeb1ee0, size: 0x3c
    // 0xeb1ee0: EnterFrame
    //     0xeb1ee0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb1ee4: mov             fp, SP
    // 0xeb1ee8: LoadField: r2 = r1->field_b
    //     0xeb1ee8: ldur            w2, [x1, #0xb]
    // 0xeb1eec: DecompressPointer r2
    //     0xeb1eec: add             x2, x2, HEAP, lsl #32
    // 0xeb1ef0: LoadField: r1 = r2->field_7
    //     0xeb1ef0: ldur            x1, [x2, #7]
    // 0xeb1ef4: tbz             x1, #0x3f, #0xeb1f00
    // 0xeb1ef8: r3 = 999
    //     0xeb1ef8: movz            x3, #0x3e7
    // 0xeb1efc: b               #0xeb1f04
    // 0xeb1f00: r3 = 0
    //     0xeb1f00: movz            x3, #0
    // 0xeb1f04: r2 = 1000
    //     0xeb1f04: movz            x2, #0x3e8
    // 0xeb1f08: sub             x4, x1, x3
    // 0xeb1f0c: sdiv            x0, x4, x2
    // 0xeb1f10: LeaveFrame
    //     0xeb1f10: mov             SP, fp
    //     0xeb1f14: ldp             fp, lr, [SP], #0x10
    // 0xeb1f18: ret
    //     0xeb1f18: ret             
  }
  _ difference(/* No info */) {
    // ** addr: 0xeb3728, size: 0x54
    // 0xeb3728: EnterFrame
    //     0xeb3728: stp             fp, lr, [SP, #-0x10]!
    //     0xeb372c: mov             fp, SP
    // 0xeb3730: CheckStackOverflow
    //     0xeb3730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb3734: cmp             SP, x16
    //     0xeb3738: b.ls            #0xeb3774
    // 0xeb373c: LoadField: r0 = r1->field_b
    //     0xeb373c: ldur            w0, [x1, #0xb]
    // 0xeb3740: DecompressPointer r0
    //     0xeb3740: add             x0, x0, HEAP, lsl #32
    // 0xeb3744: r1 = LoadClassIdInstr(r2)
    //     0xeb3744: ldur            x1, [x2, #-1]
    //     0xeb3748: ubfx            x1, x1, #0xc, #0x14
    // 0xeb374c: cmp             x1, #0x1ae
    // 0xeb3750: b.ne            #0xeb3760
    // 0xeb3754: LoadField: r1 = r2->field_b
    //     0xeb3754: ldur            w1, [x2, #0xb]
    // 0xeb3758: DecompressPointer r1
    //     0xeb3758: add             x1, x1, HEAP, lsl #32
    // 0xeb375c: mov             x2, x1
    // 0xeb3760: mov             x1, x0
    // 0xeb3764: r0 = difference()
    //     0xeb3764: bl              #0xd5cf9c  ; [dart:core] DateTime::difference
    // 0xeb3768: LeaveFrame
    //     0xeb3768: mov             SP, fp
    //     0xeb376c: ldp             fp, lr, [SP], #0x10
    // 0xeb3770: ret
    //     0xeb3770: ret             
    // 0xeb3774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb3774: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb3778: b               #0xeb373c
  }
  get _ second(/* No info */) {
    // ** addr: 0xeb4fe4, size: 0x38
    // 0xeb4fe4: EnterFrame
    //     0xeb4fe4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb4fe8: mov             fp, SP
    // 0xeb4fec: CheckStackOverflow
    //     0xeb4fec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb4ff0: cmp             SP, x16
    //     0xeb4ff4: b.ls            #0xeb5014
    // 0xeb4ff8: LoadField: r0 = r1->field_7
    //     0xeb4ff8: ldur            w0, [x1, #7]
    // 0xeb4ffc: DecompressPointer r0
    //     0xeb4ffc: add             x0, x0, HEAP, lsl #32
    // 0xeb5000: mov             x1, x0
    // 0xeb5004: r0 = second()
    //     0xeb5004: bl              #0xd5daf4  ; [dart:core] DateTime::second
    // 0xeb5008: LeaveFrame
    //     0xeb5008: mov             SP, fp
    //     0xeb500c: ldp             fp, lr, [SP], #0x10
    // 0xeb5010: ret
    //     0xeb5010: ret             
    // 0xeb5014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5018: b               #0xeb4ff8
  }
  get _ minute(/* No info */) {
    // ** addr: 0xeb5034, size: 0x38
    // 0xeb5034: EnterFrame
    //     0xeb5034: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5038: mov             fp, SP
    // 0xeb503c: CheckStackOverflow
    //     0xeb503c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5040: cmp             SP, x16
    //     0xeb5044: b.ls            #0xeb5064
    // 0xeb5048: LoadField: r0 = r1->field_7
    //     0xeb5048: ldur            w0, [x1, #7]
    // 0xeb504c: DecompressPointer r0
    //     0xeb504c: add             x0, x0, HEAP, lsl #32
    // 0xeb5050: mov             x1, x0
    // 0xeb5054: r0 = minute()
    //     0xeb5054: bl              #0xd5dc88  ; [dart:core] DateTime::minute
    // 0xeb5058: LeaveFrame
    //     0xeb5058: mov             SP, fp
    //     0xeb505c: ldp             fp, lr, [SP], #0x10
    // 0xeb5060: ret
    //     0xeb5060: ret             
    // 0xeb5064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5064: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5068: b               #0xeb5048
  }
  _ isBefore(/* No info */) {
    // ** addr: 0xeb52d8, size: 0x54
    // 0xeb52d8: EnterFrame
    //     0xeb52d8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb52dc: mov             fp, SP
    // 0xeb52e0: CheckStackOverflow
    //     0xeb52e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb52e4: cmp             SP, x16
    //     0xeb52e8: b.ls            #0xeb5324
    // 0xeb52ec: LoadField: r0 = r1->field_b
    //     0xeb52ec: ldur            w0, [x1, #0xb]
    // 0xeb52f0: DecompressPointer r0
    //     0xeb52f0: add             x0, x0, HEAP, lsl #32
    // 0xeb52f4: r1 = LoadClassIdInstr(r2)
    //     0xeb52f4: ldur            x1, [x2, #-1]
    //     0xeb52f8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb52fc: cmp             x1, #0x1ae
    // 0xeb5300: b.ne            #0xeb5310
    // 0xeb5304: LoadField: r1 = r2->field_b
    //     0xeb5304: ldur            w1, [x2, #0xb]
    // 0xeb5308: DecompressPointer r1
    //     0xeb5308: add             x1, x1, HEAP, lsl #32
    // 0xeb530c: mov             x2, x1
    // 0xeb5310: mov             x1, x0
    // 0xeb5314: r0 = isBefore()
    //     0xeb5314: bl              #0xd5ddd0  ; [dart:core] DateTime::isBefore
    // 0xeb5318: LeaveFrame
    //     0xeb5318: mov             SP, fp
    //     0xeb531c: ldp             fp, lr, [SP], #0x10
    // 0xeb5320: ret
    //     0xeb5320: ret             
    // 0xeb5324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5328: b               #0xeb52ec
  }
  _ toUtc(/* No info */) {
    // ** addr: 0xeb5580, size: 0xa4
    // 0xeb5580: EnterFrame
    //     0xeb5580: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5584: mov             fp, SP
    // 0xeb5588: AllocStack(0x18)
    //     0xeb5588: sub             SP, SP, #0x18
    // 0xeb558c: SetupParameters(TZDateTime this /* r1 => r1, fp-0x10 */)
    //     0xeb558c: stur            x1, [fp, #-0x10]
    // 0xeb5590: CheckStackOverflow
    //     0xeb5590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5594: cmp             SP, x16
    //     0xeb5598: b.ls            #0xeb561c
    // 0xeb559c: LoadField: r0 = r1->field_f
    //     0xeb559c: ldur            w0, [x1, #0xf]
    // 0xeb55a0: DecompressPointer r0
    //     0xeb55a0: add             x0, x0, HEAP, lsl #32
    // 0xeb55a4: stur            x0, [fp, #-8]
    // 0xeb55a8: r0 = InitLateStaticField(0x129c) // [package:timezone/src/env.dart] ::_UTC
    //     0xeb55a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeb55ac: ldr             x0, [x0, #0x2538]
    //     0xeb55b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeb55b4: cmp             w0, w16
    //     0xeb55b8: b.ne            #0xeb55c8
    //     0xeb55bc: add             x2, PP, #8, lsl #12  ; [pp+0x87a0] Field <::._UTC@1194310200>: static late final (offset: 0x129c)
    //     0xeb55c0: ldr             x2, [x2, #0x7a0]
    //     0xeb55c4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeb55c8: mov             x1, x0
    // 0xeb55cc: ldur            x0, [fp, #-8]
    // 0xeb55d0: stur            x1, [fp, #-0x18]
    // 0xeb55d4: cmp             w0, w1
    // 0xeb55d8: b.ne            #0xeb55e4
    // 0xeb55dc: ldur            x0, [fp, #-0x10]
    // 0xeb55e0: b               #0xeb5610
    // 0xeb55e4: ldur            x0, [fp, #-0x10]
    // 0xeb55e8: LoadField: r2 = r0->field_b
    //     0xeb55e8: ldur            w2, [x0, #0xb]
    // 0xeb55ec: DecompressPointer r2
    //     0xeb55ec: add             x2, x2, HEAP, lsl #32
    // 0xeb55f0: stur            x2, [fp, #-8]
    // 0xeb55f4: r0 = TZDateTime()
    //     0xeb55f4: bl              #0x6fdf18  ; AllocateTZDateTimeStub -> TZDateTime (size=0x18)
    // 0xeb55f8: mov             x1, x0
    // 0xeb55fc: ldur            x2, [fp, #-8]
    // 0xeb5600: ldur            x3, [fp, #-0x18]
    // 0xeb5604: stur            x0, [fp, #-8]
    // 0xeb5608: r0 = TZDateTime.from()
    //     0xeb5608: bl              #0x6fd2cc  ; [package:timezone/src/date_time.dart] TZDateTime::TZDateTime.from
    // 0xeb560c: ldur            x0, [fp, #-8]
    // 0xeb5610: LeaveFrame
    //     0xeb5610: mov             SP, fp
    //     0xeb5614: ldp             fp, lr, [SP], #0x10
    // 0xeb5618: ret
    //     0xeb5618: ret             
    // 0xeb561c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb561c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5620: b               #0xeb559c
  }
  get _ isUtc(/* No info */) {
    // ** addr: 0xeb5cf0, size: 0x70
    // 0xeb5cf0: EnterFrame
    //     0xeb5cf0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb5cf4: mov             fp, SP
    // 0xeb5cf8: AllocStack(0x8)
    //     0xeb5cf8: sub             SP, SP, #8
    // 0xeb5cfc: CheckStackOverflow
    //     0xeb5cfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb5d00: cmp             SP, x16
    //     0xeb5d04: b.ls            #0xeb5d58
    // 0xeb5d08: LoadField: r0 = r1->field_f
    //     0xeb5d08: ldur            w0, [x1, #0xf]
    // 0xeb5d0c: DecompressPointer r0
    //     0xeb5d0c: add             x0, x0, HEAP, lsl #32
    // 0xeb5d10: stur            x0, [fp, #-8]
    // 0xeb5d14: r0 = InitLateStaticField(0x129c) // [package:timezone/src/env.dart] ::_UTC
    //     0xeb5d14: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xeb5d18: ldr             x0, [x0, #0x2538]
    //     0xeb5d1c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xeb5d20: cmp             w0, w16
    //     0xeb5d24: b.ne            #0xeb5d34
    //     0xeb5d28: add             x2, PP, #8, lsl #12  ; [pp+0x87a0] Field <::._UTC@1194310200>: static late final (offset: 0x129c)
    //     0xeb5d2c: ldr             x2, [x2, #0x7a0]
    //     0xeb5d30: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xeb5d34: ldur            x1, [fp, #-8]
    // 0xeb5d38: cmp             w1, w0
    // 0xeb5d3c: r16 = true
    //     0xeb5d3c: add             x16, NULL, #0x20  ; true
    // 0xeb5d40: r17 = false
    //     0xeb5d40: add             x17, NULL, #0x30  ; false
    // 0xeb5d44: csel            x2, x16, x17, eq
    // 0xeb5d48: mov             x0, x2
    // 0xeb5d4c: LeaveFrame
    //     0xeb5d4c: mov             SP, fp
    //     0xeb5d50: ldp             fp, lr, [SP], #0x10
    // 0xeb5d54: ret
    //     0xeb5d54: ret             
    // 0xeb5d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb5d58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb5d5c: b               #0xeb5d08
  }
  get _ microsecondsSinceEpoch(/* No info */) {
    // ** addr: 0xeb5d60, size: 0x10
    // 0xeb5d60: LoadField: r2 = r1->field_b
    //     0xeb5d60: ldur            w2, [x1, #0xb]
    // 0xeb5d64: DecompressPointer r2
    //     0xeb5d64: add             x2, x2, HEAP, lsl #32
    // 0xeb5d68: LoadField: r0 = r2->field_7
    //     0xeb5d68: ldur            x0, [x2, #7]
    // 0xeb5d6c: ret
    //     0xeb5d6c: ret             
  }
  _ isAfter(/* No info */) {
    // ** addr: 0xeb7060, size: 0x54
    // 0xeb7060: EnterFrame
    //     0xeb7060: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7064: mov             fp, SP
    // 0xeb7068: CheckStackOverflow
    //     0xeb7068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb706c: cmp             SP, x16
    //     0xeb7070: b.ls            #0xeb70ac
    // 0xeb7074: LoadField: r0 = r1->field_b
    //     0xeb7074: ldur            w0, [x1, #0xb]
    // 0xeb7078: DecompressPointer r0
    //     0xeb7078: add             x0, x0, HEAP, lsl #32
    // 0xeb707c: r1 = LoadClassIdInstr(r2)
    //     0xeb707c: ldur            x1, [x2, #-1]
    //     0xeb7080: ubfx            x1, x1, #0xc, #0x14
    // 0xeb7084: cmp             x1, #0x1ae
    // 0xeb7088: b.ne            #0xeb7098
    // 0xeb708c: LoadField: r1 = r2->field_b
    //     0xeb708c: ldur            w1, [x2, #0xb]
    // 0xeb7090: DecompressPointer r1
    //     0xeb7090: add             x1, x1, HEAP, lsl #32
    // 0xeb7094: mov             x2, x1
    // 0xeb7098: mov             x1, x0
    // 0xeb709c: r0 = isAfter()
    //     0xeb709c: bl              #0xd61fd4  ; [dart:core] DateTime::isAfter
    // 0xeb70a0: LeaveFrame
    //     0xeb70a0: mov             SP, fp
    //     0xeb70a4: ldp             fp, lr, [SP], #0x10
    // 0xeb70a8: ret
    //     0xeb70a8: ret             
    // 0xeb70ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb70ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb70b0: b               #0xeb7074
  }
  _ add(/* No info */) {
    // ** addr: 0xeb70b4, size: 0x74
    // 0xeb70b4: EnterFrame
    //     0xeb70b4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb70b8: mov             fp, SP
    // 0xeb70bc: AllocStack(0x18)
    //     0xeb70bc: sub             SP, SP, #0x18
    // 0xeb70c0: SetupParameters(TZDateTime this /* r1 => r0, fp-0x8 */)
    //     0xeb70c0: mov             x0, x1
    //     0xeb70c4: stur            x1, [fp, #-8]
    // 0xeb70c8: CheckStackOverflow
    //     0xeb70c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb70cc: cmp             SP, x16
    //     0xeb70d0: b.ls            #0xeb7120
    // 0xeb70d4: LoadField: r1 = r0->field_b
    //     0xeb70d4: ldur            w1, [x0, #0xb]
    // 0xeb70d8: DecompressPointer r1
    //     0xeb70d8: add             x1, x1, HEAP, lsl #32
    // 0xeb70dc: r0 = add()
    //     0xeb70dc: bl              #0xd6203c  ; [dart:core] DateTime::add
    // 0xeb70e0: mov             x1, x0
    // 0xeb70e4: ldur            x0, [fp, #-8]
    // 0xeb70e8: stur            x1, [fp, #-0x18]
    // 0xeb70ec: LoadField: r3 = r0->field_f
    //     0xeb70ec: ldur            w3, [x0, #0xf]
    // 0xeb70f0: DecompressPointer r3
    //     0xeb70f0: add             x3, x3, HEAP, lsl #32
    // 0xeb70f4: stur            x3, [fp, #-0x10]
    // 0xeb70f8: r0 = TZDateTime()
    //     0xeb70f8: bl              #0x6fdf18  ; AllocateTZDateTimeStub -> TZDateTime (size=0x18)
    // 0xeb70fc: mov             x1, x0
    // 0xeb7100: ldur            x2, [fp, #-0x18]
    // 0xeb7104: ldur            x3, [fp, #-0x10]
    // 0xeb7108: stur            x0, [fp, #-8]
    // 0xeb710c: r0 = TZDateTime.from()
    //     0xeb710c: bl              #0x6fd2cc  ; [package:timezone/src/date_time.dart] TZDateTime::TZDateTime.from
    // 0xeb7110: ldur            x0, [fp, #-8]
    // 0xeb7114: LeaveFrame
    //     0xeb7114: mov             SP, fp
    //     0xeb7118: ldp             fp, lr, [SP], #0x10
    // 0xeb711c: ret
    //     0xeb711c: ret             
    // 0xeb7120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7124: b               #0xeb70d4
  }
  get _ hour(/* No info */) {
    // ** addr: 0xeb7128, size: 0x38
    // 0xeb7128: EnterFrame
    //     0xeb7128: stp             fp, lr, [SP, #-0x10]!
    //     0xeb712c: mov             fp, SP
    // 0xeb7130: CheckStackOverflow
    //     0xeb7130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7134: cmp             SP, x16
    //     0xeb7138: b.ls            #0xeb7158
    // 0xeb713c: LoadField: r0 = r1->field_7
    //     0xeb713c: ldur            w0, [x1, #7]
    // 0xeb7140: DecompressPointer r0
    //     0xeb7140: add             x0, x0, HEAP, lsl #32
    // 0xeb7144: mov             x1, x0
    // 0xeb7148: r0 = hour()
    //     0xeb7148: bl              #0xd623a4  ; [dart:core] DateTime::hour
    // 0xeb714c: LeaveFrame
    //     0xeb714c: mov             SP, fp
    //     0xeb7150: ldp             fp, lr, [SP], #0x10
    // 0xeb7154: ret
    //     0xeb7154: ret             
    // 0xeb7158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7158: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb715c: b               #0xeb713c
  }
  get _ weekday(/* No info */) {
    // ** addr: 0xeb73c4, size: 0x68
    // 0xeb73c4: EnterFrame
    //     0xeb73c4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb73c8: mov             fp, SP
    // 0xeb73cc: CheckStackOverflow
    //     0xeb73cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb73d0: cmp             SP, x16
    //     0xeb73d4: b.ls            #0xeb7420
    // 0xeb73d8: LoadField: r0 = r1->field_7
    //     0xeb73d8: ldur            w0, [x1, #7]
    // 0xeb73dc: DecompressPointer r0
    //     0xeb73dc: add             x0, x0, HEAP, lsl #32
    // 0xeb73e0: mov             x1, x0
    // 0xeb73e4: r0 = _parts()
    //     0xeb73e4: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xeb73e8: mov             x2, x0
    // 0xeb73ec: LoadField: r3 = r2->field_b
    //     0xeb73ec: ldur            w3, [x2, #0xb]
    // 0xeb73f0: r0 = LoadInt32Instr(r3)
    //     0xeb73f0: sbfx            x0, x3, #1, #0x1f
    // 0xeb73f4: r1 = 6
    //     0xeb73f4: movz            x1, #0x6
    // 0xeb73f8: cmp             x1, x0
    // 0xeb73fc: b.hs            #0xeb7428
    // 0xeb7400: LoadField: r1 = r2->field_27
    //     0xeb7400: ldur            w1, [x2, #0x27]
    // 0xeb7404: DecompressPointer r1
    //     0xeb7404: add             x1, x1, HEAP, lsl #32
    // 0xeb7408: r0 = LoadInt32Instr(r1)
    //     0xeb7408: sbfx            x0, x1, #1, #0x1f
    //     0xeb740c: tbz             w1, #0, #0xeb7414
    //     0xeb7410: ldur            x0, [x1, #7]
    // 0xeb7414: LeaveFrame
    //     0xeb7414: mov             SP, fp
    //     0xeb7418: ldp             fp, lr, [SP], #0x10
    // 0xeb741c: ret
    //     0xeb741c: ret             
    // 0xeb7420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7420: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7424: b               #0xeb73d8
    // 0xeb7428: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb7428: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ day(/* No info */) {
    // ** addr: 0xeb84ec, size: 0x68
    // 0xeb84ec: EnterFrame
    //     0xeb84ec: stp             fp, lr, [SP, #-0x10]!
    //     0xeb84f0: mov             fp, SP
    // 0xeb84f4: CheckStackOverflow
    //     0xeb84f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb84f8: cmp             SP, x16
    //     0xeb84fc: b.ls            #0xeb8548
    // 0xeb8500: LoadField: r0 = r1->field_7
    //     0xeb8500: ldur            w0, [x1, #7]
    // 0xeb8504: DecompressPointer r0
    //     0xeb8504: add             x0, x0, HEAP, lsl #32
    // 0xeb8508: mov             x1, x0
    // 0xeb850c: r0 = _parts()
    //     0xeb850c: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xeb8510: mov             x2, x0
    // 0xeb8514: LoadField: r3 = r2->field_b
    //     0xeb8514: ldur            w3, [x2, #0xb]
    // 0xeb8518: r0 = LoadInt32Instr(r3)
    //     0xeb8518: sbfx            x0, x3, #1, #0x1f
    // 0xeb851c: r1 = 5
    //     0xeb851c: movz            x1, #0x5
    // 0xeb8520: cmp             x1, x0
    // 0xeb8524: b.hs            #0xeb8550
    // 0xeb8528: LoadField: r1 = r2->field_23
    //     0xeb8528: ldur            w1, [x2, #0x23]
    // 0xeb852c: DecompressPointer r1
    //     0xeb852c: add             x1, x1, HEAP, lsl #32
    // 0xeb8530: r0 = LoadInt32Instr(r1)
    //     0xeb8530: sbfx            x0, x1, #1, #0x1f
    //     0xeb8534: tbz             w1, #0, #0xeb853c
    //     0xeb8538: ldur            x0, [x1, #7]
    // 0xeb853c: LeaveFrame
    //     0xeb853c: mov             SP, fp
    //     0xeb8540: ldp             fp, lr, [SP], #0x10
    // 0xeb8544: ret
    //     0xeb8544: ret             
    // 0xeb8548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb8548: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb854c: b               #0xeb8500
    // 0xeb8550: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb8550: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ year(/* No info */) {
    // ** addr: 0xeb8818, size: 0x68
    // 0xeb8818: EnterFrame
    //     0xeb8818: stp             fp, lr, [SP, #-0x10]!
    //     0xeb881c: mov             fp, SP
    // 0xeb8820: CheckStackOverflow
    //     0xeb8820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8824: cmp             SP, x16
    //     0xeb8828: b.ls            #0xeb8874
    // 0xeb882c: LoadField: r0 = r1->field_7
    //     0xeb882c: ldur            w0, [x1, #7]
    // 0xeb8830: DecompressPointer r0
    //     0xeb8830: add             x0, x0, HEAP, lsl #32
    // 0xeb8834: mov             x1, x0
    // 0xeb8838: r0 = _parts()
    //     0xeb8838: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xeb883c: mov             x2, x0
    // 0xeb8840: LoadField: r3 = r2->field_b
    //     0xeb8840: ldur            w3, [x2, #0xb]
    // 0xeb8844: r0 = LoadInt32Instr(r3)
    //     0xeb8844: sbfx            x0, x3, #1, #0x1f
    // 0xeb8848: r1 = 8
    //     0xeb8848: movz            x1, #0x8
    // 0xeb884c: cmp             x1, x0
    // 0xeb8850: b.hs            #0xeb887c
    // 0xeb8854: LoadField: r1 = r2->field_2f
    //     0xeb8854: ldur            w1, [x2, #0x2f]
    // 0xeb8858: DecompressPointer r1
    //     0xeb8858: add             x1, x1, HEAP, lsl #32
    // 0xeb885c: r0 = LoadInt32Instr(r1)
    //     0xeb885c: sbfx            x0, x1, #1, #0x1f
    //     0xeb8860: tbz             w1, #0, #0xeb8868
    //     0xeb8864: ldur            x0, [x1, #7]
    // 0xeb8868: LeaveFrame
    //     0xeb8868: mov             SP, fp
    //     0xeb886c: ldp             fp, lr, [SP], #0x10
    // 0xeb8870: ret
    //     0xeb8870: ret             
    // 0xeb8874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb8874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb8878: b               #0xeb882c
    // 0xeb887c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb887c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ month(/* No info */) {
    // ** addr: 0xeb888c, size: 0x68
    // 0xeb888c: EnterFrame
    //     0xeb888c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb8890: mov             fp, SP
    // 0xeb8894: CheckStackOverflow
    //     0xeb8894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8898: cmp             SP, x16
    //     0xeb889c: b.ls            #0xeb88e8
    // 0xeb88a0: LoadField: r0 = r1->field_7
    //     0xeb88a0: ldur            w0, [x1, #7]
    // 0xeb88a4: DecompressPointer r0
    //     0xeb88a4: add             x0, x0, HEAP, lsl #32
    // 0xeb88a8: mov             x1, x0
    // 0xeb88ac: r0 = _parts()
    //     0xeb88ac: bl              #0x6fe860  ; [dart:core] DateTime::_parts
    // 0xeb88b0: mov             x2, x0
    // 0xeb88b4: LoadField: r3 = r2->field_b
    //     0xeb88b4: ldur            w3, [x2, #0xb]
    // 0xeb88b8: r0 = LoadInt32Instr(r3)
    //     0xeb88b8: sbfx            x0, x3, #1, #0x1f
    // 0xeb88bc: r1 = 7
    //     0xeb88bc: movz            x1, #0x7
    // 0xeb88c0: cmp             x1, x0
    // 0xeb88c4: b.hs            #0xeb88f0
    // 0xeb88c8: LoadField: r1 = r2->field_2b
    //     0xeb88c8: ldur            w1, [x2, #0x2b]
    // 0xeb88cc: DecompressPointer r1
    //     0xeb88cc: add             x1, x1, HEAP, lsl #32
    // 0xeb88d0: r0 = LoadInt32Instr(r1)
    //     0xeb88d0: sbfx            x0, x1, #1, #0x1f
    //     0xeb88d4: tbz             w1, #0, #0xeb88dc
    //     0xeb88d8: ldur            x0, [x1, #7]
    // 0xeb88dc: LeaveFrame
    //     0xeb88dc: mov             SP, fp
    //     0xeb88e0: ldp             fp, lr, [SP], #0x10
    // 0xeb88e4: ret
    //     0xeb88e4: ret             
    // 0xeb88e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb88e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb88ec: b               #0xeb88a0
    // 0xeb88f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeb88f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
