// lib: , url: package:timezone/src/env.dart

// class id: 1051209, size: 0x8
class :: {

  static late Location _local; // offset: 0x12a4
  static late final Location _UTC; // offset: 0x129c
  static late final LocationDatabase _database; // offset: 0x12a0

  static Location _UTC() {
    // ** addr: 0x6fdac4, size: 0x108
    // 0x6fdac4: EnterFrame
    //     0x6fdac4: stp             fp, lr, [SP, #-0x10]!
    //     0x6fdac8: mov             fp, SP
    // 0x6fdacc: AllocStack(0x20)
    //     0x6fdacc: sub             SP, SP, #0x20
    // 0x6fdad0: r0 = 2
    //     0x6fdad0: movz            x0, #0x2
    // 0x6fdad4: CheckStackOverflow
    //     0x6fdad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fdad8: cmp             SP, x16
    //     0x6fdadc: b.ls            #0x6fdbc4
    // 0x6fdae0: mov             x2, x0
    // 0x6fdae4: r1 = Null
    //     0x6fdae4: mov             x1, NULL
    // 0x6fdae8: r0 = AllocateArray()
    //     0x6fdae8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6fdaec: stur            x0, [fp, #-8]
    // 0x6fdaf0: r16 = -8640000000000000
    //     0x6fdaf0: add             x16, PP, #8, lsl #12  ; [pp+0x87a8] -0x1eb208c2dc0000
    //     0x6fdaf4: ldr             x16, [x16, #0x7a8]
    // 0x6fdaf8: StoreField: r0->field_f = r16
    //     0x6fdaf8: stur            w16, [x0, #0xf]
    // 0x6fdafc: r1 = <int>
    //     0x6fdafc: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x6fdb00: r0 = AllocateGrowableArray()
    //     0x6fdb00: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x6fdb04: mov             x3, x0
    // 0x6fdb08: ldur            x0, [fp, #-8]
    // 0x6fdb0c: stur            x3, [fp, #-0x10]
    // 0x6fdb10: StoreField: r3->field_f = r0
    //     0x6fdb10: stur            w0, [x3, #0xf]
    // 0x6fdb14: r0 = 2
    //     0x6fdb14: movz            x0, #0x2
    // 0x6fdb18: StoreField: r3->field_b = r0
    //     0x6fdb18: stur            w0, [x3, #0xb]
    // 0x6fdb1c: mov             x2, x0
    // 0x6fdb20: r1 = Null
    //     0x6fdb20: mov             x1, NULL
    // 0x6fdb24: r0 = AllocateArray()
    //     0x6fdb24: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6fdb28: stur            x0, [fp, #-8]
    // 0x6fdb2c: StoreField: r0->field_f = rZR
    //     0x6fdb2c: stur            wzr, [x0, #0xf]
    // 0x6fdb30: r1 = <int>
    //     0x6fdb30: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x6fdb34: r0 = AllocateGrowableArray()
    //     0x6fdb34: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x6fdb38: mov             x3, x0
    // 0x6fdb3c: ldur            x0, [fp, #-8]
    // 0x6fdb40: stur            x3, [fp, #-0x18]
    // 0x6fdb44: StoreField: r3->field_f = r0
    //     0x6fdb44: stur            w0, [x3, #0xf]
    // 0x6fdb48: r0 = 2
    //     0x6fdb48: movz            x0, #0x2
    // 0x6fdb4c: StoreField: r3->field_b = r0
    //     0x6fdb4c: stur            w0, [x3, #0xb]
    // 0x6fdb50: mov             x2, x0
    // 0x6fdb54: r1 = Null
    //     0x6fdb54: mov             x1, NULL
    // 0x6fdb58: r0 = AllocateArray()
    //     0x6fdb58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6fdb5c: stur            x0, [fp, #-8]
    // 0x6fdb60: r16 = Instance_TimeZone
    //     0x6fdb60: add             x16, PP, #8, lsl #12  ; [pp+0x87b0] Obj!TimeZone@e0bf11
    //     0x6fdb64: ldr             x16, [x16, #0x7b0]
    // 0x6fdb68: StoreField: r0->field_f = r16
    //     0x6fdb68: stur            w16, [x0, #0xf]
    // 0x6fdb6c: r1 = <TimeZone>
    //     0x6fdb6c: add             x1, PP, #8, lsl #12  ; [pp+0x87b8] TypeArguments: <TimeZone>
    //     0x6fdb70: ldr             x1, [x1, #0x7b8]
    // 0x6fdb74: r0 = AllocateGrowableArray()
    //     0x6fdb74: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x6fdb78: mov             x1, x0
    // 0x6fdb7c: ldur            x0, [fp, #-8]
    // 0x6fdb80: stur            x1, [fp, #-0x20]
    // 0x6fdb84: StoreField: r1->field_f = r0
    //     0x6fdb84: stur            w0, [x1, #0xf]
    // 0x6fdb88: r0 = 2
    //     0x6fdb88: movz            x0, #0x2
    // 0x6fdb8c: StoreField: r1->field_b = r0
    //     0x6fdb8c: stur            w0, [x1, #0xb]
    // 0x6fdb90: r0 = Location()
    //     0x6fdb90: bl              #0x6fdf0c  ; AllocateLocationStub -> Location (size=0x2c)
    // 0x6fdb94: mov             x1, x0
    // 0x6fdb98: ldur            x3, [fp, #-0x10]
    // 0x6fdb9c: ldur            x5, [fp, #-0x18]
    // 0x6fdba0: ldur            x6, [fp, #-0x20]
    // 0x6fdba4: r2 = "UTC"
    //     0x6fdba4: add             x2, PP, #8, lsl #12  ; [pp+0x87c0] "UTC"
    //     0x6fdba8: ldr             x2, [x2, #0x7c0]
    // 0x6fdbac: stur            x0, [fp, #-8]
    // 0x6fdbb0: r0 = Location()
    //     0x6fdbb0: bl              #0x6fdbcc  ; [package:timezone/src/location.dart] Location::Location
    // 0x6fdbb4: ldur            x0, [fp, #-8]
    // 0x6fdbb8: LeaveFrame
    //     0x6fdbb8: mov             SP, fp
    //     0x6fdbbc: ldp             fp, lr, [SP], #0x10
    // 0x6fdbc0: ret
    //     0x6fdbc0: ret             
    // 0x6fdbc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fdbc4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fdbc8: b               #0x6fdae0
  }
  static _ getLocation(/* No info */) {
    // ** addr: 0x6fdf24, size: 0x60
    // 0x6fdf24: EnterFrame
    //     0x6fdf24: stp             fp, lr, [SP, #-0x10]!
    //     0x6fdf28: mov             fp, SP
    // 0x6fdf2c: AllocStack(0x8)
    //     0x6fdf2c: sub             SP, SP, #8
    // 0x6fdf30: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x6fdf30: mov             x2, x1
    //     0x6fdf34: stur            x1, [fp, #-8]
    // 0x6fdf38: CheckStackOverflow
    //     0x6fdf38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fdf3c: cmp             SP, x16
    //     0x6fdf40: b.ls            #0x6fdf7c
    // 0x6fdf44: r0 = InitLateStaticField(0x12a0) // [package:timezone/src/env.dart] ::_database
    //     0x6fdf44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fdf48: ldr             x0, [x0, #0x2540]
    //     0x6fdf4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fdf50: cmp             w0, w16
    //     0x6fdf54: b.ne            #0x6fdf64
    //     0x6fdf58: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ad40] Field <::._database@1194310200>: static late final (offset: 0x12a0)
    //     0x6fdf5c: ldr             x2, [x2, #0xd40]
    //     0x6fdf60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fdf64: mov             x1, x0
    // 0x6fdf68: ldur            x2, [fp, #-8]
    // 0x6fdf6c: r0 = get()
    //     0x6fdf6c: bl              #0x6fdf84  ; [package:timezone/src/location_database.dart] LocationDatabase::get
    // 0x6fdf70: LeaveFrame
    //     0x6fdf70: mov             SP, fp
    //     0x6fdf74: ldp             fp, lr, [SP], #0x10
    // 0x6fdf78: ret
    //     0x6fdf78: ret             
    // 0x6fdf7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fdf7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fdf80: b               #0x6fdf44
  }
  static LocationDatabase _database() {
    // ** addr: 0x6fe0d8, size: 0x50
    // 0x6fe0d8: EnterFrame
    //     0x6fe0d8: stp             fp, lr, [SP, #-0x10]!
    //     0x6fe0dc: mov             fp, SP
    // 0x6fe0e0: AllocStack(0x18)
    //     0x6fe0e0: sub             SP, SP, #0x18
    // 0x6fe0e4: CheckStackOverflow
    //     0x6fe0e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fe0e8: cmp             SP, x16
    //     0x6fe0ec: b.ls            #0x6fe120
    // 0x6fe0f0: r16 = <String, Location>
    //     0x6fe0f0: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ad60] TypeArguments: <String, Location>
    //     0x6fe0f4: ldr             x16, [x16, #0xd60]
    // 0x6fe0f8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6fe0fc: stp             lr, x16, [SP]
    // 0x6fe100: r0 = Map._fromLiteral()
    //     0x6fe100: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x6fe104: stur            x0, [fp, #-8]
    // 0x6fe108: r0 = LocationDatabase()
    //     0x6fe108: bl              #0x6fe128  ; AllocateLocationDatabaseStub -> LocationDatabase (size=0xc)
    // 0x6fe10c: ldur            x1, [fp, #-8]
    // 0x6fe110: StoreField: r0->field_7 = r1
    //     0x6fe110: stur            w1, [x0, #7]
    // 0x6fe114: LeaveFrame
    //     0x6fe114: mov             SP, fp
    //     0x6fe118: ldp             fp, lr, [SP], #0x10
    // 0x6fe11c: ret
    //     0x6fe11c: ret             
    // 0x6fe120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fe120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fe124: b               #0x6fe0f0
  }
  static void initializeDatabase(List<int>) {
    // ** addr: 0x91119c, size: 0x164
    // 0x91119c: EnterFrame
    //     0x91119c: stp             fp, lr, [SP, #-0x10]!
    //     0x9111a0: mov             fp, SP
    // 0x9111a4: AllocStack(0x28)
    //     0x9111a4: sub             SP, SP, #0x28
    // 0x9111a8: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x9111a8: stur            x1, [fp, #-8]
    // 0x9111ac: CheckStackOverflow
    //     0x9111ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9111b0: cmp             SP, x16
    //     0x9111b4: b.ls            #0x9112f0
    // 0x9111b8: r0 = InitLateStaticField(0x12a0) // [package:timezone/src/env.dart] ::_database
    //     0x9111b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9111bc: ldr             x0, [x0, #0x2540]
    //     0x9111c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9111c4: cmp             w0, w16
    //     0x9111c8: b.ne            #0x9111d8
    //     0x9111cc: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ad40] Field <::._database@1194310200>: static late final (offset: 0x12a0)
    //     0x9111d0: ldr             x2, [x2, #0xd40]
    //     0x9111d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9111d8: mov             x1, x0
    // 0x9111dc: stur            x0, [fp, #-0x10]
    // 0x9111e0: r0 = clear()
    //     0x9111e0: bl              #0x91262c  ; [package:timezone/src/location_database.dart] LocationDatabase::clear
    // 0x9111e4: ldur            x1, [fp, #-8]
    // 0x9111e8: r0 = tzdbDeserialize()
    //     0x9111e8: bl              #0x911300  ; [package:timezone/src/tzdb.dart] ::tzdbDeserialize
    // 0x9111ec: mov             x1, x0
    // 0x9111f0: r0 = iterator()
    //     0x9111f0: bl              #0x887928  ; [dart:async] _SyncStarIterable::iterator
    // 0x9111f4: mov             x2, x0
    // 0x9111f8: ldur            x0, [fp, #-0x10]
    // 0x9111fc: stur            x2, [fp, #-0x20]
    // 0x911200: LoadField: r3 = r0->field_7
    //     0x911200: ldur            w3, [x0, #7]
    // 0x911204: DecompressPointer r3
    //     0x911204: add             x3, x3, HEAP, lsl #32
    // 0x911208: stur            x3, [fp, #-0x18]
    // 0x91120c: LoadField: r0 = r2->field_7
    //     0x91120c: ldur            w0, [x2, #7]
    // 0x911210: DecompressPointer r0
    //     0x911210: add             x0, x0, HEAP, lsl #32
    // 0x911214: stur            x0, [fp, #-8]
    // 0x911218: CheckStackOverflow
    //     0x911218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91121c: cmp             SP, x16
    //     0x911220: b.ls            #0x9112f8
    // 0x911224: mov             x1, x2
    // 0x911228: r0 = moveNext()
    //     0x911228: bl              #0x6769a4  ; [dart:async] _SyncStarIterator::moveNext
    // 0x91122c: tbnz            w0, #4, #0x9112b8
    // 0x911230: ldur            x3, [fp, #-0x20]
    // 0x911234: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x911234: ldur            w4, [x3, #0x17]
    // 0x911238: DecompressPointer r4
    //     0x911238: add             x4, x4, HEAP, lsl #32
    // 0x91123c: stur            x4, [fp, #-0x10]
    // 0x911240: cmp             w4, NULL
    // 0x911244: b.ne            #0x911278
    // 0x911248: mov             x0, x4
    // 0x91124c: ldur            x2, [fp, #-8]
    // 0x911250: r1 = Null
    //     0x911250: mov             x1, NULL
    // 0x911254: cmp             w2, NULL
    // 0x911258: b.eq            #0x911278
    // 0x91125c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x91125c: ldur            w4, [x2, #0x17]
    // 0x911260: DecompressPointer r4
    //     0x911260: add             x4, x4, HEAP, lsl #32
    // 0x911264: r8 = X0
    //     0x911264: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x911268: LoadField: r9 = r4->field_7
    //     0x911268: ldur            x9, [x4, #7]
    // 0x91126c: r3 = Null
    //     0x91126c: add             x3, PP, #0x1a, lsl #12  ; [pp+0x1ad88] Null
    //     0x911270: ldr             x3, [x3, #0xd88]
    // 0x911274: blr             x9
    // 0x911278: ldur            x3, [fp, #-0x10]
    // 0x91127c: LoadField: r0 = r3->field_7
    //     0x91127c: ldur            w0, [x3, #7]
    // 0x911280: DecompressPointer r0
    //     0x911280: add             x0, x0, HEAP, lsl #32
    // 0x911284: ldur            x1, [fp, #-0x18]
    // 0x911288: mov             x2, x0
    // 0x91128c: stur            x0, [fp, #-0x28]
    // 0x911290: r0 = _hashCode()
    //     0x911290: bl              #0xebbcf4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x911294: ldur            x1, [fp, #-0x18]
    // 0x911298: ldur            x2, [fp, #-0x28]
    // 0x91129c: ldur            x3, [fp, #-0x10]
    // 0x9112a0: mov             x5, x0
    // 0x9112a4: r0 = _set()
    //     0x9112a4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x9112a8: ldur            x2, [fp, #-0x20]
    // 0x9112ac: ldur            x0, [fp, #-8]
    // 0x9112b0: ldur            x3, [fp, #-0x18]
    // 0x9112b4: b               #0x911218
    // 0x9112b8: r0 = InitLateStaticField(0x129c) // [package:timezone/src/env.dart] ::_UTC
    //     0x9112b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9112bc: ldr             x0, [x0, #0x2538]
    //     0x9112c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9112c4: cmp             w0, w16
    //     0x9112c8: b.ne            #0x9112d8
    //     0x9112cc: add             x2, PP, #8, lsl #12  ; [pp+0x87a0] Field <::._UTC@1194310200>: static late final (offset: 0x129c)
    //     0x9112d0: ldr             x2, [x2, #0x7a0]
    //     0x9112d4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9112d8: StoreStaticField(0x12a4, r0)
    //     0x9112d8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x9112dc: str             x0, [x1, #0x2548]
    // 0x9112e0: r0 = Null
    //     0x9112e0: mov             x0, NULL
    // 0x9112e4: LeaveFrame
    //     0x9112e4: mov             SP, fp
    //     0x9112e8: ldp             fp, lr, [SP], #0x10
    // 0x9112ec: ret
    //     0x9112ec: ret             
    // 0x9112f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9112f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9112f4: b               #0x9111b8
    // 0x9112f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9112f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9112fc: b               #0x911224
  }
}
