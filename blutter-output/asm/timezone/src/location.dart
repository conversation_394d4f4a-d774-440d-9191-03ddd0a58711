// lib: timezone.src.location, url: package:timezone/src/location.dart

// class id: 1051211, size: 0x8
class :: {
}

// class id: 425, size: 0x1c, field offset: 0x8
//   const constructor, 
class TzInstant extends Object {

  TimeZone field_8;
  _Double field_c;
  _Mint field_14;
}

// class id: 426, size: 0x18, field offset: 0x8
//   const constructor, 
class TimeZone extends Object {

  _Mint field_8;
  bool field_10;
  _OneByteString field_14;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3c38, size: 0x104
    // 0xbf3c38: EnterFrame
    //     0xbf3c38: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3c3c: mov             fp, SP
    // 0xbf3c40: AllocStack(0x10)
    //     0xbf3c40: sub             SP, SP, #0x10
    // 0xbf3c44: CheckStackOverflow
    //     0xbf3c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3c48: cmp             SP, x16
    //     0xbf3c4c: b.ls            #0xbf3d34
    // 0xbf3c50: ldr             x2, [fp, #0x10]
    // 0xbf3c54: LoadField: r3 = r2->field_7
    //     0xbf3c54: ldur            x3, [x2, #7]
    // 0xbf3c58: r0 = BoxInt64Instr(r3)
    //     0xbf3c58: sbfiz           x0, x3, #1, #0x1f
    //     0xbf3c5c: cmp             x3, x0, asr #1
    //     0xbf3c60: b.eq            #0xbf3c6c
    //     0xbf3c64: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3c68: stur            x3, [x0, #7]
    // 0xbf3c6c: r1 = 60
    //     0xbf3c6c: movz            x1, #0x3c
    // 0xbf3c70: branchIfSmi(r0, 0xbf3c7c)
    //     0xbf3c70: tbz             w0, #0, #0xbf3c7c
    // 0xbf3c74: r1 = LoadClassIdInstr(r0)
    //     0xbf3c74: ldur            x1, [x0, #-1]
    //     0xbf3c78: ubfx            x1, x1, #0xc, #0x14
    // 0xbf3c7c: str             x0, [SP]
    // 0xbf3c80: mov             x0, x1
    // 0xbf3c84: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3c84: movz            x17, #0x64af
    //     0xbf3c88: add             lr, x0, x17
    //     0xbf3c8c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3c90: blr             lr
    // 0xbf3c94: r1 = LoadInt32Instr(r0)
    //     0xbf3c94: sbfx            x1, x0, #1, #0x1f
    //     0xbf3c98: tbz             w0, #0, #0xbf3ca0
    //     0xbf3c9c: ldur            x1, [x0, #7]
    // 0xbf3ca0: add             x0, x1, #0x275
    // 0xbf3ca4: r16 = 37
    //     0xbf3ca4: movz            x16, #0x25
    // 0xbf3ca8: mul             x1, x0, x16
    // 0xbf3cac: ldr             x0, [fp, #0x10]
    // 0xbf3cb0: LoadField: r2 = r0->field_f
    //     0xbf3cb0: ldur            w2, [x0, #0xf]
    // 0xbf3cb4: DecompressPointer r2
    //     0xbf3cb4: add             x2, x2, HEAP, lsl #32
    // 0xbf3cb8: tst             x2, #0x10
    // 0xbf3cbc: cset            x3, ne
    // 0xbf3cc0: sub             x3, x3, #1
    // 0xbf3cc4: r16 = -12
    //     0xbf3cc4: movn            x16, #0xb
    // 0xbf3cc8: and             x3, x3, x16
    // 0xbf3ccc: add             x3, x3, #0x9aa
    // 0xbf3cd0: r2 = LoadInt32Instr(r3)
    //     0xbf3cd0: sbfx            x2, x3, #1, #0x1f
    // 0xbf3cd4: add             x3, x1, x2
    // 0xbf3cd8: r16 = 37
    //     0xbf3cd8: movz            x16, #0x25
    // 0xbf3cdc: mul             x1, x3, x16
    // 0xbf3ce0: stur            x1, [fp, #-8]
    // 0xbf3ce4: LoadField: r2 = r0->field_13
    //     0xbf3ce4: ldur            w2, [x0, #0x13]
    // 0xbf3ce8: DecompressPointer r2
    //     0xbf3ce8: add             x2, x2, HEAP, lsl #32
    // 0xbf3cec: r0 = LoadClassIdInstr(r2)
    //     0xbf3cec: ldur            x0, [x2, #-1]
    //     0xbf3cf0: ubfx            x0, x0, #0xc, #0x14
    // 0xbf3cf4: str             x2, [SP]
    // 0xbf3cf8: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3cf8: movz            x17, #0x64af
    //     0xbf3cfc: add             lr, x0, x17
    //     0xbf3d00: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3d04: blr             lr
    // 0xbf3d08: r2 = LoadInt32Instr(r0)
    //     0xbf3d08: sbfx            x2, x0, #1, #0x1f
    // 0xbf3d0c: ldur            x3, [fp, #-8]
    // 0xbf3d10: add             x4, x3, x2
    // 0xbf3d14: r0 = BoxInt64Instr(r4)
    //     0xbf3d14: sbfiz           x0, x4, #1, #0x1f
    //     0xbf3d18: cmp             x4, x0, asr #1
    //     0xbf3d1c: b.eq            #0xbf3d28
    //     0xbf3d20: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3d24: stur            x4, [x0, #7]
    // 0xbf3d28: LeaveFrame
    //     0xbf3d28: mov             SP, fp
    //     0xbf3d2c: ldp             fp, lr, [SP], #0x10
    // 0xbf3d30: ret
    //     0xbf3d30: ret             
    // 0xbf3d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3d34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3d38: b               #0xbf3c50
  }
  _ toString(/* No info */) {
    // ** addr: 0xc41d30, size: 0xa4
    // 0xc41d30: EnterFrame
    //     0xc41d30: stp             fp, lr, [SP, #-0x10]!
    //     0xc41d34: mov             fp, SP
    // 0xc41d38: AllocStack(0x8)
    //     0xc41d38: sub             SP, SP, #8
    // 0xc41d3c: CheckStackOverflow
    //     0xc41d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41d40: cmp             SP, x16
    //     0xc41d44: b.ls            #0xc41dcc
    // 0xc41d48: r1 = Null
    //     0xc41d48: mov             x1, NULL
    // 0xc41d4c: r2 = 14
    //     0xc41d4c: movz            x2, #0xe
    // 0xc41d50: r0 = AllocateArray()
    //     0xc41d50: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41d54: mov             x2, x0
    // 0xc41d58: r16 = "["
    //     0xc41d58: ldr             x16, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xc41d5c: StoreField: r2->field_f = r16
    //     0xc41d5c: stur            w16, [x2, #0xf]
    // 0xc41d60: ldr             x3, [fp, #0x10]
    // 0xc41d64: LoadField: r0 = r3->field_13
    //     0xc41d64: ldur            w0, [x3, #0x13]
    // 0xc41d68: DecompressPointer r0
    //     0xc41d68: add             x0, x0, HEAP, lsl #32
    // 0xc41d6c: StoreField: r2->field_13 = r0
    //     0xc41d6c: stur            w0, [x2, #0x13]
    // 0xc41d70: r16 = " offset="
    //     0xc41d70: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bab8] " offset="
    //     0xc41d74: ldr             x16, [x16, #0xab8]
    // 0xc41d78: ArrayStore: r2[0] = r16  ; List_4
    //     0xc41d78: stur            w16, [x2, #0x17]
    // 0xc41d7c: LoadField: r4 = r3->field_7
    //     0xc41d7c: ldur            x4, [x3, #7]
    // 0xc41d80: r0 = BoxInt64Instr(r4)
    //     0xc41d80: sbfiz           x0, x4, #1, #0x1f
    //     0xc41d84: cmp             x4, x0, asr #1
    //     0xc41d88: b.eq            #0xc41d94
    //     0xc41d8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc41d90: stur            x4, [x0, #7]
    // 0xc41d94: StoreField: r2->field_1b = r0
    //     0xc41d94: stur            w0, [x2, #0x1b]
    // 0xc41d98: r16 = " dst="
    //     0xc41d98: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bac0] " dst="
    //     0xc41d9c: ldr             x16, [x16, #0xac0]
    // 0xc41da0: StoreField: r2->field_1f = r16
    //     0xc41da0: stur            w16, [x2, #0x1f]
    // 0xc41da4: LoadField: r0 = r3->field_f
    //     0xc41da4: ldur            w0, [x3, #0xf]
    // 0xc41da8: DecompressPointer r0
    //     0xc41da8: add             x0, x0, HEAP, lsl #32
    // 0xc41dac: StoreField: r2->field_23 = r0
    //     0xc41dac: stur            w0, [x2, #0x23]
    // 0xc41db0: r16 = "]"
    //     0xc41db0: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xc41db4: StoreField: r2->field_27 = r16
    //     0xc41db4: stur            w16, [x2, #0x27]
    // 0xc41db8: str             x2, [SP]
    // 0xc41dbc: r0 = _interpolate()
    //     0xc41dbc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41dc0: LeaveFrame
    //     0xc41dc0: mov             SP, fp
    //     0xc41dc4: ldp             fp, lr, [SP], #0x10
    // 0xc41dc8: ret
    //     0xc41dc8: ret             
    // 0xc41dcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41dcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41dd0: b               #0xc41d48
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7e2e8, size: 0xcc
    // 0xd7e2e8: EnterFrame
    //     0xd7e2e8: stp             fp, lr, [SP, #-0x10]!
    //     0xd7e2ec: mov             fp, SP
    // 0xd7e2f0: AllocStack(0x10)
    //     0xd7e2f0: sub             SP, SP, #0x10
    // 0xd7e2f4: CheckStackOverflow
    //     0xd7e2f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7e2f8: cmp             SP, x16
    //     0xd7e2fc: b.ls            #0xd7e3ac
    // 0xd7e300: ldr             x0, [fp, #0x10]
    // 0xd7e304: cmp             w0, NULL
    // 0xd7e308: b.ne            #0xd7e31c
    // 0xd7e30c: r0 = false
    //     0xd7e30c: add             x0, NULL, #0x30  ; false
    // 0xd7e310: LeaveFrame
    //     0xd7e310: mov             SP, fp
    //     0xd7e314: ldp             fp, lr, [SP], #0x10
    // 0xd7e318: ret
    //     0xd7e318: ret             
    // 0xd7e31c: ldr             x1, [fp, #0x18]
    // 0xd7e320: cmp             w1, w0
    // 0xd7e324: b.ne            #0xd7e330
    // 0xd7e328: r0 = true
    //     0xd7e328: add             x0, NULL, #0x20  ; true
    // 0xd7e32c: b               #0xd7e3a0
    // 0xd7e330: r2 = 60
    //     0xd7e330: movz            x2, #0x3c
    // 0xd7e334: branchIfSmi(r0, 0xd7e340)
    //     0xd7e334: tbz             w0, #0, #0xd7e340
    // 0xd7e338: r2 = LoadClassIdInstr(r0)
    //     0xd7e338: ldur            x2, [x0, #-1]
    //     0xd7e33c: ubfx            x2, x2, #0xc, #0x14
    // 0xd7e340: cmp             x2, #0x1aa
    // 0xd7e344: b.ne            #0xd7e39c
    // 0xd7e348: LoadField: r2 = r1->field_7
    //     0xd7e348: ldur            x2, [x1, #7]
    // 0xd7e34c: LoadField: r3 = r0->field_7
    //     0xd7e34c: ldur            x3, [x0, #7]
    // 0xd7e350: cmp             x2, x3
    // 0xd7e354: b.ne            #0xd7e39c
    // 0xd7e358: LoadField: r2 = r1->field_f
    //     0xd7e358: ldur            w2, [x1, #0xf]
    // 0xd7e35c: DecompressPointer r2
    //     0xd7e35c: add             x2, x2, HEAP, lsl #32
    // 0xd7e360: LoadField: r3 = r0->field_f
    //     0xd7e360: ldur            w3, [x0, #0xf]
    // 0xd7e364: DecompressPointer r3
    //     0xd7e364: add             x3, x3, HEAP, lsl #32
    // 0xd7e368: cmp             w2, w3
    // 0xd7e36c: b.ne            #0xd7e39c
    // 0xd7e370: LoadField: r2 = r1->field_13
    //     0xd7e370: ldur            w2, [x1, #0x13]
    // 0xd7e374: DecompressPointer r2
    //     0xd7e374: add             x2, x2, HEAP, lsl #32
    // 0xd7e378: LoadField: r1 = r0->field_13
    //     0xd7e378: ldur            w1, [x0, #0x13]
    // 0xd7e37c: DecompressPointer r1
    //     0xd7e37c: add             x1, x1, HEAP, lsl #32
    // 0xd7e380: r0 = LoadClassIdInstr(r2)
    //     0xd7e380: ldur            x0, [x2, #-1]
    //     0xd7e384: ubfx            x0, x0, #0xc, #0x14
    // 0xd7e388: stp             x1, x2, [SP]
    // 0xd7e38c: mov             lr, x0
    // 0xd7e390: ldr             lr, [x21, lr, lsl #3]
    // 0xd7e394: blr             lr
    // 0xd7e398: b               #0xd7e3a0
    // 0xd7e39c: r0 = false
    //     0xd7e39c: add             x0, NULL, #0x30  ; false
    // 0xd7e3a0: LeaveFrame
    //     0xd7e3a0: mov             SP, fp
    //     0xd7e3a4: ldp             fp, lr, [SP], #0x10
    // 0xd7e3a8: ret
    //     0xd7e3a8: ret             
    // 0xd7e3ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7e3ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7e3b0: b               #0xd7e300
  }
}

// class id: 427, size: 0x2c, field offset: 0x8
class Location extends Object {

  static late final int _cacheNow; // offset: 0x12a8
  late TimeZone _cacheZone; // offset: 0x28

  _ timeZone(/* No info */) {
    // ** addr: 0x6fd4ec, size: 0x38
    // 0x6fd4ec: EnterFrame
    //     0x6fd4ec: stp             fp, lr, [SP, #-0x10]!
    //     0x6fd4f0: mov             fp, SP
    // 0x6fd4f4: CheckStackOverflow
    //     0x6fd4f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd4f8: cmp             SP, x16
    //     0x6fd4fc: b.ls            #0x6fd51c
    // 0x6fd500: r0 = lookupTimeZone()
    //     0x6fd500: bl              #0x6fd544  ; [package:timezone/src/location.dart] Location::lookupTimeZone
    // 0x6fd504: LoadField: r1 = r0->field_7
    //     0x6fd504: ldur            w1, [x0, #7]
    // 0x6fd508: DecompressPointer r1
    //     0x6fd508: add             x1, x1, HEAP, lsl #32
    // 0x6fd50c: mov             x0, x1
    // 0x6fd510: LeaveFrame
    //     0x6fd510: mov             SP, fp
    //     0x6fd514: ldp             fp, lr, [SP], #0x10
    // 0x6fd518: ret
    //     0x6fd518: ret             
    // 0x6fd51c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fd51c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fd520: b               #0x6fd500
  }
  _ lookupTimeZone(/* No info */) {
    // ** addr: 0x6fd544, size: 0x2d0
    // 0x6fd544: EnterFrame
    //     0x6fd544: stp             fp, lr, [SP, #-0x10]!
    //     0x6fd548: mov             fp, SP
    // 0x6fd54c: AllocStack(0x20)
    //     0x6fd54c: sub             SP, SP, #0x20
    // 0x6fd550: SetupParameters(Location this /* r1 => r3 */)
    //     0x6fd550: mov             x3, x1
    // 0x6fd554: CheckStackOverflow
    //     0x6fd554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd558: cmp             SP, x16
    //     0x6fd55c: b.ls            #0x6fd7e4
    // 0x6fd560: LoadField: r4 = r3->field_13
    //     0x6fd560: ldur            w4, [x3, #0x13]
    // 0x6fd564: DecompressPointer r4
    //     0x6fd564: add             x4, x4, HEAP, lsl #32
    // 0x6fd568: LoadField: r0 = r4->field_b
    //     0x6fd568: ldur            w0, [x4, #0xb]
    // 0x6fd56c: r5 = LoadInt32Instr(r0)
    //     0x6fd56c: sbfx            x5, x0, #1, #0x1f
    // 0x6fd570: cbnz            w0, #0x6fd588
    // 0x6fd574: r0 = Instance_TzInstant
    //     0x6fd574: add             x0, PP, #8, lsl #12  ; [pp+0x8d10] Obj!TzInstant@e0bef1
    //     0x6fd578: ldr             x0, [x0, #0xd10]
    // 0x6fd57c: LeaveFrame
    //     0x6fd57c: mov             SP, fp
    //     0x6fd580: ldp             fp, lr, [SP], #0x10
    // 0x6fd584: ret
    //     0x6fd584: ret             
    // 0x6fd588: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x6fd588: ldur            x0, [x3, #0x17]
    // 0x6fd58c: stur            x0, [fp, #-0x18]
    // 0x6fd590: cmp             x2, x0
    // 0x6fd594: b.lt            #0x6fd5f0
    // 0x6fd598: LoadField: r1 = r3->field_1f
    //     0x6fd598: ldur            x1, [x3, #0x1f]
    // 0x6fd59c: stur            x1, [fp, #-0x10]
    // 0x6fd5a0: cmp             x2, x1
    // 0x6fd5a4: b.ge            #0x6fd5f0
    // 0x6fd5a8: LoadField: r2 = r3->field_27
    //     0x6fd5a8: ldur            w2, [x3, #0x27]
    // 0x6fd5ac: DecompressPointer r2
    //     0x6fd5ac: add             x2, x2, HEAP, lsl #32
    // 0x6fd5b0: r16 = Sentinel
    //     0x6fd5b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6fd5b4: cmp             w2, w16
    // 0x6fd5b8: b.eq            #0x6fd7ec
    // 0x6fd5bc: stur            x2, [fp, #-8]
    // 0x6fd5c0: r0 = TzInstant()
    //     0x6fd5c0: bl              #0x6fda98  ; AllocateTzInstantStub -> TzInstant (size=0x1c)
    // 0x6fd5c4: mov             x1, x0
    // 0x6fd5c8: ldur            x0, [fp, #-8]
    // 0x6fd5cc: StoreField: r1->field_7 = r0
    //     0x6fd5cc: stur            w0, [x1, #7]
    // 0x6fd5d0: ldur            x0, [fp, #-0x18]
    // 0x6fd5d4: StoreField: r1->field_b = r0
    //     0x6fd5d4: stur            x0, [x1, #0xb]
    // 0x6fd5d8: ldur            x0, [fp, #-0x10]
    // 0x6fd5dc: StoreField: r1->field_13 = r0
    //     0x6fd5dc: stur            x0, [x1, #0x13]
    // 0x6fd5e0: mov             x0, x1
    // 0x6fd5e4: LeaveFrame
    //     0x6fd5e4: mov             SP, fp
    //     0x6fd5e8: ldp             fp, lr, [SP], #0x10
    // 0x6fd5ec: ret
    //     0x6fd5ec: ret             
    // 0x6fd5f0: LoadField: r6 = r3->field_b
    //     0x6fd5f0: ldur            w6, [x3, #0xb]
    // 0x6fd5f4: DecompressPointer r6
    //     0x6fd5f4: add             x6, x6, HEAP, lsl #32
    // 0x6fd5f8: stur            x6, [fp, #-8]
    // 0x6fd5fc: LoadField: r0 = r6->field_b
    //     0x6fd5fc: ldur            w0, [x6, #0xb]
    // 0x6fd600: r7 = LoadInt32Instr(r0)
    //     0x6fd600: sbfx            x7, x0, #1, #0x1f
    // 0x6fd604: cbz             w0, #0x6fd63c
    // 0x6fd608: mov             x0, x7
    // 0x6fd60c: r1 = 0
    //     0x6fd60c: movz            x1, #0
    // 0x6fd610: cmp             x1, x0
    // 0x6fd614: b.hs            #0x6fd7f8
    // 0x6fd618: LoadField: r8 = r6->field_f
    //     0x6fd618: ldur            w8, [x6, #0xf]
    // 0x6fd61c: DecompressPointer r8
    //     0x6fd61c: add             x8, x8, HEAP, lsl #32
    // 0x6fd620: LoadField: r0 = r8->field_f
    //     0x6fd620: ldur            w0, [x8, #0xf]
    // 0x6fd624: DecompressPointer r0
    //     0x6fd624: add             x0, x0, HEAP, lsl #32
    // 0x6fd628: r1 = LoadInt32Instr(r0)
    //     0x6fd628: sbfx            x1, x0, #1, #0x1f
    //     0x6fd62c: tbz             w0, #0, #0x6fd634
    //     0x6fd630: ldur            x1, [x0, #7]
    // 0x6fd634: cmp             x2, x1
    // 0x6fd638: b.ge            #0x6fd6ac
    // 0x6fd63c: mov             x1, x3
    // 0x6fd640: r0 = _firstZone()
    //     0x6fd640: bl              #0x6fd814  ; [package:timezone/src/location.dart] Location::_firstZone
    // 0x6fd644: ldur            x1, [fp, #-8]
    // 0x6fd648: stur            x0, [fp, #-0x20]
    // 0x6fd64c: LoadField: r2 = r1->field_b
    //     0x6fd64c: ldur            w2, [x1, #0xb]
    // 0x6fd650: cbnz            w2, #0x6fd660
    // 0x6fd654: r1 = 8640000000000000
    //     0x6fd654: add             x1, PP, #8, lsl #12  ; [pp+0x87d0] IMM: 0x1eb208c2dc0000
    //     0x6fd658: ldr             x1, [x1, #0x7d0]
    // 0x6fd65c: b               #0x6fd674
    // 0x6fd660: r0 = first()
    //     0x6fd660: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6fd664: r1 = LoadInt32Instr(r0)
    //     0x6fd664: sbfx            x1, x0, #1, #0x1f
    //     0x6fd668: tbz             w0, #0, #0x6fd670
    //     0x6fd66c: ldur            x1, [x0, #7]
    // 0x6fd670: ldur            x0, [fp, #-0x20]
    // 0x6fd674: stur            x1, [fp, #-0x10]
    // 0x6fd678: r0 = TzInstant()
    //     0x6fd678: bl              #0x6fda98  ; AllocateTzInstantStub -> TzInstant (size=0x1c)
    // 0x6fd67c: mov             x1, x0
    // 0x6fd680: ldur            x0, [fp, #-0x20]
    // 0x6fd684: StoreField: r1->field_7 = r0
    //     0x6fd684: stur            w0, [x1, #7]
    // 0x6fd688: r0 = -8640000000000000
    //     0x6fd688: add             x0, PP, #8, lsl #12  ; [pp+0x8d18] IMM: double(-9.72133674855476e+307) from 0xffe14df73d240000
    //     0x6fd68c: ldr             x0, [x0, #0xd18]
    // 0x6fd690: StoreField: r1->field_b = r0
    //     0x6fd690: stur            x0, [x1, #0xb]
    // 0x6fd694: ldur            x0, [fp, #-0x10]
    // 0x6fd698: StoreField: r1->field_13 = r0
    //     0x6fd698: stur            x0, [x1, #0x13]
    // 0x6fd69c: mov             x0, x1
    // 0x6fd6a0: LeaveFrame
    //     0x6fd6a0: mov             SP, fp
    //     0x6fd6a4: ldp             fp, lr, [SP], #0x10
    // 0x6fd6a8: ret
    //     0x6fd6a8: ret             
    // 0x6fd6ac: mov             x10, x7
    // 0x6fd6b0: r11 = 0
    //     0x6fd6b0: movz            x11, #0
    // 0x6fd6b4: r9 = 8640000000000000
    //     0x6fd6b4: add             x9, PP, #8, lsl #12  ; [pp+0x87d0] IMM: 0x1eb208c2dc0000
    //     0x6fd6b8: ldr             x9, [x9, #0x7d0]
    // 0x6fd6bc: r6 = 2
    //     0x6fd6bc: movz            x6, #0x2
    // 0x6fd6c0: stur            x9, [fp, #-0x10]
    // 0x6fd6c4: CheckStackOverflow
    //     0x6fd6c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd6c8: cmp             SP, x16
    //     0x6fd6cc: b.ls            #0x6fd7fc
    // 0x6fd6d0: sub             x0, x10, x11
    // 0x6fd6d4: cmp             x0, #1
    // 0x6fd6d8: b.le            #0x6fd728
    // 0x6fd6dc: sdiv            x1, x0, x6
    // 0x6fd6e0: add             x12, x11, x1
    // 0x6fd6e4: mov             x0, x7
    // 0x6fd6e8: mov             x1, x12
    // 0x6fd6ec: cmp             x1, x0
    // 0x6fd6f0: b.hs            #0x6fd804
    // 0x6fd6f4: ArrayLoad: r0 = r8[r12]  ; Unknown_4
    //     0x6fd6f4: add             x16, x8, x12, lsl #2
    //     0x6fd6f8: ldur            w0, [x16, #0xf]
    // 0x6fd6fc: DecompressPointer r0
    //     0x6fd6fc: add             x0, x0, HEAP, lsl #32
    // 0x6fd700: r1 = LoadInt32Instr(r0)
    //     0x6fd700: sbfx            x1, x0, #1, #0x1f
    //     0x6fd704: tbz             w0, #0, #0x6fd70c
    //     0x6fd708: ldur            x1, [x0, #7]
    // 0x6fd70c: cmp             x2, x1
    // 0x6fd710: b.ge            #0x6fd720
    // 0x6fd714: mov             x10, x12
    // 0x6fd718: mov             x9, x1
    // 0x6fd71c: b               #0x6fd6c0
    // 0x6fd720: mov             x11, x12
    // 0x6fd724: b               #0x6fd6c0
    // 0x6fd728: LoadField: r2 = r3->field_f
    //     0x6fd728: ldur            w2, [x3, #0xf]
    // 0x6fd72c: DecompressPointer r2
    //     0x6fd72c: add             x2, x2, HEAP, lsl #32
    // 0x6fd730: LoadField: r0 = r2->field_b
    //     0x6fd730: ldur            w0, [x2, #0xb]
    // 0x6fd734: r1 = LoadInt32Instr(r0)
    //     0x6fd734: sbfx            x1, x0, #1, #0x1f
    // 0x6fd738: mov             x0, x1
    // 0x6fd73c: mov             x1, x11
    // 0x6fd740: cmp             x1, x0
    // 0x6fd744: b.hs            #0x6fd808
    // 0x6fd748: LoadField: r0 = r2->field_f
    //     0x6fd748: ldur            w0, [x2, #0xf]
    // 0x6fd74c: DecompressPointer r0
    //     0x6fd74c: add             x0, x0, HEAP, lsl #32
    // 0x6fd750: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0x6fd750: add             x16, x0, x11, lsl #2
    //     0x6fd754: ldur            w1, [x16, #0xf]
    // 0x6fd758: DecompressPointer r1
    //     0x6fd758: add             x1, x1, HEAP, lsl #32
    // 0x6fd75c: r2 = LoadInt32Instr(r1)
    //     0x6fd75c: sbfx            x2, x1, #1, #0x1f
    //     0x6fd760: tbz             w1, #0, #0x6fd768
    //     0x6fd764: ldur            x2, [x1, #7]
    // 0x6fd768: mov             x0, x5
    // 0x6fd76c: mov             x1, x2
    // 0x6fd770: cmp             x1, x0
    // 0x6fd774: b.hs            #0x6fd80c
    // 0x6fd778: LoadField: r0 = r4->field_f
    //     0x6fd778: ldur            w0, [x4, #0xf]
    // 0x6fd77c: DecompressPointer r0
    //     0x6fd77c: add             x0, x0, HEAP, lsl #32
    // 0x6fd780: ArrayLoad: r3 = r0[r2]  ; Unknown_4
    //     0x6fd780: add             x16, x0, x2, lsl #2
    //     0x6fd784: ldur            w3, [x16, #0xf]
    // 0x6fd788: DecompressPointer r3
    //     0x6fd788: add             x3, x3, HEAP, lsl #32
    // 0x6fd78c: mov             x0, x7
    // 0x6fd790: mov             x1, x11
    // 0x6fd794: stur            x3, [fp, #-0x20]
    // 0x6fd798: cmp             x1, x0
    // 0x6fd79c: b.hs            #0x6fd810
    // 0x6fd7a0: ArrayLoad: r0 = r8[r11]  ; Unknown_4
    //     0x6fd7a0: add             x16, x8, x11, lsl #2
    //     0x6fd7a4: ldur            w0, [x16, #0xf]
    // 0x6fd7a8: DecompressPointer r0
    //     0x6fd7a8: add             x0, x0, HEAP, lsl #32
    // 0x6fd7ac: stur            x0, [fp, #-8]
    // 0x6fd7b0: r0 = TzInstant()
    //     0x6fd7b0: bl              #0x6fda98  ; AllocateTzInstantStub -> TzInstant (size=0x1c)
    // 0x6fd7b4: ldur            x1, [fp, #-0x20]
    // 0x6fd7b8: StoreField: r0->field_7 = r1
    //     0x6fd7b8: stur            w1, [x0, #7]
    // 0x6fd7bc: ldur            x1, [fp, #-8]
    // 0x6fd7c0: r2 = LoadInt32Instr(r1)
    //     0x6fd7c0: sbfx            x2, x1, #1, #0x1f
    //     0x6fd7c4: tbz             w1, #0, #0x6fd7cc
    //     0x6fd7c8: ldur            x2, [x1, #7]
    // 0x6fd7cc: StoreField: r0->field_b = r2
    //     0x6fd7cc: stur            x2, [x0, #0xb]
    // 0x6fd7d0: ldur            x1, [fp, #-0x10]
    // 0x6fd7d4: StoreField: r0->field_13 = r1
    //     0x6fd7d4: stur            x1, [x0, #0x13]
    // 0x6fd7d8: LeaveFrame
    //     0x6fd7d8: mov             SP, fp
    //     0x6fd7dc: ldp             fp, lr, [SP], #0x10
    // 0x6fd7e0: ret
    //     0x6fd7e0: ret             
    // 0x6fd7e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fd7e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fd7e8: b               #0x6fd560
    // 0x6fd7ec: r9 = _cacheZone
    //     0x6fd7ec: add             x9, PP, #8, lsl #12  ; [pp+0x8d20] Field <Location._cacheZone@1196066103>: late (offset: 0x28)
    //     0x6fd7f0: ldr             x9, [x9, #0xd20]
    // 0x6fd7f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6fd7f4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x6fd7f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fd7f8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fd7fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fd7fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fd800: b               #0x6fd6d0
    // 0x6fd804: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fd804: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fd808: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fd808: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fd80c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fd80c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fd810: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fd810: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _firstZone(/* No info */) {
    // ** addr: 0x6fd814, size: 0x210
    // 0x6fd814: EnterFrame
    //     0x6fd814: stp             fp, lr, [SP, #-0x10]!
    //     0x6fd818: mov             fp, SP
    // 0x6fd81c: AllocStack(0x18)
    //     0x6fd81c: sub             SP, SP, #0x18
    // 0x6fd820: SetupParameters(Location this /* r1 => r0, fp-0x8 */)
    //     0x6fd820: mov             x0, x1
    //     0x6fd824: stur            x1, [fp, #-8]
    // 0x6fd828: CheckStackOverflow
    //     0x6fd828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd82c: cmp             SP, x16
    //     0x6fd830: b.ls            #0x6fda00
    // 0x6fd834: mov             x1, x0
    // 0x6fd838: r0 = _firstZoneIsUsed()
    //     0x6fd838: bl              #0x6fda24  ; [package:timezone/src/location.dart] Location::_firstZoneIsUsed
    // 0x6fd83c: tbz             w0, #4, #0x6fd85c
    // 0x6fd840: ldur            x0, [fp, #-8]
    // 0x6fd844: LoadField: r1 = r0->field_13
    //     0x6fd844: ldur            w1, [x0, #0x13]
    // 0x6fd848: DecompressPointer r1
    //     0x6fd848: add             x1, x1, HEAP, lsl #32
    // 0x6fd84c: r0 = first()
    //     0x6fd84c: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6fd850: LeaveFrame
    //     0x6fd850: mov             SP, fp
    //     0x6fd854: ldp             fp, lr, [SP], #0x10
    // 0x6fd858: ret
    //     0x6fd858: ret             
    // 0x6fd85c: ldur            x0, [fp, #-8]
    // 0x6fd860: LoadField: r2 = r0->field_f
    //     0x6fd860: ldur            w2, [x0, #0xf]
    // 0x6fd864: DecompressPointer r2
    //     0x6fd864: add             x2, x2, HEAP, lsl #32
    // 0x6fd868: stur            x2, [fp, #-0x18]
    // 0x6fd86c: LoadField: r1 = r2->field_b
    //     0x6fd86c: ldur            w1, [x2, #0xb]
    // 0x6fd870: cbz             w1, #0x6fd94c
    // 0x6fd874: LoadField: r3 = r0->field_13
    //     0x6fd874: ldur            w3, [x0, #0x13]
    // 0x6fd878: DecompressPointer r3
    //     0x6fd878: add             x3, x3, HEAP, lsl #32
    // 0x6fd87c: mov             x1, x2
    // 0x6fd880: stur            x3, [fp, #-0x10]
    // 0x6fd884: r0 = first()
    //     0x6fd884: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6fd888: ldur            x2, [fp, #-0x10]
    // 0x6fd88c: LoadField: r1 = r2->field_b
    //     0x6fd88c: ldur            w1, [x2, #0xb]
    // 0x6fd890: r3 = LoadInt32Instr(r0)
    //     0x6fd890: sbfx            x3, x0, #1, #0x1f
    //     0x6fd894: tbz             w0, #0, #0x6fd89c
    //     0x6fd898: ldur            x3, [x0, #7]
    // 0x6fd89c: r0 = LoadInt32Instr(r1)
    //     0x6fd89c: sbfx            x0, x1, #1, #0x1f
    // 0x6fd8a0: mov             x1, x3
    // 0x6fd8a4: cmp             x1, x0
    // 0x6fd8a8: b.hs            #0x6fda08
    // 0x6fd8ac: LoadField: r0 = r2->field_f
    //     0x6fd8ac: ldur            w0, [x2, #0xf]
    // 0x6fd8b0: DecompressPointer r0
    //     0x6fd8b0: add             x0, x0, HEAP, lsl #32
    // 0x6fd8b4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x6fd8b4: add             x16, x0, x3, lsl #2
    //     0x6fd8b8: ldur            w1, [x16, #0xf]
    // 0x6fd8bc: DecompressPointer r1
    //     0x6fd8bc: add             x1, x1, HEAP, lsl #32
    // 0x6fd8c0: LoadField: r0 = r1->field_f
    //     0x6fd8c0: ldur            w0, [x1, #0xf]
    // 0x6fd8c4: DecompressPointer r0
    //     0x6fd8c4: add             x0, x0, HEAP, lsl #32
    // 0x6fd8c8: tbnz            w0, #4, #0x6fd94c
    // 0x6fd8cc: ldur            x1, [fp, #-0x18]
    // 0x6fd8d0: r0 = first()
    //     0x6fd8d0: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6fd8d4: r1 = LoadInt32Instr(r0)
    //     0x6fd8d4: sbfx            x1, x0, #1, #0x1f
    //     0x6fd8d8: tbz             w0, #0, #0x6fd8e0
    //     0x6fd8dc: ldur            x1, [x0, #7]
    // 0x6fd8e0: sub             x0, x1, #1
    // 0x6fd8e4: ldur            x1, [fp, #-0x10]
    // 0x6fd8e8: LoadField: r2 = r1->field_b
    //     0x6fd8e8: ldur            w2, [x1, #0xb]
    // 0x6fd8ec: r3 = LoadInt32Instr(r2)
    //     0x6fd8ec: sbfx            x3, x2, #1, #0x1f
    // 0x6fd8f0: LoadField: r2 = r1->field_f
    //     0x6fd8f0: ldur            w2, [x1, #0xf]
    // 0x6fd8f4: DecompressPointer r2
    //     0x6fd8f4: add             x2, x2, HEAP, lsl #32
    // 0x6fd8f8: mov             x4, x0
    // 0x6fd8fc: CheckStackOverflow
    //     0x6fd8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd900: cmp             SP, x16
    //     0x6fd904: b.ls            #0x6fda0c
    // 0x6fd908: tbnz            x4, #0x3f, #0x6fd94c
    // 0x6fd90c: mov             x0, x3
    // 0x6fd910: mov             x1, x4
    // 0x6fd914: cmp             x1, x0
    // 0x6fd918: b.hs            #0x6fda14
    // 0x6fd91c: ArrayLoad: r0 = r2[r4]  ; Unknown_4
    //     0x6fd91c: add             x16, x2, x4, lsl #2
    //     0x6fd920: ldur            w0, [x16, #0xf]
    // 0x6fd924: DecompressPointer r0
    //     0x6fd924: add             x0, x0, HEAP, lsl #32
    // 0x6fd928: LoadField: r1 = r0->field_f
    //     0x6fd928: ldur            w1, [x0, #0xf]
    // 0x6fd92c: DecompressPointer r1
    //     0x6fd92c: add             x1, x1, HEAP, lsl #32
    // 0x6fd930: tbnz            w1, #4, #0x6fd940
    // 0x6fd934: sub             x0, x4, #1
    // 0x6fd938: mov             x4, x0
    // 0x6fd93c: b               #0x6fd8fc
    // 0x6fd940: LeaveFrame
    //     0x6fd940: mov             SP, fp
    //     0x6fd944: ldp             fp, lr, [SP], #0x10
    // 0x6fd948: ret
    //     0x6fd948: ret             
    // 0x6fd94c: ldur            x0, [fp, #-8]
    // 0x6fd950: ldur            x1, [fp, #-0x18]
    // 0x6fd954: LoadField: r2 = r1->field_b
    //     0x6fd954: ldur            w2, [x1, #0xb]
    // 0x6fd958: r3 = LoadInt32Instr(r2)
    //     0x6fd958: sbfx            x3, x2, #1, #0x1f
    // 0x6fd95c: LoadField: r2 = r1->field_f
    //     0x6fd95c: ldur            w2, [x1, #0xf]
    // 0x6fd960: DecompressPointer r2
    //     0x6fd960: add             x2, x2, HEAP, lsl #32
    // 0x6fd964: LoadField: r4 = r0->field_13
    //     0x6fd964: ldur            w4, [x0, #0x13]
    // 0x6fd968: DecompressPointer r4
    //     0x6fd968: add             x4, x4, HEAP, lsl #32
    // 0x6fd96c: LoadField: r0 = r4->field_b
    //     0x6fd96c: ldur            w0, [x4, #0xb]
    // 0x6fd970: r5 = LoadInt32Instr(r0)
    //     0x6fd970: sbfx            x5, x0, #1, #0x1f
    // 0x6fd974: LoadField: r6 = r4->field_f
    //     0x6fd974: ldur            w6, [x4, #0xf]
    // 0x6fd978: DecompressPointer r6
    //     0x6fd978: add             x6, x6, HEAP, lsl #32
    // 0x6fd97c: r0 = 0
    //     0x6fd97c: movz            x0, #0
    // 0x6fd980: CheckStackOverflow
    //     0x6fd980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fd984: cmp             SP, x16
    //     0x6fd988: b.ls            #0x6fda18
    // 0x6fd98c: cmp             x0, x3
    // 0x6fd990: b.ge            #0x6fd9ec
    // 0x6fd994: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0x6fd994: add             x16, x2, x0, lsl #2
    //     0x6fd998: ldur            w1, [x16, #0xf]
    // 0x6fd99c: DecompressPointer r1
    //     0x6fd99c: add             x1, x1, HEAP, lsl #32
    // 0x6fd9a0: add             x7, x0, #1
    // 0x6fd9a4: r8 = LoadInt32Instr(r1)
    //     0x6fd9a4: sbfx            x8, x1, #1, #0x1f
    //     0x6fd9a8: tbz             w1, #0, #0x6fd9b0
    //     0x6fd9ac: ldur            x8, [x1, #7]
    // 0x6fd9b0: mov             x0, x5
    // 0x6fd9b4: mov             x1, x8
    // 0x6fd9b8: cmp             x1, x0
    // 0x6fd9bc: b.hs            #0x6fda20
    // 0x6fd9c0: ArrayLoad: r0 = r6[r8]  ; Unknown_4
    //     0x6fd9c0: add             x16, x6, x8, lsl #2
    //     0x6fd9c4: ldur            w0, [x16, #0xf]
    // 0x6fd9c8: DecompressPointer r0
    //     0x6fd9c8: add             x0, x0, HEAP, lsl #32
    // 0x6fd9cc: LoadField: r1 = r0->field_f
    //     0x6fd9cc: ldur            w1, [x0, #0xf]
    // 0x6fd9d0: DecompressPointer r1
    //     0x6fd9d0: add             x1, x1, HEAP, lsl #32
    // 0x6fd9d4: tbnz            w1, #4, #0x6fd9e0
    // 0x6fd9d8: mov             x0, x7
    // 0x6fd9dc: b               #0x6fd980
    // 0x6fd9e0: LeaveFrame
    //     0x6fd9e0: mov             SP, fp
    //     0x6fd9e4: ldp             fp, lr, [SP], #0x10
    // 0x6fd9e8: ret
    //     0x6fd9e8: ret             
    // 0x6fd9ec: mov             x1, x4
    // 0x6fd9f0: r0 = first()
    //     0x6fd9f0: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x6fd9f4: LeaveFrame
    //     0x6fd9f4: mov             SP, fp
    //     0x6fd9f8: ldp             fp, lr, [SP], #0x10
    // 0x6fd9fc: ret
    //     0x6fd9fc: ret             
    // 0x6fda00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fda00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fda04: b               #0x6fd834
    // 0x6fda08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fda08: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fda0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fda0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fda10: b               #0x6fd908
    // 0x6fda14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fda14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fda18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fda18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fda1c: b               #0x6fd98c
    // 0x6fda20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fda20: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _firstZoneIsUsed(/* No info */) {
    // ** addr: 0x6fda24, size: 0x74
    // 0x6fda24: LoadField: r2 = r1->field_f
    //     0x6fda24: ldur            w2, [x1, #0xf]
    // 0x6fda28: DecompressPointer r2
    //     0x6fda28: add             x2, x2, HEAP, lsl #32
    // 0x6fda2c: LoadField: r1 = r2->field_b
    //     0x6fda2c: ldur            w1, [x2, #0xb]
    // 0x6fda30: r3 = LoadInt32Instr(r1)
    //     0x6fda30: sbfx            x3, x1, #1, #0x1f
    // 0x6fda34: LoadField: r1 = r2->field_f
    //     0x6fda34: ldur            w1, [x2, #0xf]
    // 0x6fda38: DecompressPointer r1
    //     0x6fda38: add             x1, x1, HEAP, lsl #32
    // 0x6fda3c: r2 = 0
    //     0x6fda3c: movz            x2, #0
    // 0x6fda40: CheckStackOverflow
    //     0x6fda40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fda44: cmp             SP, x16
    //     0x6fda48: b.ls            #0x6fda80
    // 0x6fda4c: cmp             x2, x3
    // 0x6fda50: b.ge            #0x6fda78
    // 0x6fda54: ArrayLoad: r4 = r1[r2]  ; Unknown_4
    //     0x6fda54: add             x16, x1, x2, lsl #2
    //     0x6fda58: ldur            w4, [x16, #0xf]
    // 0x6fda5c: DecompressPointer r4
    //     0x6fda5c: add             x4, x4, HEAP, lsl #32
    // 0x6fda60: add             x0, x2, #1
    // 0x6fda64: cbz             w4, #0x6fda70
    // 0x6fda68: mov             x2, x0
    // 0x6fda6c: b               #0x6fda40
    // 0x6fda70: r0 = true
    //     0x6fda70: add             x0, NULL, #0x20  ; true
    // 0x6fda74: ret
    //     0x6fda74: ret             
    // 0x6fda78: r0 = false
    //     0x6fda78: add             x0, NULL, #0x30  ; false
    // 0x6fda7c: ret
    //     0x6fda7c: ret             
    // 0x6fda80: EnterFrame
    //     0x6fda80: stp             fp, lr, [SP, #-0x10]!
    //     0x6fda84: mov             fp, SP
    // 0x6fda88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fda88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fda8c: LeaveFrame
    //     0x6fda8c: mov             SP, fp
    //     0x6fda90: ldp             fp, lr, [SP], #0x10
    // 0x6fda94: b               #0x6fda4c
  }
  _ Location(/* No info */) {
    // ** addr: 0x6fdbcc, size: 0x2d8
    // 0x6fdbcc: EnterFrame
    //     0x6fdbcc: stp             fp, lr, [SP, #-0x10]!
    //     0x6fdbd0: mov             fp, SP
    // 0x6fdbd4: AllocStack(0x30)
    //     0x6fdbd4: sub             SP, SP, #0x30
    // 0x6fdbd8: r0 = Sentinel
    //     0x6fdbd8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6fdbdc: mov             x4, x2
    // 0x6fdbe0: mov             x2, x5
    // 0x6fdbe4: stur            x5, [fp, #-0x28]
    // 0x6fdbe8: mov             x5, x1
    // 0x6fdbec: stur            x1, [fp, #-0x18]
    // 0x6fdbf0: mov             x1, x6
    // 0x6fdbf4: stur            x3, [fp, #-0x20]
    // 0x6fdbf8: stur            x6, [fp, #-0x30]
    // 0x6fdbfc: CheckStackOverflow
    //     0x6fdbfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fdc00: cmp             SP, x16
    //     0x6fdc04: b.ls            #0x6fde88
    // 0x6fdc08: ArrayStore: r5[0] = rZR  ; List_8
    //     0x6fdc08: stur            xzr, [x5, #0x17]
    // 0x6fdc0c: StoreField: r5->field_1f = rZR
    //     0x6fdc0c: stur            xzr, [x5, #0x1f]
    // 0x6fdc10: StoreField: r5->field_27 = r0
    //     0x6fdc10: stur            w0, [x5, #0x27]
    // 0x6fdc14: mov             x0, x4
    // 0x6fdc18: StoreField: r5->field_7 = r0
    //     0x6fdc18: stur            w0, [x5, #7]
    //     0x6fdc1c: ldurb           w16, [x5, #-1]
    //     0x6fdc20: ldurb           w17, [x0, #-1]
    //     0x6fdc24: and             x16, x17, x16, lsr #2
    //     0x6fdc28: tst             x16, HEAP, lsr #32
    //     0x6fdc2c: b.eq            #0x6fdc34
    //     0x6fdc30: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x6fdc34: mov             x0, x3
    // 0x6fdc38: StoreField: r5->field_b = r0
    //     0x6fdc38: stur            w0, [x5, #0xb]
    //     0x6fdc3c: ldurb           w16, [x5, #-1]
    //     0x6fdc40: ldurb           w17, [x0, #-1]
    //     0x6fdc44: and             x16, x17, x16, lsr #2
    //     0x6fdc48: tst             x16, HEAP, lsr #32
    //     0x6fdc4c: b.eq            #0x6fdc54
    //     0x6fdc50: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x6fdc54: mov             x0, x2
    // 0x6fdc58: StoreField: r5->field_f = r0
    //     0x6fdc58: stur            w0, [x5, #0xf]
    //     0x6fdc5c: ldurb           w16, [x5, #-1]
    //     0x6fdc60: ldurb           w17, [x0, #-1]
    //     0x6fdc64: and             x16, x17, x16, lsr #2
    //     0x6fdc68: tst             x16, HEAP, lsr #32
    //     0x6fdc6c: b.eq            #0x6fdc74
    //     0x6fdc70: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x6fdc74: mov             x0, x1
    // 0x6fdc78: StoreField: r5->field_13 = r0
    //     0x6fdc78: stur            w0, [x5, #0x13]
    //     0x6fdc7c: ldurb           w16, [x5, #-1]
    //     0x6fdc80: ldurb           w17, [x0, #-1]
    //     0x6fdc84: and             x16, x17, x16, lsr #2
    //     0x6fdc88: tst             x16, HEAP, lsr #32
    //     0x6fdc8c: b.eq            #0x6fdc94
    //     0x6fdc90: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x6fdc94: r0 = 0
    //     0x6fdc94: movz            x0, #0
    // 0x6fdc98: stur            x0, [fp, #-0x10]
    // 0x6fdc9c: CheckStackOverflow
    //     0x6fdc9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fdca0: cmp             SP, x16
    //     0x6fdca4: b.ls            #0x6fde90
    // 0x6fdca8: LoadField: r4 = r3->field_b
    //     0x6fdca8: ldur            w4, [x3, #0xb]
    // 0x6fdcac: r6 = LoadInt32Instr(r4)
    //     0x6fdcac: sbfx            x6, x4, #1, #0x1f
    // 0x6fdcb0: cmp             x0, x6
    // 0x6fdcb4: b.ge            #0x6fde78
    // 0x6fdcb8: LoadField: r4 = r3->field_f
    //     0x6fdcb8: ldur            w4, [x3, #0xf]
    // 0x6fdcbc: DecompressPointer r4
    //     0x6fdcbc: add             x4, x4, HEAP, lsl #32
    // 0x6fdcc0: ArrayLoad: r6 = r4[r0]  ; Unknown_4
    //     0x6fdcc0: add             x16, x4, x0, lsl #2
    //     0x6fdcc4: ldur            w6, [x16, #0xf]
    // 0x6fdcc8: DecompressPointer r6
    //     0x6fdcc8: add             x6, x6, HEAP, lsl #32
    // 0x6fdccc: stur            x6, [fp, #-8]
    // 0x6fdcd0: r0 = InitLateStaticField(0x12a8) // [package:timezone/src/location.dart] Location::_cacheNow
    //     0x6fdcd0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6fdcd4: ldr             x0, [x0, #0x2550]
    //     0x6fdcd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6fdcdc: cmp             w0, w16
    //     0x6fdce0: b.ne            #0x6fdcf0
    //     0x6fdce4: add             x2, PP, #8, lsl #12  ; [pp+0x87c8] Field <Location._cacheNow@1196066103>: static late final (offset: 0x12a8)
    //     0x6fdce8: ldr             x2, [x2, #0x7c8]
    //     0x6fdcec: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x6fdcf0: ldur            x2, [fp, #-8]
    // 0x6fdcf4: r3 = LoadInt32Instr(r2)
    //     0x6fdcf4: sbfx            x3, x2, #1, #0x1f
    //     0x6fdcf8: tbz             w2, #0, #0x6fdd00
    //     0x6fdcfc: ldur            x3, [x2, #7]
    // 0x6fdd00: r2 = LoadInt32Instr(r0)
    //     0x6fdd00: sbfx            x2, x0, #1, #0x1f
    //     0x6fdd04: tbz             w0, #0, #0x6fdd0c
    //     0x6fdd08: ldur            x2, [x0, #7]
    // 0x6fdd0c: cmp             x3, x2
    // 0x6fdd10: b.gt            #0x6fde44
    // 0x6fdd14: ldur            x4, [fp, #-0x20]
    // 0x6fdd18: ldur            x5, [fp, #-0x10]
    // 0x6fdd1c: add             x6, x5, #1
    // 0x6fdd20: LoadField: r7 = r4->field_b
    //     0x6fdd20: ldur            w7, [x4, #0xb]
    // 0x6fdd24: r8 = LoadInt32Instr(r7)
    //     0x6fdd24: sbfx            x8, x7, #1, #0x1f
    // 0x6fdd28: cmp             x6, x8
    // 0x6fdd2c: b.eq            #0x6fdd68
    // 0x6fdd30: mov             x0, x8
    // 0x6fdd34: mov             x1, x6
    // 0x6fdd38: cmp             x1, x0
    // 0x6fdd3c: b.hs            #0x6fde98
    // 0x6fdd40: LoadField: r7 = r4->field_f
    //     0x6fdd40: ldur            w7, [x4, #0xf]
    // 0x6fdd44: DecompressPointer r7
    //     0x6fdd44: add             x7, x7, HEAP, lsl #32
    // 0x6fdd48: ArrayLoad: r9 = r7[r6]  ; Unknown_4
    //     0x6fdd48: add             x16, x7, x6, lsl #2
    //     0x6fdd4c: ldur            w9, [x16, #0xf]
    // 0x6fdd50: DecompressPointer r9
    //     0x6fdd50: add             x9, x9, HEAP, lsl #32
    // 0x6fdd54: r7 = LoadInt32Instr(r9)
    //     0x6fdd54: sbfx            x7, x9, #1, #0x1f
    //     0x6fdd58: tbz             w9, #0, #0x6fdd60
    //     0x6fdd5c: ldur            x7, [x9, #7]
    // 0x6fdd60: cmp             x2, x7
    // 0x6fdd64: b.ge            #0x6fde2c
    // 0x6fdd68: ldur            x2, [fp, #-0x18]
    // 0x6fdd6c: r7 = 8640000000000000
    //     0x6fdd6c: add             x7, PP, #8, lsl #12  ; [pp+0x87d0] IMM: 0x1eb208c2dc0000
    //     0x6fdd70: ldr             x7, [x7, #0x7d0]
    // 0x6fdd74: ArrayStore: r2[0] = r3  ; List_8
    //     0x6fdd74: stur            x3, [x2, #0x17]
    // 0x6fdd78: StoreField: r2->field_1f = r7
    //     0x6fdd78: stur            x7, [x2, #0x1f]
    // 0x6fdd7c: cmp             x6, x8
    // 0x6fdd80: b.ge            #0x6fdda8
    // 0x6fdd84: LoadField: r3 = r4->field_f
    //     0x6fdd84: ldur            w3, [x4, #0xf]
    // 0x6fdd88: DecompressPointer r3
    //     0x6fdd88: add             x3, x3, HEAP, lsl #32
    // 0x6fdd8c: ArrayLoad: r8 = r3[r6]  ; Unknown_4
    //     0x6fdd8c: add             x16, x3, x6, lsl #2
    //     0x6fdd90: ldur            w8, [x16, #0xf]
    // 0x6fdd94: DecompressPointer r8
    //     0x6fdd94: add             x8, x8, HEAP, lsl #32
    // 0x6fdd98: r3 = LoadInt32Instr(r8)
    //     0x6fdd98: sbfx            x3, x8, #1, #0x1f
    //     0x6fdd9c: tbz             w8, #0, #0x6fdda4
    //     0x6fdda0: ldur            x3, [x8, #7]
    // 0x6fdda4: StoreField: r2->field_1f = r3
    //     0x6fdda4: stur            x3, [x2, #0x1f]
    // 0x6fdda8: ldur            x6, [fp, #-0x28]
    // 0x6fddac: ldur            x3, [fp, #-0x30]
    // 0x6fddb0: LoadField: r8 = r6->field_b
    //     0x6fddb0: ldur            w8, [x6, #0xb]
    // 0x6fddb4: r0 = LoadInt32Instr(r8)
    //     0x6fddb4: sbfx            x0, x8, #1, #0x1f
    // 0x6fddb8: mov             x1, x5
    // 0x6fddbc: cmp             x1, x0
    // 0x6fddc0: b.hs            #0x6fde9c
    // 0x6fddc4: LoadField: r8 = r6->field_f
    //     0x6fddc4: ldur            w8, [x6, #0xf]
    // 0x6fddc8: DecompressPointer r8
    //     0x6fddc8: add             x8, x8, HEAP, lsl #32
    // 0x6fddcc: ArrayLoad: r9 = r8[r5]  ; Unknown_4
    //     0x6fddcc: add             x16, x8, x5, lsl #2
    //     0x6fddd0: ldur            w9, [x16, #0xf]
    // 0x6fddd4: DecompressPointer r9
    //     0x6fddd4: add             x9, x9, HEAP, lsl #32
    // 0x6fddd8: LoadField: r8 = r3->field_b
    //     0x6fddd8: ldur            w8, [x3, #0xb]
    // 0x6fdddc: r10 = LoadInt32Instr(r9)
    //     0x6fdddc: sbfx            x10, x9, #1, #0x1f
    //     0x6fdde0: tbz             w9, #0, #0x6fdde8
    //     0x6fdde4: ldur            x10, [x9, #7]
    // 0x6fdde8: r0 = LoadInt32Instr(r8)
    //     0x6fdde8: sbfx            x0, x8, #1, #0x1f
    // 0x6fddec: mov             x1, x10
    // 0x6fddf0: cmp             x1, x0
    // 0x6fddf4: b.hs            #0x6fdea0
    // 0x6fddf8: LoadField: r1 = r3->field_f
    //     0x6fddf8: ldur            w1, [x3, #0xf]
    // 0x6fddfc: DecompressPointer r1
    //     0x6fddfc: add             x1, x1, HEAP, lsl #32
    // 0x6fde00: ArrayLoad: r0 = r1[r10]  ; Unknown_4
    //     0x6fde00: add             x16, x1, x10, lsl #2
    //     0x6fde04: ldur            w0, [x16, #0xf]
    // 0x6fde08: DecompressPointer r0
    //     0x6fde08: add             x0, x0, HEAP, lsl #32
    // 0x6fde0c: StoreField: r2->field_27 = r0
    //     0x6fde0c: stur            w0, [x2, #0x27]
    //     0x6fde10: ldurb           w16, [x2, #-1]
    //     0x6fde14: ldurb           w17, [x0, #-1]
    //     0x6fde18: and             x16, x17, x16, lsr #2
    //     0x6fde1c: tst             x16, HEAP, lsr #32
    //     0x6fde20: b.eq            #0x6fde28
    //     0x6fde24: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x6fde28: b               #0x6fde60
    // 0x6fde2c: ldur            x2, [fp, #-0x18]
    // 0x6fde30: ldur            x6, [fp, #-0x28]
    // 0x6fde34: ldur            x3, [fp, #-0x30]
    // 0x6fde38: r7 = 8640000000000000
    //     0x6fde38: add             x7, PP, #8, lsl #12  ; [pp+0x87d0] IMM: 0x1eb208c2dc0000
    //     0x6fde3c: ldr             x7, [x7, #0x7d0]
    // 0x6fde40: b               #0x6fde60
    // 0x6fde44: ldur            x2, [fp, #-0x18]
    // 0x6fde48: ldur            x4, [fp, #-0x20]
    // 0x6fde4c: ldur            x6, [fp, #-0x28]
    // 0x6fde50: ldur            x3, [fp, #-0x30]
    // 0x6fde54: ldur            x5, [fp, #-0x10]
    // 0x6fde58: r7 = 8640000000000000
    //     0x6fde58: add             x7, PP, #8, lsl #12  ; [pp+0x87d0] IMM: 0x1eb208c2dc0000
    //     0x6fde5c: ldr             x7, [x7, #0x7d0]
    // 0x6fde60: add             x0, x5, #1
    // 0x6fde64: mov             x5, x2
    // 0x6fde68: mov             x1, x3
    // 0x6fde6c: mov             x3, x4
    // 0x6fde70: mov             x2, x6
    // 0x6fde74: b               #0x6fdc98
    // 0x6fde78: r0 = Null
    //     0x6fde78: mov             x0, NULL
    // 0x6fde7c: LeaveFrame
    //     0x6fde7c: mov             SP, fp
    //     0x6fde80: ldp             fp, lr, [SP], #0x10
    // 0x6fde84: ret
    //     0x6fde84: ret             
    // 0x6fde88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fde88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fde8c: b               #0x6fdc08
    // 0x6fde90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fde90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fde94: b               #0x6fdca8
    // 0x6fde98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fde98: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fde9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fde9c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6fdea0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6fdea0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3be4, size: 0x54
    // 0xbf3be4: EnterFrame
    //     0xbf3be4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3be8: mov             fp, SP
    // 0xbf3bec: AllocStack(0x8)
    //     0xbf3bec: sub             SP, SP, #8
    // 0xbf3bf0: CheckStackOverflow
    //     0xbf3bf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3bf4: cmp             SP, x16
    //     0xbf3bf8: b.ls            #0xbf3c30
    // 0xbf3bfc: ldr             x0, [fp, #0x10]
    // 0xbf3c00: LoadField: r1 = r0->field_7
    //     0xbf3c00: ldur            w1, [x0, #7]
    // 0xbf3c04: DecompressPointer r1
    //     0xbf3c04: add             x1, x1, HEAP, lsl #32
    // 0xbf3c08: r0 = LoadClassIdInstr(r1)
    //     0xbf3c08: ldur            x0, [x1, #-1]
    //     0xbf3c0c: ubfx            x0, x0, #0xc, #0x14
    // 0xbf3c10: str             x1, [SP]
    // 0xbf3c14: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3c14: movz            x17, #0x64af
    //     0xbf3c18: add             lr, x0, x17
    //     0xbf3c1c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3c20: blr             lr
    // 0xbf3c24: LeaveFrame
    //     0xbf3c24: mov             SP, fp
    //     0xbf3c28: ldp             fp, lr, [SP], #0x10
    // 0xbf3c2c: ret
    //     0xbf3c2c: ret             
    // 0xbf3c30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3c30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3c34: b               #0xbf3bfc
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7e220, size: 0xc8
    // 0xd7e220: EnterFrame
    //     0xd7e220: stp             fp, lr, [SP, #-0x10]!
    //     0xd7e224: mov             fp, SP
    // 0xd7e228: AllocStack(0x10)
    //     0xd7e228: sub             SP, SP, #0x10
    // 0xd7e22c: CheckStackOverflow
    //     0xd7e22c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7e230: cmp             SP, x16
    //     0xd7e234: b.ls            #0xd7e2e0
    // 0xd7e238: ldr             x0, [fp, #0x10]
    // 0xd7e23c: cmp             w0, NULL
    // 0xd7e240: b.ne            #0xd7e254
    // 0xd7e244: r0 = false
    //     0xd7e244: add             x0, NULL, #0x30  ; false
    // 0xd7e248: LeaveFrame
    //     0xd7e248: mov             SP, fp
    //     0xd7e24c: ldp             fp, lr, [SP], #0x10
    // 0xd7e250: ret
    //     0xd7e250: ret             
    // 0xd7e254: ldr             x1, [fp, #0x18]
    // 0xd7e258: cmp             w1, w0
    // 0xd7e25c: b.ne            #0xd7e268
    // 0xd7e260: r0 = true
    //     0xd7e260: add             x0, NULL, #0x20  ; true
    // 0xd7e264: b               #0xd7e2d4
    // 0xd7e268: r2 = 60
    //     0xd7e268: movz            x2, #0x3c
    // 0xd7e26c: branchIfSmi(r0, 0xd7e278)
    //     0xd7e26c: tbz             w0, #0, #0xd7e278
    // 0xd7e270: r2 = LoadClassIdInstr(r0)
    //     0xd7e270: ldur            x2, [x0, #-1]
    //     0xd7e274: ubfx            x2, x2, #0xc, #0x14
    // 0xd7e278: cmp             x2, #0x1ab
    // 0xd7e27c: b.ne            #0xd7e2d0
    // 0xd7e280: r16 = Location
    //     0xd7e280: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bab0] Type: Location
    //     0xd7e284: ldr             x16, [x16, #0xab0]
    // 0xd7e288: r30 = Location
    //     0xd7e288: add             lr, PP, #0x1b, lsl #12  ; [pp+0x1bab0] Type: Location
    //     0xd7e28c: ldr             lr, [lr, #0xab0]
    // 0xd7e290: stp             lr, x16, [SP]
    // 0xd7e294: r0 = ==()
    //     0xd7e294: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd7e298: tbnz            w0, #4, #0xd7e2d0
    // 0xd7e29c: ldr             x1, [fp, #0x18]
    // 0xd7e2a0: ldr             x0, [fp, #0x10]
    // 0xd7e2a4: LoadField: r2 = r1->field_7
    //     0xd7e2a4: ldur            w2, [x1, #7]
    // 0xd7e2a8: DecompressPointer r2
    //     0xd7e2a8: add             x2, x2, HEAP, lsl #32
    // 0xd7e2ac: LoadField: r1 = r0->field_7
    //     0xd7e2ac: ldur            w1, [x0, #7]
    // 0xd7e2b0: DecompressPointer r1
    //     0xd7e2b0: add             x1, x1, HEAP, lsl #32
    // 0xd7e2b4: r0 = LoadClassIdInstr(r2)
    //     0xd7e2b4: ldur            x0, [x2, #-1]
    //     0xd7e2b8: ubfx            x0, x0, #0xc, #0x14
    // 0xd7e2bc: stp             x1, x2, [SP]
    // 0xd7e2c0: mov             lr, x0
    // 0xd7e2c4: ldr             lr, [x21, lr, lsl #3]
    // 0xd7e2c8: blr             lr
    // 0xd7e2cc: b               #0xd7e2d4
    // 0xd7e2d0: r0 = false
    //     0xd7e2d0: add             x0, NULL, #0x30  ; false
    // 0xd7e2d4: LeaveFrame
    //     0xd7e2d4: mov             SP, fp
    //     0xd7e2d8: ldp             fp, lr, [SP], #0x10
    // 0xd7e2dc: ret
    //     0xd7e2dc: ret             
    // 0xd7e2e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7e2e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7e2e4: b               #0xd7e238
  }
}
