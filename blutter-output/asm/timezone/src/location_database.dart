// lib: timezone.src.location_database, url: package:timezone/src/location_database.dart

// class id: 1051212, size: 0x8
class :: {
}

// class id: 424, size: 0xc, field offset: 0x8
class LocationDatabase extends Object {

  _ get(/* No info */) {
    // ** addr: 0x6fdf84, size: 0x104
    // 0x6fdf84: EnterFrame
    //     0x6fdf84: stp             fp, lr, [SP, #-0x10]!
    //     0x6fdf88: mov             fp, SP
    // 0x6fdf8c: AllocStack(0x20)
    //     0x6fdf8c: sub             SP, SP, #0x20
    // 0x6fdf90: SetupParameters(LocationDatabase this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6fdf90: mov             x0, x1
    //     0x6fdf94: stur            x1, [fp, #-8]
    //     0x6fdf98: stur            x2, [fp, #-0x10]
    // 0x6fdf9c: CheckStackOverflow
    //     0x6fdf9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6fdfa0: cmp             SP, x16
    //     0x6fdfa4: b.ls            #0x6fe080
    // 0x6fdfa8: mov             x1, x0
    // 0x6fdfac: r0 = isInitialized()
    //     0x6fdfac: bl              #0x6fe094  ; [package:timezone/src/location_database.dart] LocationDatabase::isInitialized
    // 0x6fdfb0: tbnz            w0, #4, #0x6fe008
    // 0x6fdfb4: ldur            x0, [fp, #-8]
    // 0x6fdfb8: LoadField: r3 = r0->field_7
    //     0x6fdfb8: ldur            w3, [x0, #7]
    // 0x6fdfbc: DecompressPointer r3
    //     0x6fdfbc: add             x3, x3, HEAP, lsl #32
    // 0x6fdfc0: mov             x1, x3
    // 0x6fdfc4: ldur            x2, [fp, #-0x10]
    // 0x6fdfc8: stur            x3, [fp, #-0x18]
    // 0x6fdfcc: r0 = _getValueOrData()
    //     0x6fdfcc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6fdfd0: mov             x1, x0
    // 0x6fdfd4: ldur            x0, [fp, #-0x18]
    // 0x6fdfd8: LoadField: r2 = r0->field_f
    //     0x6fdfd8: ldur            w2, [x0, #0xf]
    // 0x6fdfdc: DecompressPointer r2
    //     0x6fdfdc: add             x2, x2, HEAP, lsl #32
    // 0x6fdfe0: cmp             w2, w1
    // 0x6fdfe4: b.ne            #0x6fdff0
    // 0x6fdfe8: r0 = Null
    //     0x6fdfe8: mov             x0, NULL
    // 0x6fdfec: b               #0x6fdff4
    // 0x6fdff0: mov             x0, x1
    // 0x6fdff4: cmp             w0, NULL
    // 0x6fdff8: b.eq            #0x6fe028
    // 0x6fdffc: LeaveFrame
    //     0x6fdffc: mov             SP, fp
    //     0x6fe000: ldp             fp, lr, [SP], #0x10
    // 0x6fe004: ret
    //     0x6fe004: ret             
    // 0x6fe008: r0 = LocationNotFoundException()
    //     0x6fe008: bl              #0x6fe088  ; AllocateLocationNotFoundExceptionStub -> LocationNotFoundException (size=0xc)
    // 0x6fe00c: mov             x1, x0
    // 0x6fe010: r0 = "Tried to get location before initializing timezone database"
    //     0x6fe010: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ad48] "Tried to get location before initializing timezone database"
    //     0x6fe014: ldr             x0, [x0, #0xd48]
    // 0x6fe018: StoreField: r1->field_7 = r0
    //     0x6fe018: stur            w0, [x1, #7]
    // 0x6fe01c: mov             x0, x1
    // 0x6fe020: r0 = Throw()
    //     0x6fe020: bl              #0xec04b8  ; ThrowStub
    // 0x6fe024: brk             #0
    // 0x6fe028: ldur            x0, [fp, #-0x10]
    // 0x6fe02c: r1 = Null
    //     0x6fe02c: mov             x1, NULL
    // 0x6fe030: r2 = 6
    //     0x6fe030: movz            x2, #0x6
    // 0x6fe034: r0 = AllocateArray()
    //     0x6fe034: bl              #0xec22fc  ; AllocateArrayStub
    // 0x6fe038: r16 = "Location with the name \""
    //     0x6fe038: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ad50] "Location with the name \""
    //     0x6fe03c: ldr             x16, [x16, #0xd50]
    // 0x6fe040: StoreField: r0->field_f = r16
    //     0x6fe040: stur            w16, [x0, #0xf]
    // 0x6fe044: ldur            x1, [fp, #-0x10]
    // 0x6fe048: StoreField: r0->field_13 = r1
    //     0x6fe048: stur            w1, [x0, #0x13]
    // 0x6fe04c: r16 = "\" doesn\'t exist"
    //     0x6fe04c: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ad58] "\" doesn\'t exist"
    //     0x6fe050: ldr             x16, [x16, #0xd58]
    // 0x6fe054: ArrayStore: r0[0] = r16  ; List_4
    //     0x6fe054: stur            w16, [x0, #0x17]
    // 0x6fe058: str             x0, [SP]
    // 0x6fe05c: r0 = _interpolate()
    //     0x6fe05c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x6fe060: stur            x0, [fp, #-8]
    // 0x6fe064: r0 = LocationNotFoundException()
    //     0x6fe064: bl              #0x6fe088  ; AllocateLocationNotFoundExceptionStub -> LocationNotFoundException (size=0xc)
    // 0x6fe068: mov             x1, x0
    // 0x6fe06c: ldur            x0, [fp, #-8]
    // 0x6fe070: StoreField: r1->field_7 = r0
    //     0x6fe070: stur            w0, [x1, #7]
    // 0x6fe074: mov             x0, x1
    // 0x6fe078: r0 = Throw()
    //     0x6fe078: bl              #0xec04b8  ; ThrowStub
    // 0x6fe07c: brk             #0
    // 0x6fe080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6fe080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6fe084: b               #0x6fdfa8
  }
  get _ isInitialized(/* No info */) {
    // ** addr: 0x6fe094, size: 0x44
    // 0x6fe094: EnterFrame
    //     0x6fe094: stp             fp, lr, [SP, #-0x10]!
    //     0x6fe098: mov             fp, SP
    // 0x6fe09c: LoadField: r2 = r1->field_7
    //     0x6fe09c: ldur            w2, [x1, #7]
    // 0x6fe0a0: DecompressPointer r2
    //     0x6fe0a0: add             x2, x2, HEAP, lsl #32
    // 0x6fe0a4: LoadField: r1 = r2->field_13
    //     0x6fe0a4: ldur            w1, [x2, #0x13]
    // 0x6fe0a8: r3 = LoadInt32Instr(r1)
    //     0x6fe0a8: sbfx            x3, x1, #1, #0x1f
    // 0x6fe0ac: asr             x1, x3, #1
    // 0x6fe0b0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x6fe0b0: ldur            w3, [x2, #0x17]
    // 0x6fe0b4: r2 = LoadInt32Instr(r3)
    //     0x6fe0b4: sbfx            x2, x3, #1, #0x1f
    // 0x6fe0b8: sub             x3, x1, x2
    // 0x6fe0bc: cbnz            x3, #0x6fe0c8
    // 0x6fe0c0: r0 = false
    //     0x6fe0c0: add             x0, NULL, #0x30  ; false
    // 0x6fe0c4: b               #0x6fe0cc
    // 0x6fe0c8: r0 = true
    //     0x6fe0c8: add             x0, NULL, #0x20  ; true
    // 0x6fe0cc: LeaveFrame
    //     0x6fe0cc: mov             SP, fp
    //     0x6fe0d0: ldp             fp, lr, [SP], #0x10
    // 0x6fe0d4: ret
    //     0x6fe0d4: ret             
  }
  _ clear(/* No info */) {
    // ** addr: 0x91262c, size: 0x3c
    // 0x91262c: EnterFrame
    //     0x91262c: stp             fp, lr, [SP, #-0x10]!
    //     0x912630: mov             fp, SP
    // 0x912634: CheckStackOverflow
    //     0x912634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x912638: cmp             SP, x16
    //     0x91263c: b.ls            #0x912660
    // 0x912640: LoadField: r0 = r1->field_7
    //     0x912640: ldur            w0, [x1, #7]
    // 0x912644: DecompressPointer r0
    //     0x912644: add             x0, x0, HEAP, lsl #32
    // 0x912648: mov             x1, x0
    // 0x91264c: r0 = clear()
    //     0x91264c: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x912650: r0 = Null
    //     0x912650: mov             x0, NULL
    // 0x912654: LeaveFrame
    //     0x912654: mov             SP, fp
    //     0x912658: ldp             fp, lr, [SP], #0x10
    // 0x91265c: ret
    //     0x91265c: ret             
    // 0x912660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912660: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912664: b               #0x912640
  }
}
