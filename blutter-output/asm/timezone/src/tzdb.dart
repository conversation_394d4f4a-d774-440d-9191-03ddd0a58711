// lib: timezone.src.tzdb, url: package:timezone/src/tzdb.dart

// class id: 1051213, size: 0x8
class :: {

  static _ tzdbDeserialize(/* No info */) {
    // ** addr: 0x911300, size: 0x2cc
    // 0x911300: EnterFrame
    //     0x911300: stp             fp, lr, [SP, #-0x10]!
    //     0x911304: mov             fp, SP
    // 0x911308: AllocStack(0x88)
    //     0x911308: sub             SP, SP, #0x88
    // 0x91130c: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x91130c: stur            NULL, [fp, #-8]
    //     0x911310: stur            x1, [fp, #-0x10]
    // 0x911314: CheckStackOverflow
    //     0x911314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x911318: cmp             SP, x16
    //     0x91131c: b.ls            #0x9115b8
    // 0x911320: InitAsync() -> Future<Location>
    //     0x911320: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1adf0] TypeArguments: <Location>
    //     0x911324: ldr             x0, [x0, #0xdf0]
    //     0x911328: bl              #0x7348c0  ; InitAsyncStub
    // 0x91132c: r0 = Null
    //     0x91132c: mov             x0, NULL
    // 0x911330: r0 = SuspendSyncStarAtStart()
    //     0x911330: bl              #0x734738  ; SuspendSyncStarAtStartStub
    // 0x911334: ldur            x1, [fp, #-0x10]
    // 0x911338: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x911338: ldur            w0, [x1, #0x17]
    // 0x91133c: DecompressPointer r0
    //     0x91133c: add             x0, x0, HEAP, lsl #32
    // 0x911340: stur            x0, [fp, #-0x18]
    // 0x911344: r0 = _ByteBuffer()
    //     0x911344: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x911348: mov             x2, x0
    // 0x91134c: ldur            x0, [fp, #-0x18]
    // 0x911350: stur            x2, [fp, #-0x28]
    // 0x911354: StoreField: r2->field_7 = r0
    //     0x911354: stur            w0, [x2, #7]
    // 0x911358: ldur            x3, [fp, #-0x10]
    // 0x91135c: LoadField: r4 = r3->field_1b
    //     0x91135c: ldur            w4, [x3, #0x1b]
    // 0x911360: mov             x1, x3
    // 0x911364: stur            x4, [fp, #-0x20]
    // 0x911368: r0 = lengthInBytes()
    //     0x911368: bl              #0xa8a410  ; [dart:typed_data] _TypedListView::lengthInBytes
    // 0x91136c: mov             x2, x0
    // 0x911370: r0 = BoxInt64Instr(r2)
    //     0x911370: sbfiz           x0, x2, #1, #0x1f
    //     0x911374: cmp             x2, x0, asr #1
    //     0x911378: b.eq            #0x911384
    //     0x91137c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x911380: stur            x2, [x0, #7]
    // 0x911384: ldur            x16, [fp, #-0x20]
    // 0x911388: stp             x0, x16, [SP]
    // 0x91138c: ldur            x1, [fp, #-0x28]
    // 0x911390: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x911390: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x911394: r0 = asByteData()
    //     0x911394: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x911398: mov             x3, x0
    // 0x91139c: ldur            x2, [fp, #-0x10]
    // 0x9113a0: stur            x3, [fp, #-0x78]
    // 0x9113a4: LoadField: r0 = r2->field_13
    //     0x9113a4: ldur            w0, [x2, #0x13]
    // 0x9113a8: r4 = LoadInt32Instr(r0)
    //     0x9113a8: sbfx            x4, x0, #1, #0x1f
    // 0x9113ac: stur            x4, [fp, #-0x70]
    // 0x9113b0: LoadField: r0 = r3->field_13
    //     0x9113b0: ldur            w0, [x3, #0x13]
    // 0x9113b4: r1 = LoadInt32Instr(r0)
    //     0x9113b4: sbfx            x1, x0, #1, #0x1f
    // 0x9113b8: sub             x5, x1, #3
    // 0x9113bc: stur            x5, [fp, #-0x68]
    // 0x9113c0: ArrayLoad: r6 = r3[0]  ; List_4
    //     0x9113c0: ldur            w6, [x3, #0x17]
    // 0x9113c4: DecompressPointer r6
    //     0x9113c4: add             x6, x6, HEAP, lsl #32
    // 0x9113c8: stur            x6, [fp, #-0x60]
    // 0x9113cc: LoadField: r0 = r3->field_1b
    //     0x9113cc: ldur            w0, [x3, #0x1b]
    // 0x9113d0: r7 = LoadInt32Instr(r0)
    //     0x9113d0: sbfx            x7, x0, #1, #0x1f
    // 0x9113d4: ldur            x0, [fp, #-0x20]
    // 0x9113d8: stur            x7, [fp, #-0x58]
    // 0x9113dc: r8 = LoadInt32Instr(r0)
    //     0x9113dc: sbfx            x8, x0, #1, #0x1f
    // 0x9113e0: ldur            x9, [fp, #-0x18]
    // 0x9113e4: stur            x8, [fp, #-0x50]
    // 0x9113e8: LoadField: r0 = r9->field_13
    //     0x9113e8: ldur            w0, [x9, #0x13]
    // 0x9113ec: r10 = LoadInt32Instr(r0)
    //     0x9113ec: sbfx            x10, x0, #1, #0x1f
    // 0x9113f0: stur            x10, [fp, #-0x48]
    // 0x9113f4: r20 = 0
    //     0x9113f4: movz            x20, #0
    // 0x9113f8: r19 = 0
    //     0x9113f8: movz            x19, #0
    // 0x9113fc: r14 = 4278255360
    //     0x9113fc: movz            x14, #0xff00
    //     0x911400: movk            x14, #0xff00, lsl #16
    // 0x911404: r13 = 16711935
    //     0x911404: movz            x13, #0xff
    //     0x911408: movk            x13, #0xff, lsl #16
    // 0x91140c: r12 = 4294901760
    //     0x91140c: orr             x12, xzr, #0xffff0000
    // 0x911410: r11 = 65535
    //     0x911410: orr             x11, xzr, #0xffff
    // 0x911414: CheckStackOverflow
    //     0x911414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x911418: cmp             SP, x16
    //     0x91141c: b.ls            #0x9115c0
    // 0x911420: cmp             x20, x4
    // 0x911424: b.ge            #0x9115a8
    // 0x911428: mov             x0, x5
    // 0x91142c: mov             x1, x20
    // 0x911430: cmp             x1, x0
    // 0x911434: b.hs            #0x9115c8
    // 0x911438: add             x0, x7, x20
    // 0x91143c: LoadField: r1 = r6->field_7
    //     0x91143c: ldur            x1, [x6, #7]
    // 0x911440: ldr             w23, [x1, x0]
    // 0x911444: and             x0, x23, x14
    // 0x911448: ubfx            x0, x0, #0, #0x20
    // 0x91144c: asr             x1, x0, #8
    // 0x911450: and             x0, x23, x13
    // 0x911454: ubfx            x0, x0, #0, #0x20
    // 0x911458: lsl             x23, x0, #8
    // 0x91145c: orr             x0, x1, x23
    // 0x911460: mov             x1, x0
    // 0x911464: ubfx            x1, x1, #0, #0x20
    // 0x911468: and             x23, x1, x12
    // 0x91146c: ubfx            x23, x23, #0, #0x20
    // 0x911470: asr             x1, x23, #0x10
    // 0x911474: ubfx            x0, x0, #0, #0x20
    // 0x911478: and             x23, x0, x11
    // 0x91147c: ubfx            x23, x23, #0, #0x20
    // 0x911480: lsl             x0, x23, #0x10
    // 0x911484: orr             x23, x1, x0
    // 0x911488: stur            x23, [fp, #-0x40]
    // 0x91148c: add             x24, x20, #8
    // 0x911490: stur            x24, [fp, #-0x38]
    // 0x911494: add             x0, fp, w19, sxtw #2
    // 0x911498: LoadField: r0 = r0->field_fffffff8
    //     0x911498: ldur            x0, [x0, #-8]
    // 0x91149c: ArrayLoad: r20 = r0[0]  ; List_4
    //     0x91149c: ldur            w20, [x0, #0x17]
    // 0x9114a0: DecompressPointer r20
    //     0x9114a0: add             x20, x20, HEAP, lsl #32
    // 0x9114a4: stur            x20, [fp, #-0x28]
    // 0x9114a8: add             x25, x8, x24
    // 0x9114ac: stur            x25, [fp, #-0x30]
    // 0x9114b0: r0 = BoxInt64Instr(r23)
    //     0x9114b0: sbfiz           x0, x23, #1, #0x1f
    //     0x9114b4: cmp             x23, x0, asr #1
    //     0x9114b8: b.eq            #0x9114c4
    //     0x9114bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9114c0: stur            x23, [x0, #7]
    // 0x9114c4: stur            x0, [fp, #-0x20]
    // 0x9114c8: r1 = LoadClassIdInstr(r9)
    //     0x9114c8: ldur            x1, [x9, #-1]
    //     0x9114cc: ubfx            x1, x1, #0xc, #0x14
    // 0x9114d0: mov             x0, x1
    // 0x9114d4: mov             x1, x9
    // 0x9114d8: r0 = GDT[cid_x0 + 0xd16b]()
    //     0x9114d8: movz            x17, #0xd16b
    //     0x9114dc: add             lr, x0, x17
    //     0x9114e0: ldr             lr, [x21, lr, lsl #3]
    //     0x9114e4: blr             lr
    // 0x9114e8: mov             x1, x0
    // 0x9114ec: ldur            x0, [fp, #-0x48]
    // 0x9114f0: mul             x2, x0, x1
    // 0x9114f4: mov             x1, x2
    // 0x9114f8: ldur            x2, [fp, #-0x30]
    // 0x9114fc: ldur            x3, [fp, #-0x40]
    // 0x911500: r0 = _rangeCheck()
    //     0x911500: bl              #0x6177e8  ; [dart:typed_data] ::_rangeCheck
    // 0x911504: r0 = _Uint8ArrayView()
    //     0x911504: bl              #0x912620  ; Allocate_Uint8ArrayViewStub -> _Uint8ArrayView (size=-0x8)
    // 0x911508: mov             x3, x0
    // 0x91150c: ldur            x2, [fp, #-0x18]
    // 0x911510: ArrayStore: r3[0] = r2  ; List_4
    //     0x911510: stur            w2, [x3, #0x17]
    // 0x911514: ldur            x4, [fp, #-0x30]
    // 0x911518: r0 = BoxInt64Instr(r4)
    //     0x911518: sbfiz           x0, x4, #1, #0x1f
    //     0x91151c: cmp             x4, x0, asr #1
    //     0x911520: b.eq            #0x91152c
    //     0x911524: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x911528: stur            x4, [x0, #7]
    // 0x91152c: StoreField: r3->field_1b = r0
    //     0x91152c: stur            w0, [x3, #0x1b]
    // 0x911530: ldur            x0, [fp, #-0x20]
    // 0x911534: StoreField: r3->field_13 = r0
    //     0x911534: stur            w0, [x3, #0x13]
    // 0x911538: LoadField: r0 = r2->field_7
    //     0x911538: ldur            x0, [x2, #7]
    // 0x91153c: add             x1, x0, x4
    // 0x911540: StoreField: r3->field_7 = r1
    //     0x911540: stur            x1, [x3, #7]
    // 0x911544: mov             x1, x3
    // 0x911548: r0 = _deserializeLocation()
    //     0x911548: bl              #0x9115cc  ; [package:timezone/src/tzdb.dart] ::_deserializeLocation
    // 0x91154c: ldur            x1, [fp, #-0x28]
    // 0x911550: ArrayStore: r1[0] = r0  ; List_4
    //     0x911550: stur            w0, [x1, #0x17]
    //     0x911554: ldurb           w16, [x1, #-1]
    //     0x911558: ldurb           w17, [x0, #-1]
    //     0x91155c: and             x16, x17, x16, lsr #2
    //     0x911560: tst             x16, HEAP, lsr #32
    //     0x911564: b.eq            #0x91156c
    //     0x911568: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x91156c: r0 = true
    //     0x91156c: add             x0, NULL, #0x20  ; true
    // 0x911570: r0 = SuspendSyncStarAtYield()
    //     0x911570: bl              #0x7345b4  ; SuspendSyncStarAtYieldStub
    // 0x911574: ldur            x2, [fp, #-0x38]
    // 0x911578: ldur            x1, [fp, #-0x40]
    // 0x91157c: add             x20, x2, x1
    // 0x911580: ldur            x2, [fp, #-0x10]
    // 0x911584: ldur            x3, [fp, #-0x78]
    // 0x911588: ldur            x5, [fp, #-0x68]
    // 0x91158c: ldur            x6, [fp, #-0x60]
    // 0x911590: ldur            x9, [fp, #-0x18]
    // 0x911594: ldur            x4, [fp, #-0x70]
    // 0x911598: ldur            x7, [fp, #-0x58]
    // 0x91159c: ldur            x8, [fp, #-0x50]
    // 0x9115a0: ldur            x10, [fp, #-0x48]
    // 0x9115a4: b               #0x9113f8
    // 0x9115a8: r0 = false
    //     0x9115a8: add             x0, NULL, #0x30  ; false
    // 0x9115ac: LeaveFrame
    //     0x9115ac: mov             SP, fp
    //     0x9115b0: ldp             fp, lr, [SP], #0x10
    // 0x9115b4: ret
    //     0x9115b4: ret             
    // 0x9115b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9115b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9115bc: b               #0x911320
    // 0x9115c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9115c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9115c4: b               #0x911420
    // 0x9115c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9115c8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _deserializeLocation(/* No info */) {
    // ** addr: 0x9115cc, size: 0xdd8
    // 0x9115cc: EnterFrame
    //     0x9115cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9115d0: mov             fp, SP
    // 0x9115d4: AllocStack(0xc8)
    //     0x9115d4: sub             SP, SP, #0xc8
    // 0x9115d8: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x9115d8: stur            x1, [fp, #-0x10]
    // 0x9115dc: CheckStackOverflow
    //     0x9115dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9115e0: cmp             SP, x16
    //     0x9115e4: b.ls            #0x91222c
    // 0x9115e8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9115e8: ldur            w0, [x1, #0x17]
    // 0x9115ec: DecompressPointer r0
    //     0x9115ec: add             x0, x0, HEAP, lsl #32
    // 0x9115f0: stur            x0, [fp, #-8]
    // 0x9115f4: r0 = _ByteBuffer()
    //     0x9115f4: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x9115f8: mov             x1, x0
    // 0x9115fc: ldur            x0, [fp, #-8]
    // 0x911600: StoreField: r1->field_7 = r0
    //     0x911600: stur            w0, [x1, #7]
    // 0x911604: ldur            x2, [fp, #-0x10]
    // 0x911608: LoadField: r3 = r2->field_1b
    //     0x911608: ldur            w3, [x2, #0x1b]
    // 0x91160c: stur            x3, [fp, #-0x20]
    // 0x911610: LoadField: r4 = r2->field_13
    //     0x911610: ldur            w4, [x2, #0x13]
    // 0x911614: stur            x4, [fp, #-0x18]
    // 0x911618: stp             x4, x3, [SP]
    // 0x91161c: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x91161c: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x911620: r0 = asByteData()
    //     0x911620: bl              #0xebb7c0  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x911624: mov             x2, x0
    // 0x911628: LoadField: r0 = r2->field_13
    //     0x911628: ldur            w0, [x2, #0x13]
    // 0x91162c: r3 = LoadInt32Instr(r0)
    //     0x91162c: sbfx            x3, x0, #1, #0x1f
    // 0x911630: stur            x3, [fp, #-0x80]
    // 0x911634: sub             x4, x3, #3
    // 0x911638: mov             x0, x4
    // 0x91163c: stur            x4, [fp, #-0x78]
    // 0x911640: r1 = 0
    //     0x911640: movz            x1, #0
    // 0x911644: cmp             x1, x0
    // 0x911648: b.hs            #0x912234
    // 0x91164c: ArrayLoad: r5 = r2[0]  ; List_4
    //     0x91164c: ldur            w5, [x2, #0x17]
    // 0x911650: DecompressPointer r5
    //     0x911650: add             x5, x5, HEAP, lsl #32
    // 0x911654: stur            x5, [fp, #-0x70]
    // 0x911658: LoadField: r6 = r2->field_1b
    //     0x911658: ldur            w6, [x2, #0x1b]
    // 0x91165c: LoadField: r0 = r5->field_7
    //     0x91165c: ldur            x0, [x5, #7]
    // 0x911660: asr             w16, w6, #1
    // 0x911664: add             x16, x0, w16, sxtw
    // 0x911668: ldr             w1, [x16]
    // 0x91166c: r2 = 4278255360
    //     0x91166c: movz            x2, #0xff00
    //     0x911670: movk            x2, #0xff00, lsl #16
    // 0x911674: and             x0, x1, x2
    // 0x911678: ubfx            x0, x0, #0, #0x20
    // 0x91167c: asr             x7, x0, #8
    // 0x911680: r8 = 16711935
    //     0x911680: movz            x8, #0xff
    //     0x911684: movk            x8, #0xff, lsl #16
    // 0x911688: and             x0, x1, x8
    // 0x91168c: ubfx            x0, x0, #0, #0x20
    // 0x911690: lsl             x1, x0, #8
    // 0x911694: orr             x0, x7, x1
    // 0x911698: mov             x1, x0
    // 0x91169c: ubfx            x1, x1, #0, #0x20
    // 0x9116a0: r7 = 4294901760
    //     0x9116a0: orr             x7, xzr, #0xffff0000
    // 0x9116a4: and             x9, x1, x7
    // 0x9116a8: ubfx            x9, x9, #0, #0x20
    // 0x9116ac: asr             x1, x9, #0x10
    // 0x9116b0: ubfx            x0, x0, #0, #0x20
    // 0x9116b4: r9 = 65535
    //     0x9116b4: orr             x9, xzr, #0xffff
    // 0x9116b8: and             x10, x0, x9
    // 0x9116bc: ubfx            x10, x10, #0, #0x20
    // 0x9116c0: lsl             x0, x10, #0x10
    // 0x9116c4: orr             x10, x1, x0
    // 0x9116c8: mov             x0, x4
    // 0x9116cc: stur            x10, [fp, #-0x68]
    // 0x9116d0: r1 = 4
    //     0x9116d0: movz            x1, #0x4
    // 0x9116d4: cmp             x1, x0
    // 0x9116d8: b.hs            #0x912238
    // 0x9116dc: r11 = LoadInt32Instr(r6)
    //     0x9116dc: sbfx            x11, x6, #1, #0x1f
    // 0x9116e0: stur            x11, [fp, #-0x60]
    // 0x9116e4: add             x0, x11, #4
    // 0x9116e8: LoadField: r1 = r5->field_7
    //     0x9116e8: ldur            x1, [x5, #7]
    // 0x9116ec: ldr             w6, [x1, x0]
    // 0x9116f0: and             x0, x6, x2
    // 0x9116f4: ubfx            x0, x0, #0, #0x20
    // 0x9116f8: asr             x1, x0, #8
    // 0x9116fc: and             x0, x6, x8
    // 0x911700: ubfx            x0, x0, #0, #0x20
    // 0x911704: lsl             x6, x0, #8
    // 0x911708: orr             x0, x1, x6
    // 0x91170c: mov             x1, x0
    // 0x911710: ubfx            x1, x1, #0, #0x20
    // 0x911714: and             x6, x1, x7
    // 0x911718: ubfx            x6, x6, #0, #0x20
    // 0x91171c: asr             x1, x6, #0x10
    // 0x911720: ubfx            x0, x0, #0, #0x20
    // 0x911724: and             x6, x0, x9
    // 0x911728: ubfx            x6, x6, #0, #0x20
    // 0x91172c: lsl             x0, x6, #0x10
    // 0x911730: orr             x6, x1, x0
    // 0x911734: mov             x0, x4
    // 0x911738: stur            x6, [fp, #-0x58]
    // 0x91173c: r1 = 8
    //     0x91173c: movz            x1, #0x8
    // 0x911740: cmp             x1, x0
    // 0x911744: b.hs            #0x91223c
    // 0x911748: add             x0, x11, #8
    // 0x91174c: LoadField: r1 = r5->field_7
    //     0x91174c: ldur            x1, [x5, #7]
    // 0x911750: ldr             w12, [x1, x0]
    // 0x911754: and             x0, x12, x2
    // 0x911758: ubfx            x0, x0, #0, #0x20
    // 0x91175c: asr             x1, x0, #8
    // 0x911760: and             x0, x12, x8
    // 0x911764: ubfx            x0, x0, #0, #0x20
    // 0x911768: lsl             x12, x0, #8
    // 0x91176c: orr             x0, x1, x12
    // 0x911770: mov             x1, x0
    // 0x911774: ubfx            x1, x1, #0, #0x20
    // 0x911778: and             x12, x1, x7
    // 0x91177c: ubfx            x12, x12, #0, #0x20
    // 0x911780: asr             x1, x12, #0x10
    // 0x911784: ubfx            x0, x0, #0, #0x20
    // 0x911788: and             x12, x0, x9
    // 0x91178c: ubfx            x12, x12, #0, #0x20
    // 0x911790: lsl             x0, x12, #0x10
    // 0x911794: orr             x12, x1, x0
    // 0x911798: mov             x0, x4
    // 0x91179c: stur            x12, [fp, #-0x50]
    // 0x9117a0: r1 = 12
    //     0x9117a0: movz            x1, #0xc
    // 0x9117a4: cmp             x1, x0
    // 0x9117a8: b.hs            #0x912240
    // 0x9117ac: add             x0, x11, #0xc
    // 0x9117b0: LoadField: r1 = r5->field_7
    //     0x9117b0: ldur            x1, [x5, #7]
    // 0x9117b4: ldr             w13, [x1, x0]
    // 0x9117b8: and             x0, x13, x2
    // 0x9117bc: ubfx            x0, x0, #0, #0x20
    // 0x9117c0: asr             x1, x0, #8
    // 0x9117c4: and             x0, x13, x8
    // 0x9117c8: ubfx            x0, x0, #0, #0x20
    // 0x9117cc: lsl             x13, x0, #8
    // 0x9117d0: orr             x0, x1, x13
    // 0x9117d4: mov             x1, x0
    // 0x9117d8: ubfx            x1, x1, #0, #0x20
    // 0x9117dc: and             x13, x1, x7
    // 0x9117e0: ubfx            x13, x13, #0, #0x20
    // 0x9117e4: asr             x1, x13, #0x10
    // 0x9117e8: ubfx            x0, x0, #0, #0x20
    // 0x9117ec: and             x13, x0, x9
    // 0x9117f0: ubfx            x13, x13, #0, #0x20
    // 0x9117f4: lsl             x0, x13, #0x10
    // 0x9117f8: orr             x13, x1, x0
    // 0x9117fc: mov             x0, x4
    // 0x911800: stur            x13, [fp, #-0x48]
    // 0x911804: r1 = 16
    //     0x911804: movz            x1, #0x10
    // 0x911808: cmp             x1, x0
    // 0x91180c: b.hs            #0x912244
    // 0x911810: add             x0, x11, #0x10
    // 0x911814: LoadField: r1 = r5->field_7
    //     0x911814: ldur            x1, [x5, #7]
    // 0x911818: ldr             w14, [x1, x0]
    // 0x91181c: and             x0, x14, x2
    // 0x911820: ubfx            x0, x0, #0, #0x20
    // 0x911824: asr             x1, x0, #8
    // 0x911828: and             x0, x14, x8
    // 0x91182c: ubfx            x0, x0, #0, #0x20
    // 0x911830: lsl             x14, x0, #8
    // 0x911834: orr             x0, x1, x14
    // 0x911838: mov             x1, x0
    // 0x91183c: ubfx            x1, x1, #0, #0x20
    // 0x911840: and             x14, x1, x7
    // 0x911844: ubfx            x14, x14, #0, #0x20
    // 0x911848: asr             x1, x14, #0x10
    // 0x91184c: ubfx            x0, x0, #0, #0x20
    // 0x911850: and             x14, x0, x9
    // 0x911854: ubfx            x14, x14, #0, #0x20
    // 0x911858: lsl             x0, x14, #0x10
    // 0x91185c: orr             x14, x1, x0
    // 0x911860: mov             x0, x4
    // 0x911864: stur            x14, [fp, #-0x40]
    // 0x911868: r1 = 20
    //     0x911868: movz            x1, #0x14
    // 0x91186c: cmp             x1, x0
    // 0x911870: b.hs            #0x912248
    // 0x911874: add             x0, x11, #0x14
    // 0x911878: LoadField: r1 = r5->field_7
    //     0x911878: ldur            x1, [x5, #7]
    // 0x91187c: ldr             w19, [x1, x0]
    // 0x911880: and             x0, x19, x2
    // 0x911884: ubfx            x0, x0, #0, #0x20
    // 0x911888: asr             x1, x0, #8
    // 0x91188c: and             x0, x19, x8
    // 0x911890: ubfx            x0, x0, #0, #0x20
    // 0x911894: lsl             x19, x0, #8
    // 0x911898: orr             x0, x1, x19
    // 0x91189c: mov             x1, x0
    // 0x9118a0: ubfx            x1, x1, #0, #0x20
    // 0x9118a4: and             x19, x1, x7
    // 0x9118a8: ubfx            x19, x19, #0, #0x20
    // 0x9118ac: asr             x1, x19, #0x10
    // 0x9118b0: ubfx            x0, x0, #0, #0x20
    // 0x9118b4: and             x19, x0, x9
    // 0x9118b8: ubfx            x19, x19, #0, #0x20
    // 0x9118bc: lsl             x0, x19, #0x10
    // 0x9118c0: orr             x19, x1, x0
    // 0x9118c4: mov             x0, x4
    // 0x9118c8: stur            x19, [fp, #-0x38]
    // 0x9118cc: r1 = 24
    //     0x9118cc: movz            x1, #0x18
    // 0x9118d0: cmp             x1, x0
    // 0x9118d4: b.hs            #0x91224c
    // 0x9118d8: add             x0, x11, #0x18
    // 0x9118dc: LoadField: r1 = r5->field_7
    //     0x9118dc: ldur            x1, [x5, #7]
    // 0x9118e0: ldr             w20, [x1, x0]
    // 0x9118e4: and             x0, x20, x2
    // 0x9118e8: ubfx            x0, x0, #0, #0x20
    // 0x9118ec: asr             x1, x0, #8
    // 0x9118f0: and             x0, x20, x8
    // 0x9118f4: ubfx            x0, x0, #0, #0x20
    // 0x9118f8: lsl             x20, x0, #8
    // 0x9118fc: orr             x0, x1, x20
    // 0x911900: mov             x1, x0
    // 0x911904: ubfx            x1, x1, #0, #0x20
    // 0x911908: and             x20, x1, x7
    // 0x91190c: ubfx            x20, x20, #0, #0x20
    // 0x911910: asr             x1, x20, #0x10
    // 0x911914: ubfx            x0, x0, #0, #0x20
    // 0x911918: and             x20, x0, x9
    // 0x91191c: ubfx            x20, x20, #0, #0x20
    // 0x911920: lsl             x0, x20, #0x10
    // 0x911924: orr             x20, x1, x0
    // 0x911928: mov             x0, x4
    // 0x91192c: stur            x20, [fp, #-0x30]
    // 0x911930: r1 = 28
    //     0x911930: movz            x1, #0x1c
    // 0x911934: cmp             x1, x0
    // 0x911938: b.hs            #0x912250
    // 0x91193c: add             x0, x11, #0x1c
    // 0x911940: LoadField: r1 = r5->field_7
    //     0x911940: ldur            x1, [x5, #7]
    // 0x911944: ldr             w23, [x1, x0]
    // 0x911948: and             x0, x23, x2
    // 0x91194c: ubfx            x0, x0, #0, #0x20
    // 0x911950: asr             x1, x0, #8
    // 0x911954: and             x0, x23, x8
    // 0x911958: ubfx            x0, x0, #0, #0x20
    // 0x91195c: lsl             x23, x0, #8
    // 0x911960: orr             x0, x1, x23
    // 0x911964: mov             x1, x0
    // 0x911968: ubfx            x1, x1, #0, #0x20
    // 0x91196c: and             x23, x1, x7
    // 0x911970: ubfx            x23, x23, #0, #0x20
    // 0x911974: asr             x1, x23, #0x10
    // 0x911978: ubfx            x0, x0, #0, #0x20
    // 0x91197c: and             x23, x0, x9
    // 0x911980: ubfx            x23, x23, #0, #0x20
    // 0x911984: lsl             x0, x23, #0x10
    // 0x911988: orr             x23, x1, x0
    // 0x91198c: stur            x23, [fp, #-0x28]
    // 0x911990: r0 = _ByteBuffer()
    //     0x911990: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x911994: mov             x3, x0
    // 0x911998: ldur            x2, [fp, #-8]
    // 0x91199c: StoreField: r3->field_7 = r2
    //     0x91199c: stur            w2, [x3, #7]
    // 0x9119a0: ldur            x0, [fp, #-0x20]
    // 0x9119a4: r4 = LoadInt32Instr(r0)
    //     0x9119a4: sbfx            x4, x0, #1, #0x1f
    // 0x9119a8: ldur            x0, [fp, #-0x68]
    // 0x9119ac: stur            x4, [fp, #-0x88]
    // 0x9119b0: add             x5, x4, x0
    // 0x9119b4: ldur            x6, [fp, #-0x58]
    // 0x9119b8: r0 = BoxInt64Instr(r6)
    //     0x9119b8: sbfiz           x0, x6, #1, #0x1f
    //     0x9119bc: cmp             x6, x0, asr #1
    //     0x9119c0: b.eq            #0x9119cc
    //     0x9119c4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9119c8: stur            x6, [x0, #7]
    // 0x9119cc: mov             x6, x0
    // 0x9119d0: r0 = BoxInt64Instr(r5)
    //     0x9119d0: sbfiz           x0, x5, #1, #0x1f
    //     0x9119d4: cmp             x5, x0, asr #1
    //     0x9119d8: b.eq            #0x9119e4
    //     0x9119dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9119e0: stur            x5, [x0, #7]
    // 0x9119e4: stp             x6, x0, [SP]
    // 0x9119e8: mov             x1, x3
    // 0x9119ec: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x9119ec: ldr             x4, [PP, #0x1a20]  ; [pp+0x1a20] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x9119f0: r0 = asUint8List()
    //     0x9119f0: bl              #0xebb96c  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0x9119f4: mov             x2, x0
    // 0x9119f8: r1 = Instance_AsciiDecoder
    //     0x9119f8: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1adf8] Obj!AsciiDecoder@e2ce01
    //     0x9119fc: ldr             x1, [x1, #0xdf8]
    // 0x911a00: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x911a00: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x911a04: r0 = convert()
    //     0x911a04: bl              #0xcf6618  ; [dart:convert] _UnicodeSubsetDecoder::convert
    // 0x911a08: r1 = <String>
    //     0x911a08: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x911a0c: r2 = 0
    //     0x911a0c: movz            x2, #0
    // 0x911a10: stur            x0, [fp, #-0x20]
    // 0x911a14: r0 = _GrowableList()
    //     0x911a14: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x911a18: r1 = <TimeZone>
    //     0x911a18: add             x1, PP, #8, lsl #12  ; [pp+0x87b8] TypeArguments: <TimeZone>
    //     0x911a1c: ldr             x1, [x1, #0x7b8]
    // 0x911a20: r2 = 0
    //     0x911a20: movz            x2, #0
    // 0x911a24: stur            x0, [fp, #-0x90]
    // 0x911a28: r0 = _GrowableList()
    //     0x911a28: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x911a2c: r1 = <int>
    //     0x911a2c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x911a30: r2 = 0
    //     0x911a30: movz            x2, #0
    // 0x911a34: stur            x0, [fp, #-0x98]
    // 0x911a38: r0 = _GrowableList()
    //     0x911a38: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x911a3c: r1 = <int>
    //     0x911a3c: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x911a40: r2 = 0
    //     0x911a40: movz            x2, #0
    // 0x911a44: stur            x0, [fp, #-0xa0]
    // 0x911a48: r0 = _GrowableList()
    //     0x911a48: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x911a4c: mov             x2, x0
    // 0x911a50: ldur            x0, [fp, #-0x50]
    // 0x911a54: ldur            x1, [fp, #-0x48]
    // 0x911a58: stur            x2, [fp, #-0xb8]
    // 0x911a5c: add             x3, x0, x1
    // 0x911a60: ldur            x1, [fp, #-0x18]
    // 0x911a64: stur            x3, [fp, #-0xb0]
    // 0x911a68: r4 = LoadInt32Instr(r1)
    //     0x911a68: sbfx            x4, x1, #1, #0x1f
    // 0x911a6c: ldur            x5, [fp, #-8]
    // 0x911a70: stur            x4, [fp, #-0xa8]
    // 0x911a74: LoadField: r1 = r5->field_13
    //     0x911a74: ldur            w1, [x5, #0x13]
    // 0x911a78: r6 = LoadInt32Instr(r1)
    //     0x911a78: sbfx            x6, x1, #1, #0x1f
    // 0x911a7c: stur            x6, [fp, #-0x68]
    // 0x911a80: mov             x11, x0
    // 0x911a84: mov             x10, x0
    // 0x911a88: ldur            x7, [fp, #-0x90]
    // 0x911a8c: ldur            x9, [fp, #-0x10]
    // 0x911a90: ldur            x8, [fp, #-0x88]
    // 0x911a94: stur            x10, [fp, #-0x58]
    // 0x911a98: CheckStackOverflow
    //     0x911a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x911a9c: cmp             SP, x16
    //     0x911aa0: b.ls            #0x912254
    // 0x911aa4: cmp             x10, x3
    // 0x911aa8: b.ge            #0x911c34
    // 0x911aac: mov             x0, x4
    // 0x911ab0: mov             x1, x10
    // 0x911ab4: cmp             x1, x0
    // 0x911ab8: b.hs            #0x91225c
    // 0x911abc: LoadField: r0 = r9->field_7
    //     0x911abc: ldur            x0, [x9, #7]
    // 0x911ac0: ldrb            w1, [x0, x10]
    // 0x911ac4: cbnz            x1, #0x911c0c
    // 0x911ac8: add             x12, x8, x11
    // 0x911acc: stur            x12, [fp, #-0x50]
    // 0x911ad0: sub             x13, x10, x11
    // 0x911ad4: stur            x13, [fp, #-0x48]
    // 0x911ad8: r0 = BoxInt64Instr(r13)
    //     0x911ad8: sbfiz           x0, x13, #1, #0x1f
    //     0x911adc: cmp             x13, x0, asr #1
    //     0x911ae0: b.eq            #0x911aec
    //     0x911ae4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x911ae8: stur            x13, [x0, #7]
    // 0x911aec: mov             x11, x0
    // 0x911af0: stur            x11, [fp, #-0x18]
    // 0x911af4: r0 = LoadClassIdInstr(r5)
    //     0x911af4: ldur            x0, [x5, #-1]
    //     0x911af8: ubfx            x0, x0, #0xc, #0x14
    // 0x911afc: mov             x1, x5
    // 0x911b00: r0 = GDT[cid_x0 + 0xd16b]()
    //     0x911b00: movz            x17, #0xd16b
    //     0x911b04: add             lr, x0, x17
    //     0x911b08: ldr             lr, [x21, lr, lsl #3]
    //     0x911b0c: blr             lr
    // 0x911b10: mov             x1, x0
    // 0x911b14: ldur            x0, [fp, #-0x68]
    // 0x911b18: mul             x2, x0, x1
    // 0x911b1c: mov             x1, x2
    // 0x911b20: ldur            x2, [fp, #-0x50]
    // 0x911b24: ldur            x3, [fp, #-0x48]
    // 0x911b28: r0 = _rangeCheck()
    //     0x911b28: bl              #0x6177e8  ; [dart:typed_data] ::_rangeCheck
    // 0x911b2c: r0 = _Uint8ArrayView()
    //     0x911b2c: bl              #0x912620  ; Allocate_Uint8ArrayViewStub -> _Uint8ArrayView (size=-0x8)
    // 0x911b30: mov             x2, x0
    // 0x911b34: ldur            x3, [fp, #-8]
    // 0x911b38: ArrayStore: r2[0] = r3  ; List_4
    //     0x911b38: stur            w3, [x2, #0x17]
    // 0x911b3c: ldur            x4, [fp, #-0x50]
    // 0x911b40: r0 = BoxInt64Instr(r4)
    //     0x911b40: sbfiz           x0, x4, #1, #0x1f
    //     0x911b44: cmp             x4, x0, asr #1
    //     0x911b48: b.eq            #0x911b54
    //     0x911b4c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x911b50: stur            x4, [x0, #7]
    // 0x911b54: StoreField: r2->field_1b = r0
    //     0x911b54: stur            w0, [x2, #0x1b]
    // 0x911b58: ldur            x0, [fp, #-0x18]
    // 0x911b5c: StoreField: r2->field_13 = r0
    //     0x911b5c: stur            w0, [x2, #0x13]
    // 0x911b60: LoadField: r0 = r3->field_7
    //     0x911b60: ldur            x0, [x3, #7]
    // 0x911b64: add             x1, x0, x4
    // 0x911b68: StoreField: r2->field_7 = r1
    //     0x911b68: stur            x1, [x2, #7]
    // 0x911b6c: r1 = Instance_AsciiDecoder
    //     0x911b6c: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1adf8] Obj!AsciiDecoder@e2ce01
    //     0x911b70: ldr             x1, [x1, #0xdf8]
    // 0x911b74: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x911b74: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x911b78: r0 = convert()
    //     0x911b78: bl              #0xcf6618  ; [dart:convert] _UnicodeSubsetDecoder::convert
    // 0x911b7c: mov             x2, x0
    // 0x911b80: ldur            x0, [fp, #-0x90]
    // 0x911b84: stur            x2, [fp, #-0x18]
    // 0x911b88: LoadField: r1 = r0->field_b
    //     0x911b88: ldur            w1, [x0, #0xb]
    // 0x911b8c: LoadField: r3 = r0->field_f
    //     0x911b8c: ldur            w3, [x0, #0xf]
    // 0x911b90: DecompressPointer r3
    //     0x911b90: add             x3, x3, HEAP, lsl #32
    // 0x911b94: LoadField: r4 = r3->field_b
    //     0x911b94: ldur            w4, [x3, #0xb]
    // 0x911b98: r3 = LoadInt32Instr(r1)
    //     0x911b98: sbfx            x3, x1, #1, #0x1f
    // 0x911b9c: stur            x3, [fp, #-0x48]
    // 0x911ba0: r1 = LoadInt32Instr(r4)
    //     0x911ba0: sbfx            x1, x4, #1, #0x1f
    // 0x911ba4: cmp             x3, x1
    // 0x911ba8: b.ne            #0x911bb4
    // 0x911bac: mov             x1, x0
    // 0x911bb0: r0 = _growToNextCapacity()
    //     0x911bb0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x911bb4: ldur            x2, [fp, #-0x90]
    // 0x911bb8: ldur            x4, [fp, #-0x58]
    // 0x911bbc: ldur            x3, [fp, #-0x48]
    // 0x911bc0: add             x0, x3, #1
    // 0x911bc4: lsl             x1, x0, #1
    // 0x911bc8: StoreField: r2->field_b = r1
    //     0x911bc8: stur            w1, [x2, #0xb]
    // 0x911bcc: LoadField: r1 = r2->field_f
    //     0x911bcc: ldur            w1, [x2, #0xf]
    // 0x911bd0: DecompressPointer r1
    //     0x911bd0: add             x1, x1, HEAP, lsl #32
    // 0x911bd4: ldur            x0, [fp, #-0x18]
    // 0x911bd8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x911bd8: add             x25, x1, x3, lsl #2
    //     0x911bdc: add             x25, x25, #0xf
    //     0x911be0: str             w0, [x25]
    //     0x911be4: tbz             w0, #0, #0x911c00
    //     0x911be8: ldurb           w16, [x1, #-1]
    //     0x911bec: ldurb           w17, [x0, #-1]
    //     0x911bf0: and             x16, x17, x16, lsr #2
    //     0x911bf4: tst             x16, HEAP, lsr #32
    //     0x911bf8: b.eq            #0x911c00
    //     0x911bfc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x911c00: add             x0, x4, #1
    // 0x911c04: mov             x11, x0
    // 0x911c08: b               #0x911c14
    // 0x911c0c: mov             x2, x7
    // 0x911c10: mov             x4, x10
    // 0x911c14: add             x10, x4, #1
    // 0x911c18: mov             x7, x2
    // 0x911c1c: ldur            x2, [fp, #-0xb8]
    // 0x911c20: ldur            x3, [fp, #-0xb0]
    // 0x911c24: ldur            x5, [fp, #-8]
    // 0x911c28: ldur            x4, [fp, #-0xa8]
    // 0x911c2c: ldur            x6, [fp, #-0x68]
    // 0x911c30: b               #0x911a8c
    // 0x911c34: mov             x2, x7
    // 0x911c38: ldur            x20, [fp, #-0x40]
    // 0x911c3c: ldur            x3, [fp, #-0x98]
    // 0x911c40: r19 = 0
    //     0x911c40: movz            x19, #0
    // 0x911c44: ldur            x4, [fp, #-0x70]
    // 0x911c48: ldur            x10, [fp, #-0x38]
    // 0x911c4c: ldur            x9, [fp, #-0x60]
    // 0x911c50: r14 = 8
    //     0x911c50: movz            x14, #0x8
    // 0x911c54: r13 = 16
    //     0x911c54: movz            x13, #0x10
    // 0x911c58: r5 = 4278255360
    //     0x911c58: movz            x5, #0xff00
    //     0x911c5c: movk            x5, #0xff00, lsl #16
    // 0x911c60: r6 = 16711935
    //     0x911c60: movz            x6, #0xff
    //     0x911c64: movk            x6, #0xff, lsl #16
    // 0x911c68: r7 = 4294901760
    //     0x911c68: orr             x7, xzr, #0xffff0000
    // 0x911c6c: r8 = 65535
    //     0x911c6c: orr             x8, xzr, #0xffff
    // 0x911c70: r12 = 2147483647
    //     0x911c70: orr             x12, xzr, #0x7fffffff
    // 0x911c74: r11 = 2147483648
    //     0x911c74: orr             x11, xzr, #0x80000000
    // 0x911c78: stur            x19, [fp, #-0x50]
    // 0x911c7c: CheckStackOverflow
    //     0x911c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x911c80: cmp             SP, x16
    //     0x911c84: b.ls            #0x912260
    // 0x911c88: cmp             x19, x10
    // 0x911c8c: b.ge            #0x911e70
    // 0x911c90: ldur            x0, [fp, #-0x78]
    // 0x911c94: mov             x1, x20
    // 0x911c98: cmp             x1, x0
    // 0x911c9c: b.hs            #0x912268
    // 0x911ca0: add             x0, x9, x20
    // 0x911ca4: LoadField: r1 = r4->field_7
    //     0x911ca4: ldur            x1, [x4, #7]
    // 0x911ca8: ldrsw           x23, [x1, x0]
    // 0x911cac: mov             x0, x23
    // 0x911cb0: and             x1, x0, x5
    // 0x911cb4: tbnz            x14, #0x3f, #0x91226c
    // 0x911cb8: lsr             w0, w1, w14
    // 0x911cbc: cmp             x14, #0x1f
    // 0x911cc0: csel            x0, x0, xzr, le
    // 0x911cc4: and             x1, x23, x6
    // 0x911cc8: tbnz            x14, #0x3f, #0x9122a8
    // 0x911ccc: lsl             w23, w1, w14
    // 0x911cd0: cmp             x14, #0x1f
    // 0x911cd4: csel            x23, x23, xzr, le
    // 0x911cd8: orr             x1, x0, x23
    // 0x911cdc: and             x0, x1, x7
    // 0x911ce0: tbnz            x13, #0x3f, #0x9122e4
    // 0x911ce4: lsr             w23, w0, w13
    // 0x911ce8: cmp             x13, #0x1f
    // 0x911cec: csel            x23, x23, xzr, le
    // 0x911cf0: and             x0, x1, x8
    // 0x911cf4: tbnz            x13, #0x3f, #0x912320
    // 0x911cf8: lsl             w1, w0, w13
    // 0x911cfc: cmp             x13, #0x1f
    // 0x911d00: csel            x1, x1, xzr, le
    // 0x911d04: orr             x0, x23, x1
    // 0x911d08: and             x1, x0, x12
    // 0x911d0c: and             x23, x0, x11
    // 0x911d10: ubfx            x1, x1, #0, #0x20
    // 0x911d14: ubfx            x23, x23, #0, #0x20
    // 0x911d18: sub             x0, x1, x23
    // 0x911d1c: r16 = 1000
    //     0x911d1c: movz            x16, #0x3e8
    // 0x911d20: mul             x23, x0, x16
    // 0x911d24: stur            x23, [fp, #-0x48]
    // 0x911d28: add             x24, x20, #4
    // 0x911d2c: ldur            x0, [fp, #-0x80]
    // 0x911d30: mov             x1, x24
    // 0x911d34: cmp             x1, x0
    // 0x911d38: b.hs            #0x91235c
    // 0x911d3c: add             x0, x9, x24
    // 0x911d40: LoadField: r1 = r4->field_7
    //     0x911d40: ldur            x1, [x4, #7]
    // 0x911d44: ldrb            w24, [x1, x0]
    // 0x911d48: add             x25, x20, #5
    // 0x911d4c: ldur            x0, [fp, #-0x80]
    // 0x911d50: mov             x1, x25
    // 0x911d54: cmp             x1, x0
    // 0x911d58: b.hs            #0x912360
    // 0x911d5c: add             x0, x9, x25
    // 0x911d60: LoadField: r1 = r4->field_7
    //     0x911d60: ldur            x1, [x4, #7]
    // 0x911d64: ldrb            w25, [x1, x0]
    // 0x911d68: add             x0, x20, #8
    // 0x911d6c: stur            x0, [fp, #-0x40]
    // 0x911d70: cmp             x24, #1
    // 0x911d74: r16 = true
    //     0x911d74: add             x16, NULL, #0x20  ; true
    // 0x911d78: r17 = false
    //     0x911d78: add             x17, NULL, #0x30  ; false
    // 0x911d7c: csel            x20, x16, x17, eq
    // 0x911d80: stur            x20, [fp, #-0x10]
    // 0x911d84: LoadField: r1 = r2->field_b
    //     0x911d84: ldur            w1, [x2, #0xb]
    // 0x911d88: r24 = LoadInt32Instr(r1)
    //     0x911d88: sbfx            x24, x1, #1, #0x1f
    // 0x911d8c: mov             x16, x0
    // 0x911d90: mov             x0, x24
    // 0x911d94: mov             x24, x16
    // 0x911d98: mov             x1, x25
    // 0x911d9c: cmp             x1, x0
    // 0x911da0: b.hs            #0x912364
    // 0x911da4: LoadField: r0 = r2->field_f
    //     0x911da4: ldur            w0, [x2, #0xf]
    // 0x911da8: DecompressPointer r0
    //     0x911da8: add             x0, x0, HEAP, lsl #32
    // 0x911dac: ArrayLoad: r1 = r0[r25]  ; Unknown_4
    //     0x911dac: add             x16, x0, x25, lsl #2
    //     0x911db0: ldur            w1, [x16, #0xf]
    // 0x911db4: DecompressPointer r1
    //     0x911db4: add             x1, x1, HEAP, lsl #32
    // 0x911db8: stur            x1, [fp, #-8]
    // 0x911dbc: r0 = TimeZone()
    //     0x911dbc: bl              #0x9123a4  ; AllocateTimeZoneStub -> TimeZone (size=0x18)
    // 0x911dc0: mov             x2, x0
    // 0x911dc4: ldur            x0, [fp, #-0x48]
    // 0x911dc8: stur            x2, [fp, #-0x18]
    // 0x911dcc: StoreField: r2->field_7 = r0
    //     0x911dcc: stur            x0, [x2, #7]
    // 0x911dd0: ldur            x0, [fp, #-0x10]
    // 0x911dd4: StoreField: r2->field_f = r0
    //     0x911dd4: stur            w0, [x2, #0xf]
    // 0x911dd8: ldur            x0, [fp, #-8]
    // 0x911ddc: StoreField: r2->field_13 = r0
    //     0x911ddc: stur            w0, [x2, #0x13]
    // 0x911de0: ldur            x0, [fp, #-0x98]
    // 0x911de4: LoadField: r1 = r0->field_b
    //     0x911de4: ldur            w1, [x0, #0xb]
    // 0x911de8: LoadField: r3 = r0->field_f
    //     0x911de8: ldur            w3, [x0, #0xf]
    // 0x911dec: DecompressPointer r3
    //     0x911dec: add             x3, x3, HEAP, lsl #32
    // 0x911df0: LoadField: r4 = r3->field_b
    //     0x911df0: ldur            w4, [x3, #0xb]
    // 0x911df4: r3 = LoadInt32Instr(r1)
    //     0x911df4: sbfx            x3, x1, #1, #0x1f
    // 0x911df8: stur            x3, [fp, #-0x48]
    // 0x911dfc: r1 = LoadInt32Instr(r4)
    //     0x911dfc: sbfx            x1, x4, #1, #0x1f
    // 0x911e00: cmp             x3, x1
    // 0x911e04: b.ne            #0x911e10
    // 0x911e08: mov             x1, x0
    // 0x911e0c: r0 = _growToNextCapacity()
    //     0x911e0c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x911e10: ldur            x6, [fp, #-0x98]
    // 0x911e14: ldur            x3, [fp, #-0x50]
    // 0x911e18: ldur            x2, [fp, #-0x48]
    // 0x911e1c: add             x0, x2, #1
    // 0x911e20: lsl             x1, x0, #1
    // 0x911e24: StoreField: r6->field_b = r1
    //     0x911e24: stur            w1, [x6, #0xb]
    // 0x911e28: LoadField: r1 = r6->field_f
    //     0x911e28: ldur            w1, [x6, #0xf]
    // 0x911e2c: DecompressPointer r1
    //     0x911e2c: add             x1, x1, HEAP, lsl #32
    // 0x911e30: ldur            x0, [fp, #-0x18]
    // 0x911e34: ArrayStore: r1[r2] = r0  ; List_4
    //     0x911e34: add             x25, x1, x2, lsl #2
    //     0x911e38: add             x25, x25, #0xf
    //     0x911e3c: str             w0, [x25]
    //     0x911e40: tbz             w0, #0, #0x911e5c
    //     0x911e44: ldurb           w16, [x1, #-1]
    //     0x911e48: ldurb           w17, [x0, #-1]
    //     0x911e4c: and             x16, x17, x16, lsr #2
    //     0x911e50: tst             x16, HEAP, lsr #32
    //     0x911e54: b.eq            #0x911e5c
    //     0x911e58: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x911e5c: add             x19, x3, #1
    // 0x911e60: ldur            x20, [fp, #-0x40]
    // 0x911e64: ldur            x2, [fp, #-0x90]
    // 0x911e68: mov             x3, x6
    // 0x911e6c: b               #0x911c44
    // 0x911e70: mov             x6, x3
    // 0x911e74: ldur            x2, [fp, #-0x80]
    // 0x911e78: sub             x3, x2, #7
    // 0x911e7c: stur            x3, [fp, #-0x40]
    // 0x911e80: ldur            x10, [fp, #-0x30]
    // 0x911e84: ldur            x7, [fp, #-0xa0]
    // 0x911e88: r9 = 0
    //     0x911e88: movz            x9, #0
    // 0x911e8c: ldur            x4, [fp, #-0x70]
    // 0x911e90: ldur            x8, [fp, #-0x28]
    // 0x911e94: ldur            x5, [fp, #-0x60]
    // 0x911e98: stur            x10, [fp, #-0x30]
    // 0x911e9c: stur            x9, [fp, #-0x38]
    // 0x911ea0: CheckStackOverflow
    //     0x911ea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x911ea4: cmp             SP, x16
    //     0x911ea8: b.ls            #0x912368
    // 0x911eac: cmp             x9, x8
    // 0x911eb0: b.ge            #0x912128
    // 0x911eb4: mov             x0, x3
    // 0x911eb8: mov             x1, x10
    // 0x911ebc: cmp             x1, x0
    // 0x911ec0: b.hs            #0x912370
    // 0x911ec4: r0 = InitLateStaticField(0x324) // [dart:typed_data] ::_convU64
    //     0x911ec4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x911ec8: ldr             x0, [x0, #0x648]
    //     0x911ecc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x911ed0: cmp             w0, w16
    //     0x911ed4: b.ne            #0x911ee4
    //     0x911ed8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae00] Field <::._convU64@8027147>: static late final (offset: 0x324)
    //     0x911edc: ldr             x2, [x2, #0xe00]
    //     0x911ee0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x911ee4: mov             x4, x0
    // 0x911ee8: ldur            x3, [fp, #-0x30]
    // 0x911eec: ldur            x2, [fp, #-0x60]
    // 0x911ef0: add             x0, x2, x3
    // 0x911ef4: ldur            x5, [fp, #-0x70]
    // 0x911ef8: LoadField: r1 = r5->field_7
    //     0x911ef8: ldur            x1, [x5, #7]
    // 0x911efc: ldr             x6, [x1, x0]
    // 0x911f00: mov             x0, x6
    // 0x911f04: ubfx            x0, x0, #0, #0x20
    // 0x911f08: r7 = 4278255360
    //     0x911f08: movz            x7, #0xff00
    //     0x911f0c: movk            x7, #0xff00, lsl #16
    // 0x911f10: and             x1, x0, x7
    // 0x911f14: ubfx            x1, x1, #0, #0x20
    // 0x911f18: asr             x0, x1, #8
    // 0x911f1c: mov             x1, x6
    // 0x911f20: ubfx            x1, x1, #0, #0x20
    // 0x911f24: r8 = 16711935
    //     0x911f24: movz            x8, #0xff
    //     0x911f28: movk            x8, #0xff, lsl #16
    // 0x911f2c: and             x9, x1, x8
    // 0x911f30: ubfx            x9, x9, #0, #0x20
    // 0x911f34: lsl             x1, x9, #8
    // 0x911f38: orr             x9, x0, x1
    // 0x911f3c: mov             x0, x9
    // 0x911f40: ubfx            x0, x0, #0, #0x20
    // 0x911f44: r10 = 4294901760
    //     0x911f44: orr             x10, xzr, #0xffff0000
    // 0x911f48: and             x1, x0, x10
    // 0x911f4c: ubfx            x1, x1, #0, #0x20
    // 0x911f50: asr             x0, x1, #0x10
    // 0x911f54: ubfx            x9, x9, #0, #0x20
    // 0x911f58: r11 = 65535
    //     0x911f58: orr             x11, xzr, #0xffff
    // 0x911f5c: and             x1, x9, x11
    // 0x911f60: ubfx            x1, x1, #0, #0x20
    // 0x911f64: lsl             x9, x1, #0x10
    // 0x911f68: orr             x1, x0, x9
    // 0x911f6c: lsl             x0, x1, #0x20
    // 0x911f70: asr             x1, x6, #0x20
    // 0x911f74: mov             x6, x1
    // 0x911f78: ubfx            x6, x6, #0, #0x20
    // 0x911f7c: and             x9, x6, x7
    // 0x911f80: ubfx            x9, x9, #0, #0x20
    // 0x911f84: asr             x6, x9, #8
    // 0x911f88: ubfx            x1, x1, #0, #0x20
    // 0x911f8c: and             x9, x1, x8
    // 0x911f90: ubfx            x9, x9, #0, #0x20
    // 0x911f94: lsl             x1, x9, #8
    // 0x911f98: orr             x9, x6, x1
    // 0x911f9c: mov             x1, x9
    // 0x911fa0: ubfx            x1, x1, #0, #0x20
    // 0x911fa4: and             x6, x1, x10
    // 0x911fa8: ubfx            x6, x6, #0, #0x20
    // 0x911fac: asr             x1, x6, #0x10
    // 0x911fb0: ubfx            x9, x9, #0, #0x20
    // 0x911fb4: and             x6, x9, x11
    // 0x911fb8: ubfx            x6, x6, #0, #0x20
    // 0x911fbc: lsl             x9, x6, #0x10
    // 0x911fc0: orr             x6, x1, x9
    // 0x911fc4: orr             x9, x0, x6
    // 0x911fc8: LoadField: r0 = r4->field_13
    //     0x911fc8: ldur            w0, [x4, #0x13]
    // 0x911fcc: r1 = LoadInt32Instr(r0)
    //     0x911fcc: sbfx            x1, x0, #1, #0x1f
    // 0x911fd0: mov             x0, x1
    // 0x911fd4: r1 = 0
    //     0x911fd4: movz            x1, #0
    // 0x911fd8: cmp             x1, x0
    // 0x911fdc: b.hs            #0x912374
    // 0x911fe0: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x911fe0: ldur            w0, [x4, #0x17]
    // 0x911fe4: DecompressPointer r0
    //     0x911fe4: add             x0, x0, HEAP, lsl #32
    // 0x911fe8: LoadField: r1 = r4->field_1b
    //     0x911fe8: ldur            w1, [x4, #0x1b]
    // 0x911fec: LoadField: r4 = r0->field_7
    //     0x911fec: ldur            x4, [x0, #7]
    // 0x911ff0: asr             w0, w1, #1
    // 0x911ff4: add             x0, x4, w0, sxtw
    // 0x911ff8: str             x9, [x0]
    // 0x911ffc: r0 = InitLateStaticField(0x32c) // [dart:typed_data] ::_convF64
    //     0x911ffc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x912000: ldr             x0, [x0, #0x658]
    //     0x912004: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x912008: cmp             w0, w16
    //     0x91200c: b.ne            #0x91201c
    //     0x912010: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ae08] Field <::._convF64@8027147>: static late final (offset: 0x32c)
    //     0x912014: ldr             x2, [x2, #0xe08]
    //     0x912018: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x91201c: mov             x2, x0
    // 0x912020: LoadField: r0 = r2->field_13
    //     0x912020: ldur            w0, [x2, #0x13]
    // 0x912024: r1 = LoadInt32Instr(r0)
    //     0x912024: sbfx            x1, x0, #1, #0x1f
    // 0x912028: mov             x0, x1
    // 0x91202c: r1 = 0
    //     0x91202c: movz            x1, #0
    // 0x912030: cmp             x1, x0
    // 0x912034: b.hs            #0x912378
    // 0x912038: LoadField: r0 = r2->field_7
    //     0x912038: ldur            x0, [x2, #7]
    // 0x91203c: ldr             d0, [x0]
    // 0x912040: fcmp            d0, d0
    // 0x912044: b.vs            #0x91237c
    // 0x912048: fcvtzs          x0, d0
    // 0x91204c: asr             x16, x0, #0x1e
    // 0x912050: cmp             x16, x0, asr #63
    // 0x912054: b.ne            #0x91237c
    // 0x912058: lsl             x0, x0, #1
    // 0x91205c: r1 = LoadInt32Instr(r0)
    //     0x91205c: sbfx            x1, x0, #1, #0x1f
    //     0x912060: tbz             w0, #0, #0x912068
    //     0x912064: ldur            x1, [x0, #7]
    // 0x912068: r16 = 1000
    //     0x912068: movz            x16, #0x3e8
    // 0x91206c: mul             x0, x1, x16
    // 0x912070: ldur            x2, [fp, #-0xa0]
    // 0x912074: stur            x0, [fp, #-0x50]
    // 0x912078: LoadField: r1 = r2->field_b
    //     0x912078: ldur            w1, [x2, #0xb]
    // 0x91207c: LoadField: r3 = r2->field_f
    //     0x91207c: ldur            w3, [x2, #0xf]
    // 0x912080: DecompressPointer r3
    //     0x912080: add             x3, x3, HEAP, lsl #32
    // 0x912084: LoadField: r4 = r3->field_b
    //     0x912084: ldur            w4, [x3, #0xb]
    // 0x912088: r3 = LoadInt32Instr(r1)
    //     0x912088: sbfx            x3, x1, #1, #0x1f
    // 0x91208c: stur            x3, [fp, #-0x48]
    // 0x912090: r1 = LoadInt32Instr(r4)
    //     0x912090: sbfx            x1, x4, #1, #0x1f
    // 0x912094: cmp             x3, x1
    // 0x912098: b.ne            #0x9120a4
    // 0x91209c: mov             x1, x2
    // 0x9120a0: r0 = _growToNextCapacity()
    //     0x9120a0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9120a4: ldur            x3, [fp, #-0xa0]
    // 0x9120a8: ldur            x5, [fp, #-0x30]
    // 0x9120ac: ldur            x6, [fp, #-0x38]
    // 0x9120b0: ldur            x2, [fp, #-0x50]
    // 0x9120b4: ldur            x4, [fp, #-0x48]
    // 0x9120b8: add             x0, x4, #1
    // 0x9120bc: lsl             x1, x0, #1
    // 0x9120c0: StoreField: r3->field_b = r1
    //     0x9120c0: stur            w1, [x3, #0xb]
    // 0x9120c4: LoadField: r7 = r3->field_f
    //     0x9120c4: ldur            w7, [x3, #0xf]
    // 0x9120c8: DecompressPointer r7
    //     0x9120c8: add             x7, x7, HEAP, lsl #32
    // 0x9120cc: r0 = BoxInt64Instr(r2)
    //     0x9120cc: sbfiz           x0, x2, #1, #0x1f
    //     0x9120d0: cmp             x2, x0, asr #1
    //     0x9120d4: b.eq            #0x9120e0
    //     0x9120d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9120dc: stur            x2, [x0, #7]
    // 0x9120e0: mov             x1, x7
    // 0x9120e4: ArrayStore: r1[r4] = r0  ; List_4
    //     0x9120e4: add             x25, x1, x4, lsl #2
    //     0x9120e8: add             x25, x25, #0xf
    //     0x9120ec: str             w0, [x25]
    //     0x9120f0: tbz             w0, #0, #0x91210c
    //     0x9120f4: ldurb           w16, [x1, #-1]
    //     0x9120f8: ldurb           w17, [x0, #-1]
    //     0x9120fc: and             x16, x17, x16, lsr #2
    //     0x912100: tst             x16, HEAP, lsr #32
    //     0x912104: b.eq            #0x91210c
    //     0x912108: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x91210c: add             x10, x5, #8
    // 0x912110: add             x9, x6, #1
    // 0x912114: ldur            x6, [fp, #-0x98]
    // 0x912118: mov             x7, x3
    // 0x91211c: ldur            x3, [fp, #-0x40]
    // 0x912120: ldur            x2, [fp, #-0x80]
    // 0x912124: b               #0x911e8c
    // 0x912128: mov             x3, x7
    // 0x91212c: mov             x5, x10
    // 0x912130: mov             x8, x5
    // 0x912134: ldur            x6, [fp, #-0xb8]
    // 0x912138: r7 = 0
    //     0x912138: movz            x7, #0
    // 0x91213c: ldur            x4, [fp, #-0x70]
    // 0x912140: ldur            x5, [fp, #-0x28]
    // 0x912144: ldur            x2, [fp, #-0x60]
    // 0x912148: stur            x8, [fp, #-0x38]
    // 0x91214c: stur            x7, [fp, #-0x40]
    // 0x912150: CheckStackOverflow
    //     0x912150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x912154: cmp             SP, x16
    //     0x912158: b.ls            #0x912398
    // 0x91215c: cmp             x7, x5
    // 0x912160: b.ge            #0x9121f8
    // 0x912164: ldur            x0, [fp, #-0x80]
    // 0x912168: mov             x1, x8
    // 0x91216c: cmp             x1, x0
    // 0x912170: b.hs            #0x9123a0
    // 0x912174: add             x0, x2, x8
    // 0x912178: LoadField: r1 = r4->field_7
    //     0x912178: ldur            x1, [x4, #7]
    // 0x91217c: ldrb            w9, [x1, x0]
    // 0x912180: lsl             x0, x9, #1
    // 0x912184: stur            x0, [fp, #-8]
    // 0x912188: LoadField: r1 = r6->field_b
    //     0x912188: ldur            w1, [x6, #0xb]
    // 0x91218c: LoadField: r9 = r6->field_f
    //     0x91218c: ldur            w9, [x6, #0xf]
    // 0x912190: DecompressPointer r9
    //     0x912190: add             x9, x9, HEAP, lsl #32
    // 0x912194: LoadField: r10 = r9->field_b
    //     0x912194: ldur            w10, [x9, #0xb]
    // 0x912198: r9 = LoadInt32Instr(r1)
    //     0x912198: sbfx            x9, x1, #1, #0x1f
    // 0x91219c: stur            x9, [fp, #-0x30]
    // 0x9121a0: r1 = LoadInt32Instr(r10)
    //     0x9121a0: sbfx            x1, x10, #1, #0x1f
    // 0x9121a4: cmp             x9, x1
    // 0x9121a8: b.ne            #0x9121b4
    // 0x9121ac: mov             x1, x6
    // 0x9121b0: r0 = _growToNextCapacity()
    //     0x9121b0: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9121b4: ldur            x5, [fp, #-0xb8]
    // 0x9121b8: ldur            x1, [fp, #-0x38]
    // 0x9121bc: ldur            x0, [fp, #-0x40]
    // 0x9121c0: ldur            x2, [fp, #-8]
    // 0x9121c4: ldur            x3, [fp, #-0x30]
    // 0x9121c8: add             x4, x3, #1
    // 0x9121cc: lsl             x6, x4, #1
    // 0x9121d0: StoreField: r5->field_b = r6
    //     0x9121d0: stur            w6, [x5, #0xb]
    // 0x9121d4: LoadField: r4 = r5->field_f
    //     0x9121d4: ldur            w4, [x5, #0xf]
    // 0x9121d8: DecompressPointer r4
    //     0x9121d8: add             x4, x4, HEAP, lsl #32
    // 0x9121dc: ArrayStore: r4[r3] = r2  ; Unknown_4
    //     0x9121dc: add             x6, x4, x3, lsl #2
    //     0x9121e0: stur            w2, [x6, #0xf]
    // 0x9121e4: add             x8, x1, #1
    // 0x9121e8: add             x7, x0, #1
    // 0x9121ec: ldur            x3, [fp, #-0xa0]
    // 0x9121f0: mov             x6, x5
    // 0x9121f4: b               #0x91213c
    // 0x9121f8: mov             x5, x6
    // 0x9121fc: r0 = Location()
    //     0x9121fc: bl              #0x6fdf0c  ; AllocateLocationStub -> Location (size=0x2c)
    // 0x912200: mov             x1, x0
    // 0x912204: ldur            x2, [fp, #-0x20]
    // 0x912208: ldur            x3, [fp, #-0xa0]
    // 0x91220c: ldur            x5, [fp, #-0xb8]
    // 0x912210: ldur            x6, [fp, #-0x98]
    // 0x912214: stur            x0, [fp, #-8]
    // 0x912218: r0 = Location()
    //     0x912218: bl              #0x6fdbcc  ; [package:timezone/src/location.dart] Location::Location
    // 0x91221c: ldur            x0, [fp, #-8]
    // 0x912220: LeaveFrame
    //     0x912220: mov             SP, fp
    //     0x912224: ldp             fp, lr, [SP], #0x10
    // 0x912228: ret
    //     0x912228: ret             
    // 0x91222c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91222c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912230: b               #0x9115e8
    // 0x912234: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912234: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912238: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912238: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x91223c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91223c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912240: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912240: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912244: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912244: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912248: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912248: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x91224c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91224c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912250: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912250: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912254: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912258: b               #0x911aa4
    // 0x91225c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91225c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912260: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912264: b               #0x911c88
    // 0x912268: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912268: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x91226c: str             x14, [THR, #0x7a8]  ; THR::
    // 0x912270: stp             x20, x23, [SP, #-0x10]!
    // 0x912274: stp             x14, x19, [SP, #-0x10]!
    // 0x912278: stp             x12, x13, [SP, #-0x10]!
    // 0x91227c: stp             x10, x11, [SP, #-0x10]!
    // 0x912280: stp             x8, x9, [SP, #-0x10]!
    // 0x912284: stp             x6, x7, [SP, #-0x10]!
    // 0x912288: stp             x4, x5, [SP, #-0x10]!
    // 0x91228c: stp             x2, x3, [SP, #-0x10]!
    // 0x912290: SaveReg r1
    //     0x912290: str             x1, [SP, #-8]!
    // 0x912294: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x912298: r4 = 0
    //     0x912298: movz            x4, #0
    // 0x91229c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x9122a0: blr             lr
    // 0x9122a4: brk             #0
    // 0x9122a8: str             x14, [THR, #0x7a8]  ; THR::
    // 0x9122ac: stp             x19, x20, [SP, #-0x10]!
    // 0x9122b0: stp             x13, x14, [SP, #-0x10]!
    // 0x9122b4: stp             x11, x12, [SP, #-0x10]!
    // 0x9122b8: stp             x9, x10, [SP, #-0x10]!
    // 0x9122bc: stp             x7, x8, [SP, #-0x10]!
    // 0x9122c0: stp             x5, x6, [SP, #-0x10]!
    // 0x9122c4: stp             x3, x4, [SP, #-0x10]!
    // 0x9122c8: stp             x1, x2, [SP, #-0x10]!
    // 0x9122cc: SaveReg r0
    //     0x9122cc: str             x0, [SP, #-8]!
    // 0x9122d0: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x9122d4: r4 = 0
    //     0x9122d4: movz            x4, #0
    // 0x9122d8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x9122dc: blr             lr
    // 0x9122e0: brk             #0
    // 0x9122e4: str             x13, [THR, #0x7a8]  ; THR::
    // 0x9122e8: stp             x19, x20, [SP, #-0x10]!
    // 0x9122ec: stp             x13, x14, [SP, #-0x10]!
    // 0x9122f0: stp             x11, x12, [SP, #-0x10]!
    // 0x9122f4: stp             x9, x10, [SP, #-0x10]!
    // 0x9122f8: stp             x7, x8, [SP, #-0x10]!
    // 0x9122fc: stp             x5, x6, [SP, #-0x10]!
    // 0x912300: stp             x3, x4, [SP, #-0x10]!
    // 0x912304: stp             x1, x2, [SP, #-0x10]!
    // 0x912308: SaveReg r0
    //     0x912308: str             x0, [SP, #-8]!
    // 0x91230c: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x912310: r4 = 0
    //     0x912310: movz            x4, #0
    // 0x912314: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x912318: blr             lr
    // 0x91231c: brk             #0
    // 0x912320: str             x13, [THR, #0x7a8]  ; THR::
    // 0x912324: stp             x20, x23, [SP, #-0x10]!
    // 0x912328: stp             x14, x19, [SP, #-0x10]!
    // 0x91232c: stp             x12, x13, [SP, #-0x10]!
    // 0x912330: stp             x10, x11, [SP, #-0x10]!
    // 0x912334: stp             x8, x9, [SP, #-0x10]!
    // 0x912338: stp             x6, x7, [SP, #-0x10]!
    // 0x91233c: stp             x4, x5, [SP, #-0x10]!
    // 0x912340: stp             x2, x3, [SP, #-0x10]!
    // 0x912344: SaveReg r0
    //     0x912344: str             x0, [SP, #-8]!
    // 0x912348: ldr             x5, [THR, #0x460]  ; THR::ArgumentErrorUnboxedInt64
    // 0x91234c: r4 = 0
    //     0x91234c: movz            x4, #0
    // 0x912350: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x912354: blr             lr
    // 0x912358: brk             #0
    // 0x91235c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91235c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912360: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912360: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912364: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912364: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912368: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91236c: b               #0x911eac
    // 0x912370: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912370: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912374: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912374: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x912378: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x912378: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x91237c: SaveReg d0
    //     0x91237c: str             q0, [SP, #-0x10]!
    // 0x912380: r0 = 74
    //     0x912380: movz            x0, #0x4a
    // 0x912384: r30 = DoubleToIntegerStub
    //     0x912384: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x912388: LoadField: r30 = r30->field_7
    //     0x912388: ldur            lr, [lr, #7]
    // 0x91238c: blr             lr
    // 0x912390: RestoreReg d0
    //     0x912390: ldr             q0, [SP], #0x10
    // 0x912394: b               #0x91205c
    // 0x912398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91239c: b               #0x91215c
    // 0x9123a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9123a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
