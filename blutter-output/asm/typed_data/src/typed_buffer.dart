// lib: , url: package:typed_data/src/typed_buffer.dart

// class id: 1051214, size: 0x8
class :: {
}

// class id: 7317, size: 0x18, field offset: 0xc
abstract class TypedDataBuffer<X0> extends ListBase<X0> {

  int length(TypedDataBuffer<X0>) {
    // ** addr: 0x91324c, size: 0x48
    // 0x91324c: ldr             x2, [SP]
    // 0x913250: LoadField: r3 = r2->field_f
    //     0x913250: ldur            x3, [x2, #0xf]
    // 0x913254: r0 = BoxInt64Instr(r3)
    //     0x913254: sbfiz           x0, x3, #1, #0x1f
    //     0x913258: cmp             x3, x0, asr #1
    //     0x91325c: b.eq            #0x913278
    //     0x913260: stp             fp, lr, [SP, #-0x10]!
    //     0x913264: mov             fp, SP
    //     0x913268: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91326c: mov             SP, fp
    //     0x913270: ldp             fp, lr, [SP], #0x10
    //     0x913274: stur            x3, [x0, #7]
    // 0x913278: ret
    //     0x913278: ret             
  }
  _ setRange(/* No info */) {
    // ** addr: 0x66701c, size: 0x778
    // 0x66701c: EnterFrame
    //     0x66701c: stp             fp, lr, [SP, #-0x10]!
    //     0x667020: mov             fp, SP
    // 0x667024: AllocStack(0x68)
    //     0x667024: sub             SP, SP, #0x68
    // 0x667028: SetupParameters(TypedDataBuffer<X0> this /* r1 => r7, fp-0x18 */, dynamic _ /* r2 => r6, fp-0x20 */, dynamic _ /* r3 => r5, fp-0x28 */, dynamic _ /* r5 => r3, fp-0x30 */, [int _ = 0 /* r4, fp-0x10 */])
    //     0x667028: mov             x7, x1
    //     0x66702c: mov             x6, x2
    //     0x667030: stur            x3, [fp, #-0x28]
    //     0x667034: mov             x16, x5
    //     0x667038: mov             x5, x3
    //     0x66703c: mov             x3, x16
    //     0x667040: stur            x1, [fp, #-0x18]
    //     0x667044: stur            x2, [fp, #-0x20]
    //     0x667048: stur            x3, [fp, #-0x30]
    //     0x66704c: ldur            w0, [x4, #0x13]
    //     0x667050: sub             x1, x0, #8
    //     0x667054: cmp             w1, #2
    //     0x667058: b.lt            #0x667078
    //     0x66705c: add             x0, fp, w1, sxtw #2
    //     0x667060: ldr             x0, [x0, #8]
    //     0x667064: sbfx            x1, x0, #1, #0x1f
    //     0x667068: tbz             w0, #0, #0x667070
    //     0x66706c: ldur            x1, [x0, #7]
    //     0x667070: mov             x4, x1
    //     0x667074: b               #0x66707c
    //     0x667078: movz            x4, #0
    //     0x66707c: stur            x4, [fp, #-0x10]
    // 0x667080: CheckStackOverflow
    //     0x667080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x667084: cmp             SP, x16
    //     0x667088: b.ls            #0x66778c
    // 0x66708c: LoadField: r8 = r7->field_7
    //     0x66708c: ldur            w8, [x7, #7]
    // 0x667090: DecompressPointer r8
    //     0x667090: add             x8, x8, HEAP, lsl #32
    // 0x667094: mov             x0, x3
    // 0x667098: mov             x2, x8
    // 0x66709c: stur            x8, [fp, #-8]
    // 0x6670a0: r1 = Null
    //     0x6670a0: mov             x1, NULL
    // 0x6670a4: r8 = Iterable<X0>
    //     0x6670a4: ldr             x8, [PP, #0x6e0]  ; [pp+0x6e0] Type: Iterable<X0>
    // 0x6670a8: LoadField: r9 = r8->field_7
    //     0x6670a8: ldur            x9, [x8, #7]
    // 0x6670ac: r3 = Null
    //     0x6670ac: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c7d8] Null
    //     0x6670b0: ldr             x3, [x3, #0x7d8]
    // 0x6670b4: blr             x9
    // 0x6670b8: ldur            x3, [fp, #-0x18]
    // 0x6670bc: LoadField: r2 = r3->field_f
    //     0x6670bc: ldur            x2, [x3, #0xf]
    // 0x6670c0: ldur            x4, [fp, #-0x28]
    // 0x6670c4: cmp             x4, x2
    // 0x6670c8: b.gt            #0x667680
    // 0x6670cc: ldur            x0, [fp, #-0x30]
    // 0x6670d0: ldur            x2, [fp, #-8]
    // 0x6670d4: r1 = Null
    //     0x6670d4: mov             x1, NULL
    // 0x6670d8: cmp             w0, NULL
    // 0x6670dc: b.eq            #0x667128
    // 0x6670e0: branchIfSmi(r0, 0x667128)
    //     0x6670e0: tbz             w0, #0, #0x667128
    // 0x6670e4: r3 = SubtypeTestCache
    //     0x6670e4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c7e8] SubtypeTestCache
    //     0x6670e8: ldr             x3, [x3, #0x7e8]
    // 0x6670ec: r30 = Subtype3TestCacheStub
    //     0x6670ec: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2c84)
    // 0x6670f0: LoadField: r30 = r30->field_7
    //     0x6670f0: ldur            lr, [lr, #7]
    // 0x6670f4: blr             lr
    // 0x6670f8: cmp             w7, NULL
    // 0x6670fc: b.eq            #0x667108
    // 0x667100: tbnz            w7, #4, #0x667128
    // 0x667104: b               #0x667130
    // 0x667108: r8 = TypedDataBuffer<X0>
    //     0x667108: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c7f0] Type: TypedDataBuffer<X0>
    //     0x66710c: ldr             x8, [x8, #0x7f0]
    // 0x667110: r3 = SubtypeTestCache
    //     0x667110: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c7f8] SubtypeTestCache
    //     0x667114: ldr             x3, [x3, #0x7f8]
    // 0x667118: r30 = InstanceOfStub
    //     0x667118: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x66711c: LoadField: r30 = r30->field_7
    //     0x66711c: ldur            lr, [lr, #7]
    // 0x667120: blr             lr
    // 0x667124: b               #0x667134
    // 0x667128: r0 = false
    //     0x667128: add             x0, NULL, #0x30  ; false
    // 0x66712c: b               #0x667134
    // 0x667130: r0 = true
    //     0x667130: add             x0, NULL, #0x20  ; true
    // 0x667134: tbnz            w0, #4, #0x667364
    // 0x667138: ldur            x0, [fp, #-0x18]
    // 0x66713c: ldur            x4, [fp, #-0x20]
    // 0x667140: ldur            x3, [fp, #-0x30]
    // 0x667144: LoadField: r5 = r0->field_b
    //     0x667144: ldur            w5, [x0, #0xb]
    // 0x667148: DecompressPointer r5
    //     0x667148: add             x5, x5, HEAP, lsl #32
    // 0x66714c: stur            x5, [fp, #-0x38]
    // 0x667150: LoadField: r6 = r3->field_b
    //     0x667150: ldur            w6, [x3, #0xb]
    // 0x667154: DecompressPointer r6
    //     0x667154: add             x6, x6, HEAP, lsl #32
    // 0x667158: stur            x6, [fp, #-8]
    // 0x66715c: tbz             x4, #0x3f, #0x667168
    // 0x667160: ldur            x7, [fp, #-0x28]
    // 0x667164: b               #0x667184
    // 0x667168: ldur            x7, [fp, #-0x28]
    // 0x66716c: cmp             x4, x7
    // 0x667170: b.gt            #0x667184
    // 0x667174: LoadField: r0 = r5->field_13
    //     0x667174: ldur            w0, [x5, #0x13]
    // 0x667178: r1 = LoadInt32Instr(r0)
    //     0x667178: sbfx            x1, x0, #1, #0x1f
    // 0x66717c: cmp             x7, x1
    // 0x667180: b.le            #0x6671b0
    // 0x667184: LoadField: r2 = r5->field_13
    //     0x667184: ldur            w2, [x5, #0x13]
    // 0x667188: r0 = BoxInt64Instr(r7)
    //     0x667188: sbfiz           x0, x7, #1, #0x1f
    //     0x66718c: cmp             x7, x0, asr #1
    //     0x667190: b.eq            #0x66719c
    //     0x667194: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x667198: stur            x7, [x0, #7]
    // 0x66719c: r3 = LoadInt32Instr(r2)
    //     0x66719c: sbfx            x3, x2, #1, #0x1f
    // 0x6671a0: mov             x1, x4
    // 0x6671a4: mov             x2, x0
    // 0x6671a8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6671a8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x6671ac: r0 = checkValidRange()
    //     0x6671ac: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x6671b0: ldur            x6, [fp, #-0x10]
    // 0x6671b4: tbnz            x6, #0x3f, #0x6676e4
    // 0x6671b8: ldur            x4, [fp, #-0x20]
    // 0x6671bc: ldur            x5, [fp, #-0x28]
    // 0x6671c0: ldur            x2, [fp, #-8]
    // 0x6671c4: sub             x3, x5, x4
    // 0x6671c8: LoadField: r0 = r2->field_13
    //     0x6671c8: ldur            w0, [x2, #0x13]
    // 0x6671cc: r1 = LoadInt32Instr(r0)
    //     0x6671cc: sbfx            x1, x0, #1, #0x1f
    // 0x6671d0: sub             x0, x1, x6
    // 0x6671d4: cmp             x0, x3
    // 0x6671d8: b.lt            #0x66772c
    // 0x6671dc: cbz             x3, #0x667670
    // 0x6671e0: r0 = BoxInt64Instr(r3)
    //     0x6671e0: sbfiz           x0, x3, #1, #0x1f
    //     0x6671e4: cmp             x3, x0, asr #1
    //     0x6671e8: b.eq            #0x6671f4
    //     0x6671ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6671f0: stur            x3, [x0, #7]
    // 0x6671f4: mov             x5, x0
    // 0x6671f8: cmp             w5, #0x800
    // 0x6671fc: b.ge            #0x667304
    // 0x667200: ldur            x7, [fp, #-0x38]
    // 0x667204: r0 = BoxInt64Instr(r4)
    //     0x667204: sbfiz           x0, x4, #1, #0x1f
    //     0x667208: cmp             x4, x0, asr #1
    //     0x66720c: b.eq            #0x667218
    //     0x667210: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x667214: stur            x4, [x0, #7]
    // 0x667218: mov             x3, x0
    // 0x66721c: r0 = BoxInt64Instr(r6)
    //     0x66721c: sbfiz           x0, x6, #1, #0x1f
    //     0x667220: cmp             x6, x0, asr #1
    //     0x667224: b.eq            #0x667230
    //     0x667228: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66722c: stur            x6, [x0, #7]
    // 0x667230: sxtw            x0, w0
    // 0x667234: add             x4, x2, x0, asr #1
    // 0x667238: add             x4, x4, #0x17
    // 0x66723c: sxtw            x3, w3
    // 0x667240: add             x1, x7, x3, asr #1
    // 0x667244: add             x1, x1, #0x17
    // 0x667248: cbz             x5, #0x667300
    // 0x66724c: cmp             x1, x4
    // 0x667250: b.ls            #0x6672b8
    // 0x667254: sxtw            x5, w5
    // 0x667258: add             x16, x4, x5, asr #1
    // 0x66725c: cmp             x1, x16
    // 0x667260: b.hs            #0x6672b8
    // 0x667264: mov             x4, x16
    // 0x667268: add             x1, x1, x5, asr #1
    // 0x66726c: tbz             w5, #4, #0x667278
    // 0x667270: ldr             x16, [x4, #-8]!
    // 0x667274: str             x16, [x1, #-8]!
    // 0x667278: tbz             w5, #3, #0x667284
    // 0x66727c: ldr             w16, [x4, #-4]!
    // 0x667280: str             w16, [x1, #-4]!
    // 0x667284: tbz             w5, #2, #0x667290
    // 0x667288: ldrh            w16, [x4, #-2]!
    // 0x66728c: strh            w16, [x1, #-2]!
    // 0x667290: tbz             w5, #1, #0x66729c
    // 0x667294: ldrb            w16, [x4, #-1]!
    // 0x667298: strb            w16, [x1, #-1]!
    // 0x66729c: ands            w5, w5, #0xffffffe1
    // 0x6672a0: b.eq            #0x667300
    // 0x6672a4: ldp             x16, x17, [x4, #-0x10]!
    // 0x6672a8: stp             x16, x17, [x1, #-0x10]!
    // 0x6672ac: subs            w5, w5, #0x20
    // 0x6672b0: b.ne            #0x6672a4
    // 0x6672b4: b               #0x667300
    // 0x6672b8: tbz             w5, #4, #0x6672c4
    // 0x6672bc: ldr             x16, [x4], #8
    // 0x6672c0: str             x16, [x1], #8
    // 0x6672c4: tbz             w5, #3, #0x6672d0
    // 0x6672c8: ldr             w16, [x4], #4
    // 0x6672cc: str             w16, [x1], #4
    // 0x6672d0: tbz             w5, #2, #0x6672dc
    // 0x6672d4: ldrh            w16, [x4], #2
    // 0x6672d8: strh            w16, [x1], #2
    // 0x6672dc: tbz             w5, #1, #0x6672e8
    // 0x6672e0: ldrb            w16, [x4], #1
    // 0x6672e4: strb            w16, [x1], #1
    // 0x6672e8: ands            w5, w5, #0xffffffe1
    // 0x6672ec: b.eq            #0x667300
    // 0x6672f0: ldp             x16, x17, [x4], #0x10
    // 0x6672f4: stp             x16, x17, [x1], #0x10
    // 0x6672f8: subs            w5, w5, #0x20
    // 0x6672fc: b.ne            #0x6672f0
    // 0x667300: b               #0x667670
    // 0x667304: ldur            x7, [fp, #-0x38]
    // 0x667308: LoadField: r0 = r7->field_7
    //     0x667308: ldur            x0, [x7, #7]
    // 0x66730c: add             x1, x0, x4
    // 0x667310: LoadField: r0 = r2->field_7
    //     0x667310: ldur            x0, [x2, #7]
    // 0x667314: add             x2, x0, x6
    // 0x667318: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x667318: mov             x0, THR
    //     0x66731c: ldr             x9, [x0, #0x658]
    //     0x667320: mov             x0, x1
    //     0x667324: mov             x1, x2
    //     0x667328: mov             x2, x3
    //     0x66732c: mov             x17, fp
    //     0x667330: str             fp, [SP, #-8]!
    //     0x667334: mov             fp, SP
    //     0x667338: and             SP, SP, #0xfffffffffffffff0
    //     0x66733c: mov             x19, sp
    //     0x667340: mov             sp, SP
    //     0x667344: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x667348: blr             x9
    //     0x66734c: movz            x16, #0x8
    //     0x667350: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x667354: mov             sp, x19
    //     0x667358: mov             SP, fp
    //     0x66735c: ldr             fp, [SP], #8
    // 0x667360: b               #0x667670
    // 0x667364: ldur            x0, [fp, #-0x18]
    // 0x667368: ldur            x4, [fp, #-0x20]
    // 0x66736c: ldur            x5, [fp, #-0x28]
    // 0x667370: ldur            x3, [fp, #-0x30]
    // 0x667374: ldur            x6, [fp, #-0x10]
    // 0x667378: LoadField: r7 = r0->field_b
    //     0x667378: ldur            w7, [x0, #0xb]
    // 0x66737c: DecompressPointer r7
    //     0x66737c: add             x7, x7, HEAP, lsl #32
    // 0x667380: mov             x0, x3
    // 0x667384: stur            x7, [fp, #-8]
    // 0x667388: r2 = Null
    //     0x667388: mov             x2, NULL
    // 0x66738c: r1 = Null
    //     0x66738c: mov             x1, NULL
    // 0x667390: r8 = Iterable<int>
    //     0x667390: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c800] Type: Iterable<int>
    //     0x667394: ldr             x8, [x8, #0x800]
    // 0x667398: r3 = Null
    //     0x667398: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c808] Null
    //     0x66739c: ldr             x3, [x3, #0x808]
    // 0x6673a0: r0 = Iterable<int>()
    //     0x6673a0: bl              #0x66781c  ; IsType_Iterable<int>_Stub
    // 0x6673a4: ldur            x4, [fp, #-0x20]
    // 0x6673a8: tbz             x4, #0x3f, #0x6673b8
    // 0x6673ac: ldur            x5, [fp, #-0x28]
    // 0x6673b0: ldur            x6, [fp, #-8]
    // 0x6673b4: b               #0x6673e0
    // 0x6673b8: ldur            x5, [fp, #-0x28]
    // 0x6673bc: cmp             x4, x5
    // 0x6673c0: b.le            #0x6673cc
    // 0x6673c4: ldur            x6, [fp, #-8]
    // 0x6673c8: b               #0x6673e0
    // 0x6673cc: ldur            x6, [fp, #-8]
    // 0x6673d0: LoadField: r0 = r6->field_13
    //     0x6673d0: ldur            w0, [x6, #0x13]
    // 0x6673d4: r1 = LoadInt32Instr(r0)
    //     0x6673d4: sbfx            x1, x0, #1, #0x1f
    // 0x6673d8: cmp             x5, x1
    // 0x6673dc: b.le            #0x66740c
    // 0x6673e0: LoadField: r2 = r6->field_13
    //     0x6673e0: ldur            w2, [x6, #0x13]
    // 0x6673e4: r0 = BoxInt64Instr(r5)
    //     0x6673e4: sbfiz           x0, x5, #1, #0x1f
    //     0x6673e8: cmp             x5, x0, asr #1
    //     0x6673ec: b.eq            #0x6673f8
    //     0x6673f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6673f4: stur            x5, [x0, #7]
    // 0x6673f8: r3 = LoadInt32Instr(r2)
    //     0x6673f8: sbfx            x3, x2, #1, #0x1f
    // 0x6673fc: mov             x1, x4
    // 0x667400: mov             x2, x0
    // 0x667404: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x667404: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x667408: r0 = checkValidRange()
    //     0x667408: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x66740c: ldur            x6, [fp, #-0x10]
    // 0x667410: tbnz            x6, #0x3f, #0x667738
    // 0x667414: ldur            x2, [fp, #-0x30]
    // 0x667418: r0 = LoadClassIdInstr(r2)
    //     0x667418: ldur            x0, [x2, #-1]
    //     0x66741c: ubfx            x0, x0, #0xc, #0x14
    // 0x667420: sub             x16, x0, #0x70
    // 0x667424: cmp             x16, #0x37
    // 0x667428: b.hi            #0x667654
    // 0x66742c: r0 = LoadClassIdInstr(r2)
    //     0x66742c: ldur            x0, [x2, #-1]
    //     0x667430: ubfx            x0, x0, #0xc, #0x14
    // 0x667434: mov             x1, x2
    // 0x667438: r0 = GDT[cid_x0 + 0xd16b]()
    //     0x667438: movz            x17, #0xd16b
    //     0x66743c: add             lr, x0, x17
    //     0x667440: ldr             lr, [x21, lr, lsl #3]
    //     0x667444: blr             lr
    // 0x667448: cmp             x0, #1
    // 0x66744c: b.ne            #0x66763c
    // 0x667450: ldur            x2, [fp, #-0x20]
    // 0x667454: ldur            x3, [fp, #-0x28]
    // 0x667458: ldur            x5, [fp, #-0x30]
    // 0x66745c: ldur            x6, [fp, #-0x10]
    // 0x667460: sub             x1, x3, x2
    // 0x667464: stur            x1, [fp, #-0x40]
    // 0x667468: r0 = LoadClassIdInstr(r5)
    //     0x667468: ldur            x0, [x5, #-1]
    //     0x66746c: ubfx            x0, x0, #0xc, #0x14
    // 0x667470: str             x5, [SP]
    // 0x667474: r0 = GDT[cid_x0 + 0xc834]()
    //     0x667474: movz            x17, #0xc834
    //     0x667478: add             lr, x0, x17
    //     0x66747c: ldr             lr, [x21, lr, lsl #3]
    //     0x667480: blr             lr
    // 0x667484: r1 = LoadInt32Instr(r0)
    //     0x667484: sbfx            x1, x0, #1, #0x1f
    //     0x667488: tbz             w0, #0, #0x667490
    //     0x66748c: ldur            x1, [x0, #7]
    // 0x667490: ldur            x6, [fp, #-0x10]
    // 0x667494: sub             x0, x1, x6
    // 0x667498: ldur            x2, [fp, #-0x40]
    // 0x66749c: cmp             x0, x2
    // 0x6674a0: b.lt            #0x667780
    // 0x6674a4: cbz             x2, #0x667670
    // 0x6674a8: r0 = BoxInt64Instr(r2)
    //     0x6674a8: sbfiz           x0, x2, #1, #0x1f
    //     0x6674ac: cmp             x2, x0, asr #1
    //     0x6674b0: b.eq            #0x6674bc
    //     0x6674b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6674b8: stur            x2, [x0, #7]
    // 0x6674bc: mov             x3, x0
    // 0x6674c0: cmp             w3, #0x800
    // 0x6674c4: b.ge            #0x6675d8
    // 0x6674c8: ldur            x4, [fp, #-0x20]
    // 0x6674cc: ldur            x5, [fp, #-0x30]
    // 0x6674d0: ldur            x7, [fp, #-8]
    // 0x6674d4: r0 = BoxInt64Instr(r4)
    //     0x6674d4: sbfiz           x0, x4, #1, #0x1f
    //     0x6674d8: cmp             x4, x0, asr #1
    //     0x6674dc: b.eq            #0x6674e8
    //     0x6674e0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6674e4: stur            x4, [x0, #7]
    // 0x6674e8: mov             x2, x0
    // 0x6674ec: r0 = BoxInt64Instr(r6)
    //     0x6674ec: sbfiz           x0, x6, #1, #0x1f
    //     0x6674f0: cmp             x6, x0, asr #1
    //     0x6674f4: b.eq            #0x667500
    //     0x6674f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6674fc: stur            x6, [x0, #7]
    // 0x667500: LoadField: r1 = r5->field_7
    //     0x667500: ldur            x1, [x5, #7]
    // 0x667504: mov             x5, x3
    // 0x667508: sxtw            x0, w0
    // 0x66750c: add             x4, x1, x0, asr #1
    // 0x667510: sxtw            x2, w2
    // 0x667514: add             x3, x7, x2, asr #1
    // 0x667518: add             x3, x3, #0x17
    // 0x66751c: cbz             x5, #0x6675d4
    // 0x667520: cmp             x3, x4
    // 0x667524: b.ls            #0x66758c
    // 0x667528: sxtw            x5, w5
    // 0x66752c: add             x16, x4, x5, asr #1
    // 0x667530: cmp             x3, x16
    // 0x667534: b.hs            #0x66758c
    // 0x667538: mov             x4, x16
    // 0x66753c: add             x3, x3, x5, asr #1
    // 0x667540: tbz             w5, #4, #0x66754c
    // 0x667544: ldr             x16, [x4, #-8]!
    // 0x667548: str             x16, [x3, #-8]!
    // 0x66754c: tbz             w5, #3, #0x667558
    // 0x667550: ldr             w16, [x4, #-4]!
    // 0x667554: str             w16, [x3, #-4]!
    // 0x667558: tbz             w5, #2, #0x667564
    // 0x66755c: ldrh            w16, [x4, #-2]!
    // 0x667560: strh            w16, [x3, #-2]!
    // 0x667564: tbz             w5, #1, #0x667570
    // 0x667568: ldrb            w16, [x4, #-1]!
    // 0x66756c: strb            w16, [x3, #-1]!
    // 0x667570: ands            w5, w5, #0xffffffe1
    // 0x667574: b.eq            #0x6675d4
    // 0x667578: ldp             x16, x17, [x4, #-0x10]!
    // 0x66757c: stp             x16, x17, [x3, #-0x10]!
    // 0x667580: subs            w5, w5, #0x20
    // 0x667584: b.ne            #0x667578
    // 0x667588: b               #0x6675d4
    // 0x66758c: tbz             w5, #4, #0x667598
    // 0x667590: ldr             x16, [x4], #8
    // 0x667594: str             x16, [x3], #8
    // 0x667598: tbz             w5, #3, #0x6675a4
    // 0x66759c: ldr             w16, [x4], #4
    // 0x6675a0: str             w16, [x3], #4
    // 0x6675a4: tbz             w5, #2, #0x6675b0
    // 0x6675a8: ldrh            w16, [x4], #2
    // 0x6675ac: strh            w16, [x3], #2
    // 0x6675b0: tbz             w5, #1, #0x6675bc
    // 0x6675b4: ldrb            w16, [x4], #1
    // 0x6675b8: strb            w16, [x3], #1
    // 0x6675bc: ands            w5, w5, #0xffffffe1
    // 0x6675c0: b.eq            #0x6675d4
    // 0x6675c4: ldp             x16, x17, [x4], #0x10
    // 0x6675c8: stp             x16, x17, [x3], #0x10
    // 0x6675cc: subs            w5, w5, #0x20
    // 0x6675d0: b.ne            #0x6675c4
    // 0x6675d4: b               #0x667670
    // 0x6675d8: ldur            x4, [fp, #-0x20]
    // 0x6675dc: ldur            x5, [fp, #-0x30]
    // 0x6675e0: ldur            x7, [fp, #-8]
    // 0x6675e4: LoadField: r0 = r7->field_7
    //     0x6675e4: ldur            x0, [x7, #7]
    // 0x6675e8: add             x1, x0, x4
    // 0x6675ec: LoadField: r0 = r5->field_7
    //     0x6675ec: ldur            x0, [x5, #7]
    // 0x6675f0: add             x3, x0, x6
    // 0x6675f4: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x6675f4: mov             x0, THR
    //     0x6675f8: ldr             x9, [x0, #0x658]
    //     0x6675fc: mov             x0, x1
    //     0x667600: mov             x1, x3
    //     0x667604: mov             x17, fp
    //     0x667608: str             fp, [SP, #-8]!
    //     0x66760c: mov             fp, SP
    //     0x667610: and             SP, SP, #0xfffffffffffffff0
    //     0x667614: mov             x19, sp
    //     0x667618: mov             sp, SP
    //     0x66761c: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x667620: blr             x9
    //     0x667624: movz            x16, #0x8
    //     0x667628: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x66762c: mov             sp, x19
    //     0x667630: mov             SP, fp
    //     0x667634: ldr             fp, [SP], #8
    // 0x667638: b               #0x667670
    // 0x66763c: ldur            x4, [fp, #-0x20]
    // 0x667640: ldur            x3, [fp, #-0x28]
    // 0x667644: ldur            x5, [fp, #-0x30]
    // 0x667648: ldur            x6, [fp, #-0x10]
    // 0x66764c: ldur            x7, [fp, #-8]
    // 0x667650: b               #0x667664
    // 0x667654: ldur            x4, [fp, #-0x20]
    // 0x667658: ldur            x3, [fp, #-0x28]
    // 0x66765c: mov             x5, x2
    // 0x667660: ldur            x7, [fp, #-8]
    // 0x667664: mov             x1, x7
    // 0x667668: mov             x2, x4
    // 0x66766c: r0 = _slowSetRange()
    //     0x66766c: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x667670: r0 = Null
    //     0x667670: mov             x0, NULL
    // 0x667674: LeaveFrame
    //     0x667674: mov             SP, fp
    //     0x667678: ldp             fp, lr, [SP], #0x10
    // 0x66767c: ret
    //     0x66767c: ret             
    // 0x667680: mov             x3, x4
    // 0x667684: r0 = BoxInt64Instr(r3)
    //     0x667684: sbfiz           x0, x3, #1, #0x1f
    //     0x667688: cmp             x3, x0, asr #1
    //     0x66768c: b.eq            #0x667698
    //     0x667690: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x667694: stur            x3, [x0, #7]
    // 0x667698: mov             x3, x0
    // 0x66769c: stur            x3, [fp, #-0x18]
    // 0x6676a0: r0 = BoxInt64Instr(r2)
    //     0x6676a0: sbfiz           x0, x2, #1, #0x1f
    //     0x6676a4: cmp             x2, x0, asr #1
    //     0x6676a8: b.eq            #0x6676b4
    //     0x6676ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6676b0: stur            x2, [x0, #7]
    // 0x6676b4: stur            x0, [fp, #-8]
    // 0x6676b8: r0 = RangeError()
    //     0x6676b8: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6676bc: stur            x0, [fp, #-0x30]
    // 0x6676c0: ldur            x16, [fp, #-0x18]
    // 0x6676c4: stp             x16, x0, [SP, #0x10]
    // 0x6676c8: ldur            x16, [fp, #-8]
    // 0x6676cc: stp             x16, xzr, [SP]
    // 0x6676d0: r4 = const [0, 0x4, 0x4, 0x4, null]
    //     0x6676d0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    // 0x6676d4: r0 = RangeError.range()
    //     0x6676d4: bl              #0x600404  ; [dart:core] RangeError::RangeError.range
    // 0x6676d8: ldur            x0, [fp, #-0x30]
    // 0x6676dc: r0 = Throw()
    //     0x6676dc: bl              #0xec04b8  ; ThrowStub
    // 0x6676e0: brk             #0
    // 0x6676e4: r0 = BoxInt64Instr(r6)
    //     0x6676e4: sbfiz           x0, x6, #1, #0x1f
    //     0x6676e8: cmp             x6, x0, asr #1
    //     0x6676ec: b.eq            #0x6676f8
    //     0x6676f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6676f4: stur            x6, [x0, #7]
    // 0x6676f8: stur            x0, [fp, #-8]
    // 0x6676fc: r0 = RangeError()
    //     0x6676fc: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x667700: stur            x0, [fp, #-0x18]
    // 0x667704: ldur            x16, [fp, #-8]
    // 0x667708: stp             x16, x0, [SP, #0x18]
    // 0x66770c: stp             NULL, xzr, [SP, #8]
    // 0x667710: r16 = "skipCount"
    //     0x667710: ldr             x16, [PP, #0xe50]  ; [pp+0xe50] "skipCount"
    // 0x667714: str             x16, [SP]
    // 0x667718: r4 = const [0, 0x5, 0x5, 0x5, null]
    //     0x667718: ldr             x4, [PP, #0xce0]  ; [pp+0xce0] List(5) [0, 0x5, 0x5, 0x5, Null]
    // 0x66771c: r0 = RangeError.range()
    //     0x66771c: bl              #0x600404  ; [dart:core] RangeError::RangeError.range
    // 0x667720: ldur            x0, [fp, #-0x18]
    // 0x667724: r0 = Throw()
    //     0x667724: bl              #0xec04b8  ; ThrowStub
    // 0x667728: brk             #0
    // 0x66772c: r0 = tooFew()
    //     0x66772c: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x667730: r0 = Throw()
    //     0x667730: bl              #0xec04b8  ; ThrowStub
    // 0x667734: brk             #0
    // 0x667738: r0 = BoxInt64Instr(r6)
    //     0x667738: sbfiz           x0, x6, #1, #0x1f
    //     0x66773c: cmp             x6, x0, asr #1
    //     0x667740: b.eq            #0x66774c
    //     0x667744: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x667748: stur            x6, [x0, #7]
    // 0x66774c: stur            x0, [fp, #-8]
    // 0x667750: r0 = RangeError()
    //     0x667750: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x667754: stur            x0, [fp, #-0x18]
    // 0x667758: ldur            x16, [fp, #-8]
    // 0x66775c: stp             x16, x0, [SP, #0x18]
    // 0x667760: stp             NULL, xzr, [SP, #8]
    // 0x667764: r16 = "skipCount"
    //     0x667764: ldr             x16, [PP, #0xe50]  ; [pp+0xe50] "skipCount"
    // 0x667768: str             x16, [SP]
    // 0x66776c: r4 = const [0, 0x5, 0x5, 0x5, null]
    //     0x66776c: ldr             x4, [PP, #0xce0]  ; [pp+0xce0] List(5) [0, 0x5, 0x5, 0x5, Null]
    // 0x667770: r0 = RangeError.range()
    //     0x667770: bl              #0x600404  ; [dart:core] RangeError::RangeError.range
    // 0x667774: ldur            x0, [fp, #-0x18]
    // 0x667778: r0 = Throw()
    //     0x667778: bl              #0xec04b8  ; ThrowStub
    // 0x66777c: brk             #0
    // 0x667780: r0 = tooFew()
    //     0x667780: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x667784: r0 = Throw()
    //     0x667784: bl              #0xec04b8  ; ThrowStub
    // 0x667788: brk             #0
    // 0x66778c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66778c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x667790: b               #0x66708c
  }
  X0 [](TypedDataBuffer<X0>, int) {
    // ** addr: 0x6677ac, size: 0x88
    // 0x6677ac: EnterFrame
    //     0x6677ac: stp             fp, lr, [SP, #-0x10]!
    //     0x6677b0: mov             fp, SP
    // 0x6677b4: AllocStack(0x10)
    //     0x6677b4: sub             SP, SP, #0x10
    // 0x6677b8: CheckStackOverflow
    //     0x6677b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6677bc: cmp             SP, x16
    //     0x6677c0: b.ls            #0x667814
    // 0x6677c4: ldr             x0, [fp, #0x10]
    // 0x6677c8: r2 = Null
    //     0x6677c8: mov             x2, NULL
    // 0x6677cc: r1 = Null
    //     0x6677cc: mov             x1, NULL
    // 0x6677d0: branchIfSmi(r0, 0x6677f8)
    //     0x6677d0: tbz             w0, #0, #0x6677f8
    // 0x6677d4: r4 = LoadClassIdInstr(r0)
    //     0x6677d4: ldur            x4, [x0, #-1]
    //     0x6677d8: ubfx            x4, x4, #0xc, #0x14
    // 0x6677dc: sub             x4, x4, #0x3c
    // 0x6677e0: cmp             x4, #1
    // 0x6677e4: b.ls            #0x6677f8
    // 0x6677e8: r8 = int
    //     0x6677e8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x6677ec: r3 = Null
    //     0x6677ec: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c860] Null
    //     0x6677f0: ldr             x3, [x3, #0x860]
    // 0x6677f4: r0 = int()
    //     0x6677f4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x6677f8: ldr             x16, [fp, #0x18]
    // 0x6677fc: ldr             lr, [fp, #0x10]
    // 0x667800: stp             lr, x16, [SP]
    // 0x667804: r0 = []()
    //     0x667804: bl              #0x66e1dc  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::[]
    // 0x667808: LeaveFrame
    //     0x667808: mov             SP, fp
    //     0x66780c: ldp             fp, lr, [SP], #0x10
    // 0x667810: ret
    //     0x667810: ret             
    // 0x667814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x667814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x667818: b               #0x6677c4
  }
  _ addAll(/* No info */) {
    // ** addr: 0x668a0c, size: 0x8c
    // 0x668a0c: EnterFrame
    //     0x668a0c: stp             fp, lr, [SP, #-0x10]!
    //     0x668a10: mov             fp, SP
    // 0x668a14: AllocStack(0x10)
    //     0x668a14: sub             SP, SP, #0x10
    // 0x668a18: SetupParameters(TypedDataBuffer<X0> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, [dynamic _])
    //     0x668a18: mov             x5, x1
    //     0x668a1c: mov             x3, x2
    //     0x668a20: stur            x1, [fp, #-8]
    //     0x668a24: stur            x2, [fp, #-0x10]
    //     0x668a28: ldur            w0, [x4, #0x13]
    //     0x668a2c: sub             x1, x0, #4
    //     0x668a30: cmp             w1, #2
    //     0x668a34: b.lt            #0x668a40
    //     0x668a38: cmp             w1, #4
    //     0x668a3c: b.ge            #0x668a40
    // 0x668a40: CheckStackOverflow
    //     0x668a40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x668a44: cmp             SP, x16
    //     0x668a48: b.ls            #0x668a90
    // 0x668a4c: LoadField: r2 = r5->field_7
    //     0x668a4c: ldur            w2, [x5, #7]
    // 0x668a50: DecompressPointer r2
    //     0x668a50: add             x2, x2, HEAP, lsl #32
    // 0x668a54: mov             x0, x3
    // 0x668a58: r1 = Null
    //     0x668a58: mov             x1, NULL
    // 0x668a5c: r8 = Iterable<X0>
    //     0x668a5c: ldr             x8, [PP, #0x6e0]  ; [pp+0x6e0] Type: Iterable<X0>
    // 0x668a60: LoadField: r9 = r8->field_7
    //     0x668a60: ldur            x9, [x8, #7]
    // 0x668a64: r3 = Null
    //     0x668a64: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c978] Null
    //     0x668a68: ldr             x3, [x3, #0x978]
    // 0x668a6c: blr             x9
    // 0x668a70: ldur            x1, [fp, #-8]
    // 0x668a74: ldur            x2, [fp, #-0x10]
    // 0x668a78: r3 = Null
    //     0x668a78: mov             x3, NULL
    // 0x668a7c: r0 = _addAll()
    //     0x668a7c: bl              #0x668a98  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_addAll
    // 0x668a80: r0 = Null
    //     0x668a80: mov             x0, NULL
    // 0x668a84: LeaveFrame
    //     0x668a84: mov             SP, fp
    //     0x668a88: ldp             fp, lr, [SP], #0x10
    // 0x668a8c: ret
    //     0x668a8c: ret             
    // 0x668a90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x668a90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x668a94: b               #0x668a4c
  }
  _ _addAll(/* No info */) {
    // ** addr: 0x668a98, size: 0x248
    // 0x668a98: EnterFrame
    //     0x668a98: stp             fp, lr, [SP, #-0x10]!
    //     0x668a9c: mov             fp, SP
    // 0x668aa0: AllocStack(0x20)
    //     0x668aa0: sub             SP, SP, #0x20
    // 0x668aa4: SetupParameters(TypedDataBuffer<X0> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */)
    //     0x668aa4: mov             x5, x1
    //     0x668aa8: mov             x4, x2
    //     0x668aac: stur            x1, [fp, #-8]
    //     0x668ab0: stur            x2, [fp, #-0x10]
    // 0x668ab4: CheckStackOverflow
    //     0x668ab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x668ab8: cmp             SP, x16
    //     0x668abc: b.ls            #0x668cd0
    // 0x668ac0: mov             x0, x4
    // 0x668ac4: r2 = Null
    //     0x668ac4: mov             x2, NULL
    // 0x668ac8: r1 = Null
    //     0x668ac8: mov             x1, NULL
    // 0x668acc: cmp             w0, NULL
    // 0x668ad0: b.eq            #0x668b74
    // 0x668ad4: branchIfSmi(r0, 0x668b74)
    //     0x668ad4: tbz             w0, #0, #0x668b74
    // 0x668ad8: r3 = LoadClassIdInstr(r0)
    //     0x668ad8: ldur            x3, [x0, #-1]
    //     0x668adc: ubfx            x3, x3, #0xc, #0x14
    // 0x668ae0: r17 = 6718
    //     0x668ae0: movz            x17, #0x1a3e
    // 0x668ae4: cmp             x3, x17
    // 0x668ae8: b.eq            #0x668b7c
    // 0x668aec: sub             x3, x3, #0x5a
    // 0x668af0: cmp             x3, #2
    // 0x668af4: b.ls            #0x668b7c
    // 0x668af8: r4 = LoadClassIdInstr(r0)
    //     0x668af8: ldur            x4, [x0, #-1]
    //     0x668afc: ubfx            x4, x4, #0xc, #0x14
    // 0x668b00: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x668b04: ldr             x3, [x3, #0x18]
    // 0x668b08: ldr             x3, [x3, x4, lsl #3]
    // 0x668b0c: LoadField: r3 = r3->field_2b
    //     0x668b0c: ldur            w3, [x3, #0x2b]
    // 0x668b10: DecompressPointer r3
    //     0x668b10: add             x3, x3, HEAP, lsl #32
    // 0x668b14: cmp             w3, NULL
    // 0x668b18: b.eq            #0x668b74
    // 0x668b1c: LoadField: r3 = r3->field_f
    //     0x668b1c: ldur            w3, [x3, #0xf]
    // 0x668b20: lsr             x3, x3, #3
    // 0x668b24: r17 = 6718
    //     0x668b24: movz            x17, #0x1a3e
    // 0x668b28: cmp             x3, x17
    // 0x668b2c: b.eq            #0x668b7c
    // 0x668b30: r3 = SubtypeTestCache
    //     0x668b30: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c988] SubtypeTestCache
    //     0x668b34: ldr             x3, [x3, #0x988]
    // 0x668b38: r30 = Subtype1TestCacheStub
    //     0x668b38: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x668b3c: LoadField: r30 = r30->field_7
    //     0x668b3c: ldur            lr, [lr, #7]
    // 0x668b40: blr             lr
    // 0x668b44: cmp             w7, NULL
    // 0x668b48: b.eq            #0x668b54
    // 0x668b4c: tbnz            w7, #4, #0x668b74
    // 0x668b50: b               #0x668b7c
    // 0x668b54: r8 = List
    //     0x668b54: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c990] Type: List
    //     0x668b58: ldr             x8, [x8, #0x990]
    // 0x668b5c: r3 = SubtypeTestCache
    //     0x668b5c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c998] SubtypeTestCache
    //     0x668b60: ldr             x3, [x3, #0x998]
    // 0x668b64: r30 = InstanceOfStub
    //     0x668b64: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x668b68: LoadField: r30 = r30->field_7
    //     0x668b68: ldur            lr, [lr, #7]
    // 0x668b6c: blr             lr
    // 0x668b70: b               #0x668b80
    // 0x668b74: r0 = false
    //     0x668b74: add             x0, NULL, #0x30  ; false
    // 0x668b78: b               #0x668b80
    // 0x668b7c: r0 = true
    //     0x668b7c: add             x0, NULL, #0x20  ; true
    // 0x668b80: tbnz            w0, #4, #0x668ba8
    // 0x668b84: ldur            x3, [fp, #-0x10]
    // 0x668b88: r0 = LoadClassIdInstr(r3)
    //     0x668b88: ldur            x0, [x3, #-1]
    //     0x668b8c: ubfx            x0, x0, #0xc, #0x14
    // 0x668b90: str             x3, [SP]
    // 0x668b94: r0 = GDT[cid_x0 + 0xc834]()
    //     0x668b94: movz            x17, #0xc834
    //     0x668b98: add             lr, x0, x17
    //     0x668b9c: ldr             lr, [x21, lr, lsl #3]
    //     0x668ba0: blr             lr
    // 0x668ba4: b               #0x668bac
    // 0x668ba8: r0 = Null
    //     0x668ba8: mov             x0, NULL
    // 0x668bac: cmp             w0, NULL
    // 0x668bb0: b.eq            #0x668bec
    // 0x668bb4: ldur            x2, [fp, #-8]
    // 0x668bb8: LoadField: r1 = r2->field_f
    //     0x668bb8: ldur            x1, [x2, #0xf]
    // 0x668bbc: r5 = LoadInt32Instr(r0)
    //     0x668bbc: sbfx            x5, x0, #1, #0x1f
    //     0x668bc0: tbz             w0, #0, #0x668bc8
    //     0x668bc4: ldur            x5, [x0, #7]
    // 0x668bc8: mov             x16, x1
    // 0x668bcc: mov             x1, x2
    // 0x668bd0: mov             x2, x16
    // 0x668bd4: ldur            x3, [fp, #-0x10]
    // 0x668bd8: r0 = _insertKnownLength()
    //     0x668bd8: bl              #0x6690c8  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_insertKnownLength
    // 0x668bdc: r0 = Null
    //     0x668bdc: mov             x0, NULL
    // 0x668be0: LeaveFrame
    //     0x668be0: mov             SP, fp
    //     0x668be4: ldp             fp, lr, [SP], #0x10
    // 0x668be8: ret
    //     0x668be8: ret             
    // 0x668bec: ldur            x2, [fp, #-8]
    // 0x668bf0: ldur            x1, [fp, #-0x10]
    // 0x668bf4: r0 = LoadClassIdInstr(r1)
    //     0x668bf4: ldur            x0, [x1, #-1]
    //     0x668bf8: ubfx            x0, x0, #0xc, #0x14
    // 0x668bfc: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x668bfc: movz            x17, #0xd35d
    //     0x668c00: add             lr, x0, x17
    //     0x668c04: ldr             lr, [x21, lr, lsl #3]
    //     0x668c08: blr             lr
    // 0x668c0c: mov             x2, x0
    // 0x668c10: stur            x2, [fp, #-0x10]
    // 0x668c14: r3 = 0
    //     0x668c14: movz            x3, #0
    // 0x668c18: stur            x3, [fp, #-0x18]
    // 0x668c1c: CheckStackOverflow
    //     0x668c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x668c20: cmp             SP, x16
    //     0x668c24: b.ls            #0x668cd8
    // 0x668c28: r0 = LoadClassIdInstr(r2)
    //     0x668c28: ldur            x0, [x2, #-1]
    //     0x668c2c: ubfx            x0, x0, #0xc, #0x14
    // 0x668c30: mov             x1, x2
    // 0x668c34: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x668c34: movz            x17, #0x292d
    //     0x668c38: movk            x17, #0x1, lsl #16
    //     0x668c3c: add             lr, x0, x17
    //     0x668c40: ldr             lr, [x21, lr, lsl #3]
    //     0x668c44: blr             lr
    // 0x668c48: tbnz            w0, #4, #0x668c9c
    // 0x668c4c: ldur            x2, [fp, #-0x10]
    // 0x668c50: ldur            x3, [fp, #-0x18]
    // 0x668c54: r0 = LoadClassIdInstr(r2)
    //     0x668c54: ldur            x0, [x2, #-1]
    //     0x668c58: ubfx            x0, x0, #0xc, #0x14
    // 0x668c5c: mov             x1, x2
    // 0x668c60: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x668c60: movz            x17, #0x384d
    //     0x668c64: movk            x17, #0x1, lsl #16
    //     0x668c68: add             lr, x0, x17
    //     0x668c6c: ldr             lr, [x21, lr, lsl #3]
    //     0x668c70: blr             lr
    // 0x668c74: mov             x1, x0
    // 0x668c78: ldur            x0, [fp, #-0x18]
    // 0x668c7c: tbnz            x0, #0x3f, #0x668c8c
    // 0x668c80: mov             x2, x1
    // 0x668c84: ldur            x1, [fp, #-8]
    // 0x668c88: r0 = _add()
    //     0x668c88: bl              #0x668d24  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_add
    // 0x668c8c: ldur            x0, [fp, #-0x18]
    // 0x668c90: add             x3, x0, #1
    // 0x668c94: ldur            x2, [fp, #-0x10]
    // 0x668c98: b               #0x668c18
    // 0x668c9c: ldur            x0, [fp, #-0x18]
    // 0x668ca0: tbnz            x0, #0x3f, #0x668cb4
    // 0x668ca4: r0 = Null
    //     0x668ca4: mov             x0, NULL
    // 0x668ca8: LeaveFrame
    //     0x668ca8: mov             SP, fp
    //     0x668cac: ldp             fp, lr, [SP], #0x10
    // 0x668cb0: ret
    //     0x668cb0: ret             
    // 0x668cb4: r0 = StateError()
    //     0x668cb4: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x668cb8: mov             x1, x0
    // 0x668cbc: r0 = "Too few elements"
    //     0x668cbc: ldr             x0, [PP, #0xe58]  ; [pp+0xe58] "Too few elements"
    // 0x668cc0: StoreField: r1->field_b = r0
    //     0x668cc0: stur            w0, [x1, #0xb]
    // 0x668cc4: mov             x0, x1
    // 0x668cc8: r0 = Throw()
    //     0x668cc8: bl              #0xec04b8  ; ThrowStub
    // 0x668ccc: brk             #0
    // 0x668cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x668cd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x668cd4: b               #0x668ac0
    // 0x668cd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x668cd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x668cdc: b               #0x668c28
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x668ce0, size: 0x44
    // 0x668ce0: EnterFrame
    //     0x668ce0: stp             fp, lr, [SP, #-0x10]!
    //     0x668ce4: mov             fp, SP
    // 0x668ce8: AllocStack(0x10)
    //     0x668ce8: sub             SP, SP, #0x10
    // 0x668cec: SetupParameters()
    //     0x668cec: ldr             x0, [fp, #0x18]
    //     0x668cf0: ldur            w1, [x0, #0x17]
    //     0x668cf4: add             x1, x1, HEAP, lsl #32
    // 0x668cf8: CheckStackOverflow
    //     0x668cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x668cfc: cmp             SP, x16
    //     0x668d00: b.ls            #0x668d1c
    // 0x668d04: ldr             x16, [fp, #0x10]
    // 0x668d08: stp             x16, x1, [SP]
    // 0x668d0c: r0 = add()
    //     0x668d0c: bl              #0x66c234  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::add
    // 0x668d10: LeaveFrame
    //     0x668d10: mov             SP, fp
    //     0x668d14: ldp             fp, lr, [SP], #0x10
    // 0x668d18: ret
    //     0x668d18: ret             
    // 0x668d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x668d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x668d20: b               #0x668d04
  }
  _ _add(/* No info */) {
    // ** addr: 0x668d24, size: 0xf4
    // 0x668d24: EnterFrame
    //     0x668d24: stp             fp, lr, [SP, #-0x10]!
    //     0x668d28: mov             fp, SP
    // 0x668d2c: AllocStack(0x20)
    //     0x668d2c: sub             SP, SP, #0x20
    // 0x668d30: SetupParameters(TypedDataBuffer<X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x668d30: mov             x3, x1
    //     0x668d34: mov             x0, x2
    //     0x668d38: stur            x1, [fp, #-8]
    //     0x668d3c: stur            x2, [fp, #-0x10]
    // 0x668d40: CheckStackOverflow
    //     0x668d40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x668d44: cmp             SP, x16
    //     0x668d48: b.ls            #0x668e0c
    // 0x668d4c: LoadField: r2 = r3->field_f
    //     0x668d4c: ldur            x2, [x3, #0xf]
    // 0x668d50: LoadField: r1 = r3->field_b
    //     0x668d50: ldur            w1, [x3, #0xb]
    // 0x668d54: DecompressPointer r1
    //     0x668d54: add             x1, x1, HEAP, lsl #32
    // 0x668d58: LoadField: r4 = r1->field_13
    //     0x668d58: ldur            w4, [x1, #0x13]
    // 0x668d5c: r1 = LoadInt32Instr(r4)
    //     0x668d5c: sbfx            x1, x4, #1, #0x1f
    // 0x668d60: cmp             x2, x1
    // 0x668d64: b.ne            #0x668d70
    // 0x668d68: mov             x1, x3
    // 0x668d6c: r0 = _grow()
    //     0x668d6c: bl              #0x668e18  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_grow
    // 0x668d70: ldur            x0, [fp, #-8]
    // 0x668d74: ldur            x3, [fp, #-0x10]
    // 0x668d78: LoadField: r4 = r0->field_b
    //     0x668d78: ldur            w4, [x0, #0xb]
    // 0x668d7c: DecompressPointer r4
    //     0x668d7c: add             x4, x4, HEAP, lsl #32
    // 0x668d80: stur            x4, [fp, #-0x20]
    // 0x668d84: LoadField: r5 = r0->field_f
    //     0x668d84: ldur            x5, [x0, #0xf]
    // 0x668d88: stur            x5, [fp, #-0x18]
    // 0x668d8c: add             x1, x5, #1
    // 0x668d90: StoreField: r0->field_f = r1
    //     0x668d90: stur            x1, [x0, #0xf]
    // 0x668d94: r3 as int
    //     0x668d94: mov             x0, x3
    //     0x668d98: mov             x2, NULL
    //     0x668d9c: mov             x1, NULL
    //     0x668da0: tbz             w0, #0, #0x668dc8
    //     0x668da4: ldur            x4, [x0, #-1]
    //     0x668da8: ubfx            x4, x4, #0xc, #0x14
    //     0x668dac: sub             x4, x4, #0x3c
    //     0x668db0: cmp             x4, #1
    //     0x668db4: b.ls            #0x668dc8
    //     0x668db8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    //     0x668dbc: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c828] Null
    //     0x668dc0: ldr             x3, [x3, #0x828]
    //     0x668dc4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x668dc8: ldur            x2, [fp, #-0x20]
    // 0x668dcc: LoadField: r3 = r2->field_13
    //     0x668dcc: ldur            w3, [x2, #0x13]
    // 0x668dd0: r0 = LoadInt32Instr(r3)
    //     0x668dd0: sbfx            x0, x3, #1, #0x1f
    // 0x668dd4: ldur            x1, [fp, #-0x18]
    // 0x668dd8: cmp             x1, x0
    // 0x668ddc: b.hs            #0x668e14
    // 0x668de0: ldur            x1, [fp, #-0x10]
    // 0x668de4: r3 = LoadInt32Instr(r1)
    //     0x668de4: sbfx            x3, x1, #1, #0x1f
    //     0x668de8: tbz             w1, #0, #0x668df0
    //     0x668dec: ldur            x3, [x1, #7]
    // 0x668df0: ldur            x1, [fp, #-0x18]
    // 0x668df4: ArrayStore: r2[r1] = r3  ; TypeUnknown_1
    //     0x668df4: add             x4, x2, x1
    //     0x668df8: strb            w3, [x4, #0x17]
    // 0x668dfc: r0 = Null
    //     0x668dfc: mov             x0, NULL
    // 0x668e00: LeaveFrame
    //     0x668e00: mov             SP, fp
    //     0x668e04: ldp             fp, lr, [SP], #0x10
    // 0x668e08: ret
    //     0x668e08: ret             
    // 0x668e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x668e0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x668e10: b               #0x668d4c
    // 0x668e14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x668e14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _grow(/* No info */) {
    // ** addr: 0x668e18, size: 0x230
    // 0x668e18: EnterFrame
    //     0x668e18: stp             fp, lr, [SP, #-0x10]!
    //     0x668e1c: mov             fp, SP
    // 0x668e20: AllocStack(0x20)
    //     0x668e20: sub             SP, SP, #0x20
    // 0x668e24: SetupParameters(TypedDataBuffer<X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x668e24: mov             x3, x1
    //     0x668e28: mov             x0, x2
    //     0x668e2c: stur            x1, [fp, #-8]
    //     0x668e30: stur            x2, [fp, #-0x10]
    // 0x668e34: CheckStackOverflow
    //     0x668e34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x668e38: cmp             SP, x16
    //     0x668e3c: b.ls            #0x669040
    // 0x668e40: mov             x1, x3
    // 0x668e44: r2 = Null
    //     0x668e44: mov             x2, NULL
    // 0x668e48: r0 = _createBiggerBuffer()
    //     0x668e48: bl              #0x669048  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_createBiggerBuffer
    // 0x668e4c: mov             x5, x0
    // 0x668e50: ldur            x4, [fp, #-8]
    // 0x668e54: stur            x5, [fp, #-0x20]
    // 0x668e58: LoadField: r6 = r4->field_b
    //     0x668e58: ldur            w6, [x4, #0xb]
    // 0x668e5c: DecompressPointer r6
    //     0x668e5c: add             x6, x6, HEAP, lsl #32
    // 0x668e60: ldur            x7, [fp, #-0x10]
    // 0x668e64: stur            x6, [fp, #-0x18]
    // 0x668e68: tbnz            x7, #0x3f, #0x668e7c
    // 0x668e6c: LoadField: r0 = r5->field_13
    //     0x668e6c: ldur            w0, [x5, #0x13]
    // 0x668e70: r1 = LoadInt32Instr(r0)
    //     0x668e70: sbfx            x1, x0, #1, #0x1f
    // 0x668e74: cmp             x7, x1
    // 0x668e78: b.le            #0x668ea8
    // 0x668e7c: LoadField: r2 = r5->field_13
    //     0x668e7c: ldur            w2, [x5, #0x13]
    // 0x668e80: r0 = BoxInt64Instr(r7)
    //     0x668e80: sbfiz           x0, x7, #1, #0x1f
    //     0x668e84: cmp             x7, x0, asr #1
    //     0x668e88: b.eq            #0x668e94
    //     0x668e8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x668e90: stur            x7, [x0, #7]
    // 0x668e94: r3 = LoadInt32Instr(r2)
    //     0x668e94: sbfx            x3, x2, #1, #0x1f
    // 0x668e98: mov             x2, x0
    // 0x668e9c: r1 = 0
    //     0x668e9c: movz            x1, #0
    // 0x668ea0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x668ea0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x668ea4: r0 = checkValidRange()
    //     0x668ea4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x668ea8: ldur            x3, [fp, #-0x10]
    // 0x668eac: ldur            x2, [fp, #-0x18]
    // 0x668eb0: LoadField: r0 = r2->field_13
    //     0x668eb0: ldur            w0, [x2, #0x13]
    // 0x668eb4: r1 = LoadInt32Instr(r0)
    //     0x668eb4: sbfx            x1, x0, #1, #0x1f
    // 0x668eb8: cmp             x1, x3
    // 0x668ebc: b.lt            #0x669034
    // 0x668ec0: cbnz            x3, #0x668ecc
    // 0x668ec4: ldur            x20, [fp, #-0x20]
    // 0x668ec8: b               #0x669000
    // 0x668ecc: r0 = BoxInt64Instr(r3)
    //     0x668ecc: sbfiz           x0, x3, #1, #0x1f
    //     0x668ed0: cmp             x3, x0, asr #1
    //     0x668ed4: b.eq            #0x668ee0
    //     0x668ed8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x668edc: stur            x3, [x0, #7]
    // 0x668ee0: cmp             w0, #0x800
    // 0x668ee4: b.ge            #0x668fb4
    // 0x668ee8: ldur            x20, [fp, #-0x20]
    // 0x668eec: mov             x3, x0
    // 0x668ef0: add             x1, x2, #0x17
    // 0x668ef4: add             x0, x20, #0x17
    // 0x668ef8: cbz             x3, #0x668fb0
    // 0x668efc: cmp             x0, x1
    // 0x668f00: b.ls            #0x668f68
    // 0x668f04: sxtw            x3, w3
    // 0x668f08: add             x16, x1, x3, asr #1
    // 0x668f0c: cmp             x0, x16
    // 0x668f10: b.hs            #0x668f68
    // 0x668f14: mov             x1, x16
    // 0x668f18: add             x0, x0, x3, asr #1
    // 0x668f1c: tbz             w3, #4, #0x668f28
    // 0x668f20: ldr             x16, [x1, #-8]!
    // 0x668f24: str             x16, [x0, #-8]!
    // 0x668f28: tbz             w3, #3, #0x668f34
    // 0x668f2c: ldr             w16, [x1, #-4]!
    // 0x668f30: str             w16, [x0, #-4]!
    // 0x668f34: tbz             w3, #2, #0x668f40
    // 0x668f38: ldrh            w16, [x1, #-2]!
    // 0x668f3c: strh            w16, [x0, #-2]!
    // 0x668f40: tbz             w3, #1, #0x668f4c
    // 0x668f44: ldrb            w16, [x1, #-1]!
    // 0x668f48: strb            w16, [x0, #-1]!
    // 0x668f4c: ands            w3, w3, #0xffffffe1
    // 0x668f50: b.eq            #0x668fb0
    // 0x668f54: ldp             x16, x17, [x1, #-0x10]!
    // 0x668f58: stp             x16, x17, [x0, #-0x10]!
    // 0x668f5c: subs            w3, w3, #0x20
    // 0x668f60: b.ne            #0x668f54
    // 0x668f64: b               #0x668fb0
    // 0x668f68: tbz             w3, #4, #0x668f74
    // 0x668f6c: ldr             x16, [x1], #8
    // 0x668f70: str             x16, [x0], #8
    // 0x668f74: tbz             w3, #3, #0x668f80
    // 0x668f78: ldr             w16, [x1], #4
    // 0x668f7c: str             w16, [x0], #4
    // 0x668f80: tbz             w3, #2, #0x668f8c
    // 0x668f84: ldrh            w16, [x1], #2
    // 0x668f88: strh            w16, [x0], #2
    // 0x668f8c: tbz             w3, #1, #0x668f98
    // 0x668f90: ldrb            w16, [x1], #1
    // 0x668f94: strb            w16, [x0], #1
    // 0x668f98: ands            w3, w3, #0xffffffe1
    // 0x668f9c: b.eq            #0x668fb0
    // 0x668fa0: ldp             x16, x17, [x1], #0x10
    // 0x668fa4: stp             x16, x17, [x0], #0x10
    // 0x668fa8: subs            w3, w3, #0x20
    // 0x668fac: b.ne            #0x668fa0
    // 0x668fb0: b               #0x669000
    // 0x668fb4: ldur            x20, [fp, #-0x20]
    // 0x668fb8: LoadField: r0 = r20->field_7
    //     0x668fb8: ldur            x0, [x20, #7]
    // 0x668fbc: LoadField: r1 = r2->field_7
    //     0x668fbc: ldur            x1, [x2, #7]
    // 0x668fc0: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x668fc0: mov             x2, THR
    //     0x668fc4: ldr             x9, [x2, #0x658]
    //     0x668fc8: mov             x2, x3
    //     0x668fcc: mov             x17, fp
    //     0x668fd0: str             fp, [SP, #-8]!
    //     0x668fd4: mov             fp, SP
    //     0x668fd8: and             SP, SP, #0xfffffffffffffff0
    //     0x668fdc: mov             x19, sp
    //     0x668fe0: mov             sp, SP
    //     0x668fe4: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x668fe8: blr             x9
    //     0x668fec: movz            x16, #0x8
    //     0x668ff0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x668ff4: mov             sp, x19
    //     0x668ff8: mov             SP, fp
    //     0x668ffc: ldr             fp, [SP], #8
    // 0x669000: ldur            x1, [fp, #-8]
    // 0x669004: mov             x0, x20
    // 0x669008: StoreField: r1->field_b = r0
    //     0x669008: stur            w0, [x1, #0xb]
    //     0x66900c: ldurb           w16, [x1, #-1]
    //     0x669010: ldurb           w17, [x0, #-1]
    //     0x669014: and             x16, x17, x16, lsr #2
    //     0x669018: tst             x16, HEAP, lsr #32
    //     0x66901c: b.eq            #0x669024
    //     0x669020: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x669024: r0 = Null
    //     0x669024: mov             x0, NULL
    // 0x669028: LeaveFrame
    //     0x669028: mov             SP, fp
    //     0x66902c: ldp             fp, lr, [SP], #0x10
    // 0x669030: ret
    //     0x669030: ret             
    // 0x669034: r0 = tooFew()
    //     0x669034: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x669038: r0 = Throw()
    //     0x669038: bl              #0xec04b8  ; ThrowStub
    // 0x66903c: brk             #0
    // 0x669040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x669040: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x669044: b               #0x668e40
  }
  _ _createBiggerBuffer(/* No info */) {
    // ** addr: 0x669048, size: 0x80
    // 0x669048: EnterFrame
    //     0x669048: stp             fp, lr, [SP, #-0x10]!
    //     0x66904c: mov             fp, SP
    // 0x669050: LoadField: r0 = r1->field_b
    //     0x669050: ldur            w0, [x1, #0xb]
    // 0x669054: DecompressPointer r0
    //     0x669054: add             x0, x0, HEAP, lsl #32
    // 0x669058: LoadField: r1 = r0->field_13
    //     0x669058: ldur            w1, [x0, #0x13]
    // 0x66905c: r0 = LoadInt32Instr(r1)
    //     0x66905c: sbfx            x0, x1, #1, #0x1f
    // 0x669060: lsl             x1, x0, #1
    // 0x669064: cmp             w2, NULL
    // 0x669068: b.eq            #0x669088
    // 0x66906c: r0 = LoadInt32Instr(r2)
    //     0x66906c: sbfx            x0, x2, #1, #0x1f
    //     0x669070: tbz             w2, #0, #0x669078
    //     0x669074: ldur            x0, [x2, #7]
    // 0x669078: cmp             x1, x0
    // 0x66907c: b.ge            #0x669088
    // 0x669080: mov             x2, x0
    // 0x669084: b               #0x6690a0
    // 0x669088: cmp             x1, #8
    // 0x66908c: b.ge            #0x669098
    // 0x669090: r0 = 8
    //     0x669090: movz            x0, #0x8
    // 0x669094: b               #0x66909c
    // 0x669098: mov             x0, x1
    // 0x66909c: mov             x2, x0
    // 0x6690a0: r0 = BoxInt64Instr(r2)
    //     0x6690a0: sbfiz           x0, x2, #1, #0x1f
    //     0x6690a4: cmp             x2, x0, asr #1
    //     0x6690a8: b.eq            #0x6690b4
    //     0x6690ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6690b0: stur            x2, [x0, #7]
    // 0x6690b4: mov             x4, x0
    // 0x6690b8: r0 = AllocateUint8Array()
    //     0x6690b8: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x6690bc: LeaveFrame
    //     0x6690bc: mov             SP, fp
    //     0x6690c0: ldp             fp, lr, [SP], #0x10
    // 0x6690c4: ret
    //     0x6690c4: ret             
  }
  _ _insertKnownLength(/* No info */) {
    // ** addr: 0x6690c8, size: 0x72c
    // 0x6690c8: EnterFrame
    //     0x6690c8: stp             fp, lr, [SP, #-0x10]!
    //     0x6690cc: mov             fp, SP
    // 0x6690d0: AllocStack(0x70)
    //     0x6690d0: sub             SP, SP, #0x70
    // 0x6690d4: SetupParameters(TypedDataBuffer<X0> this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x6690d4: mov             x6, x1
    //     0x6690d8: mov             x4, x2
    //     0x6690dc: stur            x1, [fp, #-8]
    //     0x6690e0: stur            x2, [fp, #-0x10]
    //     0x6690e4: stur            x3, [fp, #-0x18]
    //     0x6690e8: stur            x5, [fp, #-0x20]
    // 0x6690ec: CheckStackOverflow
    //     0x6690ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6690f0: cmp             SP, x16
    //     0x6690f4: b.ls            #0x6697ec
    // 0x6690f8: mov             x0, x3
    // 0x6690fc: r2 = Null
    //     0x6690fc: mov             x2, NULL
    // 0x669100: r1 = Null
    //     0x669100: mov             x1, NULL
    // 0x669104: cmp             w0, NULL
    // 0x669108: b.eq            #0x6691ac
    // 0x66910c: branchIfSmi(r0, 0x6691ac)
    //     0x66910c: tbz             w0, #0, #0x6691ac
    // 0x669110: r3 = LoadClassIdInstr(r0)
    //     0x669110: ldur            x3, [x0, #-1]
    //     0x669114: ubfx            x3, x3, #0xc, #0x14
    // 0x669118: r17 = 6718
    //     0x669118: movz            x17, #0x1a3e
    // 0x66911c: cmp             x3, x17
    // 0x669120: b.eq            #0x6691b4
    // 0x669124: sub             x3, x3, #0x5a
    // 0x669128: cmp             x3, #2
    // 0x66912c: b.ls            #0x6691b4
    // 0x669130: r4 = LoadClassIdInstr(r0)
    //     0x669130: ldur            x4, [x0, #-1]
    //     0x669134: ubfx            x4, x4, #0xc, #0x14
    // 0x669138: ldr             x3, [THR, #0x760]  ; THR::isolate_group
    // 0x66913c: ldr             x3, [x3, #0x18]
    // 0x669140: ldr             x3, [x3, x4, lsl #3]
    // 0x669144: LoadField: r3 = r3->field_2b
    //     0x669144: ldur            w3, [x3, #0x2b]
    // 0x669148: DecompressPointer r3
    //     0x669148: add             x3, x3, HEAP, lsl #32
    // 0x66914c: cmp             w3, NULL
    // 0x669150: b.eq            #0x6691ac
    // 0x669154: LoadField: r3 = r3->field_f
    //     0x669154: ldur            w3, [x3, #0xf]
    // 0x669158: lsr             x3, x3, #3
    // 0x66915c: r17 = 6718
    //     0x66915c: movz            x17, #0x1a3e
    // 0x669160: cmp             x3, x17
    // 0x669164: b.eq            #0x6691b4
    // 0x669168: r3 = SubtypeTestCache
    //     0x669168: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c9a0] SubtypeTestCache
    //     0x66916c: ldr             x3, [x3, #0x9a0]
    // 0x669170: r30 = Subtype1TestCacheStub
    //     0x669170: ldr             lr, [PP, #0x800]  ; [pp+0x800] Stub: Subtype1TestCache (0x5f2fdc)
    // 0x669174: LoadField: r30 = r30->field_7
    //     0x669174: ldur            lr, [lr, #7]
    // 0x669178: blr             lr
    // 0x66917c: cmp             w7, NULL
    // 0x669180: b.eq            #0x66918c
    // 0x669184: tbnz            w7, #4, #0x6691ac
    // 0x669188: b               #0x6691b4
    // 0x66918c: r8 = List
    //     0x66918c: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c9a8] Type: List
    //     0x669190: ldr             x8, [x8, #0x9a8]
    // 0x669194: r3 = SubtypeTestCache
    //     0x669194: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c9b0] SubtypeTestCache
    //     0x669198: ldr             x3, [x3, #0x9b0]
    // 0x66919c: r30 = InstanceOfStub
    //     0x66919c: ldr             lr, [PP, #0x710]  ; [pp+0x710] Stub: InstanceOf (0x5e1240)
    // 0x6691a0: LoadField: r30 = r30->field_7
    //     0x6691a0: ldur            lr, [lr, #7]
    // 0x6691a4: blr             lr
    // 0x6691a8: b               #0x6691b8
    // 0x6691ac: r0 = false
    //     0x6691ac: add             x0, NULL, #0x30  ; false
    // 0x6691b0: b               #0x6691b8
    // 0x6691b4: r0 = true
    //     0x6691b4: add             x0, NULL, #0x20  ; true
    // 0x6691b8: tbnz            w0, #4, #0x66922c
    // 0x6691bc: ldur            x1, [fp, #-0x18]
    // 0x6691c0: r0 = LoadClassIdInstr(r1)
    //     0x6691c0: ldur            x0, [x1, #-1]
    //     0x6691c4: ubfx            x0, x0, #0xc, #0x14
    // 0x6691c8: str             x1, [SP]
    // 0x6691cc: r0 = GDT[cid_x0 + 0xc834]()
    //     0x6691cc: movz            x17, #0xc834
    //     0x6691d0: add             lr, x0, x17
    //     0x6691d4: ldr             lr, [x21, lr, lsl #3]
    //     0x6691d8: blr             lr
    // 0x6691dc: r1 = LoadInt32Instr(r0)
    //     0x6691dc: sbfx            x1, x0, #1, #0x1f
    //     0x6691e0: tbz             w0, #0, #0x6691e8
    //     0x6691e4: ldur            x1, [x0, #7]
    // 0x6691e8: tbnz            x1, #0x3f, #0x66976c
    // 0x6691ec: ldur            x1, [fp, #-0x18]
    // 0x6691f0: ldur            x2, [fp, #-0x20]
    // 0x6691f4: r0 = LoadClassIdInstr(r1)
    //     0x6691f4: ldur            x0, [x1, #-1]
    //     0x6691f8: ubfx            x0, x0, #0xc, #0x14
    // 0x6691fc: str             x1, [SP]
    // 0x669200: r0 = GDT[cid_x0 + 0xc834]()
    //     0x669200: movz            x17, #0xc834
    //     0x669204: add             lr, x0, x17
    //     0x669208: ldr             lr, [x21, lr, lsl #3]
    //     0x66920c: blr             lr
    // 0x669210: r1 = LoadInt32Instr(r0)
    //     0x669210: sbfx            x1, x0, #1, #0x1f
    //     0x669214: tbz             w0, #0, #0x66921c
    //     0x669218: ldur            x1, [x0, #7]
    // 0x66921c: ldur            x0, [fp, #-0x20]
    // 0x669220: cmp             x0, x1
    // 0x669224: b.le            #0x669230
    // 0x669228: b               #0x66976c
    // 0x66922c: ldur            x0, [fp, #-0x20]
    // 0x669230: ldur            x4, [fp, #-8]
    // 0x669234: ldur            x3, [fp, #-0x10]
    // 0x669238: LoadField: r1 = r4->field_f
    //     0x669238: ldur            x1, [x4, #0xf]
    // 0x66923c: add             x5, x1, x0
    // 0x669240: mov             x1, x4
    // 0x669244: mov             x2, x5
    // 0x669248: stur            x5, [fp, #-0x28]
    // 0x66924c: r0 = _ensureCapacity()
    //     0x66924c: bl              #0x6697f4  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_ensureCapacity
    // 0x669250: ldur            x4, [fp, #-8]
    // 0x669254: LoadField: r5 = r4->field_b
    //     0x669254: ldur            w5, [x4, #0xb]
    // 0x669258: DecompressPointer r5
    //     0x669258: add             x5, x5, HEAP, lsl #32
    // 0x66925c: ldur            x6, [fp, #-0x10]
    // 0x669260: ldur            x0, [fp, #-0x20]
    // 0x669264: stur            x5, [fp, #-0x48]
    // 0x669268: add             x7, x6, x0
    // 0x66926c: stur            x7, [fp, #-0x40]
    // 0x669270: LoadField: r1 = r4->field_f
    //     0x669270: ldur            x1, [x4, #0xf]
    // 0x669274: add             x8, x1, x0
    // 0x669278: stur            x8, [fp, #-0x38]
    // 0x66927c: tbnz            x7, #0x3f, #0x669298
    // 0x669280: cmp             x7, x8
    // 0x669284: b.gt            #0x669298
    // 0x669288: LoadField: r0 = r5->field_13
    //     0x669288: ldur            w0, [x5, #0x13]
    // 0x66928c: r1 = LoadInt32Instr(r0)
    //     0x66928c: sbfx            x1, x0, #1, #0x1f
    // 0x669290: cmp             x8, x1
    // 0x669294: b.le            #0x6692d8
    // 0x669298: LoadField: r9 = r5->field_13
    //     0x669298: ldur            w9, [x5, #0x13]
    // 0x66929c: stur            x9, [fp, #-0x30]
    // 0x6692a0: r0 = BoxInt64Instr(r8)
    //     0x6692a0: sbfiz           x0, x8, #1, #0x1f
    //     0x6692a4: cmp             x8, x0, asr #1
    //     0x6692a8: b.eq            #0x6692b4
    //     0x6692ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6692b0: stur            x8, [x0, #7]
    // 0x6692b4: r3 = LoadInt32Instr(r9)
    //     0x6692b4: sbfx            x3, x9, #1, #0x1f
    // 0x6692b8: mov             x1, x7
    // 0x6692bc: mov             x2, x0
    // 0x6692c0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6692c0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x6692c4: r0 = checkValidRange()
    //     0x6692c4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x6692c8: ldur            x0, [fp, #-0x30]
    // 0x6692cc: r1 = LoadInt32Instr(r0)
    //     0x6692cc: sbfx            x1, x0, #1, #0x1f
    // 0x6692d0: mov             x0, x1
    // 0x6692d4: b               #0x6692e0
    // 0x6692d8: r1 = LoadInt32Instr(r0)
    //     0x6692d8: sbfx            x1, x0, #1, #0x1f
    // 0x6692dc: mov             x0, x1
    // 0x6692e0: ldur            x20, [fp, #-0x10]
    // 0x6692e4: tbnz            x20, #0x3f, #0x669788
    // 0x6692e8: ldur            x23, [fp, #-0x40]
    // 0x6692ec: ldur            x1, [fp, #-0x38]
    // 0x6692f0: sub             x2, x1, x23
    // 0x6692f4: sub             x1, x0, x20
    // 0x6692f8: cmp             x1, x2
    // 0x6692fc: b.lt            #0x6697d4
    // 0x669300: cbz             x2, #0x669484
    // 0x669304: r0 = BoxInt64Instr(r2)
    //     0x669304: sbfiz           x0, x2, #1, #0x1f
    //     0x669308: cmp             x2, x0, asr #1
    //     0x66930c: b.eq            #0x669318
    //     0x669310: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x669314: stur            x2, [x0, #7]
    // 0x669318: mov             x3, x0
    // 0x66931c: cmp             w3, #0x800
    // 0x669320: b.ge            #0x66942c
    // 0x669324: ldur            x4, [fp, #-0x48]
    // 0x669328: r0 = BoxInt64Instr(r20)
    //     0x669328: sbfiz           x0, x20, #1, #0x1f
    //     0x66932c: cmp             x20, x0, asr #1
    //     0x669330: b.eq            #0x66933c
    //     0x669334: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x669338: stur            x20, [x0, #7]
    // 0x66933c: mov             x2, x0
    // 0x669340: r0 = BoxInt64Instr(r23)
    //     0x669340: sbfiz           x0, x23, #1, #0x1f
    //     0x669344: cmp             x23, x0, asr #1
    //     0x669348: b.eq            #0x669354
    //     0x66934c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x669350: stur            x23, [x0, #7]
    // 0x669354: mov             x5, x3
    // 0x669358: sxtw            x2, w2
    // 0x66935c: add             x3, x4, x2, asr #1
    // 0x669360: add             x3, x3, #0x17
    // 0x669364: sxtw            x0, w0
    // 0x669368: add             x1, x4, x0, asr #1
    // 0x66936c: add             x1, x1, #0x17
    // 0x669370: cbz             x5, #0x669428
    // 0x669374: cmp             x1, x3
    // 0x669378: b.ls            #0x6693e0
    // 0x66937c: sxtw            x5, w5
    // 0x669380: add             x16, x3, x5, asr #1
    // 0x669384: cmp             x1, x16
    // 0x669388: b.hs            #0x6693e0
    // 0x66938c: mov             x3, x16
    // 0x669390: add             x1, x1, x5, asr #1
    // 0x669394: tbz             w5, #4, #0x6693a0
    // 0x669398: ldr             x16, [x3, #-8]!
    // 0x66939c: str             x16, [x1, #-8]!
    // 0x6693a0: tbz             w5, #3, #0x6693ac
    // 0x6693a4: ldr             w16, [x3, #-4]!
    // 0x6693a8: str             w16, [x1, #-4]!
    // 0x6693ac: tbz             w5, #2, #0x6693b8
    // 0x6693b0: ldrh            w16, [x3, #-2]!
    // 0x6693b4: strh            w16, [x1, #-2]!
    // 0x6693b8: tbz             w5, #1, #0x6693c4
    // 0x6693bc: ldrb            w16, [x3, #-1]!
    // 0x6693c0: strb            w16, [x1, #-1]!
    // 0x6693c4: ands            w5, w5, #0xffffffe1
    // 0x6693c8: b.eq            #0x669428
    // 0x6693cc: ldp             x16, x17, [x3, #-0x10]!
    // 0x6693d0: stp             x16, x17, [x1, #-0x10]!
    // 0x6693d4: subs            w5, w5, #0x20
    // 0x6693d8: b.ne            #0x6693cc
    // 0x6693dc: b               #0x669428
    // 0x6693e0: tbz             w5, #4, #0x6693ec
    // 0x6693e4: ldr             x16, [x3], #8
    // 0x6693e8: str             x16, [x1], #8
    // 0x6693ec: tbz             w5, #3, #0x6693f8
    // 0x6693f0: ldr             w16, [x3], #4
    // 0x6693f4: str             w16, [x1], #4
    // 0x6693f8: tbz             w5, #2, #0x669404
    // 0x6693fc: ldrh            w16, [x3], #2
    // 0x669400: strh            w16, [x1], #2
    // 0x669404: tbz             w5, #1, #0x669410
    // 0x669408: ldrb            w16, [x3], #1
    // 0x66940c: strb            w16, [x1], #1
    // 0x669410: ands            w5, w5, #0xffffffe1
    // 0x669414: b.eq            #0x669428
    // 0x669418: ldp             x16, x17, [x3], #0x10
    // 0x66941c: stp             x16, x17, [x1], #0x10
    // 0x669420: subs            w5, w5, #0x20
    // 0x669424: b.ne            #0x669418
    // 0x669428: b               #0x669484
    // 0x66942c: ldur            x4, [fp, #-0x48]
    // 0x669430: LoadField: r0 = r4->field_7
    //     0x669430: ldur            x0, [x4, #7]
    // 0x669434: add             x1, x0, x23
    // 0x669438: LoadField: r0 = r4->field_7
    //     0x669438: ldur            x0, [x4, #7]
    // 0x66943c: add             x3, x0, x20
    // 0x669440: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x669440: mov             x0, THR
    //     0x669444: ldr             x9, [x0, #0x658]
    //     0x669448: mov             x0, x1
    //     0x66944c: mov             x1, x3
    //     0x669450: mov             x17, fp
    //     0x669454: str             fp, [SP, #-8]!
    //     0x669458: mov             fp, SP
    //     0x66945c: and             SP, SP, #0xfffffffffffffff0
    //     0x669460: mov             x19, sp
    //     0x669464: mov             sp, SP
    //     0x669468: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x66946c: blr             x9
    //     0x669470: movz            x16, #0x8
    //     0x669474: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x669478: mov             sp, x19
    //     0x66947c: mov             SP, fp
    //     0x669480: ldr             fp, [SP], #8
    // 0x669484: ldur            x3, [fp, #-8]
    // 0x669488: LoadField: r4 = r3->field_b
    //     0x669488: ldur            w4, [x3, #0xb]
    // 0x66948c: DecompressPointer r4
    //     0x66948c: add             x4, x4, HEAP, lsl #32
    // 0x669490: ldur            x0, [fp, #-0x18]
    // 0x669494: stur            x4, [fp, #-0x30]
    // 0x669498: r2 = Null
    //     0x669498: mov             x2, NULL
    // 0x66949c: r1 = Null
    //     0x66949c: mov             x1, NULL
    // 0x6694a0: r8 = Iterable<int>
    //     0x6694a0: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c800] Type: Iterable<int>
    //     0x6694a4: ldr             x8, [x8, #0x800]
    // 0x6694a8: r3 = Null
    //     0x6694a8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c9b8] Null
    //     0x6694ac: ldr             x3, [x3, #0x9b8]
    // 0x6694b0: r0 = Iterable<int>()
    //     0x6694b0: bl              #0x66781c  ; IsType_Iterable<int>_Stub
    // 0x6694b4: ldur            x4, [fp, #-0x10]
    // 0x6694b8: tbz             x4, #0x3f, #0x6694c8
    // 0x6694bc: ldur            x5, [fp, #-0x40]
    // 0x6694c0: ldur            x6, [fp, #-0x30]
    // 0x6694c4: b               #0x6694f0
    // 0x6694c8: ldur            x5, [fp, #-0x40]
    // 0x6694cc: cmp             x4, x5
    // 0x6694d0: b.le            #0x6694dc
    // 0x6694d4: ldur            x6, [fp, #-0x30]
    // 0x6694d8: b               #0x6694f0
    // 0x6694dc: ldur            x6, [fp, #-0x30]
    // 0x6694e0: LoadField: r0 = r6->field_13
    //     0x6694e0: ldur            w0, [x6, #0x13]
    // 0x6694e4: r1 = LoadInt32Instr(r0)
    //     0x6694e4: sbfx            x1, x0, #1, #0x1f
    // 0x6694e8: cmp             x5, x1
    // 0x6694ec: b.le            #0x66951c
    // 0x6694f0: LoadField: r2 = r6->field_13
    //     0x6694f0: ldur            w2, [x6, #0x13]
    // 0x6694f4: r0 = BoxInt64Instr(r5)
    //     0x6694f4: sbfiz           x0, x5, #1, #0x1f
    //     0x6694f8: cmp             x5, x0, asr #1
    //     0x6694fc: b.eq            #0x669508
    //     0x669500: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x669504: stur            x5, [x0, #7]
    // 0x669508: r3 = LoadInt32Instr(r2)
    //     0x669508: sbfx            x3, x2, #1, #0x1f
    // 0x66950c: mov             x1, x4
    // 0x669510: mov             x2, x0
    // 0x669514: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x669514: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x669518: r0 = checkValidRange()
    //     0x669518: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x66951c: ldur            x2, [fp, #-0x18]
    // 0x669520: r0 = LoadClassIdInstr(r2)
    //     0x669520: ldur            x0, [x2, #-1]
    //     0x669524: ubfx            x0, x0, #0xc, #0x14
    // 0x669528: sub             x16, x0, #0x70
    // 0x66952c: cmp             x16, #0x37
    // 0x669530: b.hi            #0x669730
    // 0x669534: r0 = LoadClassIdInstr(r2)
    //     0x669534: ldur            x0, [x2, #-1]
    //     0x669538: ubfx            x0, x0, #0xc, #0x14
    // 0x66953c: mov             x1, x2
    // 0x669540: r0 = GDT[cid_x0 + 0xd16b]()
    //     0x669540: movz            x17, #0xd16b
    //     0x669544: add             lr, x0, x17
    //     0x669548: ldr             lr, [x21, lr, lsl #3]
    //     0x66954c: blr             lr
    // 0x669550: cmp             x0, #1
    // 0x669554: b.ne            #0x66971c
    // 0x669558: ldur            x2, [fp, #-0x10]
    // 0x66955c: ldur            x5, [fp, #-0x18]
    // 0x669560: ldur            x3, [fp, #-0x40]
    // 0x669564: sub             x1, x3, x2
    // 0x669568: stur            x1, [fp, #-0x20]
    // 0x66956c: r0 = LoadClassIdInstr(r5)
    //     0x66956c: ldur            x0, [x5, #-1]
    //     0x669570: ubfx            x0, x0, #0xc, #0x14
    // 0x669574: str             x5, [SP]
    // 0x669578: r0 = GDT[cid_x0 + 0xc834]()
    //     0x669578: movz            x17, #0xc834
    //     0x66957c: add             lr, x0, x17
    //     0x669580: ldr             lr, [x21, lr, lsl #3]
    //     0x669584: blr             lr
    // 0x669588: r1 = LoadInt32Instr(r0)
    //     0x669588: sbfx            x1, x0, #1, #0x1f
    //     0x66958c: tbz             w0, #0, #0x669594
    //     0x669590: ldur            x1, [x0, #7]
    // 0x669594: ldur            x2, [fp, #-0x20]
    // 0x669598: cmp             x1, x2
    // 0x66959c: b.lt            #0x6697e0
    // 0x6695a0: cbz             x2, #0x669750
    // 0x6695a4: r0 = BoxInt64Instr(r2)
    //     0x6695a4: sbfiz           x0, x2, #1, #0x1f
    //     0x6695a8: cmp             x2, x0, asr #1
    //     0x6695ac: b.eq            #0x6695b8
    //     0x6695b0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6695b4: stur            x2, [x0, #7]
    // 0x6695b8: mov             x3, x0
    // 0x6695bc: cmp             w3, #0x800
    // 0x6695c0: b.ge            #0x6696b8
    // 0x6695c4: ldur            x4, [fp, #-0x10]
    // 0x6695c8: ldur            x5, [fp, #-0x18]
    // 0x6695cc: ldur            x6, [fp, #-0x30]
    // 0x6695d0: r0 = BoxInt64Instr(r4)
    //     0x6695d0: sbfiz           x0, x4, #1, #0x1f
    //     0x6695d4: cmp             x4, x0, asr #1
    //     0x6695d8: b.eq            #0x6695e4
    //     0x6695dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6695e0: stur            x4, [x0, #7]
    // 0x6695e4: LoadField: r1 = r5->field_7
    //     0x6695e4: ldur            x1, [x5, #7]
    // 0x6695e8: mov             x4, x3
    // 0x6695ec: mov             x3, x1
    // 0x6695f0: sxtw            x0, w0
    // 0x6695f4: add             x2, x6, x0, asr #1
    // 0x6695f8: add             x2, x2, #0x17
    // 0x6695fc: cbz             x4, #0x6696b4
    // 0x669600: cmp             x2, x3
    // 0x669604: b.ls            #0x66966c
    // 0x669608: sxtw            x4, w4
    // 0x66960c: add             x16, x3, x4, asr #1
    // 0x669610: cmp             x2, x16
    // 0x669614: b.hs            #0x66966c
    // 0x669618: mov             x3, x16
    // 0x66961c: add             x2, x2, x4, asr #1
    // 0x669620: tbz             w4, #4, #0x66962c
    // 0x669624: ldr             x16, [x3, #-8]!
    // 0x669628: str             x16, [x2, #-8]!
    // 0x66962c: tbz             w4, #3, #0x669638
    // 0x669630: ldr             w16, [x3, #-4]!
    // 0x669634: str             w16, [x2, #-4]!
    // 0x669638: tbz             w4, #2, #0x669644
    // 0x66963c: ldrh            w16, [x3, #-2]!
    // 0x669640: strh            w16, [x2, #-2]!
    // 0x669644: tbz             w4, #1, #0x669650
    // 0x669648: ldrb            w16, [x3, #-1]!
    // 0x66964c: strb            w16, [x2, #-1]!
    // 0x669650: ands            w4, w4, #0xffffffe1
    // 0x669654: b.eq            #0x6696b4
    // 0x669658: ldp             x16, x17, [x3, #-0x10]!
    // 0x66965c: stp             x16, x17, [x2, #-0x10]!
    // 0x669660: subs            w4, w4, #0x20
    // 0x669664: b.ne            #0x669658
    // 0x669668: b               #0x6696b4
    // 0x66966c: tbz             w4, #4, #0x669678
    // 0x669670: ldr             x16, [x3], #8
    // 0x669674: str             x16, [x2], #8
    // 0x669678: tbz             w4, #3, #0x669684
    // 0x66967c: ldr             w16, [x3], #4
    // 0x669680: str             w16, [x2], #4
    // 0x669684: tbz             w4, #2, #0x669690
    // 0x669688: ldrh            w16, [x3], #2
    // 0x66968c: strh            w16, [x2], #2
    // 0x669690: tbz             w4, #1, #0x66969c
    // 0x669694: ldrb            w16, [x3], #1
    // 0x669698: strb            w16, [x2], #1
    // 0x66969c: ands            w4, w4, #0xffffffe1
    // 0x6696a0: b.eq            #0x6696b4
    // 0x6696a4: ldp             x16, x17, [x3], #0x10
    // 0x6696a8: stp             x16, x17, [x2], #0x10
    // 0x6696ac: subs            w4, w4, #0x20
    // 0x6696b0: b.ne            #0x6696a4
    // 0x6696b4: b               #0x669750
    // 0x6696b8: ldur            x4, [fp, #-0x10]
    // 0x6696bc: ldur            x5, [fp, #-0x18]
    // 0x6696c0: ldur            x6, [fp, #-0x30]
    // 0x6696c4: LoadField: r0 = r6->field_7
    //     0x6696c4: ldur            x0, [x6, #7]
    // 0x6696c8: add             x1, x0, x4
    // 0x6696cc: LoadField: r0 = r5->field_7
    //     0x6696cc: ldur            x0, [x5, #7]
    // 0x6696d0: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x6696d0: mov             x3, THR
    //     0x6696d4: ldr             x9, [x3, #0x658]
    //     0x6696d8: mov             x16, x0
    //     0x6696dc: mov             x0, x1
    //     0x6696e0: mov             x1, x16
    //     0x6696e4: mov             x17, fp
    //     0x6696e8: str             fp, [SP, #-8]!
    //     0x6696ec: mov             fp, SP
    //     0x6696f0: and             SP, SP, #0xfffffffffffffff0
    //     0x6696f4: mov             x19, sp
    //     0x6696f8: mov             sp, SP
    //     0x6696fc: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x669700: blr             x9
    //     0x669704: movz            x16, #0x8
    //     0x669708: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x66970c: mov             sp, x19
    //     0x669710: mov             SP, fp
    //     0x669714: ldr             fp, [SP], #8
    // 0x669718: b               #0x669750
    // 0x66971c: ldur            x4, [fp, #-0x10]
    // 0x669720: ldur            x5, [fp, #-0x18]
    // 0x669724: ldur            x3, [fp, #-0x40]
    // 0x669728: ldur            x6, [fp, #-0x30]
    // 0x66972c: b               #0x669740
    // 0x669730: ldur            x4, [fp, #-0x10]
    // 0x669734: mov             x5, x2
    // 0x669738: ldur            x3, [fp, #-0x40]
    // 0x66973c: ldur            x6, [fp, #-0x30]
    // 0x669740: mov             x1, x6
    // 0x669744: mov             x2, x4
    // 0x669748: r6 = 0
    //     0x669748: movz            x6, #0
    // 0x66974c: r0 = _slowSetRange()
    //     0x66974c: bl              #0xc0fe04  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x669750: ldur            x0, [fp, #-8]
    // 0x669754: ldur            x1, [fp, #-0x28]
    // 0x669758: StoreField: r0->field_f = r1
    //     0x669758: stur            x1, [x0, #0xf]
    // 0x66975c: r0 = Null
    //     0x66975c: mov             x0, NULL
    // 0x669760: LeaveFrame
    //     0x669760: mov             SP, fp
    //     0x669764: ldp             fp, lr, [SP], #0x10
    // 0x669768: ret
    //     0x669768: ret             
    // 0x66976c: r0 = StateError()
    //     0x66976c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x669770: mov             x1, x0
    // 0x669774: r0 = "Too few elements"
    //     0x669774: ldr             x0, [PP, #0xe58]  ; [pp+0xe58] "Too few elements"
    // 0x669778: StoreField: r1->field_b = r0
    //     0x669778: stur            w0, [x1, #0xb]
    // 0x66977c: mov             x0, x1
    // 0x669780: r0 = Throw()
    //     0x669780: bl              #0xec04b8  ; ThrowStub
    // 0x669784: brk             #0
    // 0x669788: mov             x4, x20
    // 0x66978c: r0 = BoxInt64Instr(r4)
    //     0x66978c: sbfiz           x0, x4, #1, #0x1f
    //     0x669790: cmp             x4, x0, asr #1
    //     0x669794: b.eq            #0x6697a0
    //     0x669798: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66979c: stur            x4, [x0, #7]
    // 0x6697a0: stur            x0, [fp, #-8]
    // 0x6697a4: r0 = RangeError()
    //     0x6697a4: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6697a8: stur            x0, [fp, #-0x18]
    // 0x6697ac: ldur            x16, [fp, #-8]
    // 0x6697b0: stp             x16, x0, [SP, #0x18]
    // 0x6697b4: stp             NULL, xzr, [SP, #8]
    // 0x6697b8: r16 = "skipCount"
    //     0x6697b8: ldr             x16, [PP, #0xe50]  ; [pp+0xe50] "skipCount"
    // 0x6697bc: str             x16, [SP]
    // 0x6697c0: r4 = const [0, 0x5, 0x5, 0x5, null]
    //     0x6697c0: ldr             x4, [PP, #0xce0]  ; [pp+0xce0] List(5) [0, 0x5, 0x5, 0x5, Null]
    // 0x6697c4: r0 = RangeError.range()
    //     0x6697c4: bl              #0x600404  ; [dart:core] RangeError::RangeError.range
    // 0x6697c8: ldur            x0, [fp, #-0x18]
    // 0x6697cc: r0 = Throw()
    //     0x6697cc: bl              #0xec04b8  ; ThrowStub
    // 0x6697d0: brk             #0
    // 0x6697d4: r0 = tooFew()
    //     0x6697d4: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x6697d8: r0 = Throw()
    //     0x6697d8: bl              #0xec04b8  ; ThrowStub
    // 0x6697dc: brk             #0
    // 0x6697e0: r0 = tooFew()
    //     0x6697e0: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x6697e4: r0 = Throw()
    //     0x6697e4: bl              #0xec04b8  ; ThrowStub
    // 0x6697e8: brk             #0
    // 0x6697ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6697ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6697f0: b               #0x6690f8
  }
  _ _ensureCapacity(/* No info */) {
    // ** addr: 0x6697f4, size: 0x264
    // 0x6697f4: EnterFrame
    //     0x6697f4: stp             fp, lr, [SP, #-0x10]!
    //     0x6697f8: mov             fp, SP
    // 0x6697fc: AllocStack(0x20)
    //     0x6697fc: sub             SP, SP, #0x20
    // 0x669800: SetupParameters(TypedDataBuffer<X0> this /* r1 => r3, fp-0x8 */)
    //     0x669800: mov             x3, x1
    //     0x669804: stur            x1, [fp, #-8]
    // 0x669808: CheckStackOverflow
    //     0x669808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66980c: cmp             SP, x16
    //     0x669810: b.ls            #0x669a50
    // 0x669814: LoadField: r0 = r3->field_b
    //     0x669814: ldur            w0, [x3, #0xb]
    // 0x669818: DecompressPointer r0
    //     0x669818: add             x0, x0, HEAP, lsl #32
    // 0x66981c: LoadField: r1 = r0->field_13
    //     0x66981c: ldur            w1, [x0, #0x13]
    // 0x669820: r0 = LoadInt32Instr(r1)
    //     0x669820: sbfx            x0, x1, #1, #0x1f
    // 0x669824: cmp             x2, x0
    // 0x669828: b.gt            #0x66983c
    // 0x66982c: r0 = Null
    //     0x66982c: mov             x0, NULL
    // 0x669830: LeaveFrame
    //     0x669830: mov             SP, fp
    //     0x669834: ldp             fp, lr, [SP], #0x10
    // 0x669838: ret
    //     0x669838: ret             
    // 0x66983c: r0 = BoxInt64Instr(r2)
    //     0x66983c: sbfiz           x0, x2, #1, #0x1f
    //     0x669840: cmp             x2, x0, asr #1
    //     0x669844: b.eq            #0x669850
    //     0x669848: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66984c: stur            x2, [x0, #7]
    // 0x669850: mov             x1, x3
    // 0x669854: mov             x2, x0
    // 0x669858: r0 = _createBiggerBuffer()
    //     0x669858: bl              #0x669048  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_createBiggerBuffer
    // 0x66985c: mov             x5, x0
    // 0x669860: ldur            x4, [fp, #-8]
    // 0x669864: stur            x5, [fp, #-0x20]
    // 0x669868: LoadField: r6 = r4->field_f
    //     0x669868: ldur            x6, [x4, #0xf]
    // 0x66986c: stur            x6, [fp, #-0x18]
    // 0x669870: LoadField: r7 = r4->field_b
    //     0x669870: ldur            w7, [x4, #0xb]
    // 0x669874: DecompressPointer r7
    //     0x669874: add             x7, x7, HEAP, lsl #32
    // 0x669878: stur            x7, [fp, #-0x10]
    // 0x66987c: tbnz            x6, #0x3f, #0x669890
    // 0x669880: LoadField: r0 = r5->field_13
    //     0x669880: ldur            w0, [x5, #0x13]
    // 0x669884: r1 = LoadInt32Instr(r0)
    //     0x669884: sbfx            x1, x0, #1, #0x1f
    // 0x669888: cmp             x6, x1
    // 0x66988c: b.le            #0x6698bc
    // 0x669890: LoadField: r2 = r5->field_13
    //     0x669890: ldur            w2, [x5, #0x13]
    // 0x669894: r0 = BoxInt64Instr(r6)
    //     0x669894: sbfiz           x0, x6, #1, #0x1f
    //     0x669898: cmp             x6, x0, asr #1
    //     0x66989c: b.eq            #0x6698a8
    //     0x6698a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6698a4: stur            x6, [x0, #7]
    // 0x6698a8: r3 = LoadInt32Instr(r2)
    //     0x6698a8: sbfx            x3, x2, #1, #0x1f
    // 0x6698ac: mov             x2, x0
    // 0x6698b0: r1 = 0
    //     0x6698b0: movz            x1, #0
    // 0x6698b4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6698b4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x6698b8: r0 = checkValidRange()
    //     0x6698b8: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x6698bc: ldur            x2, [fp, #-0x18]
    // 0x6698c0: ldur            x3, [fp, #-0x10]
    // 0x6698c4: LoadField: r0 = r3->field_13
    //     0x6698c4: ldur            w0, [x3, #0x13]
    // 0x6698c8: r1 = LoadInt32Instr(r0)
    //     0x6698c8: sbfx            x1, x0, #1, #0x1f
    // 0x6698cc: cmp             x1, x2
    // 0x6698d0: b.lt            #0x669a44
    // 0x6698d4: cbnz            x2, #0x6698e0
    // 0x6698d8: ldur            x20, [fp, #-0x20]
    // 0x6698dc: b               #0x669a10
    // 0x6698e0: r0 = BoxInt64Instr(r2)
    //     0x6698e0: sbfiz           x0, x2, #1, #0x1f
    //     0x6698e4: cmp             x2, x0, asr #1
    //     0x6698e8: b.eq            #0x6698f4
    //     0x6698ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6698f0: stur            x2, [x0, #7]
    // 0x6698f4: cmp             w0, #0x800
    // 0x6698f8: b.ge            #0x6699c8
    // 0x6698fc: ldur            x20, [fp, #-0x20]
    // 0x669900: mov             x2, x0
    // 0x669904: add             x1, x3, #0x17
    // 0x669908: add             x0, x20, #0x17
    // 0x66990c: cbz             x2, #0x6699c4
    // 0x669910: cmp             x0, x1
    // 0x669914: b.ls            #0x66997c
    // 0x669918: sxtw            x2, w2
    // 0x66991c: add             x16, x1, x2, asr #1
    // 0x669920: cmp             x0, x16
    // 0x669924: b.hs            #0x66997c
    // 0x669928: mov             x1, x16
    // 0x66992c: add             x0, x0, x2, asr #1
    // 0x669930: tbz             w2, #4, #0x66993c
    // 0x669934: ldr             x16, [x1, #-8]!
    // 0x669938: str             x16, [x0, #-8]!
    // 0x66993c: tbz             w2, #3, #0x669948
    // 0x669940: ldr             w16, [x1, #-4]!
    // 0x669944: str             w16, [x0, #-4]!
    // 0x669948: tbz             w2, #2, #0x669954
    // 0x66994c: ldrh            w16, [x1, #-2]!
    // 0x669950: strh            w16, [x0, #-2]!
    // 0x669954: tbz             w2, #1, #0x669960
    // 0x669958: ldrb            w16, [x1, #-1]!
    // 0x66995c: strb            w16, [x0, #-1]!
    // 0x669960: ands            w2, w2, #0xffffffe1
    // 0x669964: b.eq            #0x6699c4
    // 0x669968: ldp             x16, x17, [x1, #-0x10]!
    // 0x66996c: stp             x16, x17, [x0, #-0x10]!
    // 0x669970: subs            w2, w2, #0x20
    // 0x669974: b.ne            #0x669968
    // 0x669978: b               #0x6699c4
    // 0x66997c: tbz             w2, #4, #0x669988
    // 0x669980: ldr             x16, [x1], #8
    // 0x669984: str             x16, [x0], #8
    // 0x669988: tbz             w2, #3, #0x669994
    // 0x66998c: ldr             w16, [x1], #4
    // 0x669990: str             w16, [x0], #4
    // 0x669994: tbz             w2, #2, #0x6699a0
    // 0x669998: ldrh            w16, [x1], #2
    // 0x66999c: strh            w16, [x0], #2
    // 0x6699a0: tbz             w2, #1, #0x6699ac
    // 0x6699a4: ldrb            w16, [x1], #1
    // 0x6699a8: strb            w16, [x0], #1
    // 0x6699ac: ands            w2, w2, #0xffffffe1
    // 0x6699b0: b.eq            #0x6699c4
    // 0x6699b4: ldp             x16, x17, [x1], #0x10
    // 0x6699b8: stp             x16, x17, [x0], #0x10
    // 0x6699bc: subs            w2, w2, #0x20
    // 0x6699c0: b.ne            #0x6699b4
    // 0x6699c4: b               #0x669a10
    // 0x6699c8: ldur            x20, [fp, #-0x20]
    // 0x6699cc: LoadField: r0 = r20->field_7
    //     0x6699cc: ldur            x0, [x20, #7]
    // 0x6699d0: LoadField: r1 = r3->field_7
    //     0x6699d0: ldur            x1, [x3, #7]
    // 0x6699d4: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x6699d4: mov             x3, THR
    //     0x6699d8: ldr             x9, [x3, #0x658]
    //     0x6699dc: mov             x17, fp
    //     0x6699e0: str             fp, [SP, #-8]!
    //     0x6699e4: mov             fp, SP
    //     0x6699e8: and             SP, SP, #0xfffffffffffffff0
    //     0x6699ec: mov             x19, sp
    //     0x6699f0: mov             sp, SP
    //     0x6699f4: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x6699f8: blr             x9
    //     0x6699fc: movz            x16, #0x8
    //     0x669a00: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x669a04: mov             sp, x19
    //     0x669a08: mov             SP, fp
    //     0x669a0c: ldr             fp, [SP], #8
    // 0x669a10: ldur            x1, [fp, #-8]
    // 0x669a14: mov             x0, x20
    // 0x669a18: StoreField: r1->field_b = r0
    //     0x669a18: stur            w0, [x1, #0xb]
    //     0x669a1c: ldurb           w16, [x1, #-1]
    //     0x669a20: ldurb           w17, [x0, #-1]
    //     0x669a24: and             x16, x17, x16, lsr #2
    //     0x669a28: tst             x16, HEAP, lsr #32
    //     0x669a2c: b.eq            #0x669a34
    //     0x669a30: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x669a34: r0 = Null
    //     0x669a34: mov             x0, NULL
    // 0x669a38: LeaveFrame
    //     0x669a38: mov             SP, fp
    //     0x669a3c: ldp             fp, lr, [SP], #0x10
    // 0x669a40: ret
    //     0x669a40: ret             
    // 0x669a44: r0 = tooFew()
    //     0x669a44: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x669a48: r0 = Throw()
    //     0x669a48: bl              #0xec04b8  ; ThrowStub
    // 0x669a4c: brk             #0
    // 0x669a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x669a50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x669a54: b               #0x669814
  }
  _ insert(/* No info */) {
    // ** addr: 0x66a194, size: 0x5c8
    // 0x66a194: EnterFrame
    //     0x66a194: stp             fp, lr, [SP, #-0x10]!
    //     0x66a198: mov             fp, SP
    // 0x66a19c: AllocStack(0x50)
    //     0x66a19c: sub             SP, SP, #0x50
    // 0x66a1a0: SetupParameters(TypedDataBuffer<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x66a1a0: mov             x4, x1
    //     0x66a1a4: stur            x1, [fp, #-8]
    //     0x66a1a8: stur            x3, [fp, #-0x10]
    // 0x66a1ac: CheckStackOverflow
    //     0x66a1ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66a1b0: cmp             SP, x16
    //     0x66a1b4: b.ls            #0x66a74c
    // 0x66a1b8: LoadField: r2 = r4->field_7
    //     0x66a1b8: ldur            w2, [x4, #7]
    // 0x66a1bc: DecompressPointer r2
    //     0x66a1bc: add             x2, x2, HEAP, lsl #32
    // 0x66a1c0: mov             x0, x3
    // 0x66a1c4: r1 = Null
    //     0x66a1c4: mov             x1, NULL
    // 0x66a1c8: cmp             w2, NULL
    // 0x66a1cc: b.eq            #0x66a1ec
    // 0x66a1d0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66a1d0: ldur            w4, [x2, #0x17]
    // 0x66a1d4: DecompressPointer r4
    //     0x66a1d4: add             x4, x4, HEAP, lsl #32
    // 0x66a1d8: r8 = X0
    //     0x66a1d8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x66a1dc: LoadField: r9 = r4->field_7
    //     0x66a1dc: ldur            x9, [x4, #7]
    // 0x66a1e0: r3 = Null
    //     0x66a1e0: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d068] Null
    //     0x66a1e4: ldr             x3, [x3, #0x68]
    // 0x66a1e8: blr             x9
    // 0x66a1ec: ldur            x4, [fp, #-8]
    // 0x66a1f0: LoadField: r2 = r4->field_f
    //     0x66a1f0: ldur            x2, [x4, #0xf]
    // 0x66a1f4: tbnz            x2, #0x3f, #0x66a6f4
    // 0x66a1f8: LoadField: r5 = r4->field_b
    //     0x66a1f8: ldur            w5, [x4, #0xb]
    // 0x66a1fc: DecompressPointer r5
    //     0x66a1fc: add             x5, x5, HEAP, lsl #32
    // 0x66a200: stur            x5, [fp, #-0x20]
    // 0x66a204: LoadField: r0 = r5->field_13
    //     0x66a204: ldur            w0, [x5, #0x13]
    // 0x66a208: r3 = LoadInt32Instr(r0)
    //     0x66a208: sbfx            x3, x0, #1, #0x1f
    // 0x66a20c: cmp             x2, x3
    // 0x66a210: b.ge            #0x66a428
    // 0x66a214: add             x6, x2, #1
    // 0x66a218: stur            x6, [fp, #-0x18]
    // 0x66a21c: cmp             x6, #1
    // 0x66a220: b.ge            #0x66a248
    // 0x66a224: r0 = BoxInt64Instr(r6)
    //     0x66a224: sbfiz           x0, x6, #1, #0x1f
    //     0x66a228: cmp             x6, x0, asr #1
    //     0x66a22c: b.eq            #0x66a238
    //     0x66a230: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66a234: stur            x6, [x0, #7]
    // 0x66a238: mov             x2, x0
    // 0x66a23c: r1 = 1
    //     0x66a23c: movz            x1, #0x1
    // 0x66a240: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x66a240: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x66a244: r0 = checkValidRange()
    //     0x66a244: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x66a248: ldur            x0, [fp, #-0x18]
    // 0x66a24c: sub             x2, x0, #1
    // 0x66a250: cbz             x2, #0x66a390
    // 0x66a254: r0 = BoxInt64Instr(r2)
    //     0x66a254: sbfiz           x0, x2, #1, #0x1f
    //     0x66a258: cmp             x2, x0, asr #1
    //     0x66a25c: b.eq            #0x66a268
    //     0x66a260: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66a264: stur            x2, [x0, #7]
    // 0x66a268: cmp             w0, #0x800
    // 0x66a26c: b.ge            #0x66a33c
    // 0x66a270: ldur            x1, [fp, #-0x20]
    // 0x66a274: mov             x3, x0
    // 0x66a278: add             x2, x1, #0x17
    // 0x66a27c: add             x0, x1, #0x18
    // 0x66a280: cbz             x3, #0x66a338
    // 0x66a284: cmp             x0, x2
    // 0x66a288: b.ls            #0x66a2f0
    // 0x66a28c: sxtw            x3, w3
    // 0x66a290: add             x16, x2, x3, asr #1
    // 0x66a294: cmp             x0, x16
    // 0x66a298: b.hs            #0x66a2f0
    // 0x66a29c: mov             x2, x16
    // 0x66a2a0: add             x0, x0, x3, asr #1
    // 0x66a2a4: tbz             w3, #4, #0x66a2b0
    // 0x66a2a8: ldr             x16, [x2, #-8]!
    // 0x66a2ac: str             x16, [x0, #-8]!
    // 0x66a2b0: tbz             w3, #3, #0x66a2bc
    // 0x66a2b4: ldr             w16, [x2, #-4]!
    // 0x66a2b8: str             w16, [x0, #-4]!
    // 0x66a2bc: tbz             w3, #2, #0x66a2c8
    // 0x66a2c0: ldrh            w16, [x2, #-2]!
    // 0x66a2c4: strh            w16, [x0, #-2]!
    // 0x66a2c8: tbz             w3, #1, #0x66a2d4
    // 0x66a2cc: ldrb            w16, [x2, #-1]!
    // 0x66a2d0: strb            w16, [x0, #-1]!
    // 0x66a2d4: ands            w3, w3, #0xffffffe1
    // 0x66a2d8: b.eq            #0x66a338
    // 0x66a2dc: ldp             x16, x17, [x2, #-0x10]!
    // 0x66a2e0: stp             x16, x17, [x0, #-0x10]!
    // 0x66a2e4: subs            w3, w3, #0x20
    // 0x66a2e8: b.ne            #0x66a2dc
    // 0x66a2ec: b               #0x66a338
    // 0x66a2f0: tbz             w3, #4, #0x66a2fc
    // 0x66a2f4: ldr             x16, [x2], #8
    // 0x66a2f8: str             x16, [x0], #8
    // 0x66a2fc: tbz             w3, #3, #0x66a308
    // 0x66a300: ldr             w16, [x2], #4
    // 0x66a304: str             w16, [x0], #4
    // 0x66a308: tbz             w3, #2, #0x66a314
    // 0x66a30c: ldrh            w16, [x2], #2
    // 0x66a310: strh            w16, [x0], #2
    // 0x66a314: tbz             w3, #1, #0x66a320
    // 0x66a318: ldrb            w16, [x2], #1
    // 0x66a31c: strb            w16, [x0], #1
    // 0x66a320: ands            w3, w3, #0xffffffe1
    // 0x66a324: b.eq            #0x66a338
    // 0x66a328: ldp             x16, x17, [x2], #0x10
    // 0x66a32c: stp             x16, x17, [x0], #0x10
    // 0x66a330: subs            w3, w3, #0x20
    // 0x66a334: b.ne            #0x66a328
    // 0x66a338: b               #0x66a390
    // 0x66a33c: ldur            x1, [fp, #-0x20]
    // 0x66a340: LoadField: r0 = r1->field_7
    //     0x66a340: ldur            x0, [x1, #7]
    // 0x66a344: add             x3, x0, #1
    // 0x66a348: LoadField: r0 = r1->field_7
    //     0x66a348: ldur            x0, [x1, #7]
    // 0x66a34c: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x66a34c: mov             x1, THR
    //     0x66a350: ldr             x9, [x1, #0x658]
    //     0x66a354: mov             x1, x0
    //     0x66a358: mov             x0, x3
    //     0x66a35c: mov             x17, fp
    //     0x66a360: str             fp, [SP, #-8]!
    //     0x66a364: mov             fp, SP
    //     0x66a368: and             SP, SP, #0xfffffffffffffff0
    //     0x66a36c: mov             x19, sp
    //     0x66a370: mov             sp, SP
    //     0x66a374: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x66a378: blr             x9
    //     0x66a37c: movz            x16, #0x8
    //     0x66a380: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x66a384: mov             sp, x19
    //     0x66a388: mov             SP, fp
    //     0x66a38c: ldr             fp, [SP], #8
    // 0x66a390: ldur            x3, [fp, #-8]
    // 0x66a394: ldur            x4, [fp, #-0x10]
    // 0x66a398: LoadField: r5 = r3->field_b
    //     0x66a398: ldur            w5, [x3, #0xb]
    // 0x66a39c: DecompressPointer r5
    //     0x66a39c: add             x5, x5, HEAP, lsl #32
    // 0x66a3a0: mov             x0, x4
    // 0x66a3a4: stur            x5, [fp, #-0x20]
    // 0x66a3a8: r2 = Null
    //     0x66a3a8: mov             x2, NULL
    // 0x66a3ac: r1 = Null
    //     0x66a3ac: mov             x1, NULL
    // 0x66a3b0: branchIfSmi(r0, 0x66a3d8)
    //     0x66a3b0: tbz             w0, #0, #0x66a3d8
    // 0x66a3b4: r4 = LoadClassIdInstr(r0)
    //     0x66a3b4: ldur            x4, [x0, #-1]
    //     0x66a3b8: ubfx            x4, x4, #0xc, #0x14
    // 0x66a3bc: sub             x4, x4, #0x3c
    // 0x66a3c0: cmp             x4, #1
    // 0x66a3c4: b.ls            #0x66a3d8
    // 0x66a3c8: r8 = int
    //     0x66a3c8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x66a3cc: r3 = Null
    //     0x66a3cc: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d078] Null
    //     0x66a3d0: ldr             x3, [x3, #0x78]
    // 0x66a3d4: r0 = int()
    //     0x66a3d4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x66a3d8: ldur            x2, [fp, #-0x20]
    // 0x66a3dc: LoadField: r0 = r2->field_13
    //     0x66a3dc: ldur            w0, [x2, #0x13]
    // 0x66a3e0: r1 = LoadInt32Instr(r0)
    //     0x66a3e0: sbfx            x1, x0, #1, #0x1f
    // 0x66a3e4: mov             x0, x1
    // 0x66a3e8: r1 = 0
    //     0x66a3e8: movz            x1, #0
    // 0x66a3ec: cmp             x1, x0
    // 0x66a3f0: b.hs            #0x66a754
    // 0x66a3f4: ldur            x0, [fp, #-0x10]
    // 0x66a3f8: r1 = LoadInt32Instr(r0)
    //     0x66a3f8: sbfx            x1, x0, #1, #0x1f
    //     0x66a3fc: tbz             w0, #0, #0x66a404
    //     0x66a400: ldur            x1, [x0, #7]
    // 0x66a404: ArrayStore: r2[0] = r1  ; TypeUnknown_1
    //     0x66a404: strb            w1, [x2, #0x17]
    // 0x66a408: ldur            x3, [fp, #-8]
    // 0x66a40c: LoadField: r0 = r3->field_f
    //     0x66a40c: ldur            x0, [x3, #0xf]
    // 0x66a410: add             x1, x0, #1
    // 0x66a414: StoreField: r3->field_f = r1
    //     0x66a414: stur            x1, [x3, #0xf]
    // 0x66a418: r0 = Null
    //     0x66a418: mov             x0, NULL
    // 0x66a41c: LeaveFrame
    //     0x66a41c: mov             SP, fp
    //     0x66a420: ldp             fp, lr, [SP], #0x10
    // 0x66a424: ret
    //     0x66a424: ret             
    // 0x66a428: mov             x3, x4
    // 0x66a42c: ldur            x0, [fp, #-0x10]
    // 0x66a430: mov             x1, x3
    // 0x66a434: r2 = Null
    //     0x66a434: mov             x2, NULL
    // 0x66a438: r0 = _createBiggerBuffer()
    //     0x66a438: bl              #0x669048  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_createBiggerBuffer
    // 0x66a43c: mov             x4, x0
    // 0x66a440: ldur            x0, [fp, #-8]
    // 0x66a444: stur            x4, [fp, #-0x28]
    // 0x66a448: LoadField: r5 = r0->field_b
    //     0x66a448: ldur            w5, [x0, #0xb]
    // 0x66a44c: DecompressPointer r5
    //     0x66a44c: add             x5, x5, HEAP, lsl #32
    // 0x66a450: stur            x5, [fp, #-0x20]
    // 0x66a454: LoadField: r1 = r4->field_13
    //     0x66a454: ldur            w1, [x4, #0x13]
    // 0x66a458: r6 = LoadInt32Instr(r1)
    //     0x66a458: sbfx            x6, x1, #1, #0x1f
    // 0x66a45c: stur            x6, [fp, #-0x18]
    // 0x66a460: tbz             x6, #0x3f, #0x66a478
    // 0x66a464: mov             x3, x6
    // 0x66a468: r1 = 0
    //     0x66a468: movz            x1, #0
    // 0x66a46c: r2 = 0
    //     0x66a46c: movz            x2, #0
    // 0x66a470: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x66a470: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x66a474: r0 = checkValidRange()
    //     0x66a474: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x66a478: ldur            x0, [fp, #-0x20]
    // 0x66a47c: LoadField: r1 = r0->field_13
    //     0x66a47c: ldur            w1, [x0, #0x13]
    // 0x66a480: r0 = LoadInt32Instr(r1)
    //     0x66a480: sbfx            x0, x1, #1, #0x1f
    // 0x66a484: tbnz            x0, #0x3f, #0x66a734
    // 0x66a488: ldur            x4, [fp, #-8]
    // 0x66a48c: LoadField: r0 = r4->field_f
    //     0x66a48c: ldur            x0, [x4, #0xf]
    // 0x66a490: add             x5, x0, #1
    // 0x66a494: stur            x5, [fp, #-0x30]
    // 0x66a498: LoadField: r6 = r4->field_b
    //     0x66a498: ldur            w6, [x4, #0xb]
    // 0x66a49c: DecompressPointer r6
    //     0x66a49c: add             x6, x6, HEAP, lsl #32
    // 0x66a4a0: stur            x6, [fp, #-0x20]
    // 0x66a4a4: cmp             x5, #1
    // 0x66a4a8: b.ge            #0x66a4b4
    // 0x66a4ac: ldur            x7, [fp, #-0x18]
    // 0x66a4b0: b               #0x66a4c0
    // 0x66a4b4: ldur            x7, [fp, #-0x18]
    // 0x66a4b8: cmp             x5, x7
    // 0x66a4bc: b.le            #0x66a4e8
    // 0x66a4c0: r0 = BoxInt64Instr(r5)
    //     0x66a4c0: sbfiz           x0, x5, #1, #0x1f
    //     0x66a4c4: cmp             x5, x0, asr #1
    //     0x66a4c8: b.eq            #0x66a4d4
    //     0x66a4cc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66a4d0: stur            x5, [x0, #7]
    // 0x66a4d4: mov             x2, x0
    // 0x66a4d8: mov             x3, x7
    // 0x66a4dc: r1 = 1
    //     0x66a4dc: movz            x1, #0x1
    // 0x66a4e0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x66a4e0: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x66a4e4: r0 = checkValidRange()
    //     0x66a4e4: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x66a4e8: ldur            x0, [fp, #-0x30]
    // 0x66a4ec: ldur            x2, [fp, #-0x20]
    // 0x66a4f0: sub             x3, x0, #1
    // 0x66a4f4: LoadField: r0 = r2->field_13
    //     0x66a4f4: ldur            w0, [x2, #0x13]
    // 0x66a4f8: r1 = LoadInt32Instr(r0)
    //     0x66a4f8: sbfx            x1, x0, #1, #0x1f
    // 0x66a4fc: cmp             x1, x3
    // 0x66a500: b.lt            #0x66a740
    // 0x66a504: cbnz            x3, #0x66a510
    // 0x66a508: ldur            x20, [fp, #-0x28]
    // 0x66a50c: b               #0x66a654
    // 0x66a510: r0 = BoxInt64Instr(r3)
    //     0x66a510: sbfiz           x0, x3, #1, #0x1f
    //     0x66a514: cmp             x3, x0, asr #1
    //     0x66a518: b.eq            #0x66a524
    //     0x66a51c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66a520: stur            x3, [x0, #7]
    // 0x66a524: cmp             w0, #0x800
    // 0x66a528: b.ge            #0x66a5f8
    // 0x66a52c: ldur            x20, [fp, #-0x28]
    // 0x66a530: mov             x3, x0
    // 0x66a534: add             x1, x2, #0x17
    // 0x66a538: add             x0, x20, #0x18
    // 0x66a53c: cbz             x3, #0x66a5f4
    // 0x66a540: cmp             x0, x1
    // 0x66a544: b.ls            #0x66a5ac
    // 0x66a548: sxtw            x3, w3
    // 0x66a54c: add             x16, x1, x3, asr #1
    // 0x66a550: cmp             x0, x16
    // 0x66a554: b.hs            #0x66a5ac
    // 0x66a558: mov             x1, x16
    // 0x66a55c: add             x0, x0, x3, asr #1
    // 0x66a560: tbz             w3, #4, #0x66a56c
    // 0x66a564: ldr             x16, [x1, #-8]!
    // 0x66a568: str             x16, [x0, #-8]!
    // 0x66a56c: tbz             w3, #3, #0x66a578
    // 0x66a570: ldr             w16, [x1, #-4]!
    // 0x66a574: str             w16, [x0, #-4]!
    // 0x66a578: tbz             w3, #2, #0x66a584
    // 0x66a57c: ldrh            w16, [x1, #-2]!
    // 0x66a580: strh            w16, [x0, #-2]!
    // 0x66a584: tbz             w3, #1, #0x66a590
    // 0x66a588: ldrb            w16, [x1, #-1]!
    // 0x66a58c: strb            w16, [x0, #-1]!
    // 0x66a590: ands            w3, w3, #0xffffffe1
    // 0x66a594: b.eq            #0x66a5f4
    // 0x66a598: ldp             x16, x17, [x1, #-0x10]!
    // 0x66a59c: stp             x16, x17, [x0, #-0x10]!
    // 0x66a5a0: subs            w3, w3, #0x20
    // 0x66a5a4: b.ne            #0x66a598
    // 0x66a5a8: b               #0x66a5f4
    // 0x66a5ac: tbz             w3, #4, #0x66a5b8
    // 0x66a5b0: ldr             x16, [x1], #8
    // 0x66a5b4: str             x16, [x0], #8
    // 0x66a5b8: tbz             w3, #3, #0x66a5c4
    // 0x66a5bc: ldr             w16, [x1], #4
    // 0x66a5c0: str             w16, [x0], #4
    // 0x66a5c4: tbz             w3, #2, #0x66a5d0
    // 0x66a5c8: ldrh            w16, [x1], #2
    // 0x66a5cc: strh            w16, [x0], #2
    // 0x66a5d0: tbz             w3, #1, #0x66a5dc
    // 0x66a5d4: ldrb            w16, [x1], #1
    // 0x66a5d8: strb            w16, [x0], #1
    // 0x66a5dc: ands            w3, w3, #0xffffffe1
    // 0x66a5e0: b.eq            #0x66a5f4
    // 0x66a5e4: ldp             x16, x17, [x1], #0x10
    // 0x66a5e8: stp             x16, x17, [x0], #0x10
    // 0x66a5ec: subs            w3, w3, #0x20
    // 0x66a5f0: b.ne            #0x66a5e4
    // 0x66a5f4: b               #0x66a654
    // 0x66a5f8: ldur            x20, [fp, #-0x28]
    // 0x66a5fc: LoadField: r0 = r20->field_7
    //     0x66a5fc: ldur            x0, [x20, #7]
    // 0x66a600: add             x1, x0, #1
    // 0x66a604: LoadField: r0 = r2->field_7
    //     0x66a604: ldur            x0, [x2, #7]
    // 0x66a608: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x66a608: mov             x2, THR
    //     0x66a60c: ldr             x9, [x2, #0x658]
    //     0x66a610: mov             x16, x0
    //     0x66a614: mov             x0, x1
    //     0x66a618: mov             x1, x16
    //     0x66a61c: mov             x2, x3
    //     0x66a620: mov             x17, fp
    //     0x66a624: str             fp, [SP, #-8]!
    //     0x66a628: mov             fp, SP
    //     0x66a62c: and             SP, SP, #0xfffffffffffffff0
    //     0x66a630: mov             x19, sp
    //     0x66a634: mov             sp, SP
    //     0x66a638: str             x9, [THR, #0x7a0]  ; THR::vm_tag
    //     0x66a63c: blr             x9
    //     0x66a640: movz            x16, #0x8
    //     0x66a644: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x66a648: mov             sp, x19
    //     0x66a64c: mov             SP, fp
    //     0x66a650: ldr             fp, [SP], #8
    // 0x66a654: ldur            x3, [fp, #-8]
    // 0x66a658: ldur            x4, [fp, #-0x10]
    // 0x66a65c: r4 as int
    //     0x66a65c: mov             x0, x4
    //     0x66a660: mov             x2, NULL
    //     0x66a664: mov             x1, NULL
    //     0x66a668: tbz             w0, #0, #0x66a690
    //     0x66a66c: ldur            x4, [x0, #-1]
    //     0x66a670: ubfx            x4, x4, #0xc, #0x14
    //     0x66a674: sub             x4, x4, #0x3c
    //     0x66a678: cmp             x4, #1
    //     0x66a67c: b.ls            #0x66a690
    //     0x66a680: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    //     0x66a684: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d088] Null
    //     0x66a688: ldr             x3, [x3, #0x88]
    //     0x66a68c: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x66a690: ldur            x0, [fp, #-0x18]
    // 0x66a694: r1 = 0
    //     0x66a694: movz            x1, #0
    // 0x66a698: cmp             x1, x0
    // 0x66a69c: b.hs            #0x66a758
    // 0x66a6a0: ldur            x0, [fp, #-0x10]
    // 0x66a6a4: r1 = LoadInt32Instr(r0)
    //     0x66a6a4: sbfx            x1, x0, #1, #0x1f
    //     0x66a6a8: tbz             w0, #0, #0x66a6b0
    //     0x66a6ac: ldur            x1, [x0, #7]
    // 0x66a6b0: ldur            x0, [fp, #-0x28]
    // 0x66a6b4: ArrayStore: r0[0] = r1  ; TypeUnknown_1
    //     0x66a6b4: strb            w1, [x0, #0x17]
    // 0x66a6b8: ldur            x1, [fp, #-8]
    // 0x66a6bc: LoadField: r2 = r1->field_f
    //     0x66a6bc: ldur            x2, [x1, #0xf]
    // 0x66a6c0: add             x3, x2, #1
    // 0x66a6c4: StoreField: r1->field_f = r3
    //     0x66a6c4: stur            x3, [x1, #0xf]
    // 0x66a6c8: StoreField: r1->field_b = r0
    //     0x66a6c8: stur            w0, [x1, #0xb]
    //     0x66a6cc: ldurb           w16, [x1, #-1]
    //     0x66a6d0: ldurb           w17, [x0, #-1]
    //     0x66a6d4: and             x16, x17, x16, lsr #2
    //     0x66a6d8: tst             x16, HEAP, lsr #32
    //     0x66a6dc: b.eq            #0x66a6e4
    //     0x66a6e0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x66a6e4: r0 = Null
    //     0x66a6e4: mov             x0, NULL
    // 0x66a6e8: LeaveFrame
    //     0x66a6e8: mov             SP, fp
    //     0x66a6ec: ldp             fp, lr, [SP], #0x10
    // 0x66a6f0: ret
    //     0x66a6f0: ret             
    // 0x66a6f4: r0 = BoxInt64Instr(r2)
    //     0x66a6f4: sbfiz           x0, x2, #1, #0x1f
    //     0x66a6f8: cmp             x2, x0, asr #1
    //     0x66a6fc: b.eq            #0x66a708
    //     0x66a700: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66a704: stur            x2, [x0, #7]
    // 0x66a708: stur            x0, [fp, #-8]
    // 0x66a70c: r0 = RangeError()
    //     0x66a70c: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x66a710: stur            x0, [fp, #-0x10]
    // 0x66a714: stp             xzr, x0, [SP, #0x10]
    // 0x66a718: ldur            x16, [fp, #-8]
    // 0x66a71c: stp             x16, xzr, [SP]
    // 0x66a720: r4 = const [0, 0x4, 0x4, 0x4, null]
    //     0x66a720: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    // 0x66a724: r0 = RangeError.range()
    //     0x66a724: bl              #0x600404  ; [dart:core] RangeError::RangeError.range
    // 0x66a728: ldur            x0, [fp, #-0x10]
    // 0x66a72c: r0 = Throw()
    //     0x66a72c: bl              #0xec04b8  ; ThrowStub
    // 0x66a730: brk             #0
    // 0x66a734: r0 = tooFew()
    //     0x66a734: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x66a738: r0 = Throw()
    //     0x66a738: bl              #0xec04b8  ; ThrowStub
    // 0x66a73c: brk             #0
    // 0x66a740: r0 = tooFew()
    //     0x66a740: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x66a744: r0 = Throw()
    //     0x66a744: bl              #0xec04b8  ; ThrowStub
    // 0x66a748: brk             #0
    // 0x66a74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66a74c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66a750: b               #0x66a1b8
    // 0x66a754: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66a754: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x66a758: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66a758: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  set _ length=(/* No info */) {
    // ** addr: 0x66aba4, size: 0x250
    // 0x66aba4: EnterFrame
    //     0x66aba4: stp             fp, lr, [SP, #-0x10]!
    //     0x66aba8: mov             fp, SP
    // 0x66abac: AllocStack(0x28)
    //     0x66abac: sub             SP, SP, #0x28
    // 0x66abb0: SetupParameters(TypedDataBuffer<X0> this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x66abb0: mov             x3, x1
    //     0x66abb4: stur            x1, [fp, #-8]
    //     0x66abb8: stur            x2, [fp, #-0x10]
    // 0x66abbc: CheckStackOverflow
    //     0x66abbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66abc0: cmp             SP, x16
    //     0x66abc4: b.ls            #0x66ade0
    // 0x66abc8: LoadField: r4 = r3->field_f
    //     0x66abc8: ldur            x4, [x3, #0xf]
    // 0x66abcc: cmp             x2, x4
    // 0x66abd0: b.ge            #0x66ac2c
    // 0x66abd4: LoadField: r5 = r3->field_b
    //     0x66abd4: ldur            w5, [x3, #0xb]
    // 0x66abd8: DecompressPointer r5
    //     0x66abd8: add             x5, x5, HEAP, lsl #32
    // 0x66abdc: LoadField: r0 = r5->field_13
    //     0x66abdc: ldur            w0, [x5, #0x13]
    // 0x66abe0: r6 = LoadInt32Instr(r0)
    //     0x66abe0: sbfx            x6, x0, #1, #0x1f
    // 0x66abe4: mov             x7, x2
    // 0x66abe8: CheckStackOverflow
    //     0x66abe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66abec: cmp             SP, x16
    //     0x66abf0: b.ls            #0x66ade8
    // 0x66abf4: cmp             x7, x4
    // 0x66abf8: b.ge            #0x66ac20
    // 0x66abfc: mov             x0, x6
    // 0x66ac00: mov             x1, x7
    // 0x66ac04: cmp             x1, x0
    // 0x66ac08: b.hs            #0x66adf0
    // 0x66ac0c: ArrayStore: r5[r7] = rZR  ; TypeUnknown_1
    //     0x66ac0c: add             x0, x5, x7
    //     0x66ac10: strb            wzr, [x0, #0x17]
    // 0x66ac14: add             x0, x7, #1
    // 0x66ac18: mov             x7, x0
    // 0x66ac1c: b               #0x66abe8
    // 0x66ac20: mov             x1, x3
    // 0x66ac24: mov             x0, x2
    // 0x66ac28: b               #0x66adc0
    // 0x66ac2c: LoadField: r0 = r3->field_b
    //     0x66ac2c: ldur            w0, [x3, #0xb]
    // 0x66ac30: DecompressPointer r0
    //     0x66ac30: add             x0, x0, HEAP, lsl #32
    // 0x66ac34: LoadField: r1 = r0->field_13
    //     0x66ac34: ldur            w1, [x0, #0x13]
    // 0x66ac38: r0 = LoadInt32Instr(r1)
    //     0x66ac38: sbfx            x0, x1, #1, #0x1f
    // 0x66ac3c: cmp             x2, x0
    // 0x66ac40: b.le            #0x66adb8
    // 0x66ac44: cbnz            x0, #0x66ac6c
    // 0x66ac48: r0 = BoxInt64Instr(r2)
    //     0x66ac48: sbfiz           x0, x2, #1, #0x1f
    //     0x66ac4c: cmp             x2, x0, asr #1
    //     0x66ac50: b.eq            #0x66ac5c
    //     0x66ac54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66ac58: stur            x2, [x0, #7]
    // 0x66ac5c: mov             x4, x0
    // 0x66ac60: r0 = AllocateUint8Array()
    //     0x66ac60: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x66ac64: mov             x5, x0
    // 0x66ac68: b               #0x66ac94
    // 0x66ac6c: mov             x3, x2
    // 0x66ac70: r0 = BoxInt64Instr(r3)
    //     0x66ac70: sbfiz           x0, x3, #1, #0x1f
    //     0x66ac74: cmp             x3, x0, asr #1
    //     0x66ac78: b.eq            #0x66ac84
    //     0x66ac7c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66ac80: stur            x3, [x0, #7]
    // 0x66ac84: ldur            x1, [fp, #-8]
    // 0x66ac88: mov             x2, x0
    // 0x66ac8c: r0 = _createBiggerBuffer()
    //     0x66ac8c: bl              #0x669048  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_createBiggerBuffer
    // 0x66ac90: mov             x5, x0
    // 0x66ac94: ldur            x4, [fp, #-8]
    // 0x66ac98: stur            x5, [fp, #-0x28]
    // 0x66ac9c: LoadField: r6 = r4->field_f
    //     0x66ac9c: ldur            x6, [x4, #0xf]
    // 0x66aca0: stur            x6, [fp, #-0x20]
    // 0x66aca4: LoadField: r7 = r4->field_b
    //     0x66aca4: ldur            w7, [x4, #0xb]
    // 0x66aca8: DecompressPointer r7
    //     0x66aca8: add             x7, x7, HEAP, lsl #32
    // 0x66acac: stur            x7, [fp, #-0x18]
    // 0x66acb0: tbnz            x6, #0x3f, #0x66acc4
    // 0x66acb4: LoadField: r0 = r5->field_13
    //     0x66acb4: ldur            w0, [x5, #0x13]
    // 0x66acb8: r1 = LoadInt32Instr(r0)
    //     0x66acb8: sbfx            x1, x0, #1, #0x1f
    // 0x66acbc: cmp             x6, x1
    // 0x66acc0: b.le            #0x66acf0
    // 0x66acc4: LoadField: r2 = r5->field_13
    //     0x66acc4: ldur            w2, [x5, #0x13]
    // 0x66acc8: r0 = BoxInt64Instr(r6)
    //     0x66acc8: sbfiz           x0, x6, #1, #0x1f
    //     0x66accc: cmp             x6, x0, asr #1
    //     0x66acd0: b.eq            #0x66acdc
    //     0x66acd4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66acd8: stur            x6, [x0, #7]
    // 0x66acdc: r3 = LoadInt32Instr(r2)
    //     0x66acdc: sbfx            x3, x2, #1, #0x1f
    // 0x66ace0: mov             x2, x0
    // 0x66ace4: r1 = 0
    //     0x66ace4: movz            x1, #0
    // 0x66ace8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x66ace8: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x66acec: r0 = checkValidRange()
    //     0x66acec: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x66acf0: ldur            x2, [fp, #-0x28]
    // 0x66acf4: r0 = LoadClassIdInstr(r2)
    //     0x66acf4: ldur            x0, [x2, #-1]
    //     0x66acf8: ubfx            x0, x0, #0xc, #0x14
    // 0x66acfc: mov             x1, x2
    // 0x66ad00: r0 = GDT[cid_x0 + 0xd16b]()
    //     0x66ad00: movz            x17, #0xd16b
    //     0x66ad04: add             lr, x0, x17
    //     0x66ad08: ldr             lr, [x21, lr, lsl #3]
    //     0x66ad0c: blr             lr
    // 0x66ad10: cmp             x0, #1
    // 0x66ad14: b.ne            #0x66ad60
    // 0x66ad18: ldur            x3, [fp, #-0x20]
    // 0x66ad1c: ldur            x5, [fp, #-0x18]
    // 0x66ad20: LoadField: r0 = r5->field_13
    //     0x66ad20: ldur            w0, [x5, #0x13]
    // 0x66ad24: r1 = LoadInt32Instr(r0)
    //     0x66ad24: sbfx            x1, x0, #1, #0x1f
    // 0x66ad28: cmp             x1, x3
    // 0x66ad2c: b.lt            #0x66add4
    // 0x66ad30: cbz             x3, #0x66ad90
    // 0x66ad34: ldur            x4, [fp, #-0x28]
    // 0x66ad38: r0 = LoadClassIdInstr(r4)
    //     0x66ad38: ldur            x0, [x4, #-1]
    //     0x66ad3c: ubfx            x0, x0, #0xc, #0x14
    // 0x66ad40: mov             x1, x4
    // 0x66ad44: r2 = 0
    //     0x66ad44: movz            x2, #0
    // 0x66ad48: r6 = 0
    //     0x66ad48: movz            x6, #0
    // 0x66ad4c: r0 = GDT[cid_x0 + 0x7ca6]()
    //     0x66ad4c: movz            x17, #0x7ca6
    //     0x66ad50: add             lr, x0, x17
    //     0x66ad54: ldr             lr, [x21, lr, lsl #3]
    //     0x66ad58: blr             lr
    // 0x66ad5c: b               #0x66ad90
    // 0x66ad60: ldur            x4, [fp, #-0x28]
    // 0x66ad64: ldur            x3, [fp, #-0x20]
    // 0x66ad68: ldur            x5, [fp, #-0x18]
    // 0x66ad6c: r0 = LoadClassIdInstr(r4)
    //     0x66ad6c: ldur            x0, [x4, #-1]
    //     0x66ad70: ubfx            x0, x0, #0xc, #0x14
    // 0x66ad74: mov             x1, x4
    // 0x66ad78: r2 = 0
    //     0x66ad78: movz            x2, #0
    // 0x66ad7c: r6 = 0
    //     0x66ad7c: movz            x6, #0
    // 0x66ad80: r0 = GDT[cid_x0 + 0x42fa]()
    //     0x66ad80: movz            x17, #0x42fa
    //     0x66ad84: add             lr, x0, x17
    //     0x66ad88: ldr             lr, [x21, lr, lsl #3]
    //     0x66ad8c: blr             lr
    // 0x66ad90: ldur            x1, [fp, #-8]
    // 0x66ad94: ldur            x0, [fp, #-0x28]
    // 0x66ad98: StoreField: r1->field_b = r0
    //     0x66ad98: stur            w0, [x1, #0xb]
    //     0x66ad9c: ldurb           w16, [x1, #-1]
    //     0x66ada0: ldurb           w17, [x0, #-1]
    //     0x66ada4: and             x16, x17, x16, lsr #2
    //     0x66ada8: tst             x16, HEAP, lsr #32
    //     0x66adac: b.eq            #0x66adb4
    //     0x66adb0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x66adb4: b               #0x66adbc
    // 0x66adb8: mov             x1, x3
    // 0x66adbc: ldur            x0, [fp, #-0x10]
    // 0x66adc0: StoreField: r1->field_f = r0
    //     0x66adc0: stur            x0, [x1, #0xf]
    // 0x66adc4: r0 = Null
    //     0x66adc4: mov             x0, NULL
    // 0x66adc8: LeaveFrame
    //     0x66adc8: mov             SP, fp
    //     0x66adcc: ldp             fp, lr, [SP], #0x10
    // 0x66add0: ret
    //     0x66add0: ret             
    // 0x66add4: r0 = tooFew()
    //     0x66add4: bl              #0x60c970  ; [dart:_internal] IterableElementError::tooFew
    // 0x66add8: r0 = Throw()
    //     0x66add8: bl              #0xec04b8  ; ThrowStub
    // 0x66addc: brk             #0
    // 0x66ade0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66ade0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66ade4: b               #0x66abc8
    // 0x66ade8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66ade8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66adec: b               #0x66abf4
    // 0x66adf0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66adf0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ add(/* No info */) {
    // ** addr: 0x66c234, size: 0x70
    // 0x66c234: EnterFrame
    //     0x66c234: stp             fp, lr, [SP, #-0x10]!
    //     0x66c238: mov             fp, SP
    // 0x66c23c: CheckStackOverflow
    //     0x66c23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66c240: cmp             SP, x16
    //     0x66c244: b.ls            #0x66c29c
    // 0x66c248: ldr             x3, [fp, #0x18]
    // 0x66c24c: LoadField: r2 = r3->field_7
    //     0x66c24c: ldur            w2, [x3, #7]
    // 0x66c250: DecompressPointer r2
    //     0x66c250: add             x2, x2, HEAP, lsl #32
    // 0x66c254: ldr             x0, [fp, #0x10]
    // 0x66c258: r1 = Null
    //     0x66c258: mov             x1, NULL
    // 0x66c25c: cmp             w2, NULL
    // 0x66c260: b.eq            #0x66c280
    // 0x66c264: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66c264: ldur            w4, [x2, #0x17]
    // 0x66c268: DecompressPointer r4
    //     0x66c268: add             x4, x4, HEAP, lsl #32
    // 0x66c26c: r8 = X0
    //     0x66c26c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x66c270: LoadField: r9 = r4->field_7
    //     0x66c270: ldur            x9, [x4, #7]
    // 0x66c274: r3 = Null
    //     0x66c274: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c818] Null
    //     0x66c278: ldr             x3, [x3, #0x818]
    // 0x66c27c: blr             x9
    // 0x66c280: ldr             x1, [fp, #0x18]
    // 0x66c284: ldr             x2, [fp, #0x10]
    // 0x66c288: r0 = _add()
    //     0x66c288: bl              #0x668d24  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_add
    // 0x66c28c: r0 = Null
    //     0x66c28c: mov             x0, NULL
    // 0x66c290: LeaveFrame
    //     0x66c290: mov             SP, fp
    //     0x66c294: ldp             fp, lr, [SP], #0x10
    // 0x66c298: ret
    //     0x66c298: ret             
    // 0x66c29c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66c29c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66c2a0: b               #0x66c248
  }
  _ []=(/* No info */) {
    // ** addr: 0x66d620, size: 0x138
    // 0x66d620: EnterFrame
    //     0x66d620: stp             fp, lr, [SP, #-0x10]!
    //     0x66d624: mov             fp, SP
    // 0x66d628: AllocStack(0x10)
    //     0x66d628: sub             SP, SP, #0x10
    // 0x66d62c: CheckStackOverflow
    //     0x66d62c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66d630: cmp             SP, x16
    //     0x66d634: b.ls            #0x66d74c
    // 0x66d638: ldr             x3, [fp, #0x20]
    // 0x66d63c: LoadField: r2 = r3->field_7
    //     0x66d63c: ldur            w2, [x3, #7]
    // 0x66d640: DecompressPointer r2
    //     0x66d640: add             x2, x2, HEAP, lsl #32
    // 0x66d644: ldr             x0, [fp, #0x10]
    // 0x66d648: r1 = Null
    //     0x66d648: mov             x1, NULL
    // 0x66d64c: cmp             w2, NULL
    // 0x66d650: b.eq            #0x66d670
    // 0x66d654: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66d654: ldur            w4, [x2, #0x17]
    // 0x66d658: DecompressPointer r4
    //     0x66d658: add             x4, x4, HEAP, lsl #32
    // 0x66d65c: r8 = X0
    //     0x66d65c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x66d660: LoadField: r9 = r4->field_7
    //     0x66d660: ldur            x9, [x4, #7]
    // 0x66d664: r3 = Null
    //     0x66d664: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c838] Null
    //     0x66d668: ldr             x3, [x3, #0x838]
    // 0x66d66c: blr             x9
    // 0x66d670: ldr             x3, [fp, #0x20]
    // 0x66d674: LoadField: r0 = r3->field_f
    //     0x66d674: ldur            x0, [x3, #0xf]
    // 0x66d678: ldr             x1, [fp, #0x18]
    // 0x66d67c: r4 = LoadInt32Instr(r1)
    //     0x66d67c: sbfx            x4, x1, #1, #0x1f
    //     0x66d680: tbz             w1, #0, #0x66d688
    //     0x66d684: ldur            x4, [x1, #7]
    // 0x66d688: stur            x4, [fp, #-0x10]
    // 0x66d68c: cmp             x4, x0
    // 0x66d690: b.ge            #0x66d720
    // 0x66d694: ldr             x5, [fp, #0x10]
    // 0x66d698: LoadField: r6 = r3->field_b
    //     0x66d698: ldur            w6, [x3, #0xb]
    // 0x66d69c: DecompressPointer r6
    //     0x66d69c: add             x6, x6, HEAP, lsl #32
    // 0x66d6a0: mov             x0, x5
    // 0x66d6a4: stur            x6, [fp, #-8]
    // 0x66d6a8: r2 = Null
    //     0x66d6a8: mov             x2, NULL
    // 0x66d6ac: r1 = Null
    //     0x66d6ac: mov             x1, NULL
    // 0x66d6b0: branchIfSmi(r0, 0x66d6d8)
    //     0x66d6b0: tbz             w0, #0, #0x66d6d8
    // 0x66d6b4: r4 = LoadClassIdInstr(r0)
    //     0x66d6b4: ldur            x4, [x0, #-1]
    //     0x66d6b8: ubfx            x4, x4, #0xc, #0x14
    // 0x66d6bc: sub             x4, x4, #0x3c
    // 0x66d6c0: cmp             x4, #1
    // 0x66d6c4: b.ls            #0x66d6d8
    // 0x66d6c8: r8 = int
    //     0x66d6c8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x66d6cc: r3 = Null
    //     0x66d6cc: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c848] Null
    //     0x66d6d0: ldr             x3, [x3, #0x848]
    // 0x66d6d4: r0 = int()
    //     0x66d6d4: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x66d6d8: ldur            x2, [fp, #-8]
    // 0x66d6dc: LoadField: r0 = r2->field_13
    //     0x66d6dc: ldur            w0, [x2, #0x13]
    // 0x66d6e0: r1 = LoadInt32Instr(r0)
    //     0x66d6e0: sbfx            x1, x0, #1, #0x1f
    // 0x66d6e4: mov             x0, x1
    // 0x66d6e8: ldur            x1, [fp, #-0x10]
    // 0x66d6ec: cmp             x1, x0
    // 0x66d6f0: b.hs            #0x66d754
    // 0x66d6f4: ldr             x0, [fp, #0x10]
    // 0x66d6f8: r1 = LoadInt32Instr(r0)
    //     0x66d6f8: sbfx            x1, x0, #1, #0x1f
    //     0x66d6fc: tbz             w0, #0, #0x66d704
    //     0x66d700: ldur            x1, [x0, #7]
    // 0x66d704: ldur            x0, [fp, #-0x10]
    // 0x66d708: ArrayStore: r2[r0] = r1  ; TypeUnknown_1
    //     0x66d708: add             x3, x2, x0
    //     0x66d70c: strb            w1, [x3, #0x17]
    // 0x66d710: r0 = Null
    //     0x66d710: mov             x0, NULL
    // 0x66d714: LeaveFrame
    //     0x66d714: mov             SP, fp
    //     0x66d718: ldp             fp, lr, [SP], #0x10
    // 0x66d71c: ret
    //     0x66d71c: ret             
    // 0x66d720: mov             x0, x4
    // 0x66d724: r0 = IndexError()
    //     0x66d724: bl              #0x66d844  ; AllocateIndexErrorStub -> IndexError (size=0x24)
    // 0x66d728: mov             x1, x0
    // 0x66d72c: ldur            x2, [fp, #-0x10]
    // 0x66d730: ldr             x3, [fp, #0x20]
    // 0x66d734: stur            x0, [fp, #-8]
    // 0x66d738: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x66d738: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x66d73c: r0 = IndexError()
    //     0x66d73c: bl              #0x66d758  ; [dart:core] IndexError::IndexError
    // 0x66d740: ldur            x0, [fp, #-8]
    // 0x66d744: r0 = Throw()
    //     0x66d744: bl              #0xec04b8  ; ThrowStub
    // 0x66d748: brk             #0
    // 0x66d74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66d74c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66d750: b               #0x66d638
    // 0x66d754: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66d754: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  dynamic add(dynamic) {
    // ** addr: 0x66dcb0, size: 0x24
    // 0x66dcb0: EnterFrame
    //     0x66dcb0: stp             fp, lr, [SP, #-0x10]!
    //     0x66dcb4: mov             fp, SP
    // 0x66dcb8: ldr             x2, [fp, #0x10]
    // 0x66dcbc: r1 = Function 'add':.
    //     0x66dcbc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43648] AnonymousClosure: (0x668ce0), in [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::add (0x66c234)
    //     0x66dcc0: ldr             x1, [x1, #0x648]
    // 0x66dcc4: r0 = AllocateClosure()
    //     0x66dcc4: bl              #0xec1630  ; AllocateClosureStub
    // 0x66dcc8: LeaveFrame
    //     0x66dcc8: mov             SP, fp
    //     0x66dccc: ldp             fp, lr, [SP], #0x10
    // 0x66dcd0: ret
    //     0x66dcd0: ret             
  }
  X0 [](TypedDataBuffer<X0>, int) {
    // ** addr: 0x66e1dc, size: 0xac
    // 0x66e1dc: EnterFrame
    //     0x66e1dc: stp             fp, lr, [SP, #-0x10]!
    //     0x66e1e0: mov             fp, SP
    // 0x66e1e4: AllocStack(0x10)
    //     0x66e1e4: sub             SP, SP, #0x10
    // 0x66e1e8: CheckStackOverflow
    //     0x66e1e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66e1ec: cmp             SP, x16
    //     0x66e1f0: b.ls            #0x66e27c
    // 0x66e1f4: ldr             x3, [fp, #0x18]
    // 0x66e1f8: LoadField: r0 = r3->field_f
    //     0x66e1f8: ldur            x0, [x3, #0xf]
    // 0x66e1fc: ldr             x1, [fp, #0x10]
    // 0x66e200: r2 = LoadInt32Instr(r1)
    //     0x66e200: sbfx            x2, x1, #1, #0x1f
    //     0x66e204: tbz             w1, #0, #0x66e20c
    //     0x66e208: ldur            x2, [x1, #7]
    // 0x66e20c: stur            x2, [fp, #-8]
    // 0x66e210: cmp             x2, x0
    // 0x66e214: b.ge            #0x66e254
    // 0x66e218: LoadField: r4 = r3->field_b
    //     0x66e218: ldur            w4, [x3, #0xb]
    // 0x66e21c: DecompressPointer r4
    //     0x66e21c: add             x4, x4, HEAP, lsl #32
    // 0x66e220: LoadField: r0 = r4->field_13
    //     0x66e220: ldur            w0, [x4, #0x13]
    // 0x66e224: r1 = LoadInt32Instr(r0)
    //     0x66e224: sbfx            x1, x0, #1, #0x1f
    // 0x66e228: mov             x0, x1
    // 0x66e22c: mov             x1, x2
    // 0x66e230: cmp             x1, x0
    // 0x66e234: b.hs            #0x66e284
    // 0x66e238: ArrayLoad: r0 = r4[r2]  ; List_1
    //     0x66e238: add             x16, x4, x2
    //     0x66e23c: ldrb            w0, [x16, #0x17]
    // 0x66e240: lsl             x1, x0, #1
    // 0x66e244: mov             x0, x1
    // 0x66e248: LeaveFrame
    //     0x66e248: mov             SP, fp
    //     0x66e24c: ldp             fp, lr, [SP], #0x10
    // 0x66e250: ret
    //     0x66e250: ret             
    // 0x66e254: r0 = IndexError()
    //     0x66e254: bl              #0x66d844  ; AllocateIndexErrorStub -> IndexError (size=0x24)
    // 0x66e258: mov             x1, x0
    // 0x66e25c: ldur            x2, [fp, #-8]
    // 0x66e260: ldr             x3, [fp, #0x18]
    // 0x66e264: stur            x0, [fp, #-0x10]
    // 0x66e268: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x66e268: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x66e26c: r0 = IndexError()
    //     0x66e26c: bl              #0x66d758  ; [dart:core] IndexError::IndexError
    // 0x66e270: ldur            x0, [fp, #-0x10]
    // 0x66e274: r0 = Throw()
    //     0x66e274: bl              #0xec04b8  ; ThrowStub
    // 0x66e278: brk             #0
    // 0x66e27c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66e27c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66e280: b               #0x66e1f4
    // 0x66e284: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66e284: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ buffer(/* No info */) {
    // ** addr: 0xcdf61c, size: 0x30
    // 0xcdf61c: EnterFrame
    //     0xcdf61c: stp             fp, lr, [SP, #-0x10]!
    //     0xcdf620: mov             fp, SP
    // 0xcdf624: AllocStack(0x8)
    //     0xcdf624: sub             SP, SP, #8
    // 0xcdf628: LoadField: r0 = r1->field_b
    //     0xcdf628: ldur            w0, [x1, #0xb]
    // 0xcdf62c: DecompressPointer r0
    //     0xcdf62c: add             x0, x0, HEAP, lsl #32
    // 0xcdf630: stur            x0, [fp, #-8]
    // 0xcdf634: r0 = _ByteBuffer()
    //     0xcdf634: bl              #0x60c428  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0xcdf638: ldur            x1, [fp, #-8]
    // 0xcdf63c: StoreField: r0->field_7 = r1
    //     0xcdf63c: stur            w1, [x0, #7]
    // 0xcdf640: LeaveFrame
    //     0xcdf640: mov             SP, fp
    //     0xcdf644: ldp             fp, lr, [SP], #0x10
    // 0xcdf648: ret
    //     0xcdf648: ret             
  }
}

// class id: 7318, size: 0x18, field offset: 0x18
abstract class _IntBuffer extends TypedDataBuffer<dynamic> {
}

// class id: 7319, size: 0x18, field offset: 0x18
class Uint8Buffer extends _IntBuffer {
}
