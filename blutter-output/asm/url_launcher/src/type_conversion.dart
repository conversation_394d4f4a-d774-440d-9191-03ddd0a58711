// lib: , url: package:url_launcher/src/type_conversion.dart

// class id: 1051215, size: 0x8
class :: {

  static _ convertBrowserConfiguration(/* No info */) {
    // ** addr: 0x7d9ec4, size: 0x20
    // 0x7d9ec4: EnterFrame
    //     0x7d9ec4: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9ec8: mov             fp, SP
    // 0x7d9ecc: r0 = InAppBrowserConfiguration()
    //     0x7d9ecc: bl              #0x7d9ee4  ; AllocateInAppBrowserConfigurationStub -> InAppBrowserConfiguration (size=0xc)
    // 0x7d9ed0: r1 = false
    //     0x7d9ed0: add             x1, NULL, #0x30  ; false
    // 0x7d9ed4: StoreField: r0->field_7 = r1
    //     0x7d9ed4: stur            w1, [x0, #7]
    // 0x7d9ed8: LeaveFrame
    //     0x7d9ed8: mov             SP, fp
    //     0x7d9edc: ldp             fp, lr, [SP], #0x10
    // 0x7d9ee0: ret
    //     0x7d9ee0: ret             
  }
  static _ convertWebViewConfiguration(/* No info */) {
    // ** addr: 0x7d9ef0, size: 0x30
    // 0x7d9ef0: EnterFrame
    //     0x7d9ef0: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9ef4: mov             fp, SP
    // 0x7d9ef8: r0 = InAppWebViewConfiguration()
    //     0x7d9ef8: bl              #0x7d9f20  ; AllocateInAppWebViewConfigurationStub -> InAppWebViewConfiguration (size=0x14)
    // 0x7d9efc: r1 = true
    //     0x7d9efc: add             x1, NULL, #0x20  ; true
    // 0x7d9f00: StoreField: r0->field_7 = r1
    //     0x7d9f00: stur            w1, [x0, #7]
    // 0x7d9f04: StoreField: r0->field_b = r1
    //     0x7d9f04: stur            w1, [x0, #0xb]
    // 0x7d9f08: r1 = _ConstMap len:0
    //     0x7d9f08: add             x1, PP, #0x1a, lsl #12  ; [pp+0x1ac48] Map<String, String>(0)
    //     0x7d9f0c: ldr             x1, [x1, #0xc48]
    // 0x7d9f10: StoreField: r0->field_f = r1
    //     0x7d9f10: stur            w1, [x0, #0xf]
    // 0x7d9f14: LeaveFrame
    //     0x7d9f14: mov             SP, fp
    //     0x7d9f18: ldp             fp, lr, [SP], #0x10
    // 0x7d9f1c: ret
    //     0x7d9f1c: ret             
  }
}
