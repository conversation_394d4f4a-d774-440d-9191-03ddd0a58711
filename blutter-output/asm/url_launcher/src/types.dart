// lib: , url: package:url_launcher/src/types.dart

// class id: 1051216, size: 0x8
class :: {
}

// class id: 6763, size: 0x14, field offset: 0x14
enum LaunchMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ec70, size: 0x64
    // 0xc4ec70: EnterFrame
    //     0xc4ec70: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ec74: mov             fp, SP
    // 0xc4ec78: AllocStack(0x10)
    //     0xc4ec78: sub             SP, SP, #0x10
    // 0xc4ec7c: SetupParameters(LaunchMode this /* r1 => r0, fp-0x8 */)
    //     0xc4ec7c: mov             x0, x1
    //     0xc4ec80: stur            x1, [fp, #-8]
    // 0xc4ec84: CheckStackOverflow
    //     0xc4ec84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ec88: cmp             SP, x16
    //     0xc4ec8c: b.ls            #0xc4eccc
    // 0xc4ec90: r1 = Null
    //     0xc4ec90: mov             x1, NULL
    // 0xc4ec94: r2 = 4
    //     0xc4ec94: movz            x2, #0x4
    // 0xc4ec98: r0 = AllocateArray()
    //     0xc4ec98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ec9c: r16 = "LaunchMode."
    //     0xc4ec9c: add             x16, PP, #0x20, lsl #12  ; [pp+0x20aa8] "LaunchMode."
    //     0xc4eca0: ldr             x16, [x16, #0xaa8]
    // 0xc4eca4: StoreField: r0->field_f = r16
    //     0xc4eca4: stur            w16, [x0, #0xf]
    // 0xc4eca8: ldur            x1, [fp, #-8]
    // 0xc4ecac: LoadField: r2 = r1->field_f
    //     0xc4ecac: ldur            w2, [x1, #0xf]
    // 0xc4ecb0: DecompressPointer r2
    //     0xc4ecb0: add             x2, x2, HEAP, lsl #32
    // 0xc4ecb4: StoreField: r0->field_13 = r2
    //     0xc4ecb4: stur            w2, [x0, #0x13]
    // 0xc4ecb8: str             x0, [SP]
    // 0xc4ecbc: r0 = _interpolate()
    //     0xc4ecbc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ecc0: LeaveFrame
    //     0xc4ecc0: mov             SP, fp
    //     0xc4ecc4: ldp             fp, lr, [SP], #0x10
    // 0xc4ecc8: ret
    //     0xc4ecc8: ret             
    // 0xc4eccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4eccc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ecd0: b               #0xc4ec90
  }
}
