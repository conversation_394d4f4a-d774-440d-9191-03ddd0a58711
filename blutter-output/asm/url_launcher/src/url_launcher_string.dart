// lib: , url: package:url_launcher/src/url_launcher_string.dart

// class id: 1051217, size: 0x8
class :: {

  static _ launchUrlString(/* No info */) async {
    // ** addr: 0x7d9cc8, size: 0x1f0
    // 0x7d9cc8: EnterFrame
    //     0x7d9cc8: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9ccc: mov             fp, SP
    // 0x7d9cd0: AllocStack(0x30)
    //     0x7d9cd0: sub             SP, SP, #0x30
    // 0x7d9cd4: SetupParameters(dynamic _ /* r1 => r1, fp-0x18 */, {dynamic mode = Instance_LaunchMode /* r2, fp-0x10 */})
    //     0x7d9cd4: stur            NULL, [fp, #-8]
    //     0x7d9cd8: stur            x1, [fp, #-0x18]
    //     0x7d9cdc: ldur            w0, [x4, #0x13]
    //     0x7d9ce0: ldur            w2, [x4, #0x1f]
    //     0x7d9ce4: add             x2, x2, HEAP, lsl #32
    //     0x7d9ce8: ldr             x16, [PP, #0x1c10]  ; [pp+0x1c10] "mode"
    //     0x7d9cec: cmp             w2, w16
    //     0x7d9cf0: b.ne            #0x7d9d10
    //     0x7d9cf4: ldur            w2, [x4, #0x23]
    //     0x7d9cf8: add             x2, x2, HEAP, lsl #32
    //     0x7d9cfc: sub             w3, w0, w2
    //     0x7d9d00: add             x0, fp, w3, sxtw #2
    //     0x7d9d04: ldr             x0, [x0, #8]
    //     0x7d9d08: mov             x2, x0
    //     0x7d9d0c: b               #0x7d9d18
    //     0x7d9d10: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1abf0] Obj!LaunchMode@e2dec1
    //     0x7d9d14: ldr             x2, [x2, #0xbf0]
    //     0x7d9d18: stur            x2, [fp, #-0x10]
    // 0x7d9d1c: CheckStackOverflow
    //     0x7d9d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d9d20: cmp             SP, x16
    //     0x7d9d24: b.ls            #0x7d9eb0
    // 0x7d9d28: InitAsync() -> Future<bool>
    //     0x7d9d28: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x7d9d2c: bl              #0x661298  ; InitAsyncStub
    // 0x7d9d30: ldur            x0, [fp, #-0x10]
    // 0x7d9d34: r16 = Instance_LaunchMode
    //     0x7d9d34: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1abf8] Obj!LaunchMode@e2dea1
    //     0x7d9d38: ldr             x16, [x16, #0xbf8]
    // 0x7d9d3c: cmp             w0, w16
    // 0x7d9d40: b.eq            #0x7d9d54
    // 0x7d9d44: r16 = Instance_LaunchMode
    //     0x7d9d44: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac00] Obj!LaunchMode@e2de81
    //     0x7d9d48: ldr             x16, [x16, #0xc00]
    // 0x7d9d4c: cmp             w0, w16
    // 0x7d9d50: b.ne            #0x7d9d84
    // 0x7d9d54: ldur            x1, [fp, #-0x18]
    // 0x7d9d58: r2 = "https:"
    //     0x7d9d58: add             x2, PP, #0xc, lsl #12  ; [pp+0xcca8] "https:"
    //     0x7d9d5c: ldr             x2, [x2, #0xca8]
    // 0x7d9d60: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7d9d60: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7d9d64: r0 = startsWith()
    //     0x7d9d64: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0x7d9d68: tbz             w0, #4, #0x7d9d84
    // 0x7d9d6c: ldur            x1, [fp, #-0x18]
    // 0x7d9d70: r2 = "http:"
    //     0x7d9d70: add             x2, PP, #0xc, lsl #12  ; [pp+0xcca0] "http:"
    //     0x7d9d74: ldr             x2, [x2, #0xca0]
    // 0x7d9d78: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7d9d78: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7d9d7c: r0 = startsWith()
    //     0x7d9d7c: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0x7d9d80: tbnz            w0, #4, #0x7d9e70
    // 0x7d9d84: ldur            x0, [fp, #-0x10]
    // 0x7d9d88: r0 = InitLateStaticField(0x614) // [package:url_launcher_platform_interface/src/url_launcher_platform.dart] UrlLauncherPlatform::_instance
    //     0x7d9d88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7d9d8c: ldr             x0, [x0, #0xc28]
    //     0x7d9d90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7d9d94: cmp             w0, w16
    //     0x7d9d98: b.ne            #0x7d9da8
    //     0x7d9d9c: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ac08] Field <UrlLauncherPlatform._instance@681332722>: static late (offset: 0x614)
    //     0x7d9da0: ldr             x2, [x2, #0xc08]
    //     0x7d9da4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x7d9da8: mov             x1, x0
    // 0x7d9dac: ldur            x0, [fp, #-0x10]
    // 0x7d9db0: stur            x1, [fp, #-0x20]
    // 0x7d9db4: LoadField: r2 = r0->field_7
    //     0x7d9db4: ldur            x2, [x0, #7]
    // 0x7d9db8: cmp             x2, #2
    // 0x7d9dbc: b.gt            #0x7d9df4
    // 0x7d9dc0: cmp             x2, #1
    // 0x7d9dc4: b.gt            #0x7d9de8
    // 0x7d9dc8: cmp             x2, #0
    // 0x7d9dcc: b.gt            #0x7d9ddc
    // 0x7d9dd0: r0 = Instance_PreferredLaunchMode
    //     0x7d9dd0: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac10] Obj!PreferredLaunchMode@e2de61
    //     0x7d9dd4: ldr             x0, [x0, #0xc10]
    // 0x7d9dd8: b               #0x7d9e10
    // 0x7d9ddc: r0 = Instance_PreferredLaunchMode
    //     0x7d9ddc: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac18] Obj!PreferredLaunchMode@e2de41
    //     0x7d9de0: ldr             x0, [x0, #0xc18]
    // 0x7d9de4: b               #0x7d9e10
    // 0x7d9de8: r0 = Instance_PreferredLaunchMode
    //     0x7d9de8: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac20] Obj!PreferredLaunchMode@e2de21
    //     0x7d9dec: ldr             x0, [x0, #0xc20]
    // 0x7d9df0: b               #0x7d9e10
    // 0x7d9df4: cmp             x2, #3
    // 0x7d9df8: b.gt            #0x7d9e08
    // 0x7d9dfc: r0 = Instance_PreferredLaunchMode
    //     0x7d9dfc: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac28] Obj!PreferredLaunchMode@e2de01
    //     0x7d9e00: ldr             x0, [x0, #0xc28]
    // 0x7d9e04: b               #0x7d9e10
    // 0x7d9e08: r0 = Instance_PreferredLaunchMode
    //     0x7d9e08: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac30] Obj!PreferredLaunchMode@e2dde1
    //     0x7d9e0c: ldr             x0, [x0, #0xc30]
    // 0x7d9e10: stur            x0, [fp, #-0x10]
    // 0x7d9e14: r0 = convertWebViewConfiguration()
    //     0x7d9e14: bl              #0x7d9ef0  ; [package:url_launcher/src/type_conversion.dart] ::convertWebViewConfiguration
    // 0x7d9e18: stur            x0, [fp, #-0x28]
    // 0x7d9e1c: r0 = convertBrowserConfiguration()
    //     0x7d9e1c: bl              #0x7d9ec4  ; [package:url_launcher/src/type_conversion.dart] ::convertBrowserConfiguration
    // 0x7d9e20: stur            x0, [fp, #-0x30]
    // 0x7d9e24: r0 = LaunchOptions()
    //     0x7d9e24: bl              #0x7d9eb8  ; AllocateLaunchOptionsStub -> LaunchOptions (size=0x18)
    // 0x7d9e28: mov             x1, x0
    // 0x7d9e2c: ldur            x0, [fp, #-0x10]
    // 0x7d9e30: StoreField: r1->field_7 = r0
    //     0x7d9e30: stur            w0, [x1, #7]
    // 0x7d9e34: ldur            x0, [fp, #-0x28]
    // 0x7d9e38: StoreField: r1->field_b = r0
    //     0x7d9e38: stur            w0, [x1, #0xb]
    // 0x7d9e3c: ldur            x0, [fp, #-0x30]
    // 0x7d9e40: StoreField: r1->field_f = r0
    //     0x7d9e40: stur            w0, [x1, #0xf]
    // 0x7d9e44: ldur            x0, [fp, #-0x20]
    // 0x7d9e48: r2 = LoadClassIdInstr(r0)
    //     0x7d9e48: ldur            x2, [x0, #-1]
    //     0x7d9e4c: ubfx            x2, x2, #0xc, #0x14
    // 0x7d9e50: mov             x3, x1
    // 0x7d9e54: mov             x1, x0
    // 0x7d9e58: mov             x0, x2
    // 0x7d9e5c: ldur            x2, [fp, #-0x18]
    // 0x7d9e60: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7d9e60: sub             lr, x0, #1, lsl #12
    //     0x7d9e64: ldr             lr, [x21, lr, lsl #3]
    //     0x7d9e68: blr             lr
    // 0x7d9e6c: r0 = ReturnAsync()
    //     0x7d9e6c: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7d9e70: ldur            x0, [fp, #-0x18]
    // 0x7d9e74: r0 = ArgumentError()
    //     0x7d9e74: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x7d9e78: mov             x1, x0
    // 0x7d9e7c: r0 = "urlString"
    //     0x7d9e7c: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac38] "urlString"
    //     0x7d9e80: ldr             x0, [x0, #0xc38]
    // 0x7d9e84: StoreField: r1->field_13 = r0
    //     0x7d9e84: stur            w0, [x1, #0x13]
    // 0x7d9e88: r0 = "To use an in-app web view, you must provide an http(s) URL."
    //     0x7d9e88: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac40] "To use an in-app web view, you must provide an http(s) URL."
    //     0x7d9e8c: ldr             x0, [x0, #0xc40]
    // 0x7d9e90: ArrayStore: r1[0] = r0  ; List_4
    //     0x7d9e90: stur            w0, [x1, #0x17]
    // 0x7d9e94: ldur            x0, [fp, #-0x18]
    // 0x7d9e98: StoreField: r1->field_f = r0
    //     0x7d9e98: stur            w0, [x1, #0xf]
    // 0x7d9e9c: r0 = true
    //     0x7d9e9c: add             x0, NULL, #0x20  ; true
    // 0x7d9ea0: StoreField: r1->field_b = r0
    //     0x7d9ea0: stur            w0, [x1, #0xb]
    // 0x7d9ea4: mov             x0, x1
    // 0x7d9ea8: r0 = Throw()
    //     0x7d9ea8: bl              #0xec04b8  ; ThrowStub
    // 0x7d9eac: brk             #0
    // 0x7d9eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d9eb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d9eb4: b               #0x7d9d28
  }
  static _ canLaunchUrlString(/* No info */) async {
    // ** addr: 0x7da07c, size: 0x118
    // 0x7da07c: EnterFrame
    //     0x7da07c: stp             fp, lr, [SP, #-0x10]!
    //     0x7da080: mov             fp, SP
    // 0x7da084: AllocStack(0x38)
    //     0x7da084: sub             SP, SP, #0x38
    // 0x7da088: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */)
    //     0x7da088: stur            NULL, [fp, #-8]
    //     0x7da08c: mov             x2, x1
    //     0x7da090: stur            x1, [fp, #-0x10]
    // 0x7da094: CheckStackOverflow
    //     0x7da094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7da098: cmp             SP, x16
    //     0x7da09c: b.ls            #0x7da18c
    // 0x7da0a0: InitAsync() -> Future<bool>
    //     0x7da0a0: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x7da0a4: bl              #0x661298  ; InitAsyncStub
    // 0x7da0a8: r0 = InitLateStaticField(0x614) // [package:url_launcher_platform_interface/src/url_launcher_platform.dart] UrlLauncherPlatform::_instance
    //     0x7da0a8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7da0ac: ldr             x0, [x0, #0xc28]
    //     0x7da0b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7da0b4: cmp             w0, w16
    //     0x7da0b8: b.ne            #0x7da0c8
    //     0x7da0bc: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ac08] Field <UrlLauncherPlatform._instance@681332722>: static late (offset: 0x614)
    //     0x7da0c0: ldr             x2, [x2, #0xc08]
    //     0x7da0c4: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x7da0c8: r1 = LoadClassIdInstr(r0)
    //     0x7da0c8: ldur            x1, [x0, #-1]
    //     0x7da0cc: ubfx            x1, x1, #0xc, #0x14
    // 0x7da0d0: r17 = 5865
    //     0x7da0d0: movz            x17, #0x16e9
    // 0x7da0d4: cmp             x1, x17
    // 0x7da0d8: b.ne            #0x7da164
    // 0x7da0dc: ldur            x0, [fp, #-0x10]
    // 0x7da0e0: r1 = Null
    //     0x7da0e0: mov             x1, NULL
    // 0x7da0e4: r2 = 4
    //     0x7da0e4: movz            x2, #0x4
    // 0x7da0e8: r0 = AllocateArray()
    //     0x7da0e8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7da0ec: r16 = "url"
    //     0x7da0ec: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x7da0f0: ldr             x16, [x16, #0xd78]
    // 0x7da0f4: StoreField: r0->field_f = r16
    //     0x7da0f4: stur            w16, [x0, #0xf]
    // 0x7da0f8: ldur            x2, [fp, #-0x10]
    // 0x7da0fc: StoreField: r0->field_13 = r2
    //     0x7da0fc: stur            w2, [x0, #0x13]
    // 0x7da100: r16 = <String, Object>
    //     0x7da100: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0x7da104: ldr             x16, [x16, #0x790]
    // 0x7da108: stp             x0, x16, [SP]
    // 0x7da10c: r0 = Map._fromLiteral()
    //     0x7da10c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7da110: r16 = <bool>
    //     0x7da110: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x7da114: r30 = Instance_MethodChannel
    //     0x7da114: add             lr, PP, #0x21, lsl #12  ; [pp+0x21f80] Obj!MethodChannel@e11051
    //     0x7da118: ldr             lr, [lr, #0xf80]
    // 0x7da11c: stp             lr, x16, [SP, #0x10]
    // 0x7da120: r16 = "canLaunch"
    //     0x7da120: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbf0] "canLaunch"
    //     0x7da124: ldr             x16, [x16, #0xbf0]
    // 0x7da128: stp             x0, x16, [SP]
    // 0x7da12c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x7da12c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x7da130: r0 = invokeMethod()
    //     0x7da130: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x7da134: r1 = Function '<anonymous closure>':.
    //     0x7da134: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbf8] AnonymousClosure: (0x7da194), in [package:url_launcher_platform_interface/method_channel_url_launcher.dart] MethodChannelUrlLauncher::launch (0x7da1b0)
    //     0x7da138: ldr             x1, [x1, #0xbf8]
    // 0x7da13c: r2 = Null
    //     0x7da13c: mov             x2, NULL
    // 0x7da140: stur            x0, [fp, #-0x18]
    // 0x7da144: r0 = AllocateClosure()
    //     0x7da144: bl              #0xec1630  ; AllocateClosureStub
    // 0x7da148: r16 = <bool>
    //     0x7da148: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x7da14c: ldur            lr, [fp, #-0x18]
    // 0x7da150: stp             lr, x16, [SP, #8]
    // 0x7da154: str             x0, [SP]
    // 0x7da158: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7da158: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7da15c: r0 = then()
    //     0x7da15c: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x7da160: b               #0x7da188
    // 0x7da164: ldur            x2, [fp, #-0x10]
    // 0x7da168: r1 = LoadClassIdInstr(r0)
    //     0x7da168: ldur            x1, [x0, #-1]
    //     0x7da16c: ubfx            x1, x1, #0xc, #0x14
    // 0x7da170: mov             x16, x0
    // 0x7da174: mov             x0, x1
    // 0x7da178: mov             x1, x16
    // 0x7da17c: r0 = GDT[cid_x0 + -0x46]()
    //     0x7da17c: sub             lr, x0, #0x46
    //     0x7da180: ldr             lr, [x21, lr, lsl #3]
    //     0x7da184: blr             lr
    // 0x7da188: r0 = ReturnAsync()
    //     0x7da188: b               #0x6576a4  ; ReturnAsyncStub
    // 0x7da18c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7da18c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7da190: b               #0x7da0a0
  }
}
