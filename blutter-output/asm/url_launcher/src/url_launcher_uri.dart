// lib: , url: package:url_launcher/src/url_launcher_uri.dart

// class id: 1051218, size: 0x8
class :: {

  static _ launchUrl(/* No info */) async {
    // ** addr: 0x97d304, size: 0xe4
    // 0x97d304: EnterFrame
    //     0x97d304: stp             fp, lr, [SP, #-0x10]!
    //     0x97d308: mov             fp, SP
    // 0x97d30c: AllocStack(0x30)
    //     0x97d30c: sub             SP, SP, #0x30
    // 0x97d310: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x97d310: stur            NULL, [fp, #-8]
    //     0x97d314: stur            x1, [fp, #-0x10]
    // 0x97d318: CheckStackOverflow
    //     0x97d318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97d31c: cmp             SP, x16
    //     0x97d320: b.ls            #0x97d3e0
    // 0x97d324: InitAsync() -> Future<bool>
    //     0x97d324: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x97d328: bl              #0x661298  ; InitAsyncStub
    // 0x97d32c: r0 = InitLateStaticField(0x614) // [package:url_launcher_platform_interface/src/url_launcher_platform.dart] UrlLauncherPlatform::_instance
    //     0x97d32c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97d330: ldr             x0, [x0, #0xc28]
    //     0x97d334: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x97d338: cmp             w0, w16
    //     0x97d33c: b.ne            #0x97d34c
    //     0x97d340: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ac08] Field <UrlLauncherPlatform._instance@681332722>: static late (offset: 0x614)
    //     0x97d344: ldr             x2, [x2, #0xc08]
    //     0x97d348: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x97d34c: mov             x1, x0
    // 0x97d350: ldur            x0, [fp, #-0x10]
    // 0x97d354: stur            x1, [fp, #-0x18]
    // 0x97d358: r2 = LoadClassIdInstr(r0)
    //     0x97d358: ldur            x2, [x0, #-1]
    //     0x97d35c: ubfx            x2, x2, #0xc, #0x14
    // 0x97d360: str             x0, [SP]
    // 0x97d364: mov             x0, x2
    // 0x97d368: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x97d368: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x97d36c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x97d36c: movz            x17, #0x2b03
    //     0x97d370: add             lr, x0, x17
    //     0x97d374: ldr             lr, [x21, lr, lsl #3]
    //     0x97d378: blr             lr
    // 0x97d37c: stur            x0, [fp, #-0x10]
    // 0x97d380: r0 = convertWebViewConfiguration()
    //     0x97d380: bl              #0x7d9ef0  ; [package:url_launcher/src/type_conversion.dart] ::convertWebViewConfiguration
    // 0x97d384: stur            x0, [fp, #-0x20]
    // 0x97d388: r0 = convertBrowserConfiguration()
    //     0x97d388: bl              #0x7d9ec4  ; [package:url_launcher/src/type_conversion.dart] ::convertBrowserConfiguration
    // 0x97d38c: stur            x0, [fp, #-0x28]
    // 0x97d390: r0 = LaunchOptions()
    //     0x97d390: bl              #0x7d9eb8  ; AllocateLaunchOptionsStub -> LaunchOptions (size=0x18)
    // 0x97d394: mov             x1, x0
    // 0x97d398: r0 = Instance_PreferredLaunchMode
    //     0x97d398: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac10] Obj!PreferredLaunchMode@e2de61
    //     0x97d39c: ldr             x0, [x0, #0xc10]
    // 0x97d3a0: StoreField: r1->field_7 = r0
    //     0x97d3a0: stur            w0, [x1, #7]
    // 0x97d3a4: ldur            x0, [fp, #-0x20]
    // 0x97d3a8: StoreField: r1->field_b = r0
    //     0x97d3a8: stur            w0, [x1, #0xb]
    // 0x97d3ac: ldur            x0, [fp, #-0x28]
    // 0x97d3b0: StoreField: r1->field_f = r0
    //     0x97d3b0: stur            w0, [x1, #0xf]
    // 0x97d3b4: ldur            x0, [fp, #-0x18]
    // 0x97d3b8: r2 = LoadClassIdInstr(r0)
    //     0x97d3b8: ldur            x2, [x0, #-1]
    //     0x97d3bc: ubfx            x2, x2, #0xc, #0x14
    // 0x97d3c0: mov             x3, x1
    // 0x97d3c4: mov             x1, x0
    // 0x97d3c8: mov             x0, x2
    // 0x97d3cc: ldur            x2, [fp, #-0x10]
    // 0x97d3d0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x97d3d0: sub             lr, x0, #1, lsl #12
    //     0x97d3d4: ldr             lr, [x21, lr, lsl #3]
    //     0x97d3d8: blr             lr
    // 0x97d3dc: r0 = ReturnAsync()
    //     0x97d3dc: b               #0x6576a4  ; ReturnAsyncStub
    // 0x97d3e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97d3e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97d3e4: b               #0x97d324
  }
}
