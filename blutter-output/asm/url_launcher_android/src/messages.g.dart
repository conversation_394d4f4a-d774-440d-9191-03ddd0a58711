// lib: , url: package:url_launcher_android/src/messages.g.dart

// class id: 1051219, size: 0x8
class :: {
}

// class id: 419, size: 0x10, field offset: 0x8
class UrlLauncher<PERSON><PERSON> extends Object {

  _ canLaunchUrl(/* No info */) async {
    // ** addr: 0xd3d458, size: 0x35c
    // 0xd3d458: EnterFrame
    //     0xd3d458: stp             fp, lr, [SP, #-0x10]!
    //     0xd3d45c: mov             fp, SP
    // 0xd3d460: AllocStack(0x38)
    //     0xd3d460: sub             SP, SP, #0x38
    // 0xd3d464: SetupParameters(UrlLauncherApi this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xd3d464: stur            NULL, [fp, #-8]
    //     0xd3d468: stur            x1, [fp, #-0x10]
    //     0xd3d46c: stur            x2, [fp, #-0x18]
    // 0xd3d470: CheckStackOverflow
    //     0xd3d470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3d474: cmp             SP, x16
    //     0xd3d478: b.ls            #0xd3d7a4
    // 0xd3d47c: InitAsync() -> Future<bool>
    //     0xd3d47c: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0xd3d480: bl              #0x661298  ; InitAsyncStub
    // 0xd3d484: r1 = Null
    //     0xd3d484: mov             x1, NULL
    // 0xd3d488: r2 = 4
    //     0xd3d488: movz            x2, #0x4
    // 0xd3d48c: r0 = AllocateArray()
    //     0xd3d48c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd3d490: r16 = "dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.canLaunchUrl"
    //     0xd3d490: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3b2e0] "dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.canLaunchUrl"
    //     0xd3d494: ldr             x16, [x16, #0x2e0]
    // 0xd3d498: StoreField: r0->field_f = r16
    //     0xd3d498: stur            w16, [x0, #0xf]
    // 0xd3d49c: ldur            x1, [fp, #-0x10]
    // 0xd3d4a0: LoadField: r2 = r1->field_b
    //     0xd3d4a0: ldur            w2, [x1, #0xb]
    // 0xd3d4a4: DecompressPointer r2
    //     0xd3d4a4: add             x2, x2, HEAP, lsl #32
    // 0xd3d4a8: StoreField: r0->field_13 = r2
    //     0xd3d4a8: stur            w2, [x0, #0x13]
    // 0xd3d4ac: str             x0, [SP]
    // 0xd3d4b0: r0 = _interpolate()
    //     0xd3d4b0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xd3d4b4: r1 = <Object?>
    //     0xd3d4b4: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xd3d4b8: stur            x0, [fp, #-0x10]
    // 0xd3d4bc: r0 = BasicMessageChannel()
    //     0xd3d4bc: bl              #0x7edc64  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xd3d4c0: mov             x3, x0
    // 0xd3d4c4: ldur            x0, [fp, #-0x10]
    // 0xd3d4c8: stur            x3, [fp, #-0x20]
    // 0xd3d4cc: StoreField: r3->field_b = r0
    //     0xd3d4cc: stur            w0, [x3, #0xb]
    // 0xd3d4d0: r1 = Instance__PigeonCodec
    //     0xd3d4d0: add             x1, PP, #0x22, lsl #12  ; [pp+0x22750] Obj!_PigeonCodec@e259a1
    //     0xd3d4d4: ldr             x1, [x1, #0x750]
    // 0xd3d4d8: StoreField: r3->field_f = r1
    //     0xd3d4d8: stur            w1, [x3, #0xf]
    // 0xd3d4dc: r1 = Null
    //     0xd3d4dc: mov             x1, NULL
    // 0xd3d4e0: r2 = 2
    //     0xd3d4e0: movz            x2, #0x2
    // 0xd3d4e4: r0 = AllocateArray()
    //     0xd3d4e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd3d4e8: mov             x2, x0
    // 0xd3d4ec: ldur            x0, [fp, #-0x18]
    // 0xd3d4f0: stur            x2, [fp, #-0x28]
    // 0xd3d4f4: StoreField: r2->field_f = r0
    //     0xd3d4f4: stur            w0, [x2, #0xf]
    // 0xd3d4f8: r1 = <Object?>
    //     0xd3d4f8: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xd3d4fc: r0 = AllocateGrowableArray()
    //     0xd3d4fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xd3d500: mov             x1, x0
    // 0xd3d504: ldur            x0, [fp, #-0x28]
    // 0xd3d508: StoreField: r1->field_f = r0
    //     0xd3d508: stur            w0, [x1, #0xf]
    // 0xd3d50c: r0 = 2
    //     0xd3d50c: movz            x0, #0x2
    // 0xd3d510: StoreField: r1->field_b = r0
    //     0xd3d510: stur            w0, [x1, #0xb]
    // 0xd3d514: mov             x2, x1
    // 0xd3d518: ldur            x1, [fp, #-0x20]
    // 0xd3d51c: r0 = send()
    //     0xd3d51c: bl              #0x65755c  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xd3d520: mov             x1, x0
    // 0xd3d524: stur            x1, [fp, #-0x18]
    // 0xd3d528: r0 = Await()
    //     0xd3d528: bl              #0x661044  ; AwaitStub
    // 0xd3d52c: mov             x3, x0
    // 0xd3d530: r2 = Null
    //     0xd3d530: mov             x2, NULL
    // 0xd3d534: r1 = Null
    //     0xd3d534: mov             x1, NULL
    // 0xd3d538: stur            x3, [fp, #-0x18]
    // 0xd3d53c: r4 = 60
    //     0xd3d53c: movz            x4, #0x3c
    // 0xd3d540: branchIfSmi(r0, 0xd3d54c)
    //     0xd3d540: tbz             w0, #0, #0xd3d54c
    // 0xd3d544: r4 = LoadClassIdInstr(r0)
    //     0xd3d544: ldur            x4, [x0, #-1]
    //     0xd3d548: ubfx            x4, x4, #0xc, #0x14
    // 0xd3d54c: sub             x4, x4, #0x5a
    // 0xd3d550: cmp             x4, #2
    // 0xd3d554: b.ls            #0xd3d568
    // 0xd3d558: r8 = List<Object?>?
    //     0xd3d558: ldr             x8, [PP, #0x320]  ; [pp+0x320] Type: List<Object?>?
    // 0xd3d55c: r3 = Null
    //     0xd3d55c: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3b2e8] Null
    //     0xd3d560: ldr             x3, [x3, #0x2e8]
    // 0xd3d564: r0 = List<Object?>?()
    //     0xd3d564: bl              #0x624060  ; IsType_List<Object?>?_Stub
    // 0xd3d568: ldur            x1, [fp, #-0x18]
    // 0xd3d56c: cmp             w1, NULL
    // 0xd3d570: b.eq            #0xd3d63c
    // 0xd3d574: r0 = LoadClassIdInstr(r1)
    //     0xd3d574: ldur            x0, [x1, #-1]
    //     0xd3d578: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d57c: str             x1, [SP]
    // 0xd3d580: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd3d580: movz            x17, #0xc834
    //     0xd3d584: add             lr, x0, x17
    //     0xd3d588: ldr             lr, [x21, lr, lsl #3]
    //     0xd3d58c: blr             lr
    // 0xd3d590: r1 = LoadInt32Instr(r0)
    //     0xd3d590: sbfx            x1, x0, #1, #0x1f
    //     0xd3d594: tbz             w0, #0, #0xd3d59c
    //     0xd3d598: ldur            x1, [x0, #7]
    // 0xd3d59c: cmp             x1, #1
    // 0xd3d5a0: b.gt            #0xd3d64c
    // 0xd3d5a4: ldur            x1, [fp, #-0x18]
    // 0xd3d5a8: r0 = LoadClassIdInstr(r1)
    //     0xd3d5a8: ldur            x0, [x1, #-1]
    //     0xd3d5ac: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d5b0: stp             xzr, x1, [SP]
    // 0xd3d5b4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd3d5b4: movz            x17, #0x3037
    //     0xd3d5b8: movk            x17, #0x1, lsl #16
    //     0xd3d5bc: add             lr, x0, x17
    //     0xd3d5c0: ldr             lr, [x21, lr, lsl #3]
    //     0xd3d5c4: blr             lr
    // 0xd3d5c8: cmp             w0, NULL
    // 0xd3d5cc: b.eq            #0xd3d778
    // 0xd3d5d0: ldur            x1, [fp, #-0x18]
    // 0xd3d5d4: r0 = LoadClassIdInstr(r1)
    //     0xd3d5d4: ldur            x0, [x1, #-1]
    //     0xd3d5d8: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d5dc: stp             xzr, x1, [SP]
    // 0xd3d5e0: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd3d5e0: movz            x17, #0x3037
    //     0xd3d5e4: movk            x17, #0x1, lsl #16
    //     0xd3d5e8: add             lr, x0, x17
    //     0xd3d5ec: ldr             lr, [x21, lr, lsl #3]
    //     0xd3d5f0: blr             lr
    // 0xd3d5f4: mov             x3, x0
    // 0xd3d5f8: r2 = Null
    //     0xd3d5f8: mov             x2, NULL
    // 0xd3d5fc: r1 = Null
    //     0xd3d5fc: mov             x1, NULL
    // 0xd3d600: stur            x3, [fp, #-0x20]
    // 0xd3d604: r4 = 60
    //     0xd3d604: movz            x4, #0x3c
    // 0xd3d608: branchIfSmi(r0, 0xd3d614)
    //     0xd3d608: tbz             w0, #0, #0xd3d614
    // 0xd3d60c: r4 = LoadClassIdInstr(r0)
    //     0xd3d60c: ldur            x4, [x0, #-1]
    //     0xd3d610: ubfx            x4, x4, #0xc, #0x14
    // 0xd3d614: cmp             x4, #0x3f
    // 0xd3d618: b.eq            #0xd3d62c
    // 0xd3d61c: r8 = bool?
    //     0xd3d61c: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xd3d620: r3 = Null
    //     0xd3d620: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3b2f8] Null
    //     0xd3d624: ldr             x3, [x3, #0x2f8]
    // 0xd3d628: r0 = bool?()
    //     0xd3d628: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xd3d62c: ldur            x0, [fp, #-0x20]
    // 0xd3d630: cmp             w0, NULL
    // 0xd3d634: b.eq            #0xd3d7ac
    // 0xd3d638: r0 = ReturnAsyncNotFuture()
    //     0xd3d638: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xd3d63c: ldur            x1, [fp, #-0x10]
    // 0xd3d640: r0 = _createConnectionError()
    //     0xd3d640: bl              #0x7edbec  ; [package:webview_flutter_android/src/android_webkit.g.dart] ::_createConnectionError
    // 0xd3d644: r0 = Throw()
    //     0xd3d644: bl              #0xec04b8  ; ThrowStub
    // 0xd3d648: brk             #0
    // 0xd3d64c: ldur            x1, [fp, #-0x18]
    // 0xd3d650: r0 = LoadClassIdInstr(r1)
    //     0xd3d650: ldur            x0, [x1, #-1]
    //     0xd3d654: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d658: stp             xzr, x1, [SP]
    // 0xd3d65c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd3d65c: movz            x17, #0x3037
    //     0xd3d660: movk            x17, #0x1, lsl #16
    //     0xd3d664: add             lr, x0, x17
    //     0xd3d668: ldr             lr, [x21, lr, lsl #3]
    //     0xd3d66c: blr             lr
    // 0xd3d670: mov             x3, x0
    // 0xd3d674: stur            x3, [fp, #-0x10]
    // 0xd3d678: cmp             w3, NULL
    // 0xd3d67c: b.eq            #0xd3d7b0
    // 0xd3d680: mov             x0, x3
    // 0xd3d684: r2 = Null
    //     0xd3d684: mov             x2, NULL
    // 0xd3d688: r1 = Null
    //     0xd3d688: mov             x1, NULL
    // 0xd3d68c: r4 = 60
    //     0xd3d68c: movz            x4, #0x3c
    // 0xd3d690: branchIfSmi(r0, 0xd3d69c)
    //     0xd3d690: tbz             w0, #0, #0xd3d69c
    // 0xd3d694: r4 = LoadClassIdInstr(r0)
    //     0xd3d694: ldur            x4, [x0, #-1]
    //     0xd3d698: ubfx            x4, x4, #0xc, #0x14
    // 0xd3d69c: sub             x4, x4, #0x5e
    // 0xd3d6a0: cmp             x4, #1
    // 0xd3d6a4: b.ls            #0xd3d6b8
    // 0xd3d6a8: r8 = String
    //     0xd3d6a8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xd3d6ac: r3 = Null
    //     0xd3d6ac: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3b308] Null
    //     0xd3d6b0: ldr             x3, [x3, #0x308]
    // 0xd3d6b4: r0 = String()
    //     0xd3d6b4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xd3d6b8: ldur            x1, [fp, #-0x18]
    // 0xd3d6bc: r0 = LoadClassIdInstr(r1)
    //     0xd3d6bc: ldur            x0, [x1, #-1]
    //     0xd3d6c0: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d6c4: r16 = 2
    //     0xd3d6c4: movz            x16, #0x2
    // 0xd3d6c8: stp             x16, x1, [SP]
    // 0xd3d6cc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd3d6cc: movz            x17, #0x3037
    //     0xd3d6d0: movk            x17, #0x1, lsl #16
    //     0xd3d6d4: add             lr, x0, x17
    //     0xd3d6d8: ldr             lr, [x21, lr, lsl #3]
    //     0xd3d6dc: blr             lr
    // 0xd3d6e0: mov             x3, x0
    // 0xd3d6e4: r2 = Null
    //     0xd3d6e4: mov             x2, NULL
    // 0xd3d6e8: r1 = Null
    //     0xd3d6e8: mov             x1, NULL
    // 0xd3d6ec: stur            x3, [fp, #-0x20]
    // 0xd3d6f0: r4 = 60
    //     0xd3d6f0: movz            x4, #0x3c
    // 0xd3d6f4: branchIfSmi(r0, 0xd3d700)
    //     0xd3d6f4: tbz             w0, #0, #0xd3d700
    // 0xd3d6f8: r4 = LoadClassIdInstr(r0)
    //     0xd3d6f8: ldur            x4, [x0, #-1]
    //     0xd3d6fc: ubfx            x4, x4, #0xc, #0x14
    // 0xd3d700: sub             x4, x4, #0x5e
    // 0xd3d704: cmp             x4, #1
    // 0xd3d708: b.ls            #0xd3d71c
    // 0xd3d70c: r8 = String?
    //     0xd3d70c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xd3d710: r3 = Null
    //     0xd3d710: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3b318] Null
    //     0xd3d714: ldr             x3, [x3, #0x318]
    // 0xd3d718: r0 = String?()
    //     0xd3d718: bl              #0x600324  ; IsType_String?_Stub
    // 0xd3d71c: ldur            x0, [fp, #-0x18]
    // 0xd3d720: r1 = LoadClassIdInstr(r0)
    //     0xd3d720: ldur            x1, [x0, #-1]
    //     0xd3d724: ubfx            x1, x1, #0xc, #0x14
    // 0xd3d728: r16 = 4
    //     0xd3d728: movz            x16, #0x4
    // 0xd3d72c: stp             x16, x0, [SP]
    // 0xd3d730: mov             x0, x1
    // 0xd3d734: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd3d734: movz            x17, #0x3037
    //     0xd3d738: movk            x17, #0x1, lsl #16
    //     0xd3d73c: add             lr, x0, x17
    //     0xd3d740: ldr             lr, [x21, lr, lsl #3]
    //     0xd3d744: blr             lr
    // 0xd3d748: stur            x0, [fp, #-0x18]
    // 0xd3d74c: r0 = PlatformException()
    //     0xd3d74c: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xd3d750: mov             x1, x0
    // 0xd3d754: ldur            x0, [fp, #-0x10]
    // 0xd3d758: StoreField: r1->field_7 = r0
    //     0xd3d758: stur            w0, [x1, #7]
    // 0xd3d75c: ldur            x0, [fp, #-0x20]
    // 0xd3d760: StoreField: r1->field_b = r0
    //     0xd3d760: stur            w0, [x1, #0xb]
    // 0xd3d764: ldur            x0, [fp, #-0x18]
    // 0xd3d768: StoreField: r1->field_f = r0
    //     0xd3d768: stur            w0, [x1, #0xf]
    // 0xd3d76c: mov             x0, x1
    // 0xd3d770: r0 = Throw()
    //     0xd3d770: bl              #0xec04b8  ; ThrowStub
    // 0xd3d774: brk             #0
    // 0xd3d778: r0 = PlatformException()
    //     0xd3d778: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xd3d77c: mov             x1, x0
    // 0xd3d780: r0 = "null-error"
    //     0xd3d780: add             x0, PP, #0x12, lsl #12  ; [pp+0x12678] "null-error"
    //     0xd3d784: ldr             x0, [x0, #0x678]
    // 0xd3d788: StoreField: r1->field_7 = r0
    //     0xd3d788: stur            w0, [x1, #7]
    // 0xd3d78c: r0 = "Host platform returned null value for non-null return value."
    //     0xd3d78c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12680] "Host platform returned null value for non-null return value."
    //     0xd3d790: ldr             x0, [x0, #0x680]
    // 0xd3d794: StoreField: r1->field_b = r0
    //     0xd3d794: stur            w0, [x1, #0xb]
    // 0xd3d798: mov             x0, x1
    // 0xd3d79c: r0 = Throw()
    //     0xd3d79c: bl              #0xec04b8  ; ThrowStub
    // 0xd3d7a0: brk             #0
    // 0xd3d7a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3d7a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3d7a8: b               #0xd3d47c
    // 0xd3d7ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd3d7ac: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd3d7b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd3d7b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ launchUrl(/* No info */) async {
    // ** addr: 0xd74b18, size: 0x368
    // 0xd74b18: EnterFrame
    //     0xd74b18: stp             fp, lr, [SP, #-0x10]!
    //     0xd74b1c: mov             fp, SP
    // 0xd74b20: AllocStack(0x38)
    //     0xd74b20: sub             SP, SP, #0x38
    // 0xd74b24: SetupParameters(UrlLauncherApi this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xd74b24: stur            NULL, [fp, #-8]
    //     0xd74b28: stur            x1, [fp, #-0x10]
    //     0xd74b2c: stur            x2, [fp, #-0x18]
    // 0xd74b30: CheckStackOverflow
    //     0xd74b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd74b34: cmp             SP, x16
    //     0xd74b38: b.ls            #0xd74e70
    // 0xd74b3c: InitAsync() -> Future<bool>
    //     0xd74b3c: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0xd74b40: bl              #0x661298  ; InitAsyncStub
    // 0xd74b44: r1 = Null
    //     0xd74b44: mov             x1, NULL
    // 0xd74b48: r2 = 4
    //     0xd74b48: movz            x2, #0x4
    // 0xd74b4c: r0 = AllocateArray()
    //     0xd74b4c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd74b50: r16 = "dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.launchUrl"
    //     0xd74b50: add             x16, PP, #0x22, lsl #12  ; [pp+0x22748] "dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.launchUrl"
    //     0xd74b54: ldr             x16, [x16, #0x748]
    // 0xd74b58: StoreField: r0->field_f = r16
    //     0xd74b58: stur            w16, [x0, #0xf]
    // 0xd74b5c: ldur            x1, [fp, #-0x10]
    // 0xd74b60: LoadField: r2 = r1->field_b
    //     0xd74b60: ldur            w2, [x1, #0xb]
    // 0xd74b64: DecompressPointer r2
    //     0xd74b64: add             x2, x2, HEAP, lsl #32
    // 0xd74b68: StoreField: r0->field_13 = r2
    //     0xd74b68: stur            w2, [x0, #0x13]
    // 0xd74b6c: str             x0, [SP]
    // 0xd74b70: r0 = _interpolate()
    //     0xd74b70: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xd74b74: r1 = <Object?>
    //     0xd74b74: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xd74b78: stur            x0, [fp, #-0x10]
    // 0xd74b7c: r0 = BasicMessageChannel()
    //     0xd74b7c: bl              #0x7edc64  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xd74b80: mov             x3, x0
    // 0xd74b84: ldur            x0, [fp, #-0x10]
    // 0xd74b88: stur            x3, [fp, #-0x20]
    // 0xd74b8c: StoreField: r3->field_b = r0
    //     0xd74b8c: stur            w0, [x3, #0xb]
    // 0xd74b90: r1 = Instance__PigeonCodec
    //     0xd74b90: add             x1, PP, #0x22, lsl #12  ; [pp+0x22750] Obj!_PigeonCodec@e259a1
    //     0xd74b94: ldr             x1, [x1, #0x750]
    // 0xd74b98: StoreField: r3->field_f = r1
    //     0xd74b98: stur            w1, [x3, #0xf]
    // 0xd74b9c: r1 = Null
    //     0xd74b9c: mov             x1, NULL
    // 0xd74ba0: r2 = 4
    //     0xd74ba0: movz            x2, #0x4
    // 0xd74ba4: r0 = AllocateArray()
    //     0xd74ba4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd74ba8: mov             x2, x0
    // 0xd74bac: ldur            x0, [fp, #-0x18]
    // 0xd74bb0: stur            x2, [fp, #-0x28]
    // 0xd74bb4: StoreField: r2->field_f = r0
    //     0xd74bb4: stur            w0, [x2, #0xf]
    // 0xd74bb8: r16 = _ConstMap len:0
    //     0xd74bb8: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac48] Map<String, String>(0)
    //     0xd74bbc: ldr             x16, [x16, #0xc48]
    // 0xd74bc0: StoreField: r2->field_13 = r16
    //     0xd74bc0: stur            w16, [x2, #0x13]
    // 0xd74bc4: r1 = <Object?>
    //     0xd74bc4: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xd74bc8: r0 = AllocateGrowableArray()
    //     0xd74bc8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xd74bcc: mov             x1, x0
    // 0xd74bd0: ldur            x0, [fp, #-0x28]
    // 0xd74bd4: StoreField: r1->field_f = r0
    //     0xd74bd4: stur            w0, [x1, #0xf]
    // 0xd74bd8: r0 = 4
    //     0xd74bd8: movz            x0, #0x4
    // 0xd74bdc: StoreField: r1->field_b = r0
    //     0xd74bdc: stur            w0, [x1, #0xb]
    // 0xd74be0: mov             x2, x1
    // 0xd74be4: ldur            x1, [fp, #-0x20]
    // 0xd74be8: r0 = send()
    //     0xd74be8: bl              #0x65755c  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xd74bec: mov             x1, x0
    // 0xd74bf0: stur            x1, [fp, #-0x18]
    // 0xd74bf4: r0 = Await()
    //     0xd74bf4: bl              #0x661044  ; AwaitStub
    // 0xd74bf8: mov             x3, x0
    // 0xd74bfc: r2 = Null
    //     0xd74bfc: mov             x2, NULL
    // 0xd74c00: r1 = Null
    //     0xd74c00: mov             x1, NULL
    // 0xd74c04: stur            x3, [fp, #-0x18]
    // 0xd74c08: r4 = 60
    //     0xd74c08: movz            x4, #0x3c
    // 0xd74c0c: branchIfSmi(r0, 0xd74c18)
    //     0xd74c0c: tbz             w0, #0, #0xd74c18
    // 0xd74c10: r4 = LoadClassIdInstr(r0)
    //     0xd74c10: ldur            x4, [x0, #-1]
    //     0xd74c14: ubfx            x4, x4, #0xc, #0x14
    // 0xd74c18: sub             x4, x4, #0x5a
    // 0xd74c1c: cmp             x4, #2
    // 0xd74c20: b.ls            #0xd74c34
    // 0xd74c24: r8 = List<Object?>?
    //     0xd74c24: ldr             x8, [PP, #0x320]  ; [pp+0x320] Type: List<Object?>?
    // 0xd74c28: r3 = Null
    //     0xd74c28: add             x3, PP, #0x22, lsl #12  ; [pp+0x22758] Null
    //     0xd74c2c: ldr             x3, [x3, #0x758]
    // 0xd74c30: r0 = List<Object?>?()
    //     0xd74c30: bl              #0x624060  ; IsType_List<Object?>?_Stub
    // 0xd74c34: ldur            x1, [fp, #-0x18]
    // 0xd74c38: cmp             w1, NULL
    // 0xd74c3c: b.eq            #0xd74d08
    // 0xd74c40: r0 = LoadClassIdInstr(r1)
    //     0xd74c40: ldur            x0, [x1, #-1]
    //     0xd74c44: ubfx            x0, x0, #0xc, #0x14
    // 0xd74c48: str             x1, [SP]
    // 0xd74c4c: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd74c4c: movz            x17, #0xc834
    //     0xd74c50: add             lr, x0, x17
    //     0xd74c54: ldr             lr, [x21, lr, lsl #3]
    //     0xd74c58: blr             lr
    // 0xd74c5c: r1 = LoadInt32Instr(r0)
    //     0xd74c5c: sbfx            x1, x0, #1, #0x1f
    //     0xd74c60: tbz             w0, #0, #0xd74c68
    //     0xd74c64: ldur            x1, [x0, #7]
    // 0xd74c68: cmp             x1, #1
    // 0xd74c6c: b.gt            #0xd74d18
    // 0xd74c70: ldur            x1, [fp, #-0x18]
    // 0xd74c74: r0 = LoadClassIdInstr(r1)
    //     0xd74c74: ldur            x0, [x1, #-1]
    //     0xd74c78: ubfx            x0, x0, #0xc, #0x14
    // 0xd74c7c: stp             xzr, x1, [SP]
    // 0xd74c80: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd74c80: movz            x17, #0x3037
    //     0xd74c84: movk            x17, #0x1, lsl #16
    //     0xd74c88: add             lr, x0, x17
    //     0xd74c8c: ldr             lr, [x21, lr, lsl #3]
    //     0xd74c90: blr             lr
    // 0xd74c94: cmp             w0, NULL
    // 0xd74c98: b.eq            #0xd74e44
    // 0xd74c9c: ldur            x1, [fp, #-0x18]
    // 0xd74ca0: r0 = LoadClassIdInstr(r1)
    //     0xd74ca0: ldur            x0, [x1, #-1]
    //     0xd74ca4: ubfx            x0, x0, #0xc, #0x14
    // 0xd74ca8: stp             xzr, x1, [SP]
    // 0xd74cac: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd74cac: movz            x17, #0x3037
    //     0xd74cb0: movk            x17, #0x1, lsl #16
    //     0xd74cb4: add             lr, x0, x17
    //     0xd74cb8: ldr             lr, [x21, lr, lsl #3]
    //     0xd74cbc: blr             lr
    // 0xd74cc0: mov             x3, x0
    // 0xd74cc4: r2 = Null
    //     0xd74cc4: mov             x2, NULL
    // 0xd74cc8: r1 = Null
    //     0xd74cc8: mov             x1, NULL
    // 0xd74ccc: stur            x3, [fp, #-0x20]
    // 0xd74cd0: r4 = 60
    //     0xd74cd0: movz            x4, #0x3c
    // 0xd74cd4: branchIfSmi(r0, 0xd74ce0)
    //     0xd74cd4: tbz             w0, #0, #0xd74ce0
    // 0xd74cd8: r4 = LoadClassIdInstr(r0)
    //     0xd74cd8: ldur            x4, [x0, #-1]
    //     0xd74cdc: ubfx            x4, x4, #0xc, #0x14
    // 0xd74ce0: cmp             x4, #0x3f
    // 0xd74ce4: b.eq            #0xd74cf8
    // 0xd74ce8: r8 = bool?
    //     0xd74ce8: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xd74cec: r3 = Null
    //     0xd74cec: add             x3, PP, #0x22, lsl #12  ; [pp+0x22768] Null
    //     0xd74cf0: ldr             x3, [x3, #0x768]
    // 0xd74cf4: r0 = bool?()
    //     0xd74cf4: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xd74cf8: ldur            x0, [fp, #-0x20]
    // 0xd74cfc: cmp             w0, NULL
    // 0xd74d00: b.eq            #0xd74e78
    // 0xd74d04: r0 = ReturnAsyncNotFuture()
    //     0xd74d04: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xd74d08: ldur            x1, [fp, #-0x10]
    // 0xd74d0c: r0 = _createConnectionError()
    //     0xd74d0c: bl              #0x7edbec  ; [package:webview_flutter_android/src/android_webkit.g.dart] ::_createConnectionError
    // 0xd74d10: r0 = Throw()
    //     0xd74d10: bl              #0xec04b8  ; ThrowStub
    // 0xd74d14: brk             #0
    // 0xd74d18: ldur            x1, [fp, #-0x18]
    // 0xd74d1c: r0 = LoadClassIdInstr(r1)
    //     0xd74d1c: ldur            x0, [x1, #-1]
    //     0xd74d20: ubfx            x0, x0, #0xc, #0x14
    // 0xd74d24: stp             xzr, x1, [SP]
    // 0xd74d28: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd74d28: movz            x17, #0x3037
    //     0xd74d2c: movk            x17, #0x1, lsl #16
    //     0xd74d30: add             lr, x0, x17
    //     0xd74d34: ldr             lr, [x21, lr, lsl #3]
    //     0xd74d38: blr             lr
    // 0xd74d3c: mov             x3, x0
    // 0xd74d40: stur            x3, [fp, #-0x10]
    // 0xd74d44: cmp             w3, NULL
    // 0xd74d48: b.eq            #0xd74e7c
    // 0xd74d4c: mov             x0, x3
    // 0xd74d50: r2 = Null
    //     0xd74d50: mov             x2, NULL
    // 0xd74d54: r1 = Null
    //     0xd74d54: mov             x1, NULL
    // 0xd74d58: r4 = 60
    //     0xd74d58: movz            x4, #0x3c
    // 0xd74d5c: branchIfSmi(r0, 0xd74d68)
    //     0xd74d5c: tbz             w0, #0, #0xd74d68
    // 0xd74d60: r4 = LoadClassIdInstr(r0)
    //     0xd74d60: ldur            x4, [x0, #-1]
    //     0xd74d64: ubfx            x4, x4, #0xc, #0x14
    // 0xd74d68: sub             x4, x4, #0x5e
    // 0xd74d6c: cmp             x4, #1
    // 0xd74d70: b.ls            #0xd74d84
    // 0xd74d74: r8 = String
    //     0xd74d74: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xd74d78: r3 = Null
    //     0xd74d78: add             x3, PP, #0x22, lsl #12  ; [pp+0x22778] Null
    //     0xd74d7c: ldr             x3, [x3, #0x778]
    // 0xd74d80: r0 = String()
    //     0xd74d80: bl              #0xed43b0  ; IsType_String_Stub
    // 0xd74d84: ldur            x1, [fp, #-0x18]
    // 0xd74d88: r0 = LoadClassIdInstr(r1)
    //     0xd74d88: ldur            x0, [x1, #-1]
    //     0xd74d8c: ubfx            x0, x0, #0xc, #0x14
    // 0xd74d90: r16 = 2
    //     0xd74d90: movz            x16, #0x2
    // 0xd74d94: stp             x16, x1, [SP]
    // 0xd74d98: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd74d98: movz            x17, #0x3037
    //     0xd74d9c: movk            x17, #0x1, lsl #16
    //     0xd74da0: add             lr, x0, x17
    //     0xd74da4: ldr             lr, [x21, lr, lsl #3]
    //     0xd74da8: blr             lr
    // 0xd74dac: mov             x3, x0
    // 0xd74db0: r2 = Null
    //     0xd74db0: mov             x2, NULL
    // 0xd74db4: r1 = Null
    //     0xd74db4: mov             x1, NULL
    // 0xd74db8: stur            x3, [fp, #-0x20]
    // 0xd74dbc: r4 = 60
    //     0xd74dbc: movz            x4, #0x3c
    // 0xd74dc0: branchIfSmi(r0, 0xd74dcc)
    //     0xd74dc0: tbz             w0, #0, #0xd74dcc
    // 0xd74dc4: r4 = LoadClassIdInstr(r0)
    //     0xd74dc4: ldur            x4, [x0, #-1]
    //     0xd74dc8: ubfx            x4, x4, #0xc, #0x14
    // 0xd74dcc: sub             x4, x4, #0x5e
    // 0xd74dd0: cmp             x4, #1
    // 0xd74dd4: b.ls            #0xd74de8
    // 0xd74dd8: r8 = String?
    //     0xd74dd8: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xd74ddc: r3 = Null
    //     0xd74ddc: add             x3, PP, #0x22, lsl #12  ; [pp+0x22788] Null
    //     0xd74de0: ldr             x3, [x3, #0x788]
    // 0xd74de4: r0 = String?()
    //     0xd74de4: bl              #0x600324  ; IsType_String?_Stub
    // 0xd74de8: ldur            x0, [fp, #-0x18]
    // 0xd74dec: r1 = LoadClassIdInstr(r0)
    //     0xd74dec: ldur            x1, [x0, #-1]
    //     0xd74df0: ubfx            x1, x1, #0xc, #0x14
    // 0xd74df4: r16 = 4
    //     0xd74df4: movz            x16, #0x4
    // 0xd74df8: stp             x16, x0, [SP]
    // 0xd74dfc: mov             x0, x1
    // 0xd74e00: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd74e00: movz            x17, #0x3037
    //     0xd74e04: movk            x17, #0x1, lsl #16
    //     0xd74e08: add             lr, x0, x17
    //     0xd74e0c: ldr             lr, [x21, lr, lsl #3]
    //     0xd74e10: blr             lr
    // 0xd74e14: stur            x0, [fp, #-0x18]
    // 0xd74e18: r0 = PlatformException()
    //     0xd74e18: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xd74e1c: mov             x1, x0
    // 0xd74e20: ldur            x0, [fp, #-0x10]
    // 0xd74e24: StoreField: r1->field_7 = r0
    //     0xd74e24: stur            w0, [x1, #7]
    // 0xd74e28: ldur            x0, [fp, #-0x20]
    // 0xd74e2c: StoreField: r1->field_b = r0
    //     0xd74e2c: stur            w0, [x1, #0xb]
    // 0xd74e30: ldur            x0, [fp, #-0x18]
    // 0xd74e34: StoreField: r1->field_f = r0
    //     0xd74e34: stur            w0, [x1, #0xf]
    // 0xd74e38: mov             x0, x1
    // 0xd74e3c: r0 = Throw()
    //     0xd74e3c: bl              #0xec04b8  ; ThrowStub
    // 0xd74e40: brk             #0
    // 0xd74e44: r0 = PlatformException()
    //     0xd74e44: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xd74e48: mov             x1, x0
    // 0xd74e4c: r0 = "null-error"
    //     0xd74e4c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12678] "null-error"
    //     0xd74e50: ldr             x0, [x0, #0x678]
    // 0xd74e54: StoreField: r1->field_7 = r0
    //     0xd74e54: stur            w0, [x1, #7]
    // 0xd74e58: r0 = "Host platform returned null value for non-null return value."
    //     0xd74e58: add             x0, PP, #0x12, lsl #12  ; [pp+0x12680] "Host platform returned null value for non-null return value."
    //     0xd74e5c: ldr             x0, [x0, #0x680]
    // 0xd74e60: StoreField: r1->field_b = r0
    //     0xd74e60: stur            w0, [x1, #0xb]
    // 0xd74e64: mov             x0, x1
    // 0xd74e68: r0 = Throw()
    //     0xd74e68: bl              #0xec04b8  ; ThrowStub
    // 0xd74e6c: brk             #0
    // 0xd74e70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd74e70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd74e74: b               #0xd74b3c
    // 0xd74e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd74e78: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd74e7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd74e7c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ openUrlInApp(/* No info */) async {
    // ** addr: 0xd74e80, size: 0x380
    // 0xd74e80: EnterFrame
    //     0xd74e80: stp             fp, lr, [SP, #-0x10]!
    //     0xd74e84: mov             fp, SP
    // 0xd74e88: AllocStack(0x50)
    //     0xd74e88: sub             SP, SP, #0x50
    // 0xd74e8c: SetupParameters(UrlLauncherApi this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r5, fp-0x28 */, dynamic _ /* r6 => r6, fp-0x30 */)
    //     0xd74e8c: stur            NULL, [fp, #-8]
    //     0xd74e90: stur            x1, [fp, #-0x10]
    //     0xd74e94: stur            x2, [fp, #-0x18]
    //     0xd74e98: stur            x3, [fp, #-0x20]
    //     0xd74e9c: stur            x5, [fp, #-0x28]
    //     0xd74ea0: stur            x6, [fp, #-0x30]
    // 0xd74ea4: CheckStackOverflow
    //     0xd74ea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd74ea8: cmp             SP, x16
    //     0xd74eac: b.ls            #0xd751f0
    // 0xd74eb0: InitAsync() -> Future<bool>
    //     0xd74eb0: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0xd74eb4: bl              #0x661298  ; InitAsyncStub
    // 0xd74eb8: r1 = Null
    //     0xd74eb8: mov             x1, NULL
    // 0xd74ebc: r2 = 4
    //     0xd74ebc: movz            x2, #0x4
    // 0xd74ec0: r0 = AllocateArray()
    //     0xd74ec0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd74ec4: r16 = "dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.openUrlInApp"
    //     0xd74ec4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22798] "dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.openUrlInApp"
    //     0xd74ec8: ldr             x16, [x16, #0x798]
    // 0xd74ecc: StoreField: r0->field_f = r16
    //     0xd74ecc: stur            w16, [x0, #0xf]
    // 0xd74ed0: ldur            x1, [fp, #-0x10]
    // 0xd74ed4: LoadField: r2 = r1->field_b
    //     0xd74ed4: ldur            w2, [x1, #0xb]
    // 0xd74ed8: DecompressPointer r2
    //     0xd74ed8: add             x2, x2, HEAP, lsl #32
    // 0xd74edc: StoreField: r0->field_13 = r2
    //     0xd74edc: stur            w2, [x0, #0x13]
    // 0xd74ee0: str             x0, [SP]
    // 0xd74ee4: r0 = _interpolate()
    //     0xd74ee4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xd74ee8: r1 = <Object?>
    //     0xd74ee8: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xd74eec: stur            x0, [fp, #-0x10]
    // 0xd74ef0: r0 = BasicMessageChannel()
    //     0xd74ef0: bl              #0x7edc64  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xd74ef4: mov             x3, x0
    // 0xd74ef8: ldur            x0, [fp, #-0x10]
    // 0xd74efc: stur            x3, [fp, #-0x38]
    // 0xd74f00: StoreField: r3->field_b = r0
    //     0xd74f00: stur            w0, [x3, #0xb]
    // 0xd74f04: r1 = Instance__PigeonCodec
    //     0xd74f04: add             x1, PP, #0x22, lsl #12  ; [pp+0x22750] Obj!_PigeonCodec@e259a1
    //     0xd74f08: ldr             x1, [x1, #0x750]
    // 0xd74f0c: StoreField: r3->field_f = r1
    //     0xd74f0c: stur            w1, [x3, #0xf]
    // 0xd74f10: r1 = Null
    //     0xd74f10: mov             x1, NULL
    // 0xd74f14: r2 = 8
    //     0xd74f14: movz            x2, #0x8
    // 0xd74f18: r0 = AllocateArray()
    //     0xd74f18: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd74f1c: mov             x2, x0
    // 0xd74f20: ldur            x0, [fp, #-0x18]
    // 0xd74f24: stur            x2, [fp, #-0x40]
    // 0xd74f28: StoreField: r2->field_f = r0
    //     0xd74f28: stur            w0, [x2, #0xf]
    // 0xd74f2c: ldur            x0, [fp, #-0x20]
    // 0xd74f30: StoreField: r2->field_13 = r0
    //     0xd74f30: stur            w0, [x2, #0x13]
    // 0xd74f34: ldur            x0, [fp, #-0x28]
    // 0xd74f38: ArrayStore: r2[0] = r0  ; List_4
    //     0xd74f38: stur            w0, [x2, #0x17]
    // 0xd74f3c: ldur            x0, [fp, #-0x30]
    // 0xd74f40: StoreField: r2->field_1b = r0
    //     0xd74f40: stur            w0, [x2, #0x1b]
    // 0xd74f44: r1 = <Object?>
    //     0xd74f44: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xd74f48: r0 = AllocateGrowableArray()
    //     0xd74f48: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xd74f4c: mov             x1, x0
    // 0xd74f50: ldur            x0, [fp, #-0x40]
    // 0xd74f54: StoreField: r1->field_f = r0
    //     0xd74f54: stur            w0, [x1, #0xf]
    // 0xd74f58: r0 = 8
    //     0xd74f58: movz            x0, #0x8
    // 0xd74f5c: StoreField: r1->field_b = r0
    //     0xd74f5c: stur            w0, [x1, #0xb]
    // 0xd74f60: mov             x2, x1
    // 0xd74f64: ldur            x1, [fp, #-0x38]
    // 0xd74f68: r0 = send()
    //     0xd74f68: bl              #0x65755c  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xd74f6c: mov             x1, x0
    // 0xd74f70: stur            x1, [fp, #-0x18]
    // 0xd74f74: r0 = Await()
    //     0xd74f74: bl              #0x661044  ; AwaitStub
    // 0xd74f78: mov             x3, x0
    // 0xd74f7c: r2 = Null
    //     0xd74f7c: mov             x2, NULL
    // 0xd74f80: r1 = Null
    //     0xd74f80: mov             x1, NULL
    // 0xd74f84: stur            x3, [fp, #-0x18]
    // 0xd74f88: r4 = 60
    //     0xd74f88: movz            x4, #0x3c
    // 0xd74f8c: branchIfSmi(r0, 0xd74f98)
    //     0xd74f8c: tbz             w0, #0, #0xd74f98
    // 0xd74f90: r4 = LoadClassIdInstr(r0)
    //     0xd74f90: ldur            x4, [x0, #-1]
    //     0xd74f94: ubfx            x4, x4, #0xc, #0x14
    // 0xd74f98: sub             x4, x4, #0x5a
    // 0xd74f9c: cmp             x4, #2
    // 0xd74fa0: b.ls            #0xd74fb4
    // 0xd74fa4: r8 = List<Object?>?
    //     0xd74fa4: ldr             x8, [PP, #0x320]  ; [pp+0x320] Type: List<Object?>?
    // 0xd74fa8: r3 = Null
    //     0xd74fa8: add             x3, PP, #0x22, lsl #12  ; [pp+0x227a0] Null
    //     0xd74fac: ldr             x3, [x3, #0x7a0]
    // 0xd74fb0: r0 = List<Object?>?()
    //     0xd74fb0: bl              #0x624060  ; IsType_List<Object?>?_Stub
    // 0xd74fb4: ldur            x1, [fp, #-0x18]
    // 0xd74fb8: cmp             w1, NULL
    // 0xd74fbc: b.eq            #0xd75088
    // 0xd74fc0: r0 = LoadClassIdInstr(r1)
    //     0xd74fc0: ldur            x0, [x1, #-1]
    //     0xd74fc4: ubfx            x0, x0, #0xc, #0x14
    // 0xd74fc8: str             x1, [SP]
    // 0xd74fcc: r0 = GDT[cid_x0 + 0xc834]()
    //     0xd74fcc: movz            x17, #0xc834
    //     0xd74fd0: add             lr, x0, x17
    //     0xd74fd4: ldr             lr, [x21, lr, lsl #3]
    //     0xd74fd8: blr             lr
    // 0xd74fdc: r1 = LoadInt32Instr(r0)
    //     0xd74fdc: sbfx            x1, x0, #1, #0x1f
    //     0xd74fe0: tbz             w0, #0, #0xd74fe8
    //     0xd74fe4: ldur            x1, [x0, #7]
    // 0xd74fe8: cmp             x1, #1
    // 0xd74fec: b.gt            #0xd75098
    // 0xd74ff0: ldur            x1, [fp, #-0x18]
    // 0xd74ff4: r0 = LoadClassIdInstr(r1)
    //     0xd74ff4: ldur            x0, [x1, #-1]
    //     0xd74ff8: ubfx            x0, x0, #0xc, #0x14
    // 0xd74ffc: stp             xzr, x1, [SP]
    // 0xd75000: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd75000: movz            x17, #0x3037
    //     0xd75004: movk            x17, #0x1, lsl #16
    //     0xd75008: add             lr, x0, x17
    //     0xd7500c: ldr             lr, [x21, lr, lsl #3]
    //     0xd75010: blr             lr
    // 0xd75014: cmp             w0, NULL
    // 0xd75018: b.eq            #0xd751c4
    // 0xd7501c: ldur            x1, [fp, #-0x18]
    // 0xd75020: r0 = LoadClassIdInstr(r1)
    //     0xd75020: ldur            x0, [x1, #-1]
    //     0xd75024: ubfx            x0, x0, #0xc, #0x14
    // 0xd75028: stp             xzr, x1, [SP]
    // 0xd7502c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd7502c: movz            x17, #0x3037
    //     0xd75030: movk            x17, #0x1, lsl #16
    //     0xd75034: add             lr, x0, x17
    //     0xd75038: ldr             lr, [x21, lr, lsl #3]
    //     0xd7503c: blr             lr
    // 0xd75040: mov             x3, x0
    // 0xd75044: r2 = Null
    //     0xd75044: mov             x2, NULL
    // 0xd75048: r1 = Null
    //     0xd75048: mov             x1, NULL
    // 0xd7504c: stur            x3, [fp, #-0x20]
    // 0xd75050: r4 = 60
    //     0xd75050: movz            x4, #0x3c
    // 0xd75054: branchIfSmi(r0, 0xd75060)
    //     0xd75054: tbz             w0, #0, #0xd75060
    // 0xd75058: r4 = LoadClassIdInstr(r0)
    //     0xd75058: ldur            x4, [x0, #-1]
    //     0xd7505c: ubfx            x4, x4, #0xc, #0x14
    // 0xd75060: cmp             x4, #0x3f
    // 0xd75064: b.eq            #0xd75078
    // 0xd75068: r8 = bool?
    //     0xd75068: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xd7506c: r3 = Null
    //     0xd7506c: add             x3, PP, #0x22, lsl #12  ; [pp+0x227b0] Null
    //     0xd75070: ldr             x3, [x3, #0x7b0]
    // 0xd75074: r0 = bool?()
    //     0xd75074: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xd75078: ldur            x0, [fp, #-0x20]
    // 0xd7507c: cmp             w0, NULL
    // 0xd75080: b.eq            #0xd751f8
    // 0xd75084: r0 = ReturnAsyncNotFuture()
    //     0xd75084: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xd75088: ldur            x1, [fp, #-0x10]
    // 0xd7508c: r0 = _createConnectionError()
    //     0xd7508c: bl              #0x7edbec  ; [package:webview_flutter_android/src/android_webkit.g.dart] ::_createConnectionError
    // 0xd75090: r0 = Throw()
    //     0xd75090: bl              #0xec04b8  ; ThrowStub
    // 0xd75094: brk             #0
    // 0xd75098: ldur            x1, [fp, #-0x18]
    // 0xd7509c: r0 = LoadClassIdInstr(r1)
    //     0xd7509c: ldur            x0, [x1, #-1]
    //     0xd750a0: ubfx            x0, x0, #0xc, #0x14
    // 0xd750a4: stp             xzr, x1, [SP]
    // 0xd750a8: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd750a8: movz            x17, #0x3037
    //     0xd750ac: movk            x17, #0x1, lsl #16
    //     0xd750b0: add             lr, x0, x17
    //     0xd750b4: ldr             lr, [x21, lr, lsl #3]
    //     0xd750b8: blr             lr
    // 0xd750bc: mov             x3, x0
    // 0xd750c0: stur            x3, [fp, #-0x10]
    // 0xd750c4: cmp             w3, NULL
    // 0xd750c8: b.eq            #0xd751fc
    // 0xd750cc: mov             x0, x3
    // 0xd750d0: r2 = Null
    //     0xd750d0: mov             x2, NULL
    // 0xd750d4: r1 = Null
    //     0xd750d4: mov             x1, NULL
    // 0xd750d8: r4 = 60
    //     0xd750d8: movz            x4, #0x3c
    // 0xd750dc: branchIfSmi(r0, 0xd750e8)
    //     0xd750dc: tbz             w0, #0, #0xd750e8
    // 0xd750e0: r4 = LoadClassIdInstr(r0)
    //     0xd750e0: ldur            x4, [x0, #-1]
    //     0xd750e4: ubfx            x4, x4, #0xc, #0x14
    // 0xd750e8: sub             x4, x4, #0x5e
    // 0xd750ec: cmp             x4, #1
    // 0xd750f0: b.ls            #0xd75104
    // 0xd750f4: r8 = String
    //     0xd750f4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xd750f8: r3 = Null
    //     0xd750f8: add             x3, PP, #0x22, lsl #12  ; [pp+0x227c0] Null
    //     0xd750fc: ldr             x3, [x3, #0x7c0]
    // 0xd75100: r0 = String()
    //     0xd75100: bl              #0xed43b0  ; IsType_String_Stub
    // 0xd75104: ldur            x1, [fp, #-0x18]
    // 0xd75108: r0 = LoadClassIdInstr(r1)
    //     0xd75108: ldur            x0, [x1, #-1]
    //     0xd7510c: ubfx            x0, x0, #0xc, #0x14
    // 0xd75110: r16 = 2
    //     0xd75110: movz            x16, #0x2
    // 0xd75114: stp             x16, x1, [SP]
    // 0xd75118: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd75118: movz            x17, #0x3037
    //     0xd7511c: movk            x17, #0x1, lsl #16
    //     0xd75120: add             lr, x0, x17
    //     0xd75124: ldr             lr, [x21, lr, lsl #3]
    //     0xd75128: blr             lr
    // 0xd7512c: mov             x3, x0
    // 0xd75130: r2 = Null
    //     0xd75130: mov             x2, NULL
    // 0xd75134: r1 = Null
    //     0xd75134: mov             x1, NULL
    // 0xd75138: stur            x3, [fp, #-0x20]
    // 0xd7513c: r4 = 60
    //     0xd7513c: movz            x4, #0x3c
    // 0xd75140: branchIfSmi(r0, 0xd7514c)
    //     0xd75140: tbz             w0, #0, #0xd7514c
    // 0xd75144: r4 = LoadClassIdInstr(r0)
    //     0xd75144: ldur            x4, [x0, #-1]
    //     0xd75148: ubfx            x4, x4, #0xc, #0x14
    // 0xd7514c: sub             x4, x4, #0x5e
    // 0xd75150: cmp             x4, #1
    // 0xd75154: b.ls            #0xd75168
    // 0xd75158: r8 = String?
    //     0xd75158: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0xd7515c: r3 = Null
    //     0xd7515c: add             x3, PP, #0x22, lsl #12  ; [pp+0x227d0] Null
    //     0xd75160: ldr             x3, [x3, #0x7d0]
    // 0xd75164: r0 = String?()
    //     0xd75164: bl              #0x600324  ; IsType_String?_Stub
    // 0xd75168: ldur            x0, [fp, #-0x18]
    // 0xd7516c: r1 = LoadClassIdInstr(r0)
    //     0xd7516c: ldur            x1, [x0, #-1]
    //     0xd75170: ubfx            x1, x1, #0xc, #0x14
    // 0xd75174: r16 = 4
    //     0xd75174: movz            x16, #0x4
    // 0xd75178: stp             x16, x0, [SP]
    // 0xd7517c: mov             x0, x1
    // 0xd75180: r0 = GDT[cid_x0 + 0x13037]()
    //     0xd75180: movz            x17, #0x3037
    //     0xd75184: movk            x17, #0x1, lsl #16
    //     0xd75188: add             lr, x0, x17
    //     0xd7518c: ldr             lr, [x21, lr, lsl #3]
    //     0xd75190: blr             lr
    // 0xd75194: stur            x0, [fp, #-0x18]
    // 0xd75198: r0 = PlatformException()
    //     0xd75198: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xd7519c: mov             x1, x0
    // 0xd751a0: ldur            x0, [fp, #-0x10]
    // 0xd751a4: StoreField: r1->field_7 = r0
    //     0xd751a4: stur            w0, [x1, #7]
    // 0xd751a8: ldur            x0, [fp, #-0x20]
    // 0xd751ac: StoreField: r1->field_b = r0
    //     0xd751ac: stur            w0, [x1, #0xb]
    // 0xd751b0: ldur            x0, [fp, #-0x18]
    // 0xd751b4: StoreField: r1->field_f = r0
    //     0xd751b4: stur            w0, [x1, #0xf]
    // 0xd751b8: mov             x0, x1
    // 0xd751bc: r0 = Throw()
    //     0xd751bc: bl              #0xec04b8  ; ThrowStub
    // 0xd751c0: brk             #0
    // 0xd751c4: r0 = PlatformException()
    //     0xd751c4: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xd751c8: mov             x1, x0
    // 0xd751cc: r0 = "null-error"
    //     0xd751cc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12678] "null-error"
    //     0xd751d0: ldr             x0, [x0, #0x678]
    // 0xd751d4: StoreField: r1->field_7 = r0
    //     0xd751d4: stur            w0, [x1, #7]
    // 0xd751d8: r0 = "Host platform returned null value for non-null return value."
    //     0xd751d8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12680] "Host platform returned null value for non-null return value."
    //     0xd751dc: ldr             x0, [x0, #0x680]
    // 0xd751e0: StoreField: r1->field_b = r0
    //     0xd751e0: stur            w0, [x1, #0xb]
    // 0xd751e4: mov             x0, x1
    // 0xd751e8: r0 = Throw()
    //     0xd751e8: bl              #0xec04b8  ; ThrowStub
    // 0xd751ec: brk             #0
    // 0xd751f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd751f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd751f4: b               #0xd74eb0
    // 0xd751f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd751f8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xd751fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd751fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 420, size: 0xc, field offset: 0x8
class BrowserOptions extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xce7404, size: 0xe8
    // 0xce7404: EnterFrame
    //     0xce7404: stp             fp, lr, [SP, #-0x10]!
    //     0xce7408: mov             fp, SP
    // 0xce740c: AllocStack(0x18)
    //     0xce740c: sub             SP, SP, #0x18
    // 0xce7410: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xce7410: mov             x3, x1
    //     0xce7414: stur            x1, [fp, #-8]
    // 0xce7418: CheckStackOverflow
    //     0xce7418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce741c: cmp             SP, x16
    //     0xce7420: b.ls            #0xce74e0
    // 0xce7424: mov             x0, x3
    // 0xce7428: r2 = Null
    //     0xce7428: mov             x2, NULL
    // 0xce742c: r1 = Null
    //     0xce742c: mov             x1, NULL
    // 0xce7430: r4 = 60
    //     0xce7430: movz            x4, #0x3c
    // 0xce7434: branchIfSmi(r0, 0xce7440)
    //     0xce7434: tbz             w0, #0, #0xce7440
    // 0xce7438: r4 = LoadClassIdInstr(r0)
    //     0xce7438: ldur            x4, [x0, #-1]
    //     0xce743c: ubfx            x4, x4, #0xc, #0x14
    // 0xce7440: sub             x4, x4, #0x5a
    // 0xce7444: cmp             x4, #2
    // 0xce7448: b.ls            #0xce745c
    // 0xce744c: r8 = List<Object?>
    //     0xce744c: ldr             x8, [PP, #0x14e0]  ; [pp+0x14e0] Type: List<Object?>
    // 0xce7450: r3 = Null
    //     0xce7450: add             x3, PP, #0x23, lsl #12  ; [pp+0x235e0] Null
    //     0xce7454: ldr             x3, [x3, #0x5e0]
    // 0xce7458: r0 = List<Object?>()
    //     0xce7458: bl              #0x697f54  ; IsType_List<Object?>_Stub
    // 0xce745c: ldur            x0, [fp, #-8]
    // 0xce7460: r1 = LoadClassIdInstr(r0)
    //     0xce7460: ldur            x1, [x0, #-1]
    //     0xce7464: ubfx            x1, x1, #0xc, #0x14
    // 0xce7468: stp             xzr, x0, [SP]
    // 0xce746c: mov             x0, x1
    // 0xce7470: r0 = GDT[cid_x0 + 0x13037]()
    //     0xce7470: movz            x17, #0x3037
    //     0xce7474: movk            x17, #0x1, lsl #16
    //     0xce7478: add             lr, x0, x17
    //     0xce747c: ldr             lr, [x21, lr, lsl #3]
    //     0xce7480: blr             lr
    // 0xce7484: mov             x3, x0
    // 0xce7488: stur            x3, [fp, #-8]
    // 0xce748c: cmp             w3, NULL
    // 0xce7490: b.eq            #0xce74e8
    // 0xce7494: mov             x0, x3
    // 0xce7498: r2 = Null
    //     0xce7498: mov             x2, NULL
    // 0xce749c: r1 = Null
    //     0xce749c: mov             x1, NULL
    // 0xce74a0: r4 = 60
    //     0xce74a0: movz            x4, #0x3c
    // 0xce74a4: branchIfSmi(r0, 0xce74b0)
    //     0xce74a4: tbz             w0, #0, #0xce74b0
    // 0xce74a8: r4 = LoadClassIdInstr(r0)
    //     0xce74a8: ldur            x4, [x0, #-1]
    //     0xce74ac: ubfx            x4, x4, #0xc, #0x14
    // 0xce74b0: cmp             x4, #0x3f
    // 0xce74b4: b.eq            #0xce74c8
    // 0xce74b8: r8 = bool
    //     0xce74b8: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xce74bc: r3 = Null
    //     0xce74bc: add             x3, PP, #0x23, lsl #12  ; [pp+0x235f0] Null
    //     0xce74c0: ldr             x3, [x3, #0x5f0]
    // 0xce74c4: r0 = bool()
    //     0xce74c4: bl              #0xed4390  ; IsType_bool_Stub
    // 0xce74c8: r0 = BrowserOptions()
    //     0xce74c8: bl              #0xce74ec  ; AllocateBrowserOptionsStub -> BrowserOptions (size=0xc)
    // 0xce74cc: ldur            x1, [fp, #-8]
    // 0xce74d0: StoreField: r0->field_7 = r1
    //     0xce74d0: stur            w1, [x0, #7]
    // 0xce74d4: LeaveFrame
    //     0xce74d4: mov             SP, fp
    //     0xce74d8: ldp             fp, lr, [SP], #0x10
    // 0xce74dc: ret
    //     0xce74dc: ret             
    // 0xce74e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce74e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce74e4: b               #0xce7424
    // 0xce74e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce74e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 421, size: 0x14, field offset: 0x8
class WebViewOptions extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xce74f8, size: 0x1ec
    // 0xce74f8: EnterFrame
    //     0xce74f8: stp             fp, lr, [SP, #-0x10]!
    //     0xce74fc: mov             fp, SP
    // 0xce7500: AllocStack(0x28)
    //     0xce7500: sub             SP, SP, #0x28
    // 0xce7504: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xce7504: mov             x3, x1
    //     0xce7508: stur            x1, [fp, #-8]
    // 0xce750c: CheckStackOverflow
    //     0xce750c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce7510: cmp             SP, x16
    //     0xce7514: b.ls            #0xce76d0
    // 0xce7518: mov             x0, x3
    // 0xce751c: r2 = Null
    //     0xce751c: mov             x2, NULL
    // 0xce7520: r1 = Null
    //     0xce7520: mov             x1, NULL
    // 0xce7524: r4 = 60
    //     0xce7524: movz            x4, #0x3c
    // 0xce7528: branchIfSmi(r0, 0xce7534)
    //     0xce7528: tbz             w0, #0, #0xce7534
    // 0xce752c: r4 = LoadClassIdInstr(r0)
    //     0xce752c: ldur            x4, [x0, #-1]
    //     0xce7530: ubfx            x4, x4, #0xc, #0x14
    // 0xce7534: sub             x4, x4, #0x5a
    // 0xce7538: cmp             x4, #2
    // 0xce753c: b.ls            #0xce7550
    // 0xce7540: r8 = List<Object?>
    //     0xce7540: ldr             x8, [PP, #0x14e0]  ; [pp+0x14e0] Type: List<Object?>
    // 0xce7544: r3 = Null
    //     0xce7544: add             x3, PP, #0x23, lsl #12  ; [pp+0x23600] Null
    //     0xce7548: ldr             x3, [x3, #0x600]
    // 0xce754c: r0 = List<Object?>()
    //     0xce754c: bl              #0x697f54  ; IsType_List<Object?>_Stub
    // 0xce7550: ldur            x1, [fp, #-8]
    // 0xce7554: r0 = LoadClassIdInstr(r1)
    //     0xce7554: ldur            x0, [x1, #-1]
    //     0xce7558: ubfx            x0, x0, #0xc, #0x14
    // 0xce755c: stp             xzr, x1, [SP]
    // 0xce7560: r0 = GDT[cid_x0 + 0x13037]()
    //     0xce7560: movz            x17, #0x3037
    //     0xce7564: movk            x17, #0x1, lsl #16
    //     0xce7568: add             lr, x0, x17
    //     0xce756c: ldr             lr, [x21, lr, lsl #3]
    //     0xce7570: blr             lr
    // 0xce7574: mov             x3, x0
    // 0xce7578: stur            x3, [fp, #-0x10]
    // 0xce757c: cmp             w3, NULL
    // 0xce7580: b.eq            #0xce76d8
    // 0xce7584: mov             x0, x3
    // 0xce7588: r2 = Null
    //     0xce7588: mov             x2, NULL
    // 0xce758c: r1 = Null
    //     0xce758c: mov             x1, NULL
    // 0xce7590: r4 = 60
    //     0xce7590: movz            x4, #0x3c
    // 0xce7594: branchIfSmi(r0, 0xce75a0)
    //     0xce7594: tbz             w0, #0, #0xce75a0
    // 0xce7598: r4 = LoadClassIdInstr(r0)
    //     0xce7598: ldur            x4, [x0, #-1]
    //     0xce759c: ubfx            x4, x4, #0xc, #0x14
    // 0xce75a0: cmp             x4, #0x3f
    // 0xce75a4: b.eq            #0xce75b8
    // 0xce75a8: r8 = bool
    //     0xce75a8: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xce75ac: r3 = Null
    //     0xce75ac: add             x3, PP, #0x23, lsl #12  ; [pp+0x23610] Null
    //     0xce75b0: ldr             x3, [x3, #0x610]
    // 0xce75b4: r0 = bool()
    //     0xce75b4: bl              #0xed4390  ; IsType_bool_Stub
    // 0xce75b8: ldur            x1, [fp, #-8]
    // 0xce75bc: r0 = LoadClassIdInstr(r1)
    //     0xce75bc: ldur            x0, [x1, #-1]
    //     0xce75c0: ubfx            x0, x0, #0xc, #0x14
    // 0xce75c4: r16 = 2
    //     0xce75c4: movz            x16, #0x2
    // 0xce75c8: stp             x16, x1, [SP]
    // 0xce75cc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xce75cc: movz            x17, #0x3037
    //     0xce75d0: movk            x17, #0x1, lsl #16
    //     0xce75d4: add             lr, x0, x17
    //     0xce75d8: ldr             lr, [x21, lr, lsl #3]
    //     0xce75dc: blr             lr
    // 0xce75e0: mov             x3, x0
    // 0xce75e4: stur            x3, [fp, #-0x18]
    // 0xce75e8: cmp             w3, NULL
    // 0xce75ec: b.eq            #0xce76dc
    // 0xce75f0: mov             x0, x3
    // 0xce75f4: r2 = Null
    //     0xce75f4: mov             x2, NULL
    // 0xce75f8: r1 = Null
    //     0xce75f8: mov             x1, NULL
    // 0xce75fc: r4 = 60
    //     0xce75fc: movz            x4, #0x3c
    // 0xce7600: branchIfSmi(r0, 0xce760c)
    //     0xce7600: tbz             w0, #0, #0xce760c
    // 0xce7604: r4 = LoadClassIdInstr(r0)
    //     0xce7604: ldur            x4, [x0, #-1]
    //     0xce7608: ubfx            x4, x4, #0xc, #0x14
    // 0xce760c: cmp             x4, #0x3f
    // 0xce7610: b.eq            #0xce7624
    // 0xce7614: r8 = bool
    //     0xce7614: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xce7618: r3 = Null
    //     0xce7618: add             x3, PP, #0x23, lsl #12  ; [pp+0x23620] Null
    //     0xce761c: ldr             x3, [x3, #0x620]
    // 0xce7620: r0 = bool()
    //     0xce7620: bl              #0xed4390  ; IsType_bool_Stub
    // 0xce7624: ldur            x0, [fp, #-8]
    // 0xce7628: r1 = LoadClassIdInstr(r0)
    //     0xce7628: ldur            x1, [x0, #-1]
    //     0xce762c: ubfx            x1, x1, #0xc, #0x14
    // 0xce7630: r16 = 4
    //     0xce7630: movz            x16, #0x4
    // 0xce7634: stp             x16, x0, [SP]
    // 0xce7638: mov             x0, x1
    // 0xce763c: r0 = GDT[cid_x0 + 0x13037]()
    //     0xce763c: movz            x17, #0x3037
    //     0xce7640: movk            x17, #0x1, lsl #16
    //     0xce7644: add             lr, x0, x17
    //     0xce7648: ldr             lr, [x21, lr, lsl #3]
    //     0xce764c: blr             lr
    // 0xce7650: mov             x3, x0
    // 0xce7654: r2 = Null
    //     0xce7654: mov             x2, NULL
    // 0xce7658: r1 = Null
    //     0xce7658: mov             x1, NULL
    // 0xce765c: stur            x3, [fp, #-8]
    // 0xce7660: r8 = Map<Object?, Object?>?
    //     0xce7660: ldr             x8, [PP, #0x1e10]  ; [pp+0x1e10] Type: Map<Object?, Object?>?
    // 0xce7664: r3 = Null
    //     0xce7664: add             x3, PP, #0x23, lsl #12  ; [pp+0x23630] Null
    //     0xce7668: ldr             x3, [x3, #0x630]
    // 0xce766c: r0 = Map<Object?, Object?>?()
    //     0xce766c: bl              #0x6a0620  ; IsType_Map<Object?, Object?>?_Stub
    // 0xce7670: ldur            x0, [fp, #-8]
    // 0xce7674: cmp             w0, NULL
    // 0xce7678: b.eq            #0xce76e0
    // 0xce767c: r1 = LoadClassIdInstr(r0)
    //     0xce767c: ldur            x1, [x0, #-1]
    //     0xce7680: ubfx            x1, x1, #0xc, #0x14
    // 0xce7684: r16 = <String, String>
    //     0xce7684: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0xce7688: ldr             x16, [x16, #0x668]
    // 0xce768c: stp             x0, x16, [SP]
    // 0xce7690: mov             x0, x1
    // 0xce7694: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xce7694: ldr             x4, [PP, #0x1e30]  ; [pp+0x1e30] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xce7698: r0 = GDT[cid_x0 + 0x5f4]()
    //     0xce7698: add             lr, x0, #0x5f4
    //     0xce769c: ldr             lr, [x21, lr, lsl #3]
    //     0xce76a0: blr             lr
    // 0xce76a4: stur            x0, [fp, #-8]
    // 0xce76a8: r0 = WebViewOptions()
    //     0xce76a8: bl              #0xce76e4  ; AllocateWebViewOptionsStub -> WebViewOptions (size=0x14)
    // 0xce76ac: ldur            x1, [fp, #-0x10]
    // 0xce76b0: StoreField: r0->field_7 = r1
    //     0xce76b0: stur            w1, [x0, #7]
    // 0xce76b4: ldur            x1, [fp, #-0x18]
    // 0xce76b8: StoreField: r0->field_b = r1
    //     0xce76b8: stur            w1, [x0, #0xb]
    // 0xce76bc: ldur            x1, [fp, #-8]
    // 0xce76c0: StoreField: r0->field_f = r1
    //     0xce76c0: stur            w1, [x0, #0xf]
    // 0xce76c4: LeaveFrame
    //     0xce76c4: mov             SP, fp
    //     0xce76c8: ldp             fp, lr, [SP], #0x10
    // 0xce76cc: ret
    //     0xce76cc: ret             
    // 0xce76d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce76d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce76d4: b               #0xce7518
    // 0xce76d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce76d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xce76dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce76dc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xce76e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce76e0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5527, size: 0x8, field offset: 0x8
//   const constructor, 
class _PigeonCodec extends StandardMessageCodec {

  _ readValueOfType(/* No info */) {
    // ** addr: 0xce7360, size: 0xa4
    // 0xce7360: EnterFrame
    //     0xce7360: stp             fp, lr, [SP, #-0x10]!
    //     0xce7364: mov             fp, SP
    // 0xce7368: mov             x0, x2
    // 0xce736c: mov             x2, x3
    // 0xce7370: CheckStackOverflow
    //     0xce7370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce7374: cmp             SP, x16
    //     0xce7378: b.ls            #0xce73f4
    // 0xce737c: cmp             x0, #0x81
    // 0xce7380: b.gt            #0xce73b0
    // 0xce7384: lsl             x3, x0, #1
    // 0xce7388: cmp             w3, #0x102
    // 0xce738c: b.ne            #0xce73dc
    // 0xce7390: r0 = readValue()
    //     0xce7390: bl              #0xce719c  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xce7394: cmp             w0, NULL
    // 0xce7398: b.eq            #0xce73fc
    // 0xce739c: mov             x1, x0
    // 0xce73a0: r0 = decode()
    //     0xce73a0: bl              #0xce74f8  ; [package:url_launcher_android/src/messages.g.dart] WebViewOptions::decode
    // 0xce73a4: LeaveFrame
    //     0xce73a4: mov             SP, fp
    //     0xce73a8: ldp             fp, lr, [SP], #0x10
    // 0xce73ac: ret
    //     0xce73ac: ret             
    // 0xce73b0: lsl             x3, x0, #1
    // 0xce73b4: cmp             w3, #0x104
    // 0xce73b8: b.ne            #0xce73dc
    // 0xce73bc: r0 = readValue()
    //     0xce73bc: bl              #0xce719c  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xce73c0: cmp             w0, NULL
    // 0xce73c4: b.eq            #0xce7400
    // 0xce73c8: mov             x1, x0
    // 0xce73cc: r0 = decode()
    //     0xce73cc: bl              #0xce7404  ; [package:url_launcher_android/src/messages.g.dart] BrowserOptions::decode
    // 0xce73d0: LeaveFrame
    //     0xce73d0: mov             SP, fp
    //     0xce73d4: ldp             fp, lr, [SP], #0x10
    // 0xce73d8: ret
    //     0xce73d8: ret             
    // 0xce73dc: mov             x3, x2
    // 0xce73e0: mov             x2, x0
    // 0xce73e4: r0 = readValueOfType()
    //     0xce73e4: bl              #0xce7e48  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValueOfType
    // 0xce73e8: LeaveFrame
    //     0xce73e8: mov             SP, fp
    //     0xce73ec: ldp             fp, lr, [SP], #0x10
    // 0xce73f0: ret
    //     0xce73f0: ret             
    // 0xce73f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce73f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce73f8: b               #0xce737c
    // 0xce73fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce73fc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xce7400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce7400: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ writeValue(/* No info */) {
    // ** addr: 0xd49fec, size: 0xfc
    // 0xd49fec: EnterFrame
    //     0xd49fec: stp             fp, lr, [SP, #-0x10]!
    //     0xd49ff0: mov             fp, SP
    // 0xd49ff4: AllocStack(0x18)
    //     0xd49ff4: sub             SP, SP, #0x18
    // 0xd49ff8: SetupParameters(_PigeonCodec this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xd49ff8: mov             x4, x1
    //     0xd49ffc: mov             x0, x3
    //     0xd4a000: stur            x3, [fp, #-0x10]
    //     0xd4a004: mov             x3, x2
    //     0xd4a008: stur            x2, [fp, #-8]
    //     0xd4a00c: stur            x1, [fp, #-0x18]
    // 0xd4a010: CheckStackOverflow
    //     0xd4a010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4a014: cmp             SP, x16
    //     0xd4a018: b.ls            #0xd4a0e0
    // 0xd4a01c: r1 = 60
    //     0xd4a01c: movz            x1, #0x3c
    // 0xd4a020: branchIfSmi(r0, 0xd4a02c)
    //     0xd4a020: tbz             w0, #0, #0xd4a02c
    // 0xd4a024: r1 = LoadClassIdInstr(r0)
    //     0xd4a024: ldur            x1, [x0, #-1]
    //     0xd4a028: ubfx            x1, x1, #0xc, #0x14
    // 0xd4a02c: sub             x16, x1, #0x3c
    // 0xd4a030: cmp             x16, #1
    // 0xd4a034: b.hi            #0xd4a060
    // 0xd4a038: mov             x1, x3
    // 0xd4a03c: r2 = 4
    //     0xd4a03c: movz            x2, #0x4
    // 0xd4a040: r0 = _add()
    //     0xd4a040: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd4a044: ldur            x0, [fp, #-0x10]
    // 0xd4a048: r2 = LoadInt32Instr(r0)
    //     0xd4a048: sbfx            x2, x0, #1, #0x1f
    //     0xd4a04c: tbz             w0, #0, #0xd4a054
    //     0xd4a050: ldur            x2, [x0, #7]
    // 0xd4a054: ldur            x1, [fp, #-8]
    // 0xd4a058: r0 = putInt64()
    //     0xd4a058: bl              #0xd49bfc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::putInt64
    // 0xd4a05c: b               #0xd4a0d0
    // 0xd4a060: cmp             x1, #0x1a5
    // 0xd4a064: b.ne            #0xd4a090
    // 0xd4a068: ldur            x1, [fp, #-8]
    // 0xd4a06c: r2 = 129
    //     0xd4a06c: movz            x2, #0x81
    // 0xd4a070: r0 = _add()
    //     0xd4a070: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd4a074: ldur            x1, [fp, #-0x10]
    // 0xd4a078: r0 = props()
    //     0xd4a078: bl              #0xbdbd40  ; [package:nuonline/app/data/models/event.dart] Event::props
    // 0xd4a07c: ldur            x1, [fp, #-0x18]
    // 0xd4a080: ldur            x2, [fp, #-8]
    // 0xd4a084: mov             x3, x0
    // 0xd4a088: r0 = writeValue()
    //     0xd4a088: bl              #0xd49fec  ; [package:url_launcher_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xd4a08c: b               #0xd4a0d0
    // 0xd4a090: cmp             x1, #0x1a4
    // 0xd4a094: b.ne            #0xd4a0c0
    // 0xd4a098: ldur            x1, [fp, #-8]
    // 0xd4a09c: r2 = 130
    //     0xd4a09c: movz            x2, #0x82
    // 0xd4a0a0: r0 = _add()
    //     0xd4a0a0: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd4a0a4: ldur            x1, [fp, #-0x10]
    // 0xd4a0a8: r0 = props()
    //     0xd4a0a8: bl              #0xbdc38c  ; [package:nuonline/app/data/models/zakat_menu.dart] ZakatMenu::props
    // 0xd4a0ac: ldur            x1, [fp, #-0x18]
    // 0xd4a0b0: ldur            x2, [fp, #-8]
    // 0xd4a0b4: mov             x3, x0
    // 0xd4a0b8: r0 = writeValue()
    //     0xd4a0b8: bl              #0xd49fec  ; [package:url_launcher_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xd4a0bc: b               #0xd4a0d0
    // 0xd4a0c0: ldur            x1, [fp, #-0x18]
    // 0xd4a0c4: ldur            x2, [fp, #-8]
    // 0xd4a0c8: ldur            x3, [fp, #-0x10]
    // 0xd4a0cc: r0 = writeValue()
    //     0xd4a0cc: bl              #0xd4a458  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::writeValue
    // 0xd4a0d0: r0 = Null
    //     0xd4a0d0: mov             x0, NULL
    // 0xd4a0d4: LeaveFrame
    //     0xd4a0d4: mov             SP, fp
    //     0xd4a0d8: ldp             fp, lr, [SP], #0x10
    // 0xd4a0dc: ret
    //     0xd4a0dc: ret             
    // 0xd4a0e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4a0e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4a0e4: b               #0xd4a01c
  }
}
