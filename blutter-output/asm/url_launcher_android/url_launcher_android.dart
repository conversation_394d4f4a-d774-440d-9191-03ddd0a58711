// lib: , url: package:url_launcher_android/url_launcher_android.dart

// class id: 1051220, size: 0x8
class :: {
}

// class id: 5866, size: 0xc, field offset: 0x8
class UrlLauncherAndroid extends UrlLauncherPlatform {

  _ canLaunch(/* No info */) async {
    // ** addr: 0xd3d2b4, size: 0x10c
    // 0xd3d2b4: EnterFrame
    //     0xd3d2b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd3d2b8: mov             fp, SP
    // 0xd3d2bc: AllocStack(0x38)
    //     0xd3d2bc: sub             SP, SP, #0x38
    // 0xd3d2c0: SetupParameters(UrlLauncherAndroid this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xd3d2c0: stur            NULL, [fp, #-8]
    //     0xd3d2c4: stur            x1, [fp, #-0x10]
    //     0xd3d2c8: stur            x2, [fp, #-0x18]
    // 0xd3d2cc: CheckStackOverflow
    //     0xd3d2cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3d2d0: cmp             SP, x16
    //     0xd3d2d4: b.ls            #0xd3d3b8
    // 0xd3d2d8: InitAsync() -> Future<bool>
    //     0xd3d2d8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0xd3d2dc: bl              #0x661298  ; InitAsyncStub
    // 0xd3d2e0: ldur            x0, [fp, #-0x10]
    // 0xd3d2e4: LoadField: r3 = r0->field_7
    //     0xd3d2e4: ldur            w3, [x0, #7]
    // 0xd3d2e8: DecompressPointer r3
    //     0xd3d2e8: add             x3, x3, HEAP, lsl #32
    // 0xd3d2ec: mov             x1, x3
    // 0xd3d2f0: ldur            x2, [fp, #-0x18]
    // 0xd3d2f4: stur            x3, [fp, #-0x20]
    // 0xd3d2f8: r0 = canLaunchUrl()
    //     0xd3d2f8: bl              #0xd3d458  ; [package:url_launcher_android/src/messages.g.dart] UrlLauncherApi::canLaunchUrl
    // 0xd3d2fc: mov             x1, x0
    // 0xd3d300: stur            x1, [fp, #-0x28]
    // 0xd3d304: r0 = Await()
    //     0xd3d304: bl              #0x661044  ; AwaitStub
    // 0xd3d308: stur            x0, [fp, #-0x28]
    // 0xd3d30c: r16 = true
    //     0xd3d30c: add             x16, NULL, #0x20  ; true
    // 0xd3d310: cmp             w0, w16
    // 0xd3d314: b.eq            #0xd3d3b0
    // 0xd3d318: ldur            x1, [fp, #-0x10]
    // 0xd3d31c: ldur            x2, [fp, #-0x18]
    // 0xd3d320: r0 = _getUrlScheme()
    //     0xd3d320: bl              #0xd3d3c0  ; [package:url_launcher_android/url_launcher_android.dart] UrlLauncherAndroid::_getUrlScheme
    // 0xd3d324: mov             x1, x0
    // 0xd3d328: stur            x1, [fp, #-0x10]
    // 0xd3d32c: r0 = LoadClassIdInstr(r1)
    //     0xd3d32c: ldur            x0, [x1, #-1]
    //     0xd3d330: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d334: r16 = "http"
    //     0xd3d334: ldr             x16, [PP, #0xc58]  ; [pp+0xc58] "http"
    // 0xd3d338: stp             x16, x1, [SP]
    // 0xd3d33c: mov             lr, x0
    // 0xd3d340: ldr             lr, [x21, lr, lsl #3]
    // 0xd3d344: blr             lr
    // 0xd3d348: tbz             w0, #4, #0xd3d370
    // 0xd3d34c: ldur            x1, [fp, #-0x10]
    // 0xd3d350: r0 = LoadClassIdInstr(r1)
    //     0xd3d350: ldur            x0, [x1, #-1]
    //     0xd3d354: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d358: r16 = "https"
    //     0xd3d358: ldr             x16, [PP, #0xc68]  ; [pp+0xc68] "https"
    // 0xd3d35c: stp             x16, x1, [SP]
    // 0xd3d360: mov             lr, x0
    // 0xd3d364: ldr             lr, [x21, lr, lsl #3]
    // 0xd3d368: blr             lr
    // 0xd3d36c: tbnz            w0, #4, #0xd3d3b0
    // 0xd3d370: ldur            x0, [fp, #-0x10]
    // 0xd3d374: r1 = Null
    //     0xd3d374: mov             x1, NULL
    // 0xd3d378: r2 = 4
    //     0xd3d378: movz            x2, #0x4
    // 0xd3d37c: r0 = AllocateArray()
    //     0xd3d37c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd3d380: mov             x1, x0
    // 0xd3d384: ldur            x0, [fp, #-0x10]
    // 0xd3d388: StoreField: r1->field_f = r0
    //     0xd3d388: stur            w0, [x1, #0xf]
    // 0xd3d38c: r16 = "://flutter.dev"
    //     0xd3d38c: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3b2d8] "://flutter.dev"
    //     0xd3d390: ldr             x16, [x16, #0x2d8]
    // 0xd3d394: StoreField: r1->field_13 = r16
    //     0xd3d394: stur            w16, [x1, #0x13]
    // 0xd3d398: str             x1, [SP]
    // 0xd3d39c: r0 = _interpolate()
    //     0xd3d39c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xd3d3a0: ldur            x1, [fp, #-0x20]
    // 0xd3d3a4: mov             x2, x0
    // 0xd3d3a8: r0 = canLaunchUrl()
    //     0xd3d3a8: bl              #0xd3d458  ; [package:url_launcher_android/src/messages.g.dart] UrlLauncherApi::canLaunchUrl
    // 0xd3d3ac: r0 = ReturnAsync()
    //     0xd3d3ac: b               #0x6576a4  ; ReturnAsyncStub
    // 0xd3d3b0: ldur            x0, [fp, #-0x28]
    // 0xd3d3b4: r0 = ReturnAsync()
    //     0xd3d3b4: b               #0x6576a4  ; ReturnAsyncStub
    // 0xd3d3b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3d3b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3d3bc: b               #0xd3d2d8
  }
  _ _getUrlScheme(/* No info */) {
    // ** addr: 0xd3d3c0, size: 0x98
    // 0xd3d3c0: EnterFrame
    //     0xd3d3c0: stp             fp, lr, [SP, #-0x10]!
    //     0xd3d3c4: mov             fp, SP
    // 0xd3d3c8: AllocStack(0x10)
    //     0xd3d3c8: sub             SP, SP, #0x10
    // 0xd3d3cc: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xd3d3cc: mov             x3, x2
    //     0xd3d3d0: stur            x2, [fp, #-8]
    // 0xd3d3d4: CheckStackOverflow
    //     0xd3d3d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3d3d8: cmp             SP, x16
    //     0xd3d3dc: b.ls            #0xd3d450
    // 0xd3d3e0: r0 = LoadClassIdInstr(r3)
    //     0xd3d3e0: ldur            x0, [x3, #-1]
    //     0xd3d3e4: ubfx            x0, x0, #0xc, #0x14
    // 0xd3d3e8: mov             x1, x3
    // 0xd3d3ec: r2 = ":"
    //     0xd3d3ec: ldr             x2, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xd3d3f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xd3d3f0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xd3d3f4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xd3d3f4: sub             lr, x0, #0xffa
    //     0xd3d3f8: ldr             lr, [x21, lr, lsl #3]
    //     0xd3d3fc: blr             lr
    // 0xd3d400: mov             x2, x0
    // 0xd3d404: cmn             x2, #1
    // 0xd3d408: b.ne            #0xd3d41c
    // 0xd3d40c: r0 = ""
    //     0xd3d40c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xd3d410: LeaveFrame
    //     0xd3d410: mov             SP, fp
    //     0xd3d414: ldp             fp, lr, [SP], #0x10
    // 0xd3d418: ret
    //     0xd3d418: ret             
    // 0xd3d41c: r0 = BoxInt64Instr(r2)
    //     0xd3d41c: sbfiz           x0, x2, #1, #0x1f
    //     0xd3d420: cmp             x2, x0, asr #1
    //     0xd3d424: b.eq            #0xd3d430
    //     0xd3d428: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd3d42c: stur            x2, [x0, #7]
    // 0xd3d430: str             x0, [SP]
    // 0xd3d434: ldur            x1, [fp, #-8]
    // 0xd3d438: r2 = 0
    //     0xd3d438: movz            x2, #0
    // 0xd3d43c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xd3d43c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xd3d440: r0 = substring()
    //     0xd3d440: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xd3d444: LeaveFrame
    //     0xd3d444: mov             SP, fp
    //     0xd3d448: ldp             fp, lr, [SP], #0x10
    // 0xd3d44c: ret
    //     0xd3d44c: ret             
    // 0xd3d450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3d450: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3d454: b               #0xd3d3e0
  }
  _ launchUrl(/* No info */) async {
    // ** addr: 0xd74920, size: 0x1f8
    // 0xd74920: EnterFrame
    //     0xd74920: stp             fp, lr, [SP, #-0x10]!
    //     0xd74924: mov             fp, SP
    // 0xd74928: AllocStack(0x38)
    //     0xd74928: sub             SP, SP, #0x38
    // 0xd7492c: SetupParameters(UrlLauncherAndroid this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xd7492c: stur            NULL, [fp, #-8]
    //     0xd74930: stur            x1, [fp, #-0x10]
    //     0xd74934: mov             x16, x2
    //     0xd74938: mov             x2, x1
    //     0xd7493c: mov             x1, x16
    //     0xd74940: stur            x1, [fp, #-0x18]
    //     0xd74944: stur            x3, [fp, #-0x20]
    // 0xd74948: CheckStackOverflow
    //     0xd74948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7494c: cmp             SP, x16
    //     0xd74950: b.ls            #0xd74b10
    // 0xd74954: InitAsync() -> Future<bool>
    //     0xd74954: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0xd74958: bl              #0x661298  ; InitAsyncStub
    // 0xd7495c: ldur            x0, [fp, #-0x20]
    // 0xd74960: LoadField: r3 = r0->field_7
    //     0xd74960: ldur            w3, [x0, #7]
    // 0xd74964: DecompressPointer r3
    //     0xd74964: add             x3, x3, HEAP, lsl #32
    // 0xd74968: stur            x3, [fp, #-0x28]
    // 0xd7496c: LoadField: r2 = r3->field_7
    //     0xd7496c: ldur            x2, [x3, #7]
    // 0xd74970: cmp             x2, #2
    // 0xd74974: b.gt            #0xd74990
    // 0xd74978: cmp             x2, #1
    // 0xd7497c: b.gt            #0xd74988
    // 0xd74980: cmp             x2, #0
    // 0xd74984: b.le            #0xd749bc
    // 0xd74988: mov             x0, x3
    // 0xd7498c: b               #0xd749f0
    // 0xd74990: cmp             x2, #3
    // 0xd74994: b.le            #0xd749b4
    // 0xd74998: r0 = BoxInt64Instr(r2)
    //     0xd74998: sbfiz           x0, x2, #1, #0x1f
    //     0xd7499c: cmp             x2, x0, asr #1
    //     0xd749a0: b.eq            #0xd749ac
    //     0xd749a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd749a8: stur            x2, [x0, #7]
    // 0xd749ac: cmp             w0, #8
    // 0xd749b0: b.ne            #0xd749bc
    // 0xd749b4: ldur            x1, [fp, #-0x10]
    // 0xd749b8: b               #0xd74a7c
    // 0xd749bc: ldur            x1, [fp, #-0x18]
    // 0xd749c0: r2 = "http:"
    //     0xd749c0: add             x2, PP, #0xc, lsl #12  ; [pp+0xcca0] "http:"
    //     0xd749c4: ldr             x2, [x2, #0xca0]
    // 0xd749c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xd749c8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xd749cc: r0 = startsWith()
    //     0xd749cc: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xd749d0: tbz             w0, #4, #0xd749ec
    // 0xd749d4: ldur            x1, [fp, #-0x18]
    // 0xd749d8: r2 = "https:"
    //     0xd749d8: add             x2, PP, #0xc, lsl #12  ; [pp+0xcca8] "https:"
    //     0xd749dc: ldr             x2, [x2, #0xca8]
    // 0xd749e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xd749e0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xd749e4: r0 = startsWith()
    //     0xd749e4: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xd749e8: tbnz            w0, #4, #0xd74a78
    // 0xd749ec: ldur            x0, [fp, #-0x28]
    // 0xd749f0: ldur            x1, [fp, #-0x10]
    // 0xd749f4: LoadField: r2 = r1->field_7
    //     0xd749f4: ldur            w2, [x1, #7]
    // 0xd749f8: DecompressPointer r2
    //     0xd749f8: add             x2, x2, HEAP, lsl #32
    // 0xd749fc: stur            x2, [fp, #-0x30]
    // 0xd74a00: r16 = Instance_PreferredLaunchMode
    //     0xd74a00: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac18] Obj!PreferredLaunchMode@e2de41
    //     0xd74a04: ldr             x16, [x16, #0xc18]
    // 0xd74a08: cmp             w0, w16
    // 0xd74a0c: r16 = true
    //     0xd74a0c: add             x16, NULL, #0x20  ; true
    // 0xd74a10: r17 = false
    //     0xd74a10: add             x17, NULL, #0x30  ; false
    // 0xd74a14: csel            x3, x16, x17, ne
    // 0xd74a18: stur            x3, [fp, #-0x20]
    // 0xd74a1c: r0 = WebViewOptions()
    //     0xd74a1c: bl              #0xce76e4  ; AllocateWebViewOptionsStub -> WebViewOptions (size=0x14)
    // 0xd74a20: mov             x1, x0
    // 0xd74a24: r0 = true
    //     0xd74a24: add             x0, NULL, #0x20  ; true
    // 0xd74a28: stur            x1, [fp, #-0x28]
    // 0xd74a2c: StoreField: r1->field_7 = r0
    //     0xd74a2c: stur            w0, [x1, #7]
    // 0xd74a30: StoreField: r1->field_b = r0
    //     0xd74a30: stur            w0, [x1, #0xb]
    // 0xd74a34: r0 = _ConstMap len:0
    //     0xd74a34: add             x0, PP, #0x1a, lsl #12  ; [pp+0x1ac48] Map<String, String>(0)
    //     0xd74a38: ldr             x0, [x0, #0xc48]
    // 0xd74a3c: StoreField: r1->field_f = r0
    //     0xd74a3c: stur            w0, [x1, #0xf]
    // 0xd74a40: r0 = BrowserOptions()
    //     0xd74a40: bl              #0xce74ec  ; AllocateBrowserOptionsStub -> BrowserOptions (size=0xc)
    // 0xd74a44: mov             x1, x0
    // 0xd74a48: r0 = false
    //     0xd74a48: add             x0, NULL, #0x30  ; false
    // 0xd74a4c: StoreField: r1->field_7 = r0
    //     0xd74a4c: stur            w0, [x1, #7]
    // 0xd74a50: mov             x6, x1
    // 0xd74a54: ldur            x1, [fp, #-0x30]
    // 0xd74a58: ldur            x2, [fp, #-0x18]
    // 0xd74a5c: ldur            x3, [fp, #-0x20]
    // 0xd74a60: ldur            x5, [fp, #-0x28]
    // 0xd74a64: r0 = openUrlInApp()
    //     0xd74a64: bl              #0xd74e80  ; [package:url_launcher_android/src/messages.g.dart] UrlLauncherApi::openUrlInApp
    // 0xd74a68: mov             x1, x0
    // 0xd74a6c: stur            x1, [fp, #-0x20]
    // 0xd74a70: r0 = Await()
    //     0xd74a70: bl              #0x661044  ; AwaitStub
    // 0xd74a74: b               #0xd74a9c
    // 0xd74a78: ldur            x1, [fp, #-0x10]
    // 0xd74a7c: LoadField: r0 = r1->field_7
    //     0xd74a7c: ldur            w0, [x1, #7]
    // 0xd74a80: DecompressPointer r0
    //     0xd74a80: add             x0, x0, HEAP, lsl #32
    // 0xd74a84: mov             x1, x0
    // 0xd74a88: ldur            x2, [fp, #-0x18]
    // 0xd74a8c: r0 = launchUrl()
    //     0xd74a8c: bl              #0xd74b18  ; [package:url_launcher_android/src/messages.g.dart] UrlLauncherApi::launchUrl
    // 0xd74a90: mov             x1, x0
    // 0xd74a94: stur            x1, [fp, #-0x10]
    // 0xd74a98: r0 = Await()
    //     0xd74a98: bl              #0x661044  ; AwaitStub
    // 0xd74a9c: r16 = true
    //     0xd74a9c: add             x16, NULL, #0x20  ; true
    // 0xd74aa0: cmp             w0, w16
    // 0xd74aa4: b.ne            #0xd74aac
    // 0xd74aa8: r0 = ReturnAsync()
    //     0xd74aa8: b               #0x6576a4  ; ReturnAsyncStub
    // 0xd74aac: ldur            x0, [fp, #-0x18]
    // 0xd74ab0: r1 = Null
    //     0xd74ab0: mov             x1, NULL
    // 0xd74ab4: r2 = 6
    //     0xd74ab4: movz            x2, #0x6
    // 0xd74ab8: r0 = AllocateArray()
    //     0xd74ab8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd74abc: r16 = "No Activity found to handle intent { "
    //     0xd74abc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22730] "No Activity found to handle intent { "
    //     0xd74ac0: ldr             x16, [x16, #0x730]
    // 0xd74ac4: StoreField: r0->field_f = r16
    //     0xd74ac4: stur            w16, [x0, #0xf]
    // 0xd74ac8: ldur            x1, [fp, #-0x18]
    // 0xd74acc: StoreField: r0->field_13 = r1
    //     0xd74acc: stur            w1, [x0, #0x13]
    // 0xd74ad0: r16 = " }"
    //     0xd74ad0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22738] " }"
    //     0xd74ad4: ldr             x16, [x16, #0x738]
    // 0xd74ad8: ArrayStore: r0[0] = r16  ; List_4
    //     0xd74ad8: stur            w16, [x0, #0x17]
    // 0xd74adc: str             x0, [SP]
    // 0xd74ae0: r0 = _interpolate()
    //     0xd74ae0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xd74ae4: stur            x0, [fp, #-0x10]
    // 0xd74ae8: r0 = PlatformException()
    //     0xd74ae8: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xd74aec: mov             x1, x0
    // 0xd74af0: r0 = "ACTIVITY_NOT_FOUND"
    //     0xd74af0: add             x0, PP, #0x22, lsl #12  ; [pp+0x22740] "ACTIVITY_NOT_FOUND"
    //     0xd74af4: ldr             x0, [x0, #0x740]
    // 0xd74af8: StoreField: r1->field_7 = r0
    //     0xd74af8: stur            w0, [x1, #7]
    // 0xd74afc: ldur            x0, [fp, #-0x10]
    // 0xd74b00: StoreField: r1->field_b = r0
    //     0xd74b00: stur            w0, [x1, #0xb]
    // 0xd74b04: mov             x0, x1
    // 0xd74b08: r0 = Throw()
    //     0xd74b08: bl              #0xec04b8  ; ThrowStub
    // 0xd74b0c: brk             #0
    // 0xd74b10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd74b10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd74b14: b               #0xd74954
  }
  static void registerWith() {
    // ** addr: 0xec6fb8, size: 0xb0
    // 0xec6fb8: EnterFrame
    //     0xec6fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xec6fbc: mov             fp, SP
    // 0xec6fc0: AllocStack(0x10)
    //     0xec6fc0: sub             SP, SP, #0x10
    // 0xec6fc4: CheckStackOverflow
    //     0xec6fc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec6fc8: cmp             SP, x16
    //     0xec6fcc: b.ls            #0xec7060
    // 0xec6fd0: r0 = UrlLauncherApi()
    //     0xec6fd0: bl              #0xec70e0  ; AllocateUrlLauncherApiStub -> UrlLauncherApi (size=0x10)
    // 0xec6fd4: mov             x1, x0
    // 0xec6fd8: r0 = ""
    //     0xec6fd8: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xec6fdc: stur            x1, [fp, #-8]
    // 0xec6fe0: StoreField: r1->field_b = r0
    //     0xec6fe0: stur            w0, [x1, #0xb]
    // 0xec6fe4: r0 = UrlLauncherAndroid()
    //     0xec6fe4: bl              #0xec70d4  ; AllocateUrlLauncherAndroidStub -> UrlLauncherAndroid (size=0xc)
    // 0xec6fe8: mov             x1, x0
    // 0xec6fec: ldur            x0, [fp, #-8]
    // 0xec6ff0: stur            x1, [fp, #-0x10]
    // 0xec6ff4: StoreField: r1->field_7 = r0
    //     0xec6ff4: stur            w0, [x1, #7]
    // 0xec6ff8: r0 = InitLateStaticField(0x610) // [package:url_launcher_platform_interface/src/url_launcher_platform.dart] UrlLauncherPlatform::_token
    //     0xec6ff8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec6ffc: ldr             x0, [x0, #0xc20]
    //     0xec7000: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec7004: cmp             w0, w16
    //     0xec7008: b.ne            #0xec7018
    //     0xec700c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc750] Field <UrlLauncherPlatform._token@681332722>: static late final (offset: 0x610)
    //     0xec7010: ldr             x2, [x2, #0x750]
    //     0xec7014: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec7018: stur            x0, [fp, #-8]
    // 0xec701c: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xec701c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec7020: ldr             x0, [x0, #0xc08]
    //     0xec7024: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec7028: cmp             w0, w16
    //     0xec702c: b.ne            #0xec7038
    //     0xec7030: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xec7034: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec7038: mov             x1, x0
    // 0xec703c: ldur            x2, [fp, #-0x10]
    // 0xec7040: ldur            x3, [fp, #-8]
    // 0xec7044: r0 = []=()
    //     0xec7044: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xec7048: ldur            x1, [fp, #-0x10]
    // 0xec704c: r0 = instance=()
    //     0xec704c: bl              #0xec7068  ; [package:url_launcher_platform_interface/src/url_launcher_platform.dart] UrlLauncherPlatform::instance=
    // 0xec7050: r0 = Null
    //     0xec7050: mov             x0, NULL
    // 0xec7054: LeaveFrame
    //     0xec7054: mov             SP, fp
    //     0xec7058: ldp             fp, lr, [SP], #0x10
    // 0xec705c: ret
    //     0xec705c: ret             
    // 0xec7060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec7060: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec7064: b               #0xec6fd0
  }
}
