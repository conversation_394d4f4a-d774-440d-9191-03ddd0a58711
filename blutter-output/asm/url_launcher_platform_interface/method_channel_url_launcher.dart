// lib: , url: package:url_launcher_platform_interface/method_channel_url_launcher.dart

// class id: 1051221, size: 0x8
class :: {
}

// class id: 5865, size: 0x8, field offset: 0x8
class MethodChannelUrlLauncher extends UrlLauncherPlatform {

  [closure] bool <anonymous closure>(dynamic, bool?) {
    // ** addr: 0x7da194, size: 0x1c
    // 0x7da194: ldr             x1, [SP]
    // 0x7da198: cmp             w1, NULL
    // 0x7da19c: b.ne            #0x7da1a8
    // 0x7da1a0: r0 = false
    //     0x7da1a0: add             x0, NULL, #0x30  ; false
    // 0x7da1a4: b               #0x7da1ac
    // 0x7da1a8: mov             x0, x1
    // 0x7da1ac: ret
    //     0x7da1ac: ret             
  }
  _ launch(/* No info */) {
    // ** addr: 0x7da1b0, size: 0x134
    // 0x7da1b0: EnterFrame
    //     0x7da1b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7da1b4: mov             fp, SP
    // 0x7da1b8: AllocStack(0x30)
    //     0x7da1b8: sub             SP, SP, #0x30
    // 0x7da1bc: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r7 => r7, fp-0x10 */)
    //     0x7da1bc: mov             x0, x2
    //     0x7da1c0: stur            x2, [fp, #-8]
    //     0x7da1c4: stur            x7, [fp, #-0x10]
    // 0x7da1c8: CheckStackOverflow
    //     0x7da1c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7da1cc: cmp             SP, x16
    //     0x7da1d0: b.ls            #0x7da2dc
    // 0x7da1d4: r1 = Null
    //     0x7da1d4: mov             x1, NULL
    // 0x7da1d8: r2 = 28
    //     0x7da1d8: movz            x2, #0x1c
    // 0x7da1dc: r0 = AllocateArray()
    //     0x7da1dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x7da1e0: r16 = "url"
    //     0x7da1e0: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0x7da1e4: ldr             x16, [x16, #0xd78]
    // 0x7da1e8: StoreField: r0->field_f = r16
    //     0x7da1e8: stur            w16, [x0, #0xf]
    // 0x7da1ec: ldur            x1, [fp, #-8]
    // 0x7da1f0: StoreField: r0->field_13 = r1
    //     0x7da1f0: stur            w1, [x0, #0x13]
    // 0x7da1f4: r16 = "useSafariVC"
    //     0x7da1f4: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f58] "useSafariVC"
    //     0x7da1f8: ldr             x16, [x16, #0xf58]
    // 0x7da1fc: ArrayStore: r0[0] = r16  ; List_4
    //     0x7da1fc: stur            w16, [x0, #0x17]
    // 0x7da200: ldr             x1, [fp, #0x18]
    // 0x7da204: StoreField: r0->field_1b = r1
    //     0x7da204: stur            w1, [x0, #0x1b]
    // 0x7da208: r16 = "useWebView"
    //     0x7da208: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f60] "useWebView"
    //     0x7da20c: ldr             x16, [x16, #0xf60]
    // 0x7da210: StoreField: r0->field_1f = r16
    //     0x7da210: stur            w16, [x0, #0x1f]
    // 0x7da214: ldr             x1, [fp, #0x10]
    // 0x7da218: StoreField: r0->field_23 = r1
    //     0x7da218: stur            w1, [x0, #0x23]
    // 0x7da21c: r16 = "enableJavaScript"
    //     0x7da21c: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f68] "enableJavaScript"
    //     0x7da220: ldr             x16, [x16, #0xf68]
    // 0x7da224: StoreField: r0->field_27 = r16
    //     0x7da224: stur            w16, [x0, #0x27]
    // 0x7da228: r16 = true
    //     0x7da228: add             x16, NULL, #0x20  ; true
    // 0x7da22c: StoreField: r0->field_2b = r16
    //     0x7da22c: stur            w16, [x0, #0x2b]
    // 0x7da230: r16 = "enableDomStorage"
    //     0x7da230: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f70] "enableDomStorage"
    //     0x7da234: ldr             x16, [x16, #0xf70]
    // 0x7da238: StoreField: r0->field_2f = r16
    //     0x7da238: stur            w16, [x0, #0x2f]
    // 0x7da23c: r16 = true
    //     0x7da23c: add             x16, NULL, #0x20  ; true
    // 0x7da240: StoreField: r0->field_33 = r16
    //     0x7da240: stur            w16, [x0, #0x33]
    // 0x7da244: r16 = "universalLinksOnly"
    //     0x7da244: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f78] "universalLinksOnly"
    //     0x7da248: ldr             x16, [x16, #0xf78]
    // 0x7da24c: StoreField: r0->field_37 = r16
    //     0x7da24c: stur            w16, [x0, #0x37]
    // 0x7da250: ldur            x1, [fp, #-0x10]
    // 0x7da254: StoreField: r0->field_3b = r1
    //     0x7da254: stur            w1, [x0, #0x3b]
    // 0x7da258: r16 = "headers"
    //     0x7da258: add             x16, PP, #0xe, lsl #12  ; [pp+0xe8a8] "headers"
    //     0x7da25c: ldr             x16, [x16, #0x8a8]
    // 0x7da260: StoreField: r0->field_3f = r16
    //     0x7da260: stur            w16, [x0, #0x3f]
    // 0x7da264: r16 = _ConstMap len:0
    //     0x7da264: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac48] Map<String, String>(0)
    //     0x7da268: ldr             x16, [x16, #0xc48]
    // 0x7da26c: StoreField: r0->field_43 = r16
    //     0x7da26c: stur            w16, [x0, #0x43]
    // 0x7da270: r16 = <String, Object>
    //     0x7da270: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0x7da274: ldr             x16, [x16, #0x790]
    // 0x7da278: stp             x0, x16, [SP]
    // 0x7da27c: r0 = Map._fromLiteral()
    //     0x7da27c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x7da280: r16 = <bool>
    //     0x7da280: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x7da284: r30 = Instance_MethodChannel
    //     0x7da284: add             lr, PP, #0x21, lsl #12  ; [pp+0x21f80] Obj!MethodChannel@e11051
    //     0x7da288: ldr             lr, [lr, #0xf80]
    // 0x7da28c: stp             lr, x16, [SP, #0x10]
    // 0x7da290: r16 = "launch"
    //     0x7da290: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f88] "launch"
    //     0x7da294: ldr             x16, [x16, #0xf88]
    // 0x7da298: stp             x0, x16, [SP]
    // 0x7da29c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x7da29c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x7da2a0: r0 = invokeMethod()
    //     0x7da2a0: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x7da2a4: r1 = Function '<anonymous closure>':.
    //     0x7da2a4: add             x1, PP, #0x21, lsl #12  ; [pp+0x21f90] AnonymousClosure: (0x7da194), in [package:url_launcher_platform_interface/method_channel_url_launcher.dart] MethodChannelUrlLauncher::launch (0x7da1b0)
    //     0x7da2a8: ldr             x1, [x1, #0xf90]
    // 0x7da2ac: r2 = Null
    //     0x7da2ac: mov             x2, NULL
    // 0x7da2b0: stur            x0, [fp, #-8]
    // 0x7da2b4: r0 = AllocateClosure()
    //     0x7da2b4: bl              #0xec1630  ; AllocateClosureStub
    // 0x7da2b8: r16 = <bool>
    //     0x7da2b8: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x7da2bc: ldur            lr, [fp, #-8]
    // 0x7da2c0: stp             lr, x16, [SP, #8]
    // 0x7da2c4: str             x0, [SP]
    // 0x7da2c8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x7da2c8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x7da2cc: r0 = then()
    //     0x7da2cc: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0x7da2d0: LeaveFrame
    //     0x7da2d0: mov             SP, fp
    //     0x7da2d4: ldp             fp, lr, [SP], #0x10
    // 0x7da2d8: ret
    //     0x7da2d8: ret             
    // 0x7da2dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7da2dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7da2e0: b               #0x7da1d4
  }
  _ canLaunch(/* No info */) {
    // ** addr: 0xd3d7b4, size: 0xb4
    // 0xd3d7b4: EnterFrame
    //     0xd3d7b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd3d7b8: mov             fp, SP
    // 0xd3d7bc: AllocStack(0x28)
    //     0xd3d7bc: sub             SP, SP, #0x28
    // 0xd3d7c0: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xd3d7c0: mov             x0, x2
    //     0xd3d7c4: stur            x2, [fp, #-8]
    // 0xd3d7c8: CheckStackOverflow
    //     0xd3d7c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd3d7cc: cmp             SP, x16
    //     0xd3d7d0: b.ls            #0xd3d860
    // 0xd3d7d4: r1 = Null
    //     0xd3d7d4: mov             x1, NULL
    // 0xd3d7d8: r2 = 4
    //     0xd3d7d8: movz            x2, #0x4
    // 0xd3d7dc: r0 = AllocateArray()
    //     0xd3d7dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xd3d7e0: r16 = "url"
    //     0xd3d7e0: add             x16, PP, #0xb, lsl #12  ; [pp+0xbd78] "url"
    //     0xd3d7e4: ldr             x16, [x16, #0xd78]
    // 0xd3d7e8: StoreField: r0->field_f = r16
    //     0xd3d7e8: stur            w16, [x0, #0xf]
    // 0xd3d7ec: ldur            x1, [fp, #-8]
    // 0xd3d7f0: StoreField: r0->field_13 = r1
    //     0xd3d7f0: stur            w1, [x0, #0x13]
    // 0xd3d7f4: r16 = <String, Object>
    //     0xd3d7f4: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xd3d7f8: ldr             x16, [x16, #0x790]
    // 0xd3d7fc: stp             x0, x16, [SP]
    // 0xd3d800: r0 = Map._fromLiteral()
    //     0xd3d800: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xd3d804: r16 = <bool>
    //     0xd3d804: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xd3d808: r30 = Instance_MethodChannel
    //     0xd3d808: add             lr, PP, #0x21, lsl #12  ; [pp+0x21f80] Obj!MethodChannel@e11051
    //     0xd3d80c: ldr             lr, [lr, #0xf80]
    // 0xd3d810: stp             lr, x16, [SP, #0x10]
    // 0xd3d814: r16 = "canLaunch"
    //     0xd3d814: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbf0] "canLaunch"
    //     0xd3d818: ldr             x16, [x16, #0xbf0]
    // 0xd3d81c: stp             x0, x16, [SP]
    // 0xd3d820: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xd3d820: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xd3d824: r0 = invokeMethod()
    //     0xd3d824: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xd3d828: r1 = Function '<anonymous closure>':.
    //     0xd3d828: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbf8] AnonymousClosure: (0x7da194), in [package:url_launcher_platform_interface/method_channel_url_launcher.dart] MethodChannelUrlLauncher::launch (0x7da1b0)
    //     0xd3d82c: ldr             x1, [x1, #0xbf8]
    // 0xd3d830: r2 = Null
    //     0xd3d830: mov             x2, NULL
    // 0xd3d834: stur            x0, [fp, #-8]
    // 0xd3d838: r0 = AllocateClosure()
    //     0xd3d838: bl              #0xec1630  ; AllocateClosureStub
    // 0xd3d83c: r16 = <bool>
    //     0xd3d83c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xd3d840: ldur            lr, [fp, #-8]
    // 0xd3d844: stp             lr, x16, [SP, #8]
    // 0xd3d848: str             x0, [SP]
    // 0xd3d84c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xd3d84c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xd3d850: r0 = then()
    //     0xd3d850: bl              #0xd69fb8  ; [dart:async] _Future::then
    // 0xd3d854: LeaveFrame
    //     0xd3d854: mov             SP, fp
    //     0xd3d858: ldp             fp, lr, [SP], #0x10
    // 0xd3d85c: ret
    //     0xd3d85c: ret             
    // 0xd3d860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd3d860: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd3d864: b               #0xd3d7d4
  }
}
