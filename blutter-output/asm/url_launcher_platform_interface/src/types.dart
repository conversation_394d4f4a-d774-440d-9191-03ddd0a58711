// lib: , url: package:url_launcher_platform_interface/src/types.dart

// class id: 1051222, size: 0x8
class :: {
}

// class id: 416, size: 0x18, field offset: 0x8
//   const constructor, 
class LaunchOptions extends Object {
}

// class id: 417, size: 0xc, field offset: 0x8
//   const constructor, 
class InAppBrowserConfiguration extends Object {
}

// class id: 418, size: 0x14, field offset: 0x8
//   const constructor, 
class InAppWebViewConfiguration extends Object {
}

// class id: 6762, size: 0x14, field offset: 0x14
enum PreferredLaunchMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ecd4, size: 0x64
    // 0xc4ecd4: EnterFrame
    //     0xc4ecd4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ecd8: mov             fp, SP
    // 0xc4ecdc: AllocStack(0x10)
    //     0xc4ecdc: sub             SP, SP, #0x10
    // 0xc4ece0: SetupParameters(PreferredLaunchMode this /* r1 => r0, fp-0x8 */)
    //     0xc4ece0: mov             x0, x1
    //     0xc4ece4: stur            x1, [fp, #-8]
    // 0xc4ece8: CheckStackOverflow
    //     0xc4ece8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ecec: cmp             SP, x16
    //     0xc4ecf0: b.ls            #0xc4ed30
    // 0xc4ecf4: r1 = Null
    //     0xc4ecf4: mov             x1, NULL
    // 0xc4ecf8: r2 = 4
    //     0xc4ecf8: movz            x2, #0x4
    // 0xc4ecfc: r0 = AllocateArray()
    //     0xc4ecfc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ed00: r16 = "PreferredLaunchMode."
    //     0xc4ed00: add             x16, PP, #0x21, lsl #12  ; [pp+0x21f98] "PreferredLaunchMode."
    //     0xc4ed04: ldr             x16, [x16, #0xf98]
    // 0xc4ed08: StoreField: r0->field_f = r16
    //     0xc4ed08: stur            w16, [x0, #0xf]
    // 0xc4ed0c: ldur            x1, [fp, #-8]
    // 0xc4ed10: LoadField: r2 = r1->field_f
    //     0xc4ed10: ldur            w2, [x1, #0xf]
    // 0xc4ed14: DecompressPointer r2
    //     0xc4ed14: add             x2, x2, HEAP, lsl #32
    // 0xc4ed18: StoreField: r0->field_13 = r2
    //     0xc4ed18: stur            w2, [x0, #0x13]
    // 0xc4ed1c: str             x0, [SP]
    // 0xc4ed20: r0 = _interpolate()
    //     0xc4ed20: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ed24: LeaveFrame
    //     0xc4ed24: mov             SP, fp
    //     0xc4ed28: ldp             fp, lr, [SP], #0x10
    // 0xc4ed2c: ret
    //     0xc4ed2c: ret             
    // 0xc4ed30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ed30: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ed34: b               #0xc4ecf4
  }
}
