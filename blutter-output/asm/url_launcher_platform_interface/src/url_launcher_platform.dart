// lib: , url: package:url_launcher_platform_interface/src/url_launcher_platform.dart

// class id: 1051223, size: 0x8
class :: {
}

// class id: 5864, size: 0x8, field offset: 0x8
abstract class UrlLauncherPlatform extends PlatformInterface {

  static late final Object _token; // offset: 0x610
  static late UrlLauncherPlatform _instance; // offset: 0x614

  static UrlLauncherPlatform _instance() {
    // ** addr: 0x7d9f2c, size: 0x8c
    // 0x7d9f2c: EnterFrame
    //     0x7d9f2c: stp             fp, lr, [SP, #-0x10]!
    //     0x7d9f30: mov             fp, SP
    // 0x7d9f34: AllocStack(0x10)
    //     0x7d9f34: sub             SP, SP, #0x10
    // 0x7d9f38: CheckStackOverflow
    //     0x7d9f38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d9f3c: cmp             SP, x16
    //     0x7d9f40: b.ls            #0x7d9fb0
    // 0x7d9f44: r0 = InitLateStaticField(0x610) // [package:url_launcher_platform_interface/src/url_launcher_platform.dart] UrlLauncherPlatform::_token
    //     0x7d9f44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7d9f48: ldr             x0, [x0, #0xc20]
    //     0x7d9f4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7d9f50: cmp             w0, w16
    //     0x7d9f54: b.ne            #0x7d9f64
    //     0x7d9f58: add             x2, PP, #0xc, lsl #12  ; [pp+0xc750] Field <UrlLauncherPlatform._token@681332722>: static late final (offset: 0x610)
    //     0x7d9f5c: ldr             x2, [x2, #0x750]
    //     0x7d9f60: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7d9f64: stur            x0, [fp, #-8]
    // 0x7d9f68: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x7d9f68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7d9f6c: ldr             x0, [x0, #0xc08]
    //     0x7d9f70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7d9f74: cmp             w0, w16
    //     0x7d9f78: b.ne            #0x7d9f84
    //     0x7d9f7c: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x7d9f80: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x7d9f84: stur            x0, [fp, #-0x10]
    // 0x7d9f88: r0 = MethodChannelUrlLauncher()
    //     0x7d9f88: bl              #0x7da024  ; AllocateMethodChannelUrlLauncherStub -> MethodChannelUrlLauncher (size=0x8)
    // 0x7d9f8c: ldur            x1, [fp, #-0x10]
    // 0x7d9f90: mov             x2, x0
    // 0x7d9f94: ldur            x3, [fp, #-8]
    // 0x7d9f98: stur            x0, [fp, #-8]
    // 0x7d9f9c: r0 = []=()
    //     0x7d9f9c: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x7d9fa0: ldur            x0, [fp, #-8]
    // 0x7d9fa4: LeaveFrame
    //     0x7d9fa4: mov             SP, fp
    //     0x7d9fa8: ldp             fp, lr, [SP], #0x10
    // 0x7d9fac: ret
    //     0x7d9fac: ret             
    // 0x7d9fb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d9fb0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d9fb4: b               #0x7d9f44
  }
  _ launchUrl(/* No info */) {
    // ** addr: 0xd75200, size: 0x108
    // 0xd75200: EnterFrame
    //     0xd75200: stp             fp, lr, [SP, #-0x10]!
    //     0xd75204: mov             fp, SP
    // 0xd75208: AllocStack(0x28)
    //     0xd75208: sub             SP, SP, #0x28
    // 0xd7520c: SetupParameters(UrlLauncherPlatform this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xd7520c: mov             x4, x1
    //     0xd75210: mov             x0, x2
    //     0xd75214: stur            x1, [fp, #-8]
    //     0xd75218: stur            x2, [fp, #-0x10]
    //     0xd7521c: stur            x3, [fp, #-0x18]
    // 0xd75220: CheckStackOverflow
    //     0xd75220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd75224: cmp             SP, x16
    //     0xd75228: b.ls            #0xd75300
    // 0xd7522c: mov             x1, x0
    // 0xd75230: r2 = "http:"
    //     0xd75230: add             x2, PP, #0xc, lsl #12  ; [pp+0xcca0] "http:"
    //     0xd75234: ldr             x2, [x2, #0xca0]
    // 0xd75238: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xd75238: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xd7523c: r0 = startsWith()
    //     0xd7523c: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xd75240: tbnz            w0, #4, #0xd7524c
    // 0xd75244: r1 = true
    //     0xd75244: add             x1, NULL, #0x20  ; true
    // 0xd75248: b               #0xd75264
    // 0xd7524c: ldur            x1, [fp, #-0x10]
    // 0xd75250: r2 = "https:"
    //     0xd75250: add             x2, PP, #0xc, lsl #12  ; [pp+0xcca8] "https:"
    //     0xd75254: ldr             x2, [x2, #0xca8]
    // 0xd75258: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xd75258: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xd7525c: r0 = startsWith()
    //     0xd7525c: bl              #0x608410  ; [dart:core] _StringBase::startsWith
    // 0xd75260: mov             x1, x0
    // 0xd75264: ldur            x0, [fp, #-0x18]
    // 0xd75268: LoadField: r2 = r0->field_7
    //     0xd75268: ldur            w2, [x0, #7]
    // 0xd7526c: DecompressPointer r2
    //     0xd7526c: add             x2, x2, HEAP, lsl #32
    // 0xd75270: r16 = Instance_PreferredLaunchMode
    //     0xd75270: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac18] Obj!PreferredLaunchMode@e2de41
    //     0xd75274: ldr             x16, [x16, #0xc18]
    // 0xd75278: cmp             w2, w16
    // 0xd7527c: b.eq            #0xd75290
    // 0xd75280: r16 = Instance_PreferredLaunchMode
    //     0xd75280: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac20] Obj!PreferredLaunchMode@e2de21
    //     0xd75284: ldr             x16, [x16, #0xc20]
    // 0xd75288: cmp             w2, w16
    // 0xd7528c: b.ne            #0xd75298
    // 0xd75290: r0 = true
    //     0xd75290: add             x0, NULL, #0x20  ; true
    // 0xd75294: b               #0xd752bc
    // 0xd75298: tbnz            w1, #4, #0xd752b8
    // 0xd7529c: r16 = Instance_PreferredLaunchMode
    //     0xd7529c: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac10] Obj!PreferredLaunchMode@e2de61
    //     0xd752a0: ldr             x16, [x16, #0xc10]
    // 0xd752a4: cmp             w2, w16
    // 0xd752a8: r16 = true
    //     0xd752a8: add             x16, NULL, #0x20  ; true
    // 0xd752ac: r17 = false
    //     0xd752ac: add             x17, NULL, #0x30  ; false
    // 0xd752b0: csel            x0, x16, x17, eq
    // 0xd752b4: b               #0xd752bc
    // 0xd752b8: r0 = false
    //     0xd752b8: add             x0, NULL, #0x30  ; false
    // 0xd752bc: r16 = Instance_PreferredLaunchMode
    //     0xd752bc: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ac30] Obj!PreferredLaunchMode@e2dde1
    //     0xd752c0: ldr             x16, [x16, #0xc30]
    // 0xd752c4: cmp             w2, w16
    // 0xd752c8: r16 = true
    //     0xd752c8: add             x16, NULL, #0x20  ; true
    // 0xd752cc: r17 = false
    //     0xd752cc: add             x17, NULL, #0x30  ; false
    // 0xd752d0: csel            x7, x16, x17, eq
    // 0xd752d4: stp             x0, x0, [SP]
    // 0xd752d8: ldur            x1, [fp, #-8]
    // 0xd752dc: ldur            x2, [fp, #-0x10]
    // 0xd752e0: r3 = true
    //     0xd752e0: add             x3, NULL, #0x20  ; true
    // 0xd752e4: r5 = true
    //     0xd752e4: add             x5, NULL, #0x20  ; true
    // 0xd752e8: r6 = _ConstMap len:0
    //     0xd752e8: add             x6, PP, #0x1a, lsl #12  ; [pp+0x1ac48] Map<String, String>(0)
    //     0xd752ec: ldr             x6, [x6, #0xc48]
    // 0xd752f0: r0 = launch()
    //     0xd752f0: bl              #0x7da1b0  ; [package:url_launcher_platform_interface/method_channel_url_launcher.dart] MethodChannelUrlLauncher::launch
    // 0xd752f4: LeaveFrame
    //     0xd752f4: mov             SP, fp
    //     0xd752f8: ldp             fp, lr, [SP], #0x10
    // 0xd752fc: ret
    //     0xd752fc: ret             
    // 0xd75300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd75300: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd75304: b               #0xd7522c
  }
  set _ instance=(/* No info */) {
    // ** addr: 0xec7068, size: 0x6c
    // 0xec7068: EnterFrame
    //     0xec7068: stp             fp, lr, [SP, #-0x10]!
    //     0xec706c: mov             fp, SP
    // 0xec7070: AllocStack(0x8)
    //     0xec7070: sub             SP, SP, #8
    // 0xec7074: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xec7074: stur            x1, [fp, #-8]
    // 0xec7078: CheckStackOverflow
    //     0xec7078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec707c: cmp             SP, x16
    //     0xec7080: b.ls            #0xec70cc
    // 0xec7084: r0 = InitLateStaticField(0x610) // [package:url_launcher_platform_interface/src/url_launcher_platform.dart] UrlLauncherPlatform::_token
    //     0xec7084: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec7088: ldr             x0, [x0, #0xc20]
    //     0xec708c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec7090: cmp             w0, w16
    //     0xec7094: b.ne            #0xec70a4
    //     0xec7098: add             x2, PP, #0xc, lsl #12  ; [pp+0xc750] Field <UrlLauncherPlatform._token@681332722>: static late final (offset: 0x610)
    //     0xec709c: ldr             x2, [x2, #0x750]
    //     0xec70a0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec70a4: ldur            x1, [fp, #-8]
    // 0xec70a8: mov             x2, x0
    // 0xec70ac: r0 = verify()
    //     0xec70ac: bl              #0x833b94  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0xec70b0: ldur            x1, [fp, #-8]
    // 0xec70b4: StoreStaticField(0x614, r1)
    //     0xec70b4: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0xec70b8: str             x1, [x2, #0xc28]
    // 0xec70bc: r0 = Null
    //     0xec70bc: mov             x0, NULL
    // 0xec70c0: LeaveFrame
    //     0xec70c0: mov             SP, fp
    //     0xec70c4: ldp             fp, lr, [SP], #0x10
    // 0xec70c8: ret
    //     0xec70c8: ret             
    // 0xec70cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec70cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec70d0: b               #0xec7084
  }
}
