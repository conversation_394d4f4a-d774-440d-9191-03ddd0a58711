// lib: uuid, url: package:uuid/uuid.dart

// class id: 1051224, size: 0x8
class :: {
}

// class id: 415, size: 0xc, field offset: 0x8
//   const constructor, 
class Uuid extends Object {

  static late final List<String> _byteToHex; // offset: 0xca4
  static late final Expando<Map<String, dynamic>> _stateExpando; // offset: 0xca8

  _ v4(/* No info */) {
    // ** addr: 0x8b8bd8, size: 0x4a4
    // 0x8b8bd8: EnterFrame
    //     0x8b8bd8: stp             fp, lr, [SP, #-0x10]!
    //     0x8b8bdc: mov             fp, SP
    // 0x8b8be0: AllocStack(0x40)
    //     0x8b8be0: sub             SP, SP, #0x40
    // 0x8b8be4: SetupParameters(Uuid this /* r1 => r1, fp-0x8 */)
    //     0x8b8be4: stur            x1, [fp, #-8]
    // 0x8b8be8: CheckStackOverflow
    //     0x8b8be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b8bec: cmp             SP, x16
    //     0x8b8bf0: b.ls            #0x8b9070
    // 0x8b8bf4: r16 = <String, dynamic>
    //     0x8b8bf4: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8b8bf8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8b8bfc: stp             lr, x16, [SP]
    // 0x8b8c00: r0 = Map._fromLiteral()
    //     0x8b8c00: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8b8c04: ldur            x1, [fp, #-8]
    // 0x8b8c08: stur            x0, [fp, #-0x10]
    // 0x8b8c0c: r0 = _initV4()
    //     0x8b8c0c: bl              #0x8b9d10  ; [package:uuid/uuid.dart] Uuid::_initV4
    // 0x8b8c10: ldur            x1, [fp, #-0x10]
    // 0x8b8c14: r2 = "positionalArgs"
    //     0x8b8c14: add             x2, PP, #0xe, lsl #12  ; [pp+0xef90] "positionalArgs"
    //     0x8b8c18: ldr             x2, [x2, #0xf90]
    // 0x8b8c1c: r0 = _getValueOrData()
    //     0x8b8c1c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8c20: mov             x1, x0
    // 0x8b8c24: ldur            x0, [fp, #-0x10]
    // 0x8b8c28: LoadField: r2 = r0->field_f
    //     0x8b8c28: ldur            w2, [x0, #0xf]
    // 0x8b8c2c: DecompressPointer r2
    //     0x8b8c2c: add             x2, x2, HEAP, lsl #32
    // 0x8b8c30: cmp             w2, w1
    // 0x8b8c34: b.eq            #0x8b8c74
    // 0x8b8c38: cmp             w1, NULL
    // 0x8b8c3c: b.eq            #0x8b8c74
    // 0x8b8c40: mov             x1, x0
    // 0x8b8c44: r2 = "positionalArgs"
    //     0x8b8c44: add             x2, PP, #0xe, lsl #12  ; [pp+0xef90] "positionalArgs"
    //     0x8b8c48: ldr             x2, [x2, #0xf90]
    // 0x8b8c4c: r0 = _getValueOrData()
    //     0x8b8c4c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8c50: mov             x1, x0
    // 0x8b8c54: ldur            x0, [fp, #-0x10]
    // 0x8b8c58: LoadField: r2 = r0->field_f
    //     0x8b8c58: ldur            w2, [x0, #0xf]
    // 0x8b8c5c: DecompressPointer r2
    //     0x8b8c5c: add             x2, x2, HEAP, lsl #32
    // 0x8b8c60: cmp             w2, w1
    // 0x8b8c64: b.ne            #0x8b8c6c
    // 0x8b8c68: r1 = Null
    //     0x8b8c68: mov             x1, NULL
    // 0x8b8c6c: mov             x3, x1
    // 0x8b8c70: b               #0x8b8c88
    // 0x8b8c74: r1 = Null
    //     0x8b8c74: mov             x1, NULL
    // 0x8b8c78: r2 = 0
    //     0x8b8c78: movz            x2, #0
    // 0x8b8c7c: r0 = _GrowableList()
    //     0x8b8c7c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8b8c80: mov             x3, x0
    // 0x8b8c84: ldur            x0, [fp, #-0x10]
    // 0x8b8c88: mov             x1, x0
    // 0x8b8c8c: stur            x3, [fp, #-0x18]
    // 0x8b8c90: r2 = "namedArgs"
    //     0x8b8c90: add             x2, PP, #0xe, lsl #12  ; [pp+0xef98] "namedArgs"
    //     0x8b8c94: ldr             x2, [x2, #0xf98]
    // 0x8b8c98: r0 = _getValueOrData()
    //     0x8b8c98: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8c9c: mov             x1, x0
    // 0x8b8ca0: ldur            x0, [fp, #-0x10]
    // 0x8b8ca4: LoadField: r2 = r0->field_f
    //     0x8b8ca4: ldur            w2, [x0, #0xf]
    // 0x8b8ca8: DecompressPointer r2
    //     0x8b8ca8: add             x2, x2, HEAP, lsl #32
    // 0x8b8cac: cmp             w2, w1
    // 0x8b8cb0: b.eq            #0x8b8d18
    // 0x8b8cb4: cmp             w1, NULL
    // 0x8b8cb8: b.eq            #0x8b8d18
    // 0x8b8cbc: mov             x1, x0
    // 0x8b8cc0: r2 = "namedArgs"
    //     0x8b8cc0: add             x2, PP, #0xe, lsl #12  ; [pp+0xef98] "namedArgs"
    //     0x8b8cc4: ldr             x2, [x2, #0xf98]
    // 0x8b8cc8: r0 = _getValueOrData()
    //     0x8b8cc8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8ccc: ldur            x3, [fp, #-0x10]
    // 0x8b8cd0: LoadField: r1 = r3->field_f
    //     0x8b8cd0: ldur            w1, [x3, #0xf]
    // 0x8b8cd4: DecompressPointer r1
    //     0x8b8cd4: add             x1, x1, HEAP, lsl #32
    // 0x8b8cd8: cmp             w1, w0
    // 0x8b8cdc: b.ne            #0x8b8ce8
    // 0x8b8ce0: r4 = Null
    //     0x8b8ce0: mov             x4, NULL
    // 0x8b8ce4: b               #0x8b8cec
    // 0x8b8ce8: mov             x4, x0
    // 0x8b8cec: mov             x0, x4
    // 0x8b8cf0: stur            x4, [fp, #-0x20]
    // 0x8b8cf4: r2 = Null
    //     0x8b8cf4: mov             x2, NULL
    // 0x8b8cf8: r1 = Null
    //     0x8b8cf8: mov             x1, NULL
    // 0x8b8cfc: r8 = Map<Symbol, dynamic>
    //     0x8b8cfc: add             x8, PP, #0xe, lsl #12  ; [pp+0xefa0] Type: Map<Symbol, dynamic>
    //     0x8b8d00: ldr             x8, [x8, #0xfa0]
    // 0x8b8d04: r3 = Null
    //     0x8b8d04: add             x3, PP, #0xe, lsl #12  ; [pp+0xefa8] Null
    //     0x8b8d08: ldr             x3, [x3, #0xfa8]
    // 0x8b8d0c: r0 = Map<Symbol, dynamic>()
    //     0x8b8d0c: bl              #0x8ba270  ; IsType_Map<Symbol, dynamic>_Stub
    // 0x8b8d10: ldur            x3, [fp, #-0x20]
    // 0x8b8d14: b               #0x8b8d20
    // 0x8b8d18: r3 = _ConstMap len:0
    //     0x8b8d18: add             x3, PP, #0xe, lsl #12  ; [pp+0xefb8] Map<Symbol, dynamic>(0)
    //     0x8b8d1c: ldr             x3, [x3, #0xfb8]
    // 0x8b8d20: ldur            x0, [fp, #-0x10]
    // 0x8b8d24: mov             x1, x0
    // 0x8b8d28: stur            x3, [fp, #-0x20]
    // 0x8b8d2c: r2 = "rng"
    //     0x8b8d2c: add             x2, PP, #0xe, lsl #12  ; [pp+0xefc0] "rng"
    //     0x8b8d30: ldr             x2, [x2, #0xfc0]
    // 0x8b8d34: r0 = _getValueOrData()
    //     0x8b8d34: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8d38: mov             x1, x0
    // 0x8b8d3c: ldur            x0, [fp, #-0x10]
    // 0x8b8d40: LoadField: r2 = r0->field_f
    //     0x8b8d40: ldur            w2, [x0, #0xf]
    // 0x8b8d44: DecompressPointer r2
    //     0x8b8d44: add             x2, x2, HEAP, lsl #32
    // 0x8b8d48: cmp             w2, w1
    // 0x8b8d4c: b.eq            #0x8b8e40
    // 0x8b8d50: cmp             w1, NULL
    // 0x8b8d54: b.eq            #0x8b8e40
    // 0x8b8d58: mov             x1, x0
    // 0x8b8d5c: r2 = "rng"
    //     0x8b8d5c: add             x2, PP, #0xe, lsl #12  ; [pp+0xefc0] "rng"
    //     0x8b8d60: ldr             x2, [x2, #0xfc0]
    // 0x8b8d64: r0 = _getValueOrData()
    //     0x8b8d64: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8d68: ldur            x3, [fp, #-0x10]
    // 0x8b8d6c: LoadField: r1 = r3->field_f
    //     0x8b8d6c: ldur            w1, [x3, #0xf]
    // 0x8b8d70: DecompressPointer r1
    //     0x8b8d70: add             x1, x1, HEAP, lsl #32
    // 0x8b8d74: cmp             w1, w0
    // 0x8b8d78: b.ne            #0x8b8d84
    // 0x8b8d7c: r4 = Null
    //     0x8b8d7c: mov             x4, NULL
    // 0x8b8d80: b               #0x8b8d88
    // 0x8b8d84: mov             x4, x0
    // 0x8b8d88: mov             x0, x4
    // 0x8b8d8c: stur            x4, [fp, #-0x28]
    // 0x8b8d90: r2 = Null
    //     0x8b8d90: mov             x2, NULL
    // 0x8b8d94: r1 = Null
    //     0x8b8d94: mov             x1, NULL
    // 0x8b8d98: r4 = 60
    //     0x8b8d98: movz            x4, #0x3c
    // 0x8b8d9c: branchIfSmi(r0, 0x8b8da8)
    //     0x8b8d9c: tbz             w0, #0, #0x8b8da8
    // 0x8b8da0: r4 = LoadClassIdInstr(r0)
    //     0x8b8da0: ldur            x4, [x0, #-1]
    //     0x8b8da4: ubfx            x4, x4, #0xc, #0x14
    // 0x8b8da8: cmp             x4, #0x39
    // 0x8b8dac: b.eq            #0x8b8dc4
    // 0x8b8db0: r8 = Function
    //     0x8b8db0: add             x8, PP, #0xd, lsl #12  ; [pp+0xd050] Type: Function
    //     0x8b8db4: ldr             x8, [x8, #0x50]
    // 0x8b8db8: r3 = Null
    //     0x8b8db8: add             x3, PP, #0xe, lsl #12  ; [pp+0xefc8] Null
    //     0x8b8dbc: ldr             x3, [x3, #0xfc8]
    // 0x8b8dc0: r0 = Function()
    //     0x8b8dc0: bl              #0xed64f4  ; IsType_Function_Stub
    // 0x8b8dc4: ldur            x0, [fp, #-0x18]
    // 0x8b8dc8: r2 = Null
    //     0x8b8dc8: mov             x2, NULL
    // 0x8b8dcc: r1 = Null
    //     0x8b8dcc: mov             x1, NULL
    // 0x8b8dd0: r4 = 60
    //     0x8b8dd0: movz            x4, #0x3c
    // 0x8b8dd4: branchIfSmi(r0, 0x8b8de0)
    //     0x8b8dd4: tbz             w0, #0, #0x8b8de0
    // 0x8b8dd8: r4 = LoadClassIdInstr(r0)
    //     0x8b8dd8: ldur            x4, [x0, #-1]
    //     0x8b8ddc: ubfx            x4, x4, #0xc, #0x14
    // 0x8b8de0: sub             x4, x4, #0x5a
    // 0x8b8de4: cmp             x4, #2
    // 0x8b8de8: b.ls            #0x8b8e00
    // 0x8b8dec: r8 = List?
    //     0x8b8dec: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8b8df0: ldr             x8, [x8, #0x140]
    // 0x8b8df4: r3 = Null
    //     0x8b8df4: add             x3, PP, #0xe, lsl #12  ; [pp+0xefd8] Null
    //     0x8b8df8: ldr             x3, [x3, #0xfd8]
    // 0x8b8dfc: r0 = List?()
    //     0x8b8dfc: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8b8e00: ldur            x16, [fp, #-0x20]
    // 0x8b8e04: str             x16, [SP]
    // 0x8b8e08: ldur            x1, [fp, #-0x28]
    // 0x8b8e0c: ldur            x2, [fp, #-0x18]
    // 0x8b8e10: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8b8e10: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8b8e14: r0 = apply()
    //     0x8b8e14: bl              #0x889cd8  ; [dart:core] Function::apply
    // 0x8b8e18: mov             x3, x0
    // 0x8b8e1c: r2 = Null
    //     0x8b8e1c: mov             x2, NULL
    // 0x8b8e20: r1 = Null
    //     0x8b8e20: mov             x1, NULL
    // 0x8b8e24: stur            x3, [fp, #-0x18]
    // 0x8b8e28: r8 = List<int>
    //     0x8b8e28: ldr             x8, [PP, #0x1020]  ; [pp+0x1020] Type: List<int>
    // 0x8b8e2c: r3 = Null
    //     0x8b8e2c: add             x3, PP, #0xe, lsl #12  ; [pp+0xefe8] Null
    //     0x8b8e30: ldr             x3, [x3, #0xfe8]
    // 0x8b8e34: r0 = List<int>()
    //     0x8b8e34: bl              #0x601a14  ; IsType_List<int>_Stub
    // 0x8b8e38: ldur            x3, [fp, #-0x18]
    // 0x8b8e3c: b               #0x8b8edc
    // 0x8b8e40: ldur            x1, [fp, #-8]
    // 0x8b8e44: r0 = _state()
    //     0x8b8e44: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0x8b8e48: r1 = LoadClassIdInstr(r0)
    //     0x8b8e48: ldur            x1, [x0, #-1]
    //     0x8b8e4c: ubfx            x1, x1, #0xc, #0x14
    // 0x8b8e50: mov             x16, x0
    // 0x8b8e54: mov             x0, x1
    // 0x8b8e58: mov             x1, x16
    // 0x8b8e5c: r2 = "globalRNG"
    //     0x8b8e5c: add             x2, PP, #0xe, lsl #12  ; [pp+0xeff8] "globalRNG"
    //     0x8b8e60: ldr             x2, [x2, #0xff8]
    // 0x8b8e64: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b8e64: sub             lr, x0, #0x114
    //     0x8b8e68: ldr             lr, [x21, lr, lsl #3]
    //     0x8b8e6c: blr             lr
    // 0x8b8e70: mov             x3, x0
    // 0x8b8e74: stur            x3, [fp, #-8]
    // 0x8b8e78: cmp             w3, NULL
    // 0x8b8e7c: b.eq            #0x8b9078
    // 0x8b8e80: mov             x0, x3
    // 0x8b8e84: r2 = Null
    //     0x8b8e84: mov             x2, NULL
    // 0x8b8e88: r1 = Null
    //     0x8b8e88: mov             x1, NULL
    // 0x8b8e8c: r8 = (dynamic this) => dynamic
    //     0x8b8e8c: add             x8, PP, #0xd, lsl #12  ; [pp+0xd310] FunctionType: (dynamic this) => dynamic
    //     0x8b8e90: ldr             x8, [x8, #0x310]
    // 0x8b8e94: r3 = Null
    //     0x8b8e94: add             x3, PP, #0xf, lsl #12  ; [pp+0xf000] Null
    //     0x8b8e98: ldr             x3, [x3]
    // 0x8b8e9c: r0 = DefaultTypeTest()
    //     0x8b8e9c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0x8b8ea0: ldur            x16, [fp, #-8]
    // 0x8b8ea4: str             x16, [SP]
    // 0x8b8ea8: ldur            x0, [fp, #-8]
    // 0x8b8eac: ClosureCall
    //     0x8b8eac: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8b8eb0: ldur            x2, [x0, #0x1f]
    //     0x8b8eb4: blr             x2
    // 0x8b8eb8: mov             x3, x0
    // 0x8b8ebc: r2 = Null
    //     0x8b8ebc: mov             x2, NULL
    // 0x8b8ec0: r1 = Null
    //     0x8b8ec0: mov             x1, NULL
    // 0x8b8ec4: stur            x3, [fp, #-8]
    // 0x8b8ec8: r8 = List<int>
    //     0x8b8ec8: ldr             x8, [PP, #0x1020]  ; [pp+0x1020] Type: List<int>
    // 0x8b8ecc: r3 = Null
    //     0x8b8ecc: add             x3, PP, #0xf, lsl #12  ; [pp+0xf010] Null
    //     0x8b8ed0: ldr             x3, [x3, #0x10]
    // 0x8b8ed4: r0 = List<int>()
    //     0x8b8ed4: bl              #0x601a14  ; IsType_List<int>_Stub
    // 0x8b8ed8: ldur            x3, [fp, #-8]
    // 0x8b8edc: ldur            x0, [fp, #-0x10]
    // 0x8b8ee0: mov             x1, x0
    // 0x8b8ee4: stur            x3, [fp, #-8]
    // 0x8b8ee8: r2 = "random"
    //     0x8b8ee8: add             x2, PP, #0xf, lsl #12  ; [pp+0xf020] "random"
    //     0x8b8eec: ldr             x2, [x2, #0x20]
    // 0x8b8ef0: r0 = _getValueOrData()
    //     0x8b8ef0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8ef4: mov             x1, x0
    // 0x8b8ef8: ldur            x0, [fp, #-0x10]
    // 0x8b8efc: LoadField: r2 = r0->field_f
    //     0x8b8efc: ldur            w2, [x0, #0xf]
    // 0x8b8f00: DecompressPointer r2
    //     0x8b8f00: add             x2, x2, HEAP, lsl #32
    // 0x8b8f04: cmp             w2, w1
    // 0x8b8f08: b.eq            #0x8b8f70
    // 0x8b8f0c: cmp             w1, NULL
    // 0x8b8f10: b.eq            #0x8b8f70
    // 0x8b8f14: mov             x1, x0
    // 0x8b8f18: r2 = "random"
    //     0x8b8f18: add             x2, PP, #0xf, lsl #12  ; [pp+0xf020] "random"
    //     0x8b8f1c: ldr             x2, [x2, #0x20]
    // 0x8b8f20: r0 = _getValueOrData()
    //     0x8b8f20: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8b8f24: mov             x1, x0
    // 0x8b8f28: ldur            x0, [fp, #-0x10]
    // 0x8b8f2c: LoadField: r2 = r0->field_f
    //     0x8b8f2c: ldur            w2, [x0, #0xf]
    // 0x8b8f30: DecompressPointer r2
    //     0x8b8f30: add             x2, x2, HEAP, lsl #32
    // 0x8b8f34: cmp             w2, w1
    // 0x8b8f38: b.ne            #0x8b8f44
    // 0x8b8f3c: r3 = Null
    //     0x8b8f3c: mov             x3, NULL
    // 0x8b8f40: b               #0x8b8f48
    // 0x8b8f44: mov             x3, x1
    // 0x8b8f48: mov             x0, x3
    // 0x8b8f4c: stur            x3, [fp, #-0x10]
    // 0x8b8f50: r2 = Null
    //     0x8b8f50: mov             x2, NULL
    // 0x8b8f54: r1 = Null
    //     0x8b8f54: mov             x1, NULL
    // 0x8b8f58: r8 = List<int>
    //     0x8b8f58: ldr             x8, [PP, #0x1020]  ; [pp+0x1020] Type: List<int>
    // 0x8b8f5c: r3 = Null
    //     0x8b8f5c: add             x3, PP, #0xf, lsl #12  ; [pp+0xf028] Null
    //     0x8b8f60: ldr             x3, [x3, #0x28]
    // 0x8b8f64: r0 = List<int>()
    //     0x8b8f64: bl              #0x601a14  ; IsType_List<int>_Stub
    // 0x8b8f68: ldur            x1, [fp, #-0x10]
    // 0x8b8f6c: b               #0x8b8f74
    // 0x8b8f70: ldur            x1, [fp, #-8]
    // 0x8b8f74: stur            x1, [fp, #-8]
    // 0x8b8f78: r0 = LoadClassIdInstr(r1)
    //     0x8b8f78: ldur            x0, [x1, #-1]
    //     0x8b8f7c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b8f80: r16 = 12
    //     0x8b8f80: movz            x16, #0xc
    // 0x8b8f84: stp             x16, x1, [SP]
    // 0x8b8f88: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b8f88: movz            x17, #0x3037
    //     0x8b8f8c: movk            x17, #0x1, lsl #16
    //     0x8b8f90: add             lr, x0, x17
    //     0x8b8f94: ldr             lr, [x21, lr, lsl #3]
    //     0x8b8f98: blr             lr
    // 0x8b8f9c: r1 = LoadInt32Instr(r0)
    //     0x8b8f9c: sbfx            x1, x0, #1, #0x1f
    //     0x8b8fa0: tbz             w0, #0, #0x8b8fa8
    //     0x8b8fa4: ldur            x1, [x0, #7]
    // 0x8b8fa8: r0 = 15
    //     0x8b8fa8: movz            x0, #0xf
    // 0x8b8fac: and             x2, x1, x0
    // 0x8b8fb0: ubfx            x2, x2, #0, #0x20
    // 0x8b8fb4: orr             x0, x2, #0x40
    // 0x8b8fb8: lsl             x1, x0, #1
    // 0x8b8fbc: ldur            x2, [fp, #-8]
    // 0x8b8fc0: r0 = LoadClassIdInstr(r2)
    //     0x8b8fc0: ldur            x0, [x2, #-1]
    //     0x8b8fc4: ubfx            x0, x0, #0xc, #0x14
    // 0x8b8fc8: r16 = 12
    //     0x8b8fc8: movz            x16, #0xc
    // 0x8b8fcc: stp             x16, x2, [SP, #8]
    // 0x8b8fd0: str             x1, [SP]
    // 0x8b8fd4: r0 = GDT[cid_x0 + 0x1310f]()
    //     0x8b8fd4: movz            x17, #0x310f
    //     0x8b8fd8: movk            x17, #0x1, lsl #16
    //     0x8b8fdc: add             lr, x0, x17
    //     0x8b8fe0: ldr             lr, [x21, lr, lsl #3]
    //     0x8b8fe4: blr             lr
    // 0x8b8fe8: ldur            x1, [fp, #-8]
    // 0x8b8fec: r0 = LoadClassIdInstr(r1)
    //     0x8b8fec: ldur            x0, [x1, #-1]
    //     0x8b8ff0: ubfx            x0, x0, #0xc, #0x14
    // 0x8b8ff4: r16 = 16
    //     0x8b8ff4: movz            x16, #0x10
    // 0x8b8ff8: stp             x16, x1, [SP]
    // 0x8b8ffc: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b8ffc: movz            x17, #0x3037
    //     0x8b9000: movk            x17, #0x1, lsl #16
    //     0x8b9004: add             lr, x0, x17
    //     0x8b9008: ldr             lr, [x21, lr, lsl #3]
    //     0x8b900c: blr             lr
    // 0x8b9010: r1 = LoadInt32Instr(r0)
    //     0x8b9010: sbfx            x1, x0, #1, #0x1f
    //     0x8b9014: tbz             w0, #0, #0x8b901c
    //     0x8b9018: ldur            x1, [x0, #7]
    // 0x8b901c: r0 = 63
    //     0x8b901c: movz            x0, #0x3f
    // 0x8b9020: and             x2, x1, x0
    // 0x8b9024: ubfx            x2, x2, #0, #0x20
    // 0x8b9028: orr             x0, x2, #0x80
    // 0x8b902c: lsl             x1, x0, #1
    // 0x8b9030: ldur            x2, [fp, #-8]
    // 0x8b9034: r0 = LoadClassIdInstr(r2)
    //     0x8b9034: ldur            x0, [x2, #-1]
    //     0x8b9038: ubfx            x0, x0, #0xc, #0x14
    // 0x8b903c: r16 = 16
    //     0x8b903c: movz            x16, #0x10
    // 0x8b9040: stp             x16, x2, [SP, #8]
    // 0x8b9044: str             x1, [SP]
    // 0x8b9048: r0 = GDT[cid_x0 + 0x1310f]()
    //     0x8b9048: movz            x17, #0x310f
    //     0x8b904c: movk            x17, #0x1, lsl #16
    //     0x8b9050: add             lr, x0, x17
    //     0x8b9054: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9058: blr             lr
    // 0x8b905c: ldur            x1, [fp, #-8]
    // 0x8b9060: r0 = unparse()
    //     0x8b9060: bl              #0x8b907c  ; [package:uuid/uuid.dart] Uuid::unparse
    // 0x8b9064: LeaveFrame
    //     0x8b9064: mov             SP, fp
    //     0x8b9068: ldp             fp, lr, [SP], #0x10
    // 0x8b906c: ret
    //     0x8b906c: ret             
    // 0x8b9070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b9070: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b9074: b               #0x8b8bf4
    // 0x8b9078: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b9078: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ unparse(/* No info */) {
    // ** addr: 0x8b907c, size: 0xa68
    // 0x8b907c: EnterFrame
    //     0x8b907c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b9080: mov             fp, SP
    // 0x8b9084: AllocStack(0x30)
    //     0x8b9084: sub             SP, SP, #0x30
    // 0x8b9088: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x8b9088: stur            x1, [fp, #-8]
    // 0x8b908c: CheckStackOverflow
    //     0x8b908c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b9090: cmp             SP, x16
    //     0x8b9094: b.ls            #0x8b9a9c
    // 0x8b9098: r0 = LoadClassIdInstr(r1)
    //     0x8b9098: ldur            x0, [x1, #-1]
    //     0x8b909c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b90a0: str             x1, [SP]
    // 0x8b90a4: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8b90a4: movz            x17, #0xc834
    //     0x8b90a8: add             lr, x0, x17
    //     0x8b90ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8b90b0: blr             lr
    // 0x8b90b4: r1 = LoadInt32Instr(r0)
    //     0x8b90b4: sbfx            x1, x0, #1, #0x1f
    //     0x8b90b8: tbz             w0, #0, #0x8b90c0
    //     0x8b90bc: ldur            x1, [x0, #7]
    // 0x8b90c0: cmp             x1, #0x10
    // 0x8b90c4: b.lt            #0x8b99f0
    // 0x8b90c8: ldur            x0, [fp, #-8]
    // 0x8b90cc: r0 = InitLateStaticField(0xca4) // [package:uuid/uuid.dart] Uuid::_byteToHex
    //     0x8b90cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b90d0: ldr             x0, [x0, #0x1948]
    //     0x8b90d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b90d8: cmp             w0, w16
    //     0x8b90dc: b.ne            #0x8b90ec
    //     0x8b90e0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf038] Field <Uuid._byteToHex@811507722>: static late final (offset: 0xca4)
    //     0x8b90e4: ldr             x2, [x2, #0x38]
    //     0x8b90e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b90ec: mov             x2, x0
    // 0x8b90f0: ldur            x1, [fp, #-8]
    // 0x8b90f4: stur            x2, [fp, #-0x10]
    // 0x8b90f8: r0 = LoadClassIdInstr(r1)
    //     0x8b90f8: ldur            x0, [x1, #-1]
    //     0x8b90fc: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9100: stp             xzr, x1, [SP]
    // 0x8b9104: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9104: movz            x17, #0x3037
    //     0x8b9108: movk            x17, #0x1, lsl #16
    //     0x8b910c: add             lr, x0, x17
    //     0x8b9110: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9114: blr             lr
    // 0x8b9118: ldur            x3, [fp, #-0x10]
    // 0x8b911c: LoadField: r1 = r3->field_b
    //     0x8b911c: ldur            w1, [x3, #0xb]
    // 0x8b9120: r2 = LoadInt32Instr(r0)
    //     0x8b9120: sbfx            x2, x0, #1, #0x1f
    //     0x8b9124: tbz             w0, #0, #0x8b912c
    //     0x8b9128: ldur            x2, [x0, #7]
    // 0x8b912c: r0 = LoadInt32Instr(r1)
    //     0x8b912c: sbfx            x0, x1, #1, #0x1f
    // 0x8b9130: mov             x1, x2
    // 0x8b9134: cmp             x1, x0
    // 0x8b9138: b.hs            #0x8b9aa4
    // 0x8b913c: LoadField: r0 = r3->field_f
    //     0x8b913c: ldur            w0, [x3, #0xf]
    // 0x8b9140: DecompressPointer r0
    //     0x8b9140: add             x0, x0, HEAP, lsl #32
    // 0x8b9144: ArrayLoad: r4 = r0[r2]  ; Unknown_4
    //     0x8b9144: add             x16, x0, x2, lsl #2
    //     0x8b9148: ldur            w4, [x16, #0xf]
    // 0x8b914c: DecompressPointer r4
    //     0x8b914c: add             x4, x4, HEAP, lsl #32
    // 0x8b9150: stur            x4, [fp, #-0x18]
    // 0x8b9154: r1 = Null
    //     0x8b9154: mov             x1, NULL
    // 0x8b9158: r2 = 40
    //     0x8b9158: movz            x2, #0x28
    // 0x8b915c: r0 = AllocateArray()
    //     0x8b915c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b9160: mov             x1, x0
    // 0x8b9164: ldur            x0, [fp, #-0x18]
    // 0x8b9168: stur            x1, [fp, #-0x20]
    // 0x8b916c: StoreField: r1->field_f = r0
    //     0x8b916c: stur            w0, [x1, #0xf]
    // 0x8b9170: ldur            x2, [fp, #-8]
    // 0x8b9174: r0 = LoadClassIdInstr(r2)
    //     0x8b9174: ldur            x0, [x2, #-1]
    //     0x8b9178: ubfx            x0, x0, #0xc, #0x14
    // 0x8b917c: r16 = 2
    //     0x8b917c: movz            x16, #0x2
    // 0x8b9180: stp             x16, x2, [SP]
    // 0x8b9184: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9184: movz            x17, #0x3037
    //     0x8b9188: movk            x17, #0x1, lsl #16
    //     0x8b918c: add             lr, x0, x17
    //     0x8b9190: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9194: blr             lr
    // 0x8b9198: ldur            x2, [fp, #-0x10]
    // 0x8b919c: LoadField: r1 = r2->field_b
    //     0x8b919c: ldur            w1, [x2, #0xb]
    // 0x8b91a0: r3 = LoadInt32Instr(r0)
    //     0x8b91a0: sbfx            x3, x0, #1, #0x1f
    //     0x8b91a4: tbz             w0, #0, #0x8b91ac
    //     0x8b91a8: ldur            x3, [x0, #7]
    // 0x8b91ac: r0 = LoadInt32Instr(r1)
    //     0x8b91ac: sbfx            x0, x1, #1, #0x1f
    // 0x8b91b0: mov             x1, x3
    // 0x8b91b4: cmp             x1, x0
    // 0x8b91b8: b.hs            #0x8b9aa8
    // 0x8b91bc: LoadField: r0 = r2->field_f
    //     0x8b91bc: ldur            w0, [x2, #0xf]
    // 0x8b91c0: DecompressPointer r0
    //     0x8b91c0: add             x0, x0, HEAP, lsl #32
    // 0x8b91c4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b91c4: add             x16, x0, x3, lsl #2
    //     0x8b91c8: ldur            w1, [x16, #0xf]
    // 0x8b91cc: DecompressPointer r1
    //     0x8b91cc: add             x1, x1, HEAP, lsl #32
    // 0x8b91d0: mov             x0, x1
    // 0x8b91d4: ldur            x1, [fp, #-0x20]
    // 0x8b91d8: ArrayStore: r1[1] = r0  ; List_4
    //     0x8b91d8: add             x25, x1, #0x13
    //     0x8b91dc: str             w0, [x25]
    //     0x8b91e0: tbz             w0, #0, #0x8b91fc
    //     0x8b91e4: ldurb           w16, [x1, #-1]
    //     0x8b91e8: ldurb           w17, [x0, #-1]
    //     0x8b91ec: and             x16, x17, x16, lsr #2
    //     0x8b91f0: tst             x16, HEAP, lsr #32
    //     0x8b91f4: b.eq            #0x8b91fc
    //     0x8b91f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b91fc: ldur            x1, [fp, #-8]
    // 0x8b9200: r0 = LoadClassIdInstr(r1)
    //     0x8b9200: ldur            x0, [x1, #-1]
    //     0x8b9204: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9208: r16 = 4
    //     0x8b9208: movz            x16, #0x4
    // 0x8b920c: stp             x16, x1, [SP]
    // 0x8b9210: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9210: movz            x17, #0x3037
    //     0x8b9214: movk            x17, #0x1, lsl #16
    //     0x8b9218: add             lr, x0, x17
    //     0x8b921c: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9220: blr             lr
    // 0x8b9224: ldur            x2, [fp, #-0x10]
    // 0x8b9228: LoadField: r1 = r2->field_b
    //     0x8b9228: ldur            w1, [x2, #0xb]
    // 0x8b922c: r3 = LoadInt32Instr(r0)
    //     0x8b922c: sbfx            x3, x0, #1, #0x1f
    //     0x8b9230: tbz             w0, #0, #0x8b9238
    //     0x8b9234: ldur            x3, [x0, #7]
    // 0x8b9238: r0 = LoadInt32Instr(r1)
    //     0x8b9238: sbfx            x0, x1, #1, #0x1f
    // 0x8b923c: mov             x1, x3
    // 0x8b9240: cmp             x1, x0
    // 0x8b9244: b.hs            #0x8b9aac
    // 0x8b9248: LoadField: r0 = r2->field_f
    //     0x8b9248: ldur            w0, [x2, #0xf]
    // 0x8b924c: DecompressPointer r0
    //     0x8b924c: add             x0, x0, HEAP, lsl #32
    // 0x8b9250: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9250: add             x16, x0, x3, lsl #2
    //     0x8b9254: ldur            w1, [x16, #0xf]
    // 0x8b9258: DecompressPointer r1
    //     0x8b9258: add             x1, x1, HEAP, lsl #32
    // 0x8b925c: mov             x0, x1
    // 0x8b9260: ldur            x1, [fp, #-0x20]
    // 0x8b9264: ArrayStore: r1[2] = r0  ; List_4
    //     0x8b9264: add             x25, x1, #0x17
    //     0x8b9268: str             w0, [x25]
    //     0x8b926c: tbz             w0, #0, #0x8b9288
    //     0x8b9270: ldurb           w16, [x1, #-1]
    //     0x8b9274: ldurb           w17, [x0, #-1]
    //     0x8b9278: and             x16, x17, x16, lsr #2
    //     0x8b927c: tst             x16, HEAP, lsr #32
    //     0x8b9280: b.eq            #0x8b9288
    //     0x8b9284: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9288: ldur            x1, [fp, #-8]
    // 0x8b928c: r0 = LoadClassIdInstr(r1)
    //     0x8b928c: ldur            x0, [x1, #-1]
    //     0x8b9290: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9294: r16 = 6
    //     0x8b9294: movz            x16, #0x6
    // 0x8b9298: stp             x16, x1, [SP]
    // 0x8b929c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b929c: movz            x17, #0x3037
    //     0x8b92a0: movk            x17, #0x1, lsl #16
    //     0x8b92a4: add             lr, x0, x17
    //     0x8b92a8: ldr             lr, [x21, lr, lsl #3]
    //     0x8b92ac: blr             lr
    // 0x8b92b0: ldur            x2, [fp, #-0x10]
    // 0x8b92b4: LoadField: r1 = r2->field_b
    //     0x8b92b4: ldur            w1, [x2, #0xb]
    // 0x8b92b8: r3 = LoadInt32Instr(r0)
    //     0x8b92b8: sbfx            x3, x0, #1, #0x1f
    //     0x8b92bc: tbz             w0, #0, #0x8b92c4
    //     0x8b92c0: ldur            x3, [x0, #7]
    // 0x8b92c4: r0 = LoadInt32Instr(r1)
    //     0x8b92c4: sbfx            x0, x1, #1, #0x1f
    // 0x8b92c8: mov             x1, x3
    // 0x8b92cc: cmp             x1, x0
    // 0x8b92d0: b.hs            #0x8b9ab0
    // 0x8b92d4: LoadField: r0 = r2->field_f
    //     0x8b92d4: ldur            w0, [x2, #0xf]
    // 0x8b92d8: DecompressPointer r0
    //     0x8b92d8: add             x0, x0, HEAP, lsl #32
    // 0x8b92dc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b92dc: add             x16, x0, x3, lsl #2
    //     0x8b92e0: ldur            w1, [x16, #0xf]
    // 0x8b92e4: DecompressPointer r1
    //     0x8b92e4: add             x1, x1, HEAP, lsl #32
    // 0x8b92e8: mov             x0, x1
    // 0x8b92ec: ldur            x1, [fp, #-0x20]
    // 0x8b92f0: ArrayStore: r1[3] = r0  ; List_4
    //     0x8b92f0: add             x25, x1, #0x1b
    //     0x8b92f4: str             w0, [x25]
    //     0x8b92f8: tbz             w0, #0, #0x8b9314
    //     0x8b92fc: ldurb           w16, [x1, #-1]
    //     0x8b9300: ldurb           w17, [x0, #-1]
    //     0x8b9304: and             x16, x17, x16, lsr #2
    //     0x8b9308: tst             x16, HEAP, lsr #32
    //     0x8b930c: b.eq            #0x8b9314
    //     0x8b9310: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9314: ldur            x1, [fp, #-0x20]
    // 0x8b9318: r16 = "-"
    //     0x8b9318: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x8b931c: StoreField: r1->field_1f = r16
    //     0x8b931c: stur            w16, [x1, #0x1f]
    // 0x8b9320: ldur            x3, [fp, #-8]
    // 0x8b9324: r0 = LoadClassIdInstr(r3)
    //     0x8b9324: ldur            x0, [x3, #-1]
    //     0x8b9328: ubfx            x0, x0, #0xc, #0x14
    // 0x8b932c: r16 = 8
    //     0x8b932c: movz            x16, #0x8
    // 0x8b9330: stp             x16, x3, [SP]
    // 0x8b9334: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9334: movz            x17, #0x3037
    //     0x8b9338: movk            x17, #0x1, lsl #16
    //     0x8b933c: add             lr, x0, x17
    //     0x8b9340: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9344: blr             lr
    // 0x8b9348: ldur            x2, [fp, #-0x10]
    // 0x8b934c: LoadField: r1 = r2->field_b
    //     0x8b934c: ldur            w1, [x2, #0xb]
    // 0x8b9350: r3 = LoadInt32Instr(r0)
    //     0x8b9350: sbfx            x3, x0, #1, #0x1f
    //     0x8b9354: tbz             w0, #0, #0x8b935c
    //     0x8b9358: ldur            x3, [x0, #7]
    // 0x8b935c: r0 = LoadInt32Instr(r1)
    //     0x8b935c: sbfx            x0, x1, #1, #0x1f
    // 0x8b9360: mov             x1, x3
    // 0x8b9364: cmp             x1, x0
    // 0x8b9368: b.hs            #0x8b9ab4
    // 0x8b936c: LoadField: r0 = r2->field_f
    //     0x8b936c: ldur            w0, [x2, #0xf]
    // 0x8b9370: DecompressPointer r0
    //     0x8b9370: add             x0, x0, HEAP, lsl #32
    // 0x8b9374: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9374: add             x16, x0, x3, lsl #2
    //     0x8b9378: ldur            w1, [x16, #0xf]
    // 0x8b937c: DecompressPointer r1
    //     0x8b937c: add             x1, x1, HEAP, lsl #32
    // 0x8b9380: mov             x0, x1
    // 0x8b9384: ldur            x1, [fp, #-0x20]
    // 0x8b9388: ArrayStore: r1[5] = r0  ; List_4
    //     0x8b9388: add             x25, x1, #0x23
    //     0x8b938c: str             w0, [x25]
    //     0x8b9390: tbz             w0, #0, #0x8b93ac
    //     0x8b9394: ldurb           w16, [x1, #-1]
    //     0x8b9398: ldurb           w17, [x0, #-1]
    //     0x8b939c: and             x16, x17, x16, lsr #2
    //     0x8b93a0: tst             x16, HEAP, lsr #32
    //     0x8b93a4: b.eq            #0x8b93ac
    //     0x8b93a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b93ac: ldur            x1, [fp, #-8]
    // 0x8b93b0: r0 = LoadClassIdInstr(r1)
    //     0x8b93b0: ldur            x0, [x1, #-1]
    //     0x8b93b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8b93b8: r16 = 10
    //     0x8b93b8: movz            x16, #0xa
    // 0x8b93bc: stp             x16, x1, [SP]
    // 0x8b93c0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b93c0: movz            x17, #0x3037
    //     0x8b93c4: movk            x17, #0x1, lsl #16
    //     0x8b93c8: add             lr, x0, x17
    //     0x8b93cc: ldr             lr, [x21, lr, lsl #3]
    //     0x8b93d0: blr             lr
    // 0x8b93d4: ldur            x2, [fp, #-0x10]
    // 0x8b93d8: LoadField: r1 = r2->field_b
    //     0x8b93d8: ldur            w1, [x2, #0xb]
    // 0x8b93dc: r3 = LoadInt32Instr(r0)
    //     0x8b93dc: sbfx            x3, x0, #1, #0x1f
    //     0x8b93e0: tbz             w0, #0, #0x8b93e8
    //     0x8b93e4: ldur            x3, [x0, #7]
    // 0x8b93e8: r0 = LoadInt32Instr(r1)
    //     0x8b93e8: sbfx            x0, x1, #1, #0x1f
    // 0x8b93ec: mov             x1, x3
    // 0x8b93f0: cmp             x1, x0
    // 0x8b93f4: b.hs            #0x8b9ab8
    // 0x8b93f8: LoadField: r0 = r2->field_f
    //     0x8b93f8: ldur            w0, [x2, #0xf]
    // 0x8b93fc: DecompressPointer r0
    //     0x8b93fc: add             x0, x0, HEAP, lsl #32
    // 0x8b9400: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9400: add             x16, x0, x3, lsl #2
    //     0x8b9404: ldur            w1, [x16, #0xf]
    // 0x8b9408: DecompressPointer r1
    //     0x8b9408: add             x1, x1, HEAP, lsl #32
    // 0x8b940c: mov             x0, x1
    // 0x8b9410: ldur            x1, [fp, #-0x20]
    // 0x8b9414: ArrayStore: r1[6] = r0  ; List_4
    //     0x8b9414: add             x25, x1, #0x27
    //     0x8b9418: str             w0, [x25]
    //     0x8b941c: tbz             w0, #0, #0x8b9438
    //     0x8b9420: ldurb           w16, [x1, #-1]
    //     0x8b9424: ldurb           w17, [x0, #-1]
    //     0x8b9428: and             x16, x17, x16, lsr #2
    //     0x8b942c: tst             x16, HEAP, lsr #32
    //     0x8b9430: b.eq            #0x8b9438
    //     0x8b9434: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9438: ldur            x1, [fp, #-0x20]
    // 0x8b943c: r16 = "-"
    //     0x8b943c: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x8b9440: StoreField: r1->field_2b = r16
    //     0x8b9440: stur            w16, [x1, #0x2b]
    // 0x8b9444: ldur            x3, [fp, #-8]
    // 0x8b9448: r0 = LoadClassIdInstr(r3)
    //     0x8b9448: ldur            x0, [x3, #-1]
    //     0x8b944c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9450: r16 = 12
    //     0x8b9450: movz            x16, #0xc
    // 0x8b9454: stp             x16, x3, [SP]
    // 0x8b9458: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9458: movz            x17, #0x3037
    //     0x8b945c: movk            x17, #0x1, lsl #16
    //     0x8b9460: add             lr, x0, x17
    //     0x8b9464: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9468: blr             lr
    // 0x8b946c: ldur            x2, [fp, #-0x10]
    // 0x8b9470: LoadField: r1 = r2->field_b
    //     0x8b9470: ldur            w1, [x2, #0xb]
    // 0x8b9474: r3 = LoadInt32Instr(r0)
    //     0x8b9474: sbfx            x3, x0, #1, #0x1f
    //     0x8b9478: tbz             w0, #0, #0x8b9480
    //     0x8b947c: ldur            x3, [x0, #7]
    // 0x8b9480: r0 = LoadInt32Instr(r1)
    //     0x8b9480: sbfx            x0, x1, #1, #0x1f
    // 0x8b9484: mov             x1, x3
    // 0x8b9488: cmp             x1, x0
    // 0x8b948c: b.hs            #0x8b9abc
    // 0x8b9490: LoadField: r0 = r2->field_f
    //     0x8b9490: ldur            w0, [x2, #0xf]
    // 0x8b9494: DecompressPointer r0
    //     0x8b9494: add             x0, x0, HEAP, lsl #32
    // 0x8b9498: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9498: add             x16, x0, x3, lsl #2
    //     0x8b949c: ldur            w1, [x16, #0xf]
    // 0x8b94a0: DecompressPointer r1
    //     0x8b94a0: add             x1, x1, HEAP, lsl #32
    // 0x8b94a4: mov             x0, x1
    // 0x8b94a8: ldur            x1, [fp, #-0x20]
    // 0x8b94ac: ArrayStore: r1[8] = r0  ; List_4
    //     0x8b94ac: add             x25, x1, #0x2f
    //     0x8b94b0: str             w0, [x25]
    //     0x8b94b4: tbz             w0, #0, #0x8b94d0
    //     0x8b94b8: ldurb           w16, [x1, #-1]
    //     0x8b94bc: ldurb           w17, [x0, #-1]
    //     0x8b94c0: and             x16, x17, x16, lsr #2
    //     0x8b94c4: tst             x16, HEAP, lsr #32
    //     0x8b94c8: b.eq            #0x8b94d0
    //     0x8b94cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b94d0: ldur            x1, [fp, #-8]
    // 0x8b94d4: r0 = LoadClassIdInstr(r1)
    //     0x8b94d4: ldur            x0, [x1, #-1]
    //     0x8b94d8: ubfx            x0, x0, #0xc, #0x14
    // 0x8b94dc: r16 = 14
    //     0x8b94dc: movz            x16, #0xe
    // 0x8b94e0: stp             x16, x1, [SP]
    // 0x8b94e4: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b94e4: movz            x17, #0x3037
    //     0x8b94e8: movk            x17, #0x1, lsl #16
    //     0x8b94ec: add             lr, x0, x17
    //     0x8b94f0: ldr             lr, [x21, lr, lsl #3]
    //     0x8b94f4: blr             lr
    // 0x8b94f8: ldur            x2, [fp, #-0x10]
    // 0x8b94fc: LoadField: r1 = r2->field_b
    //     0x8b94fc: ldur            w1, [x2, #0xb]
    // 0x8b9500: r3 = LoadInt32Instr(r0)
    //     0x8b9500: sbfx            x3, x0, #1, #0x1f
    //     0x8b9504: tbz             w0, #0, #0x8b950c
    //     0x8b9508: ldur            x3, [x0, #7]
    // 0x8b950c: r0 = LoadInt32Instr(r1)
    //     0x8b950c: sbfx            x0, x1, #1, #0x1f
    // 0x8b9510: mov             x1, x3
    // 0x8b9514: cmp             x1, x0
    // 0x8b9518: b.hs            #0x8b9ac0
    // 0x8b951c: LoadField: r0 = r2->field_f
    //     0x8b951c: ldur            w0, [x2, #0xf]
    // 0x8b9520: DecompressPointer r0
    //     0x8b9520: add             x0, x0, HEAP, lsl #32
    // 0x8b9524: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9524: add             x16, x0, x3, lsl #2
    //     0x8b9528: ldur            w1, [x16, #0xf]
    // 0x8b952c: DecompressPointer r1
    //     0x8b952c: add             x1, x1, HEAP, lsl #32
    // 0x8b9530: mov             x0, x1
    // 0x8b9534: ldur            x1, [fp, #-0x20]
    // 0x8b9538: ArrayStore: r1[9] = r0  ; List_4
    //     0x8b9538: add             x25, x1, #0x33
    //     0x8b953c: str             w0, [x25]
    //     0x8b9540: tbz             w0, #0, #0x8b955c
    //     0x8b9544: ldurb           w16, [x1, #-1]
    //     0x8b9548: ldurb           w17, [x0, #-1]
    //     0x8b954c: and             x16, x17, x16, lsr #2
    //     0x8b9550: tst             x16, HEAP, lsr #32
    //     0x8b9554: b.eq            #0x8b955c
    //     0x8b9558: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b955c: ldur            x1, [fp, #-0x20]
    // 0x8b9560: r16 = "-"
    //     0x8b9560: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x8b9564: StoreField: r1->field_37 = r16
    //     0x8b9564: stur            w16, [x1, #0x37]
    // 0x8b9568: ldur            x3, [fp, #-8]
    // 0x8b956c: r0 = LoadClassIdInstr(r3)
    //     0x8b956c: ldur            x0, [x3, #-1]
    //     0x8b9570: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9574: r16 = 16
    //     0x8b9574: movz            x16, #0x10
    // 0x8b9578: stp             x16, x3, [SP]
    // 0x8b957c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b957c: movz            x17, #0x3037
    //     0x8b9580: movk            x17, #0x1, lsl #16
    //     0x8b9584: add             lr, x0, x17
    //     0x8b9588: ldr             lr, [x21, lr, lsl #3]
    //     0x8b958c: blr             lr
    // 0x8b9590: ldur            x2, [fp, #-0x10]
    // 0x8b9594: LoadField: r1 = r2->field_b
    //     0x8b9594: ldur            w1, [x2, #0xb]
    // 0x8b9598: r3 = LoadInt32Instr(r0)
    //     0x8b9598: sbfx            x3, x0, #1, #0x1f
    //     0x8b959c: tbz             w0, #0, #0x8b95a4
    //     0x8b95a0: ldur            x3, [x0, #7]
    // 0x8b95a4: r0 = LoadInt32Instr(r1)
    //     0x8b95a4: sbfx            x0, x1, #1, #0x1f
    // 0x8b95a8: mov             x1, x3
    // 0x8b95ac: cmp             x1, x0
    // 0x8b95b0: b.hs            #0x8b9ac4
    // 0x8b95b4: LoadField: r0 = r2->field_f
    //     0x8b95b4: ldur            w0, [x2, #0xf]
    // 0x8b95b8: DecompressPointer r0
    //     0x8b95b8: add             x0, x0, HEAP, lsl #32
    // 0x8b95bc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b95bc: add             x16, x0, x3, lsl #2
    //     0x8b95c0: ldur            w1, [x16, #0xf]
    // 0x8b95c4: DecompressPointer r1
    //     0x8b95c4: add             x1, x1, HEAP, lsl #32
    // 0x8b95c8: mov             x0, x1
    // 0x8b95cc: ldur            x1, [fp, #-0x20]
    // 0x8b95d0: ArrayStore: r1[11] = r0  ; List_4
    //     0x8b95d0: add             x25, x1, #0x3b
    //     0x8b95d4: str             w0, [x25]
    //     0x8b95d8: tbz             w0, #0, #0x8b95f4
    //     0x8b95dc: ldurb           w16, [x1, #-1]
    //     0x8b95e0: ldurb           w17, [x0, #-1]
    //     0x8b95e4: and             x16, x17, x16, lsr #2
    //     0x8b95e8: tst             x16, HEAP, lsr #32
    //     0x8b95ec: b.eq            #0x8b95f4
    //     0x8b95f0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b95f4: ldur            x1, [fp, #-8]
    // 0x8b95f8: r0 = LoadClassIdInstr(r1)
    //     0x8b95f8: ldur            x0, [x1, #-1]
    //     0x8b95fc: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9600: r16 = 18
    //     0x8b9600: movz            x16, #0x12
    // 0x8b9604: stp             x16, x1, [SP]
    // 0x8b9608: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9608: movz            x17, #0x3037
    //     0x8b960c: movk            x17, #0x1, lsl #16
    //     0x8b9610: add             lr, x0, x17
    //     0x8b9614: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9618: blr             lr
    // 0x8b961c: ldur            x2, [fp, #-0x10]
    // 0x8b9620: LoadField: r1 = r2->field_b
    //     0x8b9620: ldur            w1, [x2, #0xb]
    // 0x8b9624: r3 = LoadInt32Instr(r0)
    //     0x8b9624: sbfx            x3, x0, #1, #0x1f
    //     0x8b9628: tbz             w0, #0, #0x8b9630
    //     0x8b962c: ldur            x3, [x0, #7]
    // 0x8b9630: r0 = LoadInt32Instr(r1)
    //     0x8b9630: sbfx            x0, x1, #1, #0x1f
    // 0x8b9634: mov             x1, x3
    // 0x8b9638: cmp             x1, x0
    // 0x8b963c: b.hs            #0x8b9ac8
    // 0x8b9640: LoadField: r0 = r2->field_f
    //     0x8b9640: ldur            w0, [x2, #0xf]
    // 0x8b9644: DecompressPointer r0
    //     0x8b9644: add             x0, x0, HEAP, lsl #32
    // 0x8b9648: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9648: add             x16, x0, x3, lsl #2
    //     0x8b964c: ldur            w1, [x16, #0xf]
    // 0x8b9650: DecompressPointer r1
    //     0x8b9650: add             x1, x1, HEAP, lsl #32
    // 0x8b9654: mov             x0, x1
    // 0x8b9658: ldur            x1, [fp, #-0x20]
    // 0x8b965c: ArrayStore: r1[12] = r0  ; List_4
    //     0x8b965c: add             x25, x1, #0x3f
    //     0x8b9660: str             w0, [x25]
    //     0x8b9664: tbz             w0, #0, #0x8b9680
    //     0x8b9668: ldurb           w16, [x1, #-1]
    //     0x8b966c: ldurb           w17, [x0, #-1]
    //     0x8b9670: and             x16, x17, x16, lsr #2
    //     0x8b9674: tst             x16, HEAP, lsr #32
    //     0x8b9678: b.eq            #0x8b9680
    //     0x8b967c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9680: ldur            x1, [fp, #-0x20]
    // 0x8b9684: r16 = "-"
    //     0x8b9684: ldr             x16, [PP, #0x1bc8]  ; [pp+0x1bc8] "-"
    // 0x8b9688: StoreField: r1->field_43 = r16
    //     0x8b9688: stur            w16, [x1, #0x43]
    // 0x8b968c: ldur            x3, [fp, #-8]
    // 0x8b9690: r0 = LoadClassIdInstr(r3)
    //     0x8b9690: ldur            x0, [x3, #-1]
    //     0x8b9694: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9698: r16 = 20
    //     0x8b9698: movz            x16, #0x14
    // 0x8b969c: stp             x16, x3, [SP]
    // 0x8b96a0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b96a0: movz            x17, #0x3037
    //     0x8b96a4: movk            x17, #0x1, lsl #16
    //     0x8b96a8: add             lr, x0, x17
    //     0x8b96ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8b96b0: blr             lr
    // 0x8b96b4: ldur            x2, [fp, #-0x10]
    // 0x8b96b8: LoadField: r1 = r2->field_b
    //     0x8b96b8: ldur            w1, [x2, #0xb]
    // 0x8b96bc: r3 = LoadInt32Instr(r0)
    //     0x8b96bc: sbfx            x3, x0, #1, #0x1f
    //     0x8b96c0: tbz             w0, #0, #0x8b96c8
    //     0x8b96c4: ldur            x3, [x0, #7]
    // 0x8b96c8: r0 = LoadInt32Instr(r1)
    //     0x8b96c8: sbfx            x0, x1, #1, #0x1f
    // 0x8b96cc: mov             x1, x3
    // 0x8b96d0: cmp             x1, x0
    // 0x8b96d4: b.hs            #0x8b9acc
    // 0x8b96d8: LoadField: r0 = r2->field_f
    //     0x8b96d8: ldur            w0, [x2, #0xf]
    // 0x8b96dc: DecompressPointer r0
    //     0x8b96dc: add             x0, x0, HEAP, lsl #32
    // 0x8b96e0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b96e0: add             x16, x0, x3, lsl #2
    //     0x8b96e4: ldur            w1, [x16, #0xf]
    // 0x8b96e8: DecompressPointer r1
    //     0x8b96e8: add             x1, x1, HEAP, lsl #32
    // 0x8b96ec: mov             x0, x1
    // 0x8b96f0: ldur            x1, [fp, #-0x20]
    // 0x8b96f4: ArrayStore: r1[14] = r0  ; List_4
    //     0x8b96f4: add             x25, x1, #0x47
    //     0x8b96f8: str             w0, [x25]
    //     0x8b96fc: tbz             w0, #0, #0x8b9718
    //     0x8b9700: ldurb           w16, [x1, #-1]
    //     0x8b9704: ldurb           w17, [x0, #-1]
    //     0x8b9708: and             x16, x17, x16, lsr #2
    //     0x8b970c: tst             x16, HEAP, lsr #32
    //     0x8b9710: b.eq            #0x8b9718
    //     0x8b9714: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9718: ldur            x1, [fp, #-8]
    // 0x8b971c: r0 = LoadClassIdInstr(r1)
    //     0x8b971c: ldur            x0, [x1, #-1]
    //     0x8b9720: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9724: r16 = 22
    //     0x8b9724: movz            x16, #0x16
    // 0x8b9728: stp             x16, x1, [SP]
    // 0x8b972c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b972c: movz            x17, #0x3037
    //     0x8b9730: movk            x17, #0x1, lsl #16
    //     0x8b9734: add             lr, x0, x17
    //     0x8b9738: ldr             lr, [x21, lr, lsl #3]
    //     0x8b973c: blr             lr
    // 0x8b9740: ldur            x2, [fp, #-0x10]
    // 0x8b9744: LoadField: r1 = r2->field_b
    //     0x8b9744: ldur            w1, [x2, #0xb]
    // 0x8b9748: r3 = LoadInt32Instr(r0)
    //     0x8b9748: sbfx            x3, x0, #1, #0x1f
    //     0x8b974c: tbz             w0, #0, #0x8b9754
    //     0x8b9750: ldur            x3, [x0, #7]
    // 0x8b9754: r0 = LoadInt32Instr(r1)
    //     0x8b9754: sbfx            x0, x1, #1, #0x1f
    // 0x8b9758: mov             x1, x3
    // 0x8b975c: cmp             x1, x0
    // 0x8b9760: b.hs            #0x8b9ad0
    // 0x8b9764: LoadField: r0 = r2->field_f
    //     0x8b9764: ldur            w0, [x2, #0xf]
    // 0x8b9768: DecompressPointer r0
    //     0x8b9768: add             x0, x0, HEAP, lsl #32
    // 0x8b976c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b976c: add             x16, x0, x3, lsl #2
    //     0x8b9770: ldur            w1, [x16, #0xf]
    // 0x8b9774: DecompressPointer r1
    //     0x8b9774: add             x1, x1, HEAP, lsl #32
    // 0x8b9778: mov             x0, x1
    // 0x8b977c: ldur            x1, [fp, #-0x20]
    // 0x8b9780: ArrayStore: r1[15] = r0  ; List_4
    //     0x8b9780: add             x25, x1, #0x4b
    //     0x8b9784: str             w0, [x25]
    //     0x8b9788: tbz             w0, #0, #0x8b97a4
    //     0x8b978c: ldurb           w16, [x1, #-1]
    //     0x8b9790: ldurb           w17, [x0, #-1]
    //     0x8b9794: and             x16, x17, x16, lsr #2
    //     0x8b9798: tst             x16, HEAP, lsr #32
    //     0x8b979c: b.eq            #0x8b97a4
    //     0x8b97a0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b97a4: ldur            x1, [fp, #-8]
    // 0x8b97a8: r0 = LoadClassIdInstr(r1)
    //     0x8b97a8: ldur            x0, [x1, #-1]
    //     0x8b97ac: ubfx            x0, x0, #0xc, #0x14
    // 0x8b97b0: r16 = 24
    //     0x8b97b0: movz            x16, #0x18
    // 0x8b97b4: stp             x16, x1, [SP]
    // 0x8b97b8: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b97b8: movz            x17, #0x3037
    //     0x8b97bc: movk            x17, #0x1, lsl #16
    //     0x8b97c0: add             lr, x0, x17
    //     0x8b97c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8b97c8: blr             lr
    // 0x8b97cc: ldur            x2, [fp, #-0x10]
    // 0x8b97d0: LoadField: r1 = r2->field_b
    //     0x8b97d0: ldur            w1, [x2, #0xb]
    // 0x8b97d4: r3 = LoadInt32Instr(r0)
    //     0x8b97d4: sbfx            x3, x0, #1, #0x1f
    //     0x8b97d8: tbz             w0, #0, #0x8b97e0
    //     0x8b97dc: ldur            x3, [x0, #7]
    // 0x8b97e0: r0 = LoadInt32Instr(r1)
    //     0x8b97e0: sbfx            x0, x1, #1, #0x1f
    // 0x8b97e4: mov             x1, x3
    // 0x8b97e8: cmp             x1, x0
    // 0x8b97ec: b.hs            #0x8b9ad4
    // 0x8b97f0: LoadField: r0 = r2->field_f
    //     0x8b97f0: ldur            w0, [x2, #0xf]
    // 0x8b97f4: DecompressPointer r0
    //     0x8b97f4: add             x0, x0, HEAP, lsl #32
    // 0x8b97f8: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b97f8: add             x16, x0, x3, lsl #2
    //     0x8b97fc: ldur            w1, [x16, #0xf]
    // 0x8b9800: DecompressPointer r1
    //     0x8b9800: add             x1, x1, HEAP, lsl #32
    // 0x8b9804: mov             x0, x1
    // 0x8b9808: ldur            x1, [fp, #-0x20]
    // 0x8b980c: ArrayStore: r1[16] = r0  ; List_4
    //     0x8b980c: add             x25, x1, #0x4f
    //     0x8b9810: str             w0, [x25]
    //     0x8b9814: tbz             w0, #0, #0x8b9830
    //     0x8b9818: ldurb           w16, [x1, #-1]
    //     0x8b981c: ldurb           w17, [x0, #-1]
    //     0x8b9820: and             x16, x17, x16, lsr #2
    //     0x8b9824: tst             x16, HEAP, lsr #32
    //     0x8b9828: b.eq            #0x8b9830
    //     0x8b982c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9830: ldur            x1, [fp, #-8]
    // 0x8b9834: r0 = LoadClassIdInstr(r1)
    //     0x8b9834: ldur            x0, [x1, #-1]
    //     0x8b9838: ubfx            x0, x0, #0xc, #0x14
    // 0x8b983c: r16 = 26
    //     0x8b983c: movz            x16, #0x1a
    // 0x8b9840: stp             x16, x1, [SP]
    // 0x8b9844: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9844: movz            x17, #0x3037
    //     0x8b9848: movk            x17, #0x1, lsl #16
    //     0x8b984c: add             lr, x0, x17
    //     0x8b9850: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9854: blr             lr
    // 0x8b9858: ldur            x2, [fp, #-0x10]
    // 0x8b985c: LoadField: r1 = r2->field_b
    //     0x8b985c: ldur            w1, [x2, #0xb]
    // 0x8b9860: r3 = LoadInt32Instr(r0)
    //     0x8b9860: sbfx            x3, x0, #1, #0x1f
    //     0x8b9864: tbz             w0, #0, #0x8b986c
    //     0x8b9868: ldur            x3, [x0, #7]
    // 0x8b986c: r0 = LoadInt32Instr(r1)
    //     0x8b986c: sbfx            x0, x1, #1, #0x1f
    // 0x8b9870: mov             x1, x3
    // 0x8b9874: cmp             x1, x0
    // 0x8b9878: b.hs            #0x8b9ad8
    // 0x8b987c: LoadField: r0 = r2->field_f
    //     0x8b987c: ldur            w0, [x2, #0xf]
    // 0x8b9880: DecompressPointer r0
    //     0x8b9880: add             x0, x0, HEAP, lsl #32
    // 0x8b9884: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9884: add             x16, x0, x3, lsl #2
    //     0x8b9888: ldur            w1, [x16, #0xf]
    // 0x8b988c: DecompressPointer r1
    //     0x8b988c: add             x1, x1, HEAP, lsl #32
    // 0x8b9890: mov             x0, x1
    // 0x8b9894: ldur            x1, [fp, #-0x20]
    // 0x8b9898: ArrayStore: r1[17] = r0  ; List_4
    //     0x8b9898: add             x25, x1, #0x53
    //     0x8b989c: str             w0, [x25]
    //     0x8b98a0: tbz             w0, #0, #0x8b98bc
    //     0x8b98a4: ldurb           w16, [x1, #-1]
    //     0x8b98a8: ldurb           w17, [x0, #-1]
    //     0x8b98ac: and             x16, x17, x16, lsr #2
    //     0x8b98b0: tst             x16, HEAP, lsr #32
    //     0x8b98b4: b.eq            #0x8b98bc
    //     0x8b98b8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b98bc: ldur            x1, [fp, #-8]
    // 0x8b98c0: r0 = LoadClassIdInstr(r1)
    //     0x8b98c0: ldur            x0, [x1, #-1]
    //     0x8b98c4: ubfx            x0, x0, #0xc, #0x14
    // 0x8b98c8: r16 = 28
    //     0x8b98c8: movz            x16, #0x1c
    // 0x8b98cc: stp             x16, x1, [SP]
    // 0x8b98d0: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b98d0: movz            x17, #0x3037
    //     0x8b98d4: movk            x17, #0x1, lsl #16
    //     0x8b98d8: add             lr, x0, x17
    //     0x8b98dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8b98e0: blr             lr
    // 0x8b98e4: ldur            x2, [fp, #-0x10]
    // 0x8b98e8: LoadField: r1 = r2->field_b
    //     0x8b98e8: ldur            w1, [x2, #0xb]
    // 0x8b98ec: r3 = LoadInt32Instr(r0)
    //     0x8b98ec: sbfx            x3, x0, #1, #0x1f
    //     0x8b98f0: tbz             w0, #0, #0x8b98f8
    //     0x8b98f4: ldur            x3, [x0, #7]
    // 0x8b98f8: r0 = LoadInt32Instr(r1)
    //     0x8b98f8: sbfx            x0, x1, #1, #0x1f
    // 0x8b98fc: mov             x1, x3
    // 0x8b9900: cmp             x1, x0
    // 0x8b9904: b.hs            #0x8b9adc
    // 0x8b9908: LoadField: r0 = r2->field_f
    //     0x8b9908: ldur            w0, [x2, #0xf]
    // 0x8b990c: DecompressPointer r0
    //     0x8b990c: add             x0, x0, HEAP, lsl #32
    // 0x8b9910: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b9910: add             x16, x0, x3, lsl #2
    //     0x8b9914: ldur            w1, [x16, #0xf]
    // 0x8b9918: DecompressPointer r1
    //     0x8b9918: add             x1, x1, HEAP, lsl #32
    // 0x8b991c: mov             x0, x1
    // 0x8b9920: ldur            x1, [fp, #-0x20]
    // 0x8b9924: ArrayStore: r1[18] = r0  ; List_4
    //     0x8b9924: add             x25, x1, #0x57
    //     0x8b9928: str             w0, [x25]
    //     0x8b992c: tbz             w0, #0, #0x8b9948
    //     0x8b9930: ldurb           w16, [x1, #-1]
    //     0x8b9934: ldurb           w17, [x0, #-1]
    //     0x8b9938: and             x16, x17, x16, lsr #2
    //     0x8b993c: tst             x16, HEAP, lsr #32
    //     0x8b9940: b.eq            #0x8b9948
    //     0x8b9944: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9948: ldur            x0, [fp, #-8]
    // 0x8b994c: r1 = LoadClassIdInstr(r0)
    //     0x8b994c: ldur            x1, [x0, #-1]
    //     0x8b9950: ubfx            x1, x1, #0xc, #0x14
    // 0x8b9954: r16 = 30
    //     0x8b9954: movz            x16, #0x1e
    // 0x8b9958: stp             x16, x0, [SP]
    // 0x8b995c: mov             x0, x1
    // 0x8b9960: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8b9960: movz            x17, #0x3037
    //     0x8b9964: movk            x17, #0x1, lsl #16
    //     0x8b9968: add             lr, x0, x17
    //     0x8b996c: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9970: blr             lr
    // 0x8b9974: ldur            x2, [fp, #-0x10]
    // 0x8b9978: LoadField: r1 = r2->field_b
    //     0x8b9978: ldur            w1, [x2, #0xb]
    // 0x8b997c: r3 = LoadInt32Instr(r0)
    //     0x8b997c: sbfx            x3, x0, #1, #0x1f
    //     0x8b9980: tbz             w0, #0, #0x8b9988
    //     0x8b9984: ldur            x3, [x0, #7]
    // 0x8b9988: r0 = LoadInt32Instr(r1)
    //     0x8b9988: sbfx            x0, x1, #1, #0x1f
    // 0x8b998c: mov             x1, x3
    // 0x8b9990: cmp             x1, x0
    // 0x8b9994: b.hs            #0x8b9ae0
    // 0x8b9998: LoadField: r0 = r2->field_f
    //     0x8b9998: ldur            w0, [x2, #0xf]
    // 0x8b999c: DecompressPointer r0
    //     0x8b999c: add             x0, x0, HEAP, lsl #32
    // 0x8b99a0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8b99a0: add             x16, x0, x3, lsl #2
    //     0x8b99a4: ldur            w1, [x16, #0xf]
    // 0x8b99a8: DecompressPointer r1
    //     0x8b99a8: add             x1, x1, HEAP, lsl #32
    // 0x8b99ac: mov             x0, x1
    // 0x8b99b0: ldur            x1, [fp, #-0x20]
    // 0x8b99b4: ArrayStore: r1[19] = r0  ; List_4
    //     0x8b99b4: add             x25, x1, #0x5b
    //     0x8b99b8: str             w0, [x25]
    //     0x8b99bc: tbz             w0, #0, #0x8b99d8
    //     0x8b99c0: ldurb           w16, [x1, #-1]
    //     0x8b99c4: ldurb           w17, [x0, #-1]
    //     0x8b99c8: and             x16, x17, x16, lsr #2
    //     0x8b99cc: tst             x16, HEAP, lsr #32
    //     0x8b99d0: b.eq            #0x8b99d8
    //     0x8b99d4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b99d8: ldur            x16, [fp, #-0x20]
    // 0x8b99dc: str             x16, [SP]
    // 0x8b99e0: r0 = _interpolate()
    //     0x8b99e0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8b99e4: LeaveFrame
    //     0x8b99e4: mov             SP, fp
    //     0x8b99e8: ldp             fp, lr, [SP], #0x10
    // 0x8b99ec: ret
    //     0x8b99ec: ret             
    // 0x8b99f0: ldur            x0, [fp, #-8]
    // 0x8b99f4: r1 = Null
    //     0x8b99f4: mov             x1, NULL
    // 0x8b99f8: r2 = 6
    //     0x8b99f8: movz            x2, #0x6
    // 0x8b99fc: r0 = AllocateArray()
    //     0x8b99fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b9a00: mov             x1, x0
    // 0x8b9a04: stur            x1, [fp, #-0x10]
    // 0x8b9a08: r16 = "buffer too small: need 16: length="
    //     0x8b9a08: add             x16, PP, #0xf, lsl #12  ; [pp+0xf040] "buffer too small: need 16: length="
    //     0x8b9a0c: ldr             x16, [x16, #0x40]
    // 0x8b9a10: StoreField: r1->field_f = r16
    //     0x8b9a10: stur            w16, [x1, #0xf]
    // 0x8b9a14: ldur            x0, [fp, #-8]
    // 0x8b9a18: r2 = LoadClassIdInstr(r0)
    //     0x8b9a18: ldur            x2, [x0, #-1]
    //     0x8b9a1c: ubfx            x2, x2, #0xc, #0x14
    // 0x8b9a20: str             x0, [SP]
    // 0x8b9a24: mov             x0, x2
    // 0x8b9a28: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8b9a28: movz            x17, #0xc834
    //     0x8b9a2c: add             lr, x0, x17
    //     0x8b9a30: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9a34: blr             lr
    // 0x8b9a38: ldur            x1, [fp, #-0x10]
    // 0x8b9a3c: ArrayStore: r1[1] = r0  ; List_4
    //     0x8b9a3c: add             x25, x1, #0x13
    //     0x8b9a40: str             w0, [x25]
    //     0x8b9a44: tbz             w0, #0, #0x8b9a60
    //     0x8b9a48: ldurb           w16, [x1, #-1]
    //     0x8b9a4c: ldurb           w17, [x0, #-1]
    //     0x8b9a50: and             x16, x17, x16, lsr #2
    //     0x8b9a54: tst             x16, HEAP, lsr #32
    //     0x8b9a58: b.eq            #0x8b9a60
    //     0x8b9a5c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9a60: ldur            x0, [fp, #-0x10]
    // 0x8b9a64: r16 = ""
    //     0x8b9a64: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8b9a68: ArrayStore: r0[0] = r16  ; List_4
    //     0x8b9a68: stur            w16, [x0, #0x17]
    // 0x8b9a6c: str             x0, [SP]
    // 0x8b9a70: r0 = _interpolate()
    //     0x8b9a70: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8b9a74: stur            x0, [fp, #-8]
    // 0x8b9a78: r0 = RangeError()
    //     0x8b9a78: bl              #0x600584  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x8b9a7c: mov             x1, x0
    // 0x8b9a80: ldur            x0, [fp, #-8]
    // 0x8b9a84: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b9a84: stur            w0, [x1, #0x17]
    // 0x8b9a88: r0 = false
    //     0x8b9a88: add             x0, NULL, #0x30  ; false
    // 0x8b9a8c: StoreField: r1->field_b = r0
    //     0x8b9a8c: stur            w0, [x1, #0xb]
    // 0x8b9a90: mov             x0, x1
    // 0x8b9a94: r0 = Throw()
    //     0x8b9a94: bl              #0xec04b8  ; ThrowStub
    // 0x8b9a98: brk             #0
    // 0x8b9a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b9a9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b9aa0: b               #0x8b9098
    // 0x8b9aa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9aa4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9aa8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9aa8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9aac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9aac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ab0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ab0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ab4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ab4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ab8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ab8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9abc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9abc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ac0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ac0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ac4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ac4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ac8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ac8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9acc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9acc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ad0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ad0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ad4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ad4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ad8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ad8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9adc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9adc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8b9ae0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b9ae0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  static List<String> _byteToHex() {
    // ** addr: 0x8b9ae4, size: 0xd8
    // 0x8b9ae4: EnterFrame
    //     0x8b9ae4: stp             fp, lr, [SP, #-0x10]!
    //     0x8b9ae8: mov             fp, SP
    // 0x8b9aec: AllocStack(0x20)
    //     0x8b9aec: sub             SP, SP, #0x20
    // 0x8b9af0: CheckStackOverflow
    //     0x8b9af0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b9af4: cmp             SP, x16
    //     0x8b9af8: b.ls            #0x8b9bac
    // 0x8b9afc: r1 = <String>
    //     0x8b9afc: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x8b9b00: r2 = 256
    //     0x8b9b00: movz            x2, #0x100
    // 0x8b9b04: r0 = _GrowableList()
    //     0x8b9b04: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x8b9b08: stur            x0, [fp, #-0x20]
    // 0x8b9b0c: LoadField: r1 = r0->field_b
    //     0x8b9b0c: ldur            w1, [x0, #0xb]
    // 0x8b9b10: r2 = LoadInt32Instr(r1)
    //     0x8b9b10: sbfx            x2, x1, #1, #0x1f
    // 0x8b9b14: stur            x2, [fp, #-0x18]
    // 0x8b9b18: LoadField: r3 = r0->field_f
    //     0x8b9b18: ldur            w3, [x0, #0xf]
    // 0x8b9b1c: DecompressPointer r3
    //     0x8b9b1c: add             x3, x3, HEAP, lsl #32
    // 0x8b9b20: stur            x3, [fp, #-0x10]
    // 0x8b9b24: r4 = 0
    //     0x8b9b24: movz            x4, #0
    // 0x8b9b28: stur            x4, [fp, #-8]
    // 0x8b9b2c: CheckStackOverflow
    //     0x8b9b2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b9b30: cmp             SP, x16
    //     0x8b9b34: b.ls            #0x8b9bb4
    // 0x8b9b38: cmp             x4, x2
    // 0x8b9b3c: b.ge            #0x8b9b9c
    // 0x8b9b40: lsl             x1, x4, #1
    // 0x8b9b44: r0 = _toPow2String()
    //     0x8b9b44: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0x8b9b48: mov             x1, x0
    // 0x8b9b4c: r2 = 2
    //     0x8b9b4c: movz            x2, #0x2
    // 0x8b9b50: r3 = "0"
    //     0x8b9b50: ldr             x3, [PP, #0x44c8]  ; [pp+0x44c8] "0"
    // 0x8b9b54: r0 = padLeft()
    //     0x8b9b54: bl              #0xebe370  ; [dart:core] _OneByteString::padLeft
    // 0x8b9b58: ldur            x1, [fp, #-0x10]
    // 0x8b9b5c: ldur            x2, [fp, #-8]
    // 0x8b9b60: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8b9b60: add             x25, x1, x2, lsl #2
    //     0x8b9b64: add             x25, x25, #0xf
    //     0x8b9b68: str             w0, [x25]
    //     0x8b9b6c: tbz             w0, #0, #0x8b9b88
    //     0x8b9b70: ldurb           w16, [x1, #-1]
    //     0x8b9b74: ldurb           w17, [x0, #-1]
    //     0x8b9b78: and             x16, x17, x16, lsr #2
    //     0x8b9b7c: tst             x16, HEAP, lsr #32
    //     0x8b9b80: b.eq            #0x8b9b88
    //     0x8b9b84: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x8b9b88: add             x4, x2, #1
    // 0x8b9b8c: ldur            x0, [fp, #-0x20]
    // 0x8b9b90: ldur            x3, [fp, #-0x10]
    // 0x8b9b94: ldur            x2, [fp, #-0x18]
    // 0x8b9b98: b               #0x8b9b28
    // 0x8b9b9c: ldur            x0, [fp, #-0x20]
    // 0x8b9ba0: LeaveFrame
    //     0x8b9ba0: mov             SP, fp
    //     0x8b9ba4: ldp             fp, lr, [SP], #0x10
    // 0x8b9ba8: ret
    //     0x8b9ba8: ret             
    // 0x8b9bac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b9bac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b9bb0: b               #0x8b9afc
    // 0x8b9bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b9bb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b9bb8: b               #0x8b9b38
  }
  get _ _state(/* No info */) {
    // ** addr: 0x8b9bbc, size: 0x114
    // 0x8b9bbc: EnterFrame
    //     0x8b9bbc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b9bc0: mov             fp, SP
    // 0x8b9bc4: AllocStack(0x20)
    //     0x8b9bc4: sub             SP, SP, #0x20
    // 0x8b9bc8: SetupParameters(Uuid this /* r1 => r2, fp-0x8 */)
    //     0x8b9bc8: mov             x2, x1
    //     0x8b9bcc: stur            x1, [fp, #-8]
    // 0x8b9bd0: CheckStackOverflow
    //     0x8b9bd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b9bd4: cmp             SP, x16
    //     0x8b9bd8: b.ls            #0x8b9cc8
    // 0x8b9bdc: r0 = InitLateStaticField(0xca8) // [package:uuid/uuid.dart] Uuid::_stateExpando
    //     0x8b9bdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b9be0: ldr             x0, [x0, #0x1950]
    //     0x8b9be4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b9be8: cmp             w0, w16
    //     0x8b9bec: b.ne            #0x8b9bfc
    //     0x8b9bf0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf048] Field <Uuid._stateExpando@811507722>: static late final (offset: 0xca8)
    //     0x8b9bf4: ldr             x2, [x2, #0x48]
    //     0x8b9bf8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8b9bfc: mov             x1, x0
    // 0x8b9c00: ldur            x2, [fp, #-8]
    // 0x8b9c04: stur            x0, [fp, #-0x10]
    // 0x8b9c08: r0 = []()
    //     0x8b9c08: bl              #0x80b3cc  ; [dart:core] Expando::[]
    // 0x8b9c0c: cmp             w0, NULL
    // 0x8b9c10: b.ne            #0x8b9cbc
    // 0x8b9c14: r1 = Null
    //     0x8b9c14: mov             x1, NULL
    // 0x8b9c18: r2 = 28
    //     0x8b9c18: movz            x2, #0x1c
    // 0x8b9c1c: r0 = AllocateArray()
    //     0x8b9c1c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b9c20: r16 = "seedBytes"
    //     0x8b9c20: add             x16, PP, #0xf, lsl #12  ; [pp+0xf050] "seedBytes"
    //     0x8b9c24: ldr             x16, [x16, #0x50]
    // 0x8b9c28: StoreField: r0->field_f = r16
    //     0x8b9c28: stur            w16, [x0, #0xf]
    // 0x8b9c2c: StoreField: r0->field_13 = rNULL
    //     0x8b9c2c: stur            NULL, [x0, #0x13]
    // 0x8b9c30: r16 = "node"
    //     0x8b9c30: add             x16, PP, #0xf, lsl #12  ; [pp+0xf058] "node"
    //     0x8b9c34: ldr             x16, [x16, #0x58]
    // 0x8b9c38: ArrayStore: r0[0] = r16  ; List_4
    //     0x8b9c38: stur            w16, [x0, #0x17]
    // 0x8b9c3c: StoreField: r0->field_1b = rNULL
    //     0x8b9c3c: stur            NULL, [x0, #0x1b]
    // 0x8b9c40: r16 = "clockSeq"
    //     0x8b9c40: add             x16, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0x8b9c44: ldr             x16, [x16, #0x60]
    // 0x8b9c48: StoreField: r0->field_1f = r16
    //     0x8b9c48: stur            w16, [x0, #0x1f]
    // 0x8b9c4c: StoreField: r0->field_23 = rNULL
    //     0x8b9c4c: stur            NULL, [x0, #0x23]
    // 0x8b9c50: r16 = "mSecs"
    //     0x8b9c50: add             x16, PP, #0xf, lsl #12  ; [pp+0xf068] "mSecs"
    //     0x8b9c54: ldr             x16, [x16, #0x68]
    // 0x8b9c58: StoreField: r0->field_27 = r16
    //     0x8b9c58: stur            w16, [x0, #0x27]
    // 0x8b9c5c: StoreField: r0->field_2b = rZR
    //     0x8b9c5c: stur            wzr, [x0, #0x2b]
    // 0x8b9c60: r16 = "nSecs"
    //     0x8b9c60: add             x16, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0x8b9c64: ldr             x16, [x16, #0x70]
    // 0x8b9c68: StoreField: r0->field_2f = r16
    //     0x8b9c68: stur            w16, [x0, #0x2f]
    // 0x8b9c6c: StoreField: r0->field_33 = rZR
    //     0x8b9c6c: stur            wzr, [x0, #0x33]
    // 0x8b9c70: r16 = "hasInitV1"
    //     0x8b9c70: add             x16, PP, #0xf, lsl #12  ; [pp+0xf078] "hasInitV1"
    //     0x8b9c74: ldr             x16, [x16, #0x78]
    // 0x8b9c78: StoreField: r0->field_37 = r16
    //     0x8b9c78: stur            w16, [x0, #0x37]
    // 0x8b9c7c: r16 = false
    //     0x8b9c7c: add             x16, NULL, #0x30  ; false
    // 0x8b9c80: StoreField: r0->field_3b = r16
    //     0x8b9c80: stur            w16, [x0, #0x3b]
    // 0x8b9c84: r16 = "hasInitV4"
    //     0x8b9c84: add             x16, PP, #0xf, lsl #12  ; [pp+0xf080] "hasInitV4"
    //     0x8b9c88: ldr             x16, [x16, #0x80]
    // 0x8b9c8c: StoreField: r0->field_3f = r16
    //     0x8b9c8c: stur            w16, [x0, #0x3f]
    // 0x8b9c90: r16 = false
    //     0x8b9c90: add             x16, NULL, #0x30  ; false
    // 0x8b9c94: StoreField: r0->field_43 = r16
    //     0x8b9c94: stur            w16, [x0, #0x43]
    // 0x8b9c98: r16 = <String, dynamic>
    //     0x8b9c98: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x8b9c9c: stp             x0, x16, [SP]
    // 0x8b9ca0: r0 = Map._fromLiteral()
    //     0x8b9ca0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8b9ca4: ldur            x1, [fp, #-0x10]
    // 0x8b9ca8: ldur            x2, [fp, #-8]
    // 0x8b9cac: mov             x3, x0
    // 0x8b9cb0: stur            x0, [fp, #-8]
    // 0x8b9cb4: r0 = []=()
    //     0x8b9cb4: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x8b9cb8: ldur            x0, [fp, #-8]
    // 0x8b9cbc: LeaveFrame
    //     0x8b9cbc: mov             SP, fp
    //     0x8b9cc0: ldp             fp, lr, [SP], #0x10
    // 0x8b9cc4: ret
    //     0x8b9cc4: ret             
    // 0x8b9cc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b9cc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b9ccc: b               #0x8b9bdc
  }
  static Expando<Map<String, dynamic>> _stateExpando() {
    // ** addr: 0x8b9cd0, size: 0x40
    // 0x8b9cd0: EnterFrame
    //     0x8b9cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b9cd4: mov             fp, SP
    // 0x8b9cd8: AllocStack(0x8)
    //     0x8b9cd8: sub             SP, SP, #8
    // 0x8b9cdc: r1 = <Map<String, dynamic>>
    //     0x8b9cdc: ldr             x1, [PP, #0x1d40]  ; [pp+0x1d40] TypeArguments: <Map<String, dynamic>>
    // 0x8b9ce0: r0 = Expando()
    //     0x8b9ce0: bl              #0x7da070  ; AllocateExpandoStub -> Expando<X0> (size=0x1c)
    // 0x8b9ce4: r1 = <_WeakProperty?>
    //     0x8b9ce4: ldr             x1, [PP, #0x1210]  ; [pp+0x1210] TypeArguments: <_WeakProperty?>
    // 0x8b9ce8: r2 = 16
    //     0x8b9ce8: movz            x2, #0x10
    // 0x8b9cec: stur            x0, [fp, #-8]
    // 0x8b9cf0: r0 = AllocateArray()
    //     0x8b9cf0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8b9cf4: mov             x1, x0
    // 0x8b9cf8: ldur            x0, [fp, #-8]
    // 0x8b9cfc: StoreField: r0->field_b = r1
    //     0x8b9cfc: stur            w1, [x0, #0xb]
    // 0x8b9d00: StoreField: r0->field_f = rZR
    //     0x8b9d00: stur            xzr, [x0, #0xf]
    // 0x8b9d04: LeaveFrame
    //     0x8b9d04: mov             SP, fp
    //     0x8b9d08: ldp             fp, lr, [SP], #0x10
    // 0x8b9d0c: ret
    //     0x8b9d0c: ret             
  }
  _ _initV4(/* No info */) {
    // ** addr: 0x8b9d10, size: 0x264
    // 0x8b9d10: EnterFrame
    //     0x8b9d10: stp             fp, lr, [SP, #-0x10]!
    //     0x8b9d14: mov             fp, SP
    // 0x8b9d18: AllocStack(0x20)
    //     0x8b9d18: sub             SP, SP, #0x20
    // 0x8b9d1c: SetupParameters(Uuid this /* r1 => r0, fp-0x8 */)
    //     0x8b9d1c: mov             x0, x1
    //     0x8b9d20: stur            x1, [fp, #-8]
    // 0x8b9d24: CheckStackOverflow
    //     0x8b9d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b9d28: cmp             SP, x16
    //     0x8b9d2c: b.ls            #0x8b9f68
    // 0x8b9d30: mov             x1, x0
    // 0x8b9d34: r0 = _state()
    //     0x8b9d34: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0x8b9d38: r1 = LoadClassIdInstr(r0)
    //     0x8b9d38: ldur            x1, [x0, #-1]
    //     0x8b9d3c: ubfx            x1, x1, #0xc, #0x14
    // 0x8b9d40: mov             x16, x0
    // 0x8b9d44: mov             x0, x1
    // 0x8b9d48: mov             x1, x16
    // 0x8b9d4c: r2 = "hasInitV4"
    //     0x8b9d4c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf080] "hasInitV4"
    //     0x8b9d50: ldr             x2, [x2, #0x80]
    // 0x8b9d54: r0 = GDT[cid_x0 + -0x114]()
    //     0x8b9d54: sub             lr, x0, #0x114
    //     0x8b9d58: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9d5c: blr             lr
    // 0x8b9d60: mov             x3, x0
    // 0x8b9d64: stur            x3, [fp, #-0x10]
    // 0x8b9d68: cmp             w3, NULL
    // 0x8b9d6c: b.eq            #0x8b9f70
    // 0x8b9d70: mov             x0, x3
    // 0x8b9d74: r2 = Null
    //     0x8b9d74: mov             x2, NULL
    // 0x8b9d78: r1 = Null
    //     0x8b9d78: mov             x1, NULL
    // 0x8b9d7c: r4 = 60
    //     0x8b9d7c: movz            x4, #0x3c
    // 0x8b9d80: branchIfSmi(r0, 0x8b9d8c)
    //     0x8b9d80: tbz             w0, #0, #0x8b9d8c
    // 0x8b9d84: r4 = LoadClassIdInstr(r0)
    //     0x8b9d84: ldur            x4, [x0, #-1]
    //     0x8b9d88: ubfx            x4, x4, #0xc, #0x14
    // 0x8b9d8c: cmp             x4, #0x3f
    // 0x8b9d90: b.eq            #0x8b9da4
    // 0x8b9d94: r8 = bool
    //     0x8b9d94: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0x8b9d98: r3 = Null
    //     0x8b9d98: add             x3, PP, #0xf, lsl #12  ; [pp+0xf0b8] Null
    //     0x8b9d9c: ldr             x3, [x3, #0xb8]
    // 0x8b9da0: r0 = bool()
    //     0x8b9da0: bl              #0xed4390  ; IsType_bool_Stub
    // 0x8b9da4: ldur            x0, [fp, #-0x10]
    // 0x8b9da8: tbz             w0, #4, #0x8b9f58
    // 0x8b9dac: r1 = _ConstMap len:0
    //     0x8b9dac: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0x8b9db0: ldr             x1, [x1, #0xc8]
    // 0x8b9db4: r2 = "gPositionalArgs"
    //     0x8b9db4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf0d0] "gPositionalArgs"
    //     0x8b9db8: ldr             x2, [x2, #0xd0]
    // 0x8b9dbc: r0 = []()
    //     0x8b9dbc: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x8b9dc0: cmp             w0, NULL
    // 0x8b9dc4: b.eq            #0x8b9de0
    // 0x8b9dc8: r1 = _ConstMap len:0
    //     0x8b9dc8: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0x8b9dcc: ldr             x1, [x1, #0xc8]
    // 0x8b9dd0: r2 = "gPositionalArgs"
    //     0x8b9dd0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf0d0] "gPositionalArgs"
    //     0x8b9dd4: ldr             x2, [x2, #0xd0]
    // 0x8b9dd8: r0 = []()
    //     0x8b9dd8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x8b9ddc: b               #0x8b9de8
    // 0x8b9de0: r0 = const []
    //     0x8b9de0: add             x0, PP, #0xf, lsl #12  ; [pp+0xf0d8] List(0) []
    //     0x8b9de4: ldr             x0, [x0, #0xd8]
    // 0x8b9de8: stur            x0, [fp, #-0x10]
    // 0x8b9dec: r1 = 3
    //     0x8b9dec: movz            x1, #0x3
    // 0x8b9df0: r0 = AllocateContext()
    //     0x8b9df0: bl              #0xec126c  ; AllocateContextStub
    // 0x8b9df4: mov             x3, x0
    // 0x8b9df8: ldur            x0, [fp, #-0x10]
    // 0x8b9dfc: stur            x3, [fp, #-0x18]
    // 0x8b9e00: StoreField: r3->field_f = r0
    //     0x8b9e00: stur            w0, [x3, #0xf]
    // 0x8b9e04: r1 = _ConstMap len:0
    //     0x8b9e04: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0x8b9e08: ldr             x1, [x1, #0xc8]
    // 0x8b9e0c: r2 = "gNamedArgs"
    //     0x8b9e0c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf0e0] "gNamedArgs"
    //     0x8b9e10: ldr             x2, [x2, #0xe0]
    // 0x8b9e14: r0 = []()
    //     0x8b9e14: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x8b9e18: cmp             w0, NULL
    // 0x8b9e1c: b.eq            #0x8b9e60
    // 0x8b9e20: r1 = _ConstMap len:0
    //     0x8b9e20: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0x8b9e24: ldr             x1, [x1, #0xc8]
    // 0x8b9e28: r2 = "gNamedArgs"
    //     0x8b9e28: add             x2, PP, #0xf, lsl #12  ; [pp+0xf0e0] "gNamedArgs"
    //     0x8b9e2c: ldr             x2, [x2, #0xe0]
    // 0x8b9e30: r0 = []()
    //     0x8b9e30: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x8b9e34: mov             x3, x0
    // 0x8b9e38: r2 = Null
    //     0x8b9e38: mov             x2, NULL
    // 0x8b9e3c: r1 = Null
    //     0x8b9e3c: mov             x1, NULL
    // 0x8b9e40: stur            x3, [fp, #-0x10]
    // 0x8b9e44: r8 = Map<Symbol, dynamic>
    //     0x8b9e44: add             x8, PP, #0xe, lsl #12  ; [pp+0xefa0] Type: Map<Symbol, dynamic>
    //     0x8b9e48: ldr             x8, [x8, #0xfa0]
    // 0x8b9e4c: r3 = Null
    //     0x8b9e4c: add             x3, PP, #0xf, lsl #12  ; [pp+0xf0e8] Null
    //     0x8b9e50: ldr             x3, [x3, #0xe8]
    // 0x8b9e54: r0 = Map<Symbol, dynamic>()
    //     0x8b9e54: bl              #0x8ba270  ; IsType_Map<Symbol, dynamic>_Stub
    // 0x8b9e58: ldur            x0, [fp, #-0x10]
    // 0x8b9e5c: b               #0x8b9e68
    // 0x8b9e60: r0 = _ConstMap len:0
    //     0x8b9e60: add             x0, PP, #0xe, lsl #12  ; [pp+0xefb8] Map<Symbol, dynamic>(0)
    //     0x8b9e64: ldr             x0, [x0, #0xfb8]
    // 0x8b9e68: ldur            x3, [fp, #-0x18]
    // 0x8b9e6c: StoreField: r3->field_13 = r0
    //     0x8b9e6c: stur            w0, [x3, #0x13]
    //     0x8b9e70: ldurb           w16, [x3, #-1]
    //     0x8b9e74: ldurb           w17, [x0, #-1]
    //     0x8b9e78: and             x16, x17, x16, lsr #2
    //     0x8b9e7c: tst             x16, HEAP, lsr #32
    //     0x8b9e80: b.eq            #0x8b9e88
    //     0x8b9e84: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x8b9e88: r1 = _ConstMap len:0
    //     0x8b9e88: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0x8b9e8c: ldr             x1, [x1, #0xc8]
    // 0x8b9e90: r2 = "grng"
    //     0x8b9e90: add             x2, PP, #0xf, lsl #12  ; [pp+0xf0f8] "grng"
    //     0x8b9e94: ldr             x2, [x2, #0xf8]
    // 0x8b9e98: r0 = []()
    //     0x8b9e98: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x8b9e9c: mov             x3, x0
    // 0x8b9ea0: ldur            x2, [fp, #-0x18]
    // 0x8b9ea4: stur            x3, [fp, #-0x10]
    // 0x8b9ea8: ArrayStore: r2[0] = r0  ; List_4
    //     0x8b9ea8: stur            w0, [x2, #0x17]
    //     0x8b9eac: tbz             w0, #0, #0x8b9ec8
    //     0x8b9eb0: ldurb           w16, [x2, #-1]
    //     0x8b9eb4: ldurb           w17, [x0, #-1]
    //     0x8b9eb8: and             x16, x17, x16, lsr #2
    //     0x8b9ebc: tst             x16, HEAP, lsr #32
    //     0x8b9ec0: b.eq            #0x8b9ec8
    //     0x8b9ec4: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8b9ec8: ldur            x1, [fp, #-8]
    // 0x8b9ecc: r0 = _state()
    //     0x8b9ecc: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0x8b9ed0: mov             x3, x0
    // 0x8b9ed4: ldur            x0, [fp, #-0x10]
    // 0x8b9ed8: stur            x3, [fp, #-0x20]
    // 0x8b9edc: cmp             w0, NULL
    // 0x8b9ee0: b.eq            #0x8b9efc
    // 0x8b9ee4: ldur            x2, [fp, #-0x18]
    // 0x8b9ee8: r1 = Function '<anonymous closure>':.
    //     0x8b9ee8: add             x1, PP, #0xf, lsl #12  ; [pp+0xf100] AnonymousClosure: (0x8ba184), in [package:uuid/uuid.dart] Uuid::_initV4 (0x8b9d10)
    //     0x8b9eec: ldr             x1, [x1, #0x100]
    // 0x8b9ef0: r0 = AllocateClosure()
    //     0x8b9ef0: bl              #0xec1630  ; AllocateClosureStub
    // 0x8b9ef4: mov             x3, x0
    // 0x8b9ef8: b               #0x8b9f04
    // 0x8b9efc: r3 = Closure: ({int seed}) => Uint8List from Function 'mathRNG': static.
    //     0x8b9efc: add             x3, PP, #0xf, lsl #12  ; [pp+0xf108] Closure: ({int seed}) => Uint8List from Function 'mathRNG': static. (0x7e54fb2b9f74)
    //     0x8b9f00: ldr             x3, [x3, #0x108]
    // 0x8b9f04: ldur            x1, [fp, #-0x20]
    // 0x8b9f08: r0 = LoadClassIdInstr(r1)
    //     0x8b9f08: ldur            x0, [x1, #-1]
    //     0x8b9f0c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b9f10: r2 = "globalRNG"
    //     0x8b9f10: add             x2, PP, #0xe, lsl #12  ; [pp+0xeff8] "globalRNG"
    //     0x8b9f14: ldr             x2, [x2, #0xff8]
    // 0x8b9f18: r0 = GDT[cid_x0 + -0x10d]()
    //     0x8b9f18: sub             lr, x0, #0x10d
    //     0x8b9f1c: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9f20: blr             lr
    // 0x8b9f24: ldur            x1, [fp, #-8]
    // 0x8b9f28: r0 = _state()
    //     0x8b9f28: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0x8b9f2c: r1 = LoadClassIdInstr(r0)
    //     0x8b9f2c: ldur            x1, [x0, #-1]
    //     0x8b9f30: ubfx            x1, x1, #0xc, #0x14
    // 0x8b9f34: mov             x16, x0
    // 0x8b9f38: mov             x0, x1
    // 0x8b9f3c: mov             x1, x16
    // 0x8b9f40: r2 = "hasInitV4"
    //     0x8b9f40: add             x2, PP, #0xf, lsl #12  ; [pp+0xf080] "hasInitV4"
    //     0x8b9f44: ldr             x2, [x2, #0x80]
    // 0x8b9f48: r3 = true
    //     0x8b9f48: add             x3, NULL, #0x20  ; true
    // 0x8b9f4c: r0 = GDT[cid_x0 + -0x10d]()
    //     0x8b9f4c: sub             lr, x0, #0x10d
    //     0x8b9f50: ldr             lr, [x21, lr, lsl #3]
    //     0x8b9f54: blr             lr
    // 0x8b9f58: r0 = Null
    //     0x8b9f58: mov             x0, NULL
    // 0x8b9f5c: LeaveFrame
    //     0x8b9f5c: mov             SP, fp
    //     0x8b9f60: ldp             fp, lr, [SP], #0x10
    // 0x8b9f64: ret
    //     0x8b9f64: ret             
    // 0x8b9f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b9f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b9f6c: b               #0x8b9d30
    // 0x8b9f70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8b9f70: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic) {
    // ** addr: 0x8ba184, size: 0xec
    // 0x8ba184: EnterFrame
    //     0x8ba184: stp             fp, lr, [SP, #-0x10]!
    //     0x8ba188: mov             fp, SP
    // 0x8ba18c: AllocStack(0x20)
    //     0x8ba18c: sub             SP, SP, #0x20
    // 0x8ba190: SetupParameters()
    //     0x8ba190: ldr             x0, [fp, #0x10]
    //     0x8ba194: ldur            w3, [x0, #0x17]
    //     0x8ba198: add             x3, x3, HEAP, lsl #32
    //     0x8ba19c: stur            x3, [fp, #-0x10]
    // 0x8ba1a0: CheckStackOverflow
    //     0x8ba1a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ba1a4: cmp             SP, x16
    //     0x8ba1a8: b.ls            #0x8ba268
    // 0x8ba1ac: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x8ba1ac: ldur            w4, [x3, #0x17]
    // 0x8ba1b0: DecompressPointer r4
    //     0x8ba1b0: add             x4, x4, HEAP, lsl #32
    // 0x8ba1b4: mov             x0, x4
    // 0x8ba1b8: stur            x4, [fp, #-8]
    // 0x8ba1bc: r2 = Null
    //     0x8ba1bc: mov             x2, NULL
    // 0x8ba1c0: r1 = Null
    //     0x8ba1c0: mov             x1, NULL
    // 0x8ba1c4: r4 = 60
    //     0x8ba1c4: movz            x4, #0x3c
    // 0x8ba1c8: branchIfSmi(r0, 0x8ba1d4)
    //     0x8ba1c8: tbz             w0, #0, #0x8ba1d4
    // 0x8ba1cc: r4 = LoadClassIdInstr(r0)
    //     0x8ba1cc: ldur            x4, [x0, #-1]
    //     0x8ba1d0: ubfx            x4, x4, #0xc, #0x14
    // 0x8ba1d4: cmp             x4, #0x39
    // 0x8ba1d8: b.eq            #0x8ba1f0
    // 0x8ba1dc: r8 = Function
    //     0x8ba1dc: add             x8, PP, #0xd, lsl #12  ; [pp+0xd050] Type: Function
    //     0x8ba1e0: ldr             x8, [x8, #0x50]
    // 0x8ba1e4: r3 = Null
    //     0x8ba1e4: add             x3, PP, #0xf, lsl #12  ; [pp+0xf128] Null
    //     0x8ba1e8: ldr             x3, [x3, #0x128]
    // 0x8ba1ec: r0 = Function()
    //     0x8ba1ec: bl              #0xed64f4  ; IsType_Function_Stub
    // 0x8ba1f0: ldur            x3, [fp, #-0x10]
    // 0x8ba1f4: LoadField: r4 = r3->field_f
    //     0x8ba1f4: ldur            w4, [x3, #0xf]
    // 0x8ba1f8: DecompressPointer r4
    //     0x8ba1f8: add             x4, x4, HEAP, lsl #32
    // 0x8ba1fc: mov             x0, x4
    // 0x8ba200: stur            x4, [fp, #-0x18]
    // 0x8ba204: r2 = Null
    //     0x8ba204: mov             x2, NULL
    // 0x8ba208: r1 = Null
    //     0x8ba208: mov             x1, NULL
    // 0x8ba20c: r4 = 60
    //     0x8ba20c: movz            x4, #0x3c
    // 0x8ba210: branchIfSmi(r0, 0x8ba21c)
    //     0x8ba210: tbz             w0, #0, #0x8ba21c
    // 0x8ba214: r4 = LoadClassIdInstr(r0)
    //     0x8ba214: ldur            x4, [x0, #-1]
    //     0x8ba218: ubfx            x4, x4, #0xc, #0x14
    // 0x8ba21c: sub             x4, x4, #0x5a
    // 0x8ba220: cmp             x4, #2
    // 0x8ba224: b.ls            #0x8ba23c
    // 0x8ba228: r8 = List?
    //     0x8ba228: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0x8ba22c: ldr             x8, [x8, #0x140]
    // 0x8ba230: r3 = Null
    //     0x8ba230: add             x3, PP, #0xf, lsl #12  ; [pp+0xf138] Null
    //     0x8ba234: ldr             x3, [x3, #0x138]
    // 0x8ba238: r0 = List?()
    //     0x8ba238: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0x8ba23c: ldur            x0, [fp, #-0x10]
    // 0x8ba240: LoadField: r1 = r0->field_13
    //     0x8ba240: ldur            w1, [x0, #0x13]
    // 0x8ba244: DecompressPointer r1
    //     0x8ba244: add             x1, x1, HEAP, lsl #32
    // 0x8ba248: str             x1, [SP]
    // 0x8ba24c: ldur            x1, [fp, #-8]
    // 0x8ba250: ldur            x2, [fp, #-0x18]
    // 0x8ba254: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8ba254: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8ba258: r0 = apply()
    //     0x8ba258: bl              #0x889cd8  ; [dart:core] Function::apply
    // 0x8ba25c: LeaveFrame
    //     0x8ba25c: mov             SP, fp
    //     0x8ba260: ldp             fp, lr, [SP], #0x10
    // 0x8ba264: ret
    //     0x8ba264: ret             
    // 0x8ba268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ba268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ba26c: b               #0x8ba1ac
  }
  _ v1(/* No info */) {
    // ** addr: 0xaac748, size: 0xad0
    // 0xaac748: EnterFrame
    //     0xaac748: stp             fp, lr, [SP, #-0x10]!
    //     0xaac74c: mov             fp, SP
    // 0xaac750: AllocStack(0x60)
    //     0xaac750: sub             SP, SP, #0x60
    // 0xaac754: SetupParameters(Uuid this /* r1 => r1, fp-0x8 */)
    //     0xaac754: stur            x1, [fp, #-8]
    // 0xaac758: CheckStackOverflow
    //     0xaac758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaac75c: cmp             SP, x16
    //     0xaac760: b.ls            #0xaad1d0
    // 0xaac764: r16 = <String, dynamic>
    //     0xaac764: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0xaac768: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xaac76c: stp             lr, x16, [SP]
    // 0xaac770: r0 = Map._fromLiteral()
    //     0xaac770: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xaac774: ldur            x1, [fp, #-8]
    // 0xaac778: stur            x0, [fp, #-0x10]
    // 0xaac77c: r0 = _initV1()
    //     0xaac77c: bl              #0xaad218  ; [package:uuid/uuid.dart] Uuid::_initV1
    // 0xaac780: ldur            x3, [fp, #-0x10]
    // 0xaac784: r0 = LoadClassIdInstr(r3)
    //     0xaac784: ldur            x0, [x3, #-1]
    //     0xaac788: ubfx            x0, x0, #0xc, #0x14
    // 0xaac78c: mov             x1, x3
    // 0xaac790: r2 = "clockSeq"
    //     0xaac790: add             x2, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0xaac794: ldr             x2, [x2, #0x60]
    // 0xaac798: r0 = GDT[cid_x0 + -0x114]()
    //     0xaac798: sub             lr, x0, #0x114
    //     0xaac79c: ldr             lr, [x21, lr, lsl #3]
    //     0xaac7a0: blr             lr
    // 0xaac7a4: cmp             w0, NULL
    // 0xaac7a8: b.eq            #0xaac820
    // 0xaac7ac: ldur            x3, [fp, #-0x10]
    // 0xaac7b0: r0 = LoadClassIdInstr(r3)
    //     0xaac7b0: ldur            x0, [x3, #-1]
    //     0xaac7b4: ubfx            x0, x0, #0xc, #0x14
    // 0xaac7b8: mov             x1, x3
    // 0xaac7bc: r2 = "clockSeq"
    //     0xaac7bc: add             x2, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0xaac7c0: ldr             x2, [x2, #0x60]
    // 0xaac7c4: r0 = GDT[cid_x0 + -0x114]()
    //     0xaac7c4: sub             lr, x0, #0x114
    //     0xaac7c8: ldr             lr, [x21, lr, lsl #3]
    //     0xaac7cc: blr             lr
    // 0xaac7d0: mov             x3, x0
    // 0xaac7d4: r2 = Null
    //     0xaac7d4: mov             x2, NULL
    // 0xaac7d8: r1 = Null
    //     0xaac7d8: mov             x1, NULL
    // 0xaac7dc: stur            x3, [fp, #-0x18]
    // 0xaac7e0: branchIfSmi(r0, 0xaac808)
    //     0xaac7e0: tbz             w0, #0, #0xaac808
    // 0xaac7e4: r4 = LoadClassIdInstr(r0)
    //     0xaac7e4: ldur            x4, [x0, #-1]
    //     0xaac7e8: ubfx            x4, x4, #0xc, #0x14
    // 0xaac7ec: sub             x4, x4, #0x3c
    // 0xaac7f0: cmp             x4, #1
    // 0xaac7f4: b.ls            #0xaac808
    // 0xaac7f8: r8 = int
    //     0xaac7f8: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xaac7fc: r3 = Null
    //     0xaac7fc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dfb8] Null
    //     0xaac800: ldr             x3, [x3, #0xfb8]
    // 0xaac804: r0 = int()
    //     0xaac804: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xaac808: ldur            x0, [fp, #-0x18]
    // 0xaac80c: r1 = LoadInt32Instr(r0)
    //     0xaac80c: sbfx            x1, x0, #1, #0x1f
    //     0xaac810: tbz             w0, #0, #0xaac818
    //     0xaac814: ldur            x1, [x0, #7]
    // 0xaac818: mov             x4, x1
    // 0xaac81c: b               #0xaac89c
    // 0xaac820: ldur            x1, [fp, #-8]
    // 0xaac824: r0 = _state()
    //     0xaac824: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaac828: r1 = LoadClassIdInstr(r0)
    //     0xaac828: ldur            x1, [x0, #-1]
    //     0xaac82c: ubfx            x1, x1, #0xc, #0x14
    // 0xaac830: mov             x16, x0
    // 0xaac834: mov             x0, x1
    // 0xaac838: mov             x1, x16
    // 0xaac83c: r2 = "clockSeq"
    //     0xaac83c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0xaac840: ldr             x2, [x2, #0x60]
    // 0xaac844: r0 = GDT[cid_x0 + -0x114]()
    //     0xaac844: sub             lr, x0, #0x114
    //     0xaac848: ldr             lr, [x21, lr, lsl #3]
    //     0xaac84c: blr             lr
    // 0xaac850: mov             x3, x0
    // 0xaac854: r2 = Null
    //     0xaac854: mov             x2, NULL
    // 0xaac858: r1 = Null
    //     0xaac858: mov             x1, NULL
    // 0xaac85c: stur            x3, [fp, #-0x18]
    // 0xaac860: branchIfSmi(r0, 0xaac888)
    //     0xaac860: tbz             w0, #0, #0xaac888
    // 0xaac864: r4 = LoadClassIdInstr(r0)
    //     0xaac864: ldur            x4, [x0, #-1]
    //     0xaac868: ubfx            x4, x4, #0xc, #0x14
    // 0xaac86c: sub             x4, x4, #0x3c
    // 0xaac870: cmp             x4, #1
    // 0xaac874: b.ls            #0xaac888
    // 0xaac878: r8 = int
    //     0xaac878: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xaac87c: r3 = Null
    //     0xaac87c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dfc8] Null
    //     0xaac880: ldr             x3, [x3, #0xfc8]
    // 0xaac884: r0 = int()
    //     0xaac884: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xaac888: ldur            x0, [fp, #-0x18]
    // 0xaac88c: r1 = LoadInt32Instr(r0)
    //     0xaac88c: sbfx            x1, x0, #1, #0x1f
    //     0xaac890: tbz             w0, #0, #0xaac898
    //     0xaac894: ldur            x1, [x0, #7]
    // 0xaac898: mov             x4, x1
    // 0xaac89c: ldur            x3, [fp, #-0x10]
    // 0xaac8a0: stur            x4, [fp, #-0x20]
    // 0xaac8a4: r0 = LoadClassIdInstr(r3)
    //     0xaac8a4: ldur            x0, [x3, #-1]
    //     0xaac8a8: ubfx            x0, x0, #0xc, #0x14
    // 0xaac8ac: mov             x1, x3
    // 0xaac8b0: r2 = "mSecs"
    //     0xaac8b0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf068] "mSecs"
    //     0xaac8b4: ldr             x2, [x2, #0x68]
    // 0xaac8b8: r0 = GDT[cid_x0 + -0x114]()
    //     0xaac8b8: sub             lr, x0, #0x114
    //     0xaac8bc: ldr             lr, [x21, lr, lsl #3]
    //     0xaac8c0: blr             lr
    // 0xaac8c4: cmp             w0, NULL
    // 0xaac8c8: b.eq            #0xaac940
    // 0xaac8cc: ldur            x3, [fp, #-0x10]
    // 0xaac8d0: r0 = LoadClassIdInstr(r3)
    //     0xaac8d0: ldur            x0, [x3, #-1]
    //     0xaac8d4: ubfx            x0, x0, #0xc, #0x14
    // 0xaac8d8: mov             x1, x3
    // 0xaac8dc: r2 = "mSecs"
    //     0xaac8dc: add             x2, PP, #0xf, lsl #12  ; [pp+0xf068] "mSecs"
    //     0xaac8e0: ldr             x2, [x2, #0x68]
    // 0xaac8e4: r0 = GDT[cid_x0 + -0x114]()
    //     0xaac8e4: sub             lr, x0, #0x114
    //     0xaac8e8: ldr             lr, [x21, lr, lsl #3]
    //     0xaac8ec: blr             lr
    // 0xaac8f0: mov             x3, x0
    // 0xaac8f4: r2 = Null
    //     0xaac8f4: mov             x2, NULL
    // 0xaac8f8: r1 = Null
    //     0xaac8f8: mov             x1, NULL
    // 0xaac8fc: stur            x3, [fp, #-0x18]
    // 0xaac900: branchIfSmi(r0, 0xaac928)
    //     0xaac900: tbz             w0, #0, #0xaac928
    // 0xaac904: r4 = LoadClassIdInstr(r0)
    //     0xaac904: ldur            x4, [x0, #-1]
    //     0xaac908: ubfx            x4, x4, #0xc, #0x14
    // 0xaac90c: sub             x4, x4, #0x3c
    // 0xaac910: cmp             x4, #1
    // 0xaac914: b.ls            #0xaac928
    // 0xaac918: r8 = int
    //     0xaac918: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xaac91c: r3 = Null
    //     0xaac91c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dfd8] Null
    //     0xaac920: ldr             x3, [x3, #0xfd8]
    // 0xaac924: r0 = int()
    //     0xaac924: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xaac928: ldur            x0, [fp, #-0x18]
    // 0xaac92c: r1 = LoadInt32Instr(r0)
    //     0xaac92c: sbfx            x1, x0, #1, #0x1f
    //     0xaac930: tbz             w0, #0, #0xaac938
    //     0xaac934: ldur            x1, [x0, #7]
    // 0xaac938: mov             x4, x1
    // 0xaac93c: b               #0xaac970
    // 0xaac940: r0 = _getCurrentMicros()
    //     0xaac940: bl              #0x615ec0  ; [dart:core] DateTime::_getCurrentMicros
    // 0xaac944: r1 = LoadInt32Instr(r0)
    //     0xaac944: sbfx            x1, x0, #1, #0x1f
    //     0xaac948: tbz             w0, #0, #0xaac950
    //     0xaac94c: ldur            x1, [x0, #7]
    // 0xaac950: tbz             x1, #0x3f, #0xaac95c
    // 0xaac954: r2 = 999
    //     0xaac954: movz            x2, #0x3e7
    // 0xaac958: b               #0xaac960
    // 0xaac95c: r2 = 0
    //     0xaac95c: movz            x2, #0
    // 0xaac960: r0 = 1000
    //     0xaac960: movz            x0, #0x3e8
    // 0xaac964: sub             x3, x1, x2
    // 0xaac968: sdiv            x1, x3, x0
    // 0xaac96c: mov             x4, x1
    // 0xaac970: ldur            x3, [fp, #-0x10]
    // 0xaac974: stur            x4, [fp, #-0x28]
    // 0xaac978: r0 = LoadClassIdInstr(r3)
    //     0xaac978: ldur            x0, [x3, #-1]
    //     0xaac97c: ubfx            x0, x0, #0xc, #0x14
    // 0xaac980: mov             x1, x3
    // 0xaac984: r2 = "nSecs"
    //     0xaac984: add             x2, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0xaac988: ldr             x2, [x2, #0x70]
    // 0xaac98c: r0 = GDT[cid_x0 + -0x114]()
    //     0xaac98c: sub             lr, x0, #0x114
    //     0xaac990: ldr             lr, [x21, lr, lsl #3]
    //     0xaac994: blr             lr
    // 0xaac998: cmp             w0, NULL
    // 0xaac99c: b.eq            #0xaaca14
    // 0xaac9a0: ldur            x3, [fp, #-0x10]
    // 0xaac9a4: r0 = LoadClassIdInstr(r3)
    //     0xaac9a4: ldur            x0, [x3, #-1]
    //     0xaac9a8: ubfx            x0, x0, #0xc, #0x14
    // 0xaac9ac: mov             x1, x3
    // 0xaac9b0: r2 = "nSecs"
    //     0xaac9b0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0xaac9b4: ldr             x2, [x2, #0x70]
    // 0xaac9b8: r0 = GDT[cid_x0 + -0x114]()
    //     0xaac9b8: sub             lr, x0, #0x114
    //     0xaac9bc: ldr             lr, [x21, lr, lsl #3]
    //     0xaac9c0: blr             lr
    // 0xaac9c4: mov             x3, x0
    // 0xaac9c8: r2 = Null
    //     0xaac9c8: mov             x2, NULL
    // 0xaac9cc: r1 = Null
    //     0xaac9cc: mov             x1, NULL
    // 0xaac9d0: stur            x3, [fp, #-0x18]
    // 0xaac9d4: branchIfSmi(r0, 0xaac9fc)
    //     0xaac9d4: tbz             w0, #0, #0xaac9fc
    // 0xaac9d8: r4 = LoadClassIdInstr(r0)
    //     0xaac9d8: ldur            x4, [x0, #-1]
    //     0xaac9dc: ubfx            x4, x4, #0xc, #0x14
    // 0xaac9e0: sub             x4, x4, #0x3c
    // 0xaac9e4: cmp             x4, #1
    // 0xaac9e8: b.ls            #0xaac9fc
    // 0xaac9ec: r8 = int
    //     0xaac9ec: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xaac9f0: r3 = Null
    //     0xaac9f0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dfe8] Null
    //     0xaac9f4: ldr             x3, [x3, #0xfe8]
    // 0xaac9f8: r0 = int()
    //     0xaac9f8: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xaac9fc: ldur            x0, [fp, #-0x18]
    // 0xaaca00: r1 = LoadInt32Instr(r0)
    //     0xaaca00: sbfx            x1, x0, #1, #0x1f
    //     0xaaca04: tbz             w0, #0, #0xaaca0c
    //     0xaaca08: ldur            x1, [x0, #7]
    // 0xaaca0c: mov             x2, x1
    // 0xaaca10: b               #0xaacaa0
    // 0xaaca14: ldur            x1, [fp, #-8]
    // 0xaaca18: r0 = _state()
    //     0xaaca18: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaaca1c: r1 = LoadClassIdInstr(r0)
    //     0xaaca1c: ldur            x1, [x0, #-1]
    //     0xaaca20: ubfx            x1, x1, #0xc, #0x14
    // 0xaaca24: mov             x16, x0
    // 0xaaca28: mov             x0, x1
    // 0xaaca2c: mov             x1, x16
    // 0xaaca30: r2 = "nSecs"
    //     0xaaca30: add             x2, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0xaaca34: ldr             x2, [x2, #0x70]
    // 0xaaca38: r0 = GDT[cid_x0 + -0x114]()
    //     0xaaca38: sub             lr, x0, #0x114
    //     0xaaca3c: ldr             lr, [x21, lr, lsl #3]
    //     0xaaca40: blr             lr
    // 0xaaca44: mov             x3, x0
    // 0xaaca48: stur            x3, [fp, #-0x18]
    // 0xaaca4c: cmp             w3, NULL
    // 0xaaca50: b.eq            #0xaad1d8
    // 0xaaca54: r3 as int
    //     0xaaca54: mov             x0, x3
    //     0xaaca58: mov             x2, NULL
    //     0xaaca5c: mov             x1, NULL
    //     0xaaca60: tbz             w0, #0, #0xaaca88
    //     0xaaca64: ldur            x4, [x0, #-1]
    //     0xaaca68: ubfx            x4, x4, #0xc, #0x14
    //     0xaaca6c: sub             x4, x4, #0x3c
    //     0xaaca70: cmp             x4, #1
    //     0xaaca74: b.ls            #0xaaca88
    //     0xaaca78: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    //     0xaaca7c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dff8] Null
    //     0xaaca80: ldr             x3, [x3, #0xff8]
    //     0xaaca84: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xaaca88: ldur            x0, [fp, #-0x18]
    // 0xaaca8c: r1 = LoadInt32Instr(r0)
    //     0xaaca8c: sbfx            x1, x0, #1, #0x1f
    //     0xaaca90: tbz             w0, #0, #0xaaca98
    //     0xaaca94: ldur            x1, [x0, #7]
    // 0xaaca98: add             x0, x1, #1
    // 0xaaca9c: mov             x2, x0
    // 0xaacaa0: ldur            x0, [fp, #-0x28]
    // 0xaacaa4: ldur            x1, [fp, #-8]
    // 0xaacaa8: stur            x2, [fp, #-0x30]
    // 0xaacaac: r0 = _state()
    //     0xaacaac: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaacab0: r1 = LoadClassIdInstr(r0)
    //     0xaacab0: ldur            x1, [x0, #-1]
    //     0xaacab4: ubfx            x1, x1, #0xc, #0x14
    // 0xaacab8: mov             x16, x0
    // 0xaacabc: mov             x0, x1
    // 0xaacac0: mov             x1, x16
    // 0xaacac4: r2 = "mSecs"
    //     0xaacac4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf068] "mSecs"
    //     0xaacac8: ldr             x2, [x2, #0x68]
    // 0xaacacc: r0 = GDT[cid_x0 + -0x114]()
    //     0xaacacc: sub             lr, x0, #0x114
    //     0xaacad0: ldr             lr, [x21, lr, lsl #3]
    //     0xaacad4: blr             lr
    // 0xaacad8: mov             x3, x0
    // 0xaacadc: r2 = Null
    //     0xaacadc: mov             x2, NULL
    // 0xaacae0: r1 = Null
    //     0xaacae0: mov             x1, NULL
    // 0xaacae4: stur            x3, [fp, #-0x18]
    // 0xaacae8: branchIfSmi(r0, 0xaacb10)
    //     0xaacae8: tbz             w0, #0, #0xaacb10
    // 0xaacaec: r4 = LoadClassIdInstr(r0)
    //     0xaacaec: ldur            x4, [x0, #-1]
    //     0xaacaf0: ubfx            x4, x4, #0xc, #0x14
    // 0xaacaf4: sub             x4, x4, #0x3c
    // 0xaacaf8: cmp             x4, #2
    // 0xaacafc: b.ls            #0xaacb10
    // 0xaacb00: r8 = num
    //     0xaacb00: ldr             x8, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xaacb04: r3 = Null
    //     0xaacb04: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e008] Null
    //     0xaacb08: ldr             x3, [x3, #8]
    // 0xaacb0c: r0 = num()
    //     0xaacb0c: bl              #0xed4df4  ; IsType_num_Stub
    // 0xaacb10: ldur            x2, [fp, #-0x28]
    // 0xaacb14: r0 = BoxInt64Instr(r2)
    //     0xaacb14: sbfiz           x0, x2, #1, #0x1f
    //     0xaacb18: cmp             x2, x0, asr #1
    //     0xaacb1c: b.eq            #0xaacb28
    //     0xaacb20: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaacb24: stur            x2, [x0, #7]
    // 0xaacb28: stur            x0, [fp, #-0x38]
    // 0xaacb2c: ldur            x16, [fp, #-0x18]
    // 0xaacb30: stp             x16, x0, [SP]
    // 0xaacb34: r0 = -()
    //     0xaacb34: bl              #0xebf88c  ; [dart:core] _IntegerImplementation::-
    // 0xaacb38: ldur            x1, [fp, #-8]
    // 0xaacb3c: stur            x0, [fp, #-0x18]
    // 0xaacb40: r0 = _state()
    //     0xaacb40: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaacb44: r1 = LoadClassIdInstr(r0)
    //     0xaacb44: ldur            x1, [x0, #-1]
    //     0xaacb48: ubfx            x1, x1, #0xc, #0x14
    // 0xaacb4c: mov             x16, x0
    // 0xaacb50: mov             x0, x1
    // 0xaacb54: mov             x1, x16
    // 0xaacb58: r2 = "nSecs"
    //     0xaacb58: add             x2, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0xaacb5c: ldr             x2, [x2, #0x70]
    // 0xaacb60: r0 = GDT[cid_x0 + -0x114]()
    //     0xaacb60: sub             lr, x0, #0x114
    //     0xaacb64: ldr             lr, [x21, lr, lsl #3]
    //     0xaacb68: blr             lr
    // 0xaacb6c: mov             x3, x0
    // 0xaacb70: r2 = Null
    //     0xaacb70: mov             x2, NULL
    // 0xaacb74: r1 = Null
    //     0xaacb74: mov             x1, NULL
    // 0xaacb78: stur            x3, [fp, #-0x40]
    // 0xaacb7c: branchIfSmi(r0, 0xaacba4)
    //     0xaacb7c: tbz             w0, #0, #0xaacba4
    // 0xaacb80: r4 = LoadClassIdInstr(r0)
    //     0xaacb80: ldur            x4, [x0, #-1]
    //     0xaacb84: ubfx            x4, x4, #0xc, #0x14
    // 0xaacb88: sub             x4, x4, #0x3c
    // 0xaacb8c: cmp             x4, #2
    // 0xaacb90: b.ls            #0xaacba4
    // 0xaacb94: r8 = num
    //     0xaacb94: ldr             x8, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xaacb98: r3 = Null
    //     0xaacb98: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e018] Null
    //     0xaacb9c: ldr             x3, [x3, #0x18]
    // 0xaacba0: r0 = num()
    //     0xaacba0: bl              #0xed4df4  ; IsType_num_Stub
    // 0xaacba4: ldur            x2, [fp, #-0x30]
    // 0xaacba8: r0 = BoxInt64Instr(r2)
    //     0xaacba8: sbfiz           x0, x2, #1, #0x1f
    //     0xaacbac: cmp             x2, x0, asr #1
    //     0xaacbb0: b.eq            #0xaacbbc
    //     0xaacbb4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaacbb8: stur            x2, [x0, #7]
    // 0xaacbbc: ldur            x16, [fp, #-0x40]
    // 0xaacbc0: stp             x16, x0, [SP]
    // 0xaacbc4: r0 = -()
    //     0xaacbc4: bl              #0xebf88c  ; [dart:core] _IntegerImplementation::-
    // 0xaacbc8: r1 = 60
    //     0xaacbc8: movz            x1, #0x3c
    // 0xaacbcc: branchIfSmi(r0, 0xaacbd8)
    //     0xaacbcc: tbz             w0, #0, #0xaacbd8
    // 0xaacbd0: r1 = LoadClassIdInstr(r0)
    //     0xaacbd0: ldur            x1, [x0, #-1]
    //     0xaacbd4: ubfx            x1, x1, #0xc, #0x14
    // 0xaacbd8: r16 = 20000
    //     0xaacbd8: movz            x16, #0x4e20
    // 0xaacbdc: stp             x16, x0, [SP]
    // 0xaacbe0: mov             x0, x1
    // 0xaacbe4: r0 = GDT[cid_x0 + -0xff7]()
    //     0xaacbe4: sub             lr, x0, #0xff7
    //     0xaacbe8: ldr             lr, [x21, lr, lsl #3]
    //     0xaacbec: blr             lr
    // 0xaacbf0: mov             x1, x0
    // 0xaacbf4: ldur            x0, [fp, #-0x18]
    // 0xaacbf8: r2 = 60
    //     0xaacbf8: movz            x2, #0x3c
    // 0xaacbfc: branchIfSmi(r0, 0xaacc08)
    //     0xaacbfc: tbz             w0, #0, #0xaacc08
    // 0xaacc00: r2 = LoadClassIdInstr(r0)
    //     0xaacc00: ldur            x2, [x0, #-1]
    //     0xaacc04: ubfx            x2, x2, #0xc, #0x14
    // 0xaacc08: stp             x1, x0, [SP]
    // 0xaacc0c: mov             x0, x2
    // 0xaacc10: r0 = GDT[cid_x0 + -0xff2]()
    //     0xaacc10: sub             lr, x0, #0xff2
    //     0xaacc14: ldr             lr, [x21, lr, lsl #3]
    //     0xaacc18: blr             lr
    // 0xaacc1c: LoadField: d0 = r0->field_7
    //     0xaacc1c: ldur            d0, [x0, #7]
    // 0xaacc20: stur            d0, [fp, #-0x50]
    // 0xaacc24: d1 = 0.000000
    //     0xaacc24: eor             v1.16b, v1.16b, v1.16b
    // 0xaacc28: fcmp            d1, d0
    // 0xaacc2c: b.le            #0xaacc80
    // 0xaacc30: ldur            x3, [fp, #-0x10]
    // 0xaacc34: r0 = LoadClassIdInstr(r3)
    //     0xaacc34: ldur            x0, [x3, #-1]
    //     0xaacc38: ubfx            x0, x0, #0xc, #0x14
    // 0xaacc3c: mov             x1, x3
    // 0xaacc40: r2 = "clockSeq"
    //     0xaacc40: add             x2, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0xaacc44: ldr             x2, [x2, #0x60]
    // 0xaacc48: r0 = GDT[cid_x0 + -0x114]()
    //     0xaacc48: sub             lr, x0, #0x114
    //     0xaacc4c: ldr             lr, [x21, lr, lsl #3]
    //     0xaacc50: blr             lr
    // 0xaacc54: cmp             w0, NULL
    // 0xaacc58: b.ne            #0xaacc80
    // 0xaacc5c: r1 = 1
    //     0xaacc5c: movz            x1, #0x1
    // 0xaacc60: r0 = 16383
    //     0xaacc60: orr             x0, xzr, #0x3fff
    // 0xaacc64: ldur            x2, [fp, #-0x20]
    // 0xaacc68: ubfx            x2, x2, #0, #0x20
    // 0xaacc6c: add             w3, w2, w1
    // 0xaacc70: and             x1, x3, x0
    // 0xaacc74: ubfx            x1, x1, #0, #0x20
    // 0xaacc78: mov             x0, x1
    // 0xaacc7c: b               #0xaacc84
    // 0xaacc80: ldur            x0, [fp, #-0x20]
    // 0xaacc84: ldur            d0, [fp, #-0x50]
    // 0xaacc88: d1 = 0.000000
    //     0xaacc88: eor             v1.16b, v1.16b, v1.16b
    // 0xaacc8c: stur            x0, [fp, #-0x20]
    // 0xaacc90: fcmp            d1, d0
    // 0xaacc94: b.gt            #0xaacd14
    // 0xaacc98: ldur            x1, [fp, #-8]
    // 0xaacc9c: r0 = _state()
    //     0xaacc9c: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaacca0: r1 = LoadClassIdInstr(r0)
    //     0xaacca0: ldur            x1, [x0, #-1]
    //     0xaacca4: ubfx            x1, x1, #0xc, #0x14
    // 0xaacca8: mov             x16, x0
    // 0xaaccac: mov             x0, x1
    // 0xaaccb0: mov             x1, x16
    // 0xaaccb4: r2 = "mSecs"
    //     0xaaccb4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf068] "mSecs"
    //     0xaaccb8: ldr             x2, [x2, #0x68]
    // 0xaaccbc: r0 = GDT[cid_x0 + -0x114]()
    //     0xaaccbc: sub             lr, x0, #0x114
    //     0xaaccc0: ldr             lr, [x21, lr, lsl #3]
    //     0xaaccc4: blr             lr
    // 0xaaccc8: mov             x3, x0
    // 0xaacccc: r2 = Null
    //     0xaacccc: mov             x2, NULL
    // 0xaaccd0: r1 = Null
    //     0xaaccd0: mov             x1, NULL
    // 0xaaccd4: stur            x3, [fp, #-0x18]
    // 0xaaccd8: branchIfSmi(r0, 0xaacd00)
    //     0xaaccd8: tbz             w0, #0, #0xaacd00
    // 0xaaccdc: r4 = LoadClassIdInstr(r0)
    //     0xaaccdc: ldur            x4, [x0, #-1]
    //     0xaacce0: ubfx            x4, x4, #0xc, #0x14
    // 0xaacce4: sub             x4, x4, #0x3c
    // 0xaacce8: cmp             x4, #2
    // 0xaaccec: b.ls            #0xaacd00
    // 0xaaccf0: r8 = num
    //     0xaaccf0: ldr             x8, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0xaaccf4: r3 = Null
    //     0xaaccf4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e028] Null
    //     0xaaccf8: ldr             x3, [x3, #0x28]
    // 0xaaccfc: r0 = num()
    //     0xaaccfc: bl              #0xed4df4  ; IsType_num_Stub
    // 0xaacd00: ldur            x16, [fp, #-0x38]
    // 0xaacd04: ldur            lr, [fp, #-0x18]
    // 0xaacd08: stp             lr, x16, [SP]
    // 0xaacd0c: r0 = >()
    //     0xaacd0c: bl              #0xebf2f8  ; [dart:core] _IntegerImplementation::>
    // 0xaacd10: tbnz            w0, #4, #0xaacd48
    // 0xaacd14: ldur            x3, [fp, #-0x10]
    // 0xaacd18: r0 = LoadClassIdInstr(r3)
    //     0xaacd18: ldur            x0, [x3, #-1]
    //     0xaacd1c: ubfx            x0, x0, #0xc, #0x14
    // 0xaacd20: mov             x1, x3
    // 0xaacd24: r2 = "nSecs"
    //     0xaacd24: add             x2, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0xaacd28: ldr             x2, [x2, #0x70]
    // 0xaacd2c: r0 = GDT[cid_x0 + -0x114]()
    //     0xaacd2c: sub             lr, x0, #0x114
    //     0xaacd30: ldr             lr, [x21, lr, lsl #3]
    //     0xaacd34: blr             lr
    // 0xaacd38: cmp             w0, NULL
    // 0xaacd3c: b.ne            #0xaacd48
    // 0xaacd40: r0 = 0
    //     0xaacd40: movz            x0, #0
    // 0xaacd44: b               #0xaacd4c
    // 0xaacd48: ldur            x0, [fp, #-0x30]
    // 0xaacd4c: stur            x0, [fp, #-0x30]
    // 0xaacd50: r17 = 10000
    //     0xaacd50: movz            x17, #0x2710
    // 0xaacd54: cmp             x0, x17
    // 0xaacd58: b.ge            #0xaad1b0
    // 0xaacd5c: ldur            x2, [fp, #-0x10]
    // 0xaacd60: ldur            x4, [fp, #-0x28]
    // 0xaacd64: ldur            x3, [fp, #-0x20]
    // 0xaacd68: ldur            x1, [fp, #-8]
    // 0xaacd6c: r0 = _state()
    //     0xaacd6c: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaacd70: r1 = LoadClassIdInstr(r0)
    //     0xaacd70: ldur            x1, [x0, #-1]
    //     0xaacd74: ubfx            x1, x1, #0xc, #0x14
    // 0xaacd78: mov             x16, x0
    // 0xaacd7c: mov             x0, x1
    // 0xaacd80: mov             x1, x16
    // 0xaacd84: ldur            x3, [fp, #-0x38]
    // 0xaacd88: r2 = "mSecs"
    //     0xaacd88: add             x2, PP, #0xf, lsl #12  ; [pp+0xf068] "mSecs"
    //     0xaacd8c: ldr             x2, [x2, #0x68]
    // 0xaacd90: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaacd90: sub             lr, x0, #0x10d
    //     0xaacd94: ldr             lr, [x21, lr, lsl #3]
    //     0xaacd98: blr             lr
    // 0xaacd9c: ldur            x1, [fp, #-8]
    // 0xaacda0: r0 = _state()
    //     0xaacda0: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaacda4: mov             x2, x0
    // 0xaacda8: ldur            x4, [fp, #-0x30]
    // 0xaacdac: r0 = BoxInt64Instr(r4)
    //     0xaacdac: sbfiz           x0, x4, #1, #0x1f
    //     0xaacdb0: cmp             x4, x0, asr #1
    //     0xaacdb4: b.eq            #0xaacdc0
    //     0xaacdb8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaacdbc: stur            x4, [x0, #7]
    // 0xaacdc0: r1 = LoadClassIdInstr(r2)
    //     0xaacdc0: ldur            x1, [x2, #-1]
    //     0xaacdc4: ubfx            x1, x1, #0xc, #0x14
    // 0xaacdc8: mov             x3, x0
    // 0xaacdcc: mov             x0, x1
    // 0xaacdd0: mov             x1, x2
    // 0xaacdd4: r2 = "nSecs"
    //     0xaacdd4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0xaacdd8: ldr             x2, [x2, #0x70]
    // 0xaacddc: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaacddc: sub             lr, x0, #0x10d
    //     0xaacde0: ldr             lr, [x21, lr, lsl #3]
    //     0xaacde4: blr             lr
    // 0xaacde8: ldur            x1, [fp, #-8]
    // 0xaacdec: r0 = _state()
    //     0xaacdec: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaacdf0: mov             x2, x0
    // 0xaacdf4: ldur            x4, [fp, #-0x20]
    // 0xaacdf8: r0 = BoxInt64Instr(r4)
    //     0xaacdf8: sbfiz           x0, x4, #1, #0x1f
    //     0xaacdfc: cmp             x4, x0, asr #1
    //     0xaace00: b.eq            #0xaace0c
    //     0xaace04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaace08: stur            x4, [x0, #7]
    // 0xaace0c: r1 = LoadClassIdInstr(r2)
    //     0xaace0c: ldur            x1, [x2, #-1]
    //     0xaace10: ubfx            x1, x1, #0xc, #0x14
    // 0xaace14: mov             x3, x0
    // 0xaace18: mov             x0, x1
    // 0xaace1c: mov             x1, x2
    // 0xaace20: r2 = "clockSeq"
    //     0xaace20: add             x2, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0xaace24: ldr             x2, [x2, #0x60]
    // 0xaace28: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaace28: sub             lr, x0, #0x10d
    //     0xaace2c: ldr             lr, [x21, lr, lsl #3]
    //     0xaace30: blr             lr
    // 0xaace34: ldur            x0, [fp, #-0x28]
    // 0xaace38: r17 = 12219292800000
    //     0xaace38: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e038] IMM: 0xb1d069b5400
    //     0xaace3c: ldr             x17, [x17, #0x38]
    // 0xaace40: add             x1, x0, x17
    // 0xaace44: stur            x1, [fp, #-0x48]
    // 0xaace48: mov             x0, x1
    // 0xaace4c: ubfx            x0, x0, #0, #0x20
    // 0xaace50: r2 = 268435455
    //     0xaace50: orr             x2, xzr, #0xfffffff
    // 0xaace54: and             x3, x0, x2
    // 0xaace58: ubfx            x3, x3, #0, #0x20
    // 0xaace5c: r16 = 10000
    //     0xaace5c: movz            x16, #0x2710
    // 0xaace60: mul             x0, x3, x16
    // 0xaace64: ldur            x3, [fp, #-0x30]
    // 0xaace68: add             x4, x0, x3
    // 0xaace6c: r0 = 4294967296
    //     0xaace6c: orr             x0, xzr, #0x100000000
    // 0xaace70: sdiv            x5, x4, x0
    // 0xaace74: msub            x3, x5, x0, x4
    // 0xaace78: cmp             x3, xzr
    // 0xaace7c: b.lt            #0xaad1dc
    // 0xaace80: stur            x3, [fp, #-0x30]
    // 0xaace84: mov             x0, x3
    // 0xaace88: ubfx            x0, x0, #0, #0x20
    // 0xaace8c: lsr             w4, w0, #0x18
    // 0xaace90: r0 = 255
    //     0xaace90: movz            x0, #0xff
    // 0xaace94: and             x5, x4, x0
    // 0xaace98: stur            x5, [fp, #-0x28]
    // 0xaace9c: r4 = 32
    //     0xaace9c: movz            x4, #0x20
    // 0xaacea0: r0 = AllocateUint8Array()
    //     0xaacea0: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0xaacea4: mov             x3, x0
    // 0xaacea8: ldur            x0, [fp, #-0x28]
    // 0xaaceac: stur            x3, [fp, #-0x18]
    // 0xaaceb0: ubfx            x0, x0, #0, #0x20
    // 0xaaceb4: ArrayStore: r3[0] = r0  ; TypeUnknown_1
    //     0xaaceb4: strb            w0, [x3, #0x17]
    // 0xaaceb8: ldur            x0, [fp, #-0x30]
    // 0xaacebc: ubfx            x0, x0, #0, #0x20
    // 0xaacec0: lsr             w1, w0, #0x10
    // 0xaacec4: r0 = 255
    //     0xaacec4: movz            x0, #0xff
    // 0xaacec8: and             x2, x1, x0
    // 0xaacecc: ubfx            x2, x2, #0, #0x20
    // 0xaaced0: ArrayStore: r3[1] = r2  ; TypeUnknown_1
    //     0xaaced0: strb            w2, [x3, #0x18]
    // 0xaaced4: ldur            x1, [fp, #-0x30]
    // 0xaaced8: ubfx            x1, x1, #0, #0x20
    // 0xaacedc: lsr             w2, w1, #8
    // 0xaacee0: and             x1, x2, x0
    // 0xaacee4: ubfx            x1, x1, #0, #0x20
    // 0xaacee8: ArrayStore: r3[2] = r1  ; TypeUnknown_1
    //     0xaacee8: strb            w1, [x3, #0x19]
    // 0xaaceec: ldur            x1, [fp, #-0x30]
    // 0xaacef0: ubfx            x1, x1, #0, #0x20
    // 0xaacef4: and             x2, x1, x0
    // 0xaacef8: ubfx            x2, x2, #0, #0x20
    // 0xaacefc: ArrayStore: r3[3] = r2  ; TypeUnknown_1
    //     0xaacefc: strb            w2, [x3, #0x1a]
    // 0xaacf00: ldur            x1, [fp, #-0x48]
    // 0xaacf04: scvtf           d0, x1
    // 0xaacf08: d1 = 4294967296.000000
    //     0xaacf08: add             x17, PP, #0x2e, lsl #12  ; [pp+0x2e040] IMM: double(4294967296) from 0x41f0000000000000
    //     0xaacf0c: ldr             d1, [x17, #0x40]
    // 0xaacf10: fdiv            d2, d0, d1
    // 0xaacf14: d0 = 10000.000000
    //     0xaacf14: add             x17, PP, #0x27, lsl #12  ; [pp+0x27240] IMM: double(10000) from 0x40c3880000000000
    //     0xaacf18: ldr             d0, [x17, #0x240]
    // 0xaacf1c: fmul            d1, d2, d0
    // 0xaacf20: fcmp            d1, d1
    // 0xaacf24: b.vs            #0xaad1e4
    // 0xaacf28: fcvtms          x1, d1
    // 0xaacf2c: asr             x16, x1, #0x1e
    // 0xaacf30: cmp             x16, x1, asr #63
    // 0xaacf34: b.ne            #0xaad1e4
    // 0xaacf38: lsl             x1, x1, #1
    // 0xaacf3c: r2 = LoadInt32Instr(r1)
    //     0xaacf3c: sbfx            x2, x1, #1, #0x1f
    //     0xaacf40: tbz             w1, #0, #0xaacf48
    //     0xaacf44: ldur            x2, [x1, #7]
    // 0xaacf48: r1 = 268435455
    //     0xaacf48: orr             x1, xzr, #0xfffffff
    // 0xaacf4c: and             x4, x2, x1
    // 0xaacf50: lsr             w1, w4, #8
    // 0xaacf54: and             x2, x1, x0
    // 0xaacf58: ubfx            x2, x2, #0, #0x20
    // 0xaacf5c: ArrayStore: r3[4] = r2  ; TypeUnknown_1
    //     0xaacf5c: strb            w2, [x3, #0x1b]
    // 0xaacf60: and             x1, x4, x0
    // 0xaacf64: ubfx            x1, x1, #0, #0x20
    // 0xaacf68: ArrayStore: r3[5] = r1  ; TypeUnknown_1
    //     0xaacf68: strb            w1, [x3, #0x1c]
    // 0xaacf6c: lsr             w1, w4, #0x18
    // 0xaacf70: r2 = 15
    //     0xaacf70: movz            x2, #0xf
    // 0xaacf74: and             x5, x1, x2
    // 0xaacf78: ubfx            x5, x5, #0, #0x20
    // 0xaacf7c: orr             x1, x5, #0x10
    // 0xaacf80: ArrayStore: r3[6] = r1  ; TypeUnknown_1
    //     0xaacf80: strb            w1, [x3, #0x1d]
    // 0xaacf84: lsr             w1, w4, #0x10
    // 0xaacf88: and             x2, x1, x0
    // 0xaacf8c: ubfx            x2, x2, #0, #0x20
    // 0xaacf90: ArrayStore: r3[7] = r2  ; TypeUnknown_1
    //     0xaacf90: strb            w2, [x3, #0x1e]
    // 0xaacf94: ldur            x1, [fp, #-0x20]
    // 0xaacf98: ubfx            x1, x1, #0, #0x20
    // 0xaacf9c: r2 = 16128
    //     0xaacf9c: orr             x2, xzr, #0x3f00
    // 0xaacfa0: and             x4, x1, x2
    // 0xaacfa4: ubfx            x4, x4, #0, #0x20
    // 0xaacfa8: asr             x1, x4, #8
    // 0xaacfac: orr             x2, x1, #0x80
    // 0xaacfb0: ArrayStore: r3[8] = r2  ; TypeUnknown_1
    //     0xaacfb0: strb            w2, [x3, #0x1f]
    // 0xaacfb4: ldur            x1, [fp, #-0x20]
    // 0xaacfb8: ubfx            x1, x1, #0, #0x20
    // 0xaacfbc: and             x2, x1, x0
    // 0xaacfc0: ubfx            x2, x2, #0, #0x20
    // 0xaacfc4: ArrayStore: r3[9] = r2  ; TypeUnknown_1
    //     0xaacfc4: strb            w2, [x3, #0x20]
    // 0xaacfc8: ldur            x4, [fp, #-0x10]
    // 0xaacfcc: r0 = LoadClassIdInstr(r4)
    //     0xaacfcc: ldur            x0, [x4, #-1]
    //     0xaacfd0: ubfx            x0, x0, #0xc, #0x14
    // 0xaacfd4: mov             x1, x4
    // 0xaacfd8: r2 = "node"
    //     0xaacfd8: add             x2, PP, #0xf, lsl #12  ; [pp+0xf058] "node"
    //     0xaacfdc: ldr             x2, [x2, #0x58]
    // 0xaacfe0: r0 = GDT[cid_x0 + -0x114]()
    //     0xaacfe0: sub             lr, x0, #0x114
    //     0xaacfe4: ldr             lr, [x21, lr, lsl #3]
    //     0xaacfe8: blr             lr
    // 0xaacfec: cmp             w0, NULL
    // 0xaacff0: b.eq            #0xaad058
    // 0xaacff4: ldur            x1, [fp, #-0x10]
    // 0xaacff8: r0 = LoadClassIdInstr(r1)
    //     0xaacff8: ldur            x0, [x1, #-1]
    //     0xaacffc: ubfx            x0, x0, #0xc, #0x14
    // 0xaad000: r2 = "node"
    //     0xaad000: add             x2, PP, #0xf, lsl #12  ; [pp+0xf058] "node"
    //     0xaad004: ldr             x2, [x2, #0x58]
    // 0xaad008: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad008: sub             lr, x0, #0x114
    //     0xaad00c: ldr             lr, [x21, lr, lsl #3]
    //     0xaad010: blr             lr
    // 0xaad014: mov             x3, x0
    // 0xaad018: r2 = Null
    //     0xaad018: mov             x2, NULL
    // 0xaad01c: r1 = Null
    //     0xaad01c: mov             x1, NULL
    // 0xaad020: stur            x3, [fp, #-0x10]
    // 0xaad024: r4 = 60
    //     0xaad024: movz            x4, #0x3c
    // 0xaad028: branchIfSmi(r0, 0xaad034)
    //     0xaad028: tbz             w0, #0, #0xaad034
    // 0xaad02c: r4 = LoadClassIdInstr(r0)
    //     0xaad02c: ldur            x4, [x0, #-1]
    //     0xaad030: ubfx            x4, x4, #0xc, #0x14
    // 0xaad034: sub             x4, x4, #0x5a
    // 0xaad038: cmp             x4, #2
    // 0xaad03c: b.ls            #0xaad050
    // 0xaad040: r8 = List
    //     0xaad040: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xaad044: r3 = Null
    //     0xaad044: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e048] Null
    //     0xaad048: ldr             x3, [x3, #0x48]
    // 0xaad04c: r0 = List()
    //     0xaad04c: bl              #0xed6b40  ; IsType_List_Stub
    // 0xaad050: ldur            x2, [fp, #-0x10]
    // 0xaad054: b               #0xaad0c8
    // 0xaad058: ldur            x1, [fp, #-8]
    // 0xaad05c: r0 = _state()
    //     0xaad05c: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad060: r1 = LoadClassIdInstr(r0)
    //     0xaad060: ldur            x1, [x0, #-1]
    //     0xaad064: ubfx            x1, x1, #0xc, #0x14
    // 0xaad068: mov             x16, x0
    // 0xaad06c: mov             x0, x1
    // 0xaad070: mov             x1, x16
    // 0xaad074: r2 = "node"
    //     0xaad074: add             x2, PP, #0xf, lsl #12  ; [pp+0xf058] "node"
    //     0xaad078: ldr             x2, [x2, #0x58]
    // 0xaad07c: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad07c: sub             lr, x0, #0x114
    //     0xaad080: ldr             lr, [x21, lr, lsl #3]
    //     0xaad084: blr             lr
    // 0xaad088: mov             x3, x0
    // 0xaad08c: r2 = Null
    //     0xaad08c: mov             x2, NULL
    // 0xaad090: r1 = Null
    //     0xaad090: mov             x1, NULL
    // 0xaad094: stur            x3, [fp, #-8]
    // 0xaad098: r4 = 60
    //     0xaad098: movz            x4, #0x3c
    // 0xaad09c: branchIfSmi(r0, 0xaad0a8)
    //     0xaad09c: tbz             w0, #0, #0xaad0a8
    // 0xaad0a0: r4 = LoadClassIdInstr(r0)
    //     0xaad0a0: ldur            x4, [x0, #-1]
    //     0xaad0a4: ubfx            x4, x4, #0xc, #0x14
    // 0xaad0a8: sub             x4, x4, #0x5a
    // 0xaad0ac: cmp             x4, #2
    // 0xaad0b0: b.ls            #0xaad0c4
    // 0xaad0b4: r8 = List
    //     0xaad0b4: ldr             x8, [PP, #0x2ee0]  ; [pp+0x2ee0] Type: List
    // 0xaad0b8: r3 = Null
    //     0xaad0b8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e058] Null
    //     0xaad0bc: ldr             x3, [x3, #0x58]
    // 0xaad0c0: r0 = List()
    //     0xaad0c0: bl              #0xed6b40  ; IsType_List_Stub
    // 0xaad0c4: ldur            x2, [fp, #-8]
    // 0xaad0c8: stur            x2, [fp, #-8]
    // 0xaad0cc: ldur            x3, [fp, #-0x18]
    // 0xaad0d0: r4 = 0
    //     0xaad0d0: movz            x4, #0
    // 0xaad0d4: stur            x4, [fp, #-0x28]
    // 0xaad0d8: CheckStackOverflow
    //     0xaad0d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaad0dc: cmp             SP, x16
    //     0xaad0e0: b.ls            #0xaad210
    // 0xaad0e4: cmp             x4, #6
    // 0xaad0e8: b.ge            #0xaad198
    // 0xaad0ec: add             x5, x4, #0xa
    // 0xaad0f0: stur            x5, [fp, #-0x20]
    // 0xaad0f4: r0 = BoxInt64Instr(r4)
    //     0xaad0f4: sbfiz           x0, x4, #1, #0x1f
    //     0xaad0f8: cmp             x4, x0, asr #1
    //     0xaad0fc: b.eq            #0xaad108
    //     0xaad100: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaad104: stur            x4, [x0, #7]
    // 0xaad108: r1 = LoadClassIdInstr(r2)
    //     0xaad108: ldur            x1, [x2, #-1]
    //     0xaad10c: ubfx            x1, x1, #0xc, #0x14
    // 0xaad110: stp             x0, x2, [SP]
    // 0xaad114: mov             x0, x1
    // 0xaad118: r0 = GDT[cid_x0 + 0x13037]()
    //     0xaad118: movz            x17, #0x3037
    //     0xaad11c: movk            x17, #0x1, lsl #16
    //     0xaad120: add             lr, x0, x17
    //     0xaad124: ldr             lr, [x21, lr, lsl #3]
    //     0xaad128: blr             lr
    // 0xaad12c: mov             x3, x0
    // 0xaad130: r2 = Null
    //     0xaad130: mov             x2, NULL
    // 0xaad134: r1 = Null
    //     0xaad134: mov             x1, NULL
    // 0xaad138: stur            x3, [fp, #-0x10]
    // 0xaad13c: branchIfSmi(r0, 0xaad164)
    //     0xaad13c: tbz             w0, #0, #0xaad164
    // 0xaad140: r4 = LoadClassIdInstr(r0)
    //     0xaad140: ldur            x4, [x0, #-1]
    //     0xaad144: ubfx            x4, x4, #0xc, #0x14
    // 0xaad148: sub             x4, x4, #0x3c
    // 0xaad14c: cmp             x4, #1
    // 0xaad150: b.ls            #0xaad164
    // 0xaad154: r8 = int
    //     0xaad154: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xaad158: r3 = Null
    //     0xaad158: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e068] Null
    //     0xaad15c: ldr             x3, [x3, #0x68]
    // 0xaad160: r0 = int()
    //     0xaad160: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xaad164: ldur            x0, [fp, #-0x10]
    // 0xaad168: r1 = LoadInt32Instr(r0)
    //     0xaad168: sbfx            x1, x0, #1, #0x1f
    //     0xaad16c: tbz             w0, #0, #0xaad174
    //     0xaad170: ldur            x1, [x0, #7]
    // 0xaad174: ldur            x2, [fp, #-0x20]
    // 0xaad178: ldur            x0, [fp, #-0x18]
    // 0xaad17c: ArrayStore: r0[r2] = r1  ; TypeUnknown_1
    //     0xaad17c: add             x3, x0, x2
    //     0xaad180: strb            w1, [x3, #0x17]
    // 0xaad184: ldur            x1, [fp, #-0x28]
    // 0xaad188: add             x4, x1, #1
    // 0xaad18c: ldur            x2, [fp, #-8]
    // 0xaad190: mov             x3, x0
    // 0xaad194: b               #0xaad0d4
    // 0xaad198: mov             x0, x3
    // 0xaad19c: mov             x1, x0
    // 0xaad1a0: r0 = unparse()
    //     0xaad1a0: bl              #0x8b907c  ; [package:uuid/uuid.dart] Uuid::unparse
    // 0xaad1a4: LeaveFrame
    //     0xaad1a4: mov             SP, fp
    //     0xaad1a8: ldp             fp, lr, [SP], #0x10
    // 0xaad1ac: ret
    //     0xaad1ac: ret             
    // 0xaad1b0: r0 = _Exception()
    //     0xaad1b0: bl              #0x61bcf4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0xaad1b4: mov             x1, x0
    // 0xaad1b8: r0 = "uuid.v1(): Can\'t create more than 10M uuids/sec"
    //     0xaad1b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e078] "uuid.v1(): Can\'t create more than 10M uuids/sec"
    //     0xaad1bc: ldr             x0, [x0, #0x78]
    // 0xaad1c0: StoreField: r1->field_7 = r0
    //     0xaad1c0: stur            w0, [x1, #7]
    // 0xaad1c4: mov             x0, x1
    // 0xaad1c8: r0 = Throw()
    //     0xaad1c8: bl              #0xec04b8  ; ThrowStub
    // 0xaad1cc: brk             #0
    // 0xaad1d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaad1d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaad1d4: b               #0xaac764
    // 0xaad1d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaad1d8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaad1dc: add             x3, x3, x0
    // 0xaad1e0: b               #0xaace80
    // 0xaad1e4: SaveReg d1
    //     0xaad1e4: str             q1, [SP, #-0x10]!
    // 0xaad1e8: stp             x0, x3, [SP, #-0x10]!
    // 0xaad1ec: d0 = 0.000000
    //     0xaad1ec: fmov            d0, d1
    // 0xaad1f0: r0 = 68
    //     0xaad1f0: movz            x0, #0x44
    // 0xaad1f4: r30 = DoubleToIntegerStub
    //     0xaad1f4: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0xaad1f8: LoadField: r30 = r30->field_7
    //     0xaad1f8: ldur            lr, [lr, #7]
    // 0xaad1fc: blr             lr
    // 0xaad200: mov             x1, x0
    // 0xaad204: ldp             x0, x3, [SP], #0x10
    // 0xaad208: RestoreReg d1
    //     0xaad208: ldr             q1, [SP], #0x10
    // 0xaad20c: b               #0xaacf3c
    // 0xaad210: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaad210: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaad214: b               #0xaad0e4
  }
  _ _initV1(/* No info */) {
    // ** addr: 0xaad218, size: 0x664
    // 0xaad218: EnterFrame
    //     0xaad218: stp             fp, lr, [SP, #-0x10]!
    //     0xaad21c: mov             fp, SP
    // 0xaad220: AllocStack(0x58)
    //     0xaad220: sub             SP, SP, #0x58
    // 0xaad224: SetupParameters(Uuid this /* r1 => r0, fp-0x8 */)
    //     0xaad224: mov             x0, x1
    //     0xaad228: stur            x1, [fp, #-8]
    // 0xaad22c: CheckStackOverflow
    //     0xaad22c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaad230: cmp             SP, x16
    //     0xaad234: b.ls            #0xaad850
    // 0xaad238: mov             x1, x0
    // 0xaad23c: r0 = _state()
    //     0xaad23c: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad240: r1 = LoadClassIdInstr(r0)
    //     0xaad240: ldur            x1, [x0, #-1]
    //     0xaad244: ubfx            x1, x1, #0xc, #0x14
    // 0xaad248: mov             x16, x0
    // 0xaad24c: mov             x0, x1
    // 0xaad250: mov             x1, x16
    // 0xaad254: r2 = "hasInitV1"
    //     0xaad254: add             x2, PP, #0xf, lsl #12  ; [pp+0xf078] "hasInitV1"
    //     0xaad258: ldr             x2, [x2, #0x78]
    // 0xaad25c: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad25c: sub             lr, x0, #0x114
    //     0xaad260: ldr             lr, [x21, lr, lsl #3]
    //     0xaad264: blr             lr
    // 0xaad268: mov             x3, x0
    // 0xaad26c: stur            x3, [fp, #-0x10]
    // 0xaad270: cmp             w3, NULL
    // 0xaad274: b.eq            #0xaad858
    // 0xaad278: mov             x0, x3
    // 0xaad27c: r2 = Null
    //     0xaad27c: mov             x2, NULL
    // 0xaad280: r1 = Null
    //     0xaad280: mov             x1, NULL
    // 0xaad284: r4 = 60
    //     0xaad284: movz            x4, #0x3c
    // 0xaad288: branchIfSmi(r0, 0xaad294)
    //     0xaad288: tbz             w0, #0, #0xaad294
    // 0xaad28c: r4 = LoadClassIdInstr(r0)
    //     0xaad28c: ldur            x4, [x0, #-1]
    //     0xaad290: ubfx            x4, x4, #0xc, #0x14
    // 0xaad294: cmp             x4, #0x3f
    // 0xaad298: b.eq            #0xaad2ac
    // 0xaad29c: r8 = bool
    //     0xaad29c: ldr             x8, [PP, #0x2d60]  ; [pp+0x2d60] Type: bool
    // 0xaad2a0: r3 = Null
    //     0xaad2a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Null
    //     0xaad2a4: ldr             x3, [x3, #0x80]
    // 0xaad2a8: r0 = bool()
    //     0xaad2a8: bl              #0xed4390  ; IsType_bool_Stub
    // 0xaad2ac: ldur            x0, [fp, #-0x10]
    // 0xaad2b0: tbz             w0, #4, #0xaad840
    // 0xaad2b4: r1 = _ConstMap len:0
    //     0xaad2b4: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0xaad2b8: ldr             x1, [x1, #0xc8]
    // 0xaad2bc: r2 = "v1rngPositionalArgs"
    //     0xaad2bc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e090] "v1rngPositionalArgs"
    //     0xaad2c0: ldr             x2, [x2, #0x90]
    // 0xaad2c4: r0 = []()
    //     0xaad2c4: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaad2c8: cmp             w0, NULL
    // 0xaad2cc: b.eq            #0xaad2e8
    // 0xaad2d0: r1 = _ConstMap len:0
    //     0xaad2d0: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0xaad2d4: ldr             x1, [x1, #0xc8]
    // 0xaad2d8: r2 = "v1rngPositionalArgs"
    //     0xaad2d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e090] "v1rngPositionalArgs"
    //     0xaad2dc: ldr             x2, [x2, #0x90]
    // 0xaad2e0: r0 = []()
    //     0xaad2e0: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaad2e4: b               #0xaad2f4
    // 0xaad2e8: r1 = Null
    //     0xaad2e8: mov             x1, NULL
    // 0xaad2ec: r2 = 0
    //     0xaad2ec: movz            x2, #0
    // 0xaad2f0: r0 = _GrowableList()
    //     0xaad2f0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xaad2f4: stur            x0, [fp, #-0x10]
    // 0xaad2f8: r1 = _ConstMap len:0
    //     0xaad2f8: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0xaad2fc: ldr             x1, [x1, #0xc8]
    // 0xaad300: r2 = "v1rngNamedArgs"
    //     0xaad300: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e098] "v1rngNamedArgs"
    //     0xaad304: ldr             x2, [x2, #0x98]
    // 0xaad308: r0 = []()
    //     0xaad308: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaad30c: cmp             w0, NULL
    // 0xaad310: b.eq            #0xaad354
    // 0xaad314: r1 = _ConstMap len:0
    //     0xaad314: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0xaad318: ldr             x1, [x1, #0xc8]
    // 0xaad31c: r2 = "v1rngNamedArgs"
    //     0xaad31c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e098] "v1rngNamedArgs"
    //     0xaad320: ldr             x2, [x2, #0x98]
    // 0xaad324: r0 = []()
    //     0xaad324: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaad328: mov             x3, x0
    // 0xaad32c: r2 = Null
    //     0xaad32c: mov             x2, NULL
    // 0xaad330: r1 = Null
    //     0xaad330: mov             x1, NULL
    // 0xaad334: stur            x3, [fp, #-0x18]
    // 0xaad338: r8 = Map<Symbol, dynamic>
    //     0xaad338: add             x8, PP, #0xe, lsl #12  ; [pp+0xefa0] Type: Map<Symbol, dynamic>
    //     0xaad33c: ldr             x8, [x8, #0xfa0]
    // 0xaad340: r3 = Null
    //     0xaad340: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e0a0] Null
    //     0xaad344: ldr             x3, [x3, #0xa0]
    // 0xaad348: r0 = Map<Symbol, dynamic>()
    //     0xaad348: bl              #0x8ba270  ; IsType_Map<Symbol, dynamic>_Stub
    // 0xaad34c: ldur            x0, [fp, #-0x18]
    // 0xaad350: b               #0xaad35c
    // 0xaad354: r0 = _ConstMap len:0
    //     0xaad354: add             x0, PP, #0xe, lsl #12  ; [pp+0xefb8] Map<Symbol, dynamic>(0)
    //     0xaad358: ldr             x0, [x0, #0xfb8]
    // 0xaad35c: stur            x0, [fp, #-0x18]
    // 0xaad360: r1 = _ConstMap len:0
    //     0xaad360: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0xaad364: ldr             x1, [x1, #0xc8]
    // 0xaad368: r2 = "v1rng"
    //     0xaad368: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e0b0] "v1rng"
    //     0xaad36c: ldr             x2, [x2, #0xb0]
    // 0xaad370: r0 = []()
    //     0xaad370: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaad374: cmp             w0, NULL
    // 0xaad378: b.eq            #0xaad428
    // 0xaad37c: r1 = _ConstMap len:0
    //     0xaad37c: add             x1, PP, #0xf, lsl #12  ; [pp+0xf0c8] Map<String, dynamic>(0)
    //     0xaad380: ldr             x1, [x1, #0xc8]
    // 0xaad384: r2 = "v1rng"
    //     0xaad384: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e0b0] "v1rng"
    //     0xaad388: ldr             x2, [x2, #0xb0]
    // 0xaad38c: r0 = []()
    //     0xaad38c: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xaad390: mov             x3, x0
    // 0xaad394: r2 = Null
    //     0xaad394: mov             x2, NULL
    // 0xaad398: r1 = Null
    //     0xaad398: mov             x1, NULL
    // 0xaad39c: stur            x3, [fp, #-0x20]
    // 0xaad3a0: r4 = 60
    //     0xaad3a0: movz            x4, #0x3c
    // 0xaad3a4: branchIfSmi(r0, 0xaad3b0)
    //     0xaad3a4: tbz             w0, #0, #0xaad3b0
    // 0xaad3a8: r4 = LoadClassIdInstr(r0)
    //     0xaad3a8: ldur            x4, [x0, #-1]
    //     0xaad3ac: ubfx            x4, x4, #0xc, #0x14
    // 0xaad3b0: cmp             x4, #0x39
    // 0xaad3b4: b.eq            #0xaad3cc
    // 0xaad3b8: r8 = Function
    //     0xaad3b8: add             x8, PP, #0xd, lsl #12  ; [pp+0xd050] Type: Function
    //     0xaad3bc: ldr             x8, [x8, #0x50]
    // 0xaad3c0: r3 = Null
    //     0xaad3c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e0b8] Null
    //     0xaad3c4: ldr             x3, [x3, #0xb8]
    // 0xaad3c8: r0 = Function()
    //     0xaad3c8: bl              #0xed64f4  ; IsType_Function_Stub
    // 0xaad3cc: ldur            x0, [fp, #-0x10]
    // 0xaad3d0: r2 = Null
    //     0xaad3d0: mov             x2, NULL
    // 0xaad3d4: r1 = Null
    //     0xaad3d4: mov             x1, NULL
    // 0xaad3d8: r4 = 60
    //     0xaad3d8: movz            x4, #0x3c
    // 0xaad3dc: branchIfSmi(r0, 0xaad3e8)
    //     0xaad3dc: tbz             w0, #0, #0xaad3e8
    // 0xaad3e0: r4 = LoadClassIdInstr(r0)
    //     0xaad3e0: ldur            x4, [x0, #-1]
    //     0xaad3e4: ubfx            x4, x4, #0xc, #0x14
    // 0xaad3e8: sub             x4, x4, #0x5a
    // 0xaad3ec: cmp             x4, #2
    // 0xaad3f0: b.ls            #0xaad408
    // 0xaad3f4: r8 = List?
    //     0xaad3f4: add             x8, PP, #0xe, lsl #12  ; [pp+0xe140] Type: List?
    //     0xaad3f8: ldr             x8, [x8, #0x140]
    // 0xaad3fc: r3 = Null
    //     0xaad3fc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e0c8] Null
    //     0xaad400: ldr             x3, [x3, #0xc8]
    // 0xaad404: r0 = List?()
    //     0xaad404: bl              #0x5f8fc4  ; IsType_List?_Stub
    // 0xaad408: ldur            x16, [fp, #-0x18]
    // 0xaad40c: str             x16, [SP]
    // 0xaad410: ldur            x1, [fp, #-0x20]
    // 0xaad414: ldur            x2, [fp, #-0x10]
    // 0xaad418: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xaad418: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xaad41c: r0 = apply()
    //     0xaad41c: bl              #0x889cd8  ; [dart:core] Function::apply
    // 0xaad420: mov             x3, x0
    // 0xaad424: b               #0xaad438
    // 0xaad428: r4 = const [0, 0, 0, 0, null]
    //     0xaad428: add             x4, PP, #0xd, lsl #12  ; [pp+0xdad8] List(5) [0, 0, 0, 0, Null]
    //     0xaad42c: ldr             x4, [x4, #0xad8]
    // 0xaad430: r0 = mathRNG()
    //     0xaad430: bl              #0x8ba00c  ; [package:uuid/uuid_util.dart] UuidUtil::mathRNG
    // 0xaad434: mov             x3, x0
    // 0xaad438: mov             x0, x3
    // 0xaad43c: stur            x3, [fp, #-0x10]
    // 0xaad440: r2 = Null
    //     0xaad440: mov             x2, NULL
    // 0xaad444: r1 = Null
    //     0xaad444: mov             x1, NULL
    // 0xaad448: r4 = 60
    //     0xaad448: movz            x4, #0x3c
    // 0xaad44c: branchIfSmi(r0, 0xaad458)
    //     0xaad44c: tbz             w0, #0, #0xaad458
    // 0xaad450: r4 = LoadClassIdInstr(r0)
    //     0xaad450: ldur            x4, [x0, #-1]
    //     0xaad454: ubfx            x4, x4, #0xc, #0x14
    // 0xaad458: sub             x4, x4, #0x74
    // 0xaad45c: cmp             x4, #3
    // 0xaad460: b.ls            #0xaad478
    // 0xaad464: r8 = Uint8List
    //     0xaad464: add             x8, PP, #0x10, lsl #12  ; [pp+0x10a00] Type: Uint8List
    //     0xaad468: ldr             x8, [x8, #0xa00]
    // 0xaad46c: r3 = Null
    //     0xaad46c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e0d8] Null
    //     0xaad470: ldr             x3, [x3, #0xd8]
    // 0xaad474: r0 = Uint8List()
    //     0xaad474: bl              #0x5feb94  ; IsType_Uint8List_Stub
    // 0xaad478: ldur            x1, [fp, #-8]
    // 0xaad47c: r0 = _state()
    //     0xaad47c: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad480: r1 = LoadClassIdInstr(r0)
    //     0xaad480: ldur            x1, [x0, #-1]
    //     0xaad484: ubfx            x1, x1, #0xc, #0x14
    // 0xaad488: mov             x16, x0
    // 0xaad48c: mov             x0, x1
    // 0xaad490: mov             x1, x16
    // 0xaad494: r2 = "seedBytes"
    //     0xaad494: add             x2, PP, #0xf, lsl #12  ; [pp+0xf050] "seedBytes"
    //     0xaad498: ldr             x2, [x2, #0x50]
    // 0xaad49c: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad49c: sub             lr, x0, #0x114
    //     0xaad4a0: ldr             lr, [x21, lr, lsl #3]
    //     0xaad4a4: blr             lr
    // 0xaad4a8: cmp             w0, NULL
    // 0xaad4ac: b.eq            #0xaad4e4
    // 0xaad4b0: ldur            x1, [fp, #-8]
    // 0xaad4b4: r0 = _state()
    //     0xaad4b4: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad4b8: r1 = LoadClassIdInstr(r0)
    //     0xaad4b8: ldur            x1, [x0, #-1]
    //     0xaad4bc: ubfx            x1, x1, #0xc, #0x14
    // 0xaad4c0: mov             x16, x0
    // 0xaad4c4: mov             x0, x1
    // 0xaad4c8: mov             x1, x16
    // 0xaad4cc: r2 = "seedBytes"
    //     0xaad4cc: add             x2, PP, #0xf, lsl #12  ; [pp+0xf050] "seedBytes"
    //     0xaad4d0: ldr             x2, [x2, #0x50]
    // 0xaad4d4: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad4d4: sub             lr, x0, #0x114
    //     0xaad4d8: ldr             lr, [x21, lr, lsl #3]
    //     0xaad4dc: blr             lr
    // 0xaad4e0: b               #0xaad518
    // 0xaad4e4: ldur            x1, [fp, #-8]
    // 0xaad4e8: r0 = _state()
    //     0xaad4e8: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad4ec: r1 = LoadClassIdInstr(r0)
    //     0xaad4ec: ldur            x1, [x0, #-1]
    //     0xaad4f0: ubfx            x1, x1, #0xc, #0x14
    // 0xaad4f4: mov             x16, x0
    // 0xaad4f8: mov             x0, x1
    // 0xaad4fc: mov             x1, x16
    // 0xaad500: ldur            x3, [fp, #-0x10]
    // 0xaad504: r2 = "seedBytes"
    //     0xaad504: add             x2, PP, #0xf, lsl #12  ; [pp+0xf050] "seedBytes"
    //     0xaad508: ldr             x2, [x2, #0x50]
    // 0xaad50c: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaad50c: sub             lr, x0, #0x10d
    //     0xaad510: ldr             lr, [x21, lr, lsl #3]
    //     0xaad514: blr             lr
    // 0xaad518: ldur            x3, [fp, #-0x10]
    // 0xaad51c: r4 = 12
    //     0xaad51c: movz            x4, #0xc
    // 0xaad520: LoadField: r0 = r3->field_13
    //     0xaad520: ldur            w0, [x3, #0x13]
    // 0xaad524: r5 = LoadInt32Instr(r0)
    //     0xaad524: sbfx            x5, x0, #1, #0x1f
    // 0xaad528: mov             x0, x5
    // 0xaad52c: stur            x5, [fp, #-0x50]
    // 0xaad530: r1 = 0
    //     0xaad530: movz            x1, #0
    // 0xaad534: cmp             x1, x0
    // 0xaad538: b.hs            #0xaad85c
    // 0xaad53c: LoadField: r0 = r3->field_7
    //     0xaad53c: ldur            x0, [x3, #7]
    // 0xaad540: ldrb            w1, [x0]
    // 0xaad544: orr             x2, x1, #1
    // 0xaad548: mov             x0, x5
    // 0xaad54c: r1 = 1
    //     0xaad54c: movz            x1, #0x1
    // 0xaad550: cmp             x1, x0
    // 0xaad554: b.hs            #0xaad860
    // 0xaad558: LoadField: r0 = r3->field_7
    //     0xaad558: ldur            x0, [x3, #7]
    // 0xaad55c: ArrayLoad: r6 = r0[-22]  ; TypedUnsigned_1
    //     0xaad55c: ldrb            w6, [x0, #1]
    // 0xaad560: mov             x0, x5
    // 0xaad564: stur            x6, [fp, #-0x48]
    // 0xaad568: r1 = 2
    //     0xaad568: movz            x1, #0x2
    // 0xaad56c: cmp             x1, x0
    // 0xaad570: b.hs            #0xaad864
    // 0xaad574: LoadField: r0 = r3->field_7
    //     0xaad574: ldur            x0, [x3, #7]
    // 0xaad578: ArrayLoad: r7 = r0[-21]  ; TypedUnsigned_1
    //     0xaad578: ldrb            w7, [x0, #2]
    // 0xaad57c: mov             x0, x5
    // 0xaad580: stur            x7, [fp, #-0x40]
    // 0xaad584: r1 = 3
    //     0xaad584: movz            x1, #0x3
    // 0xaad588: cmp             x1, x0
    // 0xaad58c: b.hs            #0xaad868
    // 0xaad590: LoadField: r0 = r3->field_7
    //     0xaad590: ldur            x0, [x3, #7]
    // 0xaad594: ArrayLoad: r8 = r0[-20]  ; TypedUnsigned_1
    //     0xaad594: ldrb            w8, [x0, #3]
    // 0xaad598: mov             x0, x5
    // 0xaad59c: stur            x8, [fp, #-0x38]
    // 0xaad5a0: r1 = 4
    //     0xaad5a0: movz            x1, #0x4
    // 0xaad5a4: cmp             x1, x0
    // 0xaad5a8: b.hs            #0xaad86c
    // 0xaad5ac: LoadField: r0 = r3->field_7
    //     0xaad5ac: ldur            x0, [x3, #7]
    // 0xaad5b0: ArrayLoad: r9 = r0[-19]  ; TypedUnsigned_1
    //     0xaad5b0: ldrb            w9, [x0, #4]
    // 0xaad5b4: mov             x0, x5
    // 0xaad5b8: stur            x9, [fp, #-0x30]
    // 0xaad5bc: r1 = 5
    //     0xaad5bc: movz            x1, #0x5
    // 0xaad5c0: cmp             x1, x0
    // 0xaad5c4: b.hs            #0xaad870
    // 0xaad5c8: LoadField: r0 = r3->field_7
    //     0xaad5c8: ldur            x0, [x3, #7]
    // 0xaad5cc: ArrayLoad: r10 = r0[-18]  ; TypedUnsigned_1
    //     0xaad5cc: ldrb            w10, [x0, #5]
    // 0xaad5d0: stur            x10, [fp, #-0x28]
    // 0xaad5d4: lsl             x0, x2, #1
    // 0xaad5d8: mov             x2, x4
    // 0xaad5dc: stur            x0, [fp, #-0x18]
    // 0xaad5e0: r1 = Null
    //     0xaad5e0: mov             x1, NULL
    // 0xaad5e4: r0 = AllocateArray()
    //     0xaad5e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xaad5e8: mov             x2, x0
    // 0xaad5ec: ldur            x0, [fp, #-0x18]
    // 0xaad5f0: stur            x2, [fp, #-0x20]
    // 0xaad5f4: StoreField: r2->field_f = r0
    //     0xaad5f4: stur            w0, [x2, #0xf]
    // 0xaad5f8: ldur            x0, [fp, #-0x48]
    // 0xaad5fc: lsl             x1, x0, #1
    // 0xaad600: StoreField: r2->field_13 = r1
    //     0xaad600: stur            w1, [x2, #0x13]
    // 0xaad604: ldur            x0, [fp, #-0x40]
    // 0xaad608: lsl             x1, x0, #1
    // 0xaad60c: ArrayStore: r2[0] = r1  ; List_4
    //     0xaad60c: stur            w1, [x2, #0x17]
    // 0xaad610: ldur            x0, [fp, #-0x38]
    // 0xaad614: lsl             x1, x0, #1
    // 0xaad618: StoreField: r2->field_1b = r1
    //     0xaad618: stur            w1, [x2, #0x1b]
    // 0xaad61c: ldur            x0, [fp, #-0x30]
    // 0xaad620: lsl             x1, x0, #1
    // 0xaad624: StoreField: r2->field_1f = r1
    //     0xaad624: stur            w1, [x2, #0x1f]
    // 0xaad628: ldur            x0, [fp, #-0x28]
    // 0xaad62c: lsl             x1, x0, #1
    // 0xaad630: StoreField: r2->field_23 = r1
    //     0xaad630: stur            w1, [x2, #0x23]
    // 0xaad634: r1 = <int>
    //     0xaad634: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xaad638: r0 = AllocateGrowableArray()
    //     0xaad638: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xaad63c: mov             x2, x0
    // 0xaad640: ldur            x0, [fp, #-0x20]
    // 0xaad644: stur            x2, [fp, #-0x18]
    // 0xaad648: StoreField: r2->field_f = r0
    //     0xaad648: stur            w0, [x2, #0xf]
    // 0xaad64c: r0 = 12
    //     0xaad64c: movz            x0, #0xc
    // 0xaad650: StoreField: r2->field_b = r0
    //     0xaad650: stur            w0, [x2, #0xb]
    // 0xaad654: ldur            x1, [fp, #-8]
    // 0xaad658: r0 = _state()
    //     0xaad658: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad65c: r1 = LoadClassIdInstr(r0)
    //     0xaad65c: ldur            x1, [x0, #-1]
    //     0xaad660: ubfx            x1, x1, #0xc, #0x14
    // 0xaad664: mov             x16, x0
    // 0xaad668: mov             x0, x1
    // 0xaad66c: mov             x1, x16
    // 0xaad670: r2 = "node"
    //     0xaad670: add             x2, PP, #0xf, lsl #12  ; [pp+0xf058] "node"
    //     0xaad674: ldr             x2, [x2, #0x58]
    // 0xaad678: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad678: sub             lr, x0, #0x114
    //     0xaad67c: ldr             lr, [x21, lr, lsl #3]
    //     0xaad680: blr             lr
    // 0xaad684: cmp             w0, NULL
    // 0xaad688: b.eq            #0xaad6c0
    // 0xaad68c: ldur            x1, [fp, #-8]
    // 0xaad690: r0 = _state()
    //     0xaad690: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad694: r1 = LoadClassIdInstr(r0)
    //     0xaad694: ldur            x1, [x0, #-1]
    //     0xaad698: ubfx            x1, x1, #0xc, #0x14
    // 0xaad69c: mov             x16, x0
    // 0xaad6a0: mov             x0, x1
    // 0xaad6a4: mov             x1, x16
    // 0xaad6a8: r2 = "node"
    //     0xaad6a8: add             x2, PP, #0xf, lsl #12  ; [pp+0xf058] "node"
    //     0xaad6ac: ldr             x2, [x2, #0x58]
    // 0xaad6b0: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad6b0: sub             lr, x0, #0x114
    //     0xaad6b4: ldr             lr, [x21, lr, lsl #3]
    //     0xaad6b8: blr             lr
    // 0xaad6bc: b               #0xaad6f4
    // 0xaad6c0: ldur            x1, [fp, #-8]
    // 0xaad6c4: r0 = _state()
    //     0xaad6c4: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad6c8: r1 = LoadClassIdInstr(r0)
    //     0xaad6c8: ldur            x1, [x0, #-1]
    //     0xaad6cc: ubfx            x1, x1, #0xc, #0x14
    // 0xaad6d0: mov             x16, x0
    // 0xaad6d4: mov             x0, x1
    // 0xaad6d8: mov             x1, x16
    // 0xaad6dc: ldur            x3, [fp, #-0x18]
    // 0xaad6e0: r2 = "node"
    //     0xaad6e0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf058] "node"
    //     0xaad6e4: ldr             x2, [x2, #0x58]
    // 0xaad6e8: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaad6e8: sub             lr, x0, #0x10d
    //     0xaad6ec: ldr             lr, [x21, lr, lsl #3]
    //     0xaad6f0: blr             lr
    // 0xaad6f4: ldur            x2, [fp, #-0x10]
    // 0xaad6f8: r3 = 262143
    //     0xaad6f8: orr             x3, xzr, #0x3ffff
    // 0xaad6fc: ldur            x0, [fp, #-0x50]
    // 0xaad700: r1 = 6
    //     0xaad700: movz            x1, #0x6
    // 0xaad704: cmp             x1, x0
    // 0xaad708: b.hs            #0xaad874
    // 0xaad70c: LoadField: r0 = r2->field_7
    //     0xaad70c: ldur            x0, [x2, #7]
    // 0xaad710: ArrayLoad: r1 = r0[-17]  ; TypedUnsigned_1
    //     0xaad710: ldrb            w1, [x0, #6]
    // 0xaad714: lsl             x4, x1, #8
    // 0xaad718: ldur            x0, [fp, #-0x50]
    // 0xaad71c: r1 = 7
    //     0xaad71c: movz            x1, #0x7
    // 0xaad720: cmp             x1, x0
    // 0xaad724: b.hs            #0xaad878
    // 0xaad728: LoadField: r0 = r2->field_7
    //     0xaad728: ldur            x0, [x2, #7]
    // 0xaad72c: ArrayLoad: r1 = r0[-16]  ; TypedUnsigned_1
    //     0xaad72c: ldrb            w1, [x0, #7]
    // 0xaad730: ubfx            x4, x4, #0, #0x20
    // 0xaad734: ubfx            x1, x1, #0, #0x20
    // 0xaad738: orr             x0, x4, x1
    // 0xaad73c: and             x2, x0, x3
    // 0xaad740: ldur            x1, [fp, #-8]
    // 0xaad744: stur            x2, [fp, #-0x28]
    // 0xaad748: r0 = _state()
    //     0xaad748: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad74c: mov             x3, x0
    // 0xaad750: stur            x3, [fp, #-0x10]
    // 0xaad754: r0 = LoadClassIdInstr(r3)
    //     0xaad754: ldur            x0, [x3, #-1]
    //     0xaad758: ubfx            x0, x0, #0xc, #0x14
    // 0xaad75c: mov             x1, x3
    // 0xaad760: r2 = "clockSeq"
    //     0xaad760: add             x2, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0xaad764: ldr             x2, [x2, #0x60]
    // 0xaad768: r0 = GDT[cid_x0 + -0x114]()
    //     0xaad768: sub             lr, x0, #0x114
    //     0xaad76c: ldr             lr, [x21, lr, lsl #3]
    //     0xaad770: blr             lr
    // 0xaad774: cmp             w0, NULL
    // 0xaad778: b.ne            #0xaad7a4
    // 0xaad77c: ldur            x0, [fp, #-0x28]
    // 0xaad780: ldur            x1, [fp, #-0x10]
    // 0xaad784: lsl             w3, w0, #1
    // 0xaad788: r0 = LoadClassIdInstr(r1)
    //     0xaad788: ldur            x0, [x1, #-1]
    //     0xaad78c: ubfx            x0, x0, #0xc, #0x14
    // 0xaad790: r2 = "clockSeq"
    //     0xaad790: add             x2, PP, #0xf, lsl #12  ; [pp+0xf060] "clockSeq"
    //     0xaad794: ldr             x2, [x2, #0x60]
    // 0xaad798: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaad798: sub             lr, x0, #0x10d
    //     0xaad79c: ldr             lr, [x21, lr, lsl #3]
    //     0xaad7a0: blr             lr
    // 0xaad7a4: ldur            x1, [fp, #-8]
    // 0xaad7a8: r0 = _state()
    //     0xaad7a8: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad7ac: r1 = LoadClassIdInstr(r0)
    //     0xaad7ac: ldur            x1, [x0, #-1]
    //     0xaad7b0: ubfx            x1, x1, #0xc, #0x14
    // 0xaad7b4: mov             x16, x0
    // 0xaad7b8: mov             x0, x1
    // 0xaad7bc: mov             x1, x16
    // 0xaad7c0: r2 = "mSecs"
    //     0xaad7c0: add             x2, PP, #0xf, lsl #12  ; [pp+0xf068] "mSecs"
    //     0xaad7c4: ldr             x2, [x2, #0x68]
    // 0xaad7c8: r3 = 0
    //     0xaad7c8: movz            x3, #0
    // 0xaad7cc: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaad7cc: sub             lr, x0, #0x10d
    //     0xaad7d0: ldr             lr, [x21, lr, lsl #3]
    //     0xaad7d4: blr             lr
    // 0xaad7d8: ldur            x1, [fp, #-8]
    // 0xaad7dc: r0 = _state()
    //     0xaad7dc: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad7e0: r1 = LoadClassIdInstr(r0)
    //     0xaad7e0: ldur            x1, [x0, #-1]
    //     0xaad7e4: ubfx            x1, x1, #0xc, #0x14
    // 0xaad7e8: mov             x16, x0
    // 0xaad7ec: mov             x0, x1
    // 0xaad7f0: mov             x1, x16
    // 0xaad7f4: r2 = "nSecs"
    //     0xaad7f4: add             x2, PP, #0xf, lsl #12  ; [pp+0xf070] "nSecs"
    //     0xaad7f8: ldr             x2, [x2, #0x70]
    // 0xaad7fc: r3 = 0
    //     0xaad7fc: movz            x3, #0
    // 0xaad800: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaad800: sub             lr, x0, #0x10d
    //     0xaad804: ldr             lr, [x21, lr, lsl #3]
    //     0xaad808: blr             lr
    // 0xaad80c: ldur            x1, [fp, #-8]
    // 0xaad810: r0 = _state()
    //     0xaad810: bl              #0x8b9bbc  ; [package:uuid/uuid.dart] Uuid::_state
    // 0xaad814: r1 = LoadClassIdInstr(r0)
    //     0xaad814: ldur            x1, [x0, #-1]
    //     0xaad818: ubfx            x1, x1, #0xc, #0x14
    // 0xaad81c: mov             x16, x0
    // 0xaad820: mov             x0, x1
    // 0xaad824: mov             x1, x16
    // 0xaad828: r2 = "hasInitV1"
    //     0xaad828: add             x2, PP, #0xf, lsl #12  ; [pp+0xf078] "hasInitV1"
    //     0xaad82c: ldr             x2, [x2, #0x78]
    // 0xaad830: r3 = true
    //     0xaad830: add             x3, NULL, #0x20  ; true
    // 0xaad834: r0 = GDT[cid_x0 + -0x10d]()
    //     0xaad834: sub             lr, x0, #0x10d
    //     0xaad838: ldr             lr, [x21, lr, lsl #3]
    //     0xaad83c: blr             lr
    // 0xaad840: r0 = Null
    //     0xaad840: mov             x0, NULL
    // 0xaad844: LeaveFrame
    //     0xaad844: mov             SP, fp
    //     0xaad848: ldp             fp, lr, [SP], #0x10
    // 0xaad84c: ret
    //     0xaad84c: ret             
    // 0xaad850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaad850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaad854: b               #0xaad238
    // 0xaad858: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaad858: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaad85c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad85c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaad860: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad860: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaad864: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad864: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaad868: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad868: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaad86c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad86c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaad870: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad870: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaad874: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad874: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaad878: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaad878: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
