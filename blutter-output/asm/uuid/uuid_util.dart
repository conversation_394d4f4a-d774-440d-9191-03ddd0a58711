// lib: uuid_util, url: package:uuid/uuid_util.dart

// class id: 1051225, size: 0x8
class :: {
}

// class id: 414, size: 0x8, field offset: 0x8
abstract class UuidUtil extends Object {

  static late final Random _random; // offset: 0x1768

  [closure] static Uint8List mathRNG(dynamic, {int seed}) {
    // ** addr: 0x8b9f74, size: 0x98
    // 0x8b9f74: EnterFrame
    //     0x8b9f74: stp             fp, lr, [SP, #-0x10]!
    //     0x8b9f78: mov             fp, SP
    // 0x8b9f7c: AllocStack(0x8)
    //     0x8b9f7c: sub             SP, SP, #8
    // 0x8b9f80: SetupParameters({int seed = -1 /* r2 */})
    //     0x8b9f80: ldur            w0, [x4, #0x13]
    //     0x8b9f84: ldur            w1, [x4, #0x1f]
    //     0x8b9f88: add             x1, x1, HEAP, lsl #32
    //     0x8b9f8c: add             x16, PP, #0xf, lsl #12  ; [pp+0xf110] "seed"
    //     0x8b9f90: ldr             x16, [x16, #0x110]
    //     0x8b9f94: cmp             w1, w16
    //     0x8b9f98: b.ne            #0x8b9fc4
    //     0x8b9f9c: ldur            w1, [x4, #0x23]
    //     0x8b9fa0: add             x1, x1, HEAP, lsl #32
    //     0x8b9fa4: sub             w2, w0, w1
    //     0x8b9fa8: add             x0, fp, w2, sxtw #2
    //     0x8b9fac: ldr             x0, [x0, #8]
    //     0x8b9fb0: sbfx            x1, x0, #1, #0x1f
    //     0x8b9fb4: tbz             w0, #0, #0x8b9fbc
    //     0x8b9fb8: ldur            x1, [x0, #7]
    //     0x8b9fbc: mov             x2, x1
    //     0x8b9fc0: b               #0x8b9fc8
    //     0x8b9fc4: movn            x2, #0
    // 0x8b9fc8: CheckStackOverflow
    //     0x8b9fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b9fcc: cmp             SP, x16
    //     0x8b9fd0: b.ls            #0x8ba004
    // 0x8b9fd4: r0 = BoxInt64Instr(r2)
    //     0x8b9fd4: sbfiz           x0, x2, #1, #0x1f
    //     0x8b9fd8: cmp             x2, x0, asr #1
    //     0x8b9fdc: b.eq            #0x8b9fe8
    //     0x8b9fe0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8b9fe4: stur            x2, [x0, #7]
    // 0x8b9fe8: str             x0, [SP]
    // 0x8b9fec: r4 = const [0, 0x1, 0x1, 0, seed, 0, null]
    //     0x8b9fec: add             x4, PP, #0xf, lsl #12  ; [pp+0xf118] List(7) [0, 0x1, 0x1, 0, "seed", 0, Null]
    //     0x8b9ff0: ldr             x4, [x4, #0x118]
    // 0x8b9ff4: r0 = mathRNG()
    //     0x8b9ff4: bl              #0x8ba00c  ; [package:uuid/uuid_util.dart] UuidUtil::mathRNG
    // 0x8b9ff8: LeaveFrame
    //     0x8b9ff8: mov             SP, fp
    //     0x8b9ffc: ldp             fp, lr, [SP], #0x10
    // 0x8ba000: ret
    //     0x8ba000: ret             
    // 0x8ba004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ba004: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ba008: b               #0x8b9fd4
  }
  static _ mathRNG(/* No info */) {
    // ** addr: 0x8ba00c, size: 0x144
    // 0x8ba00c: EnterFrame
    //     0x8ba00c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ba010: mov             fp, SP
    // 0x8ba014: AllocStack(0x20)
    //     0x8ba014: sub             SP, SP, #0x20
    // 0x8ba018: SetupParameters({int seed = -1 /* r0, fp-0x8 */})
    //     0x8ba018: ldur            w0, [x4, #0x13]
    //     0x8ba01c: ldur            w1, [x4, #0x1f]
    //     0x8ba020: add             x1, x1, HEAP, lsl #32
    //     0x8ba024: add             x16, PP, #0xf, lsl #12  ; [pp+0xf110] "seed"
    //     0x8ba028: ldr             x16, [x16, #0x110]
    //     0x8ba02c: cmp             w1, w16
    //     0x8ba030: b.ne            #0x8ba05c
    //     0x8ba034: ldur            w1, [x4, #0x23]
    //     0x8ba038: add             x1, x1, HEAP, lsl #32
    //     0x8ba03c: sub             w2, w0, w1
    //     0x8ba040: add             x0, fp, w2, sxtw #2
    //     0x8ba044: ldr             x0, [x0, #8]
    //     0x8ba048: sbfx            x1, x0, #1, #0x1f
    //     0x8ba04c: tbz             w0, #0, #0x8ba054
    //     0x8ba050: ldur            x1, [x0, #7]
    //     0x8ba054: mov             x0, x1
    //     0x8ba058: b               #0x8ba060
    //     0x8ba05c: movn            x0, #0
    //     0x8ba060: stur            x0, [fp, #-8]
    // 0x8ba064: CheckStackOverflow
    //     0x8ba064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ba068: cmp             SP, x16
    //     0x8ba06c: b.ls            #0x8ba140
    // 0x8ba070: r4 = 32
    //     0x8ba070: movz            x4, #0x20
    // 0x8ba074: r0 = AllocateUint8Array()
    //     0x8ba074: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x8ba078: ldur            x2, [fp, #-8]
    // 0x8ba07c: stur            x0, [fp, #-0x10]
    // 0x8ba080: cmn             x2, #1
    // 0x8ba084: b.ne            #0x8ba0ac
    // 0x8ba088: r0 = InitLateStaticField(0x1768) // [package:uuid/uuid_util.dart] UuidUtil::_random
    //     0x8ba088: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ba08c: ldr             x0, [x0, #0x2ed0]
    //     0x8ba090: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ba094: cmp             w0, w16
    //     0x8ba098: b.ne            #0x8ba0a8
    //     0x8ba09c: add             x2, PP, #0xf, lsl #12  ; [pp+0xf120] Field <UuidUtil._random@2737452677>: static late final (offset: 0x1768)
    //     0x8ba0a0: ldr             x2, [x2, #0x120]
    //     0x8ba0a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8ba0a8: b               #0x8ba0d0
    // 0x8ba0ac: r0 = BoxInt64Instr(r2)
    //     0x8ba0ac: sbfiz           x0, x2, #1, #0x1f
    //     0x8ba0b0: cmp             x2, x0, asr #1
    //     0x8ba0b4: b.eq            #0x8ba0c0
    //     0x8ba0b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ba0bc: stur            x2, [x0, #7]
    // 0x8ba0c0: str             x0, [SP]
    // 0x8ba0c4: r1 = Null
    //     0x8ba0c4: mov             x1, NULL
    // 0x8ba0c8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8ba0c8: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8ba0cc: r0 = Random()
    //     0x8ba0cc: bl              #0x834fe4  ; [dart:math] Random::Random
    // 0x8ba0d0: stur            x0, [fp, #-0x18]
    // 0x8ba0d4: ldur            x2, [fp, #-0x10]
    // 0x8ba0d8: r3 = 0
    //     0x8ba0d8: movz            x3, #0
    // 0x8ba0dc: stur            x3, [fp, #-8]
    // 0x8ba0e0: CheckStackOverflow
    //     0x8ba0e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ba0e4: cmp             SP, x16
    //     0x8ba0e8: b.ls            #0x8ba148
    // 0x8ba0ec: cmp             x3, #0x10
    // 0x8ba0f0: b.ge            #0x8ba130
    // 0x8ba0f4: mov             x1, x0
    // 0x8ba0f8: r0 = _nextState()
    //     0x8ba0f8: bl              #0x7bef34  ; [dart:math] _Random::_nextState
    // 0x8ba0fc: ldur            x1, [fp, #-0x18]
    // 0x8ba100: LoadField: r2 = r1->field_7
    //     0x8ba100: ldur            x2, [x1, #7]
    // 0x8ba104: ubfx            x2, x2, #0, #0x20
    // 0x8ba108: r0 = 255
    //     0x8ba108: movz            x0, #0xff
    // 0x8ba10c: and             x3, x2, x0
    // 0x8ba110: ubfx            x3, x3, #0, #0x20
    // 0x8ba114: ldur            x4, [fp, #-8]
    // 0x8ba118: ldur            x2, [fp, #-0x10]
    // 0x8ba11c: ArrayStore: r2[r4] = r3  ; TypeUnknown_1
    //     0x8ba11c: add             x5, x2, x4
    //     0x8ba120: strb            w3, [x5, #0x17]
    // 0x8ba124: add             x3, x4, #1
    // 0x8ba128: mov             x0, x1
    // 0x8ba12c: b               #0x8ba0dc
    // 0x8ba130: mov             x0, x2
    // 0x8ba134: LeaveFrame
    //     0x8ba134: mov             SP, fp
    //     0x8ba138: ldp             fp, lr, [SP], #0x10
    // 0x8ba13c: ret
    //     0x8ba13c: ret             
    // 0x8ba140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ba140: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ba144: b               #0x8ba070
    // 0x8ba148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ba148: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ba14c: b               #0x8ba0ec
  }
}
