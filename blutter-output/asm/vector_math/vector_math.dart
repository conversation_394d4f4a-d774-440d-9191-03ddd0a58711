// lib: vector_math, url: package:vector_math/vector_math.dart

// class id: 1051226, size: 0x8
class :: {
}

// class id: 410, size: 0xc, field offset: 0x8
class Vector4 extends Object
    implements Vector {

  Vector4 -(Vector4, Vector4) {
    // ** addr: 0xc42034, size: 0x84
    // 0xc42034: EnterFrame
    //     0xc42034: stp             fp, lr, [SP, #-0x10]!
    //     0xc42038: mov             fp, SP
    // 0xc4203c: CheckStackOverflow
    //     0xc4203c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc42040: cmp             SP, x16
    //     0xc42044: b.ls            #0xc42098
    // 0xc42048: ldr             x0, [fp, #0x10]
    // 0xc4204c: r2 = Null
    //     0xc4204c: mov             x2, NULL
    // 0xc42050: r1 = Null
    //     0xc42050: mov             x1, NULL
    // 0xc42054: r4 = 60
    //     0xc42054: movz            x4, #0x3c
    // 0xc42058: branchIfSmi(r0, 0xc42064)
    //     0xc42058: tbz             w0, #0, #0xc42064
    // 0xc4205c: r4 = LoadClassIdInstr(r0)
    //     0xc4205c: ldur            x4, [x0, #-1]
    //     0xc42060: ubfx            x4, x4, #0xc, #0x14
    // 0xc42064: cmp             x4, #0x19a
    // 0xc42068: b.eq            #0xc42080
    // 0xc4206c: r8 = Vector4
    //     0xc4206c: add             x8, PP, #0x41, lsl #12  ; [pp+0x41b20] Type: Vector4
    //     0xc42070: ldr             x8, [x8, #0xb20]
    // 0xc42074: r3 = Null
    //     0xc42074: add             x3, PP, #0x41, lsl #12  ; [pp+0x41b38] Null
    //     0xc42078: ldr             x3, [x3, #0xb38]
    // 0xc4207c: r0 = DefaultTypeTest()
    //     0xc4207c: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc42080: ldr             x1, [fp, #0x18]
    // 0xc42084: ldr             x2, [fp, #0x10]
    // 0xc42088: r0 = -()
    //     0xc42088: bl              #0xc420a0  ; [package:vector_math/vector_math.dart] Vector4::-
    // 0xc4208c: LeaveFrame
    //     0xc4208c: mov             SP, fp
    //     0xc42090: ldp             fp, lr, [SP], #0x10
    // 0xc42094: ret
    //     0xc42094: ret             
    // 0xc42098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc42098: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4209c: b               #0xc42048
  }
  Vector4 -(Vector4, Vector4) {
    // ** addr: 0xc420a0, size: 0x48
    // 0xc420a0: EnterFrame
    //     0xc420a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc420a4: mov             fp, SP
    // 0xc420a8: AllocStack(0x8)
    //     0xc420a8: sub             SP, SP, #8
    // 0xc420ac: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xc420ac: stur            x2, [fp, #-8]
    // 0xc420b0: CheckStackOverflow
    //     0xc420b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc420b4: cmp             SP, x16
    //     0xc420b8: b.ls            #0xc420e0
    // 0xc420bc: r0 = clone()
    //     0xc420bc: bl              #0xc42230  ; [package:vector_math/vector_math.dart] Vector4::clone
    // 0xc420c0: mov             x1, x0
    // 0xc420c4: ldur            x2, [fp, #-8]
    // 0xc420c8: stur            x0, [fp, #-8]
    // 0xc420cc: r0 = sub()
    //     0xc420cc: bl              #0xc420e8  ; [package:vector_math/vector_math.dart] Vector4::sub
    // 0xc420d0: ldur            x0, [fp, #-8]
    // 0xc420d4: LeaveFrame
    //     0xc420d4: mov             SP, fp
    //     0xc420d8: ldp             fp, lr, [SP], #0x10
    // 0xc420dc: ret
    //     0xc420dc: ret             
    // 0xc420e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc420e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc420e4: b               #0xc420bc
  }
  _ sub(/* No info */) {
    // ** addr: 0xc420e8, size: 0x148
    // 0xc420e8: EnterFrame
    //     0xc420e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc420ec: mov             fp, SP
    // 0xc420f0: LoadField: r3 = r2->field_7
    //     0xc420f0: ldur            w3, [x2, #7]
    // 0xc420f4: DecompressPointer r3
    //     0xc420f4: add             x3, x3, HEAP, lsl #32
    // 0xc420f8: LoadField: r2 = r1->field_7
    //     0xc420f8: ldur            w2, [x1, #7]
    // 0xc420fc: DecompressPointer r2
    //     0xc420fc: add             x2, x2, HEAP, lsl #32
    // 0xc42100: LoadField: r4 = r2->field_13
    //     0xc42100: ldur            w4, [x2, #0x13]
    // 0xc42104: r5 = LoadInt32Instr(r4)
    //     0xc42104: sbfx            x5, x4, #1, #0x1f
    // 0xc42108: mov             x0, x5
    // 0xc4210c: r1 = 0
    //     0xc4210c: movz            x1, #0
    // 0xc42110: cmp             x1, x0
    // 0xc42114: b.hs            #0xc42210
    // 0xc42118: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xc42118: ldur            s0, [x2, #0x17]
    // 0xc4211c: fcvt            d1, s0
    // 0xc42120: LoadField: r4 = r3->field_13
    //     0xc42120: ldur            w4, [x3, #0x13]
    // 0xc42124: r6 = LoadInt32Instr(r4)
    //     0xc42124: sbfx            x6, x4, #1, #0x1f
    // 0xc42128: mov             x0, x6
    // 0xc4212c: r1 = 0
    //     0xc4212c: movz            x1, #0
    // 0xc42130: cmp             x1, x0
    // 0xc42134: b.hs            #0xc42214
    // 0xc42138: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc42138: ldur            s0, [x3, #0x17]
    // 0xc4213c: fcvt            d2, s0
    // 0xc42140: fsub            d0, d1, d2
    // 0xc42144: fcvt            s1, d0
    // 0xc42148: ArrayStore: r2[0] = d1  ; List_8
    //     0xc42148: stur            s1, [x2, #0x17]
    // 0xc4214c: mov             x0, x5
    // 0xc42150: r1 = 1
    //     0xc42150: movz            x1, #0x1
    // 0xc42154: cmp             x1, x0
    // 0xc42158: b.hs            #0xc42218
    // 0xc4215c: LoadField: d0 = r2->field_1b
    //     0xc4215c: ldur            s0, [x2, #0x1b]
    // 0xc42160: fcvt            d1, s0
    // 0xc42164: mov             x0, x6
    // 0xc42168: r1 = 1
    //     0xc42168: movz            x1, #0x1
    // 0xc4216c: cmp             x1, x0
    // 0xc42170: b.hs            #0xc4221c
    // 0xc42174: LoadField: d0 = r3->field_1b
    //     0xc42174: ldur            s0, [x3, #0x1b]
    // 0xc42178: fcvt            d2, s0
    // 0xc4217c: fsub            d0, d1, d2
    // 0xc42180: fcvt            s1, d0
    // 0xc42184: StoreField: r2->field_1b = d1
    //     0xc42184: stur            s1, [x2, #0x1b]
    // 0xc42188: mov             x0, x5
    // 0xc4218c: r1 = 2
    //     0xc4218c: movz            x1, #0x2
    // 0xc42190: cmp             x1, x0
    // 0xc42194: b.hs            #0xc42220
    // 0xc42198: LoadField: d0 = r2->field_1f
    //     0xc42198: ldur            s0, [x2, #0x1f]
    // 0xc4219c: fcvt            d1, s0
    // 0xc421a0: mov             x0, x6
    // 0xc421a4: r1 = 2
    //     0xc421a4: movz            x1, #0x2
    // 0xc421a8: cmp             x1, x0
    // 0xc421ac: b.hs            #0xc42224
    // 0xc421b0: LoadField: d0 = r3->field_1f
    //     0xc421b0: ldur            s0, [x3, #0x1f]
    // 0xc421b4: fcvt            d2, s0
    // 0xc421b8: fsub            d0, d1, d2
    // 0xc421bc: fcvt            s1, d0
    // 0xc421c0: StoreField: r2->field_1f = d1
    //     0xc421c0: stur            s1, [x2, #0x1f]
    // 0xc421c4: mov             x0, x5
    // 0xc421c8: r1 = 3
    //     0xc421c8: movz            x1, #0x3
    // 0xc421cc: cmp             x1, x0
    // 0xc421d0: b.hs            #0xc42228
    // 0xc421d4: LoadField: d0 = r2->field_23
    //     0xc421d4: ldur            s0, [x2, #0x23]
    // 0xc421d8: fcvt            d1, s0
    // 0xc421dc: mov             x0, x6
    // 0xc421e0: r1 = 3
    //     0xc421e0: movz            x1, #0x3
    // 0xc421e4: cmp             x1, x0
    // 0xc421e8: b.hs            #0xc4222c
    // 0xc421ec: LoadField: d0 = r3->field_23
    //     0xc421ec: ldur            s0, [x3, #0x23]
    // 0xc421f0: fcvt            d2, s0
    // 0xc421f4: fsub            d0, d1, d2
    // 0xc421f8: fcvt            s1, d0
    // 0xc421fc: StoreField: r2->field_23 = d1
    //     0xc421fc: stur            s1, [x2, #0x23]
    // 0xc42200: r0 = Null
    //     0xc42200: mov             x0, NULL
    // 0xc42204: LeaveFrame
    //     0xc42204: mov             SP, fp
    //     0xc42208: ldp             fp, lr, [SP], #0x10
    // 0xc4220c: ret
    //     0xc4220c: ret             
    // 0xc42210: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42210: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc42214: r0 = RangeErrorSharedWithFPURegs()
    //     0xc42214: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc42218: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42218: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc4221c: r0 = RangeErrorSharedWithFPURegs()
    //     0xc4221c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc42220: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42220: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc42224: r0 = RangeErrorSharedWithFPURegs()
    //     0xc42224: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc42228: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42228: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc4222c: r0 = RangeErrorSharedWithFPURegs()
    //     0xc4222c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  _ clone(/* No info */) {
    // ** addr: 0xc42230, size: 0x34
    // 0xc42230: EnterFrame
    //     0xc42230: stp             fp, lr, [SP, #-0x10]!
    //     0xc42234: mov             fp, SP
    // 0xc42238: mov             x2, x1
    // 0xc4223c: CheckStackOverflow
    //     0xc4223c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc42240: cmp             SP, x16
    //     0xc42244: b.ls            #0xc4225c
    // 0xc42248: r1 = Null
    //     0xc42248: mov             x1, NULL
    // 0xc4224c: r0 = Vector4.copy()
    //     0xc4224c: bl              #0xc42264  ; [package:vector_math/vector_math.dart] Vector4::Vector4.copy
    // 0xc42250: LeaveFrame
    //     0xc42250: mov             SP, fp
    //     0xc42254: ldp             fp, lr, [SP], #0x10
    // 0xc42258: ret
    //     0xc42258: ret             
    // 0xc4225c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4225c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc42260: b               #0xc42248
  }
  factory _ Vector4.copy(/* No info */) {
    // ** addr: 0xc42264, size: 0x80
    // 0xc42264: EnterFrame
    //     0xc42264: stp             fp, lr, [SP, #-0x10]!
    //     0xc42268: mov             fp, SP
    // 0xc4226c: AllocStack(0x10)
    //     0xc4226c: sub             SP, SP, #0x10
    // 0xc42270: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xc42270: stur            x2, [fp, #-8]
    // 0xc42274: r0 = Vector4()
    //     0xc42274: bl              #0xc42710  ; AllocateVector4Stub -> Vector4 (size=0xc)
    // 0xc42278: r4 = 8
    //     0xc42278: movz            x4, #0x8
    // 0xc4227c: stur            x0, [fp, #-0x10]
    // 0xc42280: r0 = AllocateFloat32Array()
    //     0xc42280: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0xc42284: mov             x3, x0
    // 0xc42288: ldur            x2, [fp, #-0x10]
    // 0xc4228c: StoreField: r2->field_7 = r3
    //     0xc4228c: stur            w3, [x2, #7]
    // 0xc42290: ldur            x4, [fp, #-8]
    // 0xc42294: LoadField: r5 = r4->field_7
    //     0xc42294: ldur            w5, [x4, #7]
    // 0xc42298: DecompressPointer r5
    //     0xc42298: add             x5, x5, HEAP, lsl #32
    // 0xc4229c: LoadField: r4 = r5->field_13
    //     0xc4229c: ldur            w4, [x5, #0x13]
    // 0xc422a0: r0 = LoadInt32Instr(r4)
    //     0xc422a0: sbfx            x0, x4, #1, #0x1f
    // 0xc422a4: r1 = 3
    //     0xc422a4: movz            x1, #0x3
    // 0xc422a8: cmp             x1, x0
    // 0xc422ac: b.hs            #0xc422e0
    // 0xc422b0: LoadField: d0 = r5->field_23
    //     0xc422b0: ldur            s0, [x5, #0x23]
    // 0xc422b4: StoreField: r3->field_23 = d0
    //     0xc422b4: stur            s0, [x3, #0x23]
    // 0xc422b8: LoadField: d0 = r5->field_1f
    //     0xc422b8: ldur            s0, [x5, #0x1f]
    // 0xc422bc: StoreField: r3->field_1f = d0
    //     0xc422bc: stur            s0, [x3, #0x1f]
    // 0xc422c0: LoadField: d0 = r5->field_1b
    //     0xc422c0: ldur            s0, [x5, #0x1b]
    // 0xc422c4: StoreField: r3->field_1b = d0
    //     0xc422c4: stur            s0, [x3, #0x1b]
    // 0xc422c8: ArrayLoad: d0 = r5[0]  ; List_8
    //     0xc422c8: ldur            s0, [x5, #0x17]
    // 0xc422cc: ArrayStore: r3[0] = d0  ; List_8
    //     0xc422cc: stur            s0, [x3, #0x17]
    // 0xc422d0: mov             x0, x2
    // 0xc422d4: LeaveFrame
    //     0xc422d4: mov             SP, fp
    //     0xc422d8: ldp             fp, lr, [SP], #0x10
    // 0xc422dc: ret
    //     0xc422dc: ret             
    // 0xc422e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc422e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  Vector4 +(Vector4, Vector4) {
    // ** addr: 0xc422fc, size: 0x84
    // 0xc422fc: EnterFrame
    //     0xc422fc: stp             fp, lr, [SP, #-0x10]!
    //     0xc42300: mov             fp, SP
    // 0xc42304: CheckStackOverflow
    //     0xc42304: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc42308: cmp             SP, x16
    //     0xc4230c: b.ls            #0xc42360
    // 0xc42310: ldr             x0, [fp, #0x10]
    // 0xc42314: r2 = Null
    //     0xc42314: mov             x2, NULL
    // 0xc42318: r1 = Null
    //     0xc42318: mov             x1, NULL
    // 0xc4231c: r4 = 60
    //     0xc4231c: movz            x4, #0x3c
    // 0xc42320: branchIfSmi(r0, 0xc4232c)
    //     0xc42320: tbz             w0, #0, #0xc4232c
    // 0xc42324: r4 = LoadClassIdInstr(r0)
    //     0xc42324: ldur            x4, [x0, #-1]
    //     0xc42328: ubfx            x4, x4, #0xc, #0x14
    // 0xc4232c: cmp             x4, #0x19a
    // 0xc42330: b.eq            #0xc42348
    // 0xc42334: r8 = Vector4
    //     0xc42334: add             x8, PP, #0x41, lsl #12  ; [pp+0x41b20] Type: Vector4
    //     0xc42338: ldr             x8, [x8, #0xb20]
    // 0xc4233c: r3 = Null
    //     0xc4233c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41b28] Null
    //     0xc42340: ldr             x3, [x3, #0xb28]
    // 0xc42344: r0 = DefaultTypeTest()
    //     0xc42344: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xc42348: ldr             x1, [fp, #0x18]
    // 0xc4234c: ldr             x2, [fp, #0x10]
    // 0xc42350: r0 = +()
    //     0xc42350: bl              #0xc42368  ; [package:vector_math/vector_math.dart] Vector4::+
    // 0xc42354: LeaveFrame
    //     0xc42354: mov             SP, fp
    //     0xc42358: ldp             fp, lr, [SP], #0x10
    // 0xc4235c: ret
    //     0xc4235c: ret             
    // 0xc42360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc42360: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc42364: b               #0xc42310
  }
  Vector4 +(Vector4, Vector4) {
    // ** addr: 0xc42368, size: 0x48
    // 0xc42368: EnterFrame
    //     0xc42368: stp             fp, lr, [SP, #-0x10]!
    //     0xc4236c: mov             fp, SP
    // 0xc42370: AllocStack(0x8)
    //     0xc42370: sub             SP, SP, #8
    // 0xc42374: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xc42374: stur            x2, [fp, #-8]
    // 0xc42378: CheckStackOverflow
    //     0xc42378: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4237c: cmp             SP, x16
    //     0xc42380: b.ls            #0xc423a8
    // 0xc42384: r0 = clone()
    //     0xc42384: bl              #0xc42230  ; [package:vector_math/vector_math.dart] Vector4::clone
    // 0xc42388: mov             x1, x0
    // 0xc4238c: ldur            x2, [fp, #-8]
    // 0xc42390: stur            x0, [fp, #-8]
    // 0xc42394: r0 = add()
    //     0xc42394: bl              #0xc423b0  ; [package:vector_math/vector_math.dart] Vector4::add
    // 0xc42398: ldur            x0, [fp, #-8]
    // 0xc4239c: LeaveFrame
    //     0xc4239c: mov             SP, fp
    //     0xc423a0: ldp             fp, lr, [SP], #0x10
    // 0xc423a4: ret
    //     0xc423a4: ret             
    // 0xc423a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc423a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc423ac: b               #0xc42384
  }
  _ add(/* No info */) {
    // ** addr: 0xc423b0, size: 0x148
    // 0xc423b0: EnterFrame
    //     0xc423b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc423b4: mov             fp, SP
    // 0xc423b8: LoadField: r3 = r2->field_7
    //     0xc423b8: ldur            w3, [x2, #7]
    // 0xc423bc: DecompressPointer r3
    //     0xc423bc: add             x3, x3, HEAP, lsl #32
    // 0xc423c0: LoadField: r2 = r1->field_7
    //     0xc423c0: ldur            w2, [x1, #7]
    // 0xc423c4: DecompressPointer r2
    //     0xc423c4: add             x2, x2, HEAP, lsl #32
    // 0xc423c8: LoadField: r4 = r2->field_13
    //     0xc423c8: ldur            w4, [x2, #0x13]
    // 0xc423cc: r5 = LoadInt32Instr(r4)
    //     0xc423cc: sbfx            x5, x4, #1, #0x1f
    // 0xc423d0: mov             x0, x5
    // 0xc423d4: r1 = 0
    //     0xc423d4: movz            x1, #0
    // 0xc423d8: cmp             x1, x0
    // 0xc423dc: b.hs            #0xc424d8
    // 0xc423e0: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xc423e0: ldur            s0, [x2, #0x17]
    // 0xc423e4: fcvt            d1, s0
    // 0xc423e8: LoadField: r4 = r3->field_13
    //     0xc423e8: ldur            w4, [x3, #0x13]
    // 0xc423ec: r6 = LoadInt32Instr(r4)
    //     0xc423ec: sbfx            x6, x4, #1, #0x1f
    // 0xc423f0: mov             x0, x6
    // 0xc423f4: r1 = 0
    //     0xc423f4: movz            x1, #0
    // 0xc423f8: cmp             x1, x0
    // 0xc423fc: b.hs            #0xc424dc
    // 0xc42400: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc42400: ldur            s0, [x3, #0x17]
    // 0xc42404: fcvt            d2, s0
    // 0xc42408: fadd            d0, d1, d2
    // 0xc4240c: fcvt            s1, d0
    // 0xc42410: ArrayStore: r2[0] = d1  ; List_8
    //     0xc42410: stur            s1, [x2, #0x17]
    // 0xc42414: mov             x0, x5
    // 0xc42418: r1 = 1
    //     0xc42418: movz            x1, #0x1
    // 0xc4241c: cmp             x1, x0
    // 0xc42420: b.hs            #0xc424e0
    // 0xc42424: LoadField: d0 = r2->field_1b
    //     0xc42424: ldur            s0, [x2, #0x1b]
    // 0xc42428: fcvt            d1, s0
    // 0xc4242c: mov             x0, x6
    // 0xc42430: r1 = 1
    //     0xc42430: movz            x1, #0x1
    // 0xc42434: cmp             x1, x0
    // 0xc42438: b.hs            #0xc424e4
    // 0xc4243c: LoadField: d0 = r3->field_1b
    //     0xc4243c: ldur            s0, [x3, #0x1b]
    // 0xc42440: fcvt            d2, s0
    // 0xc42444: fadd            d0, d1, d2
    // 0xc42448: fcvt            s1, d0
    // 0xc4244c: StoreField: r2->field_1b = d1
    //     0xc4244c: stur            s1, [x2, #0x1b]
    // 0xc42450: mov             x0, x5
    // 0xc42454: r1 = 2
    //     0xc42454: movz            x1, #0x2
    // 0xc42458: cmp             x1, x0
    // 0xc4245c: b.hs            #0xc424e8
    // 0xc42460: LoadField: d0 = r2->field_1f
    //     0xc42460: ldur            s0, [x2, #0x1f]
    // 0xc42464: fcvt            d1, s0
    // 0xc42468: mov             x0, x6
    // 0xc4246c: r1 = 2
    //     0xc4246c: movz            x1, #0x2
    // 0xc42470: cmp             x1, x0
    // 0xc42474: b.hs            #0xc424ec
    // 0xc42478: LoadField: d0 = r3->field_1f
    //     0xc42478: ldur            s0, [x3, #0x1f]
    // 0xc4247c: fcvt            d2, s0
    // 0xc42480: fadd            d0, d1, d2
    // 0xc42484: fcvt            s1, d0
    // 0xc42488: StoreField: r2->field_1f = d1
    //     0xc42488: stur            s1, [x2, #0x1f]
    // 0xc4248c: mov             x0, x5
    // 0xc42490: r1 = 3
    //     0xc42490: movz            x1, #0x3
    // 0xc42494: cmp             x1, x0
    // 0xc42498: b.hs            #0xc424f0
    // 0xc4249c: LoadField: d0 = r2->field_23
    //     0xc4249c: ldur            s0, [x2, #0x23]
    // 0xc424a0: fcvt            d1, s0
    // 0xc424a4: mov             x0, x6
    // 0xc424a8: r1 = 3
    //     0xc424a8: movz            x1, #0x3
    // 0xc424ac: cmp             x1, x0
    // 0xc424b0: b.hs            #0xc424f4
    // 0xc424b4: LoadField: d0 = r3->field_23
    //     0xc424b4: ldur            s0, [x3, #0x23]
    // 0xc424b8: fcvt            d2, s0
    // 0xc424bc: fadd            d0, d1, d2
    // 0xc424c0: fcvt            s1, d0
    // 0xc424c4: StoreField: r2->field_23 = d1
    //     0xc424c4: stur            s1, [x2, #0x23]
    // 0xc424c8: r0 = Null
    //     0xc424c8: mov             x0, NULL
    // 0xc424cc: LeaveFrame
    //     0xc424cc: mov             SP, fp
    //     0xc424d0: ldp             fp, lr, [SP], #0x10
    // 0xc424d4: ret
    //     0xc424d4: ret             
    // 0xc424d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc424d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc424dc: r0 = RangeErrorSharedWithFPURegs()
    //     0xc424dc: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc424e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc424e0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc424e4: r0 = RangeErrorSharedWithFPURegs()
    //     0xc424e4: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc424e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc424e8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc424ec: r0 = RangeErrorSharedWithFPURegs()
    //     0xc424ec: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc424f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc424f0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc424f4: r0 = RangeErrorSharedWithFPURegs()
    //     0xc424f4: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  double [](Vector4, int) {
    // ** addr: 0xc42510, size: 0xd8
    // 0xc42510: EnterFrame
    //     0xc42510: stp             fp, lr, [SP, #-0x10]!
    //     0xc42514: mov             fp, SP
    // 0xc42518: ldr             x0, [fp, #0x10]
    // 0xc4251c: r2 = Null
    //     0xc4251c: mov             x2, NULL
    // 0xc42520: r1 = Null
    //     0xc42520: mov             x1, NULL
    // 0xc42524: branchIfSmi(r0, 0xc4254c)
    //     0xc42524: tbz             w0, #0, #0xc4254c
    // 0xc42528: r4 = LoadClassIdInstr(r0)
    //     0xc42528: ldur            x4, [x0, #-1]
    //     0xc4252c: ubfx            x4, x4, #0xc, #0x14
    // 0xc42530: sub             x4, x4, #0x3c
    // 0xc42534: cmp             x4, #1
    // 0xc42538: b.ls            #0xc4254c
    // 0xc4253c: r8 = int
    //     0xc4253c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xc42540: r3 = Null
    //     0xc42540: add             x3, PP, #0x41, lsl #12  ; [pp+0x41b10] Null
    //     0xc42544: ldr             x3, [x3, #0xb10]
    // 0xc42548: r0 = int()
    //     0xc42548: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xc4254c: ldr             x2, [fp, #0x18]
    // 0xc42550: LoadField: r3 = r2->field_7
    //     0xc42550: ldur            w3, [x2, #7]
    // 0xc42554: DecompressPointer r3
    //     0xc42554: add             x3, x3, HEAP, lsl #32
    // 0xc42558: LoadField: r2 = r3->field_13
    //     0xc42558: ldur            w2, [x3, #0x13]
    // 0xc4255c: ldr             x4, [fp, #0x10]
    // 0xc42560: r5 = LoadInt32Instr(r4)
    //     0xc42560: sbfx            x5, x4, #1, #0x1f
    //     0xc42564: tbz             w4, #0, #0xc4256c
    //     0xc42568: ldur            x5, [x4, #7]
    // 0xc4256c: r0 = LoadInt32Instr(r2)
    //     0xc4256c: sbfx            x0, x2, #1, #0x1f
    // 0xc42570: mov             x1, x5
    // 0xc42574: cmp             x1, x0
    // 0xc42578: b.hs            #0xc425bc
    // 0xc4257c: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0xc4257c: add             x16, x3, x5, lsl #2
    //     0xc42580: ldur            s0, [x16, #0x17]
    // 0xc42584: fcvt            d1, s0
    // 0xc42588: r0 = inline_Allocate_Double()
    //     0xc42588: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc4258c: add             x0, x0, #0x10
    //     0xc42590: cmp             x1, x0
    //     0xc42594: b.ls            #0xc425c0
    //     0xc42598: str             x0, [THR, #0x50]  ; THR::top
    //     0xc4259c: sub             x0, x0, #0xf
    //     0xc425a0: movz            x1, #0xe15c
    //     0xc425a4: movk            x1, #0x3, lsl #16
    //     0xc425a8: stur            x1, [x0, #-1]
    // 0xc425ac: StoreField: r0->field_7 = d1
    //     0xc425ac: stur            d1, [x0, #7]
    // 0xc425b0: LeaveFrame
    //     0xc425b0: mov             SP, fp
    //     0xc425b4: ldp             fp, lr, [SP], #0x10
    // 0xc425b8: ret
    //     0xc425b8: ret             
    // 0xc425bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc425bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc425c0: SaveReg d1
    //     0xc425c0: str             q1, [SP, #-0x10]!
    // 0xc425c4: r0 = AllocateDouble()
    //     0xc425c4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc425c8: RestoreReg d1
    //     0xc425c8: ldr             q1, [SP], #0x10
    // 0xc425cc: b               #0xc425ac
  }
  Vector4 *(Vector4, double) {
    // ** addr: 0xc425e8, size: 0x50
    // 0xc425e8: EnterFrame
    //     0xc425e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc425ec: mov             fp, SP
    // 0xc425f0: CheckStackOverflow
    //     0xc425f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc425f4: cmp             SP, x16
    //     0xc425f8: b.ls            #0xc42618
    // 0xc425fc: ldr             x0, [fp, #0x10]
    // 0xc42600: LoadField: d0 = r0->field_7
    //     0xc42600: ldur            d0, [x0, #7]
    // 0xc42604: ldr             x1, [fp, #0x18]
    // 0xc42608: r0 = *()
    //     0xc42608: bl              #0xc42620  ; [package:vector_math/vector_math.dart] Vector4::*
    // 0xc4260c: LeaveFrame
    //     0xc4260c: mov             SP, fp
    //     0xc42610: ldp             fp, lr, [SP], #0x10
    // 0xc42614: ret
    //     0xc42614: ret             
    // 0xc42618: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc42618: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4261c: b               #0xc425fc
  }
  Vector4 *(Vector4, double) {
    // ** addr: 0xc42620, size: 0xf0
    // 0xc42620: EnterFrame
    //     0xc42620: stp             fp, lr, [SP, #-0x10]!
    //     0xc42624: mov             fp, SP
    // 0xc42628: AllocStack(0x8)
    //     0xc42628: sub             SP, SP, #8
    // 0xc4262c: SetupParameters(dynamic _ /* d0 => d0, fp-0x8 */)
    //     0xc4262c: stur            d0, [fp, #-8]
    // 0xc42630: CheckStackOverflow
    //     0xc42630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc42634: cmp             SP, x16
    //     0xc42638: b.ls            #0xc426f8
    // 0xc4263c: r0 = clone()
    //     0xc4263c: bl              #0xc42230  ; [package:vector_math/vector_math.dart] Vector4::clone
    // 0xc42640: mov             x2, x0
    // 0xc42644: LoadField: r3 = r2->field_7
    //     0xc42644: ldur            w3, [x2, #7]
    // 0xc42648: DecompressPointer r3
    //     0xc42648: add             x3, x3, HEAP, lsl #32
    // 0xc4264c: LoadField: r4 = r3->field_13
    //     0xc4264c: ldur            w4, [x3, #0x13]
    // 0xc42650: r5 = LoadInt32Instr(r4)
    //     0xc42650: sbfx            x5, x4, #1, #0x1f
    // 0xc42654: mov             x0, x5
    // 0xc42658: r1 = 0
    //     0xc42658: movz            x1, #0
    // 0xc4265c: cmp             x1, x0
    // 0xc42660: b.hs            #0xc42700
    // 0xc42664: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc42664: ldur            s0, [x3, #0x17]
    // 0xc42668: fcvt            d1, s0
    // 0xc4266c: ldur            d0, [fp, #-8]
    // 0xc42670: fmul            d2, d1, d0
    // 0xc42674: fcvt            s1, d2
    // 0xc42678: ArrayStore: r3[0] = d1  ; List_8
    //     0xc42678: stur            s1, [x3, #0x17]
    // 0xc4267c: mov             x0, x5
    // 0xc42680: r1 = 1
    //     0xc42680: movz            x1, #0x1
    // 0xc42684: cmp             x1, x0
    // 0xc42688: b.hs            #0xc42704
    // 0xc4268c: LoadField: d1 = r3->field_1b
    //     0xc4268c: ldur            s1, [x3, #0x1b]
    // 0xc42690: fcvt            d2, s1
    // 0xc42694: fmul            d1, d2, d0
    // 0xc42698: fcvt            s2, d1
    // 0xc4269c: StoreField: r3->field_1b = d2
    //     0xc4269c: stur            s2, [x3, #0x1b]
    // 0xc426a0: mov             x0, x5
    // 0xc426a4: r1 = 2
    //     0xc426a4: movz            x1, #0x2
    // 0xc426a8: cmp             x1, x0
    // 0xc426ac: b.hs            #0xc42708
    // 0xc426b0: LoadField: d1 = r3->field_1f
    //     0xc426b0: ldur            s1, [x3, #0x1f]
    // 0xc426b4: fcvt            d2, s1
    // 0xc426b8: fmul            d1, d2, d0
    // 0xc426bc: fcvt            s2, d1
    // 0xc426c0: StoreField: r3->field_1f = d2
    //     0xc426c0: stur            s2, [x3, #0x1f]
    // 0xc426c4: mov             x0, x5
    // 0xc426c8: r1 = 3
    //     0xc426c8: movz            x1, #0x3
    // 0xc426cc: cmp             x1, x0
    // 0xc426d0: b.hs            #0xc4270c
    // 0xc426d4: LoadField: d1 = r3->field_23
    //     0xc426d4: ldur            s1, [x3, #0x23]
    // 0xc426d8: fcvt            d2, s1
    // 0xc426dc: fmul            d1, d2, d0
    // 0xc426e0: fcvt            s0, d1
    // 0xc426e4: StoreField: r3->field_23 = d0
    //     0xc426e4: stur            s0, [x3, #0x23]
    // 0xc426e8: mov             x0, x2
    // 0xc426ec: LeaveFrame
    //     0xc426ec: mov             SP, fp
    //     0xc426f0: ldp             fp, lr, [SP], #0x10
    // 0xc426f4: ret
    //     0xc426f4: ret             
    // 0xc426f8: r0 = StackOverflowSharedWithFPURegs()
    //     0xc426f8: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xc426fc: b               #0xc4263c
    // 0xc42700: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42700: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc42704: r0 = RangeErrorSharedWithFPURegs()
    //     0xc42704: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc42708: r0 = RangeErrorSharedWithFPURegs()
    //     0xc42708: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xc4270c: r0 = RangeErrorSharedWithFPURegs()
    //     0xc4270c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4271c, size: 0x210
    // 0xc4271c: EnterFrame
    //     0xc4271c: stp             fp, lr, [SP, #-0x10]!
    //     0xc42720: mov             fp, SP
    // 0xc42724: AllocStack(0x20)
    //     0xc42724: sub             SP, SP, #0x20
    // 0xc42728: CheckStackOverflow
    //     0xc42728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4272c: cmp             SP, x16
    //     0xc42730: b.ls            #0xc428b4
    // 0xc42734: ldr             x0, [fp, #0x10]
    // 0xc42738: LoadField: r3 = r0->field_7
    //     0xc42738: ldur            w3, [x0, #7]
    // 0xc4273c: DecompressPointer r3
    //     0xc4273c: add             x3, x3, HEAP, lsl #32
    // 0xc42740: stur            x3, [fp, #-0x18]
    // 0xc42744: LoadField: r0 = r3->field_13
    //     0xc42744: ldur            w0, [x3, #0x13]
    // 0xc42748: r4 = LoadInt32Instr(r0)
    //     0xc42748: sbfx            x4, x0, #1, #0x1f
    // 0xc4274c: mov             x0, x4
    // 0xc42750: stur            x4, [fp, #-0x10]
    // 0xc42754: r1 = 0
    //     0xc42754: movz            x1, #0
    // 0xc42758: cmp             x1, x0
    // 0xc4275c: b.hs            #0xc428bc
    // 0xc42760: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xc42760: ldur            s0, [x3, #0x17]
    // 0xc42764: fcvt            d1, s0
    // 0xc42768: r0 = inline_Allocate_Double()
    //     0xc42768: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc4276c: add             x0, x0, #0x10
    //     0xc42770: cmp             x1, x0
    //     0xc42774: b.ls            #0xc428c0
    //     0xc42778: str             x0, [THR, #0x50]  ; THR::top
    //     0xc4277c: sub             x0, x0, #0xf
    //     0xc42780: movz            x1, #0xe15c
    //     0xc42784: movk            x1, #0x3, lsl #16
    //     0xc42788: stur            x1, [x0, #-1]
    // 0xc4278c: StoreField: r0->field_7 = d1
    //     0xc4278c: stur            d1, [x0, #7]
    // 0xc42790: stur            x0, [fp, #-8]
    // 0xc42794: r1 = Null
    //     0xc42794: mov             x1, NULL
    // 0xc42798: r2 = 14
    //     0xc42798: movz            x2, #0xe
    // 0xc4279c: r0 = AllocateArray()
    //     0xc4279c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc427a0: mov             x2, x0
    // 0xc427a4: ldur            x0, [fp, #-8]
    // 0xc427a8: StoreField: r2->field_f = r0
    //     0xc427a8: stur            w0, [x2, #0xf]
    // 0xc427ac: r16 = ","
    //     0xc427ac: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xc427b0: ldr             x16, [x16, #0x5f8]
    // 0xc427b4: StoreField: r2->field_13 = r16
    //     0xc427b4: stur            w16, [x2, #0x13]
    // 0xc427b8: ldur            x0, [fp, #-0x10]
    // 0xc427bc: r1 = 1
    //     0xc427bc: movz            x1, #0x1
    // 0xc427c0: cmp             x1, x0
    // 0xc427c4: b.hs            #0xc428d8
    // 0xc427c8: ldur            x3, [fp, #-0x18]
    // 0xc427cc: LoadField: d0 = r3->field_1b
    //     0xc427cc: ldur            s0, [x3, #0x1b]
    // 0xc427d0: fcvt            d1, s0
    // 0xc427d4: r0 = inline_Allocate_Double()
    //     0xc427d4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc427d8: add             x0, x0, #0x10
    //     0xc427dc: cmp             x1, x0
    //     0xc427e0: b.ls            #0xc428dc
    //     0xc427e4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc427e8: sub             x0, x0, #0xf
    //     0xc427ec: movz            x1, #0xe15c
    //     0xc427f0: movk            x1, #0x3, lsl #16
    //     0xc427f4: stur            x1, [x0, #-1]
    // 0xc427f8: StoreField: r0->field_7 = d1
    //     0xc427f8: stur            d1, [x0, #7]
    // 0xc427fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xc427fc: stur            w0, [x2, #0x17]
    // 0xc42800: r16 = ","
    //     0xc42800: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xc42804: ldr             x16, [x16, #0x5f8]
    // 0xc42808: StoreField: r2->field_1b = r16
    //     0xc42808: stur            w16, [x2, #0x1b]
    // 0xc4280c: ldur            x0, [fp, #-0x10]
    // 0xc42810: r1 = 2
    //     0xc42810: movz            x1, #0x2
    // 0xc42814: cmp             x1, x0
    // 0xc42818: b.hs            #0xc428f4
    // 0xc4281c: LoadField: d0 = r3->field_1f
    //     0xc4281c: ldur            s0, [x3, #0x1f]
    // 0xc42820: fcvt            d1, s0
    // 0xc42824: r0 = inline_Allocate_Double()
    //     0xc42824: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc42828: add             x0, x0, #0x10
    //     0xc4282c: cmp             x1, x0
    //     0xc42830: b.ls            #0xc428f8
    //     0xc42834: str             x0, [THR, #0x50]  ; THR::top
    //     0xc42838: sub             x0, x0, #0xf
    //     0xc4283c: movz            x1, #0xe15c
    //     0xc42840: movk            x1, #0x3, lsl #16
    //     0xc42844: stur            x1, [x0, #-1]
    // 0xc42848: StoreField: r0->field_7 = d1
    //     0xc42848: stur            d1, [x0, #7]
    // 0xc4284c: StoreField: r2->field_1f = r0
    //     0xc4284c: stur            w0, [x2, #0x1f]
    // 0xc42850: r16 = ","
    //     0xc42850: add             x16, PP, #0xd, lsl #12  ; [pp+0xd5f8] ","
    //     0xc42854: ldr             x16, [x16, #0x5f8]
    // 0xc42858: StoreField: r2->field_23 = r16
    //     0xc42858: stur            w16, [x2, #0x23]
    // 0xc4285c: ldur            x0, [fp, #-0x10]
    // 0xc42860: r1 = 3
    //     0xc42860: movz            x1, #0x3
    // 0xc42864: cmp             x1, x0
    // 0xc42868: b.hs            #0xc42910
    // 0xc4286c: LoadField: d0 = r3->field_23
    //     0xc4286c: ldur            s0, [x3, #0x23]
    // 0xc42870: fcvt            d1, s0
    // 0xc42874: r0 = inline_Allocate_Double()
    //     0xc42874: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc42878: add             x0, x0, #0x10
    //     0xc4287c: cmp             x1, x0
    //     0xc42880: b.ls            #0xc42914
    //     0xc42884: str             x0, [THR, #0x50]  ; THR::top
    //     0xc42888: sub             x0, x0, #0xf
    //     0xc4288c: movz            x1, #0xe15c
    //     0xc42890: movk            x1, #0x3, lsl #16
    //     0xc42894: stur            x1, [x0, #-1]
    // 0xc42898: StoreField: r0->field_7 = d1
    //     0xc42898: stur            d1, [x0, #7]
    // 0xc4289c: StoreField: r2->field_27 = r0
    //     0xc4289c: stur            w0, [x2, #0x27]
    // 0xc428a0: str             x2, [SP]
    // 0xc428a4: r0 = _interpolate()
    //     0xc428a4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc428a8: LeaveFrame
    //     0xc428a8: mov             SP, fp
    //     0xc428ac: ldp             fp, lr, [SP], #0x10
    // 0xc428b0: ret
    //     0xc428b0: ret             
    // 0xc428b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc428b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc428b8: b               #0xc42734
    // 0xc428bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc428bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc428c0: SaveReg d1
    //     0xc428c0: str             q1, [SP, #-0x10]!
    // 0xc428c4: stp             x3, x4, [SP, #-0x10]!
    // 0xc428c8: r0 = AllocateDouble()
    //     0xc428c8: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc428cc: ldp             x3, x4, [SP], #0x10
    // 0xc428d0: RestoreReg d1
    //     0xc428d0: ldr             q1, [SP], #0x10
    // 0xc428d4: b               #0xc4278c
    // 0xc428d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc428d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc428dc: SaveReg d1
    //     0xc428dc: str             q1, [SP, #-0x10]!
    // 0xc428e0: stp             x2, x3, [SP, #-0x10]!
    // 0xc428e4: r0 = AllocateDouble()
    //     0xc428e4: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc428e8: ldp             x2, x3, [SP], #0x10
    // 0xc428ec: RestoreReg d1
    //     0xc428ec: ldr             q1, [SP], #0x10
    // 0xc428f0: b               #0xc427f8
    // 0xc428f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc428f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc428f8: SaveReg d1
    //     0xc428f8: str             q1, [SP, #-0x10]!
    // 0xc428fc: stp             x2, x3, [SP, #-0x10]!
    // 0xc42900: r0 = AllocateDouble()
    //     0xc42900: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc42904: ldp             x2, x3, [SP], #0x10
    // 0xc42908: RestoreReg d1
    //     0xc42908: ldr             q1, [SP], #0x10
    // 0xc4290c: b               #0xc42848
    // 0xc42910: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42910: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc42914: SaveReg d1
    //     0xc42914: str             q1, [SP, #-0x10]!
    // 0xc42918: SaveReg r2
    //     0xc42918: str             x2, [SP, #-8]!
    // 0xc4291c: r0 = AllocateDouble()
    //     0xc4291c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc42920: RestoreReg r2
    //     0xc42920: ldr             x2, [SP], #8
    // 0xc42924: RestoreReg d1
    //     0xc42924: ldr             q1, [SP], #0x10
    // 0xc42928: b               #0xc42898
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7e834, size: 0x180
    // 0xd7e834: EnterFrame
    //     0xd7e834: stp             fp, lr, [SP, #-0x10]!
    //     0xd7e838: mov             fp, SP
    // 0xd7e83c: ldr             x2, [fp, #0x10]
    // 0xd7e840: cmp             w2, NULL
    // 0xd7e844: b.ne            #0xd7e858
    // 0xd7e848: r0 = false
    //     0xd7e848: add             x0, NULL, #0x30  ; false
    // 0xd7e84c: LeaveFrame
    //     0xd7e84c: mov             SP, fp
    //     0xd7e850: ldp             fp, lr, [SP], #0x10
    // 0xd7e854: ret
    //     0xd7e854: ret             
    // 0xd7e858: r3 = 60
    //     0xd7e858: movz            x3, #0x3c
    // 0xd7e85c: branchIfSmi(r2, 0xd7e868)
    //     0xd7e85c: tbz             w2, #0, #0xd7e868
    // 0xd7e860: r3 = LoadClassIdInstr(r2)
    //     0xd7e860: ldur            x3, [x2, #-1]
    //     0xd7e864: ubfx            x3, x3, #0xc, #0x14
    // 0xd7e868: cmp             x3, #0x19a
    // 0xd7e86c: b.ne            #0xd7e984
    // 0xd7e870: ldr             x3, [fp, #0x18]
    // 0xd7e874: LoadField: r4 = r3->field_7
    //     0xd7e874: ldur            w4, [x3, #7]
    // 0xd7e878: DecompressPointer r4
    //     0xd7e878: add             x4, x4, HEAP, lsl #32
    // 0xd7e87c: LoadField: r3 = r4->field_13
    //     0xd7e87c: ldur            w3, [x4, #0x13]
    // 0xd7e880: r5 = LoadInt32Instr(r3)
    //     0xd7e880: sbfx            x5, x3, #1, #0x1f
    // 0xd7e884: mov             x0, x5
    // 0xd7e888: r1 = 0
    //     0xd7e888: movz            x1, #0
    // 0xd7e88c: cmp             x1, x0
    // 0xd7e890: b.hs            #0xd7e994
    // 0xd7e894: ArrayLoad: d0 = r4[0]  ; List_8
    //     0xd7e894: ldur            s0, [x4, #0x17]
    // 0xd7e898: fcvt            d1, s0
    // 0xd7e89c: LoadField: r3 = r2->field_7
    //     0xd7e89c: ldur            w3, [x2, #7]
    // 0xd7e8a0: DecompressPointer r3
    //     0xd7e8a0: add             x3, x3, HEAP, lsl #32
    // 0xd7e8a4: LoadField: r2 = r3->field_13
    //     0xd7e8a4: ldur            w2, [x3, #0x13]
    // 0xd7e8a8: r6 = LoadInt32Instr(r2)
    //     0xd7e8a8: sbfx            x6, x2, #1, #0x1f
    // 0xd7e8ac: mov             x0, x6
    // 0xd7e8b0: r1 = 0
    //     0xd7e8b0: movz            x1, #0
    // 0xd7e8b4: cmp             x1, x0
    // 0xd7e8b8: b.hs            #0xd7e998
    // 0xd7e8bc: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xd7e8bc: ldur            s0, [x3, #0x17]
    // 0xd7e8c0: fcvt            d2, s0
    // 0xd7e8c4: fcmp            d1, d2
    // 0xd7e8c8: b.ne            #0xd7e984
    // 0xd7e8cc: mov             x0, x5
    // 0xd7e8d0: r1 = 1
    //     0xd7e8d0: movz            x1, #0x1
    // 0xd7e8d4: cmp             x1, x0
    // 0xd7e8d8: b.hs            #0xd7e99c
    // 0xd7e8dc: LoadField: d0 = r4->field_1b
    //     0xd7e8dc: ldur            s0, [x4, #0x1b]
    // 0xd7e8e0: fcvt            d1, s0
    // 0xd7e8e4: mov             x0, x6
    // 0xd7e8e8: r1 = 1
    //     0xd7e8e8: movz            x1, #0x1
    // 0xd7e8ec: cmp             x1, x0
    // 0xd7e8f0: b.hs            #0xd7e9a0
    // 0xd7e8f4: LoadField: d0 = r3->field_1b
    //     0xd7e8f4: ldur            s0, [x3, #0x1b]
    // 0xd7e8f8: fcvt            d2, s0
    // 0xd7e8fc: fcmp            d1, d2
    // 0xd7e900: b.ne            #0xd7e984
    // 0xd7e904: mov             x0, x5
    // 0xd7e908: r1 = 2
    //     0xd7e908: movz            x1, #0x2
    // 0xd7e90c: cmp             x1, x0
    // 0xd7e910: b.hs            #0xd7e9a4
    // 0xd7e914: LoadField: d0 = r4->field_1f
    //     0xd7e914: ldur            s0, [x4, #0x1f]
    // 0xd7e918: fcvt            d1, s0
    // 0xd7e91c: mov             x0, x6
    // 0xd7e920: r1 = 2
    //     0xd7e920: movz            x1, #0x2
    // 0xd7e924: cmp             x1, x0
    // 0xd7e928: b.hs            #0xd7e9a8
    // 0xd7e92c: LoadField: d0 = r3->field_1f
    //     0xd7e92c: ldur            s0, [x3, #0x1f]
    // 0xd7e930: fcvt            d2, s0
    // 0xd7e934: fcmp            d1, d2
    // 0xd7e938: b.ne            #0xd7e984
    // 0xd7e93c: mov             x0, x5
    // 0xd7e940: r1 = 3
    //     0xd7e940: movz            x1, #0x3
    // 0xd7e944: cmp             x1, x0
    // 0xd7e948: b.hs            #0xd7e9ac
    // 0xd7e94c: LoadField: d0 = r4->field_23
    //     0xd7e94c: ldur            s0, [x4, #0x23]
    // 0xd7e950: fcvt            d1, s0
    // 0xd7e954: mov             x0, x6
    // 0xd7e958: r1 = 3
    //     0xd7e958: movz            x1, #0x3
    // 0xd7e95c: cmp             x1, x0
    // 0xd7e960: b.hs            #0xd7e9b0
    // 0xd7e964: LoadField: d0 = r3->field_23
    //     0xd7e964: ldur            s0, [x3, #0x23]
    // 0xd7e968: fcvt            d2, s0
    // 0xd7e96c: fcmp            d1, d2
    // 0xd7e970: r16 = true
    //     0xd7e970: add             x16, NULL, #0x20  ; true
    // 0xd7e974: r17 = false
    //     0xd7e974: add             x17, NULL, #0x30  ; false
    // 0xd7e978: csel            x1, x16, x17, eq
    // 0xd7e97c: mov             x0, x1
    // 0xd7e980: b               #0xd7e988
    // 0xd7e984: r0 = false
    //     0xd7e984: add             x0, NULL, #0x30  ; false
    // 0xd7e988: LeaveFrame
    //     0xd7e988: mov             SP, fp
    //     0xd7e98c: ldp             fp, lr, [SP], #0x10
    // 0xd7e990: ret
    //     0xd7e990: ret             
    // 0xd7e994: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e994: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e998: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e998: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e99c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e99c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e9a0: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e9a0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e9a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e9a4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e9a8: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e9a8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e9ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e9ac: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e9b0: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e9b0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
}

// class id: 412, size: 0x8, field offset: 0x8
abstract class Vector extends Object {
}

// class id: 413, size: 0xc, field offset: 0x8
class Matrix4 extends Object {

  _ scale(/* No info */) {
    // ** addr: 0xac539c, size: 0x28c
    // 0xac539c: EnterFrame
    //     0xac539c: stp             fp, lr, [SP, #-0x10]!
    //     0xac53a0: mov             fp, SP
    // 0xac53a4: cmp             w2, NULL
    // 0xac53a8: b.ne            #0xac53b4
    // 0xac53ac: mov             v1.16b, v0.16b
    // 0xac53b0: b               #0xac53b8
    // 0xac53b4: LoadField: d1 = r2->field_7
    //     0xac53b4: ldur            d1, [x2, #7]
    // 0xac53b8: LoadField: r2 = r1->field_7
    //     0xac53b8: ldur            w2, [x1, #7]
    // 0xac53bc: DecompressPointer r2
    //     0xac53bc: add             x2, x2, HEAP, lsl #32
    // 0xac53c0: LoadField: r3 = r2->field_13
    //     0xac53c0: ldur            w3, [x2, #0x13]
    // 0xac53c4: r4 = LoadInt32Instr(r3)
    //     0xac53c4: sbfx            x4, x3, #1, #0x1f
    // 0xac53c8: mov             x0, x4
    // 0xac53cc: r1 = 0
    //     0xac53cc: movz            x1, #0
    // 0xac53d0: cmp             x1, x0
    // 0xac53d4: b.hs            #0xac55e8
    // 0xac53d8: ArrayLoad: d2 = r2[0]  ; List_8
    //     0xac53d8: ldur            s2, [x2, #0x17]
    // 0xac53dc: fcvt            d3, s2
    // 0xac53e0: fmul            d2, d3, d0
    // 0xac53e4: fcvt            s3, d2
    // 0xac53e8: ArrayStore: r2[0] = d3  ; List_8
    //     0xac53e8: stur            s3, [x2, #0x17]
    // 0xac53ec: mov             x0, x4
    // 0xac53f0: r1 = 1
    //     0xac53f0: movz            x1, #0x1
    // 0xac53f4: cmp             x1, x0
    // 0xac53f8: b.hs            #0xac55ec
    // 0xac53fc: LoadField: d2 = r2->field_1b
    //     0xac53fc: ldur            s2, [x2, #0x1b]
    // 0xac5400: fcvt            d3, s2
    // 0xac5404: fmul            d2, d3, d0
    // 0xac5408: fcvt            s3, d2
    // 0xac540c: StoreField: r2->field_1b = d3
    //     0xac540c: stur            s3, [x2, #0x1b]
    // 0xac5410: mov             x0, x4
    // 0xac5414: r1 = 2
    //     0xac5414: movz            x1, #0x2
    // 0xac5418: cmp             x1, x0
    // 0xac541c: b.hs            #0xac55f0
    // 0xac5420: LoadField: d2 = r2->field_1f
    //     0xac5420: ldur            s2, [x2, #0x1f]
    // 0xac5424: fcvt            d3, s2
    // 0xac5428: fmul            d2, d3, d0
    // 0xac542c: fcvt            s3, d2
    // 0xac5430: StoreField: r2->field_1f = d3
    //     0xac5430: stur            s3, [x2, #0x1f]
    // 0xac5434: mov             x0, x4
    // 0xac5438: r1 = 3
    //     0xac5438: movz            x1, #0x3
    // 0xac543c: cmp             x1, x0
    // 0xac5440: b.hs            #0xac55f4
    // 0xac5444: LoadField: d2 = r2->field_23
    //     0xac5444: ldur            s2, [x2, #0x23]
    // 0xac5448: fcvt            d3, s2
    // 0xac544c: fmul            d2, d3, d0
    // 0xac5450: fcvt            s3, d2
    // 0xac5454: StoreField: r2->field_23 = d3
    //     0xac5454: stur            s3, [x2, #0x23]
    // 0xac5458: mov             x0, x4
    // 0xac545c: r1 = 4
    //     0xac545c: movz            x1, #0x4
    // 0xac5460: cmp             x1, x0
    // 0xac5464: b.hs            #0xac55f8
    // 0xac5468: LoadField: d2 = r2->field_27
    //     0xac5468: ldur            s2, [x2, #0x27]
    // 0xac546c: fcvt            d3, s2
    // 0xac5470: fmul            d2, d3, d1
    // 0xac5474: fcvt            s3, d2
    // 0xac5478: StoreField: r2->field_27 = d3
    //     0xac5478: stur            s3, [x2, #0x27]
    // 0xac547c: mov             x0, x4
    // 0xac5480: r1 = 5
    //     0xac5480: movz            x1, #0x5
    // 0xac5484: cmp             x1, x0
    // 0xac5488: b.hs            #0xac55fc
    // 0xac548c: LoadField: d2 = r2->field_2b
    //     0xac548c: ldur            s2, [x2, #0x2b]
    // 0xac5490: fcvt            d3, s2
    // 0xac5494: fmul            d2, d3, d1
    // 0xac5498: fcvt            s3, d2
    // 0xac549c: StoreField: r2->field_2b = d3
    //     0xac549c: stur            s3, [x2, #0x2b]
    // 0xac54a0: mov             x0, x4
    // 0xac54a4: r1 = 6
    //     0xac54a4: movz            x1, #0x6
    // 0xac54a8: cmp             x1, x0
    // 0xac54ac: b.hs            #0xac5600
    // 0xac54b0: LoadField: d2 = r2->field_2f
    //     0xac54b0: ldur            s2, [x2, #0x2f]
    // 0xac54b4: fcvt            d3, s2
    // 0xac54b8: fmul            d2, d3, d1
    // 0xac54bc: fcvt            s3, d2
    // 0xac54c0: StoreField: r2->field_2f = d3
    //     0xac54c0: stur            s3, [x2, #0x2f]
    // 0xac54c4: mov             x0, x4
    // 0xac54c8: r1 = 7
    //     0xac54c8: movz            x1, #0x7
    // 0xac54cc: cmp             x1, x0
    // 0xac54d0: b.hs            #0xac5604
    // 0xac54d4: LoadField: d2 = r2->field_33
    //     0xac54d4: ldur            s2, [x2, #0x33]
    // 0xac54d8: fcvt            d3, s2
    // 0xac54dc: fmul            d2, d3, d1
    // 0xac54e0: fcvt            s1, d2
    // 0xac54e4: StoreField: r2->field_33 = d1
    //     0xac54e4: stur            s1, [x2, #0x33]
    // 0xac54e8: mov             x0, x4
    // 0xac54ec: r1 = 8
    //     0xac54ec: movz            x1, #0x8
    // 0xac54f0: cmp             x1, x0
    // 0xac54f4: b.hs            #0xac5608
    // 0xac54f8: LoadField: d1 = r2->field_37
    //     0xac54f8: ldur            s1, [x2, #0x37]
    // 0xac54fc: fcvt            d2, s1
    // 0xac5500: fmul            d1, d2, d0
    // 0xac5504: fcvt            s2, d1
    // 0xac5508: StoreField: r2->field_37 = d2
    //     0xac5508: stur            s2, [x2, #0x37]
    // 0xac550c: mov             x0, x4
    // 0xac5510: r1 = 9
    //     0xac5510: movz            x1, #0x9
    // 0xac5514: cmp             x1, x0
    // 0xac5518: b.hs            #0xac560c
    // 0xac551c: LoadField: d1 = r2->field_3b
    //     0xac551c: ldur            s1, [x2, #0x3b]
    // 0xac5520: fcvt            d2, s1
    // 0xac5524: fmul            d1, d2, d0
    // 0xac5528: fcvt            s2, d1
    // 0xac552c: StoreField: r2->field_3b = d2
    //     0xac552c: stur            s2, [x2, #0x3b]
    // 0xac5530: mov             x0, x4
    // 0xac5534: r1 = 10
    //     0xac5534: movz            x1, #0xa
    // 0xac5538: cmp             x1, x0
    // 0xac553c: b.hs            #0xac5610
    // 0xac5540: LoadField: d1 = r2->field_3f
    //     0xac5540: ldur            s1, [x2, #0x3f]
    // 0xac5544: fcvt            d2, s1
    // 0xac5548: fmul            d1, d2, d0
    // 0xac554c: fcvt            s2, d1
    // 0xac5550: StoreField: r2->field_3f = d2
    //     0xac5550: stur            s2, [x2, #0x3f]
    // 0xac5554: mov             x0, x4
    // 0xac5558: r1 = 11
    //     0xac5558: movz            x1, #0xb
    // 0xac555c: cmp             x1, x0
    // 0xac5560: b.hs            #0xac5614
    // 0xac5564: LoadField: d1 = r2->field_43
    //     0xac5564: ldur            s1, [x2, #0x43]
    // 0xac5568: fcvt            d2, s1
    // 0xac556c: fmul            d1, d2, d0
    // 0xac5570: fcvt            s0, d1
    // 0xac5574: StoreField: r2->field_43 = d0
    //     0xac5574: stur            s0, [x2, #0x43]
    // 0xac5578: mov             x0, x4
    // 0xac557c: r1 = 12
    //     0xac557c: movz            x1, #0xc
    // 0xac5580: cmp             x1, x0
    // 0xac5584: b.hs            #0xac5618
    // 0xac5588: LoadField: d0 = r2->field_47
    //     0xac5588: ldur            s0, [x2, #0x47]
    // 0xac558c: StoreField: r2->field_47 = d0
    //     0xac558c: stur            s0, [x2, #0x47]
    // 0xac5590: mov             x0, x4
    // 0xac5594: r1 = 13
    //     0xac5594: movz            x1, #0xd
    // 0xac5598: cmp             x1, x0
    // 0xac559c: b.hs            #0xac561c
    // 0xac55a0: LoadField: d0 = r2->field_4b
    //     0xac55a0: ldur            s0, [x2, #0x4b]
    // 0xac55a4: StoreField: r2->field_4b = d0
    //     0xac55a4: stur            s0, [x2, #0x4b]
    // 0xac55a8: mov             x0, x4
    // 0xac55ac: r1 = 14
    //     0xac55ac: movz            x1, #0xe
    // 0xac55b0: cmp             x1, x0
    // 0xac55b4: b.hs            #0xac5620
    // 0xac55b8: LoadField: d0 = r2->field_4f
    //     0xac55b8: ldur            s0, [x2, #0x4f]
    // 0xac55bc: StoreField: r2->field_4f = d0
    //     0xac55bc: stur            s0, [x2, #0x4f]
    // 0xac55c0: mov             x0, x4
    // 0xac55c4: r1 = 15
    //     0xac55c4: movz            x1, #0xf
    // 0xac55c8: cmp             x1, x0
    // 0xac55cc: b.hs            #0xac5624
    // 0xac55d0: LoadField: d0 = r2->field_53
    //     0xac55d0: ldur            s0, [x2, #0x53]
    // 0xac55d4: StoreField: r2->field_53 = d0
    //     0xac55d4: stur            s0, [x2, #0x53]
    // 0xac55d8: r0 = Null
    //     0xac55d8: mov             x0, NULL
    // 0xac55dc: LeaveFrame
    //     0xac55dc: mov             SP, fp
    //     0xac55e0: ldp             fp, lr, [SP], #0x10
    // 0xac55e4: ret
    //     0xac55e4: ret             
    // 0xac55e8: r0 = RangeErrorSharedWithFPURegs()
    //     0xac55e8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac55ec: r0 = RangeErrorSharedWithFPURegs()
    //     0xac55ec: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac55f0: r0 = RangeErrorSharedWithFPURegs()
    //     0xac55f0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac55f4: r0 = RangeErrorSharedWithFPURegs()
    //     0xac55f4: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac55f8: r0 = RangeErrorSharedWithFPURegs()
    //     0xac55f8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac55fc: r0 = RangeErrorSharedWithFPURegs()
    //     0xac55fc: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5600: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5600: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5604: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5604: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5608: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5608: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac560c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac560c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5610: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5610: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5614: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5614: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5618: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5618: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac561c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac561c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5620: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5620: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5624: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5624: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  Matrix4 +(Matrix4, Matrix4) {
    // ** addr: 0xac5640, size: 0x84
    // 0xac5640: EnterFrame
    //     0xac5640: stp             fp, lr, [SP, #-0x10]!
    //     0xac5644: mov             fp, SP
    // 0xac5648: CheckStackOverflow
    //     0xac5648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac564c: cmp             SP, x16
    //     0xac5650: b.ls            #0xac56a4
    // 0xac5654: ldr             x0, [fp, #0x10]
    // 0xac5658: r2 = Null
    //     0xac5658: mov             x2, NULL
    // 0xac565c: r1 = Null
    //     0xac565c: mov             x1, NULL
    // 0xac5660: r4 = 60
    //     0xac5660: movz            x4, #0x3c
    // 0xac5664: branchIfSmi(r0, 0xac5670)
    //     0xac5664: tbz             w0, #0, #0xac5670
    // 0xac5668: r4 = LoadClassIdInstr(r0)
    //     0xac5668: ldur            x4, [x0, #-1]
    //     0xac566c: ubfx            x4, x4, #0xc, #0x14
    // 0xac5670: cmp             x4, #0x19d
    // 0xac5674: b.eq            #0xac568c
    // 0xac5678: r8 = Matrix4
    //     0xac5678: add             x8, PP, #0x38, lsl #12  ; [pp+0x38fa0] Type: Matrix4
    //     0xac567c: ldr             x8, [x8, #0xfa0]
    // 0xac5680: r3 = Null
    //     0xac5680: add             x3, PP, #0x38, lsl #12  ; [pp+0x38fb8] Null
    //     0xac5684: ldr             x3, [x3, #0xfb8]
    // 0xac5688: r0 = DefaultTypeTest()
    //     0xac5688: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xac568c: ldr             x1, [fp, #0x18]
    // 0xac5690: ldr             x2, [fp, #0x10]
    // 0xac5694: r0 = +()
    //     0xac5694: bl              #0xac56ac  ; [package:vector_math/vector_math.dart] Matrix4::+
    // 0xac5698: LeaveFrame
    //     0xac5698: mov             SP, fp
    //     0xac569c: ldp             fp, lr, [SP], #0x10
    // 0xac56a0: ret
    //     0xac56a0: ret             
    // 0xac56a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac56a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac56a8: b               #0xac5654
  }
  Matrix4 +(Matrix4, Matrix4) {
    // ** addr: 0xac56ac, size: 0x48
    // 0xac56ac: EnterFrame
    //     0xac56ac: stp             fp, lr, [SP, #-0x10]!
    //     0xac56b0: mov             fp, SP
    // 0xac56b4: AllocStack(0x8)
    //     0xac56b4: sub             SP, SP, #8
    // 0xac56b8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xac56b8: stur            x2, [fp, #-8]
    // 0xac56bc: CheckStackOverflow
    //     0xac56bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac56c0: cmp             SP, x16
    //     0xac56c4: b.ls            #0xac56ec
    // 0xac56c8: r0 = clone()
    //     0xac56c8: bl              #0xac5b6c  ; [package:vector_math/vector_math.dart] Matrix4::clone
    // 0xac56cc: mov             x1, x0
    // 0xac56d0: ldur            x2, [fp, #-8]
    // 0xac56d4: stur            x0, [fp, #-8]
    // 0xac56d8: r0 = add()
    //     0xac56d8: bl              #0xac56f4  ; [package:vector_math/vector_math.dart] Matrix4::add
    // 0xac56dc: ldur            x0, [fp, #-8]
    // 0xac56e0: LeaveFrame
    //     0xac56e0: mov             SP, fp
    //     0xac56e4: ldp             fp, lr, [SP], #0x10
    // 0xac56e8: ret
    //     0xac56e8: ret             
    // 0xac56ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac56ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac56f0: b               #0xac56c8
  }
  _ add(/* No info */) {
    // ** addr: 0xac56f4, size: 0x478
    // 0xac56f4: EnterFrame
    //     0xac56f4: stp             fp, lr, [SP, #-0x10]!
    //     0xac56f8: mov             fp, SP
    // 0xac56fc: LoadField: r3 = r2->field_7
    //     0xac56fc: ldur            w3, [x2, #7]
    // 0xac5700: DecompressPointer r3
    //     0xac5700: add             x3, x3, HEAP, lsl #32
    // 0xac5704: LoadField: r2 = r1->field_7
    //     0xac5704: ldur            w2, [x1, #7]
    // 0xac5708: DecompressPointer r2
    //     0xac5708: add             x2, x2, HEAP, lsl #32
    // 0xac570c: LoadField: r4 = r2->field_13
    //     0xac570c: ldur            w4, [x2, #0x13]
    // 0xac5710: r5 = LoadInt32Instr(r4)
    //     0xac5710: sbfx            x5, x4, #1, #0x1f
    // 0xac5714: mov             x0, x5
    // 0xac5718: r1 = 0
    //     0xac5718: movz            x1, #0
    // 0xac571c: cmp             x1, x0
    // 0xac5720: b.hs            #0xac5aec
    // 0xac5724: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xac5724: ldur            s0, [x2, #0x17]
    // 0xac5728: fcvt            d1, s0
    // 0xac572c: LoadField: r4 = r3->field_13
    //     0xac572c: ldur            w4, [x3, #0x13]
    // 0xac5730: r6 = LoadInt32Instr(r4)
    //     0xac5730: sbfx            x6, x4, #1, #0x1f
    // 0xac5734: mov             x0, x6
    // 0xac5738: r1 = 0
    //     0xac5738: movz            x1, #0
    // 0xac573c: cmp             x1, x0
    // 0xac5740: b.hs            #0xac5af0
    // 0xac5744: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xac5744: ldur            s0, [x3, #0x17]
    // 0xac5748: fcvt            d2, s0
    // 0xac574c: fadd            d0, d1, d2
    // 0xac5750: fcvt            s1, d0
    // 0xac5754: ArrayStore: r2[0] = d1  ; List_8
    //     0xac5754: stur            s1, [x2, #0x17]
    // 0xac5758: mov             x0, x5
    // 0xac575c: r1 = 1
    //     0xac575c: movz            x1, #0x1
    // 0xac5760: cmp             x1, x0
    // 0xac5764: b.hs            #0xac5af4
    // 0xac5768: LoadField: d0 = r2->field_1b
    //     0xac5768: ldur            s0, [x2, #0x1b]
    // 0xac576c: fcvt            d1, s0
    // 0xac5770: mov             x0, x6
    // 0xac5774: r1 = 1
    //     0xac5774: movz            x1, #0x1
    // 0xac5778: cmp             x1, x0
    // 0xac577c: b.hs            #0xac5af8
    // 0xac5780: LoadField: d0 = r3->field_1b
    //     0xac5780: ldur            s0, [x3, #0x1b]
    // 0xac5784: fcvt            d2, s0
    // 0xac5788: fadd            d0, d1, d2
    // 0xac578c: fcvt            s1, d0
    // 0xac5790: StoreField: r2->field_1b = d1
    //     0xac5790: stur            s1, [x2, #0x1b]
    // 0xac5794: mov             x0, x5
    // 0xac5798: r1 = 2
    //     0xac5798: movz            x1, #0x2
    // 0xac579c: cmp             x1, x0
    // 0xac57a0: b.hs            #0xac5afc
    // 0xac57a4: LoadField: d0 = r2->field_1f
    //     0xac57a4: ldur            s0, [x2, #0x1f]
    // 0xac57a8: fcvt            d1, s0
    // 0xac57ac: mov             x0, x6
    // 0xac57b0: r1 = 2
    //     0xac57b0: movz            x1, #0x2
    // 0xac57b4: cmp             x1, x0
    // 0xac57b8: b.hs            #0xac5b00
    // 0xac57bc: LoadField: d0 = r3->field_1f
    //     0xac57bc: ldur            s0, [x3, #0x1f]
    // 0xac57c0: fcvt            d2, s0
    // 0xac57c4: fadd            d0, d1, d2
    // 0xac57c8: fcvt            s1, d0
    // 0xac57cc: StoreField: r2->field_1f = d1
    //     0xac57cc: stur            s1, [x2, #0x1f]
    // 0xac57d0: mov             x0, x5
    // 0xac57d4: r1 = 3
    //     0xac57d4: movz            x1, #0x3
    // 0xac57d8: cmp             x1, x0
    // 0xac57dc: b.hs            #0xac5b04
    // 0xac57e0: LoadField: d0 = r2->field_23
    //     0xac57e0: ldur            s0, [x2, #0x23]
    // 0xac57e4: fcvt            d1, s0
    // 0xac57e8: mov             x0, x6
    // 0xac57ec: r1 = 3
    //     0xac57ec: movz            x1, #0x3
    // 0xac57f0: cmp             x1, x0
    // 0xac57f4: b.hs            #0xac5b08
    // 0xac57f8: LoadField: d0 = r3->field_23
    //     0xac57f8: ldur            s0, [x3, #0x23]
    // 0xac57fc: fcvt            d2, s0
    // 0xac5800: fadd            d0, d1, d2
    // 0xac5804: fcvt            s1, d0
    // 0xac5808: StoreField: r2->field_23 = d1
    //     0xac5808: stur            s1, [x2, #0x23]
    // 0xac580c: mov             x0, x5
    // 0xac5810: r1 = 4
    //     0xac5810: movz            x1, #0x4
    // 0xac5814: cmp             x1, x0
    // 0xac5818: b.hs            #0xac5b0c
    // 0xac581c: LoadField: d0 = r2->field_27
    //     0xac581c: ldur            s0, [x2, #0x27]
    // 0xac5820: fcvt            d1, s0
    // 0xac5824: mov             x0, x6
    // 0xac5828: r1 = 4
    //     0xac5828: movz            x1, #0x4
    // 0xac582c: cmp             x1, x0
    // 0xac5830: b.hs            #0xac5b10
    // 0xac5834: LoadField: d0 = r3->field_27
    //     0xac5834: ldur            s0, [x3, #0x27]
    // 0xac5838: fcvt            d2, s0
    // 0xac583c: fadd            d0, d1, d2
    // 0xac5840: fcvt            s1, d0
    // 0xac5844: StoreField: r2->field_27 = d1
    //     0xac5844: stur            s1, [x2, #0x27]
    // 0xac5848: mov             x0, x5
    // 0xac584c: r1 = 5
    //     0xac584c: movz            x1, #0x5
    // 0xac5850: cmp             x1, x0
    // 0xac5854: b.hs            #0xac5b14
    // 0xac5858: LoadField: d0 = r2->field_2b
    //     0xac5858: ldur            s0, [x2, #0x2b]
    // 0xac585c: fcvt            d1, s0
    // 0xac5860: mov             x0, x6
    // 0xac5864: r1 = 5
    //     0xac5864: movz            x1, #0x5
    // 0xac5868: cmp             x1, x0
    // 0xac586c: b.hs            #0xac5b18
    // 0xac5870: LoadField: d0 = r3->field_2b
    //     0xac5870: ldur            s0, [x3, #0x2b]
    // 0xac5874: fcvt            d2, s0
    // 0xac5878: fadd            d0, d1, d2
    // 0xac587c: fcvt            s1, d0
    // 0xac5880: StoreField: r2->field_2b = d1
    //     0xac5880: stur            s1, [x2, #0x2b]
    // 0xac5884: mov             x0, x5
    // 0xac5888: r1 = 6
    //     0xac5888: movz            x1, #0x6
    // 0xac588c: cmp             x1, x0
    // 0xac5890: b.hs            #0xac5b1c
    // 0xac5894: LoadField: d0 = r2->field_2f
    //     0xac5894: ldur            s0, [x2, #0x2f]
    // 0xac5898: fcvt            d1, s0
    // 0xac589c: mov             x0, x6
    // 0xac58a0: r1 = 6
    //     0xac58a0: movz            x1, #0x6
    // 0xac58a4: cmp             x1, x0
    // 0xac58a8: b.hs            #0xac5b20
    // 0xac58ac: LoadField: d0 = r3->field_2f
    //     0xac58ac: ldur            s0, [x3, #0x2f]
    // 0xac58b0: fcvt            d2, s0
    // 0xac58b4: fadd            d0, d1, d2
    // 0xac58b8: fcvt            s1, d0
    // 0xac58bc: StoreField: r2->field_2f = d1
    //     0xac58bc: stur            s1, [x2, #0x2f]
    // 0xac58c0: mov             x0, x5
    // 0xac58c4: r1 = 7
    //     0xac58c4: movz            x1, #0x7
    // 0xac58c8: cmp             x1, x0
    // 0xac58cc: b.hs            #0xac5b24
    // 0xac58d0: LoadField: d0 = r2->field_33
    //     0xac58d0: ldur            s0, [x2, #0x33]
    // 0xac58d4: fcvt            d1, s0
    // 0xac58d8: mov             x0, x6
    // 0xac58dc: r1 = 7
    //     0xac58dc: movz            x1, #0x7
    // 0xac58e0: cmp             x1, x0
    // 0xac58e4: b.hs            #0xac5b28
    // 0xac58e8: LoadField: d0 = r3->field_33
    //     0xac58e8: ldur            s0, [x3, #0x33]
    // 0xac58ec: fcvt            d2, s0
    // 0xac58f0: fadd            d0, d1, d2
    // 0xac58f4: fcvt            s1, d0
    // 0xac58f8: StoreField: r2->field_33 = d1
    //     0xac58f8: stur            s1, [x2, #0x33]
    // 0xac58fc: mov             x0, x5
    // 0xac5900: r1 = 8
    //     0xac5900: movz            x1, #0x8
    // 0xac5904: cmp             x1, x0
    // 0xac5908: b.hs            #0xac5b2c
    // 0xac590c: LoadField: d0 = r2->field_37
    //     0xac590c: ldur            s0, [x2, #0x37]
    // 0xac5910: fcvt            d1, s0
    // 0xac5914: mov             x0, x6
    // 0xac5918: r1 = 8
    //     0xac5918: movz            x1, #0x8
    // 0xac591c: cmp             x1, x0
    // 0xac5920: b.hs            #0xac5b30
    // 0xac5924: LoadField: d0 = r3->field_37
    //     0xac5924: ldur            s0, [x3, #0x37]
    // 0xac5928: fcvt            d2, s0
    // 0xac592c: fadd            d0, d1, d2
    // 0xac5930: fcvt            s1, d0
    // 0xac5934: StoreField: r2->field_37 = d1
    //     0xac5934: stur            s1, [x2, #0x37]
    // 0xac5938: mov             x0, x5
    // 0xac593c: r1 = 9
    //     0xac593c: movz            x1, #0x9
    // 0xac5940: cmp             x1, x0
    // 0xac5944: b.hs            #0xac5b34
    // 0xac5948: LoadField: d0 = r2->field_3b
    //     0xac5948: ldur            s0, [x2, #0x3b]
    // 0xac594c: fcvt            d1, s0
    // 0xac5950: mov             x0, x6
    // 0xac5954: r1 = 9
    //     0xac5954: movz            x1, #0x9
    // 0xac5958: cmp             x1, x0
    // 0xac595c: b.hs            #0xac5b38
    // 0xac5960: LoadField: d0 = r3->field_3b
    //     0xac5960: ldur            s0, [x3, #0x3b]
    // 0xac5964: fcvt            d2, s0
    // 0xac5968: fadd            d0, d1, d2
    // 0xac596c: fcvt            s1, d0
    // 0xac5970: StoreField: r2->field_3b = d1
    //     0xac5970: stur            s1, [x2, #0x3b]
    // 0xac5974: mov             x0, x5
    // 0xac5978: r1 = 10
    //     0xac5978: movz            x1, #0xa
    // 0xac597c: cmp             x1, x0
    // 0xac5980: b.hs            #0xac5b3c
    // 0xac5984: LoadField: d0 = r2->field_3f
    //     0xac5984: ldur            s0, [x2, #0x3f]
    // 0xac5988: fcvt            d1, s0
    // 0xac598c: mov             x0, x6
    // 0xac5990: r1 = 10
    //     0xac5990: movz            x1, #0xa
    // 0xac5994: cmp             x1, x0
    // 0xac5998: b.hs            #0xac5b40
    // 0xac599c: LoadField: d0 = r3->field_3f
    //     0xac599c: ldur            s0, [x3, #0x3f]
    // 0xac59a0: fcvt            d2, s0
    // 0xac59a4: fadd            d0, d1, d2
    // 0xac59a8: fcvt            s1, d0
    // 0xac59ac: StoreField: r2->field_3f = d1
    //     0xac59ac: stur            s1, [x2, #0x3f]
    // 0xac59b0: mov             x0, x5
    // 0xac59b4: r1 = 11
    //     0xac59b4: movz            x1, #0xb
    // 0xac59b8: cmp             x1, x0
    // 0xac59bc: b.hs            #0xac5b44
    // 0xac59c0: LoadField: d0 = r2->field_43
    //     0xac59c0: ldur            s0, [x2, #0x43]
    // 0xac59c4: fcvt            d1, s0
    // 0xac59c8: mov             x0, x6
    // 0xac59cc: r1 = 11
    //     0xac59cc: movz            x1, #0xb
    // 0xac59d0: cmp             x1, x0
    // 0xac59d4: b.hs            #0xac5b48
    // 0xac59d8: LoadField: d0 = r3->field_43
    //     0xac59d8: ldur            s0, [x3, #0x43]
    // 0xac59dc: fcvt            d2, s0
    // 0xac59e0: fadd            d0, d1, d2
    // 0xac59e4: fcvt            s1, d0
    // 0xac59e8: StoreField: r2->field_43 = d1
    //     0xac59e8: stur            s1, [x2, #0x43]
    // 0xac59ec: mov             x0, x5
    // 0xac59f0: r1 = 12
    //     0xac59f0: movz            x1, #0xc
    // 0xac59f4: cmp             x1, x0
    // 0xac59f8: b.hs            #0xac5b4c
    // 0xac59fc: LoadField: d0 = r2->field_47
    //     0xac59fc: ldur            s0, [x2, #0x47]
    // 0xac5a00: fcvt            d1, s0
    // 0xac5a04: mov             x0, x6
    // 0xac5a08: r1 = 12
    //     0xac5a08: movz            x1, #0xc
    // 0xac5a0c: cmp             x1, x0
    // 0xac5a10: b.hs            #0xac5b50
    // 0xac5a14: LoadField: d0 = r3->field_47
    //     0xac5a14: ldur            s0, [x3, #0x47]
    // 0xac5a18: fcvt            d2, s0
    // 0xac5a1c: fadd            d0, d1, d2
    // 0xac5a20: fcvt            s1, d0
    // 0xac5a24: StoreField: r2->field_47 = d1
    //     0xac5a24: stur            s1, [x2, #0x47]
    // 0xac5a28: mov             x0, x5
    // 0xac5a2c: r1 = 13
    //     0xac5a2c: movz            x1, #0xd
    // 0xac5a30: cmp             x1, x0
    // 0xac5a34: b.hs            #0xac5b54
    // 0xac5a38: LoadField: d0 = r2->field_4b
    //     0xac5a38: ldur            s0, [x2, #0x4b]
    // 0xac5a3c: fcvt            d1, s0
    // 0xac5a40: mov             x0, x6
    // 0xac5a44: r1 = 13
    //     0xac5a44: movz            x1, #0xd
    // 0xac5a48: cmp             x1, x0
    // 0xac5a4c: b.hs            #0xac5b58
    // 0xac5a50: LoadField: d0 = r3->field_4b
    //     0xac5a50: ldur            s0, [x3, #0x4b]
    // 0xac5a54: fcvt            d2, s0
    // 0xac5a58: fadd            d0, d1, d2
    // 0xac5a5c: fcvt            s1, d0
    // 0xac5a60: StoreField: r2->field_4b = d1
    //     0xac5a60: stur            s1, [x2, #0x4b]
    // 0xac5a64: mov             x0, x5
    // 0xac5a68: r1 = 14
    //     0xac5a68: movz            x1, #0xe
    // 0xac5a6c: cmp             x1, x0
    // 0xac5a70: b.hs            #0xac5b5c
    // 0xac5a74: LoadField: d0 = r2->field_4f
    //     0xac5a74: ldur            s0, [x2, #0x4f]
    // 0xac5a78: fcvt            d1, s0
    // 0xac5a7c: mov             x0, x6
    // 0xac5a80: r1 = 14
    //     0xac5a80: movz            x1, #0xe
    // 0xac5a84: cmp             x1, x0
    // 0xac5a88: b.hs            #0xac5b60
    // 0xac5a8c: LoadField: d0 = r3->field_4f
    //     0xac5a8c: ldur            s0, [x3, #0x4f]
    // 0xac5a90: fcvt            d2, s0
    // 0xac5a94: fadd            d0, d1, d2
    // 0xac5a98: fcvt            s1, d0
    // 0xac5a9c: StoreField: r2->field_4f = d1
    //     0xac5a9c: stur            s1, [x2, #0x4f]
    // 0xac5aa0: mov             x0, x5
    // 0xac5aa4: r1 = 15
    //     0xac5aa4: movz            x1, #0xf
    // 0xac5aa8: cmp             x1, x0
    // 0xac5aac: b.hs            #0xac5b64
    // 0xac5ab0: LoadField: d0 = r2->field_53
    //     0xac5ab0: ldur            s0, [x2, #0x53]
    // 0xac5ab4: fcvt            d1, s0
    // 0xac5ab8: mov             x0, x6
    // 0xac5abc: r1 = 15
    //     0xac5abc: movz            x1, #0xf
    // 0xac5ac0: cmp             x1, x0
    // 0xac5ac4: b.hs            #0xac5b68
    // 0xac5ac8: LoadField: d0 = r3->field_53
    //     0xac5ac8: ldur            s0, [x3, #0x53]
    // 0xac5acc: fcvt            d2, s0
    // 0xac5ad0: fadd            d0, d1, d2
    // 0xac5ad4: fcvt            s1, d0
    // 0xac5ad8: StoreField: r2->field_53 = d1
    //     0xac5ad8: stur            s1, [x2, #0x53]
    // 0xac5adc: r0 = Null
    //     0xac5adc: mov             x0, NULL
    // 0xac5ae0: LeaveFrame
    //     0xac5ae0: mov             SP, fp
    //     0xac5ae4: ldp             fp, lr, [SP], #0x10
    // 0xac5ae8: ret
    //     0xac5ae8: ret             
    // 0xac5aec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5aec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5af0: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5af0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5af4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5af4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5af8: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5af8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5afc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5afc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b00: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b00: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b04: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b08: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b08: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b0c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b10: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b10: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b14: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b18: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b18: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b1c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b20: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b20: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b24: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b28: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b28: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b2c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b30: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b30: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b34: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b38: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b38: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b40: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b40: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b44: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b48: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b48: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b4c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b50: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b50: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b54: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b58: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b58: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b5c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b60: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b60: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac5b64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5b64: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5b68: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5b68: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  _ clone(/* No info */) {
    // ** addr: 0xac5b6c, size: 0x34
    // 0xac5b6c: EnterFrame
    //     0xac5b6c: stp             fp, lr, [SP, #-0x10]!
    //     0xac5b70: mov             fp, SP
    // 0xac5b74: mov             x2, x1
    // 0xac5b78: CheckStackOverflow
    //     0xac5b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac5b7c: cmp             SP, x16
    //     0xac5b80: b.ls            #0xac5b98
    // 0xac5b84: r1 = Null
    //     0xac5b84: mov             x1, NULL
    // 0xac5b88: r0 = Matrix4.copy()
    //     0xac5b88: bl              #0xac5ba0  ; [package:vector_math/vector_math.dart] Matrix4::Matrix4.copy
    // 0xac5b8c: LeaveFrame
    //     0xac5b8c: mov             SP, fp
    //     0xac5b90: ldp             fp, lr, [SP], #0x10
    // 0xac5b94: ret
    //     0xac5b94: ret             
    // 0xac5b98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac5b98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5b9c: b               #0xac5b84
  }
  factory _ Matrix4.copy(/* No info */) {
    // ** addr: 0xac5ba0, size: 0x5c
    // 0xac5ba0: EnterFrame
    //     0xac5ba0: stp             fp, lr, [SP, #-0x10]!
    //     0xac5ba4: mov             fp, SP
    // 0xac5ba8: AllocStack(0x10)
    //     0xac5ba8: sub             SP, SP, #0x10
    // 0xac5bac: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xac5bac: stur            x2, [fp, #-8]
    // 0xac5bb0: CheckStackOverflow
    //     0xac5bb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac5bb4: cmp             SP, x16
    //     0xac5bb8: b.ls            #0xac5bf4
    // 0xac5bbc: r0 = Matrix4()
    //     0xac5bbc: bl              #0xac5cd4  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xac5bc0: r4 = 32
    //     0xac5bc0: movz            x4, #0x20
    // 0xac5bc4: stur            x0, [fp, #-0x10]
    // 0xac5bc8: r0 = AllocateFloat32Array()
    //     0xac5bc8: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0xac5bcc: mov             x1, x0
    // 0xac5bd0: ldur            x0, [fp, #-0x10]
    // 0xac5bd4: StoreField: r0->field_7 = r1
    //     0xac5bd4: stur            w1, [x0, #7]
    // 0xac5bd8: mov             x1, x0
    // 0xac5bdc: ldur            x2, [fp, #-8]
    // 0xac5be0: r0 = setFrom()
    //     0xac5be0: bl              #0xac5bfc  ; [package:vector_math/vector_math.dart] Matrix4::setFrom
    // 0xac5be4: ldur            x0, [fp, #-0x10]
    // 0xac5be8: LeaveFrame
    //     0xac5be8: mov             SP, fp
    //     0xac5bec: ldp             fp, lr, [SP], #0x10
    // 0xac5bf0: ret
    //     0xac5bf0: ret             
    // 0xac5bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac5bf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5bf8: b               #0xac5bbc
  }
  _ setFrom(/* No info */) {
    // ** addr: 0xac5bfc, size: 0xd8
    // 0xac5bfc: EnterFrame
    //     0xac5bfc: stp             fp, lr, [SP, #-0x10]!
    //     0xac5c00: mov             fp, SP
    // 0xac5c04: LoadField: r3 = r2->field_7
    //     0xac5c04: ldur            w3, [x2, #7]
    // 0xac5c08: DecompressPointer r3
    //     0xac5c08: add             x3, x3, HEAP, lsl #32
    // 0xac5c0c: LoadField: r2 = r1->field_7
    //     0xac5c0c: ldur            w2, [x1, #7]
    // 0xac5c10: DecompressPointer r2
    //     0xac5c10: add             x2, x2, HEAP, lsl #32
    // 0xac5c14: LoadField: r4 = r3->field_13
    //     0xac5c14: ldur            w4, [x3, #0x13]
    // 0xac5c18: r0 = LoadInt32Instr(r4)
    //     0xac5c18: sbfx            x0, x4, #1, #0x1f
    // 0xac5c1c: r1 = 15
    //     0xac5c1c: movz            x1, #0xf
    // 0xac5c20: cmp             x1, x0
    // 0xac5c24: b.hs            #0xac5ccc
    // 0xac5c28: LoadField: d0 = r3->field_53
    //     0xac5c28: ldur            s0, [x3, #0x53]
    // 0xac5c2c: LoadField: r4 = r2->field_13
    //     0xac5c2c: ldur            w4, [x2, #0x13]
    // 0xac5c30: r0 = LoadInt32Instr(r4)
    //     0xac5c30: sbfx            x0, x4, #1, #0x1f
    // 0xac5c34: r1 = 15
    //     0xac5c34: movz            x1, #0xf
    // 0xac5c38: cmp             x1, x0
    // 0xac5c3c: b.hs            #0xac5cd0
    // 0xac5c40: StoreField: r2->field_53 = d0
    //     0xac5c40: stur            s0, [x2, #0x53]
    // 0xac5c44: LoadField: d0 = r3->field_4f
    //     0xac5c44: ldur            s0, [x3, #0x4f]
    // 0xac5c48: StoreField: r2->field_4f = d0
    //     0xac5c48: stur            s0, [x2, #0x4f]
    // 0xac5c4c: LoadField: d0 = r3->field_4b
    //     0xac5c4c: ldur            s0, [x3, #0x4b]
    // 0xac5c50: StoreField: r2->field_4b = d0
    //     0xac5c50: stur            s0, [x2, #0x4b]
    // 0xac5c54: LoadField: d0 = r3->field_47
    //     0xac5c54: ldur            s0, [x3, #0x47]
    // 0xac5c58: StoreField: r2->field_47 = d0
    //     0xac5c58: stur            s0, [x2, #0x47]
    // 0xac5c5c: LoadField: d0 = r3->field_43
    //     0xac5c5c: ldur            s0, [x3, #0x43]
    // 0xac5c60: StoreField: r2->field_43 = d0
    //     0xac5c60: stur            s0, [x2, #0x43]
    // 0xac5c64: LoadField: d0 = r3->field_3f
    //     0xac5c64: ldur            s0, [x3, #0x3f]
    // 0xac5c68: StoreField: r2->field_3f = d0
    //     0xac5c68: stur            s0, [x2, #0x3f]
    // 0xac5c6c: LoadField: d0 = r3->field_3b
    //     0xac5c6c: ldur            s0, [x3, #0x3b]
    // 0xac5c70: StoreField: r2->field_3b = d0
    //     0xac5c70: stur            s0, [x2, #0x3b]
    // 0xac5c74: LoadField: d0 = r3->field_37
    //     0xac5c74: ldur            s0, [x3, #0x37]
    // 0xac5c78: StoreField: r2->field_37 = d0
    //     0xac5c78: stur            s0, [x2, #0x37]
    // 0xac5c7c: LoadField: d0 = r3->field_33
    //     0xac5c7c: ldur            s0, [x3, #0x33]
    // 0xac5c80: StoreField: r2->field_33 = d0
    //     0xac5c80: stur            s0, [x2, #0x33]
    // 0xac5c84: LoadField: d0 = r3->field_2f
    //     0xac5c84: ldur            s0, [x3, #0x2f]
    // 0xac5c88: StoreField: r2->field_2f = d0
    //     0xac5c88: stur            s0, [x2, #0x2f]
    // 0xac5c8c: LoadField: d0 = r3->field_2b
    //     0xac5c8c: ldur            s0, [x3, #0x2b]
    // 0xac5c90: StoreField: r2->field_2b = d0
    //     0xac5c90: stur            s0, [x2, #0x2b]
    // 0xac5c94: LoadField: d0 = r3->field_27
    //     0xac5c94: ldur            s0, [x3, #0x27]
    // 0xac5c98: StoreField: r2->field_27 = d0
    //     0xac5c98: stur            s0, [x2, #0x27]
    // 0xac5c9c: LoadField: d0 = r3->field_23
    //     0xac5c9c: ldur            s0, [x3, #0x23]
    // 0xac5ca0: StoreField: r2->field_23 = d0
    //     0xac5ca0: stur            s0, [x2, #0x23]
    // 0xac5ca4: LoadField: d0 = r3->field_1f
    //     0xac5ca4: ldur            s0, [x3, #0x1f]
    // 0xac5ca8: StoreField: r2->field_1f = d0
    //     0xac5ca8: stur            s0, [x2, #0x1f]
    // 0xac5cac: LoadField: d0 = r3->field_1b
    //     0xac5cac: ldur            s0, [x3, #0x1b]
    // 0xac5cb0: StoreField: r2->field_1b = d0
    //     0xac5cb0: stur            s0, [x2, #0x1b]
    // 0xac5cb4: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xac5cb4: ldur            s0, [x3, #0x17]
    // 0xac5cb8: ArrayStore: r2[0] = d0  ; List_8
    //     0xac5cb8: stur            s0, [x2, #0x17]
    // 0xac5cbc: r0 = Null
    //     0xac5cbc: mov             x0, NULL
    // 0xac5cc0: LeaveFrame
    //     0xac5cc0: mov             SP, fp
    //     0xac5cc4: ldp             fp, lr, [SP], #0x10
    // 0xac5cc8: ret
    //     0xac5cc8: ret             
    // 0xac5ccc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac5ccc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac5cd0: r0 = RangeErrorSharedWithFPURegs()
    //     0xac5cd0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  dynamic *(Matrix4, dynamic) {
    // ** addr: 0xac5cf8, size: 0x50
    // 0xac5cf8: EnterFrame
    //     0xac5cf8: stp             fp, lr, [SP, #-0x10]!
    //     0xac5cfc: mov             fp, SP
    // 0xac5d00: CheckStackOverflow
    //     0xac5d00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac5d04: cmp             SP, x16
    //     0xac5d08: b.ls            #0xac5d28
    // 0xac5d0c: ldr             x0, [fp, #0x10]
    // 0xac5d10: LoadField: d0 = r0->field_7
    //     0xac5d10: ldur            d0, [x0, #7]
    // 0xac5d14: ldr             x1, [fp, #0x18]
    // 0xac5d18: r0 = scaled()
    //     0xac5d18: bl              #0xac5d30  ; [package:vector_math/vector_math.dart] Matrix4::scaled
    // 0xac5d1c: LeaveFrame
    //     0xac5d1c: mov             SP, fp
    //     0xac5d20: ldp             fp, lr, [SP], #0x10
    // 0xac5d24: ret
    //     0xac5d24: ret             
    // 0xac5d28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac5d28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5d2c: b               #0xac5d0c
  }
  _ scaled(/* No info */) {
    // ** addr: 0xac5d30, size: 0x4c
    // 0xac5d30: EnterFrame
    //     0xac5d30: stp             fp, lr, [SP, #-0x10]!
    //     0xac5d34: mov             fp, SP
    // 0xac5d38: AllocStack(0x10)
    //     0xac5d38: sub             SP, SP, #0x10
    // 0xac5d3c: SetupParameters(dynamic _ /* d0 => d0, fp-0x10 */)
    //     0xac5d3c: stur            d0, [fp, #-0x10]
    // 0xac5d40: CheckStackOverflow
    //     0xac5d40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac5d44: cmp             SP, x16
    //     0xac5d48: b.ls            #0xac5d74
    // 0xac5d4c: r0 = clone()
    //     0xac5d4c: bl              #0xac5b6c  ; [package:vector_math/vector_math.dart] Matrix4::clone
    // 0xac5d50: mov             x1, x0
    // 0xac5d54: ldur            d0, [fp, #-0x10]
    // 0xac5d58: r2 = Null
    //     0xac5d58: mov             x2, NULL
    // 0xac5d5c: stur            x0, [fp, #-8]
    // 0xac5d60: r0 = scale()
    //     0xac5d60: bl              #0xac539c  ; [package:vector_math/vector_math.dart] Matrix4::scale
    // 0xac5d64: ldur            x0, [fp, #-8]
    // 0xac5d68: LeaveFrame
    //     0xac5d68: mov             SP, fp
    //     0xac5d6c: ldp             fp, lr, [SP], #0x10
    // 0xac5d70: ret
    //     0xac5d70: ret             
    // 0xac5d74: r0 = StackOverflowSharedWithFPURegs()
    //     0xac5d74: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xac5d78: b               #0xac5d4c
  }
  Matrix4 -(Matrix4, Matrix4) {
    // ** addr: 0xac5d94, size: 0x84
    // 0xac5d94: EnterFrame
    //     0xac5d94: stp             fp, lr, [SP, #-0x10]!
    //     0xac5d98: mov             fp, SP
    // 0xac5d9c: CheckStackOverflow
    //     0xac5d9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac5da0: cmp             SP, x16
    //     0xac5da4: b.ls            #0xac5df8
    // 0xac5da8: ldr             x0, [fp, #0x10]
    // 0xac5dac: r2 = Null
    //     0xac5dac: mov             x2, NULL
    // 0xac5db0: r1 = Null
    //     0xac5db0: mov             x1, NULL
    // 0xac5db4: r4 = 60
    //     0xac5db4: movz            x4, #0x3c
    // 0xac5db8: branchIfSmi(r0, 0xac5dc4)
    //     0xac5db8: tbz             w0, #0, #0xac5dc4
    // 0xac5dbc: r4 = LoadClassIdInstr(r0)
    //     0xac5dbc: ldur            x4, [x0, #-1]
    //     0xac5dc0: ubfx            x4, x4, #0xc, #0x14
    // 0xac5dc4: cmp             x4, #0x19d
    // 0xac5dc8: b.eq            #0xac5de0
    // 0xac5dcc: r8 = Matrix4
    //     0xac5dcc: add             x8, PP, #0x38, lsl #12  ; [pp+0x38fa0] Type: Matrix4
    //     0xac5dd0: ldr             x8, [x8, #0xfa0]
    // 0xac5dd4: r3 = Null
    //     0xac5dd4: add             x3, PP, #0x38, lsl #12  ; [pp+0x38fa8] Null
    //     0xac5dd8: ldr             x3, [x3, #0xfa8]
    // 0xac5ddc: r0 = DefaultTypeTest()
    //     0xac5ddc: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xac5de0: ldr             x1, [fp, #0x18]
    // 0xac5de4: ldr             x2, [fp, #0x10]
    // 0xac5de8: r0 = -()
    //     0xac5de8: bl              #0xac5e00  ; [package:vector_math/vector_math.dart] Matrix4::-
    // 0xac5dec: LeaveFrame
    //     0xac5dec: mov             SP, fp
    //     0xac5df0: ldp             fp, lr, [SP], #0x10
    // 0xac5df4: ret
    //     0xac5df4: ret             
    // 0xac5df8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac5df8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5dfc: b               #0xac5da8
  }
  Matrix4 -(Matrix4, Matrix4) {
    // ** addr: 0xac5e00, size: 0x48
    // 0xac5e00: EnterFrame
    //     0xac5e00: stp             fp, lr, [SP, #-0x10]!
    //     0xac5e04: mov             fp, SP
    // 0xac5e08: AllocStack(0x8)
    //     0xac5e08: sub             SP, SP, #8
    // 0xac5e0c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xac5e0c: stur            x2, [fp, #-8]
    // 0xac5e10: CheckStackOverflow
    //     0xac5e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac5e14: cmp             SP, x16
    //     0xac5e18: b.ls            #0xac5e40
    // 0xac5e1c: r0 = clone()
    //     0xac5e1c: bl              #0xac5b6c  ; [package:vector_math/vector_math.dart] Matrix4::clone
    // 0xac5e20: mov             x1, x0
    // 0xac5e24: ldur            x2, [fp, #-8]
    // 0xac5e28: stur            x0, [fp, #-8]
    // 0xac5e2c: r0 = sub()
    //     0xac5e2c: bl              #0xac5e48  ; [package:vector_math/vector_math.dart] Matrix4::sub
    // 0xac5e30: ldur            x0, [fp, #-8]
    // 0xac5e34: LeaveFrame
    //     0xac5e34: mov             SP, fp
    //     0xac5e38: ldp             fp, lr, [SP], #0x10
    // 0xac5e3c: ret
    //     0xac5e3c: ret             
    // 0xac5e40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac5e40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac5e44: b               #0xac5e1c
  }
  _ sub(/* No info */) {
    // ** addr: 0xac5e48, size: 0x478
    // 0xac5e48: EnterFrame
    //     0xac5e48: stp             fp, lr, [SP, #-0x10]!
    //     0xac5e4c: mov             fp, SP
    // 0xac5e50: LoadField: r3 = r2->field_7
    //     0xac5e50: ldur            w3, [x2, #7]
    // 0xac5e54: DecompressPointer r3
    //     0xac5e54: add             x3, x3, HEAP, lsl #32
    // 0xac5e58: LoadField: r2 = r1->field_7
    //     0xac5e58: ldur            w2, [x1, #7]
    // 0xac5e5c: DecompressPointer r2
    //     0xac5e5c: add             x2, x2, HEAP, lsl #32
    // 0xac5e60: LoadField: r4 = r2->field_13
    //     0xac5e60: ldur            w4, [x2, #0x13]
    // 0xac5e64: r5 = LoadInt32Instr(r4)
    //     0xac5e64: sbfx            x5, x4, #1, #0x1f
    // 0xac5e68: mov             x0, x5
    // 0xac5e6c: r1 = 0
    //     0xac5e6c: movz            x1, #0
    // 0xac5e70: cmp             x1, x0
    // 0xac5e74: b.hs            #0xac6240
    // 0xac5e78: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xac5e78: ldur            s0, [x2, #0x17]
    // 0xac5e7c: fcvt            d1, s0
    // 0xac5e80: LoadField: r4 = r3->field_13
    //     0xac5e80: ldur            w4, [x3, #0x13]
    // 0xac5e84: r6 = LoadInt32Instr(r4)
    //     0xac5e84: sbfx            x6, x4, #1, #0x1f
    // 0xac5e88: mov             x0, x6
    // 0xac5e8c: r1 = 0
    //     0xac5e8c: movz            x1, #0
    // 0xac5e90: cmp             x1, x0
    // 0xac5e94: b.hs            #0xac6244
    // 0xac5e98: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xac5e98: ldur            s0, [x3, #0x17]
    // 0xac5e9c: fcvt            d2, s0
    // 0xac5ea0: fsub            d0, d1, d2
    // 0xac5ea4: fcvt            s1, d0
    // 0xac5ea8: ArrayStore: r2[0] = d1  ; List_8
    //     0xac5ea8: stur            s1, [x2, #0x17]
    // 0xac5eac: mov             x0, x5
    // 0xac5eb0: r1 = 1
    //     0xac5eb0: movz            x1, #0x1
    // 0xac5eb4: cmp             x1, x0
    // 0xac5eb8: b.hs            #0xac6248
    // 0xac5ebc: LoadField: d0 = r2->field_1b
    //     0xac5ebc: ldur            s0, [x2, #0x1b]
    // 0xac5ec0: fcvt            d1, s0
    // 0xac5ec4: mov             x0, x6
    // 0xac5ec8: r1 = 1
    //     0xac5ec8: movz            x1, #0x1
    // 0xac5ecc: cmp             x1, x0
    // 0xac5ed0: b.hs            #0xac624c
    // 0xac5ed4: LoadField: d0 = r3->field_1b
    //     0xac5ed4: ldur            s0, [x3, #0x1b]
    // 0xac5ed8: fcvt            d2, s0
    // 0xac5edc: fsub            d0, d1, d2
    // 0xac5ee0: fcvt            s1, d0
    // 0xac5ee4: StoreField: r2->field_1b = d1
    //     0xac5ee4: stur            s1, [x2, #0x1b]
    // 0xac5ee8: mov             x0, x5
    // 0xac5eec: r1 = 2
    //     0xac5eec: movz            x1, #0x2
    // 0xac5ef0: cmp             x1, x0
    // 0xac5ef4: b.hs            #0xac6250
    // 0xac5ef8: LoadField: d0 = r2->field_1f
    //     0xac5ef8: ldur            s0, [x2, #0x1f]
    // 0xac5efc: fcvt            d1, s0
    // 0xac5f00: mov             x0, x6
    // 0xac5f04: r1 = 2
    //     0xac5f04: movz            x1, #0x2
    // 0xac5f08: cmp             x1, x0
    // 0xac5f0c: b.hs            #0xac6254
    // 0xac5f10: LoadField: d0 = r3->field_1f
    //     0xac5f10: ldur            s0, [x3, #0x1f]
    // 0xac5f14: fcvt            d2, s0
    // 0xac5f18: fsub            d0, d1, d2
    // 0xac5f1c: fcvt            s1, d0
    // 0xac5f20: StoreField: r2->field_1f = d1
    //     0xac5f20: stur            s1, [x2, #0x1f]
    // 0xac5f24: mov             x0, x5
    // 0xac5f28: r1 = 3
    //     0xac5f28: movz            x1, #0x3
    // 0xac5f2c: cmp             x1, x0
    // 0xac5f30: b.hs            #0xac6258
    // 0xac5f34: LoadField: d0 = r2->field_23
    //     0xac5f34: ldur            s0, [x2, #0x23]
    // 0xac5f38: fcvt            d1, s0
    // 0xac5f3c: mov             x0, x6
    // 0xac5f40: r1 = 3
    //     0xac5f40: movz            x1, #0x3
    // 0xac5f44: cmp             x1, x0
    // 0xac5f48: b.hs            #0xac625c
    // 0xac5f4c: LoadField: d0 = r3->field_23
    //     0xac5f4c: ldur            s0, [x3, #0x23]
    // 0xac5f50: fcvt            d2, s0
    // 0xac5f54: fsub            d0, d1, d2
    // 0xac5f58: fcvt            s1, d0
    // 0xac5f5c: StoreField: r2->field_23 = d1
    //     0xac5f5c: stur            s1, [x2, #0x23]
    // 0xac5f60: mov             x0, x5
    // 0xac5f64: r1 = 4
    //     0xac5f64: movz            x1, #0x4
    // 0xac5f68: cmp             x1, x0
    // 0xac5f6c: b.hs            #0xac6260
    // 0xac5f70: LoadField: d0 = r2->field_27
    //     0xac5f70: ldur            s0, [x2, #0x27]
    // 0xac5f74: fcvt            d1, s0
    // 0xac5f78: mov             x0, x6
    // 0xac5f7c: r1 = 4
    //     0xac5f7c: movz            x1, #0x4
    // 0xac5f80: cmp             x1, x0
    // 0xac5f84: b.hs            #0xac6264
    // 0xac5f88: LoadField: d0 = r3->field_27
    //     0xac5f88: ldur            s0, [x3, #0x27]
    // 0xac5f8c: fcvt            d2, s0
    // 0xac5f90: fsub            d0, d1, d2
    // 0xac5f94: fcvt            s1, d0
    // 0xac5f98: StoreField: r2->field_27 = d1
    //     0xac5f98: stur            s1, [x2, #0x27]
    // 0xac5f9c: mov             x0, x5
    // 0xac5fa0: r1 = 5
    //     0xac5fa0: movz            x1, #0x5
    // 0xac5fa4: cmp             x1, x0
    // 0xac5fa8: b.hs            #0xac6268
    // 0xac5fac: LoadField: d0 = r2->field_2b
    //     0xac5fac: ldur            s0, [x2, #0x2b]
    // 0xac5fb0: fcvt            d1, s0
    // 0xac5fb4: mov             x0, x6
    // 0xac5fb8: r1 = 5
    //     0xac5fb8: movz            x1, #0x5
    // 0xac5fbc: cmp             x1, x0
    // 0xac5fc0: b.hs            #0xac626c
    // 0xac5fc4: LoadField: d0 = r3->field_2b
    //     0xac5fc4: ldur            s0, [x3, #0x2b]
    // 0xac5fc8: fcvt            d2, s0
    // 0xac5fcc: fsub            d0, d1, d2
    // 0xac5fd0: fcvt            s1, d0
    // 0xac5fd4: StoreField: r2->field_2b = d1
    //     0xac5fd4: stur            s1, [x2, #0x2b]
    // 0xac5fd8: mov             x0, x5
    // 0xac5fdc: r1 = 6
    //     0xac5fdc: movz            x1, #0x6
    // 0xac5fe0: cmp             x1, x0
    // 0xac5fe4: b.hs            #0xac6270
    // 0xac5fe8: LoadField: d0 = r2->field_2f
    //     0xac5fe8: ldur            s0, [x2, #0x2f]
    // 0xac5fec: fcvt            d1, s0
    // 0xac5ff0: mov             x0, x6
    // 0xac5ff4: r1 = 6
    //     0xac5ff4: movz            x1, #0x6
    // 0xac5ff8: cmp             x1, x0
    // 0xac5ffc: b.hs            #0xac6274
    // 0xac6000: LoadField: d0 = r3->field_2f
    //     0xac6000: ldur            s0, [x3, #0x2f]
    // 0xac6004: fcvt            d2, s0
    // 0xac6008: fsub            d0, d1, d2
    // 0xac600c: fcvt            s1, d0
    // 0xac6010: StoreField: r2->field_2f = d1
    //     0xac6010: stur            s1, [x2, #0x2f]
    // 0xac6014: mov             x0, x5
    // 0xac6018: r1 = 7
    //     0xac6018: movz            x1, #0x7
    // 0xac601c: cmp             x1, x0
    // 0xac6020: b.hs            #0xac6278
    // 0xac6024: LoadField: d0 = r2->field_33
    //     0xac6024: ldur            s0, [x2, #0x33]
    // 0xac6028: fcvt            d1, s0
    // 0xac602c: mov             x0, x6
    // 0xac6030: r1 = 7
    //     0xac6030: movz            x1, #0x7
    // 0xac6034: cmp             x1, x0
    // 0xac6038: b.hs            #0xac627c
    // 0xac603c: LoadField: d0 = r3->field_33
    //     0xac603c: ldur            s0, [x3, #0x33]
    // 0xac6040: fcvt            d2, s0
    // 0xac6044: fsub            d0, d1, d2
    // 0xac6048: fcvt            s1, d0
    // 0xac604c: StoreField: r2->field_33 = d1
    //     0xac604c: stur            s1, [x2, #0x33]
    // 0xac6050: mov             x0, x5
    // 0xac6054: r1 = 8
    //     0xac6054: movz            x1, #0x8
    // 0xac6058: cmp             x1, x0
    // 0xac605c: b.hs            #0xac6280
    // 0xac6060: LoadField: d0 = r2->field_37
    //     0xac6060: ldur            s0, [x2, #0x37]
    // 0xac6064: fcvt            d1, s0
    // 0xac6068: mov             x0, x6
    // 0xac606c: r1 = 8
    //     0xac606c: movz            x1, #0x8
    // 0xac6070: cmp             x1, x0
    // 0xac6074: b.hs            #0xac6284
    // 0xac6078: LoadField: d0 = r3->field_37
    //     0xac6078: ldur            s0, [x3, #0x37]
    // 0xac607c: fcvt            d2, s0
    // 0xac6080: fsub            d0, d1, d2
    // 0xac6084: fcvt            s1, d0
    // 0xac6088: StoreField: r2->field_37 = d1
    //     0xac6088: stur            s1, [x2, #0x37]
    // 0xac608c: mov             x0, x5
    // 0xac6090: r1 = 9
    //     0xac6090: movz            x1, #0x9
    // 0xac6094: cmp             x1, x0
    // 0xac6098: b.hs            #0xac6288
    // 0xac609c: LoadField: d0 = r2->field_3b
    //     0xac609c: ldur            s0, [x2, #0x3b]
    // 0xac60a0: fcvt            d1, s0
    // 0xac60a4: mov             x0, x6
    // 0xac60a8: r1 = 9
    //     0xac60a8: movz            x1, #0x9
    // 0xac60ac: cmp             x1, x0
    // 0xac60b0: b.hs            #0xac628c
    // 0xac60b4: LoadField: d0 = r3->field_3b
    //     0xac60b4: ldur            s0, [x3, #0x3b]
    // 0xac60b8: fcvt            d2, s0
    // 0xac60bc: fsub            d0, d1, d2
    // 0xac60c0: fcvt            s1, d0
    // 0xac60c4: StoreField: r2->field_3b = d1
    //     0xac60c4: stur            s1, [x2, #0x3b]
    // 0xac60c8: mov             x0, x5
    // 0xac60cc: r1 = 10
    //     0xac60cc: movz            x1, #0xa
    // 0xac60d0: cmp             x1, x0
    // 0xac60d4: b.hs            #0xac6290
    // 0xac60d8: LoadField: d0 = r2->field_3f
    //     0xac60d8: ldur            s0, [x2, #0x3f]
    // 0xac60dc: fcvt            d1, s0
    // 0xac60e0: mov             x0, x6
    // 0xac60e4: r1 = 10
    //     0xac60e4: movz            x1, #0xa
    // 0xac60e8: cmp             x1, x0
    // 0xac60ec: b.hs            #0xac6294
    // 0xac60f0: LoadField: d0 = r3->field_3f
    //     0xac60f0: ldur            s0, [x3, #0x3f]
    // 0xac60f4: fcvt            d2, s0
    // 0xac60f8: fsub            d0, d1, d2
    // 0xac60fc: fcvt            s1, d0
    // 0xac6100: StoreField: r2->field_3f = d1
    //     0xac6100: stur            s1, [x2, #0x3f]
    // 0xac6104: mov             x0, x5
    // 0xac6108: r1 = 11
    //     0xac6108: movz            x1, #0xb
    // 0xac610c: cmp             x1, x0
    // 0xac6110: b.hs            #0xac6298
    // 0xac6114: LoadField: d0 = r2->field_43
    //     0xac6114: ldur            s0, [x2, #0x43]
    // 0xac6118: fcvt            d1, s0
    // 0xac611c: mov             x0, x6
    // 0xac6120: r1 = 11
    //     0xac6120: movz            x1, #0xb
    // 0xac6124: cmp             x1, x0
    // 0xac6128: b.hs            #0xac629c
    // 0xac612c: LoadField: d0 = r3->field_43
    //     0xac612c: ldur            s0, [x3, #0x43]
    // 0xac6130: fcvt            d2, s0
    // 0xac6134: fsub            d0, d1, d2
    // 0xac6138: fcvt            s1, d0
    // 0xac613c: StoreField: r2->field_43 = d1
    //     0xac613c: stur            s1, [x2, #0x43]
    // 0xac6140: mov             x0, x5
    // 0xac6144: r1 = 12
    //     0xac6144: movz            x1, #0xc
    // 0xac6148: cmp             x1, x0
    // 0xac614c: b.hs            #0xac62a0
    // 0xac6150: LoadField: d0 = r2->field_47
    //     0xac6150: ldur            s0, [x2, #0x47]
    // 0xac6154: fcvt            d1, s0
    // 0xac6158: mov             x0, x6
    // 0xac615c: r1 = 12
    //     0xac615c: movz            x1, #0xc
    // 0xac6160: cmp             x1, x0
    // 0xac6164: b.hs            #0xac62a4
    // 0xac6168: LoadField: d0 = r3->field_47
    //     0xac6168: ldur            s0, [x3, #0x47]
    // 0xac616c: fcvt            d2, s0
    // 0xac6170: fsub            d0, d1, d2
    // 0xac6174: fcvt            s1, d0
    // 0xac6178: StoreField: r2->field_47 = d1
    //     0xac6178: stur            s1, [x2, #0x47]
    // 0xac617c: mov             x0, x5
    // 0xac6180: r1 = 13
    //     0xac6180: movz            x1, #0xd
    // 0xac6184: cmp             x1, x0
    // 0xac6188: b.hs            #0xac62a8
    // 0xac618c: LoadField: d0 = r2->field_4b
    //     0xac618c: ldur            s0, [x2, #0x4b]
    // 0xac6190: fcvt            d1, s0
    // 0xac6194: mov             x0, x6
    // 0xac6198: r1 = 13
    //     0xac6198: movz            x1, #0xd
    // 0xac619c: cmp             x1, x0
    // 0xac61a0: b.hs            #0xac62ac
    // 0xac61a4: LoadField: d0 = r3->field_4b
    //     0xac61a4: ldur            s0, [x3, #0x4b]
    // 0xac61a8: fcvt            d2, s0
    // 0xac61ac: fsub            d0, d1, d2
    // 0xac61b0: fcvt            s1, d0
    // 0xac61b4: StoreField: r2->field_4b = d1
    //     0xac61b4: stur            s1, [x2, #0x4b]
    // 0xac61b8: mov             x0, x5
    // 0xac61bc: r1 = 14
    //     0xac61bc: movz            x1, #0xe
    // 0xac61c0: cmp             x1, x0
    // 0xac61c4: b.hs            #0xac62b0
    // 0xac61c8: LoadField: d0 = r2->field_4f
    //     0xac61c8: ldur            s0, [x2, #0x4f]
    // 0xac61cc: fcvt            d1, s0
    // 0xac61d0: mov             x0, x6
    // 0xac61d4: r1 = 14
    //     0xac61d4: movz            x1, #0xe
    // 0xac61d8: cmp             x1, x0
    // 0xac61dc: b.hs            #0xac62b4
    // 0xac61e0: LoadField: d0 = r3->field_4f
    //     0xac61e0: ldur            s0, [x3, #0x4f]
    // 0xac61e4: fcvt            d2, s0
    // 0xac61e8: fsub            d0, d1, d2
    // 0xac61ec: fcvt            s1, d0
    // 0xac61f0: StoreField: r2->field_4f = d1
    //     0xac61f0: stur            s1, [x2, #0x4f]
    // 0xac61f4: mov             x0, x5
    // 0xac61f8: r1 = 15
    //     0xac61f8: movz            x1, #0xf
    // 0xac61fc: cmp             x1, x0
    // 0xac6200: b.hs            #0xac62b8
    // 0xac6204: LoadField: d0 = r2->field_53
    //     0xac6204: ldur            s0, [x2, #0x53]
    // 0xac6208: fcvt            d1, s0
    // 0xac620c: mov             x0, x6
    // 0xac6210: r1 = 15
    //     0xac6210: movz            x1, #0xf
    // 0xac6214: cmp             x1, x0
    // 0xac6218: b.hs            #0xac62bc
    // 0xac621c: LoadField: d0 = r3->field_53
    //     0xac621c: ldur            s0, [x3, #0x53]
    // 0xac6220: fcvt            d2, s0
    // 0xac6224: fsub            d0, d1, d2
    // 0xac6228: fcvt            s1, d0
    // 0xac622c: StoreField: r2->field_53 = d1
    //     0xac622c: stur            s1, [x2, #0x53]
    // 0xac6230: r0 = Null
    //     0xac6230: mov             x0, NULL
    // 0xac6234: LeaveFrame
    //     0xac6234: mov             SP, fp
    //     0xac6238: ldp             fp, lr, [SP], #0x10
    // 0xac623c: ret
    //     0xac623c: ret             
    // 0xac6240: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6240: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6244: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6244: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6248: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6248: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac624c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac624c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6250: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6250: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6254: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6254: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6258: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6258: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac625c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac625c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6260: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6260: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6264: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6264: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6268: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6268: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac626c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac626c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6270: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6270: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6274: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6274: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6278: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6278: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac627c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac627c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6280: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6280: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6284: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6284: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6288: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6288: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac628c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac628c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6290: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6290: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6294: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6294: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6298: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6298: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac629c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac629c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac62a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac62a0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac62a4: r0 = RangeErrorSharedWithFPURegs()
    //     0xac62a4: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac62a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac62a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac62ac: r0 = RangeErrorSharedWithFPURegs()
    //     0xac62ac: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac62b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac62b0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac62b4: r0 = RangeErrorSharedWithFPURegs()
    //     0xac62b4: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac62b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac62b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac62bc: r0 = RangeErrorSharedWithFPURegs()
    //     0xac62bc: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  double [](Matrix4, int) {
    // ** addr: 0xac62d8, size: 0xd8
    // 0xac62d8: EnterFrame
    //     0xac62d8: stp             fp, lr, [SP, #-0x10]!
    //     0xac62dc: mov             fp, SP
    // 0xac62e0: ldr             x0, [fp, #0x10]
    // 0xac62e4: r2 = Null
    //     0xac62e4: mov             x2, NULL
    // 0xac62e8: r1 = Null
    //     0xac62e8: mov             x1, NULL
    // 0xac62ec: branchIfSmi(r0, 0xac6314)
    //     0xac62ec: tbz             w0, #0, #0xac6314
    // 0xac62f0: r4 = LoadClassIdInstr(r0)
    //     0xac62f0: ldur            x4, [x0, #-1]
    //     0xac62f4: ubfx            x4, x4, #0xc, #0x14
    // 0xac62f8: sub             x4, x4, #0x3c
    // 0xac62fc: cmp             x4, #1
    // 0xac6300: b.ls            #0xac6314
    // 0xac6304: r8 = int
    //     0xac6304: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0xac6308: r3 = Null
    //     0xac6308: add             x3, PP, #0x38, lsl #12  ; [pp+0x38fc8] Null
    //     0xac630c: ldr             x3, [x3, #0xfc8]
    // 0xac6310: r0 = int()
    //     0xac6310: bl              #0xed4dc4  ; IsType_int_Stub
    // 0xac6314: ldr             x2, [fp, #0x18]
    // 0xac6318: LoadField: r3 = r2->field_7
    //     0xac6318: ldur            w3, [x2, #7]
    // 0xac631c: DecompressPointer r3
    //     0xac631c: add             x3, x3, HEAP, lsl #32
    // 0xac6320: LoadField: r2 = r3->field_13
    //     0xac6320: ldur            w2, [x3, #0x13]
    // 0xac6324: ldr             x4, [fp, #0x10]
    // 0xac6328: r5 = LoadInt32Instr(r4)
    //     0xac6328: sbfx            x5, x4, #1, #0x1f
    //     0xac632c: tbz             w4, #0, #0xac6334
    //     0xac6330: ldur            x5, [x4, #7]
    // 0xac6334: r0 = LoadInt32Instr(r2)
    //     0xac6334: sbfx            x0, x2, #1, #0x1f
    // 0xac6338: mov             x1, x5
    // 0xac633c: cmp             x1, x0
    // 0xac6340: b.hs            #0xac6384
    // 0xac6344: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0xac6344: add             x16, x3, x5, lsl #2
    //     0xac6348: ldur            s0, [x16, #0x17]
    // 0xac634c: fcvt            d1, s0
    // 0xac6350: r0 = inline_Allocate_Double()
    //     0xac6350: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xac6354: add             x0, x0, #0x10
    //     0xac6358: cmp             x1, x0
    //     0xac635c: b.ls            #0xac6388
    //     0xac6360: str             x0, [THR, #0x50]  ; THR::top
    //     0xac6364: sub             x0, x0, #0xf
    //     0xac6368: movz            x1, #0xe15c
    //     0xac636c: movk            x1, #0x3, lsl #16
    //     0xac6370: stur            x1, [x0, #-1]
    // 0xac6374: StoreField: r0->field_7 = d1
    //     0xac6374: stur            d1, [x0, #7]
    // 0xac6378: LeaveFrame
    //     0xac6378: mov             SP, fp
    //     0xac637c: ldp             fp, lr, [SP], #0x10
    // 0xac6380: ret
    //     0xac6380: ret             
    // 0xac6384: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xac6384: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xac6388: SaveReg d1
    //     0xac6388: str             q1, [SP, #-0x10]!
    // 0xac638c: r0 = AllocateDouble()
    //     0xac638c: bl              #0xec2254  ; AllocateDoubleStub
    // 0xac6390: RestoreReg d1
    //     0xac6390: ldr             q1, [SP], #0x10
    // 0xac6394: b               #0xac6374
  }
  _ setIdentity(/* No info */) {
    // ** addr: 0xac6398, size: 0x1b0
    // 0xac6398: EnterFrame
    //     0xac6398: stp             fp, lr, [SP, #-0x10]!
    //     0xac639c: mov             fp, SP
    // 0xac63a0: d0 = 0.000000
    //     0xac63a0: add             x17, PP, #0x26, lsl #12  ; [pp+0x261f8] IMM: 0x3f800000
    //     0xac63a4: ldr             s0, [x17, #0x1f8]
    // 0xac63a8: LoadField: r2 = r1->field_7
    //     0xac63a8: ldur            w2, [x1, #7]
    // 0xac63ac: DecompressPointer r2
    //     0xac63ac: add             x2, x2, HEAP, lsl #32
    // 0xac63b0: LoadField: r3 = r2->field_13
    //     0xac63b0: ldur            w3, [x2, #0x13]
    // 0xac63b4: r4 = LoadInt32Instr(r3)
    //     0xac63b4: sbfx            x4, x3, #1, #0x1f
    // 0xac63b8: mov             x0, x4
    // 0xac63bc: r1 = 0
    //     0xac63bc: movz            x1, #0
    // 0xac63c0: cmp             x1, x0
    // 0xac63c4: b.hs            #0xac6508
    // 0xac63c8: ArrayStore: r2[0] = d0  ; List_8
    //     0xac63c8: stur            s0, [x2, #0x17]
    // 0xac63cc: mov             x0, x4
    // 0xac63d0: r1 = 1
    //     0xac63d0: movz            x1, #0x1
    // 0xac63d4: cmp             x1, x0
    // 0xac63d8: b.hs            #0xac650c
    // 0xac63dc: StoreField: r2->field_1b = rZR
    //     0xac63dc: stur            wzr, [x2, #0x1b]
    // 0xac63e0: mov             x0, x4
    // 0xac63e4: r1 = 2
    //     0xac63e4: movz            x1, #0x2
    // 0xac63e8: cmp             x1, x0
    // 0xac63ec: b.hs            #0xac6510
    // 0xac63f0: StoreField: r2->field_1f = rZR
    //     0xac63f0: stur            wzr, [x2, #0x1f]
    // 0xac63f4: mov             x0, x4
    // 0xac63f8: r1 = 3
    //     0xac63f8: movz            x1, #0x3
    // 0xac63fc: cmp             x1, x0
    // 0xac6400: b.hs            #0xac6514
    // 0xac6404: StoreField: r2->field_23 = rZR
    //     0xac6404: stur            wzr, [x2, #0x23]
    // 0xac6408: mov             x0, x4
    // 0xac640c: r1 = 4
    //     0xac640c: movz            x1, #0x4
    // 0xac6410: cmp             x1, x0
    // 0xac6414: b.hs            #0xac6518
    // 0xac6418: StoreField: r2->field_27 = rZR
    //     0xac6418: stur            wzr, [x2, #0x27]
    // 0xac641c: mov             x0, x4
    // 0xac6420: r1 = 5
    //     0xac6420: movz            x1, #0x5
    // 0xac6424: cmp             x1, x0
    // 0xac6428: b.hs            #0xac651c
    // 0xac642c: StoreField: r2->field_2b = d0
    //     0xac642c: stur            s0, [x2, #0x2b]
    // 0xac6430: mov             x0, x4
    // 0xac6434: r1 = 6
    //     0xac6434: movz            x1, #0x6
    // 0xac6438: cmp             x1, x0
    // 0xac643c: b.hs            #0xac6520
    // 0xac6440: StoreField: r2->field_2f = rZR
    //     0xac6440: stur            wzr, [x2, #0x2f]
    // 0xac6444: mov             x0, x4
    // 0xac6448: r1 = 7
    //     0xac6448: movz            x1, #0x7
    // 0xac644c: cmp             x1, x0
    // 0xac6450: b.hs            #0xac6524
    // 0xac6454: StoreField: r2->field_33 = rZR
    //     0xac6454: stur            wzr, [x2, #0x33]
    // 0xac6458: mov             x0, x4
    // 0xac645c: r1 = 8
    //     0xac645c: movz            x1, #0x8
    // 0xac6460: cmp             x1, x0
    // 0xac6464: b.hs            #0xac6528
    // 0xac6468: StoreField: r2->field_37 = rZR
    //     0xac6468: stur            wzr, [x2, #0x37]
    // 0xac646c: mov             x0, x4
    // 0xac6470: r1 = 9
    //     0xac6470: movz            x1, #0x9
    // 0xac6474: cmp             x1, x0
    // 0xac6478: b.hs            #0xac652c
    // 0xac647c: StoreField: r2->field_3b = rZR
    //     0xac647c: stur            wzr, [x2, #0x3b]
    // 0xac6480: mov             x0, x4
    // 0xac6484: r1 = 10
    //     0xac6484: movz            x1, #0xa
    // 0xac6488: cmp             x1, x0
    // 0xac648c: b.hs            #0xac6530
    // 0xac6490: StoreField: r2->field_3f = d0
    //     0xac6490: stur            s0, [x2, #0x3f]
    // 0xac6494: mov             x0, x4
    // 0xac6498: r1 = 11
    //     0xac6498: movz            x1, #0xb
    // 0xac649c: cmp             x1, x0
    // 0xac64a0: b.hs            #0xac6534
    // 0xac64a4: StoreField: r2->field_43 = rZR
    //     0xac64a4: stur            wzr, [x2, #0x43]
    // 0xac64a8: mov             x0, x4
    // 0xac64ac: r1 = 12
    //     0xac64ac: movz            x1, #0xc
    // 0xac64b0: cmp             x1, x0
    // 0xac64b4: b.hs            #0xac6538
    // 0xac64b8: StoreField: r2->field_47 = rZR
    //     0xac64b8: stur            wzr, [x2, #0x47]
    // 0xac64bc: mov             x0, x4
    // 0xac64c0: r1 = 13
    //     0xac64c0: movz            x1, #0xd
    // 0xac64c4: cmp             x1, x0
    // 0xac64c8: b.hs            #0xac653c
    // 0xac64cc: StoreField: r2->field_4b = rZR
    //     0xac64cc: stur            wzr, [x2, #0x4b]
    // 0xac64d0: mov             x0, x4
    // 0xac64d4: r1 = 14
    //     0xac64d4: movz            x1, #0xe
    // 0xac64d8: cmp             x1, x0
    // 0xac64dc: b.hs            #0xac6540
    // 0xac64e0: StoreField: r2->field_4f = rZR
    //     0xac64e0: stur            wzr, [x2, #0x4f]
    // 0xac64e4: mov             x0, x4
    // 0xac64e8: r1 = 15
    //     0xac64e8: movz            x1, #0xf
    // 0xac64ec: cmp             x1, x0
    // 0xac64f0: b.hs            #0xac6544
    // 0xac64f4: StoreField: r2->field_53 = d0
    //     0xac64f4: stur            s0, [x2, #0x53]
    // 0xac64f8: r0 = Null
    //     0xac64f8: mov             x0, NULL
    // 0xac64fc: LeaveFrame
    //     0xac64fc: mov             SP, fp
    //     0xac6500: ldp             fp, lr, [SP], #0x10
    // 0xac6504: ret
    //     0xac6504: ret             
    // 0xac6508: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6508: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac650c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac650c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6510: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6510: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6514: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6514: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6518: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6518: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac651c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac651c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6520: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6520: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6524: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6524: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6528: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6528: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac652c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac652c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6530: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6530: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6534: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6534: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6538: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6538: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac653c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac653c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6540: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6540: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6544: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6544: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  _ rotateZ(/* No info */) {
    // ** addr: 0xac6634, size: 0x1fc
    // 0xac6634: EnterFrame
    //     0xac6634: stp             fp, lr, [SP, #-0x10]!
    //     0xac6638: mov             fp, SP
    // 0xac663c: AllocStack(0x10)
    //     0xac663c: sub             SP, SP, #0x10
    // 0xac6640: SetupParameters(Matrix4 this /* r1 => r1, fp-0x8 */, dynamic _ /* d0 => d1, fp-0x10 */)
    //     0xac6640: mov             v1.16b, v0.16b
    //     0xac6644: stur            x1, [fp, #-8]
    //     0xac6648: stur            d0, [fp, #-0x10]
    // 0xac664c: stp             fp, lr, [SP, #-0x10]!
    // 0xac6650: mov             fp, SP
    // 0xac6654: CallRuntime_LibcCos(double) -> double
    //     0xac6654: and             SP, SP, #0xfffffffffffffff0
    //     0xac6658: mov             sp, SP
    //     0xac665c: ldr             x16, [THR, #0x5a0]  ; THR::LibcCos
    //     0xac6660: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac6664: blr             x16
    //     0xac6668: movz            x16, #0x8
    //     0xac666c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac6670: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac6674: sub             sp, x16, #1, lsl #12
    //     0xac6678: mov             SP, fp
    //     0xac667c: ldp             fp, lr, [SP], #0x10
    // 0xac6680: mov             v1.16b, v0.16b
    // 0xac6684: ldur            d0, [fp, #-0x10]
    // 0xac6688: stur            d1, [fp, #-0x10]
    // 0xac668c: stp             fp, lr, [SP, #-0x10]!
    // 0xac6690: mov             fp, SP
    // 0xac6694: CallRuntime_LibcSin(double) -> double
    //     0xac6694: and             SP, SP, #0xfffffffffffffff0
    //     0xac6698: mov             sp, SP
    //     0xac669c: ldr             x16, [THR, #0x5a8]  ; THR::LibcSin
    //     0xac66a0: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac66a4: blr             x16
    //     0xac66a8: movz            x16, #0x8
    //     0xac66ac: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0xac66b0: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0xac66b4: sub             sp, x16, #1, lsl #12
    //     0xac66b8: mov             SP, fp
    //     0xac66bc: ldp             fp, lr, [SP], #0x10
    // 0xac66c0: ldur            x2, [fp, #-8]
    // 0xac66c4: LoadField: r3 = r2->field_7
    //     0xac66c4: ldur            w3, [x2, #7]
    // 0xac66c8: DecompressPointer r3
    //     0xac66c8: add             x3, x3, HEAP, lsl #32
    // 0xac66cc: LoadField: r2 = r3->field_13
    //     0xac66cc: ldur            w2, [x3, #0x13]
    // 0xac66d0: r4 = LoadInt32Instr(r2)
    //     0xac66d0: sbfx            x4, x2, #1, #0x1f
    // 0xac66d4: mov             x0, x4
    // 0xac66d8: r1 = 0
    //     0xac66d8: movz            x1, #0
    // 0xac66dc: cmp             x1, x0
    // 0xac66e0: b.hs            #0xac681c
    // 0xac66e4: ArrayLoad: d1 = r3[0]  ; List_8
    //     0xac66e4: ldur            s1, [x3, #0x17]
    // 0xac66e8: fcvt            d2, s1
    // 0xac66ec: ldur            d1, [fp, #-0x10]
    // 0xac66f0: fmul            d3, d2, d1
    // 0xac66f4: mov             x0, x4
    // 0xac66f8: r1 = 4
    //     0xac66f8: movz            x1, #0x4
    // 0xac66fc: cmp             x1, x0
    // 0xac6700: b.hs            #0xac6820
    // 0xac6704: LoadField: d4 = r3->field_27
    //     0xac6704: ldur            s4, [x3, #0x27]
    // 0xac6708: fcvt            d5, s4
    // 0xac670c: fmul            d4, d5, d0
    // 0xac6710: fadd            d6, d3, d4
    // 0xac6714: LoadField: d3 = r3->field_1b
    //     0xac6714: ldur            s3, [x3, #0x1b]
    // 0xac6718: fcvt            d4, s3
    // 0xac671c: fmul            d3, d4, d1
    // 0xac6720: mov             x0, x4
    // 0xac6724: r1 = 5
    //     0xac6724: movz            x1, #0x5
    // 0xac6728: cmp             x1, x0
    // 0xac672c: b.hs            #0xac6824
    // 0xac6730: LoadField: d7 = r3->field_2b
    //     0xac6730: ldur            s7, [x3, #0x2b]
    // 0xac6734: fcvt            d8, s7
    // 0xac6738: fmul            d7, d8, d0
    // 0xac673c: fadd            d9, d3, d7
    // 0xac6740: LoadField: d3 = r3->field_1f
    //     0xac6740: ldur            s3, [x3, #0x1f]
    // 0xac6744: fcvt            d7, s3
    // 0xac6748: fmul            d3, d7, d1
    // 0xac674c: mov             x0, x4
    // 0xac6750: r1 = 6
    //     0xac6750: movz            x1, #0x6
    // 0xac6754: cmp             x1, x0
    // 0xac6758: b.hs            #0xac6828
    // 0xac675c: LoadField: d10 = r3->field_2f
    //     0xac675c: ldur            s10, [x3, #0x2f]
    // 0xac6760: fcvt            d11, s10
    // 0xac6764: fmul            d10, d11, d0
    // 0xac6768: fadd            d12, d3, d10
    // 0xac676c: LoadField: d3 = r3->field_23
    //     0xac676c: ldur            s3, [x3, #0x23]
    // 0xac6770: fcvt            d10, s3
    // 0xac6774: fmul            d3, d10, d1
    // 0xac6778: mov             x0, x4
    // 0xac677c: r1 = 7
    //     0xac677c: movz            x1, #0x7
    // 0xac6780: cmp             x1, x0
    // 0xac6784: b.hs            #0xac682c
    // 0xac6788: LoadField: d13 = r3->field_33
    //     0xac6788: ldur            s13, [x3, #0x33]
    // 0xac678c: fcvt            d14, s13
    // 0xac6790: fmul            d13, d14, d0
    // 0xac6794: fadd            d15, d3, d13
    // 0xac6798: fneg            d3, d0
    // 0xac679c: fmul            d0, d2, d3
    // 0xac67a0: fmul            d2, d5, d1
    // 0xac67a4: fadd            d5, d0, d2
    // 0xac67a8: fmul            d0, d4, d3
    // 0xac67ac: fmul            d2, d8, d1
    // 0xac67b0: fadd            d4, d0, d2
    // 0xac67b4: fmul            d0, d7, d3
    // 0xac67b8: fmul            d2, d11, d1
    // 0xac67bc: fadd            d7, d0, d2
    // 0xac67c0: fmul            d0, d10, d3
    // 0xac67c4: fmul            d2, d14, d1
    // 0xac67c8: fadd            d1, d0, d2
    // 0xac67cc: fcvt            s0, d6
    // 0xac67d0: ArrayStore: r3[0] = d0  ; List_8
    //     0xac67d0: stur            s0, [x3, #0x17]
    // 0xac67d4: fcvt            s0, d9
    // 0xac67d8: StoreField: r3->field_1b = d0
    //     0xac67d8: stur            s0, [x3, #0x1b]
    // 0xac67dc: fcvt            s0, d12
    // 0xac67e0: StoreField: r3->field_1f = d0
    //     0xac67e0: stur            s0, [x3, #0x1f]
    // 0xac67e4: fcvt            s0, d15
    // 0xac67e8: StoreField: r3->field_23 = d0
    //     0xac67e8: stur            s0, [x3, #0x23]
    // 0xac67ec: fcvt            s0, d5
    // 0xac67f0: StoreField: r3->field_27 = d0
    //     0xac67f0: stur            s0, [x3, #0x27]
    // 0xac67f4: fcvt            s0, d4
    // 0xac67f8: StoreField: r3->field_2b = d0
    //     0xac67f8: stur            s0, [x3, #0x2b]
    // 0xac67fc: fcvt            s0, d7
    // 0xac6800: StoreField: r3->field_2f = d0
    //     0xac6800: stur            s0, [x3, #0x2f]
    // 0xac6804: fcvt            s0, d1
    // 0xac6808: StoreField: r3->field_33 = d0
    //     0xac6808: stur            s0, [x3, #0x33]
    // 0xac680c: r0 = Null
    //     0xac680c: mov             x0, NULL
    // 0xac6810: LeaveFrame
    //     0xac6810: mov             SP, fp
    //     0xac6814: ldp             fp, lr, [SP], #0x10
    // 0xac6818: ret
    //     0xac6818: ret             
    // 0xac681c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac681c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6820: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6820: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6824: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6824: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac6828: r0 = RangeErrorSharedWithFPURegs()
    //     0xac6828: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xac682c: r0 = RangeErrorSharedWithFPURegs()
    //     0xac682c: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
  factory _ Matrix4.identity(/* No info */) {
    // ** addr: 0xac6830, size: 0x54
    // 0xac6830: EnterFrame
    //     0xac6830: stp             fp, lr, [SP, #-0x10]!
    //     0xac6834: mov             fp, SP
    // 0xac6838: AllocStack(0x8)
    //     0xac6838: sub             SP, SP, #8
    // 0xac683c: CheckStackOverflow
    //     0xac683c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac6840: cmp             SP, x16
    //     0xac6844: b.ls            #0xac687c
    // 0xac6848: r0 = Matrix4()
    //     0xac6848: bl              #0xac5cd4  ; AllocateMatrix4Stub -> Matrix4 (size=0xc)
    // 0xac684c: r4 = 32
    //     0xac684c: movz            x4, #0x20
    // 0xac6850: stur            x0, [fp, #-8]
    // 0xac6854: r0 = AllocateFloat32Array()
    //     0xac6854: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0xac6858: mov             x1, x0
    // 0xac685c: ldur            x0, [fp, #-8]
    // 0xac6860: StoreField: r0->field_7 = r1
    //     0xac6860: stur            w1, [x0, #7]
    // 0xac6864: mov             x1, x0
    // 0xac6868: r0 = setIdentity()
    //     0xac6868: bl              #0xac6398  ; [package:vector_math/vector_math.dart] Matrix4::setIdentity
    // 0xac686c: ldur            x0, [fp, #-8]
    // 0xac6870: LeaveFrame
    //     0xac6870: mov             SP, fp
    //     0xac6874: ldp             fp, lr, [SP], #0x10
    // 0xac6878: ret
    //     0xac6878: ret             
    // 0xac687c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac687c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac6880: b               #0xac6848
  }
  _ toString(/* No info */) {
    // ** addr: 0xc41dd4, size: 0x15c
    // 0xc41dd4: EnterFrame
    //     0xc41dd4: stp             fp, lr, [SP, #-0x10]!
    //     0xc41dd8: mov             fp, SP
    // 0xc41ddc: AllocStack(0x10)
    //     0xc41ddc: sub             SP, SP, #0x10
    // 0xc41de0: CheckStackOverflow
    //     0xc41de0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc41de4: cmp             SP, x16
    //     0xc41de8: b.ls            #0xc41f28
    // 0xc41dec: r1 = Null
    //     0xc41dec: mov             x1, NULL
    // 0xc41df0: r2 = 18
    //     0xc41df0: movz            x2, #0x12
    // 0xc41df4: r0 = AllocateArray()
    //     0xc41df4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc41df8: stur            x0, [fp, #-8]
    // 0xc41dfc: r16 = "[0] "
    //     0xc41dfc: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d840] "[0] "
    //     0xc41e00: ldr             x16, [x16, #0x840]
    // 0xc41e04: StoreField: r0->field_f = r16
    //     0xc41e04: stur            w16, [x0, #0xf]
    // 0xc41e08: ldr             x1, [fp, #0x10]
    // 0xc41e0c: r2 = 0
    //     0xc41e0c: movz            x2, #0
    // 0xc41e10: r0 = getRow()
    //     0xc41e10: bl              #0xc41f30  ; [package:vector_math/vector_math.dart] Matrix4::getRow
    // 0xc41e14: ldur            x1, [fp, #-8]
    // 0xc41e18: ArrayStore: r1[1] = r0  ; List_4
    //     0xc41e18: add             x25, x1, #0x13
    //     0xc41e1c: str             w0, [x25]
    //     0xc41e20: tbz             w0, #0, #0xc41e3c
    //     0xc41e24: ldurb           w16, [x1, #-1]
    //     0xc41e28: ldurb           w17, [x0, #-1]
    //     0xc41e2c: and             x16, x17, x16, lsr #2
    //     0xc41e30: tst             x16, HEAP, lsr #32
    //     0xc41e34: b.eq            #0xc41e3c
    //     0xc41e38: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc41e3c: ldur            x0, [fp, #-8]
    // 0xc41e40: r16 = "\n[1] "
    //     0xc41e40: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d848] "\n[1] "
    //     0xc41e44: ldr             x16, [x16, #0x848]
    // 0xc41e48: ArrayStore: r0[0] = r16  ; List_4
    //     0xc41e48: stur            w16, [x0, #0x17]
    // 0xc41e4c: ldr             x1, [fp, #0x10]
    // 0xc41e50: r2 = 1
    //     0xc41e50: movz            x2, #0x1
    // 0xc41e54: r0 = getRow()
    //     0xc41e54: bl              #0xc41f30  ; [package:vector_math/vector_math.dart] Matrix4::getRow
    // 0xc41e58: ldur            x1, [fp, #-8]
    // 0xc41e5c: ArrayStore: r1[3] = r0  ; List_4
    //     0xc41e5c: add             x25, x1, #0x1b
    //     0xc41e60: str             w0, [x25]
    //     0xc41e64: tbz             w0, #0, #0xc41e80
    //     0xc41e68: ldurb           w16, [x1, #-1]
    //     0xc41e6c: ldurb           w17, [x0, #-1]
    //     0xc41e70: and             x16, x17, x16, lsr #2
    //     0xc41e74: tst             x16, HEAP, lsr #32
    //     0xc41e78: b.eq            #0xc41e80
    //     0xc41e7c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc41e80: ldur            x0, [fp, #-8]
    // 0xc41e84: r16 = "\n[2] "
    //     0xc41e84: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d850] "\n[2] "
    //     0xc41e88: ldr             x16, [x16, #0x850]
    // 0xc41e8c: StoreField: r0->field_1f = r16
    //     0xc41e8c: stur            w16, [x0, #0x1f]
    // 0xc41e90: ldr             x1, [fp, #0x10]
    // 0xc41e94: r2 = 2
    //     0xc41e94: movz            x2, #0x2
    // 0xc41e98: r0 = getRow()
    //     0xc41e98: bl              #0xc41f30  ; [package:vector_math/vector_math.dart] Matrix4::getRow
    // 0xc41e9c: ldur            x1, [fp, #-8]
    // 0xc41ea0: ArrayStore: r1[5] = r0  ; List_4
    //     0xc41ea0: add             x25, x1, #0x23
    //     0xc41ea4: str             w0, [x25]
    //     0xc41ea8: tbz             w0, #0, #0xc41ec4
    //     0xc41eac: ldurb           w16, [x1, #-1]
    //     0xc41eb0: ldurb           w17, [x0, #-1]
    //     0xc41eb4: and             x16, x17, x16, lsr #2
    //     0xc41eb8: tst             x16, HEAP, lsr #32
    //     0xc41ebc: b.eq            #0xc41ec4
    //     0xc41ec0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc41ec4: ldur            x0, [fp, #-8]
    // 0xc41ec8: r16 = "\n[3] "
    //     0xc41ec8: add             x16, PP, #0x1d, lsl #12  ; [pp+0x1d858] "\n[3] "
    //     0xc41ecc: ldr             x16, [x16, #0x858]
    // 0xc41ed0: StoreField: r0->field_27 = r16
    //     0xc41ed0: stur            w16, [x0, #0x27]
    // 0xc41ed4: ldr             x1, [fp, #0x10]
    // 0xc41ed8: r2 = 3
    //     0xc41ed8: movz            x2, #0x3
    // 0xc41edc: r0 = getRow()
    //     0xc41edc: bl              #0xc41f30  ; [package:vector_math/vector_math.dart] Matrix4::getRow
    // 0xc41ee0: ldur            x1, [fp, #-8]
    // 0xc41ee4: ArrayStore: r1[7] = r0  ; List_4
    //     0xc41ee4: add             x25, x1, #0x2b
    //     0xc41ee8: str             w0, [x25]
    //     0xc41eec: tbz             w0, #0, #0xc41f08
    //     0xc41ef0: ldurb           w16, [x1, #-1]
    //     0xc41ef4: ldurb           w17, [x0, #-1]
    //     0xc41ef8: and             x16, x17, x16, lsr #2
    //     0xc41efc: tst             x16, HEAP, lsr #32
    //     0xc41f00: b.eq            #0xc41f08
    //     0xc41f04: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc41f08: ldur            x0, [fp, #-8]
    // 0xc41f0c: r16 = "\n"
    //     0xc41f0c: ldr             x16, [PP, #0x4b0]  ; [pp+0x4b0] "\n"
    // 0xc41f10: StoreField: r0->field_2f = r16
    //     0xc41f10: stur            w16, [x0, #0x2f]
    // 0xc41f14: str             x0, [SP]
    // 0xc41f18: r0 = _interpolate()
    //     0xc41f18: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc41f1c: LeaveFrame
    //     0xc41f1c: mov             SP, fp
    //     0xc41f20: ldp             fp, lr, [SP], #0x10
    // 0xc41f24: ret
    //     0xc41f24: ret             
    // 0xc41f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc41f28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc41f2c: b               #0xc41dec
  }
  _ getRow(/* No info */) {
    // ** addr: 0xc41f30, size: 0xec
    // 0xc41f30: EnterFrame
    //     0xc41f30: stp             fp, lr, [SP, #-0x10]!
    //     0xc41f34: mov             fp, SP
    // 0xc41f38: AllocStack(0x18)
    //     0xc41f38: sub             SP, SP, #0x18
    // 0xc41f3c: SetupParameters(Matrix4 this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc41f3c: mov             x0, x1
    //     0xc41f40: stur            x1, [fp, #-8]
    //     0xc41f44: mov             x1, x2
    //     0xc41f48: stur            x2, [fp, #-0x10]
    // 0xc41f4c: r0 = Vector4()
    //     0xc41f4c: bl              #0xc42710  ; AllocateVector4Stub -> Vector4 (size=0xc)
    // 0xc41f50: r4 = 8
    //     0xc41f50: movz            x4, #0x8
    // 0xc41f54: stur            x0, [fp, #-0x18]
    // 0xc41f58: r0 = AllocateFloat32Array()
    //     0xc41f58: bl              #0xec19f8  ; AllocateFloat32ArrayStub
    // 0xc41f5c: mov             x3, x0
    // 0xc41f60: ldur            x2, [fp, #-0x18]
    // 0xc41f64: StoreField: r2->field_7 = r3
    //     0xc41f64: stur            w3, [x2, #7]
    // 0xc41f68: ldur            x4, [fp, #-8]
    // 0xc41f6c: LoadField: r5 = r4->field_7
    //     0xc41f6c: ldur            w5, [x4, #7]
    // 0xc41f70: DecompressPointer r5
    //     0xc41f70: add             x5, x5, HEAP, lsl #32
    // 0xc41f74: LoadField: r4 = r5->field_13
    //     0xc41f74: ldur            w4, [x5, #0x13]
    // 0xc41f78: r6 = LoadInt32Instr(r4)
    //     0xc41f78: sbfx            x6, x4, #1, #0x1f
    // 0xc41f7c: mov             x0, x6
    // 0xc41f80: ldur            x1, [fp, #-0x10]
    // 0xc41f84: cmp             x1, x0
    // 0xc41f88: b.hs            #0xc4200c
    // 0xc41f8c: ldur            x4, [fp, #-0x10]
    // 0xc41f90: ArrayLoad: d0 = r5[r4]  ; List_8
    //     0xc41f90: add             x16, x5, x4, lsl #2
    //     0xc41f94: ldur            s0, [x16, #0x17]
    // 0xc41f98: ArrayStore: r3[0] = d0  ; List_8
    //     0xc41f98: stur            s0, [x3, #0x17]
    // 0xc41f9c: add             x7, x4, #4
    // 0xc41fa0: mov             x0, x6
    // 0xc41fa4: mov             x1, x7
    // 0xc41fa8: cmp             x1, x0
    // 0xc41fac: b.hs            #0xc42010
    // 0xc41fb0: ArrayLoad: d0 = r5[r7]  ; List_8
    //     0xc41fb0: add             x16, x5, x7, lsl #2
    //     0xc41fb4: ldur            s0, [x16, #0x17]
    // 0xc41fb8: StoreField: r3->field_1b = d0
    //     0xc41fb8: stur            s0, [x3, #0x1b]
    // 0xc41fbc: add             x7, x4, #8
    // 0xc41fc0: mov             x0, x6
    // 0xc41fc4: mov             x1, x7
    // 0xc41fc8: cmp             x1, x0
    // 0xc41fcc: b.hs            #0xc42014
    // 0xc41fd0: ArrayLoad: d0 = r5[r7]  ; List_8
    //     0xc41fd0: add             x16, x5, x7, lsl #2
    //     0xc41fd4: ldur            s0, [x16, #0x17]
    // 0xc41fd8: StoreField: r3->field_1f = d0
    //     0xc41fd8: stur            s0, [x3, #0x1f]
    // 0xc41fdc: add             x7, x4, #0xc
    // 0xc41fe0: mov             x0, x6
    // 0xc41fe4: mov             x1, x7
    // 0xc41fe8: cmp             x1, x0
    // 0xc41fec: b.hs            #0xc42018
    // 0xc41ff0: ArrayLoad: d0 = r5[r7]  ; List_8
    //     0xc41ff0: add             x16, x5, x7, lsl #2
    //     0xc41ff4: ldur            s0, [x16, #0x17]
    // 0xc41ff8: StoreField: r3->field_23 = d0
    //     0xc41ff8: stur            s0, [x3, #0x23]
    // 0xc41ffc: mov             x0, x2
    // 0xc42000: LeaveFrame
    //     0xc42000: mov             SP, fp
    //     0xc42004: ldp             fp, lr, [SP], #0x10
    // 0xc42008: ret
    //     0xc42008: ret             
    // 0xc4200c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc4200c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc42010: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42010: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc42014: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42014: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc42018: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc42018: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7e3b4, size: 0x480
    // 0xd7e3b4: EnterFrame
    //     0xd7e3b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd7e3b8: mov             fp, SP
    // 0xd7e3bc: ldr             x2, [fp, #0x10]
    // 0xd7e3c0: cmp             w2, NULL
    // 0xd7e3c4: b.ne            #0xd7e3d8
    // 0xd7e3c8: r0 = false
    //     0xd7e3c8: add             x0, NULL, #0x30  ; false
    // 0xd7e3cc: LeaveFrame
    //     0xd7e3cc: mov             SP, fp
    //     0xd7e3d0: ldp             fp, lr, [SP], #0x10
    // 0xd7e3d4: ret
    //     0xd7e3d4: ret             
    // 0xd7e3d8: r3 = 60
    //     0xd7e3d8: movz            x3, #0x3c
    // 0xd7e3dc: branchIfSmi(r2, 0xd7e3e8)
    //     0xd7e3dc: tbz             w2, #0, #0xd7e3e8
    // 0xd7e3e0: r3 = LoadClassIdInstr(r2)
    //     0xd7e3e0: ldur            x3, [x2, #-1]
    //     0xd7e3e4: ubfx            x3, x3, #0xc, #0x14
    // 0xd7e3e8: cmp             x3, #0x19d
    // 0xd7e3ec: b.ne            #0xd7e7a4
    // 0xd7e3f0: ldr             x3, [fp, #0x18]
    // 0xd7e3f4: LoadField: r4 = r3->field_7
    //     0xd7e3f4: ldur            w4, [x3, #7]
    // 0xd7e3f8: DecompressPointer r4
    //     0xd7e3f8: add             x4, x4, HEAP, lsl #32
    // 0xd7e3fc: LoadField: r3 = r4->field_13
    //     0xd7e3fc: ldur            w3, [x4, #0x13]
    // 0xd7e400: r5 = LoadInt32Instr(r3)
    //     0xd7e400: sbfx            x5, x3, #1, #0x1f
    // 0xd7e404: mov             x0, x5
    // 0xd7e408: r1 = 0
    //     0xd7e408: movz            x1, #0
    // 0xd7e40c: cmp             x1, x0
    // 0xd7e410: b.hs            #0xd7e7b4
    // 0xd7e414: ArrayLoad: d0 = r4[0]  ; List_8
    //     0xd7e414: ldur            s0, [x4, #0x17]
    // 0xd7e418: fcvt            d1, s0
    // 0xd7e41c: LoadField: r3 = r2->field_7
    //     0xd7e41c: ldur            w3, [x2, #7]
    // 0xd7e420: DecompressPointer r3
    //     0xd7e420: add             x3, x3, HEAP, lsl #32
    // 0xd7e424: LoadField: r2 = r3->field_13
    //     0xd7e424: ldur            w2, [x3, #0x13]
    // 0xd7e428: r6 = LoadInt32Instr(r2)
    //     0xd7e428: sbfx            x6, x2, #1, #0x1f
    // 0xd7e42c: mov             x0, x6
    // 0xd7e430: r1 = 0
    //     0xd7e430: movz            x1, #0
    // 0xd7e434: cmp             x1, x0
    // 0xd7e438: b.hs            #0xd7e7b8
    // 0xd7e43c: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xd7e43c: ldur            s0, [x3, #0x17]
    // 0xd7e440: fcvt            d2, s0
    // 0xd7e444: fcmp            d1, d2
    // 0xd7e448: b.ne            #0xd7e7a4
    // 0xd7e44c: mov             x0, x5
    // 0xd7e450: r1 = 1
    //     0xd7e450: movz            x1, #0x1
    // 0xd7e454: cmp             x1, x0
    // 0xd7e458: b.hs            #0xd7e7bc
    // 0xd7e45c: LoadField: d0 = r4->field_1b
    //     0xd7e45c: ldur            s0, [x4, #0x1b]
    // 0xd7e460: fcvt            d1, s0
    // 0xd7e464: mov             x0, x6
    // 0xd7e468: r1 = 1
    //     0xd7e468: movz            x1, #0x1
    // 0xd7e46c: cmp             x1, x0
    // 0xd7e470: b.hs            #0xd7e7c0
    // 0xd7e474: LoadField: d0 = r3->field_1b
    //     0xd7e474: ldur            s0, [x3, #0x1b]
    // 0xd7e478: fcvt            d2, s0
    // 0xd7e47c: fcmp            d1, d2
    // 0xd7e480: b.ne            #0xd7e7a4
    // 0xd7e484: mov             x0, x5
    // 0xd7e488: r1 = 2
    //     0xd7e488: movz            x1, #0x2
    // 0xd7e48c: cmp             x1, x0
    // 0xd7e490: b.hs            #0xd7e7c4
    // 0xd7e494: LoadField: d0 = r4->field_1f
    //     0xd7e494: ldur            s0, [x4, #0x1f]
    // 0xd7e498: fcvt            d1, s0
    // 0xd7e49c: mov             x0, x6
    // 0xd7e4a0: r1 = 2
    //     0xd7e4a0: movz            x1, #0x2
    // 0xd7e4a4: cmp             x1, x0
    // 0xd7e4a8: b.hs            #0xd7e7c8
    // 0xd7e4ac: LoadField: d0 = r3->field_1f
    //     0xd7e4ac: ldur            s0, [x3, #0x1f]
    // 0xd7e4b0: fcvt            d2, s0
    // 0xd7e4b4: fcmp            d1, d2
    // 0xd7e4b8: b.ne            #0xd7e7a4
    // 0xd7e4bc: mov             x0, x5
    // 0xd7e4c0: r1 = 3
    //     0xd7e4c0: movz            x1, #0x3
    // 0xd7e4c4: cmp             x1, x0
    // 0xd7e4c8: b.hs            #0xd7e7cc
    // 0xd7e4cc: LoadField: d0 = r4->field_23
    //     0xd7e4cc: ldur            s0, [x4, #0x23]
    // 0xd7e4d0: fcvt            d1, s0
    // 0xd7e4d4: mov             x0, x6
    // 0xd7e4d8: r1 = 3
    //     0xd7e4d8: movz            x1, #0x3
    // 0xd7e4dc: cmp             x1, x0
    // 0xd7e4e0: b.hs            #0xd7e7d0
    // 0xd7e4e4: LoadField: d0 = r3->field_23
    //     0xd7e4e4: ldur            s0, [x3, #0x23]
    // 0xd7e4e8: fcvt            d2, s0
    // 0xd7e4ec: fcmp            d1, d2
    // 0xd7e4f0: b.ne            #0xd7e7a4
    // 0xd7e4f4: mov             x0, x5
    // 0xd7e4f8: r1 = 4
    //     0xd7e4f8: movz            x1, #0x4
    // 0xd7e4fc: cmp             x1, x0
    // 0xd7e500: b.hs            #0xd7e7d4
    // 0xd7e504: LoadField: d0 = r4->field_27
    //     0xd7e504: ldur            s0, [x4, #0x27]
    // 0xd7e508: fcvt            d1, s0
    // 0xd7e50c: mov             x0, x6
    // 0xd7e510: r1 = 4
    //     0xd7e510: movz            x1, #0x4
    // 0xd7e514: cmp             x1, x0
    // 0xd7e518: b.hs            #0xd7e7d8
    // 0xd7e51c: LoadField: d0 = r3->field_27
    //     0xd7e51c: ldur            s0, [x3, #0x27]
    // 0xd7e520: fcvt            d2, s0
    // 0xd7e524: fcmp            d1, d2
    // 0xd7e528: b.ne            #0xd7e7a4
    // 0xd7e52c: mov             x0, x5
    // 0xd7e530: r1 = 5
    //     0xd7e530: movz            x1, #0x5
    // 0xd7e534: cmp             x1, x0
    // 0xd7e538: b.hs            #0xd7e7dc
    // 0xd7e53c: LoadField: d0 = r4->field_2b
    //     0xd7e53c: ldur            s0, [x4, #0x2b]
    // 0xd7e540: fcvt            d1, s0
    // 0xd7e544: mov             x0, x6
    // 0xd7e548: r1 = 5
    //     0xd7e548: movz            x1, #0x5
    // 0xd7e54c: cmp             x1, x0
    // 0xd7e550: b.hs            #0xd7e7e0
    // 0xd7e554: LoadField: d0 = r3->field_2b
    //     0xd7e554: ldur            s0, [x3, #0x2b]
    // 0xd7e558: fcvt            d2, s0
    // 0xd7e55c: fcmp            d1, d2
    // 0xd7e560: b.ne            #0xd7e7a4
    // 0xd7e564: mov             x0, x5
    // 0xd7e568: r1 = 6
    //     0xd7e568: movz            x1, #0x6
    // 0xd7e56c: cmp             x1, x0
    // 0xd7e570: b.hs            #0xd7e7e4
    // 0xd7e574: LoadField: d0 = r4->field_2f
    //     0xd7e574: ldur            s0, [x4, #0x2f]
    // 0xd7e578: fcvt            d1, s0
    // 0xd7e57c: mov             x0, x6
    // 0xd7e580: r1 = 6
    //     0xd7e580: movz            x1, #0x6
    // 0xd7e584: cmp             x1, x0
    // 0xd7e588: b.hs            #0xd7e7e8
    // 0xd7e58c: LoadField: d0 = r3->field_2f
    //     0xd7e58c: ldur            s0, [x3, #0x2f]
    // 0xd7e590: fcvt            d2, s0
    // 0xd7e594: fcmp            d1, d2
    // 0xd7e598: b.ne            #0xd7e7a4
    // 0xd7e59c: mov             x0, x5
    // 0xd7e5a0: r1 = 7
    //     0xd7e5a0: movz            x1, #0x7
    // 0xd7e5a4: cmp             x1, x0
    // 0xd7e5a8: b.hs            #0xd7e7ec
    // 0xd7e5ac: LoadField: d0 = r4->field_33
    //     0xd7e5ac: ldur            s0, [x4, #0x33]
    // 0xd7e5b0: fcvt            d1, s0
    // 0xd7e5b4: mov             x0, x6
    // 0xd7e5b8: r1 = 7
    //     0xd7e5b8: movz            x1, #0x7
    // 0xd7e5bc: cmp             x1, x0
    // 0xd7e5c0: b.hs            #0xd7e7f0
    // 0xd7e5c4: LoadField: d0 = r3->field_33
    //     0xd7e5c4: ldur            s0, [x3, #0x33]
    // 0xd7e5c8: fcvt            d2, s0
    // 0xd7e5cc: fcmp            d1, d2
    // 0xd7e5d0: b.ne            #0xd7e7a4
    // 0xd7e5d4: mov             x0, x5
    // 0xd7e5d8: r1 = 8
    //     0xd7e5d8: movz            x1, #0x8
    // 0xd7e5dc: cmp             x1, x0
    // 0xd7e5e0: b.hs            #0xd7e7f4
    // 0xd7e5e4: LoadField: d0 = r4->field_37
    //     0xd7e5e4: ldur            s0, [x4, #0x37]
    // 0xd7e5e8: fcvt            d1, s0
    // 0xd7e5ec: mov             x0, x6
    // 0xd7e5f0: r1 = 8
    //     0xd7e5f0: movz            x1, #0x8
    // 0xd7e5f4: cmp             x1, x0
    // 0xd7e5f8: b.hs            #0xd7e7f8
    // 0xd7e5fc: LoadField: d0 = r3->field_37
    //     0xd7e5fc: ldur            s0, [x3, #0x37]
    // 0xd7e600: fcvt            d2, s0
    // 0xd7e604: fcmp            d1, d2
    // 0xd7e608: b.ne            #0xd7e7a4
    // 0xd7e60c: mov             x0, x5
    // 0xd7e610: r1 = 9
    //     0xd7e610: movz            x1, #0x9
    // 0xd7e614: cmp             x1, x0
    // 0xd7e618: b.hs            #0xd7e7fc
    // 0xd7e61c: LoadField: d0 = r4->field_3b
    //     0xd7e61c: ldur            s0, [x4, #0x3b]
    // 0xd7e620: fcvt            d1, s0
    // 0xd7e624: mov             x0, x6
    // 0xd7e628: r1 = 9
    //     0xd7e628: movz            x1, #0x9
    // 0xd7e62c: cmp             x1, x0
    // 0xd7e630: b.hs            #0xd7e800
    // 0xd7e634: LoadField: d0 = r3->field_3b
    //     0xd7e634: ldur            s0, [x3, #0x3b]
    // 0xd7e638: fcvt            d2, s0
    // 0xd7e63c: fcmp            d1, d2
    // 0xd7e640: b.ne            #0xd7e7a4
    // 0xd7e644: mov             x0, x5
    // 0xd7e648: r1 = 10
    //     0xd7e648: movz            x1, #0xa
    // 0xd7e64c: cmp             x1, x0
    // 0xd7e650: b.hs            #0xd7e804
    // 0xd7e654: LoadField: d0 = r4->field_3f
    //     0xd7e654: ldur            s0, [x4, #0x3f]
    // 0xd7e658: fcvt            d1, s0
    // 0xd7e65c: mov             x0, x6
    // 0xd7e660: r1 = 10
    //     0xd7e660: movz            x1, #0xa
    // 0xd7e664: cmp             x1, x0
    // 0xd7e668: b.hs            #0xd7e808
    // 0xd7e66c: LoadField: d0 = r3->field_3f
    //     0xd7e66c: ldur            s0, [x3, #0x3f]
    // 0xd7e670: fcvt            d2, s0
    // 0xd7e674: fcmp            d1, d2
    // 0xd7e678: b.ne            #0xd7e7a4
    // 0xd7e67c: mov             x0, x5
    // 0xd7e680: r1 = 11
    //     0xd7e680: movz            x1, #0xb
    // 0xd7e684: cmp             x1, x0
    // 0xd7e688: b.hs            #0xd7e80c
    // 0xd7e68c: LoadField: d0 = r4->field_43
    //     0xd7e68c: ldur            s0, [x4, #0x43]
    // 0xd7e690: fcvt            d1, s0
    // 0xd7e694: mov             x0, x6
    // 0xd7e698: r1 = 11
    //     0xd7e698: movz            x1, #0xb
    // 0xd7e69c: cmp             x1, x0
    // 0xd7e6a0: b.hs            #0xd7e810
    // 0xd7e6a4: LoadField: d0 = r3->field_43
    //     0xd7e6a4: ldur            s0, [x3, #0x43]
    // 0xd7e6a8: fcvt            d2, s0
    // 0xd7e6ac: fcmp            d1, d2
    // 0xd7e6b0: b.ne            #0xd7e7a4
    // 0xd7e6b4: mov             x0, x5
    // 0xd7e6b8: r1 = 12
    //     0xd7e6b8: movz            x1, #0xc
    // 0xd7e6bc: cmp             x1, x0
    // 0xd7e6c0: b.hs            #0xd7e814
    // 0xd7e6c4: LoadField: d0 = r4->field_47
    //     0xd7e6c4: ldur            s0, [x4, #0x47]
    // 0xd7e6c8: fcvt            d1, s0
    // 0xd7e6cc: mov             x0, x6
    // 0xd7e6d0: r1 = 12
    //     0xd7e6d0: movz            x1, #0xc
    // 0xd7e6d4: cmp             x1, x0
    // 0xd7e6d8: b.hs            #0xd7e818
    // 0xd7e6dc: LoadField: d0 = r3->field_47
    //     0xd7e6dc: ldur            s0, [x3, #0x47]
    // 0xd7e6e0: fcvt            d2, s0
    // 0xd7e6e4: fcmp            d1, d2
    // 0xd7e6e8: b.ne            #0xd7e7a4
    // 0xd7e6ec: mov             x0, x5
    // 0xd7e6f0: r1 = 13
    //     0xd7e6f0: movz            x1, #0xd
    // 0xd7e6f4: cmp             x1, x0
    // 0xd7e6f8: b.hs            #0xd7e81c
    // 0xd7e6fc: LoadField: d0 = r4->field_4b
    //     0xd7e6fc: ldur            s0, [x4, #0x4b]
    // 0xd7e700: fcvt            d1, s0
    // 0xd7e704: mov             x0, x6
    // 0xd7e708: r1 = 13
    //     0xd7e708: movz            x1, #0xd
    // 0xd7e70c: cmp             x1, x0
    // 0xd7e710: b.hs            #0xd7e820
    // 0xd7e714: LoadField: d0 = r3->field_4b
    //     0xd7e714: ldur            s0, [x3, #0x4b]
    // 0xd7e718: fcvt            d2, s0
    // 0xd7e71c: fcmp            d1, d2
    // 0xd7e720: b.ne            #0xd7e7a4
    // 0xd7e724: mov             x0, x5
    // 0xd7e728: r1 = 14
    //     0xd7e728: movz            x1, #0xe
    // 0xd7e72c: cmp             x1, x0
    // 0xd7e730: b.hs            #0xd7e824
    // 0xd7e734: LoadField: d0 = r4->field_4f
    //     0xd7e734: ldur            s0, [x4, #0x4f]
    // 0xd7e738: fcvt            d1, s0
    // 0xd7e73c: mov             x0, x6
    // 0xd7e740: r1 = 14
    //     0xd7e740: movz            x1, #0xe
    // 0xd7e744: cmp             x1, x0
    // 0xd7e748: b.hs            #0xd7e828
    // 0xd7e74c: LoadField: d0 = r3->field_4f
    //     0xd7e74c: ldur            s0, [x3, #0x4f]
    // 0xd7e750: fcvt            d2, s0
    // 0xd7e754: fcmp            d1, d2
    // 0xd7e758: b.ne            #0xd7e7a4
    // 0xd7e75c: mov             x0, x5
    // 0xd7e760: r1 = 15
    //     0xd7e760: movz            x1, #0xf
    // 0xd7e764: cmp             x1, x0
    // 0xd7e768: b.hs            #0xd7e82c
    // 0xd7e76c: LoadField: d0 = r4->field_53
    //     0xd7e76c: ldur            s0, [x4, #0x53]
    // 0xd7e770: fcvt            d1, s0
    // 0xd7e774: mov             x0, x6
    // 0xd7e778: r1 = 15
    //     0xd7e778: movz            x1, #0xf
    // 0xd7e77c: cmp             x1, x0
    // 0xd7e780: b.hs            #0xd7e830
    // 0xd7e784: LoadField: d0 = r3->field_53
    //     0xd7e784: ldur            s0, [x3, #0x53]
    // 0xd7e788: fcvt            d2, s0
    // 0xd7e78c: fcmp            d1, d2
    // 0xd7e790: r16 = true
    //     0xd7e790: add             x16, NULL, #0x20  ; true
    // 0xd7e794: r17 = false
    //     0xd7e794: add             x17, NULL, #0x30  ; false
    // 0xd7e798: csel            x1, x16, x17, eq
    // 0xd7e79c: mov             x0, x1
    // 0xd7e7a0: b               #0xd7e7a8
    // 0xd7e7a4: r0 = false
    //     0xd7e7a4: add             x0, NULL, #0x30  ; false
    // 0xd7e7a8: LeaveFrame
    //     0xd7e7a8: mov             SP, fp
    //     0xd7e7ac: ldp             fp, lr, [SP], #0x10
    // 0xd7e7b0: ret
    //     0xd7e7b0: ret             
    // 0xd7e7b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7b4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7b8: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7b8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7bc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7c0: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7c0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7c4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7c8: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7c8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7cc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7d0: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7d0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7d4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7d8: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7d8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7dc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7e0: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7e0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7e4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7e8: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7e8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7ec: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7f0: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7f0: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7f4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e7f8: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e7f8: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e7fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e7fc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e800: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e800: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e804: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e804: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e808: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e808: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e80c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e80c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e810: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e810: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e814: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e814: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e818: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e818: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e81c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e81c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e820: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e820: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e824: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e824: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e828: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e828: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
    // 0xd7e82c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd7e82c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd7e830: r0 = RangeErrorSharedWithFPURegs()
    //     0xd7e830: bl              #0xec28dc  ; RangeErrorSharedWithFPURegsStub
  }
}
