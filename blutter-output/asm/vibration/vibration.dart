// lib: vibration, url: package:vibration/vibration.dart

// class id: 1051228, size: 0x8
class :: {
}

// class id: 403, size: 0x8, field offset: 0x8
abstract class Vibration extends Object {

  static _ vibrate(/* No info */) {
    // ** addr: 0xb56b38, size: 0x78
    // 0xb56b38: EnterFrame
    //     0xb56b38: stp             fp, lr, [SP, #-0x10]!
    //     0xb56b3c: mov             fp, SP
    // 0xb56b40: AllocStack(0x8)
    //     0xb56b40: sub             SP, SP, #8
    // 0xb56b44: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xb56b44: mov             x3, x1
    //     0xb56b48: stur            x1, [fp, #-8]
    // 0xb56b4c: CheckStackOverflow
    //     0xb56b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56b50: cmp             SP, x16
    //     0xb56b54: b.ls            #0xb56ba8
    // 0xb56b58: r0 = InitLateStaticField(0x1770) // [package:vibration_platform_interface/vibration_platform_interface.dart] VibrationPlatform::_instance
    //     0xb56b58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb56b5c: ldr             x0, [x0, #0x2ee0]
    //     0xb56b60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb56b64: cmp             w0, w16
    //     0xb56b68: b.ne            #0xb56b78
    //     0xb56b6c: add             x2, PP, #0x29, lsl #12  ; [pp+0x29be8] Field <VibrationPlatform._instance@2738008900>: static late (offset: 0x1770)
    //     0xb56b70: ldr             x2, [x2, #0xbe8]
    //     0xb56b74: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xb56b78: mov             x1, x0
    // 0xb56b7c: ldur            x3, [fp, #-8]
    // 0xb56b80: r2 = -1
    //     0xb56b80: movn            x2, #0
    // 0xb56b84: r5 = const []
    //     0xb56b84: add             x5, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0xb56b88: ldr             x5, [x5, #0x8c0]
    // 0xb56b8c: r6 = const []
    //     0xb56b8c: add             x6, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0xb56b90: ldr             x6, [x6, #0x8c0]
    // 0xb56b94: r7 = -1
    //     0xb56b94: movn            x7, #0
    // 0xb56b98: r0 = vibrate()
    //     0xb56b98: bl              #0xb56bb0  ; [package:vibration_platform_interface/src/method_channel_vibration.dart] MethodChannelVibration::vibrate
    // 0xb56b9c: LeaveFrame
    //     0xb56b9c: mov             SP, fp
    //     0xb56ba0: ldp             fp, lr, [SP], #0x10
    // 0xb56ba4: ret
    //     0xb56ba4: ret             
    // 0xb56ba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56ba8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56bac: b               #0xb56b58
  }
}
