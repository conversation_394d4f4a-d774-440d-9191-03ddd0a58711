// lib: , url: package:vibration_platform_interface/src/method_channel_vibration.dart

// class id: 1051229, size: 0x8
class :: {
}

// class id: 5863, size: 0xc, field offset: 0x8
class MethodChannelVibration extends VibrationPlatform {

  _ vibrate(/* No info */) {
    // ** addr: 0xb56bb0, size: 0xdc
    // 0xb56bb0: EnterFrame
    //     0xb56bb0: stp             fp, lr, [SP, #-0x10]!
    //     0xb56bb4: mov             fp, SP
    // 0xb56bb8: AllocStack(0x28)
    //     0xb56bb8: sub             SP, SP, #0x28
    // 0xb56bbc: SetupParameters(dynamic _ /* r3 => r3, fp-0x8 */)
    //     0xb56bbc: stur            x3, [fp, #-8]
    // 0xb56bc0: CheckStackOverflow
    //     0xb56bc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56bc4: cmp             SP, x16
    //     0xb56bc8: b.ls            #0xb56c84
    // 0xb56bcc: r1 = Null
    //     0xb56bcc: mov             x1, NULL
    // 0xb56bd0: r2 = 20
    //     0xb56bd0: movz            x2, #0x14
    // 0xb56bd4: r0 = AllocateArray()
    //     0xb56bd4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xb56bd8: r16 = "duration"
    //     0xb56bd8: ldr             x16, [PP, #0x4e68]  ; [pp+0x4e68] "duration"
    // 0xb56bdc: StoreField: r0->field_f = r16
    //     0xb56bdc: stur            w16, [x0, #0xf]
    // 0xb56be0: ldur            x1, [fp, #-8]
    // 0xb56be4: lsl             x2, x1, #1
    // 0xb56be8: StoreField: r0->field_13 = r2
    //     0xb56be8: stur            w2, [x0, #0x13]
    // 0xb56bec: r16 = "pattern"
    //     0xb56bec: add             x16, PP, #0x29, lsl #12  ; [pp+0x29bf0] "pattern"
    //     0xb56bf0: ldr             x16, [x16, #0xbf0]
    // 0xb56bf4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb56bf4: stur            w16, [x0, #0x17]
    // 0xb56bf8: r16 = const []
    //     0xb56bf8: add             x16, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0xb56bfc: ldr             x16, [x16, #0x8c0]
    // 0xb56c00: StoreField: r0->field_1b = r16
    //     0xb56c00: stur            w16, [x0, #0x1b]
    // 0xb56c04: r16 = "repeat"
    //     0xb56c04: add             x16, PP, #0x26, lsl #12  ; [pp+0x263f0] "repeat"
    //     0xb56c08: ldr             x16, [x16, #0x3f0]
    // 0xb56c0c: StoreField: r0->field_1f = r16
    //     0xb56c0c: stur            w16, [x0, #0x1f]
    // 0xb56c10: r16 = -2
    //     0xb56c10: orr             x16, xzr, #0xfffffffffffffffe
    // 0xb56c14: StoreField: r0->field_23 = r16
    //     0xb56c14: stur            w16, [x0, #0x23]
    // 0xb56c18: r16 = "amplitude"
    //     0xb56c18: add             x16, PP, #0x29, lsl #12  ; [pp+0x29bf8] "amplitude"
    //     0xb56c1c: ldr             x16, [x16, #0xbf8]
    // 0xb56c20: StoreField: r0->field_27 = r16
    //     0xb56c20: stur            w16, [x0, #0x27]
    // 0xb56c24: r16 = -2
    //     0xb56c24: orr             x16, xzr, #0xfffffffffffffffe
    // 0xb56c28: StoreField: r0->field_2b = r16
    //     0xb56c28: stur            w16, [x0, #0x2b]
    // 0xb56c2c: r16 = "intensities"
    //     0xb56c2c: add             x16, PP, #0x29, lsl #12  ; [pp+0x29c00] "intensities"
    //     0xb56c30: ldr             x16, [x16, #0xc00]
    // 0xb56c34: StoreField: r0->field_2f = r16
    //     0xb56c34: stur            w16, [x0, #0x2f]
    // 0xb56c38: r16 = const []
    //     0xb56c38: add             x16, PP, #0x22, lsl #12  ; [pp+0x228c0] List<int>(0)
    //     0xb56c3c: ldr             x16, [x16, #0x8c0]
    // 0xb56c40: StoreField: r0->field_33 = r16
    //     0xb56c40: stur            w16, [x0, #0x33]
    // 0xb56c44: r16 = <String, Object>
    //     0xb56c44: add             x16, PP, #8, lsl #12  ; [pp+0x8790] TypeArguments: <String, Object>
    //     0xb56c48: ldr             x16, [x16, #0x790]
    // 0xb56c4c: stp             x0, x16, [SP]
    // 0xb56c50: r0 = Map._fromLiteral()
    //     0xb56c50: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xb56c54: r16 = <void?>
    //     0xb56c54: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xb56c58: r30 = Instance_MethodChannel
    //     0xb56c58: add             lr, PP, #0x29, lsl #12  ; [pp+0x29c08] Obj!MethodChannel@e112f1
    //     0xb56c5c: ldr             lr, [lr, #0xc08]
    // 0xb56c60: stp             lr, x16, [SP, #0x10]
    // 0xb56c64: r16 = "vibrate"
    //     0xb56c64: add             x16, PP, #0x29, lsl #12  ; [pp+0x29c10] "vibrate"
    //     0xb56c68: ldr             x16, [x16, #0xc10]
    // 0xb56c6c: stp             x0, x16, [SP]
    // 0xb56c70: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xb56c70: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xb56c74: r0 = invokeMethod()
    //     0xb56c74: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xb56c78: LeaveFrame
    //     0xb56c78: mov             SP, fp
    //     0xb56c7c: ldp             fp, lr, [SP], #0x10
    // 0xb56c80: ret
    //     0xb56c80: ret             
    // 0xb56c84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56c84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56c88: b               #0xb56bcc
  }
}
