// lib: vibration_platform_interface, url: package:vibration_platform_interface/vibration_platform_interface.dart

// class id: 1051230, size: 0x8
class :: {
}

// class id: 5862, size: 0x8, field offset: 0x8
abstract class VibrationPlatform extends PlatformInterface {

  static late VibrationPlatform _instance; // offset: 0x1770
  static late final Object _token; // offset: 0x176c

  static VibrationPlatform _instance() {
    // ** addr: 0xb56c8c, size: 0x98
    // 0xb56c8c: EnterFrame
    //     0xb56c8c: stp             fp, lr, [SP, #-0x10]!
    //     0xb56c90: mov             fp, SP
    // 0xb56c94: AllocStack(0x10)
    //     0xb56c94: sub             SP, SP, #0x10
    // 0xb56c98: CheckStackOverflow
    //     0xb56c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56c9c: cmp             SP, x16
    //     0xb56ca0: b.ls            #0xb56d1c
    // 0xb56ca4: r0 = MethodChannelVibration()
    //     0xb56ca4: bl              #0xb56d24  ; AllocateMethodChannelVibrationStub -> MethodChannelVibration (size=0xc)
    // 0xb56ca8: mov             x1, x0
    // 0xb56cac: r0 = Instance_MethodChannel
    //     0xb56cac: add             x0, PP, #0x29, lsl #12  ; [pp+0x29c08] Obj!MethodChannel@e112f1
    //     0xb56cb0: ldr             x0, [x0, #0xc08]
    // 0xb56cb4: stur            x1, [fp, #-8]
    // 0xb56cb8: StoreField: r1->field_7 = r0
    //     0xb56cb8: stur            w0, [x1, #7]
    // 0xb56cbc: r0 = InitLateStaticField(0x176c) // [package:vibration_platform_interface/vibration_platform_interface.dart] VibrationPlatform::_token
    //     0xb56cbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb56cc0: ldr             x0, [x0, #0x2ed8]
    //     0xb56cc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb56cc8: cmp             w0, w16
    //     0xb56ccc: b.ne            #0xb56cdc
    //     0xb56cd0: add             x2, PP, #0x29, lsl #12  ; [pp+0x29c18] Field <VibrationPlatform._token@2738008900>: static late final (offset: 0x176c)
    //     0xb56cd4: ldr             x2, [x2, #0xc18]
    //     0xb56cd8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb56cdc: stur            x0, [fp, #-0x10]
    // 0xb56ce0: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xb56ce0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb56ce4: ldr             x0, [x0, #0xc08]
    //     0xb56ce8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb56cec: cmp             w0, w16
    //     0xb56cf0: b.ne            #0xb56cfc
    //     0xb56cf4: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xb56cf8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xb56cfc: mov             x1, x0
    // 0xb56d00: ldur            x2, [fp, #-8]
    // 0xb56d04: ldur            x3, [fp, #-0x10]
    // 0xb56d08: r0 = []=()
    //     0xb56d08: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xb56d0c: ldur            x0, [fp, #-8]
    // 0xb56d10: LeaveFrame
    //     0xb56d10: mov             SP, fp
    //     0xb56d14: ldp             fp, lr, [SP], #0x10
    // 0xb56d18: ret
    //     0xb56d18: ret             
    // 0xb56d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56d1c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56d20: b               #0xb56ca4
  }
}
