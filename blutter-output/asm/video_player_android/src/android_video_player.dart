// lib: , url: package:video_player_android/src/android_video_player.dart

// class id: 1051231, size: 0x8
class :: {
}

// class id: 402, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class _VideoPlayerViewState extends Object {
}

// class id: 5861, size: 0x8, field offset: 0x8
class AndroidVideoPlayer extends VideoPlayerPlatform {

  static void registerWith() {
    // ** addr: 0xec6e4c, size: 0x48
    // 0xec6e4c: EnterFrame
    //     0xec6e4c: stp             fp, lr, [SP, #-0x10]!
    //     0xec6e50: mov             fp, SP
    // 0xec6e54: AllocStack(0x8)
    //     0xec6e54: sub             SP, SP, #8
    // 0xec6e58: CheckStackOverflow
    //     0xec6e58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec6e5c: cmp             SP, x16
    //     0xec6e60: b.ls            #0xec6e8c
    // 0xec6e64: r0 = AndroidVideoPlayer()
    //     0xec6e64: bl              #0xec6fac  ; AllocateAndroidVideoPlayerStub -> AndroidVideoPlayer (size=0x8)
    // 0xec6e68: mov             x1, x0
    // 0xec6e6c: stur            x0, [fp, #-8]
    // 0xec6e70: r0 = AndroidVideoPlayer()
    //     0xec6e70: bl              #0xec6ef4  ; [package:video_player_android/src/android_video_player.dart] AndroidVideoPlayer::AndroidVideoPlayer
    // 0xec6e74: ldur            x1, [fp, #-8]
    // 0xec6e78: r0 = instance=()
    //     0xec6e78: bl              #0xec6e94  ; [package:video_player_platform_interface/video_player_platform_interface.dart] VideoPlayerPlatform::instance=
    // 0xec6e7c: r0 = Null
    //     0xec6e7c: mov             x0, NULL
    // 0xec6e80: LeaveFrame
    //     0xec6e80: mov             SP, fp
    //     0xec6e84: ldp             fp, lr, [SP], #0x10
    // 0xec6e88: ret
    //     0xec6e88: ret             
    // 0xec6e8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec6e8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec6e90: b               #0xec6e64
  }
  _ AndroidVideoPlayer(/* No info */) {
    // ** addr: 0xec6ef4, size: 0x9c
    // 0xec6ef4: EnterFrame
    //     0xec6ef4: stp             fp, lr, [SP, #-0x10]!
    //     0xec6ef8: mov             fp, SP
    // 0xec6efc: AllocStack(0x20)
    //     0xec6efc: sub             SP, SP, #0x20
    // 0xec6f00: SetupParameters(AndroidVideoPlayer this /* r1 => r2, fp-0x8 */)
    //     0xec6f00: mov             x2, x1
    //     0xec6f04: stur            x1, [fp, #-8]
    // 0xec6f08: CheckStackOverflow
    //     0xec6f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec6f0c: cmp             SP, x16
    //     0xec6f10: b.ls            #0xec6f88
    // 0xec6f14: r16 = <int, _VideoPlayerViewState>
    //     0xec6f14: add             x16, PP, #0xc, lsl #12  ; [pp+0xc748] TypeArguments: <int, _VideoPlayerViewState>
    //     0xec6f18: ldr             x16, [x16, #0x748]
    // 0xec6f1c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xec6f20: stp             lr, x16, [SP]
    // 0xec6f24: r0 = Map._fromLiteral()
    //     0xec6f24: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0xec6f28: r0 = InitLateStaticField(0x9e4) // [package:video_player_platform_interface/video_player_platform_interface.dart] VideoPlayerPlatform::_token
    //     0xec6f28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec6f2c: ldr             x0, [x0, #0x13c8]
    //     0xec6f30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec6f34: cmp             w0, w16
    //     0xec6f38: b.ne            #0xec6f48
    //     0xec6f3c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc740] Field <VideoPlayerPlatform._token@682265862>: static late final (offset: 0x9e4)
    //     0xec6f40: ldr             x2, [x2, #0x740]
    //     0xec6f44: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec6f48: stur            x0, [fp, #-0x10]
    // 0xec6f4c: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xec6f4c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec6f50: ldr             x0, [x0, #0xc08]
    //     0xec6f54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec6f58: cmp             w0, w16
    //     0xec6f5c: b.ne            #0xec6f68
    //     0xec6f60: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xec6f64: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec6f68: mov             x1, x0
    // 0xec6f6c: ldur            x2, [fp, #-8]
    // 0xec6f70: ldur            x3, [fp, #-0x10]
    // 0xec6f74: r0 = []=()
    //     0xec6f74: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xec6f78: r0 = Null
    //     0xec6f78: mov             x0, NULL
    // 0xec6f7c: LeaveFrame
    //     0xec6f7c: mov             SP, fp
    //     0xec6f80: ldp             fp, lr, [SP], #0x10
    // 0xec6f84: ret
    //     0xec6f84: ret             
    // 0xec6f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec6f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec6f8c: b               #0xec6f14
  }
}
