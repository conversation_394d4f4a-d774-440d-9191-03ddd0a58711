// lib: , url: package:video_player_platform_interface/video_player_platform_interface.dart

// class id: 1051233, size: 0x8
class :: {
}

// class id: 5859, size: 0x8, field offset: 0x8
abstract class VideoPlayerPlatform extends PlatformInterface {

  static late final Object _token; // offset: 0x9e4

  set _ instance=(/* No info */) {
    // ** addr: 0xec6e94, size: 0x60
    // 0xec6e94: EnterFrame
    //     0xec6e94: stp             fp, lr, [SP, #-0x10]!
    //     0xec6e98: mov             fp, SP
    // 0xec6e9c: AllocStack(0x8)
    //     0xec6e9c: sub             SP, SP, #8
    // 0xec6ea0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xec6ea0: stur            x1, [fp, #-8]
    // 0xec6ea4: CheckStackOverflow
    //     0xec6ea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec6ea8: cmp             SP, x16
    //     0xec6eac: b.ls            #0xec6eec
    // 0xec6eb0: r0 = InitLateStaticField(0x9e4) // [package:video_player_platform_interface/video_player_platform_interface.dart] VideoPlayerPlatform::_token
    //     0xec6eb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec6eb4: ldr             x0, [x0, #0x13c8]
    //     0xec6eb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec6ebc: cmp             w0, w16
    //     0xec6ec0: b.ne            #0xec6ed0
    //     0xec6ec4: add             x2, PP, #0xc, lsl #12  ; [pp+0xc740] Field <VideoPlayerPlatform._token@682265862>: static late final (offset: 0x9e4)
    //     0xec6ec8: ldr             x2, [x2, #0x740]
    //     0xec6ecc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec6ed0: ldur            x1, [fp, #-8]
    // 0xec6ed4: mov             x2, x0
    // 0xec6ed8: r0 = verify()
    //     0xec6ed8: bl              #0x833b94  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0xec6edc: r0 = Null
    //     0xec6edc: mov             x0, NULL
    // 0xec6ee0: LeaveFrame
    //     0xec6ee0: mov             SP, fp
    //     0xec6ee4: ldp             fp, lr, [SP], #0x10
    // 0xec6ee8: ret
    //     0xec6ee8: ret             
    // 0xec6eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec6eec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec6ef0: b               #0xec6eb0
  }
}
