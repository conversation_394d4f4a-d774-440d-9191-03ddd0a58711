// lib: , url: package:wakelock_plus/wakelock_plus.dart

// class id: 1051234, size: 0x8
class :: {

  static late WakelockPlusPlatformInterface wakelockPlusPlatformInstance; // offset: 0x61c

  static WakelockPlusPlatformInterface wakelockPlusPlatformInstance() {
    // ** addr: 0x8ad9c0, size: 0x48
    // 0x8ad9c0: EnterFrame
    //     0x8ad9c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ad9c4: mov             fp, SP
    // 0x8ad9c8: CheckStackOverflow
    //     0x8ad9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ad9cc: cmp             SP, x16
    //     0x8ad9d0: b.ls            #0x8ada00
    // 0x8ad9d4: r0 = InitLateStaticField(0x177c) // [package:wakelock_plus_platform_interface/wakelock_plus_platform_interface.dart] WakelockPlusPlatformInterface::_instance
    //     0x8ad9d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ad9d8: ldr             x0, [x0, #0x2ef8]
    //     0x8ad9dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ad9e0: cmp             w0, w16
    //     0x8ad9e4: b.ne            #0x8ad9f4
    //     0x8ad9e8: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c470] Field <WakelockPlusPlatformInterface._instance@2741180744>: static late (offset: 0x177c)
    //     0x8ad9ec: ldr             x2, [x2, #0x470]
    //     0x8ad9f0: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8ad9f4: LeaveFrame
    //     0x8ad9f4: mov             SP, fp
    //     0x8ad9f8: ldp             fp, lr, [SP], #0x10
    // 0x8ad9fc: ret
    //     0x8ad9fc: ret             
    // 0x8ada00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ada00: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ada04: b               #0x8ad9d4
  }
}

// class id: 398, size: 0x8, field offset: 0x8
abstract class WakelockPlus extends Object {

  static _ toggle(/* No info */) {
    // ** addr: 0x8ad644, size: 0x60
    // 0x8ad644: EnterFrame
    //     0x8ad644: stp             fp, lr, [SP, #-0x10]!
    //     0x8ad648: mov             fp, SP
    // 0x8ad64c: AllocStack(0x8)
    //     0x8ad64c: sub             SP, SP, #8
    // 0x8ad650: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x8ad650: mov             x2, x1
    //     0x8ad654: stur            x1, [fp, #-8]
    // 0x8ad658: CheckStackOverflow
    //     0x8ad658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ad65c: cmp             SP, x16
    //     0x8ad660: b.ls            #0x8ad69c
    // 0x8ad664: r0 = InitLateStaticField(0x61c) // [package:wakelock_plus/wakelock_plus.dart] ::wakelockPlusPlatformInstance
    //     0x8ad664: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ad668: ldr             x0, [x0, #0xc38]
    //     0x8ad66c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ad670: cmp             w0, w16
    //     0x8ad674: b.ne            #0x8ad684
    //     0x8ad678: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c428] Field <::.wakelockPlusPlatformInstance>: static late (offset: 0x61c)
    //     0x8ad67c: ldr             x2, [x2, #0x428]
    //     0x8ad680: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x8ad684: mov             x1, x0
    // 0x8ad688: ldur            x2, [fp, #-8]
    // 0x8ad68c: r0 = toggle()
    //     0x8ad68c: bl              #0x8ad6a4  ; [package:wakelock_plus_platform_interface/src/method_channel_wakelock_plus.dart] MethodChannelWakelockPlus::toggle
    // 0x8ad690: LeaveFrame
    //     0x8ad690: mov             SP, fp
    //     0x8ad694: ldp             fp, lr, [SP], #0x10
    // 0x8ad698: ret
    //     0x8ad698: ret             
    // 0x8ad69c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ad69c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ad6a0: b               #0x8ad664
  }
  static Future<void> disable() {
    // ** addr: 0x9270f0, size: 0x30
    // 0x9270f0: EnterFrame
    //     0x9270f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9270f4: mov             fp, SP
    // 0x9270f8: CheckStackOverflow
    //     0x9270f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9270fc: cmp             SP, x16
    //     0x927100: b.ls            #0x927118
    // 0x927104: r1 = false
    //     0x927104: add             x1, NULL, #0x30  ; false
    // 0x927108: r0 = toggle()
    //     0x927108: bl              #0x8ad644  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::toggle
    // 0x92710c: LeaveFrame
    //     0x92710c: mov             SP, fp
    //     0x927110: ldp             fp, lr, [SP], #0x10
    // 0x927114: ret
    //     0x927114: ret             
    // 0x927118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927118: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92711c: b               #0x927104
  }
}
