// lib: , url: package:wakelock_plus_platform_interface/messages.g.dart

// class id: 1051235, size: 0x8
class :: {
}

// class id: 395, size: 0x10, field offset: 0x8
class WakelockPlusApi extends Object {

  _ toggle(/* No info */) async {
    // ** addr: 0x8ad718, size: 0x29c
    // 0x8ad718: EnterFrame
    //     0x8ad718: stp             fp, lr, [SP, #-0x10]!
    //     0x8ad71c: mov             fp, SP
    // 0x8ad720: AllocStack(0x38)
    //     0x8ad720: sub             SP, SP, #0x38
    // 0x8ad724: SetupParameters(WakelockPlusApi this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8ad724: stur            NULL, [fp, #-8]
    //     0x8ad728: stur            x1, [fp, #-0x10]
    //     0x8ad72c: stur            x2, [fp, #-0x18]
    // 0x8ad730: CheckStackOverflow
    //     0x8ad730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ad734: cmp             SP, x16
    //     0x8ad738: b.ls            #0x8ad9a8
    // 0x8ad73c: InitAsync() -> Future<void?>
    //     0x8ad73c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8ad740: bl              #0x661298  ; InitAsyncStub
    // 0x8ad744: r1 = Null
    //     0x8ad744: mov             x1, NULL
    // 0x8ad748: r2 = 4
    //     0x8ad748: movz            x2, #0x4
    // 0x8ad74c: r0 = AllocateArray()
    //     0x8ad74c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ad750: r16 = "dev.flutter.pigeon.wakelock_plus_platform_interface.WakelockPlusApi.toggle"
    //     0x8ad750: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c430] "dev.flutter.pigeon.wakelock_plus_platform_interface.WakelockPlusApi.toggle"
    //     0x8ad754: ldr             x16, [x16, #0x430]
    // 0x8ad758: StoreField: r0->field_f = r16
    //     0x8ad758: stur            w16, [x0, #0xf]
    // 0x8ad75c: ldur            x1, [fp, #-0x10]
    // 0x8ad760: LoadField: r2 = r1->field_b
    //     0x8ad760: ldur            w2, [x1, #0xb]
    // 0x8ad764: DecompressPointer r2
    //     0x8ad764: add             x2, x2, HEAP, lsl #32
    // 0x8ad768: StoreField: r0->field_13 = r2
    //     0x8ad768: stur            w2, [x0, #0x13]
    // 0x8ad76c: str             x0, [SP]
    // 0x8ad770: r0 = _interpolate()
    //     0x8ad770: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x8ad774: r1 = <Object?>
    //     0x8ad774: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8ad778: stur            x0, [fp, #-0x10]
    // 0x8ad77c: r0 = BasicMessageChannel()
    //     0x8ad77c: bl              #0x7edc64  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0x8ad780: mov             x3, x0
    // 0x8ad784: ldur            x0, [fp, #-0x10]
    // 0x8ad788: stur            x3, [fp, #-0x20]
    // 0x8ad78c: StoreField: r3->field_b = r0
    //     0x8ad78c: stur            w0, [x3, #0xb]
    // 0x8ad790: r1 = Instance__PigeonCodec
    //     0x8ad790: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c438] Obj!_PigeonCodec@e25991
    //     0x8ad794: ldr             x1, [x1, #0x438]
    // 0x8ad798: StoreField: r3->field_f = r1
    //     0x8ad798: stur            w1, [x3, #0xf]
    // 0x8ad79c: r1 = Null
    //     0x8ad79c: mov             x1, NULL
    // 0x8ad7a0: r2 = 2
    //     0x8ad7a0: movz            x2, #0x2
    // 0x8ad7a4: r0 = AllocateArray()
    //     0x8ad7a4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8ad7a8: mov             x2, x0
    // 0x8ad7ac: ldur            x0, [fp, #-0x18]
    // 0x8ad7b0: stur            x2, [fp, #-0x28]
    // 0x8ad7b4: StoreField: r2->field_f = r0
    //     0x8ad7b4: stur            w0, [x2, #0xf]
    // 0x8ad7b8: r1 = <Object?>
    //     0x8ad7b8: ldr             x1, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0x8ad7bc: r0 = AllocateGrowableArray()
    //     0x8ad7bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x8ad7c0: mov             x1, x0
    // 0x8ad7c4: ldur            x0, [fp, #-0x28]
    // 0x8ad7c8: StoreField: r1->field_f = r0
    //     0x8ad7c8: stur            w0, [x1, #0xf]
    // 0x8ad7cc: r0 = 2
    //     0x8ad7cc: movz            x0, #0x2
    // 0x8ad7d0: StoreField: r1->field_b = r0
    //     0x8ad7d0: stur            w0, [x1, #0xb]
    // 0x8ad7d4: mov             x2, x1
    // 0x8ad7d8: ldur            x1, [fp, #-0x20]
    // 0x8ad7dc: r0 = send()
    //     0x8ad7dc: bl              #0x65755c  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0x8ad7e0: mov             x1, x0
    // 0x8ad7e4: stur            x1, [fp, #-0x18]
    // 0x8ad7e8: r0 = Await()
    //     0x8ad7e8: bl              #0x661044  ; AwaitStub
    // 0x8ad7ec: mov             x3, x0
    // 0x8ad7f0: r2 = Null
    //     0x8ad7f0: mov             x2, NULL
    // 0x8ad7f4: r1 = Null
    //     0x8ad7f4: mov             x1, NULL
    // 0x8ad7f8: stur            x3, [fp, #-0x18]
    // 0x8ad7fc: r4 = 60
    //     0x8ad7fc: movz            x4, #0x3c
    // 0x8ad800: branchIfSmi(r0, 0x8ad80c)
    //     0x8ad800: tbz             w0, #0, #0x8ad80c
    // 0x8ad804: r4 = LoadClassIdInstr(r0)
    //     0x8ad804: ldur            x4, [x0, #-1]
    //     0x8ad808: ubfx            x4, x4, #0xc, #0x14
    // 0x8ad80c: sub             x4, x4, #0x5a
    // 0x8ad810: cmp             x4, #2
    // 0x8ad814: b.ls            #0x8ad828
    // 0x8ad818: r8 = List<Object?>?
    //     0x8ad818: ldr             x8, [PP, #0x320]  ; [pp+0x320] Type: List<Object?>?
    // 0x8ad81c: r3 = Null
    //     0x8ad81c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c440] Null
    //     0x8ad820: ldr             x3, [x3, #0x440]
    // 0x8ad824: r0 = List<Object?>?()
    //     0x8ad824: bl              #0x624060  ; IsType_List<Object?>?_Stub
    // 0x8ad828: ldur            x1, [fp, #-0x18]
    // 0x8ad82c: cmp             w1, NULL
    // 0x8ad830: b.eq            #0x8ad86c
    // 0x8ad834: r0 = LoadClassIdInstr(r1)
    //     0x8ad834: ldur            x0, [x1, #-1]
    //     0x8ad838: ubfx            x0, x0, #0xc, #0x14
    // 0x8ad83c: str             x1, [SP]
    // 0x8ad840: r0 = GDT[cid_x0 + 0xc834]()
    //     0x8ad840: movz            x17, #0xc834
    //     0x8ad844: add             lr, x0, x17
    //     0x8ad848: ldr             lr, [x21, lr, lsl #3]
    //     0x8ad84c: blr             lr
    // 0x8ad850: r1 = LoadInt32Instr(r0)
    //     0x8ad850: sbfx            x1, x0, #1, #0x1f
    //     0x8ad854: tbz             w0, #0, #0x8ad85c
    //     0x8ad858: ldur            x1, [x0, #7]
    // 0x8ad85c: cmp             x1, #1
    // 0x8ad860: b.gt            #0x8ad87c
    // 0x8ad864: r0 = Null
    //     0x8ad864: mov             x0, NULL
    // 0x8ad868: r0 = ReturnAsyncNotFuture()
    //     0x8ad868: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ad86c: ldur            x1, [fp, #-0x10]
    // 0x8ad870: r0 = _createConnectionError()
    //     0x8ad870: bl              #0x7edbec  ; [package:webview_flutter_android/src/android_webkit.g.dart] ::_createConnectionError
    // 0x8ad874: r0 = Throw()
    //     0x8ad874: bl              #0xec04b8  ; ThrowStub
    // 0x8ad878: brk             #0
    // 0x8ad87c: ldur            x1, [fp, #-0x18]
    // 0x8ad880: r0 = LoadClassIdInstr(r1)
    //     0x8ad880: ldur            x0, [x1, #-1]
    //     0x8ad884: ubfx            x0, x0, #0xc, #0x14
    // 0x8ad888: stp             xzr, x1, [SP]
    // 0x8ad88c: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8ad88c: movz            x17, #0x3037
    //     0x8ad890: movk            x17, #0x1, lsl #16
    //     0x8ad894: add             lr, x0, x17
    //     0x8ad898: ldr             lr, [x21, lr, lsl #3]
    //     0x8ad89c: blr             lr
    // 0x8ad8a0: mov             x3, x0
    // 0x8ad8a4: stur            x3, [fp, #-0x10]
    // 0x8ad8a8: cmp             w3, NULL
    // 0x8ad8ac: b.eq            #0x8ad9b0
    // 0x8ad8b0: mov             x0, x3
    // 0x8ad8b4: r2 = Null
    //     0x8ad8b4: mov             x2, NULL
    // 0x8ad8b8: r1 = Null
    //     0x8ad8b8: mov             x1, NULL
    // 0x8ad8bc: r4 = 60
    //     0x8ad8bc: movz            x4, #0x3c
    // 0x8ad8c0: branchIfSmi(r0, 0x8ad8cc)
    //     0x8ad8c0: tbz             w0, #0, #0x8ad8cc
    // 0x8ad8c4: r4 = LoadClassIdInstr(r0)
    //     0x8ad8c4: ldur            x4, [x0, #-1]
    //     0x8ad8c8: ubfx            x4, x4, #0xc, #0x14
    // 0x8ad8cc: sub             x4, x4, #0x5e
    // 0x8ad8d0: cmp             x4, #1
    // 0x8ad8d4: b.ls            #0x8ad8e8
    // 0x8ad8d8: r8 = String
    //     0x8ad8d8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x8ad8dc: r3 = Null
    //     0x8ad8dc: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c450] Null
    //     0x8ad8e0: ldr             x3, [x3, #0x450]
    // 0x8ad8e4: r0 = String()
    //     0x8ad8e4: bl              #0xed43b0  ; IsType_String_Stub
    // 0x8ad8e8: ldur            x1, [fp, #-0x18]
    // 0x8ad8ec: r0 = LoadClassIdInstr(r1)
    //     0x8ad8ec: ldur            x0, [x1, #-1]
    //     0x8ad8f0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ad8f4: r16 = 2
    //     0x8ad8f4: movz            x16, #0x2
    // 0x8ad8f8: stp             x16, x1, [SP]
    // 0x8ad8fc: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8ad8fc: movz            x17, #0x3037
    //     0x8ad900: movk            x17, #0x1, lsl #16
    //     0x8ad904: add             lr, x0, x17
    //     0x8ad908: ldr             lr, [x21, lr, lsl #3]
    //     0x8ad90c: blr             lr
    // 0x8ad910: mov             x3, x0
    // 0x8ad914: r2 = Null
    //     0x8ad914: mov             x2, NULL
    // 0x8ad918: r1 = Null
    //     0x8ad918: mov             x1, NULL
    // 0x8ad91c: stur            x3, [fp, #-0x20]
    // 0x8ad920: r4 = 60
    //     0x8ad920: movz            x4, #0x3c
    // 0x8ad924: branchIfSmi(r0, 0x8ad930)
    //     0x8ad924: tbz             w0, #0, #0x8ad930
    // 0x8ad928: r4 = LoadClassIdInstr(r0)
    //     0x8ad928: ldur            x4, [x0, #-1]
    //     0x8ad92c: ubfx            x4, x4, #0xc, #0x14
    // 0x8ad930: sub             x4, x4, #0x5e
    // 0x8ad934: cmp             x4, #1
    // 0x8ad938: b.ls            #0x8ad94c
    // 0x8ad93c: r8 = String?
    //     0x8ad93c: ldr             x8, [PP, #0x338]  ; [pp+0x338] Type: String?
    // 0x8ad940: r3 = Null
    //     0x8ad940: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c460] Null
    //     0x8ad944: ldr             x3, [x3, #0x460]
    // 0x8ad948: r0 = String?()
    //     0x8ad948: bl              #0x600324  ; IsType_String?_Stub
    // 0x8ad94c: ldur            x0, [fp, #-0x18]
    // 0x8ad950: r1 = LoadClassIdInstr(r0)
    //     0x8ad950: ldur            x1, [x0, #-1]
    //     0x8ad954: ubfx            x1, x1, #0xc, #0x14
    // 0x8ad958: r16 = 4
    //     0x8ad958: movz            x16, #0x4
    // 0x8ad95c: stp             x16, x0, [SP]
    // 0x8ad960: mov             x0, x1
    // 0x8ad964: r0 = GDT[cid_x0 + 0x13037]()
    //     0x8ad964: movz            x17, #0x3037
    //     0x8ad968: movk            x17, #0x1, lsl #16
    //     0x8ad96c: add             lr, x0, x17
    //     0x8ad970: ldr             lr, [x21, lr, lsl #3]
    //     0x8ad974: blr             lr
    // 0x8ad978: stur            x0, [fp, #-0x18]
    // 0x8ad97c: r0 = PlatformException()
    //     0x8ad97c: bl              #0x7edbe0  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0x8ad980: mov             x1, x0
    // 0x8ad984: ldur            x0, [fp, #-0x10]
    // 0x8ad988: StoreField: r1->field_7 = r0
    //     0x8ad988: stur            w0, [x1, #7]
    // 0x8ad98c: ldur            x0, [fp, #-0x20]
    // 0x8ad990: StoreField: r1->field_b = r0
    //     0x8ad990: stur            w0, [x1, #0xb]
    // 0x8ad994: ldur            x0, [fp, #-0x18]
    // 0x8ad998: StoreField: r1->field_f = r0
    //     0x8ad998: stur            w0, [x1, #0xf]
    // 0x8ad99c: mov             x0, x1
    // 0x8ad9a0: r0 = Throw()
    //     0x8ad9a0: bl              #0xec04b8  ; ThrowStub
    // 0x8ad9a4: brk             #0
    // 0x8ad9a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ad9a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ad9ac: b               #0x8ad73c
    // 0x8ad9b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8ad9b0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 396, size: 0xc, field offset: 0x8
class IsEnabledMessage extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xce7794, size: 0xd8
    // 0xce7794: EnterFrame
    //     0xce7794: stp             fp, lr, [SP, #-0x10]!
    //     0xce7798: mov             fp, SP
    // 0xce779c: AllocStack(0x18)
    //     0xce779c: sub             SP, SP, #0x18
    // 0xce77a0: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xce77a0: mov             x3, x1
    //     0xce77a4: stur            x1, [fp, #-8]
    // 0xce77a8: CheckStackOverflow
    //     0xce77a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce77ac: cmp             SP, x16
    //     0xce77b0: b.ls            #0xce7864
    // 0xce77b4: mov             x0, x3
    // 0xce77b8: r2 = Null
    //     0xce77b8: mov             x2, NULL
    // 0xce77bc: r1 = Null
    //     0xce77bc: mov             x1, NULL
    // 0xce77c0: r4 = 60
    //     0xce77c0: movz            x4, #0x3c
    // 0xce77c4: branchIfSmi(r0, 0xce77d0)
    //     0xce77c4: tbz             w0, #0, #0xce77d0
    // 0xce77c8: r4 = LoadClassIdInstr(r0)
    //     0xce77c8: ldur            x4, [x0, #-1]
    //     0xce77cc: ubfx            x4, x4, #0xc, #0x14
    // 0xce77d0: sub             x4, x4, #0x5a
    // 0xce77d4: cmp             x4, #2
    // 0xce77d8: b.ls            #0xce77ec
    // 0xce77dc: r8 = List<Object?>
    //     0xce77dc: ldr             x8, [PP, #0x14e0]  ; [pp+0x14e0] Type: List<Object?>
    // 0xce77e0: r3 = Null
    //     0xce77e0: add             x3, PP, #0x46, lsl #12  ; [pp+0x46c88] Null
    //     0xce77e4: ldr             x3, [x3, #0xc88]
    // 0xce77e8: r0 = List<Object?>()
    //     0xce77e8: bl              #0x697f54  ; IsType_List<Object?>_Stub
    // 0xce77ec: ldur            x0, [fp, #-8]
    // 0xce77f0: r1 = LoadClassIdInstr(r0)
    //     0xce77f0: ldur            x1, [x0, #-1]
    //     0xce77f4: ubfx            x1, x1, #0xc, #0x14
    // 0xce77f8: stp             xzr, x0, [SP]
    // 0xce77fc: mov             x0, x1
    // 0xce7800: r0 = GDT[cid_x0 + 0x13037]()
    //     0xce7800: movz            x17, #0x3037
    //     0xce7804: movk            x17, #0x1, lsl #16
    //     0xce7808: add             lr, x0, x17
    //     0xce780c: ldr             lr, [x21, lr, lsl #3]
    //     0xce7810: blr             lr
    // 0xce7814: mov             x3, x0
    // 0xce7818: r2 = Null
    //     0xce7818: mov             x2, NULL
    // 0xce781c: r1 = Null
    //     0xce781c: mov             x1, NULL
    // 0xce7820: stur            x3, [fp, #-8]
    // 0xce7824: r4 = 60
    //     0xce7824: movz            x4, #0x3c
    // 0xce7828: branchIfSmi(r0, 0xce7834)
    //     0xce7828: tbz             w0, #0, #0xce7834
    // 0xce782c: r4 = LoadClassIdInstr(r0)
    //     0xce782c: ldur            x4, [x0, #-1]
    //     0xce7830: ubfx            x4, x4, #0xc, #0x14
    // 0xce7834: cmp             x4, #0x3f
    // 0xce7838: b.eq            #0xce784c
    // 0xce783c: r8 = bool?
    //     0xce783c: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xce7840: r3 = Null
    //     0xce7840: add             x3, PP, #0x46, lsl #12  ; [pp+0x46c98] Null
    //     0xce7844: ldr             x3, [x3, #0xc98]
    // 0xce7848: r0 = bool?()
    //     0xce7848: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xce784c: r0 = IsEnabledMessage()
    //     0xce784c: bl              #0xce786c  ; AllocateIsEnabledMessageStub -> IsEnabledMessage (size=0xc)
    // 0xce7850: ldur            x1, [fp, #-8]
    // 0xce7854: StoreField: r0->field_7 = r1
    //     0xce7854: stur            w1, [x0, #7]
    // 0xce7858: LeaveFrame
    //     0xce7858: mov             SP, fp
    //     0xce785c: ldp             fp, lr, [SP], #0x10
    // 0xce7860: ret
    //     0xce7860: ret             
    // 0xce7864: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce7864: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce7868: b               #0xce77b4
  }
}

// class id: 397, size: 0xc, field offset: 0x8
class ToggleMessage extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xce7878, size: 0xd8
    // 0xce7878: EnterFrame
    //     0xce7878: stp             fp, lr, [SP, #-0x10]!
    //     0xce787c: mov             fp, SP
    // 0xce7880: AllocStack(0x18)
    //     0xce7880: sub             SP, SP, #0x18
    // 0xce7884: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xce7884: mov             x3, x1
    //     0xce7888: stur            x1, [fp, #-8]
    // 0xce788c: CheckStackOverflow
    //     0xce788c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce7890: cmp             SP, x16
    //     0xce7894: b.ls            #0xce7948
    // 0xce7898: mov             x0, x3
    // 0xce789c: r2 = Null
    //     0xce789c: mov             x2, NULL
    // 0xce78a0: r1 = Null
    //     0xce78a0: mov             x1, NULL
    // 0xce78a4: r4 = 60
    //     0xce78a4: movz            x4, #0x3c
    // 0xce78a8: branchIfSmi(r0, 0xce78b4)
    //     0xce78a8: tbz             w0, #0, #0xce78b4
    // 0xce78ac: r4 = LoadClassIdInstr(r0)
    //     0xce78ac: ldur            x4, [x0, #-1]
    //     0xce78b0: ubfx            x4, x4, #0xc, #0x14
    // 0xce78b4: sub             x4, x4, #0x5a
    // 0xce78b8: cmp             x4, #2
    // 0xce78bc: b.ls            #0xce78d0
    // 0xce78c0: r8 = List<Object?>
    //     0xce78c0: ldr             x8, [PP, #0x14e0]  ; [pp+0x14e0] Type: List<Object?>
    // 0xce78c4: r3 = Null
    //     0xce78c4: add             x3, PP, #0x46, lsl #12  ; [pp+0x46ca8] Null
    //     0xce78c8: ldr             x3, [x3, #0xca8]
    // 0xce78cc: r0 = List<Object?>()
    //     0xce78cc: bl              #0x697f54  ; IsType_List<Object?>_Stub
    // 0xce78d0: ldur            x0, [fp, #-8]
    // 0xce78d4: r1 = LoadClassIdInstr(r0)
    //     0xce78d4: ldur            x1, [x0, #-1]
    //     0xce78d8: ubfx            x1, x1, #0xc, #0x14
    // 0xce78dc: stp             xzr, x0, [SP]
    // 0xce78e0: mov             x0, x1
    // 0xce78e4: r0 = GDT[cid_x0 + 0x13037]()
    //     0xce78e4: movz            x17, #0x3037
    //     0xce78e8: movk            x17, #0x1, lsl #16
    //     0xce78ec: add             lr, x0, x17
    //     0xce78f0: ldr             lr, [x21, lr, lsl #3]
    //     0xce78f4: blr             lr
    // 0xce78f8: mov             x3, x0
    // 0xce78fc: r2 = Null
    //     0xce78fc: mov             x2, NULL
    // 0xce7900: r1 = Null
    //     0xce7900: mov             x1, NULL
    // 0xce7904: stur            x3, [fp, #-8]
    // 0xce7908: r4 = 60
    //     0xce7908: movz            x4, #0x3c
    // 0xce790c: branchIfSmi(r0, 0xce7918)
    //     0xce790c: tbz             w0, #0, #0xce7918
    // 0xce7910: r4 = LoadClassIdInstr(r0)
    //     0xce7910: ldur            x4, [x0, #-1]
    //     0xce7914: ubfx            x4, x4, #0xc, #0x14
    // 0xce7918: cmp             x4, #0x3f
    // 0xce791c: b.eq            #0xce7930
    // 0xce7920: r8 = bool?
    //     0xce7920: ldr             x8, [PP, #0x7948]  ; [pp+0x7948] Type: bool?
    // 0xce7924: r3 = Null
    //     0xce7924: add             x3, PP, #0x46, lsl #12  ; [pp+0x46cb8] Null
    //     0xce7928: ldr             x3, [x3, #0xcb8]
    // 0xce792c: r0 = bool?()
    //     0xce792c: bl              #0x60b174  ; IsType_bool?_Stub
    // 0xce7930: r0 = ToggleMessage()
    //     0xce7930: bl              #0x8ad9b4  ; AllocateToggleMessageStub -> ToggleMessage (size=0xc)
    // 0xce7934: ldur            x1, [fp, #-8]
    // 0xce7938: StoreField: r0->field_7 = r1
    //     0xce7938: stur            w1, [x0, #7]
    // 0xce793c: LeaveFrame
    //     0xce793c: mov             SP, fp
    //     0xce7940: ldp             fp, lr, [SP], #0x10
    // 0xce7944: ret
    //     0xce7944: ret             
    // 0xce7948: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce7948: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce794c: b               #0xce7898
  }
}

// class id: 5525, size: 0x8, field offset: 0x8
//   const constructor, 
class _PigeonCodec extends StandardMessageCodec {

  _ readValueOfType(/* No info */) {
    // ** addr: 0xce76f0, size: 0xa4
    // 0xce76f0: EnterFrame
    //     0xce76f0: stp             fp, lr, [SP, #-0x10]!
    //     0xce76f4: mov             fp, SP
    // 0xce76f8: mov             x0, x2
    // 0xce76fc: mov             x2, x3
    // 0xce7700: CheckStackOverflow
    //     0xce7700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce7704: cmp             SP, x16
    //     0xce7708: b.ls            #0xce7784
    // 0xce770c: cmp             x0, #0x81
    // 0xce7710: b.gt            #0xce7740
    // 0xce7714: lsl             x3, x0, #1
    // 0xce7718: cmp             w3, #0x102
    // 0xce771c: b.ne            #0xce776c
    // 0xce7720: r0 = readValue()
    //     0xce7720: bl              #0xce719c  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xce7724: cmp             w0, NULL
    // 0xce7728: b.eq            #0xce778c
    // 0xce772c: mov             x1, x0
    // 0xce7730: r0 = decode()
    //     0xce7730: bl              #0xce7878  ; [package:wakelock_plus_platform_interface/messages.g.dart] ToggleMessage::decode
    // 0xce7734: LeaveFrame
    //     0xce7734: mov             SP, fp
    //     0xce7738: ldp             fp, lr, [SP], #0x10
    // 0xce773c: ret
    //     0xce773c: ret             
    // 0xce7740: lsl             x3, x0, #1
    // 0xce7744: cmp             w3, #0x104
    // 0xce7748: b.ne            #0xce776c
    // 0xce774c: r0 = readValue()
    //     0xce774c: bl              #0xce719c  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xce7750: cmp             w0, NULL
    // 0xce7754: b.eq            #0xce7790
    // 0xce7758: mov             x1, x0
    // 0xce775c: r0 = decode()
    //     0xce775c: bl              #0xce7794  ; [package:wakelock_plus_platform_interface/messages.g.dart] IsEnabledMessage::decode
    // 0xce7760: LeaveFrame
    //     0xce7760: mov             SP, fp
    //     0xce7764: ldp             fp, lr, [SP], #0x10
    // 0xce7768: ret
    //     0xce7768: ret             
    // 0xce776c: mov             x3, x2
    // 0xce7770: mov             x2, x0
    // 0xce7774: r0 = readValueOfType()
    //     0xce7774: bl              #0xce7e48  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValueOfType
    // 0xce7778: LeaveFrame
    //     0xce7778: mov             SP, fp
    //     0xce777c: ldp             fp, lr, [SP], #0x10
    // 0xce7780: ret
    //     0xce7780: ret             
    // 0xce7784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce7784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce7788: b               #0xce770c
    // 0xce778c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce778c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xce7790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce7790: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ writeValue(/* No info */) {
    // ** addr: 0xd4a0e8, size: 0xfc
    // 0xd4a0e8: EnterFrame
    //     0xd4a0e8: stp             fp, lr, [SP, #-0x10]!
    //     0xd4a0ec: mov             fp, SP
    // 0xd4a0f0: AllocStack(0x18)
    //     0xd4a0f0: sub             SP, SP, #0x18
    // 0xd4a0f4: SetupParameters(_PigeonCodec this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xd4a0f4: mov             x4, x1
    //     0xd4a0f8: mov             x0, x3
    //     0xd4a0fc: stur            x3, [fp, #-0x10]
    //     0xd4a100: mov             x3, x2
    //     0xd4a104: stur            x2, [fp, #-8]
    //     0xd4a108: stur            x1, [fp, #-0x18]
    // 0xd4a10c: CheckStackOverflow
    //     0xd4a10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4a110: cmp             SP, x16
    //     0xd4a114: b.ls            #0xd4a1dc
    // 0xd4a118: r1 = 60
    //     0xd4a118: movz            x1, #0x3c
    // 0xd4a11c: branchIfSmi(r0, 0xd4a128)
    //     0xd4a11c: tbz             w0, #0, #0xd4a128
    // 0xd4a120: r1 = LoadClassIdInstr(r0)
    //     0xd4a120: ldur            x1, [x0, #-1]
    //     0xd4a124: ubfx            x1, x1, #0xc, #0x14
    // 0xd4a128: sub             x16, x1, #0x3c
    // 0xd4a12c: cmp             x16, #1
    // 0xd4a130: b.hi            #0xd4a15c
    // 0xd4a134: mov             x1, x3
    // 0xd4a138: r2 = 4
    //     0xd4a138: movz            x2, #0x4
    // 0xd4a13c: r0 = _add()
    //     0xd4a13c: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd4a140: ldur            x0, [fp, #-0x10]
    // 0xd4a144: r2 = LoadInt32Instr(r0)
    //     0xd4a144: sbfx            x2, x0, #1, #0x1f
    //     0xd4a148: tbz             w0, #0, #0xd4a150
    //     0xd4a14c: ldur            x2, [x0, #7]
    // 0xd4a150: ldur            x1, [fp, #-8]
    // 0xd4a154: r0 = putInt64()
    //     0xd4a154: bl              #0xd49bfc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::putInt64
    // 0xd4a158: b               #0xd4a1cc
    // 0xd4a15c: cmp             x1, #0x18d
    // 0xd4a160: b.ne            #0xd4a18c
    // 0xd4a164: ldur            x1, [fp, #-8]
    // 0xd4a168: r2 = 129
    //     0xd4a168: movz            x2, #0x81
    // 0xd4a16c: r0 = _add()
    //     0xd4a16c: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd4a170: ldur            x1, [fp, #-0x10]
    // 0xd4a174: r0 = props()
    //     0xd4a174: bl              #0xbdc38c  ; [package:nuonline/app/data/models/zakat_menu.dart] ZakatMenu::props
    // 0xd4a178: ldur            x1, [fp, #-0x18]
    // 0xd4a17c: ldur            x2, [fp, #-8]
    // 0xd4a180: mov             x3, x0
    // 0xd4a184: r0 = writeValue()
    //     0xd4a184: bl              #0xd4a0e8  ; [package:wakelock_plus_platform_interface/messages.g.dart] _PigeonCodec::writeValue
    // 0xd4a188: b               #0xd4a1cc
    // 0xd4a18c: cmp             x1, #0x18c
    // 0xd4a190: b.ne            #0xd4a1bc
    // 0xd4a194: ldur            x1, [fp, #-8]
    // 0xd4a198: r2 = 130
    //     0xd4a198: movz            x2, #0x82
    // 0xd4a19c: r0 = _add()
    //     0xd4a19c: bl              #0xd496bc  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xd4a1a0: ldur            x1, [fp, #-0x10]
    // 0xd4a1a4: r0 = props()
    //     0xd4a1a4: bl              #0xbdc38c  ; [package:nuonline/app/data/models/zakat_menu.dart] ZakatMenu::props
    // 0xd4a1a8: ldur            x1, [fp, #-0x18]
    // 0xd4a1ac: ldur            x2, [fp, #-8]
    // 0xd4a1b0: mov             x3, x0
    // 0xd4a1b4: r0 = writeValue()
    //     0xd4a1b4: bl              #0xd4a0e8  ; [package:wakelock_plus_platform_interface/messages.g.dart] _PigeonCodec::writeValue
    // 0xd4a1b8: b               #0xd4a1cc
    // 0xd4a1bc: ldur            x1, [fp, #-0x18]
    // 0xd4a1c0: ldur            x2, [fp, #-8]
    // 0xd4a1c4: ldur            x3, [fp, #-0x10]
    // 0xd4a1c8: r0 = writeValue()
    //     0xd4a1c8: bl              #0xd4a458  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::writeValue
    // 0xd4a1cc: r0 = Null
    //     0xd4a1cc: mov             x0, NULL
    // 0xd4a1d0: LeaveFrame
    //     0xd4a1d0: mov             SP, fp
    //     0xd4a1d4: ldp             fp, lr, [SP], #0x10
    // 0xd4a1d8: ret
    //     0xd4a1d8: ret             
    // 0xd4a1dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4a1dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4a1e0: b               #0xd4a118
  }
}
