// lib: , url: package:wakelock_plus_platform_interface/src/method_channel_wakelock_plus.dart

// class id: 1051236, size: 0x8
class :: {
}

// class id: 5858, size: 0xc, field offset: 0x8
class MethodChannelWakelockPlus extends WakelockPlusPlatformInterface {

  _ toggle(/* No info */) async {
    // ** addr: 0x8ad6a4, size: 0x74
    // 0x8ad6a4: EnterFrame
    //     0x8ad6a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ad6a8: mov             fp, SP
    // 0x8ad6ac: AllocStack(0x18)
    //     0x8ad6ac: sub             SP, SP, #0x18
    // 0x8ad6b0: SetupParameters(MethodChannelWakelockPlus this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8ad6b0: stur            NULL, [fp, #-8]
    //     0x8ad6b4: stur            x1, [fp, #-0x10]
    //     0x8ad6b8: stur            x2, [fp, #-0x18]
    // 0x8ad6bc: CheckStackOverflow
    //     0x8ad6bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ad6c0: cmp             SP, x16
    //     0x8ad6c4: b.ls            #0x8ad710
    // 0x8ad6c8: InitAsync() -> Future<void?>
    //     0x8ad6c8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8ad6cc: bl              #0x661298  ; InitAsyncStub
    // 0x8ad6d0: r0 = ToggleMessage()
    //     0x8ad6d0: bl              #0x8ad9b4  ; AllocateToggleMessageStub -> ToggleMessage (size=0xc)
    // 0x8ad6d4: mov             x1, x0
    // 0x8ad6d8: ldur            x0, [fp, #-0x18]
    // 0x8ad6dc: StoreField: r1->field_7 = r0
    //     0x8ad6dc: stur            w0, [x1, #7]
    // 0x8ad6e0: ldur            x0, [fp, #-0x10]
    // 0x8ad6e4: LoadField: r2 = r0->field_7
    //     0x8ad6e4: ldur            w2, [x0, #7]
    // 0x8ad6e8: DecompressPointer r2
    //     0x8ad6e8: add             x2, x2, HEAP, lsl #32
    // 0x8ad6ec: mov             x16, x1
    // 0x8ad6f0: mov             x1, x2
    // 0x8ad6f4: mov             x2, x16
    // 0x8ad6f8: r0 = toggle()
    //     0x8ad6f8: bl              #0x8ad718  ; [package:wakelock_plus_platform_interface/messages.g.dart] WakelockPlusApi::toggle
    // 0x8ad6fc: mov             x1, x0
    // 0x8ad700: stur            x1, [fp, #-0x10]
    // 0x8ad704: r0 = Await()
    //     0x8ad704: bl              #0x661044  ; AwaitStub
    // 0x8ad708: r0 = Null
    //     0x8ad708: mov             x0, NULL
    // 0x8ad70c: r0 = ReturnAsyncNotFuture()
    //     0x8ad70c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x8ad710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ad710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ad714: b               #0x8ad6c8
  }
  _ MethodChannelWakelockPlus(/* No info */) {
    // ** addr: 0x8ada48, size: 0xbc
    // 0x8ada48: EnterFrame
    //     0x8ada48: stp             fp, lr, [SP, #-0x10]!
    //     0x8ada4c: mov             fp, SP
    // 0x8ada50: AllocStack(0x10)
    //     0x8ada50: sub             SP, SP, #0x10
    // 0x8ada54: SetupParameters(MethodChannelWakelockPlus this /* r1 => r2, fp-0x8 */)
    //     0x8ada54: mov             x2, x1
    //     0x8ada58: stur            x1, [fp, #-8]
    // 0x8ada5c: CheckStackOverflow
    //     0x8ada5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ada60: cmp             SP, x16
    //     0x8ada64: b.ls            #0x8adafc
    // 0x8ada68: r0 = WakelockPlusApi()
    //     0x8ada68: bl              #0x8adb04  ; AllocateWakelockPlusApiStub -> WakelockPlusApi (size=0x10)
    // 0x8ada6c: mov             x1, x0
    // 0x8ada70: r0 = ""
    //     0x8ada70: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x8ada74: StoreField: r1->field_b = r0
    //     0x8ada74: stur            w0, [x1, #0xb]
    // 0x8ada78: mov             x0, x1
    // 0x8ada7c: ldur            x2, [fp, #-8]
    // 0x8ada80: StoreField: r2->field_7 = r0
    //     0x8ada80: stur            w0, [x2, #7]
    //     0x8ada84: ldurb           w16, [x2, #-1]
    //     0x8ada88: ldurb           w17, [x0, #-1]
    //     0x8ada8c: and             x16, x17, x16, lsr #2
    //     0x8ada90: tst             x16, HEAP, lsr #32
    //     0x8ada94: b.eq            #0x8ada9c
    //     0x8ada98: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x8ada9c: r0 = InitLateStaticField(0x1778) // [package:wakelock_plus_platform_interface/wakelock_plus_platform_interface.dart] WakelockPlusPlatformInterface::_token
    //     0x8ada9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8adaa0: ldr             x0, [x0, #0x2ef0]
    //     0x8adaa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8adaa8: cmp             w0, w16
    //     0x8adaac: b.ne            #0x8adabc
    //     0x8adab0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c478] Field <WakelockPlusPlatformInterface._token@2741180744>: static late final (offset: 0x1778)
    //     0x8adab4: ldr             x2, [x2, #0x478]
    //     0x8adab8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8adabc: stur            x0, [fp, #-0x10]
    // 0x8adac0: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x8adac0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8adac4: ldr             x0, [x0, #0xc08]
    //     0x8adac8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8adacc: cmp             w0, w16
    //     0x8adad0: b.ne            #0x8adadc
    //     0x8adad4: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x8adad8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8adadc: mov             x1, x0
    // 0x8adae0: ldur            x2, [fp, #-8]
    // 0x8adae4: ldur            x3, [fp, #-0x10]
    // 0x8adae8: r0 = []=()
    //     0x8adae8: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x8adaec: r0 = Null
    //     0x8adaec: mov             x0, NULL
    // 0x8adaf0: LeaveFrame
    //     0x8adaf0: mov             SP, fp
    //     0x8adaf4: ldp             fp, lr, [SP], #0x10
    // 0x8adaf8: ret
    //     0x8adaf8: ret             
    // 0x8adafc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8adafc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8adb00: b               #0x8ada68
  }
}
