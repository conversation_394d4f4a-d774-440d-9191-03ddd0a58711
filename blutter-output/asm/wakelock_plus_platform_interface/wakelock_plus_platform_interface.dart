// lib: , url: package:wakelock_plus_platform_interface/wakelock_plus_platform_interface.dart

// class id: 1051237, size: 0x8
class :: {
}

// class id: 5857, size: 0x8, field offset: 0x8
abstract class WakelockPlusPlatformInterface extends PlatformInterface {

  static late WakelockPlusPlatformInterface _instance; // offset: 0x177c
  static late final Object _token; // offset: 0x1778

  static WakelockPlusPlatformInterface _instance() {
    // ** addr: 0x8ada08, size: 0x40
    // 0x8ada08: EnterFrame
    //     0x8ada08: stp             fp, lr, [SP, #-0x10]!
    //     0x8ada0c: mov             fp, SP
    // 0x8ada10: AllocStack(0x8)
    //     0x8ada10: sub             SP, SP, #8
    // 0x8ada14: CheckStackOverflow
    //     0x8ada14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ada18: cmp             SP, x16
    //     0x8ada1c: b.ls            #0x8ada40
    // 0x8ada20: r0 = MethodChannelWakelockPlus()
    //     0x8ada20: bl              #0x8adb10  ; AllocateMethodChannelWakelockPlusStub -> MethodChannelWakelockPlus (size=0xc)
    // 0x8ada24: mov             x1, x0
    // 0x8ada28: stur            x0, [fp, #-8]
    // 0x8ada2c: r0 = MethodChannelWakelockPlus()
    //     0x8ada2c: bl              #0x8ada48  ; [package:wakelock_plus_platform_interface/src/method_channel_wakelock_plus.dart] MethodChannelWakelockPlus::MethodChannelWakelockPlus
    // 0x8ada30: ldur            x0, [fp, #-8]
    // 0x8ada34: LeaveFrame
    //     0x8ada34: mov             SP, fp
    //     0x8ada38: ldp             fp, lr, [SP], #0x10
    // 0x8ada3c: ret
    //     0x8ada3c: ret             
    // 0x8ada40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ada40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ada44: b               #0x8ada20
  }
}
