// lib: , url: package:waris/src/heirs.dart

// class id: 1051238, size: 0x8
class :: {

  static _ HeirsExt.countOf(/* No info */) {
    // ** addr: 0x90939c, size: 0xa0
    // 0x90939c: EnterFrame
    //     0x90939c: stp             fp, lr, [SP, #-0x10]!
    //     0x9093a0: mov             fp, SP
    // 0x9093a4: AllocStack(0x10)
    //     0x9093a4: sub             SP, SP, #0x10
    // 0x9093a8: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x9093a8: mov             x4, x1
    //     0x9093ac: mov             x3, x2
    //     0x9093b0: stur            x1, [fp, #-8]
    //     0x9093b4: stur            x2, [fp, #-0x10]
    // 0x9093b8: CheckStackOverflow
    //     0x9093b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9093bc: cmp             SP, x16
    //     0x9093c0: b.ls            #0x909434
    // 0x9093c4: r0 = LoadClassIdInstr(r4)
    //     0x9093c4: ldur            x0, [x4, #-1]
    //     0x9093c8: ubfx            x0, x0, #0xc, #0x14
    // 0x9093cc: mov             x1, x4
    // 0x9093d0: mov             x2, x3
    // 0x9093d4: r0 = GDT[cid_x0 + 0x55f]()
    //     0x9093d4: add             lr, x0, #0x55f
    //     0x9093d8: ldr             lr, [x21, lr, lsl #3]
    //     0x9093dc: blr             lr
    // 0x9093e0: tbnz            w0, #4, #0x909424
    // 0x9093e4: ldur            x1, [fp, #-8]
    // 0x9093e8: r0 = LoadClassIdInstr(r1)
    //     0x9093e8: ldur            x0, [x1, #-1]
    //     0x9093ec: ubfx            x0, x0, #0xc, #0x14
    // 0x9093f0: ldur            x2, [fp, #-0x10]
    // 0x9093f4: r0 = GDT[cid_x0 + -0x114]()
    //     0x9093f4: sub             lr, x0, #0x114
    //     0x9093f8: ldr             lr, [x21, lr, lsl #3]
    //     0x9093fc: blr             lr
    // 0x909400: cmp             w0, NULL
    // 0x909404: b.ne            #0x909410
    // 0x909408: r1 = 0
    //     0x909408: movz            x1, #0
    // 0x90940c: b               #0x90941c
    // 0x909410: r1 = LoadInt32Instr(r0)
    //     0x909410: sbfx            x1, x0, #1, #0x1f
    //     0x909414: tbz             w0, #0, #0x90941c
    //     0x909418: ldur            x1, [x0, #7]
    // 0x90941c: mov             x0, x1
    // 0x909420: b               #0x909428
    // 0x909424: r0 = 0
    //     0x909424: movz            x0, #0
    // 0x909428: LeaveFrame
    //     0x909428: mov             SP, fp
    //     0x90942c: ldp             fp, lr, [SP], #0x10
    // 0x909430: ret
    //     0x909430: ret             
    // 0x909434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909434: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909438: b               #0x9093c4
  }
  static _ HeirsExt.notHasAnyOf(/* No info */) {
    // ** addr: 0x909850, size: 0x34
    // 0x909850: EnterFrame
    //     0x909850: stp             fp, lr, [SP, #-0x10]!
    //     0x909854: mov             fp, SP
    // 0x909858: CheckStackOverflow
    //     0x909858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90985c: cmp             SP, x16
    //     0x909860: b.ls            #0x90987c
    // 0x909864: r0 = HeirsExt.hasAnyOf()
    //     0x909864: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x909868: eor             x1, x0, #0x10
    // 0x90986c: mov             x0, x1
    // 0x909870: LeaveFrame
    //     0x909870: mov             SP, fp
    //     0x909874: ldp             fp, lr, [SP], #0x10
    // 0x909878: ret
    //     0x909878: ret             
    // 0x90987c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90987c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909880: b               #0x909864
  }
  static _ HeirsExt.hasAnyOf(/* No info */) {
    // ** addr: 0x909884, size: 0x70
    // 0x909884: EnterFrame
    //     0x909884: stp             fp, lr, [SP, #-0x10]!
    //     0x909888: mov             fp, SP
    // 0x90988c: AllocStack(0x10)
    //     0x90988c: sub             SP, SP, #0x10
    // 0x909890: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x909890: stur            x2, [fp, #-8]
    // 0x909894: CheckStackOverflow
    //     0x909894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x909898: cmp             SP, x16
    //     0x90989c: b.ls            #0x9098ec
    // 0x9098a0: r0 = LoadClassIdInstr(r1)
    //     0x9098a0: ldur            x0, [x1, #-1]
    //     0x9098a4: ubfx            x0, x0, #0xc, #0x14
    // 0x9098a8: r0 = GDT[cid_x0 + 0x656]()
    //     0x9098a8: add             lr, x0, #0x656
    //     0x9098ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9098b0: blr             lr
    // 0x9098b4: r1 = LoadClassIdInstr(r0)
    //     0x9098b4: ldur            x1, [x0, #-1]
    //     0x9098b8: ubfx            x1, x1, #0xc, #0x14
    // 0x9098bc: str             x0, [SP]
    // 0x9098c0: mov             x0, x1
    // 0x9098c4: r0 = GDT[cid_x0 + 0xf0bf]()
    //     0x9098c4: movz            x17, #0xf0bf
    //     0x9098c8: add             lr, x0, x17
    //     0x9098cc: ldr             lr, [x21, lr, lsl #3]
    //     0x9098d0: blr             lr
    // 0x9098d4: ldur            x1, [fp, #-8]
    // 0x9098d8: mov             x2, x0
    // 0x9098dc: r0 = any()
    //     0x9098dc: bl              #0x7b4a2c  ; [dart:collection] ListBase::any
    // 0x9098e0: LeaveFrame
    //     0x9098e0: mov             SP, fp
    //     0x9098e4: ldp             fp, lr, [SP], #0x10
    // 0x9098e8: ret
    //     0x9098e8: ret             
    // 0x9098ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9098ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9098f0: b               #0x9098a0
  }
  static _ HeirsExt.search(/* No info */) {
    // ** addr: 0x909c2c, size: 0x80
    // 0x909c2c: EnterFrame
    //     0x909c2c: stp             fp, lr, [SP, #-0x10]!
    //     0x909c30: mov             fp, SP
    // 0x909c34: AllocStack(0x10)
    //     0x909c34: sub             SP, SP, #0x10
    // 0x909c38: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x909c38: stur            x2, [fp, #-8]
    // 0x909c3c: CheckStackOverflow
    //     0x909c3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x909c40: cmp             SP, x16
    //     0x909c44: b.ls            #0x909ca4
    // 0x909c48: r0 = LoadClassIdInstr(r1)
    //     0x909c48: ldur            x0, [x1, #-1]
    //     0x909c4c: ubfx            x0, x0, #0xc, #0x14
    // 0x909c50: r0 = GDT[cid_x0 + 0x656]()
    //     0x909c50: add             lr, x0, #0x656
    //     0x909c54: ldr             lr, [x21, lr, lsl #3]
    //     0x909c58: blr             lr
    // 0x909c5c: r1 = LoadClassIdInstr(r0)
    //     0x909c5c: ldur            x1, [x0, #-1]
    //     0x909c60: ubfx            x1, x1, #0xc, #0x14
    // 0x909c64: str             x0, [SP]
    // 0x909c68: mov             x0, x1
    // 0x909c6c: r0 = GDT[cid_x0 + 0xf0bf]()
    //     0x909c6c: movz            x17, #0xf0bf
    //     0x909c70: add             lr, x0, x17
    //     0x909c74: ldr             lr, [x21, lr, lsl #3]
    //     0x909c78: blr             lr
    // 0x909c7c: ldur            x1, [fp, #-8]
    // 0x909c80: mov             x2, x0
    // 0x909c84: r0 = where()
    //     0x909c84: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x909c88: LoadField: r1 = r0->field_7
    //     0x909c88: ldur            w1, [x0, #7]
    // 0x909c8c: DecompressPointer r1
    //     0x909c8c: add             x1, x1, HEAP, lsl #32
    // 0x909c90: mov             x2, x0
    // 0x909c94: r0 = _GrowableList.of()
    //     0x909c94: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x909c98: LeaveFrame
    //     0x909c98: mov             SP, fp
    //     0x909c9c: ldp             fp, lr, [SP], #0x10
    // 0x909ca0: ret
    //     0x909ca0: ret             
    // 0x909ca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909ca4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909ca8: b               #0x909c48
  }
  static _ HeirsExt.hasEveryOf(/* No info */) {
    // ** addr: 0x90a48c, size: 0x70
    // 0x90a48c: EnterFrame
    //     0x90a48c: stp             fp, lr, [SP, #-0x10]!
    //     0x90a490: mov             fp, SP
    // 0x90a494: AllocStack(0x10)
    //     0x90a494: sub             SP, SP, #0x10
    // 0x90a498: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x90a498: stur            x2, [fp, #-8]
    // 0x90a49c: CheckStackOverflow
    //     0x90a49c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a4a0: cmp             SP, x16
    //     0x90a4a4: b.ls            #0x90a4f4
    // 0x90a4a8: r0 = LoadClassIdInstr(r1)
    //     0x90a4a8: ldur            x0, [x1, #-1]
    //     0x90a4ac: ubfx            x0, x0, #0xc, #0x14
    // 0x90a4b0: r0 = GDT[cid_x0 + 0x656]()
    //     0x90a4b0: add             lr, x0, #0x656
    //     0x90a4b4: ldr             lr, [x21, lr, lsl #3]
    //     0x90a4b8: blr             lr
    // 0x90a4bc: r1 = LoadClassIdInstr(r0)
    //     0x90a4bc: ldur            x1, [x0, #-1]
    //     0x90a4c0: ubfx            x1, x1, #0xc, #0x14
    // 0x90a4c4: str             x0, [SP]
    // 0x90a4c8: mov             x0, x1
    // 0x90a4cc: r0 = GDT[cid_x0 + 0xf0bf]()
    //     0x90a4cc: movz            x17, #0xf0bf
    //     0x90a4d0: add             lr, x0, x17
    //     0x90a4d4: ldr             lr, [x21, lr, lsl #3]
    //     0x90a4d8: blr             lr
    // 0x90a4dc: ldur            x1, [fp, #-8]
    // 0x90a4e0: mov             x2, x0
    // 0x90a4e4: r0 = every()
    //     0x90a4e4: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0x90a4e8: LeaveFrame
    //     0x90a4e8: mov             SP, fp
    //     0x90a4ec: ldp             fp, lr, [SP], #0x10
    // 0x90a4f0: ret
    //     0x90a4f0: ret             
    // 0x90a4f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a4f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a4f8: b               #0x90a4a8
  }
  static _ HeirsExt.has(/* No info */) {
    // ** addr: 0x90a4fc, size: 0x6c
    // 0x90a4fc: EnterFrame
    //     0x90a4fc: stp             fp, lr, [SP, #-0x10]!
    //     0x90a500: mov             fp, SP
    // 0x90a504: AllocStack(0x8)
    //     0x90a504: sub             SP, SP, #8
    // 0x90a508: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x90a508: stur            x2, [fp, #-8]
    // 0x90a50c: CheckStackOverflow
    //     0x90a50c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a510: cmp             SP, x16
    //     0x90a514: b.ls            #0x90a560
    // 0x90a518: r0 = LoadClassIdInstr(r1)
    //     0x90a518: ldur            x0, [x1, #-1]
    //     0x90a51c: ubfx            x0, x0, #0xc, #0x14
    // 0x90a520: r0 = GDT[cid_x0 + 0x656]()
    //     0x90a520: add             lr, x0, #0x656
    //     0x90a524: ldr             lr, [x21, lr, lsl #3]
    //     0x90a528: blr             lr
    // 0x90a52c: r1 = LoadClassIdInstr(r0)
    //     0x90a52c: ldur            x1, [x0, #-1]
    //     0x90a530: ubfx            x1, x1, #0xc, #0x14
    // 0x90a534: mov             x16, x0
    // 0x90a538: mov             x0, x1
    // 0x90a53c: mov             x1, x16
    // 0x90a540: ldur            x2, [fp, #-8]
    // 0x90a544: r0 = GDT[cid_x0 + 0xf20c]()
    //     0x90a544: movz            x17, #0xf20c
    //     0x90a548: add             lr, x0, x17
    //     0x90a54c: ldr             lr, [x21, lr, lsl #3]
    //     0x90a550: blr             lr
    // 0x90a554: LeaveFrame
    //     0x90a554: mov             SP, fp
    //     0x90a558: ldp             fp, lr, [SP], #0x10
    // 0x90a55c: ret
    //     0x90a55c: ret             
    // 0x90a560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a560: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a564: b               #0x90a518
  }
  [closure] static int <anonymous closure>(dynamic, Heir) {
    // ** addr: 0x90ac14, size: 0x60
    // 0x90ac14: EnterFrame
    //     0x90ac14: stp             fp, lr, [SP, #-0x10]!
    //     0x90ac18: mov             fp, SP
    // 0x90ac1c: ldr             x0, [fp, #0x18]
    // 0x90ac20: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x90ac20: ldur            w1, [x0, #0x17]
    // 0x90ac24: DecompressPointer r1
    //     0x90ac24: add             x1, x1, HEAP, lsl #32
    // 0x90ac28: CheckStackOverflow
    //     0x90ac28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ac2c: cmp             SP, x16
    //     0x90ac30: b.ls            #0x90ac6c
    // 0x90ac34: LoadField: r0 = r1->field_f
    //     0x90ac34: ldur            w0, [x1, #0xf]
    // 0x90ac38: DecompressPointer r0
    //     0x90ac38: add             x0, x0, HEAP, lsl #32
    // 0x90ac3c: mov             x1, x0
    // 0x90ac40: ldr             x2, [fp, #0x10]
    // 0x90ac44: r0 = HeirsExt.countOf()
    //     0x90ac44: bl              #0x90939c  ; [package:waris/src/heirs.dart] ::HeirsExt.countOf
    // 0x90ac48: mov             x2, x0
    // 0x90ac4c: r0 = BoxInt64Instr(r2)
    //     0x90ac4c: sbfiz           x0, x2, #1, #0x1f
    //     0x90ac50: cmp             x2, x0, asr #1
    //     0x90ac54: b.eq            #0x90ac60
    //     0x90ac58: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90ac5c: stur            x2, [x0, #7]
    // 0x90ac60: LeaveFrame
    //     0x90ac60: mov             SP, fp
    //     0x90ac64: ldp             fp, lr, [SP], #0x10
    // 0x90ac68: ret
    //     0x90ac68: ret             
    // 0x90ac6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90ac6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90ac70: b               #0x90ac34
  }
  static _ HeirsExt.hasOneOf(/* No info */) {
    // ** addr: 0x90c538, size: 0x94
    // 0x90c538: EnterFrame
    //     0x90c538: stp             fp, lr, [SP, #-0x10]!
    //     0x90c53c: mov             fp, SP
    // 0x90c540: AllocStack(0x10)
    //     0x90c540: sub             SP, SP, #0x10
    // 0x90c544: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x90c544: mov             x4, x1
    //     0x90c548: mov             x3, x2
    //     0x90c54c: stur            x1, [fp, #-8]
    //     0x90c550: stur            x2, [fp, #-0x10]
    // 0x90c554: CheckStackOverflow
    //     0x90c554: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c558: cmp             SP, x16
    //     0x90c55c: b.ls            #0x90c5c4
    // 0x90c560: r0 = LoadClassIdInstr(r4)
    //     0x90c560: ldur            x0, [x4, #-1]
    //     0x90c564: ubfx            x0, x0, #0xc, #0x14
    // 0x90c568: mov             x1, x4
    // 0x90c56c: mov             x2, x3
    // 0x90c570: r0 = GDT[cid_x0 + 0x55f]()
    //     0x90c570: add             lr, x0, #0x55f
    //     0x90c574: ldr             lr, [x21, lr, lsl #3]
    //     0x90c578: blr             lr
    // 0x90c57c: tbnz            w0, #4, #0x90c5b4
    // 0x90c580: ldur            x1, [fp, #-8]
    // 0x90c584: r0 = LoadClassIdInstr(r1)
    //     0x90c584: ldur            x0, [x1, #-1]
    //     0x90c588: ubfx            x0, x0, #0xc, #0x14
    // 0x90c58c: ldur            x2, [fp, #-0x10]
    // 0x90c590: r0 = GDT[cid_x0 + -0x114]()
    //     0x90c590: sub             lr, x0, #0x114
    //     0x90c594: ldr             lr, [x21, lr, lsl #3]
    //     0x90c598: blr             lr
    // 0x90c59c: cmp             w0, #2
    // 0x90c5a0: r16 = true
    //     0x90c5a0: add             x16, NULL, #0x20  ; true
    // 0x90c5a4: r17 = false
    //     0x90c5a4: add             x17, NULL, #0x30  ; false
    // 0x90c5a8: csel            x1, x16, x17, eq
    // 0x90c5ac: mov             x0, x1
    // 0x90c5b0: b               #0x90c5b8
    // 0x90c5b4: r0 = false
    //     0x90c5b4: add             x0, NULL, #0x30  ; false
    // 0x90c5b8: LeaveFrame
    //     0x90c5b8: mov             SP, fp
    //     0x90c5bc: ldp             fp, lr, [SP], #0x10
    // 0x90c5c0: ret
    //     0x90c5c0: ret             
    // 0x90c5c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c5c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c5c8: b               #0x90c560
  }
  static _ HeirsExt.notHas(/* No info */) {
    // ** addr: 0x90c8a8, size: 0x34
    // 0x90c8a8: EnterFrame
    //     0x90c8a8: stp             fp, lr, [SP, #-0x10]!
    //     0x90c8ac: mov             fp, SP
    // 0x90c8b0: CheckStackOverflow
    //     0x90c8b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c8b4: cmp             SP, x16
    //     0x90c8b8: b.ls            #0x90c8d4
    // 0x90c8bc: r0 = HeirsExt.has()
    //     0x90c8bc: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90c8c0: eor             x1, x0, #0x10
    // 0x90c8c4: mov             x0, x1
    // 0x90c8c8: LeaveFrame
    //     0x90c8c8: mov             SP, fp
    //     0x90c8cc: ldp             fp, lr, [SP], #0x10
    // 0x90c8d0: ret
    //     0x90c8d0: ret             
    // 0x90c8d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c8d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c8d8: b               #0x90c8bc
  }
  static _ HeirsExt.hasManyOf(/* No info */) {
    // ** addr: 0x90c8dc, size: 0xac
    // 0x90c8dc: EnterFrame
    //     0x90c8dc: stp             fp, lr, [SP, #-0x10]!
    //     0x90c8e0: mov             fp, SP
    // 0x90c8e4: AllocStack(0x10)
    //     0x90c8e4: sub             SP, SP, #0x10
    // 0x90c8e8: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x90c8e8: mov             x4, x1
    //     0x90c8ec: mov             x3, x2
    //     0x90c8f0: stur            x1, [fp, #-8]
    //     0x90c8f4: stur            x2, [fp, #-0x10]
    // 0x90c8f8: CheckStackOverflow
    //     0x90c8f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c8fc: cmp             SP, x16
    //     0x90c900: b.ls            #0x90c97c
    // 0x90c904: r0 = LoadClassIdInstr(r4)
    //     0x90c904: ldur            x0, [x4, #-1]
    //     0x90c908: ubfx            x0, x0, #0xc, #0x14
    // 0x90c90c: mov             x1, x4
    // 0x90c910: mov             x2, x3
    // 0x90c914: r0 = GDT[cid_x0 + 0x55f]()
    //     0x90c914: add             lr, x0, #0x55f
    //     0x90c918: ldr             lr, [x21, lr, lsl #3]
    //     0x90c91c: blr             lr
    // 0x90c920: tbnz            w0, #4, #0x90c96c
    // 0x90c924: ldur            x1, [fp, #-8]
    // 0x90c928: r0 = LoadClassIdInstr(r1)
    //     0x90c928: ldur            x0, [x1, #-1]
    //     0x90c92c: ubfx            x0, x0, #0xc, #0x14
    // 0x90c930: ldur            x2, [fp, #-0x10]
    // 0x90c934: r0 = GDT[cid_x0 + -0x114]()
    //     0x90c934: sub             lr, x0, #0x114
    //     0x90c938: ldr             lr, [x21, lr, lsl #3]
    //     0x90c93c: blr             lr
    // 0x90c940: cmp             w0, NULL
    // 0x90c944: b.eq            #0x90c984
    // 0x90c948: r1 = LoadInt32Instr(r0)
    //     0x90c948: sbfx            x1, x0, #1, #0x1f
    //     0x90c94c: tbz             w0, #0, #0x90c954
    //     0x90c950: ldur            x1, [x0, #7]
    // 0x90c954: cmp             x1, #1
    // 0x90c958: r16 = true
    //     0x90c958: add             x16, NULL, #0x20  ; true
    // 0x90c95c: r17 = false
    //     0x90c95c: add             x17, NULL, #0x30  ; false
    // 0x90c960: csel            x2, x16, x17, gt
    // 0x90c964: mov             x0, x2
    // 0x90c968: b               #0x90c970
    // 0x90c96c: r0 = false
    //     0x90c96c: add             x0, NULL, #0x30  ; false
    // 0x90c970: LeaveFrame
    //     0x90c970: mov             SP, fp
    //     0x90c974: ldp             fp, lr, [SP], #0x10
    // 0x90c978: ret
    //     0x90c978: ret             
    // 0x90c97c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c97c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c980: b               #0x90c904
    // 0x90c984: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90c984: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 6760, size: 0x24, field offset: 0x14
enum Heir extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  _TwoByteString field_14;
  _OneByteString field_18;
  _Mint field_1c;

  get _ portion(/* No info */) {
    // ** addr: 0xbb0c24, size: 0x1c
    // 0xbb0c24: LoadField: r2 = r1->field_7
    //     0xbb0c24: ldur            x2, [x1, #7]
    // 0xbb0c28: cmp             x2, #0xf
    // 0xbb0c2c: b.gt            #0xbb0c38
    // 0xbb0c30: r0 = 2
    //     0xbb0c30: movz            x0, #0x2
    // 0xbb0c34: b               #0xbb0c3c
    // 0xbb0c38: r0 = 1
    //     0xbb0c38: movz            x0, #0x1
    // 0xbb0c3c: ret
    //     0xbb0c3c: ret             
  }
  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ed38, size: 0x64
    // 0xc4ed38: EnterFrame
    //     0xc4ed38: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ed3c: mov             fp, SP
    // 0xc4ed40: AllocStack(0x10)
    //     0xc4ed40: sub             SP, SP, #0x10
    // 0xc4ed44: SetupParameters(Heir this /* r1 => r0, fp-0x8 */)
    //     0xc4ed44: mov             x0, x1
    //     0xc4ed48: stur            x1, [fp, #-8]
    // 0xc4ed4c: CheckStackOverflow
    //     0xc4ed4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ed50: cmp             SP, x16
    //     0xc4ed54: b.ls            #0xc4ed94
    // 0xc4ed58: r1 = Null
    //     0xc4ed58: mov             x1, NULL
    // 0xc4ed5c: r2 = 4
    //     0xc4ed5c: movz            x2, #0x4
    // 0xc4ed60: r0 = AllocateArray()
    //     0xc4ed60: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ed64: r16 = "Heir."
    //     0xc4ed64: add             x16, PP, #0x31, lsl #12  ; [pp+0x31eb0] "Heir."
    //     0xc4ed68: ldr             x16, [x16, #0xeb0]
    // 0xc4ed6c: StoreField: r0->field_f = r16
    //     0xc4ed6c: stur            w16, [x0, #0xf]
    // 0xc4ed70: ldur            x1, [fp, #-8]
    // 0xc4ed74: LoadField: r2 = r1->field_f
    //     0xc4ed74: ldur            w2, [x1, #0xf]
    // 0xc4ed78: DecompressPointer r2
    //     0xc4ed78: add             x2, x2, HEAP, lsl #32
    // 0xc4ed7c: StoreField: r0->field_13 = r2
    //     0xc4ed7c: stur            w2, [x0, #0x13]
    // 0xc4ed80: str             x0, [SP]
    // 0xc4ed84: r0 = _interpolate()
    //     0xc4ed84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ed88: LeaveFrame
    //     0xc4ed88: mov             SP, fp
    //     0xc4ed8c: ldp             fp, lr, [SP], #0x10
    // 0xc4ed90: ret
    //     0xc4ed90: ret             
    // 0xc4ed94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ed94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ed98: b               #0xc4ed58
  }
}
