// lib: , url: package:waris/src/result.dart

// class id: 1051239, size: 0x8
class :: {
}

// class id: 394, size: 0x20, field offset: 0x8
//   const constructor, 
class Result extends Object {

  _ copyWith(/* No info */) {
    // ** addr: 0x907cf4, size: 0x190
    // 0x907cf4: EnterFrame
    //     0x907cf4: stp             fp, lr, [SP, #-0x10]!
    //     0x907cf8: mov             fp, SP
    // 0x907cfc: AllocStack(0x20)
    //     0x907cfc: sub             SP, SP, #0x20
    // 0x907d00: SetupParameters({dynamic amount = Null /* r3 */, dynamic share = Null /* r5 */, dynamic siham = Null /* r0 */})
    //     0x907d00: ldur            w0, [x4, #0x13]
    //     0x907d04: ldur            w2, [x4, #0x1f]
    //     0x907d08: add             x2, x2, HEAP, lsl #32
    //     0x907d0c: add             x16, PP, #0x27, lsl #12  ; [pp+0x270e0] "amount"
    //     0x907d10: ldr             x16, [x16, #0xe0]
    //     0x907d14: cmp             w2, w16
    //     0x907d18: b.ne            #0x907d3c
    //     0x907d1c: ldur            w2, [x4, #0x23]
    //     0x907d20: add             x2, x2, HEAP, lsl #32
    //     0x907d24: sub             w3, w0, w2
    //     0x907d28: add             x2, fp, w3, sxtw #2
    //     0x907d2c: ldr             x2, [x2, #8]
    //     0x907d30: mov             x3, x2
    //     0x907d34: movz            x2, #0x1
    //     0x907d38: b               #0x907d44
    //     0x907d3c: mov             x3, NULL
    //     0x907d40: movz            x2, #0
    //     0x907d44: lsl             x5, x2, #1
    //     0x907d48: lsl             w6, w5, #1
    //     0x907d4c: add             w7, w6, #8
    //     0x907d50: add             x16, x4, w7, sxtw #1
    //     0x907d54: ldur            w8, [x16, #0xf]
    //     0x907d58: add             x8, x8, HEAP, lsl #32
    //     0x907d5c: add             x16, PP, #0x28, lsl #12  ; [pp+0x284c0] "share"
    //     0x907d60: ldr             x16, [x16, #0x4c0]
    //     0x907d64: cmp             w8, w16
    //     0x907d68: b.ne            #0x907d9c
    //     0x907d6c: add             w2, w6, #0xa
    //     0x907d70: add             x16, x4, w2, sxtw #1
    //     0x907d74: ldur            w6, [x16, #0xf]
    //     0x907d78: add             x6, x6, HEAP, lsl #32
    //     0x907d7c: sub             w2, w0, w6
    //     0x907d80: add             x6, fp, w2, sxtw #2
    //     0x907d84: ldr             x6, [x6, #8]
    //     0x907d88: add             w2, w5, #2
    //     0x907d8c: sbfx            x5, x2, #1, #0x1f
    //     0x907d90: mov             x2, x5
    //     0x907d94: mov             x5, x6
    //     0x907d98: b               #0x907da0
    //     0x907d9c: mov             x5, NULL
    //     0x907da0: lsl             x6, x2, #1
    //     0x907da4: lsl             w2, w6, #1
    //     0x907da8: add             w6, w2, #8
    //     0x907dac: add             x16, x4, w6, sxtw #1
    //     0x907db0: ldur            w7, [x16, #0xf]
    //     0x907db4: add             x7, x7, HEAP, lsl #32
    //     0x907db8: add             x16, PP, #0x28, lsl #12  ; [pp+0x284c8] "siham"
    //     0x907dbc: ldr             x16, [x16, #0x4c8]
    //     0x907dc0: cmp             w7, w16
    //     0x907dc4: b.ne            #0x907de8
    //     0x907dc8: add             w6, w2, #0xa
    //     0x907dcc: add             x16, x4, w6, sxtw #1
    //     0x907dd0: ldur            w2, [x16, #0xf]
    //     0x907dd4: add             x2, x2, HEAP, lsl #32
    //     0x907dd8: sub             w4, w0, w2
    //     0x907ddc: add             x0, fp, w4, sxtw #2
    //     0x907de0: ldr             x0, [x0, #8]
    //     0x907de4: b               #0x907dec
    //     0x907de8: mov             x0, NULL
    // 0x907dec: LoadField: r2 = r1->field_7
    //     0x907dec: ldur            x2, [x1, #7]
    // 0x907df0: stur            x2, [fp, #-0x20]
    // 0x907df4: cmp             w5, NULL
    // 0x907df8: b.ne            #0x907e08
    // 0x907dfc: LoadField: r4 = r1->field_f
    //     0x907dfc: ldur            w4, [x1, #0xf]
    // 0x907e00: DecompressPointer r4
    //     0x907e00: add             x4, x4, HEAP, lsl #32
    // 0x907e04: b               #0x907e0c
    // 0x907e08: mov             x4, x5
    // 0x907e0c: stur            x4, [fp, #-0x18]
    // 0x907e10: cmp             w0, NULL
    // 0x907e14: b.ne            #0x907e20
    // 0x907e18: LoadField: r0 = r1->field_13
    //     0x907e18: ldur            x0, [x1, #0x13]
    // 0x907e1c: b               #0x907e30
    // 0x907e20: r5 = LoadInt32Instr(r0)
    //     0x907e20: sbfx            x5, x0, #1, #0x1f
    //     0x907e24: tbz             w0, #0, #0x907e2c
    //     0x907e28: ldur            x5, [x0, #7]
    // 0x907e2c: mov             x0, x5
    // 0x907e30: stur            x0, [fp, #-0x10]
    // 0x907e34: cmp             w3, NULL
    // 0x907e38: b.ne            #0x907e4c
    // 0x907e3c: LoadField: r3 = r1->field_1b
    //     0x907e3c: ldur            w3, [x1, #0x1b]
    // 0x907e40: DecompressPointer r3
    //     0x907e40: add             x3, x3, HEAP, lsl #32
    // 0x907e44: mov             x1, x3
    // 0x907e48: b               #0x907e50
    // 0x907e4c: mov             x1, x3
    // 0x907e50: stur            x1, [fp, #-8]
    // 0x907e54: r0 = Result()
    //     0x907e54: bl              #0x907e84  ; AllocateResultStub -> Result (size=0x20)
    // 0x907e58: ldur            x1, [fp, #-0x20]
    // 0x907e5c: StoreField: r0->field_7 = r1
    //     0x907e5c: stur            x1, [x0, #7]
    // 0x907e60: ldur            x1, [fp, #-0x18]
    // 0x907e64: StoreField: r0->field_f = r1
    //     0x907e64: stur            w1, [x0, #0xf]
    // 0x907e68: ldur            x1, [fp, #-0x10]
    // 0x907e6c: StoreField: r0->field_13 = r1
    //     0x907e6c: stur            x1, [x0, #0x13]
    // 0x907e70: ldur            x1, [fp, #-8]
    // 0x907e74: StoreField: r0->field_1b = r1
    //     0x907e74: stur            w1, [x0, #0x1b]
    // 0x907e78: LeaveFrame
    //     0x907e78: mov             SP, fp
    //     0x907e7c: ldp             fp, lr, [SP], #0x10
    // 0x907e80: ret
    //     0x907e80: ret             
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3d8c, size: 0x244
    // 0xbf3d8c: EnterFrame
    //     0xbf3d8c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3d90: mov             fp, SP
    // 0xbf3d94: AllocStack(0x20)
    //     0xbf3d94: sub             SP, SP, #0x20
    // 0xbf3d98: CheckStackOverflow
    //     0xbf3d98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3d9c: cmp             SP, x16
    //     0xbf3da0: b.ls            #0xbf3fc8
    // 0xbf3da4: ldr             x2, [fp, #0x10]
    // 0xbf3da8: LoadField: r3 = r2->field_7
    //     0xbf3da8: ldur            x3, [x2, #7]
    // 0xbf3dac: r0 = BoxInt64Instr(r3)
    //     0xbf3dac: sbfiz           x0, x3, #1, #0x1f
    //     0xbf3db0: cmp             x3, x0, asr #1
    //     0xbf3db4: b.eq            #0xbf3dc0
    //     0xbf3db8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3dbc: stur            x3, [x0, #7]
    // 0xbf3dc0: r1 = 60
    //     0xbf3dc0: movz            x1, #0x3c
    // 0xbf3dc4: branchIfSmi(r0, 0xbf3dd0)
    //     0xbf3dc4: tbz             w0, #0, #0xbf3dd0
    // 0xbf3dc8: r1 = LoadClassIdInstr(r0)
    //     0xbf3dc8: ldur            x1, [x0, #-1]
    //     0xbf3dcc: ubfx            x1, x1, #0xc, #0x14
    // 0xbf3dd0: str             x0, [SP]
    // 0xbf3dd4: mov             x0, x1
    // 0xbf3dd8: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3dd8: movz            x17, #0x64af
    //     0xbf3ddc: add             lr, x0, x17
    //     0xbf3de0: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3de4: blr             lr
    // 0xbf3de8: mov             x1, x0
    // 0xbf3dec: ldr             x0, [fp, #0x10]
    // 0xbf3df0: stur            x1, [fp, #-8]
    // 0xbf3df4: LoadField: r2 = r0->field_f
    //     0xbf3df4: ldur            w2, [x0, #0xf]
    // 0xbf3df8: DecompressPointer r2
    //     0xbf3df8: add             x2, x2, HEAP, lsl #32
    // 0xbf3dfc: r3 = LoadClassIdInstr(r2)
    //     0xbf3dfc: ldur            x3, [x2, #-1]
    //     0xbf3e00: ubfx            x3, x3, #0xc, #0x14
    // 0xbf3e04: cmp             x3, #0x182
    // 0xbf3e08: b.ne            #0xbf3e24
    // 0xbf3e0c: r16 = _$Unknown
    //     0xbf3e0c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e98] Type: _$Unknown
    //     0xbf3e10: ldr             x16, [x16, #0xe98]
    // 0xbf3e14: str             x16, [SP]
    // 0xbf3e18: r0 = hashCode()
    //     0xbf3e18: bl              #0xbf4e5c  ; [dart:core] _AbstractType::hashCode
    // 0xbf3e1c: r1 = LoadInt32Instr(r0)
    //     0xbf3e1c: sbfx            x1, x0, #1, #0x1f
    // 0xbf3e20: b               #0xbf3ef0
    // 0xbf3e24: cmp             x3, #0x184
    // 0xbf3e28: b.ne            #0xbf3e78
    // 0xbf3e2c: LoadField: r0 = r2->field_7
    //     0xbf3e2c: ldur            w0, [x2, #7]
    // 0xbf3e30: DecompressPointer r0
    //     0xbf3e30: add             x0, x0, HEAP, lsl #32
    // 0xbf3e34: mov             x2, x0
    // 0xbf3e38: r1 = Instance_DeepCollectionEquality
    //     0xbf3e38: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c48] Obj!DeepCollectionEquality@e25cf1
    //     0xbf3e3c: ldr             x1, [x1, #0xc48]
    // 0xbf3e40: r0 = hash()
    //     0xbf3e40: bl              #0xd3c340  ; [package:collection/src/equality.dart] DeepCollectionEquality::hash
    // 0xbf3e44: mov             x2, x0
    // 0xbf3e48: r0 = BoxInt64Instr(r2)
    //     0xbf3e48: sbfiz           x0, x2, #1, #0x1f
    //     0xbf3e4c: cmp             x2, x0, asr #1
    //     0xbf3e50: b.eq            #0xbf3e5c
    //     0xbf3e54: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3e58: stur            x2, [x0, #7]
    // 0xbf3e5c: mov             x2, x0
    // 0xbf3e60: r1 = _$Mahjub
    //     0xbf3e60: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c50] Type: _$Mahjub
    //     0xbf3e64: ldr             x1, [x1, #0xc50]
    // 0xbf3e68: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf3e68: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf3e6c: r0 = hash()
    //     0xbf3e6c: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf3e70: mov             x1, x0
    // 0xbf3e74: b               #0xbf3ef0
    // 0xbf3e78: cmp             x3, #0x186
    // 0xbf3e7c: b.ne            #0xbf3eb4
    // 0xbf3e80: LoadField: r3 = r2->field_7
    //     0xbf3e80: ldur            x3, [x2, #7]
    // 0xbf3e84: r0 = BoxInt64Instr(r3)
    //     0xbf3e84: sbfiz           x0, x3, #1, #0x1f
    //     0xbf3e88: cmp             x3, x0, asr #1
    //     0xbf3e8c: b.eq            #0xbf3e98
    //     0xbf3e90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3e94: stur            x3, [x0, #7]
    // 0xbf3e98: mov             x2, x0
    // 0xbf3e9c: r1 = _$Ashobah
    //     0xbf3e9c: add             x1, PP, #0x31, lsl #12  ; [pp+0x31ea0] Type: _$Ashobah
    //     0xbf3ea0: ldr             x1, [x1, #0xea0]
    // 0xbf3ea4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf3ea4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf3ea8: r0 = hash()
    //     0xbf3ea8: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf3eac: mov             x1, x0
    // 0xbf3eb0: b               #0xbf3ef0
    // 0xbf3eb4: LoadField: r3 = r2->field_7
    //     0xbf3eb4: ldur            w3, [x2, #7]
    // 0xbf3eb8: DecompressPointer r3
    //     0xbf3eb8: add             x3, x3, HEAP, lsl #32
    // 0xbf3ebc: LoadField: r4 = r2->field_b
    //     0xbf3ebc: ldur            x4, [x2, #0xb]
    // 0xbf3ec0: r0 = BoxInt64Instr(r4)
    //     0xbf3ec0: sbfiz           x0, x4, #1, #0x1f
    //     0xbf3ec4: cmp             x4, x0, asr #1
    //     0xbf3ec8: b.eq            #0xbf3ed4
    //     0xbf3ecc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3ed0: stur            x4, [x0, #7]
    // 0xbf3ed4: str             x0, [SP]
    // 0xbf3ed8: mov             x2, x3
    // 0xbf3edc: r1 = _$Furudh
    //     0xbf3edc: add             x1, PP, #0x31, lsl #12  ; [pp+0x31ea8] Type: _$Furudh
    //     0xbf3ee0: ldr             x1, [x1, #0xea8]
    // 0xbf3ee4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbf3ee4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbf3ee8: r0 = hash()
    //     0xbf3ee8: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf3eec: mov             x1, x0
    // 0xbf3ef0: ldr             x2, [fp, #0x10]
    // 0xbf3ef4: ldur            x0, [fp, #-8]
    // 0xbf3ef8: r3 = LoadInt32Instr(r0)
    //     0xbf3ef8: sbfx            x3, x0, #1, #0x1f
    //     0xbf3efc: tbz             w0, #0, #0xbf3f04
    //     0xbf3f00: ldur            x3, [x0, #7]
    // 0xbf3f04: eor             x4, x3, x1
    // 0xbf3f08: stur            x4, [fp, #-0x10]
    // 0xbf3f0c: LoadField: r3 = r2->field_13
    //     0xbf3f0c: ldur            x3, [x2, #0x13]
    // 0xbf3f10: r0 = BoxInt64Instr(r3)
    //     0xbf3f10: sbfiz           x0, x3, #1, #0x1f
    //     0xbf3f14: cmp             x3, x0, asr #1
    //     0xbf3f18: b.eq            #0xbf3f24
    //     0xbf3f1c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3f20: stur            x3, [x0, #7]
    // 0xbf3f24: r1 = 60
    //     0xbf3f24: movz            x1, #0x3c
    // 0xbf3f28: branchIfSmi(r0, 0xbf3f34)
    //     0xbf3f28: tbz             w0, #0, #0xbf3f34
    // 0xbf3f2c: r1 = LoadClassIdInstr(r0)
    //     0xbf3f2c: ldur            x1, [x0, #-1]
    //     0xbf3f30: ubfx            x1, x1, #0xc, #0x14
    // 0xbf3f34: str             x0, [SP]
    // 0xbf3f38: mov             x0, x1
    // 0xbf3f3c: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3f3c: movz            x17, #0x64af
    //     0xbf3f40: add             lr, x0, x17
    //     0xbf3f44: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3f48: blr             lr
    // 0xbf3f4c: r1 = LoadInt32Instr(r0)
    //     0xbf3f4c: sbfx            x1, x0, #1, #0x1f
    //     0xbf3f50: tbz             w0, #0, #0xbf3f58
    //     0xbf3f54: ldur            x1, [x0, #7]
    // 0xbf3f58: ldur            x0, [fp, #-0x10]
    // 0xbf3f5c: eor             x2, x0, x1
    // 0xbf3f60: ldr             x0, [fp, #0x10]
    // 0xbf3f64: stur            x2, [fp, #-0x18]
    // 0xbf3f68: LoadField: r1 = r0->field_1b
    //     0xbf3f68: ldur            w1, [x0, #0x1b]
    // 0xbf3f6c: DecompressPointer r1
    //     0xbf3f6c: add             x1, x1, HEAP, lsl #32
    // 0xbf3f70: r0 = 60
    //     0xbf3f70: movz            x0, #0x3c
    // 0xbf3f74: branchIfSmi(r1, 0xbf3f80)
    //     0xbf3f74: tbz             w1, #0, #0xbf3f80
    // 0xbf3f78: r0 = LoadClassIdInstr(r1)
    //     0xbf3f78: ldur            x0, [x1, #-1]
    //     0xbf3f7c: ubfx            x0, x0, #0xc, #0x14
    // 0xbf3f80: str             x1, [SP]
    // 0xbf3f84: r0 = GDT[cid_x0 + 0x64af]()
    //     0xbf3f84: movz            x17, #0x64af
    //     0xbf3f88: add             lr, x0, x17
    //     0xbf3f8c: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3f90: blr             lr
    // 0xbf3f94: r2 = LoadInt32Instr(r0)
    //     0xbf3f94: sbfx            x2, x0, #1, #0x1f
    //     0xbf3f98: tbz             w0, #0, #0xbf3fa0
    //     0xbf3f9c: ldur            x2, [x0, #7]
    // 0xbf3fa0: ldur            x3, [fp, #-0x18]
    // 0xbf3fa4: eor             x4, x3, x2
    // 0xbf3fa8: r0 = BoxInt64Instr(r4)
    //     0xbf3fa8: sbfiz           x0, x4, #1, #0x1f
    //     0xbf3fac: cmp             x4, x0, asr #1
    //     0xbf3fb0: b.eq            #0xbf3fbc
    //     0xbf3fb4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf3fb8: stur            x4, [x0, #7]
    // 0xbf3fbc: LeaveFrame
    //     0xbf3fbc: mov             SP, fp
    //     0xbf3fc0: ldp             fp, lr, [SP], #0x10
    // 0xbf3fc4: ret
    //     0xbf3fc4: ret             
    // 0xbf3fc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3fc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3fcc: b               #0xbf3da4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7efa4, size: 0x2c4
    // 0xd7efa4: EnterFrame
    //     0xd7efa4: stp             fp, lr, [SP, #-0x10]!
    //     0xd7efa8: mov             fp, SP
    // 0xd7efac: AllocStack(0x30)
    //     0xd7efac: sub             SP, SP, #0x30
    // 0xd7efb0: CheckStackOverflow
    //     0xd7efb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7efb4: cmp             SP, x16
    //     0xd7efb8: b.ls            #0xd7f260
    // 0xd7efbc: ldr             x0, [fp, #0x10]
    // 0xd7efc0: cmp             w0, NULL
    // 0xd7efc4: b.ne            #0xd7efd8
    // 0xd7efc8: r0 = false
    //     0xd7efc8: add             x0, NULL, #0x30  ; false
    // 0xd7efcc: LeaveFrame
    //     0xd7efcc: mov             SP, fp
    //     0xd7efd0: ldp             fp, lr, [SP], #0x10
    // 0xd7efd4: ret
    //     0xd7efd4: ret             
    // 0xd7efd8: ldr             x1, [fp, #0x18]
    // 0xd7efdc: cmp             w1, w0
    // 0xd7efe0: b.ne            #0xd7eff4
    // 0xd7efe4: r0 = true
    //     0xd7efe4: add             x0, NULL, #0x20  ; true
    // 0xd7efe8: LeaveFrame
    //     0xd7efe8: mov             SP, fp
    //     0xd7efec: ldp             fp, lr, [SP], #0x10
    // 0xd7eff0: ret
    //     0xd7eff0: ret             
    // 0xd7eff4: r2 = 60
    //     0xd7eff4: movz            x2, #0x3c
    // 0xd7eff8: branchIfSmi(r0, 0xd7f004)
    //     0xd7eff8: tbz             w0, #0, #0xd7f004
    // 0xd7effc: r2 = LoadClassIdInstr(r0)
    //     0xd7effc: ldur            x2, [x0, #-1]
    //     0xd7f000: ubfx            x2, x2, #0xc, #0x14
    // 0xd7f004: cmp             x2, #0x18a
    // 0xd7f008: b.ne            #0xd7f250
    // 0xd7f00c: LoadField: r2 = r0->field_7
    //     0xd7f00c: ldur            x2, [x0, #7]
    // 0xd7f010: LoadField: r3 = r1->field_7
    //     0xd7f010: ldur            x3, [x1, #7]
    // 0xd7f014: cmp             x2, x3
    // 0xd7f018: b.ne            #0xd7f250
    // 0xd7f01c: LoadField: r2 = r0->field_f
    //     0xd7f01c: ldur            w2, [x0, #0xf]
    // 0xd7f020: DecompressPointer r2
    //     0xd7f020: add             x2, x2, HEAP, lsl #32
    // 0xd7f024: stur            x2, [fp, #-0x10]
    // 0xd7f028: LoadField: r3 = r1->field_f
    //     0xd7f028: ldur            w3, [x1, #0xf]
    // 0xd7f02c: DecompressPointer r3
    //     0xd7f02c: add             x3, x3, HEAP, lsl #32
    // 0xd7f030: stur            x3, [fp, #-8]
    // 0xd7f034: r4 = LoadClassIdInstr(r2)
    //     0xd7f034: ldur            x4, [x2, #-1]
    //     0xd7f038: ubfx            x4, x4, #0xc, #0x14
    // 0xd7f03c: cmp             x4, #0x182
    // 0xd7f040: b.ne            #0xd7f094
    // 0xd7f044: cmp             w2, w3
    // 0xd7f048: b.eq            #0xd7f200
    // 0xd7f04c: str             x3, [SP]
    // 0xd7f050: r0 = runtimeType()
    //     0xd7f050: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7f054: r1 = LoadClassIdInstr(r0)
    //     0xd7f054: ldur            x1, [x0, #-1]
    //     0xd7f058: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f05c: r16 = _$Unknown
    //     0xd7f05c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e98] Type: _$Unknown
    //     0xd7f060: ldr             x16, [x16, #0xe98]
    // 0xd7f064: stp             x16, x0, [SP]
    // 0xd7f068: mov             x0, x1
    // 0xd7f06c: mov             lr, x0
    // 0xd7f070: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f074: blr             lr
    // 0xd7f078: tbnz            w0, #4, #0xd7f250
    // 0xd7f07c: ldur            x0, [fp, #-8]
    // 0xd7f080: r1 = LoadClassIdInstr(r0)
    //     0xd7f080: ldur            x1, [x0, #-1]
    //     0xd7f084: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f088: cmp             x1, #0x182
    // 0xd7f08c: b.ne            #0xd7f250
    // 0xd7f090: b               #0xd7f200
    // 0xd7f094: mov             x0, x3
    // 0xd7f098: cmp             x4, #0x184
    // 0xd7f09c: b.ne            #0xd7f114
    // 0xd7f0a0: cmp             w2, w0
    // 0xd7f0a4: b.eq            #0xd7f200
    // 0xd7f0a8: str             x0, [SP]
    // 0xd7f0ac: r0 = runtimeType()
    //     0xd7f0ac: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7f0b0: r1 = LoadClassIdInstr(r0)
    //     0xd7f0b0: ldur            x1, [x0, #-1]
    //     0xd7f0b4: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f0b8: r16 = _$Mahjub
    //     0xd7f0b8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c50] Type: _$Mahjub
    //     0xd7f0bc: ldr             x16, [x16, #0xc50]
    // 0xd7f0c0: stp             x16, x0, [SP]
    // 0xd7f0c4: mov             x0, x1
    // 0xd7f0c8: mov             lr, x0
    // 0xd7f0cc: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f0d0: blr             lr
    // 0xd7f0d4: tbnz            w0, #4, #0xd7f250
    // 0xd7f0d8: ldur            x0, [fp, #-8]
    // 0xd7f0dc: r1 = LoadClassIdInstr(r0)
    //     0xd7f0dc: ldur            x1, [x0, #-1]
    //     0xd7f0e0: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f0e4: cmp             x1, #0x184
    // 0xd7f0e8: b.ne            #0xd7f250
    // 0xd7f0ec: ldur            x1, [fp, #-0x10]
    // 0xd7f0f0: LoadField: r2 = r0->field_7
    //     0xd7f0f0: ldur            w2, [x0, #7]
    // 0xd7f0f4: DecompressPointer r2
    //     0xd7f0f4: add             x2, x2, HEAP, lsl #32
    // 0xd7f0f8: LoadField: r3 = r1->field_7
    //     0xd7f0f8: ldur            w3, [x1, #7]
    // 0xd7f0fc: DecompressPointer r3
    //     0xd7f0fc: add             x3, x3, HEAP, lsl #32
    // 0xd7f100: r1 = Instance_DeepCollectionEquality
    //     0xd7f100: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c48] Obj!DeepCollectionEquality@e25cf1
    //     0xd7f104: ldr             x1, [x1, #0xc48]
    // 0xd7f108: r0 = equals()
    //     0xd7f108: bl              #0xd2f358  ; [package:collection/src/equality.dart] DeepCollectionEquality::equals
    // 0xd7f10c: tbnz            w0, #4, #0xd7f250
    // 0xd7f110: b               #0xd7f200
    // 0xd7f114: mov             x1, x2
    // 0xd7f118: cmp             x4, #0x186
    // 0xd7f11c: b.ne            #0xd7f1e0
    // 0xd7f120: cmp             w1, w0
    // 0xd7f124: b.eq            #0xd7f200
    // 0xd7f128: str             x0, [SP]
    // 0xd7f12c: r0 = runtimeType()
    //     0xd7f12c: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7f130: r1 = LoadClassIdInstr(r0)
    //     0xd7f130: ldur            x1, [x0, #-1]
    //     0xd7f134: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f138: r16 = _$Ashobah
    //     0xd7f138: add             x16, PP, #0x31, lsl #12  ; [pp+0x31ea0] Type: _$Ashobah
    //     0xd7f13c: ldr             x16, [x16, #0xea0]
    // 0xd7f140: stp             x16, x0, [SP]
    // 0xd7f144: mov             x0, x1
    // 0xd7f148: mov             lr, x0
    // 0xd7f14c: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f150: blr             lr
    // 0xd7f154: tbnz            w0, #4, #0xd7f250
    // 0xd7f158: ldur            x0, [fp, #-8]
    // 0xd7f15c: r1 = LoadClassIdInstr(r0)
    //     0xd7f15c: ldur            x1, [x0, #-1]
    //     0xd7f160: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f164: cmp             x1, #0x186
    // 0xd7f168: b.ne            #0xd7f250
    // 0xd7f16c: ldur            x1, [fp, #-0x10]
    // 0xd7f170: LoadField: r2 = r0->field_7
    //     0xd7f170: ldur            x2, [x0, #7]
    // 0xd7f174: stur            x2, [fp, #-0x20]
    // 0xd7f178: LoadField: r3 = r1->field_7
    //     0xd7f178: ldur            x3, [x1, #7]
    // 0xd7f17c: stur            x3, [fp, #-0x18]
    // 0xd7f180: r0 = BoxInt64Instr(r2)
    //     0xd7f180: sbfiz           x0, x2, #1, #0x1f
    //     0xd7f184: cmp             x2, x0, asr #1
    //     0xd7f188: b.eq            #0xd7f194
    //     0xd7f18c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd7f190: stur            x2, [x0, #7]
    // 0xd7f194: mov             x4, x0
    // 0xd7f198: r0 = BoxInt64Instr(r3)
    //     0xd7f198: sbfiz           x0, x3, #1, #0x1f
    //     0xd7f19c: cmp             x3, x0, asr #1
    //     0xd7f1a0: b.eq            #0xd7f1ac
    //     0xd7f1a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd7f1a8: stur            x3, [x0, #7]
    // 0xd7f1ac: mov             x1, x0
    // 0xd7f1b0: mov             x0, x4
    // 0xd7f1b4: stp             x1, x0, [SP, #-0x10]!
    // 0xd7f1b8: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd7f1b8: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd7f1bc: LoadField: r30 = r30->field_7
    //     0xd7f1bc: ldur            lr, [lr, #7]
    // 0xd7f1c0: blr             lr
    // 0xd7f1c4: ldp             x1, x0, [SP], #0x10
    // 0xd7f1c8: b.eq            #0xd7f200
    // 0xd7f1cc: ldur            x0, [fp, #-0x20]
    // 0xd7f1d0: ldur            x1, [fp, #-0x18]
    // 0xd7f1d4: cmp             x0, x1
    // 0xd7f1d8: b.ne            #0xd7f250
    // 0xd7f1dc: b               #0xd7f200
    // 0xd7f1e0: r2 = LoadClassIdInstr(r1)
    //     0xd7f1e0: ldur            x2, [x1, #-1]
    //     0xd7f1e4: ubfx            x2, x2, #0xc, #0x14
    // 0xd7f1e8: stp             x0, x1, [SP]
    // 0xd7f1ec: mov             x0, x2
    // 0xd7f1f0: mov             lr, x0
    // 0xd7f1f4: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f1f8: blr             lr
    // 0xd7f1fc: tbnz            w0, #4, #0xd7f250
    // 0xd7f200: ldr             x1, [fp, #0x18]
    // 0xd7f204: ldr             x0, [fp, #0x10]
    // 0xd7f208: LoadField: r2 = r0->field_13
    //     0xd7f208: ldur            x2, [x0, #0x13]
    // 0xd7f20c: LoadField: r3 = r1->field_13
    //     0xd7f20c: ldur            x3, [x1, #0x13]
    // 0xd7f210: cmp             x2, x3
    // 0xd7f214: b.ne            #0xd7f250
    // 0xd7f218: LoadField: r2 = r0->field_1b
    //     0xd7f218: ldur            w2, [x0, #0x1b]
    // 0xd7f21c: DecompressPointer r2
    //     0xd7f21c: add             x2, x2, HEAP, lsl #32
    // 0xd7f220: LoadField: r0 = r1->field_1b
    //     0xd7f220: ldur            w0, [x1, #0x1b]
    // 0xd7f224: DecompressPointer r0
    //     0xd7f224: add             x0, x0, HEAP, lsl #32
    // 0xd7f228: r1 = 60
    //     0xd7f228: movz            x1, #0x3c
    // 0xd7f22c: branchIfSmi(r2, 0xd7f238)
    //     0xd7f22c: tbz             w2, #0, #0xd7f238
    // 0xd7f230: r1 = LoadClassIdInstr(r2)
    //     0xd7f230: ldur            x1, [x2, #-1]
    //     0xd7f234: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f238: stp             x0, x2, [SP]
    // 0xd7f23c: mov             x0, x1
    // 0xd7f240: mov             lr, x0
    // 0xd7f244: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f248: blr             lr
    // 0xd7f24c: b               #0xd7f254
    // 0xd7f250: r0 = false
    //     0xd7f250: add             x0, NULL, #0x30  ; false
    // 0xd7f254: LeaveFrame
    //     0xd7f254: mov             SP, fp
    //     0xd7f258: ldp             fp, lr, [SP], #0x10
    // 0xd7f25c: ret
    //     0xd7f25c: ret             
    // 0xd7f260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7f260: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7f264: b               #0xd7efbc
  }
}
