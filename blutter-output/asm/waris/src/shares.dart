// lib: , url: package:waris/src/shares.dart

// class id: 1051240, size: 0x8
class :: {

  static _ ShareConditionExt.sharedWith(/* No info */) {
    // ** addr: 0x90b8dc, size: 0x40
    // 0x90b8dc: EnterFrame
    //     0x90b8dc: stp             fp, lr, [SP, #-0x10]!
    //     0x90b8e0: mov             fp, SP
    // 0x90b8e4: AllocStack(0x10)
    //     0x90b8e4: sub             SP, SP, #0x10
    // 0x90b8e8: SetupParameters(dynamic _ /* r1 => r0 */)
    //     0x90b8e8: mov             x0, x1
    // 0x90b8ec: CheckStackOverflow
    //     0x90b8ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b8f0: cmp             SP, x16
    //     0x90b8f4: b.ls            #0x90b914
    // 0x90b8f8: stp             x2, x0, [SP]
    // 0x90b8fc: ClosureCall
    //     0x90b8fc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x90b900: ldur            x2, [x0, #0x1f]
    //     0x90b904: blr             x2
    // 0x90b908: LeaveFrame
    //     0x90b908: mov             SP, fp
    //     0x90b90c: ldp             fp, lr, [SP], #0x10
    // 0x90b910: ret
    //     0x90b910: ret             
    // 0x90b914: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b914: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b918: b               #0x90b8f8
  }
  static _ isQualified(/* No info */) {
    // ** addr: 0xbb1094, size: 0xb0
    // 0xbb1094: EnterFrame
    //     0xbb1094: stp             fp, lr, [SP, #-0x10]!
    //     0xbb1098: mov             fp, SP
    // 0xbb109c: AllocStack(0x30)
    //     0xbb109c: sub             SP, SP, #0x30
    // 0xbb10a0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xbb10a0: stur            x2, [fp, #-8]
    // 0xbb10a4: CheckStackOverflow
    //     0xbb10a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb10a8: cmp             SP, x16
    //     0xbb10ac: b.ls            #0xbb113c
    // 0xbb10b0: r0 = of()
    //     0xbb10b0: bl              #0x90b91c  ; [package:waris/src/shares.dart] Shares::of
    // 0xbb10b4: ldur            x16, [fp, #-8]
    // 0xbb10b8: stp             x16, x0, [SP]
    // 0xbb10bc: ClosureCall
    //     0xbb10bc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xbb10c0: ldur            x2, [x0, #0x1f]
    //     0xbb10c4: blr             x2
    // 0xbb10c8: r1 = Function '<anonymous closure>': static.
    //     0xbb10c8: add             x1, PP, #0x31, lsl #12  ; [pp+0x31808] Function: [dart:core] Object::_simpleInstanceOfTrue (0xebbd8c)
    //     0xbb10cc: ldr             x1, [x1, #0x808]
    // 0xbb10d0: r2 = Null
    //     0xbb10d0: mov             x2, NULL
    // 0xbb10d4: stur            x0, [fp, #-8]
    // 0xbb10d8: r0 = AllocateClosure()
    //     0xbb10d8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb10dc: r1 = Function '<anonymous closure>': static.
    //     0xbb10dc: add             x1, PP, #0x31, lsl #12  ; [pp+0x31810] Function: [dart:core] Object::_simpleInstanceOfFalse (0xebd578)
    //     0xbb10e0: ldr             x1, [x1, #0x810]
    // 0xbb10e4: r2 = Null
    //     0xbb10e4: mov             x2, NULL
    // 0xbb10e8: stur            x0, [fp, #-0x10]
    // 0xbb10ec: r0 = AllocateClosure()
    //     0xbb10ec: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb10f0: mov             x1, x0
    // 0xbb10f4: ldur            x0, [fp, #-8]
    // 0xbb10f8: r2 = LoadClassIdInstr(r0)
    //     0xbb10f8: ldur            x2, [x0, #-1]
    //     0xbb10fc: ubfx            x2, x2, #0xc, #0x14
    // 0xbb1100: r16 = <bool>
    //     0xbb1100: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0xbb1104: stp             x0, x16, [SP, #0x10]
    // 0xbb1108: ldur            x16, [fp, #-0x10]
    // 0xbb110c: stp             x16, x1, [SP]
    // 0xbb1110: mov             x0, x2
    // 0xbb1114: r4 = const [0x1, 0x3, 0x3, 0x2, mahjub, 0x2, null]
    //     0xbb1114: add             x4, PP, #0x31, lsl #12  ; [pp+0x31818] List(7) [0x1, 0x3, 0x3, 0x2, "mahjub", 0x2, Null]
    //     0xbb1118: ldr             x4, [x4, #0x818]
    // 0xbb111c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbb111c: sub             lr, x0, #1, lsl #12
    //     0xbb1120: ldr             lr, [x21, lr, lsl #3]
    //     0xbb1124: blr             lr
    // 0xbb1128: eor             x1, x0, #0x10
    // 0xbb112c: mov             x0, x1
    // 0xbb1130: LeaveFrame
    //     0xbb1130: mov             SP, fp
    //     0xbb1134: ldp             fp, lr, [SP], #0x10
    // 0xbb1138: ret
    //     0xbb1138: ret             
    // 0xbb113c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb113c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb1140: b               #0xbb10b0
  }
}

// class id: 378, size: 0xc, field offset: 0x8
abstract class _$$MahjubCopyWith<X0> extends Object {
}

// class id: 379, size: 0x10, field offset: 0x8
abstract class _$ShareCopyWithImpl<X0, X1 bound Share> extends Object
    implements $ShareCopyWith<X0> {
}

// class id: 380, size: 0x10, field offset: 0x10
class __$$MahjubCopyWithImpl<C2X0> extends _$ShareCopyWithImpl<C2X0, dynamic>
    implements _$$MahjubCopyWith<X0> {
}

// class id: 381, size: 0xc, field offset: 0x8
abstract class $ShareCopyWith<X0> extends Object {
}

// class id: 382, size: 0x8, field offset: 0x8
abstract class _$Share extends Object {
}

// class id: 383, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _Share&Object&_$Share extends Object
     with _$Share {
}

// class id: 384, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Share extends _Share&Object&_$Share {
}

// class id: 385, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Unknown extends Share {
}

// class id: 386, size: 0x8, field offset: 0x8
//   const constructor, 
class _$Unknown extends Unknown {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf4140, size: 0x3c
    // 0xbf4140: EnterFrame
    //     0xbf4140: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4144: mov             fp, SP
    // 0xbf4148: AllocStack(0x8)
    //     0xbf4148: sub             SP, SP, #8
    // 0xbf414c: CheckStackOverflow
    //     0xbf414c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4150: cmp             SP, x16
    //     0xbf4154: b.ls            #0xbf4174
    // 0xbf4158: r16 = _$Unknown
    //     0xbf4158: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e98] Type: _$Unknown
    //     0xbf415c: ldr             x16, [x16, #0xe98]
    // 0xbf4160: str             x16, [SP]
    // 0xbf4164: r0 = hashCode()
    //     0xbf4164: bl              #0xbf4e5c  ; [dart:core] _AbstractType::hashCode
    // 0xbf4168: LeaveFrame
    //     0xbf4168: mov             SP, fp
    //     0xbf416c: ldp             fp, lr, [SP], #0x10
    // 0xbf4170: ret
    //     0xbf4170: ret             
    // 0xbf4174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4174: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4178: b               #0xbf4158
  }
  _ toString(/* No info */) {
    // ** addr: 0xc43370, size: 0xc
    // 0xc43370: r0 = "Share.unknown()"
    //     0xc43370: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c4d8] "Share.unknown()"
    //     0xc43374: ldr             x0, [x0, #0x4d8]
    // 0xc43378: ret
    //     0xc43378: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7f65c, size: 0xbc
    // 0xd7f65c: EnterFrame
    //     0xd7f65c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7f660: mov             fp, SP
    // 0xd7f664: AllocStack(0x10)
    //     0xd7f664: sub             SP, SP, #0x10
    // 0xd7f668: CheckStackOverflow
    //     0xd7f668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7f66c: cmp             SP, x16
    //     0xd7f670: b.ls            #0xd7f710
    // 0xd7f674: ldr             x0, [fp, #0x10]
    // 0xd7f678: cmp             w0, NULL
    // 0xd7f67c: b.ne            #0xd7f690
    // 0xd7f680: r0 = false
    //     0xd7f680: add             x0, NULL, #0x30  ; false
    // 0xd7f684: LeaveFrame
    //     0xd7f684: mov             SP, fp
    //     0xd7f688: ldp             fp, lr, [SP], #0x10
    // 0xd7f68c: ret
    //     0xd7f68c: ret             
    // 0xd7f690: ldr             x1, [fp, #0x18]
    // 0xd7f694: cmp             w1, w0
    // 0xd7f698: b.ne            #0xd7f6a4
    // 0xd7f69c: r0 = true
    //     0xd7f69c: add             x0, NULL, #0x20  ; true
    // 0xd7f6a0: b               #0xd7f704
    // 0xd7f6a4: str             x0, [SP]
    // 0xd7f6a8: r0 = runtimeType()
    //     0xd7f6a8: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7f6ac: r1 = LoadClassIdInstr(r0)
    //     0xd7f6ac: ldur            x1, [x0, #-1]
    //     0xd7f6b0: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f6b4: r16 = _$Unknown
    //     0xd7f6b4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e98] Type: _$Unknown
    //     0xd7f6b8: ldr             x16, [x16, #0xe98]
    // 0xd7f6bc: stp             x16, x0, [SP]
    // 0xd7f6c0: mov             x0, x1
    // 0xd7f6c4: mov             lr, x0
    // 0xd7f6c8: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f6cc: blr             lr
    // 0xd7f6d0: tbnz            w0, #4, #0xd7f700
    // 0xd7f6d4: ldr             x1, [fp, #0x10]
    // 0xd7f6d8: r2 = 60
    //     0xd7f6d8: movz            x2, #0x3c
    // 0xd7f6dc: branchIfSmi(r1, 0xd7f6e8)
    //     0xd7f6dc: tbz             w1, #0, #0xd7f6e8
    // 0xd7f6e0: r2 = LoadClassIdInstr(r1)
    //     0xd7f6e0: ldur            x2, [x1, #-1]
    //     0xd7f6e4: ubfx            x2, x2, #0xc, #0x14
    // 0xd7f6e8: cmp             x2, #0x182
    // 0xd7f6ec: r16 = true
    //     0xd7f6ec: add             x16, NULL, #0x20  ; true
    // 0xd7f6f0: r17 = false
    //     0xd7f6f0: add             x17, NULL, #0x30  ; false
    // 0xd7f6f4: csel            x1, x16, x17, eq
    // 0xd7f6f8: mov             x0, x1
    // 0xd7f6fc: b               #0xd7f704
    // 0xd7f700: r0 = false
    //     0xd7f700: add             x0, NULL, #0x30  ; false
    // 0xd7f704: LeaveFrame
    //     0xd7f704: mov             SP, fp
    //     0xd7f708: ldp             fp, lr, [SP], #0x10
    // 0xd7f70c: ret
    //     0xd7f70c: ret             
    // 0xd7f710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7f710: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7f714: b               #0xd7f674
  }
  _ maybeMap(/* No info */) {
    // ** addr: 0xeb96f4, size: 0x98
    // 0xeb96f4: EnterFrame
    //     0xeb96f4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb96f8: mov             fp, SP
    // 0xeb96fc: AllocStack(0x8)
    //     0xeb96fc: sub             SP, SP, #8
    // 0xeb9700: SetupParameters(_$Unknown this /* r0 */, {dynamic ashobah, dynamic furudh})
    //     0xeb9700: ldur            w0, [x4, #0x13]
    //     0xeb9704: sub             x1, x0, #4
    //     0xeb9708: add             x0, fp, w1, sxtw #2
    //     0xeb970c: ldr             x0, [x0, #0x10]
    //     0xeb9710: ldur            w1, [x4, #0x1f]
    //     0xeb9714: add             x1, x1, HEAP, lsl #32
    //     0xeb9718: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c30] "ashobah"
    //     0xeb971c: ldr             x16, [x16, #0xc30]
    //     0xeb9720: cmp             w1, w16
    //     0xeb9724: b.ne            #0xeb9730
    //     0xeb9728: movz            x1, #0x1
    //     0xeb972c: b               #0xeb9734
    //     0xeb9730: movz            x1, #0
    //     0xeb9734: lsl             x2, x1, #1
    //     0xeb9738: lsl             w1, w2, #1
    //     0xeb973c: add             w2, w1, #8
    //     0xeb9740: add             x16, x4, w2, sxtw #1
    //     0xeb9744: ldur            w1, [x16, #0xf]
    //     0xeb9748: add             x1, x1, HEAP, lsl #32
    //     0xeb974c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c38] "furudh"
    //     0xeb9750: ldr             x16, [x16, #0xc38]
    //     0xeb9754: cmp             w1, w16
    //     0xeb9758: b.eq            #0xeb975c
    // 0xeb975c: CheckStackOverflow
    //     0xeb975c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9760: cmp             SP, x16
    //     0xeb9764: b.ls            #0xeb9784
    // 0xeb9768: str             x0, [SP]
    // 0xeb976c: ClosureCall
    //     0xeb976c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xeb9770: ldur            x2, [x0, #0x1f]
    //     0xeb9774: blr             x2
    // 0xeb9778: LeaveFrame
    //     0xeb9778: mov             SP, fp
    //     0xeb977c: ldp             fp, lr, [SP], #0x10
    // 0xeb9780: ret
    //     0xeb9780: ret             
    // 0xeb9784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9788: b               #0xeb9768
  }
}

// class id: 387, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Mahjub extends Share {
}

// class id: 388, size: 0xc, field offset: 0x8
//   const constructor, 
class _$Mahjub extends Mahjub {

  _ImmutableList<Heir> field_8;

  get _ heirs(/* No info */) {
    // ** addr: 0xb5de14, size: 0x3c
    // 0xb5de14: EnterFrame
    //     0xb5de14: stp             fp, lr, [SP, #-0x10]!
    //     0xb5de18: mov             fp, SP
    // 0xb5de1c: AllocStack(0x8)
    //     0xb5de1c: sub             SP, SP, #8
    // 0xb5de20: LoadField: r0 = r1->field_7
    //     0xb5de20: ldur            w0, [x1, #7]
    // 0xb5de24: DecompressPointer r0
    //     0xb5de24: add             x0, x0, HEAP, lsl #32
    // 0xb5de28: stur            x0, [fp, #-8]
    // 0xb5de2c: r1 = <Heir>
    //     0xb5de2c: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0xb5de30: ldr             x1, [x1, #0xc0]
    // 0xb5de34: r0 = EqualUnmodifiableListView()
    //     0xb5de34: bl              #0xb5de50  ; AllocateEqualUnmodifiableListViewStub -> EqualUnmodifiableListView<X0> (size=0x14)
    // 0xb5de38: ldur            x1, [fp, #-8]
    // 0xb5de3c: StoreField: r0->field_f = r1
    //     0xb5de3c: stur            w1, [x0, #0xf]
    // 0xb5de40: StoreField: r0->field_b = r1
    //     0xb5de40: stur            w1, [x0, #0xb]
    // 0xb5de44: LeaveFrame
    //     0xb5de44: mov             SP, fp
    //     0xb5de48: ldp             fp, lr, [SP], #0x10
    // 0xb5de4c: ret
    //     0xb5de4c: ret             
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf40bc, size: 0x84
    // 0xbf40bc: EnterFrame
    //     0xbf40bc: stp             fp, lr, [SP, #-0x10]!
    //     0xbf40c0: mov             fp, SP
    // 0xbf40c4: CheckStackOverflow
    //     0xbf40c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf40c8: cmp             SP, x16
    //     0xbf40cc: b.ls            #0xbf4138
    // 0xbf40d0: ldr             x0, [fp, #0x10]
    // 0xbf40d4: LoadField: r2 = r0->field_7
    //     0xbf40d4: ldur            w2, [x0, #7]
    // 0xbf40d8: DecompressPointer r2
    //     0xbf40d8: add             x2, x2, HEAP, lsl #32
    // 0xbf40dc: r1 = Instance_DeepCollectionEquality
    //     0xbf40dc: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c48] Obj!DeepCollectionEquality@e25cf1
    //     0xbf40e0: ldr             x1, [x1, #0xc48]
    // 0xbf40e4: r0 = hash()
    //     0xbf40e4: bl              #0xd3c340  ; [package:collection/src/equality.dart] DeepCollectionEquality::hash
    // 0xbf40e8: mov             x2, x0
    // 0xbf40ec: r0 = BoxInt64Instr(r2)
    //     0xbf40ec: sbfiz           x0, x2, #1, #0x1f
    //     0xbf40f0: cmp             x2, x0, asr #1
    //     0xbf40f4: b.eq            #0xbf4100
    //     0xbf40f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf40fc: stur            x2, [x0, #7]
    // 0xbf4100: mov             x2, x0
    // 0xbf4104: r1 = _$Mahjub
    //     0xbf4104: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c50] Type: _$Mahjub
    //     0xbf4108: ldr             x1, [x1, #0xc50]
    // 0xbf410c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf410c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf4110: r0 = hash()
    //     0xbf4110: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4114: mov             x2, x0
    // 0xbf4118: r0 = BoxInt64Instr(r2)
    //     0xbf4118: sbfiz           x0, x2, #1, #0x1f
    //     0xbf411c: cmp             x2, x0, asr #1
    //     0xbf4120: b.eq            #0xbf412c
    //     0xbf4124: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4128: stur            x2, [x0, #7]
    // 0xbf412c: LeaveFrame
    //     0xbf412c: mov             SP, fp
    //     0xbf4130: ldp             fp, lr, [SP], #0x10
    // 0xbf4134: ret
    //     0xbf4134: ret             
    // 0xbf4138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf413c: b               #0xbf40d0
  }
  _ toString(/* No info */) {
    // ** addr: 0xc432e4, size: 0x8c
    // 0xc432e4: EnterFrame
    //     0xc432e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc432e8: mov             fp, SP
    // 0xc432ec: AllocStack(0x18)
    //     0xc432ec: sub             SP, SP, #0x18
    // 0xc432f0: CheckStackOverflow
    //     0xc432f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc432f4: cmp             SP, x16
    //     0xc432f8: b.ls            #0xc43368
    // 0xc432fc: r1 = Null
    //     0xc432fc: mov             x1, NULL
    // 0xc43300: r2 = 6
    //     0xc43300: movz            x2, #0x6
    // 0xc43304: r0 = AllocateArray()
    //     0xc43304: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc43308: stur            x0, [fp, #-0x10]
    // 0xc4330c: r16 = "Share.mahjub(heirs: "
    //     0xc4330c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e90] "Share.mahjub(heirs: "
    //     0xc43310: ldr             x16, [x16, #0xe90]
    // 0xc43314: StoreField: r0->field_f = r16
    //     0xc43314: stur            w16, [x0, #0xf]
    // 0xc43318: ldr             x1, [fp, #0x10]
    // 0xc4331c: LoadField: r2 = r1->field_7
    //     0xc4331c: ldur            w2, [x1, #7]
    // 0xc43320: DecompressPointer r2
    //     0xc43320: add             x2, x2, HEAP, lsl #32
    // 0xc43324: stur            x2, [fp, #-8]
    // 0xc43328: r1 = <Heir>
    //     0xc43328: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0xc4332c: ldr             x1, [x1, #0xc0]
    // 0xc43330: r0 = EqualUnmodifiableListView()
    //     0xc43330: bl              #0xb5de50  ; AllocateEqualUnmodifiableListViewStub -> EqualUnmodifiableListView<X0> (size=0x14)
    // 0xc43334: mov             x1, x0
    // 0xc43338: ldur            x0, [fp, #-8]
    // 0xc4333c: StoreField: r1->field_f = r0
    //     0xc4333c: stur            w0, [x1, #0xf]
    // 0xc43340: StoreField: r1->field_b = r0
    //     0xc43340: stur            w0, [x1, #0xb]
    // 0xc43344: ldur            x0, [fp, #-0x10]
    // 0xc43348: StoreField: r0->field_13 = r1
    //     0xc43348: stur            w1, [x0, #0x13]
    // 0xc4334c: r16 = ")"
    //     0xc4334c: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc43350: ArrayStore: r0[0] = r16  ; List_4
    //     0xc43350: stur            w16, [x0, #0x17]
    // 0xc43354: str             x0, [SP]
    // 0xc43358: r0 = _interpolate()
    //     0xc43358: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4335c: LeaveFrame
    //     0xc4335c: mov             SP, fp
    //     0xc43360: ldp             fp, lr, [SP], #0x10
    // 0xc43364: ret
    //     0xc43364: ret             
    // 0xc43368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43368: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4336c: b               #0xc432fc
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7f58c, size: 0xd0
    // 0xd7f58c: EnterFrame
    //     0xd7f58c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7f590: mov             fp, SP
    // 0xd7f594: AllocStack(0x10)
    //     0xd7f594: sub             SP, SP, #0x10
    // 0xd7f598: CheckStackOverflow
    //     0xd7f598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7f59c: cmp             SP, x16
    //     0xd7f5a0: b.ls            #0xd7f654
    // 0xd7f5a4: ldr             x0, [fp, #0x10]
    // 0xd7f5a8: cmp             w0, NULL
    // 0xd7f5ac: b.ne            #0xd7f5c0
    // 0xd7f5b0: r0 = false
    //     0xd7f5b0: add             x0, NULL, #0x30  ; false
    // 0xd7f5b4: LeaveFrame
    //     0xd7f5b4: mov             SP, fp
    //     0xd7f5b8: ldp             fp, lr, [SP], #0x10
    // 0xd7f5bc: ret
    //     0xd7f5bc: ret             
    // 0xd7f5c0: ldr             x1, [fp, #0x18]
    // 0xd7f5c4: cmp             w1, w0
    // 0xd7f5c8: b.ne            #0xd7f5d4
    // 0xd7f5cc: r0 = true
    //     0xd7f5cc: add             x0, NULL, #0x20  ; true
    // 0xd7f5d0: b               #0xd7f648
    // 0xd7f5d4: str             x0, [SP]
    // 0xd7f5d8: r0 = runtimeType()
    //     0xd7f5d8: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7f5dc: r1 = LoadClassIdInstr(r0)
    //     0xd7f5dc: ldur            x1, [x0, #-1]
    //     0xd7f5e0: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f5e4: r16 = _$Mahjub
    //     0xd7f5e4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c50] Type: _$Mahjub
    //     0xd7f5e8: ldr             x16, [x16, #0xc50]
    // 0xd7f5ec: stp             x16, x0, [SP]
    // 0xd7f5f0: mov             x0, x1
    // 0xd7f5f4: mov             lr, x0
    // 0xd7f5f8: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f5fc: blr             lr
    // 0xd7f600: tbnz            w0, #4, #0xd7f644
    // 0xd7f604: ldr             x0, [fp, #0x10]
    // 0xd7f608: r1 = 60
    //     0xd7f608: movz            x1, #0x3c
    // 0xd7f60c: branchIfSmi(r0, 0xd7f618)
    //     0xd7f60c: tbz             w0, #0, #0xd7f618
    // 0xd7f610: r1 = LoadClassIdInstr(r0)
    //     0xd7f610: ldur            x1, [x0, #-1]
    //     0xd7f614: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f618: cmp             x1, #0x184
    // 0xd7f61c: b.ne            #0xd7f644
    // 0xd7f620: ldr             x1, [fp, #0x18]
    // 0xd7f624: LoadField: r2 = r0->field_7
    //     0xd7f624: ldur            w2, [x0, #7]
    // 0xd7f628: DecompressPointer r2
    //     0xd7f628: add             x2, x2, HEAP, lsl #32
    // 0xd7f62c: LoadField: r3 = r1->field_7
    //     0xd7f62c: ldur            w3, [x1, #7]
    // 0xd7f630: DecompressPointer r3
    //     0xd7f630: add             x3, x3, HEAP, lsl #32
    // 0xd7f634: r1 = Instance_DeepCollectionEquality
    //     0xd7f634: add             x1, PP, #0x31, lsl #12  ; [pp+0x31c48] Obj!DeepCollectionEquality@e25cf1
    //     0xd7f638: ldr             x1, [x1, #0xc48]
    // 0xd7f63c: r0 = equals()
    //     0xd7f63c: bl              #0xd2f358  ; [package:collection/src/equality.dart] DeepCollectionEquality::equals
    // 0xd7f640: b               #0xd7f648
    // 0xd7f644: r0 = false
    //     0xd7f644: add             x0, NULL, #0x30  ; false
    // 0xd7f648: LeaveFrame
    //     0xd7f648: mov             SP, fp
    //     0xd7f64c: ldp             fp, lr, [SP], #0x10
    // 0xd7f650: ret
    //     0xd7f650: ret             
    // 0xd7f654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7f654: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7f658: b               #0xd7f5a4
  }
  _ maybeMap(/* No info */) {
    // ** addr: 0xeb9460, size: 0x124
    // 0xeb9460: EnterFrame
    //     0xeb9460: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9464: mov             fp, SP
    // 0xeb9468: AllocStack(0x10)
    //     0xeb9468: sub             SP, SP, #0x10
    // 0xeb946c: SetupParameters(_$Mahjub this /* r2 */, dynamic _ /* r3 */, {dynamic ashobah, dynamic furudh, dynamic mahjub = Null /* r0 */})
    //     0xeb946c: ldur            w0, [x4, #0x13]
    //     0xeb9470: sub             x1, x0, #4
    //     0xeb9474: add             x2, fp, w1, sxtw #2
    //     0xeb9478: ldr             x2, [x2, #0x18]
    //     0xeb947c: add             x3, fp, w1, sxtw #2
    //     0xeb9480: ldr             x3, [x3, #0x10]
    //     0xeb9484: ldur            w1, [x4, #0x1f]
    //     0xeb9488: add             x1, x1, HEAP, lsl #32
    //     0xeb948c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c30] "ashobah"
    //     0xeb9490: ldr             x16, [x16, #0xc30]
    //     0xeb9494: cmp             w1, w16
    //     0xeb9498: b.ne            #0xeb94a4
    //     0xeb949c: movz            x1, #0x1
    //     0xeb94a0: b               #0xeb94a8
    //     0xeb94a4: movz            x1, #0
    //     0xeb94a8: lsl             x5, x1, #1
    //     0xeb94ac: lsl             w6, w5, #1
    //     0xeb94b0: add             w7, w6, #8
    //     0xeb94b4: add             x16, x4, w7, sxtw #1
    //     0xeb94b8: ldur            w6, [x16, #0xf]
    //     0xeb94bc: add             x6, x6, HEAP, lsl #32
    //     0xeb94c0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c38] "furudh"
    //     0xeb94c4: ldr             x16, [x16, #0xc38]
    //     0xeb94c8: cmp             w6, w16
    //     0xeb94cc: b.ne            #0xeb94dc
    //     0xeb94d0: add             w1, w5, #2
    //     0xeb94d4: sbfx            x5, x1, #1, #0x1f
    //     0xeb94d8: mov             x1, x5
    //     0xeb94dc: lsl             x5, x1, #1
    //     0xeb94e0: lsl             w1, w5, #1
    //     0xeb94e4: add             w5, w1, #8
    //     0xeb94e8: add             x16, x4, w5, sxtw #1
    //     0xeb94ec: ldur            w6, [x16, #0xf]
    //     0xeb94f0: add             x6, x6, HEAP, lsl #32
    //     0xeb94f4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c40] "mahjub"
    //     0xeb94f8: ldr             x16, [x16, #0xc40]
    //     0xeb94fc: cmp             w6, w16
    //     0xeb9500: b.ne            #0xeb9524
    //     0xeb9504: add             w5, w1, #0xa
    //     0xeb9508: add             x16, x4, w5, sxtw #1
    //     0xeb950c: ldur            w1, [x16, #0xf]
    //     0xeb9510: add             x1, x1, HEAP, lsl #32
    //     0xeb9514: sub             w4, w0, w1
    //     0xeb9518: add             x0, fp, w4, sxtw #2
    //     0xeb951c: ldr             x0, [x0, #8]
    //     0xeb9520: b               #0xeb9528
    //     0xeb9524: mov             x0, NULL
    // 0xeb9528: CheckStackOverflow
    //     0xeb9528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb952c: cmp             SP, x16
    //     0xeb9530: b.ls            #0xeb957c
    // 0xeb9534: cmp             w0, NULL
    // 0xeb9538: b.eq            #0xeb955c
    // 0xeb953c: stp             x2, x0, [SP]
    // 0xeb9540: ClosureCall
    //     0xeb9540: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeb9544: ldur            x2, [x0, #0x1f]
    //     0xeb9548: blr             x2
    // 0xeb954c: r0 = true
    //     0xeb954c: add             x0, NULL, #0x20  ; true
    // 0xeb9550: LeaveFrame
    //     0xeb9550: mov             SP, fp
    //     0xeb9554: ldp             fp, lr, [SP], #0x10
    // 0xeb9558: ret
    //     0xeb9558: ret             
    // 0xeb955c: str             x3, [SP]
    // 0xeb9560: mov             x0, x3
    // 0xeb9564: ClosureCall
    //     0xeb9564: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xeb9568: ldur            x2, [x0, #0x1f]
    //     0xeb956c: blr             x2
    // 0xeb9570: LeaveFrame
    //     0xeb9570: mov             SP, fp
    //     0xeb9574: ldp             fp, lr, [SP], #0x10
    // 0xeb9578: ret
    //     0xeb9578: ret             
    // 0xeb957c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb957c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9580: b               #0xeb9534
  }
}

// class id: 389, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Ashobah extends Share {
}

// class id: 390, size: 0x10, field offset: 0x8
//   const constructor, 
class _$Ashobah extends Ashobah {

  _Mint field_8;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf404c, size: 0x70
    // 0xbf404c: EnterFrame
    //     0xbf404c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4050: mov             fp, SP
    // 0xbf4054: CheckStackOverflow
    //     0xbf4054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4058: cmp             SP, x16
    //     0xbf405c: b.ls            #0xbf40b4
    // 0xbf4060: ldr             x0, [fp, #0x10]
    // 0xbf4064: LoadField: r2 = r0->field_7
    //     0xbf4064: ldur            x2, [x0, #7]
    // 0xbf4068: r0 = BoxInt64Instr(r2)
    //     0xbf4068: sbfiz           x0, x2, #1, #0x1f
    //     0xbf406c: cmp             x2, x0, asr #1
    //     0xbf4070: b.eq            #0xbf407c
    //     0xbf4074: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4078: stur            x2, [x0, #7]
    // 0xbf407c: mov             x2, x0
    // 0xbf4080: r1 = _$Ashobah
    //     0xbf4080: add             x1, PP, #0x31, lsl #12  ; [pp+0x31ea0] Type: _$Ashobah
    //     0xbf4084: ldr             x1, [x1, #0xea0]
    // 0xbf4088: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf4088: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf408c: r0 = hash()
    //     0xbf408c: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4090: mov             x2, x0
    // 0xbf4094: r0 = BoxInt64Instr(r2)
    //     0xbf4094: sbfiz           x0, x2, #1, #0x1f
    //     0xbf4098: cmp             x2, x0, asr #1
    //     0xbf409c: b.eq            #0xbf40a8
    //     0xbf40a0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf40a4: stur            x2, [x0, #7]
    // 0xbf40a8: LeaveFrame
    //     0xbf40a8: mov             SP, fp
    //     0xbf40ac: ldp             fp, lr, [SP], #0x10
    // 0xbf40b0: ret
    //     0xbf40b0: ret             
    // 0xbf40b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf40b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf40b8: b               #0xbf4060
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4326c, size: 0x78
    // 0xc4326c: EnterFrame
    //     0xc4326c: stp             fp, lr, [SP, #-0x10]!
    //     0xc43270: mov             fp, SP
    // 0xc43274: AllocStack(0x8)
    //     0xc43274: sub             SP, SP, #8
    // 0xc43278: CheckStackOverflow
    //     0xc43278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4327c: cmp             SP, x16
    //     0xc43280: b.ls            #0xc432dc
    // 0xc43284: r1 = Null
    //     0xc43284: mov             x1, NULL
    // 0xc43288: r2 = 6
    //     0xc43288: movz            x2, #0x6
    // 0xc4328c: r0 = AllocateArray()
    //     0xc4328c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc43290: mov             x2, x0
    // 0xc43294: r16 = "Share.ashobah(type: "
    //     0xc43294: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c4e0] "Share.ashobah(type: "
    //     0xc43298: ldr             x16, [x16, #0x4e0]
    // 0xc4329c: StoreField: r2->field_f = r16
    //     0xc4329c: stur            w16, [x2, #0xf]
    // 0xc432a0: ldr             x0, [fp, #0x10]
    // 0xc432a4: LoadField: r3 = r0->field_7
    //     0xc432a4: ldur            x3, [x0, #7]
    // 0xc432a8: r0 = BoxInt64Instr(r3)
    //     0xc432a8: sbfiz           x0, x3, #1, #0x1f
    //     0xc432ac: cmp             x3, x0, asr #1
    //     0xc432b0: b.eq            #0xc432bc
    //     0xc432b4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc432b8: stur            x3, [x0, #7]
    // 0xc432bc: StoreField: r2->field_13 = r0
    //     0xc432bc: stur            w0, [x2, #0x13]
    // 0xc432c0: r16 = ")"
    //     0xc432c0: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc432c4: ArrayStore: r2[0] = r16  ; List_4
    //     0xc432c4: stur            w16, [x2, #0x17]
    // 0xc432c8: str             x2, [SP]
    // 0xc432cc: r0 = _interpolate()
    //     0xc432cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc432d0: LeaveFrame
    //     0xc432d0: mov             SP, fp
    //     0xc432d4: ldp             fp, lr, [SP], #0x10
    // 0xc432d8: ret
    //     0xc432d8: ret             
    // 0xc432dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc432dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc432e0: b               #0xc43284
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7f40c, size: 0x12c
    // 0xd7f40c: EnterFrame
    //     0xd7f40c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7f410: mov             fp, SP
    // 0xd7f414: AllocStack(0x20)
    //     0xd7f414: sub             SP, SP, #0x20
    // 0xd7f418: CheckStackOverflow
    //     0xd7f418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7f41c: cmp             SP, x16
    //     0xd7f420: b.ls            #0xd7f530
    // 0xd7f424: ldr             x0, [fp, #0x10]
    // 0xd7f428: cmp             w0, NULL
    // 0xd7f42c: b.ne            #0xd7f440
    // 0xd7f430: r0 = false
    //     0xd7f430: add             x0, NULL, #0x30  ; false
    // 0xd7f434: LeaveFrame
    //     0xd7f434: mov             SP, fp
    //     0xd7f438: ldp             fp, lr, [SP], #0x10
    // 0xd7f43c: ret
    //     0xd7f43c: ret             
    // 0xd7f440: ldr             x1, [fp, #0x18]
    // 0xd7f444: cmp             w1, w0
    // 0xd7f448: b.eq            #0xd7f4f8
    // 0xd7f44c: str             x0, [SP]
    // 0xd7f450: r0 = runtimeType()
    //     0xd7f450: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7f454: r1 = LoadClassIdInstr(r0)
    //     0xd7f454: ldur            x1, [x0, #-1]
    //     0xd7f458: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f45c: r16 = _$Ashobah
    //     0xd7f45c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31ea0] Type: _$Ashobah
    //     0xd7f460: ldr             x16, [x16, #0xea0]
    // 0xd7f464: stp             x16, x0, [SP]
    // 0xd7f468: mov             x0, x1
    // 0xd7f46c: mov             lr, x0
    // 0xd7f470: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f474: blr             lr
    // 0xd7f478: tbnz            w0, #4, #0xd7f520
    // 0xd7f47c: ldr             x0, [fp, #0x10]
    // 0xd7f480: r1 = 60
    //     0xd7f480: movz            x1, #0x3c
    // 0xd7f484: branchIfSmi(r0, 0xd7f490)
    //     0xd7f484: tbz             w0, #0, #0xd7f490
    // 0xd7f488: r1 = LoadClassIdInstr(r0)
    //     0xd7f488: ldur            x1, [x0, #-1]
    //     0xd7f48c: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f490: cmp             x1, #0x186
    // 0xd7f494: b.ne            #0xd7f520
    // 0xd7f498: ldr             x1, [fp, #0x18]
    // 0xd7f49c: LoadField: r2 = r0->field_7
    //     0xd7f49c: ldur            x2, [x0, #7]
    // 0xd7f4a0: stur            x2, [fp, #-0x10]
    // 0xd7f4a4: LoadField: r3 = r1->field_7
    //     0xd7f4a4: ldur            x3, [x1, #7]
    // 0xd7f4a8: stur            x3, [fp, #-8]
    // 0xd7f4ac: r0 = BoxInt64Instr(r2)
    //     0xd7f4ac: sbfiz           x0, x2, #1, #0x1f
    //     0xd7f4b0: cmp             x2, x0, asr #1
    //     0xd7f4b4: b.eq            #0xd7f4c0
    //     0xd7f4b8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd7f4bc: stur            x2, [x0, #7]
    // 0xd7f4c0: mov             x4, x0
    // 0xd7f4c4: r0 = BoxInt64Instr(r3)
    //     0xd7f4c4: sbfiz           x0, x3, #1, #0x1f
    //     0xd7f4c8: cmp             x3, x0, asr #1
    //     0xd7f4cc: b.eq            #0xd7f4d8
    //     0xd7f4d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd7f4d4: stur            x3, [x0, #7]
    // 0xd7f4d8: mov             x1, x0
    // 0xd7f4dc: mov             x0, x4
    // 0xd7f4e0: stp             x1, x0, [SP, #-0x10]!
    // 0xd7f4e4: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd7f4e4: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd7f4e8: LoadField: r30 = r30->field_7
    //     0xd7f4e8: ldur            lr, [lr, #7]
    // 0xd7f4ec: blr             lr
    // 0xd7f4f0: ldp             x1, x0, [SP], #0x10
    // 0xd7f4f4: b.ne            #0xd7f500
    // 0xd7f4f8: r0 = true
    //     0xd7f4f8: add             x0, NULL, #0x20  ; true
    // 0xd7f4fc: b               #0xd7f524
    // 0xd7f500: ldur            x1, [fp, #-0x10]
    // 0xd7f504: ldur            x2, [fp, #-8]
    // 0xd7f508: cmp             x1, x2
    // 0xd7f50c: r16 = true
    //     0xd7f50c: add             x16, NULL, #0x20  ; true
    // 0xd7f510: r17 = false
    //     0xd7f510: add             x17, NULL, #0x30  ; false
    // 0xd7f514: csel            x3, x16, x17, eq
    // 0xd7f518: mov             x0, x3
    // 0xd7f51c: b               #0xd7f524
    // 0xd7f520: r0 = false
    //     0xd7f520: add             x0, NULL, #0x30  ; false
    // 0xd7f524: LeaveFrame
    //     0xd7f524: mov             SP, fp
    //     0xd7f528: ldp             fp, lr, [SP], #0x10
    // 0xd7f52c: ret
    //     0xd7f52c: ret             
    // 0xd7f530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7f530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7f534: b               #0xd7f424
  }
  _ maybeMap(/* No info */) {
    // ** addr: 0xeb9374, size: 0xec
    // 0xeb9374: EnterFrame
    //     0xeb9374: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9378: mov             fp, SP
    // 0xeb937c: AllocStack(0x10)
    //     0xeb937c: sub             SP, SP, #0x10
    // 0xeb9380: SetupParameters(_$Ashobah this /* r2 */, dynamic _ /* r3 */, {dynamic ashobah = Null /* r1 */, dynamic furudh})
    //     0xeb9380: ldur            w0, [x4, #0x13]
    //     0xeb9384: sub             x1, x0, #4
    //     0xeb9388: add             x2, fp, w1, sxtw #2
    //     0xeb938c: ldr             x2, [x2, #0x18]
    //     0xeb9390: add             x3, fp, w1, sxtw #2
    //     0xeb9394: ldr             x3, [x3, #0x10]
    //     0xeb9398: ldur            w1, [x4, #0x1f]
    //     0xeb939c: add             x1, x1, HEAP, lsl #32
    //     0xeb93a0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c30] "ashobah"
    //     0xeb93a4: ldr             x16, [x16, #0xc30]
    //     0xeb93a8: cmp             w1, w16
    //     0xeb93ac: b.ne            #0xeb93d0
    //     0xeb93b0: ldur            w1, [x4, #0x23]
    //     0xeb93b4: add             x1, x1, HEAP, lsl #32
    //     0xeb93b8: sub             w5, w0, w1
    //     0xeb93bc: add             x0, fp, w5, sxtw #2
    //     0xeb93c0: ldr             x0, [x0, #8]
    //     0xeb93c4: mov             x1, x0
    //     0xeb93c8: movz            x0, #0x1
    //     0xeb93cc: b               #0xeb93d8
    //     0xeb93d0: mov             x1, NULL
    //     0xeb93d4: movz            x0, #0
    //     0xeb93d8: lsl             x5, x0, #1
    //     0xeb93dc: lsl             w0, w5, #1
    //     0xeb93e0: add             w5, w0, #8
    //     0xeb93e4: add             x16, x4, w5, sxtw #1
    //     0xeb93e8: ldur            w0, [x16, #0xf]
    //     0xeb93ec: add             x0, x0, HEAP, lsl #32
    //     0xeb93f0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c38] "furudh"
    //     0xeb93f4: ldr             x16, [x16, #0xc38]
    //     0xeb93f8: cmp             w0, w16
    //     0xeb93fc: b.eq            #0xeb9400
    // 0xeb9400: CheckStackOverflow
    //     0xeb9400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9404: cmp             SP, x16
    //     0xeb9408: b.ls            #0xeb9458
    // 0xeb940c: cmp             w1, NULL
    // 0xeb9410: b.eq            #0xeb9434
    // 0xeb9414: stp             x2, x1, [SP]
    // 0xeb9418: mov             x0, x1
    // 0xeb941c: ClosureCall
    //     0xeb941c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeb9420: ldur            x2, [x0, #0x1f]
    //     0xeb9424: blr             x2
    // 0xeb9428: LeaveFrame
    //     0xeb9428: mov             SP, fp
    //     0xeb942c: ldp             fp, lr, [SP], #0x10
    // 0xeb9430: ret
    //     0xeb9430: ret             
    // 0xeb9434: str             x3, [SP]
    // 0xeb9438: mov             x0, x3
    // 0xeb943c: ClosureCall
    //     0xeb943c: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xeb9440: ldur            x2, [x0, #0x1f]
    //     0xeb9444: blr             x2
    // 0xeb9448: r0 = false
    //     0xeb9448: add             x0, NULL, #0x30  ; false
    // 0xeb944c: LeaveFrame
    //     0xeb944c: mov             SP, fp
    //     0xeb9450: ldp             fp, lr, [SP], #0x10
    // 0xeb9454: ret
    //     0xeb9454: ret             
    // 0xeb9458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb945c: b               #0xeb940c
  }
}

// class id: 391, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Furudh extends Share {
}

// class id: 392, size: 0x14, field offset: 0x8
//   const constructor, 
class _$Furudh extends Furudh {

  Fraction field_8;
  _Mint field_c;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf3fd0, size: 0x7c
    // 0xbf3fd0: EnterFrame
    //     0xbf3fd0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3fd4: mov             fp, SP
    // 0xbf3fd8: AllocStack(0x8)
    //     0xbf3fd8: sub             SP, SP, #8
    // 0xbf3fdc: CheckStackOverflow
    //     0xbf3fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3fe0: cmp             SP, x16
    //     0xbf3fe4: b.ls            #0xbf4044
    // 0xbf3fe8: ldr             x0, [fp, #0x10]
    // 0xbf3fec: LoadField: r2 = r0->field_7
    //     0xbf3fec: ldur            w2, [x0, #7]
    // 0xbf3ff0: DecompressPointer r2
    //     0xbf3ff0: add             x2, x2, HEAP, lsl #32
    // 0xbf3ff4: LoadField: r3 = r0->field_b
    //     0xbf3ff4: ldur            x3, [x0, #0xb]
    // 0xbf3ff8: r0 = BoxInt64Instr(r3)
    //     0xbf3ff8: sbfiz           x0, x3, #1, #0x1f
    //     0xbf3ffc: cmp             x3, x0, asr #1
    //     0xbf4000: b.eq            #0xbf400c
    //     0xbf4004: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4008: stur            x3, [x0, #7]
    // 0xbf400c: str             x0, [SP]
    // 0xbf4010: r1 = _$Furudh
    //     0xbf4010: add             x1, PP, #0x31, lsl #12  ; [pp+0x31ea8] Type: _$Furudh
    //     0xbf4014: ldr             x1, [x1, #0xea8]
    // 0xbf4018: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbf4018: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbf401c: r0 = hash()
    //     0xbf401c: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4020: mov             x2, x0
    // 0xbf4024: r0 = BoxInt64Instr(r2)
    //     0xbf4024: sbfiz           x0, x2, #1, #0x1f
    //     0xbf4028: cmp             x2, x0, asr #1
    //     0xbf402c: b.eq            #0xbf4038
    //     0xbf4030: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4034: stur            x2, [x0, #7]
    // 0xbf4038: LeaveFrame
    //     0xbf4038: mov             SP, fp
    //     0xbf403c: ldp             fp, lr, [SP], #0x10
    // 0xbf4040: ret
    //     0xbf4040: ret             
    // 0xbf4044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4048: b               #0xbf3fe8
  }
  _ toString(/* No info */) {
    // ** addr: 0xc431dc, size: 0x90
    // 0xc431dc: EnterFrame
    //     0xc431dc: stp             fp, lr, [SP, #-0x10]!
    //     0xc431e0: mov             fp, SP
    // 0xc431e4: AllocStack(0x8)
    //     0xc431e4: sub             SP, SP, #8
    // 0xc431e8: CheckStackOverflow
    //     0xc431e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc431ec: cmp             SP, x16
    //     0xc431f0: b.ls            #0xc43264
    // 0xc431f4: r1 = Null
    //     0xc431f4: mov             x1, NULL
    // 0xc431f8: r2 = 10
    //     0xc431f8: movz            x2, #0xa
    // 0xc431fc: r0 = AllocateArray()
    //     0xc431fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc43200: mov             x2, x0
    // 0xc43204: r16 = "Share.furudh(fraction: "
    //     0xc43204: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c4e8] "Share.furudh(fraction: "
    //     0xc43208: ldr             x16, [x16, #0x4e8]
    // 0xc4320c: StoreField: r2->field_f = r16
    //     0xc4320c: stur            w16, [x2, #0xf]
    // 0xc43210: ldr             x0, [fp, #0x10]
    // 0xc43214: LoadField: r1 = r0->field_7
    //     0xc43214: ldur            w1, [x0, #7]
    // 0xc43218: DecompressPointer r1
    //     0xc43218: add             x1, x1, HEAP, lsl #32
    // 0xc4321c: StoreField: r2->field_13 = r1
    //     0xc4321c: stur            w1, [x2, #0x13]
    // 0xc43220: r16 = ", type: "
    //     0xc43220: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c4f0] ", type: "
    //     0xc43224: ldr             x16, [x16, #0x4f0]
    // 0xc43228: ArrayStore: r2[0] = r16  ; List_4
    //     0xc43228: stur            w16, [x2, #0x17]
    // 0xc4322c: LoadField: r3 = r0->field_b
    //     0xc4322c: ldur            x3, [x0, #0xb]
    // 0xc43230: r0 = BoxInt64Instr(r3)
    //     0xc43230: sbfiz           x0, x3, #1, #0x1f
    //     0xc43234: cmp             x3, x0, asr #1
    //     0xc43238: b.eq            #0xc43244
    //     0xc4323c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc43240: stur            x3, [x0, #7]
    // 0xc43244: StoreField: r2->field_1b = r0
    //     0xc43244: stur            w0, [x2, #0x1b]
    // 0xc43248: r16 = ")"
    //     0xc43248: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc4324c: StoreField: r2->field_1f = r16
    //     0xc4324c: stur            w16, [x2, #0x1f]
    // 0xc43250: str             x2, [SP]
    // 0xc43254: r0 = _interpolate()
    //     0xc43254: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc43258: LeaveFrame
    //     0xc43258: mov             SP, fp
    //     0xc4325c: ldp             fp, lr, [SP], #0x10
    // 0xc43260: ret
    //     0xc43260: ret             
    // 0xc43264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43264: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc43268: b               #0xc431f4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7f2c0, size: 0x14c
    // 0xd7f2c0: EnterFrame
    //     0xd7f2c0: stp             fp, lr, [SP, #-0x10]!
    //     0xd7f2c4: mov             fp, SP
    // 0xd7f2c8: AllocStack(0x20)
    //     0xd7f2c8: sub             SP, SP, #0x20
    // 0xd7f2cc: CheckStackOverflow
    //     0xd7f2cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7f2d0: cmp             SP, x16
    //     0xd7f2d4: b.ls            #0xd7f404
    // 0xd7f2d8: ldr             x0, [fp, #0x10]
    // 0xd7f2dc: cmp             w0, NULL
    // 0xd7f2e0: b.ne            #0xd7f2f4
    // 0xd7f2e4: r0 = false
    //     0xd7f2e4: add             x0, NULL, #0x30  ; false
    // 0xd7f2e8: LeaveFrame
    //     0xd7f2e8: mov             SP, fp
    //     0xd7f2ec: ldp             fp, lr, [SP], #0x10
    // 0xd7f2f0: ret
    //     0xd7f2f0: ret             
    // 0xd7f2f4: ldr             x1, [fp, #0x18]
    // 0xd7f2f8: cmp             w1, w0
    // 0xd7f2fc: b.eq            #0xd7f3cc
    // 0xd7f300: str             x0, [SP]
    // 0xd7f304: r0 = runtimeType()
    //     0xd7f304: bl              #0xbf92d8  ; [dart:core] Object::runtimeType
    // 0xd7f308: r1 = LoadClassIdInstr(r0)
    //     0xd7f308: ldur            x1, [x0, #-1]
    //     0xd7f30c: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f310: r16 = _$Furudh
    //     0xd7f310: add             x16, PP, #0x31, lsl #12  ; [pp+0x31ea8] Type: _$Furudh
    //     0xd7f314: ldr             x16, [x16, #0xea8]
    // 0xd7f318: stp             x16, x0, [SP]
    // 0xd7f31c: mov             x0, x1
    // 0xd7f320: mov             lr, x0
    // 0xd7f324: ldr             lr, [x21, lr, lsl #3]
    // 0xd7f328: blr             lr
    // 0xd7f32c: tbnz            w0, #4, #0xd7f3f4
    // 0xd7f330: ldr             x0, [fp, #0x10]
    // 0xd7f334: r1 = 60
    //     0xd7f334: movz            x1, #0x3c
    // 0xd7f338: branchIfSmi(r0, 0xd7f344)
    //     0xd7f338: tbz             w0, #0, #0xd7f344
    // 0xd7f33c: r1 = LoadClassIdInstr(r0)
    //     0xd7f33c: ldur            x1, [x0, #-1]
    //     0xd7f340: ubfx            x1, x1, #0xc, #0x14
    // 0xd7f344: cmp             x1, #0x188
    // 0xd7f348: b.ne            #0xd7f3f4
    // 0xd7f34c: ldr             x1, [fp, #0x18]
    // 0xd7f350: LoadField: r2 = r0->field_7
    //     0xd7f350: ldur            w2, [x0, #7]
    // 0xd7f354: DecompressPointer r2
    //     0xd7f354: add             x2, x2, HEAP, lsl #32
    // 0xd7f358: LoadField: r3 = r1->field_7
    //     0xd7f358: ldur            w3, [x1, #7]
    // 0xd7f35c: DecompressPointer r3
    //     0xd7f35c: add             x3, x3, HEAP, lsl #32
    // 0xd7f360: cmp             w2, w3
    // 0xd7f364: b.eq            #0xd7f370
    // 0xd7f368: cmp             w2, w3
    // 0xd7f36c: b.ne            #0xd7f3f4
    // 0xd7f370: LoadField: r2 = r0->field_b
    //     0xd7f370: ldur            x2, [x0, #0xb]
    // 0xd7f374: stur            x2, [fp, #-0x10]
    // 0xd7f378: LoadField: r3 = r1->field_b
    //     0xd7f378: ldur            x3, [x1, #0xb]
    // 0xd7f37c: stur            x3, [fp, #-8]
    // 0xd7f380: r0 = BoxInt64Instr(r2)
    //     0xd7f380: sbfiz           x0, x2, #1, #0x1f
    //     0xd7f384: cmp             x2, x0, asr #1
    //     0xd7f388: b.eq            #0xd7f394
    //     0xd7f38c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd7f390: stur            x2, [x0, #7]
    // 0xd7f394: mov             x4, x0
    // 0xd7f398: r0 = BoxInt64Instr(r3)
    //     0xd7f398: sbfiz           x0, x3, #1, #0x1f
    //     0xd7f39c: cmp             x3, x0, asr #1
    //     0xd7f3a0: b.eq            #0xd7f3ac
    //     0xd7f3a4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd7f3a8: stur            x3, [x0, #7]
    // 0xd7f3ac: mov             x1, x0
    // 0xd7f3b0: mov             x0, x4
    // 0xd7f3b4: stp             x1, x0, [SP, #-0x10]!
    // 0xd7f3b8: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0xd7f3b8: ldr             lr, [PP, #0x1200]  ; [pp+0x1200] Stub: OptimizedIdenticalWithNumberCheck (0x5f32bc)
    // 0xd7f3bc: LoadField: r30 = r30->field_7
    //     0xd7f3bc: ldur            lr, [lr, #7]
    // 0xd7f3c0: blr             lr
    // 0xd7f3c4: ldp             x1, x0, [SP], #0x10
    // 0xd7f3c8: b.ne            #0xd7f3d4
    // 0xd7f3cc: r0 = true
    //     0xd7f3cc: add             x0, NULL, #0x20  ; true
    // 0xd7f3d0: b               #0xd7f3f8
    // 0xd7f3d4: ldur            x1, [fp, #-0x10]
    // 0xd7f3d8: ldur            x2, [fp, #-8]
    // 0xd7f3dc: cmp             x1, x2
    // 0xd7f3e0: r16 = true
    //     0xd7f3e0: add             x16, NULL, #0x20  ; true
    // 0xd7f3e4: r17 = false
    //     0xd7f3e4: add             x17, NULL, #0x30  ; false
    // 0xd7f3e8: csel            x3, x16, x17, eq
    // 0xd7f3ec: mov             x0, x3
    // 0xd7f3f0: b               #0xd7f3f8
    // 0xd7f3f4: r0 = false
    //     0xd7f3f4: add             x0, NULL, #0x30  ; false
    // 0xd7f3f8: LeaveFrame
    //     0xd7f3f8: mov             SP, fp
    //     0xd7f3fc: ldp             fp, lr, [SP], #0x10
    // 0xd7f400: ret
    //     0xd7f400: ret             
    // 0xd7f404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7f404: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7f408: b               #0xd7f2d8
  }
  _ maybeMap(/* No info */) {
    // ** addr: 0xeb9284, size: 0xf0
    // 0xeb9284: EnterFrame
    //     0xeb9284: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9288: mov             fp, SP
    // 0xeb928c: AllocStack(0x10)
    //     0xeb928c: sub             SP, SP, #0x10
    // 0xeb9290: SetupParameters(_$Furudh this /* r2 */, dynamic _ /* r3 */, {dynamic ashobah, dynamic furudh = Null /* r0 */})
    //     0xeb9290: ldur            w0, [x4, #0x13]
    //     0xeb9294: sub             x1, x0, #4
    //     0xeb9298: add             x2, fp, w1, sxtw #2
    //     0xeb929c: ldr             x2, [x2, #0x18]
    //     0xeb92a0: add             x3, fp, w1, sxtw #2
    //     0xeb92a4: ldr             x3, [x3, #0x10]
    //     0xeb92a8: ldur            w1, [x4, #0x1f]
    //     0xeb92ac: add             x1, x1, HEAP, lsl #32
    //     0xeb92b0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c30] "ashobah"
    //     0xeb92b4: ldr             x16, [x16, #0xc30]
    //     0xeb92b8: cmp             w1, w16
    //     0xeb92bc: b.ne            #0xeb92c8
    //     0xeb92c0: movz            x1, #0x1
    //     0xeb92c4: b               #0xeb92cc
    //     0xeb92c8: movz            x1, #0
    //     0xeb92cc: lsl             x5, x1, #1
    //     0xeb92d0: lsl             w1, w5, #1
    //     0xeb92d4: add             w5, w1, #8
    //     0xeb92d8: add             x16, x4, w5, sxtw #1
    //     0xeb92dc: ldur            w6, [x16, #0xf]
    //     0xeb92e0: add             x6, x6, HEAP, lsl #32
    //     0xeb92e4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31c38] "furudh"
    //     0xeb92e8: ldr             x16, [x16, #0xc38]
    //     0xeb92ec: cmp             w6, w16
    //     0xeb92f0: b.ne            #0xeb9314
    //     0xeb92f4: add             w5, w1, #0xa
    //     0xeb92f8: add             x16, x4, w5, sxtw #1
    //     0xeb92fc: ldur            w1, [x16, #0xf]
    //     0xeb9300: add             x1, x1, HEAP, lsl #32
    //     0xeb9304: sub             w4, w0, w1
    //     0xeb9308: add             x0, fp, w4, sxtw #2
    //     0xeb930c: ldr             x0, [x0, #8]
    //     0xeb9310: b               #0xeb9318
    //     0xeb9314: mov             x0, NULL
    // 0xeb9318: CheckStackOverflow
    //     0xeb9318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb931c: cmp             SP, x16
    //     0xeb9320: b.ls            #0xeb936c
    // 0xeb9324: cmp             w0, NULL
    // 0xeb9328: b.eq            #0xeb9348
    // 0xeb932c: stp             x2, x0, [SP]
    // 0xeb9330: ClosureCall
    //     0xeb9330: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeb9334: ldur            x2, [x0, #0x1f]
    //     0xeb9338: blr             x2
    // 0xeb933c: LeaveFrame
    //     0xeb933c: mov             SP, fp
    //     0xeb9340: ldp             fp, lr, [SP], #0x10
    // 0xeb9344: ret
    //     0xeb9344: ret             
    // 0xeb9348: str             x3, [SP]
    // 0xeb934c: mov             x0, x3
    // 0xeb9350: ClosureCall
    //     0xeb9350: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xeb9354: ldur            x2, [x0, #0x1f]
    //     0xeb9358: blr             x2
    // 0xeb935c: r0 = false
    //     0xeb935c: add             x0, NULL, #0x30  ; false
    // 0xeb9360: LeaveFrame
    //     0xeb9360: mov             SP, fp
    //     0xeb9364: ldp             fp, lr, [SP], #0x10
    // 0xeb9368: ret
    //     0xeb9368: ret             
    // 0xeb936c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb936c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9370: b               #0xeb9324
  }
}

// class id: 393, size: 0x8, field offset: 0x8
abstract class Shares extends Object {

  static late final Map<Heir, (dynamic, Map<Heir, int>) => Share> _shares; // offset: 0x16a0

  static _ of(/* No info */) {
    // ** addr: 0x90b91c, size: 0x88
    // 0x90b91c: EnterFrame
    //     0x90b91c: stp             fp, lr, [SP, #-0x10]!
    //     0x90b920: mov             fp, SP
    // 0x90b924: AllocStack(0x8)
    //     0x90b924: sub             SP, SP, #8
    // 0x90b928: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x90b928: mov             x2, x1
    //     0x90b92c: stur            x1, [fp, #-8]
    // 0x90b930: CheckStackOverflow
    //     0x90b930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b934: cmp             SP, x16
    //     0x90b938: b.ls            #0x90b998
    // 0x90b93c: r0 = InitLateStaticField(0x16a0) // [package:waris/src/shares.dart] Shares::_shares
    //     0x90b93c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x90b940: ldr             x0, [x0, #0x2d40]
    //     0x90b944: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x90b948: cmp             w0, w16
    //     0x90b94c: b.ne            #0x90b95c
    //     0x90b950: add             x2, PP, #0x31, lsl #12  ; [pp+0x31820] Field <Shares._shares@2491330188>: static late final (offset: 0x16a0)
    //     0x90b954: ldr             x2, [x2, #0x820]
    //     0x90b958: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x90b95c: mov             x1, x0
    // 0x90b960: ldur            x2, [fp, #-8]
    // 0x90b964: stur            x0, [fp, #-8]
    // 0x90b968: r0 = _getValueOrData()
    //     0x90b968: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90b96c: ldur            x1, [fp, #-8]
    // 0x90b970: LoadField: r2 = r1->field_f
    //     0x90b970: ldur            w2, [x1, #0xf]
    // 0x90b974: DecompressPointer r2
    //     0x90b974: add             x2, x2, HEAP, lsl #32
    // 0x90b978: cmp             w2, w0
    // 0x90b97c: b.ne            #0x90b984
    // 0x90b980: r0 = Null
    //     0x90b980: mov             x0, NULL
    // 0x90b984: cmp             w0, NULL
    // 0x90b988: b.eq            #0x90b9a0
    // 0x90b98c: LeaveFrame
    //     0x90b98c: mov             SP, fp
    //     0x90b990: ldp             fp, lr, [SP], #0x10
    // 0x90b994: ret
    //     0x90b994: ret             
    // 0x90b998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b998: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b99c: b               #0x90b93c
    // 0x90b9a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90b9a0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static Map<Heir, (dynamic, Map<Heir, int>) => Share> _shares() {
    // ** addr: 0x90b9a4, size: 0x79c
    // 0x90b9a4: EnterFrame
    //     0x90b9a4: stp             fp, lr, [SP, #-0x10]!
    //     0x90b9a8: mov             fp, SP
    // 0x90b9ac: AllocStack(0x18)
    //     0x90b9ac: sub             SP, SP, #0x18
    // 0x90b9b0: CheckStackOverflow
    //     0x90b9b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b9b4: cmp             SP, x16
    //     0x90b9b8: b.ls            #0x90c138
    // 0x90b9bc: r1 = Null
    //     0x90b9bc: mov             x1, NULL
    // 0x90b9c0: r2 = 104
    //     0x90b9c0: movz            x2, #0x68
    // 0x90b9c4: r0 = AllocateArray()
    //     0x90b9c4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90b9c8: stur            x0, [fp, #-8]
    // 0x90b9cc: r16 = Instance_Heir
    //     0x90b9cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90b9d0: ldr             x16, [x16, #0x7d0]
    // 0x90b9d4: StoreField: r0->field_f = r16
    //     0x90b9d4: stur            w16, [x0, #0xf]
    // 0x90b9d8: r1 = Function '<anonymous closure>': static.
    //     0x90b9d8: add             x1, PP, #0x31, lsl #12  ; [pp+0x31828] AnonymousClosure: static (0x90ea14), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90b9dc: ldr             x1, [x1, #0x828]
    // 0x90b9e0: r2 = Null
    //     0x90b9e0: mov             x2, NULL
    // 0x90b9e4: r0 = AllocateClosure()
    //     0x90b9e4: bl              #0xec1630  ; AllocateClosureStub
    // 0x90b9e8: ldur            x1, [fp, #-8]
    // 0x90b9ec: ArrayStore: r1[1] = r0  ; List_4
    //     0x90b9ec: add             x25, x1, #0x13
    //     0x90b9f0: str             w0, [x25]
    //     0x90b9f4: tbz             w0, #0, #0x90ba10
    //     0x90b9f8: ldurb           w16, [x1, #-1]
    //     0x90b9fc: ldurb           w17, [x0, #-1]
    //     0x90ba00: and             x16, x17, x16, lsr #2
    //     0x90ba04: tst             x16, HEAP, lsr #32
    //     0x90ba08: b.eq            #0x90ba10
    //     0x90ba0c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90ba10: ldur            x0, [fp, #-8]
    // 0x90ba14: r16 = Instance_Heir
    //     0x90ba14: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90ba18: ldr             x16, [x16, #0x7e0]
    // 0x90ba1c: ArrayStore: r0[0] = r16  ; List_4
    //     0x90ba1c: stur            w16, [x0, #0x17]
    // 0x90ba20: r1 = Function '<anonymous closure>': static.
    //     0x90ba20: add             x1, PP, #0x31, lsl #12  ; [pp+0x31830] AnonymousClosure: static (0x90e994), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90ba24: ldr             x1, [x1, #0x830]
    // 0x90ba28: r2 = Null
    //     0x90ba28: mov             x2, NULL
    // 0x90ba2c: r0 = AllocateClosure()
    //     0x90ba2c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90ba30: ldur            x1, [fp, #-8]
    // 0x90ba34: ArrayStore: r1[3] = r0  ; List_4
    //     0x90ba34: add             x25, x1, #0x1b
    //     0x90ba38: str             w0, [x25]
    //     0x90ba3c: tbz             w0, #0, #0x90ba58
    //     0x90ba40: ldurb           w16, [x1, #-1]
    //     0x90ba44: ldurb           w17, [x0, #-1]
    //     0x90ba48: and             x16, x17, x16, lsr #2
    //     0x90ba4c: tst             x16, HEAP, lsr #32
    //     0x90ba50: b.eq            #0x90ba58
    //     0x90ba54: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90ba58: ldur            x0, [fp, #-8]
    // 0x90ba5c: r16 = Instance_Heir
    //     0x90ba5c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90ba60: ldr             x16, [x16, #0x838]
    // 0x90ba64: StoreField: r0->field_1f = r16
    //     0x90ba64: stur            w16, [x0, #0x1f]
    // 0x90ba68: r1 = Function '<anonymous closure>': static.
    //     0x90ba68: add             x1, PP, #0x31, lsl #12  ; [pp+0x31840] AnonymousClosure: static (0x90e8d8), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90ba6c: ldr             x1, [x1, #0x840]
    // 0x90ba70: r2 = Null
    //     0x90ba70: mov             x2, NULL
    // 0x90ba74: r0 = AllocateClosure()
    //     0x90ba74: bl              #0xec1630  ; AllocateClosureStub
    // 0x90ba78: ldur            x1, [fp, #-8]
    // 0x90ba7c: ArrayStore: r1[5] = r0  ; List_4
    //     0x90ba7c: add             x25, x1, #0x23
    //     0x90ba80: str             w0, [x25]
    //     0x90ba84: tbz             w0, #0, #0x90baa0
    //     0x90ba88: ldurb           w16, [x1, #-1]
    //     0x90ba8c: ldurb           w17, [x0, #-1]
    //     0x90ba90: and             x16, x17, x16, lsr #2
    //     0x90ba94: tst             x16, HEAP, lsr #32
    //     0x90ba98: b.eq            #0x90baa0
    //     0x90ba9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90baa0: ldur            x0, [fp, #-8]
    // 0x90baa4: r16 = Instance_Heir
    //     0x90baa4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90baa8: ldr             x16, [x16, #0x88]
    // 0x90baac: StoreField: r0->field_27 = r16
    //     0x90baac: stur            w16, [x0, #0x27]
    // 0x90bab0: r1 = Function '<anonymous closure>': static.
    //     0x90bab0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31848] AnonymousClosure: static (0x90e7f4), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bab4: ldr             x1, [x1, #0x848]
    // 0x90bab8: r2 = Null
    //     0x90bab8: mov             x2, NULL
    // 0x90babc: r0 = AllocateClosure()
    //     0x90babc: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bac0: ldur            x1, [fp, #-8]
    // 0x90bac4: ArrayStore: r1[7] = r0  ; List_4
    //     0x90bac4: add             x25, x1, #0x2b
    //     0x90bac8: str             w0, [x25]
    //     0x90bacc: tbz             w0, #0, #0x90bae8
    //     0x90bad0: ldurb           w16, [x1, #-1]
    //     0x90bad4: ldurb           w17, [x0, #-1]
    //     0x90bad8: and             x16, x17, x16, lsr #2
    //     0x90badc: tst             x16, HEAP, lsr #32
    //     0x90bae0: b.eq            #0x90bae8
    //     0x90bae4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bae8: ldur            x0, [fp, #-8]
    // 0x90baec: r16 = Instance_Heir
    //     0x90baec: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90baf0: ldr             x16, [x16, #0x90]
    // 0x90baf4: StoreField: r0->field_2f = r16
    //     0x90baf4: stur            w16, [x0, #0x2f]
    // 0x90baf8: r1 = Function '<anonymous closure>': static.
    //     0x90baf8: add             x1, PP, #0x31, lsl #12  ; [pp+0x31850] AnonymousClosure: static (0x90e738), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bafc: ldr             x1, [x1, #0x850]
    // 0x90bb00: r2 = Null
    //     0x90bb00: mov             x2, NULL
    // 0x90bb04: r0 = AllocateClosure()
    //     0x90bb04: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bb08: ldur            x1, [fp, #-8]
    // 0x90bb0c: ArrayStore: r1[9] = r0  ; List_4
    //     0x90bb0c: add             x25, x1, #0x33
    //     0x90bb10: str             w0, [x25]
    //     0x90bb14: tbz             w0, #0, #0x90bb30
    //     0x90bb18: ldurb           w16, [x1, #-1]
    //     0x90bb1c: ldurb           w17, [x0, #-1]
    //     0x90bb20: and             x16, x17, x16, lsr #2
    //     0x90bb24: tst             x16, HEAP, lsr #32
    //     0x90bb28: b.eq            #0x90bb30
    //     0x90bb2c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bb30: ldur            x0, [fp, #-8]
    // 0x90bb34: r16 = Instance_Heir
    //     0x90bb34: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90bb38: ldr             x16, [x16, #0x98]
    // 0x90bb3c: StoreField: r0->field_37 = r16
    //     0x90bb3c: stur            w16, [x0, #0x37]
    // 0x90bb40: r1 = Function '<anonymous closure>': static.
    //     0x90bb40: add             x1, PP, #0x31, lsl #12  ; [pp+0x31858] AnonymousClosure: static (0x90e588), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bb44: ldr             x1, [x1, #0x858]
    // 0x90bb48: r2 = Null
    //     0x90bb48: mov             x2, NULL
    // 0x90bb4c: r0 = AllocateClosure()
    //     0x90bb4c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bb50: ldur            x1, [fp, #-8]
    // 0x90bb54: ArrayStore: r1[11] = r0  ; List_4
    //     0x90bb54: add             x25, x1, #0x3b
    //     0x90bb58: str             w0, [x25]
    //     0x90bb5c: tbz             w0, #0, #0x90bb78
    //     0x90bb60: ldurb           w16, [x1, #-1]
    //     0x90bb64: ldurb           w17, [x0, #-1]
    //     0x90bb68: and             x16, x17, x16, lsr #2
    //     0x90bb6c: tst             x16, HEAP, lsr #32
    //     0x90bb70: b.eq            #0x90bb78
    //     0x90bb74: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bb78: ldur            x0, [fp, #-8]
    // 0x90bb7c: r16 = Instance_Heir
    //     0x90bb7c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x90bb80: ldr             x16, [x16, #0xa0]
    // 0x90bb84: StoreField: r0->field_3f = r16
    //     0x90bb84: stur            w16, [x0, #0x3f]
    // 0x90bb88: r1 = Function '<anonymous closure>': static.
    //     0x90bb88: add             x1, PP, #0x31, lsl #12  ; [pp+0x31860] AnonymousClosure: static (0x90e450), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bb8c: ldr             x1, [x1, #0x860]
    // 0x90bb90: r2 = Null
    //     0x90bb90: mov             x2, NULL
    // 0x90bb94: r0 = AllocateClosure()
    //     0x90bb94: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bb98: ldur            x1, [fp, #-8]
    // 0x90bb9c: ArrayStore: r1[13] = r0  ; List_4
    //     0x90bb9c: add             x25, x1, #0x43
    //     0x90bba0: str             w0, [x25]
    //     0x90bba4: tbz             w0, #0, #0x90bbc0
    //     0x90bba8: ldurb           w16, [x1, #-1]
    //     0x90bbac: ldurb           w17, [x0, #-1]
    //     0x90bbb0: and             x16, x17, x16, lsr #2
    //     0x90bbb4: tst             x16, HEAP, lsr #32
    //     0x90bbb8: b.eq            #0x90bbc0
    //     0x90bbbc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bbc0: ldur            x0, [fp, #-8]
    // 0x90bbc4: r16 = Instance_Heir
    //     0x90bbc4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90bbc8: ldr             x16, [x16, #0x868]
    // 0x90bbcc: StoreField: r0->field_47 = r16
    //     0x90bbcc: stur            w16, [x0, #0x47]
    // 0x90bbd0: r1 = Function '<anonymous closure>': static.
    //     0x90bbd0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31870] AnonymousClosure: static (0x90e1a8), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bbd4: ldr             x1, [x1, #0x870]
    // 0x90bbd8: r2 = Null
    //     0x90bbd8: mov             x2, NULL
    // 0x90bbdc: r0 = AllocateClosure()
    //     0x90bbdc: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bbe0: ldur            x1, [fp, #-8]
    // 0x90bbe4: ArrayStore: r1[15] = r0  ; List_4
    //     0x90bbe4: add             x25, x1, #0x4b
    //     0x90bbe8: str             w0, [x25]
    //     0x90bbec: tbz             w0, #0, #0x90bc08
    //     0x90bbf0: ldurb           w16, [x1, #-1]
    //     0x90bbf4: ldurb           w17, [x0, #-1]
    //     0x90bbf8: and             x16, x17, x16, lsr #2
    //     0x90bbfc: tst             x16, HEAP, lsr #32
    //     0x90bc00: b.eq            #0x90bc08
    //     0x90bc04: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bc08: ldur            x0, [fp, #-8]
    // 0x90bc0c: r16 = Instance_Heir
    //     0x90bc0c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x90bc10: ldr             x16, [x16, #0x878]
    // 0x90bc14: StoreField: r0->field_4f = r16
    //     0x90bc14: stur            w16, [x0, #0x4f]
    // 0x90bc18: r1 = Function '<anonymous closure>': static.
    //     0x90bc18: add             x1, PP, #0x31, lsl #12  ; [pp+0x31880] AnonymousClosure: static (0x90def4), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bc1c: ldr             x1, [x1, #0x880]
    // 0x90bc20: r2 = Null
    //     0x90bc20: mov             x2, NULL
    // 0x90bc24: r0 = AllocateClosure()
    //     0x90bc24: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bc28: ldur            x1, [fp, #-8]
    // 0x90bc2c: ArrayStore: r1[17] = r0  ; List_4
    //     0x90bc2c: add             x25, x1, #0x53
    //     0x90bc30: str             w0, [x25]
    //     0x90bc34: tbz             w0, #0, #0x90bc50
    //     0x90bc38: ldurb           w16, [x1, #-1]
    //     0x90bc3c: ldurb           w17, [x0, #-1]
    //     0x90bc40: and             x16, x17, x16, lsr #2
    //     0x90bc44: tst             x16, HEAP, lsr #32
    //     0x90bc48: b.eq            #0x90bc50
    //     0x90bc4c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bc50: ldur            x0, [fp, #-8]
    // 0x90bc54: r16 = Instance_Heir
    //     0x90bc54: add             x16, PP, #0x31, lsl #12  ; [pp+0x31888] Obj!Heir@e2db71
    //     0x90bc58: ldr             x16, [x16, #0x888]
    // 0x90bc5c: StoreField: r0->field_57 = r16
    //     0x90bc5c: stur            w16, [x0, #0x57]
    // 0x90bc60: r1 = Function '<anonymous closure>': static.
    //     0x90bc60: add             x1, PP, #0x31, lsl #12  ; [pp+0x31890] AnonymousClosure: static (0x90dc34), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bc64: ldr             x1, [x1, #0x890]
    // 0x90bc68: r2 = Null
    //     0x90bc68: mov             x2, NULL
    // 0x90bc6c: r0 = AllocateClosure()
    //     0x90bc6c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bc70: ldur            x1, [fp, #-8]
    // 0x90bc74: ArrayStore: r1[19] = r0  ; List_4
    //     0x90bc74: add             x25, x1, #0x5b
    //     0x90bc78: str             w0, [x25]
    //     0x90bc7c: tbz             w0, #0, #0x90bc98
    //     0x90bc80: ldurb           w16, [x1, #-1]
    //     0x90bc84: ldurb           w17, [x0, #-1]
    //     0x90bc88: and             x16, x17, x16, lsr #2
    //     0x90bc8c: tst             x16, HEAP, lsr #32
    //     0x90bc90: b.eq            #0x90bc98
    //     0x90bc94: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bc98: ldur            x0, [fp, #-8]
    // 0x90bc9c: r16 = Instance_Heir
    //     0x90bc9c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31898] Obj!Heir@e2db41
    //     0x90bca0: ldr             x16, [x16, #0x898]
    // 0x90bca4: StoreField: r0->field_5f = r16
    //     0x90bca4: stur            w16, [x0, #0x5f]
    // 0x90bca8: r1 = Function '<anonymous closure>': static.
    //     0x90bca8: add             x1, PP, #0x31, lsl #12  ; [pp+0x318a0] AnonymousClosure: static (0x90d964), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bcac: ldr             x1, [x1, #0x8a0]
    // 0x90bcb0: r2 = Null
    //     0x90bcb0: mov             x2, NULL
    // 0x90bcb4: r0 = AllocateClosure()
    //     0x90bcb4: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bcb8: ldur            x1, [fp, #-8]
    // 0x90bcbc: ArrayStore: r1[21] = r0  ; List_4
    //     0x90bcbc: add             x25, x1, #0x63
    //     0x90bcc0: str             w0, [x25]
    //     0x90bcc4: tbz             w0, #0, #0x90bce0
    //     0x90bcc8: ldurb           w16, [x1, #-1]
    //     0x90bccc: ldurb           w17, [x0, #-1]
    //     0x90bcd0: and             x16, x17, x16, lsr #2
    //     0x90bcd4: tst             x16, HEAP, lsr #32
    //     0x90bcd8: b.eq            #0x90bce0
    //     0x90bcdc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bce0: ldur            x0, [fp, #-8]
    // 0x90bce4: r16 = Instance_Heir
    //     0x90bce4: add             x16, PP, #0x31, lsl #12  ; [pp+0x318a8] Obj!Heir@e2db11
    //     0x90bce8: ldr             x16, [x16, #0x8a8]
    // 0x90bcec: StoreField: r0->field_67 = r16
    //     0x90bcec: stur            w16, [x0, #0x67]
    // 0x90bcf0: r1 = Function '<anonymous closure>': static.
    //     0x90bcf0: add             x1, PP, #0x31, lsl #12  ; [pp+0x318b0] AnonymousClosure: static (0x90d688), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bcf4: ldr             x1, [x1, #0x8b0]
    // 0x90bcf8: r2 = Null
    //     0x90bcf8: mov             x2, NULL
    // 0x90bcfc: r0 = AllocateClosure()
    //     0x90bcfc: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bd00: ldur            x1, [fp, #-8]
    // 0x90bd04: ArrayStore: r1[23] = r0  ; List_4
    //     0x90bd04: add             x25, x1, #0x6b
    //     0x90bd08: str             w0, [x25]
    //     0x90bd0c: tbz             w0, #0, #0x90bd28
    //     0x90bd10: ldurb           w16, [x1, #-1]
    //     0x90bd14: ldurb           w17, [x0, #-1]
    //     0x90bd18: and             x16, x17, x16, lsr #2
    //     0x90bd1c: tst             x16, HEAP, lsr #32
    //     0x90bd20: b.eq            #0x90bd28
    //     0x90bd24: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bd28: ldur            x0, [fp, #-8]
    // 0x90bd2c: r16 = Instance_Heir
    //     0x90bd2c: add             x16, PP, #0x31, lsl #12  ; [pp+0x318b8] Obj!Heir@e2dae1
    //     0x90bd30: ldr             x16, [x16, #0x8b8]
    // 0x90bd34: StoreField: r0->field_6f = r16
    //     0x90bd34: stur            w16, [x0, #0x6f]
    // 0x90bd38: r1 = Function '<anonymous closure>': static.
    //     0x90bd38: add             x1, PP, #0x31, lsl #12  ; [pp+0x318c0] AnonymousClosure: static (0x90d3a0), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bd3c: ldr             x1, [x1, #0x8c0]
    // 0x90bd40: r2 = Null
    //     0x90bd40: mov             x2, NULL
    // 0x90bd44: r0 = AllocateClosure()
    //     0x90bd44: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bd48: ldur            x1, [fp, #-8]
    // 0x90bd4c: ArrayStore: r1[25] = r0  ; List_4
    //     0x90bd4c: add             x25, x1, #0x73
    //     0x90bd50: str             w0, [x25]
    //     0x90bd54: tbz             w0, #0, #0x90bd70
    //     0x90bd58: ldurb           w16, [x1, #-1]
    //     0x90bd5c: ldurb           w17, [x0, #-1]
    //     0x90bd60: and             x16, x17, x16, lsr #2
    //     0x90bd64: tst             x16, HEAP, lsr #32
    //     0x90bd68: b.eq            #0x90bd70
    //     0x90bd6c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bd70: ldur            x0, [fp, #-8]
    // 0x90bd74: r16 = Instance_Heir
    //     0x90bd74: add             x16, PP, #0x31, lsl #12  ; [pp+0x318c8] Obj!Heir@e2dd81
    //     0x90bd78: ldr             x16, [x16, #0x8c8]
    // 0x90bd7c: StoreField: r0->field_77 = r16
    //     0x90bd7c: stur            w16, [x0, #0x77]
    // 0x90bd80: r1 = Function '<anonymous closure>': static.
    //     0x90bd80: add             x1, PP, #0x31, lsl #12  ; [pp+0x318d0] AnonymousClosure: static (0x90d2e4), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bd84: ldr             x1, [x1, #0x8d0]
    // 0x90bd88: r2 = Null
    //     0x90bd88: mov             x2, NULL
    // 0x90bd8c: r0 = AllocateClosure()
    //     0x90bd8c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bd90: ldur            x1, [fp, #-8]
    // 0x90bd94: ArrayStore: r1[27] = r0  ; List_4
    //     0x90bd94: add             x25, x1, #0x7b
    //     0x90bd98: str             w0, [x25]
    //     0x90bd9c: tbz             w0, #0, #0x90bdb8
    //     0x90bda0: ldurb           w16, [x1, #-1]
    //     0x90bda4: ldurb           w17, [x0, #-1]
    //     0x90bda8: and             x16, x17, x16, lsr #2
    //     0x90bdac: tst             x16, HEAP, lsr #32
    //     0x90bdb0: b.eq            #0x90bdb8
    //     0x90bdb4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bdb8: ldur            x0, [fp, #-8]
    // 0x90bdbc: r16 = Instance_Heir
    //     0x90bdbc: add             x16, PP, #0x31, lsl #12  ; [pp+0x318d8] Obj!Heir@e2dab1
    //     0x90bdc0: ldr             x16, [x16, #0x8d8]
    // 0x90bdc4: StoreField: r0->field_7f = r16
    //     0x90bdc4: stur            w16, [x0, #0x7f]
    // 0x90bdc8: r1 = Function '<anonymous closure>': static.
    //     0x90bdc8: add             x1, PP, #0x31, lsl #12  ; [pp+0x318e0] AnonymousClosure: static (0x90cff0), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bdcc: ldr             x1, [x1, #0x8e0]
    // 0x90bdd0: r2 = Null
    //     0x90bdd0: mov             x2, NULL
    // 0x90bdd4: r0 = AllocateClosure()
    //     0x90bdd4: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bdd8: ldur            x1, [fp, #-8]
    // 0x90bddc: ArrayStore: r1[29] = r0  ; List_4
    //     0x90bddc: add             x25, x1, #0x83
    //     0x90bde0: str             w0, [x25]
    //     0x90bde4: tbz             w0, #0, #0x90be00
    //     0x90bde8: ldurb           w16, [x1, #-1]
    //     0x90bdec: ldurb           w17, [x0, #-1]
    //     0x90bdf0: and             x16, x17, x16, lsr #2
    //     0x90bdf4: tst             x16, HEAP, lsr #32
    //     0x90bdf8: b.eq            #0x90be00
    //     0x90bdfc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90be00: ldur            x0, [fp, #-8]
    // 0x90be04: r16 = Instance_Heir
    //     0x90be04: add             x16, PP, #0x31, lsl #12  ; [pp+0x318e8] Obj!Heir@e2dcf1
    //     0x90be08: ldr             x16, [x16, #0x8e8]
    // 0x90be0c: StoreField: r0->field_87 = r16
    //     0x90be0c: stur            w16, [x0, #0x87]
    // 0x90be10: r1 = Function '<anonymous closure>': static.
    //     0x90be10: add             x1, PP, #0x31, lsl #12  ; [pp+0x318f0] AnonymousClosure: static (0x90cf4c), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90be14: ldr             x1, [x1, #0x8f0]
    // 0x90be18: r2 = Null
    //     0x90be18: mov             x2, NULL
    // 0x90be1c: r0 = AllocateClosure()
    //     0x90be1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90be20: ldur            x1, [fp, #-8]
    // 0x90be24: ArrayStore: r1[31] = r0  ; List_4
    //     0x90be24: add             x25, x1, #0x8b
    //     0x90be28: str             w0, [x25]
    //     0x90be2c: tbz             w0, #0, #0x90be48
    //     0x90be30: ldurb           w16, [x1, #-1]
    //     0x90be34: ldurb           w17, [x0, #-1]
    //     0x90be38: and             x16, x17, x16, lsr #2
    //     0x90be3c: tst             x16, HEAP, lsr #32
    //     0x90be40: b.eq            #0x90be48
    //     0x90be44: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90be48: ldur            x0, [fp, #-8]
    // 0x90be4c: r16 = Instance_Heir
    //     0x90be4c: add             x16, PP, #0x31, lsl #12  ; [pp+0x318f8] Obj!Heir@e2d9c1
    //     0x90be50: ldr             x16, [x16, #0x8f8]
    // 0x90be54: StoreField: r0->field_8f = r16
    //     0x90be54: stur            w16, [x0, #0x8f]
    // 0x90be58: r1 = Function '<anonymous closure>': static.
    //     0x90be58: add             x1, PP, #0x31, lsl #12  ; [pp+0x31900] AnonymousClosure: static (0x90ce9c), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90be5c: ldr             x1, [x1, #0x900]
    // 0x90be60: r2 = Null
    //     0x90be60: mov             x2, NULL
    // 0x90be64: r0 = AllocateClosure()
    //     0x90be64: bl              #0xec1630  ; AllocateClosureStub
    // 0x90be68: ldur            x1, [fp, #-8]
    // 0x90be6c: ArrayStore: r1[33] = r0  ; List_4
    //     0x90be6c: add             x25, x1, #0x93
    //     0x90be70: str             w0, [x25]
    //     0x90be74: tbz             w0, #0, #0x90be90
    //     0x90be78: ldurb           w16, [x1, #-1]
    //     0x90be7c: ldurb           w17, [x0, #-1]
    //     0x90be80: and             x16, x17, x16, lsr #2
    //     0x90be84: tst             x16, HEAP, lsr #32
    //     0x90be88: b.eq            #0x90be90
    //     0x90be8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90be90: ldur            x0, [fp, #-8]
    // 0x90be94: r16 = Instance_Heir
    //     0x90be94: add             x16, PP, #0x31, lsl #12  ; [pp+0x31908] Obj!Heir@e2d991
    //     0x90be98: ldr             x16, [x16, #0x908]
    // 0x90be9c: StoreField: r0->field_97 = r16
    //     0x90be9c: stur            w16, [x0, #0x97]
    // 0x90bea0: r1 = Function '<anonymous closure>': static.
    //     0x90bea0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31910] AnonymousClosure: static (0x90ce44), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bea4: ldr             x1, [x1, #0x910]
    // 0x90bea8: r2 = Null
    //     0x90bea8: mov             x2, NULL
    // 0x90beac: r0 = AllocateClosure()
    //     0x90beac: bl              #0xec1630  ; AllocateClosureStub
    // 0x90beb0: ldur            x1, [fp, #-8]
    // 0x90beb4: ArrayStore: r1[35] = r0  ; List_4
    //     0x90beb4: add             x25, x1, #0x9b
    //     0x90beb8: str             w0, [x25]
    //     0x90bebc: tbz             w0, #0, #0x90bed8
    //     0x90bec0: ldurb           w16, [x1, #-1]
    //     0x90bec4: ldurb           w17, [x0, #-1]
    //     0x90bec8: and             x16, x17, x16, lsr #2
    //     0x90becc: tst             x16, HEAP, lsr #32
    //     0x90bed0: b.eq            #0x90bed8
    //     0x90bed4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bed8: ldur            x0, [fp, #-8]
    // 0x90bedc: r16 = Instance_Heir
    //     0x90bedc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31918] Obj!Heir@e2d9f1
    //     0x90bee0: ldr             x16, [x16, #0x918]
    // 0x90bee4: StoreField: r0->field_9f = r16
    //     0x90bee4: stur            w16, [x0, #0x9f]
    // 0x90bee8: r1 = Function '<anonymous closure>': static.
    //     0x90bee8: add             x1, PP, #0x31, lsl #12  ; [pp+0x31920] AnonymousClosure: static (0x90cd58), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90beec: ldr             x1, [x1, #0x920]
    // 0x90bef0: r2 = Null
    //     0x90bef0: mov             x2, NULL
    // 0x90bef4: r0 = AllocateClosure()
    //     0x90bef4: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bef8: ldur            x1, [fp, #-8]
    // 0x90befc: ArrayStore: r1[37] = r0  ; List_4
    //     0x90befc: add             x25, x1, #0xa3
    //     0x90bf00: str             w0, [x25]
    //     0x90bf04: tbz             w0, #0, #0x90bf20
    //     0x90bf08: ldurb           w16, [x1, #-1]
    //     0x90bf0c: ldurb           w17, [x0, #-1]
    //     0x90bf10: and             x16, x17, x16, lsr #2
    //     0x90bf14: tst             x16, HEAP, lsr #32
    //     0x90bf18: b.eq            #0x90bf20
    //     0x90bf1c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bf20: ldur            x0, [fp, #-8]
    // 0x90bf24: r16 = Instance_Heir
    //     0x90bf24: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90bf28: ldr             x16, [x16, #0x7d8]
    // 0x90bf2c: StoreField: r0->field_a7 = r16
    //     0x90bf2c: stur            w16, [x0, #0xa7]
    // 0x90bf30: r1 = Function '<anonymous closure>': static.
    //     0x90bf30: add             x1, PP, #0x31, lsl #12  ; [pp+0x31928] AnonymousClosure: static (0x90ccd8), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bf34: ldr             x1, [x1, #0x928]
    // 0x90bf38: r2 = Null
    //     0x90bf38: mov             x2, NULL
    // 0x90bf3c: r0 = AllocateClosure()
    //     0x90bf3c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bf40: ldur            x1, [fp, #-8]
    // 0x90bf44: ArrayStore: r1[39] = r0  ; List_4
    //     0x90bf44: add             x25, x1, #0xab
    //     0x90bf48: str             w0, [x25]
    //     0x90bf4c: tbz             w0, #0, #0x90bf68
    //     0x90bf50: ldurb           w16, [x1, #-1]
    //     0x90bf54: ldurb           w17, [x0, #-1]
    //     0x90bf58: and             x16, x17, x16, lsr #2
    //     0x90bf5c: tst             x16, HEAP, lsr #32
    //     0x90bf60: b.eq            #0x90bf68
    //     0x90bf64: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bf68: ldur            x0, [fp, #-8]
    // 0x90bf6c: r16 = Instance_Heir
    //     0x90bf6c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90bf70: ldr             x16, [x16, #0x7e8]
    // 0x90bf74: StoreField: r0->field_af = r16
    //     0x90bf74: stur            w16, [x0, #0xaf]
    // 0x90bf78: r1 = Function '<anonymous closure>': static.
    //     0x90bf78: add             x1, PP, #0x31, lsl #12  ; [pp+0x31930] AnonymousClosure: static (0x90cb7c), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bf7c: ldr             x1, [x1, #0x930]
    // 0x90bf80: r2 = Null
    //     0x90bf80: mov             x2, NULL
    // 0x90bf84: r0 = AllocateClosure()
    //     0x90bf84: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bf88: ldur            x1, [fp, #-8]
    // 0x90bf8c: ArrayStore: r1[41] = r0  ; List_4
    //     0x90bf8c: add             x25, x1, #0xb3
    //     0x90bf90: str             w0, [x25]
    //     0x90bf94: tbz             w0, #0, #0x90bfb0
    //     0x90bf98: ldurb           w16, [x1, #-1]
    //     0x90bf9c: ldurb           w17, [x0, #-1]
    //     0x90bfa0: and             x16, x17, x16, lsr #2
    //     0x90bfa4: tst             x16, HEAP, lsr #32
    //     0x90bfa8: b.eq            #0x90bfb0
    //     0x90bfac: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bfb0: ldur            x0, [fp, #-8]
    // 0x90bfb4: r16 = Instance_Heir
    //     0x90bfb4: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90bfb8: ldr             x16, [x16, #0xa8]
    // 0x90bfbc: StoreField: r0->field_b7 = r16
    //     0x90bfbc: stur            w16, [x0, #0xb7]
    // 0x90bfc0: r1 = Function '<anonymous closure>': static.
    //     0x90bfc0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31938] AnonymousClosure: static (0x90c988), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90bfc4: ldr             x1, [x1, #0x938]
    // 0x90bfc8: r2 = Null
    //     0x90bfc8: mov             x2, NULL
    // 0x90bfcc: r0 = AllocateClosure()
    //     0x90bfcc: bl              #0xec1630  ; AllocateClosureStub
    // 0x90bfd0: ldur            x1, [fp, #-8]
    // 0x90bfd4: ArrayStore: r1[43] = r0  ; List_4
    //     0x90bfd4: add             x25, x1, #0xbb
    //     0x90bfd8: str             w0, [x25]
    //     0x90bfdc: tbz             w0, #0, #0x90bff8
    //     0x90bfe0: ldurb           w16, [x1, #-1]
    //     0x90bfe4: ldurb           w17, [x0, #-1]
    //     0x90bfe8: and             x16, x17, x16, lsr #2
    //     0x90bfec: tst             x16, HEAP, lsr #32
    //     0x90bff0: b.eq            #0x90bff8
    //     0x90bff4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90bff8: ldur            x0, [fp, #-8]
    // 0x90bffc: r16 = Instance_Heir
    //     0x90bffc: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90c000: ldr             x16, [x16, #0xb0]
    // 0x90c004: StoreField: r0->field_bf = r16
    //     0x90c004: stur            w16, [x0, #0xbf]
    // 0x90c008: r1 = Function '<anonymous closure>': static.
    //     0x90c008: add             x1, PP, #0x31, lsl #12  ; [pp+0x31940] AnonymousClosure: static (0x90c5cc), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90c00c: ldr             x1, [x1, #0x940]
    // 0x90c010: r2 = Null
    //     0x90c010: mov             x2, NULL
    // 0x90c014: r0 = AllocateClosure()
    //     0x90c014: bl              #0xec1630  ; AllocateClosureStub
    // 0x90c018: ldur            x1, [fp, #-8]
    // 0x90c01c: ArrayStore: r1[45] = r0  ; List_4
    //     0x90c01c: add             x25, x1, #0xc3
    //     0x90c020: str             w0, [x25]
    //     0x90c024: tbz             w0, #0, #0x90c040
    //     0x90c028: ldurb           w16, [x1, #-1]
    //     0x90c02c: ldurb           w17, [x0, #-1]
    //     0x90c030: and             x16, x17, x16, lsr #2
    //     0x90c034: tst             x16, HEAP, lsr #32
    //     0x90c038: b.eq            #0x90c040
    //     0x90c03c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90c040: ldur            x0, [fp, #-8]
    // 0x90c044: r16 = Instance_Heir
    //     0x90c044: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b8] Obj!Heir@e2d901
    //     0x90c048: ldr             x16, [x16, #0xb8]
    // 0x90c04c: StoreField: r0->field_c7 = r16
    //     0x90c04c: stur            w16, [x0, #0xc7]
    // 0x90c050: r1 = Function '<anonymous closure>': static.
    //     0x90c050: add             x1, PP, #0x31, lsl #12  ; [pp+0x31948] AnonymousClosure: static (0x90c428), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90c054: ldr             x1, [x1, #0x948]
    // 0x90c058: r2 = Null
    //     0x90c058: mov             x2, NULL
    // 0x90c05c: r0 = AllocateClosure()
    //     0x90c05c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90c060: ldur            x1, [fp, #-8]
    // 0x90c064: ArrayStore: r1[47] = r0  ; List_4
    //     0x90c064: add             x25, x1, #0xcb
    //     0x90c068: str             w0, [x25]
    //     0x90c06c: tbz             w0, #0, #0x90c088
    //     0x90c070: ldurb           w16, [x1, #-1]
    //     0x90c074: ldurb           w17, [x0, #-1]
    //     0x90c078: and             x16, x17, x16, lsr #2
    //     0x90c07c: tst             x16, HEAP, lsr #32
    //     0x90c080: b.eq            #0x90c088
    //     0x90c084: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90c088: ldur            x0, [fp, #-8]
    // 0x90c08c: r16 = Instance_Heir
    //     0x90c08c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31950] Obj!Heir@e2da81
    //     0x90c090: ldr             x16, [x16, #0x950]
    // 0x90c094: StoreField: r0->field_cf = r16
    //     0x90c094: stur            w16, [x0, #0xcf]
    // 0x90c098: r1 = Function '<anonymous closure>': static.
    //     0x90c098: add             x1, PP, #0x31, lsl #12  ; [pp+0x31958] AnonymousClosure: static (0x90c36c), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90c09c: ldr             x1, [x1, #0x958]
    // 0x90c0a0: r2 = Null
    //     0x90c0a0: mov             x2, NULL
    // 0x90c0a4: r0 = AllocateClosure()
    //     0x90c0a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x90c0a8: ldur            x1, [fp, #-8]
    // 0x90c0ac: ArrayStore: r1[49] = r0  ; List_4
    //     0x90c0ac: add             x25, x1, #0xd3
    //     0x90c0b0: str             w0, [x25]
    //     0x90c0b4: tbz             w0, #0, #0x90c0d0
    //     0x90c0b8: ldurb           w16, [x1, #-1]
    //     0x90c0bc: ldurb           w17, [x0, #-1]
    //     0x90c0c0: and             x16, x17, x16, lsr #2
    //     0x90c0c4: tst             x16, HEAP, lsr #32
    //     0x90c0c8: b.eq            #0x90c0d0
    //     0x90c0cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90c0d0: ldur            x0, [fp, #-8]
    // 0x90c0d4: r16 = Instance_Heir
    //     0x90c0d4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31960] Obj!Heir@e2d8d1
    //     0x90c0d8: ldr             x16, [x16, #0x960]
    // 0x90c0dc: StoreField: r0->field_d7 = r16
    //     0x90c0dc: stur            w16, [x0, #0xd7]
    // 0x90c0e0: r1 = Function '<anonymous closure>': static.
    //     0x90c0e0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31968] AnonymousClosure: static (0x90c140), in [package:waris/src/shares.dart] Shares::_shares (0x90b9a4)
    //     0x90c0e4: ldr             x1, [x1, #0x968]
    // 0x90c0e8: r2 = Null
    //     0x90c0e8: mov             x2, NULL
    // 0x90c0ec: r0 = AllocateClosure()
    //     0x90c0ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x90c0f0: ldur            x1, [fp, #-8]
    // 0x90c0f4: ArrayStore: r1[51] = r0  ; List_4
    //     0x90c0f4: add             x25, x1, #0xdb
    //     0x90c0f8: str             w0, [x25]
    //     0x90c0fc: tbz             w0, #0, #0x90c118
    //     0x90c100: ldurb           w16, [x1, #-1]
    //     0x90c104: ldurb           w17, [x0, #-1]
    //     0x90c108: and             x16, x17, x16, lsr #2
    //     0x90c10c: tst             x16, HEAP, lsr #32
    //     0x90c110: b.eq            #0x90c118
    //     0x90c114: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x90c118: r16 = <Heir, (dynamic this, Map<Heir, int>) => Share>
    //     0x90c118: add             x16, PP, #0x31, lsl #12  ; [pp+0x31970] TypeArguments: <Heir, (dynamic this, Map<Heir, int>) => Share>
    //     0x90c11c: ldr             x16, [x16, #0x970]
    // 0x90c120: ldur            lr, [fp, #-8]
    // 0x90c124: stp             lr, x16, [SP]
    // 0x90c128: r0 = Map._fromLiteral()
    //     0x90c128: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90c12c: LeaveFrame
    //     0x90c12c: mov             SP, fp
    //     0x90c130: ldp             fp, lr, [SP], #0x10
    // 0x90c134: ret
    //     0x90c134: ret             
    // 0x90c138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c13c: b               #0x90b9bc
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90c140, size: 0x220
    // 0x90c140: EnterFrame
    //     0x90c140: stp             fp, lr, [SP, #-0x10]!
    //     0x90c144: mov             fp, SP
    // 0x90c148: AllocStack(0x8)
    //     0x90c148: sub             SP, SP, #8
    // 0x90c14c: SetupParameters()
    //     0x90c14c: movz            x0, #0x1a
    // 0x90c14c: r0 = 26
    // 0x90c150: CheckStackOverflow
    //     0x90c150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c154: cmp             SP, x16
    //     0x90c158: b.ls            #0x90c358
    // 0x90c15c: mov             x2, x0
    // 0x90c160: r1 = <Heir>
    //     0x90c160: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c164: ldr             x1, [x1, #0xc0]
    // 0x90c168: r0 = AllocateArray()
    //     0x90c168: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c16c: stur            x0, [fp, #-8]
    // 0x90c170: r16 = Instance_Heir
    //     0x90c170: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90c174: ldr             x16, [x16, #0x7d0]
    // 0x90c178: StoreField: r0->field_f = r16
    //     0x90c178: stur            w16, [x0, #0xf]
    // 0x90c17c: r16 = Instance_Heir
    //     0x90c17c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90c180: ldr             x16, [x16, #0x7e0]
    // 0x90c184: StoreField: r0->field_13 = r16
    //     0x90c184: stur            w16, [x0, #0x13]
    // 0x90c188: r16 = Instance_Heir
    //     0x90c188: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90c18c: ldr             x16, [x16, #0x838]
    // 0x90c190: ArrayStore: r0[0] = r16  ; List_4
    //     0x90c190: stur            w16, [x0, #0x17]
    // 0x90c194: r16 = Instance_Heir
    //     0x90c194: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90c198: ldr             x16, [x16, #0x88]
    // 0x90c19c: StoreField: r0->field_1b = r16
    //     0x90c19c: stur            w16, [x0, #0x1b]
    // 0x90c1a0: r16 = Instance_Heir
    //     0x90c1a0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90c1a4: ldr             x16, [x16, #0x90]
    // 0x90c1a8: StoreField: r0->field_1f = r16
    //     0x90c1a8: stur            w16, [x0, #0x1f]
    // 0x90c1ac: r16 = Instance_Heir
    //     0x90c1ac: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90c1b0: ldr             x16, [x16, #0x98]
    // 0x90c1b4: StoreField: r0->field_23 = r16
    //     0x90c1b4: stur            w16, [x0, #0x23]
    // 0x90c1b8: r16 = Instance_Heir
    //     0x90c1b8: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x90c1bc: ldr             x16, [x16, #0xa0]
    // 0x90c1c0: StoreField: r0->field_27 = r16
    //     0x90c1c0: stur            w16, [x0, #0x27]
    // 0x90c1c4: r16 = Instance_Heir
    //     0x90c1c4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90c1c8: ldr             x16, [x16, #0x868]
    // 0x90c1cc: StoreField: r0->field_2b = r16
    //     0x90c1cc: stur            w16, [x0, #0x2b]
    // 0x90c1d0: r16 = Instance_Heir
    //     0x90c1d0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x90c1d4: ldr             x16, [x16, #0x878]
    // 0x90c1d8: StoreField: r0->field_2f = r16
    //     0x90c1d8: stur            w16, [x0, #0x2f]
    // 0x90c1dc: r16 = Instance_Heir
    //     0x90c1dc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31888] Obj!Heir@e2db71
    //     0x90c1e0: ldr             x16, [x16, #0x888]
    // 0x90c1e4: StoreField: r0->field_33 = r16
    //     0x90c1e4: stur            w16, [x0, #0x33]
    // 0x90c1e8: r16 = Instance_Heir
    //     0x90c1e8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31898] Obj!Heir@e2db41
    //     0x90c1ec: ldr             x16, [x16, #0x898]
    // 0x90c1f0: StoreField: r0->field_37 = r16
    //     0x90c1f0: stur            w16, [x0, #0x37]
    // 0x90c1f4: r16 = Instance_Heir
    //     0x90c1f4: add             x16, PP, #0x31, lsl #12  ; [pp+0x318a8] Obj!Heir@e2db11
    //     0x90c1f8: ldr             x16, [x16, #0x8a8]
    // 0x90c1fc: StoreField: r0->field_3b = r16
    //     0x90c1fc: stur            w16, [x0, #0x3b]
    // 0x90c200: r16 = Instance_Heir
    //     0x90c200: add             x16, PP, #0x31, lsl #12  ; [pp+0x318b8] Obj!Heir@e2dae1
    //     0x90c204: ldr             x16, [x16, #0x8b8]
    // 0x90c208: StoreField: r0->field_3f = r16
    //     0x90c208: stur            w16, [x0, #0x3f]
    // 0x90c20c: r1 = <Heir>
    //     0x90c20c: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c210: ldr             x1, [x1, #0xc0]
    // 0x90c214: r0 = AllocateGrowableArray()
    //     0x90c214: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c218: mov             x1, x0
    // 0x90c21c: ldur            x0, [fp, #-8]
    // 0x90c220: StoreField: r1->field_f = r0
    //     0x90c220: stur            w0, [x1, #0xf]
    // 0x90c224: r0 = 26
    //     0x90c224: movz            x0, #0x1a
    // 0x90c228: StoreField: r1->field_b = r0
    //     0x90c228: stur            w0, [x1, #0xb]
    // 0x90c22c: mov             x2, x1
    // 0x90c230: ldr             x1, [fp, #0x10]
    // 0x90c234: r0 = HeirsExt.search()
    //     0x90c234: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90c238: stur            x0, [fp, #-8]
    // 0x90c23c: LoadField: r1 = r0->field_b
    //     0x90c23c: ldur            w1, [x0, #0xb]
    // 0x90c240: cbz             w1, #0x90c264
    // 0x90c244: r0 = _$Mahjub()
    //     0x90c244: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90c248: mov             x1, x0
    // 0x90c24c: ldur            x0, [fp, #-8]
    // 0x90c250: StoreField: r1->field_7 = r0
    //     0x90c250: stur            w0, [x1, #7]
    // 0x90c254: mov             x0, x1
    // 0x90c258: LeaveFrame
    //     0x90c258: mov             SP, fp
    //     0x90c25c: ldp             fp, lr, [SP], #0x10
    // 0x90c260: ret
    //     0x90c260: ret             
    // 0x90c264: r0 = 4
    //     0x90c264: movz            x0, #0x4
    // 0x90c268: mov             x2, x0
    // 0x90c26c: r1 = Null
    //     0x90c26c: mov             x1, NULL
    // 0x90c270: r0 = AllocateArray()
    //     0x90c270: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c274: stur            x0, [fp, #-8]
    // 0x90c278: r16 = Instance_Heir
    //     0x90c278: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90c27c: ldr             x16, [x16, #0xb0]
    // 0x90c280: StoreField: r0->field_f = r16
    //     0x90c280: stur            w16, [x0, #0xf]
    // 0x90c284: r16 = Instance_Heir
    //     0x90c284: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90c288: ldr             x16, [x16, #0x7d8]
    // 0x90c28c: StoreField: r0->field_13 = r16
    //     0x90c28c: stur            w16, [x0, #0x13]
    // 0x90c290: r1 = <Heir>
    //     0x90c290: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c294: ldr             x1, [x1, #0xc0]
    // 0x90c298: r0 = AllocateGrowableArray()
    //     0x90c298: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c29c: mov             x1, x0
    // 0x90c2a0: ldur            x0, [fp, #-8]
    // 0x90c2a4: StoreField: r1->field_f = r0
    //     0x90c2a4: stur            w0, [x1, #0xf]
    // 0x90c2a8: r0 = 4
    //     0x90c2a8: movz            x0, #0x4
    // 0x90c2ac: StoreField: r1->field_b = r0
    //     0x90c2ac: stur            w0, [x1, #0xb]
    // 0x90c2b0: mov             x2, x1
    // 0x90c2b4: ldr             x1, [fp, #0x10]
    // 0x90c2b8: r0 = HeirsExt.hasEveryOf()
    //     0x90c2b8: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90c2bc: tbnz            w0, #4, #0x90c2d4
    // 0x90c2c0: r0 = Instance__$Mahjub
    //     0x90c2c0: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90c2c4: ldr             x0, [x0, #0x978]
    // 0x90c2c8: LeaveFrame
    //     0x90c2c8: mov             SP, fp
    //     0x90c2cc: ldp             fp, lr, [SP], #0x10
    // 0x90c2d0: ret
    //     0x90c2d0: ret             
    // 0x90c2d4: r0 = 4
    //     0x90c2d4: movz            x0, #0x4
    // 0x90c2d8: mov             x2, x0
    // 0x90c2dc: r1 = Null
    //     0x90c2dc: mov             x1, NULL
    // 0x90c2e0: r0 = AllocateArray()
    //     0x90c2e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c2e4: stur            x0, [fp, #-8]
    // 0x90c2e8: r16 = Instance_Heir
    //     0x90c2e8: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90c2ec: ldr             x16, [x16, #0xb0]
    // 0x90c2f0: StoreField: r0->field_f = r16
    //     0x90c2f0: stur            w16, [x0, #0xf]
    // 0x90c2f4: r16 = Instance_Heir
    //     0x90c2f4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90c2f8: ldr             x16, [x16, #0x7d8]
    // 0x90c2fc: StoreField: r0->field_13 = r16
    //     0x90c2fc: stur            w16, [x0, #0x13]
    // 0x90c300: r1 = <Heir>
    //     0x90c300: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c304: ldr             x1, [x1, #0xc0]
    // 0x90c308: r0 = AllocateGrowableArray()
    //     0x90c308: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c30c: mov             x1, x0
    // 0x90c310: ldur            x0, [fp, #-8]
    // 0x90c314: StoreField: r1->field_f = r0
    //     0x90c314: stur            w0, [x1, #0xf]
    // 0x90c318: r0 = 4
    //     0x90c318: movz            x0, #0x4
    // 0x90c31c: StoreField: r1->field_b = r0
    //     0x90c31c: stur            w0, [x1, #0xb]
    // 0x90c320: mov             x2, x1
    // 0x90c324: ldr             x1, [fp, #0x10]
    // 0x90c328: r0 = HeirsExt.hasEveryOf()
    //     0x90c328: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90c32c: tbnz            w0, #4, #0x90c344
    // 0x90c330: r0 = Instance__$Mahjub
    //     0x90c330: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90c334: ldr             x0, [x0, #0x980]
    // 0x90c338: LeaveFrame
    //     0x90c338: mov             SP, fp
    //     0x90c33c: ldp             fp, lr, [SP], #0x10
    // 0x90c340: ret
    //     0x90c340: ret             
    // 0x90c344: r0 = Instance__$Ashobah
    //     0x90c344: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90c348: ldr             x0, [x0, #0x988]
    // 0x90c34c: LeaveFrame
    //     0x90c34c: mov             SP, fp
    //     0x90c350: ldp             fp, lr, [SP], #0x10
    // 0x90c354: ret
    //     0x90c354: ret             
    // 0x90c358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c358: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c35c: b               #0x90c15c
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90c36c, size: 0xbc
    // 0x90c36c: EnterFrame
    //     0x90c36c: stp             fp, lr, [SP, #-0x10]!
    //     0x90c370: mov             fp, SP
    // 0x90c374: AllocStack(0x8)
    //     0x90c374: sub             SP, SP, #8
    // 0x90c378: SetupParameters()
    //     0x90c378: movz            x0, #0x8
    // 0x90c378: r0 = 8
    // 0x90c37c: CheckStackOverflow
    //     0x90c37c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c380: cmp             SP, x16
    //     0x90c384: b.ls            #0x90c420
    // 0x90c388: mov             x2, x0
    // 0x90c38c: r1 = Null
    //     0x90c38c: mov             x1, NULL
    // 0x90c390: r0 = AllocateArray()
    //     0x90c390: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c394: stur            x0, [fp, #-8]
    // 0x90c398: r16 = Instance_Heir
    //     0x90c398: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90c39c: ldr             x16, [x16, #0x7d0]
    // 0x90c3a0: StoreField: r0->field_f = r16
    //     0x90c3a0: stur            w16, [x0, #0xf]
    // 0x90c3a4: r16 = Instance_Heir
    //     0x90c3a4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90c3a8: ldr             x16, [x16, #0x7e0]
    // 0x90c3ac: StoreField: r0->field_13 = r16
    //     0x90c3ac: stur            w16, [x0, #0x13]
    // 0x90c3b0: r16 = Instance_Heir
    //     0x90c3b0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90c3b4: ldr             x16, [x16, #0x7d8]
    // 0x90c3b8: ArrayStore: r0[0] = r16  ; List_4
    //     0x90c3b8: stur            w16, [x0, #0x17]
    // 0x90c3bc: r16 = Instance_Heir
    //     0x90c3bc: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90c3c0: ldr             x16, [x16, #0x7e8]
    // 0x90c3c4: StoreField: r0->field_1b = r16
    //     0x90c3c4: stur            w16, [x0, #0x1b]
    // 0x90c3c8: r1 = <Heir>
    //     0x90c3c8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c3cc: ldr             x1, [x1, #0xc0]
    // 0x90c3d0: r0 = AllocateGrowableArray()
    //     0x90c3d0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c3d4: mov             x1, x0
    // 0x90c3d8: ldur            x0, [fp, #-8]
    // 0x90c3dc: StoreField: r1->field_f = r0
    //     0x90c3dc: stur            w0, [x1, #0xf]
    // 0x90c3e0: r0 = 8
    //     0x90c3e0: movz            x0, #0x8
    // 0x90c3e4: StoreField: r1->field_b = r0
    //     0x90c3e4: stur            w0, [x1, #0xb]
    // 0x90c3e8: mov             x2, x1
    // 0x90c3ec: ldr             x1, [fp, #0x10]
    // 0x90c3f0: r0 = HeirsExt.hasAnyOf()
    //     0x90c3f0: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90c3f4: tbnz            w0, #4, #0x90c40c
    // 0x90c3f8: r0 = Instance__$Furudh
    //     0x90c3f8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31990] Obj!_$Furudh@e0be61
    //     0x90c3fc: ldr             x0, [x0, #0x990]
    // 0x90c400: LeaveFrame
    //     0x90c400: mov             SP, fp
    //     0x90c404: ldp             fp, lr, [SP], #0x10
    // 0x90c408: ret
    //     0x90c408: ret             
    // 0x90c40c: r0 = Instance__$Furudh
    //     0x90c40c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31998] Obj!_$Furudh@e0be21
    //     0x90c410: ldr             x0, [x0, #0x998]
    // 0x90c414: LeaveFrame
    //     0x90c414: mov             SP, fp
    //     0x90c418: ldp             fp, lr, [SP], #0x10
    // 0x90c41c: ret
    //     0x90c41c: ret             
    // 0x90c420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c420: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c424: b               #0x90c388
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90c428, size: 0x110
    // 0x90c428: EnterFrame
    //     0x90c428: stp             fp, lr, [SP, #-0x10]!
    //     0x90c42c: mov             fp, SP
    // 0x90c430: AllocStack(0x8)
    //     0x90c430: sub             SP, SP, #8
    // 0x90c434: SetupParameters()
    //     0x90c434: movz            x0, #0xc
    // 0x90c434: r0 = 12
    // 0x90c438: CheckStackOverflow
    //     0x90c438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c43c: cmp             SP, x16
    //     0x90c440: b.ls            #0x90c530
    // 0x90c444: mov             x2, x0
    // 0x90c448: r1 = Null
    //     0x90c448: mov             x1, NULL
    // 0x90c44c: r0 = AllocateArray()
    //     0x90c44c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c450: stur            x0, [fp, #-8]
    // 0x90c454: r16 = Instance_Heir
    //     0x90c454: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90c458: ldr             x16, [x16, #0x7d0]
    // 0x90c45c: StoreField: r0->field_f = r16
    //     0x90c45c: stur            w16, [x0, #0xf]
    // 0x90c460: r16 = Instance_Heir
    //     0x90c460: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90c464: ldr             x16, [x16, #0x7e0]
    // 0x90c468: StoreField: r0->field_13 = r16
    //     0x90c468: stur            w16, [x0, #0x13]
    // 0x90c46c: r16 = Instance_Heir
    //     0x90c46c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90c470: ldr             x16, [x16, #0x838]
    // 0x90c474: ArrayStore: r0[0] = r16  ; List_4
    //     0x90c474: stur            w16, [x0, #0x17]
    // 0x90c478: r16 = Instance_Heir
    //     0x90c478: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90c47c: ldr             x16, [x16, #0x88]
    // 0x90c480: StoreField: r0->field_1b = r16
    //     0x90c480: stur            w16, [x0, #0x1b]
    // 0x90c484: r16 = Instance_Heir
    //     0x90c484: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90c488: ldr             x16, [x16, #0x7d8]
    // 0x90c48c: StoreField: r0->field_1f = r16
    //     0x90c48c: stur            w16, [x0, #0x1f]
    // 0x90c490: r16 = Instance_Heir
    //     0x90c490: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90c494: ldr             x16, [x16, #0x7e8]
    // 0x90c498: StoreField: r0->field_23 = r16
    //     0x90c498: stur            w16, [x0, #0x23]
    // 0x90c49c: r1 = <Heir>
    //     0x90c49c: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c4a0: ldr             x1, [x1, #0xc0]
    // 0x90c4a4: r0 = AllocateGrowableArray()
    //     0x90c4a4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c4a8: mov             x1, x0
    // 0x90c4ac: ldur            x0, [fp, #-8]
    // 0x90c4b0: StoreField: r1->field_f = r0
    //     0x90c4b0: stur            w0, [x1, #0xf]
    // 0x90c4b4: r0 = 12
    //     0x90c4b4: movz            x0, #0xc
    // 0x90c4b8: StoreField: r1->field_b = r0
    //     0x90c4b8: stur            w0, [x1, #0xb]
    // 0x90c4bc: mov             x2, x1
    // 0x90c4c0: ldr             x1, [fp, #0x10]
    // 0x90c4c4: r0 = HeirsExt.search()
    //     0x90c4c4: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90c4c8: stur            x0, [fp, #-8]
    // 0x90c4cc: LoadField: r1 = r0->field_b
    //     0x90c4cc: ldur            w1, [x0, #0xb]
    // 0x90c4d0: cbz             w1, #0x90c4f4
    // 0x90c4d4: r0 = _$Mahjub()
    //     0x90c4d4: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90c4d8: mov             x1, x0
    // 0x90c4dc: ldur            x0, [fp, #-8]
    // 0x90c4e0: StoreField: r1->field_7 = r0
    //     0x90c4e0: stur            w0, [x1, #7]
    // 0x90c4e4: mov             x0, x1
    // 0x90c4e8: LeaveFrame
    //     0x90c4e8: mov             SP, fp
    //     0x90c4ec: ldp             fp, lr, [SP], #0x10
    // 0x90c4f0: ret
    //     0x90c4f0: ret             
    // 0x90c4f4: ldr             x1, [fp, #0x10]
    // 0x90c4f8: r2 = Instance_Heir
    //     0x90c4f8: add             x2, PP, #0x28, lsl #12  ; [pp+0x280b8] Obj!Heir@e2d901
    //     0x90c4fc: ldr             x2, [x2, #0xb8]
    // 0x90c500: r0 = HeirsExt.hasOneOf()
    //     0x90c500: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90c504: tbnz            w0, #4, #0x90c51c
    // 0x90c508: r0 = Instance__$Furudh
    //     0x90c508: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90c50c: ldr             x0, [x0, #0x9a0]
    // 0x90c510: LeaveFrame
    //     0x90c510: mov             SP, fp
    //     0x90c514: ldp             fp, lr, [SP], #0x10
    // 0x90c518: ret
    //     0x90c518: ret             
    // 0x90c51c: r0 = Instance__$Furudh
    //     0x90c51c: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a8] Obj!_$Furudh@e0be81
    //     0x90c520: ldr             x0, [x0, #0x9a8]
    // 0x90c524: LeaveFrame
    //     0x90c524: mov             SP, fp
    //     0x90c528: ldp             fp, lr, [SP], #0x10
    // 0x90c52c: ret
    //     0x90c52c: ret             
    // 0x90c530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c534: b               #0x90c444
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90c5cc, size: 0x2dc
    // 0x90c5cc: EnterFrame
    //     0x90c5cc: stp             fp, lr, [SP, #-0x10]!
    //     0x90c5d0: mov             fp, SP
    // 0x90c5d4: AllocStack(0x8)
    //     0x90c5d4: sub             SP, SP, #8
    // 0x90c5d8: SetupParameters()
    //     0x90c5d8: movz            x0, #0x8
    // 0x90c5d8: r0 = 8
    // 0x90c5dc: CheckStackOverflow
    //     0x90c5dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c5e0: cmp             SP, x16
    //     0x90c5e4: b.ls            #0x90c8a0
    // 0x90c5e8: mov             x2, x0
    // 0x90c5ec: r1 = Null
    //     0x90c5ec: mov             x1, NULL
    // 0x90c5f0: r0 = AllocateArray()
    //     0x90c5f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c5f4: stur            x0, [fp, #-8]
    // 0x90c5f8: r16 = Instance_Heir
    //     0x90c5f8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90c5fc: ldr             x16, [x16, #0x7d0]
    // 0x90c600: StoreField: r0->field_f = r16
    //     0x90c600: stur            w16, [x0, #0xf]
    // 0x90c604: r16 = Instance_Heir
    //     0x90c604: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90c608: ldr             x16, [x16, #0x7e0]
    // 0x90c60c: StoreField: r0->field_13 = r16
    //     0x90c60c: stur            w16, [x0, #0x13]
    // 0x90c610: r16 = Instance_Heir
    //     0x90c610: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90c614: ldr             x16, [x16, #0x838]
    // 0x90c618: ArrayStore: r0[0] = r16  ; List_4
    //     0x90c618: stur            w16, [x0, #0x17]
    // 0x90c61c: r16 = Instance_Heir
    //     0x90c61c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90c620: ldr             x16, [x16, #0x90]
    // 0x90c624: StoreField: r0->field_1b = r16
    //     0x90c624: stur            w16, [x0, #0x1b]
    // 0x90c628: r1 = <Heir>
    //     0x90c628: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c62c: ldr             x1, [x1, #0xc0]
    // 0x90c630: r0 = AllocateGrowableArray()
    //     0x90c630: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c634: mov             x1, x0
    // 0x90c638: ldur            x0, [fp, #-8]
    // 0x90c63c: StoreField: r1->field_f = r0
    //     0x90c63c: stur            w0, [x1, #0xf]
    // 0x90c640: r0 = 8
    //     0x90c640: movz            x0, #0x8
    // 0x90c644: StoreField: r1->field_b = r0
    //     0x90c644: stur            w0, [x1, #0xb]
    // 0x90c648: mov             x2, x1
    // 0x90c64c: ldr             x1, [fp, #0x10]
    // 0x90c650: r0 = HeirsExt.search()
    //     0x90c650: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90c654: stur            x0, [fp, #-8]
    // 0x90c658: LoadField: r1 = r0->field_b
    //     0x90c658: ldur            w1, [x0, #0xb]
    // 0x90c65c: cbz             w1, #0x90c680
    // 0x90c660: r0 = _$Mahjub()
    //     0x90c660: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90c664: mov             x1, x0
    // 0x90c668: ldur            x0, [fp, #-8]
    // 0x90c66c: StoreField: r1->field_7 = r0
    //     0x90c66c: stur            w0, [x1, #7]
    // 0x90c670: mov             x0, x1
    // 0x90c674: LeaveFrame
    //     0x90c674: mov             SP, fp
    //     0x90c678: ldp             fp, lr, [SP], #0x10
    // 0x90c67c: ret
    //     0x90c67c: ret             
    // 0x90c680: ldr             x1, [fp, #0x10]
    // 0x90c684: r2 = Instance_Heir
    //     0x90c684: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90c688: ldr             x2, [x2, #0xa8]
    // 0x90c68c: r0 = HeirsExt.hasOneOf()
    //     0x90c68c: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90c690: tbnz            w0, #4, #0x90c6bc
    // 0x90c694: ldr             x1, [fp, #0x10]
    // 0x90c698: r2 = Instance_Heir
    //     0x90c698: add             x2, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90c69c: ldr             x2, [x2, #0x7d8]
    // 0x90c6a0: r0 = HeirsExt.has()
    //     0x90c6a0: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90c6a4: tbnz            w0, #4, #0x90c6bc
    // 0x90c6a8: r0 = Instance__$Mahjub
    //     0x90c6a8: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90c6ac: ldr             x0, [x0, #0x9b0]
    // 0x90c6b0: LeaveFrame
    //     0x90c6b0: mov             SP, fp
    //     0x90c6b4: ldp             fp, lr, [SP], #0x10
    // 0x90c6b8: ret
    //     0x90c6b8: ret             
    // 0x90c6bc: ldr             x1, [fp, #0x10]
    // 0x90c6c0: r2 = Instance_Heir
    //     0x90c6c0: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90c6c4: ldr             x2, [x2, #0xa8]
    // 0x90c6c8: r0 = HeirsExt.hasOneOf()
    //     0x90c6c8: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90c6cc: tbnz            w0, #4, #0x90c6f8
    // 0x90c6d0: ldr             x1, [fp, #0x10]
    // 0x90c6d4: r2 = Instance_Heir
    //     0x90c6d4: add             x2, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90c6d8: ldr             x2, [x2, #0x7e8]
    // 0x90c6dc: r0 = HeirsExt.has()
    //     0x90c6dc: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90c6e0: tbnz            w0, #4, #0x90c6f8
    // 0x90c6e4: r0 = Instance__$Mahjub
    //     0x90c6e4: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90c6e8: ldr             x0, [x0, #0x9b8]
    // 0x90c6ec: LeaveFrame
    //     0x90c6ec: mov             SP, fp
    //     0x90c6f0: ldp             fp, lr, [SP], #0x10
    // 0x90c6f4: ret
    //     0x90c6f4: ret             
    // 0x90c6f8: ldr             x1, [fp, #0x10]
    // 0x90c6fc: r2 = Instance_Heir
    //     0x90c6fc: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90c700: ldr             x2, [x2, #0xa8]
    // 0x90c704: r0 = HeirsExt.hasManyOf()
    //     0x90c704: bl              #0x90c8dc  ; [package:waris/src/heirs.dart] ::HeirsExt.hasManyOf
    // 0x90c708: tbnz            w0, #4, #0x90c734
    // 0x90c70c: ldr             x1, [fp, #0x10]
    // 0x90c710: r2 = Instance_Heir
    //     0x90c710: add             x2, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90c714: ldr             x2, [x2, #0x98]
    // 0x90c718: r0 = HeirsExt.notHas()
    //     0x90c718: bl              #0x90c8a8  ; [package:waris/src/heirs.dart] ::HeirsExt.notHas
    // 0x90c71c: tbnz            w0, #4, #0x90c734
    // 0x90c720: r0 = Instance__$Mahjub
    //     0x90c720: add             x0, PP, #0x31, lsl #12  ; [pp+0x319c0] Obj!_$Mahjub@e0bd31
    //     0x90c724: ldr             x0, [x0, #0x9c0]
    // 0x90c728: LeaveFrame
    //     0x90c728: mov             SP, fp
    //     0x90c72c: ldp             fp, lr, [SP], #0x10
    // 0x90c730: ret
    //     0x90c730: ret             
    // 0x90c734: r0 = 4
    //     0x90c734: movz            x0, #0x4
    // 0x90c738: mov             x2, x0
    // 0x90c73c: r1 = Null
    //     0x90c73c: mov             x1, NULL
    // 0x90c740: r0 = AllocateArray()
    //     0x90c740: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c744: stur            x0, [fp, #-8]
    // 0x90c748: r16 = Instance_Heir
    //     0x90c748: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90c74c: ldr             x16, [x16, #0x98]
    // 0x90c750: StoreField: r0->field_f = r16
    //     0x90c750: stur            w16, [x0, #0xf]
    // 0x90c754: r16 = Instance_Heir
    //     0x90c754: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90c758: ldr             x16, [x16, #0x88]
    // 0x90c75c: StoreField: r0->field_13 = r16
    //     0x90c75c: stur            w16, [x0, #0x13]
    // 0x90c760: r1 = <Heir>
    //     0x90c760: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c764: ldr             x1, [x1, #0xc0]
    // 0x90c768: r0 = AllocateGrowableArray()
    //     0x90c768: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c76c: mov             x1, x0
    // 0x90c770: ldur            x0, [fp, #-8]
    // 0x90c774: StoreField: r1->field_f = r0
    //     0x90c774: stur            w0, [x1, #0xf]
    // 0x90c778: r0 = 4
    //     0x90c778: movz            x0, #0x4
    // 0x90c77c: StoreField: r1->field_b = r0
    //     0x90c77c: stur            w0, [x1, #0xb]
    // 0x90c780: mov             x2, x1
    // 0x90c784: ldr             x1, [fp, #0x10]
    // 0x90c788: r0 = HeirsExt.hasAnyOf()
    //     0x90c788: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90c78c: tbnz            w0, #4, #0x90c7a4
    // 0x90c790: r0 = Instance__$Ashobah
    //     0x90c790: add             x0, PP, #0x31, lsl #12  ; [pp+0x319c8] Obj!_$Ashobah@e0bdd1
    //     0x90c794: ldr             x0, [x0, #0x9c8]
    // 0x90c798: LeaveFrame
    //     0x90c798: mov             SP, fp
    //     0x90c79c: ldp             fp, lr, [SP], #0x10
    // 0x90c7a0: ret
    //     0x90c7a0: ret             
    // 0x90c7a4: r0 = 4
    //     0x90c7a4: movz            x0, #0x4
    // 0x90c7a8: mov             x2, x0
    // 0x90c7ac: r1 = Null
    //     0x90c7ac: mov             x1, NULL
    // 0x90c7b0: r0 = AllocateArray()
    //     0x90c7b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c7b4: stur            x0, [fp, #-8]
    // 0x90c7b8: r16 = Instance_Heir
    //     0x90c7b8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90c7bc: ldr             x16, [x16, #0x7d8]
    // 0x90c7c0: StoreField: r0->field_f = r16
    //     0x90c7c0: stur            w16, [x0, #0xf]
    // 0x90c7c4: r16 = Instance_Heir
    //     0x90c7c4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90c7c8: ldr             x16, [x16, #0x7e8]
    // 0x90c7cc: StoreField: r0->field_13 = r16
    //     0x90c7cc: stur            w16, [x0, #0x13]
    // 0x90c7d0: r1 = <Heir>
    //     0x90c7d0: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c7d4: ldr             x1, [x1, #0xc0]
    // 0x90c7d8: r0 = AllocateGrowableArray()
    //     0x90c7d8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c7dc: mov             x1, x0
    // 0x90c7e0: ldur            x0, [fp, #-8]
    // 0x90c7e4: StoreField: r1->field_f = r0
    //     0x90c7e4: stur            w0, [x1, #0xf]
    // 0x90c7e8: r0 = 4
    //     0x90c7e8: movz            x0, #0x4
    // 0x90c7ec: StoreField: r1->field_b = r0
    //     0x90c7ec: stur            w0, [x1, #0xb]
    // 0x90c7f0: mov             x2, x1
    // 0x90c7f4: ldr             x1, [fp, #0x10]
    // 0x90c7f8: r0 = HeirsExt.hasAnyOf()
    //     0x90c7f8: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90c7fc: tbnz            w0, #4, #0x90c814
    // 0x90c800: r0 = Instance__$Ashobah
    //     0x90c800: add             x0, PP, #0x31, lsl #12  ; [pp+0x319d0] Obj!_$Ashobah@e0bdc1
    //     0x90c804: ldr             x0, [x0, #0x9d0]
    // 0x90c808: LeaveFrame
    //     0x90c808: mov             SP, fp
    //     0x90c80c: ldp             fp, lr, [SP], #0x10
    // 0x90c810: ret
    //     0x90c810: ret             
    // 0x90c814: ldr             x1, [fp, #0x10]
    // 0x90c818: r2 = Instance_Heir
    //     0x90c818: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90c81c: ldr             x2, [x2, #0xa8]
    // 0x90c820: r0 = HeirsExt.hasOneOf()
    //     0x90c820: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90c824: tbnz            w0, #4, #0x90c83c
    // 0x90c828: r0 = Instance__$Furudh
    //     0x90c828: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90c82c: ldr             x0, [x0, #0x9a0]
    // 0x90c830: LeaveFrame
    //     0x90c830: mov             SP, fp
    //     0x90c834: ldp             fp, lr, [SP], #0x10
    // 0x90c838: ret
    //     0x90c838: ret             
    // 0x90c83c: ldr             x1, [fp, #0x10]
    // 0x90c840: r2 = Instance_Heir
    //     0x90c840: add             x2, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90c844: ldr             x2, [x2, #0xb0]
    // 0x90c848: r0 = HeirsExt.hasManyOf()
    //     0x90c848: bl              #0x90c8dc  ; [package:waris/src/heirs.dart] ::HeirsExt.hasManyOf
    // 0x90c84c: tbnz            w0, #4, #0x90c864
    // 0x90c850: r0 = Instance__$Furudh
    //     0x90c850: add             x0, PP, #0x31, lsl #12  ; [pp+0x319d8] Obj!_$Furudh@e0bec1
    //     0x90c854: ldr             x0, [x0, #0x9d8]
    // 0x90c858: LeaveFrame
    //     0x90c858: mov             SP, fp
    //     0x90c85c: ldp             fp, lr, [SP], #0x10
    // 0x90c860: ret
    //     0x90c860: ret             
    // 0x90c864: ldr             x1, [fp, #0x10]
    // 0x90c868: r2 = Instance_Heir
    //     0x90c868: add             x2, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90c86c: ldr             x2, [x2, #0xb0]
    // 0x90c870: r0 = HeirsExt.hasOneOf()
    //     0x90c870: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90c874: tbnz            w0, #4, #0x90c88c
    // 0x90c878: r0 = Instance__$Furudh
    //     0x90c878: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e0] Obj!_$Furudh@e0be41
    //     0x90c87c: ldr             x0, [x0, #0x9e0]
    // 0x90c880: LeaveFrame
    //     0x90c880: mov             SP, fp
    //     0x90c884: ldp             fp, lr, [SP], #0x10
    // 0x90c888: ret
    //     0x90c888: ret             
    // 0x90c88c: r0 = Instance__$Unknown
    //     0x90c88c: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e8] Obj!_$Unknown@e0bd01
    //     0x90c890: ldr             x0, [x0, #0x9e8]
    // 0x90c894: LeaveFrame
    //     0x90c894: mov             SP, fp
    //     0x90c898: ldp             fp, lr, [SP], #0x10
    // 0x90c89c: ret
    //     0x90c89c: ret             
    // 0x90c8a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90c8a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90c8a4: b               #0x90c5e8
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90c988, size: 0x1f4
    // 0x90c988: EnterFrame
    //     0x90c988: stp             fp, lr, [SP, #-0x10]!
    //     0x90c98c: mov             fp, SP
    // 0x90c990: AllocStack(0x8)
    //     0x90c990: sub             SP, SP, #8
    // 0x90c994: SetupParameters()
    //     0x90c994: movz            x0, #0x6
    // 0x90c994: r0 = 6
    // 0x90c998: CheckStackOverflow
    //     0x90c998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90c99c: cmp             SP, x16
    //     0x90c9a0: b.ls            #0x90cb74
    // 0x90c9a4: mov             x2, x0
    // 0x90c9a8: r1 = Null
    //     0x90c9a8: mov             x1, NULL
    // 0x90c9ac: r0 = AllocateArray()
    //     0x90c9ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90c9b0: stur            x0, [fp, #-8]
    // 0x90c9b4: r16 = Instance_Heir
    //     0x90c9b4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90c9b8: ldr             x16, [x16, #0x7d0]
    // 0x90c9bc: StoreField: r0->field_f = r16
    //     0x90c9bc: stur            w16, [x0, #0xf]
    // 0x90c9c0: r16 = Instance_Heir
    //     0x90c9c0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90c9c4: ldr             x16, [x16, #0x7e0]
    // 0x90c9c8: StoreField: r0->field_13 = r16
    //     0x90c9c8: stur            w16, [x0, #0x13]
    // 0x90c9cc: r16 = Instance_Heir
    //     0x90c9cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90c9d0: ldr             x16, [x16, #0x838]
    // 0x90c9d4: ArrayStore: r0[0] = r16  ; List_4
    //     0x90c9d4: stur            w16, [x0, #0x17]
    // 0x90c9d8: r1 = <Heir>
    //     0x90c9d8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90c9dc: ldr             x1, [x1, #0xc0]
    // 0x90c9e0: r0 = AllocateGrowableArray()
    //     0x90c9e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90c9e4: mov             x1, x0
    // 0x90c9e8: ldur            x0, [fp, #-8]
    // 0x90c9ec: StoreField: r1->field_f = r0
    //     0x90c9ec: stur            w0, [x1, #0xf]
    // 0x90c9f0: r0 = 6
    //     0x90c9f0: movz            x0, #0x6
    // 0x90c9f4: StoreField: r1->field_b = r0
    //     0x90c9f4: stur            w0, [x1, #0xb]
    // 0x90c9f8: mov             x2, x1
    // 0x90c9fc: ldr             x1, [fp, #0x10]
    // 0x90ca00: r0 = HeirsExt.search()
    //     0x90ca00: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90ca04: stur            x0, [fp, #-8]
    // 0x90ca08: LoadField: r1 = r0->field_b
    //     0x90ca08: ldur            w1, [x0, #0xb]
    // 0x90ca0c: cbz             w1, #0x90ca30
    // 0x90ca10: r0 = _$Mahjub()
    //     0x90ca10: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90ca14: mov             x1, x0
    // 0x90ca18: ldur            x0, [fp, #-8]
    // 0x90ca1c: StoreField: r1->field_7 = r0
    //     0x90ca1c: stur            w0, [x1, #7]
    // 0x90ca20: mov             x0, x1
    // 0x90ca24: LeaveFrame
    //     0x90ca24: mov             SP, fp
    //     0x90ca28: ldp             fp, lr, [SP], #0x10
    // 0x90ca2c: ret
    //     0x90ca2c: ret             
    // 0x90ca30: r0 = 4
    //     0x90ca30: movz            x0, #0x4
    // 0x90ca34: mov             x2, x0
    // 0x90ca38: r1 = Null
    //     0x90ca38: mov             x1, NULL
    // 0x90ca3c: r0 = AllocateArray()
    //     0x90ca3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90ca40: stur            x0, [fp, #-8]
    // 0x90ca44: r16 = Instance_Heir
    //     0x90ca44: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90ca48: ldr             x16, [x16, #0x88]
    // 0x90ca4c: StoreField: r0->field_f = r16
    //     0x90ca4c: stur            w16, [x0, #0xf]
    // 0x90ca50: r16 = Instance_Heir
    //     0x90ca50: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90ca54: ldr             x16, [x16, #0x90]
    // 0x90ca58: StoreField: r0->field_13 = r16
    //     0x90ca58: stur            w16, [x0, #0x13]
    // 0x90ca5c: r1 = <Heir>
    //     0x90ca5c: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90ca60: ldr             x1, [x1, #0xc0]
    // 0x90ca64: r0 = AllocateGrowableArray()
    //     0x90ca64: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90ca68: mov             x1, x0
    // 0x90ca6c: ldur            x0, [fp, #-8]
    // 0x90ca70: StoreField: r1->field_f = r0
    //     0x90ca70: stur            w0, [x1, #0xf]
    // 0x90ca74: r0 = 4
    //     0x90ca74: movz            x0, #0x4
    // 0x90ca78: StoreField: r1->field_b = r0
    //     0x90ca78: stur            w0, [x1, #0xb]
    // 0x90ca7c: mov             x2, x1
    // 0x90ca80: ldr             x1, [fp, #0x10]
    // 0x90ca84: r0 = HeirsExt.hasAnyOf()
    //     0x90ca84: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90ca88: tbnz            w0, #4, #0x90caa0
    // 0x90ca8c: r0 = Instance__$Ashobah
    //     0x90ca8c: add             x0, PP, #0x31, lsl #12  ; [pp+0x319c8] Obj!_$Ashobah@e0bdd1
    //     0x90ca90: ldr             x0, [x0, #0x9c8]
    // 0x90ca94: LeaveFrame
    //     0x90ca94: mov             SP, fp
    //     0x90ca98: ldp             fp, lr, [SP], #0x10
    // 0x90ca9c: ret
    //     0x90ca9c: ret             
    // 0x90caa0: r0 = 4
    //     0x90caa0: movz            x0, #0x4
    // 0x90caa4: mov             x2, x0
    // 0x90caa8: r1 = Null
    //     0x90caa8: mov             x1, NULL
    // 0x90caac: r0 = AllocateArray()
    //     0x90caac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90cab0: stur            x0, [fp, #-8]
    // 0x90cab4: r16 = Instance_Heir
    //     0x90cab4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90cab8: ldr             x16, [x16, #0x7d8]
    // 0x90cabc: StoreField: r0->field_f = r16
    //     0x90cabc: stur            w16, [x0, #0xf]
    // 0x90cac0: r16 = Instance_Heir
    //     0x90cac0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90cac4: ldr             x16, [x16, #0x7e8]
    // 0x90cac8: StoreField: r0->field_13 = r16
    //     0x90cac8: stur            w16, [x0, #0x13]
    // 0x90cacc: r1 = <Heir>
    //     0x90cacc: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90cad0: ldr             x1, [x1, #0xc0]
    // 0x90cad4: r0 = AllocateGrowableArray()
    //     0x90cad4: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90cad8: mov             x1, x0
    // 0x90cadc: ldur            x0, [fp, #-8]
    // 0x90cae0: StoreField: r1->field_f = r0
    //     0x90cae0: stur            w0, [x1, #0xf]
    // 0x90cae4: r0 = 4
    //     0x90cae4: movz            x0, #0x4
    // 0x90cae8: StoreField: r1->field_b = r0
    //     0x90cae8: stur            w0, [x1, #0xb]
    // 0x90caec: mov             x2, x1
    // 0x90caf0: ldr             x1, [fp, #0x10]
    // 0x90caf4: r0 = HeirsExt.hasAnyOf()
    //     0x90caf4: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90caf8: tbnz            w0, #4, #0x90cb10
    // 0x90cafc: r0 = Instance__$Ashobah
    //     0x90cafc: add             x0, PP, #0x31, lsl #12  ; [pp+0x319d0] Obj!_$Ashobah@e0bdc1
    //     0x90cb00: ldr             x0, [x0, #0x9d0]
    // 0x90cb04: LeaveFrame
    //     0x90cb04: mov             SP, fp
    //     0x90cb08: ldp             fp, lr, [SP], #0x10
    // 0x90cb0c: ret
    //     0x90cb0c: ret             
    // 0x90cb10: ldr             x1, [fp, #0x10]
    // 0x90cb14: r2 = Instance_Heir
    //     0x90cb14: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90cb18: ldr             x2, [x2, #0xa8]
    // 0x90cb1c: r0 = HeirsExt.hasOneOf()
    //     0x90cb1c: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90cb20: tbnz            w0, #4, #0x90cb38
    // 0x90cb24: r0 = Instance__$Furudh
    //     0x90cb24: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e0] Obj!_$Furudh@e0be41
    //     0x90cb28: ldr             x0, [x0, #0x9e0]
    // 0x90cb2c: LeaveFrame
    //     0x90cb2c: mov             SP, fp
    //     0x90cb30: ldp             fp, lr, [SP], #0x10
    // 0x90cb34: ret
    //     0x90cb34: ret             
    // 0x90cb38: ldr             x1, [fp, #0x10]
    // 0x90cb3c: r2 = Instance_Heir
    //     0x90cb3c: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90cb40: ldr             x2, [x2, #0xa8]
    // 0x90cb44: r0 = HeirsExt.hasManyOf()
    //     0x90cb44: bl              #0x90c8dc  ; [package:waris/src/heirs.dart] ::HeirsExt.hasManyOf
    // 0x90cb48: tbnz            w0, #4, #0x90cb60
    // 0x90cb4c: r0 = Instance__$Furudh
    //     0x90cb4c: add             x0, PP, #0x31, lsl #12  ; [pp+0x319d8] Obj!_$Furudh@e0bec1
    //     0x90cb50: ldr             x0, [x0, #0x9d8]
    // 0x90cb54: LeaveFrame
    //     0x90cb54: mov             SP, fp
    //     0x90cb58: ldp             fp, lr, [SP], #0x10
    // 0x90cb5c: ret
    //     0x90cb5c: ret             
    // 0x90cb60: r0 = Instance__$Unknown
    //     0x90cb60: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e8] Obj!_$Unknown@e0bd01
    //     0x90cb64: ldr             x0, [x0, #0x9e8]
    // 0x90cb68: LeaveFrame
    //     0x90cb68: mov             SP, fp
    //     0x90cb6c: ldp             fp, lr, [SP], #0x10
    // 0x90cb70: ret
    //     0x90cb70: ret             
    // 0x90cb74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90cb74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90cb78: b               #0x90c9a4
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90cb7c, size: 0x15c
    // 0x90cb7c: EnterFrame
    //     0x90cb7c: stp             fp, lr, [SP, #-0x10]!
    //     0x90cb80: mov             fp, SP
    // 0x90cb84: CheckStackOverflow
    //     0x90cb84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90cb88: cmp             SP, x16
    //     0x90cb8c: b.ls            #0x90ccd0
    // 0x90cb90: ldr             x1, [fp, #0x10]
    // 0x90cb94: r2 = Instance_Heir
    //     0x90cb94: add             x2, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90cb98: ldr             x2, [x2, #0x7d0]
    // 0x90cb9c: r0 = HeirsExt.has()
    //     0x90cb9c: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90cba0: tbnz            w0, #4, #0x90cbb8
    // 0x90cba4: r0 = Instance__$Mahjub
    //     0x90cba4: add             x0, PP, #0x31, lsl #12  ; [pp+0x319f0] Obj!_$Mahjub@e0bd71
    //     0x90cba8: ldr             x0, [x0, #0x9f0]
    // 0x90cbac: LeaveFrame
    //     0x90cbac: mov             SP, fp
    //     0x90cbb0: ldp             fp, lr, [SP], #0x10
    // 0x90cbb4: ret
    //     0x90cbb4: ret             
    // 0x90cbb8: ldr             x1, [fp, #0x10]
    // 0x90cbbc: r2 = Instance_Heir
    //     0x90cbbc: add             x2, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90cbc0: ldr             x2, [x2, #0x7d8]
    // 0x90cbc4: r0 = HeirsExt.hasManyOf()
    //     0x90cbc4: bl              #0x90c8dc  ; [package:waris/src/heirs.dart] ::HeirsExt.hasManyOf
    // 0x90cbc8: tbnz            w0, #4, #0x90cc1c
    // 0x90cbcc: ldr             x1, [fp, #0x10]
    // 0x90cbd0: r2 = Instance_Heir
    //     0x90cbd0: add             x2, PP, #0x31, lsl #12  ; [pp+0x318e8] Obj!Heir@e2dcf1
    //     0x90cbd4: ldr             x2, [x2, #0x8e8]
    // 0x90cbd8: r0 = HeirsExt.has()
    //     0x90cbd8: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90cbdc: tbnz            w0, #4, #0x90cbf4
    // 0x90cbe0: r0 = Instance__$Ashobah
    //     0x90cbe0: add             x0, PP, #0x31, lsl #12  ; [pp+0x319c8] Obj!_$Ashobah@e0bdd1
    //     0x90cbe4: ldr             x0, [x0, #0x9c8]
    // 0x90cbe8: LeaveFrame
    //     0x90cbe8: mov             SP, fp
    //     0x90cbec: ldp             fp, lr, [SP], #0x10
    // 0x90cbf0: ret
    //     0x90cbf0: ret             
    // 0x90cbf4: ldr             x1, [fp, #0x10]
    // 0x90cbf8: r2 = Instance_Heir
    //     0x90cbf8: add             x2, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90cbfc: ldr             x2, [x2, #0x7e0]
    // 0x90cc00: r0 = HeirsExt.notHas()
    //     0x90cc00: bl              #0x90c8a8  ; [package:waris/src/heirs.dart] ::HeirsExt.notHas
    // 0x90cc04: tbnz            w0, #4, #0x90cc1c
    // 0x90cc08: r0 = Instance__$Mahjub
    //     0x90cc08: add             x0, PP, #0x31, lsl #12  ; [pp+0x319f8] Obj!_$Mahjub@e0bd61
    //     0x90cc0c: ldr             x0, [x0, #0x9f8]
    // 0x90cc10: LeaveFrame
    //     0x90cc10: mov             SP, fp
    //     0x90cc14: ldp             fp, lr, [SP], #0x10
    // 0x90cc18: ret
    //     0x90cc18: ret             
    // 0x90cc1c: ldr             x1, [fp, #0x10]
    // 0x90cc20: r2 = Instance_Heir
    //     0x90cc20: add             x2, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90cc24: ldr             x2, [x2, #0x7e0]
    // 0x90cc28: r0 = HeirsExt.has()
    //     0x90cc28: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90cc2c: tbnz            w0, #4, #0x90cc44
    // 0x90cc30: r0 = Instance__$Ashobah
    //     0x90cc30: add             x0, PP, #0x31, lsl #12  ; [pp+0x319c8] Obj!_$Ashobah@e0bdd1
    //     0x90cc34: ldr             x0, [x0, #0x9c8]
    // 0x90cc38: LeaveFrame
    //     0x90cc38: mov             SP, fp
    //     0x90cc3c: ldp             fp, lr, [SP], #0x10
    // 0x90cc40: ret
    //     0x90cc40: ret             
    // 0x90cc44: ldr             x1, [fp, #0x10]
    // 0x90cc48: r2 = Instance_Heir
    //     0x90cc48: add             x2, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90cc4c: ldr             x2, [x2, #0x7d8]
    // 0x90cc50: r0 = HeirsExt.hasOneOf()
    //     0x90cc50: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90cc54: tbnz            w0, #4, #0x90cc6c
    // 0x90cc58: r0 = Instance__$Furudh
    //     0x90cc58: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90cc5c: ldr             x0, [x0, #0x9a0]
    // 0x90cc60: LeaveFrame
    //     0x90cc60: mov             SP, fp
    //     0x90cc64: ldp             fp, lr, [SP], #0x10
    // 0x90cc68: ret
    //     0x90cc68: ret             
    // 0x90cc6c: ldr             x1, [fp, #0x10]
    // 0x90cc70: r2 = Instance_Heir
    //     0x90cc70: add             x2, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90cc74: ldr             x2, [x2, #0x7e8]
    // 0x90cc78: r0 = HeirsExt.hasManyOf()
    //     0x90cc78: bl              #0x90c8dc  ; [package:waris/src/heirs.dart] ::HeirsExt.hasManyOf
    // 0x90cc7c: tbnz            w0, #4, #0x90cc94
    // 0x90cc80: r0 = Instance__$Furudh
    //     0x90cc80: add             x0, PP, #0x31, lsl #12  ; [pp+0x319d8] Obj!_$Furudh@e0bec1
    //     0x90cc84: ldr             x0, [x0, #0x9d8]
    // 0x90cc88: LeaveFrame
    //     0x90cc88: mov             SP, fp
    //     0x90cc8c: ldp             fp, lr, [SP], #0x10
    // 0x90cc90: ret
    //     0x90cc90: ret             
    // 0x90cc94: ldr             x1, [fp, #0x10]
    // 0x90cc98: r2 = Instance_Heir
    //     0x90cc98: add             x2, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90cc9c: ldr             x2, [x2, #0x7e8]
    // 0x90cca0: r0 = HeirsExt.hasOneOf()
    //     0x90cca0: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90cca4: tbnz            w0, #4, #0x90ccbc
    // 0x90cca8: r0 = Instance__$Furudh
    //     0x90cca8: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e0] Obj!_$Furudh@e0be41
    //     0x90ccac: ldr             x0, [x0, #0x9e0]
    // 0x90ccb0: LeaveFrame
    //     0x90ccb0: mov             SP, fp
    //     0x90ccb4: ldp             fp, lr, [SP], #0x10
    // 0x90ccb8: ret
    //     0x90ccb8: ret             
    // 0x90ccbc: r0 = Instance__$Unknown
    //     0x90ccbc: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e8] Obj!_$Unknown@e0bd01
    //     0x90ccc0: ldr             x0, [x0, #0x9e8]
    // 0x90ccc4: LeaveFrame
    //     0x90ccc4: mov             SP, fp
    //     0x90ccc8: ldp             fp, lr, [SP], #0x10
    // 0x90cccc: ret
    //     0x90cccc: ret             
    // 0x90ccd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90ccd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90ccd4: b               #0x90cb90
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90ccd8, size: 0x80
    // 0x90ccd8: EnterFrame
    //     0x90ccd8: stp             fp, lr, [SP, #-0x10]!
    //     0x90ccdc: mov             fp, SP
    // 0x90cce0: CheckStackOverflow
    //     0x90cce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90cce4: cmp             SP, x16
    //     0x90cce8: b.ls            #0x90cd50
    // 0x90ccec: ldr             x1, [fp, #0x10]
    // 0x90ccf0: r2 = Instance_Heir
    //     0x90ccf0: add             x2, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90ccf4: ldr             x2, [x2, #0x7d0]
    // 0x90ccf8: r0 = HeirsExt.has()
    //     0x90ccf8: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90ccfc: tbnz            w0, #4, #0x90cd14
    // 0x90cd00: r0 = Instance__$Ashobah
    //     0x90cd00: add             x0, PP, #0x31, lsl #12  ; [pp+0x319c8] Obj!_$Ashobah@e0bdd1
    //     0x90cd04: ldr             x0, [x0, #0x9c8]
    // 0x90cd08: LeaveFrame
    //     0x90cd08: mov             SP, fp
    //     0x90cd0c: ldp             fp, lr, [SP], #0x10
    // 0x90cd10: ret
    //     0x90cd10: ret             
    // 0x90cd14: ldr             x1, [fp, #0x10]
    // 0x90cd18: r2 = Instance_Heir
    //     0x90cd18: add             x2, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90cd1c: ldr             x2, [x2, #0x7d8]
    // 0x90cd20: r0 = HeirsExt.hasOneOf()
    //     0x90cd20: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90cd24: tbnz            w0, #4, #0x90cd3c
    // 0x90cd28: r0 = Instance__$Furudh
    //     0x90cd28: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e0] Obj!_$Furudh@e0be41
    //     0x90cd2c: ldr             x0, [x0, #0x9e0]
    // 0x90cd30: LeaveFrame
    //     0x90cd30: mov             SP, fp
    //     0x90cd34: ldp             fp, lr, [SP], #0x10
    // 0x90cd38: ret
    //     0x90cd38: ret             
    // 0x90cd3c: r0 = Instance__$Furudh
    //     0x90cd3c: add             x0, PP, #0x31, lsl #12  ; [pp+0x319d8] Obj!_$Furudh@e0bec1
    //     0x90cd40: ldr             x0, [x0, #0x9d8]
    // 0x90cd44: LeaveFrame
    //     0x90cd44: mov             SP, fp
    //     0x90cd48: ldp             fp, lr, [SP], #0x10
    // 0x90cd4c: ret
    //     0x90cd4c: ret             
    // 0x90cd50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90cd50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90cd54: b               #0x90ccec
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90cd58, size: 0xec
    // 0x90cd58: EnterFrame
    //     0x90cd58: stp             fp, lr, [SP, #-0x10]!
    //     0x90cd5c: mov             fp, SP
    // 0x90cd60: AllocStack(0x28)
    //     0x90cd60: sub             SP, SP, #0x28
    // 0x90cd64: CheckStackOverflow
    //     0x90cd64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90cd68: cmp             SP, x16
    //     0x90cd6c: b.ls            #0x90ce3c
    // 0x90cd70: ldr             x1, [fp, #0x10]
    // 0x90cd74: r2 = const [Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir']
    //     0x90cd74: add             x2, PP, #0x31, lsl #12  ; [pp+0x31a00] List<Heir>(4)
    //     0x90cd78: ldr             x2, [x2, #0xa00]
    // 0x90cd7c: r0 = HeirsExt.search()
    //     0x90cd7c: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90cd80: LoadField: r3 = r0->field_b
    //     0x90cd80: ldur            w3, [x0, #0xb]
    // 0x90cd84: ldr             x1, [fp, #0x10]
    // 0x90cd88: stur            x3, [fp, #-8]
    // 0x90cd8c: r2 = const [Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir']
    //     0x90cd8c: add             x2, PP, #0x31, lsl #12  ; [pp+0x31a08] List<Heir>(6)
    //     0x90cd90: ldr             x2, [x2, #0xa08]
    // 0x90cd94: r0 = HeirsExt.search()
    //     0x90cd94: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90cd98: stur            x0, [fp, #-0x10]
    // 0x90cd9c: LoadField: r1 = r0->field_b
    //     0x90cd9c: ldur            w1, [x0, #0xb]
    // 0x90cda0: cbz             w1, #0x90ce04
    // 0x90cda4: ldr             x1, [fp, #0x10]
    // 0x90cda8: r1 = 1
    //     0x90cda8: movz            x1, #0x1
    // 0x90cdac: r0 = AllocateContext()
    //     0x90cdac: bl              #0xec126c  ; AllocateContextStub
    // 0x90cdb0: mov             x1, x0
    // 0x90cdb4: ldr             x0, [fp, #0x10]
    // 0x90cdb8: StoreField: r1->field_f = r0
    //     0x90cdb8: stur            w0, [x1, #0xf]
    // 0x90cdbc: mov             x2, x1
    // 0x90cdc0: r1 = Function '<anonymous closure>': static.
    //     0x90cdc0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31a10] AnonymousClosure: static (0x90ac14), of [package:waris/src/heirs.dart] 
    //     0x90cdc4: ldr             x1, [x1, #0xa10]
    // 0x90cdc8: r0 = AllocateClosure()
    //     0x90cdc8: bl              #0xec1630  ; AllocateClosureStub
    // 0x90cdcc: r16 = <int>
    //     0x90cdcc: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x90cdd0: ldur            lr, [fp, #-0x10]
    // 0x90cdd4: stp             lr, x16, [SP, #8]
    // 0x90cdd8: str             x0, [SP]
    // 0x90cddc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x90cddc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90cde0: r0 = map()
    //     0x90cde0: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x90cde4: mov             x1, x0
    // 0x90cde8: r0 = IterableIntegerExtension.sum()
    //     0x90cde8: bl              #0x909eb0  ; [package:collection/src/iterable_extensions.dart] ::IterableIntegerExtension.sum
    // 0x90cdec: cmp             x0, #1
    // 0x90cdf0: r16 = true
    //     0x90cdf0: add             x16, NULL, #0x20  ; true
    // 0x90cdf4: r17 = false
    //     0x90cdf4: add             x17, NULL, #0x30  ; false
    // 0x90cdf8: csel            x1, x16, x17, gt
    // 0x90cdfc: mov             x2, x1
    // 0x90ce00: b               #0x90ce08
    // 0x90ce04: r2 = false
    //     0x90ce04: add             x2, NULL, #0x30  ; false
    // 0x90ce08: ldur            x1, [fp, #-8]
    // 0x90ce0c: cbnz            w1, #0x90ce14
    // 0x90ce10: tbnz            w2, #4, #0x90ce28
    // 0x90ce14: r0 = Instance__$Furudh
    //     0x90ce14: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90ce18: ldr             x0, [x0, #0x9a0]
    // 0x90ce1c: LeaveFrame
    //     0x90ce1c: mov             SP, fp
    //     0x90ce20: ldp             fp, lr, [SP], #0x10
    // 0x90ce24: ret
    //     0x90ce24: ret             
    // 0x90ce28: r0 = Instance__$Furudh
    //     0x90ce28: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a8] Obj!_$Furudh@e0be81
    //     0x90ce2c: ldr             x0, [x0, #0x9a8]
    // 0x90ce30: LeaveFrame
    //     0x90ce30: mov             SP, fp
    //     0x90ce34: ldp             fp, lr, [SP], #0x10
    // 0x90ce38: ret
    //     0x90ce38: ret             
    // 0x90ce3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90ce3c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90ce40: b               #0x90cd70
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90ce44, size: 0x58
    // 0x90ce44: EnterFrame
    //     0x90ce44: stp             fp, lr, [SP, #-0x10]!
    //     0x90ce48: mov             fp, SP
    // 0x90ce4c: CheckStackOverflow
    //     0x90ce4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ce50: cmp             SP, x16
    //     0x90ce54: b.ls            #0x90ce94
    // 0x90ce58: ldr             x1, [fp, #0x10]
    // 0x90ce5c: r2 = Instance_Heir
    //     0x90ce5c: add             x2, PP, #0x31, lsl #12  ; [pp+0x31918] Obj!Heir@e2d9f1
    //     0x90ce60: ldr             x2, [x2, #0x918]
    // 0x90ce64: r0 = HeirsExt.has()
    //     0x90ce64: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90ce68: tbnz            w0, #4, #0x90ce80
    // 0x90ce6c: r0 = Instance__$Mahjub
    //     0x90ce6c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31a28] Obj!_$Mahjub@e0bd81
    //     0x90ce70: ldr             x0, [x0, #0xa28]
    // 0x90ce74: LeaveFrame
    //     0x90ce74: mov             SP, fp
    //     0x90ce78: ldp             fp, lr, [SP], #0x10
    // 0x90ce7c: ret
    //     0x90ce7c: ret             
    // 0x90ce80: r0 = Instance__$Furudh
    //     0x90ce80: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90ce84: ldr             x0, [x0, #0x9a0]
    // 0x90ce88: LeaveFrame
    //     0x90ce88: mov             SP, fp
    //     0x90ce8c: ldp             fp, lr, [SP], #0x10
    // 0x90ce90: ret
    //     0x90ce90: ret             
    // 0x90ce94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90ce94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90ce98: b               #0x90ce58
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90ce9c, size: 0xb0
    // 0x90ce9c: EnterFrame
    //     0x90ce9c: stp             fp, lr, [SP, #-0x10]!
    //     0x90cea0: mov             fp, SP
    // 0x90cea4: AllocStack(0x8)
    //     0x90cea4: sub             SP, SP, #8
    // 0x90cea8: SetupParameters()
    //     0x90cea8: movz            x0, #0x4
    // 0x90cea8: r0 = 4
    // 0x90ceac: CheckStackOverflow
    //     0x90ceac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ceb0: cmp             SP, x16
    //     0x90ceb4: b.ls            #0x90cf44
    // 0x90ceb8: mov             x2, x0
    // 0x90cebc: r1 = Null
    //     0x90cebc: mov             x1, NULL
    // 0x90cec0: r0 = AllocateArray()
    //     0x90cec0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90cec4: stur            x0, [fp, #-8]
    // 0x90cec8: r16 = Instance_Heir
    //     0x90cec8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90cecc: ldr             x16, [x16, #0x838]
    // 0x90ced0: StoreField: r0->field_f = r16
    //     0x90ced0: stur            w16, [x0, #0xf]
    // 0x90ced4: r16 = Instance_Heir
    //     0x90ced4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31918] Obj!Heir@e2d9f1
    //     0x90ced8: ldr             x16, [x16, #0x918]
    // 0x90cedc: StoreField: r0->field_13 = r16
    //     0x90cedc: stur            w16, [x0, #0x13]
    // 0x90cee0: r1 = <Heir>
    //     0x90cee0: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90cee4: ldr             x1, [x1, #0xc0]
    // 0x90cee8: r0 = AllocateGrowableArray()
    //     0x90cee8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90ceec: mov             x1, x0
    // 0x90cef0: ldur            x0, [fp, #-8]
    // 0x90cef4: StoreField: r1->field_f = r0
    //     0x90cef4: stur            w0, [x1, #0xf]
    // 0x90cef8: r0 = 4
    //     0x90cef8: movz            x0, #0x4
    // 0x90cefc: StoreField: r1->field_b = r0
    //     0x90cefc: stur            w0, [x1, #0xb]
    // 0x90cf00: mov             x2, x1
    // 0x90cf04: ldr             x1, [fp, #0x10]
    // 0x90cf08: r0 = HeirsExt.search()
    //     0x90cf08: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90cf0c: stur            x0, [fp, #-8]
    // 0x90cf10: LoadField: r1 = r0->field_b
    //     0x90cf10: ldur            w1, [x0, #0xb]
    // 0x90cf14: cbz             w1, #0x90cf30
    // 0x90cf18: r0 = _$Mahjub()
    //     0x90cf18: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90cf1c: ldur            x1, [fp, #-8]
    // 0x90cf20: StoreField: r0->field_7 = r1
    //     0x90cf20: stur            w1, [x0, #7]
    // 0x90cf24: LeaveFrame
    //     0x90cf24: mov             SP, fp
    //     0x90cf28: ldp             fp, lr, [SP], #0x10
    // 0x90cf2c: ret
    //     0x90cf2c: ret             
    // 0x90cf30: r0 = Instance__$Furudh
    //     0x90cf30: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90cf34: ldr             x0, [x0, #0x9a0]
    // 0x90cf38: LeaveFrame
    //     0x90cf38: mov             SP, fp
    //     0x90cf3c: ldp             fp, lr, [SP], #0x10
    // 0x90cf40: ret
    //     0x90cf40: ret             
    // 0x90cf44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90cf44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90cf48: b               #0x90ceb8
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90cf4c, size: 0xa4
    // 0x90cf4c: EnterFrame
    //     0x90cf4c: stp             fp, lr, [SP, #-0x10]!
    //     0x90cf50: mov             fp, SP
    // 0x90cf54: AllocStack(0x8)
    //     0x90cf54: sub             SP, SP, #8
    // 0x90cf58: SetupParameters()
    //     0x90cf58: movz            x0, #0x4
    // 0x90cf58: r0 = 4
    // 0x90cf5c: CheckStackOverflow
    //     0x90cf5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90cf60: cmp             SP, x16
    //     0x90cf64: b.ls            #0x90cfe8
    // 0x90cf68: mov             x2, x0
    // 0x90cf6c: r1 = Null
    //     0x90cf6c: mov             x1, NULL
    // 0x90cf70: r0 = AllocateArray()
    //     0x90cf70: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90cf74: stur            x0, [fp, #-8]
    // 0x90cf78: r16 = Instance_Heir
    //     0x90cf78: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90cf7c: ldr             x16, [x16, #0x7d0]
    // 0x90cf80: StoreField: r0->field_f = r16
    //     0x90cf80: stur            w16, [x0, #0xf]
    // 0x90cf84: r16 = Instance_Heir
    //     0x90cf84: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90cf88: ldr             x16, [x16, #0x7e0]
    // 0x90cf8c: StoreField: r0->field_13 = r16
    //     0x90cf8c: stur            w16, [x0, #0x13]
    // 0x90cf90: r1 = <Heir>
    //     0x90cf90: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90cf94: ldr             x1, [x1, #0xc0]
    // 0x90cf98: r0 = AllocateGrowableArray()
    //     0x90cf98: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90cf9c: mov             x1, x0
    // 0x90cfa0: ldur            x0, [fp, #-8]
    // 0x90cfa4: StoreField: r1->field_f = r0
    //     0x90cfa4: stur            w0, [x1, #0xf]
    // 0x90cfa8: r0 = 4
    //     0x90cfa8: movz            x0, #0x4
    // 0x90cfac: StoreField: r1->field_b = r0
    //     0x90cfac: stur            w0, [x1, #0xb]
    // 0x90cfb0: mov             x2, x1
    // 0x90cfb4: ldr             x1, [fp, #0x10]
    // 0x90cfb8: r0 = HeirsExt.hasAnyOf()
    //     0x90cfb8: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90cfbc: tbnz            w0, #4, #0x90cfd4
    // 0x90cfc0: r0 = Instance__$Mahjub
    //     0x90cfc0: add             x0, PP, #0x31, lsl #12  ; [pp+0x31a30] Obj!_$Mahjub@e0bd91
    //     0x90cfc4: ldr             x0, [x0, #0xa30]
    // 0x90cfc8: LeaveFrame
    //     0x90cfc8: mov             SP, fp
    //     0x90cfcc: ldp             fp, lr, [SP], #0x10
    // 0x90cfd0: ret
    //     0x90cfd0: ret             
    // 0x90cfd4: r0 = Instance__$Ashobah
    //     0x90cfd4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90cfd8: ldr             x0, [x0, #0x988]
    // 0x90cfdc: LeaveFrame
    //     0x90cfdc: mov             SP, fp
    //     0x90cfe0: ldp             fp, lr, [SP], #0x10
    // 0x90cfe4: ret
    //     0x90cfe4: ret             
    // 0x90cfe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90cfe8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90cfec: b               #0x90cf68
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90cff0, size: 0x2f4
    // 0x90cff0: EnterFrame
    //     0x90cff0: stp             fp, lr, [SP, #-0x10]!
    //     0x90cff4: mov             fp, SP
    // 0x90cff8: AllocStack(0x8)
    //     0x90cff8: sub             SP, SP, #8
    // 0x90cffc: SetupParameters()
    //     0x90cffc: movz            x0, #0x18
    // 0x90cffc: r0 = 24
    // 0x90d000: CheckStackOverflow
    //     0x90d000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90d004: cmp             SP, x16
    //     0x90d008: b.ls            #0x90d2dc
    // 0x90d00c: mov             x2, x0
    // 0x90d010: r1 = <Heir>
    //     0x90d010: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d014: ldr             x1, [x1, #0xc0]
    // 0x90d018: r0 = AllocateArray()
    //     0x90d018: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d01c: stur            x0, [fp, #-8]
    // 0x90d020: r16 = Instance_Heir
    //     0x90d020: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90d024: ldr             x16, [x16, #0x7d0]
    // 0x90d028: StoreField: r0->field_f = r16
    //     0x90d028: stur            w16, [x0, #0xf]
    // 0x90d02c: r16 = Instance_Heir
    //     0x90d02c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90d030: ldr             x16, [x16, #0x7e0]
    // 0x90d034: StoreField: r0->field_13 = r16
    //     0x90d034: stur            w16, [x0, #0x13]
    // 0x90d038: r16 = Instance_Heir
    //     0x90d038: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90d03c: ldr             x16, [x16, #0x838]
    // 0x90d040: ArrayStore: r0[0] = r16  ; List_4
    //     0x90d040: stur            w16, [x0, #0x17]
    // 0x90d044: r16 = Instance_Heir
    //     0x90d044: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90d048: ldr             x16, [x16, #0x88]
    // 0x90d04c: StoreField: r0->field_1b = r16
    //     0x90d04c: stur            w16, [x0, #0x1b]
    // 0x90d050: r16 = Instance_Heir
    //     0x90d050: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90d054: ldr             x16, [x16, #0x90]
    // 0x90d058: StoreField: r0->field_1f = r16
    //     0x90d058: stur            w16, [x0, #0x1f]
    // 0x90d05c: r16 = Instance_Heir
    //     0x90d05c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90d060: ldr             x16, [x16, #0x98]
    // 0x90d064: StoreField: r0->field_23 = r16
    //     0x90d064: stur            w16, [x0, #0x23]
    // 0x90d068: r16 = Instance_Heir
    //     0x90d068: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90d06c: ldr             x16, [x16, #0x868]
    // 0x90d070: StoreField: r0->field_27 = r16
    //     0x90d070: stur            w16, [x0, #0x27]
    // 0x90d074: r16 = Instance_Heir
    //     0x90d074: add             x16, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x90d078: ldr             x16, [x16, #0x878]
    // 0x90d07c: StoreField: r0->field_2b = r16
    //     0x90d07c: stur            w16, [x0, #0x2b]
    // 0x90d080: r16 = Instance_Heir
    //     0x90d080: add             x16, PP, #0x31, lsl #12  ; [pp+0x31888] Obj!Heir@e2db71
    //     0x90d084: ldr             x16, [x16, #0x888]
    // 0x90d088: StoreField: r0->field_2f = r16
    //     0x90d088: stur            w16, [x0, #0x2f]
    // 0x90d08c: r16 = Instance_Heir
    //     0x90d08c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31898] Obj!Heir@e2db41
    //     0x90d090: ldr             x16, [x16, #0x898]
    // 0x90d094: StoreField: r0->field_33 = r16
    //     0x90d094: stur            w16, [x0, #0x33]
    // 0x90d098: r16 = Instance_Heir
    //     0x90d098: add             x16, PP, #0x31, lsl #12  ; [pp+0x318a8] Obj!Heir@e2db11
    //     0x90d09c: ldr             x16, [x16, #0x8a8]
    // 0x90d0a0: StoreField: r0->field_37 = r16
    //     0x90d0a0: stur            w16, [x0, #0x37]
    // 0x90d0a4: r16 = Instance_Heir
    //     0x90d0a4: add             x16, PP, #0x31, lsl #12  ; [pp+0x318b8] Obj!Heir@e2dae1
    //     0x90d0a8: ldr             x16, [x16, #0x8b8]
    // 0x90d0ac: StoreField: r0->field_3b = r16
    //     0x90d0ac: stur            w16, [x0, #0x3b]
    // 0x90d0b0: r1 = <Heir>
    //     0x90d0b0: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d0b4: ldr             x1, [x1, #0xc0]
    // 0x90d0b8: r0 = AllocateGrowableArray()
    //     0x90d0b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d0bc: mov             x1, x0
    // 0x90d0c0: ldur            x0, [fp, #-8]
    // 0x90d0c4: StoreField: r1->field_f = r0
    //     0x90d0c4: stur            w0, [x1, #0xf]
    // 0x90d0c8: r0 = 24
    //     0x90d0c8: movz            x0, #0x18
    // 0x90d0cc: StoreField: r1->field_b = r0
    //     0x90d0cc: stur            w0, [x1, #0xb]
    // 0x90d0d0: mov             x2, x1
    // 0x90d0d4: ldr             x1, [fp, #0x10]
    // 0x90d0d8: r0 = HeirsExt.search()
    //     0x90d0d8: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90d0dc: stur            x0, [fp, #-8]
    // 0x90d0e0: LoadField: r1 = r0->field_b
    //     0x90d0e0: ldur            w1, [x0, #0xb]
    // 0x90d0e4: cbz             w1, #0x90d108
    // 0x90d0e8: r0 = _$Mahjub()
    //     0x90d0e8: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90d0ec: mov             x1, x0
    // 0x90d0f0: ldur            x0, [fp, #-8]
    // 0x90d0f4: StoreField: r1->field_7 = r0
    //     0x90d0f4: stur            w0, [x1, #7]
    // 0x90d0f8: mov             x0, x1
    // 0x90d0fc: LeaveFrame
    //     0x90d0fc: mov             SP, fp
    //     0x90d100: ldp             fp, lr, [SP], #0x10
    // 0x90d104: ret
    //     0x90d104: ret             
    // 0x90d108: r0 = 4
    //     0x90d108: movz            x0, #0x4
    // 0x90d10c: mov             x2, x0
    // 0x90d110: r1 = Null
    //     0x90d110: mov             x1, NULL
    // 0x90d114: r0 = AllocateArray()
    //     0x90d114: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d118: stur            x0, [fp, #-8]
    // 0x90d11c: r16 = Instance_Heir
    //     0x90d11c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90d120: ldr             x16, [x16, #0xa8]
    // 0x90d124: StoreField: r0->field_f = r16
    //     0x90d124: stur            w16, [x0, #0xf]
    // 0x90d128: r16 = Instance_Heir
    //     0x90d128: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90d12c: ldr             x16, [x16, #0x7d8]
    // 0x90d130: StoreField: r0->field_13 = r16
    //     0x90d130: stur            w16, [x0, #0x13]
    // 0x90d134: r1 = <Heir>
    //     0x90d134: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d138: ldr             x1, [x1, #0xc0]
    // 0x90d13c: r0 = AllocateGrowableArray()
    //     0x90d13c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d140: mov             x1, x0
    // 0x90d144: ldur            x0, [fp, #-8]
    // 0x90d148: StoreField: r1->field_f = r0
    //     0x90d148: stur            w0, [x1, #0xf]
    // 0x90d14c: r0 = 4
    //     0x90d14c: movz            x0, #0x4
    // 0x90d150: StoreField: r1->field_b = r0
    //     0x90d150: stur            w0, [x1, #0xb]
    // 0x90d154: mov             x2, x1
    // 0x90d158: ldr             x1, [fp, #0x10]
    // 0x90d15c: r0 = HeirsExt.hasEveryOf()
    //     0x90d15c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d160: tbnz            w0, #4, #0x90d178
    // 0x90d164: r0 = Instance__$Mahjub
    //     0x90d164: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90d168: ldr             x0, [x0, #0x9b0]
    // 0x90d16c: LeaveFrame
    //     0x90d16c: mov             SP, fp
    //     0x90d170: ldp             fp, lr, [SP], #0x10
    // 0x90d174: ret
    //     0x90d174: ret             
    // 0x90d178: r0 = 4
    //     0x90d178: movz            x0, #0x4
    // 0x90d17c: mov             x2, x0
    // 0x90d180: r1 = Null
    //     0x90d180: mov             x1, NULL
    // 0x90d184: r0 = AllocateArray()
    //     0x90d184: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d188: stur            x0, [fp, #-8]
    // 0x90d18c: r16 = Instance_Heir
    //     0x90d18c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90d190: ldr             x16, [x16, #0xa8]
    // 0x90d194: StoreField: r0->field_f = r16
    //     0x90d194: stur            w16, [x0, #0xf]
    // 0x90d198: r16 = Instance_Heir
    //     0x90d198: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90d19c: ldr             x16, [x16, #0x7e8]
    // 0x90d1a0: StoreField: r0->field_13 = r16
    //     0x90d1a0: stur            w16, [x0, #0x13]
    // 0x90d1a4: r1 = <Heir>
    //     0x90d1a4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d1a8: ldr             x1, [x1, #0xc0]
    // 0x90d1ac: r0 = AllocateGrowableArray()
    //     0x90d1ac: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d1b0: mov             x1, x0
    // 0x90d1b4: ldur            x0, [fp, #-8]
    // 0x90d1b8: StoreField: r1->field_f = r0
    //     0x90d1b8: stur            w0, [x1, #0xf]
    // 0x90d1bc: r0 = 4
    //     0x90d1bc: movz            x0, #0x4
    // 0x90d1c0: StoreField: r1->field_b = r0
    //     0x90d1c0: stur            w0, [x1, #0xb]
    // 0x90d1c4: mov             x2, x1
    // 0x90d1c8: ldr             x1, [fp, #0x10]
    // 0x90d1cc: r0 = HeirsExt.hasEveryOf()
    //     0x90d1cc: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d1d0: tbnz            w0, #4, #0x90d1e8
    // 0x90d1d4: r0 = Instance__$Mahjub
    //     0x90d1d4: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90d1d8: ldr             x0, [x0, #0x9b8]
    // 0x90d1dc: LeaveFrame
    //     0x90d1dc: mov             SP, fp
    //     0x90d1e0: ldp             fp, lr, [SP], #0x10
    // 0x90d1e4: ret
    //     0x90d1e4: ret             
    // 0x90d1e8: r0 = 4
    //     0x90d1e8: movz            x0, #0x4
    // 0x90d1ec: mov             x2, x0
    // 0x90d1f0: r1 = Null
    //     0x90d1f0: mov             x1, NULL
    // 0x90d1f4: r0 = AllocateArray()
    //     0x90d1f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d1f8: stur            x0, [fp, #-8]
    // 0x90d1fc: r16 = Instance_Heir
    //     0x90d1fc: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90d200: ldr             x16, [x16, #0xb0]
    // 0x90d204: StoreField: r0->field_f = r16
    //     0x90d204: stur            w16, [x0, #0xf]
    // 0x90d208: r16 = Instance_Heir
    //     0x90d208: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90d20c: ldr             x16, [x16, #0x7d8]
    // 0x90d210: StoreField: r0->field_13 = r16
    //     0x90d210: stur            w16, [x0, #0x13]
    // 0x90d214: r1 = <Heir>
    //     0x90d214: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d218: ldr             x1, [x1, #0xc0]
    // 0x90d21c: r0 = AllocateGrowableArray()
    //     0x90d21c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d220: mov             x1, x0
    // 0x90d224: ldur            x0, [fp, #-8]
    // 0x90d228: StoreField: r1->field_f = r0
    //     0x90d228: stur            w0, [x1, #0xf]
    // 0x90d22c: r0 = 4
    //     0x90d22c: movz            x0, #0x4
    // 0x90d230: StoreField: r1->field_b = r0
    //     0x90d230: stur            w0, [x1, #0xb]
    // 0x90d234: mov             x2, x1
    // 0x90d238: ldr             x1, [fp, #0x10]
    // 0x90d23c: r0 = HeirsExt.hasEveryOf()
    //     0x90d23c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d240: tbnz            w0, #4, #0x90d258
    // 0x90d244: r0 = Instance__$Mahjub
    //     0x90d244: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90d248: ldr             x0, [x0, #0x978]
    // 0x90d24c: LeaveFrame
    //     0x90d24c: mov             SP, fp
    //     0x90d250: ldp             fp, lr, [SP], #0x10
    // 0x90d254: ret
    //     0x90d254: ret             
    // 0x90d258: r0 = 4
    //     0x90d258: movz            x0, #0x4
    // 0x90d25c: mov             x2, x0
    // 0x90d260: r1 = Null
    //     0x90d260: mov             x1, NULL
    // 0x90d264: r0 = AllocateArray()
    //     0x90d264: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d268: stur            x0, [fp, #-8]
    // 0x90d26c: r16 = Instance_Heir
    //     0x90d26c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90d270: ldr             x16, [x16, #0xb0]
    // 0x90d274: StoreField: r0->field_f = r16
    //     0x90d274: stur            w16, [x0, #0xf]
    // 0x90d278: r16 = Instance_Heir
    //     0x90d278: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90d27c: ldr             x16, [x16, #0x7e8]
    // 0x90d280: StoreField: r0->field_13 = r16
    //     0x90d280: stur            w16, [x0, #0x13]
    // 0x90d284: r1 = <Heir>
    //     0x90d284: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d288: ldr             x1, [x1, #0xc0]
    // 0x90d28c: r0 = AllocateGrowableArray()
    //     0x90d28c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d290: mov             x1, x0
    // 0x90d294: ldur            x0, [fp, #-8]
    // 0x90d298: StoreField: r1->field_f = r0
    //     0x90d298: stur            w0, [x1, #0xf]
    // 0x90d29c: r0 = 4
    //     0x90d29c: movz            x0, #0x4
    // 0x90d2a0: StoreField: r1->field_b = r0
    //     0x90d2a0: stur            w0, [x1, #0xb]
    // 0x90d2a4: mov             x2, x1
    // 0x90d2a8: ldr             x1, [fp, #0x10]
    // 0x90d2ac: r0 = HeirsExt.hasEveryOf()
    //     0x90d2ac: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d2b0: tbnz            w0, #4, #0x90d2c8
    // 0x90d2b4: r0 = Instance__$Mahjub
    //     0x90d2b4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90d2b8: ldr             x0, [x0, #0x980]
    // 0x90d2bc: LeaveFrame
    //     0x90d2bc: mov             SP, fp
    //     0x90d2c0: ldp             fp, lr, [SP], #0x10
    // 0x90d2c4: ret
    //     0x90d2c4: ret             
    // 0x90d2c8: r0 = Instance__$Ashobah
    //     0x90d2c8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90d2cc: ldr             x0, [x0, #0x988]
    // 0x90d2d0: LeaveFrame
    //     0x90d2d0: mov             SP, fp
    //     0x90d2d4: ldp             fp, lr, [SP], #0x10
    // 0x90d2d8: ret
    //     0x90d2d8: ret             
    // 0x90d2dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90d2dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90d2e0: b               #0x90d00c
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90d2e4, size: 0xbc
    // 0x90d2e4: EnterFrame
    //     0x90d2e4: stp             fp, lr, [SP, #-0x10]!
    //     0x90d2e8: mov             fp, SP
    // 0x90d2ec: AllocStack(0x8)
    //     0x90d2ec: sub             SP, SP, #8
    // 0x90d2f0: SetupParameters()
    //     0x90d2f0: movz            x0, #0x8
    // 0x90d2f0: r0 = 8
    // 0x90d2f4: CheckStackOverflow
    //     0x90d2f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90d2f8: cmp             SP, x16
    //     0x90d2fc: b.ls            #0x90d398
    // 0x90d300: mov             x2, x0
    // 0x90d304: r1 = Null
    //     0x90d304: mov             x1, NULL
    // 0x90d308: r0 = AllocateArray()
    //     0x90d308: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d30c: stur            x0, [fp, #-8]
    // 0x90d310: r16 = Instance_Heir
    //     0x90d310: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90d314: ldr             x16, [x16, #0x7d0]
    // 0x90d318: StoreField: r0->field_f = r16
    //     0x90d318: stur            w16, [x0, #0xf]
    // 0x90d31c: r16 = Instance_Heir
    //     0x90d31c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90d320: ldr             x16, [x16, #0x7e0]
    // 0x90d324: StoreField: r0->field_13 = r16
    //     0x90d324: stur            w16, [x0, #0x13]
    // 0x90d328: r16 = Instance_Heir
    //     0x90d328: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90d32c: ldr             x16, [x16, #0x7d8]
    // 0x90d330: ArrayStore: r0[0] = r16  ; List_4
    //     0x90d330: stur            w16, [x0, #0x17]
    // 0x90d334: r16 = Instance_Heir
    //     0x90d334: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90d338: ldr             x16, [x16, #0x7e8]
    // 0x90d33c: StoreField: r0->field_1b = r16
    //     0x90d33c: stur            w16, [x0, #0x1b]
    // 0x90d340: r1 = <Heir>
    //     0x90d340: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d344: ldr             x1, [x1, #0xc0]
    // 0x90d348: r0 = AllocateGrowableArray()
    //     0x90d348: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d34c: mov             x1, x0
    // 0x90d350: ldur            x0, [fp, #-8]
    // 0x90d354: StoreField: r1->field_f = r0
    //     0x90d354: stur            w0, [x1, #0xf]
    // 0x90d358: r0 = 8
    //     0x90d358: movz            x0, #0x8
    // 0x90d35c: StoreField: r1->field_b = r0
    //     0x90d35c: stur            w0, [x1, #0xb]
    // 0x90d360: mov             x2, x1
    // 0x90d364: ldr             x1, [fp, #0x10]
    // 0x90d368: r0 = HeirsExt.hasAnyOf()
    //     0x90d368: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90d36c: tbnz            w0, #4, #0x90d384
    // 0x90d370: r0 = Instance__$Furudh
    //     0x90d370: add             x0, PP, #0x31, lsl #12  ; [pp+0x31998] Obj!_$Furudh@e0be21
    //     0x90d374: ldr             x0, [x0, #0x998]
    // 0x90d378: LeaveFrame
    //     0x90d378: mov             SP, fp
    //     0x90d37c: ldp             fp, lr, [SP], #0x10
    // 0x90d380: ret
    //     0x90d380: ret             
    // 0x90d384: r0 = Instance__$Furudh
    //     0x90d384: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e0] Obj!_$Furudh@e0be41
    //     0x90d388: ldr             x0, [x0, #0x9e0]
    // 0x90d38c: LeaveFrame
    //     0x90d38c: mov             SP, fp
    //     0x90d390: ldp             fp, lr, [SP], #0x10
    // 0x90d394: ret
    //     0x90d394: ret             
    // 0x90d398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90d398: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90d39c: b               #0x90d300
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90d3a0, size: 0x2e8
    // 0x90d3a0: EnterFrame
    //     0x90d3a0: stp             fp, lr, [SP, #-0x10]!
    //     0x90d3a4: mov             fp, SP
    // 0x90d3a8: AllocStack(0x8)
    //     0x90d3a8: sub             SP, SP, #8
    // 0x90d3ac: SetupParameters()
    //     0x90d3ac: movz            x0, #0x16
    // 0x90d3ac: r0 = 22
    // 0x90d3b0: CheckStackOverflow
    //     0x90d3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90d3b4: cmp             SP, x16
    //     0x90d3b8: b.ls            #0x90d680
    // 0x90d3bc: mov             x2, x0
    // 0x90d3c0: r1 = <Heir>
    //     0x90d3c0: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d3c4: ldr             x1, [x1, #0xc0]
    // 0x90d3c8: r0 = AllocateArray()
    //     0x90d3c8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d3cc: stur            x0, [fp, #-8]
    // 0x90d3d0: r16 = Instance_Heir
    //     0x90d3d0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90d3d4: ldr             x16, [x16, #0x7d0]
    // 0x90d3d8: StoreField: r0->field_f = r16
    //     0x90d3d8: stur            w16, [x0, #0xf]
    // 0x90d3dc: r16 = Instance_Heir
    //     0x90d3dc: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90d3e0: ldr             x16, [x16, #0x7e0]
    // 0x90d3e4: StoreField: r0->field_13 = r16
    //     0x90d3e4: stur            w16, [x0, #0x13]
    // 0x90d3e8: r16 = Instance_Heir
    //     0x90d3e8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90d3ec: ldr             x16, [x16, #0x838]
    // 0x90d3f0: ArrayStore: r0[0] = r16  ; List_4
    //     0x90d3f0: stur            w16, [x0, #0x17]
    // 0x90d3f4: r16 = Instance_Heir
    //     0x90d3f4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90d3f8: ldr             x16, [x16, #0x88]
    // 0x90d3fc: StoreField: r0->field_1b = r16
    //     0x90d3fc: stur            w16, [x0, #0x1b]
    // 0x90d400: r16 = Instance_Heir
    //     0x90d400: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90d404: ldr             x16, [x16, #0x90]
    // 0x90d408: StoreField: r0->field_1f = r16
    //     0x90d408: stur            w16, [x0, #0x1f]
    // 0x90d40c: r16 = Instance_Heir
    //     0x90d40c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90d410: ldr             x16, [x16, #0x98]
    // 0x90d414: StoreField: r0->field_23 = r16
    //     0x90d414: stur            w16, [x0, #0x23]
    // 0x90d418: r16 = Instance_Heir
    //     0x90d418: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90d41c: ldr             x16, [x16, #0x868]
    // 0x90d420: StoreField: r0->field_27 = r16
    //     0x90d420: stur            w16, [x0, #0x27]
    // 0x90d424: r16 = Instance_Heir
    //     0x90d424: add             x16, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x90d428: ldr             x16, [x16, #0x878]
    // 0x90d42c: StoreField: r0->field_2b = r16
    //     0x90d42c: stur            w16, [x0, #0x2b]
    // 0x90d430: r16 = Instance_Heir
    //     0x90d430: add             x16, PP, #0x31, lsl #12  ; [pp+0x31888] Obj!Heir@e2db71
    //     0x90d434: ldr             x16, [x16, #0x888]
    // 0x90d438: StoreField: r0->field_2f = r16
    //     0x90d438: stur            w16, [x0, #0x2f]
    // 0x90d43c: r16 = Instance_Heir
    //     0x90d43c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31898] Obj!Heir@e2db41
    //     0x90d440: ldr             x16, [x16, #0x898]
    // 0x90d444: StoreField: r0->field_33 = r16
    //     0x90d444: stur            w16, [x0, #0x33]
    // 0x90d448: r16 = Instance_Heir
    //     0x90d448: add             x16, PP, #0x31, lsl #12  ; [pp+0x318a8] Obj!Heir@e2db11
    //     0x90d44c: ldr             x16, [x16, #0x8a8]
    // 0x90d450: StoreField: r0->field_37 = r16
    //     0x90d450: stur            w16, [x0, #0x37]
    // 0x90d454: r1 = <Heir>
    //     0x90d454: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d458: ldr             x1, [x1, #0xc0]
    // 0x90d45c: r0 = AllocateGrowableArray()
    //     0x90d45c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d460: mov             x1, x0
    // 0x90d464: ldur            x0, [fp, #-8]
    // 0x90d468: StoreField: r1->field_f = r0
    //     0x90d468: stur            w0, [x1, #0xf]
    // 0x90d46c: r0 = 22
    //     0x90d46c: movz            x0, #0x16
    // 0x90d470: StoreField: r1->field_b = r0
    //     0x90d470: stur            w0, [x1, #0xb]
    // 0x90d474: mov             x2, x1
    // 0x90d478: ldr             x1, [fp, #0x10]
    // 0x90d47c: r0 = HeirsExt.search()
    //     0x90d47c: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90d480: stur            x0, [fp, #-8]
    // 0x90d484: LoadField: r1 = r0->field_b
    //     0x90d484: ldur            w1, [x0, #0xb]
    // 0x90d488: cbz             w1, #0x90d4ac
    // 0x90d48c: r0 = _$Mahjub()
    //     0x90d48c: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90d490: mov             x1, x0
    // 0x90d494: ldur            x0, [fp, #-8]
    // 0x90d498: StoreField: r1->field_7 = r0
    //     0x90d498: stur            w0, [x1, #7]
    // 0x90d49c: mov             x0, x1
    // 0x90d4a0: LeaveFrame
    //     0x90d4a0: mov             SP, fp
    //     0x90d4a4: ldp             fp, lr, [SP], #0x10
    // 0x90d4a8: ret
    //     0x90d4a8: ret             
    // 0x90d4ac: r0 = 4
    //     0x90d4ac: movz            x0, #0x4
    // 0x90d4b0: mov             x2, x0
    // 0x90d4b4: r1 = Null
    //     0x90d4b4: mov             x1, NULL
    // 0x90d4b8: r0 = AllocateArray()
    //     0x90d4b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d4bc: stur            x0, [fp, #-8]
    // 0x90d4c0: r16 = Instance_Heir
    //     0x90d4c0: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90d4c4: ldr             x16, [x16, #0xa8]
    // 0x90d4c8: StoreField: r0->field_f = r16
    //     0x90d4c8: stur            w16, [x0, #0xf]
    // 0x90d4cc: r16 = Instance_Heir
    //     0x90d4cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90d4d0: ldr             x16, [x16, #0x7d8]
    // 0x90d4d4: StoreField: r0->field_13 = r16
    //     0x90d4d4: stur            w16, [x0, #0x13]
    // 0x90d4d8: r1 = <Heir>
    //     0x90d4d8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d4dc: ldr             x1, [x1, #0xc0]
    // 0x90d4e0: r0 = AllocateGrowableArray()
    //     0x90d4e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d4e4: mov             x1, x0
    // 0x90d4e8: ldur            x0, [fp, #-8]
    // 0x90d4ec: StoreField: r1->field_f = r0
    //     0x90d4ec: stur            w0, [x1, #0xf]
    // 0x90d4f0: r0 = 4
    //     0x90d4f0: movz            x0, #0x4
    // 0x90d4f4: StoreField: r1->field_b = r0
    //     0x90d4f4: stur            w0, [x1, #0xb]
    // 0x90d4f8: mov             x2, x1
    // 0x90d4fc: ldr             x1, [fp, #0x10]
    // 0x90d500: r0 = HeirsExt.hasEveryOf()
    //     0x90d500: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d504: tbnz            w0, #4, #0x90d51c
    // 0x90d508: r0 = Instance__$Mahjub
    //     0x90d508: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90d50c: ldr             x0, [x0, #0x9b0]
    // 0x90d510: LeaveFrame
    //     0x90d510: mov             SP, fp
    //     0x90d514: ldp             fp, lr, [SP], #0x10
    // 0x90d518: ret
    //     0x90d518: ret             
    // 0x90d51c: r0 = 4
    //     0x90d51c: movz            x0, #0x4
    // 0x90d520: mov             x2, x0
    // 0x90d524: r1 = Null
    //     0x90d524: mov             x1, NULL
    // 0x90d528: r0 = AllocateArray()
    //     0x90d528: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d52c: stur            x0, [fp, #-8]
    // 0x90d530: r16 = Instance_Heir
    //     0x90d530: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90d534: ldr             x16, [x16, #0xa8]
    // 0x90d538: StoreField: r0->field_f = r16
    //     0x90d538: stur            w16, [x0, #0xf]
    // 0x90d53c: r16 = Instance_Heir
    //     0x90d53c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90d540: ldr             x16, [x16, #0x7e8]
    // 0x90d544: StoreField: r0->field_13 = r16
    //     0x90d544: stur            w16, [x0, #0x13]
    // 0x90d548: r1 = <Heir>
    //     0x90d548: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d54c: ldr             x1, [x1, #0xc0]
    // 0x90d550: r0 = AllocateGrowableArray()
    //     0x90d550: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d554: mov             x1, x0
    // 0x90d558: ldur            x0, [fp, #-8]
    // 0x90d55c: StoreField: r1->field_f = r0
    //     0x90d55c: stur            w0, [x1, #0xf]
    // 0x90d560: r0 = 4
    //     0x90d560: movz            x0, #0x4
    // 0x90d564: StoreField: r1->field_b = r0
    //     0x90d564: stur            w0, [x1, #0xb]
    // 0x90d568: mov             x2, x1
    // 0x90d56c: ldr             x1, [fp, #0x10]
    // 0x90d570: r0 = HeirsExt.hasEveryOf()
    //     0x90d570: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d574: tbnz            w0, #4, #0x90d58c
    // 0x90d578: r0 = Instance__$Mahjub
    //     0x90d578: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90d57c: ldr             x0, [x0, #0x9b8]
    // 0x90d580: LeaveFrame
    //     0x90d580: mov             SP, fp
    //     0x90d584: ldp             fp, lr, [SP], #0x10
    // 0x90d588: ret
    //     0x90d588: ret             
    // 0x90d58c: r0 = 4
    //     0x90d58c: movz            x0, #0x4
    // 0x90d590: mov             x2, x0
    // 0x90d594: r1 = Null
    //     0x90d594: mov             x1, NULL
    // 0x90d598: r0 = AllocateArray()
    //     0x90d598: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d59c: stur            x0, [fp, #-8]
    // 0x90d5a0: r16 = Instance_Heir
    //     0x90d5a0: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90d5a4: ldr             x16, [x16, #0xb0]
    // 0x90d5a8: StoreField: r0->field_f = r16
    //     0x90d5a8: stur            w16, [x0, #0xf]
    // 0x90d5ac: r16 = Instance_Heir
    //     0x90d5ac: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90d5b0: ldr             x16, [x16, #0x7d8]
    // 0x90d5b4: StoreField: r0->field_13 = r16
    //     0x90d5b4: stur            w16, [x0, #0x13]
    // 0x90d5b8: r1 = <Heir>
    //     0x90d5b8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d5bc: ldr             x1, [x1, #0xc0]
    // 0x90d5c0: r0 = AllocateGrowableArray()
    //     0x90d5c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d5c4: mov             x1, x0
    // 0x90d5c8: ldur            x0, [fp, #-8]
    // 0x90d5cc: StoreField: r1->field_f = r0
    //     0x90d5cc: stur            w0, [x1, #0xf]
    // 0x90d5d0: r0 = 4
    //     0x90d5d0: movz            x0, #0x4
    // 0x90d5d4: StoreField: r1->field_b = r0
    //     0x90d5d4: stur            w0, [x1, #0xb]
    // 0x90d5d8: mov             x2, x1
    // 0x90d5dc: ldr             x1, [fp, #0x10]
    // 0x90d5e0: r0 = HeirsExt.hasEveryOf()
    //     0x90d5e0: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d5e4: tbnz            w0, #4, #0x90d5fc
    // 0x90d5e8: r0 = Instance__$Mahjub
    //     0x90d5e8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90d5ec: ldr             x0, [x0, #0x978]
    // 0x90d5f0: LeaveFrame
    //     0x90d5f0: mov             SP, fp
    //     0x90d5f4: ldp             fp, lr, [SP], #0x10
    // 0x90d5f8: ret
    //     0x90d5f8: ret             
    // 0x90d5fc: r0 = 4
    //     0x90d5fc: movz            x0, #0x4
    // 0x90d600: mov             x2, x0
    // 0x90d604: r1 = Null
    //     0x90d604: mov             x1, NULL
    // 0x90d608: r0 = AllocateArray()
    //     0x90d608: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d60c: stur            x0, [fp, #-8]
    // 0x90d610: r16 = Instance_Heir
    //     0x90d610: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90d614: ldr             x16, [x16, #0xb0]
    // 0x90d618: StoreField: r0->field_f = r16
    //     0x90d618: stur            w16, [x0, #0xf]
    // 0x90d61c: r16 = Instance_Heir
    //     0x90d61c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90d620: ldr             x16, [x16, #0x7e8]
    // 0x90d624: StoreField: r0->field_13 = r16
    //     0x90d624: stur            w16, [x0, #0x13]
    // 0x90d628: r1 = <Heir>
    //     0x90d628: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d62c: ldr             x1, [x1, #0xc0]
    // 0x90d630: r0 = AllocateGrowableArray()
    //     0x90d630: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d634: mov             x1, x0
    // 0x90d638: ldur            x0, [fp, #-8]
    // 0x90d63c: StoreField: r1->field_f = r0
    //     0x90d63c: stur            w0, [x1, #0xf]
    // 0x90d640: r0 = 4
    //     0x90d640: movz            x0, #0x4
    // 0x90d644: StoreField: r1->field_b = r0
    //     0x90d644: stur            w0, [x1, #0xb]
    // 0x90d648: mov             x2, x1
    // 0x90d64c: ldr             x1, [fp, #0x10]
    // 0x90d650: r0 = HeirsExt.hasEveryOf()
    //     0x90d650: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d654: tbnz            w0, #4, #0x90d66c
    // 0x90d658: r0 = Instance__$Mahjub
    //     0x90d658: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90d65c: ldr             x0, [x0, #0x980]
    // 0x90d660: LeaveFrame
    //     0x90d660: mov             SP, fp
    //     0x90d664: ldp             fp, lr, [SP], #0x10
    // 0x90d668: ret
    //     0x90d668: ret             
    // 0x90d66c: r0 = Instance__$Ashobah
    //     0x90d66c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90d670: ldr             x0, [x0, #0x988]
    // 0x90d674: LeaveFrame
    //     0x90d674: mov             SP, fp
    //     0x90d678: ldp             fp, lr, [SP], #0x10
    // 0x90d67c: ret
    //     0x90d67c: ret             
    // 0x90d680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90d680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90d684: b               #0x90d3bc
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90d688, size: 0x2dc
    // 0x90d688: EnterFrame
    //     0x90d688: stp             fp, lr, [SP, #-0x10]!
    //     0x90d68c: mov             fp, SP
    // 0x90d690: AllocStack(0x8)
    //     0x90d690: sub             SP, SP, #8
    // 0x90d694: SetupParameters()
    //     0x90d694: movz            x0, #0x14
    // 0x90d694: r0 = 20
    // 0x90d698: CheckStackOverflow
    //     0x90d698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90d69c: cmp             SP, x16
    //     0x90d6a0: b.ls            #0x90d95c
    // 0x90d6a4: mov             x2, x0
    // 0x90d6a8: r1 = <Heir>
    //     0x90d6a8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d6ac: ldr             x1, [x1, #0xc0]
    // 0x90d6b0: r0 = AllocateArray()
    //     0x90d6b0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d6b4: stur            x0, [fp, #-8]
    // 0x90d6b8: r16 = Instance_Heir
    //     0x90d6b8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90d6bc: ldr             x16, [x16, #0x7d0]
    // 0x90d6c0: StoreField: r0->field_f = r16
    //     0x90d6c0: stur            w16, [x0, #0xf]
    // 0x90d6c4: r16 = Instance_Heir
    //     0x90d6c4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90d6c8: ldr             x16, [x16, #0x7e0]
    // 0x90d6cc: StoreField: r0->field_13 = r16
    //     0x90d6cc: stur            w16, [x0, #0x13]
    // 0x90d6d0: r16 = Instance_Heir
    //     0x90d6d0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90d6d4: ldr             x16, [x16, #0x838]
    // 0x90d6d8: ArrayStore: r0[0] = r16  ; List_4
    //     0x90d6d8: stur            w16, [x0, #0x17]
    // 0x90d6dc: r16 = Instance_Heir
    //     0x90d6dc: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90d6e0: ldr             x16, [x16, #0x88]
    // 0x90d6e4: StoreField: r0->field_1b = r16
    //     0x90d6e4: stur            w16, [x0, #0x1b]
    // 0x90d6e8: r16 = Instance_Heir
    //     0x90d6e8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90d6ec: ldr             x16, [x16, #0x90]
    // 0x90d6f0: StoreField: r0->field_1f = r16
    //     0x90d6f0: stur            w16, [x0, #0x1f]
    // 0x90d6f4: r16 = Instance_Heir
    //     0x90d6f4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90d6f8: ldr             x16, [x16, #0x98]
    // 0x90d6fc: StoreField: r0->field_23 = r16
    //     0x90d6fc: stur            w16, [x0, #0x23]
    // 0x90d700: r16 = Instance_Heir
    //     0x90d700: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90d704: ldr             x16, [x16, #0x868]
    // 0x90d708: StoreField: r0->field_27 = r16
    //     0x90d708: stur            w16, [x0, #0x27]
    // 0x90d70c: r16 = Instance_Heir
    //     0x90d70c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x90d710: ldr             x16, [x16, #0x878]
    // 0x90d714: StoreField: r0->field_2b = r16
    //     0x90d714: stur            w16, [x0, #0x2b]
    // 0x90d718: r16 = Instance_Heir
    //     0x90d718: add             x16, PP, #0x31, lsl #12  ; [pp+0x31888] Obj!Heir@e2db71
    //     0x90d71c: ldr             x16, [x16, #0x888]
    // 0x90d720: StoreField: r0->field_2f = r16
    //     0x90d720: stur            w16, [x0, #0x2f]
    // 0x90d724: r16 = Instance_Heir
    //     0x90d724: add             x16, PP, #0x31, lsl #12  ; [pp+0x31898] Obj!Heir@e2db41
    //     0x90d728: ldr             x16, [x16, #0x898]
    // 0x90d72c: StoreField: r0->field_33 = r16
    //     0x90d72c: stur            w16, [x0, #0x33]
    // 0x90d730: r1 = <Heir>
    //     0x90d730: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d734: ldr             x1, [x1, #0xc0]
    // 0x90d738: r0 = AllocateGrowableArray()
    //     0x90d738: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d73c: mov             x1, x0
    // 0x90d740: ldur            x0, [fp, #-8]
    // 0x90d744: StoreField: r1->field_f = r0
    //     0x90d744: stur            w0, [x1, #0xf]
    // 0x90d748: r0 = 20
    //     0x90d748: movz            x0, #0x14
    // 0x90d74c: StoreField: r1->field_b = r0
    //     0x90d74c: stur            w0, [x1, #0xb]
    // 0x90d750: mov             x2, x1
    // 0x90d754: ldr             x1, [fp, #0x10]
    // 0x90d758: r0 = HeirsExt.search()
    //     0x90d758: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90d75c: stur            x0, [fp, #-8]
    // 0x90d760: LoadField: r1 = r0->field_b
    //     0x90d760: ldur            w1, [x0, #0xb]
    // 0x90d764: cbz             w1, #0x90d788
    // 0x90d768: r0 = _$Mahjub()
    //     0x90d768: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90d76c: mov             x1, x0
    // 0x90d770: ldur            x0, [fp, #-8]
    // 0x90d774: StoreField: r1->field_7 = r0
    //     0x90d774: stur            w0, [x1, #7]
    // 0x90d778: mov             x0, x1
    // 0x90d77c: LeaveFrame
    //     0x90d77c: mov             SP, fp
    //     0x90d780: ldp             fp, lr, [SP], #0x10
    // 0x90d784: ret
    //     0x90d784: ret             
    // 0x90d788: r0 = 4
    //     0x90d788: movz            x0, #0x4
    // 0x90d78c: mov             x2, x0
    // 0x90d790: r1 = Null
    //     0x90d790: mov             x1, NULL
    // 0x90d794: r0 = AllocateArray()
    //     0x90d794: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d798: stur            x0, [fp, #-8]
    // 0x90d79c: r16 = Instance_Heir
    //     0x90d79c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90d7a0: ldr             x16, [x16, #0xa8]
    // 0x90d7a4: StoreField: r0->field_f = r16
    //     0x90d7a4: stur            w16, [x0, #0xf]
    // 0x90d7a8: r16 = Instance_Heir
    //     0x90d7a8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90d7ac: ldr             x16, [x16, #0x7d8]
    // 0x90d7b0: StoreField: r0->field_13 = r16
    //     0x90d7b0: stur            w16, [x0, #0x13]
    // 0x90d7b4: r1 = <Heir>
    //     0x90d7b4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d7b8: ldr             x1, [x1, #0xc0]
    // 0x90d7bc: r0 = AllocateGrowableArray()
    //     0x90d7bc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d7c0: mov             x1, x0
    // 0x90d7c4: ldur            x0, [fp, #-8]
    // 0x90d7c8: StoreField: r1->field_f = r0
    //     0x90d7c8: stur            w0, [x1, #0xf]
    // 0x90d7cc: r0 = 4
    //     0x90d7cc: movz            x0, #0x4
    // 0x90d7d0: StoreField: r1->field_b = r0
    //     0x90d7d0: stur            w0, [x1, #0xb]
    // 0x90d7d4: mov             x2, x1
    // 0x90d7d8: ldr             x1, [fp, #0x10]
    // 0x90d7dc: r0 = HeirsExt.hasEveryOf()
    //     0x90d7dc: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d7e0: tbnz            w0, #4, #0x90d7f8
    // 0x90d7e4: r0 = Instance__$Mahjub
    //     0x90d7e4: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90d7e8: ldr             x0, [x0, #0x9b0]
    // 0x90d7ec: LeaveFrame
    //     0x90d7ec: mov             SP, fp
    //     0x90d7f0: ldp             fp, lr, [SP], #0x10
    // 0x90d7f4: ret
    //     0x90d7f4: ret             
    // 0x90d7f8: r0 = 4
    //     0x90d7f8: movz            x0, #0x4
    // 0x90d7fc: mov             x2, x0
    // 0x90d800: r1 = Null
    //     0x90d800: mov             x1, NULL
    // 0x90d804: r0 = AllocateArray()
    //     0x90d804: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d808: stur            x0, [fp, #-8]
    // 0x90d80c: r16 = Instance_Heir
    //     0x90d80c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90d810: ldr             x16, [x16, #0xa8]
    // 0x90d814: StoreField: r0->field_f = r16
    //     0x90d814: stur            w16, [x0, #0xf]
    // 0x90d818: r16 = Instance_Heir
    //     0x90d818: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90d81c: ldr             x16, [x16, #0x7e8]
    // 0x90d820: StoreField: r0->field_13 = r16
    //     0x90d820: stur            w16, [x0, #0x13]
    // 0x90d824: r1 = <Heir>
    //     0x90d824: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d828: ldr             x1, [x1, #0xc0]
    // 0x90d82c: r0 = AllocateGrowableArray()
    //     0x90d82c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d830: mov             x1, x0
    // 0x90d834: ldur            x0, [fp, #-8]
    // 0x90d838: StoreField: r1->field_f = r0
    //     0x90d838: stur            w0, [x1, #0xf]
    // 0x90d83c: r0 = 4
    //     0x90d83c: movz            x0, #0x4
    // 0x90d840: StoreField: r1->field_b = r0
    //     0x90d840: stur            w0, [x1, #0xb]
    // 0x90d844: mov             x2, x1
    // 0x90d848: ldr             x1, [fp, #0x10]
    // 0x90d84c: r0 = HeirsExt.hasEveryOf()
    //     0x90d84c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d850: tbnz            w0, #4, #0x90d868
    // 0x90d854: r0 = Instance__$Mahjub
    //     0x90d854: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90d858: ldr             x0, [x0, #0x9b8]
    // 0x90d85c: LeaveFrame
    //     0x90d85c: mov             SP, fp
    //     0x90d860: ldp             fp, lr, [SP], #0x10
    // 0x90d864: ret
    //     0x90d864: ret             
    // 0x90d868: r0 = 4
    //     0x90d868: movz            x0, #0x4
    // 0x90d86c: mov             x2, x0
    // 0x90d870: r1 = Null
    //     0x90d870: mov             x1, NULL
    // 0x90d874: r0 = AllocateArray()
    //     0x90d874: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d878: stur            x0, [fp, #-8]
    // 0x90d87c: r16 = Instance_Heir
    //     0x90d87c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90d880: ldr             x16, [x16, #0xb0]
    // 0x90d884: StoreField: r0->field_f = r16
    //     0x90d884: stur            w16, [x0, #0xf]
    // 0x90d888: r16 = Instance_Heir
    //     0x90d888: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90d88c: ldr             x16, [x16, #0x7d8]
    // 0x90d890: StoreField: r0->field_13 = r16
    //     0x90d890: stur            w16, [x0, #0x13]
    // 0x90d894: r1 = <Heir>
    //     0x90d894: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d898: ldr             x1, [x1, #0xc0]
    // 0x90d89c: r0 = AllocateGrowableArray()
    //     0x90d89c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d8a0: mov             x1, x0
    // 0x90d8a4: ldur            x0, [fp, #-8]
    // 0x90d8a8: StoreField: r1->field_f = r0
    //     0x90d8a8: stur            w0, [x1, #0xf]
    // 0x90d8ac: r0 = 4
    //     0x90d8ac: movz            x0, #0x4
    // 0x90d8b0: StoreField: r1->field_b = r0
    //     0x90d8b0: stur            w0, [x1, #0xb]
    // 0x90d8b4: mov             x2, x1
    // 0x90d8b8: ldr             x1, [fp, #0x10]
    // 0x90d8bc: r0 = HeirsExt.hasEveryOf()
    //     0x90d8bc: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d8c0: tbnz            w0, #4, #0x90d8d8
    // 0x90d8c4: r0 = Instance__$Mahjub
    //     0x90d8c4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90d8c8: ldr             x0, [x0, #0x978]
    // 0x90d8cc: LeaveFrame
    //     0x90d8cc: mov             SP, fp
    //     0x90d8d0: ldp             fp, lr, [SP], #0x10
    // 0x90d8d4: ret
    //     0x90d8d4: ret             
    // 0x90d8d8: r0 = 4
    //     0x90d8d8: movz            x0, #0x4
    // 0x90d8dc: mov             x2, x0
    // 0x90d8e0: r1 = Null
    //     0x90d8e0: mov             x1, NULL
    // 0x90d8e4: r0 = AllocateArray()
    //     0x90d8e4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d8e8: stur            x0, [fp, #-8]
    // 0x90d8ec: r16 = Instance_Heir
    //     0x90d8ec: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90d8f0: ldr             x16, [x16, #0xb0]
    // 0x90d8f4: StoreField: r0->field_f = r16
    //     0x90d8f4: stur            w16, [x0, #0xf]
    // 0x90d8f8: r16 = Instance_Heir
    //     0x90d8f8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90d8fc: ldr             x16, [x16, #0x7e8]
    // 0x90d900: StoreField: r0->field_13 = r16
    //     0x90d900: stur            w16, [x0, #0x13]
    // 0x90d904: r1 = <Heir>
    //     0x90d904: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d908: ldr             x1, [x1, #0xc0]
    // 0x90d90c: r0 = AllocateGrowableArray()
    //     0x90d90c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90d910: mov             x1, x0
    // 0x90d914: ldur            x0, [fp, #-8]
    // 0x90d918: StoreField: r1->field_f = r0
    //     0x90d918: stur            w0, [x1, #0xf]
    // 0x90d91c: r0 = 4
    //     0x90d91c: movz            x0, #0x4
    // 0x90d920: StoreField: r1->field_b = r0
    //     0x90d920: stur            w0, [x1, #0xb]
    // 0x90d924: mov             x2, x1
    // 0x90d928: ldr             x1, [fp, #0x10]
    // 0x90d92c: r0 = HeirsExt.hasEveryOf()
    //     0x90d92c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90d930: tbnz            w0, #4, #0x90d948
    // 0x90d934: r0 = Instance__$Mahjub
    //     0x90d934: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90d938: ldr             x0, [x0, #0x980]
    // 0x90d93c: LeaveFrame
    //     0x90d93c: mov             SP, fp
    //     0x90d940: ldp             fp, lr, [SP], #0x10
    // 0x90d944: ret
    //     0x90d944: ret             
    // 0x90d948: r0 = Instance__$Ashobah
    //     0x90d948: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90d94c: ldr             x0, [x0, #0x988]
    // 0x90d950: LeaveFrame
    //     0x90d950: mov             SP, fp
    //     0x90d954: ldp             fp, lr, [SP], #0x10
    // 0x90d958: ret
    //     0x90d958: ret             
    // 0x90d95c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90d95c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90d960: b               #0x90d6a4
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90d964, size: 0x2d0
    // 0x90d964: EnterFrame
    //     0x90d964: stp             fp, lr, [SP, #-0x10]!
    //     0x90d968: mov             fp, SP
    // 0x90d96c: AllocStack(0x8)
    //     0x90d96c: sub             SP, SP, #8
    // 0x90d970: SetupParameters()
    //     0x90d970: movz            x0, #0x12
    // 0x90d970: r0 = 18
    // 0x90d974: CheckStackOverflow
    //     0x90d974: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90d978: cmp             SP, x16
    //     0x90d97c: b.ls            #0x90dc2c
    // 0x90d980: mov             x2, x0
    // 0x90d984: r1 = <Heir>
    //     0x90d984: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90d988: ldr             x1, [x1, #0xc0]
    // 0x90d98c: r0 = AllocateArray()
    //     0x90d98c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90d990: stur            x0, [fp, #-8]
    // 0x90d994: r16 = Instance_Heir
    //     0x90d994: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90d998: ldr             x16, [x16, #0x7d0]
    // 0x90d99c: StoreField: r0->field_f = r16
    //     0x90d99c: stur            w16, [x0, #0xf]
    // 0x90d9a0: r16 = Instance_Heir
    //     0x90d9a0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90d9a4: ldr             x16, [x16, #0x7e0]
    // 0x90d9a8: StoreField: r0->field_13 = r16
    //     0x90d9a8: stur            w16, [x0, #0x13]
    // 0x90d9ac: r16 = Instance_Heir
    //     0x90d9ac: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90d9b0: ldr             x16, [x16, #0x838]
    // 0x90d9b4: ArrayStore: r0[0] = r16  ; List_4
    //     0x90d9b4: stur            w16, [x0, #0x17]
    // 0x90d9b8: r16 = Instance_Heir
    //     0x90d9b8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90d9bc: ldr             x16, [x16, #0x88]
    // 0x90d9c0: StoreField: r0->field_1b = r16
    //     0x90d9c0: stur            w16, [x0, #0x1b]
    // 0x90d9c4: r16 = Instance_Heir
    //     0x90d9c4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90d9c8: ldr             x16, [x16, #0x90]
    // 0x90d9cc: StoreField: r0->field_1f = r16
    //     0x90d9cc: stur            w16, [x0, #0x1f]
    // 0x90d9d0: r16 = Instance_Heir
    //     0x90d9d0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90d9d4: ldr             x16, [x16, #0x98]
    // 0x90d9d8: StoreField: r0->field_23 = r16
    //     0x90d9d8: stur            w16, [x0, #0x23]
    // 0x90d9dc: r16 = Instance_Heir
    //     0x90d9dc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90d9e0: ldr             x16, [x16, #0x868]
    // 0x90d9e4: StoreField: r0->field_27 = r16
    //     0x90d9e4: stur            w16, [x0, #0x27]
    // 0x90d9e8: r16 = Instance_Heir
    //     0x90d9e8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x90d9ec: ldr             x16, [x16, #0x878]
    // 0x90d9f0: StoreField: r0->field_2b = r16
    //     0x90d9f0: stur            w16, [x0, #0x2b]
    // 0x90d9f4: r16 = Instance_Heir
    //     0x90d9f4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31888] Obj!Heir@e2db71
    //     0x90d9f8: ldr             x16, [x16, #0x888]
    // 0x90d9fc: StoreField: r0->field_2f = r16
    //     0x90d9fc: stur            w16, [x0, #0x2f]
    // 0x90da00: r1 = <Heir>
    //     0x90da00: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90da04: ldr             x1, [x1, #0xc0]
    // 0x90da08: r0 = AllocateGrowableArray()
    //     0x90da08: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90da0c: mov             x1, x0
    // 0x90da10: ldur            x0, [fp, #-8]
    // 0x90da14: StoreField: r1->field_f = r0
    //     0x90da14: stur            w0, [x1, #0xf]
    // 0x90da18: r0 = 18
    //     0x90da18: movz            x0, #0x12
    // 0x90da1c: StoreField: r1->field_b = r0
    //     0x90da1c: stur            w0, [x1, #0xb]
    // 0x90da20: mov             x2, x1
    // 0x90da24: ldr             x1, [fp, #0x10]
    // 0x90da28: r0 = HeirsExt.search()
    //     0x90da28: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90da2c: stur            x0, [fp, #-8]
    // 0x90da30: LoadField: r1 = r0->field_b
    //     0x90da30: ldur            w1, [x0, #0xb]
    // 0x90da34: cbz             w1, #0x90da58
    // 0x90da38: r0 = _$Mahjub()
    //     0x90da38: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90da3c: mov             x1, x0
    // 0x90da40: ldur            x0, [fp, #-8]
    // 0x90da44: StoreField: r1->field_7 = r0
    //     0x90da44: stur            w0, [x1, #7]
    // 0x90da48: mov             x0, x1
    // 0x90da4c: LeaveFrame
    //     0x90da4c: mov             SP, fp
    //     0x90da50: ldp             fp, lr, [SP], #0x10
    // 0x90da54: ret
    //     0x90da54: ret             
    // 0x90da58: r0 = 4
    //     0x90da58: movz            x0, #0x4
    // 0x90da5c: mov             x2, x0
    // 0x90da60: r1 = Null
    //     0x90da60: mov             x1, NULL
    // 0x90da64: r0 = AllocateArray()
    //     0x90da64: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90da68: stur            x0, [fp, #-8]
    // 0x90da6c: r16 = Instance_Heir
    //     0x90da6c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90da70: ldr             x16, [x16, #0xa8]
    // 0x90da74: StoreField: r0->field_f = r16
    //     0x90da74: stur            w16, [x0, #0xf]
    // 0x90da78: r16 = Instance_Heir
    //     0x90da78: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90da7c: ldr             x16, [x16, #0x7d8]
    // 0x90da80: StoreField: r0->field_13 = r16
    //     0x90da80: stur            w16, [x0, #0x13]
    // 0x90da84: r1 = <Heir>
    //     0x90da84: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90da88: ldr             x1, [x1, #0xc0]
    // 0x90da8c: r0 = AllocateGrowableArray()
    //     0x90da8c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90da90: mov             x1, x0
    // 0x90da94: ldur            x0, [fp, #-8]
    // 0x90da98: StoreField: r1->field_f = r0
    //     0x90da98: stur            w0, [x1, #0xf]
    // 0x90da9c: r0 = 4
    //     0x90da9c: movz            x0, #0x4
    // 0x90daa0: StoreField: r1->field_b = r0
    //     0x90daa0: stur            w0, [x1, #0xb]
    // 0x90daa4: mov             x2, x1
    // 0x90daa8: ldr             x1, [fp, #0x10]
    // 0x90daac: r0 = HeirsExt.hasEveryOf()
    //     0x90daac: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90dab0: tbnz            w0, #4, #0x90dac8
    // 0x90dab4: r0 = Instance__$Mahjub
    //     0x90dab4: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90dab8: ldr             x0, [x0, #0x9b0]
    // 0x90dabc: LeaveFrame
    //     0x90dabc: mov             SP, fp
    //     0x90dac0: ldp             fp, lr, [SP], #0x10
    // 0x90dac4: ret
    //     0x90dac4: ret             
    // 0x90dac8: r0 = 4
    //     0x90dac8: movz            x0, #0x4
    // 0x90dacc: mov             x2, x0
    // 0x90dad0: r1 = Null
    //     0x90dad0: mov             x1, NULL
    // 0x90dad4: r0 = AllocateArray()
    //     0x90dad4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90dad8: stur            x0, [fp, #-8]
    // 0x90dadc: r16 = Instance_Heir
    //     0x90dadc: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90dae0: ldr             x16, [x16, #0xa8]
    // 0x90dae4: StoreField: r0->field_f = r16
    //     0x90dae4: stur            w16, [x0, #0xf]
    // 0x90dae8: r16 = Instance_Heir
    //     0x90dae8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90daec: ldr             x16, [x16, #0x7e8]
    // 0x90daf0: StoreField: r0->field_13 = r16
    //     0x90daf0: stur            w16, [x0, #0x13]
    // 0x90daf4: r1 = <Heir>
    //     0x90daf4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90daf8: ldr             x1, [x1, #0xc0]
    // 0x90dafc: r0 = AllocateGrowableArray()
    //     0x90dafc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90db00: mov             x1, x0
    // 0x90db04: ldur            x0, [fp, #-8]
    // 0x90db08: StoreField: r1->field_f = r0
    //     0x90db08: stur            w0, [x1, #0xf]
    // 0x90db0c: r0 = 4
    //     0x90db0c: movz            x0, #0x4
    // 0x90db10: StoreField: r1->field_b = r0
    //     0x90db10: stur            w0, [x1, #0xb]
    // 0x90db14: mov             x2, x1
    // 0x90db18: ldr             x1, [fp, #0x10]
    // 0x90db1c: r0 = HeirsExt.hasEveryOf()
    //     0x90db1c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90db20: tbnz            w0, #4, #0x90db38
    // 0x90db24: r0 = Instance__$Mahjub
    //     0x90db24: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90db28: ldr             x0, [x0, #0x9b8]
    // 0x90db2c: LeaveFrame
    //     0x90db2c: mov             SP, fp
    //     0x90db30: ldp             fp, lr, [SP], #0x10
    // 0x90db34: ret
    //     0x90db34: ret             
    // 0x90db38: r0 = 4
    //     0x90db38: movz            x0, #0x4
    // 0x90db3c: mov             x2, x0
    // 0x90db40: r1 = Null
    //     0x90db40: mov             x1, NULL
    // 0x90db44: r0 = AllocateArray()
    //     0x90db44: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90db48: stur            x0, [fp, #-8]
    // 0x90db4c: r16 = Instance_Heir
    //     0x90db4c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90db50: ldr             x16, [x16, #0xb0]
    // 0x90db54: StoreField: r0->field_f = r16
    //     0x90db54: stur            w16, [x0, #0xf]
    // 0x90db58: r16 = Instance_Heir
    //     0x90db58: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90db5c: ldr             x16, [x16, #0x7d8]
    // 0x90db60: StoreField: r0->field_13 = r16
    //     0x90db60: stur            w16, [x0, #0x13]
    // 0x90db64: r1 = <Heir>
    //     0x90db64: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90db68: ldr             x1, [x1, #0xc0]
    // 0x90db6c: r0 = AllocateGrowableArray()
    //     0x90db6c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90db70: mov             x1, x0
    // 0x90db74: ldur            x0, [fp, #-8]
    // 0x90db78: StoreField: r1->field_f = r0
    //     0x90db78: stur            w0, [x1, #0xf]
    // 0x90db7c: r0 = 4
    //     0x90db7c: movz            x0, #0x4
    // 0x90db80: StoreField: r1->field_b = r0
    //     0x90db80: stur            w0, [x1, #0xb]
    // 0x90db84: mov             x2, x1
    // 0x90db88: ldr             x1, [fp, #0x10]
    // 0x90db8c: r0 = HeirsExt.hasEveryOf()
    //     0x90db8c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90db90: tbnz            w0, #4, #0x90dba8
    // 0x90db94: r0 = Instance__$Mahjub
    //     0x90db94: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90db98: ldr             x0, [x0, #0x978]
    // 0x90db9c: LeaveFrame
    //     0x90db9c: mov             SP, fp
    //     0x90dba0: ldp             fp, lr, [SP], #0x10
    // 0x90dba4: ret
    //     0x90dba4: ret             
    // 0x90dba8: r0 = 4
    //     0x90dba8: movz            x0, #0x4
    // 0x90dbac: mov             x2, x0
    // 0x90dbb0: r1 = Null
    //     0x90dbb0: mov             x1, NULL
    // 0x90dbb4: r0 = AllocateArray()
    //     0x90dbb4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90dbb8: stur            x0, [fp, #-8]
    // 0x90dbbc: r16 = Instance_Heir
    //     0x90dbbc: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90dbc0: ldr             x16, [x16, #0xb0]
    // 0x90dbc4: StoreField: r0->field_f = r16
    //     0x90dbc4: stur            w16, [x0, #0xf]
    // 0x90dbc8: r16 = Instance_Heir
    //     0x90dbc8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90dbcc: ldr             x16, [x16, #0x7e8]
    // 0x90dbd0: StoreField: r0->field_13 = r16
    //     0x90dbd0: stur            w16, [x0, #0x13]
    // 0x90dbd4: r1 = <Heir>
    //     0x90dbd4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90dbd8: ldr             x1, [x1, #0xc0]
    // 0x90dbdc: r0 = AllocateGrowableArray()
    //     0x90dbdc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90dbe0: mov             x1, x0
    // 0x90dbe4: ldur            x0, [fp, #-8]
    // 0x90dbe8: StoreField: r1->field_f = r0
    //     0x90dbe8: stur            w0, [x1, #0xf]
    // 0x90dbec: r0 = 4
    //     0x90dbec: movz            x0, #0x4
    // 0x90dbf0: StoreField: r1->field_b = r0
    //     0x90dbf0: stur            w0, [x1, #0xb]
    // 0x90dbf4: mov             x2, x1
    // 0x90dbf8: ldr             x1, [fp, #0x10]
    // 0x90dbfc: r0 = HeirsExt.hasEveryOf()
    //     0x90dbfc: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90dc00: tbnz            w0, #4, #0x90dc18
    // 0x90dc04: r0 = Instance__$Mahjub
    //     0x90dc04: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90dc08: ldr             x0, [x0, #0x980]
    // 0x90dc0c: LeaveFrame
    //     0x90dc0c: mov             SP, fp
    //     0x90dc10: ldp             fp, lr, [SP], #0x10
    // 0x90dc14: ret
    //     0x90dc14: ret             
    // 0x90dc18: r0 = Instance__$Ashobah
    //     0x90dc18: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90dc1c: ldr             x0, [x0, #0x988]
    // 0x90dc20: LeaveFrame
    //     0x90dc20: mov             SP, fp
    //     0x90dc24: ldp             fp, lr, [SP], #0x10
    // 0x90dc28: ret
    //     0x90dc28: ret             
    // 0x90dc2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90dc2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90dc30: b               #0x90d980
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90dc34, size: 0x2c0
    // 0x90dc34: EnterFrame
    //     0x90dc34: stp             fp, lr, [SP, #-0x10]!
    //     0x90dc38: mov             fp, SP
    // 0x90dc3c: AllocStack(0x8)
    //     0x90dc3c: sub             SP, SP, #8
    // 0x90dc40: SetupParameters()
    //     0x90dc40: movz            x0, #0x10
    // 0x90dc40: r0 = 16
    // 0x90dc44: CheckStackOverflow
    //     0x90dc44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90dc48: cmp             SP, x16
    //     0x90dc4c: b.ls            #0x90deec
    // 0x90dc50: mov             x2, x0
    // 0x90dc54: r1 = Null
    //     0x90dc54: mov             x1, NULL
    // 0x90dc58: r0 = AllocateArray()
    //     0x90dc58: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90dc5c: stur            x0, [fp, #-8]
    // 0x90dc60: r16 = Instance_Heir
    //     0x90dc60: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90dc64: ldr             x16, [x16, #0x7d0]
    // 0x90dc68: StoreField: r0->field_f = r16
    //     0x90dc68: stur            w16, [x0, #0xf]
    // 0x90dc6c: r16 = Instance_Heir
    //     0x90dc6c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90dc70: ldr             x16, [x16, #0x7e0]
    // 0x90dc74: StoreField: r0->field_13 = r16
    //     0x90dc74: stur            w16, [x0, #0x13]
    // 0x90dc78: r16 = Instance_Heir
    //     0x90dc78: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90dc7c: ldr             x16, [x16, #0x838]
    // 0x90dc80: ArrayStore: r0[0] = r16  ; List_4
    //     0x90dc80: stur            w16, [x0, #0x17]
    // 0x90dc84: r16 = Instance_Heir
    //     0x90dc84: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90dc88: ldr             x16, [x16, #0x88]
    // 0x90dc8c: StoreField: r0->field_1b = r16
    //     0x90dc8c: stur            w16, [x0, #0x1b]
    // 0x90dc90: r16 = Instance_Heir
    //     0x90dc90: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90dc94: ldr             x16, [x16, #0x90]
    // 0x90dc98: StoreField: r0->field_1f = r16
    //     0x90dc98: stur            w16, [x0, #0x1f]
    // 0x90dc9c: r16 = Instance_Heir
    //     0x90dc9c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90dca0: ldr             x16, [x16, #0x98]
    // 0x90dca4: StoreField: r0->field_23 = r16
    //     0x90dca4: stur            w16, [x0, #0x23]
    // 0x90dca8: r16 = Instance_Heir
    //     0x90dca8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90dcac: ldr             x16, [x16, #0x868]
    // 0x90dcb0: StoreField: r0->field_27 = r16
    //     0x90dcb0: stur            w16, [x0, #0x27]
    // 0x90dcb4: r16 = Instance_Heir
    //     0x90dcb4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31878] Obj!Heir@e2dba1
    //     0x90dcb8: ldr             x16, [x16, #0x878]
    // 0x90dcbc: StoreField: r0->field_2b = r16
    //     0x90dcbc: stur            w16, [x0, #0x2b]
    // 0x90dcc0: r1 = <Heir>
    //     0x90dcc0: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90dcc4: ldr             x1, [x1, #0xc0]
    // 0x90dcc8: r0 = AllocateGrowableArray()
    //     0x90dcc8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90dccc: mov             x1, x0
    // 0x90dcd0: ldur            x0, [fp, #-8]
    // 0x90dcd4: StoreField: r1->field_f = r0
    //     0x90dcd4: stur            w0, [x1, #0xf]
    // 0x90dcd8: r0 = 16
    //     0x90dcd8: movz            x0, #0x10
    // 0x90dcdc: StoreField: r1->field_b = r0
    //     0x90dcdc: stur            w0, [x1, #0xb]
    // 0x90dce0: mov             x2, x1
    // 0x90dce4: ldr             x1, [fp, #0x10]
    // 0x90dce8: r0 = HeirsExt.search()
    //     0x90dce8: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90dcec: stur            x0, [fp, #-8]
    // 0x90dcf0: LoadField: r1 = r0->field_b
    //     0x90dcf0: ldur            w1, [x0, #0xb]
    // 0x90dcf4: cbz             w1, #0x90dd18
    // 0x90dcf8: r0 = _$Mahjub()
    //     0x90dcf8: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90dcfc: mov             x1, x0
    // 0x90dd00: ldur            x0, [fp, #-8]
    // 0x90dd04: StoreField: r1->field_7 = r0
    //     0x90dd04: stur            w0, [x1, #7]
    // 0x90dd08: mov             x0, x1
    // 0x90dd0c: LeaveFrame
    //     0x90dd0c: mov             SP, fp
    //     0x90dd10: ldp             fp, lr, [SP], #0x10
    // 0x90dd14: ret
    //     0x90dd14: ret             
    // 0x90dd18: r0 = 4
    //     0x90dd18: movz            x0, #0x4
    // 0x90dd1c: mov             x2, x0
    // 0x90dd20: r1 = Null
    //     0x90dd20: mov             x1, NULL
    // 0x90dd24: r0 = AllocateArray()
    //     0x90dd24: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90dd28: stur            x0, [fp, #-8]
    // 0x90dd2c: r16 = Instance_Heir
    //     0x90dd2c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90dd30: ldr             x16, [x16, #0xa8]
    // 0x90dd34: StoreField: r0->field_f = r16
    //     0x90dd34: stur            w16, [x0, #0xf]
    // 0x90dd38: r16 = Instance_Heir
    //     0x90dd38: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90dd3c: ldr             x16, [x16, #0x7d8]
    // 0x90dd40: StoreField: r0->field_13 = r16
    //     0x90dd40: stur            w16, [x0, #0x13]
    // 0x90dd44: r1 = <Heir>
    //     0x90dd44: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90dd48: ldr             x1, [x1, #0xc0]
    // 0x90dd4c: r0 = AllocateGrowableArray()
    //     0x90dd4c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90dd50: mov             x1, x0
    // 0x90dd54: ldur            x0, [fp, #-8]
    // 0x90dd58: StoreField: r1->field_f = r0
    //     0x90dd58: stur            w0, [x1, #0xf]
    // 0x90dd5c: r0 = 4
    //     0x90dd5c: movz            x0, #0x4
    // 0x90dd60: StoreField: r1->field_b = r0
    //     0x90dd60: stur            w0, [x1, #0xb]
    // 0x90dd64: mov             x2, x1
    // 0x90dd68: ldr             x1, [fp, #0x10]
    // 0x90dd6c: r0 = HeirsExt.hasEveryOf()
    //     0x90dd6c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90dd70: tbnz            w0, #4, #0x90dd88
    // 0x90dd74: r0 = Instance__$Mahjub
    //     0x90dd74: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90dd78: ldr             x0, [x0, #0x9b0]
    // 0x90dd7c: LeaveFrame
    //     0x90dd7c: mov             SP, fp
    //     0x90dd80: ldp             fp, lr, [SP], #0x10
    // 0x90dd84: ret
    //     0x90dd84: ret             
    // 0x90dd88: r0 = 4
    //     0x90dd88: movz            x0, #0x4
    // 0x90dd8c: mov             x2, x0
    // 0x90dd90: r1 = Null
    //     0x90dd90: mov             x1, NULL
    // 0x90dd94: r0 = AllocateArray()
    //     0x90dd94: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90dd98: stur            x0, [fp, #-8]
    // 0x90dd9c: r16 = Instance_Heir
    //     0x90dd9c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90dda0: ldr             x16, [x16, #0xa8]
    // 0x90dda4: StoreField: r0->field_f = r16
    //     0x90dda4: stur            w16, [x0, #0xf]
    // 0x90dda8: r16 = Instance_Heir
    //     0x90dda8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90ddac: ldr             x16, [x16, #0x7e8]
    // 0x90ddb0: StoreField: r0->field_13 = r16
    //     0x90ddb0: stur            w16, [x0, #0x13]
    // 0x90ddb4: r1 = <Heir>
    //     0x90ddb4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90ddb8: ldr             x1, [x1, #0xc0]
    // 0x90ddbc: r0 = AllocateGrowableArray()
    //     0x90ddbc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90ddc0: mov             x1, x0
    // 0x90ddc4: ldur            x0, [fp, #-8]
    // 0x90ddc8: StoreField: r1->field_f = r0
    //     0x90ddc8: stur            w0, [x1, #0xf]
    // 0x90ddcc: r0 = 4
    //     0x90ddcc: movz            x0, #0x4
    // 0x90ddd0: StoreField: r1->field_b = r0
    //     0x90ddd0: stur            w0, [x1, #0xb]
    // 0x90ddd4: mov             x2, x1
    // 0x90ddd8: ldr             x1, [fp, #0x10]
    // 0x90dddc: r0 = HeirsExt.hasEveryOf()
    //     0x90dddc: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90dde0: tbnz            w0, #4, #0x90ddf8
    // 0x90dde4: r0 = Instance__$Mahjub
    //     0x90dde4: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90dde8: ldr             x0, [x0, #0x9b8]
    // 0x90ddec: LeaveFrame
    //     0x90ddec: mov             SP, fp
    //     0x90ddf0: ldp             fp, lr, [SP], #0x10
    // 0x90ddf4: ret
    //     0x90ddf4: ret             
    // 0x90ddf8: r0 = 4
    //     0x90ddf8: movz            x0, #0x4
    // 0x90ddfc: mov             x2, x0
    // 0x90de00: r1 = Null
    //     0x90de00: mov             x1, NULL
    // 0x90de04: r0 = AllocateArray()
    //     0x90de04: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90de08: stur            x0, [fp, #-8]
    // 0x90de0c: r16 = Instance_Heir
    //     0x90de0c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90de10: ldr             x16, [x16, #0xb0]
    // 0x90de14: StoreField: r0->field_f = r16
    //     0x90de14: stur            w16, [x0, #0xf]
    // 0x90de18: r16 = Instance_Heir
    //     0x90de18: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90de1c: ldr             x16, [x16, #0x7d8]
    // 0x90de20: StoreField: r0->field_13 = r16
    //     0x90de20: stur            w16, [x0, #0x13]
    // 0x90de24: r1 = <Heir>
    //     0x90de24: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90de28: ldr             x1, [x1, #0xc0]
    // 0x90de2c: r0 = AllocateGrowableArray()
    //     0x90de2c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90de30: mov             x1, x0
    // 0x90de34: ldur            x0, [fp, #-8]
    // 0x90de38: StoreField: r1->field_f = r0
    //     0x90de38: stur            w0, [x1, #0xf]
    // 0x90de3c: r0 = 4
    //     0x90de3c: movz            x0, #0x4
    // 0x90de40: StoreField: r1->field_b = r0
    //     0x90de40: stur            w0, [x1, #0xb]
    // 0x90de44: mov             x2, x1
    // 0x90de48: ldr             x1, [fp, #0x10]
    // 0x90de4c: r0 = HeirsExt.hasEveryOf()
    //     0x90de4c: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90de50: tbnz            w0, #4, #0x90de68
    // 0x90de54: r0 = Instance__$Mahjub
    //     0x90de54: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90de58: ldr             x0, [x0, #0x978]
    // 0x90de5c: LeaveFrame
    //     0x90de5c: mov             SP, fp
    //     0x90de60: ldp             fp, lr, [SP], #0x10
    // 0x90de64: ret
    //     0x90de64: ret             
    // 0x90de68: r0 = 4
    //     0x90de68: movz            x0, #0x4
    // 0x90de6c: mov             x2, x0
    // 0x90de70: r1 = Null
    //     0x90de70: mov             x1, NULL
    // 0x90de74: r0 = AllocateArray()
    //     0x90de74: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90de78: stur            x0, [fp, #-8]
    // 0x90de7c: r16 = Instance_Heir
    //     0x90de7c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90de80: ldr             x16, [x16, #0xb0]
    // 0x90de84: StoreField: r0->field_f = r16
    //     0x90de84: stur            w16, [x0, #0xf]
    // 0x90de88: r16 = Instance_Heir
    //     0x90de88: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90de8c: ldr             x16, [x16, #0x7e8]
    // 0x90de90: StoreField: r0->field_13 = r16
    //     0x90de90: stur            w16, [x0, #0x13]
    // 0x90de94: r1 = <Heir>
    //     0x90de94: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90de98: ldr             x1, [x1, #0xc0]
    // 0x90de9c: r0 = AllocateGrowableArray()
    //     0x90de9c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90dea0: mov             x1, x0
    // 0x90dea4: ldur            x0, [fp, #-8]
    // 0x90dea8: StoreField: r1->field_f = r0
    //     0x90dea8: stur            w0, [x1, #0xf]
    // 0x90deac: r0 = 4
    //     0x90deac: movz            x0, #0x4
    // 0x90deb0: StoreField: r1->field_b = r0
    //     0x90deb0: stur            w0, [x1, #0xb]
    // 0x90deb4: mov             x2, x1
    // 0x90deb8: ldr             x1, [fp, #0x10]
    // 0x90debc: r0 = HeirsExt.hasEveryOf()
    //     0x90debc: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90dec0: tbnz            w0, #4, #0x90ded8
    // 0x90dec4: r0 = Instance__$Mahjub
    //     0x90dec4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90dec8: ldr             x0, [x0, #0x980]
    // 0x90decc: LeaveFrame
    //     0x90decc: mov             SP, fp
    //     0x90ded0: ldp             fp, lr, [SP], #0x10
    // 0x90ded4: ret
    //     0x90ded4: ret             
    // 0x90ded8: r0 = Instance__$Ashobah
    //     0x90ded8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90dedc: ldr             x0, [x0, #0x988]
    // 0x90dee0: LeaveFrame
    //     0x90dee0: mov             SP, fp
    //     0x90dee4: ldp             fp, lr, [SP], #0x10
    // 0x90dee8: ret
    //     0x90dee8: ret             
    // 0x90deec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90deec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90def0: b               #0x90dc50
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90def4, size: 0x2b4
    // 0x90def4: EnterFrame
    //     0x90def4: stp             fp, lr, [SP, #-0x10]!
    //     0x90def8: mov             fp, SP
    // 0x90defc: AllocStack(0x8)
    //     0x90defc: sub             SP, SP, #8
    // 0x90df00: SetupParameters()
    //     0x90df00: movz            x0, #0xe
    // 0x90df00: r0 = 14
    // 0x90df04: CheckStackOverflow
    //     0x90df04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90df08: cmp             SP, x16
    //     0x90df0c: b.ls            #0x90e1a0
    // 0x90df10: mov             x2, x0
    // 0x90df14: r1 = Null
    //     0x90df14: mov             x1, NULL
    // 0x90df18: r0 = AllocateArray()
    //     0x90df18: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90df1c: stur            x0, [fp, #-8]
    // 0x90df20: r16 = Instance_Heir
    //     0x90df20: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90df24: ldr             x16, [x16, #0x7d0]
    // 0x90df28: StoreField: r0->field_f = r16
    //     0x90df28: stur            w16, [x0, #0xf]
    // 0x90df2c: r16 = Instance_Heir
    //     0x90df2c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90df30: ldr             x16, [x16, #0x7e0]
    // 0x90df34: StoreField: r0->field_13 = r16
    //     0x90df34: stur            w16, [x0, #0x13]
    // 0x90df38: r16 = Instance_Heir
    //     0x90df38: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90df3c: ldr             x16, [x16, #0x838]
    // 0x90df40: ArrayStore: r0[0] = r16  ; List_4
    //     0x90df40: stur            w16, [x0, #0x17]
    // 0x90df44: r16 = Instance_Heir
    //     0x90df44: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90df48: ldr             x16, [x16, #0x88]
    // 0x90df4c: StoreField: r0->field_1b = r16
    //     0x90df4c: stur            w16, [x0, #0x1b]
    // 0x90df50: r16 = Instance_Heir
    //     0x90df50: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90df54: ldr             x16, [x16, #0x90]
    // 0x90df58: StoreField: r0->field_1f = r16
    //     0x90df58: stur            w16, [x0, #0x1f]
    // 0x90df5c: r16 = Instance_Heir
    //     0x90df5c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90df60: ldr             x16, [x16, #0x98]
    // 0x90df64: StoreField: r0->field_23 = r16
    //     0x90df64: stur            w16, [x0, #0x23]
    // 0x90df68: r16 = Instance_Heir
    //     0x90df68: add             x16, PP, #0x31, lsl #12  ; [pp+0x31868] Obj!Heir@e2dbd1
    //     0x90df6c: ldr             x16, [x16, #0x868]
    // 0x90df70: StoreField: r0->field_27 = r16
    //     0x90df70: stur            w16, [x0, #0x27]
    // 0x90df74: r1 = <Heir>
    //     0x90df74: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90df78: ldr             x1, [x1, #0xc0]
    // 0x90df7c: r0 = AllocateGrowableArray()
    //     0x90df7c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90df80: mov             x1, x0
    // 0x90df84: ldur            x0, [fp, #-8]
    // 0x90df88: StoreField: r1->field_f = r0
    //     0x90df88: stur            w0, [x1, #0xf]
    // 0x90df8c: r0 = 14
    //     0x90df8c: movz            x0, #0xe
    // 0x90df90: StoreField: r1->field_b = r0
    //     0x90df90: stur            w0, [x1, #0xb]
    // 0x90df94: mov             x2, x1
    // 0x90df98: ldr             x1, [fp, #0x10]
    // 0x90df9c: r0 = HeirsExt.search()
    //     0x90df9c: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90dfa0: stur            x0, [fp, #-8]
    // 0x90dfa4: LoadField: r1 = r0->field_b
    //     0x90dfa4: ldur            w1, [x0, #0xb]
    // 0x90dfa8: cbz             w1, #0x90dfcc
    // 0x90dfac: r0 = _$Mahjub()
    //     0x90dfac: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90dfb0: mov             x1, x0
    // 0x90dfb4: ldur            x0, [fp, #-8]
    // 0x90dfb8: StoreField: r1->field_7 = r0
    //     0x90dfb8: stur            w0, [x1, #7]
    // 0x90dfbc: mov             x0, x1
    // 0x90dfc0: LeaveFrame
    //     0x90dfc0: mov             SP, fp
    //     0x90dfc4: ldp             fp, lr, [SP], #0x10
    // 0x90dfc8: ret
    //     0x90dfc8: ret             
    // 0x90dfcc: r0 = 4
    //     0x90dfcc: movz            x0, #0x4
    // 0x90dfd0: mov             x2, x0
    // 0x90dfd4: r1 = Null
    //     0x90dfd4: mov             x1, NULL
    // 0x90dfd8: r0 = AllocateArray()
    //     0x90dfd8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90dfdc: stur            x0, [fp, #-8]
    // 0x90dfe0: r16 = Instance_Heir
    //     0x90dfe0: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90dfe4: ldr             x16, [x16, #0xa8]
    // 0x90dfe8: StoreField: r0->field_f = r16
    //     0x90dfe8: stur            w16, [x0, #0xf]
    // 0x90dfec: r16 = Instance_Heir
    //     0x90dfec: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90dff0: ldr             x16, [x16, #0x7d8]
    // 0x90dff4: StoreField: r0->field_13 = r16
    //     0x90dff4: stur            w16, [x0, #0x13]
    // 0x90dff8: r1 = <Heir>
    //     0x90dff8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90dffc: ldr             x1, [x1, #0xc0]
    // 0x90e000: r0 = AllocateGrowableArray()
    //     0x90e000: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e004: mov             x1, x0
    // 0x90e008: ldur            x0, [fp, #-8]
    // 0x90e00c: StoreField: r1->field_f = r0
    //     0x90e00c: stur            w0, [x1, #0xf]
    // 0x90e010: r0 = 4
    //     0x90e010: movz            x0, #0x4
    // 0x90e014: StoreField: r1->field_b = r0
    //     0x90e014: stur            w0, [x1, #0xb]
    // 0x90e018: mov             x2, x1
    // 0x90e01c: ldr             x1, [fp, #0x10]
    // 0x90e020: r0 = HeirsExt.hasEveryOf()
    //     0x90e020: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e024: tbnz            w0, #4, #0x90e03c
    // 0x90e028: r0 = Instance__$Mahjub
    //     0x90e028: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90e02c: ldr             x0, [x0, #0x9b0]
    // 0x90e030: LeaveFrame
    //     0x90e030: mov             SP, fp
    //     0x90e034: ldp             fp, lr, [SP], #0x10
    // 0x90e038: ret
    //     0x90e038: ret             
    // 0x90e03c: r0 = 4
    //     0x90e03c: movz            x0, #0x4
    // 0x90e040: mov             x2, x0
    // 0x90e044: r1 = Null
    //     0x90e044: mov             x1, NULL
    // 0x90e048: r0 = AllocateArray()
    //     0x90e048: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e04c: stur            x0, [fp, #-8]
    // 0x90e050: r16 = Instance_Heir
    //     0x90e050: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90e054: ldr             x16, [x16, #0xa8]
    // 0x90e058: StoreField: r0->field_f = r16
    //     0x90e058: stur            w16, [x0, #0xf]
    // 0x90e05c: r16 = Instance_Heir
    //     0x90e05c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e060: ldr             x16, [x16, #0x7e8]
    // 0x90e064: StoreField: r0->field_13 = r16
    //     0x90e064: stur            w16, [x0, #0x13]
    // 0x90e068: r1 = <Heir>
    //     0x90e068: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e06c: ldr             x1, [x1, #0xc0]
    // 0x90e070: r0 = AllocateGrowableArray()
    //     0x90e070: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e074: mov             x1, x0
    // 0x90e078: ldur            x0, [fp, #-8]
    // 0x90e07c: StoreField: r1->field_f = r0
    //     0x90e07c: stur            w0, [x1, #0xf]
    // 0x90e080: r0 = 4
    //     0x90e080: movz            x0, #0x4
    // 0x90e084: StoreField: r1->field_b = r0
    //     0x90e084: stur            w0, [x1, #0xb]
    // 0x90e088: mov             x2, x1
    // 0x90e08c: ldr             x1, [fp, #0x10]
    // 0x90e090: r0 = HeirsExt.hasEveryOf()
    //     0x90e090: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e094: tbnz            w0, #4, #0x90e0ac
    // 0x90e098: r0 = Instance__$Mahjub
    //     0x90e098: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90e09c: ldr             x0, [x0, #0x9b8]
    // 0x90e0a0: LeaveFrame
    //     0x90e0a0: mov             SP, fp
    //     0x90e0a4: ldp             fp, lr, [SP], #0x10
    // 0x90e0a8: ret
    //     0x90e0a8: ret             
    // 0x90e0ac: r0 = 4
    //     0x90e0ac: movz            x0, #0x4
    // 0x90e0b0: mov             x2, x0
    // 0x90e0b4: r1 = Null
    //     0x90e0b4: mov             x1, NULL
    // 0x90e0b8: r0 = AllocateArray()
    //     0x90e0b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e0bc: stur            x0, [fp, #-8]
    // 0x90e0c0: r16 = Instance_Heir
    //     0x90e0c0: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90e0c4: ldr             x16, [x16, #0xb0]
    // 0x90e0c8: StoreField: r0->field_f = r16
    //     0x90e0c8: stur            w16, [x0, #0xf]
    // 0x90e0cc: r16 = Instance_Heir
    //     0x90e0cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90e0d0: ldr             x16, [x16, #0x7d8]
    // 0x90e0d4: StoreField: r0->field_13 = r16
    //     0x90e0d4: stur            w16, [x0, #0x13]
    // 0x90e0d8: r1 = <Heir>
    //     0x90e0d8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e0dc: ldr             x1, [x1, #0xc0]
    // 0x90e0e0: r0 = AllocateGrowableArray()
    //     0x90e0e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e0e4: mov             x1, x0
    // 0x90e0e8: ldur            x0, [fp, #-8]
    // 0x90e0ec: StoreField: r1->field_f = r0
    //     0x90e0ec: stur            w0, [x1, #0xf]
    // 0x90e0f0: r0 = 4
    //     0x90e0f0: movz            x0, #0x4
    // 0x90e0f4: StoreField: r1->field_b = r0
    //     0x90e0f4: stur            w0, [x1, #0xb]
    // 0x90e0f8: mov             x2, x1
    // 0x90e0fc: ldr             x1, [fp, #0x10]
    // 0x90e100: r0 = HeirsExt.hasEveryOf()
    //     0x90e100: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e104: tbnz            w0, #4, #0x90e11c
    // 0x90e108: r0 = Instance__$Mahjub
    //     0x90e108: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90e10c: ldr             x0, [x0, #0x978]
    // 0x90e110: LeaveFrame
    //     0x90e110: mov             SP, fp
    //     0x90e114: ldp             fp, lr, [SP], #0x10
    // 0x90e118: ret
    //     0x90e118: ret             
    // 0x90e11c: r0 = 4
    //     0x90e11c: movz            x0, #0x4
    // 0x90e120: mov             x2, x0
    // 0x90e124: r1 = Null
    //     0x90e124: mov             x1, NULL
    // 0x90e128: r0 = AllocateArray()
    //     0x90e128: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e12c: stur            x0, [fp, #-8]
    // 0x90e130: r16 = Instance_Heir
    //     0x90e130: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90e134: ldr             x16, [x16, #0xb0]
    // 0x90e138: StoreField: r0->field_f = r16
    //     0x90e138: stur            w16, [x0, #0xf]
    // 0x90e13c: r16 = Instance_Heir
    //     0x90e13c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e140: ldr             x16, [x16, #0x7e8]
    // 0x90e144: StoreField: r0->field_13 = r16
    //     0x90e144: stur            w16, [x0, #0x13]
    // 0x90e148: r1 = <Heir>
    //     0x90e148: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e14c: ldr             x1, [x1, #0xc0]
    // 0x90e150: r0 = AllocateGrowableArray()
    //     0x90e150: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e154: mov             x1, x0
    // 0x90e158: ldur            x0, [fp, #-8]
    // 0x90e15c: StoreField: r1->field_f = r0
    //     0x90e15c: stur            w0, [x1, #0xf]
    // 0x90e160: r0 = 4
    //     0x90e160: movz            x0, #0x4
    // 0x90e164: StoreField: r1->field_b = r0
    //     0x90e164: stur            w0, [x1, #0xb]
    // 0x90e168: mov             x2, x1
    // 0x90e16c: ldr             x1, [fp, #0x10]
    // 0x90e170: r0 = HeirsExt.hasEveryOf()
    //     0x90e170: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e174: tbnz            w0, #4, #0x90e18c
    // 0x90e178: r0 = Instance__$Mahjub
    //     0x90e178: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90e17c: ldr             x0, [x0, #0x980]
    // 0x90e180: LeaveFrame
    //     0x90e180: mov             SP, fp
    //     0x90e184: ldp             fp, lr, [SP], #0x10
    // 0x90e188: ret
    //     0x90e188: ret             
    // 0x90e18c: r0 = Instance__$Ashobah
    //     0x90e18c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90e190: ldr             x0, [x0, #0x988]
    // 0x90e194: LeaveFrame
    //     0x90e194: mov             SP, fp
    //     0x90e198: ldp             fp, lr, [SP], #0x10
    // 0x90e19c: ret
    //     0x90e19c: ret             
    // 0x90e1a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90e1a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90e1a4: b               #0x90df10
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90e1a8, size: 0x2a8
    // 0x90e1a8: EnterFrame
    //     0x90e1a8: stp             fp, lr, [SP, #-0x10]!
    //     0x90e1ac: mov             fp, SP
    // 0x90e1b0: AllocStack(0x8)
    //     0x90e1b0: sub             SP, SP, #8
    // 0x90e1b4: SetupParameters()
    //     0x90e1b4: movz            x0, #0xc
    // 0x90e1b4: r0 = 12
    // 0x90e1b8: CheckStackOverflow
    //     0x90e1b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90e1bc: cmp             SP, x16
    //     0x90e1c0: b.ls            #0x90e448
    // 0x90e1c4: mov             x2, x0
    // 0x90e1c8: r1 = Null
    //     0x90e1c8: mov             x1, NULL
    // 0x90e1cc: r0 = AllocateArray()
    //     0x90e1cc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e1d0: stur            x0, [fp, #-8]
    // 0x90e1d4: r16 = Instance_Heir
    //     0x90e1d4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90e1d8: ldr             x16, [x16, #0x7d0]
    // 0x90e1dc: StoreField: r0->field_f = r16
    //     0x90e1dc: stur            w16, [x0, #0xf]
    // 0x90e1e0: r16 = Instance_Heir
    //     0x90e1e0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90e1e4: ldr             x16, [x16, #0x7e0]
    // 0x90e1e8: StoreField: r0->field_13 = r16
    //     0x90e1e8: stur            w16, [x0, #0x13]
    // 0x90e1ec: r16 = Instance_Heir
    //     0x90e1ec: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90e1f0: ldr             x16, [x16, #0x838]
    // 0x90e1f4: ArrayStore: r0[0] = r16  ; List_4
    //     0x90e1f4: stur            w16, [x0, #0x17]
    // 0x90e1f8: r16 = Instance_Heir
    //     0x90e1f8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90e1fc: ldr             x16, [x16, #0x88]
    // 0x90e200: StoreField: r0->field_1b = r16
    //     0x90e200: stur            w16, [x0, #0x1b]
    // 0x90e204: r16 = Instance_Heir
    //     0x90e204: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90e208: ldr             x16, [x16, #0x90]
    // 0x90e20c: StoreField: r0->field_1f = r16
    //     0x90e20c: stur            w16, [x0, #0x1f]
    // 0x90e210: r16 = Instance_Heir
    //     0x90e210: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0x90e214: ldr             x16, [x16, #0x98]
    // 0x90e218: StoreField: r0->field_23 = r16
    //     0x90e218: stur            w16, [x0, #0x23]
    // 0x90e21c: r1 = <Heir>
    //     0x90e21c: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e220: ldr             x1, [x1, #0xc0]
    // 0x90e224: r0 = AllocateGrowableArray()
    //     0x90e224: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e228: mov             x1, x0
    // 0x90e22c: ldur            x0, [fp, #-8]
    // 0x90e230: StoreField: r1->field_f = r0
    //     0x90e230: stur            w0, [x1, #0xf]
    // 0x90e234: r0 = 12
    //     0x90e234: movz            x0, #0xc
    // 0x90e238: StoreField: r1->field_b = r0
    //     0x90e238: stur            w0, [x1, #0xb]
    // 0x90e23c: mov             x2, x1
    // 0x90e240: ldr             x1, [fp, #0x10]
    // 0x90e244: r0 = HeirsExt.search()
    //     0x90e244: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90e248: stur            x0, [fp, #-8]
    // 0x90e24c: LoadField: r1 = r0->field_b
    //     0x90e24c: ldur            w1, [x0, #0xb]
    // 0x90e250: cbz             w1, #0x90e274
    // 0x90e254: r0 = _$Mahjub()
    //     0x90e254: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90e258: mov             x1, x0
    // 0x90e25c: ldur            x0, [fp, #-8]
    // 0x90e260: StoreField: r1->field_7 = r0
    //     0x90e260: stur            w0, [x1, #7]
    // 0x90e264: mov             x0, x1
    // 0x90e268: LeaveFrame
    //     0x90e268: mov             SP, fp
    //     0x90e26c: ldp             fp, lr, [SP], #0x10
    // 0x90e270: ret
    //     0x90e270: ret             
    // 0x90e274: r0 = 4
    //     0x90e274: movz            x0, #0x4
    // 0x90e278: mov             x2, x0
    // 0x90e27c: r1 = Null
    //     0x90e27c: mov             x1, NULL
    // 0x90e280: r0 = AllocateArray()
    //     0x90e280: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e284: stur            x0, [fp, #-8]
    // 0x90e288: r16 = Instance_Heir
    //     0x90e288: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90e28c: ldr             x16, [x16, #0xa8]
    // 0x90e290: StoreField: r0->field_f = r16
    //     0x90e290: stur            w16, [x0, #0xf]
    // 0x90e294: r16 = Instance_Heir
    //     0x90e294: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90e298: ldr             x16, [x16, #0x7d8]
    // 0x90e29c: StoreField: r0->field_13 = r16
    //     0x90e29c: stur            w16, [x0, #0x13]
    // 0x90e2a0: r1 = <Heir>
    //     0x90e2a0: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e2a4: ldr             x1, [x1, #0xc0]
    // 0x90e2a8: r0 = AllocateGrowableArray()
    //     0x90e2a8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e2ac: mov             x1, x0
    // 0x90e2b0: ldur            x0, [fp, #-8]
    // 0x90e2b4: StoreField: r1->field_f = r0
    //     0x90e2b4: stur            w0, [x1, #0xf]
    // 0x90e2b8: r0 = 4
    //     0x90e2b8: movz            x0, #0x4
    // 0x90e2bc: StoreField: r1->field_b = r0
    //     0x90e2bc: stur            w0, [x1, #0xb]
    // 0x90e2c0: mov             x2, x1
    // 0x90e2c4: ldr             x1, [fp, #0x10]
    // 0x90e2c8: r0 = HeirsExt.hasEveryOf()
    //     0x90e2c8: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e2cc: tbnz            w0, #4, #0x90e2e4
    // 0x90e2d0: r0 = Instance__$Mahjub
    //     0x90e2d0: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90e2d4: ldr             x0, [x0, #0x9b0]
    // 0x90e2d8: LeaveFrame
    //     0x90e2d8: mov             SP, fp
    //     0x90e2dc: ldp             fp, lr, [SP], #0x10
    // 0x90e2e0: ret
    //     0x90e2e0: ret             
    // 0x90e2e4: r0 = 4
    //     0x90e2e4: movz            x0, #0x4
    // 0x90e2e8: mov             x2, x0
    // 0x90e2ec: r1 = Null
    //     0x90e2ec: mov             x1, NULL
    // 0x90e2f0: r0 = AllocateArray()
    //     0x90e2f0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e2f4: stur            x0, [fp, #-8]
    // 0x90e2f8: r16 = Instance_Heir
    //     0x90e2f8: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90e2fc: ldr             x16, [x16, #0xa8]
    // 0x90e300: StoreField: r0->field_f = r16
    //     0x90e300: stur            w16, [x0, #0xf]
    // 0x90e304: r16 = Instance_Heir
    //     0x90e304: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e308: ldr             x16, [x16, #0x7e8]
    // 0x90e30c: StoreField: r0->field_13 = r16
    //     0x90e30c: stur            w16, [x0, #0x13]
    // 0x90e310: r1 = <Heir>
    //     0x90e310: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e314: ldr             x1, [x1, #0xc0]
    // 0x90e318: r0 = AllocateGrowableArray()
    //     0x90e318: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e31c: mov             x1, x0
    // 0x90e320: ldur            x0, [fp, #-8]
    // 0x90e324: StoreField: r1->field_f = r0
    //     0x90e324: stur            w0, [x1, #0xf]
    // 0x90e328: r0 = 4
    //     0x90e328: movz            x0, #0x4
    // 0x90e32c: StoreField: r1->field_b = r0
    //     0x90e32c: stur            w0, [x1, #0xb]
    // 0x90e330: mov             x2, x1
    // 0x90e334: ldr             x1, [fp, #0x10]
    // 0x90e338: r0 = HeirsExt.hasEveryOf()
    //     0x90e338: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e33c: tbnz            w0, #4, #0x90e354
    // 0x90e340: r0 = Instance__$Mahjub
    //     0x90e340: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90e344: ldr             x0, [x0, #0x9b8]
    // 0x90e348: LeaveFrame
    //     0x90e348: mov             SP, fp
    //     0x90e34c: ldp             fp, lr, [SP], #0x10
    // 0x90e350: ret
    //     0x90e350: ret             
    // 0x90e354: r0 = 4
    //     0x90e354: movz            x0, #0x4
    // 0x90e358: mov             x2, x0
    // 0x90e35c: r1 = Null
    //     0x90e35c: mov             x1, NULL
    // 0x90e360: r0 = AllocateArray()
    //     0x90e360: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e364: stur            x0, [fp, #-8]
    // 0x90e368: r16 = Instance_Heir
    //     0x90e368: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90e36c: ldr             x16, [x16, #0xb0]
    // 0x90e370: StoreField: r0->field_f = r16
    //     0x90e370: stur            w16, [x0, #0xf]
    // 0x90e374: r16 = Instance_Heir
    //     0x90e374: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90e378: ldr             x16, [x16, #0x7d8]
    // 0x90e37c: StoreField: r0->field_13 = r16
    //     0x90e37c: stur            w16, [x0, #0x13]
    // 0x90e380: r1 = <Heir>
    //     0x90e380: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e384: ldr             x1, [x1, #0xc0]
    // 0x90e388: r0 = AllocateGrowableArray()
    //     0x90e388: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e38c: mov             x1, x0
    // 0x90e390: ldur            x0, [fp, #-8]
    // 0x90e394: StoreField: r1->field_f = r0
    //     0x90e394: stur            w0, [x1, #0xf]
    // 0x90e398: r0 = 4
    //     0x90e398: movz            x0, #0x4
    // 0x90e39c: StoreField: r1->field_b = r0
    //     0x90e39c: stur            w0, [x1, #0xb]
    // 0x90e3a0: mov             x2, x1
    // 0x90e3a4: ldr             x1, [fp, #0x10]
    // 0x90e3a8: r0 = HeirsExt.hasEveryOf()
    //     0x90e3a8: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e3ac: tbnz            w0, #4, #0x90e3c4
    // 0x90e3b0: r0 = Instance__$Mahjub
    //     0x90e3b0: add             x0, PP, #0x31, lsl #12  ; [pp+0x31978] Obj!_$Mahjub@e0bd21
    //     0x90e3b4: ldr             x0, [x0, #0x978]
    // 0x90e3b8: LeaveFrame
    //     0x90e3b8: mov             SP, fp
    //     0x90e3bc: ldp             fp, lr, [SP], #0x10
    // 0x90e3c0: ret
    //     0x90e3c0: ret             
    // 0x90e3c4: r0 = 4
    //     0x90e3c4: movz            x0, #0x4
    // 0x90e3c8: mov             x2, x0
    // 0x90e3cc: r1 = Null
    //     0x90e3cc: mov             x1, NULL
    // 0x90e3d0: r0 = AllocateArray()
    //     0x90e3d0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e3d4: stur            x0, [fp, #-8]
    // 0x90e3d8: r16 = Instance_Heir
    //     0x90e3d8: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0x90e3dc: ldr             x16, [x16, #0xb0]
    // 0x90e3e0: StoreField: r0->field_f = r16
    //     0x90e3e0: stur            w16, [x0, #0xf]
    // 0x90e3e4: r16 = Instance_Heir
    //     0x90e3e4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e3e8: ldr             x16, [x16, #0x7e8]
    // 0x90e3ec: StoreField: r0->field_13 = r16
    //     0x90e3ec: stur            w16, [x0, #0x13]
    // 0x90e3f0: r1 = <Heir>
    //     0x90e3f0: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e3f4: ldr             x1, [x1, #0xc0]
    // 0x90e3f8: r0 = AllocateGrowableArray()
    //     0x90e3f8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e3fc: mov             x1, x0
    // 0x90e400: ldur            x0, [fp, #-8]
    // 0x90e404: StoreField: r1->field_f = r0
    //     0x90e404: stur            w0, [x1, #0xf]
    // 0x90e408: r0 = 4
    //     0x90e408: movz            x0, #0x4
    // 0x90e40c: StoreField: r1->field_b = r0
    //     0x90e40c: stur            w0, [x1, #0xb]
    // 0x90e410: mov             x2, x1
    // 0x90e414: ldr             x1, [fp, #0x10]
    // 0x90e418: r0 = HeirsExt.hasEveryOf()
    //     0x90e418: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e41c: tbnz            w0, #4, #0x90e434
    // 0x90e420: r0 = Instance__$Mahjub
    //     0x90e420: add             x0, PP, #0x31, lsl #12  ; [pp+0x31980] Obj!_$Mahjub@e0bd11
    //     0x90e424: ldr             x0, [x0, #0x980]
    // 0x90e428: LeaveFrame
    //     0x90e428: mov             SP, fp
    //     0x90e42c: ldp             fp, lr, [SP], #0x10
    // 0x90e430: ret
    //     0x90e430: ret             
    // 0x90e434: r0 = Instance__$Ashobah
    //     0x90e434: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90e438: ldr             x0, [x0, #0x988]
    // 0x90e43c: LeaveFrame
    //     0x90e43c: mov             SP, fp
    //     0x90e440: ldp             fp, lr, [SP], #0x10
    // 0x90e444: ret
    //     0x90e444: ret             
    // 0x90e448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90e448: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90e44c: b               #0x90e1c4
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90e450, size: 0x138
    // 0x90e450: EnterFrame
    //     0x90e450: stp             fp, lr, [SP, #-0x10]!
    //     0x90e454: mov             fp, SP
    // 0x90e458: AllocStack(0x8)
    //     0x90e458: sub             SP, SP, #8
    // 0x90e45c: SetupParameters()
    //     0x90e45c: movz            x0, #0xc
    // 0x90e45c: r0 = 12
    // 0x90e460: CheckStackOverflow
    //     0x90e460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90e464: cmp             SP, x16
    //     0x90e468: b.ls            #0x90e580
    // 0x90e46c: mov             x2, x0
    // 0x90e470: r1 = Null
    //     0x90e470: mov             x1, NULL
    // 0x90e474: r0 = AllocateArray()
    //     0x90e474: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e478: stur            x0, [fp, #-8]
    // 0x90e47c: r16 = Instance_Heir
    //     0x90e47c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90e480: ldr             x16, [x16, #0x7d0]
    // 0x90e484: StoreField: r0->field_f = r16
    //     0x90e484: stur            w16, [x0, #0xf]
    // 0x90e488: r16 = Instance_Heir
    //     0x90e488: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90e48c: ldr             x16, [x16, #0x7e0]
    // 0x90e490: StoreField: r0->field_13 = r16
    //     0x90e490: stur            w16, [x0, #0x13]
    // 0x90e494: r16 = Instance_Heir
    //     0x90e494: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90e498: ldr             x16, [x16, #0x838]
    // 0x90e49c: ArrayStore: r0[0] = r16  ; List_4
    //     0x90e49c: stur            w16, [x0, #0x17]
    // 0x90e4a0: r16 = Instance_Heir
    //     0x90e4a0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x90e4a4: ldr             x16, [x16, #0x88]
    // 0x90e4a8: StoreField: r0->field_1b = r16
    //     0x90e4a8: stur            w16, [x0, #0x1b]
    // 0x90e4ac: r16 = Instance_Heir
    //     0x90e4ac: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90e4b0: ldr             x16, [x16, #0x7d8]
    // 0x90e4b4: StoreField: r0->field_1f = r16
    //     0x90e4b4: stur            w16, [x0, #0x1f]
    // 0x90e4b8: r16 = Instance_Heir
    //     0x90e4b8: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e4bc: ldr             x16, [x16, #0x7e8]
    // 0x90e4c0: StoreField: r0->field_23 = r16
    //     0x90e4c0: stur            w16, [x0, #0x23]
    // 0x90e4c4: r1 = <Heir>
    //     0x90e4c4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e4c8: ldr             x1, [x1, #0xc0]
    // 0x90e4cc: r0 = AllocateGrowableArray()
    //     0x90e4cc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e4d0: mov             x1, x0
    // 0x90e4d4: ldur            x0, [fp, #-8]
    // 0x90e4d8: StoreField: r1->field_f = r0
    //     0x90e4d8: stur            w0, [x1, #0xf]
    // 0x90e4dc: r0 = 12
    //     0x90e4dc: movz            x0, #0xc
    // 0x90e4e0: StoreField: r1->field_b = r0
    //     0x90e4e0: stur            w0, [x1, #0xb]
    // 0x90e4e4: mov             x2, x1
    // 0x90e4e8: ldr             x1, [fp, #0x10]
    // 0x90e4ec: r0 = HeirsExt.search()
    //     0x90e4ec: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90e4f0: stur            x0, [fp, #-8]
    // 0x90e4f4: LoadField: r1 = r0->field_b
    //     0x90e4f4: ldur            w1, [x0, #0xb]
    // 0x90e4f8: cbz             w1, #0x90e51c
    // 0x90e4fc: r0 = _$Mahjub()
    //     0x90e4fc: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90e500: mov             x1, x0
    // 0x90e504: ldur            x0, [fp, #-8]
    // 0x90e508: StoreField: r1->field_7 = r0
    //     0x90e508: stur            w0, [x1, #7]
    // 0x90e50c: mov             x0, x1
    // 0x90e510: LeaveFrame
    //     0x90e510: mov             SP, fp
    //     0x90e514: ldp             fp, lr, [SP], #0x10
    // 0x90e518: ret
    //     0x90e518: ret             
    // 0x90e51c: ldr             x1, [fp, #0x10]
    // 0x90e520: r2 = Instance_Heir
    //     0x90e520: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x90e524: ldr             x2, [x2, #0xa0]
    // 0x90e528: r0 = HeirsExt.hasOneOf()
    //     0x90e528: bl              #0x90c538  ; [package:waris/src/heirs.dart] ::HeirsExt.hasOneOf
    // 0x90e52c: tbnz            w0, #4, #0x90e544
    // 0x90e530: r0 = Instance__$Furudh
    //     0x90e530: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90e534: ldr             x0, [x0, #0x9a0]
    // 0x90e538: LeaveFrame
    //     0x90e538: mov             SP, fp
    //     0x90e53c: ldp             fp, lr, [SP], #0x10
    // 0x90e540: ret
    //     0x90e540: ret             
    // 0x90e544: ldr             x1, [fp, #0x10]
    // 0x90e548: r2 = Instance_Heir
    //     0x90e548: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x90e54c: ldr             x2, [x2, #0xa0]
    // 0x90e550: r0 = HeirsExt.hasManyOf()
    //     0x90e550: bl              #0x90c8dc  ; [package:waris/src/heirs.dart] ::HeirsExt.hasManyOf
    // 0x90e554: tbnz            w0, #4, #0x90e56c
    // 0x90e558: r0 = Instance__$Furudh
    //     0x90e558: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a8] Obj!_$Furudh@e0be81
    //     0x90e55c: ldr             x0, [x0, #0x9a8]
    // 0x90e560: LeaveFrame
    //     0x90e560: mov             SP, fp
    //     0x90e564: ldp             fp, lr, [SP], #0x10
    // 0x90e568: ret
    //     0x90e568: ret             
    // 0x90e56c: r0 = Instance__$Unknown
    //     0x90e56c: add             x0, PP, #0x31, lsl #12  ; [pp+0x319e8] Obj!_$Unknown@e0bd01
    //     0x90e570: ldr             x0, [x0, #0x9e8]
    // 0x90e574: LeaveFrame
    //     0x90e574: mov             SP, fp
    //     0x90e578: ldp             fp, lr, [SP], #0x10
    // 0x90e57c: ret
    //     0x90e57c: ret             
    // 0x90e580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90e580: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90e584: b               #0x90e46c
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90e588, size: 0x1b0
    // 0x90e588: EnterFrame
    //     0x90e588: stp             fp, lr, [SP, #-0x10]!
    //     0x90e58c: mov             fp, SP
    // 0x90e590: AllocStack(0x8)
    //     0x90e590: sub             SP, SP, #8
    // 0x90e594: SetupParameters()
    //     0x90e594: movz            x0, #0x8
    // 0x90e594: r0 = 8
    // 0x90e598: CheckStackOverflow
    //     0x90e598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90e59c: cmp             SP, x16
    //     0x90e5a0: b.ls            #0x90e730
    // 0x90e5a4: mov             x2, x0
    // 0x90e5a8: r1 = Null
    //     0x90e5a8: mov             x1, NULL
    // 0x90e5ac: r0 = AllocateArray()
    //     0x90e5ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e5b0: stur            x0, [fp, #-8]
    // 0x90e5b4: r16 = Instance_Heir
    //     0x90e5b4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90e5b8: ldr             x16, [x16, #0x7d0]
    // 0x90e5bc: StoreField: r0->field_f = r16
    //     0x90e5bc: stur            w16, [x0, #0xf]
    // 0x90e5c0: r16 = Instance_Heir
    //     0x90e5c0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90e5c4: ldr             x16, [x16, #0x7e0]
    // 0x90e5c8: StoreField: r0->field_13 = r16
    //     0x90e5c8: stur            w16, [x0, #0x13]
    // 0x90e5cc: r16 = Instance_Heir
    //     0x90e5cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90e5d0: ldr             x16, [x16, #0x838]
    // 0x90e5d4: ArrayStore: r0[0] = r16  ; List_4
    //     0x90e5d4: stur            w16, [x0, #0x17]
    // 0x90e5d8: r16 = Instance_Heir
    //     0x90e5d8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x90e5dc: ldr             x16, [x16, #0x90]
    // 0x90e5e0: StoreField: r0->field_1b = r16
    //     0x90e5e0: stur            w16, [x0, #0x1b]
    // 0x90e5e4: r1 = <Heir>
    //     0x90e5e4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e5e8: ldr             x1, [x1, #0xc0]
    // 0x90e5ec: r0 = AllocateGrowableArray()
    //     0x90e5ec: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e5f0: mov             x1, x0
    // 0x90e5f4: ldur            x0, [fp, #-8]
    // 0x90e5f8: StoreField: r1->field_f = r0
    //     0x90e5f8: stur            w0, [x1, #0xf]
    // 0x90e5fc: r0 = 8
    //     0x90e5fc: movz            x0, #0x8
    // 0x90e600: StoreField: r1->field_b = r0
    //     0x90e600: stur            w0, [x1, #0xb]
    // 0x90e604: mov             x2, x1
    // 0x90e608: ldr             x1, [fp, #0x10]
    // 0x90e60c: r0 = HeirsExt.search()
    //     0x90e60c: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90e610: stur            x0, [fp, #-8]
    // 0x90e614: LoadField: r1 = r0->field_b
    //     0x90e614: ldur            w1, [x0, #0xb]
    // 0x90e618: cbz             w1, #0x90e63c
    // 0x90e61c: r0 = _$Mahjub()
    //     0x90e61c: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90e620: mov             x1, x0
    // 0x90e624: ldur            x0, [fp, #-8]
    // 0x90e628: StoreField: r1->field_7 = r0
    //     0x90e628: stur            w0, [x1, #7]
    // 0x90e62c: mov             x0, x1
    // 0x90e630: LeaveFrame
    //     0x90e630: mov             SP, fp
    //     0x90e634: ldp             fp, lr, [SP], #0x10
    // 0x90e638: ret
    //     0x90e638: ret             
    // 0x90e63c: r0 = 4
    //     0x90e63c: movz            x0, #0x4
    // 0x90e640: mov             x2, x0
    // 0x90e644: r1 = Null
    //     0x90e644: mov             x1, NULL
    // 0x90e648: r0 = AllocateArray()
    //     0x90e648: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e64c: stur            x0, [fp, #-8]
    // 0x90e650: r16 = Instance_Heir
    //     0x90e650: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90e654: ldr             x16, [x16, #0xa8]
    // 0x90e658: StoreField: r0->field_f = r16
    //     0x90e658: stur            w16, [x0, #0xf]
    // 0x90e65c: r16 = Instance_Heir
    //     0x90e65c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90e660: ldr             x16, [x16, #0x7d8]
    // 0x90e664: StoreField: r0->field_13 = r16
    //     0x90e664: stur            w16, [x0, #0x13]
    // 0x90e668: r1 = <Heir>
    //     0x90e668: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e66c: ldr             x1, [x1, #0xc0]
    // 0x90e670: r0 = AllocateGrowableArray()
    //     0x90e670: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e674: mov             x1, x0
    // 0x90e678: ldur            x0, [fp, #-8]
    // 0x90e67c: StoreField: r1->field_f = r0
    //     0x90e67c: stur            w0, [x1, #0xf]
    // 0x90e680: r0 = 4
    //     0x90e680: movz            x0, #0x4
    // 0x90e684: StoreField: r1->field_b = r0
    //     0x90e684: stur            w0, [x1, #0xb]
    // 0x90e688: mov             x2, x1
    // 0x90e68c: ldr             x1, [fp, #0x10]
    // 0x90e690: r0 = HeirsExt.hasEveryOf()
    //     0x90e690: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e694: tbnz            w0, #4, #0x90e6ac
    // 0x90e698: r0 = Instance__$Mahjub
    //     0x90e698: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b0] Obj!_$Mahjub@e0bd51
    //     0x90e69c: ldr             x0, [x0, #0x9b0]
    // 0x90e6a0: LeaveFrame
    //     0x90e6a0: mov             SP, fp
    //     0x90e6a4: ldp             fp, lr, [SP], #0x10
    // 0x90e6a8: ret
    //     0x90e6a8: ret             
    // 0x90e6ac: r0 = 4
    //     0x90e6ac: movz            x0, #0x4
    // 0x90e6b0: mov             x2, x0
    // 0x90e6b4: r1 = Null
    //     0x90e6b4: mov             x1, NULL
    // 0x90e6b8: r0 = AllocateArray()
    //     0x90e6b8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e6bc: stur            x0, [fp, #-8]
    // 0x90e6c0: r16 = Instance_Heir
    //     0x90e6c0: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0x90e6c4: ldr             x16, [x16, #0xa8]
    // 0x90e6c8: StoreField: r0->field_f = r16
    //     0x90e6c8: stur            w16, [x0, #0xf]
    // 0x90e6cc: r16 = Instance_Heir
    //     0x90e6cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e6d0: ldr             x16, [x16, #0x7e8]
    // 0x90e6d4: StoreField: r0->field_13 = r16
    //     0x90e6d4: stur            w16, [x0, #0x13]
    // 0x90e6d8: r1 = <Heir>
    //     0x90e6d8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e6dc: ldr             x1, [x1, #0xc0]
    // 0x90e6e0: r0 = AllocateGrowableArray()
    //     0x90e6e0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e6e4: mov             x1, x0
    // 0x90e6e8: ldur            x0, [fp, #-8]
    // 0x90e6ec: StoreField: r1->field_f = r0
    //     0x90e6ec: stur            w0, [x1, #0xf]
    // 0x90e6f0: r0 = 4
    //     0x90e6f0: movz            x0, #0x4
    // 0x90e6f4: StoreField: r1->field_b = r0
    //     0x90e6f4: stur            w0, [x1, #0xb]
    // 0x90e6f8: mov             x2, x1
    // 0x90e6fc: ldr             x1, [fp, #0x10]
    // 0x90e700: r0 = HeirsExt.hasEveryOf()
    //     0x90e700: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90e704: tbnz            w0, #4, #0x90e71c
    // 0x90e708: r0 = Instance__$Mahjub
    //     0x90e708: add             x0, PP, #0x31, lsl #12  ; [pp+0x319b8] Obj!_$Mahjub@e0bd41
    //     0x90e70c: ldr             x0, [x0, #0x9b8]
    // 0x90e710: LeaveFrame
    //     0x90e710: mov             SP, fp
    //     0x90e714: ldp             fp, lr, [SP], #0x10
    // 0x90e718: ret
    //     0x90e718: ret             
    // 0x90e71c: r0 = Instance__$Ashobah
    //     0x90e71c: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90e720: ldr             x0, [x0, #0x988]
    // 0x90e724: LeaveFrame
    //     0x90e724: mov             SP, fp
    //     0x90e728: ldp             fp, lr, [SP], #0x10
    // 0x90e72c: ret
    //     0x90e72c: ret             
    // 0x90e730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90e730: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90e734: b               #0x90e5a4
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90e738, size: 0xbc
    // 0x90e738: EnterFrame
    //     0x90e738: stp             fp, lr, [SP, #-0x10]!
    //     0x90e73c: mov             fp, SP
    // 0x90e740: AllocStack(0x8)
    //     0x90e740: sub             SP, SP, #8
    // 0x90e744: SetupParameters()
    //     0x90e744: movz            x0, #0x6
    // 0x90e744: r0 = 6
    // 0x90e748: CheckStackOverflow
    //     0x90e748: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90e74c: cmp             SP, x16
    //     0x90e750: b.ls            #0x90e7ec
    // 0x90e754: mov             x2, x0
    // 0x90e758: r1 = Null
    //     0x90e758: mov             x1, NULL
    // 0x90e75c: r0 = AllocateArray()
    //     0x90e75c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e760: stur            x0, [fp, #-8]
    // 0x90e764: r16 = Instance_Heir
    //     0x90e764: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90e768: ldr             x16, [x16, #0x7d0]
    // 0x90e76c: StoreField: r0->field_f = r16
    //     0x90e76c: stur            w16, [x0, #0xf]
    // 0x90e770: r16 = Instance_Heir
    //     0x90e770: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90e774: ldr             x16, [x16, #0x7e0]
    // 0x90e778: StoreField: r0->field_13 = r16
    //     0x90e778: stur            w16, [x0, #0x13]
    // 0x90e77c: r16 = Instance_Heir
    //     0x90e77c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90e780: ldr             x16, [x16, #0x838]
    // 0x90e784: ArrayStore: r0[0] = r16  ; List_4
    //     0x90e784: stur            w16, [x0, #0x17]
    // 0x90e788: r1 = <Heir>
    //     0x90e788: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e78c: ldr             x1, [x1, #0xc0]
    // 0x90e790: r0 = AllocateGrowableArray()
    //     0x90e790: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e794: mov             x1, x0
    // 0x90e798: ldur            x0, [fp, #-8]
    // 0x90e79c: StoreField: r1->field_f = r0
    //     0x90e79c: stur            w0, [x1, #0xf]
    // 0x90e7a0: r0 = 6
    //     0x90e7a0: movz            x0, #0x6
    // 0x90e7a4: StoreField: r1->field_b = r0
    //     0x90e7a4: stur            w0, [x1, #0xb]
    // 0x90e7a8: mov             x2, x1
    // 0x90e7ac: ldr             x1, [fp, #0x10]
    // 0x90e7b0: r0 = HeirsExt.search()
    //     0x90e7b0: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90e7b4: stur            x0, [fp, #-8]
    // 0x90e7b8: LoadField: r1 = r0->field_b
    //     0x90e7b8: ldur            w1, [x0, #0xb]
    // 0x90e7bc: cbz             w1, #0x90e7d8
    // 0x90e7c0: r0 = _$Mahjub()
    //     0x90e7c0: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0x90e7c4: ldur            x1, [fp, #-8]
    // 0x90e7c8: StoreField: r0->field_7 = r1
    //     0x90e7c8: stur            w1, [x0, #7]
    // 0x90e7cc: LeaveFrame
    //     0x90e7cc: mov             SP, fp
    //     0x90e7d0: ldp             fp, lr, [SP], #0x10
    // 0x90e7d4: ret
    //     0x90e7d4: ret             
    // 0x90e7d8: r0 = Instance__$Ashobah
    //     0x90e7d8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90e7dc: ldr             x0, [x0, #0x988]
    // 0x90e7e0: LeaveFrame
    //     0x90e7e0: mov             SP, fp
    //     0x90e7e4: ldp             fp, lr, [SP], #0x10
    // 0x90e7e8: ret
    //     0x90e7e8: ret             
    // 0x90e7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90e7ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90e7f0: b               #0x90e754
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90e7f4, size: 0xe4
    // 0x90e7f4: EnterFrame
    //     0x90e7f4: stp             fp, lr, [SP, #-0x10]!
    //     0x90e7f8: mov             fp, SP
    // 0x90e7fc: AllocStack(0x8)
    //     0x90e7fc: sub             SP, SP, #8
    // 0x90e800: CheckStackOverflow
    //     0x90e800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90e804: cmp             SP, x16
    //     0x90e808: b.ls            #0x90e8d0
    // 0x90e80c: ldr             x1, [fp, #0x10]
    // 0x90e810: r2 = Instance_Heir
    //     0x90e810: add             x2, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x90e814: ldr             x2, [x2, #0x838]
    // 0x90e818: r0 = HeirsExt.has()
    //     0x90e818: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90e81c: tbnz            w0, #4, #0x90e834
    // 0x90e820: r0 = Instance__$Mahjub
    //     0x90e820: add             x0, PP, #0x31, lsl #12  ; [pp+0x31a38] Obj!_$Mahjub@e0bda1
    //     0x90e824: ldr             x0, [x0, #0xa38]
    // 0x90e828: LeaveFrame
    //     0x90e828: mov             SP, fp
    //     0x90e82c: ldp             fp, lr, [SP], #0x10
    // 0x90e830: ret
    //     0x90e830: ret             
    // 0x90e834: r0 = 8
    //     0x90e834: movz            x0, #0x8
    // 0x90e838: mov             x2, x0
    // 0x90e83c: r1 = Null
    //     0x90e83c: mov             x1, NULL
    // 0x90e840: r0 = AllocateArray()
    //     0x90e840: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e844: stur            x0, [fp, #-8]
    // 0x90e848: r16 = Instance_Heir
    //     0x90e848: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90e84c: ldr             x16, [x16, #0x7d0]
    // 0x90e850: StoreField: r0->field_f = r16
    //     0x90e850: stur            w16, [x0, #0xf]
    // 0x90e854: r16 = Instance_Heir
    //     0x90e854: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90e858: ldr             x16, [x16, #0x7e0]
    // 0x90e85c: StoreField: r0->field_13 = r16
    //     0x90e85c: stur            w16, [x0, #0x13]
    // 0x90e860: r16 = Instance_Heir
    //     0x90e860: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90e864: ldr             x16, [x16, #0x7d8]
    // 0x90e868: ArrayStore: r0[0] = r16  ; List_4
    //     0x90e868: stur            w16, [x0, #0x17]
    // 0x90e86c: r16 = Instance_Heir
    //     0x90e86c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e870: ldr             x16, [x16, #0x7e8]
    // 0x90e874: StoreField: r0->field_1b = r16
    //     0x90e874: stur            w16, [x0, #0x1b]
    // 0x90e878: r1 = <Heir>
    //     0x90e878: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e87c: ldr             x1, [x1, #0xc0]
    // 0x90e880: r0 = AllocateGrowableArray()
    //     0x90e880: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e884: mov             x1, x0
    // 0x90e888: ldur            x0, [fp, #-8]
    // 0x90e88c: StoreField: r1->field_f = r0
    //     0x90e88c: stur            w0, [x1, #0xf]
    // 0x90e890: r0 = 8
    //     0x90e890: movz            x0, #0x8
    // 0x90e894: StoreField: r1->field_b = r0
    //     0x90e894: stur            w0, [x1, #0xb]
    // 0x90e898: mov             x2, x1
    // 0x90e89c: ldr             x1, [fp, #0x10]
    // 0x90e8a0: r0 = HeirsExt.hasAnyOf()
    //     0x90e8a0: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90e8a4: tbnz            w0, #4, #0x90e8bc
    // 0x90e8a8: r0 = Instance__$Furudh
    //     0x90e8a8: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90e8ac: ldr             x0, [x0, #0x9a0]
    // 0x90e8b0: LeaveFrame
    //     0x90e8b0: mov             SP, fp
    //     0x90e8b4: ldp             fp, lr, [SP], #0x10
    // 0x90e8b8: ret
    //     0x90e8b8: ret             
    // 0x90e8bc: r0 = Instance__$Ashobah
    //     0x90e8bc: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90e8c0: ldr             x0, [x0, #0x988]
    // 0x90e8c4: LeaveFrame
    //     0x90e8c4: mov             SP, fp
    //     0x90e8c8: ldp             fp, lr, [SP], #0x10
    // 0x90e8cc: ret
    //     0x90e8cc: ret             
    // 0x90e8d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90e8d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90e8d4: b               #0x90e80c
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90e8d8, size: 0xbc
    // 0x90e8d8: EnterFrame
    //     0x90e8d8: stp             fp, lr, [SP, #-0x10]!
    //     0x90e8dc: mov             fp, SP
    // 0x90e8e0: AllocStack(0x8)
    //     0x90e8e0: sub             SP, SP, #8
    // 0x90e8e4: SetupParameters()
    //     0x90e8e4: movz            x0, #0x8
    // 0x90e8e4: r0 = 8
    // 0x90e8e8: CheckStackOverflow
    //     0x90e8e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90e8ec: cmp             SP, x16
    //     0x90e8f0: b.ls            #0x90e98c
    // 0x90e8f4: mov             x2, x0
    // 0x90e8f8: r1 = Null
    //     0x90e8f8: mov             x1, NULL
    // 0x90e8fc: r0 = AllocateArray()
    //     0x90e8fc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90e900: stur            x0, [fp, #-8]
    // 0x90e904: r16 = Instance_Heir
    //     0x90e904: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90e908: ldr             x16, [x16, #0x7d0]
    // 0x90e90c: StoreField: r0->field_f = r16
    //     0x90e90c: stur            w16, [x0, #0xf]
    // 0x90e910: r16 = Instance_Heir
    //     0x90e910: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x90e914: ldr             x16, [x16, #0x7e0]
    // 0x90e918: StoreField: r0->field_13 = r16
    //     0x90e918: stur            w16, [x0, #0x13]
    // 0x90e91c: r16 = Instance_Heir
    //     0x90e91c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0x90e920: ldr             x16, [x16, #0x7d8]
    // 0x90e924: ArrayStore: r0[0] = r16  ; List_4
    //     0x90e924: stur            w16, [x0, #0x17]
    // 0x90e928: r16 = Instance_Heir
    //     0x90e928: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e92c: ldr             x16, [x16, #0x7e8]
    // 0x90e930: StoreField: r0->field_1b = r16
    //     0x90e930: stur            w16, [x0, #0x1b]
    // 0x90e934: r1 = <Heir>
    //     0x90e934: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x90e938: ldr             x1, [x1, #0xc0]
    // 0x90e93c: r0 = AllocateGrowableArray()
    //     0x90e93c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x90e940: mov             x1, x0
    // 0x90e944: ldur            x0, [fp, #-8]
    // 0x90e948: StoreField: r1->field_f = r0
    //     0x90e948: stur            w0, [x1, #0xf]
    // 0x90e94c: r0 = 8
    //     0x90e94c: movz            x0, #0x8
    // 0x90e950: StoreField: r1->field_b = r0
    //     0x90e950: stur            w0, [x1, #0xb]
    // 0x90e954: mov             x2, x1
    // 0x90e958: ldr             x1, [fp, #0x10]
    // 0x90e95c: r0 = HeirsExt.hasAnyOf()
    //     0x90e95c: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90e960: tbnz            w0, #4, #0x90e978
    // 0x90e964: r0 = Instance__$Furudh
    //     0x90e964: add             x0, PP, #0x31, lsl #12  ; [pp+0x319a0] Obj!_$Furudh@e0bea1
    //     0x90e968: ldr             x0, [x0, #0x9a0]
    // 0x90e96c: LeaveFrame
    //     0x90e96c: mov             SP, fp
    //     0x90e970: ldp             fp, lr, [SP], #0x10
    // 0x90e974: ret
    //     0x90e974: ret             
    // 0x90e978: r0 = Instance__$Ashobah
    //     0x90e978: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90e97c: ldr             x0, [x0, #0x988]
    // 0x90e980: LeaveFrame
    //     0x90e980: mov             SP, fp
    //     0x90e984: ldp             fp, lr, [SP], #0x10
    // 0x90e988: ret
    //     0x90e988: ret             
    // 0x90e98c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90e98c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90e990: b               #0x90e8f4
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90e994, size: 0x80
    // 0x90e994: EnterFrame
    //     0x90e994: stp             fp, lr, [SP, #-0x10]!
    //     0x90e998: mov             fp, SP
    // 0x90e99c: CheckStackOverflow
    //     0x90e99c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90e9a0: cmp             SP, x16
    //     0x90e9a4: b.ls            #0x90ea0c
    // 0x90e9a8: ldr             x1, [fp, #0x10]
    // 0x90e9ac: r2 = Instance_Heir
    //     0x90e9ac: add             x2, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x90e9b0: ldr             x2, [x2, #0x7d0]
    // 0x90e9b4: r0 = HeirsExt.has()
    //     0x90e9b4: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90e9b8: tbnz            w0, #4, #0x90e9d0
    // 0x90e9bc: r0 = Instance__$Mahjub
    //     0x90e9bc: add             x0, PP, #0x31, lsl #12  ; [pp+0x319f0] Obj!_$Mahjub@e0bd71
    //     0x90e9c0: ldr             x0, [x0, #0x9f0]
    // 0x90e9c4: LeaveFrame
    //     0x90e9c4: mov             SP, fp
    //     0x90e9c8: ldp             fp, lr, [SP], #0x10
    // 0x90e9cc: ret
    //     0x90e9cc: ret             
    // 0x90e9d0: ldr             x1, [fp, #0x10]
    // 0x90e9d4: r2 = Instance_Heir
    //     0x90e9d4: add             x2, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0x90e9d8: ldr             x2, [x2, #0x7e8]
    // 0x90e9dc: r0 = HeirsExt.has()
    //     0x90e9dc: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x90e9e0: tbnz            w0, #4, #0x90e9f8
    // 0x90e9e4: r0 = Instance__$Ashobah
    //     0x90e9e4: add             x0, PP, #0x31, lsl #12  ; [pp+0x319c8] Obj!_$Ashobah@e0bdd1
    //     0x90e9e8: ldr             x0, [x0, #0x9c8]
    // 0x90e9ec: LeaveFrame
    //     0x90e9ec: mov             SP, fp
    //     0x90e9f0: ldp             fp, lr, [SP], #0x10
    // 0x90e9f4: ret
    //     0x90e9f4: ret             
    // 0x90e9f8: r0 = Instance__$Ashobah
    //     0x90e9f8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90e9fc: ldr             x0, [x0, #0x988]
    // 0x90ea00: LeaveFrame
    //     0x90ea00: mov             SP, fp
    //     0x90ea04: ldp             fp, lr, [SP], #0x10
    // 0x90ea08: ret
    //     0x90ea08: ret             
    // 0x90ea0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90ea0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90ea10: b               #0x90e9a8
  }
  [closure] static Share <anonymous closure>(dynamic, Map<Heir, int>) {
    // ** addr: 0x90ea14, size: 0xc
    // 0x90ea14: r0 = Instance__$Ashobah
    //     0x90ea14: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x90ea18: ldr             x0, [x0, #0x988]
    // 0x90ea1c: ret
    //     0x90ea1c: ret             
  }
}
