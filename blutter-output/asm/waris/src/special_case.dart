// lib: , url: package:waris/src/special_case.dart

// class id: 1051241, size: 0x8
class :: {
}

// class id: 377, size: 0xc, field offset: 0x8
class SpecialCase extends Object {

  get _ isMusytarokah(/* No info */) {
    // ** addr: 0x909cac, size: 0x204
    // 0x909cac: EnterFrame
    //     0x909cac: stp             fp, lr, [SP, #-0x10]!
    //     0x909cb0: mov             fp, SP
    // 0x909cb4: AllocStack(0x30)
    //     0x909cb4: sub             SP, SP, #0x30
    // 0x909cb8: CheckStackOverflow
    //     0x909cb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x909cbc: cmp             SP, x16
    //     0x909cc0: b.ls            #0x909ea8
    // 0x909cc4: LoadField: r0 = r1->field_7
    //     0x909cc4: ldur            w0, [x1, #7]
    // 0x909cc8: DecompressPointer r0
    //     0x909cc8: add             x0, x0, HEAP, lsl #32
    // 0x909ccc: mov             x1, x0
    // 0x909cd0: stur            x0, [fp, #-8]
    // 0x909cd4: r2 = Instance_Heir
    //     0x909cd4: add             x2, PP, #0x31, lsl #12  ; [pp+0x318c8] Obj!Heir@e2dd81
    //     0x909cd8: ldr             x2, [x2, #0x8c8]
    // 0x909cdc: r0 = HeirsExt.has()
    //     0x909cdc: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x909ce0: r1 = Null
    //     0x909ce0: mov             x1, NULL
    // 0x909ce4: r2 = 6
    //     0x909ce4: movz            x2, #0x6
    // 0x909ce8: stur            x0, [fp, #-0x10]
    // 0x909cec: r0 = AllocateArray()
    //     0x909cec: bl              #0xec22fc  ; AllocateArrayStub
    // 0x909cf0: stur            x0, [fp, #-0x18]
    // 0x909cf4: r16 = Instance_Heir
    //     0x909cf4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31918] Obj!Heir@e2d9f1
    //     0x909cf8: ldr             x16, [x16, #0x918]
    // 0x909cfc: StoreField: r0->field_f = r16
    //     0x909cfc: stur            w16, [x0, #0xf]
    // 0x909d00: r16 = Instance_Heir
    //     0x909d00: add             x16, PP, #0x31, lsl #12  ; [pp+0x318f8] Obj!Heir@e2d9c1
    //     0x909d04: ldr             x16, [x16, #0x8f8]
    // 0x909d08: StoreField: r0->field_13 = r16
    //     0x909d08: stur            w16, [x0, #0x13]
    // 0x909d0c: r16 = Instance_Heir
    //     0x909d0c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31908] Obj!Heir@e2d991
    //     0x909d10: ldr             x16, [x16, #0x908]
    // 0x909d14: ArrayStore: r0[0] = r16  ; List_4
    //     0x909d14: stur            w16, [x0, #0x17]
    // 0x909d18: r1 = <Heir>
    //     0x909d18: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x909d1c: ldr             x1, [x1, #0xc0]
    // 0x909d20: r0 = AllocateGrowableArray()
    //     0x909d20: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x909d24: mov             x1, x0
    // 0x909d28: ldur            x0, [fp, #-0x18]
    // 0x909d2c: StoreField: r1->field_f = r0
    //     0x909d2c: stur            w0, [x1, #0xf]
    // 0x909d30: r0 = 6
    //     0x909d30: movz            x0, #0x6
    // 0x909d34: StoreField: r1->field_b = r0
    //     0x909d34: stur            w0, [x1, #0xb]
    // 0x909d38: mov             x2, x1
    // 0x909d3c: ldur            x1, [fp, #-8]
    // 0x909d40: r0 = HeirsExt.hasAnyOf()
    //     0x909d40: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x909d44: r1 = Null
    //     0x909d44: mov             x1, NULL
    // 0x909d48: r2 = 4
    //     0x909d48: movz            x2, #0x4
    // 0x909d4c: stur            x0, [fp, #-0x18]
    // 0x909d50: r0 = AllocateArray()
    //     0x909d50: bl              #0xec22fc  ; AllocateArrayStub
    // 0x909d54: stur            x0, [fp, #-0x20]
    // 0x909d58: r16 = Instance_Heir
    //     0x909d58: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b8] Obj!Heir@e2d901
    //     0x909d5c: ldr             x16, [x16, #0xb8]
    // 0x909d60: StoreField: r0->field_f = r16
    //     0x909d60: stur            w16, [x0, #0xf]
    // 0x909d64: r16 = Instance_Heir
    //     0x909d64: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x909d68: ldr             x16, [x16, #0xa0]
    // 0x909d6c: StoreField: r0->field_13 = r16
    //     0x909d6c: stur            w16, [x0, #0x13]
    // 0x909d70: r1 = <Heir>
    //     0x909d70: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x909d74: ldr             x1, [x1, #0xc0]
    // 0x909d78: r0 = AllocateGrowableArray()
    //     0x909d78: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x909d7c: mov             x1, x0
    // 0x909d80: ldur            x0, [fp, #-0x20]
    // 0x909d84: StoreField: r1->field_f = r0
    //     0x909d84: stur            w0, [x1, #0xf]
    // 0x909d88: r0 = 4
    //     0x909d88: movz            x0, #0x4
    // 0x909d8c: StoreField: r1->field_b = r0
    //     0x909d8c: stur            w0, [x1, #0xb]
    // 0x909d90: mov             x2, x1
    // 0x909d94: ldur            x1, [fp, #-8]
    // 0x909d98: r0 = HeirsExt.hasAnyOf()
    //     0x909d98: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x909d9c: ldur            x1, [fp, #-8]
    // 0x909da0: r2 = Instance_Heir
    //     0x909da0: add             x2, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x909da4: ldr             x2, [x2, #0x90]
    // 0x909da8: stur            x0, [fp, #-0x20]
    // 0x909dac: r0 = HeirsExt.has()
    //     0x909dac: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x909db0: r1 = Null
    //     0x909db0: mov             x1, NULL
    // 0x909db4: r2 = 8
    //     0x909db4: movz            x2, #0x8
    // 0x909db8: stur            x0, [fp, #-0x28]
    // 0x909dbc: r0 = AllocateArray()
    //     0x909dbc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x909dc0: stur            x0, [fp, #-0x30]
    // 0x909dc4: r16 = Instance_Heir
    //     0x909dc4: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0x909dc8: ldr             x16, [x16, #0x7d0]
    // 0x909dcc: StoreField: r0->field_f = r16
    //     0x909dcc: stur            w16, [x0, #0xf]
    // 0x909dd0: r16 = Instance_Heir
    //     0x909dd0: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0x909dd4: ldr             x16, [x16, #0x7e0]
    // 0x909dd8: StoreField: r0->field_13 = r16
    //     0x909dd8: stur            w16, [x0, #0x13]
    // 0x909ddc: r16 = Instance_Heir
    //     0x909ddc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x909de0: ldr             x16, [x16, #0x838]
    // 0x909de4: ArrayStore: r0[0] = r16  ; List_4
    //     0x909de4: stur            w16, [x0, #0x17]
    // 0x909de8: r16 = Instance_Heir
    //     0x909de8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0x909dec: ldr             x16, [x16, #0x88]
    // 0x909df0: StoreField: r0->field_1b = r16
    //     0x909df0: stur            w16, [x0, #0x1b]
    // 0x909df4: r1 = <Heir>
    //     0x909df4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x909df8: ldr             x1, [x1, #0xc0]
    // 0x909dfc: r0 = AllocateGrowableArray()
    //     0x909dfc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x909e00: mov             x1, x0
    // 0x909e04: ldur            x0, [fp, #-0x30]
    // 0x909e08: StoreField: r1->field_f = r0
    //     0x909e08: stur            w0, [x1, #0xf]
    // 0x909e0c: r0 = 8
    //     0x909e0c: movz            x0, #0x8
    // 0x909e10: StoreField: r1->field_b = r0
    //     0x909e10: stur            w0, [x1, #0xb]
    // 0x909e14: mov             x2, x1
    // 0x909e18: ldur            x1, [fp, #-8]
    // 0x909e1c: r0 = HeirsExt.notHasAnyOf()
    //     0x909e1c: bl              #0x909850  ; [package:waris/src/heirs.dart] ::HeirsExt.notHasAnyOf
    // 0x909e20: r1 = Null
    //     0x909e20: mov             x1, NULL
    // 0x909e24: r2 = 10
    //     0x909e24: movz            x2, #0xa
    // 0x909e28: stur            x0, [fp, #-8]
    // 0x909e2c: r0 = AllocateArray()
    //     0x909e2c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x909e30: mov             x2, x0
    // 0x909e34: ldur            x0, [fp, #-0x10]
    // 0x909e38: stur            x2, [fp, #-0x30]
    // 0x909e3c: StoreField: r2->field_f = r0
    //     0x909e3c: stur            w0, [x2, #0xf]
    // 0x909e40: ldur            x0, [fp, #-0x18]
    // 0x909e44: StoreField: r2->field_13 = r0
    //     0x909e44: stur            w0, [x2, #0x13]
    // 0x909e48: ldur            x0, [fp, #-0x20]
    // 0x909e4c: ArrayStore: r2[0] = r0  ; List_4
    //     0x909e4c: stur            w0, [x2, #0x17]
    // 0x909e50: ldur            x0, [fp, #-0x28]
    // 0x909e54: StoreField: r2->field_1b = r0
    //     0x909e54: stur            w0, [x2, #0x1b]
    // 0x909e58: ldur            x0, [fp, #-8]
    // 0x909e5c: StoreField: r2->field_1f = r0
    //     0x909e5c: stur            w0, [x2, #0x1f]
    // 0x909e60: r1 = <bool>
    //     0x909e60: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    // 0x909e64: r0 = AllocateGrowableArray()
    //     0x909e64: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x909e68: mov             x3, x0
    // 0x909e6c: ldur            x0, [fp, #-0x30]
    // 0x909e70: stur            x3, [fp, #-8]
    // 0x909e74: StoreField: r3->field_f = r0
    //     0x909e74: stur            w0, [x3, #0xf]
    // 0x909e78: r0 = 10
    //     0x909e78: movz            x0, #0xa
    // 0x909e7c: StoreField: r3->field_b = r0
    //     0x909e7c: stur            w0, [x3, #0xb]
    // 0x909e80: r1 = Function '<anonymous closure>':.
    //     0x909e80: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c658] AnonymousClosure: (0xebd554), in [package:flutter/src/services/restoration.dart] RestorationBucket::_visitChildren (0x69ffb0)
    //     0x909e84: ldr             x1, [x1, #0x658]
    // 0x909e88: r2 = Null
    //     0x909e88: mov             x2, NULL
    // 0x909e8c: r0 = AllocateClosure()
    //     0x909e8c: bl              #0xec1630  ; AllocateClosureStub
    // 0x909e90: ldur            x1, [fp, #-8]
    // 0x909e94: mov             x2, x0
    // 0x909e98: r0 = every()
    //     0x909e98: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0x909e9c: LeaveFrame
    //     0x909e9c: mov             SP, fp
    //     0x909ea0: ldp             fp, lr, [SP], #0x10
    // 0x909ea4: ret
    //     0x909ea4: ret             
    // 0x909ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909ea8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909eac: b               #0x909cc4
  }
  get _ hasMaternalSibling(/* No info */) {
    // ** addr: 0x90a44c, size: 0x40
    // 0x90a44c: EnterFrame
    //     0x90a44c: stp             fp, lr, [SP, #-0x10]!
    //     0x90a450: mov             fp, SP
    // 0x90a454: CheckStackOverflow
    //     0x90a454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a458: cmp             SP, x16
    //     0x90a45c: b.ls            #0x90a484
    // 0x90a460: LoadField: r0 = r1->field_7
    //     0x90a460: ldur            w0, [x1, #7]
    // 0x90a464: DecompressPointer r0
    //     0x90a464: add             x0, x0, HEAP, lsl #32
    // 0x90a468: mov             x1, x0
    // 0x90a46c: r2 = const [Instance of 'Heir', Instance of 'Heir']
    //     0x90a46c: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c598] List<Heir>(2)
    //     0x90a470: ldr             x2, [x2, #0x598]
    // 0x90a474: r0 = HeirsExt.hasEveryOf()
    //     0x90a474: bl              #0x90a48c  ; [package:waris/src/heirs.dart] ::HeirsExt.hasEveryOf
    // 0x90a478: LeaveFrame
    //     0x90a478: mov             SP, fp
    //     0x90a47c: ldp             fp, lr, [SP], #0x10
    // 0x90a480: ret
    //     0x90a480: ret             
    // 0x90a484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a484: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a488: b               #0x90a460
  }
  get _ isGharrawain(/* No info */) {
    // ** addr: 0x90a568, size: 0xfc
    // 0x90a568: EnterFrame
    //     0x90a568: stp             fp, lr, [SP, #-0x10]!
    //     0x90a56c: mov             fp, SP
    // 0x90a570: AllocStack(0x28)
    //     0x90a570: sub             SP, SP, #0x28
    // 0x90a574: CheckStackOverflow
    //     0x90a574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a578: cmp             SP, x16
    //     0x90a57c: b.ls            #0x90a65c
    // 0x90a580: LoadField: r0 = r1->field_7
    //     0x90a580: ldur            w0, [x1, #7]
    // 0x90a584: DecompressPointer r0
    //     0x90a584: add             x0, x0, HEAP, lsl #32
    // 0x90a588: mov             x1, x0
    // 0x90a58c: stur            x0, [fp, #-8]
    // 0x90a590: r2 = const [Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir']
    //     0x90a590: add             x2, PP, #0x31, lsl #12  ; [pp+0x31a00] List<Heir>(4)
    //     0x90a594: ldr             x2, [x2, #0xa00]
    // 0x90a598: r0 = HeirsExt.hasAnyOf()
    //     0x90a598: bl              #0x909884  ; [package:waris/src/heirs.dart] ::HeirsExt.hasAnyOf
    // 0x90a59c: tbnz            w0, #4, #0x90a5b0
    // 0x90a5a0: r0 = false
    //     0x90a5a0: add             x0, NULL, #0x30  ; false
    // 0x90a5a4: LeaveFrame
    //     0x90a5a4: mov             SP, fp
    //     0x90a5a8: ldp             fp, lr, [SP], #0x10
    // 0x90a5ac: ret
    //     0x90a5ac: ret             
    // 0x90a5b0: ldur            x1, [fp, #-8]
    // 0x90a5b4: r2 = const [Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir']
    //     0x90a5b4: add             x2, PP, #0x31, lsl #12  ; [pp+0x31a08] List<Heir>(6)
    //     0x90a5b8: ldr             x2, [x2, #0xa08]
    // 0x90a5bc: r0 = HeirsExt.search()
    //     0x90a5bc: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90a5c0: stur            x0, [fp, #-0x10]
    // 0x90a5c4: LoadField: r1 = r0->field_b
    //     0x90a5c4: ldur            w1, [x0, #0xb]
    // 0x90a5c8: cbz             w1, #0x90a62c
    // 0x90a5cc: ldur            x1, [fp, #-8]
    // 0x90a5d0: r1 = 1
    //     0x90a5d0: movz            x1, #0x1
    // 0x90a5d4: r0 = AllocateContext()
    //     0x90a5d4: bl              #0xec126c  ; AllocateContextStub
    // 0x90a5d8: mov             x1, x0
    // 0x90a5dc: ldur            x0, [fp, #-8]
    // 0x90a5e0: StoreField: r1->field_f = r0
    //     0x90a5e0: stur            w0, [x1, #0xf]
    // 0x90a5e4: mov             x2, x1
    // 0x90a5e8: r1 = Function '<anonymous closure>': static.
    //     0x90a5e8: add             x1, PP, #0x31, lsl #12  ; [pp+0x31a10] AnonymousClosure: static (0x90ac14), of [package:waris/src/heirs.dart] 
    //     0x90a5ec: ldr             x1, [x1, #0xa10]
    // 0x90a5f0: r0 = AllocateClosure()
    //     0x90a5f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x90a5f4: r16 = <int>
    //     0x90a5f4: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x90a5f8: ldur            lr, [fp, #-0x10]
    // 0x90a5fc: stp             lr, x16, [SP, #8]
    // 0x90a600: str             x0, [SP]
    // 0x90a604: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x90a604: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90a608: r0 = map()
    //     0x90a608: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x90a60c: mov             x1, x0
    // 0x90a610: r0 = IterableIntegerExtension.sum()
    //     0x90a610: bl              #0x909eb0  ; [package:collection/src/iterable_extensions.dart] ::IterableIntegerExtension.sum
    // 0x90a614: cmp             x0, #1
    // 0x90a618: b.le            #0x90a62c
    // 0x90a61c: r0 = false
    //     0x90a61c: add             x0, NULL, #0x30  ; false
    // 0x90a620: LeaveFrame
    //     0x90a620: mov             SP, fp
    //     0x90a624: ldp             fp, lr, [SP], #0x10
    // 0x90a628: ret
    //     0x90a628: ret             
    // 0x90a62c: ldur            x1, [fp, #-8]
    // 0x90a630: r2 = const [Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir']
    //     0x90a630: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c678] List<Heir>(4)
    //     0x90a634: ldr             x2, [x2, #0x678]
    // 0x90a638: r0 = HeirsExt.search()
    //     0x90a638: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x90a63c: LoadField: r1 = r0->field_b
    //     0x90a63c: ldur            w1, [x0, #0xb]
    // 0x90a640: cmp             w1, #6
    // 0x90a644: r16 = true
    //     0x90a644: add             x16, NULL, #0x20  ; true
    // 0x90a648: r17 = false
    //     0x90a648: add             x17, NULL, #0x30  ; false
    // 0x90a64c: csel            x0, x16, x17, eq
    // 0x90a650: LeaveFrame
    //     0x90a650: mov             SP, fp
    //     0x90a654: ldp             fp, lr, [SP], #0x10
    // 0x90a658: ret
    //     0x90a658: ret             
    // 0x90a65c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a65c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a660: b               #0x90a580
  }
}
