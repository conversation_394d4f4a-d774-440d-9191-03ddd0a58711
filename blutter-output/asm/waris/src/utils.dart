// lib: , url: package:waris/src/utils.dart

// class id: 1051242, size: 0x8
class :: {

  static _ IntIterableExt.gcd(/* No info */) {
    // ** addr: 0x90943c, size: 0x40
    // 0x90943c: EnterFrame
    //     0x90943c: stp             fp, lr, [SP, #-0x10]!
    //     0x909440: mov             fp, SP
    // 0x909444: mov             x2, x1
    // 0x909448: CheckStackOverflow
    //     0x909448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90944c: cmp             SP, x16
    //     0x909450: b.ls            #0x909474
    // 0x909454: LoadField: r1 = r2->field_7
    //     0x909454: ldur            w1, [x2, #7]
    // 0x909458: DecompressPointer r1
    //     0x909458: add             x1, x1, HEAP, lsl #32
    // 0x90945c: r0 = _GrowableList.of()
    //     0x90945c: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x909460: mov             x1, x0
    // 0x909464: r0 = gcdOfMany()
    //     0x909464: bl              #0x90947c  ; [package:waris/src/utils.dart] ::gcdOfMany
    // 0x909468: LeaveFrame
    //     0x909468: mov             SP, fp
    //     0x90946c: ldp             fp, lr, [SP], #0x10
    // 0x909470: ret
    //     0x909470: ret             
    // 0x909474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909474: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909478: b               #0x909454
  }
  static _ gcdOfMany(/* No info */) {
    // ** addr: 0x90947c, size: 0x118
    // 0x90947c: EnterFrame
    //     0x90947c: stp             fp, lr, [SP, #-0x10]!
    //     0x909480: mov             fp, SP
    // 0x909484: AllocStack(0x10)
    //     0x909484: sub             SP, SP, #0x10
    // 0x909488: SetupParameters(dynamic _ /* r1 => r3, fp-0x10 */)
    //     0x909488: mov             x3, x1
    //     0x90948c: stur            x1, [fp, #-0x10]
    // 0x909490: CheckStackOverflow
    //     0x909490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x909494: cmp             SP, x16
    //     0x909498: b.ls            #0x909580
    // 0x90949c: LoadField: r0 = r3->field_b
    //     0x90949c: ldur            w0, [x3, #0xb]
    // 0x9094a0: r1 = LoadInt32Instr(r0)
    //     0x9094a0: sbfx            x1, x0, #1, #0x1f
    // 0x9094a4: cbnz            x1, #0x9094b8
    // 0x9094a8: r0 = 0
    //     0x9094a8: movz            x0, #0
    // 0x9094ac: LeaveFrame
    //     0x9094ac: mov             SP, fp
    //     0x9094b0: ldp             fp, lr, [SP], #0x10
    // 0x9094b4: ret
    //     0x9094b4: ret             
    // 0x9094b8: mov             x0, x1
    // 0x9094bc: r1 = 0
    //     0x9094bc: movz            x1, #0
    // 0x9094c0: cmp             x1, x0
    // 0x9094c4: b.hs            #0x909588
    // 0x9094c8: LoadField: r0 = r3->field_f
    //     0x9094c8: ldur            w0, [x3, #0xf]
    // 0x9094cc: DecompressPointer r0
    //     0x9094cc: add             x0, x0, HEAP, lsl #32
    // 0x9094d0: LoadField: r1 = r0->field_f
    //     0x9094d0: ldur            w1, [x0, #0xf]
    // 0x9094d4: DecompressPointer r1
    //     0x9094d4: add             x1, x1, HEAP, lsl #32
    // 0x9094d8: r0 = LoadInt32Instr(r1)
    //     0x9094d8: sbfx            x0, x1, #1, #0x1f
    //     0x9094dc: tbz             w1, #0, #0x9094e4
    //     0x9094e0: ldur            x0, [x1, #7]
    // 0x9094e4: tbz             x0, #0x3f, #0x9094f0
    // 0x9094e8: neg             x1, x0
    // 0x9094ec: mov             x0, x1
    // 0x9094f0: mov             x2, x0
    // 0x9094f4: r4 = 1
    //     0x9094f4: movz            x4, #0x1
    // 0x9094f8: stur            x4, [fp, #-8]
    // 0x9094fc: CheckStackOverflow
    //     0x9094fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x909500: cmp             SP, x16
    //     0x909504: b.ls            #0x90958c
    // 0x909508: LoadField: r0 = r3->field_b
    //     0x909508: ldur            w0, [x3, #0xb]
    // 0x90950c: r1 = LoadInt32Instr(r0)
    //     0x90950c: sbfx            x1, x0, #1, #0x1f
    // 0x909510: cmp             x4, x1
    // 0x909514: b.ge            #0x909570
    // 0x909518: cmp             x2, #1
    // 0x90951c: b.le            #0x909570
    // 0x909520: LoadField: r0 = r3->field_f
    //     0x909520: ldur            w0, [x3, #0xf]
    // 0x909524: DecompressPointer r0
    //     0x909524: add             x0, x0, HEAP, lsl #32
    // 0x909528: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0x909528: add             x16, x0, x4, lsl #2
    //     0x90952c: ldur            w5, [x16, #0xf]
    // 0x909530: DecompressPointer r5
    //     0x909530: add             x5, x5, HEAP, lsl #32
    // 0x909534: r0 = BoxInt64Instr(r2)
    //     0x909534: sbfiz           x0, x2, #1, #0x1f
    //     0x909538: cmp             x2, x0, asr #1
    //     0x90953c: b.eq            #0x909548
    //     0x909540: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x909544: stur            x2, [x0, #7]
    // 0x909548: r2 = LoadInt32Instr(r5)
    //     0x909548: sbfx            x2, x5, #1, #0x1f
    //     0x90954c: tbz             w5, #0, #0x909554
    //     0x909550: ldur            x2, [x5, #7]
    // 0x909554: mov             x1, x0
    // 0x909558: r0 = gcd()
    //     0x909558: bl              #0x909594  ; [dart:core] _IntegerImplementation::gcd
    // 0x90955c: ldur            x1, [fp, #-8]
    // 0x909560: add             x4, x1, #1
    // 0x909564: mov             x2, x0
    // 0x909568: ldur            x3, [fp, #-0x10]
    // 0x90956c: b               #0x9094f8
    // 0x909570: mov             x0, x2
    // 0x909574: LeaveFrame
    //     0x909574: mov             SP, fp
    //     0x909578: ldp             fp, lr, [SP], #0x10
    // 0x90957c: ret
    //     0x90957c: ret             
    // 0x909580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909580: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909584: b               #0x90949c
    // 0x909588: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x909588: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x90958c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90958c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909590: b               #0x909508
  }
  static _ FractionIterableExt.lcm(/* No info */) {
    // ** addr: 0x90a054, size: 0x74
    // 0x90a054: EnterFrame
    //     0x90a054: stp             fp, lr, [SP, #-0x10]!
    //     0x90a058: mov             fp, SP
    // 0x90a05c: AllocStack(0x20)
    //     0x90a05c: sub             SP, SP, #0x20
    // 0x90a060: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x90a060: mov             x0, x1
    //     0x90a064: stur            x1, [fp, #-8]
    // 0x90a068: CheckStackOverflow
    //     0x90a068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a06c: cmp             SP, x16
    //     0x90a070: b.ls            #0x90a0c0
    // 0x90a074: r1 = Function '<anonymous closure>': static.
    //     0x90a074: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c660] Function: [dart:io] _SocketControlMessageImpl::type (0xbdd58c)
    //     0x90a078: ldr             x1, [x1, #0x660]
    // 0x90a07c: r2 = Null
    //     0x90a07c: mov             x2, NULL
    // 0x90a080: r0 = AllocateClosure()
    //     0x90a080: bl              #0xec1630  ; AllocateClosureStub
    // 0x90a084: r16 = <int>
    //     0x90a084: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x90a088: ldur            lr, [fp, #-8]
    // 0x90a08c: stp             lr, x16, [SP, #8]
    // 0x90a090: str             x0, [SP]
    // 0x90a094: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x90a094: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90a098: r0 = map()
    //     0x90a098: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0x90a09c: LoadField: r1 = r0->field_7
    //     0x90a09c: ldur            w1, [x0, #7]
    // 0x90a0a0: DecompressPointer r1
    //     0x90a0a0: add             x1, x1, HEAP, lsl #32
    // 0x90a0a4: mov             x2, x0
    // 0x90a0a8: r0 = _GrowableList.of()
    //     0x90a0a8: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x90a0ac: mov             x1, x0
    // 0x90a0b0: r0 = lcmOfMany()
    //     0x90a0b0: bl              #0x90a0c8  ; [package:waris/src/utils.dart] ::lcmOfMany
    // 0x90a0b4: LeaveFrame
    //     0x90a0b4: mov             SP, fp
    //     0x90a0b8: ldp             fp, lr, [SP], #0x10
    // 0x90a0bc: ret
    //     0x90a0bc: ret             
    // 0x90a0c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a0c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a0c4: b               #0x90a074
  }
  static _ lcmOfMany(/* No info */) {
    // ** addr: 0x90a0c8, size: 0x170
    // 0x90a0c8: EnterFrame
    //     0x90a0c8: stp             fp, lr, [SP, #-0x10]!
    //     0x90a0cc: mov             fp, SP
    // 0x90a0d0: AllocStack(0x20)
    //     0x90a0d0: sub             SP, SP, #0x20
    // 0x90a0d4: SetupParameters(dynamic _ /* r1 => r3, fp-0x20 */)
    //     0x90a0d4: mov             x3, x1
    //     0x90a0d8: stur            x1, [fp, #-0x20]
    // 0x90a0dc: CheckStackOverflow
    //     0x90a0dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a0e0: cmp             SP, x16
    //     0x90a0e4: b.ls            #0x90a20c
    // 0x90a0e8: LoadField: r0 = r3->field_b
    //     0x90a0e8: ldur            w0, [x3, #0xb]
    // 0x90a0ec: r1 = LoadInt32Instr(r0)
    //     0x90a0ec: sbfx            x1, x0, #1, #0x1f
    // 0x90a0f0: cbnz            x1, #0x90a104
    // 0x90a0f4: r0 = 1
    //     0x90a0f4: movz            x0, #0x1
    // 0x90a0f8: LeaveFrame
    //     0x90a0f8: mov             SP, fp
    //     0x90a0fc: ldp             fp, lr, [SP], #0x10
    // 0x90a100: ret
    //     0x90a100: ret             
    // 0x90a104: mov             x0, x1
    // 0x90a108: r1 = 0
    //     0x90a108: movz            x1, #0
    // 0x90a10c: cmp             x1, x0
    // 0x90a110: b.hs            #0x90a214
    // 0x90a114: LoadField: r0 = r3->field_f
    //     0x90a114: ldur            w0, [x3, #0xf]
    // 0x90a118: DecompressPointer r0
    //     0x90a118: add             x0, x0, HEAP, lsl #32
    // 0x90a11c: LoadField: r1 = r0->field_f
    //     0x90a11c: ldur            w1, [x0, #0xf]
    // 0x90a120: DecompressPointer r1
    //     0x90a120: add             x1, x1, HEAP, lsl #32
    // 0x90a124: r0 = LoadInt32Instr(r1)
    //     0x90a124: sbfx            x0, x1, #1, #0x1f
    //     0x90a128: tbz             w1, #0, #0x90a130
    //     0x90a12c: ldur            x0, [x1, #7]
    // 0x90a130: tbz             x0, #0x3f, #0x90a13c
    // 0x90a134: neg             x1, x0
    // 0x90a138: mov             x0, x1
    // 0x90a13c: mov             x5, x0
    // 0x90a140: r4 = 1
    //     0x90a140: movz            x4, #0x1
    // 0x90a144: stur            x5, [fp, #-0x10]
    // 0x90a148: stur            x4, [fp, #-0x18]
    // 0x90a14c: CheckStackOverflow
    //     0x90a14c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a150: cmp             SP, x16
    //     0x90a154: b.ls            #0x90a218
    // 0x90a158: LoadField: r0 = r3->field_b
    //     0x90a158: ldur            w0, [x3, #0xb]
    // 0x90a15c: r1 = LoadInt32Instr(r0)
    //     0x90a15c: sbfx            x1, x0, #1, #0x1f
    // 0x90a160: cmp             x4, x1
    // 0x90a164: b.ge            #0x90a1fc
    // 0x90a168: LoadField: r0 = r3->field_f
    //     0x90a168: ldur            w0, [x3, #0xf]
    // 0x90a16c: DecompressPointer r0
    //     0x90a16c: add             x0, x0, HEAP, lsl #32
    // 0x90a170: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x90a170: add             x16, x0, x4, lsl #2
    //     0x90a174: ldur            w1, [x16, #0xf]
    // 0x90a178: DecompressPointer r1
    //     0x90a178: add             x1, x1, HEAP, lsl #32
    // 0x90a17c: cbz             x5, #0x90a194
    // 0x90a180: r6 = LoadInt32Instr(r1)
    //     0x90a180: sbfx            x6, x1, #1, #0x1f
    //     0x90a184: tbz             w1, #0, #0x90a18c
    //     0x90a188: ldur            x6, [x1, #7]
    // 0x90a18c: stur            x6, [fp, #-8]
    // 0x90a190: cbnz            x6, #0x90a1a0
    // 0x90a194: mov             x1, x4
    // 0x90a198: r5 = 0
    //     0x90a198: movz            x5, #0
    // 0x90a19c: b               #0x90a1f0
    // 0x90a1a0: r0 = BoxInt64Instr(r5)
    //     0x90a1a0: sbfiz           x0, x5, #1, #0x1f
    //     0x90a1a4: cmp             x5, x0, asr #1
    //     0x90a1a8: b.eq            #0x90a1b4
    //     0x90a1ac: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90a1b0: stur            x5, [x0, #7]
    // 0x90a1b4: mov             x1, x0
    // 0x90a1b8: mov             x2, x6
    // 0x90a1bc: r0 = gcd()
    //     0x90a1bc: bl              #0x909594  ; [dart:core] _IntegerImplementation::gcd
    // 0x90a1c0: mov             x1, x0
    // 0x90a1c4: ldur            x0, [fp, #-0x10]
    // 0x90a1c8: cbz             x1, #0x90a220
    // 0x90a1cc: sdiv            x2, x0, x1
    // 0x90a1d0: ldur            x1, [fp, #-8]
    // 0x90a1d4: mul             x3, x2, x1
    // 0x90a1d8: tbz             x3, #0x3f, #0x90a1e4
    // 0x90a1dc: neg             x1, x3
    // 0x90a1e0: b               #0x90a1e8
    // 0x90a1e4: mov             x1, x3
    // 0x90a1e8: mov             x5, x1
    // 0x90a1ec: ldur            x1, [fp, #-0x18]
    // 0x90a1f0: add             x4, x1, #1
    // 0x90a1f4: ldur            x3, [fp, #-0x20]
    // 0x90a1f8: b               #0x90a144
    // 0x90a1fc: mov             x0, x5
    // 0x90a200: LeaveFrame
    //     0x90a200: mov             SP, fp
    //     0x90a204: ldp             fp, lr, [SP], #0x10
    // 0x90a208: ret
    //     0x90a208: ret             
    // 0x90a20c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a20c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a210: b               #0x90a0e8
    // 0x90a214: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x90a214: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x90a218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a218: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a21c: b               #0x90a158
    // 0x90a220: stp             x0, x1, [SP, #-0x10]!
    // 0x90a224: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x90a228: r4 = 0
    //     0x90a228: movz            x4, #0
    // 0x90a22c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x90a230: blr             lr
    // 0x90a234: brk             #0
  }
}

// class id: 376, size: 0x18, field offset: 0x8
//   const constructor, 
class Fraction extends Object {

  _Mint field_8;
  _Mint field_10;

  _ toString(/* No info */) {
    // ** addr: 0xc433c8, size: 0x94
    // 0xc433c8: EnterFrame
    //     0xc433c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc433cc: mov             fp, SP
    // 0xc433d0: AllocStack(0x10)
    //     0xc433d0: sub             SP, SP, #0x10
    // 0xc433d4: CheckStackOverflow
    //     0xc433d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc433d8: cmp             SP, x16
    //     0xc433dc: b.ls            #0xc43454
    // 0xc433e0: ldr             x3, [fp, #0x10]
    // 0xc433e4: LoadField: r2 = r3->field_7
    //     0xc433e4: ldur            x2, [x3, #7]
    // 0xc433e8: r0 = BoxInt64Instr(r2)
    //     0xc433e8: sbfiz           x0, x2, #1, #0x1f
    //     0xc433ec: cmp             x2, x0, asr #1
    //     0xc433f0: b.eq            #0xc433fc
    //     0xc433f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc433f8: stur            x2, [x0, #7]
    // 0xc433fc: r1 = Null
    //     0xc433fc: mov             x1, NULL
    // 0xc43400: r2 = 6
    //     0xc43400: movz            x2, #0x6
    // 0xc43404: stur            x0, [fp, #-8]
    // 0xc43408: r0 = AllocateArray()
    //     0xc43408: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4340c: mov             x2, x0
    // 0xc43410: ldur            x0, [fp, #-8]
    // 0xc43414: StoreField: r2->field_f = r0
    //     0xc43414: stur            w0, [x2, #0xf]
    // 0xc43418: r16 = "/"
    //     0xc43418: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] "/"
    // 0xc4341c: StoreField: r2->field_13 = r16
    //     0xc4341c: stur            w16, [x2, #0x13]
    // 0xc43420: ldr             x0, [fp, #0x10]
    // 0xc43424: LoadField: r3 = r0->field_f
    //     0xc43424: ldur            x3, [x0, #0xf]
    // 0xc43428: r0 = BoxInt64Instr(r3)
    //     0xc43428: sbfiz           x0, x3, #1, #0x1f
    //     0xc4342c: cmp             x3, x0, asr #1
    //     0xc43430: b.eq            #0xc4343c
    //     0xc43434: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc43438: stur            x3, [x0, #7]
    // 0xc4343c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc4343c: stur            w0, [x2, #0x17]
    // 0xc43440: str             x2, [SP]
    // 0xc43444: r0 = _interpolate()
    //     0xc43444: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc43448: LeaveFrame
    //     0xc43448: mov             SP, fp
    //     0xc4344c: ldp             fp, lr, [SP], #0x10
    // 0xc43450: ret
    //     0xc43450: ret             
    // 0xc43454: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43454: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc43458: b               #0xc433e0
  }
}
