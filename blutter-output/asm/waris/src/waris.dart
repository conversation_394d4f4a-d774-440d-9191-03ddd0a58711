// lib: , url: package:waris/src/waris.dart

// class id: 1051243, size: 0x8
class :: {
}

// class id: 375, size: 0x3c, field offset: 0x8
class War<PERSON> extends Object {

  _ calculate(/* No info */) {
    // ** addr: 0x90726c, size: 0x74
    // 0x90726c: EnterFrame
    //     0x90726c: stp             fp, lr, [SP, #-0x10]!
    //     0x907270: mov             fp, SP
    // 0x907274: AllocStack(0x8)
    //     0x907274: sub             SP, SP, #8
    // 0x907278: SetupParameters(Waris this /* r1 => r0, fp-0x8 */)
    //     0x907278: mov             x0, x1
    //     0x90727c: stur            x1, [fp, #-8]
    // 0x907280: CheckStackOverflow
    //     0x907280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x907284: cmp             SP, x16
    //     0x907288: b.ls            #0x9072d8
    // 0x90728c: mov             x1, x0
    // 0x907290: r0 = _calculateShares()
    //     0x907290: bl              #0x90b6e4  ; [package:waris/src/waris.dart] Waris::_calculateShares
    // 0x907294: ldur            x1, [fp, #-8]
    // 0x907298: r0 = _calculateAsalMasalah()
    //     0x907298: bl              #0x90b610  ; [package:waris/src/waris.dart] Waris::_calculateAsalMasalah
    // 0x90729c: ldur            x1, [fp, #-8]
    // 0x9072a0: r0 = _calculateSihamForFurudh()
    //     0x9072a0: bl              #0x90b474  ; [package:waris/src/waris.dart] Waris::_calculateSihamForFurudh
    // 0x9072a4: ldur            x1, [fp, #-8]
    // 0x9072a8: r0 = _calculateSihamForAshobah()
    //     0x9072a8: bl              #0x90ac74  ; [package:waris/src/waris.dart] Waris::_calculateSihamForAshobah
    // 0x9072ac: ldur            x1, [fp, #-8]
    // 0x9072b0: r0 = _calculateSpecialCases()
    //     0x9072b0: bl              #0x907f78  ; [package:waris/src/waris.dart] Waris::_calculateSpecialCases
    // 0x9072b4: ldur            x1, [fp, #-8]
    // 0x9072b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9072b8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9072bc: r0 = _calculateInkisar()
    //     0x9072bc: bl              #0x907998  ; [package:waris/src/waris.dart] Waris::_calculateInkisar
    // 0x9072c0: ldur            x1, [fp, #-8]
    // 0x9072c4: r0 = _sortByPriorityOrder()
    //     0x9072c4: bl              #0x907300  ; [package:waris/src/waris.dart] Waris::_sortByPriorityOrder
    // 0x9072c8: r0 = Null
    //     0x9072c8: mov             x0, NULL
    // 0x9072cc: LeaveFrame
    //     0x9072cc: mov             SP, fp
    //     0x9072d0: ldp             fp, lr, [SP], #0x10
    // 0x9072d4: ret
    //     0x9072d4: ret             
    // 0x9072d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9072d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9072dc: b               #0x90728c
  }
  _ _sortByPriorityOrder(/* No info */) {
    // ** addr: 0x907300, size: 0x1e0
    // 0x907300: EnterFrame
    //     0x907300: stp             fp, lr, [SP, #-0x10]!
    //     0x907304: mov             fp, SP
    // 0x907308: AllocStack(0x40)
    //     0x907308: sub             SP, SP, #0x40
    // 0x90730c: SetupParameters(Waris this /* r1 => r1, fp-0x8 */)
    //     0x90730c: stur            x1, [fp, #-8]
    // 0x907310: CheckStackOverflow
    //     0x907310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x907314: cmp             SP, x16
    //     0x907318: b.ls            #0x9074d8
    // 0x90731c: r16 = <Heir, Result>
    //     0x90731c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x907320: ldr             x16, [x16, #0x438]
    // 0x907324: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x907328: stp             lr, x16, [SP]
    // 0x90732c: r0 = Map._fromLiteral()
    //     0x90732c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x907330: stur            x0, [fp, #-0x10]
    // 0x907334: r1 = 3
    //     0x907334: movz            x1, #0x3
    // 0x907338: r0 = AllocateContext()
    //     0x907338: bl              #0xec126c  ; AllocateContextStub
    // 0x90733c: ldur            x1, [fp, #-0x10]
    // 0x907340: stur            x0, [fp, #-0x18]
    // 0x907344: StoreField: r0->field_f = r1
    //     0x907344: stur            w1, [x0, #0xf]
    // 0x907348: r16 = <Heir, Result>
    //     0x907348: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x90734c: ldr             x16, [x16, #0x438]
    // 0x907350: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x907354: stp             lr, x16, [SP]
    // 0x907358: r0 = Map._fromLiteral()
    //     0x907358: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90735c: mov             x1, x0
    // 0x907360: ldur            x2, [fp, #-0x18]
    // 0x907364: stur            x1, [fp, #-0x20]
    // 0x907368: StoreField: r2->field_13 = r0
    //     0x907368: stur            w0, [x2, #0x13]
    //     0x90736c: ldurb           w16, [x2, #-1]
    //     0x907370: ldurb           w17, [x0, #-1]
    //     0x907374: and             x16, x17, x16, lsr #2
    //     0x907378: tst             x16, HEAP, lsr #32
    //     0x90737c: b.eq            #0x907384
    //     0x907380: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x907384: r16 = <Heir, Result>
    //     0x907384: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x907388: ldr             x16, [x16, #0x438]
    // 0x90738c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x907390: stp             lr, x16, [SP]
    // 0x907394: r0 = Map._fromLiteral()
    //     0x907394: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x907398: mov             x3, x0
    // 0x90739c: ldur            x2, [fp, #-0x18]
    // 0x9073a0: stur            x3, [fp, #-0x30]
    // 0x9073a4: ArrayStore: r2[0] = r0  ; List_4
    //     0x9073a4: stur            w0, [x2, #0x17]
    //     0x9073a8: ldurb           w16, [x2, #-1]
    //     0x9073ac: ldurb           w17, [x0, #-1]
    //     0x9073b0: and             x16, x17, x16, lsr #2
    //     0x9073b4: tst             x16, HEAP, lsr #32
    //     0x9073b8: b.eq            #0x9073c0
    //     0x9073bc: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9073c0: ldur            x0, [fp, #-8]
    // 0x9073c4: LoadField: r4 = r0->field_b
    //     0x9073c4: ldur            w4, [x0, #0xb]
    // 0x9073c8: DecompressPointer r4
    //     0x9073c8: add             x4, x4, HEAP, lsl #32
    // 0x9073cc: stur            x4, [fp, #-0x28]
    // 0x9073d0: r1 = Function '<anonymous closure>':.
    //     0x9073d0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c510] AnonymousClosure: (0x90763c), in [package:waris/src/waris.dart] Waris::_sortByPriorityOrder (0x907300)
    //     0x9073d4: ldr             x1, [x1, #0x510]
    // 0x9073d8: r0 = AllocateClosure()
    //     0x9073d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9073dc: ldur            x1, [fp, #-0x28]
    // 0x9073e0: mov             x2, x0
    // 0x9073e4: r0 = forEach()
    //     0x9073e4: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x9073e8: ldur            x1, [fp, #-0x10]
    // 0x9073ec: r0 = entries()
    //     0x9073ec: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0x9073f0: mov             x2, x0
    // 0x9073f4: r1 = <MapEntry<Heir, Result>>
    //     0x9073f4: add             x1, PP, #0x28, lsl #12  ; [pp+0x28460] TypeArguments: <MapEntry<Heir, Result>>
    //     0x9073f8: ldr             x1, [x1, #0x460]
    // 0x9073fc: r0 = _GrowableList.of()
    //     0x9073fc: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x907400: r1 = Function '<anonymous closure>':.
    //     0x907400: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c518] AnonymousClosure: (0x9074e0), in [package:waris/src/waris.dart] Waris::_sortByPriorityOrder (0x907300)
    //     0x907404: ldr             x1, [x1, #0x518]
    // 0x907408: r2 = Null
    //     0x907408: mov             x2, NULL
    // 0x90740c: stur            x0, [fp, #-8]
    // 0x907410: r0 = AllocateClosure()
    //     0x907410: bl              #0xec1630  ; AllocateClosureStub
    // 0x907414: str             x0, [SP]
    // 0x907418: ldur            x1, [fp, #-8]
    // 0x90741c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x90741c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x907420: r0 = sort()
    //     0x907420: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x907424: ldur            x1, [fp, #-0x20]
    // 0x907428: r0 = entries()
    //     0x907428: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0x90742c: mov             x2, x0
    // 0x907430: r1 = <MapEntry<Heir, Result>>
    //     0x907430: add             x1, PP, #0x28, lsl #12  ; [pp+0x28460] TypeArguments: <MapEntry<Heir, Result>>
    //     0x907434: ldr             x1, [x1, #0x460]
    // 0x907438: r0 = _GrowableList.of()
    //     0x907438: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x90743c: r1 = Function '<anonymous closure>':.
    //     0x90743c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c520] AnonymousClosure: (0x9074e0), in [package:waris/src/waris.dart] Waris::_sortByPriorityOrder (0x907300)
    //     0x907440: ldr             x1, [x1, #0x520]
    // 0x907444: r2 = Null
    //     0x907444: mov             x2, NULL
    // 0x907448: stur            x0, [fp, #-0x10]
    // 0x90744c: r0 = AllocateClosure()
    //     0x90744c: bl              #0xec1630  ; AllocateClosureStub
    // 0x907450: str             x0, [SP]
    // 0x907454: ldur            x1, [fp, #-0x10]
    // 0x907458: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x907458: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x90745c: r0 = sort()
    //     0x90745c: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x907460: ldur            x1, [fp, #-0x30]
    // 0x907464: r0 = entries()
    //     0x907464: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0x907468: mov             x2, x0
    // 0x90746c: r1 = <MapEntry<Heir, Result>>
    //     0x90746c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28460] TypeArguments: <MapEntry<Heir, Result>>
    //     0x907470: ldr             x1, [x1, #0x460]
    // 0x907474: r0 = _GrowableList.of()
    //     0x907474: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x907478: r1 = Function '<anonymous closure>':.
    //     0x907478: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c528] AnonymousClosure: (0x9074e0), in [package:waris/src/waris.dart] Waris::_sortByPriorityOrder (0x907300)
    //     0x90747c: ldr             x1, [x1, #0x528]
    // 0x907480: r2 = Null
    //     0x907480: mov             x2, NULL
    // 0x907484: stur            x0, [fp, #-0x18]
    // 0x907488: r0 = AllocateClosure()
    //     0x907488: bl              #0xec1630  ; AllocateClosureStub
    // 0x90748c: str             x0, [SP]
    // 0x907490: ldur            x1, [fp, #-0x18]
    // 0x907494: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x907494: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x907498: r0 = sort()
    //     0x907498: bl              #0x6e4074  ; [dart:collection] ListBase::sort
    // 0x90749c: ldur            x1, [fp, #-0x28]
    // 0x9074a0: r0 = clear()
    //     0x9074a0: bl              #0x623ad0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x9074a4: ldur            x1, [fp, #-0x28]
    // 0x9074a8: ldur            x2, [fp, #-8]
    // 0x9074ac: r0 = addEntries()
    //     0x9074ac: bl              #0x765b84  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::addEntries
    // 0x9074b0: ldur            x1, [fp, #-0x28]
    // 0x9074b4: ldur            x2, [fp, #-0x10]
    // 0x9074b8: r0 = addEntries()
    //     0x9074b8: bl              #0x765b84  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::addEntries
    // 0x9074bc: ldur            x1, [fp, #-0x28]
    // 0x9074c0: ldur            x2, [fp, #-0x18]
    // 0x9074c4: r0 = addEntries()
    //     0x9074c4: bl              #0x765b84  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::addEntries
    // 0x9074c8: r0 = Null
    //     0x9074c8: mov             x0, NULL
    // 0x9074cc: LeaveFrame
    //     0x9074cc: mov             SP, fp
    //     0x9074d0: ldp             fp, lr, [SP], #0x10
    // 0x9074d4: ret
    //     0x9074d4: ret             
    // 0x9074d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9074d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9074dc: b               #0x90731c
  }
  [closure] int <anonymous closure>(dynamic, MapEntry<Heir, Result>, MapEntry<Heir, Result>) {
    // ** addr: 0x9074e0, size: 0xa0
    // 0x9074e0: EnterFrame
    //     0x9074e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9074e4: mov             fp, SP
    // 0x9074e8: CheckStackOverflow
    //     0x9074e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9074ec: cmp             SP, x16
    //     0x9074f0: b.ls            #0x907570
    // 0x9074f4: ldr             x0, [fp, #0x18]
    // 0x9074f8: LoadField: r1 = r0->field_b
    //     0x9074f8: ldur            w1, [x0, #0xb]
    // 0x9074fc: DecompressPointer r1
    //     0x9074fc: add             x1, x1, HEAP, lsl #32
    // 0x907500: cmp             w1, NULL
    // 0x907504: b.eq            #0x907578
    // 0x907508: LoadField: r2 = r1->field_1b
    //     0x907508: ldur            x2, [x1, #0x1b]
    // 0x90750c: ldr             x0, [fp, #0x10]
    // 0x907510: LoadField: r1 = r0->field_b
    //     0x907510: ldur            w1, [x0, #0xb]
    // 0x907514: DecompressPointer r1
    //     0x907514: add             x1, x1, HEAP, lsl #32
    // 0x907518: cmp             w1, NULL
    // 0x90751c: b.eq            #0x90757c
    // 0x907520: LoadField: r3 = r1->field_1b
    //     0x907520: ldur            x3, [x1, #0x1b]
    // 0x907524: r0 = BoxInt64Instr(r2)
    //     0x907524: sbfiz           x0, x2, #1, #0x1f
    //     0x907528: cmp             x2, x0, asr #1
    //     0x90752c: b.eq            #0x907538
    //     0x907530: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x907534: stur            x2, [x0, #7]
    // 0x907538: mov             x2, x0
    // 0x90753c: r0 = BoxInt64Instr(r3)
    //     0x90753c: sbfiz           x0, x3, #1, #0x1f
    //     0x907540: cmp             x3, x0, asr #1
    //     0x907544: b.eq            #0x907550
    //     0x907548: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90754c: stur            x3, [x0, #7]
    // 0x907550: mov             x1, x2
    // 0x907554: mov             x2, x0
    // 0x907558: r0 = compareTo()
    //     0x907558: bl              #0x6daaec  ; [dart:core] _IntegerImplementation::compareTo
    // 0x90755c: lsl             x1, x0, #1
    // 0x907560: mov             x0, x1
    // 0x907564: LeaveFrame
    //     0x907564: mov             SP, fp
    //     0x907568: ldp             fp, lr, [SP], #0x10
    // 0x90756c: ret
    //     0x90756c: ret             
    // 0x907570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x907570: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x907574: b               #0x9074f4
    // 0x907578: r0 = NullErrorSharedWithoutFPURegs()
    //     0x907578: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x90757c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x90757c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Heir, Result) {
    // ** addr: 0x90763c, size: 0xec
    // 0x90763c: EnterFrame
    //     0x90763c: stp             fp, lr, [SP, #-0x10]!
    //     0x907640: mov             fp, SP
    // 0x907644: AllocStack(0x40)
    //     0x907644: sub             SP, SP, #0x40
    // 0x907648: SetupParameters()
    //     0x907648: ldr             x0, [fp, #0x20]
    //     0x90764c: ldur            w1, [x0, #0x17]
    //     0x907650: add             x1, x1, HEAP, lsl #32
    //     0x907654: stur            x1, [fp, #-8]
    // 0x907658: CheckStackOverflow
    //     0x907658: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90765c: cmp             SP, x16
    //     0x907660: b.ls            #0x907720
    // 0x907664: r1 = 2
    //     0x907664: movz            x1, #0x2
    // 0x907668: r0 = AllocateContext()
    //     0x907668: bl              #0xec126c  ; AllocateContextStub
    // 0x90766c: mov             x3, x0
    // 0x907670: ldur            x0, [fp, #-8]
    // 0x907674: stur            x3, [fp, #-0x10]
    // 0x907678: StoreField: r3->field_b = r0
    //     0x907678: stur            w0, [x3, #0xb]
    // 0x90767c: ldr             x0, [fp, #0x18]
    // 0x907680: StoreField: r3->field_f = r0
    //     0x907680: stur            w0, [x3, #0xf]
    // 0x907684: ldr             x0, [fp, #0x10]
    // 0x907688: StoreField: r3->field_13 = r0
    //     0x907688: stur            w0, [x3, #0x13]
    // 0x90768c: LoadField: r4 = r0->field_f
    //     0x90768c: ldur            w4, [x0, #0xf]
    // 0x907690: DecompressPointer r4
    //     0x907690: add             x4, x4, HEAP, lsl #32
    // 0x907694: mov             x2, x3
    // 0x907698: stur            x4, [fp, #-8]
    // 0x90769c: r1 = Function '<anonymous closure>':.
    //     0x90769c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c538] AnonymousClosure: (0x9078b4), in [package:waris/src/waris.dart] Waris::_sortByPriorityOrder (0x907300)
    //     0x9076a0: ldr             x1, [x1, #0x538]
    // 0x9076a4: r0 = AllocateClosure()
    //     0x9076a4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9076a8: ldur            x2, [fp, #-0x10]
    // 0x9076ac: r1 = Function '<anonymous closure>':.
    //     0x9076ac: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c540] AnonymousClosure: (0x9077cc), in [package:waris/src/waris.dart] Waris::_sortByPriorityOrder (0x907300)
    //     0x9076b0: ldr             x1, [x1, #0x540]
    // 0x9076b4: stur            x0, [fp, #-0x18]
    // 0x9076b8: r0 = AllocateClosure()
    //     0x9076b8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9076bc: ldur            x2, [fp, #-0x10]
    // 0x9076c0: r1 = Function '<anonymous closure>':.
    //     0x9076c0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c548] AnonymousClosure: (0x907728), in [package:waris/src/waris.dart] Waris::_sortByPriorityOrder (0x907300)
    //     0x9076c4: ldr             x1, [x1, #0x548]
    // 0x9076c8: stur            x0, [fp, #-0x10]
    // 0x9076cc: r0 = AllocateClosure()
    //     0x9076cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9076d0: mov             x1, x0
    // 0x9076d4: ldur            x0, [fp, #-8]
    // 0x9076d8: r2 = LoadClassIdInstr(r0)
    //     0x9076d8: ldur            x2, [x0, #-1]
    //     0x9076dc: ubfx            x2, x2, #0xc, #0x14
    // 0x9076e0: r16 = <void?>
    //     0x9076e0: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x9076e4: stp             x0, x16, [SP, #0x18]
    // 0x9076e8: ldur            x16, [fp, #-0x18]
    // 0x9076ec: stp             x16, x1, [SP, #8]
    // 0x9076f0: ldur            x16, [fp, #-0x10]
    // 0x9076f4: str             x16, [SP]
    // 0x9076f8: mov             x0, x2
    // 0x9076fc: r4 = const [0x1, 0x4, 0x4, 0x2, ashobah, 0x3, furudh, 0x2, null]
    //     0x9076fc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c550] List(9) [0x1, 0x4, 0x4, 0x2, "ashobah", 0x3, "furudh", 0x2, Null]
    //     0x907700: ldr             x4, [x4, #0x550]
    // 0x907704: r0 = GDT[cid_x0 + -0x1000]()
    //     0x907704: sub             lr, x0, #1, lsl #12
    //     0x907708: ldr             lr, [x21, lr, lsl #3]
    //     0x90770c: blr             lr
    // 0x907710: r0 = Null
    //     0x907710: mov             x0, NULL
    // 0x907714: LeaveFrame
    //     0x907714: mov             SP, fp
    //     0x907718: ldp             fp, lr, [SP], #0x10
    // 0x90771c: ret
    //     0x90771c: ret             
    // 0x907720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x907720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x907724: b               #0x907664
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x907728, size: 0xa4
    // 0x907728: EnterFrame
    //     0x907728: stp             fp, lr, [SP, #-0x10]!
    //     0x90772c: mov             fp, SP
    // 0x907730: AllocStack(0x28)
    //     0x907730: sub             SP, SP, #0x28
    // 0x907734: SetupParameters()
    //     0x907734: ldr             x0, [fp, #0x10]
    //     0x907738: ldur            w3, [x0, #0x17]
    //     0x90773c: add             x3, x3, HEAP, lsl #32
    //     0x907740: stur            x3, [fp, #-0x18]
    // 0x907744: CheckStackOverflow
    //     0x907744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x907748: cmp             SP, x16
    //     0x90774c: b.ls            #0x9077c4
    // 0x907750: LoadField: r0 = r3->field_b
    //     0x907750: ldur            w0, [x3, #0xb]
    // 0x907754: DecompressPointer r0
    //     0x907754: add             x0, x0, HEAP, lsl #32
    // 0x907758: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x907758: ldur            w4, [x0, #0x17]
    // 0x90775c: DecompressPointer r4
    //     0x90775c: add             x4, x4, HEAP, lsl #32
    // 0x907760: stur            x4, [fp, #-0x10]
    // 0x907764: LoadField: r0 = r3->field_f
    //     0x907764: ldur            w0, [x3, #0xf]
    // 0x907768: DecompressPointer r0
    //     0x907768: add             x0, x0, HEAP, lsl #32
    // 0x90776c: stur            x0, [fp, #-8]
    // 0x907770: r1 = Null
    //     0x907770: mov             x1, NULL
    // 0x907774: r2 = 4
    //     0x907774: movz            x2, #0x4
    // 0x907778: r0 = AllocateArray()
    //     0x907778: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90777c: mov             x1, x0
    // 0x907780: ldur            x0, [fp, #-8]
    // 0x907784: StoreField: r1->field_f = r0
    //     0x907784: stur            w0, [x1, #0xf]
    // 0x907788: ldur            x0, [fp, #-0x18]
    // 0x90778c: LoadField: r2 = r0->field_13
    //     0x90778c: ldur            w2, [x0, #0x13]
    // 0x907790: DecompressPointer r2
    //     0x907790: add             x2, x2, HEAP, lsl #32
    // 0x907794: StoreField: r1->field_13 = r2
    //     0x907794: stur            w2, [x1, #0x13]
    // 0x907798: r16 = <Heir, Result>
    //     0x907798: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x90779c: ldr             x16, [x16, #0x438]
    // 0x9077a0: stp             x1, x16, [SP]
    // 0x9077a4: r0 = Map._fromLiteral()
    //     0x9077a4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x9077a8: ldur            x1, [fp, #-0x10]
    // 0x9077ac: mov             x2, x0
    // 0x9077b0: r0 = addAll()
    //     0x9077b0: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x9077b4: r0 = Null
    //     0x9077b4: mov             x0, NULL
    // 0x9077b8: LeaveFrame
    //     0x9077b8: mov             SP, fp
    //     0x9077bc: ldp             fp, lr, [SP], #0x10
    // 0x9077c0: ret
    //     0x9077c0: ret             
    // 0x9077c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9077c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9077c8: b               #0x907750
  }
  [closure] void <anonymous closure>(dynamic, Ashobah) {
    // ** addr: 0x9077cc, size: 0xa4
    // 0x9077cc: EnterFrame
    //     0x9077cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9077d0: mov             fp, SP
    // 0x9077d4: AllocStack(0x28)
    //     0x9077d4: sub             SP, SP, #0x28
    // 0x9077d8: SetupParameters()
    //     0x9077d8: ldr             x0, [fp, #0x18]
    //     0x9077dc: ldur            w3, [x0, #0x17]
    //     0x9077e0: add             x3, x3, HEAP, lsl #32
    //     0x9077e4: stur            x3, [fp, #-0x18]
    // 0x9077e8: CheckStackOverflow
    //     0x9077e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9077ec: cmp             SP, x16
    //     0x9077f0: b.ls            #0x907868
    // 0x9077f4: LoadField: r0 = r3->field_b
    //     0x9077f4: ldur            w0, [x3, #0xb]
    // 0x9077f8: DecompressPointer r0
    //     0x9077f8: add             x0, x0, HEAP, lsl #32
    // 0x9077fc: LoadField: r4 = r0->field_13
    //     0x9077fc: ldur            w4, [x0, #0x13]
    // 0x907800: DecompressPointer r4
    //     0x907800: add             x4, x4, HEAP, lsl #32
    // 0x907804: stur            x4, [fp, #-0x10]
    // 0x907808: LoadField: r0 = r3->field_f
    //     0x907808: ldur            w0, [x3, #0xf]
    // 0x90780c: DecompressPointer r0
    //     0x90780c: add             x0, x0, HEAP, lsl #32
    // 0x907810: stur            x0, [fp, #-8]
    // 0x907814: r1 = Null
    //     0x907814: mov             x1, NULL
    // 0x907818: r2 = 4
    //     0x907818: movz            x2, #0x4
    // 0x90781c: r0 = AllocateArray()
    //     0x90781c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x907820: mov             x1, x0
    // 0x907824: ldur            x0, [fp, #-8]
    // 0x907828: StoreField: r1->field_f = r0
    //     0x907828: stur            w0, [x1, #0xf]
    // 0x90782c: ldur            x0, [fp, #-0x18]
    // 0x907830: LoadField: r2 = r0->field_13
    //     0x907830: ldur            w2, [x0, #0x13]
    // 0x907834: DecompressPointer r2
    //     0x907834: add             x2, x2, HEAP, lsl #32
    // 0x907838: StoreField: r1->field_13 = r2
    //     0x907838: stur            w2, [x1, #0x13]
    // 0x90783c: r16 = <Heir, Result>
    //     0x90783c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x907840: ldr             x16, [x16, #0x438]
    // 0x907844: stp             x1, x16, [SP]
    // 0x907848: r0 = Map._fromLiteral()
    //     0x907848: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90784c: ldur            x1, [fp, #-0x10]
    // 0x907850: mov             x2, x0
    // 0x907854: r0 = addAll()
    //     0x907854: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x907858: r0 = Null
    //     0x907858: mov             x0, NULL
    // 0x90785c: LeaveFrame
    //     0x90785c: mov             SP, fp
    //     0x907860: ldp             fp, lr, [SP], #0x10
    // 0x907864: ret
    //     0x907864: ret             
    // 0x907868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x907868: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90786c: b               #0x9077f4
  }
  [closure] void <anonymous closure>(dynamic, Furudh) {
    // ** addr: 0x9078b4, size: 0xa4
    // 0x9078b4: EnterFrame
    //     0x9078b4: stp             fp, lr, [SP, #-0x10]!
    //     0x9078b8: mov             fp, SP
    // 0x9078bc: AllocStack(0x28)
    //     0x9078bc: sub             SP, SP, #0x28
    // 0x9078c0: SetupParameters()
    //     0x9078c0: ldr             x0, [fp, #0x18]
    //     0x9078c4: ldur            w3, [x0, #0x17]
    //     0x9078c8: add             x3, x3, HEAP, lsl #32
    //     0x9078cc: stur            x3, [fp, #-0x18]
    // 0x9078d0: CheckStackOverflow
    //     0x9078d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9078d4: cmp             SP, x16
    //     0x9078d8: b.ls            #0x907950
    // 0x9078dc: LoadField: r0 = r3->field_b
    //     0x9078dc: ldur            w0, [x3, #0xb]
    // 0x9078e0: DecompressPointer r0
    //     0x9078e0: add             x0, x0, HEAP, lsl #32
    // 0x9078e4: LoadField: r4 = r0->field_f
    //     0x9078e4: ldur            w4, [x0, #0xf]
    // 0x9078e8: DecompressPointer r4
    //     0x9078e8: add             x4, x4, HEAP, lsl #32
    // 0x9078ec: stur            x4, [fp, #-0x10]
    // 0x9078f0: LoadField: r0 = r3->field_f
    //     0x9078f0: ldur            w0, [x3, #0xf]
    // 0x9078f4: DecompressPointer r0
    //     0x9078f4: add             x0, x0, HEAP, lsl #32
    // 0x9078f8: stur            x0, [fp, #-8]
    // 0x9078fc: r1 = Null
    //     0x9078fc: mov             x1, NULL
    // 0x907900: r2 = 4
    //     0x907900: movz            x2, #0x4
    // 0x907904: r0 = AllocateArray()
    //     0x907904: bl              #0xec22fc  ; AllocateArrayStub
    // 0x907908: mov             x1, x0
    // 0x90790c: ldur            x0, [fp, #-8]
    // 0x907910: StoreField: r1->field_f = r0
    //     0x907910: stur            w0, [x1, #0xf]
    // 0x907914: ldur            x0, [fp, #-0x18]
    // 0x907918: LoadField: r2 = r0->field_13
    //     0x907918: ldur            w2, [x0, #0x13]
    // 0x90791c: DecompressPointer r2
    //     0x90791c: add             x2, x2, HEAP, lsl #32
    // 0x907920: StoreField: r1->field_13 = r2
    //     0x907920: stur            w2, [x1, #0x13]
    // 0x907924: r16 = <Heir, Result>
    //     0x907924: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x907928: ldr             x16, [x16, #0x438]
    // 0x90792c: stp             x1, x16, [SP]
    // 0x907930: r0 = Map._fromLiteral()
    //     0x907930: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x907934: ldur            x1, [fp, #-0x10]
    // 0x907938: mov             x2, x0
    // 0x90793c: r0 = addAll()
    //     0x90793c: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x907940: r0 = Null
    //     0x907940: mov             x0, NULL
    // 0x907944: LeaveFrame
    //     0x907944: mov             SP, fp
    //     0x907948: ldp             fp, lr, [SP], #0x10
    // 0x90794c: ret
    //     0x90794c: ret             
    // 0x907950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x907950: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x907954: b               #0x9078dc
  }
  _ _calculateInkisar(/* No info */) {
    // ** addr: 0x907998, size: 0x1c0
    // 0x907998: EnterFrame
    //     0x907998: stp             fp, lr, [SP, #-0x10]!
    //     0x90799c: mov             fp, SP
    // 0x9079a0: AllocStack(0x38)
    //     0x9079a0: sub             SP, SP, #0x38
    // 0x9079a4: SetupParameters(Waris this /* r1 => r1, fp-0x10 */, [dynamic _ = Null /* r0, fp-0x8 */])
    //     0x9079a4: stur            x1, [fp, #-0x10]
    //     0x9079a8: ldur            w0, [x4, #0x13]
    //     0x9079ac: sub             x2, x0, #2
    //     0x9079b0: cmp             w2, #2
    //     0x9079b4: b.lt            #0x9079c4
    //     0x9079b8: add             x0, fp, w2, sxtw #2
    //     0x9079bc: ldr             x0, [x0, #8]
    //     0x9079c0: b               #0x9079c8
    //     0x9079c4: mov             x0, NULL
    //     0x9079c8: stur            x0, [fp, #-8]
    // 0x9079cc: CheckStackOverflow
    //     0x9079cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9079d0: cmp             SP, x16
    //     0x9079d4: b.ls            #0x907b50
    // 0x9079d8: r1 = 2
    //     0x9079d8: movz            x1, #0x2
    // 0x9079dc: r0 = AllocateContext()
    //     0x9079dc: bl              #0xec126c  ; AllocateContextStub
    // 0x9079e0: mov             x4, x0
    // 0x9079e4: ldur            x0, [fp, #-0x10]
    // 0x9079e8: stur            x4, [fp, #-0x20]
    // 0x9079ec: StoreField: r4->field_f = r0
    //     0x9079ec: stur            w0, [x4, #0xf]
    // 0x9079f0: ldur            x1, [fp, #-8]
    // 0x9079f4: cmp             w1, NULL
    // 0x9079f8: b.ne            #0x907ab4
    // 0x9079fc: LoadField: r5 = r0->field_b
    //     0x9079fc: ldur            w5, [x0, #0xb]
    // 0x907a00: DecompressPointer r5
    //     0x907a00: add             x5, x5, HEAP, lsl #32
    // 0x907a04: stur            x5, [fp, #-0x18]
    // 0x907a08: LoadField: r2 = r5->field_7
    //     0x907a08: ldur            w2, [x5, #7]
    // 0x907a0c: DecompressPointer r2
    //     0x907a0c: add             x2, x2, HEAP, lsl #32
    // 0x907a10: r1 = Null
    //     0x907a10: mov             x1, NULL
    // 0x907a14: r3 = <X1>
    //     0x907a14: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x907a18: r0 = Null
    //     0x907a18: mov             x0, NULL
    // 0x907a1c: cmp             x2, x0
    // 0x907a20: b.eq            #0x907a30
    // 0x907a24: r30 = InstantiateTypeArgumentsStub
    //     0x907a24: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x907a28: LoadField: r30 = r30->field_7
    //     0x907a28: ldur            lr, [lr, #7]
    // 0x907a2c: blr             lr
    // 0x907a30: mov             x1, x0
    // 0x907a34: r0 = _CompactIterable()
    //     0x907a34: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x907a38: mov             x3, x0
    // 0x907a3c: ldur            x0, [fp, #-0x18]
    // 0x907a40: stur            x3, [fp, #-0x28]
    // 0x907a44: StoreField: r3->field_b = r0
    //     0x907a44: stur            w0, [x3, #0xb]
    // 0x907a48: r0 = -1
    //     0x907a48: movn            x0, #0
    // 0x907a4c: StoreField: r3->field_f = r0
    //     0x907a4c: stur            x0, [x3, #0xf]
    // 0x907a50: r0 = 2
    //     0x907a50: movz            x0, #0x2
    // 0x907a54: ArrayStore: r3[0] = r0  ; List_8
    //     0x907a54: stur            x0, [x3, #0x17]
    // 0x907a58: r1 = Function '<anonymous closure>':.
    //     0x907a58: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c558] AnonymousClosure: (0x907ef4), in [package:waris/src/waris.dart] Waris::_calculateInkisar (0x907998)
    //     0x907a5c: ldr             x1, [x1, #0x558]
    // 0x907a60: r2 = Null
    //     0x907a60: mov             x2, NULL
    // 0x907a64: r0 = AllocateClosure()
    //     0x907a64: bl              #0xec1630  ; AllocateClosureStub
    // 0x907a68: ldur            x1, [fp, #-0x28]
    // 0x907a6c: mov             x2, x0
    // 0x907a70: r0 = where()
    //     0x907a70: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x907a74: r16 = <Result>
    //     0x907a74: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c560] TypeArguments: <Result>
    //     0x907a78: ldr             x16, [x16, #0x560]
    // 0x907a7c: stp             x0, x16, [SP]
    // 0x907a80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x907a80: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x907a84: r0 = IterableExtension.firstOrNull()
    //     0x907a84: bl              #0x907b58  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstOrNull
    // 0x907a88: cmp             w0, NULL
    // 0x907a8c: b.ne            #0x907a98
    // 0x907a90: r0 = Null
    //     0x907a90: mov             x0, NULL
    // 0x907a94: b               #0x907ab0
    // 0x907a98: LoadField: r2 = r0->field_7
    //     0x907a98: ldur            x2, [x0, #7]
    // 0x907a9c: r0 = BoxInt64Instr(r2)
    //     0x907a9c: sbfiz           x0, x2, #1, #0x1f
    //     0x907aa0: cmp             x2, x0, asr #1
    //     0x907aa4: b.eq            #0x907ab0
    //     0x907aa8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x907aac: stur            x2, [x0, #7]
    // 0x907ab0: mov             x1, x0
    // 0x907ab4: ldur            x2, [fp, #-0x20]
    // 0x907ab8: mov             x0, x1
    // 0x907abc: StoreField: r2->field_13 = r0
    //     0x907abc: stur            w0, [x2, #0x13]
    //     0x907ac0: tbz             w0, #0, #0x907adc
    //     0x907ac4: ldurb           w16, [x2, #-1]
    //     0x907ac8: ldurb           w17, [x0, #-1]
    //     0x907acc: and             x16, x17, x16, lsr #2
    //     0x907ad0: tst             x16, HEAP, lsr #32
    //     0x907ad4: b.eq            #0x907adc
    //     0x907ad8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x907adc: cmp             w1, NULL
    // 0x907ae0: b.ne            #0x907af4
    // 0x907ae4: r0 = Null
    //     0x907ae4: mov             x0, NULL
    // 0x907ae8: LeaveFrame
    //     0x907ae8: mov             SP, fp
    //     0x907aec: ldp             fp, lr, [SP], #0x10
    // 0x907af0: ret
    //     0x907af0: ret             
    // 0x907af4: ldur            x0, [fp, #-0x10]
    // 0x907af8: r3 = true
    //     0x907af8: add             x3, NULL, #0x20  ; true
    // 0x907afc: StoreField: r0->field_37 = r3
    //     0x907afc: stur            w3, [x0, #0x37]
    // 0x907b00: LoadField: r3 = r0->field_1f
    //     0x907b00: ldur            x3, [x0, #0x1f]
    // 0x907b04: r4 = LoadInt32Instr(r1)
    //     0x907b04: sbfx            x4, x1, #1, #0x1f
    //     0x907b08: tbz             w1, #0, #0x907b10
    //     0x907b0c: ldur            x4, [x1, #7]
    // 0x907b10: mul             x1, x3, x4
    // 0x907b14: StoreField: r0->field_1f = r1
    //     0x907b14: stur            x1, [x0, #0x1f]
    // 0x907b18: StoreField: r0->field_27 = rZR
    //     0x907b18: stur            xzr, [x0, #0x27]
    // 0x907b1c: LoadField: r3 = r0->field_b
    //     0x907b1c: ldur            w3, [x0, #0xb]
    // 0x907b20: DecompressPointer r3
    //     0x907b20: add             x3, x3, HEAP, lsl #32
    // 0x907b24: stur            x3, [fp, #-8]
    // 0x907b28: r1 = Function '<anonymous closure>':.
    //     0x907b28: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c568] AnonymousClosure: (0x907bfc), in [package:waris/src/waris.dart] Waris::_calculateInkisar (0x907998)
    //     0x907b2c: ldr             x1, [x1, #0x568]
    // 0x907b30: r0 = AllocateClosure()
    //     0x907b30: bl              #0xec1630  ; AllocateClosureStub
    // 0x907b34: ldur            x1, [fp, #-8]
    // 0x907b38: mov             x2, x0
    // 0x907b3c: r0 = forEach()
    //     0x907b3c: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x907b40: r0 = Null
    //     0x907b40: mov             x0, NULL
    // 0x907b44: LeaveFrame
    //     0x907b44: mov             SP, fp
    //     0x907b48: ldp             fp, lr, [SP], #0x10
    // 0x907b4c: ret
    //     0x907b4c: ret             
    // 0x907b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x907b50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x907b54: b               #0x9079d8
  }
  [closure] void <anonymous closure>(dynamic, Heir, Result) {
    // ** addr: 0x907bfc, size: 0xf8
    // 0x907bfc: EnterFrame
    //     0x907bfc: stp             fp, lr, [SP, #-0x10]!
    //     0x907c00: mov             fp, SP
    // 0x907c04: AllocStack(0x20)
    //     0x907c04: sub             SP, SP, #0x20
    // 0x907c08: SetupParameters()
    //     0x907c08: ldr             x0, [fp, #0x20]
    //     0x907c0c: ldur            w3, [x0, #0x17]
    //     0x907c10: add             x3, x3, HEAP, lsl #32
    //     0x907c14: stur            x3, [fp, #-8]
    // 0x907c18: CheckStackOverflow
    //     0x907c18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x907c1c: cmp             SP, x16
    //     0x907c20: b.ls            #0x907ce8
    // 0x907c24: LoadField: r1 = r3->field_f
    //     0x907c24: ldur            w1, [x3, #0xf]
    // 0x907c28: DecompressPointer r1
    //     0x907c28: add             x1, x1, HEAP, lsl #32
    // 0x907c2c: ldr             x2, [fp, #0x18]
    // 0x907c30: r0 = get()
    //     0x907c30: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x907c34: LoadField: r1 = r0->field_13
    //     0x907c34: ldur            x1, [x0, #0x13]
    // 0x907c38: ldur            x0, [fp, #-8]
    // 0x907c3c: LoadField: r2 = r0->field_13
    //     0x907c3c: ldur            w2, [x0, #0x13]
    // 0x907c40: DecompressPointer r2
    //     0x907c40: add             x2, x2, HEAP, lsl #32
    // 0x907c44: cmp             w2, NULL
    // 0x907c48: b.eq            #0x907cf0
    // 0x907c4c: r3 = LoadInt32Instr(r2)
    //     0x907c4c: sbfx            x3, x2, #1, #0x1f
    //     0x907c50: tbz             w2, #0, #0x907c58
    //     0x907c54: ldur            x3, [x2, #7]
    // 0x907c58: mul             x4, x1, x3
    // 0x907c5c: stur            x4, [fp, #-0x18]
    // 0x907c60: LoadField: r1 = r0->field_f
    //     0x907c60: ldur            w1, [x0, #0xf]
    // 0x907c64: DecompressPointer r1
    //     0x907c64: add             x1, x1, HEAP, lsl #32
    // 0x907c68: LoadField: r3 = r1->field_b
    //     0x907c68: ldur            w3, [x1, #0xb]
    // 0x907c6c: DecompressPointer r3
    //     0x907c6c: add             x3, x3, HEAP, lsl #32
    // 0x907c70: ldr             x2, [fp, #0x18]
    // 0x907c74: stur            x3, [fp, #-0x10]
    // 0x907c78: r0 = get()
    //     0x907c78: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x907c7c: mov             x3, x0
    // 0x907c80: ldur            x2, [fp, #-0x18]
    // 0x907c84: r0 = BoxInt64Instr(r2)
    //     0x907c84: sbfiz           x0, x2, #1, #0x1f
    //     0x907c88: cmp             x2, x0, asr #1
    //     0x907c8c: b.eq            #0x907c98
    //     0x907c90: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x907c94: stur            x2, [x0, #7]
    // 0x907c98: str             x0, [SP]
    // 0x907c9c: mov             x1, x3
    // 0x907ca0: r4 = const [0, 0x2, 0x1, 0x1, siham, 0x1, null]
    //     0x907ca0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c570] List(7) [0, 0x2, 0x1, 0x1, "siham", 0x1, Null]
    //     0x907ca4: ldr             x4, [x4, #0x570]
    // 0x907ca8: r0 = copyWith()
    //     0x907ca8: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x907cac: ldur            x1, [fp, #-0x10]
    // 0x907cb0: ldr             x2, [fp, #0x18]
    // 0x907cb4: mov             x3, x0
    // 0x907cb8: r0 = []=()
    //     0x907cb8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x907cbc: ldur            x1, [fp, #-8]
    // 0x907cc0: LoadField: r2 = r1->field_f
    //     0x907cc0: ldur            w2, [x1, #0xf]
    // 0x907cc4: DecompressPointer r2
    //     0x907cc4: add             x2, x2, HEAP, lsl #32
    // 0x907cc8: LoadField: r1 = r2->field_27
    //     0x907cc8: ldur            x1, [x2, #0x27]
    // 0x907ccc: ldur            x3, [fp, #-0x18]
    // 0x907cd0: add             x4, x1, x3
    // 0x907cd4: StoreField: r2->field_27 = r4
    //     0x907cd4: stur            x4, [x2, #0x27]
    // 0x907cd8: r0 = Null
    //     0x907cd8: mov             x0, NULL
    // 0x907cdc: LeaveFrame
    //     0x907cdc: mov             SP, fp
    //     0x907ce0: ldp             fp, lr, [SP], #0x10
    // 0x907ce4: ret
    //     0x907ce4: ret             
    // 0x907ce8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x907ce8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x907cec: b               #0x907c24
    // 0x907cf0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x907cf0: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ get(/* No info */) {
    // ** addr: 0x907e90, size: 0x64
    // 0x907e90: EnterFrame
    //     0x907e90: stp             fp, lr, [SP, #-0x10]!
    //     0x907e94: mov             fp, SP
    // 0x907e98: AllocStack(0x8)
    //     0x907e98: sub             SP, SP, #8
    // 0x907e9c: CheckStackOverflow
    //     0x907e9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x907ea0: cmp             SP, x16
    //     0x907ea4: b.ls            #0x907ee8
    // 0x907ea8: LoadField: r0 = r1->field_b
    //     0x907ea8: ldur            w0, [x1, #0xb]
    // 0x907eac: DecompressPointer r0
    //     0x907eac: add             x0, x0, HEAP, lsl #32
    // 0x907eb0: mov             x1, x0
    // 0x907eb4: stur            x0, [fp, #-8]
    // 0x907eb8: r0 = _getValueOrData()
    //     0x907eb8: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x907ebc: ldur            x1, [fp, #-8]
    // 0x907ec0: LoadField: r2 = r1->field_f
    //     0x907ec0: ldur            w2, [x1, #0xf]
    // 0x907ec4: DecompressPointer r2
    //     0x907ec4: add             x2, x2, HEAP, lsl #32
    // 0x907ec8: cmp             w2, w0
    // 0x907ecc: b.ne            #0x907ed4
    // 0x907ed0: r0 = Null
    //     0x907ed0: mov             x0, NULL
    // 0x907ed4: cmp             w0, NULL
    // 0x907ed8: b.eq            #0x907ef0
    // 0x907edc: LeaveFrame
    //     0x907edc: mov             SP, fp
    //     0x907ee0: ldp             fp, lr, [SP], #0x10
    // 0x907ee4: ret
    //     0x907ee4: ret             
    // 0x907ee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x907ee8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x907eec: b               #0x907ea8
    // 0x907ef0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x907ef0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, Result) {
    // ** addr: 0x907ef4, size: 0x84
    // 0x907ef4: EnterFrame
    //     0x907ef4: stp             fp, lr, [SP, #-0x10]!
    //     0x907ef8: mov             fp, SP
    // 0x907efc: ldr             x1, [fp, #0x10]
    // 0x907f00: LoadField: r2 = r1->field_7
    //     0x907f00: ldur            x2, [x1, #7]
    // 0x907f04: cmp             x2, #1
    // 0x907f08: b.le            #0x907f3c
    // 0x907f0c: LoadField: r3 = r1->field_13
    //     0x907f0c: ldur            x3, [x1, #0x13]
    // 0x907f10: cbz             x2, #0x907f4c
    // 0x907f14: sdiv            x4, x3, x2
    // 0x907f18: msub            x1, x4, x2, x3
    // 0x907f1c: cmp             x1, xzr
    // 0x907f20: b.lt            #0x907f64
    // 0x907f24: cbnz            x1, #0x907f30
    // 0x907f28: r2 = false
    //     0x907f28: add             x2, NULL, #0x30  ; false
    // 0x907f2c: b               #0x907f34
    // 0x907f30: r2 = true
    //     0x907f30: add             x2, NULL, #0x20  ; true
    // 0x907f34: mov             x0, x2
    // 0x907f38: b               #0x907f40
    // 0x907f3c: r0 = false
    //     0x907f3c: add             x0, NULL, #0x30  ; false
    // 0x907f40: LeaveFrame
    //     0x907f40: mov             SP, fp
    //     0x907f44: ldp             fp, lr, [SP], #0x10
    // 0x907f48: ret
    //     0x907f48: ret             
    // 0x907f4c: stp             x2, x3, [SP, #-0x10]!
    // 0x907f50: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x907f54: r4 = 0
    //     0x907f54: movz            x4, #0
    // 0x907f58: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x907f5c: blr             lr
    // 0x907f60: brk             #0
    // 0x907f64: cmp             x2, xzr
    // 0x907f68: sub             x4, x1, x2
    // 0x907f6c: add             x1, x1, x2
    // 0x907f70: csel            x1, x4, x1, lt
    // 0x907f74: b               #0x907f24
  }
  _ _calculateSpecialCases(/* No info */) {
    // ** addr: 0x907f78, size: 0x13d4
    // 0x907f78: EnterFrame
    //     0x907f78: stp             fp, lr, [SP, #-0x10]!
    //     0x907f7c: mov             fp, SP
    // 0x907f80: AllocStack(0xa8)
    //     0x907f80: sub             SP, SP, #0xa8
    // 0x907f84: SetupParameters(Waris this /* r1 => r1, fp-0x8 */)
    //     0x907f84: stur            x1, [fp, #-8]
    // 0x907f88: CheckStackOverflow
    //     0x907f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x907f8c: cmp             SP, x16
    //     0x907f90: b.ls            #0x909258
    // 0x907f94: r1 = 7
    //     0x907f94: movz            x1, #0x7
    // 0x907f98: r0 = AllocateContext()
    //     0x907f98: bl              #0xec126c  ; AllocateContextStub
    // 0x907f9c: ldur            x1, [fp, #-8]
    // 0x907fa0: stur            x0, [fp, #-0x18]
    // 0x907fa4: StoreField: r0->field_f = r1
    //     0x907fa4: stur            w1, [x0, #0xf]
    // 0x907fa8: LoadField: r2 = r1->field_7
    //     0x907fa8: ldur            w2, [x1, #7]
    // 0x907fac: DecompressPointer r2
    //     0x907fac: add             x2, x2, HEAP, lsl #32
    // 0x907fb0: stur            x2, [fp, #-0x10]
    // 0x907fb4: r0 = SpecialCase()
    //     0x907fb4: bl              #0x90a664  ; AllocateSpecialCaseStub -> SpecialCase (size=0xc)
    // 0x907fb8: mov             x2, x0
    // 0x907fbc: ldur            x0, [fp, #-0x10]
    // 0x907fc0: stur            x2, [fp, #-0x20]
    // 0x907fc4: StoreField: r2->field_7 = r0
    //     0x907fc4: stur            w0, [x2, #7]
    // 0x907fc8: mov             x1, x2
    // 0x907fcc: r0 = isGharrawain()
    //     0x907fcc: bl              #0x90a568  ; [package:waris/src/special_case.dart] SpecialCase::isGharrawain
    // 0x907fd0: tbnz            w0, #4, #0x9081b0
    // 0x907fd4: ldur            x0, [fp, #-8]
    // 0x907fd8: StoreField: r0->field_27 = rZR
    //     0x907fd8: stur            xzr, [x0, #0x27]
    // 0x907fdc: ldur            x1, [fp, #-0x10]
    // 0x907fe0: r2 = Instance_Heir
    //     0x907fe0: add             x2, PP, #0x31, lsl #12  ; [pp+0x318c8] Obj!Heir@e2dd81
    //     0x907fe4: ldr             x2, [x2, #0x8c8]
    // 0x907fe8: r0 = HeirsExt.has()
    //     0x907fe8: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x907fec: tbnz            w0, #4, #0x908060
    // 0x907ff0: ldur            x0, [fp, #-8]
    // 0x907ff4: r1 = 6
    //     0x907ff4: movz            x1, #0x6
    // 0x907ff8: StoreField: r0->field_1f = r1
    //     0x907ff8: stur            x1, [x0, #0x1f]
    // 0x907ffc: LoadField: r3 = r0->field_b
    //     0x907ffc: ldur            w3, [x0, #0xb]
    // 0x908000: DecompressPointer r3
    //     0x908000: add             x3, x3, HEAP, lsl #32
    // 0x908004: mov             x1, x0
    // 0x908008: stur            x3, [fp, #-0x28]
    // 0x90800c: r2 = Instance_Heir
    //     0x90800c: add             x2, PP, #0x31, lsl #12  ; [pp+0x318c8] Obj!Heir@e2dd81
    //     0x908010: ldr             x2, [x2, #0x8c8]
    // 0x908014: r0 = get()
    //     0x908014: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x908018: r16 = Instance__$Furudh
    //     0x908018: add             x16, PP, #0x31, lsl #12  ; [pp+0x319e0] Obj!_$Furudh@e0be41
    //     0x90801c: ldr             x16, [x16, #0x9e0]
    // 0x908020: r30 = 6
    //     0x908020: movz            lr, #0x6
    // 0x908024: stp             lr, x16, [SP]
    // 0x908028: mov             x1, x0
    // 0x90802c: r4 = const [0, 0x3, 0x2, 0x1, share, 0x1, siham, 0x2, null]
    //     0x90802c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c578] List(9) [0, 0x3, 0x2, 0x1, "share", 0x1, "siham", 0x2, Null]
    //     0x908030: ldr             x4, [x4, #0x578]
    // 0x908034: r0 = copyWith()
    //     0x908034: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x908038: ldur            x1, [fp, #-0x28]
    // 0x90803c: mov             x3, x0
    // 0x908040: r2 = Instance_Heir
    //     0x908040: add             x2, PP, #0x31, lsl #12  ; [pp+0x318c8] Obj!Heir@e2dd81
    //     0x908044: ldr             x2, [x2, #0x8c8]
    // 0x908048: r0 = []=()
    //     0x908048: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90804c: ldur            x0, [fp, #-8]
    // 0x908050: LoadField: r1 = r0->field_27
    //     0x908050: ldur            x1, [x0, #0x27]
    // 0x908054: add             x2, x1, #3
    // 0x908058: StoreField: r0->field_27 = r2
    //     0x908058: stur            x2, [x0, #0x27]
    // 0x90805c: b               #0x908064
    // 0x908060: ldur            x0, [fp, #-8]
    // 0x908064: ldur            x1, [fp, #-0x10]
    // 0x908068: r2 = Instance_Heir
    //     0x908068: add             x2, PP, #0x31, lsl #12  ; [pp+0x31950] Obj!Heir@e2da81
    //     0x90806c: ldr             x2, [x2, #0x950]
    // 0x908070: r0 = HeirsExt.has()
    //     0x908070: bl              #0x90a4fc  ; [package:waris/src/heirs.dart] ::HeirsExt.has
    // 0x908074: tbnz            w0, #4, #0x9080e8
    // 0x908078: ldur            x0, [fp, #-8]
    // 0x90807c: r1 = 4
    //     0x90807c: movz            x1, #0x4
    // 0x908080: StoreField: r0->field_1f = r1
    //     0x908080: stur            x1, [x0, #0x1f]
    // 0x908084: LoadField: r3 = r0->field_b
    //     0x908084: ldur            w3, [x0, #0xb]
    // 0x908088: DecompressPointer r3
    //     0x908088: add             x3, x3, HEAP, lsl #32
    // 0x90808c: mov             x1, x0
    // 0x908090: stur            x3, [fp, #-0x28]
    // 0x908094: r2 = Instance_Heir
    //     0x908094: add             x2, PP, #0x31, lsl #12  ; [pp+0x31950] Obj!Heir@e2da81
    //     0x908098: ldr             x2, [x2, #0x950]
    // 0x90809c: r0 = get()
    //     0x90809c: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x9080a0: r16 = Instance__$Furudh
    //     0x9080a0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31998] Obj!_$Furudh@e0be21
    //     0x9080a4: ldr             x16, [x16, #0x998]
    // 0x9080a8: r30 = 2
    //     0x9080a8: movz            lr, #0x2
    // 0x9080ac: stp             lr, x16, [SP]
    // 0x9080b0: mov             x1, x0
    // 0x9080b4: r4 = const [0, 0x3, 0x2, 0x1, share, 0x1, siham, 0x2, null]
    //     0x9080b4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c578] List(9) [0, 0x3, 0x2, 0x1, "share", 0x1, "siham", 0x2, Null]
    //     0x9080b8: ldr             x4, [x4, #0x578]
    // 0x9080bc: r0 = copyWith()
    //     0x9080bc: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x9080c0: ldur            x1, [fp, #-0x28]
    // 0x9080c4: mov             x3, x0
    // 0x9080c8: r2 = Instance_Heir
    //     0x9080c8: add             x2, PP, #0x31, lsl #12  ; [pp+0x31950] Obj!Heir@e2da81
    //     0x9080cc: ldr             x2, [x2, #0x950]
    // 0x9080d0: r0 = []=()
    //     0x9080d0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x9080d4: ldur            x0, [fp, #-8]
    // 0x9080d8: LoadField: r1 = r0->field_27
    //     0x9080d8: ldur            x1, [x0, #0x27]
    // 0x9080dc: add             x2, x1, #1
    // 0x9080e0: StoreField: r0->field_27 = r2
    //     0x9080e0: stur            x2, [x0, #0x27]
    // 0x9080e4: b               #0x9080ec
    // 0x9080e8: ldur            x0, [fp, #-8]
    // 0x9080ec: LoadField: r3 = r0->field_b
    //     0x9080ec: ldur            w3, [x0, #0xb]
    // 0x9080f0: DecompressPointer r3
    //     0x9080f0: add             x3, x3, HEAP, lsl #32
    // 0x9080f4: mov             x1, x0
    // 0x9080f8: stur            x3, [fp, #-0x28]
    // 0x9080fc: r2 = Instance_Heir
    //     0x9080fc: add             x2, PP, #0x31, lsl #12  ; [pp+0x31918] Obj!Heir@e2d9f1
    //     0x908100: ldr             x2, [x2, #0x918]
    // 0x908104: r0 = get()
    //     0x908104: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x908108: r16 = Instance__$Furudh
    //     0x908108: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c580] Obj!_$Furudh@e0be01
    //     0x90810c: ldr             x16, [x16, #0x580]
    // 0x908110: r30 = 2
    //     0x908110: movz            lr, #0x2
    // 0x908114: stp             lr, x16, [SP]
    // 0x908118: mov             x1, x0
    // 0x90811c: r4 = const [0, 0x3, 0x2, 0x1, share, 0x1, siham, 0x2, null]
    //     0x90811c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c578] List(9) [0, 0x3, 0x2, 0x1, "share", 0x1, "siham", 0x2, Null]
    //     0x908120: ldr             x4, [x4, #0x578]
    // 0x908124: r0 = copyWith()
    //     0x908124: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x908128: ldur            x1, [fp, #-0x28]
    // 0x90812c: mov             x3, x0
    // 0x908130: r2 = Instance_Heir
    //     0x908130: add             x2, PP, #0x31, lsl #12  ; [pp+0x31918] Obj!Heir@e2d9f1
    //     0x908134: ldr             x2, [x2, #0x918]
    // 0x908138: r0 = []=()
    //     0x908138: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90813c: ldur            x0, [fp, #-8]
    // 0x908140: LoadField: r1 = r0->field_27
    //     0x908140: ldur            x1, [x0, #0x27]
    // 0x908144: add             x2, x1, #1
    // 0x908148: StoreField: r0->field_27 = r2
    //     0x908148: stur            x2, [x0, #0x27]
    // 0x90814c: mov             x1, x0
    // 0x908150: r2 = Instance_Heir
    //     0x908150: add             x2, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x908154: ldr             x2, [x2, #0x838]
    // 0x908158: r0 = get()
    //     0x908158: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x90815c: r16 = Instance__$Ashobah
    //     0x90815c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x908160: ldr             x16, [x16, #0x988]
    // 0x908164: r30 = 4
    //     0x908164: movz            lr, #0x4
    // 0x908168: stp             lr, x16, [SP]
    // 0x90816c: mov             x1, x0
    // 0x908170: r4 = const [0, 0x3, 0x2, 0x1, share, 0x1, siham, 0x2, null]
    //     0x908170: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c578] List(9) [0, 0x3, 0x2, 0x1, "share", 0x1, "siham", 0x2, Null]
    //     0x908174: ldr             x4, [x4, #0x578]
    // 0x908178: r0 = copyWith()
    //     0x908178: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90817c: ldur            x1, [fp, #-0x28]
    // 0x908180: mov             x3, x0
    // 0x908184: r2 = Instance_Heir
    //     0x908184: add             x2, PP, #0x31, lsl #12  ; [pp+0x31838] Obj!Heir@e2dcc1
    //     0x908188: ldr             x2, [x2, #0x838]
    // 0x90818c: r0 = []=()
    //     0x90818c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x908190: ldur            x0, [fp, #-8]
    // 0x908194: LoadField: r1 = r0->field_27
    //     0x908194: ldur            x1, [x0, #0x27]
    // 0x908198: add             x2, x1, #2
    // 0x90819c: StoreField: r0->field_27 = r2
    //     0x90819c: stur            x2, [x0, #0x27]
    // 0x9081a0: r0 = Null
    //     0x9081a0: mov             x0, NULL
    // 0x9081a4: LeaveFrame
    //     0x9081a4: mov             SP, fp
    //     0x9081a8: ldp             fp, lr, [SP], #0x10
    // 0x9081ac: ret
    //     0x9081ac: ret             
    // 0x9081b0: ldur            x0, [fp, #-8]
    // 0x9081b4: ldur            x1, [fp, #-0x20]
    // 0x9081b8: r0 = hasMaternalSibling()
    //     0x9081b8: bl              #0x90a44c  ; [package:waris/src/special_case.dart] SpecialCase::hasMaternalSibling
    // 0x9081bc: tbnz            w0, #4, #0x9086e4
    // 0x9081c0: ldur            x0, [fp, #-8]
    // 0x9081c4: LoadField: r3 = r0->field_b
    //     0x9081c4: ldur            w3, [x0, #0xb]
    // 0x9081c8: DecompressPointer r3
    //     0x9081c8: add             x3, x3, HEAP, lsl #32
    // 0x9081cc: mov             x1, x3
    // 0x9081d0: stur            x3, [fp, #-0x28]
    // 0x9081d4: r2 = Instance_Heir
    //     0x9081d4: add             x2, PP, #0x28, lsl #12  ; [pp+0x280a0] Obj!Heir@e2dc01
    //     0x9081d8: ldr             x2, [x2, #0xa0]
    // 0x9081dc: r0 = _getValueOrData()
    //     0x9081dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9081e0: mov             x1, x0
    // 0x9081e4: ldur            x0, [fp, #-0x28]
    // 0x9081e8: LoadField: r2 = r0->field_f
    //     0x9081e8: ldur            w2, [x0, #0xf]
    // 0x9081ec: DecompressPointer r2
    //     0x9081ec: add             x2, x2, HEAP, lsl #32
    // 0x9081f0: cmp             w2, w1
    // 0x9081f4: b.ne            #0x9081fc
    // 0x9081f8: r1 = Null
    //     0x9081f8: mov             x1, NULL
    // 0x9081fc: cmp             w1, NULL
    // 0x908200: b.eq            #0x909260
    // 0x908204: LoadField: r3 = r1->field_f
    //     0x908204: ldur            w3, [x1, #0xf]
    // 0x908208: DecompressPointer r3
    //     0x908208: add             x3, x3, HEAP, lsl #32
    // 0x90820c: mov             x1, x0
    // 0x908210: stur            x3, [fp, #-0x30]
    // 0x908214: r2 = Instance_Heir
    //     0x908214: add             x2, PP, #0x28, lsl #12  ; [pp+0x280b8] Obj!Heir@e2d901
    //     0x908218: ldr             x2, [x2, #0xb8]
    // 0x90821c: r0 = _getValueOrData()
    //     0x90821c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x908220: mov             x1, x0
    // 0x908224: ldur            x0, [fp, #-0x28]
    // 0x908228: LoadField: r2 = r0->field_f
    //     0x908228: ldur            w2, [x0, #0xf]
    // 0x90822c: DecompressPointer r2
    //     0x90822c: add             x2, x2, HEAP, lsl #32
    // 0x908230: cmp             w2, w1
    // 0x908234: b.ne            #0x90823c
    // 0x908238: r1 = Null
    //     0x908238: mov             x1, NULL
    // 0x90823c: ldur            x3, [fp, #-0x30]
    // 0x908240: r4 = 4
    //     0x908240: movz            x4, #0x4
    // 0x908244: cmp             w1, NULL
    // 0x908248: b.eq            #0x909264
    // 0x90824c: LoadField: r5 = r1->field_f
    //     0x90824c: ldur            w5, [x1, #0xf]
    // 0x908250: DecompressPointer r5
    //     0x908250: add             x5, x5, HEAP, lsl #32
    // 0x908254: mov             x2, x4
    // 0x908258: stur            x5, [fp, #-0x38]
    // 0x90825c: r1 = Null
    //     0x90825c: mov             x1, NULL
    // 0x908260: r0 = AllocateArray()
    //     0x908260: bl              #0xec22fc  ; AllocateArrayStub
    // 0x908264: mov             x2, x0
    // 0x908268: ldur            x0, [fp, #-0x30]
    // 0x90826c: stur            x2, [fp, #-0x40]
    // 0x908270: StoreField: r2->field_f = r0
    //     0x908270: stur            w0, [x2, #0xf]
    // 0x908274: ldur            x0, [fp, #-0x38]
    // 0x908278: StoreField: r2->field_13 = r0
    //     0x908278: stur            w0, [x2, #0x13]
    // 0x90827c: r1 = <Share>
    //     0x90827c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c588] TypeArguments: <Share>
    //     0x908280: ldr             x1, [x1, #0x588]
    // 0x908284: r0 = AllocateGrowableArray()
    //     0x908284: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x908288: mov             x3, x0
    // 0x90828c: ldur            x0, [fp, #-0x40]
    // 0x908290: stur            x3, [fp, #-0x30]
    // 0x908294: StoreField: r3->field_f = r0
    //     0x908294: stur            w0, [x3, #0xf]
    // 0x908298: r0 = 4
    //     0x908298: movz            x0, #0x4
    // 0x90829c: StoreField: r3->field_b = r0
    //     0x90829c: stur            w0, [x3, #0xb]
    // 0x9082a0: r1 = Function '<anonymous closure>':.
    //     0x9082a0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c590] AnonymousClosure: (0x90abf4), in [package:waris/src/waris.dart] Waris::_calculateSpecialCases (0x907f78)
    //     0x9082a4: ldr             x1, [x1, #0x590]
    // 0x9082a8: r2 = Null
    //     0x9082a8: mov             x2, NULL
    // 0x9082ac: r0 = AllocateClosure()
    //     0x9082ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x9082b0: ldur            x1, [fp, #-0x30]
    // 0x9082b4: mov             x2, x0
    // 0x9082b8: r0 = every()
    //     0x9082b8: bl              #0x6a87c8  ; [dart:collection] ListBase::every
    // 0x9082bc: tbnz            w0, #4, #0x9086e4
    // 0x9082c0: ldur            x1, [fp, #-8]
    // 0x9082c4: LoadField: r0 = r1->field_f
    //     0x9082c4: ldur            w0, [x1, #0xf]
    // 0x9082c8: DecompressPointer r0
    //     0x9082c8: add             x0, x0, HEAP, lsl #32
    // 0x9082cc: stur            x0, [fp, #-0x38]
    // 0x9082d0: r3 = 0
    //     0x9082d0: movz            x3, #0
    // 0x9082d4: r2 = const [Instance of 'Heir', Instance of 'Heir']
    //     0x9082d4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c598] List<Heir>(2)
    //     0x9082d8: ldr             x2, [x2, #0x598]
    // 0x9082dc: CheckStackOverflow
    //     0x9082dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9082e0: cmp             SP, x16
    //     0x9082e4: b.ls            #0x909268
    // 0x9082e8: cmp             x3, #2
    // 0x9082ec: b.ge            #0x908338
    // 0x9082f0: ArrayLoad: r4 = r2[r3]  ; Unknown_4
    //     0x9082f0: add             x16, x2, x3, lsl #2
    //     0x9082f4: ldur            w4, [x16, #0xf]
    // 0x9082f8: DecompressPointer r4
    //     0x9082f8: add             x4, x4, HEAP, lsl #32
    // 0x9082fc: stur            x4, [fp, #-0x30]
    // 0x908300: add             x5, x3, #1
    // 0x908304: stur            x5, [fp, #-0x48]
    // 0x908308: str             x4, [SP]
    // 0x90830c: r0 = _getHash()
    //     0x90830c: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0x908310: r5 = LoadInt32Instr(r0)
    //     0x908310: sbfx            x5, x0, #1, #0x1f
    // 0x908314: ldur            x1, [fp, #-0x38]
    // 0x908318: ldur            x2, [fp, #-0x30]
    // 0x90831c: r3 = Instance__$Furudh
    //     0x90831c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c5a0] Obj!_$Furudh@e0bde1
    //     0x908320: ldr             x3, [x3, #0x5a0]
    // 0x908324: r0 = _set()
    //     0x908324: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x908328: ldur            x3, [fp, #-0x48]
    // 0x90832c: ldur            x1, [fp, #-8]
    // 0x908330: ldur            x0, [fp, #-0x38]
    // 0x908334: b               #0x9082d4
    // 0x908338: mov             x4, x0
    // 0x90833c: mov             x0, x1
    // 0x908340: ldur            x5, [fp, #-0x18]
    // 0x908344: LoadField: r2 = r4->field_7
    //     0x908344: ldur            w2, [x4, #7]
    // 0x908348: DecompressPointer r2
    //     0x908348: add             x2, x2, HEAP, lsl #32
    // 0x90834c: r1 = Null
    //     0x90834c: mov             x1, NULL
    // 0x908350: r3 = <X1>
    //     0x908350: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x908354: r0 = Null
    //     0x908354: mov             x0, NULL
    // 0x908358: cmp             x2, x0
    // 0x90835c: b.eq            #0x90836c
    // 0x908360: r30 = InstantiateTypeArgumentsStub
    //     0x908360: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x908364: LoadField: r30 = r30->field_7
    //     0x908364: ldur            lr, [lr, #7]
    // 0x908368: blr             lr
    // 0x90836c: mov             x1, x0
    // 0x908370: r0 = _CompactIterable()
    //     0x908370: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x908374: mov             x3, x0
    // 0x908378: ldur            x0, [fp, #-0x38]
    // 0x90837c: stur            x3, [fp, #-0x30]
    // 0x908380: StoreField: r3->field_b = r0
    //     0x908380: stur            w0, [x3, #0xb]
    // 0x908384: r4 = -1
    //     0x908384: movn            x4, #0
    // 0x908388: StoreField: r3->field_f = r4
    //     0x908388: stur            x4, [x3, #0xf]
    // 0x90838c: r5 = 2
    //     0x90838c: movz            x5, #0x2
    // 0x908390: ArrayStore: r3[0] = r5  ; List_8
    //     0x908390: stur            x5, [x3, #0x17]
    // 0x908394: r1 = Function '<anonymous closure>':.
    //     0x908394: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5a8] AnonymousClosure: (0x90a898), in [package:waris/src/waris.dart] Waris::_calculateSpecialCases (0x907f78)
    //     0x908398: ldr             x1, [x1, #0x5a8]
    // 0x90839c: r2 = Null
    //     0x90839c: mov             x2, NULL
    // 0x9083a0: r0 = AllocateClosure()
    //     0x9083a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x9083a4: r16 = <Fraction?>
    //     0x9083a4: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c5b0] TypeArguments: <Fraction?>
    //     0x9083a8: ldr             x16, [x16, #0x5b0]
    // 0x9083ac: ldur            lr, [fp, #-0x30]
    // 0x9083b0: stp             lr, x16, [SP, #8]
    // 0x9083b4: str             x0, [SP]
    // 0x9083b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9083b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9083bc: r0 = map()
    //     0x9083bc: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0x9083c0: r16 = <Fraction>
    //     0x9083c0: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c5b8] TypeArguments: <Fraction>
    //     0x9083c4: ldr             x16, [x16, #0x5b8]
    // 0x9083c8: stp             x0, x16, [SP]
    // 0x9083cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9083cc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9083d0: r0 = IterableNullableExtension.whereNotNull()
    //     0x9083d0: bl              #0x90a258  ; [package:collection/src/iterable_extensions.dart] ::IterableNullableExtension.whereNotNull
    // 0x9083d4: r1 = Null
    //     0x9083d4: mov             x1, NULL
    // 0x9083d8: r2 = 2
    //     0x9083d8: movz            x2, #0x2
    // 0x9083dc: stur            x0, [fp, #-0x30]
    // 0x9083e0: r0 = AllocateArray()
    //     0x9083e0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9083e4: stur            x0, [fp, #-0x40]
    // 0x9083e8: r16 = Instance_Fraction
    //     0x9083e8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c5c0] Obj!Fraction@e0bc41
    //     0x9083ec: ldr             x16, [x16, #0x5c0]
    // 0x9083f0: StoreField: r0->field_f = r16
    //     0x9083f0: stur            w16, [x0, #0xf]
    // 0x9083f4: r1 = <Fraction>
    //     0x9083f4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5b8] TypeArguments: <Fraction>
    //     0x9083f8: ldr             x1, [x1, #0x5b8]
    // 0x9083fc: r0 = AllocateGrowableArray()
    //     0x9083fc: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x908400: mov             x1, x0
    // 0x908404: ldur            x0, [fp, #-0x40]
    // 0x908408: StoreField: r1->field_f = r0
    //     0x908408: stur            w0, [x1, #0xf]
    // 0x90840c: r0 = 2
    //     0x90840c: movz            x0, #0x2
    // 0x908410: StoreField: r1->field_b = r0
    //     0x908410: stur            w0, [x1, #0xb]
    // 0x908414: mov             x2, x1
    // 0x908418: ldur            x1, [fp, #-0x30]
    // 0x90841c: r0 = followedBy()
    //     0x90841c: bl              #0x7e6360  ; [dart:core] Iterable::followedBy
    // 0x908420: mov             x1, x0
    // 0x908424: r0 = FractionIterableExt.lcm()
    //     0x908424: bl              #0x90a054  ; [package:waris/src/utils.dart] ::FractionIterableExt.lcm
    // 0x908428: mov             x1, x0
    // 0x90842c: ldur            x0, [fp, #-8]
    // 0x908430: StoreField: r0->field_1f = r1
    //     0x908430: stur            x1, [x0, #0x1f]
    // 0x908434: StoreField: r0->field_27 = rZR
    //     0x908434: stur            xzr, [x0, #0x27]
    // 0x908438: ldur            x3, [fp, #-0x18]
    // 0x90843c: r4 = false
    //     0x90843c: add             x4, NULL, #0x30  ; false
    // 0x908440: StoreField: r3->field_1f = r4
    //     0x908440: stur            w4, [x3, #0x1f]
    // 0x908444: StoreField: r3->field_23 = rZR
    //     0x908444: stur            wzr, [x3, #0x23]
    // 0x908448: mov             x2, x3
    // 0x90844c: r1 = Function '<anonymous closure>':.
    //     0x90844c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5c8] AnonymousClosure: (0x90aa58), in [package:waris/src/waris.dart] Waris::_calculateSpecialCases (0x907f78)
    //     0x908450: ldr             x1, [x1, #0x5c8]
    // 0x908454: r0 = AllocateClosure()
    //     0x908454: bl              #0xec1630  ; AllocateClosureStub
    // 0x908458: ldur            x1, [fp, #-0x38]
    // 0x90845c: mov             x2, x0
    // 0x908460: r0 = forEach()
    //     0x908460: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x908464: ldur            x0, [fp, #-8]
    // 0x908468: LoadField: r1 = r0->field_1f
    //     0x908468: ldur            x1, [x0, #0x1f]
    // 0x90846c: LoadField: r2 = r0->field_27
    //     0x90846c: ldur            x2, [x0, #0x27]
    // 0x908470: sub             x3, x1, x2
    // 0x908474: stur            x3, [fp, #-0x50]
    // 0x908478: cmp             x3, #0
    // 0x90847c: b.le            #0x9085ec
    // 0x908480: StoreField: r0->field_27 = r1
    //     0x908480: stur            x1, [x0, #0x27]
    // 0x908484: r1 = 0
    //     0x908484: movz            x1, #0
    // 0x908488: ldur            x4, [fp, #-0x18]
    // 0x90848c: ldur            x6, [fp, #-0x28]
    // 0x908490: r5 = const [Instance of 'Heir', Instance of 'Heir']
    //     0x908490: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c598] List<Heir>(2)
    //     0x908494: ldr             x5, [x5, #0x598]
    // 0x908498: CheckStackOverflow
    //     0x908498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90849c: cmp             SP, x16
    //     0x9084a0: b.ls            #0x909270
    // 0x9084a4: cmp             x1, #2
    // 0x9084a8: b.ge            #0x9085ec
    // 0x9084ac: ArrayLoad: r7 = r5[r1]  ; Unknown_4
    //     0x9084ac: add             x16, x5, x1, lsl #2
    //     0x9084b0: ldur            w7, [x16, #0xf]
    // 0x9084b4: DecompressPointer r7
    //     0x9084b4: add             x7, x7, HEAP, lsl #32
    // 0x9084b8: stur            x7, [fp, #-0x30]
    // 0x9084bc: add             x8, x1, #1
    // 0x9084c0: mov             x1, x6
    // 0x9084c4: mov             x2, x7
    // 0x9084c8: stur            x8, [fp, #-0x48]
    // 0x9084cc: r0 = _getValueOrData()
    //     0x9084cc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9084d0: ldur            x3, [fp, #-0x28]
    // 0x9084d4: LoadField: r1 = r3->field_f
    //     0x9084d4: ldur            w1, [x3, #0xf]
    // 0x9084d8: DecompressPointer r1
    //     0x9084d8: add             x1, x1, HEAP, lsl #32
    // 0x9084dc: cmp             w1, w0
    // 0x9084e0: b.ne            #0x9084e8
    // 0x9084e4: r0 = Null
    //     0x9084e4: mov             x0, NULL
    // 0x9084e8: ldur            x5, [fp, #-0x18]
    // 0x9084ec: ldur            x4, [fp, #-0x50]
    // 0x9084f0: cmp             w0, NULL
    // 0x9084f4: b.eq            #0x909278
    // 0x9084f8: LoadField: r1 = r0->field_13
    //     0x9084f8: ldur            x1, [x0, #0x13]
    // 0x9084fc: add             x6, x1, x4
    // 0x908500: stur            x6, [fp, #-0x58]
    // 0x908504: r0 = BoxInt64Instr(r6)
    //     0x908504: sbfiz           x0, x6, #1, #0x1f
    //     0x908508: cmp             x6, x0, asr #1
    //     0x90850c: b.eq            #0x908518
    //     0x908510: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x908514: stur            x6, [x0, #7]
    // 0x908518: StoreField: r5->field_23 = r0
    //     0x908518: stur            w0, [x5, #0x23]
    //     0x90851c: tbz             w0, #0, #0x908538
    //     0x908520: ldurb           w16, [x5, #-1]
    //     0x908524: ldurb           w17, [x0, #-1]
    //     0x908528: and             x16, x17, x16, lsr #2
    //     0x90852c: tst             x16, HEAP, lsr #32
    //     0x908530: b.eq            #0x908538
    //     0x908534: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0x908538: mov             x1, x3
    // 0x90853c: ldur            x2, [fp, #-0x30]
    // 0x908540: r0 = _getValueOrData()
    //     0x908540: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x908544: ldur            x1, [fp, #-0x28]
    // 0x908548: LoadField: r2 = r1->field_f
    //     0x908548: ldur            w2, [x1, #0xf]
    // 0x90854c: DecompressPointer r2
    //     0x90854c: add             x2, x2, HEAP, lsl #32
    // 0x908550: cmp             w2, w0
    // 0x908554: b.ne            #0x908560
    // 0x908558: r2 = Null
    //     0x908558: mov             x2, NULL
    // 0x90855c: b               #0x908564
    // 0x908560: mov             x2, x0
    // 0x908564: ldur            x0, [fp, #-0x58]
    // 0x908568: cmp             w2, NULL
    // 0x90856c: b.eq            #0x90927c
    // 0x908570: LoadField: r3 = r2->field_7
    //     0x908570: ldur            x3, [x2, #7]
    // 0x908574: stur            x3, [fp, #-0x68]
    // 0x908578: LoadField: r4 = r2->field_f
    //     0x908578: ldur            w4, [x2, #0xf]
    // 0x90857c: DecompressPointer r4
    //     0x90857c: add             x4, x4, HEAP, lsl #32
    // 0x908580: stur            x4, [fp, #-0x60]
    // 0x908584: LoadField: r5 = r2->field_1b
    //     0x908584: ldur            w5, [x2, #0x1b]
    // 0x908588: DecompressPointer r5
    //     0x908588: add             x5, x5, HEAP, lsl #32
    // 0x90858c: stur            x5, [fp, #-0x40]
    // 0x908590: r0 = Result()
    //     0x908590: bl              #0x907e84  ; AllocateResultStub -> Result (size=0x20)
    // 0x908594: mov             x1, x0
    // 0x908598: ldur            x0, [fp, #-0x68]
    // 0x90859c: stur            x1, [fp, #-0x70]
    // 0x9085a0: StoreField: r1->field_7 = r0
    //     0x9085a0: stur            x0, [x1, #7]
    // 0x9085a4: ldur            x0, [fp, #-0x60]
    // 0x9085a8: StoreField: r1->field_f = r0
    //     0x9085a8: stur            w0, [x1, #0xf]
    // 0x9085ac: ldur            x0, [fp, #-0x58]
    // 0x9085b0: StoreField: r1->field_13 = r0
    //     0x9085b0: stur            x0, [x1, #0x13]
    // 0x9085b4: ldur            x0, [fp, #-0x40]
    // 0x9085b8: StoreField: r1->field_1b = r0
    //     0x9085b8: stur            w0, [x1, #0x1b]
    // 0x9085bc: ldur            x16, [fp, #-0x30]
    // 0x9085c0: str             x16, [SP]
    // 0x9085c4: r0 = _getHash()
    //     0x9085c4: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0x9085c8: r5 = LoadInt32Instr(r0)
    //     0x9085c8: sbfx            x5, x0, #1, #0x1f
    // 0x9085cc: ldur            x1, [fp, #-0x28]
    // 0x9085d0: ldur            x2, [fp, #-0x30]
    // 0x9085d4: ldur            x3, [fp, #-0x70]
    // 0x9085d8: r0 = _set()
    //     0x9085d8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x9085dc: ldur            x1, [fp, #-0x48]
    // 0x9085e0: ldur            x0, [fp, #-8]
    // 0x9085e4: ldur            x3, [fp, #-0x50]
    // 0x9085e8: b               #0x908488
    // 0x9085ec: ldur            x2, [fp, #-0x18]
    // 0x9085f0: ldur            x1, [fp, #-0x10]
    // 0x9085f4: r1 = 1
    //     0x9085f4: movz            x1, #0x1
    // 0x9085f8: r0 = AllocateContext()
    //     0x9085f8: bl              #0xec126c  ; AllocateContextStub
    // 0x9085fc: mov             x1, x0
    // 0x908600: ldur            x0, [fp, #-0x10]
    // 0x908604: StoreField: r1->field_f = r0
    //     0x908604: stur            w0, [x1, #0xf]
    // 0x908608: mov             x2, x1
    // 0x90860c: r1 = Function '<anonymous closure>': static.
    //     0x90860c: add             x1, PP, #0x31, lsl #12  ; [pp+0x31a10] AnonymousClosure: static (0x90ac14), of [package:waris/src/heirs.dart] 
    //     0x908610: ldr             x1, [x1, #0xa10]
    // 0x908614: r0 = AllocateClosure()
    //     0x908614: bl              #0xec1630  ; AllocateClosureStub
    // 0x908618: r16 = <int>
    //     0x908618: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x90861c: r30 = const [Instance of 'Heir', Instance of 'Heir']
    //     0x90861c: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3c598] List<Heir>(2)
    //     0x908620: ldr             lr, [lr, #0x598]
    // 0x908624: stp             lr, x16, [SP, #8]
    // 0x908628: str             x0, [SP]
    // 0x90862c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x90862c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x908630: r0 = map()
    //     0x908630: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x908634: mov             x1, x0
    // 0x908638: r0 = IterableIntegerExtension.sum()
    //     0x908638: bl              #0x909eb0  ; [package:collection/src/iterable_extensions.dart] ::IterableIntegerExtension.sum
    // 0x90863c: mov             x2, x0
    // 0x908640: r0 = BoxInt64Instr(r2)
    //     0x908640: sbfiz           x0, x2, #1, #0x1f
    //     0x908644: cmp             x2, x0, asr #1
    //     0x908648: b.eq            #0x908654
    //     0x90864c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x908650: stur            x2, [x0, #7]
    // 0x908654: ldur            x3, [fp, #-0x18]
    // 0x908658: StoreField: r3->field_27 = r0
    //     0x908658: stur            w0, [x3, #0x27]
    //     0x90865c: tbz             w0, #0, #0x908678
    //     0x908660: ldurb           w16, [x3, #-1]
    //     0x908664: ldurb           w17, [x0, #-1]
    //     0x908668: and             x16, x17, x16, lsr #2
    //     0x90866c: tst             x16, HEAP, lsr #32
    //     0x908670: b.eq            #0x908678
    //     0x908674: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x908678: LoadField: r0 = r3->field_23
    //     0x908678: ldur            w0, [x3, #0x23]
    // 0x90867c: DecompressPointer r0
    //     0x90867c: add             x0, x0, HEAP, lsl #32
    // 0x908680: r1 = LoadInt32Instr(r0)
    //     0x908680: sbfx            x1, x0, #1, #0x1f
    //     0x908684: tbz             w0, #0, #0x90868c
    //     0x908688: ldur            x1, [x0, #7]
    // 0x90868c: cbz             x2, #0x909280
    // 0x908690: sdiv            x4, x1, x2
    // 0x908694: msub            x0, x4, x2, x1
    // 0x908698: cmp             x0, xzr
    // 0x90869c: b.lt            #0x90929c
    // 0x9086a0: cmp             x0, #0
    // 0x9086a4: b.le            #0x9086e4
    // 0x9086a8: ldur            x0, [fp, #-8]
    // 0x9086ac: r4 = false
    //     0x9086ac: add             x4, NULL, #0x30  ; false
    // 0x9086b0: LoadField: r1 = r0->field_1f
    //     0x9086b0: ldur            x1, [x0, #0x1f]
    // 0x9086b4: mul             x5, x1, x2
    // 0x9086b8: StoreField: r0->field_1f = r5
    //     0x9086b8: stur            x5, [x0, #0x1f]
    // 0x9086bc: StoreField: r0->field_27 = rZR
    //     0x9086bc: stur            xzr, [x0, #0x27]
    // 0x9086c0: StoreField: r3->field_1f = r4
    //     0x9086c0: stur            w4, [x3, #0x1f]
    // 0x9086c4: StoreField: r3->field_23 = rZR
    //     0x9086c4: stur            wzr, [x3, #0x23]
    // 0x9086c8: mov             x2, x3
    // 0x9086cc: r1 = Function '<anonymous closure>':.
    //     0x9086cc: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5d0] AnonymousClosure: (0x90a908), in [package:waris/src/waris.dart] Waris::_calculateSpecialCases (0x907f78)
    //     0x9086d0: ldr             x1, [x1, #0x5d0]
    // 0x9086d4: r0 = AllocateClosure()
    //     0x9086d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9086d8: ldur            x1, [fp, #-0x38]
    // 0x9086dc: mov             x2, x0
    // 0x9086e0: r0 = forEach()
    //     0x9086e0: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x9086e4: ldur            x1, [fp, #-0x20]
    // 0x9086e8: r0 = isMusytarokah()
    //     0x9086e8: bl              #0x909cac  ; [package:waris/src/special_case.dart] SpecialCase::isMusytarokah
    // 0x9086ec: tbnz            w0, #4, #0x908bf8
    // 0x9086f0: ldur            x1, [fp, #-8]
    // 0x9086f4: r2 = Instance_Heir
    //     0x9086f4: add             x2, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0x9086f8: ldr             x2, [x2, #0x90]
    // 0x9086fc: r0 = get()
    //     0x9086fc: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x908700: LoadField: r1 = r0->field_13
    //     0x908700: ldur            x1, [x0, #0x13]
    // 0x908704: cbnz            x1, #0x908bf8
    // 0x908708: ldur            x3, [fp, #-8]
    // 0x90870c: ldur            x0, [fp, #-0x18]
    // 0x908710: ldur            x1, [fp, #-0x10]
    // 0x908714: r2 = const [Instance of 'Heir', Instance of 'Heir', Instance of 'Heir']
    //     0x908714: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c5d8] List<Heir>(3)
    //     0x908718: ldr             x2, [x2, #0x5d8]
    // 0x90871c: r0 = HeirsExt.search()
    //     0x90871c: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x908720: mov             x1, x0
    // 0x908724: ldur            x2, [fp, #-0x18]
    // 0x908728: stur            x1, [fp, #-0x30]
    // 0x90872c: StoreField: r2->field_13 = r0
    //     0x90872c: stur            w0, [x2, #0x13]
    //     0x908730: ldurb           w16, [x2, #-1]
    //     0x908734: ldurb           w17, [x0, #-1]
    //     0x908738: and             x16, x17, x16, lsr #2
    //     0x90873c: tst             x16, HEAP, lsr #32
    //     0x908740: b.eq            #0x908748
    //     0x908744: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x908748: LoadField: r0 = r1->field_b
    //     0x908748: ldur            w0, [x1, #0xb]
    // 0x90874c: r3 = LoadInt32Instr(r0)
    //     0x90874c: sbfx            x3, x0, #1, #0x1f
    // 0x908750: ldur            x0, [fp, #-8]
    // 0x908754: stur            x3, [fp, #-0x50]
    // 0x908758: LoadField: r4 = r0->field_f
    //     0x908758: ldur            w4, [x0, #0xf]
    // 0x90875c: DecompressPointer r4
    //     0x90875c: add             x4, x4, HEAP, lsl #32
    // 0x908760: stur            x4, [fp, #-0x28]
    // 0x908764: r5 = 0
    //     0x908764: movz            x5, #0
    // 0x908768: CheckStackOverflow
    //     0x908768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90876c: cmp             SP, x16
    //     0x908770: b.ls            #0x9092b0
    // 0x908774: LoadField: r6 = r1->field_b
    //     0x908774: ldur            w6, [x1, #0xb]
    // 0x908778: r7 = LoadInt32Instr(r6)
    //     0x908778: sbfx            x7, x6, #1, #0x1f
    // 0x90877c: cmp             x3, x7
    // 0x908780: b.ne            #0x90921c
    // 0x908784: cmp             x5, x7
    // 0x908788: b.ge            #0x9087e8
    // 0x90878c: LoadField: r6 = r1->field_f
    //     0x90878c: ldur            w6, [x1, #0xf]
    // 0x908790: DecompressPointer r6
    //     0x908790: add             x6, x6, HEAP, lsl #32
    // 0x908794: ArrayLoad: r7 = r6[r5]  ; Unknown_4
    //     0x908794: add             x16, x6, x5, lsl #2
    //     0x908798: ldur            w7, [x16, #0xf]
    // 0x90879c: DecompressPointer r7
    //     0x90879c: add             x7, x7, HEAP, lsl #32
    // 0x9087a0: stur            x7, [fp, #-0x20]
    // 0x9087a4: add             x6, x5, #1
    // 0x9087a8: stur            x6, [fp, #-0x48]
    // 0x9087ac: str             x7, [SP]
    // 0x9087b0: r0 = _getHash()
    //     0x9087b0: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0x9087b4: r5 = LoadInt32Instr(r0)
    //     0x9087b4: sbfx            x5, x0, #1, #0x1f
    // 0x9087b8: ldur            x1, [fp, #-0x28]
    // 0x9087bc: ldur            x2, [fp, #-0x20]
    // 0x9087c0: r3 = Instance__$Furudh
    //     0x9087c0: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c5a0] Obj!_$Furudh@e0bde1
    //     0x9087c4: ldr             x3, [x3, #0x5a0]
    // 0x9087c8: r0 = _set()
    //     0x9087c8: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x9087cc: ldur            x5, [fp, #-0x48]
    // 0x9087d0: ldur            x0, [fp, #-8]
    // 0x9087d4: ldur            x2, [fp, #-0x18]
    // 0x9087d8: ldur            x1, [fp, #-0x30]
    // 0x9087dc: ldur            x4, [fp, #-0x28]
    // 0x9087e0: ldur            x3, [fp, #-0x50]
    // 0x9087e4: b               #0x908768
    // 0x9087e8: mov             x3, x0
    // 0x9087ec: mov             x0, x2
    // 0x9087f0: ldur            x5, [fp, #-0x10]
    // 0x9087f4: LoadField: r6 = r3->field_13
    //     0x9087f4: ldur            w6, [x3, #0x13]
    // 0x9087f8: DecompressPointer r6
    //     0x9087f8: add             x6, x6, HEAP, lsl #32
    // 0x9087fc: mov             x2, x0
    // 0x908800: stur            x6, [fp, #-0x20]
    // 0x908804: r1 = Function '<anonymous closure>':.
    //     0x908804: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5e0] AnonymousClosure: (0x90a8c0), in [package:waris/src/waris.dart] Waris::_calculateSpecialCases (0x907f78)
    //     0x908808: ldr             x1, [x1, #0x5e0]
    // 0x90880c: r0 = AllocateClosure()
    //     0x90880c: bl              #0xec1630  ; AllocateClosureStub
    // 0x908810: ldur            x1, [fp, #-0x20]
    // 0x908814: mov             x2, x0
    // 0x908818: r0 = removeWhere()
    //     0x908818: bl              #0x9098f4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::removeWhere
    // 0x90881c: ldur            x0, [fp, #-0x28]
    // 0x908820: LoadField: r2 = r0->field_7
    //     0x908820: ldur            w2, [x0, #7]
    // 0x908824: DecompressPointer r2
    //     0x908824: add             x2, x2, HEAP, lsl #32
    // 0x908828: r1 = Null
    //     0x908828: mov             x1, NULL
    // 0x90882c: r3 = <X1>
    //     0x90882c: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x908830: r0 = Null
    //     0x908830: mov             x0, NULL
    // 0x908834: cmp             x2, x0
    // 0x908838: b.eq            #0x908848
    // 0x90883c: r30 = InstantiateTypeArgumentsStub
    //     0x90883c: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x908840: LoadField: r30 = r30->field_7
    //     0x908840: ldur            lr, [lr, #7]
    // 0x908844: blr             lr
    // 0x908848: mov             x1, x0
    // 0x90884c: r0 = _CompactIterable()
    //     0x90884c: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x908850: mov             x3, x0
    // 0x908854: ldur            x0, [fp, #-0x28]
    // 0x908858: stur            x3, [fp, #-0x20]
    // 0x90885c: StoreField: r3->field_b = r0
    //     0x90885c: stur            w0, [x3, #0xb]
    // 0x908860: r0 = -1
    //     0x908860: movn            x0, #0
    // 0x908864: StoreField: r3->field_f = r0
    //     0x908864: stur            x0, [x3, #0xf]
    // 0x908868: r4 = 2
    //     0x908868: movz            x4, #0x2
    // 0x90886c: ArrayStore: r3[0] = r4  ; List_8
    //     0x90886c: stur            x4, [x3, #0x17]
    // 0x908870: r1 = Function '<anonymous closure>':.
    //     0x908870: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5e8] AnonymousClosure: (0x90a898), in [package:waris/src/waris.dart] Waris::_calculateSpecialCases (0x907f78)
    //     0x908874: ldr             x1, [x1, #0x5e8]
    // 0x908878: r2 = Null
    //     0x908878: mov             x2, NULL
    // 0x90887c: r0 = AllocateClosure()
    //     0x90887c: bl              #0xec1630  ; AllocateClosureStub
    // 0x908880: r16 = <Fraction?>
    //     0x908880: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c5b0] TypeArguments: <Fraction?>
    //     0x908884: ldr             x16, [x16, #0x5b0]
    // 0x908888: ldur            lr, [fp, #-0x20]
    // 0x90888c: stp             lr, x16, [SP, #8]
    // 0x908890: str             x0, [SP]
    // 0x908894: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x908894: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x908898: r0 = map()
    //     0x908898: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0x90889c: r16 = <Fraction>
    //     0x90889c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c5b8] TypeArguments: <Fraction>
    //     0x9088a0: ldr             x16, [x16, #0x5b8]
    // 0x9088a4: stp             x0, x16, [SP]
    // 0x9088a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9088a8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9088ac: r0 = IterableNullableExtension.whereNotNull()
    //     0x9088ac: bl              #0x90a258  ; [package:collection/src/iterable_extensions.dart] ::IterableNullableExtension.whereNotNull
    // 0x9088b0: r1 = Null
    //     0x9088b0: mov             x1, NULL
    // 0x9088b4: r2 = 2
    //     0x9088b4: movz            x2, #0x2
    // 0x9088b8: stur            x0, [fp, #-0x20]
    // 0x9088bc: r0 = AllocateArray()
    //     0x9088bc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x9088c0: stur            x0, [fp, #-0x28]
    // 0x9088c4: r16 = Instance_Fraction
    //     0x9088c4: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c5c0] Obj!Fraction@e0bc41
    //     0x9088c8: ldr             x16, [x16, #0x5c0]
    // 0x9088cc: StoreField: r0->field_f = r16
    //     0x9088cc: stur            w16, [x0, #0xf]
    // 0x9088d0: r1 = <Fraction>
    //     0x9088d0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5b8] TypeArguments: <Fraction>
    //     0x9088d4: ldr             x1, [x1, #0x5b8]
    // 0x9088d8: r0 = AllocateGrowableArray()
    //     0x9088d8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x9088dc: mov             x1, x0
    // 0x9088e0: ldur            x0, [fp, #-0x28]
    // 0x9088e4: StoreField: r1->field_f = r0
    //     0x9088e4: stur            w0, [x1, #0xf]
    // 0x9088e8: r0 = 2
    //     0x9088e8: movz            x0, #0x2
    // 0x9088ec: StoreField: r1->field_b = r0
    //     0x9088ec: stur            w0, [x1, #0xb]
    // 0x9088f0: mov             x2, x1
    // 0x9088f4: ldur            x1, [fp, #-0x20]
    // 0x9088f8: r0 = followedBy()
    //     0x9088f8: bl              #0x7e6360  ; [dart:core] Iterable::followedBy
    // 0x9088fc: mov             x1, x0
    // 0x908900: r0 = FractionIterableExt.lcm()
    //     0x908900: bl              #0x90a054  ; [package:waris/src/utils.dart] ::FractionIterableExt.lcm
    // 0x908904: mov             x1, x0
    // 0x908908: ldur            x0, [fp, #-8]
    // 0x90890c: StoreField: r0->field_1f = r1
    //     0x90890c: stur            x1, [x0, #0x1f]
    // 0x908910: StoreField: r0->field_27 = rZR
    //     0x908910: stur            xzr, [x0, #0x27]
    // 0x908914: ldur            x3, [fp, #-0x18]
    // 0x908918: r4 = false
    //     0x908918: add             x4, NULL, #0x30  ; false
    // 0x90891c: ArrayStore: r3[0] = r4  ; List_4
    //     0x90891c: stur            w4, [x3, #0x17]
    // 0x908920: StoreField: r3->field_1b = rZR
    //     0x908920: stur            wzr, [x3, #0x1b]
    // 0x908924: LoadField: r1 = r3->field_f
    //     0x908924: ldur            w1, [x3, #0xf]
    // 0x908928: DecompressPointer r1
    //     0x908928: add             x1, x1, HEAP, lsl #32
    // 0x90892c: LoadField: r5 = r1->field_f
    //     0x90892c: ldur            w5, [x1, #0xf]
    // 0x908930: DecompressPointer r5
    //     0x908930: add             x5, x5, HEAP, lsl #32
    // 0x908934: mov             x2, x3
    // 0x908938: stur            x5, [fp, #-0x20]
    // 0x90893c: r1 = Function '<anonymous closure>':.
    //     0x90893c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5f0] AnonymousClosure: (0x90a6fc), of [package:waris/src/waris.dart] Waris
    //     0x908940: ldr             x1, [x1, #0x5f0]
    // 0x908944: r0 = AllocateClosure()
    //     0x908944: bl              #0xec1630  ; AllocateClosureStub
    // 0x908948: ldur            x1, [fp, #-0x20]
    // 0x90894c: mov             x2, x0
    // 0x908950: r0 = forEach()
    //     0x908950: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x908954: r1 = 1
    //     0x908954: movz            x1, #0x1
    // 0x908958: r0 = AllocateContext()
    //     0x908958: bl              #0xec126c  ; AllocateContextStub
    // 0x90895c: mov             x1, x0
    // 0x908960: ldur            x0, [fp, #-0x10]
    // 0x908964: StoreField: r1->field_f = r0
    //     0x908964: stur            w0, [x1, #0xf]
    // 0x908968: mov             x2, x1
    // 0x90896c: r1 = Function '<anonymous closure>': static.
    //     0x90896c: add             x1, PP, #0x31, lsl #12  ; [pp+0x31a10] AnonymousClosure: static (0x90ac14), of [package:waris/src/heirs.dart] 
    //     0x908970: ldr             x1, [x1, #0xa10]
    // 0x908974: r0 = AllocateClosure()
    //     0x908974: bl              #0xec1630  ; AllocateClosureStub
    // 0x908978: r16 = <int>
    //     0x908978: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0x90897c: ldur            lr, [fp, #-0x30]
    // 0x908980: stp             lr, x16, [SP, #8]
    // 0x908984: str             x0, [SP]
    // 0x908988: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x908988: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90898c: r0 = map()
    //     0x90898c: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x908990: mov             x1, x0
    // 0x908994: r0 = IterableIntegerExtension.sum()
    //     0x908994: bl              #0x909eb0  ; [package:collection/src/iterable_extensions.dart] ::IterableIntegerExtension.sum
    // 0x908998: mov             x3, x0
    // 0x90899c: ldur            x0, [fp, #-0x18]
    // 0x9089a0: stur            x3, [fp, #-0x48]
    // 0x9089a4: LoadField: r1 = r0->field_1b
    //     0x9089a4: ldur            w1, [x0, #0x1b]
    // 0x9089a8: DecompressPointer r1
    //     0x9089a8: add             x1, x1, HEAP, lsl #32
    // 0x9089ac: r2 = LoadInt32Instr(r1)
    //     0x9089ac: sbfx            x2, x1, #1, #0x1f
    //     0x9089b0: tbz             w1, #0, #0x9089b8
    //     0x9089b4: ldur            x2, [x1, #7]
    // 0x9089b8: cbz             x3, #0x9092b8
    // 0x9089bc: sdiv            x4, x2, x3
    // 0x9089c0: msub            x1, x4, x3, x2
    // 0x9089c4: cmp             x1, xzr
    // 0x9089c8: b.lt            #0x9092d4
    // 0x9089cc: cmp             x1, #0
    // 0x9089d0: b.le            #0x908a20
    // 0x9089d4: ldur            x4, [fp, #-8]
    // 0x9089d8: r1 = false
    //     0x9089d8: add             x1, NULL, #0x30  ; false
    // 0x9089dc: LoadField: r2 = r4->field_1f
    //     0x9089dc: ldur            x2, [x4, #0x1f]
    // 0x9089e0: mul             x5, x2, x3
    // 0x9089e4: StoreField: r4->field_1f = r5
    //     0x9089e4: stur            x5, [x4, #0x1f]
    // 0x9089e8: StoreField: r4->field_27 = rZR
    //     0x9089e8: stur            xzr, [x4, #0x27]
    // 0x9089ec: ArrayStore: r0[0] = r1  ; List_4
    //     0x9089ec: stur            w1, [x0, #0x17]
    // 0x9089f0: LoadField: r1 = r0->field_f
    //     0x9089f0: ldur            w1, [x0, #0xf]
    // 0x9089f4: DecompressPointer r1
    //     0x9089f4: add             x1, x1, HEAP, lsl #32
    // 0x9089f8: LoadField: r5 = r1->field_f
    //     0x9089f8: ldur            w5, [x1, #0xf]
    // 0x9089fc: DecompressPointer r5
    //     0x9089fc: add             x5, x5, HEAP, lsl #32
    // 0x908a00: mov             x2, x0
    // 0x908a04: stur            x5, [fp, #-0x20]
    // 0x908a08: r1 = Function '<anonymous closure>':.
    //     0x908a08: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c5f0] AnonymousClosure: (0x90a6fc), of [package:waris/src/waris.dart] Waris
    //     0x908a0c: ldr             x1, [x1, #0x5f0]
    // 0x908a10: r0 = AllocateClosure()
    //     0x908a10: bl              #0xec1630  ; AllocateClosureStub
    // 0x908a14: ldur            x1, [fp, #-0x20]
    // 0x908a18: mov             x2, x0
    // 0x908a1c: r0 = forEach()
    //     0x908a1c: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x908a20: ldur            x3, [fp, #-8]
    // 0x908a24: ldur            x0, [fp, #-0x18]
    // 0x908a28: ldur            x4, [fp, #-0x30]
    // 0x908a2c: ldur            x1, [fp, #-0x48]
    // 0x908a30: LoadField: r2 = r0->field_1b
    //     0x908a30: ldur            w2, [x0, #0x1b]
    // 0x908a34: DecompressPointer r2
    //     0x908a34: add             x2, x2, HEAP, lsl #32
    // 0x908a38: r5 = LoadInt32Instr(r2)
    //     0x908a38: sbfx            x5, x2, #1, #0x1f
    //     0x908a3c: tbz             w2, #0, #0x908a44
    //     0x908a40: ldur            x5, [x2, #7]
    // 0x908a44: cbz             x1, #0x9092e8
    // 0x908a48: sdiv            x6, x5, x1
    // 0x908a4c: stur            x6, [fp, #-0x58]
    // 0x908a50: LoadField: r1 = r4->field_b
    //     0x908a50: ldur            w1, [x4, #0xb]
    // 0x908a54: r5 = LoadInt32Instr(r1)
    //     0x908a54: sbfx            x5, x1, #1, #0x1f
    // 0x908a58: stur            x5, [fp, #-0x50]
    // 0x908a5c: LoadField: r7 = r3->field_b
    //     0x908a5c: ldur            w7, [x3, #0xb]
    // 0x908a60: DecompressPointer r7
    //     0x908a60: add             x7, x7, HEAP, lsl #32
    // 0x908a64: stur            x7, [fp, #-0x28]
    // 0x908a68: r1 = 0
    //     0x908a68: movz            x1, #0
    // 0x908a6c: ldur            x8, [fp, #-0x10]
    // 0x908a70: CheckStackOverflow
    //     0x908a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x908a74: cmp             SP, x16
    //     0x908a78: b.ls            #0x909308
    // 0x908a7c: LoadField: r2 = r4->field_b
    //     0x908a7c: ldur            w2, [x4, #0xb]
    // 0x908a80: r9 = LoadInt32Instr(r2)
    //     0x908a80: sbfx            x9, x2, #1, #0x1f
    // 0x908a84: cmp             x5, x9
    // 0x908a88: b.ne            #0x9091fc
    // 0x908a8c: cmp             x1, x9
    // 0x908a90: b.ge            #0x908bf8
    // 0x908a94: LoadField: r2 = r4->field_f
    //     0x908a94: ldur            w2, [x4, #0xf]
    // 0x908a98: DecompressPointer r2
    //     0x908a98: add             x2, x2, HEAP, lsl #32
    // 0x908a9c: ArrayLoad: r9 = r2[r1]  ; Unknown_4
    //     0x908a9c: add             x16, x2, x1, lsl #2
    //     0x908aa0: ldur            w9, [x16, #0xf]
    // 0x908aa4: DecompressPointer r9
    //     0x908aa4: add             x9, x9, HEAP, lsl #32
    // 0x908aa8: stur            x9, [fp, #-0x20]
    // 0x908aac: add             x10, x1, #1
    // 0x908ab0: mov             x1, x7
    // 0x908ab4: mov             x2, x9
    // 0x908ab8: stur            x10, [fp, #-0x48]
    // 0x908abc: r0 = _getValueOrData()
    //     0x908abc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x908ac0: ldur            x3, [fp, #-0x28]
    // 0x908ac4: LoadField: r1 = r3->field_f
    //     0x908ac4: ldur            w1, [x3, #0xf]
    // 0x908ac8: DecompressPointer r1
    //     0x908ac8: add             x1, x1, HEAP, lsl #32
    // 0x908acc: cmp             w1, w0
    // 0x908ad0: b.ne            #0x908adc
    // 0x908ad4: r5 = Null
    //     0x908ad4: mov             x5, NULL
    // 0x908ad8: b               #0x908ae0
    // 0x908adc: mov             x5, x0
    // 0x908ae0: ldur            x4, [fp, #-0x10]
    // 0x908ae4: stur            x5, [fp, #-0x38]
    // 0x908ae8: cmp             w5, NULL
    // 0x908aec: b.eq            #0x909310
    // 0x908af0: r0 = LoadClassIdInstr(r4)
    //     0x908af0: ldur            x0, [x4, #-1]
    //     0x908af4: ubfx            x0, x0, #0xc, #0x14
    // 0x908af8: mov             x1, x4
    // 0x908afc: ldur            x2, [fp, #-0x20]
    // 0x908b00: r0 = GDT[cid_x0 + 0x55f]()
    //     0x908b00: add             lr, x0, #0x55f
    //     0x908b04: ldr             lr, [x21, lr, lsl #3]
    //     0x908b08: blr             lr
    // 0x908b0c: tbnz            w0, #4, #0x908b58
    // 0x908b10: ldur            x3, [fp, #-0x10]
    // 0x908b14: r0 = LoadClassIdInstr(r3)
    //     0x908b14: ldur            x0, [x3, #-1]
    //     0x908b18: ubfx            x0, x0, #0xc, #0x14
    // 0x908b1c: mov             x1, x3
    // 0x908b20: ldur            x2, [fp, #-0x20]
    // 0x908b24: r0 = GDT[cid_x0 + -0x114]()
    //     0x908b24: sub             lr, x0, #0x114
    //     0x908b28: ldr             lr, [x21, lr, lsl #3]
    //     0x908b2c: blr             lr
    // 0x908b30: cmp             w0, NULL
    // 0x908b34: b.ne            #0x908b40
    // 0x908b38: r0 = 0
    //     0x908b38: movz            x0, #0
    // 0x908b3c: b               #0x908b50
    // 0x908b40: r2 = LoadInt32Instr(r0)
    //     0x908b40: sbfx            x2, x0, #1, #0x1f
    //     0x908b44: tbz             w0, #0, #0x908b4c
    //     0x908b48: ldur            x2, [x0, #7]
    // 0x908b4c: mov             x0, x2
    // 0x908b50: mov             x2, x0
    // 0x908b54: b               #0x908b5c
    // 0x908b58: r2 = 0
    //     0x908b58: movz            x2, #0
    // 0x908b5c: ldur            x1, [fp, #-0x58]
    // 0x908b60: ldur            x0, [fp, #-0x38]
    // 0x908b64: mul             x3, x1, x2
    // 0x908b68: stur            x3, [fp, #-0x78]
    // 0x908b6c: LoadField: r2 = r0->field_7
    //     0x908b6c: ldur            x2, [x0, #7]
    // 0x908b70: stur            x2, [fp, #-0x68]
    // 0x908b74: LoadField: r4 = r0->field_f
    //     0x908b74: ldur            w4, [x0, #0xf]
    // 0x908b78: DecompressPointer r4
    //     0x908b78: add             x4, x4, HEAP, lsl #32
    // 0x908b7c: stur            x4, [fp, #-0x60]
    // 0x908b80: LoadField: r5 = r0->field_1b
    //     0x908b80: ldur            w5, [x0, #0x1b]
    // 0x908b84: DecompressPointer r5
    //     0x908b84: add             x5, x5, HEAP, lsl #32
    // 0x908b88: stur            x5, [fp, #-0x40]
    // 0x908b8c: r0 = Result()
    //     0x908b8c: bl              #0x907e84  ; AllocateResultStub -> Result (size=0x20)
    // 0x908b90: mov             x1, x0
    // 0x908b94: ldur            x0, [fp, #-0x68]
    // 0x908b98: stur            x1, [fp, #-0x38]
    // 0x908b9c: StoreField: r1->field_7 = r0
    //     0x908b9c: stur            x0, [x1, #7]
    // 0x908ba0: ldur            x0, [fp, #-0x60]
    // 0x908ba4: StoreField: r1->field_f = r0
    //     0x908ba4: stur            w0, [x1, #0xf]
    // 0x908ba8: ldur            x0, [fp, #-0x78]
    // 0x908bac: StoreField: r1->field_13 = r0
    //     0x908bac: stur            x0, [x1, #0x13]
    // 0x908bb0: ldur            x0, [fp, #-0x40]
    // 0x908bb4: StoreField: r1->field_1b = r0
    //     0x908bb4: stur            w0, [x1, #0x1b]
    // 0x908bb8: ldur            x16, [fp, #-0x20]
    // 0x908bbc: str             x16, [SP]
    // 0x908bc0: r0 = _getHash()
    //     0x908bc0: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0x908bc4: r5 = LoadInt32Instr(r0)
    //     0x908bc4: sbfx            x5, x0, #1, #0x1f
    // 0x908bc8: ldur            x1, [fp, #-0x28]
    // 0x908bcc: ldur            x2, [fp, #-0x20]
    // 0x908bd0: ldur            x3, [fp, #-0x38]
    // 0x908bd4: r0 = _set()
    //     0x908bd4: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x908bd8: ldur            x1, [fp, #-0x48]
    // 0x908bdc: ldur            x3, [fp, #-8]
    // 0x908be0: ldur            x0, [fp, #-0x18]
    // 0x908be4: ldur            x4, [fp, #-0x30]
    // 0x908be8: ldur            x6, [fp, #-0x58]
    // 0x908bec: ldur            x7, [fp, #-0x28]
    // 0x908bf0: ldur            x5, [fp, #-0x50]
    // 0x908bf4: b               #0x908a6c
    // 0x908bf8: ldur            x0, [fp, #-8]
    // 0x908bfc: LoadField: r1 = r0->field_27
    //     0x908bfc: ldur            x1, [x0, #0x27]
    // 0x908c00: LoadField: r2 = r0->field_1f
    //     0x908c00: ldur            x2, [x0, #0x1f]
    // 0x908c04: cmp             x1, x2
    // 0x908c08: b.ne            #0x908c1c
    // 0x908c0c: r0 = Null
    //     0x908c0c: mov             x0, NULL
    // 0x908c10: LeaveFrame
    //     0x908c10: mov             SP, fp
    //     0x908c14: ldp             fp, lr, [SP], #0x10
    // 0x908c18: ret
    //     0x908c18: ret             
    // 0x908c1c: cmp             x1, x2
    // 0x908c20: b.le            #0x908c40
    // 0x908c24: r3 = true
    //     0x908c24: add             x3, NULL, #0x20  ; true
    // 0x908c28: StoreField: r0->field_1f = r1
    //     0x908c28: stur            x1, [x0, #0x1f]
    // 0x908c2c: StoreField: r0->field_33 = r3
    //     0x908c2c: stur            w3, [x0, #0x33]
    // 0x908c30: r0 = Null
    //     0x908c30: mov             x0, NULL
    // 0x908c34: LeaveFrame
    //     0x908c34: mov             SP, fp
    //     0x908c38: ldp             fp, lr, [SP], #0x10
    // 0x908c3c: ret
    //     0x908c3c: ret             
    // 0x908c40: r3 = true
    //     0x908c40: add             x3, NULL, #0x20  ; true
    // 0x908c44: ldur            x1, [fp, #-0x10]
    // 0x908c48: r2 = const [Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir', Instance of 'Heir']
    //     0x908c48: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c5f8] List<Heir>(8)
    //     0x908c4c: ldr             x2, [x2, #0x5f8]
    // 0x908c50: r0 = HeirsExt.search()
    //     0x908c50: bl              #0x909c2c  ; [package:waris/src/heirs.dart] ::HeirsExt.search
    // 0x908c54: stur            x0, [fp, #-0x38]
    // 0x908c58: LoadField: r1 = r0->field_b
    //     0x908c58: ldur            w1, [x0, #0xb]
    // 0x908c5c: cbnz            w1, #0x908cfc
    // 0x908c60: ldur            x0, [fp, #-8]
    // 0x908c64: LoadField: r3 = r0->field_b
    //     0x908c64: ldur            w3, [x0, #0xb]
    // 0x908c68: DecompressPointer r3
    //     0x908c68: add             x3, x3, HEAP, lsl #32
    // 0x908c6c: stur            x3, [fp, #-0x20]
    // 0x908c70: r1 = Null
    //     0x908c70: mov             x1, NULL
    // 0x908c74: r2 = 4
    //     0x908c74: movz            x2, #0x4
    // 0x908c78: r0 = AllocateArray()
    //     0x908c78: bl              #0xec22fc  ; AllocateArrayStub
    // 0x908c7c: stur            x0, [fp, #-0x28]
    // 0x908c80: r16 = Instance_Heir
    //     0x908c80: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c600] Obj!Heir@e2ddb1
    //     0x908c84: ldr             x16, [x16, #0x600]
    // 0x908c88: StoreField: r0->field_f = r16
    //     0x908c88: stur            w16, [x0, #0xf]
    // 0x908c8c: ldur            x3, [fp, #-8]
    // 0x908c90: LoadField: r1 = r3->field_1f
    //     0x908c90: ldur            x1, [x3, #0x1f]
    // 0x908c94: LoadField: r2 = r3->field_27
    //     0x908c94: ldur            x2, [x3, #0x27]
    // 0x908c98: sub             x3, x1, x2
    // 0x908c9c: stur            x3, [fp, #-0x48]
    // 0x908ca0: r0 = Result()
    //     0x908ca0: bl              #0x907e84  ; AllocateResultStub -> Result (size=0x20)
    // 0x908ca4: mov             x1, x0
    // 0x908ca8: r0 = 1
    //     0x908ca8: movz            x0, #0x1
    // 0x908cac: StoreField: r1->field_7 = r0
    //     0x908cac: stur            x0, [x1, #7]
    // 0x908cb0: r0 = Instance__$Ashobah
    //     0x908cb0: add             x0, PP, #0x31, lsl #12  ; [pp+0x31988] Obj!_$Ashobah@e0bdb1
    //     0x908cb4: ldr             x0, [x0, #0x988]
    // 0x908cb8: StoreField: r1->field_f = r0
    //     0x908cb8: stur            w0, [x1, #0xf]
    // 0x908cbc: ldur            x0, [fp, #-0x48]
    // 0x908cc0: StoreField: r1->field_13 = r0
    //     0x908cc0: stur            x0, [x1, #0x13]
    // 0x908cc4: StoreField: r1->field_1b = rZR
    //     0x908cc4: stur            wzr, [x1, #0x1b]
    // 0x908cc8: ldur            x0, [fp, #-0x28]
    // 0x908ccc: StoreField: r0->field_13 = r1
    //     0x908ccc: stur            w1, [x0, #0x13]
    // 0x908cd0: r16 = <Heir, Result>
    //     0x908cd0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x908cd4: ldr             x16, [x16, #0x438]
    // 0x908cd8: stp             x0, x16, [SP]
    // 0x908cdc: r0 = Map._fromLiteral()
    //     0x908cdc: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x908ce0: ldur            x1, [fp, #-0x20]
    // 0x908ce4: mov             x2, x0
    // 0x908ce8: r0 = addAll()
    //     0x908ce8: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x908cec: r0 = Null
    //     0x908cec: mov             x0, NULL
    // 0x908cf0: LeaveFrame
    //     0x908cf0: mov             SP, fp
    //     0x908cf4: ldp             fp, lr, [SP], #0x10
    // 0x908cf8: ret
    //     0x908cf8: ret             
    // 0x908cfc: ldur            x3, [fp, #-8]
    // 0x908d00: r1 = true
    //     0x908d00: add             x1, NULL, #0x20  ; true
    // 0x908d04: r4 = 4
    //     0x908d04: movz            x4, #0x4
    // 0x908d08: StoreField: r3->field_2f = r1
    //     0x908d08: stur            w1, [x3, #0x2f]
    // 0x908d0c: mov             x2, x4
    // 0x908d10: r1 = Null
    //     0x908d10: mov             x1, NULL
    // 0x908d14: r0 = AllocateArray()
    //     0x908d14: bl              #0xec22fc  ; AllocateArrayStub
    // 0x908d18: stur            x0, [fp, #-0x20]
    // 0x908d1c: r16 = Instance_Heir
    //     0x908d1c: add             x16, PP, #0x31, lsl #12  ; [pp+0x318c8] Obj!Heir@e2dd81
    //     0x908d20: ldr             x16, [x16, #0x8c8]
    // 0x908d24: StoreField: r0->field_f = r16
    //     0x908d24: stur            w16, [x0, #0xf]
    // 0x908d28: r16 = Instance_Heir
    //     0x908d28: add             x16, PP, #0x31, lsl #12  ; [pp+0x31950] Obj!Heir@e2da81
    //     0x908d2c: ldr             x16, [x16, #0x950]
    // 0x908d30: StoreField: r0->field_13 = r16
    //     0x908d30: stur            w16, [x0, #0x13]
    // 0x908d34: r1 = <Heir>
    //     0x908d34: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0x908d38: ldr             x1, [x1, #0xc0]
    // 0x908d3c: r0 = AllocateGrowableArray()
    //     0x908d3c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x908d40: mov             x1, x0
    // 0x908d44: ldur            x0, [fp, #-0x20]
    // 0x908d48: StoreField: r1->field_f = r0
    //     0x908d48: stur            w0, [x1, #0xf]
    // 0x908d4c: r0 = 4
    //     0x908d4c: movz            x0, #0x4
    // 0x908d50: StoreField: r1->field_b = r0
    //     0x908d50: stur            w0, [x1, #0xb]
    // 0x908d54: mov             x2, x1
    // 0x908d58: ldur            x1, [fp, #-0x10]
    // 0x908d5c: r0 = HeirsExt.notHasAnyOf()
    //     0x908d5c: bl              #0x909850  ; [package:waris/src/heirs.dart] ::HeirsExt.notHasAnyOf
    // 0x908d60: tbnz            w0, #4, #0x908d80
    // 0x908d64: ldur            x0, [fp, #-8]
    // 0x908d68: LoadField: r1 = r0->field_1f
    //     0x908d68: ldur            x1, [x0, #0x1f]
    // 0x908d6c: LoadField: r2 = r0->field_27
    //     0x908d6c: ldur            x2, [x0, #0x27]
    // 0x908d70: sub             x3, x1, x2
    // 0x908d74: sub             x2, x1, x3
    // 0x908d78: StoreField: r0->field_1f = r2
    //     0x908d78: stur            x2, [x0, #0x1f]
    // 0x908d7c: b               #0x9091ec
    // 0x908d80: ldur            x0, [fp, #-8]
    // 0x908d84: ldur            x2, [fp, #-0x38]
    // 0x908d88: LoadField: r1 = r2->field_b
    //     0x908d88: ldur            w1, [x2, #0xb]
    // 0x908d8c: cmp             w1, #2
    // 0x908d90: b.ne            #0x908e90
    // 0x908d94: LoadField: r3 = r0->field_b
    //     0x908d94: ldur            w3, [x0, #0xb]
    // 0x908d98: DecompressPointer r3
    //     0x908d98: add             x3, x3, HEAP, lsl #32
    // 0x908d9c: mov             x1, x2
    // 0x908da0: stur            x3, [fp, #-0x10]
    // 0x908da4: r0 = first()
    //     0x908da4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x908da8: ldur            x1, [fp, #-0x38]
    // 0x908dac: stur            x0, [fp, #-0x20]
    // 0x908db0: r0 = first()
    //     0x908db0: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x908db4: ldur            x1, [fp, #-0x10]
    // 0x908db8: mov             x2, x0
    // 0x908dbc: r0 = _getValueOrData()
    //     0x908dbc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x908dc0: mov             x1, x0
    // 0x908dc4: ldur            x0, [fp, #-0x10]
    // 0x908dc8: LoadField: r2 = r0->field_f
    //     0x908dc8: ldur            w2, [x0, #0xf]
    // 0x908dcc: DecompressPointer r2
    //     0x908dcc: add             x2, x2, HEAP, lsl #32
    // 0x908dd0: cmp             w2, w1
    // 0x908dd4: b.ne            #0x908de0
    // 0x908dd8: r2 = Null
    //     0x908dd8: mov             x2, NULL
    // 0x908ddc: b               #0x908de4
    // 0x908de0: mov             x2, x1
    // 0x908de4: stur            x2, [fp, #-0x28]
    // 0x908de8: cmp             w2, NULL
    // 0x908dec: b.eq            #0x909314
    // 0x908df0: ldur            x1, [fp, #-0x38]
    // 0x908df4: r0 = first()
    //     0x908df4: bl              #0xa7a1f0  ; [dart:core] _GrowableList::first
    // 0x908df8: ldur            x1, [fp, #-0x10]
    // 0x908dfc: mov             x2, x0
    // 0x908e00: r0 = _getValueOrData()
    //     0x908e00: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x908e04: ldur            x2, [fp, #-0x10]
    // 0x908e08: LoadField: r1 = r2->field_f
    //     0x908e08: ldur            w1, [x2, #0xf]
    // 0x908e0c: DecompressPointer r1
    //     0x908e0c: add             x1, x1, HEAP, lsl #32
    // 0x908e10: cmp             w1, w0
    // 0x908e14: b.ne            #0x908e1c
    // 0x908e18: r0 = Null
    //     0x908e18: mov             x0, NULL
    // 0x908e1c: ldur            x3, [fp, #-8]
    // 0x908e20: cmp             w0, NULL
    // 0x908e24: b.eq            #0x909318
    // 0x908e28: LoadField: r1 = r0->field_13
    //     0x908e28: ldur            x1, [x0, #0x13]
    // 0x908e2c: LoadField: r0 = r3->field_1f
    //     0x908e2c: ldur            x0, [x3, #0x1f]
    // 0x908e30: LoadField: r4 = r3->field_27
    //     0x908e30: ldur            x4, [x3, #0x27]
    // 0x908e34: sub             x5, x0, x4
    // 0x908e38: add             x4, x1, x5
    // 0x908e3c: r0 = BoxInt64Instr(r4)
    //     0x908e3c: sbfiz           x0, x4, #1, #0x1f
    //     0x908e40: cmp             x4, x0, asr #1
    //     0x908e44: b.eq            #0x908e50
    //     0x908e48: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x908e4c: stur            x4, [x0, #7]
    // 0x908e50: str             x0, [SP]
    // 0x908e54: ldur            x1, [fp, #-0x28]
    // 0x908e58: r4 = const [0, 0x2, 0x1, 0x1, siham, 0x1, null]
    //     0x908e58: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c570] List(7) [0, 0x2, 0x1, 0x1, "siham", 0x1, Null]
    //     0x908e5c: ldr             x4, [x4, #0x570]
    // 0x908e60: r0 = copyWith()
    //     0x908e60: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x908e64: ldur            x1, [fp, #-0x10]
    // 0x908e68: ldur            x2, [fp, #-0x20]
    // 0x908e6c: mov             x3, x0
    // 0x908e70: r0 = []=()
    //     0x908e70: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x908e74: ldur            x0, [fp, #-8]
    // 0x908e78: LoadField: r1 = r0->field_27
    //     0x908e78: ldur            x1, [x0, #0x27]
    // 0x908e7c: LoadField: r2 = r0->field_1f
    //     0x908e7c: ldur            x2, [x0, #0x1f]
    // 0x908e80: sub             x3, x2, x1
    // 0x908e84: add             x2, x1, x3
    // 0x908e88: StoreField: r0->field_27 = r2
    //     0x908e88: stur            x2, [x0, #0x27]
    // 0x908e8c: b               #0x9091ec
    // 0x908e90: mov             x3, x2
    // 0x908e94: ldur            x2, [fp, #-0x18]
    // 0x908e98: r1 = Function '<anonymous closure>':.
    //     0x908e98: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c608] AnonymousClosure: (0x90a670), in [package:waris/src/waris.dart] Waris::_calculateSpecialCases (0x907f78)
    //     0x908e9c: ldr             x1, [x1, #0x608]
    // 0x908ea0: r0 = AllocateClosure()
    //     0x908ea0: bl              #0xec1630  ; AllocateClosureStub
    // 0x908ea4: r16 = <MapEntry<Heir, int>>
    //     0x908ea4: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c610] TypeArguments: <MapEntry<Heir, int>>
    //     0x908ea8: ldr             x16, [x16, #0x610]
    // 0x908eac: ldur            lr, [fp, #-0x38]
    // 0x908eb0: stp             lr, x16, [SP, #8]
    // 0x908eb4: str             x0, [SP]
    // 0x908eb8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x908eb8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x908ebc: r0 = map()
    //     0x908ebc: bl              #0x86991c  ; [dart:collection] ListBase::map
    // 0x908ec0: stur            x0, [fp, #-0x10]
    // 0x908ec4: r16 = <Heir, int>
    //     0x908ec4: add             x16, PP, #0x28, lsl #12  ; [pp+0x28060] TypeArguments: <Heir, int>
    //     0x908ec8: ldr             x16, [x16, #0x60]
    // 0x908ecc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x908ed0: stp             lr, x16, [SP]
    // 0x908ed4: r0 = Map._fromLiteral()
    //     0x908ed4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x908ed8: mov             x1, x0
    // 0x908edc: ldur            x2, [fp, #-0x10]
    // 0x908ee0: stur            x0, [fp, #-0x10]
    // 0x908ee4: r0 = addEntries()
    //     0x908ee4: bl              #0x765b84  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::addEntries
    // 0x908ee8: ldur            x0, [fp, #-0x10]
    // 0x908eec: LoadField: r2 = r0->field_7
    //     0x908eec: ldur            w2, [x0, #7]
    // 0x908ef0: DecompressPointer r2
    //     0x908ef0: add             x2, x2, HEAP, lsl #32
    // 0x908ef4: r1 = Null
    //     0x908ef4: mov             x1, NULL
    // 0x908ef8: r3 = <X1>
    //     0x908ef8: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x908efc: r0 = Null
    //     0x908efc: mov             x0, NULL
    // 0x908f00: cmp             x2, x0
    // 0x908f04: b.eq            #0x908f14
    // 0x908f08: r30 = InstantiateTypeArgumentsStub
    //     0x908f08: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x908f0c: LoadField: r30 = r30->field_7
    //     0x908f0c: ldur            lr, [lr, #7]
    // 0x908f10: blr             lr
    // 0x908f14: mov             x1, x0
    // 0x908f18: r0 = _CompactIterable()
    //     0x908f18: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x908f1c: mov             x1, x0
    // 0x908f20: ldur            x0, [fp, #-0x10]
    // 0x908f24: StoreField: r1->field_b = r0
    //     0x908f24: stur            w0, [x1, #0xb]
    // 0x908f28: r2 = -1
    //     0x908f28: movn            x2, #0
    // 0x908f2c: StoreField: r1->field_f = r2
    //     0x908f2c: stur            x2, [x1, #0xf]
    // 0x908f30: r2 = 2
    //     0x908f30: movz            x2, #0x2
    // 0x908f34: ArrayStore: r1[0] = r2  ; List_8
    //     0x908f34: stur            x2, [x1, #0x17]
    // 0x908f38: r0 = IntIterableExt.gcd()
    //     0x908f38: bl              #0x90943c  ; [package:waris/src/utils.dart] ::IntIterableExt.gcd
    // 0x908f3c: mov             x2, x0
    // 0x908f40: ldur            x0, [fp, #-8]
    // 0x908f44: stur            x2, [fp, #-0x48]
    // 0x908f48: LoadField: r1 = r0->field_1f
    //     0x908f48: ldur            x1, [x0, #0x1f]
    // 0x908f4c: mul             x3, x1, x2
    // 0x908f50: StoreField: r0->field_1f = r3
    //     0x908f50: stur            x3, [x0, #0x1f]
    // 0x908f54: StoreField: r0->field_27 = rZR
    //     0x908f54: stur            xzr, [x0, #0x27]
    // 0x908f58: mov             x1, x0
    // 0x908f5c: r0 = _calculateSihamForFurudh()
    //     0x908f5c: bl              #0x90b474  ; [package:waris/src/waris.dart] Waris::_calculateSihamForFurudh
    // 0x908f60: ldur            x1, [fp, #-8]
    // 0x908f64: r0 = _calculateSihamForAshobah()
    //     0x908f64: bl              #0x90ac74  ; [package:waris/src/waris.dart] Waris::_calculateSihamForAshobah
    // 0x908f68: ldur            x0, [fp, #-0x38]
    // 0x908f6c: LoadField: r1 = r0->field_b
    //     0x908f6c: ldur            w1, [x0, #0xb]
    // 0x908f70: r3 = LoadInt32Instr(r1)
    //     0x908f70: sbfx            x3, x1, #1, #0x1f
    // 0x908f74: ldur            x1, [fp, #-0x48]
    // 0x908f78: stur            x3, [fp, #-0x58]
    // 0x908f7c: scvtf           d0, x1
    // 0x908f80: ldur            x4, [fp, #-8]
    // 0x908f84: stur            d0, [fp, #-0x90]
    // 0x908f88: LoadField: r5 = r4->field_b
    //     0x908f88: ldur            w5, [x4, #0xb]
    // 0x908f8c: DecompressPointer r5
    //     0x908f8c: add             x5, x5, HEAP, lsl #32
    // 0x908f90: stur            x5, [fp, #-0x20]
    // 0x908f94: r7 = 0
    //     0x908f94: movz            x7, #0
    // 0x908f98: r1 = 0
    //     0x908f98: movz            x1, #0
    // 0x908f9c: ldur            x6, [fp, #-0x10]
    // 0x908fa0: stur            x7, [fp, #-0x50]
    // 0x908fa4: CheckStackOverflow
    //     0x908fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x908fa8: cmp             SP, x16
    //     0x908fac: b.ls            #0x90931c
    // 0x908fb0: LoadField: r2 = r0->field_b
    //     0x908fb0: ldur            w2, [x0, #0xb]
    // 0x908fb4: r8 = LoadInt32Instr(r2)
    //     0x908fb4: sbfx            x8, x2, #1, #0x1f
    // 0x908fb8: cmp             x3, x8
    // 0x908fbc: b.ne            #0x90923c
    // 0x908fc0: cmp             x1, x8
    // 0x908fc4: b.ge            #0x9091d8
    // 0x908fc8: LoadField: r2 = r0->field_f
    //     0x908fc8: ldur            w2, [x0, #0xf]
    // 0x908fcc: DecompressPointer r2
    //     0x908fcc: add             x2, x2, HEAP, lsl #32
    // 0x908fd0: ArrayLoad: r8 = r2[r1]  ; Unknown_4
    //     0x908fd0: add             x16, x2, x1, lsl #2
    //     0x908fd4: ldur            w8, [x16, #0xf]
    // 0x908fd8: DecompressPointer r8
    //     0x908fd8: add             x8, x8, HEAP, lsl #32
    // 0x908fdc: stur            x8, [fp, #-0x18]
    // 0x908fe0: add             x9, x1, #1
    // 0x908fe4: stur            x9, [fp, #-0x48]
    // 0x908fe8: LoadField: r1 = r4->field_1f
    //     0x908fe8: ldur            x1, [x4, #0x1f]
    // 0x908fec: LoadField: r2 = r4->field_27
    //     0x908fec: ldur            x2, [x4, #0x27]
    // 0x908ff0: sub             x10, x1, x2
    // 0x908ff4: scvtf           d1, x10
    // 0x908ff8: fdiv            d2, d1, d0
    // 0x908ffc: mov             x1, x6
    // 0x909000: mov             x2, x8
    // 0x909004: stur            d2, [fp, #-0x88]
    // 0x909008: r0 = _getValueOrData()
    //     0x909008: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90900c: mov             x2, x0
    // 0x909010: ldur            x0, [fp, #-0x10]
    // 0x909014: LoadField: r1 = r0->field_f
    //     0x909014: ldur            w1, [x0, #0xf]
    // 0x909018: DecompressPointer r1
    //     0x909018: add             x1, x1, HEAP, lsl #32
    // 0x90901c: cmp             w1, w2
    // 0x909020: b.ne            #0x909028
    // 0x909024: r2 = Null
    //     0x909024: mov             x2, NULL
    // 0x909028: ldur            d0, [fp, #-0x88]
    // 0x90902c: ldur            x1, [fp, #-0x20]
    // 0x909030: ldur            d1, [fp, #-0x90]
    // 0x909034: cmp             w2, NULL
    // 0x909038: b.eq            #0x909324
    // 0x90903c: r3 = LoadInt32Instr(r2)
    //     0x90903c: sbfx            x3, x2, #1, #0x1f
    //     0x909040: tbz             w2, #0, #0x909048
    //     0x909044: ldur            x3, [x2, #7]
    // 0x909048: scvtf           d2, x3
    // 0x90904c: fdiv            d3, d2, d1
    // 0x909050: fmul            d2, d0, d3
    // 0x909054: mov             v0.16b, v2.16b
    // 0x909058: stp             fp, lr, [SP, #-0x10]!
    // 0x90905c: mov             fp, SP
    // 0x909060: CallRuntime_LibcRound(double) -> double
    //     0x909060: and             SP, SP, #0xfffffffffffffff0
    //     0x909064: mov             sp, SP
    //     0x909068: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x90906c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x909070: blr             x16
    //     0x909074: movz            x16, #0x8
    //     0x909078: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x90907c: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x909080: sub             sp, x16, #1, lsl #12
    //     0x909084: mov             SP, fp
    //     0x909088: ldp             fp, lr, [SP], #0x10
    // 0x90908c: fcmp            d0, d0
    // 0x909090: b.vs            #0x909328
    // 0x909094: fcvtzs          x0, d0
    // 0x909098: asr             x16, x0, #0x1e
    // 0x90909c: cmp             x16, x0, asr #63
    // 0x9090a0: b.ne            #0x909328
    // 0x9090a4: lsl             x0, x0, #1
    // 0x9090a8: ldur            x1, [fp, #-0x20]
    // 0x9090ac: ldur            x2, [fp, #-0x18]
    // 0x9090b0: stur            x0, [fp, #-0x28]
    // 0x9090b4: r0 = _getValueOrData()
    //     0x9090b4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9090b8: mov             x2, x0
    // 0x9090bc: ldur            x0, [fp, #-0x20]
    // 0x9090c0: LoadField: r1 = r0->field_f
    //     0x9090c0: ldur            w1, [x0, #0xf]
    // 0x9090c4: DecompressPointer r1
    //     0x9090c4: add             x1, x1, HEAP, lsl #32
    // 0x9090c8: cmp             w1, w2
    // 0x9090cc: b.ne            #0x9090d8
    // 0x9090d0: r3 = Null
    //     0x9090d0: mov             x3, NULL
    // 0x9090d4: b               #0x9090dc
    // 0x9090d8: mov             x3, x2
    // 0x9090dc: stur            x3, [fp, #-0x40]
    // 0x9090e0: cmp             w3, NULL
    // 0x9090e4: b.eq            #0x909344
    // 0x9090e8: mov             x1, x0
    // 0x9090ec: ldur            x2, [fp, #-0x18]
    // 0x9090f0: r0 = _getValueOrData()
    //     0x9090f0: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x9090f4: ldur            x1, [fp, #-0x20]
    // 0x9090f8: LoadField: r2 = r1->field_f
    //     0x9090f8: ldur            w2, [x1, #0xf]
    // 0x9090fc: DecompressPointer r2
    //     0x9090fc: add             x2, x2, HEAP, lsl #32
    // 0x909100: cmp             w2, w0
    // 0x909104: b.ne            #0x909110
    // 0x909108: r4 = Null
    //     0x909108: mov             x4, NULL
    // 0x90910c: b               #0x909114
    // 0x909110: mov             x4, x0
    // 0x909114: ldur            x3, [fp, #-0x50]
    // 0x909118: ldur            x2, [fp, #-0x28]
    // 0x90911c: ldur            x0, [fp, #-0x40]
    // 0x909120: cmp             w4, NULL
    // 0x909124: b.eq            #0x909348
    // 0x909128: LoadField: r5 = r4->field_13
    //     0x909128: ldur            x5, [x4, #0x13]
    // 0x90912c: r4 = LoadInt32Instr(r2)
    //     0x90912c: sbfx            x4, x2, #1, #0x1f
    //     0x909130: tbz             w2, #0, #0x909138
    //     0x909134: ldur            x4, [x2, #7]
    // 0x909138: stur            x4, [fp, #-0x80]
    // 0x90913c: add             x2, x5, x4
    // 0x909140: stur            x2, [fp, #-0x78]
    // 0x909144: LoadField: r5 = r0->field_7
    //     0x909144: ldur            x5, [x0, #7]
    // 0x909148: stur            x5, [fp, #-0x68]
    // 0x90914c: LoadField: r6 = r0->field_f
    //     0x90914c: ldur            w6, [x0, #0xf]
    // 0x909150: DecompressPointer r6
    //     0x909150: add             x6, x6, HEAP, lsl #32
    // 0x909154: stur            x6, [fp, #-0x60]
    // 0x909158: LoadField: r7 = r0->field_1b
    //     0x909158: ldur            w7, [x0, #0x1b]
    // 0x90915c: DecompressPointer r7
    //     0x90915c: add             x7, x7, HEAP, lsl #32
    // 0x909160: stur            x7, [fp, #-0x28]
    // 0x909164: r0 = Result()
    //     0x909164: bl              #0x907e84  ; AllocateResultStub -> Result (size=0x20)
    // 0x909168: mov             x1, x0
    // 0x90916c: ldur            x0, [fp, #-0x68]
    // 0x909170: stur            x1, [fp, #-0x40]
    // 0x909174: StoreField: r1->field_7 = r0
    //     0x909174: stur            x0, [x1, #7]
    // 0x909178: ldur            x0, [fp, #-0x60]
    // 0x90917c: StoreField: r1->field_f = r0
    //     0x90917c: stur            w0, [x1, #0xf]
    // 0x909180: ldur            x0, [fp, #-0x78]
    // 0x909184: StoreField: r1->field_13 = r0
    //     0x909184: stur            x0, [x1, #0x13]
    // 0x909188: ldur            x0, [fp, #-0x28]
    // 0x90918c: StoreField: r1->field_1b = r0
    //     0x90918c: stur            w0, [x1, #0x1b]
    // 0x909190: ldur            x16, [fp, #-0x18]
    // 0x909194: str             x16, [SP]
    // 0x909198: r0 = _getHash()
    //     0x909198: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0x90919c: r5 = LoadInt32Instr(r0)
    //     0x90919c: sbfx            x5, x0, #1, #0x1f
    // 0x9091a0: ldur            x1, [fp, #-0x20]
    // 0x9091a4: ldur            x2, [fp, #-0x18]
    // 0x9091a8: ldur            x3, [fp, #-0x40]
    // 0x9091ac: r0 = _set()
    //     0x9091ac: bl              #0x5f8458  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x9091b0: ldur            x0, [fp, #-0x50]
    // 0x9091b4: ldur            x1, [fp, #-0x80]
    // 0x9091b8: add             x7, x0, x1
    // 0x9091bc: ldur            x1, [fp, #-0x48]
    // 0x9091c0: ldur            x4, [fp, #-8]
    // 0x9091c4: ldur            x0, [fp, #-0x38]
    // 0x9091c8: ldur            x5, [fp, #-0x20]
    // 0x9091cc: ldur            d0, [fp, #-0x90]
    // 0x9091d0: ldur            x3, [fp, #-0x58]
    // 0x9091d4: b               #0x908f9c
    // 0x9091d8: mov             x1, x4
    // 0x9091dc: mov             x0, x7
    // 0x9091e0: LoadField: r2 = r1->field_27
    //     0x9091e0: ldur            x2, [x1, #0x27]
    // 0x9091e4: add             x3, x2, x0
    // 0x9091e8: StoreField: r1->field_27 = r3
    //     0x9091e8: stur            x3, [x1, #0x27]
    // 0x9091ec: r0 = Null
    //     0x9091ec: mov             x0, NULL
    // 0x9091f0: LeaveFrame
    //     0x9091f0: mov             SP, fp
    //     0x9091f4: ldp             fp, lr, [SP], #0x10
    // 0x9091f8: ret
    //     0x9091f8: ret             
    // 0x9091fc: mov             x0, x4
    // 0x909200: r0 = ConcurrentModificationError()
    //     0x909200: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x909204: mov             x1, x0
    // 0x909208: ldur            x0, [fp, #-0x30]
    // 0x90920c: StoreField: r1->field_b = r0
    //     0x90920c: stur            w0, [x1, #0xb]
    // 0x909210: mov             x0, x1
    // 0x909214: r0 = Throw()
    //     0x909214: bl              #0xec04b8  ; ThrowStub
    // 0x909218: brk             #0
    // 0x90921c: mov             x0, x1
    // 0x909220: r0 = ConcurrentModificationError()
    //     0x909220: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x909224: mov             x1, x0
    // 0x909228: ldur            x0, [fp, #-0x30]
    // 0x90922c: StoreField: r1->field_b = r0
    //     0x90922c: stur            w0, [x1, #0xb]
    // 0x909230: mov             x0, x1
    // 0x909234: r0 = Throw()
    //     0x909234: bl              #0xec04b8  ; ThrowStub
    // 0x909238: brk             #0
    // 0x90923c: r0 = ConcurrentModificationError()
    //     0x90923c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x909240: mov             x1, x0
    // 0x909244: ldur            x0, [fp, #-0x38]
    // 0x909248: StoreField: r1->field_b = r0
    //     0x909248: stur            w0, [x1, #0xb]
    // 0x90924c: mov             x0, x1
    // 0x909250: r0 = Throw()
    //     0x909250: bl              #0xec04b8  ; ThrowStub
    // 0x909254: brk             #0
    // 0x909258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90925c: b               #0x907f94
    // 0x909260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909260: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x909264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909264: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x909268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90926c: b               #0x9082e8
    // 0x909270: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909270: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909274: b               #0x9084a4
    // 0x909278: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909278: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x90927c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90927c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x909280: stp             x2, x3, [SP, #-0x10]!
    // 0x909284: SaveReg r1
    //     0x909284: str             x1, [SP, #-8]!
    // 0x909288: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x90928c: r4 = 0
    //     0x90928c: movz            x4, #0
    // 0x909290: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x909294: blr             lr
    // 0x909298: brk             #0
    // 0x90929c: cmp             x2, xzr
    // 0x9092a0: sub             x4, x0, x2
    // 0x9092a4: add             x0, x0, x2
    // 0x9092a8: csel            x0, x4, x0, lt
    // 0x9092ac: b               #0x9086a0
    // 0x9092b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9092b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9092b4: b               #0x908774
    // 0x9092b8: stp             x2, x3, [SP, #-0x10]!
    // 0x9092bc: SaveReg r0
    //     0x9092bc: str             x0, [SP, #-8]!
    // 0x9092c0: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x9092c4: r4 = 0
    //     0x9092c4: movz            x4, #0
    // 0x9092c8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x9092cc: blr             lr
    // 0x9092d0: brk             #0
    // 0x9092d4: cmp             x3, xzr
    // 0x9092d8: sub             x4, x1, x3
    // 0x9092dc: add             x1, x1, x3
    // 0x9092e0: csel            x1, x4, x1, lt
    // 0x9092e4: b               #0x9089cc
    // 0x9092e8: stp             x4, x5, [SP, #-0x10]!
    // 0x9092ec: stp             x1, x3, [SP, #-0x10]!
    // 0x9092f0: SaveReg r0
    //     0x9092f0: str             x0, [SP, #-8]!
    // 0x9092f4: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x9092f8: r4 = 0
    //     0x9092f8: movz            x4, #0
    // 0x9092fc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x909300: blr             lr
    // 0x909304: brk             #0
    // 0x909308: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909308: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90930c: b               #0x908a7c
    // 0x909310: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909310: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x909314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909314: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x909318: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909318: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x90931c: r0 = StackOverflowSharedWithFPURegs()
    //     0x90931c: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0x909320: b               #0x908fb0
    // 0x909324: r0 = NullCastErrorSharedWithFPURegs()
    //     0x909324: bl              #0xec29f8  ; NullCastErrorSharedWithFPURegsStub
    // 0x909328: SaveReg d0
    //     0x909328: str             q0, [SP, #-0x10]!
    // 0x90932c: r0 = 74
    //     0x90932c: movz            x0, #0x4a
    // 0x909330: r30 = DoubleToIntegerStub
    //     0x909330: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x909334: LoadField: r30 = r30->field_7
    //     0x909334: ldur            lr, [lr, #7]
    // 0x909338: blr             lr
    // 0x90933c: RestoreReg d0
    //     0x90933c: ldr             q0, [SP], #0x10
    // 0x909340: b               #0x9090a8
    // 0x909344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909344: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x909348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x909348: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] MapEntry<Heir, int> <anonymous closure>(dynamic, Heir) {
    // ** addr: 0x90a670, size: 0x8c
    // 0x90a670: EnterFrame
    //     0x90a670: stp             fp, lr, [SP, #-0x10]!
    //     0x90a674: mov             fp, SP
    // 0x90a678: AllocStack(0x8)
    //     0x90a678: sub             SP, SP, #8
    // 0x90a67c: SetupParameters()
    //     0x90a67c: ldr             x0, [fp, #0x18]
    //     0x90a680: ldur            w1, [x0, #0x17]
    //     0x90a684: add             x1, x1, HEAP, lsl #32
    // 0x90a688: CheckStackOverflow
    //     0x90a688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a68c: cmp             SP, x16
    //     0x90a690: b.ls            #0x90a6f4
    // 0x90a694: LoadField: r0 = r1->field_f
    //     0x90a694: ldur            w0, [x1, #0xf]
    // 0x90a698: DecompressPointer r0
    //     0x90a698: add             x0, x0, HEAP, lsl #32
    // 0x90a69c: mov             x1, x0
    // 0x90a6a0: ldr             x2, [fp, #0x10]
    // 0x90a6a4: r0 = get()
    //     0x90a6a4: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x90a6a8: LoadField: r2 = r0->field_13
    //     0x90a6a8: ldur            x2, [x0, #0x13]
    // 0x90a6ac: stur            x2, [fp, #-8]
    // 0x90a6b0: r1 = <Heir, int>
    //     0x90a6b0: add             x1, PP, #0x28, lsl #12  ; [pp+0x28060] TypeArguments: <Heir, int>
    //     0x90a6b4: ldr             x1, [x1, #0x60]
    // 0x90a6b8: r0 = MapEntry()
    //     0x90a6b8: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x90a6bc: mov             x3, x0
    // 0x90a6c0: ldr             x2, [fp, #0x10]
    // 0x90a6c4: StoreField: r3->field_b = r2
    //     0x90a6c4: stur            w2, [x3, #0xb]
    // 0x90a6c8: ldur            x2, [fp, #-8]
    // 0x90a6cc: r0 = BoxInt64Instr(r2)
    //     0x90a6cc: sbfiz           x0, x2, #1, #0x1f
    //     0x90a6d0: cmp             x2, x0, asr #1
    //     0x90a6d4: b.eq            #0x90a6e0
    //     0x90a6d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90a6dc: stur            x2, [x0, #7]
    // 0x90a6e0: StoreField: r3->field_f = r0
    //     0x90a6e0: stur            w0, [x3, #0xf]
    // 0x90a6e4: mov             x0, x3
    // 0x90a6e8: LeaveFrame
    //     0x90a6e8: mov             SP, fp
    //     0x90a6ec: ldp             fp, lr, [SP], #0x10
    // 0x90a6f0: ret
    //     0x90a6f0: ret             
    // 0x90a6f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a6f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a6f8: b               #0x90a694
  }
  [closure] void <anonymous closure>(dynamic, Heir, Furudh) {
    // ** addr: 0x90a6fc, size: 0x19c
    // 0x90a6fc: EnterFrame
    //     0x90a6fc: stp             fp, lr, [SP, #-0x10]!
    //     0x90a700: mov             fp, SP
    // 0x90a704: AllocStack(0x30)
    //     0x90a704: sub             SP, SP, #0x30
    // 0x90a708: SetupParameters()
    //     0x90a708: ldr             x0, [fp, #0x20]
    //     0x90a70c: ldur            w3, [x0, #0x17]
    //     0x90a710: add             x3, x3, HEAP, lsl #32
    //     0x90a714: stur            x3, [fp, #-0x18]
    // 0x90a718: CheckStackOverflow
    //     0x90a718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a71c: cmp             SP, x16
    //     0x90a720: b.ls            #0x90a86c
    // 0x90a724: ldr             x0, [fp, #0x10]
    // 0x90a728: LoadField: r1 = r0->field_7
    //     0x90a728: ldur            w1, [x0, #7]
    // 0x90a72c: DecompressPointer r1
    //     0x90a72c: add             x1, x1, HEAP, lsl #32
    // 0x90a730: LoadField: r2 = r3->field_f
    //     0x90a730: ldur            w2, [x3, #0xf]
    // 0x90a734: DecompressPointer r2
    //     0x90a734: add             x2, x2, HEAP, lsl #32
    // 0x90a738: LoadField: r4 = r2->field_1f
    //     0x90a738: ldur            x4, [x2, #0x1f]
    // 0x90a73c: LoadField: r5 = r1->field_7
    //     0x90a73c: ldur            x5, [x1, #7]
    // 0x90a740: mul             x6, x4, x5
    // 0x90a744: LoadField: r4 = r1->field_f
    //     0x90a744: ldur            x4, [x1, #0xf]
    // 0x90a748: cbz             x4, #0x90a874
    // 0x90a74c: sdiv            x5, x6, x4
    // 0x90a750: stur            x5, [fp, #-0x10]
    // 0x90a754: LoadField: r4 = r2->field_b
    //     0x90a754: ldur            w4, [x2, #0xb]
    // 0x90a758: DecompressPointer r4
    //     0x90a758: add             x4, x4, HEAP, lsl #32
    // 0x90a75c: mov             x1, x4
    // 0x90a760: ldr             x2, [fp, #0x18]
    // 0x90a764: stur            x4, [fp, #-8]
    // 0x90a768: r0 = _getValueOrData()
    //     0x90a768: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90a76c: ldur            x2, [fp, #-8]
    // 0x90a770: LoadField: r1 = r2->field_f
    //     0x90a770: ldur            w1, [x2, #0xf]
    // 0x90a774: DecompressPointer r1
    //     0x90a774: add             x1, x1, HEAP, lsl #32
    // 0x90a778: cmp             w1, w0
    // 0x90a77c: b.ne            #0x90a788
    // 0x90a780: r5 = Null
    //     0x90a780: mov             x5, NULL
    // 0x90a784: b               #0x90a78c
    // 0x90a788: mov             x5, x0
    // 0x90a78c: ldr             x3, [fp, #0x10]
    // 0x90a790: ldur            x4, [fp, #-0x10]
    // 0x90a794: cmp             w5, NULL
    // 0x90a798: b.eq            #0x90a894
    // 0x90a79c: r0 = BoxInt64Instr(r4)
    //     0x90a79c: sbfiz           x0, x4, #1, #0x1f
    //     0x90a7a0: cmp             x4, x0, asr #1
    //     0x90a7a4: b.eq            #0x90a7b0
    //     0x90a7a8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90a7ac: stur            x4, [x0, #7]
    // 0x90a7b0: stur            x0, [fp, #-0x20]
    // 0x90a7b4: stp             x0, x3, [SP]
    // 0x90a7b8: mov             x1, x5
    // 0x90a7bc: r4 = const [0, 0x3, 0x2, 0x1, share, 0x1, siham, 0x2, null]
    //     0x90a7bc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c578] List(9) [0, 0x3, 0x2, 0x1, "share", 0x1, "siham", 0x2, Null]
    //     0x90a7c0: ldr             x4, [x4, #0x578]
    // 0x90a7c4: r0 = copyWith()
    //     0x90a7c4: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90a7c8: ldur            x1, [fp, #-8]
    // 0x90a7cc: ldr             x2, [fp, #0x18]
    // 0x90a7d0: mov             x3, x0
    // 0x90a7d4: r0 = []=()
    //     0x90a7d4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90a7d8: ldr             x1, [fp, #0x10]
    // 0x90a7dc: LoadField: r2 = r1->field_b
    //     0x90a7dc: ldur            x2, [x1, #0xb]
    // 0x90a7e0: cmp             x2, #2
    // 0x90a7e4: b.ne            #0x90a840
    // 0x90a7e8: ldur            x1, [fp, #-0x18]
    // 0x90a7ec: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x90a7ec: ldur            w2, [x1, #0x17]
    // 0x90a7f0: DecompressPointer r2
    //     0x90a7f0: add             x2, x2, HEAP, lsl #32
    // 0x90a7f4: tbz             w2, #4, #0x90a85c
    // 0x90a7f8: ldur            x2, [fp, #-0x10]
    // 0x90a7fc: r3 = true
    //     0x90a7fc: add             x3, NULL, #0x20  ; true
    // 0x90a800: ldur            x0, [fp, #-0x20]
    // 0x90a804: StoreField: r1->field_1b = r0
    //     0x90a804: stur            w0, [x1, #0x1b]
    //     0x90a808: tbz             w0, #0, #0x90a824
    //     0x90a80c: ldurb           w16, [x1, #-1]
    //     0x90a810: ldurb           w17, [x0, #-1]
    //     0x90a814: and             x16, x17, x16, lsr #2
    //     0x90a818: tst             x16, HEAP, lsr #32
    //     0x90a81c: b.eq            #0x90a824
    //     0x90a820: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90a824: LoadField: r4 = r1->field_f
    //     0x90a824: ldur            w4, [x1, #0xf]
    // 0x90a828: DecompressPointer r4
    //     0x90a828: add             x4, x4, HEAP, lsl #32
    // 0x90a82c: LoadField: r5 = r4->field_27
    //     0x90a82c: ldur            x5, [x4, #0x27]
    // 0x90a830: add             x6, x5, x2
    // 0x90a834: StoreField: r4->field_27 = r6
    //     0x90a834: stur            x6, [x4, #0x27]
    // 0x90a838: ArrayStore: r1[0] = r3  ; List_4
    //     0x90a838: stur            w3, [x1, #0x17]
    // 0x90a83c: b               #0x90a85c
    // 0x90a840: ldur            x1, [fp, #-0x18]
    // 0x90a844: ldur            x2, [fp, #-0x10]
    // 0x90a848: LoadField: r3 = r1->field_f
    //     0x90a848: ldur            w3, [x1, #0xf]
    // 0x90a84c: DecompressPointer r3
    //     0x90a84c: add             x3, x3, HEAP, lsl #32
    // 0x90a850: LoadField: r1 = r3->field_27
    //     0x90a850: ldur            x1, [x3, #0x27]
    // 0x90a854: add             x4, x1, x2
    // 0x90a858: StoreField: r3->field_27 = r4
    //     0x90a858: stur            x4, [x3, #0x27]
    // 0x90a85c: r0 = Null
    //     0x90a85c: mov             x0, NULL
    // 0x90a860: LeaveFrame
    //     0x90a860: mov             SP, fp
    //     0x90a864: ldp             fp, lr, [SP], #0x10
    // 0x90a868: ret
    //     0x90a868: ret             
    // 0x90a86c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a86c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a870: b               #0x90a724
    // 0x90a874: stp             x4, x6, [SP, #-0x10]!
    // 0x90a878: stp             x2, x3, [SP, #-0x10]!
    // 0x90a87c: SaveReg r0
    //     0x90a87c: str             x0, [SP, #-8]!
    // 0x90a880: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x90a884: r4 = 0
    //     0x90a884: movz            x4, #0
    // 0x90a888: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x90a88c: blr             lr
    // 0x90a890: brk             #0
    // 0x90a894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90a894: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Fraction? <anonymous closure>(dynamic, Furudh) {
    // ** addr: 0x90a898, size: 0x28
    // 0x90a898: ldr             x1, [SP]
    // 0x90a89c: LoadField: r2 = r1->field_b
    //     0x90a89c: ldur            x2, [x1, #0xb]
    // 0x90a8a0: cmp             x2, #2
    // 0x90a8a4: b.ne            #0x90a8b0
    // 0x90a8a8: r0 = Null
    //     0x90a8a8: mov             x0, NULL
    // 0x90a8ac: b               #0x90a8bc
    // 0x90a8b0: LoadField: r2 = r1->field_7
    //     0x90a8b0: ldur            w2, [x1, #7]
    // 0x90a8b4: DecompressPointer r2
    //     0x90a8b4: add             x2, x2, HEAP, lsl #32
    // 0x90a8b8: mov             x0, x2
    // 0x90a8bc: ret
    //     0x90a8bc: ret             
  }
  [closure] bool <anonymous closure>(dynamic, Heir, Ashobah) {
    // ** addr: 0x90a8c0, size: 0x48
    // 0x90a8c0: EnterFrame
    //     0x90a8c0: stp             fp, lr, [SP, #-0x10]!
    //     0x90a8c4: mov             fp, SP
    // 0x90a8c8: ldr             x0, [fp, #0x20]
    // 0x90a8cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x90a8cc: ldur            w1, [x0, #0x17]
    // 0x90a8d0: DecompressPointer r1
    //     0x90a8d0: add             x1, x1, HEAP, lsl #32
    // 0x90a8d4: CheckStackOverflow
    //     0x90a8d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a8d8: cmp             SP, x16
    //     0x90a8dc: b.ls            #0x90a900
    // 0x90a8e0: LoadField: r0 = r1->field_13
    //     0x90a8e0: ldur            w0, [x1, #0x13]
    // 0x90a8e4: DecompressPointer r0
    //     0x90a8e4: add             x0, x0, HEAP, lsl #32
    // 0x90a8e8: mov             x1, x0
    // 0x90a8ec: ldr             x2, [fp, #0x18]
    // 0x90a8f0: r0 = contains()
    //     0x90a8f0: bl              #0x86a94c  ; [dart:collection] ListBase::contains
    // 0x90a8f4: LeaveFrame
    //     0x90a8f4: mov             SP, fp
    //     0x90a8f8: ldp             fp, lr, [SP], #0x10
    // 0x90a8fc: ret
    //     0x90a8fc: ret             
    // 0x90a900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90a900: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90a904: b               #0x90a8e0
  }
  [closure] void <anonymous closure>(dynamic, Heir, Furudh) {
    // ** addr: 0x90a908, size: 0x150
    // 0x90a908: EnterFrame
    //     0x90a908: stp             fp, lr, [SP, #-0x10]!
    //     0x90a90c: mov             fp, SP
    // 0x90a910: AllocStack(0x30)
    //     0x90a910: sub             SP, SP, #0x30
    // 0x90a914: SetupParameters()
    //     0x90a914: ldr             x0, [fp, #0x20]
    //     0x90a918: ldur            w3, [x0, #0x17]
    //     0x90a91c: add             x3, x3, HEAP, lsl #32
    //     0x90a920: stur            x3, [fp, #-8]
    // 0x90a924: CheckStackOverflow
    //     0x90a924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90a928: cmp             SP, x16
    //     0x90a92c: b.ls            #0x90aa50
    // 0x90a930: LoadField: r1 = r3->field_f
    //     0x90a930: ldur            w1, [x3, #0xf]
    // 0x90a934: DecompressPointer r1
    //     0x90a934: add             x1, x1, HEAP, lsl #32
    // 0x90a938: ldr             x2, [fp, #0x18]
    // 0x90a93c: r0 = get()
    //     0x90a93c: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x90a940: mov             x2, x0
    // 0x90a944: LoadField: r0 = r2->field_13
    //     0x90a944: ldur            x0, [x2, #0x13]
    // 0x90a948: ldur            x3, [fp, #-8]
    // 0x90a94c: LoadField: r1 = r3->field_27
    //     0x90a94c: ldur            w1, [x3, #0x27]
    // 0x90a950: DecompressPointer r1
    //     0x90a950: add             x1, x1, HEAP, lsl #32
    // 0x90a954: r4 = LoadInt32Instr(r1)
    //     0x90a954: sbfx            x4, x1, #1, #0x1f
    //     0x90a958: tbz             w1, #0, #0x90a960
    //     0x90a95c: ldur            x4, [x1, #7]
    // 0x90a960: mul             x5, x0, x4
    // 0x90a964: stur            x5, [fp, #-0x20]
    // 0x90a968: LoadField: r0 = r3->field_f
    //     0x90a968: ldur            w0, [x3, #0xf]
    // 0x90a96c: DecompressPointer r0
    //     0x90a96c: add             x0, x0, HEAP, lsl #32
    // 0x90a970: LoadField: r4 = r0->field_b
    //     0x90a970: ldur            w4, [x0, #0xb]
    // 0x90a974: DecompressPointer r4
    //     0x90a974: add             x4, x4, HEAP, lsl #32
    // 0x90a978: stur            x4, [fp, #-0x18]
    // 0x90a97c: r0 = BoxInt64Instr(r5)
    //     0x90a97c: sbfiz           x0, x5, #1, #0x1f
    //     0x90a980: cmp             x5, x0, asr #1
    //     0x90a984: b.eq            #0x90a990
    //     0x90a988: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90a98c: stur            x5, [x0, #7]
    // 0x90a990: stur            x0, [fp, #-0x10]
    // 0x90a994: ldr             x16, [fp, #0x10]
    // 0x90a998: stp             x0, x16, [SP]
    // 0x90a99c: mov             x1, x2
    // 0x90a9a0: r4 = const [0, 0x3, 0x2, 0x1, share, 0x1, siham, 0x2, null]
    //     0x90a9a0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c578] List(9) [0, 0x3, 0x2, 0x1, "share", 0x1, "siham", 0x2, Null]
    //     0x90a9a4: ldr             x4, [x4, #0x578]
    // 0x90a9a8: r0 = copyWith()
    //     0x90a9a8: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90a9ac: ldur            x1, [fp, #-0x18]
    // 0x90a9b0: ldr             x2, [fp, #0x18]
    // 0x90a9b4: mov             x3, x0
    // 0x90a9b8: r0 = []=()
    //     0x90a9b8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90a9bc: ldr             x1, [fp, #0x10]
    // 0x90a9c0: LoadField: r2 = r1->field_b
    //     0x90a9c0: ldur            x2, [x1, #0xb]
    // 0x90a9c4: cmp             x2, #2
    // 0x90a9c8: b.ne            #0x90aa24
    // 0x90a9cc: ldur            x1, [fp, #-8]
    // 0x90a9d0: LoadField: r2 = r1->field_1f
    //     0x90a9d0: ldur            w2, [x1, #0x1f]
    // 0x90a9d4: DecompressPointer r2
    //     0x90a9d4: add             x2, x2, HEAP, lsl #32
    // 0x90a9d8: tbz             w2, #4, #0x90aa40
    // 0x90a9dc: ldur            x2, [fp, #-0x20]
    // 0x90a9e0: r3 = true
    //     0x90a9e0: add             x3, NULL, #0x20  ; true
    // 0x90a9e4: ldur            x0, [fp, #-0x10]
    // 0x90a9e8: StoreField: r1->field_23 = r0
    //     0x90a9e8: stur            w0, [x1, #0x23]
    //     0x90a9ec: tbz             w0, #0, #0x90aa08
    //     0x90a9f0: ldurb           w16, [x1, #-1]
    //     0x90a9f4: ldurb           w17, [x0, #-1]
    //     0x90a9f8: and             x16, x17, x16, lsr #2
    //     0x90a9fc: tst             x16, HEAP, lsr #32
    //     0x90aa00: b.eq            #0x90aa08
    //     0x90aa04: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90aa08: LoadField: r4 = r1->field_f
    //     0x90aa08: ldur            w4, [x1, #0xf]
    // 0x90aa0c: DecompressPointer r4
    //     0x90aa0c: add             x4, x4, HEAP, lsl #32
    // 0x90aa10: LoadField: r5 = r4->field_27
    //     0x90aa10: ldur            x5, [x4, #0x27]
    // 0x90aa14: add             x6, x5, x2
    // 0x90aa18: StoreField: r4->field_27 = r6
    //     0x90aa18: stur            x6, [x4, #0x27]
    // 0x90aa1c: StoreField: r1->field_1f = r3
    //     0x90aa1c: stur            w3, [x1, #0x1f]
    // 0x90aa20: b               #0x90aa40
    // 0x90aa24: ldur            x1, [fp, #-8]
    // 0x90aa28: ldur            x2, [fp, #-0x20]
    // 0x90aa2c: LoadField: r3 = r1->field_f
    //     0x90aa2c: ldur            w3, [x1, #0xf]
    // 0x90aa30: DecompressPointer r3
    //     0x90aa30: add             x3, x3, HEAP, lsl #32
    // 0x90aa34: LoadField: r1 = r3->field_27
    //     0x90aa34: ldur            x1, [x3, #0x27]
    // 0x90aa38: add             x4, x1, x2
    // 0x90aa3c: StoreField: r3->field_27 = r4
    //     0x90aa3c: stur            x4, [x3, #0x27]
    // 0x90aa40: r0 = Null
    //     0x90aa40: mov             x0, NULL
    // 0x90aa44: LeaveFrame
    //     0x90aa44: mov             SP, fp
    //     0x90aa48: ldp             fp, lr, [SP], #0x10
    // 0x90aa4c: ret
    //     0x90aa4c: ret             
    // 0x90aa50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90aa50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90aa54: b               #0x90a930
  }
  [closure] void <anonymous closure>(dynamic, Heir, Furudh) {
    // ** addr: 0x90aa58, size: 0x19c
    // 0x90aa58: EnterFrame
    //     0x90aa58: stp             fp, lr, [SP, #-0x10]!
    //     0x90aa5c: mov             fp, SP
    // 0x90aa60: AllocStack(0x30)
    //     0x90aa60: sub             SP, SP, #0x30
    // 0x90aa64: SetupParameters()
    //     0x90aa64: ldr             x0, [fp, #0x20]
    //     0x90aa68: ldur            w3, [x0, #0x17]
    //     0x90aa6c: add             x3, x3, HEAP, lsl #32
    //     0x90aa70: stur            x3, [fp, #-0x18]
    // 0x90aa74: CheckStackOverflow
    //     0x90aa74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90aa78: cmp             SP, x16
    //     0x90aa7c: b.ls            #0x90abc8
    // 0x90aa80: ldr             x0, [fp, #0x10]
    // 0x90aa84: LoadField: r1 = r0->field_7
    //     0x90aa84: ldur            w1, [x0, #7]
    // 0x90aa88: DecompressPointer r1
    //     0x90aa88: add             x1, x1, HEAP, lsl #32
    // 0x90aa8c: LoadField: r2 = r3->field_f
    //     0x90aa8c: ldur            w2, [x3, #0xf]
    // 0x90aa90: DecompressPointer r2
    //     0x90aa90: add             x2, x2, HEAP, lsl #32
    // 0x90aa94: LoadField: r4 = r2->field_1f
    //     0x90aa94: ldur            x4, [x2, #0x1f]
    // 0x90aa98: LoadField: r5 = r1->field_7
    //     0x90aa98: ldur            x5, [x1, #7]
    // 0x90aa9c: mul             x6, x4, x5
    // 0x90aaa0: LoadField: r4 = r1->field_f
    //     0x90aaa0: ldur            x4, [x1, #0xf]
    // 0x90aaa4: cbz             x4, #0x90abd0
    // 0x90aaa8: sdiv            x5, x6, x4
    // 0x90aaac: stur            x5, [fp, #-0x10]
    // 0x90aab0: LoadField: r4 = r2->field_b
    //     0x90aab0: ldur            w4, [x2, #0xb]
    // 0x90aab4: DecompressPointer r4
    //     0x90aab4: add             x4, x4, HEAP, lsl #32
    // 0x90aab8: mov             x1, x4
    // 0x90aabc: ldr             x2, [fp, #0x18]
    // 0x90aac0: stur            x4, [fp, #-8]
    // 0x90aac4: r0 = _getValueOrData()
    //     0x90aac4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90aac8: ldur            x2, [fp, #-8]
    // 0x90aacc: LoadField: r1 = r2->field_f
    //     0x90aacc: ldur            w1, [x2, #0xf]
    // 0x90aad0: DecompressPointer r1
    //     0x90aad0: add             x1, x1, HEAP, lsl #32
    // 0x90aad4: cmp             w1, w0
    // 0x90aad8: b.ne            #0x90aae4
    // 0x90aadc: r5 = Null
    //     0x90aadc: mov             x5, NULL
    // 0x90aae0: b               #0x90aae8
    // 0x90aae4: mov             x5, x0
    // 0x90aae8: ldr             x3, [fp, #0x10]
    // 0x90aaec: ldur            x4, [fp, #-0x10]
    // 0x90aaf0: cmp             w5, NULL
    // 0x90aaf4: b.eq            #0x90abf0
    // 0x90aaf8: r0 = BoxInt64Instr(r4)
    //     0x90aaf8: sbfiz           x0, x4, #1, #0x1f
    //     0x90aafc: cmp             x4, x0, asr #1
    //     0x90ab00: b.eq            #0x90ab0c
    //     0x90ab04: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90ab08: stur            x4, [x0, #7]
    // 0x90ab0c: stur            x0, [fp, #-0x20]
    // 0x90ab10: stp             x0, x3, [SP]
    // 0x90ab14: mov             x1, x5
    // 0x90ab18: r4 = const [0, 0x3, 0x2, 0x1, share, 0x1, siham, 0x2, null]
    //     0x90ab18: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c578] List(9) [0, 0x3, 0x2, 0x1, "share", 0x1, "siham", 0x2, Null]
    //     0x90ab1c: ldr             x4, [x4, #0x578]
    // 0x90ab20: r0 = copyWith()
    //     0x90ab20: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90ab24: ldur            x1, [fp, #-8]
    // 0x90ab28: ldr             x2, [fp, #0x18]
    // 0x90ab2c: mov             x3, x0
    // 0x90ab30: r0 = []=()
    //     0x90ab30: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90ab34: ldr             x1, [fp, #0x10]
    // 0x90ab38: LoadField: r2 = r1->field_b
    //     0x90ab38: ldur            x2, [x1, #0xb]
    // 0x90ab3c: cmp             x2, #2
    // 0x90ab40: b.ne            #0x90ab9c
    // 0x90ab44: ldur            x1, [fp, #-0x18]
    // 0x90ab48: LoadField: r2 = r1->field_1f
    //     0x90ab48: ldur            w2, [x1, #0x1f]
    // 0x90ab4c: DecompressPointer r2
    //     0x90ab4c: add             x2, x2, HEAP, lsl #32
    // 0x90ab50: tbz             w2, #4, #0x90abb8
    // 0x90ab54: ldur            x2, [fp, #-0x10]
    // 0x90ab58: r3 = true
    //     0x90ab58: add             x3, NULL, #0x20  ; true
    // 0x90ab5c: ldur            x0, [fp, #-0x20]
    // 0x90ab60: StoreField: r1->field_23 = r0
    //     0x90ab60: stur            w0, [x1, #0x23]
    //     0x90ab64: tbz             w0, #0, #0x90ab80
    //     0x90ab68: ldurb           w16, [x1, #-1]
    //     0x90ab6c: ldurb           w17, [x0, #-1]
    //     0x90ab70: and             x16, x17, x16, lsr #2
    //     0x90ab74: tst             x16, HEAP, lsr #32
    //     0x90ab78: b.eq            #0x90ab80
    //     0x90ab7c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90ab80: LoadField: r4 = r1->field_f
    //     0x90ab80: ldur            w4, [x1, #0xf]
    // 0x90ab84: DecompressPointer r4
    //     0x90ab84: add             x4, x4, HEAP, lsl #32
    // 0x90ab88: LoadField: r5 = r4->field_27
    //     0x90ab88: ldur            x5, [x4, #0x27]
    // 0x90ab8c: add             x6, x5, x2
    // 0x90ab90: StoreField: r4->field_27 = r6
    //     0x90ab90: stur            x6, [x4, #0x27]
    // 0x90ab94: StoreField: r1->field_1f = r3
    //     0x90ab94: stur            w3, [x1, #0x1f]
    // 0x90ab98: b               #0x90abb8
    // 0x90ab9c: ldur            x1, [fp, #-0x18]
    // 0x90aba0: ldur            x2, [fp, #-0x10]
    // 0x90aba4: LoadField: r3 = r1->field_f
    //     0x90aba4: ldur            w3, [x1, #0xf]
    // 0x90aba8: DecompressPointer r3
    //     0x90aba8: add             x3, x3, HEAP, lsl #32
    // 0x90abac: LoadField: r1 = r3->field_27
    //     0x90abac: ldur            x1, [x3, #0x27]
    // 0x90abb0: add             x4, x1, x2
    // 0x90abb4: StoreField: r3->field_27 = r4
    //     0x90abb4: stur            x4, [x3, #0x27]
    // 0x90abb8: r0 = Null
    //     0x90abb8: mov             x0, NULL
    // 0x90abbc: LeaveFrame
    //     0x90abbc: mov             SP, fp
    //     0x90abc0: ldp             fp, lr, [SP], #0x10
    // 0x90abc4: ret
    //     0x90abc4: ret             
    // 0x90abc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90abc8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90abcc: b               #0x90aa80
    // 0x90abd0: stp             x4, x6, [SP, #-0x10]!
    // 0x90abd4: stp             x2, x3, [SP, #-0x10]!
    // 0x90abd8: SaveReg r0
    //     0x90abd8: str             x0, [SP, #-8]!
    // 0x90abdc: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x90abe0: r4 = 0
    //     0x90abe0: movz            x4, #0
    // 0x90abe4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x90abe8: blr             lr
    // 0x90abec: brk             #0
    // 0x90abf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90abf0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, Share) {
    // ** addr: 0x90abf4, size: 0x20
    // 0x90abf4: ldr             x1, [SP]
    // 0x90abf8: r2 = LoadClassIdInstr(r1)
    //     0x90abf8: ldur            x2, [x1, #-1]
    //     0x90abfc: ubfx            x2, x2, #0xc, #0x14
    // 0x90ac00: cmp             x2, #0x188
    // 0x90ac04: r16 = true
    //     0x90ac04: add             x16, NULL, #0x20  ; true
    // 0x90ac08: r17 = false
    //     0x90ac08: add             x17, NULL, #0x30  ; false
    // 0x90ac0c: csel            x0, x16, x17, eq
    // 0x90ac10: ret
    //     0x90ac10: ret             
  }
  _ _calculateSihamForAshobah(/* No info */) {
    // ** addr: 0x90ac74, size: 0x364
    // 0x90ac74: EnterFrame
    //     0x90ac74: stp             fp, lr, [SP, #-0x10]!
    //     0x90ac78: mov             fp, SP
    // 0x90ac7c: AllocStack(0x38)
    //     0x90ac7c: sub             SP, SP, #0x38
    // 0x90ac80: SetupParameters(Waris this /* r1 => r1, fp-0x8 */)
    //     0x90ac80: stur            x1, [fp, #-8]
    // 0x90ac84: CheckStackOverflow
    //     0x90ac84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ac88: cmp             SP, x16
    //     0x90ac8c: b.ls            #0x90af98
    // 0x90ac90: r1 = 5
    //     0x90ac90: movz            x1, #0x5
    // 0x90ac94: r0 = AllocateContext()
    //     0x90ac94: bl              #0xec126c  ; AllocateContextStub
    // 0x90ac98: mov             x1, x0
    // 0x90ac9c: ldur            x0, [fp, #-8]
    // 0x90aca0: stur            x1, [fp, #-0x28]
    // 0x90aca4: StoreField: r1->field_f = r0
    //     0x90aca4: stur            w0, [x1, #0xf]
    // 0x90aca8: LoadField: r2 = r0->field_1f
    //     0x90aca8: ldur            x2, [x0, #0x1f]
    // 0x90acac: LoadField: r3 = r0->field_27
    //     0x90acac: ldur            x3, [x0, #0x27]
    // 0x90acb0: sub             x4, x2, x3
    // 0x90acb4: cmp             x4, #0
    // 0x90acb8: b.le            #0x90af88
    // 0x90acbc: LoadField: r2 = r0->field_13
    //     0x90acbc: ldur            w2, [x0, #0x13]
    // 0x90acc0: DecompressPointer r2
    //     0x90acc0: add             x2, x2, HEAP, lsl #32
    // 0x90acc4: stur            x2, [fp, #-0x10]
    // 0x90acc8: LoadField: r3 = r2->field_13
    //     0x90acc8: ldur            w3, [x2, #0x13]
    // 0x90accc: r4 = LoadInt32Instr(r3)
    //     0x90accc: sbfx            x4, x3, #1, #0x1f
    // 0x90acd0: asr             x3, x4, #1
    // 0x90acd4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x90acd4: ldur            w4, [x2, #0x17]
    // 0x90acd8: r5 = LoadInt32Instr(r4)
    //     0x90acd8: sbfx            x5, x4, #1, #0x1f
    // 0x90acdc: sub             x4, x3, x5
    // 0x90ace0: cbz             x4, #0x90af88
    // 0x90ace4: cmp             x4, #1
    // 0x90ace8: b.ne            #0x90add4
    // 0x90acec: LoadField: r1 = r2->field_7
    //     0x90acec: ldur            w1, [x2, #7]
    // 0x90acf0: DecompressPointer r1
    //     0x90acf0: add             x1, x1, HEAP, lsl #32
    // 0x90acf4: r0 = _CompactIterable()
    //     0x90acf4: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x90acf8: mov             x1, x0
    // 0x90acfc: ldur            x0, [fp, #-0x10]
    // 0x90ad00: StoreField: r1->field_b = r0
    //     0x90ad00: stur            w0, [x1, #0xb]
    // 0x90ad04: r0 = -2
    //     0x90ad04: orr             x0, xzr, #0xfffffffffffffffe
    // 0x90ad08: StoreField: r1->field_f = r0
    //     0x90ad08: stur            x0, [x1, #0xf]
    // 0x90ad0c: r0 = 2
    //     0x90ad0c: movz            x0, #0x2
    // 0x90ad10: ArrayStore: r1[0] = r0  ; List_8
    //     0x90ad10: stur            x0, [x1, #0x17]
    // 0x90ad14: r0 = first()
    //     0x90ad14: bl              #0x8939e0  ; [dart:core] Iterable::first
    // 0x90ad18: mov             x3, x0
    // 0x90ad1c: ldur            x0, [fp, #-8]
    // 0x90ad20: stur            x3, [fp, #-0x20]
    // 0x90ad24: LoadField: r4 = r0->field_b
    //     0x90ad24: ldur            w4, [x0, #0xb]
    // 0x90ad28: DecompressPointer r4
    //     0x90ad28: add             x4, x4, HEAP, lsl #32
    // 0x90ad2c: mov             x1, x4
    // 0x90ad30: mov             x2, x3
    // 0x90ad34: stur            x4, [fp, #-0x18]
    // 0x90ad38: r0 = _getValueOrData()
    //     0x90ad38: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90ad3c: ldur            x2, [fp, #-0x18]
    // 0x90ad40: LoadField: r1 = r2->field_f
    //     0x90ad40: ldur            w1, [x2, #0xf]
    // 0x90ad44: DecompressPointer r1
    //     0x90ad44: add             x1, x1, HEAP, lsl #32
    // 0x90ad48: cmp             w1, w0
    // 0x90ad4c: b.ne            #0x90ad58
    // 0x90ad50: r4 = Null
    //     0x90ad50: mov             x4, NULL
    // 0x90ad54: b               #0x90ad5c
    // 0x90ad58: mov             x4, x0
    // 0x90ad5c: ldur            x3, [fp, #-8]
    // 0x90ad60: cmp             w4, NULL
    // 0x90ad64: b.eq            #0x90afa0
    // 0x90ad68: LoadField: r0 = r3->field_1f
    //     0x90ad68: ldur            x0, [x3, #0x1f]
    // 0x90ad6c: LoadField: r1 = r3->field_27
    //     0x90ad6c: ldur            x1, [x3, #0x27]
    // 0x90ad70: sub             x5, x0, x1
    // 0x90ad74: r0 = BoxInt64Instr(r5)
    //     0x90ad74: sbfiz           x0, x5, #1, #0x1f
    //     0x90ad78: cmp             x5, x0, asr #1
    //     0x90ad7c: b.eq            #0x90ad88
    //     0x90ad80: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90ad84: stur            x5, [x0, #7]
    // 0x90ad88: str             x0, [SP]
    // 0x90ad8c: mov             x1, x4
    // 0x90ad90: r4 = const [0, 0x2, 0x1, 0x1, siham, 0x1, null]
    //     0x90ad90: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c570] List(7) [0, 0x2, 0x1, 0x1, "siham", 0x1, Null]
    //     0x90ad94: ldr             x4, [x4, #0x570]
    // 0x90ad98: r0 = copyWith()
    //     0x90ad98: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90ad9c: ldur            x1, [fp, #-0x18]
    // 0x90ada0: ldur            x2, [fp, #-0x20]
    // 0x90ada4: mov             x3, x0
    // 0x90ada8: r0 = []=()
    //     0x90ada8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90adac: ldur            x2, [fp, #-8]
    // 0x90adb0: LoadField: r0 = r2->field_27
    //     0x90adb0: ldur            x0, [x2, #0x27]
    // 0x90adb4: LoadField: r1 = r2->field_1f
    //     0x90adb4: ldur            x1, [x2, #0x1f]
    // 0x90adb8: sub             x3, x1, x0
    // 0x90adbc: add             x1, x0, x3
    // 0x90adc0: StoreField: r2->field_27 = r1
    //     0x90adc0: stur            x1, [x2, #0x27]
    // 0x90adc4: r0 = Null
    //     0x90adc4: mov             x0, NULL
    // 0x90adc8: LeaveFrame
    //     0x90adc8: mov             SP, fp
    //     0x90adcc: ldp             fp, lr, [SP], #0x10
    // 0x90add0: ret
    //     0x90add0: ret             
    // 0x90add4: mov             x16, x2
    // 0x90add8: mov             x2, x0
    // 0x90addc: mov             x0, x16
    // 0x90ade0: r16 = <Heir, Ashobah>
    //     0x90ade0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28448] TypeArguments: <Heir, Ashobah>
    //     0x90ade4: ldr             x16, [x16, #0x448]
    // 0x90ade8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x90adec: stp             lr, x16, [SP]
    // 0x90adf0: r0 = Map._fromLiteral()
    //     0x90adf0: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90adf4: mov             x1, x0
    // 0x90adf8: ldur            x2, [fp, #-0x28]
    // 0x90adfc: stur            x1, [fp, #-0x18]
    // 0x90ae00: StoreField: r2->field_13 = r0
    //     0x90ae00: stur            w0, [x2, #0x13]
    //     0x90ae04: ldurb           w16, [x2, #-1]
    //     0x90ae08: ldurb           w17, [x0, #-1]
    //     0x90ae0c: and             x16, x17, x16, lsr #2
    //     0x90ae10: tst             x16, HEAP, lsr #32
    //     0x90ae14: b.eq            #0x90ae1c
    //     0x90ae18: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x90ae1c: r16 = <Heir, Ashobah>
    //     0x90ae1c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28448] TypeArguments: <Heir, Ashobah>
    //     0x90ae20: ldr             x16, [x16, #0x448]
    // 0x90ae24: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x90ae28: stp             lr, x16, [SP]
    // 0x90ae2c: r0 = Map._fromLiteral()
    //     0x90ae2c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90ae30: mov             x4, x0
    // 0x90ae34: ldur            x3, [fp, #-0x28]
    // 0x90ae38: stur            x4, [fp, #-0x20]
    // 0x90ae3c: ArrayStore: r3[0] = r0  ; List_4
    //     0x90ae3c: stur            w0, [x3, #0x17]
    //     0x90ae40: ldurb           w16, [x3, #-1]
    //     0x90ae44: ldurb           w17, [x0, #-1]
    //     0x90ae48: and             x16, x17, x16, lsr #2
    //     0x90ae4c: tst             x16, HEAP, lsr #32
    //     0x90ae50: b.eq            #0x90ae58
    //     0x90ae54: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x90ae58: StoreField: r3->field_1b = rZR
    //     0x90ae58: stur            wzr, [x3, #0x1b]
    // 0x90ae5c: mov             x2, x3
    // 0x90ae60: r1 = Function '<anonymous closure>':.
    //     0x90ae60: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c680] AnonymousClosure: (0x90b2ac), in [package:waris/src/waris.dart] Waris::_calculateSihamForAshobah (0x90ac74)
    //     0x90ae64: ldr             x1, [x1, #0x680]
    // 0x90ae68: r0 = AllocateClosure()
    //     0x90ae68: bl              #0xec1630  ; AllocateClosureStub
    // 0x90ae6c: ldur            x1, [fp, #-0x10]
    // 0x90ae70: mov             x2, x0
    // 0x90ae74: r0 = forEach()
    //     0x90ae74: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x90ae78: ldur            x0, [fp, #-8]
    // 0x90ae7c: LoadField: r1 = r0->field_1f
    //     0x90ae7c: ldur            x1, [x0, #0x1f]
    // 0x90ae80: LoadField: r2 = r0->field_27
    //     0x90ae80: ldur            x2, [x0, #0x27]
    // 0x90ae84: sub             x3, x1, x2
    // 0x90ae88: ldur            x2, [fp, #-0x28]
    // 0x90ae8c: LoadField: r1 = r2->field_1b
    //     0x90ae8c: ldur            w1, [x2, #0x1b]
    // 0x90ae90: DecompressPointer r1
    //     0x90ae90: add             x1, x1, HEAP, lsl #32
    // 0x90ae94: r4 = LoadInt32Instr(r1)
    //     0x90ae94: sbfx            x4, x1, #1, #0x1f
    //     0x90ae98: tbz             w1, #0, #0x90aea0
    //     0x90ae9c: ldur            x4, [x1, #7]
    // 0x90aea0: cbz             x4, #0x90afa4
    // 0x90aea4: sdiv            x6, x3, x4
    // 0x90aea8: msub            x5, x6, x4, x3
    // 0x90aeac: cmp             x5, xzr
    // 0x90aeb0: b.lt            #0x90afc4
    // 0x90aeb4: cbz             x5, #0x90aec8
    // 0x90aeb8: str             x1, [SP]
    // 0x90aebc: mov             x1, x0
    // 0x90aec0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x90aec0: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x90aec4: r0 = _calculateInkisar()
    //     0x90aec4: bl              #0x907998  ; [package:waris/src/waris.dart] Waris::_calculateInkisar
    // 0x90aec8: ldur            x3, [fp, #-0x18]
    // 0x90aecc: LoadField: r0 = r3->field_13
    //     0x90aecc: ldur            w0, [x3, #0x13]
    // 0x90aed0: r1 = LoadInt32Instr(r0)
    //     0x90aed0: sbfx            x1, x0, #1, #0x1f
    // 0x90aed4: asr             x0, x1, #1
    // 0x90aed8: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x90aed8: ldur            w1, [x3, #0x17]
    // 0x90aedc: r2 = LoadInt32Instr(r1)
    //     0x90aedc: sbfx            x2, x1, #1, #0x1f
    // 0x90aee0: sub             x1, x0, x2
    // 0x90aee4: cbz             x1, #0x90af4c
    // 0x90aee8: ldur            x0, [fp, #-8]
    // 0x90aeec: ldur            x4, [fp, #-0x28]
    // 0x90aef0: LoadField: r1 = r0->field_1f
    //     0x90aef0: ldur            x1, [x0, #0x1f]
    // 0x90aef4: LoadField: r2 = r0->field_27
    //     0x90aef4: ldur            x2, [x0, #0x27]
    // 0x90aef8: sub             x5, x1, x2
    // 0x90aefc: r0 = BoxInt64Instr(r5)
    //     0x90aefc: sbfiz           x0, x5, #1, #0x1f
    //     0x90af00: cmp             x5, x0, asr #1
    //     0x90af04: b.eq            #0x90af10
    //     0x90af08: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90af0c: stur            x5, [x0, #7]
    // 0x90af10: StoreField: r4->field_1f = r0
    //     0x90af10: stur            w0, [x4, #0x1f]
    //     0x90af14: tbz             w0, #0, #0x90af30
    //     0x90af18: ldurb           w16, [x4, #-1]
    //     0x90af1c: ldurb           w17, [x0, #-1]
    //     0x90af20: and             x16, x17, x16, lsr #2
    //     0x90af24: tst             x16, HEAP, lsr #32
    //     0x90af28: b.eq            #0x90af30
    //     0x90af2c: bl              #0xec0a88  ; WriteBarrierWrappersStub
    // 0x90af30: mov             x2, x4
    // 0x90af34: r1 = Function '<anonymous closure>':.
    //     0x90af34: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c688] AnonymousClosure: (0x90b0d4), in [package:waris/src/waris.dart] Waris::_calculateSihamForAshobah (0x90ac74)
    //     0x90af38: ldr             x1, [x1, #0x688]
    // 0x90af3c: r0 = AllocateClosure()
    //     0x90af3c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90af40: ldur            x1, [fp, #-0x18]
    // 0x90af44: mov             x2, x0
    // 0x90af48: r0 = forEach()
    //     0x90af48: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x90af4c: ldur            x0, [fp, #-0x20]
    // 0x90af50: LoadField: r1 = r0->field_13
    //     0x90af50: ldur            w1, [x0, #0x13]
    // 0x90af54: r2 = LoadInt32Instr(r1)
    //     0x90af54: sbfx            x2, x1, #1, #0x1f
    // 0x90af58: asr             x1, x2, #1
    // 0x90af5c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x90af5c: ldur            w2, [x0, #0x17]
    // 0x90af60: r3 = LoadInt32Instr(r2)
    //     0x90af60: sbfx            x3, x2, #1, #0x1f
    // 0x90af64: sub             x2, x1, x3
    // 0x90af68: cbz             x2, #0x90af88
    // 0x90af6c: ldur            x2, [fp, #-0x28]
    // 0x90af70: r1 = Function '<anonymous closure>':.
    //     0x90af70: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c690] AnonymousClosure: (0x90afd8), in [package:waris/src/waris.dart] Waris::_calculateSihamForAshobah (0x90ac74)
    //     0x90af74: ldr             x1, [x1, #0x690]
    // 0x90af78: r0 = AllocateClosure()
    //     0x90af78: bl              #0xec1630  ; AllocateClosureStub
    // 0x90af7c: ldur            x1, [fp, #-0x20]
    // 0x90af80: mov             x2, x0
    // 0x90af84: r0 = forEach()
    //     0x90af84: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x90af88: r0 = Null
    //     0x90af88: mov             x0, NULL
    // 0x90af8c: LeaveFrame
    //     0x90af8c: mov             SP, fp
    //     0x90af90: ldp             fp, lr, [SP], #0x10
    // 0x90af94: ret
    //     0x90af94: ret             
    // 0x90af98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90af98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90af9c: b               #0x90ac90
    // 0x90afa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90afa0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x90afa4: stp             x3, x4, [SP, #-0x10]!
    // 0x90afa8: stp             x1, x2, [SP, #-0x10]!
    // 0x90afac: SaveReg r0
    //     0x90afac: str             x0, [SP, #-8]!
    // 0x90afb0: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x90afb4: r4 = 0
    //     0x90afb4: movz            x4, #0
    // 0x90afb8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x90afbc: blr             lr
    // 0x90afc0: brk             #0
    // 0x90afc4: cmp             x4, xzr
    // 0x90afc8: sub             x6, x5, x4
    // 0x90afcc: add             x5, x5, x4
    // 0x90afd0: csel            x5, x6, x5, lt
    // 0x90afd4: b               #0x90aeb4
  }
  [closure] void <anonymous closure>(dynamic, Heir, Ashobah) {
    // ** addr: 0x90afd8, size: 0xfc
    // 0x90afd8: EnterFrame
    //     0x90afd8: stp             fp, lr, [SP, #-0x10]!
    //     0x90afdc: mov             fp, SP
    // 0x90afe0: AllocStack(0x18)
    //     0x90afe0: sub             SP, SP, #0x18
    // 0x90afe4: SetupParameters()
    //     0x90afe4: ldr             x0, [fp, #0x20]
    //     0x90afe8: ldur            w3, [x0, #0x17]
    //     0x90afec: add             x3, x3, HEAP, lsl #32
    //     0x90aff0: stur            x3, [fp, #-0x10]
    // 0x90aff4: CheckStackOverflow
    //     0x90aff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90aff8: cmp             SP, x16
    //     0x90affc: b.ls            #0x90b0c8
    // 0x90b000: LoadField: r0 = r3->field_f
    //     0x90b000: ldur            w0, [x3, #0xf]
    // 0x90b004: DecompressPointer r0
    //     0x90b004: add             x0, x0, HEAP, lsl #32
    // 0x90b008: LoadField: r4 = r0->field_b
    //     0x90b008: ldur            w4, [x0, #0xb]
    // 0x90b00c: DecompressPointer r4
    //     0x90b00c: add             x4, x4, HEAP, lsl #32
    // 0x90b010: mov             x1, x4
    // 0x90b014: ldr             x2, [fp, #0x18]
    // 0x90b018: stur            x4, [fp, #-8]
    // 0x90b01c: r0 = _getValueOrData()
    //     0x90b01c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90b020: ldur            x2, [fp, #-8]
    // 0x90b024: LoadField: r1 = r2->field_f
    //     0x90b024: ldur            w1, [x2, #0xf]
    // 0x90b028: DecompressPointer r1
    //     0x90b028: add             x1, x1, HEAP, lsl #32
    // 0x90b02c: cmp             w1, w0
    // 0x90b030: b.ne            #0x90b03c
    // 0x90b034: r4 = Null
    //     0x90b034: mov             x4, NULL
    // 0x90b038: b               #0x90b040
    // 0x90b03c: mov             x4, x0
    // 0x90b040: ldur            x3, [fp, #-0x10]
    // 0x90b044: cmp             w4, NULL
    // 0x90b048: b.eq            #0x90b0d0
    // 0x90b04c: LoadField: r0 = r3->field_f
    //     0x90b04c: ldur            w0, [x3, #0xf]
    // 0x90b050: DecompressPointer r0
    //     0x90b050: add             x0, x0, HEAP, lsl #32
    // 0x90b054: LoadField: r1 = r0->field_1f
    //     0x90b054: ldur            x1, [x0, #0x1f]
    // 0x90b058: LoadField: r5 = r0->field_27
    //     0x90b058: ldur            x5, [x0, #0x27]
    // 0x90b05c: sub             x6, x1, x5
    // 0x90b060: r0 = BoxInt64Instr(r6)
    //     0x90b060: sbfiz           x0, x6, #1, #0x1f
    //     0x90b064: cmp             x6, x0, asr #1
    //     0x90b068: b.eq            #0x90b074
    //     0x90b06c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90b070: stur            x6, [x0, #7]
    // 0x90b074: str             x0, [SP]
    // 0x90b078: mov             x1, x4
    // 0x90b07c: r4 = const [0, 0x2, 0x1, 0x1, siham, 0x1, null]
    //     0x90b07c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c570] List(7) [0, 0x2, 0x1, 0x1, "siham", 0x1, Null]
    //     0x90b080: ldr             x4, [x4, #0x570]
    // 0x90b084: r0 = copyWith()
    //     0x90b084: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90b088: ldur            x1, [fp, #-8]
    // 0x90b08c: ldr             x2, [fp, #0x18]
    // 0x90b090: mov             x3, x0
    // 0x90b094: r0 = []=()
    //     0x90b094: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90b098: ldur            x1, [fp, #-0x10]
    // 0x90b09c: LoadField: r2 = r1->field_f
    //     0x90b09c: ldur            w2, [x1, #0xf]
    // 0x90b0a0: DecompressPointer r2
    //     0x90b0a0: add             x2, x2, HEAP, lsl #32
    // 0x90b0a4: LoadField: r1 = r2->field_27
    //     0x90b0a4: ldur            x1, [x2, #0x27]
    // 0x90b0a8: LoadField: r3 = r2->field_1f
    //     0x90b0a8: ldur            x3, [x2, #0x1f]
    // 0x90b0ac: sub             x4, x3, x1
    // 0x90b0b0: add             x3, x1, x4
    // 0x90b0b4: StoreField: r2->field_27 = r3
    //     0x90b0b4: stur            x3, [x2, #0x27]
    // 0x90b0b8: r0 = Null
    //     0x90b0b8: mov             x0, NULL
    // 0x90b0bc: LeaveFrame
    //     0x90b0bc: mov             SP, fp
    //     0x90b0c0: ldp             fp, lr, [SP], #0x10
    // 0x90b0c4: ret
    //     0x90b0c4: ret             
    // 0x90b0c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b0c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b0cc: b               #0x90b000
    // 0x90b0d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90b0d0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Heir, Ashobah) {
    // ** addr: 0x90b0d4, size: 0x1d8
    // 0x90b0d4: EnterFrame
    //     0x90b0d4: stp             fp, lr, [SP, #-0x10]!
    //     0x90b0d8: mov             fp, SP
    // 0x90b0dc: AllocStack(0x28)
    //     0x90b0dc: sub             SP, SP, #0x28
    // 0x90b0e0: SetupParameters()
    //     0x90b0e0: ldr             x0, [fp, #0x20]
    //     0x90b0e4: ldur            w3, [x0, #0x17]
    //     0x90b0e8: add             x3, x3, HEAP, lsl #32
    //     0x90b0ec: stur            x3, [fp, #-0x10]
    // 0x90b0f0: CheckStackOverflow
    //     0x90b0f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b0f4: cmp             SP, x16
    //     0x90b0f8: b.ls            #0x90b284
    // 0x90b0fc: ldr             x0, [fp, #0x18]
    // 0x90b100: LoadField: r1 = r0->field_7
    //     0x90b100: ldur            x1, [x0, #7]
    // 0x90b104: cmp             x1, #0xf
    // 0x90b108: b.gt            #0x90b114
    // 0x90b10c: r4 = 2
    //     0x90b10c: movz            x4, #0x2
    // 0x90b110: b               #0x90b118
    // 0x90b114: r4 = 1
    //     0x90b114: movz            x4, #0x1
    // 0x90b118: stur            x4, [fp, #-8]
    // 0x90b11c: LoadField: r1 = r3->field_f
    //     0x90b11c: ldur            w1, [x3, #0xf]
    // 0x90b120: DecompressPointer r1
    //     0x90b120: add             x1, x1, HEAP, lsl #32
    // 0x90b124: LoadField: r2 = r1->field_7
    //     0x90b124: ldur            w2, [x1, #7]
    // 0x90b128: DecompressPointer r2
    //     0x90b128: add             x2, x2, HEAP, lsl #32
    // 0x90b12c: mov             x1, x2
    // 0x90b130: mov             x2, x0
    // 0x90b134: r0 = HeirsExt.countOf()
    //     0x90b134: bl              #0x90939c  ; [package:waris/src/heirs.dart] ::HeirsExt.countOf
    // 0x90b138: mov             x1, x0
    // 0x90b13c: ldur            x0, [fp, #-8]
    // 0x90b140: mul             x2, x0, x1
    // 0x90b144: ldur            x0, [fp, #-0x10]
    // 0x90b148: LoadField: r1 = r0->field_1b
    //     0x90b148: ldur            w1, [x0, #0x1b]
    // 0x90b14c: DecompressPointer r1
    //     0x90b14c: add             x1, x1, HEAP, lsl #32
    // 0x90b150: scvtf           d0, x2
    // 0x90b154: r2 = LoadInt32Instr(r1)
    //     0x90b154: sbfx            x2, x1, #1, #0x1f
    //     0x90b158: tbz             w1, #0, #0x90b160
    //     0x90b15c: ldur            x2, [x1, #7]
    // 0x90b160: scvtf           d1, x2
    // 0x90b164: fdiv            d2, d0, d1
    // 0x90b168: LoadField: r1 = r0->field_1f
    //     0x90b168: ldur            w1, [x0, #0x1f]
    // 0x90b16c: DecompressPointer r1
    //     0x90b16c: add             x1, x1, HEAP, lsl #32
    // 0x90b170: r2 = LoadInt32Instr(r1)
    //     0x90b170: sbfx            x2, x1, #1, #0x1f
    //     0x90b174: tbz             w1, #0, #0x90b17c
    //     0x90b178: ldur            x2, [x1, #7]
    // 0x90b17c: scvtf           d0, x2
    // 0x90b180: fmul            d1, d2, d0
    // 0x90b184: mov             v0.16b, v1.16b
    // 0x90b188: stp             fp, lr, [SP, #-0x10]!
    // 0x90b18c: mov             fp, SP
    // 0x90b190: CallRuntime_LibcRound(double) -> double
    //     0x90b190: and             SP, SP, #0xfffffffffffffff0
    //     0x90b194: mov             sp, SP
    //     0x90b198: ldr             x16, [THR, #0x598]  ; THR::LibcRound
    //     0x90b19c: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x90b1a0: blr             x16
    //     0x90b1a4: movz            x16, #0x8
    //     0x90b1a8: str             x16, [THR, #0x7a0]  ; THR::vm_tag
    //     0x90b1ac: ldr             x16, [THR, #0x768]  ; THR::saved_stack_limit
    //     0x90b1b0: sub             sp, x16, #1, lsl #12
    //     0x90b1b4: mov             SP, fp
    //     0x90b1b8: ldp             fp, lr, [SP], #0x10
    // 0x90b1bc: fcmp            d0, d0
    // 0x90b1c0: b.vs            #0x90b28c
    // 0x90b1c4: fcvtzs          x0, d0
    // 0x90b1c8: asr             x16, x0, #0x1e
    // 0x90b1cc: cmp             x16, x0, asr #63
    // 0x90b1d0: b.ne            #0x90b28c
    // 0x90b1d4: lsl             x0, x0, #1
    // 0x90b1d8: ldur            x3, [fp, #-0x10]
    // 0x90b1dc: stur            x0, [fp, #-0x20]
    // 0x90b1e0: LoadField: r1 = r3->field_f
    //     0x90b1e0: ldur            w1, [x3, #0xf]
    // 0x90b1e4: DecompressPointer r1
    //     0x90b1e4: add             x1, x1, HEAP, lsl #32
    // 0x90b1e8: LoadField: r4 = r1->field_b
    //     0x90b1e8: ldur            w4, [x1, #0xb]
    // 0x90b1ec: DecompressPointer r4
    //     0x90b1ec: add             x4, x4, HEAP, lsl #32
    // 0x90b1f0: mov             x1, x4
    // 0x90b1f4: ldr             x2, [fp, #0x18]
    // 0x90b1f8: stur            x4, [fp, #-0x18]
    // 0x90b1fc: r0 = _getValueOrData()
    //     0x90b1fc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90b200: mov             x1, x0
    // 0x90b204: ldur            x0, [fp, #-0x18]
    // 0x90b208: LoadField: r2 = r0->field_f
    //     0x90b208: ldur            w2, [x0, #0xf]
    // 0x90b20c: DecompressPointer r2
    //     0x90b20c: add             x2, x2, HEAP, lsl #32
    // 0x90b210: cmp             w2, w1
    // 0x90b214: b.ne            #0x90b21c
    // 0x90b218: r1 = Null
    //     0x90b218: mov             x1, NULL
    // 0x90b21c: ldur            x3, [fp, #-0x10]
    // 0x90b220: ldur            x2, [fp, #-0x20]
    // 0x90b224: cmp             w1, NULL
    // 0x90b228: b.eq            #0x90b2a8
    // 0x90b22c: str             x2, [SP]
    // 0x90b230: r4 = const [0, 0x2, 0x1, 0x1, siham, 0x1, null]
    //     0x90b230: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c570] List(7) [0, 0x2, 0x1, 0x1, "siham", 0x1, Null]
    //     0x90b234: ldr             x4, [x4, #0x570]
    // 0x90b238: r0 = copyWith()
    //     0x90b238: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90b23c: ldur            x1, [fp, #-0x18]
    // 0x90b240: ldr             x2, [fp, #0x18]
    // 0x90b244: mov             x3, x0
    // 0x90b248: r0 = []=()
    //     0x90b248: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90b24c: ldur            x1, [fp, #-0x10]
    // 0x90b250: LoadField: r2 = r1->field_f
    //     0x90b250: ldur            w2, [x1, #0xf]
    // 0x90b254: DecompressPointer r2
    //     0x90b254: add             x2, x2, HEAP, lsl #32
    // 0x90b258: LoadField: r1 = r2->field_27
    //     0x90b258: ldur            x1, [x2, #0x27]
    // 0x90b25c: ldur            x3, [fp, #-0x20]
    // 0x90b260: r4 = LoadInt32Instr(r3)
    //     0x90b260: sbfx            x4, x3, #1, #0x1f
    //     0x90b264: tbz             w3, #0, #0x90b26c
    //     0x90b268: ldur            x4, [x3, #7]
    // 0x90b26c: add             x3, x1, x4
    // 0x90b270: StoreField: r2->field_27 = r3
    //     0x90b270: stur            x3, [x2, #0x27]
    // 0x90b274: r0 = Null
    //     0x90b274: mov             x0, NULL
    // 0x90b278: LeaveFrame
    //     0x90b278: mov             SP, fp
    //     0x90b27c: ldp             fp, lr, [SP], #0x10
    // 0x90b280: ret
    //     0x90b280: ret             
    // 0x90b284: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b284: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b288: b               #0x90b0fc
    // 0x90b28c: SaveReg d0
    //     0x90b28c: str             q0, [SP, #-0x10]!
    // 0x90b290: r0 = 74
    //     0x90b290: movz            x0, #0x4a
    // 0x90b294: r30 = DoubleToIntegerStub
    //     0x90b294: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x90b298: LoadField: r30 = r30->field_7
    //     0x90b298: ldur            lr, [lr, #7]
    // 0x90b29c: blr             lr
    // 0x90b2a0: RestoreReg d0
    //     0x90b2a0: ldr             q0, [SP], #0x10
    // 0x90b2a4: b               #0x90b1d8
    // 0x90b2a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90b2a8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Heir, Ashobah) {
    // ** addr: 0x90b2ac, size: 0x1c8
    // 0x90b2ac: EnterFrame
    //     0x90b2ac: stp             fp, lr, [SP, #-0x10]!
    //     0x90b2b0: mov             fp, SP
    // 0x90b2b4: AllocStack(0x28)
    //     0x90b2b4: sub             SP, SP, #0x28
    // 0x90b2b8: SetupParameters()
    //     0x90b2b8: ldr             x0, [fp, #0x20]
    //     0x90b2bc: ldur            w3, [x0, #0x17]
    //     0x90b2c0: add             x3, x3, HEAP, lsl #32
    //     0x90b2c4: stur            x3, [fp, #-0x10]
    // 0x90b2c8: CheckStackOverflow
    //     0x90b2c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b2cc: cmp             SP, x16
    //     0x90b2d0: b.ls            #0x90b46c
    // 0x90b2d4: ldr             x4, [fp, #0x10]
    // 0x90b2d8: LoadField: r2 = r4->field_7
    //     0x90b2d8: ldur            x2, [x4, #7]
    // 0x90b2dc: cmp             x2, #2
    // 0x90b2e0: b.gt            #0x90b408
    // 0x90b2e4: cmp             x2, #1
    // 0x90b2e8: b.gt            #0x90b318
    // 0x90b2ec: r0 = BoxInt64Instr(r2)
    //     0x90b2ec: sbfiz           x0, x2, #1, #0x1f
    //     0x90b2f0: cmp             x2, x0, asr #1
    //     0x90b2f4: b.eq            #0x90b300
    //     0x90b2f8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90b2fc: stur            x2, [x0, #7]
    // 0x90b300: cmp             w0, #2
    // 0x90b304: b.eq            #0x90b318
    // 0x90b308: mov             x1, x3
    // 0x90b30c: mov             x3, x4
    // 0x90b310: ldr             x4, [fp, #0x18]
    // 0x90b314: b               #0x90b414
    // 0x90b318: ldr             x0, [fp, #0x18]
    // 0x90b31c: LoadField: r5 = r3->field_13
    //     0x90b31c: ldur            w5, [x3, #0x13]
    // 0x90b320: DecompressPointer r5
    //     0x90b320: add             x5, x5, HEAP, lsl #32
    // 0x90b324: stur            x5, [fp, #-8]
    // 0x90b328: r1 = Null
    //     0x90b328: mov             x1, NULL
    // 0x90b32c: r2 = 4
    //     0x90b32c: movz            x2, #0x4
    // 0x90b330: r0 = AllocateArray()
    //     0x90b330: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90b334: ldr             x2, [fp, #0x18]
    // 0x90b338: StoreField: r0->field_f = r2
    //     0x90b338: stur            w2, [x0, #0xf]
    // 0x90b33c: ldr             x3, [fp, #0x10]
    // 0x90b340: StoreField: r0->field_13 = r3
    //     0x90b340: stur            w3, [x0, #0x13]
    // 0x90b344: r16 = <Heir, Ashobah>
    //     0x90b344: add             x16, PP, #0x28, lsl #12  ; [pp+0x28448] TypeArguments: <Heir, Ashobah>
    //     0x90b348: ldr             x16, [x16, #0x448]
    // 0x90b34c: stp             x0, x16, [SP]
    // 0x90b350: r0 = Map._fromLiteral()
    //     0x90b350: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90b354: ldur            x1, [fp, #-8]
    // 0x90b358: mov             x2, x0
    // 0x90b35c: r0 = addAll()
    //     0x90b35c: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x90b360: ldur            x0, [fp, #-0x10]
    // 0x90b364: LoadField: r3 = r0->field_1b
    //     0x90b364: ldur            w3, [x0, #0x1b]
    // 0x90b368: DecompressPointer r3
    //     0x90b368: add             x3, x3, HEAP, lsl #32
    // 0x90b36c: ldr             x4, [fp, #0x18]
    // 0x90b370: stur            x3, [fp, #-8]
    // 0x90b374: LoadField: r1 = r4->field_7
    //     0x90b374: ldur            x1, [x4, #7]
    // 0x90b378: cmp             x1, #0xf
    // 0x90b37c: b.gt            #0x90b388
    // 0x90b380: r5 = 2
    //     0x90b380: movz            x5, #0x2
    // 0x90b384: b               #0x90b38c
    // 0x90b388: r5 = 1
    //     0x90b388: movz            x5, #0x1
    // 0x90b38c: stur            x5, [fp, #-0x18]
    // 0x90b390: LoadField: r1 = r0->field_f
    //     0x90b390: ldur            w1, [x0, #0xf]
    // 0x90b394: DecompressPointer r1
    //     0x90b394: add             x1, x1, HEAP, lsl #32
    // 0x90b398: LoadField: r2 = r1->field_7
    //     0x90b398: ldur            w2, [x1, #7]
    // 0x90b39c: DecompressPointer r2
    //     0x90b39c: add             x2, x2, HEAP, lsl #32
    // 0x90b3a0: mov             x1, x2
    // 0x90b3a4: mov             x2, x4
    // 0x90b3a8: r0 = HeirsExt.countOf()
    //     0x90b3a8: bl              #0x90939c  ; [package:waris/src/heirs.dart] ::HeirsExt.countOf
    // 0x90b3ac: mov             x1, x0
    // 0x90b3b0: ldur            x0, [fp, #-0x18]
    // 0x90b3b4: mul             x2, x0, x1
    // 0x90b3b8: ldur            x0, [fp, #-8]
    // 0x90b3bc: r1 = LoadInt32Instr(r0)
    //     0x90b3bc: sbfx            x1, x0, #1, #0x1f
    //     0x90b3c0: tbz             w0, #0, #0x90b3c8
    //     0x90b3c4: ldur            x1, [x0, #7]
    // 0x90b3c8: add             x3, x1, x2
    // 0x90b3cc: r0 = BoxInt64Instr(r3)
    //     0x90b3cc: sbfiz           x0, x3, #1, #0x1f
    //     0x90b3d0: cmp             x3, x0, asr #1
    //     0x90b3d4: b.eq            #0x90b3e0
    //     0x90b3d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90b3dc: stur            x3, [x0, #7]
    // 0x90b3e0: ldur            x1, [fp, #-0x10]
    // 0x90b3e4: StoreField: r1->field_1b = r0
    //     0x90b3e4: stur            w0, [x1, #0x1b]
    //     0x90b3e8: tbz             w0, #0, #0x90b404
    //     0x90b3ec: ldurb           w16, [x1, #-1]
    //     0x90b3f0: ldurb           w17, [x0, #-1]
    //     0x90b3f4: and             x16, x17, x16, lsr #2
    //     0x90b3f8: tst             x16, HEAP, lsr #32
    //     0x90b3fc: b.eq            #0x90b404
    //     0x90b400: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90b404: b               #0x90b45c
    // 0x90b408: mov             x1, x3
    // 0x90b40c: mov             x3, x4
    // 0x90b410: ldr             x4, [fp, #0x18]
    // 0x90b414: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x90b414: ldur            w0, [x1, #0x17]
    // 0x90b418: DecompressPointer r0
    //     0x90b418: add             x0, x0, HEAP, lsl #32
    // 0x90b41c: stur            x0, [fp, #-8]
    // 0x90b420: r1 = Null
    //     0x90b420: mov             x1, NULL
    // 0x90b424: r2 = 4
    //     0x90b424: movz            x2, #0x4
    // 0x90b428: r0 = AllocateArray()
    //     0x90b428: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90b42c: mov             x1, x0
    // 0x90b430: ldr             x0, [fp, #0x18]
    // 0x90b434: StoreField: r1->field_f = r0
    //     0x90b434: stur            w0, [x1, #0xf]
    // 0x90b438: ldr             x0, [fp, #0x10]
    // 0x90b43c: StoreField: r1->field_13 = r0
    //     0x90b43c: stur            w0, [x1, #0x13]
    // 0x90b440: r16 = <Heir, Ashobah>
    //     0x90b440: add             x16, PP, #0x28, lsl #12  ; [pp+0x28448] TypeArguments: <Heir, Ashobah>
    //     0x90b444: ldr             x16, [x16, #0x448]
    // 0x90b448: stp             x1, x16, [SP]
    // 0x90b44c: r0 = Map._fromLiteral()
    //     0x90b44c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90b450: ldur            x1, [fp, #-8]
    // 0x90b454: mov             x2, x0
    // 0x90b458: r0 = addAll()
    //     0x90b458: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x90b45c: r0 = Null
    //     0x90b45c: mov             x0, NULL
    // 0x90b460: LeaveFrame
    //     0x90b460: mov             SP, fp
    //     0x90b464: ldp             fp, lr, [SP], #0x10
    // 0x90b468: ret
    //     0x90b468: ret             
    // 0x90b46c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b46c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b470: b               #0x90b2d4
  }
  _ _calculateSihamForFurudh(/* No info */) {
    // ** addr: 0x90b474, size: 0x70
    // 0x90b474: EnterFrame
    //     0x90b474: stp             fp, lr, [SP, #-0x10]!
    //     0x90b478: mov             fp, SP
    // 0x90b47c: AllocStack(0x10)
    //     0x90b47c: sub             SP, SP, #0x10
    // 0x90b480: SetupParameters(Waris this /* r1 => r1, fp-0x8 */)
    //     0x90b480: stur            x1, [fp, #-8]
    // 0x90b484: CheckStackOverflow
    //     0x90b484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b488: cmp             SP, x16
    //     0x90b48c: b.ls            #0x90b4dc
    // 0x90b490: r1 = 1
    //     0x90b490: movz            x1, #0x1
    // 0x90b494: r0 = AllocateContext()
    //     0x90b494: bl              #0xec126c  ; AllocateContextStub
    // 0x90b498: mov             x1, x0
    // 0x90b49c: ldur            x0, [fp, #-8]
    // 0x90b4a0: StoreField: r1->field_f = r0
    //     0x90b4a0: stur            w0, [x1, #0xf]
    // 0x90b4a4: LoadField: r3 = r0->field_f
    //     0x90b4a4: ldur            w3, [x0, #0xf]
    // 0x90b4a8: DecompressPointer r3
    //     0x90b4a8: add             x3, x3, HEAP, lsl #32
    // 0x90b4ac: mov             x2, x1
    // 0x90b4b0: stur            x3, [fp, #-0x10]
    // 0x90b4b4: r1 = Function '<anonymous closure>':.
    //     0x90b4b4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c698] AnonymousClosure: (0x90b4e4), in [package:waris/src/waris.dart] Waris::_calculateSihamForFurudh (0x90b474)
    //     0x90b4b8: ldr             x1, [x1, #0x698]
    // 0x90b4bc: r0 = AllocateClosure()
    //     0x90b4bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x90b4c0: ldur            x1, [fp, #-0x10]
    // 0x90b4c4: mov             x2, x0
    // 0x90b4c8: r0 = forEach()
    //     0x90b4c8: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x90b4cc: r0 = Null
    //     0x90b4cc: mov             x0, NULL
    // 0x90b4d0: LeaveFrame
    //     0x90b4d0: mov             SP, fp
    //     0x90b4d4: ldp             fp, lr, [SP], #0x10
    // 0x90b4d8: ret
    //     0x90b4d8: ret             
    // 0x90b4dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b4dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b4e0: b               #0x90b490
  }
  [closure] void <anonymous closure>(dynamic, Heir, Furudh) {
    // ** addr: 0x90b4e4, size: 0x12c
    // 0x90b4e4: EnterFrame
    //     0x90b4e4: stp             fp, lr, [SP, #-0x10]!
    //     0x90b4e8: mov             fp, SP
    // 0x90b4ec: AllocStack(0x20)
    //     0x90b4ec: sub             SP, SP, #0x20
    // 0x90b4f0: SetupParameters()
    //     0x90b4f0: ldr             x0, [fp, #0x20]
    //     0x90b4f4: ldur            w3, [x0, #0x17]
    //     0x90b4f8: add             x3, x3, HEAP, lsl #32
    //     0x90b4fc: stur            x3, [fp, #-0x18]
    // 0x90b500: CheckStackOverflow
    //     0x90b500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b504: cmp             SP, x16
    //     0x90b508: b.ls            #0x90b5e8
    // 0x90b50c: ldr             x0, [fp, #0x10]
    // 0x90b510: LoadField: r1 = r0->field_7
    //     0x90b510: ldur            w1, [x0, #7]
    // 0x90b514: DecompressPointer r1
    //     0x90b514: add             x1, x1, HEAP, lsl #32
    // 0x90b518: LoadField: r0 = r3->field_f
    //     0x90b518: ldur            w0, [x3, #0xf]
    // 0x90b51c: DecompressPointer r0
    //     0x90b51c: add             x0, x0, HEAP, lsl #32
    // 0x90b520: LoadField: r2 = r0->field_1f
    //     0x90b520: ldur            x2, [x0, #0x1f]
    // 0x90b524: LoadField: r4 = r1->field_7
    //     0x90b524: ldur            x4, [x1, #7]
    // 0x90b528: mul             x5, x2, x4
    // 0x90b52c: LoadField: r2 = r1->field_f
    //     0x90b52c: ldur            x2, [x1, #0xf]
    // 0x90b530: cbz             x2, #0x90b5f0
    // 0x90b534: sdiv            x4, x5, x2
    // 0x90b538: stur            x4, [fp, #-0x10]
    // 0x90b53c: LoadField: r5 = r0->field_b
    //     0x90b53c: ldur            w5, [x0, #0xb]
    // 0x90b540: DecompressPointer r5
    //     0x90b540: add             x5, x5, HEAP, lsl #32
    // 0x90b544: mov             x1, x5
    // 0x90b548: ldr             x2, [fp, #0x18]
    // 0x90b54c: stur            x5, [fp, #-8]
    // 0x90b550: r0 = _getValueOrData()
    //     0x90b550: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x90b554: ldur            x2, [fp, #-8]
    // 0x90b558: LoadField: r1 = r2->field_f
    //     0x90b558: ldur            w1, [x2, #0xf]
    // 0x90b55c: DecompressPointer r1
    //     0x90b55c: add             x1, x1, HEAP, lsl #32
    // 0x90b560: cmp             w1, w0
    // 0x90b564: b.ne            #0x90b570
    // 0x90b568: r5 = Null
    //     0x90b568: mov             x5, NULL
    // 0x90b56c: b               #0x90b574
    // 0x90b570: mov             x5, x0
    // 0x90b574: ldur            x3, [fp, #-0x18]
    // 0x90b578: ldur            x4, [fp, #-0x10]
    // 0x90b57c: cmp             w5, NULL
    // 0x90b580: b.eq            #0x90b60c
    // 0x90b584: r0 = BoxInt64Instr(r4)
    //     0x90b584: sbfiz           x0, x4, #1, #0x1f
    //     0x90b588: cmp             x4, x0, asr #1
    //     0x90b58c: b.eq            #0x90b598
    //     0x90b590: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90b594: stur            x4, [x0, #7]
    // 0x90b598: str             x0, [SP]
    // 0x90b59c: mov             x1, x5
    // 0x90b5a0: r4 = const [0, 0x2, 0x1, 0x1, siham, 0x1, null]
    //     0x90b5a0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c570] List(7) [0, 0x2, 0x1, 0x1, "siham", 0x1, Null]
    //     0x90b5a4: ldr             x4, [x4, #0x570]
    // 0x90b5a8: r0 = copyWith()
    //     0x90b5a8: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90b5ac: ldur            x1, [fp, #-8]
    // 0x90b5b0: ldr             x2, [fp, #0x18]
    // 0x90b5b4: mov             x3, x0
    // 0x90b5b8: r0 = []=()
    //     0x90b5b8: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90b5bc: ldur            x1, [fp, #-0x18]
    // 0x90b5c0: LoadField: r2 = r1->field_f
    //     0x90b5c0: ldur            w2, [x1, #0xf]
    // 0x90b5c4: DecompressPointer r2
    //     0x90b5c4: add             x2, x2, HEAP, lsl #32
    // 0x90b5c8: LoadField: r1 = r2->field_27
    //     0x90b5c8: ldur            x1, [x2, #0x27]
    // 0x90b5cc: ldur            x3, [fp, #-0x10]
    // 0x90b5d0: add             x4, x1, x3
    // 0x90b5d4: StoreField: r2->field_27 = r4
    //     0x90b5d4: stur            x4, [x2, #0x27]
    // 0x90b5d8: r0 = Null
    //     0x90b5d8: mov             x0, NULL
    // 0x90b5dc: LeaveFrame
    //     0x90b5dc: mov             SP, fp
    //     0x90b5e0: ldp             fp, lr, [SP], #0x10
    // 0x90b5e4: ret
    //     0x90b5e4: ret             
    // 0x90b5e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b5e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b5ec: b               #0x90b50c
    // 0x90b5f0: stp             x3, x5, [SP, #-0x10]!
    // 0x90b5f4: stp             x0, x2, [SP, #-0x10]!
    // 0x90b5f8: ldr             x5, [THR, #0x468]  ; THR::IntegerDivisionByZeroException
    // 0x90b5fc: r4 = 0
    //     0x90b5fc: movz            x4, #0
    // 0x90b600: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x90b604: blr             lr
    // 0x90b608: brk             #0
    // 0x90b60c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x90b60c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateAsalMasalah(/* No info */) {
    // ** addr: 0x90b610, size: 0xd4
    // 0x90b610: EnterFrame
    //     0x90b610: stp             fp, lr, [SP, #-0x10]!
    //     0x90b614: mov             fp, SP
    // 0x90b618: AllocStack(0x30)
    //     0x90b618: sub             SP, SP, #0x30
    // 0x90b61c: SetupParameters(Waris this /* r1 => r0, fp-0x10 */)
    //     0x90b61c: mov             x0, x1
    //     0x90b620: stur            x1, [fp, #-0x10]
    // 0x90b624: CheckStackOverflow
    //     0x90b624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b628: cmp             SP, x16
    //     0x90b62c: b.ls            #0x90b6dc
    // 0x90b630: LoadField: r4 = r0->field_f
    //     0x90b630: ldur            w4, [x0, #0xf]
    // 0x90b634: DecompressPointer r4
    //     0x90b634: add             x4, x4, HEAP, lsl #32
    // 0x90b638: stur            x4, [fp, #-8]
    // 0x90b63c: LoadField: r2 = r4->field_7
    //     0x90b63c: ldur            w2, [x4, #7]
    // 0x90b640: DecompressPointer r2
    //     0x90b640: add             x2, x2, HEAP, lsl #32
    // 0x90b644: r1 = Null
    //     0x90b644: mov             x1, NULL
    // 0x90b648: r3 = <X1>
    //     0x90b648: ldr             x3, [PP, #0x2680]  ; [pp+0x2680] TypeArguments: <X1>
    // 0x90b64c: r0 = Null
    //     0x90b64c: mov             x0, NULL
    // 0x90b650: cmp             x2, x0
    // 0x90b654: b.eq            #0x90b664
    // 0x90b658: r30 = InstantiateTypeArgumentsStub
    //     0x90b658: ldr             lr, [PP, #0x7c0]  ; [pp+0x7c0] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x90b65c: LoadField: r30 = r30->field_7
    //     0x90b65c: ldur            lr, [lr, #7]
    // 0x90b660: blr             lr
    // 0x90b664: mov             x1, x0
    // 0x90b668: r0 = _CompactIterable()
    //     0x90b668: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x90b66c: mov             x3, x0
    // 0x90b670: ldur            x0, [fp, #-8]
    // 0x90b674: stur            x3, [fp, #-0x18]
    // 0x90b678: StoreField: r3->field_b = r0
    //     0x90b678: stur            w0, [x3, #0xb]
    // 0x90b67c: r0 = -1
    //     0x90b67c: movn            x0, #0
    // 0x90b680: StoreField: r3->field_f = r0
    //     0x90b680: stur            x0, [x3, #0xf]
    // 0x90b684: r0 = 2
    //     0x90b684: movz            x0, #0x2
    // 0x90b688: ArrayStore: r3[0] = r0  ; List_8
    //     0x90b688: stur            x0, [x3, #0x17]
    // 0x90b68c: r1 = Function '<anonymous closure>':.
    //     0x90b68c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6a0] Function: [dart:ui] Paint::_data (0xc41d20)
    //     0x90b690: ldr             x1, [x1, #0x6a0]
    // 0x90b694: r2 = Null
    //     0x90b694: mov             x2, NULL
    // 0x90b698: r0 = AllocateClosure()
    //     0x90b698: bl              #0xec1630  ; AllocateClosureStub
    // 0x90b69c: r16 = <Fraction>
    //     0x90b69c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c5b8] TypeArguments: <Fraction>
    //     0x90b6a0: ldr             x16, [x16, #0x5b8]
    // 0x90b6a4: ldur            lr, [fp, #-0x18]
    // 0x90b6a8: stp             lr, x16, [SP, #8]
    // 0x90b6ac: str             x0, [SP]
    // 0x90b6b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x90b6b0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90b6b4: r0 = map()
    //     0x90b6b4: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0x90b6b8: mov             x1, x0
    // 0x90b6bc: r0 = FractionIterableExt.lcm()
    //     0x90b6bc: bl              #0x90a054  ; [package:waris/src/utils.dart] ::FractionIterableExt.lcm
    // 0x90b6c0: ldur            x1, [fp, #-0x10]
    // 0x90b6c4: ArrayStore: r1[0] = r0  ; List_8
    //     0x90b6c4: stur            x0, [x1, #0x17]
    // 0x90b6c8: StoreField: r1->field_1f = r0
    //     0x90b6c8: stur            x0, [x1, #0x1f]
    // 0x90b6cc: r0 = Null
    //     0x90b6cc: mov             x0, NULL
    // 0x90b6d0: LeaveFrame
    //     0x90b6d0: mov             SP, fp
    //     0x90b6d4: ldp             fp, lr, [SP], #0x10
    // 0x90b6d8: ret
    //     0x90b6d8: ret             
    // 0x90b6dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b6dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b6e0: b               #0x90b630
  }
  _ _calculateShares(/* No info */) {
    // ** addr: 0x90b6e4, size: 0x88
    // 0x90b6e4: EnterFrame
    //     0x90b6e4: stp             fp, lr, [SP, #-0x10]!
    //     0x90b6e8: mov             fp, SP
    // 0x90b6ec: AllocStack(0x10)
    //     0x90b6ec: sub             SP, SP, #0x10
    // 0x90b6f0: SetupParameters(Waris this /* r1 => r1, fp-0x8 */)
    //     0x90b6f0: stur            x1, [fp, #-8]
    // 0x90b6f4: CheckStackOverflow
    //     0x90b6f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b6f8: cmp             SP, x16
    //     0x90b6fc: b.ls            #0x90b764
    // 0x90b700: r1 = 1
    //     0x90b700: movz            x1, #0x1
    // 0x90b704: r0 = AllocateContext()
    //     0x90b704: bl              #0xec126c  ; AllocateContextStub
    // 0x90b708: mov             x1, x0
    // 0x90b70c: ldur            x0, [fp, #-8]
    // 0x90b710: StoreField: r1->field_f = r0
    //     0x90b710: stur            w0, [x1, #0xf]
    // 0x90b714: LoadField: r3 = r0->field_7
    //     0x90b714: ldur            w3, [x0, #7]
    // 0x90b718: DecompressPointer r3
    //     0x90b718: add             x3, x3, HEAP, lsl #32
    // 0x90b71c: mov             x2, x1
    // 0x90b720: stur            x3, [fp, #-0x10]
    // 0x90b724: r1 = Function '<anonymous closure>':.
    //     0x90b724: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6a8] AnonymousClosure: (0x90b76c), in [package:waris/src/waris.dart] Waris::_calculateShares (0x90b6e4)
    //     0x90b728: ldr             x1, [x1, #0x6a8]
    // 0x90b72c: r0 = AllocateClosure()
    //     0x90b72c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90b730: ldur            x1, [fp, #-0x10]
    // 0x90b734: r2 = LoadClassIdInstr(r1)
    //     0x90b734: ldur            x2, [x1, #-1]
    //     0x90b738: ubfx            x2, x2, #0xc, #0x14
    // 0x90b73c: mov             x16, x0
    // 0x90b740: mov             x0, x2
    // 0x90b744: mov             x2, x16
    // 0x90b748: r0 = GDT[cid_x0 + 0x64e]()
    //     0x90b748: add             lr, x0, #0x64e
    //     0x90b74c: ldr             lr, [x21, lr, lsl #3]
    //     0x90b750: blr             lr
    // 0x90b754: r0 = Null
    //     0x90b754: mov             x0, NULL
    // 0x90b758: LeaveFrame
    //     0x90b758: mov             SP, fp
    //     0x90b75c: ldp             fp, lr, [SP], #0x10
    // 0x90b760: ret
    //     0x90b760: ret             
    // 0x90b764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b764: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b768: b               #0x90b700
  }
  [closure] void <anonymous closure>(dynamic, Heir, int) {
    // ** addr: 0x90b76c, size: 0x170
    // 0x90b76c: EnterFrame
    //     0x90b76c: stp             fp, lr, [SP, #-0x10]!
    //     0x90b770: mov             fp, SP
    // 0x90b774: AllocStack(0x50)
    //     0x90b774: sub             SP, SP, #0x50
    // 0x90b778: SetupParameters()
    //     0x90b778: ldr             x0, [fp, #0x20]
    //     0x90b77c: ldur            w1, [x0, #0x17]
    //     0x90b780: add             x1, x1, HEAP, lsl #32
    //     0x90b784: stur            x1, [fp, #-8]
    // 0x90b788: CheckStackOverflow
    //     0x90b788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90b78c: cmp             SP, x16
    //     0x90b790: b.ls            #0x90b8d4
    // 0x90b794: r1 = 1
    //     0x90b794: movz            x1, #0x1
    // 0x90b798: r0 = AllocateContext()
    //     0x90b798: bl              #0xec126c  ; AllocateContextStub
    // 0x90b79c: mov             x2, x0
    // 0x90b7a0: ldur            x0, [fp, #-8]
    // 0x90b7a4: stur            x2, [fp, #-0x10]
    // 0x90b7a8: StoreField: r2->field_b = r0
    //     0x90b7a8: stur            w0, [x2, #0xb]
    // 0x90b7ac: ldr             x1, [fp, #0x18]
    // 0x90b7b0: StoreField: r2->field_f = r1
    //     0x90b7b0: stur            w1, [x2, #0xf]
    // 0x90b7b4: r0 = of()
    //     0x90b7b4: bl              #0x90b91c  ; [package:waris/src/shares.dart] Shares::of
    // 0x90b7b8: mov             x1, x0
    // 0x90b7bc: ldur            x0, [fp, #-8]
    // 0x90b7c0: LoadField: r2 = r0->field_f
    //     0x90b7c0: ldur            w2, [x0, #0xf]
    // 0x90b7c4: DecompressPointer r2
    //     0x90b7c4: add             x2, x2, HEAP, lsl #32
    // 0x90b7c8: LoadField: r3 = r2->field_7
    //     0x90b7c8: ldur            w3, [x2, #7]
    // 0x90b7cc: DecompressPointer r3
    //     0x90b7cc: add             x3, x3, HEAP, lsl #32
    // 0x90b7d0: mov             x2, x3
    // 0x90b7d4: r0 = ShareConditionExt.sharedWith()
    //     0x90b7d4: bl              #0x90b8dc  ; [package:waris/src/shares.dart] ::ShareConditionExt.sharedWith
    // 0x90b7d8: mov             x1, x0
    // 0x90b7dc: ldur            x0, [fp, #-8]
    // 0x90b7e0: stur            x1, [fp, #-0x28]
    // 0x90b7e4: LoadField: r2 = r0->field_f
    //     0x90b7e4: ldur            w2, [x0, #0xf]
    // 0x90b7e8: DecompressPointer r2
    //     0x90b7e8: add             x2, x2, HEAP, lsl #32
    // 0x90b7ec: LoadField: r0 = r2->field_b
    //     0x90b7ec: ldur            w0, [x2, #0xb]
    // 0x90b7f0: DecompressPointer r0
    //     0x90b7f0: add             x0, x0, HEAP, lsl #32
    // 0x90b7f4: ldur            x2, [fp, #-0x10]
    // 0x90b7f8: stur            x0, [fp, #-0x20]
    // 0x90b7fc: LoadField: r3 = r2->field_f
    //     0x90b7fc: ldur            w3, [x2, #0xf]
    // 0x90b800: DecompressPointer r3
    //     0x90b800: add             x3, x3, HEAP, lsl #32
    // 0x90b804: ldr             x4, [fp, #0x10]
    // 0x90b808: stur            x3, [fp, #-8]
    // 0x90b80c: r5 = LoadInt32Instr(r4)
    //     0x90b80c: sbfx            x5, x4, #1, #0x1f
    //     0x90b810: tbz             w4, #0, #0x90b818
    //     0x90b814: ldur            x5, [x4, #7]
    // 0x90b818: stur            x5, [fp, #-0x18]
    // 0x90b81c: r0 = Result()
    //     0x90b81c: bl              #0x907e84  ; AllocateResultStub -> Result (size=0x20)
    // 0x90b820: mov             x1, x0
    // 0x90b824: ldur            x0, [fp, #-0x18]
    // 0x90b828: StoreField: r1->field_7 = r0
    //     0x90b828: stur            x0, [x1, #7]
    // 0x90b82c: ldur            x0, [fp, #-0x28]
    // 0x90b830: StoreField: r1->field_f = r0
    //     0x90b830: stur            w0, [x1, #0xf]
    // 0x90b834: StoreField: r1->field_13 = rZR
    //     0x90b834: stur            xzr, [x1, #0x13]
    // 0x90b838: StoreField: r1->field_1b = rZR
    //     0x90b838: stur            wzr, [x1, #0x1b]
    // 0x90b83c: mov             x3, x1
    // 0x90b840: ldur            x1, [fp, #-0x20]
    // 0x90b844: ldur            x2, [fp, #-8]
    // 0x90b848: r0 = []=()
    //     0x90b848: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90b84c: ldur            x2, [fp, #-0x10]
    // 0x90b850: r1 = Function '<anonymous closure>':.
    //     0x90b850: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6b0] AnonymousClosure: (0x90eac0), in [package:waris/src/waris.dart] Waris::_calculateShares (0x90b6e4)
    //     0x90b854: ldr             x1, [x1, #0x6b0]
    // 0x90b858: r0 = AllocateClosure()
    //     0x90b858: bl              #0xec1630  ; AllocateClosureStub
    // 0x90b85c: ldur            x2, [fp, #-0x10]
    // 0x90b860: r1 = Function '<anonymous closure>':.
    //     0x90b860: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6b8] AnonymousClosure: (0x90ea20), in [package:waris/src/waris.dart] Waris::_calculateShares (0x90b6e4)
    //     0x90b864: ldr             x1, [x1, #0x6b8]
    // 0x90b868: stur            x0, [fp, #-8]
    // 0x90b86c: r0 = AllocateClosure()
    //     0x90b86c: bl              #0xec1630  ; AllocateClosureStub
    // 0x90b870: r1 = Function '<anonymous closure>':.
    //     0x90b870: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c6c0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x90b874: ldr             x1, [x1, #0x6c0]
    // 0x90b878: r2 = Null
    //     0x90b878: mov             x2, NULL
    // 0x90b87c: stur            x0, [fp, #-0x10]
    // 0x90b880: r0 = AllocateClosure()
    //     0x90b880: bl              #0xec1630  ; AllocateClosureStub
    // 0x90b884: mov             x1, x0
    // 0x90b888: ldur            x0, [fp, #-0x28]
    // 0x90b88c: r2 = LoadClassIdInstr(r0)
    //     0x90b88c: ldur            x2, [x0, #-1]
    //     0x90b890: ubfx            x2, x2, #0xc, #0x14
    // 0x90b894: r16 = <void?>
    //     0x90b894: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x90b898: stp             x0, x16, [SP, #0x18]
    // 0x90b89c: ldur            x16, [fp, #-8]
    // 0x90b8a0: stp             x16, x1, [SP, #8]
    // 0x90b8a4: ldur            x16, [fp, #-0x10]
    // 0x90b8a8: str             x16, [SP]
    // 0x90b8ac: mov             x0, x2
    // 0x90b8b0: r4 = const [0x1, 0x4, 0x4, 0x2, ashobah, 0x3, furudh, 0x2, null]
    //     0x90b8b0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c550] List(9) [0x1, 0x4, 0x4, 0x2, "ashobah", 0x3, "furudh", 0x2, Null]
    //     0x90b8b4: ldr             x4, [x4, #0x550]
    // 0x90b8b8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x90b8b8: sub             lr, x0, #1, lsl #12
    //     0x90b8bc: ldr             lr, [x21, lr, lsl #3]
    //     0x90b8c0: blr             lr
    // 0x90b8c4: r0 = Null
    //     0x90b8c4: mov             x0, NULL
    // 0x90b8c8: LeaveFrame
    //     0x90b8c8: mov             SP, fp
    //     0x90b8cc: ldp             fp, lr, [SP], #0x10
    // 0x90b8d0: ret
    //     0x90b8d0: ret             
    // 0x90b8d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90b8d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90b8d8: b               #0x90b794
  }
  [closure] void <anonymous closure>(dynamic, Ashobah) {
    // ** addr: 0x90ea20, size: 0xa0
    // 0x90ea20: EnterFrame
    //     0x90ea20: stp             fp, lr, [SP, #-0x10]!
    //     0x90ea24: mov             fp, SP
    // 0x90ea28: AllocStack(0x20)
    //     0x90ea28: sub             SP, SP, #0x20
    // 0x90ea2c: SetupParameters()
    //     0x90ea2c: ldr             x0, [fp, #0x18]
    //     0x90ea30: ldur            w1, [x0, #0x17]
    //     0x90ea34: add             x1, x1, HEAP, lsl #32
    // 0x90ea38: CheckStackOverflow
    //     0x90ea38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ea3c: cmp             SP, x16
    //     0x90ea40: b.ls            #0x90eab8
    // 0x90ea44: LoadField: r0 = r1->field_b
    //     0x90ea44: ldur            w0, [x1, #0xb]
    // 0x90ea48: DecompressPointer r0
    //     0x90ea48: add             x0, x0, HEAP, lsl #32
    // 0x90ea4c: LoadField: r2 = r0->field_f
    //     0x90ea4c: ldur            w2, [x0, #0xf]
    // 0x90ea50: DecompressPointer r2
    //     0x90ea50: add             x2, x2, HEAP, lsl #32
    // 0x90ea54: LoadField: r0 = r2->field_13
    //     0x90ea54: ldur            w0, [x2, #0x13]
    // 0x90ea58: DecompressPointer r0
    //     0x90ea58: add             x0, x0, HEAP, lsl #32
    // 0x90ea5c: stur            x0, [fp, #-0x10]
    // 0x90ea60: LoadField: r3 = r1->field_f
    //     0x90ea60: ldur            w3, [x1, #0xf]
    // 0x90ea64: DecompressPointer r3
    //     0x90ea64: add             x3, x3, HEAP, lsl #32
    // 0x90ea68: stur            x3, [fp, #-8]
    // 0x90ea6c: r1 = Null
    //     0x90ea6c: mov             x1, NULL
    // 0x90ea70: r2 = 4
    //     0x90ea70: movz            x2, #0x4
    // 0x90ea74: r0 = AllocateArray()
    //     0x90ea74: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90ea78: mov             x1, x0
    // 0x90ea7c: ldur            x0, [fp, #-8]
    // 0x90ea80: StoreField: r1->field_f = r0
    //     0x90ea80: stur            w0, [x1, #0xf]
    // 0x90ea84: ldr             x0, [fp, #0x10]
    // 0x90ea88: StoreField: r1->field_13 = r0
    //     0x90ea88: stur            w0, [x1, #0x13]
    // 0x90ea8c: r16 = <Heir, Ashobah>
    //     0x90ea8c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28448] TypeArguments: <Heir, Ashobah>
    //     0x90ea90: ldr             x16, [x16, #0x448]
    // 0x90ea94: stp             x1, x16, [SP]
    // 0x90ea98: r0 = Map._fromLiteral()
    //     0x90ea98: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90ea9c: ldur            x1, [fp, #-0x10]
    // 0x90eaa0: mov             x2, x0
    // 0x90eaa4: r0 = addAll()
    //     0x90eaa4: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x90eaa8: r0 = Null
    //     0x90eaa8: mov             x0, NULL
    // 0x90eaac: LeaveFrame
    //     0x90eaac: mov             SP, fp
    //     0x90eab0: ldp             fp, lr, [SP], #0x10
    // 0x90eab4: ret
    //     0x90eab4: ret             
    // 0x90eab8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90eab8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90eabc: b               #0x90ea44
  }
  [closure] void <anonymous closure>(dynamic, Furudh) {
    // ** addr: 0x90eac0, size: 0xa0
    // 0x90eac0: EnterFrame
    //     0x90eac0: stp             fp, lr, [SP, #-0x10]!
    //     0x90eac4: mov             fp, SP
    // 0x90eac8: AllocStack(0x20)
    //     0x90eac8: sub             SP, SP, #0x20
    // 0x90eacc: SetupParameters()
    //     0x90eacc: ldr             x0, [fp, #0x18]
    //     0x90ead0: ldur            w1, [x0, #0x17]
    //     0x90ead4: add             x1, x1, HEAP, lsl #32
    // 0x90ead8: CheckStackOverflow
    //     0x90ead8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90eadc: cmp             SP, x16
    //     0x90eae0: b.ls            #0x90eb58
    // 0x90eae4: LoadField: r0 = r1->field_b
    //     0x90eae4: ldur            w0, [x1, #0xb]
    // 0x90eae8: DecompressPointer r0
    //     0x90eae8: add             x0, x0, HEAP, lsl #32
    // 0x90eaec: LoadField: r2 = r0->field_f
    //     0x90eaec: ldur            w2, [x0, #0xf]
    // 0x90eaf0: DecompressPointer r2
    //     0x90eaf0: add             x2, x2, HEAP, lsl #32
    // 0x90eaf4: LoadField: r0 = r2->field_f
    //     0x90eaf4: ldur            w0, [x2, #0xf]
    // 0x90eaf8: DecompressPointer r0
    //     0x90eaf8: add             x0, x0, HEAP, lsl #32
    // 0x90eafc: stur            x0, [fp, #-0x10]
    // 0x90eb00: LoadField: r3 = r1->field_f
    //     0x90eb00: ldur            w3, [x1, #0xf]
    // 0x90eb04: DecompressPointer r3
    //     0x90eb04: add             x3, x3, HEAP, lsl #32
    // 0x90eb08: stur            x3, [fp, #-8]
    // 0x90eb0c: r1 = Null
    //     0x90eb0c: mov             x1, NULL
    // 0x90eb10: r2 = 4
    //     0x90eb10: movz            x2, #0x4
    // 0x90eb14: r0 = AllocateArray()
    //     0x90eb14: bl              #0xec22fc  ; AllocateArrayStub
    // 0x90eb18: mov             x1, x0
    // 0x90eb1c: ldur            x0, [fp, #-8]
    // 0x90eb20: StoreField: r1->field_f = r0
    //     0x90eb20: stur            w0, [x1, #0xf]
    // 0x90eb24: ldr             x0, [fp, #0x10]
    // 0x90eb28: StoreField: r1->field_13 = r0
    //     0x90eb28: stur            w0, [x1, #0x13]
    // 0x90eb2c: r16 = <Heir, Furudh>
    //     0x90eb2c: add             x16, PP, #0x28, lsl #12  ; [pp+0x28440] TypeArguments: <Heir, Furudh>
    //     0x90eb30: ldr             x16, [x16, #0x440]
    // 0x90eb34: stp             x1, x16, [SP]
    // 0x90eb38: r0 = Map._fromLiteral()
    //     0x90eb38: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90eb3c: ldur            x1, [fp, #-0x10]
    // 0x90eb40: mov             x2, x0
    // 0x90eb44: r0 = addAll()
    //     0x90eb44: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x90eb48: r0 = Null
    //     0x90eb48: mov             x0, NULL
    // 0x90eb4c: LeaveFrame
    //     0x90eb4c: mov             SP, fp
    //     0x90eb50: ldp             fp, lr, [SP], #0x10
    // 0x90eb54: ret
    //     0x90eb54: ret             
    // 0x90eb58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90eb58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90eb5c: b               #0x90eae4
  }
  _ distribute(/* No info */) {
    // ** addr: 0x90ec1c, size: 0x90
    // 0x90ec1c: EnterFrame
    //     0x90ec1c: stp             fp, lr, [SP, #-0x10]!
    //     0x90ec20: mov             fp, SP
    // 0x90ec24: AllocStack(0x18)
    //     0x90ec24: sub             SP, SP, #0x18
    // 0x90ec28: SetupParameters(Waris this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x90ec28: stur            x1, [fp, #-8]
    //     0x90ec2c: stur            x2, [fp, #-0x10]
    // 0x90ec30: CheckStackOverflow
    //     0x90ec30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ec34: cmp             SP, x16
    //     0x90ec38: b.ls            #0x90eca4
    // 0x90ec3c: r1 = 2
    //     0x90ec3c: movz            x1, #0x2
    // 0x90ec40: r0 = AllocateContext()
    //     0x90ec40: bl              #0xec126c  ; AllocateContextStub
    // 0x90ec44: mov             x3, x0
    // 0x90ec48: ldur            x2, [fp, #-8]
    // 0x90ec4c: StoreField: r3->field_f = r2
    //     0x90ec4c: stur            w2, [x3, #0xf]
    // 0x90ec50: ldur            x4, [fp, #-0x10]
    // 0x90ec54: r0 = BoxInt64Instr(r4)
    //     0x90ec54: sbfiz           x0, x4, #1, #0x1f
    //     0x90ec58: cmp             x4, x0, asr #1
    //     0x90ec5c: b.eq            #0x90ec68
    //     0x90ec60: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x90ec64: stur            x4, [x0, #7]
    // 0x90ec68: StoreField: r3->field_13 = r0
    //     0x90ec68: stur            w0, [x3, #0x13]
    // 0x90ec6c: LoadField: r0 = r2->field_b
    //     0x90ec6c: ldur            w0, [x2, #0xb]
    // 0x90ec70: DecompressPointer r0
    //     0x90ec70: add             x0, x0, HEAP, lsl #32
    // 0x90ec74: mov             x2, x3
    // 0x90ec78: stur            x0, [fp, #-0x18]
    // 0x90ec7c: r1 = Function '<anonymous closure>':.
    //     0x90ec7c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c500] AnonymousClosure: (0x90ecac), in [package:waris/src/waris.dart] Waris::distribute (0x90ec1c)
    //     0x90ec80: ldr             x1, [x1, #0x500]
    // 0x90ec84: r0 = AllocateClosure()
    //     0x90ec84: bl              #0xec1630  ; AllocateClosureStub
    // 0x90ec88: ldur            x1, [fp, #-0x18]
    // 0x90ec8c: mov             x2, x0
    // 0x90ec90: r0 = forEach()
    //     0x90ec90: bl              #0xd759f0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x90ec94: r0 = Null
    //     0x90ec94: mov             x0, NULL
    // 0x90ec98: LeaveFrame
    //     0x90ec98: mov             SP, fp
    //     0x90ec9c: ldp             fp, lr, [SP], #0x10
    // 0x90eca0: ret
    //     0x90eca0: ret             
    // 0x90eca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90eca4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90eca8: b               #0x90ec3c
  }
  [closure] void <anonymous closure>(dynamic, Heir, Result) {
    // ** addr: 0x90ecac, size: 0x110
    // 0x90ecac: EnterFrame
    //     0x90ecac: stp             fp, lr, [SP, #-0x10]!
    //     0x90ecb0: mov             fp, SP
    // 0x90ecb4: AllocStack(0x28)
    //     0x90ecb4: sub             SP, SP, #0x28
    // 0x90ecb8: SetupParameters()
    //     0x90ecb8: ldr             x0, [fp, #0x20]
    //     0x90ecbc: ldur            w3, [x0, #0x17]
    //     0x90ecc0: add             x3, x3, HEAP, lsl #32
    //     0x90ecc4: stur            x3, [fp, #-0x10]
    // 0x90ecc8: CheckStackOverflow
    //     0x90ecc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90eccc: cmp             SP, x16
    //     0x90ecd0: b.ls            #0x90eda4
    // 0x90ecd4: LoadField: r1 = r3->field_f
    //     0x90ecd4: ldur            w1, [x3, #0xf]
    // 0x90ecd8: DecompressPointer r1
    //     0x90ecd8: add             x1, x1, HEAP, lsl #32
    // 0x90ecdc: LoadField: r0 = r1->field_b
    //     0x90ecdc: ldur            w0, [x1, #0xb]
    // 0x90ece0: DecompressPointer r0
    //     0x90ece0: add             x0, x0, HEAP, lsl #32
    // 0x90ece4: ldr             x2, [fp, #0x18]
    // 0x90ece8: stur            x0, [fp, #-8]
    // 0x90ecec: r0 = get()
    //     0x90ecec: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x90ecf0: mov             x3, x0
    // 0x90ecf4: ldur            x0, [fp, #-0x10]
    // 0x90ecf8: stur            x3, [fp, #-0x18]
    // 0x90ecfc: LoadField: r1 = r0->field_13
    //     0x90ecfc: ldur            w1, [x0, #0x13]
    // 0x90ed00: DecompressPointer r1
    //     0x90ed00: add             x1, x1, HEAP, lsl #32
    // 0x90ed04: LoadField: r2 = r0->field_f
    //     0x90ed04: ldur            w2, [x0, #0xf]
    // 0x90ed08: DecompressPointer r2
    //     0x90ed08: add             x2, x2, HEAP, lsl #32
    // 0x90ed0c: LoadField: r0 = r2->field_1f
    //     0x90ed0c: ldur            x0, [x2, #0x1f]
    // 0x90ed10: r4 = LoadInt32Instr(r1)
    //     0x90ed10: sbfx            x4, x1, #1, #0x1f
    //     0x90ed14: tbz             w1, #0, #0x90ed1c
    //     0x90ed18: ldur            x4, [x1, #7]
    // 0x90ed1c: scvtf           d0, x4
    // 0x90ed20: scvtf           d1, x0
    // 0x90ed24: fdiv            d2, d0, d1
    // 0x90ed28: mov             x1, x2
    // 0x90ed2c: ldr             x2, [fp, #0x18]
    // 0x90ed30: stur            d2, [fp, #-0x20]
    // 0x90ed34: r0 = get()
    //     0x90ed34: bl              #0x907e90  ; [package:waris/src/waris.dart] Waris::get
    // 0x90ed38: LoadField: r1 = r0->field_13
    //     0x90ed38: ldur            x1, [x0, #0x13]
    // 0x90ed3c: scvtf           d0, x1
    // 0x90ed40: ldur            d1, [fp, #-0x20]
    // 0x90ed44: fmul            d2, d1, d0
    // 0x90ed48: r0 = inline_Allocate_Double()
    //     0x90ed48: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x90ed4c: add             x0, x0, #0x10
    //     0x90ed50: cmp             x1, x0
    //     0x90ed54: b.ls            #0x90edac
    //     0x90ed58: str             x0, [THR, #0x50]  ; THR::top
    //     0x90ed5c: sub             x0, x0, #0xf
    //     0x90ed60: movz            x1, #0xe15c
    //     0x90ed64: movk            x1, #0x3, lsl #16
    //     0x90ed68: stur            x1, [x0, #-1]
    // 0x90ed6c: StoreField: r0->field_7 = d2
    //     0x90ed6c: stur            d2, [x0, #7]
    // 0x90ed70: str             x0, [SP]
    // 0x90ed74: ldur            x1, [fp, #-0x18]
    // 0x90ed78: r4 = const [0, 0x2, 0x1, 0x1, amount, 0x1, null]
    //     0x90ed78: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c508] List(7) [0, 0x2, 0x1, 0x1, "amount", 0x1, Null]
    //     0x90ed7c: ldr             x4, [x4, #0x508]
    // 0x90ed80: r0 = copyWith()
    //     0x90ed80: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0x90ed84: ldur            x1, [fp, #-8]
    // 0x90ed88: ldr             x2, [fp, #0x18]
    // 0x90ed8c: mov             x3, x0
    // 0x90ed90: r0 = []=()
    //     0x90ed90: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x90ed94: r0 = Null
    //     0x90ed94: mov             x0, NULL
    // 0x90ed98: LeaveFrame
    //     0x90ed98: mov             SP, fp
    //     0x90ed9c: ldp             fp, lr, [SP], #0x10
    // 0x90eda0: ret
    //     0x90eda0: ret             
    // 0x90eda4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90eda4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90eda8: b               #0x90ecd4
    // 0x90edac: SaveReg d2
    //     0x90edac: str             q2, [SP, #-0x10]!
    // 0x90edb0: r0 = AllocateDouble()
    //     0x90edb0: bl              #0xec2254  ; AllocateDoubleStub
    // 0x90edb4: RestoreReg d2
    //     0x90edb4: ldr             q2, [SP], #0x10
    // 0x90edb8: b               #0x90ed6c
  }
  _ Waris(/* No info */) {
    // ** addr: 0x90ee2c, size: 0x11c
    // 0x90ee2c: EnterFrame
    //     0x90ee2c: stp             fp, lr, [SP, #-0x10]!
    //     0x90ee30: mov             fp, SP
    // 0x90ee34: AllocStack(0x20)
    //     0x90ee34: sub             SP, SP, #0x20
    // 0x90ee38: r0 = false
    //     0x90ee38: add             x0, NULL, #0x30  ; false
    // 0x90ee3c: stur            x1, [fp, #-8]
    // 0x90ee40: mov             x16, x2
    // 0x90ee44: mov             x2, x1
    // 0x90ee48: mov             x1, x16
    // 0x90ee4c: stur            x1, [fp, #-0x10]
    // 0x90ee50: CheckStackOverflow
    //     0x90ee50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ee54: cmp             SP, x16
    //     0x90ee58: b.ls            #0x90ef40
    // 0x90ee5c: ArrayStore: r2[0] = rZR  ; List_8
    //     0x90ee5c: stur            xzr, [x2, #0x17]
    // 0x90ee60: StoreField: r2->field_1f = rZR
    //     0x90ee60: stur            xzr, [x2, #0x1f]
    // 0x90ee64: StoreField: r2->field_27 = rZR
    //     0x90ee64: stur            xzr, [x2, #0x27]
    // 0x90ee68: StoreField: r2->field_2f = r0
    //     0x90ee68: stur            w0, [x2, #0x2f]
    // 0x90ee6c: StoreField: r2->field_33 = r0
    //     0x90ee6c: stur            w0, [x2, #0x33]
    // 0x90ee70: StoreField: r2->field_37 = r0
    //     0x90ee70: stur            w0, [x2, #0x37]
    // 0x90ee74: r16 = <Heir, Result>
    //     0x90ee74: add             x16, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0x90ee78: ldr             x16, [x16, #0x438]
    // 0x90ee7c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x90ee80: stp             lr, x16, [SP]
    // 0x90ee84: r0 = Map._fromLiteral()
    //     0x90ee84: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90ee88: ldur            x1, [fp, #-8]
    // 0x90ee8c: StoreField: r1->field_b = r0
    //     0x90ee8c: stur            w0, [x1, #0xb]
    //     0x90ee90: ldurb           w16, [x1, #-1]
    //     0x90ee94: ldurb           w17, [x0, #-1]
    //     0x90ee98: and             x16, x17, x16, lsr #2
    //     0x90ee9c: tst             x16, HEAP, lsr #32
    //     0x90eea0: b.eq            #0x90eea8
    //     0x90eea4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90eea8: r16 = <Heir, Furudh>
    //     0x90eea8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28440] TypeArguments: <Heir, Furudh>
    //     0x90eeac: ldr             x16, [x16, #0x440]
    // 0x90eeb0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x90eeb4: stp             lr, x16, [SP]
    // 0x90eeb8: r0 = Map._fromLiteral()
    //     0x90eeb8: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90eebc: ldur            x1, [fp, #-8]
    // 0x90eec0: StoreField: r1->field_f = r0
    //     0x90eec0: stur            w0, [x1, #0xf]
    //     0x90eec4: ldurb           w16, [x1, #-1]
    //     0x90eec8: ldurb           w17, [x0, #-1]
    //     0x90eecc: and             x16, x17, x16, lsr #2
    //     0x90eed0: tst             x16, HEAP, lsr #32
    //     0x90eed4: b.eq            #0x90eedc
    //     0x90eed8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90eedc: r16 = <Heir, Ashobah>
    //     0x90eedc: add             x16, PP, #0x28, lsl #12  ; [pp+0x28448] TypeArguments: <Heir, Ashobah>
    //     0x90eee0: ldr             x16, [x16, #0x448]
    // 0x90eee4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x90eee8: stp             lr, x16, [SP]
    // 0x90eeec: r0 = Map._fromLiteral()
    //     0x90eeec: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x90eef0: ldur            x1, [fp, #-8]
    // 0x90eef4: StoreField: r1->field_13 = r0
    //     0x90eef4: stur            w0, [x1, #0x13]
    //     0x90eef8: ldurb           w16, [x1, #-1]
    //     0x90eefc: ldurb           w17, [x0, #-1]
    //     0x90ef00: and             x16, x17, x16, lsr #2
    //     0x90ef04: tst             x16, HEAP, lsr #32
    //     0x90ef08: b.eq            #0x90ef10
    //     0x90ef0c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90ef10: ldur            x0, [fp, #-0x10]
    // 0x90ef14: StoreField: r1->field_7 = r0
    //     0x90ef14: stur            w0, [x1, #7]
    //     0x90ef18: ldurb           w16, [x1, #-1]
    //     0x90ef1c: ldurb           w17, [x0, #-1]
    //     0x90ef20: and             x16, x17, x16, lsr #2
    //     0x90ef24: tst             x16, HEAP, lsr #32
    //     0x90ef28: b.eq            #0x90ef30
    //     0x90ef2c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x90ef30: r0 = Null
    //     0x90ef30: mov             x0, NULL
    // 0x90ef34: LeaveFrame
    //     0x90ef34: mov             SP, fp
    //     0x90ef38: ldp             fp, lr, [SP], #0x10
    // 0x90ef3c: ret
    //     0x90ef3c: ret             
    // 0x90ef40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90ef40: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90ef44: b               #0x90ee5c
  }
  get _ unqualified(/* No info */) {
    // ** addr: 0xb5dbfc, size: 0xb4
    // 0xb5dbfc: EnterFrame
    //     0xb5dbfc: stp             fp, lr, [SP, #-0x10]!
    //     0xb5dc00: mov             fp, SP
    // 0xb5dc04: AllocStack(0x28)
    //     0xb5dc04: sub             SP, SP, #0x28
    // 0xb5dc08: SetupParameters(Waris this /* r1 => r1, fp-0x8 */)
    //     0xb5dc08: stur            x1, [fp, #-8]
    // 0xb5dc0c: CheckStackOverflow
    //     0xb5dc0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5dc10: cmp             SP, x16
    //     0xb5dc14: b.ls            #0xb5dca8
    // 0xb5dc18: r1 = 1
    //     0xb5dc18: movz            x1, #0x1
    // 0xb5dc1c: r0 = AllocateContext()
    //     0xb5dc1c: bl              #0xec126c  ; AllocateContextStub
    // 0xb5dc20: mov             x2, x0
    // 0xb5dc24: ldur            x0, [fp, #-8]
    // 0xb5dc28: stur            x2, [fp, #-0x10]
    // 0xb5dc2c: StoreField: r2->field_f = r0
    //     0xb5dc2c: stur            w0, [x2, #0xf]
    // 0xb5dc30: LoadField: r1 = r0->field_b
    //     0xb5dc30: ldur            w1, [x0, #0xb]
    // 0xb5dc34: DecompressPointer r1
    //     0xb5dc34: add             x1, x1, HEAP, lsl #32
    // 0xb5dc38: r0 = entries()
    //     0xb5dc38: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0xb5dc3c: r1 = Function '<anonymous closure>':.
    //     0xb5dc3c: add             x1, PP, #0x28, lsl #12  ; [pp+0x28450] AnonymousClosure: (0xb5e030), in [package:waris/src/waris.dart] Waris::unqualified (0xb5dbfc)
    //     0xb5dc40: ldr             x1, [x1, #0x450]
    // 0xb5dc44: r2 = Null
    //     0xb5dc44: mov             x2, NULL
    // 0xb5dc48: stur            x0, [fp, #-8]
    // 0xb5dc4c: r0 = AllocateClosure()
    //     0xb5dc4c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb5dc50: ldur            x1, [fp, #-8]
    // 0xb5dc54: mov             x2, x0
    // 0xb5dc58: r0 = where()
    //     0xb5dc58: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xb5dc5c: ldur            x2, [fp, #-0x10]
    // 0xb5dc60: r1 = Function '<anonymous closure>':.
    //     0xb5dc60: add             x1, PP, #0x28, lsl #12  ; [pp+0x28458] AnonymousClosure: (0xb5dcb0), in [package:waris/src/waris.dart] Waris::unqualified (0xb5dbfc)
    //     0xb5dc64: ldr             x1, [x1, #0x458]
    // 0xb5dc68: stur            x0, [fp, #-8]
    // 0xb5dc6c: r0 = AllocateClosure()
    //     0xb5dc6c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb5dc70: r16 = <MapEntry<Heir, Result>>
    //     0xb5dc70: add             x16, PP, #0x28, lsl #12  ; [pp+0x28460] TypeArguments: <MapEntry<Heir, Result>>
    //     0xb5dc74: ldr             x16, [x16, #0x460]
    // 0xb5dc78: ldur            lr, [fp, #-8]
    // 0xb5dc7c: stp             lr, x16, [SP, #8]
    // 0xb5dc80: str             x0, [SP]
    // 0xb5dc84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb5dc84: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb5dc88: r0 = map()
    //     0xb5dc88: bl              #0x7abfa0  ; [dart:_internal] WhereIterable::map
    // 0xb5dc8c: mov             x2, x0
    // 0xb5dc90: r1 = <Heir, Result>
    //     0xb5dc90: add             x1, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0xb5dc94: ldr             x1, [x1, #0x438]
    // 0xb5dc98: r0 = Map.fromEntries()
    //     0xb5dc98: bl              #0x90934c  ; [dart:core] Map::Map.fromEntries
    // 0xb5dc9c: LeaveFrame
    //     0xb5dc9c: mov             SP, fp
    //     0xb5dca0: ldp             fp, lr, [SP], #0x10
    // 0xb5dca4: ret
    //     0xb5dca4: ret             
    // 0xb5dca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5dca8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5dcac: b               #0xb5dc18
  }
  [closure] MapEntry<Heir, Result> <anonymous closure>(dynamic, MapEntry<Heir, Result>) {
    // ** addr: 0xb5dcb0, size: 0x164
    // 0xb5dcb0: EnterFrame
    //     0xb5dcb0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5dcb4: mov             fp, SP
    // 0xb5dcb8: AllocStack(0x30)
    //     0xb5dcb8: sub             SP, SP, #0x30
    // 0xb5dcbc: SetupParameters()
    //     0xb5dcbc: ldr             x0, [fp, #0x18]
    //     0xb5dcc0: ldur            w3, [x0, #0x17]
    //     0xb5dcc4: add             x3, x3, HEAP, lsl #32
    //     0xb5dcc8: stur            x3, [fp, #-0x18]
    // 0xb5dccc: CheckStackOverflow
    //     0xb5dccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5dcd0: cmp             SP, x16
    //     0xb5dcd4: b.ls            #0xb5de08
    // 0xb5dcd8: ldr             x4, [fp, #0x10]
    // 0xb5dcdc: LoadField: r5 = r4->field_f
    //     0xb5dcdc: ldur            w5, [x4, #0xf]
    // 0xb5dce0: DecompressPointer r5
    //     0xb5dce0: add             x5, x5, HEAP, lsl #32
    // 0xb5dce4: stur            x5, [fp, #-0x10]
    // 0xb5dce8: cmp             w5, NULL
    // 0xb5dcec: b.eq            #0xb5de10
    // 0xb5dcf0: LoadField: r6 = r5->field_f
    //     0xb5dcf0: ldur            w6, [x5, #0xf]
    // 0xb5dcf4: DecompressPointer r6
    //     0xb5dcf4: add             x6, x6, HEAP, lsl #32
    // 0xb5dcf8: mov             x0, x6
    // 0xb5dcfc: stur            x6, [fp, #-8]
    // 0xb5dd00: r2 = Null
    //     0xb5dd00: mov             x2, NULL
    // 0xb5dd04: r1 = Null
    //     0xb5dd04: mov             x1, NULL
    // 0xb5dd08: r4 = LoadClassIdInstr(r0)
    //     0xb5dd08: ldur            x4, [x0, #-1]
    //     0xb5dd0c: ubfx            x4, x4, #0xc, #0x14
    // 0xb5dd10: cmp             x4, #0x184
    // 0xb5dd14: b.eq            #0xb5dd2c
    // 0xb5dd18: r8 = Mahjub
    //     0xb5dd18: add             x8, PP, #0x28, lsl #12  ; [pp+0x28468] Type: Mahjub
    //     0xb5dd1c: ldr             x8, [x8, #0x468]
    // 0xb5dd20: r3 = Null
    //     0xb5dd20: add             x3, PP, #0x28, lsl #12  ; [pp+0x28470] Null
    //     0xb5dd24: ldr             x3, [x3, #0x470]
    // 0xb5dd28: r0 = DefaultTypeTest()
    //     0xb5dd28: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xb5dd2c: ldr             x0, [fp, #0x10]
    // 0xb5dd30: LoadField: r2 = r0->field_b
    //     0xb5dd30: ldur            w2, [x0, #0xb]
    // 0xb5dd34: DecompressPointer r2
    //     0xb5dd34: add             x2, x2, HEAP, lsl #32
    // 0xb5dd38: ldur            x1, [fp, #-8]
    // 0xb5dd3c: stur            x2, [fp, #-0x20]
    // 0xb5dd40: r0 = heirs()
    //     0xb5dd40: bl              #0xb5de14  ; [package:waris/src/shares.dart] _$Mahjub::heirs
    // 0xb5dd44: ldur            x2, [fp, #-0x18]
    // 0xb5dd48: r1 = Function '<anonymous closure>':.
    //     0xb5dd48: add             x1, PP, #0x28, lsl #12  ; [pp+0x28480] AnonymousClosure: (0xb5df04), in [package:waris/src/waris.dart] Waris::unqualified (0xb5dbfc)
    //     0xb5dd4c: ldr             x1, [x1, #0x480]
    // 0xb5dd50: stur            x0, [fp, #-8]
    // 0xb5dd54: r0 = AllocateClosure()
    //     0xb5dd54: bl              #0xec1630  ; AllocateClosureStub
    // 0xb5dd58: ldur            x1, [fp, #-8]
    // 0xb5dd5c: mov             x2, x0
    // 0xb5dd60: r0 = where()
    //     0xb5dd60: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xb5dd64: LoadField: r1 = r0->field_7
    //     0xb5dd64: ldur            w1, [x0, #7]
    // 0xb5dd68: DecompressPointer r1
    //     0xb5dd68: add             x1, x1, HEAP, lsl #32
    // 0xb5dd6c: mov             x2, x0
    // 0xb5dd70: r0 = _GrowableList.of()
    //     0xb5dd70: bl              #0x60b56c  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xb5dd74: mov             x3, x0
    // 0xb5dd78: r2 = Null
    //     0xb5dd78: mov             x2, NULL
    // 0xb5dd7c: r1 = Null
    //     0xb5dd7c: mov             x1, NULL
    // 0xb5dd80: stur            x3, [fp, #-8]
    // 0xb5dd84: r8 = List<Heir>
    //     0xb5dd84: add             x8, PP, #0x28, lsl #12  ; [pp+0x28488] Type: List<Heir>
    //     0xb5dd88: ldr             x8, [x8, #0x488]
    // 0xb5dd8c: r3 = Null
    //     0xb5dd8c: add             x3, PP, #0x28, lsl #12  ; [pp+0x28490] Null
    //     0xb5dd90: ldr             x3, [x3, #0x490]
    // 0xb5dd94: r0 = List<Heir>()
    //     0xb5dd94: bl              #0xb5de5c  ; IsType_List<Heir>_Stub
    // 0xb5dd98: r0 = _$Mahjub()
    //     0xb5dd98: bl              #0x90c360  ; Allocate_$MahjubStub -> _$Mahjub (size=0xc)
    // 0xb5dd9c: mov             x1, x0
    // 0xb5dda0: ldur            x0, [fp, #-8]
    // 0xb5dda4: StoreField: r1->field_7 = r0
    //     0xb5dda4: stur            w0, [x1, #7]
    // 0xb5dda8: r16 = Closure: (_$Mahjub) => _$Mahjub from Function '_$identity@2491330188': static.
    //     0xb5dda8: add             x16, PP, #0x28, lsl #12  ; [pp+0x284a0] Closure: (_$Mahjub) => _$Mahjub from Function '_$identity@2491330188': static. (0x7e54fb8bd554)
    //     0xb5ddac: ldr             x16, [x16, #0x4a0]
    // 0xb5ddb0: stp             x1, x16, [SP]
    // 0xb5ddb4: r0 = Closure: (_$Mahjub) => _$Mahjub from Function '_$identity@2491330188': static.
    //     0xb5ddb4: add             x0, PP, #0x28, lsl #12  ; [pp+0x284a0] Closure: (_$Mahjub) => _$Mahjub from Function '_$identity@2491330188': static. (0x7e54fb8bd554)
    //     0xb5ddb8: ldr             x0, [x0, #0x4a0]
    // 0xb5ddbc: ClosureCall
    //     0xb5ddbc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb5ddc0: ldur            x2, [x0, #0x1f]
    //     0xb5ddc4: blr             x2
    // 0xb5ddc8: str             x0, [SP]
    // 0xb5ddcc: ldur            x1, [fp, #-0x10]
    // 0xb5ddd0: r4 = const [0, 0x2, 0x1, 0x1, share, 0x1, null]
    //     0xb5ddd0: add             x4, PP, #0x28, lsl #12  ; [pp+0x284a8] List(7) [0, 0x2, 0x1, 0x1, "share", 0x1, Null]
    //     0xb5ddd4: ldr             x4, [x4, #0x4a8]
    // 0xb5ddd8: r0 = copyWith()
    //     0xb5ddd8: bl              #0x907cf4  ; [package:waris/src/result.dart] Result::copyWith
    // 0xb5dddc: r1 = <Heir, Result>
    //     0xb5dddc: add             x1, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0xb5dde0: ldr             x1, [x1, #0x438]
    // 0xb5dde4: stur            x0, [fp, #-8]
    // 0xb5dde8: r0 = MapEntry()
    //     0xb5dde8: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0xb5ddec: ldur            x1, [fp, #-0x20]
    // 0xb5ddf0: StoreField: r0->field_b = r1
    //     0xb5ddf0: stur            w1, [x0, #0xb]
    // 0xb5ddf4: ldur            x1, [fp, #-8]
    // 0xb5ddf8: StoreField: r0->field_f = r1
    //     0xb5ddf8: stur            w1, [x0, #0xf]
    // 0xb5ddfc: LeaveFrame
    //     0xb5ddfc: mov             SP, fp
    //     0xb5de00: ldp             fp, lr, [SP], #0x10
    // 0xb5de04: ret
    //     0xb5de04: ret             
    // 0xb5de08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5de08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5de0c: b               #0xb5dcd8
    // 0xb5de10: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5de10: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, Heir) {
    // ** addr: 0xb5df04, size: 0x7c
    // 0xb5df04: EnterFrame
    //     0xb5df04: stp             fp, lr, [SP, #-0x10]!
    //     0xb5df08: mov             fp, SP
    // 0xb5df0c: AllocStack(0x8)
    //     0xb5df0c: sub             SP, SP, #8
    // 0xb5df10: SetupParameters()
    //     0xb5df10: ldr             x0, [fp, #0x18]
    //     0xb5df14: ldur            w1, [x0, #0x17]
    //     0xb5df18: add             x1, x1, HEAP, lsl #32
    // 0xb5df1c: CheckStackOverflow
    //     0xb5df1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5df20: cmp             SP, x16
    //     0xb5df24: b.ls            #0xb5df78
    // 0xb5df28: LoadField: r0 = r1->field_f
    //     0xb5df28: ldur            w0, [x1, #0xf]
    // 0xb5df2c: DecompressPointer r0
    //     0xb5df2c: add             x0, x0, HEAP, lsl #32
    // 0xb5df30: mov             x1, x0
    // 0xb5df34: r0 = qualified()
    //     0xb5df34: bl              #0xb5df80  ; [package:waris/src/waris.dart] Waris::qualified
    // 0xb5df38: stur            x0, [fp, #-8]
    // 0xb5df3c: LoadField: r1 = r0->field_7
    //     0xb5df3c: ldur            w1, [x0, #7]
    // 0xb5df40: DecompressPointer r1
    //     0xb5df40: add             x1, x1, HEAP, lsl #32
    // 0xb5df44: r0 = _CompactIterable()
    //     0xb5df44: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xb5df48: mov             x1, x0
    // 0xb5df4c: ldur            x0, [fp, #-8]
    // 0xb5df50: StoreField: r1->field_b = r0
    //     0xb5df50: stur            w0, [x1, #0xb]
    // 0xb5df54: r0 = -2
    //     0xb5df54: orr             x0, xzr, #0xfffffffffffffffe
    // 0xb5df58: StoreField: r1->field_f = r0
    //     0xb5df58: stur            x0, [x1, #0xf]
    // 0xb5df5c: r0 = 2
    //     0xb5df5c: movz            x0, #0x2
    // 0xb5df60: ArrayStore: r1[0] = r0  ; List_8
    //     0xb5df60: stur            x0, [x1, #0x17]
    // 0xb5df64: ldr             x2, [fp, #0x10]
    // 0xb5df68: r0 = contains()
    //     0xb5df68: bl              #0x7adb80  ; [dart:core] Iterable::contains
    // 0xb5df6c: LeaveFrame
    //     0xb5df6c: mov             SP, fp
    //     0xb5df70: ldp             fp, lr, [SP], #0x10
    // 0xb5df74: ret
    //     0xb5df74: ret             
    // 0xb5df78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5df78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5df7c: b               #0xb5df28
  }
  get _ qualified(/* No info */) {
    // ** addr: 0xb5df80, size: 0x6c
    // 0xb5df80: EnterFrame
    //     0xb5df80: stp             fp, lr, [SP, #-0x10]!
    //     0xb5df84: mov             fp, SP
    // 0xb5df88: AllocStack(0x8)
    //     0xb5df88: sub             SP, SP, #8
    // 0xb5df8c: CheckStackOverflow
    //     0xb5df8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5df90: cmp             SP, x16
    //     0xb5df94: b.ls            #0xb5dfe4
    // 0xb5df98: LoadField: r0 = r1->field_b
    //     0xb5df98: ldur            w0, [x1, #0xb]
    // 0xb5df9c: DecompressPointer r0
    //     0xb5df9c: add             x0, x0, HEAP, lsl #32
    // 0xb5dfa0: mov             x1, x0
    // 0xb5dfa4: r0 = entries()
    //     0xb5dfa4: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0xb5dfa8: r1 = Function '<anonymous closure>':.
    //     0xb5dfa8: add             x1, PP, #0x28, lsl #12  ; [pp+0x284b8] AnonymousClosure: (0xb5dfec), in [package:waris/src/waris.dart] Waris::qualified (0xb5df80)
    //     0xb5dfac: ldr             x1, [x1, #0x4b8]
    // 0xb5dfb0: r2 = Null
    //     0xb5dfb0: mov             x2, NULL
    // 0xb5dfb4: stur            x0, [fp, #-8]
    // 0xb5dfb8: r0 = AllocateClosure()
    //     0xb5dfb8: bl              #0xec1630  ; AllocateClosureStub
    // 0xb5dfbc: ldur            x1, [fp, #-8]
    // 0xb5dfc0: mov             x2, x0
    // 0xb5dfc4: r0 = where()
    //     0xb5dfc4: bl              #0x89be78  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0xb5dfc8: mov             x2, x0
    // 0xb5dfcc: r1 = <Heir, Result>
    //     0xb5dfcc: add             x1, PP, #0x28, lsl #12  ; [pp+0x28438] TypeArguments: <Heir, Result>
    //     0xb5dfd0: ldr             x1, [x1, #0x438]
    // 0xb5dfd4: r0 = Map.fromEntries()
    //     0xb5dfd4: bl              #0x90934c  ; [dart:core] Map::Map.fromEntries
    // 0xb5dfd8: LeaveFrame
    //     0xb5dfd8: mov             SP, fp
    //     0xb5dfdc: ldp             fp, lr, [SP], #0x10
    // 0xb5dfe0: ret
    //     0xb5dfe0: ret             
    // 0xb5dfe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5dfe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5dfe8: b               #0xb5df98
  }
  [closure] bool <anonymous closure>(dynamic, MapEntry<Heir, Result>) {
    // ** addr: 0xb5dfec, size: 0x44
    // 0xb5dfec: ldr             x1, [SP]
    // 0xb5dff0: LoadField: r2 = r1->field_f
    //     0xb5dff0: ldur            w2, [x1, #0xf]
    // 0xb5dff4: DecompressPointer r2
    //     0xb5dff4: add             x2, x2, HEAP, lsl #32
    // 0xb5dff8: cmp             w2, NULL
    // 0xb5dffc: b.eq            #0xb5e024
    // 0xb5e000: LoadField: r1 = r2->field_f
    //     0xb5e000: ldur            w1, [x2, #0xf]
    // 0xb5e004: DecompressPointer r1
    //     0xb5e004: add             x1, x1, HEAP, lsl #32
    // 0xb5e008: r2 = LoadClassIdInstr(r1)
    //     0xb5e008: ldur            x2, [x1, #-1]
    //     0xb5e00c: ubfx            x2, x2, #0xc, #0x14
    // 0xb5e010: cmp             x2, #0x184
    // 0xb5e014: r16 = true
    //     0xb5e014: add             x16, NULL, #0x20  ; true
    // 0xb5e018: r17 = false
    //     0xb5e018: add             x17, NULL, #0x30  ; false
    // 0xb5e01c: csel            x0, x16, x17, ne
    // 0xb5e020: ret
    //     0xb5e020: ret             
    // 0xb5e024: EnterFrame
    //     0xb5e024: stp             fp, lr, [SP, #-0x10]!
    //     0xb5e028: mov             fp, SP
    // 0xb5e02c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5e02c: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, MapEntry<Heir, Result>) {
    // ** addr: 0xb5e030, size: 0x44
    // 0xb5e030: ldr             x1, [SP]
    // 0xb5e034: LoadField: r2 = r1->field_f
    //     0xb5e034: ldur            w2, [x1, #0xf]
    // 0xb5e038: DecompressPointer r2
    //     0xb5e038: add             x2, x2, HEAP, lsl #32
    // 0xb5e03c: cmp             w2, NULL
    // 0xb5e040: b.eq            #0xb5e068
    // 0xb5e044: LoadField: r1 = r2->field_f
    //     0xb5e044: ldur            w1, [x2, #0xf]
    // 0xb5e048: DecompressPointer r1
    //     0xb5e048: add             x1, x1, HEAP, lsl #32
    // 0xb5e04c: r2 = LoadClassIdInstr(r1)
    //     0xb5e04c: ldur            x2, [x1, #-1]
    //     0xb5e050: ubfx            x2, x2, #0xc, #0x14
    // 0xb5e054: cmp             x2, #0x184
    // 0xb5e058: r16 = true
    //     0xb5e058: add             x16, NULL, #0x20  ; true
    // 0xb5e05c: r17 = false
    //     0xb5e05c: add             x17, NULL, #0x30  ; false
    // 0xb5e060: csel            x0, x16, x17, eq
    // 0xb5e064: ret
    //     0xb5e064: ret             
    // 0xb5e068: EnterFrame
    //     0xb5e068: stp             fp, lr, [SP, #-0x10]!
    //     0xb5e06c: mov             fp, SP
    // 0xb5e070: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb5e070: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
  }
  _ hasEqualSiblingOf(/* No info */) {
    // ** addr: 0xbb0dd0, size: 0x2c4
    // 0xbb0dd0: EnterFrame
    //     0xbb0dd0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb0dd4: mov             fp, SP
    // 0xbb0dd8: AllocStack(0x28)
    //     0xbb0dd8: sub             SP, SP, #0x28
    // 0xbb0ddc: SetupParameters(Waris this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbb0ddc: stur            x1, [fp, #-8]
    //     0xbb0de0: stur            x2, [fp, #-0x10]
    // 0xbb0de4: CheckStackOverflow
    //     0xbb0de4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb0de8: cmp             SP, x16
    //     0xbb0dec: b.ls            #0xbb108c
    // 0xbb0df0: r1 = 1
    //     0xbb0df0: movz            x1, #0x1
    // 0xbb0df4: r0 = AllocateContext()
    //     0xbb0df4: bl              #0xec126c  ; AllocateContextStub
    // 0xbb0df8: mov             x3, x0
    // 0xbb0dfc: ldur            x0, [fp, #-8]
    // 0xbb0e00: stur            x3, [fp, #-0x18]
    // 0xbb0e04: StoreField: r3->field_f = r0
    //     0xbb0e04: stur            w0, [x3, #0xf]
    // 0xbb0e08: ldur            x1, [fp, #-0x10]
    // 0xbb0e0c: r16 = Instance_Heir
    //     0xbb0e0c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0xbb0e10: ldr             x16, [x16, #0x7d0]
    // 0xbb0e14: cmp             w1, w16
    // 0xbb0e18: b.ne            #0xbb0e34
    // 0xbb0e1c: LoadField: r2 = r0->field_7
    //     0xbb0e1c: ldur            w2, [x0, #7]
    // 0xbb0e20: DecompressPointer r2
    //     0xbb0e20: add             x2, x2, HEAP, lsl #32
    // 0xbb0e24: r1 = Instance_Heir
    //     0xbb0e24: add             x1, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0xbb0e28: ldr             x1, [x1, #0x7d8]
    // 0xbb0e2c: r0 = isQualified()
    //     0xbb0e2c: bl              #0xbb1094  ; [package:waris/src/shares.dart] ::isQualified
    // 0xbb0e30: b               #0xbb1080
    // 0xbb0e34: r16 = Instance_Heir
    //     0xbb0e34: add             x16, PP, #0x31, lsl #12  ; [pp+0x317d8] Obj!Heir@e2da51
    //     0xbb0e38: ldr             x16, [x16, #0x7d8]
    // 0xbb0e3c: cmp             w1, w16
    // 0xbb0e40: b.ne            #0xbb0e5c
    // 0xbb0e44: LoadField: r2 = r0->field_7
    //     0xbb0e44: ldur            w2, [x0, #7]
    // 0xbb0e48: DecompressPointer r2
    //     0xbb0e48: add             x2, x2, HEAP, lsl #32
    // 0xbb0e4c: r1 = Instance_Heir
    //     0xbb0e4c: add             x1, PP, #0x31, lsl #12  ; [pp+0x317d0] Obj!Heir@e2dd51
    //     0xbb0e50: ldr             x1, [x1, #0x7d0]
    // 0xbb0e54: r0 = isQualified()
    //     0xbb0e54: bl              #0xbb1094  ; [package:waris/src/shares.dart] ::isQualified
    // 0xbb0e58: b               #0xbb1080
    // 0xbb0e5c: r16 = Instance_Heir
    //     0xbb0e5c: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0xbb0e60: ldr             x16, [x16, #0x7e0]
    // 0xbb0e64: cmp             w1, w16
    // 0xbb0e68: b.ne            #0xbb0e84
    // 0xbb0e6c: LoadField: r2 = r0->field_7
    //     0xbb0e6c: ldur            w2, [x0, #7]
    // 0xbb0e70: DecompressPointer r2
    //     0xbb0e70: add             x2, x2, HEAP, lsl #32
    // 0xbb0e74: r1 = Instance_Heir
    //     0xbb0e74: add             x1, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0xbb0e78: ldr             x1, [x1, #0x7e8]
    // 0xbb0e7c: r0 = isQualified()
    //     0xbb0e7c: bl              #0xbb1094  ; [package:waris/src/shares.dart] ::isQualified
    // 0xbb0e80: b               #0xbb1080
    // 0xbb0e84: r16 = Instance_Heir
    //     0xbb0e84: add             x16, PP, #0x31, lsl #12  ; [pp+0x317e8] Obj!Heir@e2da21
    //     0xbb0e88: ldr             x16, [x16, #0x7e8]
    // 0xbb0e8c: cmp             w1, w16
    // 0xbb0e90: b.ne            #0xbb0eac
    // 0xbb0e94: LoadField: r2 = r0->field_7
    //     0xbb0e94: ldur            w2, [x0, #7]
    // 0xbb0e98: DecompressPointer r2
    //     0xbb0e98: add             x2, x2, HEAP, lsl #32
    // 0xbb0e9c: r1 = Instance_Heir
    //     0xbb0e9c: add             x1, PP, #0x31, lsl #12  ; [pp+0x317e0] Obj!Heir@e2dd21
    //     0xbb0ea0: ldr             x1, [x1, #0x7e0]
    // 0xbb0ea4: r0 = isQualified()
    //     0xbb0ea4: bl              #0xbb1094  ; [package:waris/src/shares.dart] ::isQualified
    // 0xbb0ea8: b               #0xbb1080
    // 0xbb0eac: r16 = Instance_Heir
    //     0xbb0eac: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0xbb0eb0: ldr             x16, [x16, #0x90]
    // 0xbb0eb4: cmp             w1, w16
    // 0xbb0eb8: b.ne            #0xbb0ed4
    // 0xbb0ebc: LoadField: r2 = r0->field_7
    //     0xbb0ebc: ldur            w2, [x0, #7]
    // 0xbb0ec0: DecompressPointer r2
    //     0xbb0ec0: add             x2, x2, HEAP, lsl #32
    // 0xbb0ec4: r1 = Instance_Heir
    //     0xbb0ec4: add             x1, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0xbb0ec8: ldr             x1, [x1, #0xa8]
    // 0xbb0ecc: r0 = isQualified()
    //     0xbb0ecc: bl              #0xbb1094  ; [package:waris/src/shares.dart] ::isQualified
    // 0xbb0ed0: b               #0xbb1080
    // 0xbb0ed4: r16 = Instance_Heir
    //     0xbb0ed4: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0xbb0ed8: ldr             x16, [x16, #0xa8]
    // 0xbb0edc: cmp             w1, w16
    // 0xbb0ee0: b.ne            #0xbb0f54
    // 0xbb0ee4: r0 = 4
    //     0xbb0ee4: movz            x0, #0x4
    // 0xbb0ee8: mov             x2, x0
    // 0xbb0eec: r1 = Null
    //     0xbb0eec: mov             x1, NULL
    // 0xbb0ef0: r0 = AllocateArray()
    //     0xbb0ef0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb0ef4: stur            x0, [fp, #-0x20]
    // 0xbb0ef8: r16 = Instance_Heir
    //     0xbb0ef8: add             x16, PP, #0x28, lsl #12  ; [pp+0x28090] Obj!Heir@e2dc61
    //     0xbb0efc: ldr             x16, [x16, #0x90]
    // 0xbb0f00: StoreField: r0->field_f = r16
    //     0xbb0f00: stur            w16, [x0, #0xf]
    // 0xbb0f04: r16 = Instance_Heir
    //     0xbb0f04: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0xbb0f08: ldr             x16, [x16, #0x88]
    // 0xbb0f0c: StoreField: r0->field_13 = r16
    //     0xbb0f0c: stur            w16, [x0, #0x13]
    // 0xbb0f10: r1 = <Heir>
    //     0xbb0f10: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0xbb0f14: ldr             x1, [x1, #0xc0]
    // 0xbb0f18: r0 = AllocateGrowableArray()
    //     0xbb0f18: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb0f1c: mov             x3, x0
    // 0xbb0f20: ldur            x0, [fp, #-0x20]
    // 0xbb0f24: stur            x3, [fp, #-0x28]
    // 0xbb0f28: StoreField: r3->field_f = r0
    //     0xbb0f28: stur            w0, [x3, #0xf]
    // 0xbb0f2c: r4 = 4
    //     0xbb0f2c: movz            x4, #0x4
    // 0xbb0f30: StoreField: r3->field_b = r4
    //     0xbb0f30: stur            w4, [x3, #0xb]
    // 0xbb0f34: ldur            x2, [fp, #-0x18]
    // 0xbb0f38: r1 = Function '<anonymous closure>':.
    //     0xbb0f38: add             x1, PP, #0x31, lsl #12  ; [pp+0x317f0] AnonymousClosure: (0xbb1144), in [package:waris/src/waris.dart] Waris::hasEqualSiblingOf (0xbb0dd0)
    //     0xbb0f3c: ldr             x1, [x1, #0x7f0]
    // 0xbb0f40: r0 = AllocateClosure()
    //     0xbb0f40: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb0f44: ldur            x1, [fp, #-0x28]
    // 0xbb0f48: mov             x2, x0
    // 0xbb0f4c: r0 = any()
    //     0xbb0f4c: bl              #0x7b4a2c  ; [dart:collection] ListBase::any
    // 0xbb0f50: b               #0xbb1080
    // 0xbb0f54: r4 = 4
    //     0xbb0f54: movz            x4, #0x4
    // 0xbb0f58: r16 = Instance_Heir
    //     0xbb0f58: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0xbb0f5c: ldr             x16, [x16, #0x98]
    // 0xbb0f60: cmp             w1, w16
    // 0xbb0f64: b.ne            #0xbb0f80
    // 0xbb0f68: LoadField: r2 = r0->field_7
    //     0xbb0f68: ldur            w2, [x0, #7]
    // 0xbb0f6c: DecompressPointer r2
    //     0xbb0f6c: add             x2, x2, HEAP, lsl #32
    // 0xbb0f70: r1 = Instance_Heir
    //     0xbb0f70: add             x1, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0xbb0f74: ldr             x1, [x1, #0xb0]
    // 0xbb0f78: r0 = isQualified()
    //     0xbb0f78: bl              #0xbb1094  ; [package:waris/src/shares.dart] ::isQualified
    // 0xbb0f7c: b               #0xbb1080
    // 0xbb0f80: r16 = Instance_Heir
    //     0xbb0f80: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0xbb0f84: ldr             x16, [x16, #0xb0]
    // 0xbb0f88: cmp             w1, w16
    // 0xbb0f8c: b.ne            #0xbb0ffc
    // 0xbb0f90: mov             x2, x4
    // 0xbb0f94: r1 = Null
    //     0xbb0f94: mov             x1, NULL
    // 0xbb0f98: r0 = AllocateArray()
    //     0xbb0f98: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb0f9c: stur            x0, [fp, #-8]
    // 0xbb0fa0: r16 = Instance_Heir
    //     0xbb0fa0: add             x16, PP, #0x28, lsl #12  ; [pp+0x28098] Obj!Heir@e2dc31
    //     0xbb0fa4: ldr             x16, [x16, #0x98]
    // 0xbb0fa8: StoreField: r0->field_f = r16
    //     0xbb0fa8: stur            w16, [x0, #0xf]
    // 0xbb0fac: r16 = Instance_Heir
    //     0xbb0fac: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0xbb0fb0: ldr             x16, [x16, #0x88]
    // 0xbb0fb4: StoreField: r0->field_13 = r16
    //     0xbb0fb4: stur            w16, [x0, #0x13]
    // 0xbb0fb8: r1 = <Heir>
    //     0xbb0fb8: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0xbb0fbc: ldr             x1, [x1, #0xc0]
    // 0xbb0fc0: r0 = AllocateGrowableArray()
    //     0xbb0fc0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb0fc4: mov             x3, x0
    // 0xbb0fc8: ldur            x0, [fp, #-8]
    // 0xbb0fcc: stur            x3, [fp, #-0x20]
    // 0xbb0fd0: StoreField: r3->field_f = r0
    //     0xbb0fd0: stur            w0, [x3, #0xf]
    // 0xbb0fd4: r0 = 4
    //     0xbb0fd4: movz            x0, #0x4
    // 0xbb0fd8: StoreField: r3->field_b = r0
    //     0xbb0fd8: stur            w0, [x3, #0xb]
    // 0xbb0fdc: ldur            x2, [fp, #-0x18]
    // 0xbb0fe0: r1 = Function '<anonymous closure>':.
    //     0xbb0fe0: add             x1, PP, #0x31, lsl #12  ; [pp+0x317f8] AnonymousClosure: (0xbb1144), in [package:waris/src/waris.dart] Waris::hasEqualSiblingOf (0xbb0dd0)
    //     0xbb0fe4: ldr             x1, [x1, #0x7f8]
    // 0xbb0fe8: r0 = AllocateClosure()
    //     0xbb0fe8: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb0fec: ldur            x1, [fp, #-0x20]
    // 0xbb0ff0: mov             x2, x0
    // 0xbb0ff4: r0 = any()
    //     0xbb0ff4: bl              #0x7b4a2c  ; [dart:collection] ListBase::any
    // 0xbb0ff8: b               #0xbb1080
    // 0xbb0ffc: mov             x0, x4
    // 0xbb1000: r16 = Instance_Heir
    //     0xbb1000: add             x16, PP, #0x28, lsl #12  ; [pp+0x28088] Obj!Heir@e2dc91
    //     0xbb1004: ldr             x16, [x16, #0x88]
    // 0xbb1008: cmp             w1, w16
    // 0xbb100c: b.ne            #0xbb107c
    // 0xbb1010: mov             x2, x0
    // 0xbb1014: r1 = Null
    //     0xbb1014: mov             x1, NULL
    // 0xbb1018: r0 = AllocateArray()
    //     0xbb1018: bl              #0xec22fc  ; AllocateArrayStub
    // 0xbb101c: stur            x0, [fp, #-8]
    // 0xbb1020: r16 = Instance_Heir
    //     0xbb1020: add             x16, PP, #0x28, lsl #12  ; [pp+0x280a8] Obj!Heir@e2d961
    //     0xbb1024: ldr             x16, [x16, #0xa8]
    // 0xbb1028: StoreField: r0->field_f = r16
    //     0xbb1028: stur            w16, [x0, #0xf]
    // 0xbb102c: r16 = Instance_Heir
    //     0xbb102c: add             x16, PP, #0x28, lsl #12  ; [pp+0x280b0] Obj!Heir@e2d931
    //     0xbb1030: ldr             x16, [x16, #0xb0]
    // 0xbb1034: StoreField: r0->field_13 = r16
    //     0xbb1034: stur            w16, [x0, #0x13]
    // 0xbb1038: r1 = <Heir>
    //     0xbb1038: add             x1, PP, #0x28, lsl #12  ; [pp+0x280c0] TypeArguments: <Heir>
    //     0xbb103c: ldr             x1, [x1, #0xc0]
    // 0xbb1040: r0 = AllocateGrowableArray()
    //     0xbb1040: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xbb1044: mov             x3, x0
    // 0xbb1048: ldur            x0, [fp, #-8]
    // 0xbb104c: stur            x3, [fp, #-0x10]
    // 0xbb1050: StoreField: r3->field_f = r0
    //     0xbb1050: stur            w0, [x3, #0xf]
    // 0xbb1054: r0 = 4
    //     0xbb1054: movz            x0, #0x4
    // 0xbb1058: StoreField: r3->field_b = r0
    //     0xbb1058: stur            w0, [x3, #0xb]
    // 0xbb105c: ldur            x2, [fp, #-0x18]
    // 0xbb1060: r1 = Function '<anonymous closure>':.
    //     0xbb1060: add             x1, PP, #0x31, lsl #12  ; [pp+0x31800] AnonymousClosure: (0xbb1144), in [package:waris/src/waris.dart] Waris::hasEqualSiblingOf (0xbb0dd0)
    //     0xbb1064: ldr             x1, [x1, #0x800]
    // 0xbb1068: r0 = AllocateClosure()
    //     0xbb1068: bl              #0xec1630  ; AllocateClosureStub
    // 0xbb106c: ldur            x1, [fp, #-0x10]
    // 0xbb1070: mov             x2, x0
    // 0xbb1074: r0 = any()
    //     0xbb1074: bl              #0x7b4a2c  ; [dart:collection] ListBase::any
    // 0xbb1078: b               #0xbb1080
    // 0xbb107c: r0 = false
    //     0xbb107c: add             x0, NULL, #0x30  ; false
    // 0xbb1080: LeaveFrame
    //     0xbb1080: mov             SP, fp
    //     0xbb1084: ldp             fp, lr, [SP], #0x10
    // 0xbb1088: ret
    //     0xbb1088: ret             
    // 0xbb108c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb108c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb1090: b               #0xbb0df0
  }
  [closure] bool <anonymous closure>(dynamic, Heir) {
    // ** addr: 0xbb1144, size: 0x4c
    // 0xbb1144: EnterFrame
    //     0xbb1144: stp             fp, lr, [SP, #-0x10]!
    //     0xbb1148: mov             fp, SP
    // 0xbb114c: ldr             x0, [fp, #0x18]
    // 0xbb1150: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb1150: ldur            w1, [x0, #0x17]
    // 0xbb1154: DecompressPointer r1
    //     0xbb1154: add             x1, x1, HEAP, lsl #32
    // 0xbb1158: CheckStackOverflow
    //     0xbb1158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb115c: cmp             SP, x16
    //     0xbb1160: b.ls            #0xbb1188
    // 0xbb1164: LoadField: r0 = r1->field_f
    //     0xbb1164: ldur            w0, [x1, #0xf]
    // 0xbb1168: DecompressPointer r0
    //     0xbb1168: add             x0, x0, HEAP, lsl #32
    // 0xbb116c: LoadField: r2 = r0->field_7
    //     0xbb116c: ldur            w2, [x0, #7]
    // 0xbb1170: DecompressPointer r2
    //     0xbb1170: add             x2, x2, HEAP, lsl #32
    // 0xbb1174: ldr             x1, [fp, #0x10]
    // 0xbb1178: r0 = isQualified()
    //     0xbb1178: bl              #0xbb1094  ; [package:waris/src/shares.dart] ::isQualified
    // 0xbb117c: LeaveFrame
    //     0xbb117c: mov             SP, fp
    //     0xbb1180: ldp             fp, lr, [SP], #0x10
    // 0xbb1184: ret
    //     0xbb1184: ret             
    // 0xbb1188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb1188: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb118c: b               #0xbb1164
  }
}
