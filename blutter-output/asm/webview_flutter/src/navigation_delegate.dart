// lib: , url: package:webview_flutter/src/navigation_delegate.dart

// class id: 1051244, size: 0x8
class :: {
}

// class id: 374, size: 0x20, field offset: 0x8
class NavigationDelegate extends Object {

  _ NavigationDelegate.fromPlatform(/* No info */) {
    // ** addr: 0x979fac, size: 0xc0
    // 0x979fac: EnterFrame
    //     0x979fac: stp             fp, lr, [SP, #-0x10]!
    //     0x979fb0: mov             fp, SP
    // 0x979fb4: AllocStack(0x10)
    //     0x979fb4: sub             SP, SP, #0x10
    // 0x979fb8: SetupParameters(NavigationDelegate this /* r1 => r2 */, dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r1 */, dynamic _ /* r5 => r3, fp-0x10 */)
    //     0x979fb8: mov             x4, x2
    //     0x979fbc: stur            x2, [fp, #-8]
    //     0x979fc0: mov             x2, x1
    //     0x979fc4: mov             x1, x3
    //     0x979fc8: mov             x3, x5
    //     0x979fcc: stur            x5, [fp, #-0x10]
    // 0x979fd0: CheckStackOverflow
    //     0x979fd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979fd4: cmp             SP, x16
    //     0x979fd8: b.ls            #0x97a064
    // 0x979fdc: mov             x0, x4
    // 0x979fe0: StoreField: r2->field_7 = r0
    //     0x979fe0: stur            w0, [x2, #7]
    //     0x979fe4: ldurb           w16, [x2, #-1]
    //     0x979fe8: ldurb           w17, [x0, #-1]
    //     0x979fec: and             x16, x17, x16, lsr #2
    //     0x979ff0: tst             x16, HEAP, lsr #32
    //     0x979ff4: b.eq            #0x979ffc
    //     0x979ff8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x979ffc: mov             x0, x1
    // 0x97a000: StoreField: r2->field_b = r0
    //     0x97a000: stur            w0, [x2, #0xb]
    //     0x97a004: ldurb           w16, [x2, #-1]
    //     0x97a008: ldurb           w17, [x0, #-1]
    //     0x97a00c: and             x16, x17, x16, lsr #2
    //     0x97a010: tst             x16, HEAP, lsr #32
    //     0x97a014: b.eq            #0x97a01c
    //     0x97a018: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x97a01c: mov             x0, x3
    // 0x97a020: StoreField: r2->field_1b = r0
    //     0x97a020: stur            w0, [x2, #0x1b]
    //     0x97a024: ldurb           w16, [x2, #-1]
    //     0x97a028: ldurb           w17, [x0, #-1]
    //     0x97a02c: and             x16, x17, x16, lsr #2
    //     0x97a030: tst             x16, HEAP, lsr #32
    //     0x97a034: b.eq            #0x97a03c
    //     0x97a038: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x97a03c: mov             x2, x1
    // 0x97a040: mov             x1, x4
    // 0x97a044: r0 = setOnNavigationRequest()
    //     0x97a044: bl              #0x97a0d8  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::setOnNavigationRequest
    // 0x97a048: ldur            x1, [fp, #-8]
    // 0x97a04c: ldur            x2, [fp, #-0x10]
    // 0x97a050: r0 = setOnWebResourceError()
    //     0x97a050: bl              #0x97a06c  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::setOnWebResourceError
    // 0x97a054: r0 = Null
    //     0x97a054: mov             x0, NULL
    // 0x97a058: LeaveFrame
    //     0x97a058: mov             SP, fp
    //     0x97a05c: ldp             fp, lr, [SP], #0x10
    // 0x97a060: ret
    //     0x97a060: ret             
    // 0x97a064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a064: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a068: b               #0x979fdc
  }
}
