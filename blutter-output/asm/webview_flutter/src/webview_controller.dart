// lib: , url: package:webview_flutter/src/webview_controller.dart

// class id: 1051245, size: 0x8
class :: {
}

// class id: 373, size: 0xc, field offset: 0x8
class WebViewController extends Object {

  _ runJavaScript(/* No info */) {
    // ** addr: 0x965bac, size: 0x38
    // 0x965bac: EnterFrame
    //     0x965bac: stp             fp, lr, [SP, #-0x10]!
    //     0x965bb0: mov             fp, SP
    // 0x965bb4: CheckStackOverflow
    //     0x965bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x965bb8: cmp             SP, x16
    //     0x965bbc: b.ls            #0x965bdc
    // 0x965bc0: LoadField: r0 = r1->field_7
    //     0x965bc0: ldur            w0, [x1, #7]
    // 0x965bc4: DecompressPointer r0
    //     0x965bc4: add             x0, x0, HEAP, lsl #32
    // 0x965bc8: mov             x1, x0
    // 0x965bcc: r0 = runJavaScript()
    //     0x965bcc: bl              #0x965be4  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::runJavaScript
    // 0x965bd0: LeaveFrame
    //     0x965bd0: mov             SP, fp
    //     0x965bd4: ldp             fp, lr, [SP], #0x10
    // 0x965bd8: ret
    //     0x965bd8: ret             
    // 0x965bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x965bdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x965be0: b               #0x965bc0
  }
  _ enableZoom(/* No info */) {
    // ** addr: 0x971a3c, size: 0x3c
    // 0x971a3c: EnterFrame
    //     0x971a3c: stp             fp, lr, [SP, #-0x10]!
    //     0x971a40: mov             fp, SP
    // 0x971a44: CheckStackOverflow
    //     0x971a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x971a48: cmp             SP, x16
    //     0x971a4c: b.ls            #0x971a70
    // 0x971a50: LoadField: r0 = r1->field_7
    //     0x971a50: ldur            w0, [x1, #7]
    // 0x971a54: DecompressPointer r0
    //     0x971a54: add             x0, x0, HEAP, lsl #32
    // 0x971a58: mov             x1, x0
    // 0x971a5c: r2 = false
    //     0x971a5c: add             x2, NULL, #0x30  ; false
    // 0x971a60: r0 = enableZoom()
    //     0x971a60: bl              #0x971a78  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::enableZoom
    // 0x971a64: LeaveFrame
    //     0x971a64: mov             SP, fp
    //     0x971a68: ldp             fp, lr, [SP], #0x10
    // 0x971a6c: ret
    //     0x971a6c: ret             
    // 0x971a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x971a70: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x971a74: b               #0x971a50
  }
  _ addJavaScriptChannel(/* No info */) {
    // ** addr: 0x971da0, size: 0x64
    // 0x971da0: EnterFrame
    //     0x971da0: stp             fp, lr, [SP, #-0x10]!
    //     0x971da4: mov             fp, SP
    // 0x971da8: AllocStack(0x18)
    //     0x971da8: sub             SP, SP, #0x18
    // 0x971dac: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x971dac: stur            x2, [fp, #-0x10]
    //     0x971db0: stur            x3, [fp, #-0x18]
    // 0x971db4: CheckStackOverflow
    //     0x971db4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x971db8: cmp             SP, x16
    //     0x971dbc: b.ls            #0x971dfc
    // 0x971dc0: LoadField: r0 = r1->field_7
    //     0x971dc0: ldur            w0, [x1, #7]
    // 0x971dc4: DecompressPointer r0
    //     0x971dc4: add             x0, x0, HEAP, lsl #32
    // 0x971dc8: stur            x0, [fp, #-8]
    // 0x971dcc: r0 = JavaScriptChannelParams()
    //     0x971dcc: bl              #0x972d80  ; AllocateJavaScriptChannelParamsStub -> JavaScriptChannelParams (size=0x10)
    // 0x971dd0: mov             x1, x0
    // 0x971dd4: ldur            x0, [fp, #-0x10]
    // 0x971dd8: StoreField: r1->field_7 = r0
    //     0x971dd8: stur            w0, [x1, #7]
    // 0x971ddc: ldur            x0, [fp, #-0x18]
    // 0x971de0: StoreField: r1->field_b = r0
    //     0x971de0: stur            w0, [x1, #0xb]
    // 0x971de4: mov             x2, x1
    // 0x971de8: ldur            x1, [fp, #-8]
    // 0x971dec: r0 = addJavaScriptChannel()
    //     0x971dec: bl              #0x971e04  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::addJavaScriptChannel
    // 0x971df0: LeaveFrame
    //     0x971df0: mov             SP, fp
    //     0x971df4: ldp             fp, lr, [SP], #0x10
    // 0x971df8: ret
    //     0x971df8: ret             
    // 0x971dfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x971dfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x971e00: b               #0x971dc0
  }
  _ setUserAgent(/* No info */) {
    // ** addr: 0x972d8c, size: 0x3c
    // 0x972d8c: EnterFrame
    //     0x972d8c: stp             fp, lr, [SP, #-0x10]!
    //     0x972d90: mov             fp, SP
    // 0x972d94: CheckStackOverflow
    //     0x972d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x972d98: cmp             SP, x16
    //     0x972d9c: b.ls            #0x972dc0
    // 0x972da0: LoadField: r0 = r1->field_7
    //     0x972da0: ldur            w0, [x1, #7]
    // 0x972da4: DecompressPointer r0
    //     0x972da4: add             x0, x0, HEAP, lsl #32
    // 0x972da8: mov             x1, x0
    // 0x972dac: r2 = Null
    //     0x972dac: mov             x2, NULL
    // 0x972db0: r0 = setUserAgent()
    //     0x972db0: bl              #0x972dc8  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::setUserAgent
    // 0x972db4: LeaveFrame
    //     0x972db4: mov             SP, fp
    //     0x972db8: ldp             fp, lr, [SP], #0x10
    // 0x972dbc: ret
    //     0x972dbc: ret             
    // 0x972dc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x972dc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x972dc4: b               #0x972da0
  }
  _ setNavigationDelegate(/* No info */) {
    // ** addr: 0x9730ec, size: 0x44
    // 0x9730ec: EnterFrame
    //     0x9730ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9730f0: mov             fp, SP
    // 0x9730f4: CheckStackOverflow
    //     0x9730f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9730f8: cmp             SP, x16
    //     0x9730fc: b.ls            #0x973128
    // 0x973100: LoadField: r0 = r1->field_7
    //     0x973100: ldur            w0, [x1, #7]
    // 0x973104: DecompressPointer r0
    //     0x973104: add             x0, x0, HEAP, lsl #32
    // 0x973108: LoadField: r1 = r2->field_7
    //     0x973108: ldur            w1, [x2, #7]
    // 0x97310c: DecompressPointer r1
    //     0x97310c: add             x1, x1, HEAP, lsl #32
    // 0x973110: mov             x2, x1
    // 0x973114: mov             x1, x0
    // 0x973118: r0 = setPlatformNavigationDelegate()
    //     0x973118: bl              #0x973130  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::setPlatformNavigationDelegate
    // 0x97311c: LeaveFrame
    //     0x97311c: mov             SP, fp
    //     0x973120: ldp             fp, lr, [SP], #0x10
    // 0x973124: ret
    //     0x973124: ret             
    // 0x973128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x973128: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97312c: b               #0x973100
  }
  _ setJavaScriptMode(/* No info */) {
    // ** addr: 0x973fe4, size: 0x40
    // 0x973fe4: EnterFrame
    //     0x973fe4: stp             fp, lr, [SP, #-0x10]!
    //     0x973fe8: mov             fp, SP
    // 0x973fec: CheckStackOverflow
    //     0x973fec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x973ff0: cmp             SP, x16
    //     0x973ff4: b.ls            #0x97401c
    // 0x973ff8: LoadField: r0 = r1->field_7
    //     0x973ff8: ldur            w0, [x1, #7]
    // 0x973ffc: DecompressPointer r0
    //     0x973ffc: add             x0, x0, HEAP, lsl #32
    // 0x974000: mov             x1, x0
    // 0x974004: r2 = Instance_JavaScriptMode
    //     0x974004: add             x2, PP, #0x49, lsl #12  ; [pp+0x493e8] Obj!JavaScriptMode@e2d771
    //     0x974008: ldr             x2, [x2, #0x3e8]
    // 0x97400c: r0 = setJavaScriptMode()
    //     0x97400c: bl              #0x974024  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::setJavaScriptMode
    // 0x974010: LeaveFrame
    //     0x974010: mov             SP, fp
    //     0x974014: ldp             fp, lr, [SP], #0x10
    // 0x974018: ret
    //     0x974018: ret             
    // 0x97401c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97401c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x974020: b               #0x973ff8
  }
  _ runJavaScriptReturningResult(/* No info */) {
    // ** addr: 0x97c8b0, size: 0x38
    // 0x97c8b0: EnterFrame
    //     0x97c8b0: stp             fp, lr, [SP, #-0x10]!
    //     0x97c8b4: mov             fp, SP
    // 0x97c8b8: CheckStackOverflow
    //     0x97c8b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c8bc: cmp             SP, x16
    //     0x97c8c0: b.ls            #0x97c8e0
    // 0x97c8c4: LoadField: r0 = r1->field_7
    //     0x97c8c4: ldur            w0, [x1, #7]
    // 0x97c8c8: DecompressPointer r0
    //     0x97c8c8: add             x0, x0, HEAP, lsl #32
    // 0x97c8cc: mov             x1, x0
    // 0x97c8d0: r0 = runJavaScriptReturningResult()
    //     0x97c8d0: bl              #0x97c8e8  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::runJavaScriptReturningResult
    // 0x97c8d4: LeaveFrame
    //     0x97c8d4: mov             SP, fp
    //     0x97c8d8: ldp             fp, lr, [SP], #0x10
    // 0x97c8dc: ret
    //     0x97c8dc: ret             
    // 0x97c8e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c8e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c8e4: b               #0x97c8c4
  }
  _ loadHtmlString(/* No info */) {
    // ** addr: 0x981ed4, size: 0x40
    // 0x981ed4: EnterFrame
    //     0x981ed4: stp             fp, lr, [SP, #-0x10]!
    //     0x981ed8: mov             fp, SP
    // 0x981edc: CheckStackOverflow
    //     0x981edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981ee0: cmp             SP, x16
    //     0x981ee4: b.ls            #0x981f0c
    // 0x981ee8: LoadField: r0 = r1->field_7
    //     0x981ee8: ldur            w0, [x1, #7]
    // 0x981eec: DecompressPointer r0
    //     0x981eec: add             x0, x0, HEAP, lsl #32
    // 0x981ef0: mov             x1, x0
    // 0x981ef4: r3 = "https://www.youtube.com"
    //     0x981ef4: add             x3, PP, #0x48, lsl #12  ; [pp+0x48fa0] "https://www.youtube.com"
    //     0x981ef8: ldr             x3, [x3, #0xfa0]
    // 0x981efc: r0 = loadHtmlString()
    //     0x981efc: bl              #0x981f14  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::loadHtmlString
    // 0x981f00: LeaveFrame
    //     0x981f00: mov             SP, fp
    //     0x981f04: ldp             fp, lr, [SP], #0x10
    // 0x981f08: ret
    //     0x981f08: ret             
    // 0x981f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981f10: b               #0x981ee8
  }
  _ setBackgroundColor(/* No info */) {
    // ** addr: 0x9824f0, size: 0x38
    // 0x9824f0: EnterFrame
    //     0x9824f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9824f4: mov             fp, SP
    // 0x9824f8: CheckStackOverflow
    //     0x9824f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9824fc: cmp             SP, x16
    //     0x982500: b.ls            #0x982520
    // 0x982504: LoadField: r0 = r1->field_7
    //     0x982504: ldur            w0, [x1, #7]
    // 0x982508: DecompressPointer r0
    //     0x982508: add             x0, x0, HEAP, lsl #32
    // 0x98250c: mov             x1, x0
    // 0x982510: r0 = setBackgroundColor()
    //     0x982510: bl              #0x982528  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::setBackgroundColor
    // 0x982514: LeaveFrame
    //     0x982514: mov             SP, fp
    //     0x982518: ldp             fp, lr, [SP], #0x10
    // 0x98251c: ret
    //     0x98251c: ret             
    // 0x982520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x982520: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x982524: b               #0x982504
  }
  _ removeJavaScriptChannel(/* No info */) {
    // ** addr: 0xa83860, size: 0x38
    // 0xa83860: EnterFrame
    //     0xa83860: stp             fp, lr, [SP, #-0x10]!
    //     0xa83864: mov             fp, SP
    // 0xa83868: CheckStackOverflow
    //     0xa83868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8386c: cmp             SP, x16
    //     0xa83870: b.ls            #0xa83890
    // 0xa83874: LoadField: r0 = r1->field_7
    //     0xa83874: ldur            w0, [x1, #7]
    // 0xa83878: DecompressPointer r0
    //     0xa83878: add             x0, x0, HEAP, lsl #32
    // 0xa8387c: mov             x1, x0
    // 0xa83880: r0 = removeJavaScriptChannel()
    //     0xa83880: bl              #0xa83898  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::removeJavaScriptChannel
    // 0xa83884: LeaveFrame
    //     0xa83884: mov             SP, fp
    //     0xa83888: ldp             fp, lr, [SP], #0x10
    // 0xa8388c: ret
    //     0xa8388c: ret             
    // 0xa83890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83890: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83894: b               #0xa83874
  }
}
