// lib: , url: package:webview_flutter/src/webview_widget.dart

// class id: 1051246, size: 0x8
class :: {
}

// class id: 4904, size: 0x10, field offset: 0xc
class WebViewWidget extends StatelessWidget {

  _ WebViewWidget(/* No info */) {
    // ** addr: 0xa515d0, size: 0x90
    // 0xa515d0: EnterFrame
    //     0xa515d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa515d4: mov             fp, SP
    // 0xa515d8: AllocStack(0x10)
    //     0xa515d8: sub             SP, SP, #0x10
    // 0xa515dc: SetupParameters(WebViewWidget this /* r1 => r1, fp-0x10 */)
    //     0xa515dc: stur            x1, [fp, #-0x10]
    // 0xa515e0: CheckStackOverflow
    //     0xa515e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa515e4: cmp             SP, x16
    //     0xa515e8: b.ls            #0xa51658
    // 0xa515ec: LoadField: r0 = r2->field_7
    //     0xa515ec: ldur            w0, [x2, #7]
    // 0xa515f0: DecompressPointer r0
    //     0xa515f0: add             x0, x0, HEAP, lsl #32
    // 0xa515f4: stur            x0, [fp, #-8]
    // 0xa515f8: r0 = PlatformWebViewWidgetCreationParams()
    //     0xa515f8: bl              #0xa51884  ; AllocatePlatformWebViewWidgetCreationParamsStub -> PlatformWebViewWidgetCreationParams (size=0x18)
    // 0xa515fc: mov             x1, x0
    // 0xa51600: ldur            x0, [fp, #-8]
    // 0xa51604: StoreField: r1->field_b = r0
    //     0xa51604: stur            w0, [x1, #0xb]
    // 0xa51608: r0 = Instance_TextDirection
    //     0xa51608: ldr             x0, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0xa5160c: StoreField: r1->field_f = r0
    //     0xa5160c: stur            w0, [x1, #0xf]
    // 0xa51610: r0 = _ConstSet len:0
    //     0xa51610: add             x0, PP, #0x42, lsl #12  ; [pp+0x42930] Set<Factory<OneSequenceGestureRecognizer>>(0)
    //     0xa51614: ldr             x0, [x0, #0x930]
    // 0xa51618: StoreField: r1->field_13 = r0
    //     0xa51618: stur            w0, [x1, #0x13]
    // 0xa5161c: mov             x2, x1
    // 0xa51620: r1 = Null
    //     0xa51620: mov             x1, NULL
    // 0xa51624: r0 = PlatformWebViewWidget()
    //     0xa51624: bl              #0xa51660  ; [package:webview_flutter_platform_interface/src/platform_webview_widget.dart] PlatformWebViewWidget::PlatformWebViewWidget
    // 0xa51628: ldur            x1, [fp, #-0x10]
    // 0xa5162c: StoreField: r1->field_b = r0
    //     0xa5162c: stur            w0, [x1, #0xb]
    //     0xa51630: ldurb           w16, [x1, #-1]
    //     0xa51634: ldurb           w17, [x0, #-1]
    //     0xa51638: and             x16, x17, x16, lsr #2
    //     0xa5163c: tst             x16, HEAP, lsr #32
    //     0xa51640: b.eq            #0xa51648
    //     0xa51644: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa51648: r0 = Null
    //     0xa51648: mov             x0, NULL
    // 0xa5164c: LeaveFrame
    //     0xa5164c: mov             SP, fp
    //     0xa51650: ldp             fp, lr, [SP], #0x10
    // 0xa51654: ret
    //     0xa51654: ret             
    // 0xa51658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa51658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5165c: b               #0xa515ec
  }
  _ build(/* No info */) {
    // ** addr: 0xbbcab4, size: 0x38
    // 0xbbcab4: EnterFrame
    //     0xbbcab4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbcab8: mov             fp, SP
    // 0xbbcabc: CheckStackOverflow
    //     0xbbcabc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbcac0: cmp             SP, x16
    //     0xbbcac4: b.ls            #0xbbcae4
    // 0xbbcac8: LoadField: r0 = r1->field_b
    //     0xbbcac8: ldur            w0, [x1, #0xb]
    // 0xbbcacc: DecompressPointer r0
    //     0xbbcacc: add             x0, x0, HEAP, lsl #32
    // 0xbbcad0: mov             x1, x0
    // 0xbbcad4: r0 = build()
    //     0xbbcad4: bl              #0xbbcaec  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewWidget::build
    // 0xbbcad8: LeaveFrame
    //     0xbbcad8: mov             SP, fp
    //     0xbbcadc: ldp             fp, lr, [SP], #0x10
    // 0xbbcae0: ret
    //     0xbbcae0: ret             
    // 0xbbcae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbcae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbcae8: b               #0xbbcac8
  }
}
