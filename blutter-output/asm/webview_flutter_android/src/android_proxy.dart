// lib: , url: package:webview_flutter_android/src/android_proxy.dart

// class id: 1051247, size: 0x8
class :: {
}

// class id: 371, size: 0x2c, field offset: 0x8
//   const constructor, 
class AndroidWebViewProxy extends Object {

  _Closure field_8;
  _Closure field_c;
  _Closure field_10;
  _Closure field_14;
  _Closure field_18;
  _Closure field_1c;
  _Closure field_20;
  _Closure field_24;
  _Closure field_28;

  [closure] static WebStorage _instanceWebStorage(dynamic) {
    // ** addr: 0x975cd4, size: 0x48
    // 0x975cd4: EnterFrame
    //     0x975cd4: stp             fp, lr, [SP, #-0x10]!
    //     0x975cd8: mov             fp, SP
    // 0x975cdc: CheckStackOverflow
    //     0x975cdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x975ce0: cmp             SP, x16
    //     0x975ce4: b.ls            #0x975d14
    // 0x975ce8: r0 = InitLateStaticField(0xa00) // [package:webview_flutter_android/src/android_webkit.g.dart] WebStorage::instance
    //     0x975ce8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x975cec: ldr             x0, [x0, #0x1400]
    //     0x975cf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x975cf4: cmp             w0, w16
    //     0x975cf8: b.ne            #0x975d08
    //     0x975cfc: add             x2, PP, #0x49, lsl #12  ; [pp+0x49820] Field <WebStorage.instance>: static late final (offset: 0xa00)
    //     0x975d00: ldr             x2, [x2, #0x820]
    //     0x975d04: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x975d08: LeaveFrame
    //     0x975d08: mov             SP, fp
    //     0x975d0c: ldp             fp, lr, [SP], #0x10
    // 0x975d10: ret
    //     0x975d10: ret             
    // 0x975d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x975d14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x975d18: b               #0x975ce8
  }
  [closure] static FlutterAssetManager _instanceFlutterAssetManager(dynamic) {
    // ** addr: 0x975d1c, size: 0x48
    // 0x975d1c: EnterFrame
    //     0x975d1c: stp             fp, lr, [SP, #-0x10]!
    //     0x975d20: mov             fp, SP
    // 0x975d24: CheckStackOverflow
    //     0x975d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x975d28: cmp             SP, x16
    //     0x975d2c: b.ls            #0x975d5c
    // 0x975d30: r0 = InitLateStaticField(0xa04) // [package:webview_flutter_android/src/android_webkit.g.dart] FlutterAssetManager::instance
    //     0x975d30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x975d34: ldr             x0, [x0, #0x1408]
    //     0x975d38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x975d3c: cmp             w0, w16
    //     0x975d40: b.ne            #0x975d50
    //     0x975d44: add             x2, PP, #0x49, lsl #12  ; [pp+0x49830] Field <FlutterAssetManager.instance>: static late final (offset: 0xa04)
    //     0x975d48: ldr             x2, [x2, #0x830]
    //     0x975d4c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x975d50: LeaveFrame
    //     0x975d50: mov             SP, fp
    //     0x975d54: ldp             fp, lr, [SP], #0x10
    // 0x975d58: ret
    //     0x975d58: ret             
    // 0x975d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x975d5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x975d60: b               #0x975d30
  }
  [closure] static CookieManager _instanceCookieManager(dynamic) {
    // ** addr: 0x976168, size: 0x48
    // 0x976168: EnterFrame
    //     0x976168: stp             fp, lr, [SP, #-0x10]!
    //     0x97616c: mov             fp, SP
    // 0x976170: CheckStackOverflow
    //     0x976170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x976174: cmp             SP, x16
    //     0x976178: b.ls            #0x9761a8
    // 0x97617c: r0 = InitLateStaticField(0xa08) // [package:webview_flutter_android/src/android_webkit.g.dart] CookieManager::instance
    //     0x97617c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x976180: ldr             x0, [x0, #0x1410]
    //     0x976184: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x976188: cmp             w0, w16
    //     0x97618c: b.ne            #0x97619c
    //     0x976190: add             x2, PP, #0x49, lsl #12  ; [pp+0x49878] Field <CookieManager.instance>: static late final (offset: 0xa08)
    //     0x976194: ldr             x2, [x2, #0x878]
    //     0x976198: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x97619c: LeaveFrame
    //     0x97619c: mov             SP, fp
    //     0x9761a0: ldp             fp, lr, [SP], #0x10
    // 0x9761a4: ret
    //     0x9761a4: ret             
    // 0x9761a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9761a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9761ac: b               #0x97617c
  }
}
