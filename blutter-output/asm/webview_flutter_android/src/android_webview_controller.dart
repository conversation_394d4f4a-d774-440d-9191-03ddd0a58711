// lib: , url: package:webview_flutter_android/src/android_webview_controller.dart

// class id: 1051249, size: 0x8
class :: {

  static _ _initAndroidView(/* No info */) {
    // ** addr: 0xbbcfb4, size: 0x6c
    // 0xbbcfb4: EnterFrame
    //     0xbbcfb4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbcfb8: mov             fp, SP
    // 0xbbcfbc: AllocStack(0x8)
    //     0xbbcfbc: sub             SP, SP, #8
    // 0xbbcfc0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r2 */)
    //     0xbbcfc0: mov             x0, x1
    //     0xbbcfc4: stur            x1, [fp, #-8]
    //     0xbbcfc8: mov             x1, x2
    //     0xbbcfcc: mov             x2, x3
    // 0xbbcfd0: CheckStackOverflow
    //     0xbbcfd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbcfd4: cmp             SP, x16
    //     0xbbcfd8: b.ls            #0xbbd014
    // 0xbbcfdc: r0 = getIdentifier()
    //     0xbbcfdc: bl              #0xbbd07c  ; [package:webview_flutter_android/src/android_webkit.g.dart] PigeonInstanceManager::getIdentifier
    // 0xbbcfe0: cmp             w0, NULL
    // 0xbbcfe4: b.eq            #0xbbd01c
    // 0xbbcfe8: ldur            x1, [fp, #-8]
    // 0xbbcfec: LoadField: r3 = r1->field_7
    //     0xbbcfec: ldur            x3, [x1, #7]
    // 0xbbcff0: r2 = LoadInt32Instr(r0)
    //     0xbbcff0: sbfx            x2, x0, #1, #0x1f
    //     0xbbcff4: tbz             w0, #0, #0xbbcffc
    //     0xbbcff8: ldur            x2, [x0, #7]
    // 0xbbcffc: r1 = Instance_PlatformViewsServiceProxy
    //     0xbbcffc: add             x1, PP, #0x49, lsl #12  ; [pp+0x49570] Obj!PlatformViewsServiceProxy@e0bc01
    //     0xbbd000: ldr             x1, [x1, #0x570]
    // 0xbbd004: r0 = initSurfaceAndroidView()
    //     0xbbd004: bl              #0xbbd020  ; [package:webview_flutter_android/src/platform_views_service_proxy.dart] PlatformViewsServiceProxy::initSurfaceAndroidView
    // 0xbbd008: LeaveFrame
    //     0xbbd008: mov             SP, fp
    //     0xbbd00c: ldp             fp, lr, [SP], #0x10
    // 0xbbd010: ret
    //     0xbbd010: ret             
    // 0xbbd014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbd014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbd018: b               #0xbbcfdc
    // 0xbbd01c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbd01c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 330, size: 0xc, field offset: 0x8
//   const constructor, 
class AndroidNavigationDelegateCreationParams extends PlatformNavigationDelegateCreationParams {

  factory _ AndroidNavigationDelegateCreationParams.fromPlatformNavigationDelegateCreationParams(/* No info */) {
    // ** addr: 0x97a7d0, size: 0x24
    // 0x97a7d0: EnterFrame
    //     0x97a7d0: stp             fp, lr, [SP, #-0x10]!
    //     0x97a7d4: mov             fp, SP
    // 0x97a7d8: r0 = AndroidNavigationDelegateCreationParams()
    //     0x97a7d8: bl              #0x97a7f4  ; AllocateAndroidNavigationDelegateCreationParamsStub -> AndroidNavigationDelegateCreationParams (size=0xc)
    // 0x97a7dc: r1 = Instance_AndroidWebViewProxy
    //     0x97a7dc: add             x1, PP, #0x49, lsl #12  ; [pp+0x49828] Obj!AndroidWebViewProxy@e0bc11
    //     0x97a7e0: ldr             x1, [x1, #0x828]
    // 0x97a7e4: StoreField: r0->field_7 = r1
    //     0x97a7e4: stur            w1, [x0, #7]
    // 0x97a7e8: LeaveFrame
    //     0x97a7e8: mov             SP, fp
    //     0x97a7ec: ldp             fp, lr, [SP], #0x10
    // 0x97a7f0: ret
    //     0x97a7f0: ret             
  }
}

// class id: 332, size: 0x10, field offset: 0x10
class AndroidWebResourceError extends WebResourceError {

  static _ _errorCodeToErrorType(/* No info */) {
    // ** addr: 0x97b02c, size: 0x214
    // 0x97b02c: EnterFrame
    //     0x97b02c: stp             fp, lr, [SP, #-0x10]!
    //     0x97b030: mov             fp, SP
    // 0x97b034: AllocStack(0x18)
    //     0x97b034: sub             SP, SP, #0x18
    // 0x97b038: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x97b038: mov             x0, x1
    //     0x97b03c: stur            x1, [fp, #-8]
    // 0x97b040: CheckStackOverflow
    //     0x97b040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b044: cmp             SP, x16
    //     0x97b048: b.ls            #0x97b238
    // 0x97b04c: cmn             x0, #0x10
    // 0x97b050: b.lt            #0x97b1d0
    // 0x97b054: cmn             x0, #1
    // 0x97b058: b.gt            #0x97b1d0
    // 0x97b05c: add             x2, x0, #0x10
    // 0x97b060: r0 = BoxInt64Instr(r2)
    //     0x97b060: sbfiz           x0, x2, #1, #0x1f
    //     0x97b064: cmp             x2, x0, asr #1
    //     0x97b068: b.eq            #0x97b074
    //     0x97b06c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x97b070: stur            x2, [x0, #7]
    // 0x97b074: r1 = _Int32List
    //     0x97b074: add             x1, PP, #0x49, lsl #12  ; [pp+0x49ab0] _Int32List(16) [0x64, 0x78, 0x8c, 0xa0, 0xb4, 0xc8, 0xdc, 0xf0, 0x104, 0x118, 0x12c, 0x140, 0x154, 0x168, 0x17c, 0x190]
    //     0x97b078: ldr             x1, [x1, #0xab0]
    // 0x97b07c: ArrayLoad: r1 = r1[r0]  ; TypedSigned_4
    //     0x97b07c: add             x16, x1, w0, sxtw #1
    //     0x97b080: ldursw          x1, [x16, #0x17]
    // 0x97b084: adr             x2, #0x97b02c
    // 0x97b088: add             x2, x2, x1
    // 0x97b08c: br              x2
    // 0x97b090: r0 = Instance_WebResourceErrorType
    //     0x97b090: add             x0, PP, #0x49, lsl #12  ; [pp+0x49ab8] Obj!WebResourceErrorType@e2d6f1
    //     0x97b094: ldr             x0, [x0, #0xab8]
    // 0x97b098: LeaveFrame
    //     0x97b098: mov             SP, fp
    //     0x97b09c: ldp             fp, lr, [SP], #0x10
    // 0x97b0a0: ret
    //     0x97b0a0: ret             
    // 0x97b0a4: r0 = Instance_WebResourceErrorType
    //     0x97b0a4: add             x0, PP, #0x49, lsl #12  ; [pp+0x49ac0] Obj!WebResourceErrorType@e2d6d1
    //     0x97b0a8: ldr             x0, [x0, #0xac0]
    // 0x97b0ac: LeaveFrame
    //     0x97b0ac: mov             SP, fp
    //     0x97b0b0: ldp             fp, lr, [SP], #0x10
    // 0x97b0b4: ret
    //     0x97b0b4: ret             
    // 0x97b0b8: r0 = Instance_WebResourceErrorType
    //     0x97b0b8: add             x0, PP, #0x49, lsl #12  ; [pp+0x49ac8] Obj!WebResourceErrorType@e2d6b1
    //     0x97b0bc: ldr             x0, [x0, #0xac8]
    // 0x97b0c0: LeaveFrame
    //     0x97b0c0: mov             SP, fp
    //     0x97b0c4: ldp             fp, lr, [SP], #0x10
    // 0x97b0c8: ret
    //     0x97b0c8: ret             
    // 0x97b0cc: r0 = Instance_WebResourceErrorType
    //     0x97b0cc: add             x0, PP, #0x49, lsl #12  ; [pp+0x49ad0] Obj!WebResourceErrorType@e2d691
    //     0x97b0d0: ldr             x0, [x0, #0xad0]
    // 0x97b0d4: LeaveFrame
    //     0x97b0d4: mov             SP, fp
    //     0x97b0d8: ldp             fp, lr, [SP], #0x10
    // 0x97b0dc: ret
    //     0x97b0dc: ret             
    // 0x97b0e0: r0 = Instance_WebResourceErrorType
    //     0x97b0e0: add             x0, PP, #0x49, lsl #12  ; [pp+0x49ad8] Obj!WebResourceErrorType@e2d671
    //     0x97b0e4: ldr             x0, [x0, #0xad8]
    // 0x97b0e8: LeaveFrame
    //     0x97b0e8: mov             SP, fp
    //     0x97b0ec: ldp             fp, lr, [SP], #0x10
    // 0x97b0f0: ret
    //     0x97b0f0: ret             
    // 0x97b0f4: r0 = Instance_WebResourceErrorType
    //     0x97b0f4: add             x0, PP, #0x49, lsl #12  ; [pp+0x49ae0] Obj!WebResourceErrorType@e2d651
    //     0x97b0f8: ldr             x0, [x0, #0xae0]
    // 0x97b0fc: LeaveFrame
    //     0x97b0fc: mov             SP, fp
    //     0x97b100: ldp             fp, lr, [SP], #0x10
    // 0x97b104: ret
    //     0x97b104: ret             
    // 0x97b108: r0 = Instance_WebResourceErrorType
    //     0x97b108: add             x0, PP, #0x49, lsl #12  ; [pp+0x49ae8] Obj!WebResourceErrorType@e2d631
    //     0x97b10c: ldr             x0, [x0, #0xae8]
    // 0x97b110: LeaveFrame
    //     0x97b110: mov             SP, fp
    //     0x97b114: ldp             fp, lr, [SP], #0x10
    // 0x97b118: ret
    //     0x97b118: ret             
    // 0x97b11c: r0 = Instance_WebResourceErrorType
    //     0x97b11c: add             x0, PP, #0x49, lsl #12  ; [pp+0x49af0] Obj!WebResourceErrorType@e2d611
    //     0x97b120: ldr             x0, [x0, #0xaf0]
    // 0x97b124: LeaveFrame
    //     0x97b124: mov             SP, fp
    //     0x97b128: ldp             fp, lr, [SP], #0x10
    // 0x97b12c: ret
    //     0x97b12c: ret             
    // 0x97b130: r0 = Instance_WebResourceErrorType
    //     0x97b130: add             x0, PP, #0x49, lsl #12  ; [pp+0x49af8] Obj!WebResourceErrorType@e2d5f1
    //     0x97b134: ldr             x0, [x0, #0xaf8]
    // 0x97b138: LeaveFrame
    //     0x97b138: mov             SP, fp
    //     0x97b13c: ldp             fp, lr, [SP], #0x10
    // 0x97b140: ret
    //     0x97b140: ret             
    // 0x97b144: r0 = Instance_WebResourceErrorType
    //     0x97b144: add             x0, PP, #0x49, lsl #12  ; [pp+0x49b00] Obj!WebResourceErrorType@e2d5d1
    //     0x97b148: ldr             x0, [x0, #0xb00]
    // 0x97b14c: LeaveFrame
    //     0x97b14c: mov             SP, fp
    //     0x97b150: ldp             fp, lr, [SP], #0x10
    // 0x97b154: ret
    //     0x97b154: ret             
    // 0x97b158: r0 = Instance_WebResourceErrorType
    //     0x97b158: add             x0, PP, #0x49, lsl #12  ; [pp+0x49b08] Obj!WebResourceErrorType@e2d5b1
    //     0x97b15c: ldr             x0, [x0, #0xb08]
    // 0x97b160: LeaveFrame
    //     0x97b160: mov             SP, fp
    //     0x97b164: ldp             fp, lr, [SP], #0x10
    // 0x97b168: ret
    //     0x97b168: ret             
    // 0x97b16c: r0 = Instance_WebResourceErrorType
    //     0x97b16c: add             x0, PP, #0x49, lsl #12  ; [pp+0x49b10] Obj!WebResourceErrorType@e2d591
    //     0x97b170: ldr             x0, [x0, #0xb10]
    // 0x97b174: LeaveFrame
    //     0x97b174: mov             SP, fp
    //     0x97b178: ldp             fp, lr, [SP], #0x10
    // 0x97b17c: ret
    //     0x97b17c: ret             
    // 0x97b180: r0 = Instance_WebResourceErrorType
    //     0x97b180: add             x0, PP, #0x49, lsl #12  ; [pp+0x49b18] Obj!WebResourceErrorType@e2d571
    //     0x97b184: ldr             x0, [x0, #0xb18]
    // 0x97b188: LeaveFrame
    //     0x97b188: mov             SP, fp
    //     0x97b18c: ldp             fp, lr, [SP], #0x10
    // 0x97b190: ret
    //     0x97b190: ret             
    // 0x97b194: r0 = Instance_WebResourceErrorType
    //     0x97b194: add             x0, PP, #0x49, lsl #12  ; [pp+0x49b20] Obj!WebResourceErrorType@e2d551
    //     0x97b198: ldr             x0, [x0, #0xb20]
    // 0x97b19c: LeaveFrame
    //     0x97b19c: mov             SP, fp
    //     0x97b1a0: ldp             fp, lr, [SP], #0x10
    // 0x97b1a4: ret
    //     0x97b1a4: ret             
    // 0x97b1a8: r0 = Instance_WebResourceErrorType
    //     0x97b1a8: add             x0, PP, #0x49, lsl #12  ; [pp+0x49b28] Obj!WebResourceErrorType@e2d531
    //     0x97b1ac: ldr             x0, [x0, #0xb28]
    // 0x97b1b0: LeaveFrame
    //     0x97b1b0: mov             SP, fp
    //     0x97b1b4: ldp             fp, lr, [SP], #0x10
    // 0x97b1b8: ret
    //     0x97b1b8: ret             
    // 0x97b1bc: r0 = Instance_WebResourceErrorType
    //     0x97b1bc: add             x0, PP, #0x49, lsl #12  ; [pp+0x49b30] Obj!WebResourceErrorType@e2d511
    //     0x97b1c0: ldr             x0, [x0, #0xb30]
    // 0x97b1c4: LeaveFrame
    //     0x97b1c4: mov             SP, fp
    //     0x97b1c8: ldp             fp, lr, [SP], #0x10
    // 0x97b1cc: ret
    //     0x97b1cc: ret             
    // 0x97b1d0: r1 = Null
    //     0x97b1d0: mov             x1, NULL
    // 0x97b1d4: r2 = 4
    //     0x97b1d4: movz            x2, #0x4
    // 0x97b1d8: r0 = AllocateArray()
    //     0x97b1d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x97b1dc: mov             x2, x0
    // 0x97b1e0: r16 = "Could not find a WebResourceErrorType for errorCode: "
    //     0x97b1e0: add             x16, PP, #0x49, lsl #12  ; [pp+0x49b38] "Could not find a WebResourceErrorType for errorCode: "
    //     0x97b1e4: ldr             x16, [x16, #0xb38]
    // 0x97b1e8: StoreField: r2->field_f = r16
    //     0x97b1e8: stur            w16, [x2, #0xf]
    // 0x97b1ec: ldur            x3, [fp, #-8]
    // 0x97b1f0: r0 = BoxInt64Instr(r3)
    //     0x97b1f0: sbfiz           x0, x3, #1, #0x1f
    //     0x97b1f4: cmp             x3, x0, asr #1
    //     0x97b1f8: b.eq            #0x97b204
    //     0x97b1fc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x97b200: stur            x3, [x0, #7]
    // 0x97b204: StoreField: r2->field_13 = r0
    //     0x97b204: stur            w0, [x2, #0x13]
    // 0x97b208: str             x2, [SP]
    // 0x97b20c: r0 = _interpolate()
    //     0x97b20c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x97b210: stur            x0, [fp, #-0x10]
    // 0x97b214: r0 = ArgumentError()
    //     0x97b214: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x97b218: mov             x1, x0
    // 0x97b21c: ldur            x0, [fp, #-0x10]
    // 0x97b220: ArrayStore: r1[0] = r0  ; List_4
    //     0x97b220: stur            w0, [x1, #0x17]
    // 0x97b224: r0 = false
    //     0x97b224: add             x0, NULL, #0x30  ; false
    // 0x97b228: StoreField: r1->field_b = r0
    //     0x97b228: stur            w0, [x1, #0xb]
    // 0x97b22c: mov             x0, x1
    // 0x97b230: r0 = Throw()
    //     0x97b230: bl              #0xec04b8  ; ThrowStub
    // 0x97b234: brk             #0
    // 0x97b238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b238: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b23c: b               #0x97b04c
  }
}

// class id: 334, size: 0x24, field offset: 0x18
class AndroidWebViewWidgetCreationParams extends PlatformWebViewWidgetCreationParams {

  _ AndroidWebViewWidgetCreationParams.fromPlatformWebViewWidgetCreationParams(/* No info */) {
    // ** addr: 0xa51790, size: 0xc8
    // 0xa51790: EnterFrame
    //     0xa51790: stp             fp, lr, [SP, #-0x10]!
    //     0xa51794: mov             fp, SP
    // 0xa51798: AllocStack(0x10)
    //     0xa51798: sub             SP, SP, #0x10
    // 0xa5179c: r3 = false
    //     0xa5179c: add             x3, NULL, #0x30  ; false
    // 0xa517a0: r0 = Instance_PlatformViewsServiceProxy
    //     0xa517a0: add             x0, PP, #0x49, lsl #12  ; [pp+0x49570] Obj!PlatformViewsServiceProxy@e0bc01
    //     0xa517a4: ldr             x0, [x0, #0x570]
    // 0xa517a8: stur            x1, [fp, #-0x10]
    // 0xa517ac: CheckStackOverflow
    //     0xa517ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa517b0: cmp             SP, x16
    //     0xa517b4: b.ls            #0xa51850
    // 0xa517b8: LoadField: r4 = r2->field_b
    //     0xa517b8: ldur            w4, [x2, #0xb]
    // 0xa517bc: DecompressPointer r4
    //     0xa517bc: add             x4, x4, HEAP, lsl #32
    // 0xa517c0: stur            x4, [fp, #-8]
    // 0xa517c4: StoreField: r1->field_1f = r3
    //     0xa517c4: stur            w3, [x1, #0x1f]
    // 0xa517c8: StoreField: r1->field_1b = r0
    //     0xa517c8: stur            w0, [x1, #0x1b]
    // 0xa517cc: r0 = InitLateStaticField(0xa10) // [package:webview_flutter_android/src/android_webkit.g.dart] PigeonInstanceManager::instance
    //     0xa517cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa517d0: ldr             x0, [x0, #0x1420]
    //     0xa517d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa517d8: cmp             w0, w16
    //     0xa517dc: b.ne            #0xa517ec
    //     0xa517e0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f68] Field <PigeonInstanceManager.instance>: static late final (offset: 0xa10)
    //     0xa517e4: ldr             x2, [x2, #0xf68]
    //     0xa517e8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa517ec: ldur            x1, [fp, #-0x10]
    // 0xa517f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa517f0: stur            w0, [x1, #0x17]
    //     0xa517f4: ldurb           w16, [x1, #-1]
    //     0xa517f8: ldurb           w17, [x0, #-1]
    //     0xa517fc: and             x16, x17, x16, lsr #2
    //     0xa51800: tst             x16, HEAP, lsr #32
    //     0xa51804: b.eq            #0xa5180c
    //     0xa51808: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa5180c: ldur            x0, [fp, #-8]
    // 0xa51810: StoreField: r1->field_b = r0
    //     0xa51810: stur            w0, [x1, #0xb]
    //     0xa51814: ldurb           w16, [x1, #-1]
    //     0xa51818: ldurb           w17, [x0, #-1]
    //     0xa5181c: and             x16, x17, x16, lsr #2
    //     0xa51820: tst             x16, HEAP, lsr #32
    //     0xa51824: b.eq            #0xa5182c
    //     0xa51828: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xa5182c: r2 = Instance_TextDirection
    //     0xa5182c: ldr             x2, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0xa51830: StoreField: r1->field_f = r2
    //     0xa51830: stur            w2, [x1, #0xf]
    // 0xa51834: r2 = _ConstSet len:0
    //     0xa51834: add             x2, PP, #0x42, lsl #12  ; [pp+0x42930] Set<Factory<OneSequenceGestureRecognizer>>(0)
    //     0xa51838: ldr             x2, [x2, #0x930]
    // 0xa5183c: StoreField: r1->field_13 = r2
    //     0xa5183c: stur            w2, [x1, #0x13]
    // 0xa51840: r0 = Null
    //     0xa51840: mov             x0, NULL
    // 0xa51844: LeaveFrame
    //     0xa51844: mov             SP, fp
    //     0xa51848: ldp             fp, lr, [SP], #0x10
    // 0xa5184c: ret
    //     0xa5184c: ret             
    // 0xa51850: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa51850: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa51854: b               #0xa517b8
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf417c, size: 0x7c
    // 0xbf417c: EnterFrame
    //     0xbf417c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4180: mov             fp, SP
    // 0xbf4184: AllocStack(0x18)
    //     0xbf4184: sub             SP, SP, #0x18
    // 0xbf4188: CheckStackOverflow
    //     0xbf4188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf418c: cmp             SP, x16
    //     0xbf4190: b.ls            #0xbf41f0
    // 0xbf4194: ldr             x0, [fp, #0x10]
    // 0xbf4198: LoadField: r1 = r0->field_b
    //     0xbf4198: ldur            w1, [x0, #0xb]
    // 0xbf419c: DecompressPointer r1
    //     0xbf419c: add             x1, x1, HEAP, lsl #32
    // 0xbf41a0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbf41a0: ldur            w2, [x0, #0x17]
    // 0xbf41a4: DecompressPointer r2
    //     0xbf41a4: add             x2, x2, HEAP, lsl #32
    // 0xbf41a8: r16 = false
    //     0xbf41a8: add             x16, NULL, #0x30  ; false
    // 0xbf41ac: r30 = Instance_PlatformViewsServiceProxy
    //     0xbf41ac: add             lr, PP, #0x49, lsl #12  ; [pp+0x49570] Obj!PlatformViewsServiceProxy@e0bc01
    //     0xbf41b0: ldr             lr, [lr, #0x570]
    // 0xbf41b4: stp             lr, x16, [SP, #8]
    // 0xbf41b8: str             x2, [SP]
    // 0xbf41bc: r2 = Instance_TextDirection
    //     0xbf41bc: ldr             x2, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0xbf41c0: r4 = const [0, 0x5, 0x3, 0x5, null]
    //     0xbf41c0: add             x4, PP, #0x1c, lsl #12  ; [pp+0x1ced8] List(5) [0, 0x5, 0x3, 0x5, Null]
    //     0xbf41c4: ldr             x4, [x4, #0xed8]
    // 0xbf41c8: r0 = hash()
    //     0xbf41c8: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf41cc: mov             x2, x0
    // 0xbf41d0: r0 = BoxInt64Instr(r2)
    //     0xbf41d0: sbfiz           x0, x2, #1, #0x1f
    //     0xbf41d4: cmp             x2, x0, asr #1
    //     0xbf41d8: b.eq            #0xbf41e4
    //     0xbf41dc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf41e0: stur            x2, [x0, #7]
    // 0xbf41e4: LeaveFrame
    //     0xbf41e4: mov             SP, fp
    //     0xbf41e8: ldp             fp, lr, [SP], #0x10
    // 0xbf41ec: ret
    //     0xbf41ec: ret             
    // 0xbf41f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf41f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf41f4: b               #0xbf4194
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7f7b8, size: 0x78
    // 0xd7f7b8: ldr             x1, [SP]
    // 0xd7f7bc: cmp             w1, NULL
    // 0xd7f7c0: b.ne            #0xd7f7cc
    // 0xd7f7c4: r0 = false
    //     0xd7f7c4: add             x0, NULL, #0x30  ; false
    // 0xd7f7c8: ret
    //     0xd7f7c8: ret             
    // 0xd7f7cc: r2 = 60
    //     0xd7f7cc: movz            x2, #0x3c
    // 0xd7f7d0: branchIfSmi(r1, 0xd7f7dc)
    //     0xd7f7d0: tbz             w1, #0, #0xd7f7dc
    // 0xd7f7d4: r2 = LoadClassIdInstr(r1)
    //     0xd7f7d4: ldur            x2, [x1, #-1]
    //     0xd7f7d8: ubfx            x2, x2, #0xc, #0x14
    // 0xd7f7dc: cmp             x2, #0x14e
    // 0xd7f7e0: b.ne            #0xd7f828
    // 0xd7f7e4: ldr             x2, [SP, #8]
    // 0xd7f7e8: LoadField: r3 = r2->field_b
    //     0xd7f7e8: ldur            w3, [x2, #0xb]
    // 0xd7f7ec: DecompressPointer r3
    //     0xd7f7ec: add             x3, x3, HEAP, lsl #32
    // 0xd7f7f0: LoadField: r4 = r1->field_b
    //     0xd7f7f0: ldur            w4, [x1, #0xb]
    // 0xd7f7f4: DecompressPointer r4
    //     0xd7f7f4: add             x4, x4, HEAP, lsl #32
    // 0xd7f7f8: cmp             w3, w4
    // 0xd7f7fc: b.ne            #0xd7f828
    // 0xd7f800: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xd7f800: ldur            w3, [x2, #0x17]
    // 0xd7f804: DecompressPointer r3
    //     0xd7f804: add             x3, x3, HEAP, lsl #32
    // 0xd7f808: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd7f808: ldur            w2, [x1, #0x17]
    // 0xd7f80c: DecompressPointer r2
    //     0xd7f80c: add             x2, x2, HEAP, lsl #32
    // 0xd7f810: cmp             w3, w2
    // 0xd7f814: r16 = true
    //     0xd7f814: add             x16, NULL, #0x20  ; true
    // 0xd7f818: r17 = false
    //     0xd7f818: add             x17, NULL, #0x30  ; false
    // 0xd7f81c: csel            x1, x16, x17, eq
    // 0xd7f820: mov             x0, x1
    // 0xd7f824: b               #0xd7f82c
    // 0xd7f828: r0 = false
    //     0xd7f828: add             x0, NULL, #0x30  ; false
    // 0xd7f82c: ret
    //     0xd7f82c: ret             
  }
}

// class id: 336, size: 0x14, field offset: 0x10
class AndroidJavaScriptChannelParams extends JavaScriptChannelParams {

  _ AndroidJavaScriptChannelParams(/* No info */) {
    // ** addr: 0x9724a0, size: 0x104
    // 0x9724a0: EnterFrame
    //     0x9724a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9724a4: mov             fp, SP
    // 0x9724a8: AllocStack(0x48)
    //     0x9724a8: sub             SP, SP, #0x48
    // 0x9724ac: SetupParameters(AndroidJavaScriptChannelParams this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x9724ac: mov             x4, x1
    //     0x9724b0: mov             x0, x3
    //     0x9724b4: stur            x3, [fp, #-0x18]
    //     0x9724b8: mov             x3, x2
    //     0x9724bc: stur            x1, [fp, #-8]
    //     0x9724c0: stur            x2, [fp, #-0x10]
    // 0x9724c4: CheckStackOverflow
    //     0x9724c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9724c8: cmp             SP, x16
    //     0x9724cc: b.ls            #0x97259c
    // 0x9724d0: r1 = Function '<anonymous closure>':.
    //     0x9724d0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49248] AnonymousClosure: (0x972c7c), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidJavaScriptChannelParams::AndroidJavaScriptChannelParams (0x9724a0)
    //     0x9724d4: ldr             x1, [x1, #0x248]
    // 0x9724d8: r2 = Null
    //     0x9724d8: mov             x2, NULL
    // 0x9724dc: r0 = AllocateClosure()
    //     0x9724dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x9724e0: r16 = <(dynamic this, JavaScriptMessage) => void?, (dynamic this, JavaScriptChannel, String) => void?>
    //     0x9724e0: add             x16, PP, #0x49, lsl #12  ; [pp+0x49250] TypeArguments: <(dynamic this, JavaScriptMessage) => void?, (dynamic this, JavaScriptChannel, String) => void?>
    //     0x9724e4: ldr             x16, [x16, #0x250]
    // 0x9724e8: ldur            lr, [fp, #-0x18]
    // 0x9724ec: stp             lr, x16, [SP, #8]
    // 0x9724f0: str             x0, [SP]
    // 0x9724f4: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x9724f4: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x9724f8: r0 = withWeakReferenceTo()
    //     0x9724f8: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x9724fc: stur            x0, [fp, #-0x20]
    // 0x972500: r0 = JavaScriptChannel()
    //     0x972500: bl              #0x929b0c  ; AllocateJavaScriptChannelStub -> JavaScriptChannel (size=0x1c)
    // 0x972504: stur            x0, [fp, #-0x28]
    // 0x972508: stp             NULL, NULL, [SP, #0x10]
    // 0x97250c: ldur            x16, [fp, #-0x10]
    // 0x972510: ldur            lr, [fp, #-0x20]
    // 0x972514: stp             lr, x16, [SP]
    // 0x972518: mov             x1, x0
    // 0x97251c: r4 = const [0, 0x5, 0x4, 0x1, channelName, 0x3, pigeon_binaryMessenger, 0x1, pigeon_instanceManager, 0x2, postMessage, 0x4, null]
    //     0x97251c: add             x4, PP, #0x49, lsl #12  ; [pp+0x49258] List(13) [0, 0x5, 0x4, 0x1, "channelName", 0x3, "pigeon_binaryMessenger", 0x1, "pigeon_instanceManager", 0x2, "postMessage", 0x4, Null]
    //     0x972520: ldr             x4, [x4, #0x258]
    // 0x972524: r0 = JavaScriptChannel()
    //     0x972524: bl              #0x9726ec  ; [package:webview_flutter_android/src/android_webkit.g.dart] JavaScriptChannel::JavaScriptChannel
    // 0x972528: ldur            x0, [fp, #-0x28]
    // 0x97252c: ldur            x1, [fp, #-8]
    // 0x972530: StoreField: r1->field_f = r0
    //     0x972530: stur            w0, [x1, #0xf]
    //     0x972534: ldurb           w16, [x1, #-1]
    //     0x972538: ldurb           w17, [x0, #-1]
    //     0x97253c: and             x16, x17, x16, lsr #2
    //     0x972540: tst             x16, HEAP, lsr #32
    //     0x972544: b.eq            #0x97254c
    //     0x972548: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97254c: ldur            x0, [fp, #-0x10]
    // 0x972550: StoreField: r1->field_7 = r0
    //     0x972550: stur            w0, [x1, #7]
    //     0x972554: ldurb           w16, [x1, #-1]
    //     0x972558: ldurb           w17, [x0, #-1]
    //     0x97255c: and             x16, x17, x16, lsr #2
    //     0x972560: tst             x16, HEAP, lsr #32
    //     0x972564: b.eq            #0x97256c
    //     0x972568: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97256c: ldur            x0, [fp, #-0x18]
    // 0x972570: StoreField: r1->field_b = r0
    //     0x972570: stur            w0, [x1, #0xb]
    //     0x972574: ldurb           w16, [x1, #-1]
    //     0x972578: ldurb           w17, [x0, #-1]
    //     0x97257c: and             x16, x17, x16, lsr #2
    //     0x972580: tst             x16, HEAP, lsr #32
    //     0x972584: b.eq            #0x97258c
    //     0x972588: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97258c: r0 = Null
    //     0x97258c: mov             x0, NULL
    // 0x972590: LeaveFrame
    //     0x972590: mov             SP, fp
    //     0x972594: ldp             fp, lr, [SP], #0x10
    // 0x972598: ret
    //     0x972598: ret             
    // 0x97259c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97259c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9725a0: b               #0x9724d0
  }
  [closure] (dynamic, JavaScriptChannel, String) => void <anonymous closure>(dynamic, WeakReference<(dynamic, JavaScriptMessage) => void>) {
    // ** addr: 0x972c7c, size: 0x54
    // 0x972c7c: EnterFrame
    //     0x972c7c: stp             fp, lr, [SP, #-0x10]!
    //     0x972c80: mov             fp, SP
    // 0x972c84: AllocStack(0x8)
    //     0x972c84: sub             SP, SP, #8
    // 0x972c88: SetupParameters()
    //     0x972c88: ldr             x0, [fp, #0x18]
    //     0x972c8c: ldur            w1, [x0, #0x17]
    //     0x972c90: add             x1, x1, HEAP, lsl #32
    //     0x972c94: stur            x1, [fp, #-8]
    // 0x972c98: r1 = 1
    //     0x972c98: movz            x1, #0x1
    // 0x972c9c: r0 = AllocateContext()
    //     0x972c9c: bl              #0xec126c  ; AllocateContextStub
    // 0x972ca0: mov             x1, x0
    // 0x972ca4: ldur            x0, [fp, #-8]
    // 0x972ca8: StoreField: r1->field_b = r0
    //     0x972ca8: stur            w0, [x1, #0xb]
    // 0x972cac: ldr             x0, [fp, #0x10]
    // 0x972cb0: StoreField: r1->field_f = r0
    //     0x972cb0: stur            w0, [x1, #0xf]
    // 0x972cb4: mov             x2, x1
    // 0x972cb8: r1 = Function '<anonymous closure>':.
    //     0x972cb8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49260] AnonymousClosure: (0x972cd0), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidJavaScriptChannelParams::AndroidJavaScriptChannelParams (0x9724a0)
    //     0x972cbc: ldr             x1, [x1, #0x260]
    // 0x972cc0: r0 = AllocateClosure()
    //     0x972cc0: bl              #0xec1630  ; AllocateClosureStub
    // 0x972cc4: LeaveFrame
    //     0x972cc4: mov             SP, fp
    //     0x972cc8: ldp             fp, lr, [SP], #0x10
    // 0x972ccc: ret
    //     0x972ccc: ret             
  }
  [closure] void <anonymous closure>(dynamic, JavaScriptChannel, String) {
    // ** addr: 0x972cd0, size: 0x98
    // 0x972cd0: EnterFrame
    //     0x972cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x972cd4: mov             fp, SP
    // 0x972cd8: AllocStack(0x18)
    //     0x972cd8: sub             SP, SP, #0x18
    // 0x972cdc: SetupParameters()
    //     0x972cdc: ldr             x0, [fp, #0x20]
    //     0x972ce0: ldur            w1, [x0, #0x17]
    //     0x972ce4: add             x1, x1, HEAP, lsl #32
    // 0x972ce8: CheckStackOverflow
    //     0x972ce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x972cec: cmp             SP, x16
    //     0x972cf0: b.ls            #0x972d5c
    // 0x972cf4: LoadField: r0 = r1->field_f
    //     0x972cf4: ldur            w0, [x1, #0xf]
    // 0x972cf8: DecompressPointer r0
    //     0x972cf8: add             x0, x0, HEAP, lsl #32
    // 0x972cfc: LoadField: r1 = r0->field_7
    //     0x972cfc: ldur            w1, [x0, #7]
    // 0x972d00: DecompressPointer r1
    //     0x972d00: add             x1, x1, HEAP, lsl #32
    // 0x972d04: cmp             w1, NULL
    // 0x972d08: b.eq            #0x972d4c
    // 0x972d0c: ldr             x1, [fp, #0x10]
    // 0x972d10: LoadField: r2 = r0->field_7
    //     0x972d10: ldur            w2, [x0, #7]
    // 0x972d14: DecompressPointer r2
    //     0x972d14: add             x2, x2, HEAP, lsl #32
    // 0x972d18: stur            x2, [fp, #-8]
    // 0x972d1c: cmp             w2, NULL
    // 0x972d20: b.eq            #0x972d64
    // 0x972d24: r0 = JavaScriptMessage()
    //     0x972d24: bl              #0x972d68  ; AllocateJavaScriptMessageStub -> JavaScriptMessage (size=0xc)
    // 0x972d28: mov             x1, x0
    // 0x972d2c: ldr             x0, [fp, #0x10]
    // 0x972d30: StoreField: r1->field_7 = r0
    //     0x972d30: stur            w0, [x1, #7]
    // 0x972d34: ldur            x16, [fp, #-8]
    // 0x972d38: stp             x1, x16, [SP]
    // 0x972d3c: ldur            x0, [fp, #-8]
    // 0x972d40: ClosureCall
    //     0x972d40: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x972d44: ldur            x2, [x0, #0x1f]
    //     0x972d48: blr             x2
    // 0x972d4c: r0 = Null
    //     0x972d4c: mov             x0, NULL
    // 0x972d50: LeaveFrame
    //     0x972d50: mov             SP, fp
    //     0x972d54: ldp             fp, lr, [SP], #0x10
    // 0x972d58: ret
    //     0x972d58: ret             
    // 0x972d5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x972d5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x972d60: b               #0x972cf4
    // 0x972d64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x972d64: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 346, size: 0xc, field offset: 0x8
class AndroidWebViewControllerCreationParams extends PlatformWebViewControllerCreationParams {

  factory _ AndroidWebViewControllerCreationParams.fromPlatformWebViewControllerCreationParams(/* No info */) {
    // ** addr: 0x975c70, size: 0x58
    // 0x975c70: EnterFrame
    //     0x975c70: stp             fp, lr, [SP, #-0x10]!
    //     0x975c74: mov             fp, SP
    // 0x975c78: CheckStackOverflow
    //     0x975c78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x975c7c: cmp             SP, x16
    //     0x975c80: b.ls            #0x975cc0
    // 0x975c84: r0 = InitLateStaticField(0xa00) // [package:webview_flutter_android/src/android_webkit.g.dart] WebStorage::instance
    //     0x975c84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x975c88: ldr             x0, [x0, #0x1400]
    //     0x975c8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x975c90: cmp             w0, w16
    //     0x975c94: b.ne            #0x975ca4
    //     0x975c98: add             x2, PP, #0x49, lsl #12  ; [pp+0x49820] Field <WebStorage.instance>: static late final (offset: 0xa00)
    //     0x975c9c: ldr             x2, [x2, #0x820]
    //     0x975ca0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x975ca4: r0 = AndroidWebViewControllerCreationParams()
    //     0x975ca4: bl              #0x975cc8  ; AllocateAndroidWebViewControllerCreationParamsStub -> AndroidWebViewControllerCreationParams (size=0xc)
    // 0x975ca8: r1 = Instance_AndroidWebViewProxy
    //     0x975ca8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49828] Obj!AndroidWebViewProxy@e0bc11
    //     0x975cac: ldr             x1, [x1, #0x828]
    // 0x975cb0: StoreField: r0->field_7 = r1
    //     0x975cb0: stur            w1, [x0, #7]
    // 0x975cb4: LeaveFrame
    //     0x975cb4: mov             SP, fp
    //     0x975cb8: ldp             fp, lr, [SP], #0x10
    // 0x975cbc: ret
    //     0x975cbc: ret             
    // 0x975cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x975cc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x975cc4: b               #0x975c84
  }
}

// class id: 4903, size: 0x18, field offset: 0xc
class AndroidCustomViewWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xbbd120, size: 0x98
    // 0xbbd120: EnterFrame
    //     0xbbd120: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd124: mov             fp, SP
    // 0xbbd128: AllocStack(0x18)
    //     0xbbd128: sub             SP, SP, #0x18
    // 0xbbd12c: SetupParameters(AndroidCustomViewWidget this /* r1 => r1, fp-0x8 */)
    //     0xbbd12c: stur            x1, [fp, #-8]
    // 0xbbd130: r1 = 1
    //     0xbbd130: movz            x1, #0x1
    // 0xbbd134: r0 = AllocateContext()
    //     0xbbd134: bl              #0xec126c  ; AllocateContextStub
    // 0xbbd138: mov             x1, x0
    // 0xbbd13c: ldur            x0, [fp, #-8]
    // 0xbbd140: stur            x1, [fp, #-0x18]
    // 0xbbd144: StoreField: r1->field_f = r0
    //     0xbbd144: stur            w0, [x1, #0xf]
    // 0xbbd148: LoadField: r2 = r0->field_7
    //     0xbbd148: ldur            w2, [x0, #7]
    // 0xbbd14c: DecompressPointer r2
    //     0xbbd14c: add             x2, x2, HEAP, lsl #32
    // 0xbbd150: stur            x2, [fp, #-0x10]
    // 0xbbd154: r0 = PlatformViewLink()
    //     0xbbd154: bl              #0xa25090  ; AllocatePlatformViewLinkStub -> PlatformViewLink (size=0x18)
    // 0xbbd158: mov             x3, x0
    // 0xbbd15c: r0 = "plugins.flutter.io/webview"
    //     0xbbd15c: add             x0, PP, #0x55, lsl #12  ; [pp+0x55240] "plugins.flutter.io/webview"
    //     0xbbd160: ldr             x0, [x0, #0x240]
    // 0xbbd164: stur            x3, [fp, #-8]
    // 0xbbd168: StoreField: r3->field_13 = r0
    //     0xbbd168: stur            w0, [x3, #0x13]
    // 0xbbd16c: r1 = Function '<anonymous closure>':.
    //     0xbbd16c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55248] AnonymousClosure: (0xbbd2f0), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidCustomViewWidget::build (0xbbd120)
    //     0xbbd170: ldr             x1, [x1, #0x248]
    // 0xbbd174: r2 = Null
    //     0xbbd174: mov             x2, NULL
    // 0xbbd178: r0 = AllocateClosure()
    //     0xbbd178: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbd17c: mov             x1, x0
    // 0xbbd180: ldur            x0, [fp, #-8]
    // 0xbbd184: StoreField: r0->field_b = r1
    //     0xbbd184: stur            w1, [x0, #0xb]
    // 0xbbd188: ldur            x2, [fp, #-0x18]
    // 0xbbd18c: r1 = Function '<anonymous closure>':.
    //     0xbbd18c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55250] AnonymousClosure: (0xbbd1b8), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidCustomViewWidget::build (0xbbd120)
    //     0xbbd190: ldr             x1, [x1, #0x250]
    // 0xbbd194: r0 = AllocateClosure()
    //     0xbbd194: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbd198: mov             x1, x0
    // 0xbbd19c: ldur            x0, [fp, #-8]
    // 0xbbd1a0: StoreField: r0->field_f = r1
    //     0xbbd1a0: stur            w1, [x0, #0xf]
    // 0xbbd1a4: ldur            x1, [fp, #-0x10]
    // 0xbbd1a8: StoreField: r0->field_7 = r1
    //     0xbbd1a8: stur            w1, [x0, #7]
    // 0xbbd1ac: LeaveFrame
    //     0xbbd1ac: mov             SP, fp
    //     0xbbd1b0: ldp             fp, lr, [SP], #0x10
    // 0xbbd1b4: ret
    //     0xbbd1b4: ret             
  }
  [closure] AndroidViewController <anonymous closure>(dynamic, PlatformViewCreationParams) {
    // ** addr: 0xbbd1b8, size: 0x138
    // 0xbbd1b8: EnterFrame
    //     0xbbd1b8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd1bc: mov             fp, SP
    // 0xbbd1c0: AllocStack(0x20)
    //     0xbbd1c0: sub             SP, SP, #0x20
    // 0xbbd1c4: SetupParameters()
    //     0xbbd1c4: ldr             x0, [fp, #0x18]
    //     0xbbd1c8: ldur            w1, [x0, #0x17]
    //     0xbbd1cc: add             x1, x1, HEAP, lsl #32
    // 0xbbd1d0: CheckStackOverflow
    //     0xbbd1d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbd1d4: cmp             SP, x16
    //     0xbbd1d8: b.ls            #0xbbd2e8
    // 0xbbd1dc: LoadField: r0 = r1->field_f
    //     0xbbd1dc: ldur            w0, [x1, #0xf]
    // 0xbbd1e0: DecompressPointer r0
    //     0xbbd1e0: add             x0, x0, HEAP, lsl #32
    // 0xbbd1e4: LoadField: r3 = r0->field_b
    //     0xbbd1e4: ldur            w3, [x0, #0xb]
    // 0xbbd1e8: DecompressPointer r3
    //     0xbbd1e8: add             x3, x3, HEAP, lsl #32
    // 0xbbd1ec: LoadField: r2 = r0->field_f
    //     0xbbd1ec: ldur            w2, [x0, #0xf]
    // 0xbbd1f0: DecompressPointer r2
    //     0xbbd1f0: add             x2, x2, HEAP, lsl #32
    // 0xbbd1f4: ldr             x1, [fp, #0x10]
    // 0xbbd1f8: r0 = _initAndroidView()
    //     0xbbd1f8: bl              #0xbbcfb4  ; [package:webview_flutter_android/src/android_webview_controller.dart] ::_initAndroidView
    // 0xbbd1fc: mov             x3, x0
    // 0xbbd200: ldr             x0, [fp, #0x10]
    // 0xbbd204: stur            x3, [fp, #-0x18]
    // 0xbbd208: LoadField: r4 = r0->field_f
    //     0xbbd208: ldur            w4, [x0, #0xf]
    // 0xbbd20c: DecompressPointer r4
    //     0xbbd20c: add             x4, x4, HEAP, lsl #32
    // 0xbbd210: stur            x4, [fp, #-0x10]
    // 0xbbd214: LoadField: r5 = r3->field_23
    //     0xbbd214: ldur            w5, [x3, #0x23]
    // 0xbbd218: DecompressPointer r5
    //     0xbbd218: add             x5, x5, HEAP, lsl #32
    // 0xbbd21c: stur            x5, [fp, #-8]
    // 0xbbd220: LoadField: r2 = r5->field_7
    //     0xbbd220: ldur            w2, [x5, #7]
    // 0xbbd224: DecompressPointer r2
    //     0xbbd224: add             x2, x2, HEAP, lsl #32
    // 0xbbd228: mov             x0, x4
    // 0xbbd22c: r1 = Null
    //     0xbbd22c: mov             x1, NULL
    // 0xbbd230: cmp             w2, NULL
    // 0xbbd234: b.eq            #0xbbd254
    // 0xbbd238: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbd238: ldur            w4, [x2, #0x17]
    // 0xbbd23c: DecompressPointer r4
    //     0xbbd23c: add             x4, x4, HEAP, lsl #32
    // 0xbbd240: r8 = X0
    //     0xbbd240: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xbbd244: LoadField: r9 = r4->field_7
    //     0xbbd244: ldur            x9, [x4, #7]
    // 0xbbd248: r3 = Null
    //     0xbbd248: add             x3, PP, #0x55, lsl #12  ; [pp+0x55258] Null
    //     0xbbd24c: ldr             x3, [x3, #0x258]
    // 0xbbd250: blr             x9
    // 0xbbd254: ldur            x0, [fp, #-8]
    // 0xbbd258: LoadField: r1 = r0->field_b
    //     0xbbd258: ldur            w1, [x0, #0xb]
    // 0xbbd25c: LoadField: r2 = r0->field_f
    //     0xbbd25c: ldur            w2, [x0, #0xf]
    // 0xbbd260: DecompressPointer r2
    //     0xbbd260: add             x2, x2, HEAP, lsl #32
    // 0xbbd264: LoadField: r3 = r2->field_b
    //     0xbbd264: ldur            w3, [x2, #0xb]
    // 0xbbd268: r2 = LoadInt32Instr(r1)
    //     0xbbd268: sbfx            x2, x1, #1, #0x1f
    // 0xbbd26c: stur            x2, [fp, #-0x20]
    // 0xbbd270: r1 = LoadInt32Instr(r3)
    //     0xbbd270: sbfx            x1, x3, #1, #0x1f
    // 0xbbd274: cmp             x2, x1
    // 0xbbd278: b.ne            #0xbbd284
    // 0xbbd27c: mov             x1, x0
    // 0xbbd280: r0 = _growToNextCapacity()
    //     0xbbd280: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbd284: ldur            x0, [fp, #-8]
    // 0xbbd288: ldur            x2, [fp, #-0x20]
    // 0xbbd28c: add             x1, x2, #1
    // 0xbbd290: lsl             x3, x1, #1
    // 0xbbd294: StoreField: r0->field_b = r3
    //     0xbbd294: stur            w3, [x0, #0xb]
    // 0xbbd298: LoadField: r1 = r0->field_f
    //     0xbbd298: ldur            w1, [x0, #0xf]
    // 0xbbd29c: DecompressPointer r1
    //     0xbbd29c: add             x1, x1, HEAP, lsl #32
    // 0xbbd2a0: ldur            x0, [fp, #-0x10]
    // 0xbbd2a4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbbd2a4: add             x25, x1, x2, lsl #2
    //     0xbbd2a8: add             x25, x25, #0xf
    //     0xbbd2ac: str             w0, [x25]
    //     0xbbd2b0: tbz             w0, #0, #0xbbd2cc
    //     0xbbd2b4: ldurb           w16, [x1, #-1]
    //     0xbbd2b8: ldurb           w17, [x0, #-1]
    //     0xbbd2bc: and             x16, x17, x16, lsr #2
    //     0xbbd2c0: tst             x16, HEAP, lsr #32
    //     0xbbd2c4: b.eq            #0xbbd2cc
    //     0xbbd2c8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbd2cc: ldur            x1, [fp, #-0x18]
    // 0xbbd2d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbbd2d0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbbd2d4: r0 = create()
    //     0xbbd2d4: bl              #0x7e1f3c  ; [package:flutter/src/services/platform_views.dart] AndroidViewController::create
    // 0xbbd2d8: ldur            x0, [fp, #-0x18]
    // 0xbbd2dc: LeaveFrame
    //     0xbbd2dc: mov             SP, fp
    //     0xbbd2e0: ldp             fp, lr, [SP], #0x10
    // 0xbbd2e4: ret
    //     0xbbd2e4: ret             
    // 0xbbd2e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbd2e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbd2ec: b               #0xbbd1dc
  }
  [closure] AndroidViewSurface <anonymous closure>(dynamic, BuildContext, PlatformViewController) {
    // ** addr: 0xbbd2f0, size: 0x6c
    // 0xbbd2f0: EnterFrame
    //     0xbbd2f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd2f4: mov             fp, SP
    // 0xbbd2f8: ldr             x0, [fp, #0x10]
    // 0xbbd2fc: r2 = Null
    //     0xbbd2fc: mov             x2, NULL
    // 0xbbd300: r1 = Null
    //     0xbbd300: mov             x1, NULL
    // 0xbbd304: r4 = LoadClassIdInstr(r0)
    //     0xbbd304: ldur            x4, [x0, #-1]
    //     0xbbd308: ubfx            x4, x4, #0xc, #0x14
    // 0xbbd30c: sub             x4, x4, #0xadb
    // 0xbbd310: cmp             x4, #1
    // 0xbbd314: b.ls            #0xbbd32c
    // 0xbbd318: r8 = AndroidViewController
    //     0xbbd318: add             x8, PP, #0x42, lsl #12  ; [pp+0x42910] Type: AndroidViewController
    //     0xbbd31c: ldr             x8, [x8, #0x910]
    // 0xbbd320: r3 = Null
    //     0xbbd320: add             x3, PP, #0x55, lsl #12  ; [pp+0x55270] Null
    //     0xbbd324: ldr             x3, [x3, #0x270]
    // 0xbbd328: r0 = DefaultTypeTest()
    //     0xbbd328: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xbbd32c: r0 = AndroidViewSurface()
    //     0xbbd32c: bl              #0xa2ceb0  ; AllocateAndroidViewSurfaceStub -> AndroidViewSurface (size=0x18)
    // 0xbbd330: ldr             x1, [fp, #0x10]
    // 0xbbd334: StoreField: r0->field_b = r1
    //     0xbbd334: stur            w1, [x0, #0xb]
    // 0xbbd338: r1 = Instance_PlatformViewHitTestBehavior
    //     0xbbd338: add             x1, PP, #0x42, lsl #12  ; [pp+0x42928] Obj!PlatformViewHitTestBehavior@e35961
    //     0xbbd33c: ldr             x1, [x1, #0x928]
    // 0xbbd340: StoreField: r0->field_13 = r1
    //     0xbbd340: stur            w1, [x0, #0x13]
    // 0xbbd344: r1 = _ConstSet len:0
    //     0xbbd344: add             x1, PP, #0x42, lsl #12  ; [pp+0x42930] Set<Factory<OneSequenceGestureRecognizer>>(0)
    //     0xbbd348: ldr             x1, [x1, #0x930]
    // 0xbbd34c: StoreField: r0->field_f = r1
    //     0xbbd34c: stur            w1, [x0, #0xf]
    // 0xbbd350: LeaveFrame
    //     0xbbd350: mov             SP, fp
    //     0xbbd354: ldp             fp, lr, [SP], #0x10
    // 0xbbd358: ret
    //     0xbbd358: ret             
  }
}

// class id: 5852, size: 0x38, field offset: 0xc
class AndroidNavigationDelegate extends PlatformNavigationDelegate {

  late final WebViewClient _webViewClient; // offset: 0xc
  late final DownloadListener _downloadListener; // offset: 0x10

  _ setOnLoadRequest(/* No info */) async {
    // ** addr: 0x973820, size: 0x6c
    // 0x973820: EnterFrame
    //     0x973820: stp             fp, lr, [SP, #-0x10]!
    //     0x973824: mov             fp, SP
    // 0x973828: AllocStack(0x18)
    //     0x973828: sub             SP, SP, #0x18
    // 0x97382c: SetupParameters(AndroidNavigationDelegate this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x97382c: stur            NULL, [fp, #-8]
    //     0x973830: stur            x1, [fp, #-0x10]
    //     0x973834: mov             x16, x2
    //     0x973838: mov             x2, x1
    //     0x97383c: mov             x1, x16
    //     0x973840: stur            x1, [fp, #-0x18]
    // 0x973844: CheckStackOverflow
    //     0x973844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x973848: cmp             SP, x16
    //     0x97384c: b.ls            #0x973884
    // 0x973850: InitAsync() -> Future<void?>
    //     0x973850: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x973854: bl              #0x661298  ; InitAsyncStub
    // 0x973858: ldur            x0, [fp, #-0x18]
    // 0x97385c: ldur            x1, [fp, #-0x10]
    // 0x973860: StoreField: r1->field_2b = r0
    //     0x973860: stur            w0, [x1, #0x2b]
    //     0x973864: ldurb           w16, [x1, #-1]
    //     0x973868: ldurb           w17, [x0, #-1]
    //     0x97386c: and             x16, x17, x16, lsr #2
    //     0x973870: tst             x16, HEAP, lsr #32
    //     0x973874: b.eq            #0x97387c
    //     0x973878: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97387c: r0 = Null
    //     0x97387c: mov             x0, NULL
    // 0x973880: r0 = ReturnAsyncNotFuture()
    //     0x973880: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x973884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x973884: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x973888: b               #0x973850
  }
  _ setOnWebResourceError(/* No info */) async {
    // ** addr: 0x97a06c, size: 0x6c
    // 0x97a06c: EnterFrame
    //     0x97a06c: stp             fp, lr, [SP, #-0x10]!
    //     0x97a070: mov             fp, SP
    // 0x97a074: AllocStack(0x18)
    //     0x97a074: sub             SP, SP, #0x18
    // 0x97a078: SetupParameters(AndroidNavigationDelegate this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x97a078: stur            NULL, [fp, #-8]
    //     0x97a07c: stur            x1, [fp, #-0x10]
    //     0x97a080: mov             x16, x2
    //     0x97a084: mov             x2, x1
    //     0x97a088: mov             x1, x16
    //     0x97a08c: stur            x1, [fp, #-0x18]
    // 0x97a090: CheckStackOverflow
    //     0x97a090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a094: cmp             SP, x16
    //     0x97a098: b.ls            #0x97a0d0
    // 0x97a09c: InitAsync() -> Future<void?>
    //     0x97a09c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x97a0a0: bl              #0x661298  ; InitAsyncStub
    // 0x97a0a4: ldur            x0, [fp, #-0x18]
    // 0x97a0a8: ldur            x1, [fp, #-0x10]
    // 0x97a0ac: StoreField: r1->field_23 = r0
    //     0x97a0ac: stur            w0, [x1, #0x23]
    //     0x97a0b0: ldurb           w16, [x1, #-1]
    //     0x97a0b4: ldurb           w17, [x0, #-1]
    //     0x97a0b8: and             x16, x17, x16, lsr #2
    //     0x97a0bc: tst             x16, HEAP, lsr #32
    //     0x97a0c0: b.eq            #0x97a0c8
    //     0x97a0c4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97a0c8: r0 = Null
    //     0x97a0c8: mov             x0, NULL
    // 0x97a0cc: r0 = ReturnAsyncNotFuture()
    //     0x97a0cc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97a0d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a0d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a0d4: b               #0x97a09c
  }
  _ setOnNavigationRequest(/* No info */) async {
    // ** addr: 0x97a0d8, size: 0x90
    // 0x97a0d8: EnterFrame
    //     0x97a0d8: stp             fp, lr, [SP, #-0x10]!
    //     0x97a0dc: mov             fp, SP
    // 0x97a0e0: AllocStack(0x18)
    //     0x97a0e0: sub             SP, SP, #0x18
    // 0x97a0e4: SetupParameters(AndroidNavigationDelegate this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x97a0e4: stur            NULL, [fp, #-8]
    //     0x97a0e8: stur            x1, [fp, #-0x10]
    //     0x97a0ec: mov             x16, x2
    //     0x97a0f0: mov             x2, x1
    //     0x97a0f4: mov             x1, x16
    //     0x97a0f8: stur            x1, [fp, #-0x18]
    // 0x97a0fc: CheckStackOverflow
    //     0x97a0fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a100: cmp             SP, x16
    //     0x97a104: b.ls            #0x97a154
    // 0x97a108: InitAsync() -> Future<void?>
    //     0x97a108: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x97a10c: bl              #0x661298  ; InitAsyncStub
    // 0x97a110: ldur            x0, [fp, #-0x18]
    // 0x97a114: ldur            x1, [fp, #-0x10]
    // 0x97a118: StoreField: r1->field_27 = r0
    //     0x97a118: stur            w0, [x1, #0x27]
    //     0x97a11c: ldurb           w16, [x1, #-1]
    //     0x97a120: ldurb           w17, [x0, #-1]
    //     0x97a124: and             x16, x17, x16, lsr #2
    //     0x97a128: tst             x16, HEAP, lsr #32
    //     0x97a12c: b.eq            #0x97a134
    //     0x97a130: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97a134: LoadField: r0 = r1->field_b
    //     0x97a134: ldur            w0, [x1, #0xb]
    // 0x97a138: DecompressPointer r0
    //     0x97a138: add             x0, x0, HEAP, lsl #32
    // 0x97a13c: r16 = Sentinel
    //     0x97a13c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97a140: cmp             w0, w16
    // 0x97a144: b.eq            #0x97a15c
    // 0x97a148: mov             x1, x0
    // 0x97a14c: r0 = setSynchronousReturnValueForShouldOverrideUrlLoading()
    //     0x97a14c: bl              #0x97a168  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebViewClient::setSynchronousReturnValueForShouldOverrideUrlLoading
    // 0x97a150: r0 = ReturnAsync()
    //     0x97a150: b               #0x6576a4  ; ReturnAsyncStub
    // 0x97a154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a154: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a158: b               #0x97a108
    // 0x97a15c: r9 = _webViewClient
    //     0x97a15c: add             x9, PP, #0x49, lsl #12  ; [pp+0x492f0] Field <AndroidNavigationDelegate._webViewClient@470193571>: late final (offset: 0xc)
    //     0x97a160: ldr             x9, [x9, #0x2f0]
    // 0x97a164: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97a164: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ AndroidNavigationDelegate(/* No info */) {
    // ** addr: 0x97a4e0, size: 0x2f0
    // 0x97a4e0: EnterFrame
    //     0x97a4e0: stp             fp, lr, [SP, #-0x10]!
    //     0x97a4e4: mov             fp, SP
    // 0x97a4e8: AllocStack(0xc8)
    //     0x97a4e8: sub             SP, SP, #0xc8
    // 0x97a4ec: r0 = Sentinel
    //     0x97a4ec: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97a4f0: mov             x2, x1
    // 0x97a4f4: stur            x1, [fp, #-8]
    // 0x97a4f8: CheckStackOverflow
    //     0x97a4f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a4fc: cmp             SP, x16
    //     0x97a500: b.ls            #0x97a7c8
    // 0x97a504: StoreField: r2->field_b = r0
    //     0x97a504: stur            w0, [x2, #0xb]
    // 0x97a508: StoreField: r2->field_f = r0
    //     0x97a508: stur            w0, [x2, #0xf]
    // 0x97a50c: r1 = Null
    //     0x97a50c: mov             x1, NULL
    // 0x97a510: r0 = AndroidNavigationDelegateCreationParams.fromPlatformNavigationDelegateCreationParams()
    //     0x97a510: bl              #0x97a7d0  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegateCreationParams::AndroidNavigationDelegateCreationParams.fromPlatformNavigationDelegateCreationParams
    // 0x97a514: ldur            x2, [fp, #-8]
    // 0x97a518: StoreField: r2->field_7 = r0
    //     0x97a518: stur            w0, [x2, #7]
    //     0x97a51c: ldurb           w16, [x2, #-1]
    //     0x97a520: ldurb           w17, [x0, #-1]
    //     0x97a524: and             x16, x17, x16, lsr #2
    //     0x97a528: tst             x16, HEAP, lsr #32
    //     0x97a52c: b.eq            #0x97a534
    //     0x97a530: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x97a534: r0 = InitLateStaticField(0x9f0) // [package:webview_flutter_platform_interface/src/platform_navigation_delegate.dart] PlatformNavigationDelegate::_token
    //     0x97a534: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97a538: ldr             x0, [x0, #0x13e0]
    //     0x97a53c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x97a540: cmp             w0, w16
    //     0x97a544: b.ne            #0x97a554
    //     0x97a548: add             x2, PP, #0x49, lsl #12  ; [pp+0x499d8] Field <PlatformNavigationDelegate._token@473149494>: static late final (offset: 0x9f0)
    //     0x97a54c: ldr             x2, [x2, #0x9d8]
    //     0x97a550: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x97a554: stur            x0, [fp, #-0x10]
    // 0x97a558: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x97a558: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97a55c: ldr             x0, [x0, #0xc08]
    //     0x97a560: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x97a564: cmp             w0, w16
    //     0x97a568: b.ne            #0x97a574
    //     0x97a56c: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x97a570: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x97a574: mov             x1, x0
    // 0x97a578: ldur            x2, [fp, #-8]
    // 0x97a57c: ldur            x3, [fp, #-0x10]
    // 0x97a580: r0 = []=()
    //     0x97a580: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x97a584: r1 = <AndroidNavigationDelegate>
    //     0x97a584: add             x1, PP, #0x49, lsl #12  ; [pp+0x499e0] TypeArguments: <AndroidNavigationDelegate>
    //     0x97a588: ldr             x1, [x1, #0x9e0]
    // 0x97a58c: r0 = _WeakReference()
    //     0x97a58c: bl              #0x657400  ; Allocate_WeakReferenceStub -> _WeakReference<X0> (size=-0x8)
    // 0x97a590: mov             x1, x0
    // 0x97a594: ldur            x0, [fp, #-8]
    // 0x97a598: stur            x1, [fp, #-0x10]
    // 0x97a59c: StoreField: r1->field_7 = r0
    //     0x97a59c: stur            w0, [x1, #7]
    // 0x97a5a0: r1 = 1
    //     0x97a5a0: movz            x1, #0x1
    // 0x97a5a4: r0 = AllocateContext()
    //     0x97a5a4: bl              #0xec126c  ; AllocateContextStub
    // 0x97a5a8: mov             x3, x0
    // 0x97a5ac: ldur            x0, [fp, #-0x10]
    // 0x97a5b0: stur            x3, [fp, #-0x18]
    // 0x97a5b4: StoreField: r3->field_f = r0
    //     0x97a5b4: stur            w0, [x3, #0xf]
    // 0x97a5b8: mov             x2, x3
    // 0x97a5bc: r1 = Function '<anonymous closure>':.
    //     0x97a5bc: add             x1, PP, #0x49, lsl #12  ; [pp+0x499e8] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x97a5c0: ldr             x1, [x1, #0x9e8]
    // 0x97a5c4: r0 = AllocateClosure()
    //     0x97a5c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a5c8: ldur            x2, [fp, #-0x18]
    // 0x97a5cc: r1 = Function '<anonymous closure>':.
    //     0x97a5cc: add             x1, PP, #0x49, lsl #12  ; [pp+0x499f0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x97a5d0: ldr             x1, [x1, #0x9f0]
    // 0x97a5d4: stur            x0, [fp, #-0x10]
    // 0x97a5d8: r0 = AllocateClosure()
    //     0x97a5d8: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a5dc: ldur            x2, [fp, #-0x18]
    // 0x97a5e0: r1 = Function '<anonymous closure>':.
    //     0x97a5e0: add             x1, PP, #0x49, lsl #12  ; [pp+0x499f8] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x97a5e4: ldr             x1, [x1, #0x9f8]
    // 0x97a5e8: stur            x0, [fp, #-0x20]
    // 0x97a5ec: r0 = AllocateClosure()
    //     0x97a5ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a5f0: ldur            x2, [fp, #-0x18]
    // 0x97a5f4: r1 = Function '<anonymous closure>':.
    //     0x97a5f4: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a00] AnonymousClosure: (0x97b240), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97a5f8: ldr             x1, [x1, #0xa00]
    // 0x97a5fc: stur            x0, [fp, #-0x28]
    // 0x97a600: r0 = AllocateClosure()
    //     0x97a600: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a604: ldur            x2, [fp, #-0x18]
    // 0x97a608: r1 = Function '<anonymous closure>':.
    //     0x97a608: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a08] AnonymousClosure: (0x97b240), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97a60c: ldr             x1, [x1, #0xa08]
    // 0x97a610: stur            x0, [fp, #-0x30]
    // 0x97a614: r0 = AllocateClosure()
    //     0x97a614: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a618: ldur            x2, [fp, #-0x18]
    // 0x97a61c: r1 = Function '<anonymous closure>':.
    //     0x97a61c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a10] AnonymousClosure: (0x97af60), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97a620: ldr             x1, [x1, #0xa10]
    // 0x97a624: stur            x0, [fp, #-0x38]
    // 0x97a628: r0 = AllocateClosure()
    //     0x97a628: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a62c: ldur            x2, [fp, #-0x18]
    // 0x97a630: r1 = Function '<anonymous closure>':.
    //     0x97a630: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a18] AnonymousClosure: (0x97ad04), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97a634: ldr             x1, [x1, #0xa18]
    // 0x97a638: stur            x0, [fp, #-0x40]
    // 0x97a63c: r0 = AllocateClosure()
    //     0x97a63c: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a640: ldur            x2, [fp, #-0x18]
    // 0x97a644: r1 = Function '<anonymous closure>':.
    //     0x97a644: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a20] AnonymousClosure: (0x97aca4), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97a648: ldr             x1, [x1, #0xa20]
    // 0x97a64c: stur            x0, [fp, #-0x48]
    // 0x97a650: r0 = AllocateClosure()
    //     0x97a650: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a654: ldur            x2, [fp, #-0x18]
    // 0x97a658: r1 = Function '<anonymous closure>':.
    //     0x97a658: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a28] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x97a65c: ldr             x1, [x1, #0xa28]
    // 0x97a660: stur            x0, [fp, #-0x50]
    // 0x97a664: r0 = AllocateClosure()
    //     0x97a664: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a668: ldur            x2, [fp, #-0x18]
    // 0x97a66c: r1 = Function '<anonymous closure>':.
    //     0x97a66c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a30] AnonymousClosure: (0x97a9c4), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97a670: ldr             x1, [x1, #0xa30]
    // 0x97a674: stur            x0, [fp, #-0x58]
    // 0x97a678: r0 = AllocateClosure()
    //     0x97a678: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a67c: stur            x0, [fp, #-0x60]
    // 0x97a680: r0 = WebViewClient()
    //     0x97a680: bl              #0x92a14c  ; AllocateWebViewClientStub -> WebViewClient (size=0x3c)
    // 0x97a684: stur            x0, [fp, #-0x68]
    // 0x97a688: stp             NULL, NULL, [SP, #0x50]
    // 0x97a68c: ldur            x16, [fp, #-0x20]
    // 0x97a690: ldur            lr, [fp, #-0x10]
    // 0x97a694: stp             lr, x16, [SP, #0x40]
    // 0x97a698: ldur            x16, [fp, #-0x28]
    // 0x97a69c: ldur            lr, [fp, #-0x30]
    // 0x97a6a0: stp             lr, x16, [SP, #0x30]
    // 0x97a6a4: ldur            x16, [fp, #-0x38]
    // 0x97a6a8: ldur            lr, [fp, #-0x40]
    // 0x97a6ac: stp             lr, x16, [SP, #0x20]
    // 0x97a6b0: ldur            x16, [fp, #-0x48]
    // 0x97a6b4: ldur            lr, [fp, #-0x50]
    // 0x97a6b8: stp             lr, x16, [SP, #0x10]
    // 0x97a6bc: ldur            x16, [fp, #-0x58]
    // 0x97a6c0: ldur            lr, [fp, #-0x60]
    // 0x97a6c4: stp             lr, x16, [SP]
    // 0x97a6c8: mov             x1, x0
    // 0x97a6cc: r4 = const [0, 0xd, 0xc, 0x1, doUpdateVisitedHistory, 0xb, onPageFinished, 0x4, onPageStarted, 0x3, onReceivedError, 0x8, onReceivedHttpAuthRequest, 0xc, onReceivedHttpError, 0x5, onReceivedRequestError, 0x6, onReceivedRequestErrorCompat, 0x7, pigeon_binaryMessenger, 0x1, pigeon_instanceManager, 0x2, requestLoading, 0x9, urlLoading, 0xa, null]
    //     0x97a6cc: add             x4, PP, #0x49, lsl #12  ; [pp+0x49910] List(29) [0, 0xd, 0xc, 0x1, "doUpdateVisitedHistory", 0xb, "onPageFinished", 0x4, "onPageStarted", 0x3, "onReceivedError", 0x8, "onReceivedHttpAuthRequest", 0xc, "onReceivedHttpError", 0x5, "onReceivedRequestError", 0x6, "onReceivedRequestErrorCompat", 0x7, "pigeon_binaryMessenger", 0x1, "pigeon_instanceManager", 0x2, "requestLoading", 0x9, "urlLoading", 0xa, Null]
    //     0x97a6d0: ldr             x4, [x4, #0x910]
    // 0x97a6d4: r0 = WebViewClient()
    //     0x97a6d4: bl              #0x977f98  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebViewClient::WebViewClient
    // 0x97a6d8: ldur            x0, [fp, #-8]
    // 0x97a6dc: LoadField: r1 = r0->field_b
    //     0x97a6dc: ldur            w1, [x0, #0xb]
    // 0x97a6e0: DecompressPointer r1
    //     0x97a6e0: add             x1, x1, HEAP, lsl #32
    // 0x97a6e4: r16 = Sentinel
    //     0x97a6e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97a6e8: cmp             w1, w16
    // 0x97a6ec: b.ne            #0x97a6f8
    // 0x97a6f0: mov             x3, x0
    // 0x97a6f4: b               #0x97a70c
    // 0x97a6f8: r16 = "_webViewClient@470193571"
    //     0x97a6f8: add             x16, PP, #0x49, lsl #12  ; [pp+0x49a38] "_webViewClient@470193571"
    //     0x97a6fc: ldr             x16, [x16, #0xa38]
    // 0x97a700: str             x16, [SP]
    // 0x97a704: r0 = _throwFieldAlreadyInitialized()
    //     0x97a704: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x97a708: ldur            x3, [fp, #-8]
    // 0x97a70c: ldur            x0, [fp, #-0x68]
    // 0x97a710: StoreField: r3->field_b = r0
    //     0x97a710: stur            w0, [x3, #0xb]
    //     0x97a714: ldurb           w16, [x3, #-1]
    //     0x97a718: ldurb           w17, [x0, #-1]
    //     0x97a71c: and             x16, x17, x16, lsr #2
    //     0x97a720: tst             x16, HEAP, lsr #32
    //     0x97a724: b.eq            #0x97a72c
    //     0x97a728: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x97a72c: ldur            x2, [fp, #-0x18]
    // 0x97a730: r1 = Function '<anonymous closure>':.
    //     0x97a730: add             x1, PP, #0x49, lsl #12  ; [pp+0x49a40] AnonymousClosure: (0x97a800), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97a734: ldr             x1, [x1, #0xa40]
    // 0x97a738: r0 = AllocateClosure()
    //     0x97a738: bl              #0xec1630  ; AllocateClosureStub
    // 0x97a73c: stur            x0, [fp, #-0x10]
    // 0x97a740: r0 = DownloadListener()
    //     0x97a740: bl              #0x92a1b8  ; AllocateDownloadListenerStub -> DownloadListener (size=0x18)
    // 0x97a744: stur            x0, [fp, #-0x18]
    // 0x97a748: stp             NULL, NULL, [SP, #8]
    // 0x97a74c: ldur            x16, [fp, #-0x10]
    // 0x97a750: str             x16, [SP]
    // 0x97a754: mov             x1, x0
    // 0x97a758: r4 = const [0, 0x4, 0x3, 0x1, onDownloadStart, 0x3, pigeon_binaryMessenger, 0x1, pigeon_instanceManager, 0x2, null]
    //     0x97a758: add             x4, PP, #0x49, lsl #12  ; [pp+0x498c0] List(11) [0, 0x4, 0x3, 0x1, "onDownloadStart", 0x3, "pigeon_binaryMessenger", 0x1, "pigeon_instanceManager", 0x2, Null]
    //     0x97a75c: ldr             x4, [x4, #0x8c0]
    // 0x97a760: r0 = DownloadListener()
    //     0x97a760: bl              #0x9775ec  ; [package:webview_flutter_android/src/android_webkit.g.dart] DownloadListener::DownloadListener
    // 0x97a764: ldur            x0, [fp, #-8]
    // 0x97a768: LoadField: r1 = r0->field_f
    //     0x97a768: ldur            w1, [x0, #0xf]
    // 0x97a76c: DecompressPointer r1
    //     0x97a76c: add             x1, x1, HEAP, lsl #32
    // 0x97a770: r16 = Sentinel
    //     0x97a770: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97a774: cmp             w1, w16
    // 0x97a778: b.ne            #0x97a784
    // 0x97a77c: mov             x1, x0
    // 0x97a780: b               #0x97a798
    // 0x97a784: r16 = "_downloadListener@470193571"
    //     0x97a784: add             x16, PP, #0x49, lsl #12  ; [pp+0x49a48] "_downloadListener@470193571"
    //     0x97a788: ldr             x16, [x16, #0xa48]
    // 0x97a78c: str             x16, [SP]
    // 0x97a790: r0 = _throwFieldAlreadyInitialized()
    //     0x97a790: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x97a794: ldur            x1, [fp, #-8]
    // 0x97a798: ldur            x0, [fp, #-0x18]
    // 0x97a79c: StoreField: r1->field_f = r0
    //     0x97a79c: stur            w0, [x1, #0xf]
    //     0x97a7a0: ldurb           w16, [x1, #-1]
    //     0x97a7a4: ldurb           w17, [x0, #-1]
    //     0x97a7a8: and             x16, x17, x16, lsr #2
    //     0x97a7ac: tst             x16, HEAP, lsr #32
    //     0x97a7b0: b.eq            #0x97a7b8
    //     0x97a7b4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97a7b8: r0 = Null
    //     0x97a7b8: mov             x0, NULL
    // 0x97a7bc: LeaveFrame
    //     0x97a7bc: mov             SP, fp
    //     0x97a7c0: ldp             fp, lr, [SP], #0x10
    // 0x97a7c4: ret
    //     0x97a7c4: ret             
    // 0x97a7c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a7c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a7cc: b               #0x97a504
  }
  [closure] void <anonymous closure>(dynamic, DownloadListener, String, String, String, String, int) {
    // ** addr: 0x97a800, size: 0x70
    // 0x97a800: EnterFrame
    //     0x97a800: stp             fp, lr, [SP, #-0x10]!
    //     0x97a804: mov             fp, SP
    // 0x97a808: ldr             x0, [fp, #0x40]
    // 0x97a80c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97a80c: ldur            w1, [x0, #0x17]
    // 0x97a810: DecompressPointer r1
    //     0x97a810: add             x1, x1, HEAP, lsl #32
    // 0x97a814: CheckStackOverflow
    //     0x97a814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a818: cmp             SP, x16
    //     0x97a81c: b.ls            #0x97a868
    // 0x97a820: LoadField: r0 = r1->field_f
    //     0x97a820: ldur            w0, [x1, #0xf]
    // 0x97a824: DecompressPointer r0
    //     0x97a824: add             x0, x0, HEAP, lsl #32
    // 0x97a828: LoadField: r1 = r0->field_7
    //     0x97a828: ldur            w1, [x0, #7]
    // 0x97a82c: DecompressPointer r1
    //     0x97a82c: add             x1, x1, HEAP, lsl #32
    // 0x97a830: cmp             w1, NULL
    // 0x97a834: b.eq            #0x97a858
    // 0x97a838: LoadField: r1 = r0->field_7
    //     0x97a838: ldur            w1, [x0, #7]
    // 0x97a83c: DecompressPointer r1
    //     0x97a83c: add             x1, x1, HEAP, lsl #32
    // 0x97a840: cmp             w1, NULL
    // 0x97a844: b.eq            #0x97a858
    // 0x97a848: ldr             x2, [fp, #0x30]
    // 0x97a84c: r3 = true
    //     0x97a84c: add             x3, NULL, #0x20  ; true
    // 0x97a850: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x97a850: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x97a854: r0 = _handleNavigation()
    //     0x97a854: bl              #0x97a870  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::_handleNavigation
    // 0x97a858: r0 = Null
    //     0x97a858: mov             x0, NULL
    // 0x97a85c: LeaveFrame
    //     0x97a85c: mov             SP, fp
    //     0x97a860: ldp             fp, lr, [SP], #0x10
    // 0x97a864: ret
    //     0x97a864: ret             
    // 0x97a868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a868: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a86c: b               #0x97a820
  }
  _ _handleNavigation(/* No info */) {
    // ** addr: 0x97a870, size: 0x13c
    // 0x97a870: EnterFrame
    //     0x97a870: stp             fp, lr, [SP, #-0x10]!
    //     0x97a874: mov             fp, SP
    // 0x97a878: AllocStack(0x30)
    //     0x97a878: sub             SP, SP, #0x30
    // 0x97a87c: SetupParameters(AndroidNavigationDelegate this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x20 */, {dynamic headers = _ConstMap len:0 /* r2, fp-0x18 */})
    //     0x97a87c: mov             x0, x1
    //     0x97a880: mov             x1, x2
    //     0x97a884: stur            x2, [fp, #-0x20]
    //     0x97a888: ldur            w2, [x4, #0x13]
    //     0x97a88c: ldur            w5, [x4, #0x1f]
    //     0x97a890: add             x5, x5, HEAP, lsl #32
    //     0x97a894: add             x16, PP, #0xe, lsl #12  ; [pp+0xe8a8] "headers"
    //     0x97a898: ldr             x16, [x16, #0x8a8]
    //     0x97a89c: cmp             w5, w16
    //     0x97a8a0: b.ne            #0x97a8bc
    //     0x97a8a4: ldur            w5, [x4, #0x23]
    //     0x97a8a8: add             x5, x5, HEAP, lsl #32
    //     0x97a8ac: sub             w4, w2, w5
    //     0x97a8b0: add             x2, fp, w4, sxtw #2
    //     0x97a8b4: ldr             x2, [x2, #8]
    //     0x97a8b8: b               #0x97a8c4
    //     0x97a8bc: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1ac48] Map<String, String>(0)
    //     0x97a8c0: ldr             x2, [x2, #0xc48]
    //     0x97a8c4: stur            x2, [fp, #-0x18]
    // 0x97a8c8: CheckStackOverflow
    //     0x97a8c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a8cc: cmp             SP, x16
    //     0x97a8d0: b.ls            #0x97a9a4
    // 0x97a8d4: LoadField: r4 = r0->field_2b
    //     0x97a8d4: ldur            w4, [x0, #0x2b]
    // 0x97a8d8: DecompressPointer r4
    //     0x97a8d8: add             x4, x4, HEAP, lsl #32
    // 0x97a8dc: stur            x4, [fp, #-0x10]
    // 0x97a8e0: LoadField: r5 = r0->field_27
    //     0x97a8e0: ldur            w5, [x0, #0x27]
    // 0x97a8e4: DecompressPointer r5
    //     0x97a8e4: add             x5, x5, HEAP, lsl #32
    // 0x97a8e8: stur            x5, [fp, #-8]
    // 0x97a8ec: tbnz            w3, #4, #0x97a900
    // 0x97a8f0: cmp             w5, NULL
    // 0x97a8f4: b.eq            #0x97a900
    // 0x97a8f8: cmp             w4, NULL
    // 0x97a8fc: b.ne            #0x97a910
    // 0x97a900: r0 = Null
    //     0x97a900: mov             x0, NULL
    // 0x97a904: LeaveFrame
    //     0x97a904: mov             SP, fp
    //     0x97a908: ldp             fp, lr, [SP], #0x10
    // 0x97a90c: ret
    //     0x97a90c: ret             
    // 0x97a910: r0 = NavigationRequest()
    //     0x97a910: bl              #0x97a9b8  ; AllocateNavigationRequestStub -> NavigationRequest (size=0xc)
    // 0x97a914: ldur            x1, [fp, #-0x20]
    // 0x97a918: StoreField: r0->field_7 = r1
    //     0x97a918: stur            w1, [x0, #7]
    // 0x97a91c: ldur            x16, [fp, #-8]
    // 0x97a920: stp             x0, x16, [SP]
    // 0x97a924: ldur            x0, [fp, #-8]
    // 0x97a928: ClosureCall
    //     0x97a928: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97a92c: ldur            x2, [x0, #0x1f]
    //     0x97a930: blr             x2
    // 0x97a934: r16 = Instance_NavigationDecision
    //     0x97a934: add             x16, PP, #0x49, lsl #12  ; [pp+0x49a50] Obj!NavigationDecision@e2d711
    //     0x97a938: ldr             x16, [x16, #0xa50]
    // 0x97a93c: cmp             w0, w16
    // 0x97a940: b.ne            #0x97a994
    // 0x97a944: ldur            x0, [fp, #-0x18]
    // 0x97a948: ldur            x1, [fp, #-0x20]
    // 0x97a94c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97a94c: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97a950: r0 = parse()
    //     0x97a950: bl              #0x60d170  ; [dart:core] Uri::parse
    // 0x97a954: stur            x0, [fp, #-8]
    // 0x97a958: r0 = LoadRequestParams()
    //     0x97a958: bl              #0x97a9ac  ; AllocateLoadRequestParamsStub -> LoadRequestParams (size=0x18)
    // 0x97a95c: mov             x1, x0
    // 0x97a960: ldur            x0, [fp, #-8]
    // 0x97a964: StoreField: r1->field_7 = r0
    //     0x97a964: stur            w0, [x1, #7]
    // 0x97a968: r0 = Instance_LoadRequestMethod
    //     0x97a968: add             x0, PP, #0x49, lsl #12  ; [pp+0x49a58] Obj!LoadRequestMethod@e2d751
    //     0x97a96c: ldr             x0, [x0, #0xa58]
    // 0x97a970: StoreField: r1->field_b = r0
    //     0x97a970: stur            w0, [x1, #0xb]
    // 0x97a974: ldur            x0, [fp, #-0x18]
    // 0x97a978: StoreField: r1->field_f = r0
    //     0x97a978: stur            w0, [x1, #0xf]
    // 0x97a97c: ldur            x16, [fp, #-0x10]
    // 0x97a980: stp             x1, x16, [SP]
    // 0x97a984: ldur            x0, [fp, #-0x10]
    // 0x97a988: ClosureCall
    //     0x97a988: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97a98c: ldur            x2, [x0, #0x1f]
    //     0x97a990: blr             x2
    // 0x97a994: r0 = Null
    //     0x97a994: mov             x0, NULL
    // 0x97a998: LeaveFrame
    //     0x97a998: mov             SP, fp
    //     0x97a99c: ldp             fp, lr, [SP], #0x10
    // 0x97a9a0: ret
    //     0x97a9a0: ret             
    // 0x97a9a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a9a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a9a8: b               #0x97a8d4
  }
  [closure] void <anonymous closure>(dynamic, WebViewClient, WebView, HttpAuthHandler, String, String) {
    // ** addr: 0x97a9c4, size: 0x34
    // 0x97a9c4: EnterFrame
    //     0x97a9c4: stp             fp, lr, [SP, #-0x10]!
    //     0x97a9c8: mov             fp, SP
    // 0x97a9cc: CheckStackOverflow
    //     0x97a9cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a9d0: cmp             SP, x16
    //     0x97a9d4: b.ls            #0x97a9f0
    // 0x97a9d8: ldr             x1, [fp, #0x20]
    // 0x97a9dc: r0 = cancel()
    //     0x97a9dc: bl              #0x97a9f8  ; [package:webview_flutter_android/src/android_webkit.g.dart] HttpAuthHandler::cancel
    // 0x97a9e0: r0 = Null
    //     0x97a9e0: mov             x0, NULL
    // 0x97a9e4: LeaveFrame
    //     0x97a9e4: mov             SP, fp
    //     0x97a9e8: ldp             fp, lr, [SP], #0x10
    // 0x97a9ec: ret
    //     0x97a9ec: ret             
    // 0x97a9f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a9f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a9f4: b               #0x97a9d8
  }
  [closure] void <anonymous closure>(dynamic, WebViewClient, WebView, String) {
    // ** addr: 0x97aca4, size: 0x60
    // 0x97aca4: EnterFrame
    //     0x97aca4: stp             fp, lr, [SP, #-0x10]!
    //     0x97aca8: mov             fp, SP
    // 0x97acac: ldr             x0, [fp, #0x28]
    // 0x97acb0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97acb0: ldur            w1, [x0, #0x17]
    // 0x97acb4: DecompressPointer r1
    //     0x97acb4: add             x1, x1, HEAP, lsl #32
    // 0x97acb8: CheckStackOverflow
    //     0x97acb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97acbc: cmp             SP, x16
    //     0x97acc0: b.ls            #0x97acfc
    // 0x97acc4: LoadField: r0 = r1->field_f
    //     0x97acc4: ldur            w0, [x1, #0xf]
    // 0x97acc8: DecompressPointer r0
    //     0x97acc8: add             x0, x0, HEAP, lsl #32
    // 0x97accc: LoadField: r1 = r0->field_7
    //     0x97accc: ldur            w1, [x0, #7]
    // 0x97acd0: DecompressPointer r1
    //     0x97acd0: add             x1, x1, HEAP, lsl #32
    // 0x97acd4: cmp             w1, NULL
    // 0x97acd8: b.eq            #0x97acec
    // 0x97acdc: ldr             x2, [fp, #0x10]
    // 0x97ace0: r3 = true
    //     0x97ace0: add             x3, NULL, #0x20  ; true
    // 0x97ace4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x97ace4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x97ace8: r0 = _handleNavigation()
    //     0x97ace8: bl              #0x97a870  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::_handleNavigation
    // 0x97acec: r0 = Null
    //     0x97acec: mov             x0, NULL
    // 0x97acf0: LeaveFrame
    //     0x97acf0: mov             SP, fp
    //     0x97acf4: ldp             fp, lr, [SP], #0x10
    // 0x97acf8: ret
    //     0x97acf8: ret             
    // 0x97acfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97acfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ad00: b               #0x97acc4
  }
  [closure] void <anonymous closure>(dynamic, WebViewClient, WebView, WebResourceRequest) {
    // ** addr: 0x97ad04, size: 0x11c
    // 0x97ad04: EnterFrame
    //     0x97ad04: stp             fp, lr, [SP, #-0x10]!
    //     0x97ad08: mov             fp, SP
    // 0x97ad0c: AllocStack(0x30)
    //     0x97ad0c: sub             SP, SP, #0x30
    // 0x97ad10: SetupParameters()
    //     0x97ad10: ldr             x0, [fp, #0x28]
    //     0x97ad14: ldur            w1, [x0, #0x17]
    //     0x97ad18: add             x1, x1, HEAP, lsl #32
    // 0x97ad1c: CheckStackOverflow
    //     0x97ad1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ad20: cmp             SP, x16
    //     0x97ad24: b.ls            #0x97ae18
    // 0x97ad28: LoadField: r0 = r1->field_f
    //     0x97ad28: ldur            w0, [x1, #0xf]
    // 0x97ad2c: DecompressPointer r0
    //     0x97ad2c: add             x0, x0, HEAP, lsl #32
    // 0x97ad30: LoadField: r3 = r0->field_7
    //     0x97ad30: ldur            w3, [x0, #7]
    // 0x97ad34: DecompressPointer r3
    //     0x97ad34: add             x3, x3, HEAP, lsl #32
    // 0x97ad38: stur            x3, [fp, #-0x18]
    // 0x97ad3c: cmp             w3, NULL
    // 0x97ad40: b.eq            #0x97ae08
    // 0x97ad44: ldr             x0, [fp, #0x10]
    // 0x97ad48: LoadField: r4 = r0->field_f
    //     0x97ad48: ldur            w4, [x0, #0xf]
    // 0x97ad4c: DecompressPointer r4
    //     0x97ad4c: add             x4, x4, HEAP, lsl #32
    // 0x97ad50: stur            x4, [fp, #-0x10]
    // 0x97ad54: LoadField: r5 = r0->field_23
    //     0x97ad54: ldur            w5, [x0, #0x23]
    // 0x97ad58: DecompressPointer r5
    //     0x97ad58: add             x5, x5, HEAP, lsl #32
    // 0x97ad5c: stur            x5, [fp, #-8]
    // 0x97ad60: cmp             w5, NULL
    // 0x97ad64: b.ne            #0x97ad70
    // 0x97ad68: r0 = Null
    //     0x97ad68: mov             x0, NULL
    // 0x97ad6c: b               #0x97adbc
    // 0x97ad70: r1 = Function '<anonymous closure>':.
    //     0x97ad70: add             x1, PP, #0x49, lsl #12  ; [pp+0x49aa0] AnonymousClosure: (0x97ae20), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate (0x97a4e0)
    //     0x97ad74: ldr             x1, [x1, #0xaa0]
    // 0x97ad78: r2 = Null
    //     0x97ad78: mov             x2, NULL
    // 0x97ad7c: r0 = AllocateClosure()
    //     0x97ad7c: bl              #0xec1630  ; AllocateClosureStub
    // 0x97ad80: mov             x1, x0
    // 0x97ad84: ldur            x0, [fp, #-8]
    // 0x97ad88: r2 = LoadClassIdInstr(r0)
    //     0x97ad88: ldur            x2, [x0, #-1]
    //     0x97ad8c: ubfx            x2, x2, #0xc, #0x14
    // 0x97ad90: r16 = <String, String>
    //     0x97ad90: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x97ad94: ldr             x16, [x16, #0x668]
    // 0x97ad98: stp             x0, x16, [SP, #8]
    // 0x97ad9c: str             x1, [SP]
    // 0x97ada0: mov             x0, x2
    // 0x97ada4: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x97ada4: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x97ada8: r0 = GDT[cid_x0 + 0x1200e]()
    //     0x97ada8: movz            x17, #0x200e
    //     0x97adac: movk            x17, #0x1, lsl #16
    //     0x97adb0: add             lr, x0, x17
    //     0x97adb4: ldr             lr, [x21, lr, lsl #3]
    //     0x97adb8: blr             lr
    // 0x97adbc: cmp             w0, NULL
    // 0x97adc0: b.ne            #0x97ade0
    // 0x97adc4: r16 = <String, String>
    //     0x97adc4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x97adc8: ldr             x16, [x16, #0x668]
    // 0x97adcc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x97add0: stp             lr, x16, [SP]
    // 0x97add4: r0 = Map._fromLiteral()
    //     0x97add4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x97add8: mov             x1, x0
    // 0x97addc: b               #0x97ade4
    // 0x97ade0: mov             x1, x0
    // 0x97ade4: ldr             x0, [fp, #0x10]
    // 0x97ade8: LoadField: r3 = r0->field_13
    //     0x97ade8: ldur            w3, [x0, #0x13]
    // 0x97adec: DecompressPointer r3
    //     0x97adec: add             x3, x3, HEAP, lsl #32
    // 0x97adf0: str             x1, [SP]
    // 0x97adf4: ldur            x1, [fp, #-0x18]
    // 0x97adf8: ldur            x2, [fp, #-0x10]
    // 0x97adfc: r4 = const [0, 0x4, 0x1, 0x3, headers, 0x3, null]
    //     0x97adfc: add             x4, PP, #0x49, lsl #12  ; [pp+0x49aa8] List(7) [0, 0x4, 0x1, 0x3, "headers", 0x3, Null]
    //     0x97ae00: ldr             x4, [x4, #0xaa8]
    // 0x97ae04: r0 = _handleNavigation()
    //     0x97ae04: bl              #0x97a870  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::_handleNavigation
    // 0x97ae08: r0 = Null
    //     0x97ae08: mov             x0, NULL
    // 0x97ae0c: LeaveFrame
    //     0x97ae0c: mov             SP, fp
    //     0x97ae10: ldp             fp, lr, [SP], #0x10
    // 0x97ae14: ret
    //     0x97ae14: ret             
    // 0x97ae18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ae18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ae1c: b               #0x97ad28
  }
  [closure] MapEntry<String, String> <anonymous closure>(dynamic, String?, String?) {
    // ** addr: 0x97ae20, size: 0x50
    // 0x97ae20: EnterFrame
    //     0x97ae20: stp             fp, lr, [SP, #-0x10]!
    //     0x97ae24: mov             fp, SP
    // 0x97ae28: ldr             x0, [fp, #0x18]
    // 0x97ae2c: cmp             w0, NULL
    // 0x97ae30: b.eq            #0x97ae68
    // 0x97ae34: ldr             x2, [fp, #0x10]
    // 0x97ae38: cmp             w2, NULL
    // 0x97ae3c: b.eq            #0x97ae6c
    // 0x97ae40: r1 = <String, String>
    //     0x97ae40: add             x1, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x97ae44: ldr             x1, [x1, #0x668]
    // 0x97ae48: r0 = MapEntry()
    //     0x97ae48: bl              #0x65ce18  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x97ae4c: ldr             x1, [fp, #0x18]
    // 0x97ae50: StoreField: r0->field_b = r1
    //     0x97ae50: stur            w1, [x0, #0xb]
    // 0x97ae54: ldr             x1, [fp, #0x10]
    // 0x97ae58: StoreField: r0->field_f = r1
    //     0x97ae58: stur            w1, [x0, #0xf]
    // 0x97ae5c: LeaveFrame
    //     0x97ae5c: mov             SP, fp
    //     0x97ae60: ldp             fp, lr, [SP], #0x10
    // 0x97ae64: ret
    //     0x97ae64: ret             
    // 0x97ae68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97ae68: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x97ae6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97ae6c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, WebViewClient, WebView, int, String, String) {
    // ** addr: 0x97af60, size: 0xc0
    // 0x97af60: EnterFrame
    //     0x97af60: stp             fp, lr, [SP, #-0x10]!
    //     0x97af64: mov             fp, SP
    // 0x97af68: AllocStack(0x20)
    //     0x97af68: sub             SP, SP, #0x20
    // 0x97af6c: SetupParameters()
    //     0x97af6c: ldr             x0, [fp, #0x38]
    //     0x97af70: ldur            w1, [x0, #0x17]
    //     0x97af74: add             x1, x1, HEAP, lsl #32
    // 0x97af78: CheckStackOverflow
    //     0x97af78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97af7c: cmp             SP, x16
    //     0x97af80: b.ls            #0x97b018
    // 0x97af84: LoadField: r0 = r1->field_f
    //     0x97af84: ldur            w0, [x1, #0xf]
    // 0x97af88: DecompressPointer r0
    //     0x97af88: add             x0, x0, HEAP, lsl #32
    // 0x97af8c: LoadField: r1 = r0->field_7
    //     0x97af8c: ldur            w1, [x0, #7]
    // 0x97af90: DecompressPointer r1
    //     0x97af90: add             x1, x1, HEAP, lsl #32
    // 0x97af94: cmp             w1, NULL
    // 0x97af98: b.ne            #0x97afa4
    // 0x97af9c: r0 = Null
    //     0x97af9c: mov             x0, NULL
    // 0x97afa0: b               #0x97afac
    // 0x97afa4: LoadField: r0 = r1->field_23
    //     0x97afa4: ldur            w0, [x1, #0x23]
    // 0x97afa8: DecompressPointer r0
    //     0x97afa8: add             x0, x0, HEAP, lsl #32
    // 0x97afac: stur            x0, [fp, #-8]
    // 0x97afb0: cmp             w0, NULL
    // 0x97afb4: b.eq            #0x97b008
    // 0x97afb8: ldr             x1, [fp, #0x20]
    // 0x97afbc: ldr             x2, [fp, #0x18]
    // 0x97afc0: r3 = LoadInt32Instr(r1)
    //     0x97afc0: sbfx            x3, x1, #1, #0x1f
    //     0x97afc4: tbz             w1, #0, #0x97afcc
    //     0x97afc8: ldur            x3, [x1, #7]
    // 0x97afcc: mov             x1, x3
    // 0x97afd0: r0 = _errorCodeToErrorType()
    //     0x97afd0: bl              #0x97b02c  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebResourceError::_errorCodeToErrorType
    // 0x97afd4: stur            x0, [fp, #-0x10]
    // 0x97afd8: r0 = AndroidWebResourceError()
    //     0x97afd8: bl              #0x97b020  ; AllocateAndroidWebResourceErrorStub -> AndroidWebResourceError (size=0x10)
    // 0x97afdc: mov             x1, x0
    // 0x97afe0: ldr             x0, [fp, #0x18]
    // 0x97afe4: StoreField: r1->field_7 = r0
    //     0x97afe4: stur            w0, [x1, #7]
    // 0x97afe8: ldur            x0, [fp, #-0x10]
    // 0x97afec: StoreField: r1->field_b = r0
    //     0x97afec: stur            w0, [x1, #0xb]
    // 0x97aff0: ldur            x16, [fp, #-8]
    // 0x97aff4: stp             x1, x16, [SP]
    // 0x97aff8: ldur            x0, [fp, #-8]
    // 0x97affc: ClosureCall
    //     0x97affc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97b000: ldur            x2, [x0, #0x1f]
    //     0x97b004: blr             x2
    // 0x97b008: r0 = Null
    //     0x97b008: mov             x0, NULL
    // 0x97b00c: LeaveFrame
    //     0x97b00c: mov             SP, fp
    //     0x97b010: ldp             fp, lr, [SP], #0x10
    // 0x97b014: ret
    //     0x97b014: ret             
    // 0x97b018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b018: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b01c: b               #0x97af84
  }
  [closure] void <anonymous closure>(dynamic, WebViewClient, WebView, WebResourceRequest, WebResourceError) {
    // ** addr: 0x97b240, size: 0xc0
    // 0x97b240: EnterFrame
    //     0x97b240: stp             fp, lr, [SP, #-0x10]!
    //     0x97b244: mov             fp, SP
    // 0x97b248: AllocStack(0x28)
    //     0x97b248: sub             SP, SP, #0x28
    // 0x97b24c: SetupParameters()
    //     0x97b24c: ldr             x0, [fp, #0x30]
    //     0x97b250: ldur            w1, [x0, #0x17]
    //     0x97b254: add             x1, x1, HEAP, lsl #32
    // 0x97b258: CheckStackOverflow
    //     0x97b258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b25c: cmp             SP, x16
    //     0x97b260: b.ls            #0x97b2f8
    // 0x97b264: LoadField: r0 = r1->field_f
    //     0x97b264: ldur            w0, [x1, #0xf]
    // 0x97b268: DecompressPointer r0
    //     0x97b268: add             x0, x0, HEAP, lsl #32
    // 0x97b26c: LoadField: r1 = r0->field_7
    //     0x97b26c: ldur            w1, [x0, #7]
    // 0x97b270: DecompressPointer r1
    //     0x97b270: add             x1, x1, HEAP, lsl #32
    // 0x97b274: cmp             w1, NULL
    // 0x97b278: b.ne            #0x97b284
    // 0x97b27c: r0 = Null
    //     0x97b27c: mov             x0, NULL
    // 0x97b280: b               #0x97b28c
    // 0x97b284: LoadField: r0 = r1->field_23
    //     0x97b284: ldur            w0, [x1, #0x23]
    // 0x97b288: DecompressPointer r0
    //     0x97b288: add             x0, x0, HEAP, lsl #32
    // 0x97b28c: stur            x0, [fp, #-0x10]
    // 0x97b290: cmp             w0, NULL
    // 0x97b294: b.eq            #0x97b2e8
    // 0x97b298: ldr             x1, [fp, #0x10]
    // 0x97b29c: LoadField: r2 = r1->field_f
    //     0x97b29c: ldur            x2, [x1, #0xf]
    // 0x97b2a0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x97b2a0: ldur            w3, [x1, #0x17]
    // 0x97b2a4: DecompressPointer r3
    //     0x97b2a4: add             x3, x3, HEAP, lsl #32
    // 0x97b2a8: mov             x1, x2
    // 0x97b2ac: stur            x3, [fp, #-8]
    // 0x97b2b0: r0 = _errorCodeToErrorType()
    //     0x97b2b0: bl              #0x97b02c  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebResourceError::_errorCodeToErrorType
    // 0x97b2b4: stur            x0, [fp, #-0x18]
    // 0x97b2b8: r0 = AndroidWebResourceError()
    //     0x97b2b8: bl              #0x97b020  ; AllocateAndroidWebResourceErrorStub -> AndroidWebResourceError (size=0x10)
    // 0x97b2bc: mov             x1, x0
    // 0x97b2c0: ldur            x0, [fp, #-8]
    // 0x97b2c4: StoreField: r1->field_7 = r0
    //     0x97b2c4: stur            w0, [x1, #7]
    // 0x97b2c8: ldur            x0, [fp, #-0x18]
    // 0x97b2cc: StoreField: r1->field_b = r0
    //     0x97b2cc: stur            w0, [x1, #0xb]
    // 0x97b2d0: ldur            x16, [fp, #-0x10]
    // 0x97b2d4: stp             x1, x16, [SP]
    // 0x97b2d8: ldur            x0, [fp, #-0x10]
    // 0x97b2dc: ClosureCall
    //     0x97b2dc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97b2e0: ldur            x2, [x0, #0x1f]
    //     0x97b2e4: blr             x2
    // 0x97b2e8: r0 = Null
    //     0x97b2e8: mov             x0, NULL
    // 0x97b2ec: LeaveFrame
    //     0x97b2ec: mov             SP, fp
    //     0x97b2f0: ldp             fp, lr, [SP], #0x10
    // 0x97b2f4: ret
    //     0x97b2f4: ret             
    // 0x97b2f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b2f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b2fc: b               #0x97b264
  }
}

// class id: 5854, size: 0xc, field offset: 0xc
class AndroidWebViewWidget extends PlatformWebViewWidget {

  _ build(/* No info */) {
    // ** addr: 0xbbcaec, size: 0xdc
    // 0xbbcaec: EnterFrame
    //     0xbbcaec: stp             fp, lr, [SP, #-0x10]!
    //     0xbbcaf0: mov             fp, SP
    // 0xbbcaf4: AllocStack(0x18)
    //     0xbbcaf4: sub             SP, SP, #0x18
    // 0xbbcaf8: SetupParameters(AndroidWebViewWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbbcaf8: stur            x1, [fp, #-8]
    //     0xbbcafc: stur            x2, [fp, #-0x10]
    // 0xbbcb00: CheckStackOverflow
    //     0xbbcb00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbcb04: cmp             SP, x16
    //     0xbbcb08: b.ls            #0xbbcbc0
    // 0xbbcb0c: r1 = 1
    //     0xbbcb0c: movz            x1, #0x1
    // 0xbbcb10: r0 = AllocateContext()
    //     0xbbcb10: bl              #0xec126c  ; AllocateContextStub
    // 0xbbcb14: mov             x3, x0
    // 0xbbcb18: ldur            x0, [fp, #-8]
    // 0xbbcb1c: stur            x3, [fp, #-0x18]
    // 0xbbcb20: StoreField: r3->field_f = r0
    //     0xbbcb20: stur            w0, [x3, #0xf]
    // 0xbbcb24: mov             x1, x0
    // 0xbbcb28: ldur            x2, [fp, #-0x10]
    // 0xbbcb2c: r0 = _trySetDefaultOnShowCustomWidgetCallbacks()
    //     0xbbcb2c: bl              #0xbbcbc8  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewWidget::_trySetDefaultOnShowCustomWidgetCallbacks
    // 0xbbcb30: ldur            x0, [fp, #-8]
    // 0xbbcb34: LoadField: r2 = r0->field_7
    //     0xbbcb34: ldur            w2, [x0, #7]
    // 0xbbcb38: DecompressPointer r2
    //     0xbbcb38: add             x2, x2, HEAP, lsl #32
    // 0xbbcb3c: stur            x2, [fp, #-0x10]
    // 0xbbcb40: r1 = <AndroidWebViewWidgetCreationParams>
    //     0xbbcb40: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ada8] TypeArguments: <AndroidWebViewWidgetCreationParams>
    //     0xbbcb44: ldr             x1, [x1, #0xda8]
    // 0xbbcb48: r0 = ValueKey()
    //     0xbbcb48: bl              #0x65c2bc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xbbcb4c: mov             x1, x0
    // 0xbbcb50: ldur            x0, [fp, #-0x10]
    // 0xbbcb54: stur            x1, [fp, #-8]
    // 0xbbcb58: StoreField: r1->field_b = r0
    //     0xbbcb58: stur            w0, [x1, #0xb]
    // 0xbbcb5c: r0 = PlatformViewLink()
    //     0xbbcb5c: bl              #0xa25090  ; AllocatePlatformViewLinkStub -> PlatformViewLink (size=0x18)
    // 0xbbcb60: mov             x3, x0
    // 0xbbcb64: r0 = "plugins.flutter.io/webview"
    //     0xbbcb64: add             x0, PP, #0x55, lsl #12  ; [pp+0x55240] "plugins.flutter.io/webview"
    //     0xbbcb68: ldr             x0, [x0, #0x240]
    // 0xbbcb6c: stur            x3, [fp, #-0x10]
    // 0xbbcb70: StoreField: r3->field_13 = r0
    //     0xbbcb70: stur            w0, [x3, #0x13]
    // 0xbbcb74: ldur            x2, [fp, #-0x18]
    // 0xbbcb78: r1 = Function '<anonymous closure>':.
    //     0xbbcb78: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5adb0] AnonymousClosure: (0xbbd0b4), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewWidget::build (0xbbcaec)
    //     0xbbcb7c: ldr             x1, [x1, #0xdb0]
    // 0xbbcb80: r0 = AllocateClosure()
    //     0xbbcb80: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbcb84: mov             x1, x0
    // 0xbbcb88: ldur            x0, [fp, #-0x10]
    // 0xbbcb8c: StoreField: r0->field_b = r1
    //     0xbbcb8c: stur            w1, [x0, #0xb]
    // 0xbbcb90: ldur            x2, [fp, #-0x18]
    // 0xbbcb94: r1 = Function '<anonymous closure>':.
    //     0xbbcb94: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5adb8] AnonymousClosure: (0xbbce30), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewWidget::build (0xbbcaec)
    //     0xbbcb98: ldr             x1, [x1, #0xdb8]
    // 0xbbcb9c: r0 = AllocateClosure()
    //     0xbbcb9c: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbcba0: mov             x1, x0
    // 0xbbcba4: ldur            x0, [fp, #-0x10]
    // 0xbbcba8: StoreField: r0->field_f = r1
    //     0xbbcba8: stur            w1, [x0, #0xf]
    // 0xbbcbac: ldur            x1, [fp, #-8]
    // 0xbbcbb0: StoreField: r0->field_7 = r1
    //     0xbbcbb0: stur            w1, [x0, #7]
    // 0xbbcbb4: LeaveFrame
    //     0xbbcbb4: mov             SP, fp
    //     0xbbcbb8: ldp             fp, lr, [SP], #0x10
    // 0xbbcbbc: ret
    //     0xbbcbbc: ret             
    // 0xbbcbc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbcbc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbcbc4: b               #0xbbcb0c
  }
  _ _trySetDefaultOnShowCustomWidgetCallbacks(/* No info */) {
    // ** addr: 0xbbcbc8, size: 0xac
    // 0xbbcbc8: EnterFrame
    //     0xbbcbc8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbcbcc: mov             fp, SP
    // 0xbbcbd0: AllocStack(0x18)
    //     0xbbcbd0: sub             SP, SP, #0x18
    // 0xbbcbd4: SetupParameters(AndroidWebViewWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbbcbd4: stur            x1, [fp, #-8]
    //     0xbbcbd8: stur            x2, [fp, #-0x10]
    // 0xbbcbdc: CheckStackOverflow
    //     0xbbcbdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbcbe0: cmp             SP, x16
    //     0xbbcbe4: b.ls            #0xbbcc6c
    // 0xbbcbe8: r1 = 1
    //     0xbbcbe8: movz            x1, #0x1
    // 0xbbcbec: r0 = AllocateContext()
    //     0xbbcbec: bl              #0xec126c  ; AllocateContextStub
    // 0xbbcbf0: mov             x3, x0
    // 0xbbcbf4: ldur            x0, [fp, #-0x10]
    // 0xbbcbf8: stur            x3, [fp, #-0x18]
    // 0xbbcbfc: StoreField: r3->field_f = r0
    //     0xbbcbfc: stur            w0, [x3, #0xf]
    // 0xbbcc00: ldur            x0, [fp, #-8]
    // 0xbbcc04: LoadField: r1 = r0->field_7
    //     0xbbcc04: ldur            w1, [x0, #7]
    // 0xbbcc08: DecompressPointer r1
    //     0xbbcc08: add             x1, x1, HEAP, lsl #32
    // 0xbbcc0c: LoadField: r0 = r1->field_b
    //     0xbbcc0c: ldur            w0, [x1, #0xb]
    // 0xbbcc10: DecompressPointer r0
    //     0xbbcc10: add             x0, x0, HEAP, lsl #32
    // 0xbbcc14: stur            x0, [fp, #-8]
    // 0xbbcc18: LoadField: r1 = r0->field_27
    //     0xbbcc18: ldur            w1, [x0, #0x27]
    // 0xbbcc1c: DecompressPointer r1
    //     0xbbcc1c: add             x1, x1, HEAP, lsl #32
    // 0xbbcc20: cmp             w1, NULL
    // 0xbbcc24: b.ne            #0xbbcc5c
    // 0xbbcc28: mov             x2, x3
    // 0xbbcc2c: r1 = Function '<anonymous closure>':.
    //     0xbbcc2c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ade0] AnonymousClosure: (0xbbcd64), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewWidget::_trySetDefaultOnShowCustomWidgetCallbacks (0xbbcbc8)
    //     0xbbcc30: ldr             x1, [x1, #0xde0]
    // 0xbbcc34: r0 = AllocateClosure()
    //     0xbbcc34: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbcc38: ldur            x2, [fp, #-0x18]
    // 0xbbcc3c: r1 = Function '<anonymous closure>':.
    //     0xbbcc3c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ade8] AnonymousClosure: (0xbbcd04), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewWidget::_trySetDefaultOnShowCustomWidgetCallbacks (0xbbcbc8)
    //     0xbbcc40: ldr             x1, [x1, #0xde8]
    // 0xbbcc44: stur            x0, [fp, #-0x10]
    // 0xbbcc48: r0 = AllocateClosure()
    //     0xbbcc48: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbcc4c: ldur            x1, [fp, #-8]
    // 0xbbcc50: mov             x2, x0
    // 0xbbcc54: ldur            x3, [fp, #-0x10]
    // 0xbbcc58: r0 = setCustomWidgetCallbacks()
    //     0xbbcc58: bl              #0xbbcc74  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::setCustomWidgetCallbacks
    // 0xbbcc5c: r0 = Null
    //     0xbbcc5c: mov             x0, NULL
    // 0xbbcc60: LeaveFrame
    //     0xbbcc60: mov             SP, fp
    //     0xbbcc64: ldp             fp, lr, [SP], #0x10
    // 0xbbcc68: ret
    //     0xbbcc68: ret             
    // 0xbbcc6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbcc6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbcc70: b               #0xbbcbe8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbcd04, size: 0x60
    // 0xbbcd04: EnterFrame
    //     0xbbcd04: stp             fp, lr, [SP, #-0x10]!
    //     0xbbcd08: mov             fp, SP
    // 0xbbcd0c: AllocStack(0x10)
    //     0xbbcd0c: sub             SP, SP, #0x10
    // 0xbbcd10: SetupParameters()
    //     0xbbcd10: ldr             x0, [fp, #0x10]
    //     0xbbcd14: ldur            w1, [x0, #0x17]
    //     0xbbcd18: add             x1, x1, HEAP, lsl #32
    // 0xbbcd1c: CheckStackOverflow
    //     0xbbcd1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbcd20: cmp             SP, x16
    //     0xbbcd24: b.ls            #0xbbcd5c
    // 0xbbcd28: LoadField: r0 = r1->field_f
    //     0xbbcd28: ldur            w0, [x1, #0xf]
    // 0xbbcd2c: DecompressPointer r0
    //     0xbbcd2c: add             x0, x0, HEAP, lsl #32
    // 0xbbcd30: mov             x1, x0
    // 0xbbcd34: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbbcd34: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbbcd38: r0 = of()
    //     0xbbcd38: bl              #0x917a34  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xbbcd3c: r16 = <Object?>
    //     0xbbcd3c: ldr             x16, [PP, #0x198]  ; [pp+0x198] TypeArguments: <Object?>
    // 0xbbcd40: stp             x0, x16, [SP]
    // 0xbbcd44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbcd44: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbcd48: r0 = pop()
    //     0xbbcd48: bl              #0x63e39c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xbbcd4c: r0 = Null
    //     0xbbcd4c: mov             x0, NULL
    // 0xbbcd50: LeaveFrame
    //     0xbbcd50: mov             SP, fp
    //     0xbbcd54: ldp             fp, lr, [SP], #0x10
    // 0xbbcd58: ret
    //     0xbbcd58: ret             
    // 0xbbcd5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbcd5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbcd60: b               #0xbbcd28
  }
  [closure] void <anonymous closure>(dynamic, Widget, (dynamic) => void) {
    // ** addr: 0xbbcd64, size: 0xcc
    // 0xbbcd64: EnterFrame
    //     0xbbcd64: stp             fp, lr, [SP, #-0x10]!
    //     0xbbcd68: mov             fp, SP
    // 0xbbcd6c: AllocStack(0x30)
    //     0xbbcd6c: sub             SP, SP, #0x30
    // 0xbbcd70: SetupParameters()
    //     0xbbcd70: ldr             x0, [fp, #0x20]
    //     0xbbcd74: ldur            w1, [x0, #0x17]
    //     0xbbcd78: add             x1, x1, HEAP, lsl #32
    //     0xbbcd7c: stur            x1, [fp, #-8]
    // 0xbbcd80: CheckStackOverflow
    //     0xbbcd80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbcd84: cmp             SP, x16
    //     0xbbcd88: b.ls            #0xbbce28
    // 0xbbcd8c: r1 = 1
    //     0xbbcd8c: movz            x1, #0x1
    // 0xbbcd90: r0 = AllocateContext()
    //     0xbbcd90: bl              #0xec126c  ; AllocateContextStub
    // 0xbbcd94: mov             x2, x0
    // 0xbbcd98: ldur            x0, [fp, #-8]
    // 0xbbcd9c: stur            x2, [fp, #-0x10]
    // 0xbbcda0: StoreField: r2->field_b = r0
    //     0xbbcda0: stur            w0, [x2, #0xb]
    // 0xbbcda4: ldr             x1, [fp, #0x18]
    // 0xbbcda8: StoreField: r2->field_f = r1
    //     0xbbcda8: stur            w1, [x2, #0xf]
    // 0xbbcdac: LoadField: r1 = r0->field_f
    //     0xbbcdac: ldur            w1, [x0, #0xf]
    // 0xbbcdb0: DecompressPointer r1
    //     0xbbcdb0: add             x1, x1, HEAP, lsl #32
    // 0xbbcdb4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbbcdb4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbbcdb8: r0 = of()
    //     0xbbcdb8: bl              #0x917a34  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xbbcdbc: ldur            x2, [fp, #-0x10]
    // 0xbbcdc0: r1 = Function '<anonymous closure>':.
    //     0xbbcdc0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5adf0] AnonymousClosure: static (0x5fe1f0), in [dart:async] _Future::_propagateToListeners (0x5fa1e8)
    //     0xbbcdc4: ldr             x1, [x1, #0xdf0]
    // 0xbbcdc8: stur            x0, [fp, #-8]
    // 0xbbcdcc: r0 = AllocateClosure()
    //     0xbbcdcc: bl              #0xec1630  ; AllocateClosureStub
    // 0xbbcdd0: r1 = <void?>
    //     0xbbcdd0: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xbbcdd4: stur            x0, [fp, #-0x10]
    // 0xbbcdd8: r0 = MaterialPageRoute()
    //     0xbbcdd8: bl              #0x9e4b78  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xa4)
    // 0xbbcddc: stur            x0, [fp, #-0x18]
    // 0xbbcde0: r16 = true
    //     0xbbcde0: add             x16, NULL, #0x20  ; true
    // 0xbbcde4: str             x16, [SP]
    // 0xbbcde8: mov             x1, x0
    // 0xbbcdec: ldur            x2, [fp, #-0x10]
    // 0xbbcdf0: r4 = const [0, 0x3, 0x1, 0x2, fullscreenDialog, 0x2, null]
    //     0xbbcdf0: add             x4, PP, #0x5a, lsl #12  ; [pp+0x5adf8] List(7) [0, 0x3, 0x1, 0x2, "fullscreenDialog", 0x2, Null]
    //     0xbbcdf4: ldr             x4, [x4, #0xdf8]
    // 0xbbcdf8: r0 = MaterialPageRoute()
    //     0xbbcdf8: bl              #0x9e4a78  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xbbcdfc: r16 = <void?>
    //     0xbbcdfc: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0xbbce00: ldur            lr, [fp, #-8]
    // 0xbbce04: stp             lr, x16, [SP, #8]
    // 0xbbce08: ldur            x16, [fp, #-0x18]
    // 0xbbce0c: str             x16, [SP]
    // 0xbbce10: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbbce10: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbbce14: r0 = push()
    //     0xbbce14: bl              #0x6598bc  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xbbce18: r0 = Null
    //     0xbbce18: mov             x0, NULL
    // 0xbbce1c: LeaveFrame
    //     0xbbce1c: mov             SP, fp
    //     0xbbce20: ldp             fp, lr, [SP], #0x10
    // 0xbbce24: ret
    //     0xbbce24: ret             
    // 0xbbce28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbce28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbce2c: b               #0xbbcd8c
  }
  [closure] AndroidViewController <anonymous closure>(dynamic, PlatformViewCreationParams) {
    // ** addr: 0xbbce30, size: 0x184
    // 0xbbce30: EnterFrame
    //     0xbbce30: stp             fp, lr, [SP, #-0x10]!
    //     0xbbce34: mov             fp, SP
    // 0xbbce38: AllocStack(0x20)
    //     0xbbce38: sub             SP, SP, #0x20
    // 0xbbce3c: SetupParameters()
    //     0xbbce3c: ldr             x0, [fp, #0x18]
    //     0xbbce40: ldur            w2, [x0, #0x17]
    //     0xbbce44: add             x2, x2, HEAP, lsl #32
    //     0xbbce48: stur            x2, [fp, #-8]
    // 0xbbce4c: CheckStackOverflow
    //     0xbbce4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbce50: cmp             SP, x16
    //     0xbbce54: b.ls            #0xbbcfac
    // 0xbbce58: LoadField: r0 = r2->field_f
    //     0xbbce58: ldur            w0, [x2, #0xf]
    // 0xbbce5c: DecompressPointer r0
    //     0xbbce5c: add             x0, x0, HEAP, lsl #32
    // 0xbbce60: LoadField: r1 = r0->field_7
    //     0xbbce60: ldur            w1, [x0, #7]
    // 0xbbce64: DecompressPointer r1
    //     0xbbce64: add             x1, x1, HEAP, lsl #32
    // 0xbbce68: LoadField: r0 = r1->field_b
    //     0xbbce68: ldur            w0, [x1, #0xb]
    // 0xbbce6c: DecompressPointer r0
    //     0xbbce6c: add             x0, x0, HEAP, lsl #32
    // 0xbbce70: mov             x1, x0
    // 0xbbce74: LoadField: r0 = r1->field_b
    //     0xbbce74: ldur            w0, [x1, #0xb]
    // 0xbbce78: DecompressPointer r0
    //     0xbbce78: add             x0, x0, HEAP, lsl #32
    // 0xbbce7c: r16 = Sentinel
    //     0xbbce7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbce80: cmp             w0, w16
    // 0xbbce84: b.ne            #0xbbce94
    // 0xbbce88: r2 = _webView
    //     0xbbce88: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0xbbce8c: ldr             x2, [x2, #0xf30]
    // 0xbbce90: r0 = InitLateFinalInstanceField()
    //     0xbbce90: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xbbce94: mov             x1, x0
    // 0xbbce98: ldur            x0, [fp, #-8]
    // 0xbbce9c: LoadField: r2 = r0->field_f
    //     0xbbce9c: ldur            w2, [x0, #0xf]
    // 0xbbcea0: DecompressPointer r2
    //     0xbbcea0: add             x2, x2, HEAP, lsl #32
    // 0xbbcea4: LoadField: r0 = r2->field_7
    //     0xbbcea4: ldur            w0, [x2, #7]
    // 0xbbcea8: DecompressPointer r0
    //     0xbbcea8: add             x0, x0, HEAP, lsl #32
    // 0xbbceac: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbbceac: ldur            w2, [x0, #0x17]
    // 0xbbceb0: DecompressPointer r2
    //     0xbbceb0: add             x2, x2, HEAP, lsl #32
    // 0xbbceb4: mov             x3, x1
    // 0xbbceb8: ldr             x1, [fp, #0x10]
    // 0xbbcebc: r0 = _initAndroidView()
    //     0xbbcebc: bl              #0xbbcfb4  ; [package:webview_flutter_android/src/android_webview_controller.dart] ::_initAndroidView
    // 0xbbcec0: mov             x3, x0
    // 0xbbcec4: ldr             x0, [fp, #0x10]
    // 0xbbcec8: stur            x3, [fp, #-0x18]
    // 0xbbcecc: LoadField: r4 = r0->field_f
    //     0xbbcecc: ldur            w4, [x0, #0xf]
    // 0xbbced0: DecompressPointer r4
    //     0xbbced0: add             x4, x4, HEAP, lsl #32
    // 0xbbced4: stur            x4, [fp, #-0x10]
    // 0xbbced8: LoadField: r5 = r3->field_23
    //     0xbbced8: ldur            w5, [x3, #0x23]
    // 0xbbcedc: DecompressPointer r5
    //     0xbbcedc: add             x5, x5, HEAP, lsl #32
    // 0xbbcee0: stur            x5, [fp, #-8]
    // 0xbbcee4: LoadField: r2 = r5->field_7
    //     0xbbcee4: ldur            w2, [x5, #7]
    // 0xbbcee8: DecompressPointer r2
    //     0xbbcee8: add             x2, x2, HEAP, lsl #32
    // 0xbbceec: mov             x0, x4
    // 0xbbcef0: r1 = Null
    //     0xbbcef0: mov             x1, NULL
    // 0xbbcef4: cmp             w2, NULL
    // 0xbbcef8: b.eq            #0xbbcf18
    // 0xbbcefc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbbcefc: ldur            w4, [x2, #0x17]
    // 0xbbcf00: DecompressPointer r4
    //     0xbbcf00: add             x4, x4, HEAP, lsl #32
    // 0xbbcf04: r8 = X0
    //     0xbbcf04: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xbbcf08: LoadField: r9 = r4->field_7
    //     0xbbcf08: ldur            x9, [x4, #7]
    // 0xbbcf0c: r3 = Null
    //     0xbbcf0c: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5adc0] Null
    //     0xbbcf10: ldr             x3, [x3, #0xdc0]
    // 0xbbcf14: blr             x9
    // 0xbbcf18: ldur            x0, [fp, #-8]
    // 0xbbcf1c: LoadField: r1 = r0->field_b
    //     0xbbcf1c: ldur            w1, [x0, #0xb]
    // 0xbbcf20: LoadField: r2 = r0->field_f
    //     0xbbcf20: ldur            w2, [x0, #0xf]
    // 0xbbcf24: DecompressPointer r2
    //     0xbbcf24: add             x2, x2, HEAP, lsl #32
    // 0xbbcf28: LoadField: r3 = r2->field_b
    //     0xbbcf28: ldur            w3, [x2, #0xb]
    // 0xbbcf2c: r2 = LoadInt32Instr(r1)
    //     0xbbcf2c: sbfx            x2, x1, #1, #0x1f
    // 0xbbcf30: stur            x2, [fp, #-0x20]
    // 0xbbcf34: r1 = LoadInt32Instr(r3)
    //     0xbbcf34: sbfx            x1, x3, #1, #0x1f
    // 0xbbcf38: cmp             x2, x1
    // 0xbbcf3c: b.ne            #0xbbcf48
    // 0xbbcf40: mov             x1, x0
    // 0xbbcf44: r0 = _growToNextCapacity()
    //     0xbbcf44: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbcf48: ldur            x0, [fp, #-8]
    // 0xbbcf4c: ldur            x2, [fp, #-0x20]
    // 0xbbcf50: add             x1, x2, #1
    // 0xbbcf54: lsl             x3, x1, #1
    // 0xbbcf58: StoreField: r0->field_b = r3
    //     0xbbcf58: stur            w3, [x0, #0xb]
    // 0xbbcf5c: LoadField: r1 = r0->field_f
    //     0xbbcf5c: ldur            w1, [x0, #0xf]
    // 0xbbcf60: DecompressPointer r1
    //     0xbbcf60: add             x1, x1, HEAP, lsl #32
    // 0xbbcf64: ldur            x0, [fp, #-0x10]
    // 0xbbcf68: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbbcf68: add             x25, x1, x2, lsl #2
    //     0xbbcf6c: add             x25, x25, #0xf
    //     0xbbcf70: str             w0, [x25]
    //     0xbbcf74: tbz             w0, #0, #0xbbcf90
    //     0xbbcf78: ldurb           w16, [x1, #-1]
    //     0xbbcf7c: ldurb           w17, [x0, #-1]
    //     0xbbcf80: and             x16, x17, x16, lsr #2
    //     0xbbcf84: tst             x16, HEAP, lsr #32
    //     0xbbcf88: b.eq            #0xbbcf90
    //     0xbbcf8c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xbbcf90: ldur            x1, [fp, #-0x18]
    // 0xbbcf94: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbbcf94: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbbcf98: r0 = create()
    //     0xbbcf98: bl              #0x7e1f3c  ; [package:flutter/src/services/platform_views.dart] AndroidViewController::create
    // 0xbbcf9c: ldur            x0, [fp, #-0x18]
    // 0xbbcfa0: LeaveFrame
    //     0xbbcfa0: mov             SP, fp
    //     0xbbcfa4: ldp             fp, lr, [SP], #0x10
    // 0xbbcfa8: ret
    //     0xbbcfa8: ret             
    // 0xbbcfac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbcfac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbcfb0: b               #0xbbce58
  }
  [closure] AndroidViewSurface <anonymous closure>(dynamic, BuildContext, PlatformViewController) {
    // ** addr: 0xbbd0b4, size: 0x6c
    // 0xbbd0b4: EnterFrame
    //     0xbbd0b4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd0b8: mov             fp, SP
    // 0xbbd0bc: ldr             x0, [fp, #0x10]
    // 0xbbd0c0: r2 = Null
    //     0xbbd0c0: mov             x2, NULL
    // 0xbbd0c4: r1 = Null
    //     0xbbd0c4: mov             x1, NULL
    // 0xbbd0c8: r4 = LoadClassIdInstr(r0)
    //     0xbbd0c8: ldur            x4, [x0, #-1]
    //     0xbbd0cc: ubfx            x4, x4, #0xc, #0x14
    // 0xbbd0d0: sub             x4, x4, #0xadb
    // 0xbbd0d4: cmp             x4, #1
    // 0xbbd0d8: b.ls            #0xbbd0f0
    // 0xbbd0dc: r8 = AndroidViewController
    //     0xbbd0dc: add             x8, PP, #0x42, lsl #12  ; [pp+0x42910] Type: AndroidViewController
    //     0xbbd0e0: ldr             x8, [x8, #0x910]
    // 0xbbd0e4: r3 = Null
    //     0xbbd0e4: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5add0] Null
    //     0xbbd0e8: ldr             x3, [x3, #0xdd0]
    // 0xbbd0ec: r0 = DefaultTypeTest()
    //     0xbbd0ec: bl              #0xec00c0  ; DefaultTypeTestStub
    // 0xbbd0f0: r0 = AndroidViewSurface()
    //     0xbbd0f0: bl              #0xa2ceb0  ; AllocateAndroidViewSurfaceStub -> AndroidViewSurface (size=0x18)
    // 0xbbd0f4: ldr             x1, [fp, #0x10]
    // 0xbbd0f8: StoreField: r0->field_b = r1
    //     0xbbd0f8: stur            w1, [x0, #0xb]
    // 0xbbd0fc: r1 = Instance_PlatformViewHitTestBehavior
    //     0xbbd0fc: add             x1, PP, #0x42, lsl #12  ; [pp+0x42928] Obj!PlatformViewHitTestBehavior@e35961
    //     0xbbd100: ldr             x1, [x1, #0x928]
    // 0xbbd104: StoreField: r0->field_13 = r1
    //     0xbbd104: stur            w1, [x0, #0x13]
    // 0xbbd108: r1 = _ConstSet len:0
    //     0xbbd108: add             x1, PP, #0x42, lsl #12  ; [pp+0x42930] Set<Factory<OneSequenceGestureRecognizer>>(0)
    //     0xbbd10c: ldr             x1, [x1, #0x930]
    // 0xbbd110: StoreField: r0->field_f = r1
    //     0xbbd110: stur            w1, [x0, #0xf]
    // 0xbbd114: LeaveFrame
    //     0xbbd114: mov             SP, fp
    //     0xbbd118: ldp             fp, lr, [SP], #0x10
    // 0xbbd11c: ret
    //     0xbbd11c: ret             
  }
}

// class id: 5856, size: 0x48, field offset: 0xc
class AndroidWebViewController extends PlatformWebViewController {

  late final WebView _webView; // offset: 0xc
  late final WebChromeClient _webChromeClient; // offset: 0x10

  _ runJavaScript(/* No info */) {
    // ** addr: 0x965be4, size: 0x5c
    // 0x965be4: EnterFrame
    //     0x965be4: stp             fp, lr, [SP, #-0x10]!
    //     0x965be8: mov             fp, SP
    // 0x965bec: AllocStack(0x8)
    //     0x965bec: sub             SP, SP, #8
    // 0x965bf0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x965bf0: stur            x2, [fp, #-8]
    // 0x965bf4: CheckStackOverflow
    //     0x965bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x965bf8: cmp             SP, x16
    //     0x965bfc: b.ls            #0x965c38
    // 0x965c00: LoadField: r0 = r1->field_b
    //     0x965c00: ldur            w0, [x1, #0xb]
    // 0x965c04: DecompressPointer r0
    //     0x965c04: add             x0, x0, HEAP, lsl #32
    // 0x965c08: r16 = Sentinel
    //     0x965c08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x965c0c: cmp             w0, w16
    // 0x965c10: b.ne            #0x965c20
    // 0x965c14: r2 = _webView
    //     0x965c14: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x965c18: ldr             x2, [x2, #0xf30]
    // 0x965c1c: r0 = InitLateFinalInstanceField()
    //     0x965c1c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x965c20: mov             x1, x0
    // 0x965c24: ldur            x2, [fp, #-8]
    // 0x965c28: r0 = evaluateJavascript()
    //     0x965c28: bl              #0x965c40  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::evaluateJavascript
    // 0x965c2c: LeaveFrame
    //     0x965c2c: mov             SP, fp
    //     0x965c30: ldp             fp, lr, [SP], #0x10
    // 0x965c34: ret
    //     0x965c34: ret             
    // 0x965c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x965c38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x965c3c: b               #0x965c00
  }
  WebView _webView(AndroidWebViewController) {
    // ** addr: 0x965f98, size: 0x84
    // 0x965f98: EnterFrame
    //     0x965f98: stp             fp, lr, [SP, #-0x10]!
    //     0x965f9c: mov             fp, SP
    // 0x965fa0: AllocStack(0x28)
    //     0x965fa0: sub             SP, SP, #0x28
    // 0x965fa4: CheckStackOverflow
    //     0x965fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x965fa8: cmp             SP, x16
    //     0x965fac: b.ls            #0x966014
    // 0x965fb0: r1 = Function '<anonymous closure>':.
    //     0x965fb0: add             x1, PP, #0x47, lsl #12  ; [pp+0x47f38] AnonymousClosure: (0x970934), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webView (0x965f98)
    //     0x965fb4: ldr             x1, [x1, #0xf38]
    // 0x965fb8: r2 = Null
    //     0x965fb8: mov             x2, NULL
    // 0x965fbc: r0 = AllocateClosure()
    //     0x965fbc: bl              #0xec1630  ; AllocateClosureStub
    // 0x965fc0: r16 = <AndroidWebViewController, (dynamic this, WebView, int, int, int, int) => void?>
    //     0x965fc0: add             x16, PP, #0x47, lsl #12  ; [pp+0x47f40] TypeArguments: <AndroidWebViewController, (dynamic this, WebView, int, int, int, int) => void?>
    //     0x965fc4: ldr             x16, [x16, #0xf40]
    // 0x965fc8: ldr             lr, [fp, #0x10]
    // 0x965fcc: stp             lr, x16, [SP, #8]
    // 0x965fd0: str             x0, [SP]
    // 0x965fd4: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x965fd4: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x965fd8: r0 = withWeakReferenceTo()
    //     0x965fd8: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x965fdc: stur            x0, [fp, #-8]
    // 0x965fe0: r0 = WebView()
    //     0x965fe0: bl              #0x9299e4  ; AllocateWebViewStub -> WebView (size=0x1c)
    // 0x965fe4: stur            x0, [fp, #-0x10]
    // 0x965fe8: stp             NULL, NULL, [SP, #8]
    // 0x965fec: ldur            x16, [fp, #-8]
    // 0x965ff0: str             x16, [SP]
    // 0x965ff4: mov             x1, x0
    // 0x965ff8: r4 = const [0, 0x4, 0x3, 0x1, onScrollChanged, 0x3, pigeon_binaryMessenger, 0x1, pigeon_instanceManager, 0x2, null]
    //     0x965ff8: add             x4, PP, #0x47, lsl #12  ; [pp+0x47f48] List(11) [0, 0x4, 0x3, 0x1, "onScrollChanged", 0x3, "pigeon_binaryMessenger", 0x1, "pigeon_instanceManager", 0x2, Null]
    //     0x965ffc: ldr             x4, [x4, #0xf48]
    // 0x966000: r0 = WebView()
    //     0x966000: bl              #0x966170  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::WebView
    // 0x966004: ldur            x0, [fp, #-0x10]
    // 0x966008: LeaveFrame
    //     0x966008: mov             SP, fp
    //     0x96600c: ldp             fp, lr, [SP], #0x10
    // 0x966010: ret
    //     0x966010: ret             
    // 0x966014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x966014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x966018: b               #0x965fb0
  }
  [closure] (dynamic, WebView, int, int, int, int) => Future<void> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x970934, size: 0x54
    // 0x970934: EnterFrame
    //     0x970934: stp             fp, lr, [SP, #-0x10]!
    //     0x970938: mov             fp, SP
    // 0x97093c: AllocStack(0x8)
    //     0x97093c: sub             SP, SP, #8
    // 0x970940: SetupParameters()
    //     0x970940: ldr             x0, [fp, #0x18]
    //     0x970944: ldur            w1, [x0, #0x17]
    //     0x970948: add             x1, x1, HEAP, lsl #32
    //     0x97094c: stur            x1, [fp, #-8]
    // 0x970950: r1 = 1
    //     0x970950: movz            x1, #0x1
    // 0x970954: r0 = AllocateContext()
    //     0x970954: bl              #0xec126c  ; AllocateContextStub
    // 0x970958: mov             x1, x0
    // 0x97095c: ldur            x0, [fp, #-8]
    // 0x970960: StoreField: r1->field_b = r0
    //     0x970960: stur            w0, [x1, #0xb]
    // 0x970964: ldr             x0, [fp, #0x10]
    // 0x970968: StoreField: r1->field_f = r0
    //     0x970968: stur            w0, [x1, #0xf]
    // 0x97096c: mov             x2, x1
    // 0x970970: r1 = Function '<anonymous closure>':.
    //     0x970970: add             x1, PP, #0x47, lsl #12  ; [pp+0x47f50] AnonymousClosure: (0x970988), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webView (0x965f98)
    //     0x970974: ldr             x1, [x1, #0xf50]
    // 0x970978: r0 = AllocateClosure()
    //     0x970978: bl              #0xec1630  ; AllocateClosureStub
    // 0x97097c: LeaveFrame
    //     0x97097c: mov             SP, fp
    //     0x970980: ldp             fp, lr, [SP], #0x10
    // 0x970984: ret
    //     0x970984: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic, WebView, int, int, int, int) async {
    // ** addr: 0x970988, size: 0x4c
    // 0x970988: EnterFrame
    //     0x970988: stp             fp, lr, [SP, #-0x10]!
    //     0x97098c: mov             fp, SP
    // 0x970990: AllocStack(0x10)
    //     0x970990: sub             SP, SP, #0x10
    // 0x970994: SetupParameters(AndroidWebViewController this /* r1 */)
    //     0x970994: stur            NULL, [fp, #-8]
    //     0x970998: movz            x0, #0
    //     0x97099c: add             x1, fp, w0, sxtw #2
    //     0x9709a0: ldr             x1, [x1, #0x38]
    //     0x9709a4: ldur            w2, [x1, #0x17]
    //     0x9709a8: add             x2, x2, HEAP, lsl #32
    //     0x9709ac: stur            x2, [fp, #-0x10]
    // 0x9709b0: CheckStackOverflow
    //     0x9709b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9709b4: cmp             SP, x16
    //     0x9709b8: b.ls            #0x9709cc
    // 0x9709bc: InitAsync() -> Future<void?>
    //     0x9709bc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x9709c0: bl              #0x661298  ; InitAsyncStub
    // 0x9709c4: r0 = Null
    //     0x9709c4: mov             x0, NULL
    // 0x9709c8: r0 = ReturnAsyncNotFuture()
    //     0x9709c8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9709cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9709cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9709d0: b               #0x9709bc
  }
  _ setMediaPlaybackRequiresUserGesture(/* No info */) {
    // ** addr: 0x970e80, size: 0x74
    // 0x970e80: EnterFrame
    //     0x970e80: stp             fp, lr, [SP, #-0x10]!
    //     0x970e84: mov             fp, SP
    // 0x970e88: CheckStackOverflow
    //     0x970e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x970e8c: cmp             SP, x16
    //     0x970e90: b.ls            #0x970eec
    // 0x970e94: LoadField: r0 = r1->field_b
    //     0x970e94: ldur            w0, [x1, #0xb]
    // 0x970e98: DecompressPointer r0
    //     0x970e98: add             x0, x0, HEAP, lsl #32
    // 0x970e9c: r16 = Sentinel
    //     0x970e9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x970ea0: cmp             w0, w16
    // 0x970ea4: b.ne            #0x970eb4
    // 0x970ea8: r2 = _webView
    //     0x970ea8: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x970eac: ldr             x2, [x2, #0xf30]
    // 0x970eb0: r0 = InitLateFinalInstanceField()
    //     0x970eb0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x970eb4: mov             x1, x0
    // 0x970eb8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x970eb8: ldur            w0, [x1, #0x17]
    // 0x970ebc: DecompressPointer r0
    //     0x970ebc: add             x0, x0, HEAP, lsl #32
    // 0x970ec0: r16 = Sentinel
    //     0x970ec0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x970ec4: cmp             w0, w16
    // 0x970ec8: b.ne            #0x970ed8
    // 0x970ecc: r2 = settings
    //     0x970ecc: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x970ed0: ldr             x2, [x2, #0x110]
    // 0x970ed4: r0 = InitLateFinalInstanceField()
    //     0x970ed4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x970ed8: mov             x1, x0
    // 0x970edc: r0 = setMediaPlaybackRequiresUserGesture()
    //     0x970edc: bl              #0x970ef4  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setMediaPlaybackRequiresUserGesture
    // 0x970ee0: LeaveFrame
    //     0x970ee0: mov             SP, fp
    //     0x970ee4: ldp             fp, lr, [SP], #0x10
    // 0x970ee8: ret
    //     0x970ee8: ret             
    // 0x970eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x970eec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x970ef0: b               #0x970e94
  }
  static Future<void> enableDebugging() {
    // ** addr: 0x9715bc, size: 0x40
    // 0x9715bc: EnterFrame
    //     0x9715bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9715c0: mov             fp, SP
    // 0x9715c4: AllocStack(0x10)
    //     0x9715c4: sub             SP, SP, #0x10
    // 0x9715c8: CheckStackOverflow
    //     0x9715c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9715cc: cmp             SP, x16
    //     0x9715d0: b.ls            #0x9715f4
    // 0x9715d4: stp             NULL, NULL, [SP]
    // 0x9715d8: r1 = false
    //     0x9715d8: add             x1, NULL, #0x30  ; false
    // 0x9715dc: r4 = const [0, 0x3, 0x2, 0x1, pigeon_binaryMessenger, 0x1, pigeon_instanceManager, 0x2, null]
    //     0x9715dc: add             x4, PP, #0x49, lsl #12  ; [pp+0x49198] List(9) [0, 0x3, 0x2, 0x1, "pigeon_binaryMessenger", 0x1, "pigeon_instanceManager", 0x2, Null]
    //     0x9715e0: ldr             x4, [x4, #0x198]
    // 0x9715e4: r0 = setWebContentsDebuggingEnabled()
    //     0x9715e4: bl              #0x9716d8  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::setWebContentsDebuggingEnabled
    // 0x9715e8: LeaveFrame
    //     0x9715e8: mov             SP, fp
    //     0x9715ec: ldp             fp, lr, [SP], #0x10
    // 0x9715f0: ret
    //     0x9715f0: ret             
    // 0x9715f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9715f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9715f8: b               #0x9715d4
  }
  _ enableZoom(/* No info */) {
    // ** addr: 0x971a78, size: 0x74
    // 0x971a78: EnterFrame
    //     0x971a78: stp             fp, lr, [SP, #-0x10]!
    //     0x971a7c: mov             fp, SP
    // 0x971a80: CheckStackOverflow
    //     0x971a80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x971a84: cmp             SP, x16
    //     0x971a88: b.ls            #0x971ae4
    // 0x971a8c: LoadField: r0 = r1->field_b
    //     0x971a8c: ldur            w0, [x1, #0xb]
    // 0x971a90: DecompressPointer r0
    //     0x971a90: add             x0, x0, HEAP, lsl #32
    // 0x971a94: r16 = Sentinel
    //     0x971a94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x971a98: cmp             w0, w16
    // 0x971a9c: b.ne            #0x971aac
    // 0x971aa0: r2 = _webView
    //     0x971aa0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x971aa4: ldr             x2, [x2, #0xf30]
    // 0x971aa8: r0 = InitLateFinalInstanceField()
    //     0x971aa8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x971aac: mov             x1, x0
    // 0x971ab0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x971ab0: ldur            w0, [x1, #0x17]
    // 0x971ab4: DecompressPointer r0
    //     0x971ab4: add             x0, x0, HEAP, lsl #32
    // 0x971ab8: r16 = Sentinel
    //     0x971ab8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x971abc: cmp             w0, w16
    // 0x971ac0: b.ne            #0x971ad0
    // 0x971ac4: r2 = settings
    //     0x971ac4: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x971ac8: ldr             x2, [x2, #0x110]
    // 0x971acc: r0 = InitLateFinalInstanceField()
    //     0x971acc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x971ad0: mov             x1, x0
    // 0x971ad4: r0 = setSupportZoom()
    //     0x971ad4: bl              #0x971aec  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setSupportZoom
    // 0x971ad8: LeaveFrame
    //     0x971ad8: mov             SP, fp
    //     0x971adc: ldp             fp, lr, [SP], #0x10
    // 0x971ae0: ret
    //     0x971ae0: ret             
    // 0x971ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x971ae4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x971ae8: b               #0x971a8c
  }
  _ addJavaScriptChannel(/* No info */) {
    // ** addr: 0x971e04, size: 0x10c
    // 0x971e04: EnterFrame
    //     0x971e04: stp             fp, lr, [SP, #-0x10]!
    //     0x971e08: mov             fp, SP
    // 0x971e0c: AllocStack(0x20)
    //     0x971e0c: sub             SP, SP, #0x20
    // 0x971e10: SetupParameters(AndroidWebViewController this /* r1 => r1, fp-0x18 */)
    //     0x971e10: stur            x1, [fp, #-0x18]
    // 0x971e14: CheckStackOverflow
    //     0x971e14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x971e18: cmp             SP, x16
    //     0x971e1c: b.ls            #0x971f08
    // 0x971e20: LoadField: r0 = r2->field_7
    //     0x971e20: ldur            w0, [x2, #7]
    // 0x971e24: DecompressPointer r0
    //     0x971e24: add             x0, x0, HEAP, lsl #32
    // 0x971e28: stur            x0, [fp, #-0x10]
    // 0x971e2c: LoadField: r3 = r2->field_b
    //     0x971e2c: ldur            w3, [x2, #0xb]
    // 0x971e30: DecompressPointer r3
    //     0x971e30: add             x3, x3, HEAP, lsl #32
    // 0x971e34: stur            x3, [fp, #-8]
    // 0x971e38: r0 = AndroidJavaScriptChannelParams()
    //     0x971e38: bl              #0x972d74  ; AllocateAndroidJavaScriptChannelParamsStub -> AndroidJavaScriptChannelParams (size=0x14)
    // 0x971e3c: mov             x1, x0
    // 0x971e40: ldur            x2, [fp, #-0x10]
    // 0x971e44: ldur            x3, [fp, #-8]
    // 0x971e48: stur            x0, [fp, #-8]
    // 0x971e4c: r0 = AndroidJavaScriptChannelParams()
    //     0x971e4c: bl              #0x9724a0  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidJavaScriptChannelParams::AndroidJavaScriptChannelParams
    // 0x971e50: ldur            x0, [fp, #-0x18]
    // 0x971e54: LoadField: r3 = r0->field_13
    //     0x971e54: ldur            w3, [x0, #0x13]
    // 0x971e58: DecompressPointer r3
    //     0x971e58: add             x3, x3, HEAP, lsl #32
    // 0x971e5c: ldur            x4, [fp, #-8]
    // 0x971e60: stur            x3, [fp, #-0x20]
    // 0x971e64: LoadField: r5 = r4->field_7
    //     0x971e64: ldur            w5, [x4, #7]
    // 0x971e68: DecompressPointer r5
    //     0x971e68: add             x5, x5, HEAP, lsl #32
    // 0x971e6c: mov             x1, x3
    // 0x971e70: mov             x2, x5
    // 0x971e74: stur            x5, [fp, #-0x10]
    // 0x971e78: r0 = containsKey()
    //     0x971e78: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x971e7c: tbnz            w0, #4, #0x971eb0
    // 0x971e80: ldur            x1, [fp, #-0x18]
    // 0x971e84: LoadField: r0 = r1->field_b
    //     0x971e84: ldur            w0, [x1, #0xb]
    // 0x971e88: DecompressPointer r0
    //     0x971e88: add             x0, x0, HEAP, lsl #32
    // 0x971e8c: r16 = Sentinel
    //     0x971e8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x971e90: cmp             w0, w16
    // 0x971e94: b.ne            #0x971ea4
    // 0x971e98: r2 = _webView
    //     0x971e98: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x971e9c: ldr             x2, [x2, #0xf30]
    // 0x971ea0: r0 = InitLateFinalInstanceField()
    //     0x971ea0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x971ea4: mov             x1, x0
    // 0x971ea8: ldur            x2, [fp, #-0x10]
    // 0x971eac: r0 = removeJavaScriptChannel()
    //     0x971eac: bl              #0x9721e8  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::removeJavaScriptChannel
    // 0x971eb0: ldur            x0, [fp, #-8]
    // 0x971eb4: ldur            x1, [fp, #-0x20]
    // 0x971eb8: ldur            x2, [fp, #-0x10]
    // 0x971ebc: mov             x3, x0
    // 0x971ec0: r0 = []=()
    //     0x971ec0: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x971ec4: ldur            x1, [fp, #-0x18]
    // 0x971ec8: LoadField: r0 = r1->field_b
    //     0x971ec8: ldur            w0, [x1, #0xb]
    // 0x971ecc: DecompressPointer r0
    //     0x971ecc: add             x0, x0, HEAP, lsl #32
    // 0x971ed0: r16 = Sentinel
    //     0x971ed0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x971ed4: cmp             w0, w16
    // 0x971ed8: b.ne            #0x971ee8
    // 0x971edc: r2 = _webView
    //     0x971edc: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x971ee0: ldr             x2, [x2, #0xf30]
    // 0x971ee4: r0 = InitLateFinalInstanceField()
    //     0x971ee4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x971ee8: mov             x1, x0
    // 0x971eec: ldur            x0, [fp, #-8]
    // 0x971ef0: LoadField: r2 = r0->field_f
    //     0x971ef0: ldur            w2, [x0, #0xf]
    // 0x971ef4: DecompressPointer r2
    //     0x971ef4: add             x2, x2, HEAP, lsl #32
    // 0x971ef8: r0 = addJavaScriptChannel()
    //     0x971ef8: bl              #0x971f30  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::addJavaScriptChannel
    // 0x971efc: LeaveFrame
    //     0x971efc: mov             SP, fp
    //     0x971f00: ldp             fp, lr, [SP], #0x10
    // 0x971f04: ret
    //     0x971f04: ret             
    // 0x971f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x971f08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x971f0c: b               #0x971e20
  }
  _ setUserAgent(/* No info */) {
    // ** addr: 0x972dc8, size: 0x74
    // 0x972dc8: EnterFrame
    //     0x972dc8: stp             fp, lr, [SP, #-0x10]!
    //     0x972dcc: mov             fp, SP
    // 0x972dd0: CheckStackOverflow
    //     0x972dd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x972dd4: cmp             SP, x16
    //     0x972dd8: b.ls            #0x972e34
    // 0x972ddc: LoadField: r0 = r1->field_b
    //     0x972ddc: ldur            w0, [x1, #0xb]
    // 0x972de0: DecompressPointer r0
    //     0x972de0: add             x0, x0, HEAP, lsl #32
    // 0x972de4: r16 = Sentinel
    //     0x972de4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x972de8: cmp             w0, w16
    // 0x972dec: b.ne            #0x972dfc
    // 0x972df0: r2 = _webView
    //     0x972df0: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x972df4: ldr             x2, [x2, #0xf30]
    // 0x972df8: r0 = InitLateFinalInstanceField()
    //     0x972df8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x972dfc: mov             x1, x0
    // 0x972e00: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x972e00: ldur            w0, [x1, #0x17]
    // 0x972e04: DecompressPointer r0
    //     0x972e04: add             x0, x0, HEAP, lsl #32
    // 0x972e08: r16 = Sentinel
    //     0x972e08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x972e0c: cmp             w0, w16
    // 0x972e10: b.ne            #0x972e20
    // 0x972e14: r2 = settings
    //     0x972e14: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x972e18: ldr             x2, [x2, #0x110]
    // 0x972e1c: r0 = InitLateFinalInstanceField()
    //     0x972e1c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x972e20: mov             x1, x0
    // 0x972e24: r0 = setUserAgentString()
    //     0x972e24: bl              #0x972e3c  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setUserAgentString
    // 0x972e28: LeaveFrame
    //     0x972e28: mov             SP, fp
    //     0x972e2c: ldp             fp, lr, [SP], #0x10
    // 0x972e30: ret
    //     0x972e30: ret             
    // 0x972e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x972e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x972e38: b               #0x972ddc
  }
  _ setPlatformNavigationDelegate(/* No info */) async {
    // ** addr: 0x973130, size: 0x180
    // 0x973130: EnterFrame
    //     0x973130: stp             fp, lr, [SP, #-0x10]!
    //     0x973134: mov             fp, SP
    // 0x973138: AllocStack(0x38)
    //     0x973138: sub             SP, SP, #0x38
    // 0x97313c: SetupParameters(AndroidWebViewController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x97313c: stur            NULL, [fp, #-8]
    //     0x973140: stur            x1, [fp, #-0x10]
    //     0x973144: mov             x16, x2
    //     0x973148: mov             x2, x1
    //     0x97314c: mov             x1, x16
    //     0x973150: stur            x1, [fp, #-0x18]
    // 0x973154: CheckStackOverflow
    //     0x973154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x973158: cmp             SP, x16
    //     0x97315c: b.ls            #0x973290
    // 0x973160: InitAsync() -> Future<void?>
    //     0x973160: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x973164: bl              #0x661298  ; InitAsyncStub
    // 0x973168: ldur            x0, [fp, #-0x18]
    // 0x97316c: ldur            x3, [fp, #-0x10]
    // 0x973170: ArrayStore: r3[0] = r0  ; List_4
    //     0x973170: stur            w0, [x3, #0x17]
    //     0x973174: ldurb           w16, [x3, #-1]
    //     0x973178: ldurb           w17, [x0, #-1]
    //     0x97317c: and             x16, x17, x16, lsr #2
    //     0x973180: tst             x16, HEAP, lsr #32
    //     0x973184: b.eq            #0x97318c
    //     0x973188: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x97318c: mov             x2, x3
    // 0x973190: r1 = Function 'loadRequest':.
    //     0x973190: add             x1, PP, #0x49, lsl #12  ; [pp+0x492e8] AnonymousClosure: (0x97388c), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::loadRequest (0x9738c8)
    //     0x973194: ldr             x1, [x1, #0x2e8]
    // 0x973198: r0 = AllocateClosure()
    //     0x973198: bl              #0xec1630  ; AllocateClosureStub
    // 0x97319c: ldur            x1, [fp, #-0x18]
    // 0x9731a0: mov             x2, x0
    // 0x9731a4: r0 = setOnLoadRequest()
    //     0x9731a4: bl              #0x973820  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::setOnLoadRequest
    // 0x9731a8: ldur            x1, [fp, #-0x10]
    // 0x9731ac: stur            x0, [fp, #-0x20]
    // 0x9731b0: LoadField: r0 = r1->field_b
    //     0x9731b0: ldur            w0, [x1, #0xb]
    // 0x9731b4: DecompressPointer r0
    //     0x9731b4: add             x0, x0, HEAP, lsl #32
    // 0x9731b8: r16 = Sentinel
    //     0x9731b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9731bc: cmp             w0, w16
    // 0x9731c0: b.ne            #0x9731d0
    // 0x9731c4: r2 = _webView
    //     0x9731c4: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x9731c8: ldr             x2, [x2, #0xf30]
    // 0x9731cc: r0 = InitLateFinalInstanceField()
    //     0x9731cc: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9731d0: mov             x1, x0
    // 0x9731d4: ldur            x0, [fp, #-0x18]
    // 0x9731d8: LoadField: r2 = r0->field_b
    //     0x9731d8: ldur            w2, [x0, #0xb]
    // 0x9731dc: DecompressPointer r2
    //     0x9731dc: add             x2, x2, HEAP, lsl #32
    // 0x9731e0: r16 = Sentinel
    //     0x9731e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9731e4: cmp             w2, w16
    // 0x9731e8: b.eq            #0x973298
    // 0x9731ec: r0 = setWebViewClient()
    //     0x9731ec: bl              #0x973568  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::setWebViewClient
    // 0x9731f0: mov             x3, x0
    // 0x9731f4: ldur            x0, [fp, #-0x10]
    // 0x9731f8: stur            x3, [fp, #-0x28]
    // 0x9731fc: LoadField: r1 = r0->field_b
    //     0x9731fc: ldur            w1, [x0, #0xb]
    // 0x973200: DecompressPointer r1
    //     0x973200: add             x1, x1, HEAP, lsl #32
    // 0x973204: ldur            x0, [fp, #-0x18]
    // 0x973208: LoadField: r2 = r0->field_f
    //     0x973208: ldur            w2, [x0, #0xf]
    // 0x97320c: DecompressPointer r2
    //     0x97320c: add             x2, x2, HEAP, lsl #32
    // 0x973210: r16 = Sentinel
    //     0x973210: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x973214: cmp             w2, w16
    // 0x973218: b.eq            #0x9732a4
    // 0x97321c: r0 = setDownloadListener()
    //     0x97321c: bl              #0x9732b0  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::setDownloadListener
    // 0x973220: r1 = Null
    //     0x973220: mov             x1, NULL
    // 0x973224: r2 = 6
    //     0x973224: movz            x2, #0x6
    // 0x973228: stur            x0, [fp, #-0x10]
    // 0x97322c: r0 = AllocateArray()
    //     0x97322c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x973230: mov             x2, x0
    // 0x973234: ldur            x0, [fp, #-0x20]
    // 0x973238: stur            x2, [fp, #-0x18]
    // 0x97323c: StoreField: r2->field_f = r0
    //     0x97323c: stur            w0, [x2, #0xf]
    // 0x973240: ldur            x0, [fp, #-0x28]
    // 0x973244: StoreField: r2->field_13 = r0
    //     0x973244: stur            w0, [x2, #0x13]
    // 0x973248: ldur            x0, [fp, #-0x10]
    // 0x97324c: ArrayStore: r2[0] = r0  ; List_4
    //     0x97324c: stur            w0, [x2, #0x17]
    // 0x973250: r1 = <Future<void?>>
    //     0x973250: ldr             x1, [PP, #0x33f8]  ; [pp+0x33f8] TypeArguments: <Future<void?>>
    // 0x973254: r0 = AllocateGrowableArray()
    //     0x973254: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x973258: mov             x1, x0
    // 0x97325c: ldur            x0, [fp, #-0x18]
    // 0x973260: StoreField: r1->field_f = r0
    //     0x973260: stur            w0, [x1, #0xf]
    // 0x973264: r0 = 6
    //     0x973264: movz            x0, #0x6
    // 0x973268: StoreField: r1->field_b = r0
    //     0x973268: stur            w0, [x1, #0xb]
    // 0x97326c: r16 = <void?>
    //     0x97326c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x973270: stp             x1, x16, [SP]
    // 0x973274: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x973274: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x973278: r0 = wait()
    //     0x973278: bl              #0x678258  ; [dart:async] Future::wait
    // 0x97327c: mov             x1, x0
    // 0x973280: stur            x1, [fp, #-0x10]
    // 0x973284: r0 = Await()
    //     0x973284: bl              #0x661044  ; AwaitStub
    // 0x973288: r0 = Null
    //     0x973288: mov             x0, NULL
    // 0x97328c: r0 = ReturnAsyncNotFuture()
    //     0x97328c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x973290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x973290: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x973294: b               #0x973160
    // 0x973298: r9 = _webViewClient
    //     0x973298: add             x9, PP, #0x49, lsl #12  ; [pp+0x492f0] Field <AndroidNavigationDelegate._webViewClient@470193571>: late final (offset: 0xc)
    //     0x97329c: ldr             x9, [x9, #0x2f0]
    // 0x9732a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9732a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9732a4: r9 = _downloadListener
    //     0x9732a4: add             x9, PP, #0x49, lsl #12  ; [pp+0x492f8] Field <AndroidNavigationDelegate._downloadListener@470193571>: late final (offset: 0x10)
    //     0x9732a8: ldr             x9, [x9, #0x2f8]
    // 0x9732ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9732ac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> loadRequest(dynamic, LoadRequestParams) {
    // ** addr: 0x97388c, size: 0x3c
    // 0x97388c: EnterFrame
    //     0x97388c: stp             fp, lr, [SP, #-0x10]!
    //     0x973890: mov             fp, SP
    // 0x973894: ldr             x0, [fp, #0x18]
    // 0x973898: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x973898: ldur            w1, [x0, #0x17]
    // 0x97389c: DecompressPointer r1
    //     0x97389c: add             x1, x1, HEAP, lsl #32
    // 0x9738a0: CheckStackOverflow
    //     0x9738a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9738a4: cmp             SP, x16
    //     0x9738a8: b.ls            #0x9738c0
    // 0x9738ac: ldr             x2, [fp, #0x10]
    // 0x9738b0: r0 = loadRequest()
    //     0x9738b0: bl              #0x9738c8  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::loadRequest
    // 0x9738b4: LeaveFrame
    //     0x9738b4: mov             SP, fp
    //     0x9738b8: ldp             fp, lr, [SP], #0x10
    // 0x9738bc: ret
    //     0x9738bc: ret             
    // 0x9738c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9738c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9738c4: b               #0x9738ac
  }
  _ loadRequest(/* No info */) {
    // ** addr: 0x9738c8, size: 0x194
    // 0x9738c8: EnterFrame
    //     0x9738c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9738cc: mov             fp, SP
    // 0x9738d0: AllocStack(0x28)
    //     0x9738d0: sub             SP, SP, #0x28
    // 0x9738d4: SetupParameters(AndroidWebViewController this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x9738d4: mov             x3, x1
    //     0x9738d8: stur            x1, [fp, #-0x10]
    //     0x9738dc: stur            x2, [fp, #-0x18]
    // 0x9738e0: CheckStackOverflow
    //     0x9738e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9738e4: cmp             SP, x16
    //     0x9738e8: b.ls            #0x973a54
    // 0x9738ec: LoadField: r4 = r2->field_7
    //     0x9738ec: ldur            w4, [x2, #7]
    // 0x9738f0: DecompressPointer r4
    //     0x9738f0: add             x4, x4, HEAP, lsl #32
    // 0x9738f4: stur            x4, [fp, #-8]
    // 0x9738f8: r0 = LoadClassIdInstr(r4)
    //     0x9738f8: ldur            x0, [x4, #-1]
    //     0x9738fc: ubfx            x0, x0, #0xc, #0x14
    // 0x973900: mov             x1, x4
    // 0x973904: r0 = GDT[cid_x0 + -0xb71]()
    //     0x973904: sub             lr, x0, #0xb71
    //     0x973908: ldr             lr, [x21, lr, lsl #3]
    //     0x97390c: blr             lr
    // 0x973910: tbnz            w0, #4, #0x973a2c
    // 0x973914: ldur            x0, [fp, #-0x18]
    // 0x973918: LoadField: r1 = r0->field_b
    //     0x973918: ldur            w1, [x0, #0xb]
    // 0x97391c: DecompressPointer r1
    //     0x97391c: add             x1, x1, HEAP, lsl #32
    // 0x973920: LoadField: r2 = r1->field_7
    //     0x973920: ldur            x2, [x1, #7]
    // 0x973924: cmp             x2, #0
    // 0x973928: b.gt            #0x9739ac
    // 0x97392c: ldur            x2, [fp, #-8]
    // 0x973930: ldur            x1, [fp, #-0x10]
    // 0x973934: LoadField: r0 = r1->field_b
    //     0x973934: ldur            w0, [x1, #0xb]
    // 0x973938: DecompressPointer r0
    //     0x973938: add             x0, x0, HEAP, lsl #32
    // 0x97393c: r16 = Sentinel
    //     0x97393c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x973940: cmp             w0, w16
    // 0x973944: b.ne            #0x973954
    // 0x973948: r2 = _webView
    //     0x973948: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x97394c: ldr             x2, [x2, #0xf30]
    // 0x973950: r0 = InitLateFinalInstanceField()
    //     0x973950: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x973954: mov             x1, x0
    // 0x973958: ldur            x0, [fp, #-8]
    // 0x97395c: stur            x1, [fp, #-0x20]
    // 0x973960: r2 = LoadClassIdInstr(r0)
    //     0x973960: ldur            x2, [x0, #-1]
    //     0x973964: ubfx            x2, x2, #0xc, #0x14
    // 0x973968: str             x0, [SP]
    // 0x97396c: mov             x0, x2
    // 0x973970: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x973970: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x973974: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x973974: movz            x17, #0x2b03
    //     0x973978: add             lr, x0, x17
    //     0x97397c: ldr             lr, [x21, lr, lsl #3]
    //     0x973980: blr             lr
    // 0x973984: mov             x1, x0
    // 0x973988: ldur            x0, [fp, #-0x18]
    // 0x97398c: LoadField: r3 = r0->field_f
    //     0x97398c: ldur            w3, [x0, #0xf]
    // 0x973990: DecompressPointer r3
    //     0x973990: add             x3, x3, HEAP, lsl #32
    // 0x973994: mov             x2, x1
    // 0x973998: ldur            x1, [fp, #-0x20]
    // 0x97399c: r0 = loadUrl()
    //     0x97399c: bl              #0x973d20  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::loadUrl
    // 0x9739a0: LeaveFrame
    //     0x9739a0: mov             SP, fp
    //     0x9739a4: ldp             fp, lr, [SP], #0x10
    // 0x9739a8: ret
    //     0x9739a8: ret             
    // 0x9739ac: ldur            x0, [fp, #-8]
    // 0x9739b0: ldur            x1, [fp, #-0x10]
    // 0x9739b4: LoadField: r0 = r1->field_b
    //     0x9739b4: ldur            w0, [x1, #0xb]
    // 0x9739b8: DecompressPointer r0
    //     0x9739b8: add             x0, x0, HEAP, lsl #32
    // 0x9739bc: r16 = Sentinel
    //     0x9739bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9739c0: cmp             w0, w16
    // 0x9739c4: b.ne            #0x9739d4
    // 0x9739c8: r2 = _webView
    //     0x9739c8: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x9739cc: ldr             x2, [x2, #0xf30]
    // 0x9739d0: r0 = InitLateFinalInstanceField()
    //     0x9739d0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9739d4: mov             x1, x0
    // 0x9739d8: ldur            x0, [fp, #-8]
    // 0x9739dc: stur            x1, [fp, #-0x10]
    // 0x9739e0: r2 = LoadClassIdInstr(r0)
    //     0x9739e0: ldur            x2, [x0, #-1]
    //     0x9739e4: ubfx            x2, x2, #0xc, #0x14
    // 0x9739e8: str             x0, [SP]
    // 0x9739ec: mov             x0, x2
    // 0x9739f0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x9739f0: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x9739f4: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x9739f4: movz            x17, #0x2b03
    //     0x9739f8: add             lr, x0, x17
    //     0x9739fc: ldr             lr, [x21, lr, lsl #3]
    //     0x973a00: blr             lr
    // 0x973a04: r4 = 0
    //     0x973a04: movz            x4, #0
    // 0x973a08: stur            x0, [fp, #-8]
    // 0x973a0c: r0 = AllocateUint8Array()
    //     0x973a0c: bl              #0xec1fe0  ; AllocateUint8ArrayStub
    // 0x973a10: ldur            x1, [fp, #-0x10]
    // 0x973a14: ldur            x2, [fp, #-8]
    // 0x973a18: mov             x3, x0
    // 0x973a1c: r0 = postUrl()
    //     0x973a1c: bl              #0x973a5c  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::postUrl
    // 0x973a20: LeaveFrame
    //     0x973a20: mov             SP, fp
    //     0x973a24: ldp             fp, lr, [SP], #0x10
    // 0x973a28: ret
    //     0x973a28: ret             
    // 0x973a2c: r0 = ArgumentError()
    //     0x973a2c: bl              #0x66204c  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x973a30: mov             x1, x0
    // 0x973a34: r0 = "WebViewRequest#uri is required to have a scheme."
    //     0x973a34: add             x0, PP, #0x49, lsl #12  ; [pp+0x49300] "WebViewRequest#uri is required to have a scheme."
    //     0x973a38: ldr             x0, [x0, #0x300]
    // 0x973a3c: ArrayStore: r1[0] = r0  ; List_4
    //     0x973a3c: stur            w0, [x1, #0x17]
    // 0x973a40: r0 = false
    //     0x973a40: add             x0, NULL, #0x30  ; false
    // 0x973a44: StoreField: r1->field_b = r0
    //     0x973a44: stur            w0, [x1, #0xb]
    // 0x973a48: mov             x0, x1
    // 0x973a4c: r0 = Throw()
    //     0x973a4c: bl              #0xec04b8  ; ThrowStub
    // 0x973a50: brk             #0
    // 0x973a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x973a54: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x973a58: b               #0x9738ec
  }
  _ setJavaScriptMode(/* No info */) {
    // ** addr: 0x974024, size: 0x78
    // 0x974024: EnterFrame
    //     0x974024: stp             fp, lr, [SP, #-0x10]!
    //     0x974028: mov             fp, SP
    // 0x97402c: CheckStackOverflow
    //     0x97402c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x974030: cmp             SP, x16
    //     0x974034: b.ls            #0x974094
    // 0x974038: LoadField: r0 = r1->field_b
    //     0x974038: ldur            w0, [x1, #0xb]
    // 0x97403c: DecompressPointer r0
    //     0x97403c: add             x0, x0, HEAP, lsl #32
    // 0x974040: r16 = Sentinel
    //     0x974040: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974044: cmp             w0, w16
    // 0x974048: b.ne            #0x974058
    // 0x97404c: r2 = _webView
    //     0x97404c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x974050: ldr             x2, [x2, #0xf30]
    // 0x974054: r0 = InitLateFinalInstanceField()
    //     0x974054: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x974058: mov             x1, x0
    // 0x97405c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x97405c: ldur            w0, [x1, #0x17]
    // 0x974060: DecompressPointer r0
    //     0x974060: add             x0, x0, HEAP, lsl #32
    // 0x974064: r16 = Sentinel
    //     0x974064: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974068: cmp             w0, w16
    // 0x97406c: b.ne            #0x97407c
    // 0x974070: r2 = settings
    //     0x974070: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x974074: ldr             x2, [x2, #0x110]
    // 0x974078: r0 = InitLateFinalInstanceField()
    //     0x974078: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x97407c: mov             x1, x0
    // 0x974080: r2 = true
    //     0x974080: add             x2, NULL, #0x20  ; true
    // 0x974084: r0 = setJavaScriptEnabled()
    //     0x974084: bl              #0x97409c  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setJavaScriptEnabled
    // 0x974088: LeaveFrame
    //     0x974088: mov             SP, fp
    //     0x97408c: ldp             fp, lr, [SP], #0x10
    // 0x974090: ret
    //     0x974090: ret             
    // 0x974094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x974094: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x974098: b               #0x974038
  }
  _ AndroidWebViewController(/* No info */) {
    // ** addr: 0x974418, size: 0x2b4
    // 0x974418: EnterFrame
    //     0x974418: stp             fp, lr, [SP, #-0x10]!
    //     0x97441c: mov             fp, SP
    // 0x974420: AllocStack(0x20)
    //     0x974420: sub             SP, SP, #0x20
    // 0x974424: r0 = Sentinel
    //     0x974424: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974428: mov             x2, x1
    // 0x97442c: stur            x1, [fp, #-8]
    // 0x974430: CheckStackOverflow
    //     0x974430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x974434: cmp             SP, x16
    //     0x974438: b.ls            #0x9746c4
    // 0x97443c: StoreField: r2->field_b = r0
    //     0x97443c: stur            w0, [x2, #0xb]
    // 0x974440: StoreField: r2->field_f = r0
    //     0x974440: stur            w0, [x2, #0xf]
    // 0x974444: r16 = <String, AndroidJavaScriptChannelParams>
    //     0x974444: add             x16, PP, #0x49, lsl #12  ; [pp+0x49430] TypeArguments: <String, AndroidJavaScriptChannelParams>
    //     0x974448: ldr             x16, [x16, #0x430]
    // 0x97444c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x974450: stp             lr, x16, [SP]
    // 0x974454: r0 = Map._fromLiteral()
    //     0x974454: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x974458: ldur            x2, [fp, #-8]
    // 0x97445c: StoreField: r2->field_13 = r0
    //     0x97445c: stur            w0, [x2, #0x13]
    //     0x974460: ldurb           w16, [x2, #-1]
    //     0x974464: ldurb           w17, [x0, #-1]
    //     0x974468: and             x16, x17, x16, lsr #2
    //     0x97446c: tst             x16, HEAP, lsr #32
    //     0x974470: b.eq            #0x974478
    //     0x974474: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x974478: r1 = Null
    //     0x974478: mov             x1, NULL
    // 0x97447c: r0 = AndroidWebViewControllerCreationParams.fromPlatformWebViewControllerCreationParams()
    //     0x97447c: bl              #0x975c70  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewControllerCreationParams::AndroidWebViewControllerCreationParams.fromPlatformWebViewControllerCreationParams
    // 0x974480: ldur            x2, [fp, #-8]
    // 0x974484: StoreField: r2->field_7 = r0
    //     0x974484: stur            w0, [x2, #7]
    //     0x974488: ldurb           w16, [x2, #-1]
    //     0x97448c: ldurb           w17, [x0, #-1]
    //     0x974490: and             x16, x17, x16, lsr #2
    //     0x974494: tst             x16, HEAP, lsr #32
    //     0x974498: b.eq            #0x9744a0
    //     0x97449c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x9744a0: r0 = InitLateStaticField(0x9e8) // [package:webview_flutter_platform_interface/src/platform_webview_controller.dart] PlatformWebViewController::_token
    //     0x9744a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9744a4: ldr             x0, [x0, #0x13d0]
    //     0x9744a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9744ac: cmp             w0, w16
    //     0x9744b0: b.ne            #0x9744c0
    //     0x9744b4: add             x2, PP, #0x49, lsl #12  ; [pp+0x49428] Field <PlatformWebViewController._token@474166800>: static late final (offset: 0x9e8)
    //     0x9744b8: ldr             x2, [x2, #0x428]
    //     0x9744bc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9744c0: stur            x0, [fp, #-0x10]
    // 0x9744c4: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x9744c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9744c8: ldr             x0, [x0, #0xc08]
    //     0x9744cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9744d0: cmp             w0, w16
    //     0x9744d4: b.ne            #0x9744e0
    //     0x9744d8: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0x9744dc: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9744e0: mov             x1, x0
    // 0x9744e4: ldur            x2, [fp, #-8]
    // 0x9744e8: ldur            x3, [fp, #-0x10]
    // 0x9744ec: r0 = []=()
    //     0x9744ec: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0x9744f0: ldur            x1, [fp, #-8]
    // 0x9744f4: LoadField: r0 = r1->field_b
    //     0x9744f4: ldur            w0, [x1, #0xb]
    // 0x9744f8: DecompressPointer r0
    //     0x9744f8: add             x0, x0, HEAP, lsl #32
    // 0x9744fc: r16 = Sentinel
    //     0x9744fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974500: cmp             w0, w16
    // 0x974504: b.ne            #0x974514
    // 0x974508: r2 = _webView
    //     0x974508: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x97450c: ldr             x2, [x2, #0xf30]
    // 0x974510: r0 = InitLateFinalInstanceField()
    //     0x974510: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x974514: mov             x1, x0
    // 0x974518: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x974518: ldur            w0, [x1, #0x17]
    // 0x97451c: DecompressPointer r0
    //     0x97451c: add             x0, x0, HEAP, lsl #32
    // 0x974520: r16 = Sentinel
    //     0x974520: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974524: cmp             w0, w16
    // 0x974528: b.ne            #0x974538
    // 0x97452c: r2 = settings
    //     0x97452c: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x974530: ldr             x2, [x2, #0x110]
    // 0x974534: r0 = InitLateFinalInstanceField()
    //     0x974534: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x974538: mov             x1, x0
    // 0x97453c: r0 = setDomStorageEnabled()
    //     0x97453c: bl              #0x9759bc  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setDomStorageEnabled
    // 0x974540: ldur            x0, [fp, #-8]
    // 0x974544: LoadField: r1 = r0->field_b
    //     0x974544: ldur            w1, [x0, #0xb]
    // 0x974548: DecompressPointer r1
    //     0x974548: add             x1, x1, HEAP, lsl #32
    // 0x97454c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x97454c: ldur            w0, [x1, #0x17]
    // 0x974550: DecompressPointer r0
    //     0x974550: add             x0, x0, HEAP, lsl #32
    // 0x974554: r16 = Sentinel
    //     0x974554: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974558: cmp             w0, w16
    // 0x97455c: b.ne            #0x97456c
    // 0x974560: r2 = settings
    //     0x974560: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x974564: ldr             x2, [x2, #0x110]
    // 0x974568: r0 = InitLateFinalInstanceField()
    //     0x974568: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x97456c: mov             x1, x0
    // 0x974570: r0 = setJavaScriptCanOpenWindowsAutomatically()
    //     0x974570: bl              #0x975708  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setJavaScriptCanOpenWindowsAutomatically
    // 0x974574: ldur            x0, [fp, #-8]
    // 0x974578: LoadField: r1 = r0->field_b
    //     0x974578: ldur            w1, [x0, #0xb]
    // 0x97457c: DecompressPointer r1
    //     0x97457c: add             x1, x1, HEAP, lsl #32
    // 0x974580: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x974580: ldur            w0, [x1, #0x17]
    // 0x974584: DecompressPointer r0
    //     0x974584: add             x0, x0, HEAP, lsl #32
    // 0x974588: r16 = Sentinel
    //     0x974588: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97458c: cmp             w0, w16
    // 0x974590: b.ne            #0x9745a0
    // 0x974594: r2 = settings
    //     0x974594: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x974598: ldr             x2, [x2, #0x110]
    // 0x97459c: r0 = InitLateFinalInstanceField()
    //     0x97459c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9745a0: mov             x1, x0
    // 0x9745a4: r0 = setSupportMultipleWindows()
    //     0x9745a4: bl              #0x975454  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setSupportMultipleWindows
    // 0x9745a8: ldur            x0, [fp, #-8]
    // 0x9745ac: LoadField: r1 = r0->field_b
    //     0x9745ac: ldur            w1, [x0, #0xb]
    // 0x9745b0: DecompressPointer r1
    //     0x9745b0: add             x1, x1, HEAP, lsl #32
    // 0x9745b4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9745b4: ldur            w0, [x1, #0x17]
    // 0x9745b8: DecompressPointer r0
    //     0x9745b8: add             x0, x0, HEAP, lsl #32
    // 0x9745bc: r16 = Sentinel
    //     0x9745bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9745c0: cmp             w0, w16
    // 0x9745c4: b.ne            #0x9745d4
    // 0x9745c8: r2 = settings
    //     0x9745c8: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x9745cc: ldr             x2, [x2, #0x110]
    // 0x9745d0: r0 = InitLateFinalInstanceField()
    //     0x9745d0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9745d4: mov             x1, x0
    // 0x9745d8: r0 = setLoadWithOverviewMode()
    //     0x9745d8: bl              #0x9751a0  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setLoadWithOverviewMode
    // 0x9745dc: ldur            x0, [fp, #-8]
    // 0x9745e0: LoadField: r1 = r0->field_b
    //     0x9745e0: ldur            w1, [x0, #0xb]
    // 0x9745e4: DecompressPointer r1
    //     0x9745e4: add             x1, x1, HEAP, lsl #32
    // 0x9745e8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9745e8: ldur            w0, [x1, #0x17]
    // 0x9745ec: DecompressPointer r0
    //     0x9745ec: add             x0, x0, HEAP, lsl #32
    // 0x9745f0: r16 = Sentinel
    //     0x9745f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9745f4: cmp             w0, w16
    // 0x9745f8: b.ne            #0x974608
    // 0x9745fc: r2 = settings
    //     0x9745fc: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x974600: ldr             x2, [x2, #0x110]
    // 0x974604: r0 = InitLateFinalInstanceField()
    //     0x974604: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x974608: mov             x1, x0
    // 0x97460c: r0 = setUseWideViewPort()
    //     0x97460c: bl              #0x974eec  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setUseWideViewPort
    // 0x974610: ldur            x0, [fp, #-8]
    // 0x974614: LoadField: r1 = r0->field_b
    //     0x974614: ldur            w1, [x0, #0xb]
    // 0x974618: DecompressPointer r1
    //     0x974618: add             x1, x1, HEAP, lsl #32
    // 0x97461c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x97461c: ldur            w0, [x1, #0x17]
    // 0x974620: DecompressPointer r0
    //     0x974620: add             x0, x0, HEAP, lsl #32
    // 0x974624: r16 = Sentinel
    //     0x974624: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974628: cmp             w0, w16
    // 0x97462c: b.ne            #0x97463c
    // 0x974630: r2 = settings
    //     0x974630: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x974634: ldr             x2, [x2, #0x110]
    // 0x974638: r0 = InitLateFinalInstanceField()
    //     0x974638: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x97463c: mov             x1, x0
    // 0x974640: r0 = setDisplayZoomControls()
    //     0x974640: bl              #0x974c38  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setDisplayZoomControls
    // 0x974644: ldur            x0, [fp, #-8]
    // 0x974648: LoadField: r1 = r0->field_b
    //     0x974648: ldur            w1, [x0, #0xb]
    // 0x97464c: DecompressPointer r1
    //     0x97464c: add             x1, x1, HEAP, lsl #32
    // 0x974650: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x974650: ldur            w0, [x1, #0x17]
    // 0x974654: DecompressPointer r0
    //     0x974654: add             x0, x0, HEAP, lsl #32
    // 0x974658: r16 = Sentinel
    //     0x974658: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97465c: cmp             w0, w16
    // 0x974660: b.ne            #0x974670
    // 0x974664: r2 = settings
    //     0x974664: add             x2, PP, #0x49, lsl #12  ; [pp+0x49110] Field <WebView.settings>: late final (offset: 0x18)
    //     0x974668: ldr             x2, [x2, #0x110]
    // 0x97466c: r0 = InitLateFinalInstanceField()
    //     0x97466c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x974670: mov             x1, x0
    // 0x974674: r0 = setBuiltInZoomControls()
    //     0x974674: bl              #0x974984  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebSettings::setBuiltInZoomControls
    // 0x974678: ldur            x1, [fp, #-8]
    // 0x97467c: LoadField: r0 = r1->field_b
    //     0x97467c: ldur            w0, [x1, #0xb]
    // 0x974680: DecompressPointer r0
    //     0x974680: add             x0, x0, HEAP, lsl #32
    // 0x974684: stur            x0, [fp, #-0x10]
    // 0x974688: LoadField: r0 = r1->field_f
    //     0x974688: ldur            w0, [x1, #0xf]
    // 0x97468c: DecompressPointer r0
    //     0x97468c: add             x0, x0, HEAP, lsl #32
    // 0x974690: r16 = Sentinel
    //     0x974690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x974694: cmp             w0, w16
    // 0x974698: b.ne            #0x9746a8
    // 0x97469c: r2 = _webChromeClient
    //     0x97469c: add             x2, PP, #0x49, lsl #12  ; [pp+0x49438] Field <AndroidWebViewController._webChromeClient@470193571>: late final (offset: 0x10)
    //     0x9746a0: ldr             x2, [x2, #0x438]
    // 0x9746a4: r0 = InitLateFinalInstanceField()
    //     0x9746a4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x9746a8: ldur            x1, [fp, #-0x10]
    // 0x9746ac: mov             x2, x0
    // 0x9746b0: r0 = setWebChromeClient()
    //     0x9746b0: bl              #0x9746cc  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::setWebChromeClient
    // 0x9746b4: r0 = Null
    //     0x9746b4: mov             x0, NULL
    // 0x9746b8: LeaveFrame
    //     0x9746b8: mov             SP, fp
    //     0x9746bc: ldp             fp, lr, [SP], #0x10
    // 0x9746c0: ret
    //     0x9746c0: ret             
    // 0x9746c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9746c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9746c8: b               #0x97443c
  }
  WebChromeClient _webChromeClient(AndroidWebViewController) {
    // ** addr: 0x978d04, size: 0x2a0
    // 0x978d04: EnterFrame
    //     0x978d04: stp             fp, lr, [SP, #-0x10]!
    //     0x978d08: mov             fp, SP
    // 0x978d0c: AllocStack(0xc8)
    //     0x978d0c: sub             SP, SP, #0xc8
    // 0x978d10: CheckStackOverflow
    //     0x978d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x978d14: cmp             SP, x16
    //     0x978d18: b.ls            #0x978f9c
    // 0x978d1c: r1 = Function '<anonymous closure>':.
    //     0x978d1c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49440] AnonymousClosure: (0x979f4c), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978d20: ldr             x1, [x1, #0x440]
    // 0x978d24: r2 = Null
    //     0x978d24: mov             x2, NULL
    // 0x978d28: r0 = AllocateClosure()
    //     0x978d28: bl              #0xec1630  ; AllocateClosureStub
    // 0x978d2c: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, int) => void?>
    //     0x978d2c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49448] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, int) => void?>
    //     0x978d30: ldr             x16, [x16, #0x448]
    // 0x978d34: ldr             lr, [fp, #0x10]
    // 0x978d38: stp             lr, x16, [SP, #8]
    // 0x978d3c: str             x0, [SP]
    // 0x978d40: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978d40: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978d44: r0 = withWeakReferenceTo()
    //     0x978d44: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978d48: r1 = Function '<anonymous closure>':.
    //     0x978d48: add             x1, PP, #0x49, lsl #12  ; [pp+0x49450] AnonymousClosure: (0x979bc4), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978d4c: ldr             x1, [x1, #0x450]
    // 0x978d50: r2 = Null
    //     0x978d50: mov             x2, NULL
    // 0x978d54: stur            x0, [fp, #-8]
    // 0x978d58: r0 = AllocateClosure()
    //     0x978d58: bl              #0xec1630  ; AllocateClosureStub
    // 0x978d5c: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, String, GeolocationPermissionsCallback) => void?>
    //     0x978d5c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49458] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, String, GeolocationPermissionsCallback) => void?>
    //     0x978d60: ldr             x16, [x16, #0x458]
    // 0x978d64: ldr             lr, [fp, #0x10]
    // 0x978d68: stp             lr, x16, [SP, #8]
    // 0x978d6c: str             x0, [SP]
    // 0x978d70: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978d70: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978d74: r0 = withWeakReferenceTo()
    //     0x978d74: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978d78: r1 = Function '<anonymous closure>':.
    //     0x978d78: add             x1, PP, #0x49, lsl #12  ; [pp+0x49460] AnonymousClosure: (0x979b70), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978d7c: ldr             x1, [x1, #0x460]
    // 0x978d80: r2 = Null
    //     0x978d80: mov             x2, NULL
    // 0x978d84: stur            x0, [fp, #-0x10]
    // 0x978d88: r0 = AllocateClosure()
    //     0x978d88: bl              #0xec1630  ; AllocateClosureStub
    // 0x978d8c: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient) => void?>
    //     0x978d8c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49468] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient) => void?>
    //     0x978d90: ldr             x16, [x16, #0x468]
    // 0x978d94: ldr             lr, [fp, #0x10]
    // 0x978d98: stp             lr, x16, [SP, #8]
    // 0x978d9c: str             x0, [SP]
    // 0x978da0: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978da0: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978da4: r0 = withWeakReferenceTo()
    //     0x978da4: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978da8: r1 = Function '<anonymous closure>':.
    //     0x978da8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49470] AnonymousClosure: (0x9796fc), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978dac: ldr             x1, [x1, #0x470]
    // 0x978db0: r2 = Null
    //     0x978db0: mov             x2, NULL
    // 0x978db4: stur            x0, [fp, #-0x18]
    // 0x978db8: r0 = AllocateClosure()
    //     0x978db8: bl              #0xec1630  ; AllocateClosureStub
    // 0x978dbc: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, View, CustomViewCallback) => void?>
    //     0x978dbc: add             x16, PP, #0x49, lsl #12  ; [pp+0x49478] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, View, CustomViewCallback) => void?>
    //     0x978dc0: ldr             x16, [x16, #0x478]
    // 0x978dc4: ldr             lr, [fp, #0x10]
    // 0x978dc8: stp             lr, x16, [SP, #8]
    // 0x978dcc: str             x0, [SP]
    // 0x978dd0: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978dd0: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978dd4: r0 = withWeakReferenceTo()
    //     0x978dd4: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978dd8: r1 = Function '<anonymous closure>':.
    //     0x978dd8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49480] AnonymousClosure: (0x97962c), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978ddc: ldr             x1, [x1, #0x480]
    // 0x978de0: r2 = Null
    //     0x978de0: mov             x2, NULL
    // 0x978de4: stur            x0, [fp, #-0x20]
    // 0x978de8: r0 = AllocateClosure()
    //     0x978de8: bl              #0xec1630  ; AllocateClosureStub
    // 0x978dec: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient) => void?>
    //     0x978dec: add             x16, PP, #0x49, lsl #12  ; [pp+0x49468] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient) => void?>
    //     0x978df0: ldr             x16, [x16, #0x468]
    // 0x978df4: ldr             lr, [fp, #0x10]
    // 0x978df8: stp             lr, x16, [SP, #8]
    // 0x978dfc: str             x0, [SP]
    // 0x978e00: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978e00: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978e04: r0 = withWeakReferenceTo()
    //     0x978e04: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978e08: r1 = Function '<anonymous closure>':.
    //     0x978e08: add             x1, PP, #0x49, lsl #12  ; [pp+0x49488] AnonymousClosure: (0x979580), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978e0c: ldr             x1, [x1, #0x488]
    // 0x978e10: r2 = Null
    //     0x978e10: mov             x2, NULL
    // 0x978e14: stur            x0, [fp, #-0x28]
    // 0x978e18: r0 = AllocateClosure()
    //     0x978e18: bl              #0xec1630  ; AllocateClosureStub
    // 0x978e1c: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, FileChooserParams) => Future<List<String>>>
    //     0x978e1c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49490] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, FileChooserParams) => Future<List<String>>>
    //     0x978e20: ldr             x16, [x16, #0x490]
    // 0x978e24: ldr             lr, [fp, #0x10]
    // 0x978e28: stp             lr, x16, [SP, #8]
    // 0x978e2c: str             x0, [SP]
    // 0x978e30: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978e30: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978e34: r0 = withWeakReferenceTo()
    //     0x978e34: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978e38: r1 = Function '<anonymous closure>':.
    //     0x978e38: add             x1, PP, #0x49, lsl #12  ; [pp+0x49498] AnonymousClosure: (0x9794e0), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978e3c: ldr             x1, [x1, #0x498]
    // 0x978e40: r2 = Null
    //     0x978e40: mov             x2, NULL
    // 0x978e44: stur            x0, [fp, #-0x30]
    // 0x978e48: r0 = AllocateClosure()
    //     0x978e48: bl              #0xec1630  ; AllocateClosureStub
    // 0x978e4c: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, ConsoleMessage) => void?>
    //     0x978e4c: add             x16, PP, #0x49, lsl #12  ; [pp+0x494a0] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, ConsoleMessage) => void?>
    //     0x978e50: ldr             x16, [x16, #0x4a0]
    // 0x978e54: ldr             lr, [fp, #0x10]
    // 0x978e58: stp             lr, x16, [SP, #8]
    // 0x978e5c: str             x0, [SP]
    // 0x978e60: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978e60: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978e64: r0 = withWeakReferenceTo()
    //     0x978e64: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978e68: r1 = Function '<anonymous closure>':.
    //     0x978e68: add             x1, PP, #0x49, lsl #12  ; [pp+0x494a8] AnonymousClosure: (0x979184), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978e6c: ldr             x1, [x1, #0x4a8]
    // 0x978e70: r2 = Null
    //     0x978e70: mov             x2, NULL
    // 0x978e74: stur            x0, [fp, #-0x38]
    // 0x978e78: r0 = AllocateClosure()
    //     0x978e78: bl              #0xec1630  ; AllocateClosureStub
    // 0x978e7c: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, PermissionRequest) => void?>
    //     0x978e7c: add             x16, PP, #0x49, lsl #12  ; [pp+0x494b0] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, PermissionRequest) => void?>
    //     0x978e80: ldr             x16, [x16, #0x4b0]
    // 0x978e84: ldr             lr, [fp, #0x10]
    // 0x978e88: stp             lr, x16, [SP, #8]
    // 0x978e8c: str             x0, [SP]
    // 0x978e90: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978e90: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978e94: r0 = withWeakReferenceTo()
    //     0x978e94: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978e98: r1 = Function '<anonymous closure>':.
    //     0x978e98: add             x1, PP, #0x49, lsl #12  ; [pp+0x494b8] AnonymousClosure: (0x9790e4), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978e9c: ldr             x1, [x1, #0x4b8]
    // 0x978ea0: r2 = Null
    //     0x978ea0: mov             x2, NULL
    // 0x978ea4: stur            x0, [fp, #-0x40]
    // 0x978ea8: r0 = AllocateClosure()
    //     0x978ea8: bl              #0xec1630  ; AllocateClosureStub
    // 0x978eac: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, String, String) => Future<void?>>
    //     0x978eac: add             x16, PP, #0x49, lsl #12  ; [pp+0x494c0] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, String, String) => Future<void?>>
    //     0x978eb0: ldr             x16, [x16, #0x4c0]
    // 0x978eb4: ldr             lr, [fp, #0x10]
    // 0x978eb8: stp             lr, x16, [SP, #8]
    // 0x978ebc: str             x0, [SP]
    // 0x978ec0: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978ec0: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978ec4: r0 = withWeakReferenceTo()
    //     0x978ec4: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978ec8: r1 = Function '<anonymous closure>':.
    //     0x978ec8: add             x1, PP, #0x49, lsl #12  ; [pp+0x494c8] AnonymousClosure: (0x979044), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978ecc: ldr             x1, [x1, #0x4c8]
    // 0x978ed0: r2 = Null
    //     0x978ed0: mov             x2, NULL
    // 0x978ed4: stur            x0, [fp, #-0x48]
    // 0x978ed8: r0 = AllocateClosure()
    //     0x978ed8: bl              #0xec1630  ; AllocateClosureStub
    // 0x978edc: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, String, String) => Future<bool>>
    //     0x978edc: add             x16, PP, #0x49, lsl #12  ; [pp+0x494d0] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, String, String) => Future<bool>>
    //     0x978ee0: ldr             x16, [x16, #0x4d0]
    // 0x978ee4: ldr             lr, [fp, #0x10]
    // 0x978ee8: stp             lr, x16, [SP, #8]
    // 0x978eec: str             x0, [SP]
    // 0x978ef0: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978ef0: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978ef4: r0 = withWeakReferenceTo()
    //     0x978ef4: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978ef8: r1 = Function '<anonymous closure>':.
    //     0x978ef8: add             x1, PP, #0x49, lsl #12  ; [pp+0x494d8] AnonymousClosure: (0x978fa4), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978efc: ldr             x1, [x1, #0x4d8]
    // 0x978f00: r2 = Null
    //     0x978f00: mov             x2, NULL
    // 0x978f04: stur            x0, [fp, #-0x50]
    // 0x978f08: r0 = AllocateClosure()
    //     0x978f08: bl              #0xec1630  ; AllocateClosureStub
    // 0x978f0c: r16 = <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, String, String, String) => Future<String?>>
    //     0x978f0c: add             x16, PP, #0x49, lsl #12  ; [pp+0x494e0] TypeArguments: <AndroidWebViewController, (dynamic this, WebChromeClient, WebView, String, String, String) => Future<String?>>
    //     0x978f10: ldr             x16, [x16, #0x4e0]
    // 0x978f14: ldr             lr, [fp, #0x10]
    // 0x978f18: stp             lr, x16, [SP, #8]
    // 0x978f1c: str             x0, [SP]
    // 0x978f20: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x978f20: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x978f24: r0 = withWeakReferenceTo()
    //     0x978f24: bl              #0x970890  ; [package:webview_flutter_android/src/weak_reference_utils.dart] ::withWeakReferenceTo
    // 0x978f28: stur            x0, [fp, #-0x58]
    // 0x978f2c: r0 = WebChromeClient()
    //     0x978f2c: bl              #0x92a888  ; AllocateWebChromeClientStub -> WebChromeClient (size=0x40)
    // 0x978f30: stur            x0, [fp, #-0x60]
    // 0x978f34: stp             NULL, NULL, [SP, #0x58]
    // 0x978f38: ldur            x16, [fp, #-8]
    // 0x978f3c: ldur            lr, [fp, #-0x30]
    // 0x978f40: stp             lr, x16, [SP, #0x48]
    // 0x978f44: ldur            x16, [fp, #-0x40]
    // 0x978f48: ldur            lr, [fp, #-0x20]
    // 0x978f4c: stp             lr, x16, [SP, #0x38]
    // 0x978f50: ldur            x16, [fp, #-0x28]
    // 0x978f54: ldur            lr, [fp, #-0x10]
    // 0x978f58: stp             lr, x16, [SP, #0x28]
    // 0x978f5c: ldur            x16, [fp, #-0x18]
    // 0x978f60: ldur            lr, [fp, #-0x38]
    // 0x978f64: stp             lr, x16, [SP, #0x18]
    // 0x978f68: ldur            x16, [fp, #-0x48]
    // 0x978f6c: ldur            lr, [fp, #-0x50]
    // 0x978f70: stp             lr, x16, [SP, #8]
    // 0x978f74: ldur            x16, [fp, #-0x58]
    // 0x978f78: str             x16, [SP]
    // 0x978f7c: mov             x1, x0
    // 0x978f80: r4 = const [0, 0xe, 0xd, 0x1, onConsoleMessage, 0xa, onGeolocationPermissionsHidePrompt, 0x9, onGeolocationPermissionsShowPrompt, 0x8, onHideCustomView, 0x7, onJsAlert, 0xb, onJsConfirm, 0xc, onJsPrompt, 0xd, onPermissionRequest, 0x5, onProgressChanged, 0x3, onShowCustomView, 0x6, onShowFileChooser, 0x4, pigeon_binaryMessenger, 0x1, pigeon_instanceManager, 0x2, null]
    //     0x978f80: add             x4, PP, #0x49, lsl #12  ; [pp+0x494e8] List(31) [0, 0xe, 0xd, 0x1, "onConsoleMessage", 0xa, "onGeolocationPermissionsHidePrompt", 0x9, "onGeolocationPermissionsShowPrompt", 0x8, "onHideCustomView", 0x7, "onJsAlert", 0xb, "onJsConfirm", 0xc, "onJsPrompt", 0xd, "onPermissionRequest", 0x5, "onProgressChanged", 0x3, "onShowCustomView", 0x6, "onShowFileChooser", 0x4, "pigeon_binaryMessenger", 0x1, "pigeon_instanceManager", 0x2, Null]
    //     0x978f84: ldr             x4, [x4, #0x4e8]
    // 0x978f88: r0 = WebChromeClient()
    //     0x978f88: bl              #0x976b04  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebChromeClient::WebChromeClient
    // 0x978f8c: ldur            x0, [fp, #-0x60]
    // 0x978f90: LeaveFrame
    //     0x978f90: mov             SP, fp
    //     0x978f94: ldp             fp, lr, [SP], #0x10
    // 0x978f98: ret
    //     0x978f98: ret             
    // 0x978f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x978f9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x978fa0: b               #0x978d1c
  }
  [closure] (dynamic, WebChromeClient, WebView, String, String, String) => Future<String> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x978fa4, size: 0x54
    // 0x978fa4: EnterFrame
    //     0x978fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x978fa8: mov             fp, SP
    // 0x978fac: AllocStack(0x8)
    //     0x978fac: sub             SP, SP, #8
    // 0x978fb0: SetupParameters()
    //     0x978fb0: ldr             x0, [fp, #0x18]
    //     0x978fb4: ldur            w1, [x0, #0x17]
    //     0x978fb8: add             x1, x1, HEAP, lsl #32
    //     0x978fbc: stur            x1, [fp, #-8]
    // 0x978fc0: r1 = 1
    //     0x978fc0: movz            x1, #0x1
    // 0x978fc4: r0 = AllocateContext()
    //     0x978fc4: bl              #0xec126c  ; AllocateContextStub
    // 0x978fc8: mov             x1, x0
    // 0x978fcc: ldur            x0, [fp, #-8]
    // 0x978fd0: StoreField: r1->field_b = r0
    //     0x978fd0: stur            w0, [x1, #0xb]
    // 0x978fd4: ldr             x0, [fp, #0x10]
    // 0x978fd8: StoreField: r1->field_f = r0
    //     0x978fd8: stur            w0, [x1, #0xf]
    // 0x978fdc: mov             x2, x1
    // 0x978fe0: r1 = Function '<anonymous closure>':.
    //     0x978fe0: add             x1, PP, #0x49, lsl #12  ; [pp+0x494f0] AnonymousClosure: (0x978ff8), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x978fe4: ldr             x1, [x1, #0x4f0]
    // 0x978fe8: r0 = AllocateClosure()
    //     0x978fe8: bl              #0xec1630  ; AllocateClosureStub
    // 0x978fec: LeaveFrame
    //     0x978fec: mov             SP, fp
    //     0x978ff0: ldp             fp, lr, [SP], #0x10
    // 0x978ff4: ret
    //     0x978ff4: ret             
  }
  [closure] Future<String> <anonymous closure>(dynamic, WebChromeClient, WebView, String, String, String) async {
    // ** addr: 0x978ff8, size: 0x4c
    // 0x978ff8: EnterFrame
    //     0x978ff8: stp             fp, lr, [SP, #-0x10]!
    //     0x978ffc: mov             fp, SP
    // 0x979000: AllocStack(0x10)
    //     0x979000: sub             SP, SP, #0x10
    // 0x979004: SetupParameters(AndroidWebViewController this /* r1 */)
    //     0x979004: stur            NULL, [fp, #-8]
    //     0x979008: movz            x0, #0
    //     0x97900c: add             x1, fp, w0, sxtw #2
    //     0x979010: ldr             x1, [x1, #0x38]
    //     0x979014: ldur            w2, [x1, #0x17]
    //     0x979018: add             x2, x2, HEAP, lsl #32
    //     0x97901c: stur            x2, [fp, #-0x10]
    // 0x979020: CheckStackOverflow
    //     0x979020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979024: cmp             SP, x16
    //     0x979028: b.ls            #0x97903c
    // 0x97902c: InitAsync() -> Future<String>
    //     0x97902c: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0x979030: bl              #0x661298  ; InitAsyncStub
    // 0x979034: r0 = ""
    //     0x979034: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x979038: r0 = ReturnAsyncNotFuture()
    //     0x979038: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97903c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97903c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x979040: b               #0x97902c
  }
  [closure] (dynamic, WebChromeClient, WebView, String, String) => Future<bool> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x979044, size: 0x54
    // 0x979044: EnterFrame
    //     0x979044: stp             fp, lr, [SP, #-0x10]!
    //     0x979048: mov             fp, SP
    // 0x97904c: AllocStack(0x8)
    //     0x97904c: sub             SP, SP, #8
    // 0x979050: SetupParameters()
    //     0x979050: ldr             x0, [fp, #0x18]
    //     0x979054: ldur            w1, [x0, #0x17]
    //     0x979058: add             x1, x1, HEAP, lsl #32
    //     0x97905c: stur            x1, [fp, #-8]
    // 0x979060: r1 = 1
    //     0x979060: movz            x1, #0x1
    // 0x979064: r0 = AllocateContext()
    //     0x979064: bl              #0xec126c  ; AllocateContextStub
    // 0x979068: mov             x1, x0
    // 0x97906c: ldur            x0, [fp, #-8]
    // 0x979070: StoreField: r1->field_b = r0
    //     0x979070: stur            w0, [x1, #0xb]
    // 0x979074: ldr             x0, [fp, #0x10]
    // 0x979078: StoreField: r1->field_f = r0
    //     0x979078: stur            w0, [x1, #0xf]
    // 0x97907c: mov             x2, x1
    // 0x979080: r1 = Function '<anonymous closure>':.
    //     0x979080: add             x1, PP, #0x49, lsl #12  ; [pp+0x494f8] AnonymousClosure: (0x979098), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x979084: ldr             x1, [x1, #0x4f8]
    // 0x979088: r0 = AllocateClosure()
    //     0x979088: bl              #0xec1630  ; AllocateClosureStub
    // 0x97908c: LeaveFrame
    //     0x97908c: mov             SP, fp
    //     0x979090: ldp             fp, lr, [SP], #0x10
    // 0x979094: ret
    //     0x979094: ret             
  }
  [closure] Future<bool> <anonymous closure>(dynamic, WebChromeClient, WebView, String, String) async {
    // ** addr: 0x979098, size: 0x4c
    // 0x979098: EnterFrame
    //     0x979098: stp             fp, lr, [SP, #-0x10]!
    //     0x97909c: mov             fp, SP
    // 0x9790a0: AllocStack(0x10)
    //     0x9790a0: sub             SP, SP, #0x10
    // 0x9790a4: SetupParameters(AndroidWebViewController this /* r1 */)
    //     0x9790a4: stur            NULL, [fp, #-8]
    //     0x9790a8: movz            x0, #0
    //     0x9790ac: add             x1, fp, w0, sxtw #2
    //     0x9790b0: ldr             x1, [x1, #0x30]
    //     0x9790b4: ldur            w2, [x1, #0x17]
    //     0x9790b8: add             x2, x2, HEAP, lsl #32
    //     0x9790bc: stur            x2, [fp, #-0x10]
    // 0x9790c0: CheckStackOverflow
    //     0x9790c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9790c4: cmp             SP, x16
    //     0x9790c8: b.ls            #0x9790dc
    // 0x9790cc: InitAsync() -> Future<bool>
    //     0x9790cc: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x9790d0: bl              #0x661298  ; InitAsyncStub
    // 0x9790d4: r0 = false
    //     0x9790d4: add             x0, NULL, #0x30  ; false
    // 0x9790d8: r0 = ReturnAsyncNotFuture()
    //     0x9790d8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x9790dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9790dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9790e0: b               #0x9790cc
  }
  [closure] (dynamic, WebChromeClient, WebView, String, String) => Future<void> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x9790e4, size: 0x54
    // 0x9790e4: EnterFrame
    //     0x9790e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9790e8: mov             fp, SP
    // 0x9790ec: AllocStack(0x8)
    //     0x9790ec: sub             SP, SP, #8
    // 0x9790f0: SetupParameters()
    //     0x9790f0: ldr             x0, [fp, #0x18]
    //     0x9790f4: ldur            w1, [x0, #0x17]
    //     0x9790f8: add             x1, x1, HEAP, lsl #32
    //     0x9790fc: stur            x1, [fp, #-8]
    // 0x979100: r1 = 1
    //     0x979100: movz            x1, #0x1
    // 0x979104: r0 = AllocateContext()
    //     0x979104: bl              #0xec126c  ; AllocateContextStub
    // 0x979108: mov             x1, x0
    // 0x97910c: ldur            x0, [fp, #-8]
    // 0x979110: StoreField: r1->field_b = r0
    //     0x979110: stur            w0, [x1, #0xb]
    // 0x979114: ldr             x0, [fp, #0x10]
    // 0x979118: StoreField: r1->field_f = r0
    //     0x979118: stur            w0, [x1, #0xf]
    // 0x97911c: mov             x2, x1
    // 0x979120: r1 = Function '<anonymous closure>':.
    //     0x979120: add             x1, PP, #0x49, lsl #12  ; [pp+0x49500] AnonymousClosure: (0x979138), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x979124: ldr             x1, [x1, #0x500]
    // 0x979128: r0 = AllocateClosure()
    //     0x979128: bl              #0xec1630  ; AllocateClosureStub
    // 0x97912c: LeaveFrame
    //     0x97912c: mov             SP, fp
    //     0x979130: ldp             fp, lr, [SP], #0x10
    // 0x979134: ret
    //     0x979134: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic, WebChromeClient, WebView, String, String) async {
    // ** addr: 0x979138, size: 0x4c
    // 0x979138: EnterFrame
    //     0x979138: stp             fp, lr, [SP, #-0x10]!
    //     0x97913c: mov             fp, SP
    // 0x979140: AllocStack(0x10)
    //     0x979140: sub             SP, SP, #0x10
    // 0x979144: SetupParameters(AndroidWebViewController this /* r1 */)
    //     0x979144: stur            NULL, [fp, #-8]
    //     0x979148: movz            x0, #0
    //     0x97914c: add             x1, fp, w0, sxtw #2
    //     0x979150: ldr             x1, [x1, #0x30]
    //     0x979154: ldur            w2, [x1, #0x17]
    //     0x979158: add             x2, x2, HEAP, lsl #32
    //     0x97915c: stur            x2, [fp, #-0x10]
    // 0x979160: CheckStackOverflow
    //     0x979160: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979164: cmp             SP, x16
    //     0x979168: b.ls            #0x97917c
    // 0x97916c: InitAsync() -> Future<void?>
    //     0x97916c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x979170: bl              #0x661298  ; InitAsyncStub
    // 0x979174: r0 = Null
    //     0x979174: mov             x0, NULL
    // 0x979178: r0 = ReturnAsyncNotFuture()
    //     0x979178: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97917c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97917c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x979180: b               #0x97916c
  }
  [closure] (dynamic, WebChromeClient, PermissionRequest) => Future<void> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x979184, size: 0x54
    // 0x979184: EnterFrame
    //     0x979184: stp             fp, lr, [SP, #-0x10]!
    //     0x979188: mov             fp, SP
    // 0x97918c: AllocStack(0x8)
    //     0x97918c: sub             SP, SP, #8
    // 0x979190: SetupParameters()
    //     0x979190: ldr             x0, [fp, #0x18]
    //     0x979194: ldur            w1, [x0, #0x17]
    //     0x979198: add             x1, x1, HEAP, lsl #32
    //     0x97919c: stur            x1, [fp, #-8]
    // 0x9791a0: r1 = 1
    //     0x9791a0: movz            x1, #0x1
    // 0x9791a4: r0 = AllocateContext()
    //     0x9791a4: bl              #0xec126c  ; AllocateContextStub
    // 0x9791a8: mov             x1, x0
    // 0x9791ac: ldur            x0, [fp, #-8]
    // 0x9791b0: StoreField: r1->field_b = r0
    //     0x9791b0: stur            w0, [x1, #0xb]
    // 0x9791b4: ldr             x0, [fp, #0x10]
    // 0x9791b8: StoreField: r1->field_f = r0
    //     0x9791b8: stur            w0, [x1, #0xf]
    // 0x9791bc: mov             x2, x1
    // 0x9791c0: r1 = Function '<anonymous closure>':.
    //     0x9791c0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49508] AnonymousClosure: (0x9791d8), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x9791c4: ldr             x1, [x1, #0x508]
    // 0x9791c8: r0 = AllocateClosure()
    //     0x9791c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x9791cc: LeaveFrame
    //     0x9791cc: mov             SP, fp
    //     0x9791d0: ldp             fp, lr, [SP], #0x10
    // 0x9791d4: ret
    //     0x9791d4: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic, WebChromeClient, PermissionRequest) async {
    // ** addr: 0x9791d8, size: 0x5c
    // 0x9791d8: EnterFrame
    //     0x9791d8: stp             fp, lr, [SP, #-0x10]!
    //     0x9791dc: mov             fp, SP
    // 0x9791e0: AllocStack(0x18)
    //     0x9791e0: sub             SP, SP, #0x18
    // 0x9791e4: SetupParameters(AndroidWebViewController this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9791e4: stur            NULL, [fp, #-8]
    //     0x9791e8: movz            x0, #0
    //     0x9791ec: add             x1, fp, w0, sxtw #2
    //     0x9791f0: ldr             x1, [x1, #0x20]
    //     0x9791f4: add             x2, fp, w0, sxtw #2
    //     0x9791f8: ldr             x2, [x2, #0x10]
    //     0x9791fc: stur            x2, [fp, #-0x18]
    //     0x979200: ldur            w3, [x1, #0x17]
    //     0x979204: add             x3, x3, HEAP, lsl #32
    //     0x979208: stur            x3, [fp, #-0x10]
    // 0x97920c: CheckStackOverflow
    //     0x97920c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979210: cmp             SP, x16
    //     0x979214: b.ls            #0x97922c
    // 0x979218: InitAsync() -> Future<void?>
    //     0x979218: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x97921c: bl              #0x661298  ; InitAsyncStub
    // 0x979220: ldur            x1, [fp, #-0x18]
    // 0x979224: r0 = deny()
    //     0x979224: bl              #0x979234  ; [package:webview_flutter_android/src/android_webkit.g.dart] PermissionRequest::deny
    // 0x979228: r0 = ReturnAsync()
    //     0x979228: b               #0x6576a4  ; ReturnAsyncStub
    // 0x97922c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97922c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x979230: b               #0x979218
  }
  [closure] (dynamic, WebChromeClient, ConsoleMessage) => Future<void> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x9794e0, size: 0x54
    // 0x9794e0: EnterFrame
    //     0x9794e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9794e4: mov             fp, SP
    // 0x9794e8: AllocStack(0x8)
    //     0x9794e8: sub             SP, SP, #8
    // 0x9794ec: SetupParameters()
    //     0x9794ec: ldr             x0, [fp, #0x18]
    //     0x9794f0: ldur            w1, [x0, #0x17]
    //     0x9794f4: add             x1, x1, HEAP, lsl #32
    //     0x9794f8: stur            x1, [fp, #-8]
    // 0x9794fc: r1 = 1
    //     0x9794fc: movz            x1, #0x1
    // 0x979500: r0 = AllocateContext()
    //     0x979500: bl              #0xec126c  ; AllocateContextStub
    // 0x979504: mov             x1, x0
    // 0x979508: ldur            x0, [fp, #-8]
    // 0x97950c: StoreField: r1->field_b = r0
    //     0x97950c: stur            w0, [x1, #0xb]
    // 0x979510: ldr             x0, [fp, #0x10]
    // 0x979514: StoreField: r1->field_f = r0
    //     0x979514: stur            w0, [x1, #0xf]
    // 0x979518: mov             x2, x1
    // 0x97951c: r1 = Function '<anonymous closure>':.
    //     0x97951c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49550] AnonymousClosure: (0x979534), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x979520: ldr             x1, [x1, #0x550]
    // 0x979524: r0 = AllocateClosure()
    //     0x979524: bl              #0xec1630  ; AllocateClosureStub
    // 0x979528: LeaveFrame
    //     0x979528: mov             SP, fp
    //     0x97952c: ldp             fp, lr, [SP], #0x10
    // 0x979530: ret
    //     0x979530: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic, WebChromeClient, ConsoleMessage) async {
    // ** addr: 0x979534, size: 0x4c
    // 0x979534: EnterFrame
    //     0x979534: stp             fp, lr, [SP, #-0x10]!
    //     0x979538: mov             fp, SP
    // 0x97953c: AllocStack(0x10)
    //     0x97953c: sub             SP, SP, #0x10
    // 0x979540: SetupParameters(AndroidWebViewController this /* r1 */)
    //     0x979540: stur            NULL, [fp, #-8]
    //     0x979544: movz            x0, #0
    //     0x979548: add             x1, fp, w0, sxtw #2
    //     0x97954c: ldr             x1, [x1, #0x20]
    //     0x979550: ldur            w2, [x1, #0x17]
    //     0x979554: add             x2, x2, HEAP, lsl #32
    //     0x979558: stur            x2, [fp, #-0x10]
    // 0x97955c: CheckStackOverflow
    //     0x97955c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979560: cmp             SP, x16
    //     0x979564: b.ls            #0x979578
    // 0x979568: InitAsync() -> Future<void?>
    //     0x979568: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x97956c: bl              #0x661298  ; InitAsyncStub
    // 0x979570: r0 = Null
    //     0x979570: mov             x0, NULL
    // 0x979574: r0 = ReturnAsyncNotFuture()
    //     0x979574: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x979578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x979578: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97957c: b               #0x979568
  }
  [closure] (dynamic, WebChromeClient, WebView, FileChooserParams) => Future<List<String>> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x979580, size: 0x54
    // 0x979580: EnterFrame
    //     0x979580: stp             fp, lr, [SP, #-0x10]!
    //     0x979584: mov             fp, SP
    // 0x979588: AllocStack(0x8)
    //     0x979588: sub             SP, SP, #8
    // 0x97958c: SetupParameters()
    //     0x97958c: ldr             x0, [fp, #0x18]
    //     0x979590: ldur            w1, [x0, #0x17]
    //     0x979594: add             x1, x1, HEAP, lsl #32
    //     0x979598: stur            x1, [fp, #-8]
    // 0x97959c: r1 = 1
    //     0x97959c: movz            x1, #0x1
    // 0x9795a0: r0 = AllocateContext()
    //     0x9795a0: bl              #0xec126c  ; AllocateContextStub
    // 0x9795a4: mov             x1, x0
    // 0x9795a8: ldur            x0, [fp, #-8]
    // 0x9795ac: StoreField: r1->field_b = r0
    //     0x9795ac: stur            w0, [x1, #0xb]
    // 0x9795b0: ldr             x0, [fp, #0x10]
    // 0x9795b4: StoreField: r1->field_f = r0
    //     0x9795b4: stur            w0, [x1, #0xf]
    // 0x9795b8: mov             x2, x1
    // 0x9795bc: r1 = Function '<anonymous closure>':.
    //     0x9795bc: add             x1, PP, #0x49, lsl #12  ; [pp+0x49558] AnonymousClosure: (0x9795d4), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x9795c0: ldr             x1, [x1, #0x558]
    // 0x9795c4: r0 = AllocateClosure()
    //     0x9795c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x9795c8: LeaveFrame
    //     0x9795c8: mov             SP, fp
    //     0x9795cc: ldp             fp, lr, [SP], #0x10
    // 0x9795d0: ret
    //     0x9795d0: ret             
  }
  [closure] Future<List<String>> <anonymous closure>(dynamic, WebChromeClient, WebView, FileChooserParams) async {
    // ** addr: 0x9795d4, size: 0x58
    // 0x9795d4: EnterFrame
    //     0x9795d4: stp             fp, lr, [SP, #-0x10]!
    //     0x9795d8: mov             fp, SP
    // 0x9795dc: AllocStack(0x10)
    //     0x9795dc: sub             SP, SP, #0x10
    // 0x9795e0: SetupParameters(AndroidWebViewController this /* r1 */)
    //     0x9795e0: stur            NULL, [fp, #-8]
    //     0x9795e4: movz            x0, #0
    //     0x9795e8: add             x1, fp, w0, sxtw #2
    //     0x9795ec: ldr             x1, [x1, #0x28]
    //     0x9795f0: ldur            w2, [x1, #0x17]
    //     0x9795f4: add             x2, x2, HEAP, lsl #32
    //     0x9795f8: stur            x2, [fp, #-0x10]
    // 0x9795fc: CheckStackOverflow
    //     0x9795fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979600: cmp             SP, x16
    //     0x979604: b.ls            #0x979624
    // 0x979608: InitAsync() -> Future<List<String>>
    //     0x979608: add             x0, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0x97960c: ldr             x0, [x0, #0xb40]
    //     0x979610: bl              #0x661298  ; InitAsyncStub
    // 0x979614: r1 = <String>
    //     0x979614: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x979618: r2 = 0
    //     0x979618: movz            x2, #0
    // 0x97961c: r0 = _GrowableList()
    //     0x97961c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x979620: r0 = ReturnAsyncNotFuture()
    //     0x979620: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x979624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x979624: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x979628: b               #0x979608
  }
  [closure] (dynamic, WebChromeClient) => void <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x97962c, size: 0x54
    // 0x97962c: EnterFrame
    //     0x97962c: stp             fp, lr, [SP, #-0x10]!
    //     0x979630: mov             fp, SP
    // 0x979634: AllocStack(0x8)
    //     0x979634: sub             SP, SP, #8
    // 0x979638: SetupParameters()
    //     0x979638: ldr             x0, [fp, #0x18]
    //     0x97963c: ldur            w1, [x0, #0x17]
    //     0x979640: add             x1, x1, HEAP, lsl #32
    //     0x979644: stur            x1, [fp, #-8]
    // 0x979648: r1 = 1
    //     0x979648: movz            x1, #0x1
    // 0x97964c: r0 = AllocateContext()
    //     0x97964c: bl              #0xec126c  ; AllocateContextStub
    // 0x979650: mov             x1, x0
    // 0x979654: ldur            x0, [fp, #-8]
    // 0x979658: StoreField: r1->field_b = r0
    //     0x979658: stur            w0, [x1, #0xb]
    // 0x97965c: ldr             x0, [fp, #0x10]
    // 0x979660: StoreField: r1->field_f = r0
    //     0x979660: stur            w0, [x1, #0xf]
    // 0x979664: mov             x2, x1
    // 0x979668: r1 = Function '<anonymous closure>':.
    //     0x979668: add             x1, PP, #0x49, lsl #12  ; [pp+0x49560] AnonymousClosure: (0x979680), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x97966c: ldr             x1, [x1, #0x560]
    // 0x979670: r0 = AllocateClosure()
    //     0x979670: bl              #0xec1630  ; AllocateClosureStub
    // 0x979674: LeaveFrame
    //     0x979674: mov             SP, fp
    //     0x979678: ldp             fp, lr, [SP], #0x10
    // 0x97967c: ret
    //     0x97967c: ret             
  }
  [closure] void <anonymous closure>(dynamic, WebChromeClient) {
    // ** addr: 0x979680, size: 0x7c
    // 0x979680: EnterFrame
    //     0x979680: stp             fp, lr, [SP, #-0x10]!
    //     0x979684: mov             fp, SP
    // 0x979688: AllocStack(0x8)
    //     0x979688: sub             SP, SP, #8
    // 0x97968c: SetupParameters()
    //     0x97968c: ldr             x0, [fp, #0x18]
    //     0x979690: ldur            w1, [x0, #0x17]
    //     0x979694: add             x1, x1, HEAP, lsl #32
    // 0x979698: CheckStackOverflow
    //     0x979698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97969c: cmp             SP, x16
    //     0x9796a0: b.ls            #0x9796f4
    // 0x9796a4: LoadField: r0 = r1->field_f
    //     0x9796a4: ldur            w0, [x1, #0xf]
    // 0x9796a8: DecompressPointer r0
    //     0x9796a8: add             x0, x0, HEAP, lsl #32
    // 0x9796ac: LoadField: r1 = r0->field_7
    //     0x9796ac: ldur            w1, [x0, #7]
    // 0x9796b0: DecompressPointer r1
    //     0x9796b0: add             x1, x1, HEAP, lsl #32
    // 0x9796b4: cmp             w1, NULL
    // 0x9796b8: b.ne            #0x9796c4
    // 0x9796bc: r0 = Null
    //     0x9796bc: mov             x0, NULL
    // 0x9796c0: b               #0x9796cc
    // 0x9796c4: LoadField: r0 = r1->field_2b
    //     0x9796c4: ldur            w0, [x1, #0x2b]
    // 0x9796c8: DecompressPointer r0
    //     0x9796c8: add             x0, x0, HEAP, lsl #32
    // 0x9796cc: cmp             w0, NULL
    // 0x9796d0: b.eq            #0x9796e4
    // 0x9796d4: str             x0, [SP]
    // 0x9796d8: ClosureCall
    //     0x9796d8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x9796dc: ldur            x2, [x0, #0x1f]
    //     0x9796e0: blr             x2
    // 0x9796e4: r0 = Null
    //     0x9796e4: mov             x0, NULL
    // 0x9796e8: LeaveFrame
    //     0x9796e8: mov             SP, fp
    //     0x9796ec: ldp             fp, lr, [SP], #0x10
    // 0x9796f0: ret
    //     0x9796f0: ret             
    // 0x9796f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9796f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9796f8: b               #0x9796a4
  }
  [closure] (dynamic, WebChromeClient, View, CustomViewCallback) => void <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x9796fc, size: 0x54
    // 0x9796fc: EnterFrame
    //     0x9796fc: stp             fp, lr, [SP, #-0x10]!
    //     0x979700: mov             fp, SP
    // 0x979704: AllocStack(0x8)
    //     0x979704: sub             SP, SP, #8
    // 0x979708: SetupParameters()
    //     0x979708: ldr             x0, [fp, #0x18]
    //     0x97970c: ldur            w1, [x0, #0x17]
    //     0x979710: add             x1, x1, HEAP, lsl #32
    //     0x979714: stur            x1, [fp, #-8]
    // 0x979718: r1 = 1
    //     0x979718: movz            x1, #0x1
    // 0x97971c: r0 = AllocateContext()
    //     0x97971c: bl              #0xec126c  ; AllocateContextStub
    // 0x979720: mov             x1, x0
    // 0x979724: ldur            x0, [fp, #-8]
    // 0x979728: StoreField: r1->field_b = r0
    //     0x979728: stur            w0, [x1, #0xb]
    // 0x97972c: ldr             x0, [fp, #0x10]
    // 0x979730: StoreField: r1->field_f = r0
    //     0x979730: stur            w0, [x1, #0xf]
    // 0x979734: mov             x2, x1
    // 0x979738: r1 = Function '<anonymous closure>':.
    //     0x979738: add             x1, PP, #0x49, lsl #12  ; [pp+0x49568] AnonymousClosure: (0x979750), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x97973c: ldr             x1, [x1, #0x568]
    // 0x979740: r0 = AllocateClosure()
    //     0x979740: bl              #0xec1630  ; AllocateClosureStub
    // 0x979744: LeaveFrame
    //     0x979744: mov             SP, fp
    //     0x979748: ldp             fp, lr, [SP], #0x10
    // 0x97974c: ret
    //     0x97974c: ret             
  }
  [closure] void <anonymous closure>(dynamic, WebChromeClient, View, CustomViewCallback) {
    // ** addr: 0x979750, size: 0x124
    // 0x979750: EnterFrame
    //     0x979750: stp             fp, lr, [SP, #-0x10]!
    //     0x979754: mov             fp, SP
    // 0x979758: AllocStack(0x38)
    //     0x979758: sub             SP, SP, #0x38
    // 0x97975c: SetupParameters()
    //     0x97975c: ldr             x0, [fp, #0x28]
    //     0x979760: ldur            w1, [x0, #0x17]
    //     0x979764: add             x1, x1, HEAP, lsl #32
    //     0x979768: stur            x1, [fp, #-8]
    // 0x97976c: CheckStackOverflow
    //     0x97976c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979770: cmp             SP, x16
    //     0x979774: b.ls            #0x97986c
    // 0x979778: r1 = 1
    //     0x979778: movz            x1, #0x1
    // 0x97977c: r0 = AllocateContext()
    //     0x97977c: bl              #0xec126c  ; AllocateContextStub
    // 0x979780: mov             x1, x0
    // 0x979784: ldur            x0, [fp, #-8]
    // 0x979788: stur            x1, [fp, #-0x18]
    // 0x97978c: StoreField: r1->field_b = r0
    //     0x97978c: stur            w0, [x1, #0xb]
    // 0x979790: ldr             x2, [fp, #0x10]
    // 0x979794: StoreField: r1->field_f = r2
    //     0x979794: stur            w2, [x1, #0xf]
    // 0x979798: LoadField: r3 = r0->field_f
    //     0x979798: ldur            w3, [x0, #0xf]
    // 0x97979c: DecompressPointer r3
    //     0x97979c: add             x3, x3, HEAP, lsl #32
    // 0x9797a0: LoadField: r0 = r3->field_7
    //     0x9797a0: ldur            w0, [x3, #7]
    // 0x9797a4: DecompressPointer r0
    //     0x9797a4: add             x0, x0, HEAP, lsl #32
    // 0x9797a8: cmp             w0, NULL
    // 0x9797ac: b.ne            #0x9797c8
    // 0x9797b0: mov             x1, x2
    // 0x9797b4: r0 = onCustomViewHidden()
    //     0x9797b4: bl              #0x979880  ; [package:webview_flutter_android/src/android_webkit.g.dart] CustomViewCallback::onCustomViewHidden
    // 0x9797b8: r0 = Null
    //     0x9797b8: mov             x0, NULL
    // 0x9797bc: LeaveFrame
    //     0x9797bc: mov             SP, fp
    //     0x9797c0: ldp             fp, lr, [SP], #0x10
    // 0x9797c4: ret
    //     0x9797c4: ret             
    // 0x9797c8: LoadField: r3 = r0->field_27
    //     0x9797c8: ldur            w3, [x0, #0x27]
    // 0x9797cc: DecompressPointer r3
    //     0x9797cc: add             x3, x3, HEAP, lsl #32
    // 0x9797d0: stur            x3, [fp, #-0x10]
    // 0x9797d4: cmp             w3, NULL
    // 0x9797d8: b.ne            #0x9797f4
    // 0x9797dc: mov             x1, x2
    // 0x9797e0: r0 = onCustomViewHidden()
    //     0x9797e0: bl              #0x979880  ; [package:webview_flutter_android/src/android_webkit.g.dart] CustomViewCallback::onCustomViewHidden
    // 0x9797e4: r0 = Null
    //     0x9797e4: mov             x0, NULL
    // 0x9797e8: LeaveFrame
    //     0x9797e8: mov             SP, fp
    //     0x9797ec: ldp             fp, lr, [SP], #0x10
    // 0x9797f0: ret
    //     0x9797f0: ret             
    // 0x9797f4: ldr             x0, [fp, #0x18]
    // 0x9797f8: LoadField: r2 = r0->field_b
    //     0x9797f8: ldur            w2, [x0, #0xb]
    // 0x9797fc: DecompressPointer r2
    //     0x9797fc: add             x2, x2, HEAP, lsl #32
    // 0x979800: stur            x2, [fp, #-8]
    // 0x979804: r0 = AndroidCustomViewWidget()
    //     0x979804: bl              #0x979874  ; AllocateAndroidCustomViewWidgetStub -> AndroidCustomViewWidget (size=0x18)
    // 0x979808: mov             x3, x0
    // 0x97980c: ldr             x0, [fp, #0x18]
    // 0x979810: stur            x3, [fp, #-0x20]
    // 0x979814: StoreField: r3->field_b = r0
    //     0x979814: stur            w0, [x3, #0xb]
    // 0x979818: r0 = Instance_PlatformViewsServiceProxy
    //     0x979818: add             x0, PP, #0x49, lsl #12  ; [pp+0x49570] Obj!PlatformViewsServiceProxy@e0bc01
    //     0x97981c: ldr             x0, [x0, #0x570]
    // 0x979820: StoreField: r3->field_13 = r0
    //     0x979820: stur            w0, [x3, #0x13]
    // 0x979824: ldur            x0, [fp, #-8]
    // 0x979828: StoreField: r3->field_f = r0
    //     0x979828: stur            w0, [x3, #0xf]
    // 0x97982c: ldur            x2, [fp, #-0x18]
    // 0x979830: r1 = Function '<anonymous closure>':.
    //     0x979830: add             x1, PP, #0x49, lsl #12  ; [pp+0x49578] AnonymousClosure: (0x979b2c), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x979834: ldr             x1, [x1, #0x578]
    // 0x979838: r0 = AllocateClosure()
    //     0x979838: bl              #0xec1630  ; AllocateClosureStub
    // 0x97983c: ldur            x16, [fp, #-0x10]
    // 0x979840: ldur            lr, [fp, #-0x20]
    // 0x979844: stp             lr, x16, [SP, #8]
    // 0x979848: str             x0, [SP]
    // 0x97984c: ldur            x0, [fp, #-0x10]
    // 0x979850: ClosureCall
    //     0x979850: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x979854: ldur            x2, [x0, #0x1f]
    //     0x979858: blr             x2
    // 0x97985c: r0 = Null
    //     0x97985c: mov             x0, NULL
    // 0x979860: LeaveFrame
    //     0x979860: mov             SP, fp
    //     0x979864: ldp             fp, lr, [SP], #0x10
    // 0x979868: ret
    //     0x979868: ret             
    // 0x97986c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97986c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x979870: b               #0x979778
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x979b2c, size: 0x44
    // 0x979b2c: EnterFrame
    //     0x979b2c: stp             fp, lr, [SP, #-0x10]!
    //     0x979b30: mov             fp, SP
    // 0x979b34: ldr             x0, [fp, #0x10]
    // 0x979b38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x979b38: ldur            w1, [x0, #0x17]
    // 0x979b3c: DecompressPointer r1
    //     0x979b3c: add             x1, x1, HEAP, lsl #32
    // 0x979b40: CheckStackOverflow
    //     0x979b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979b44: cmp             SP, x16
    //     0x979b48: b.ls            #0x979b68
    // 0x979b4c: LoadField: r0 = r1->field_f
    //     0x979b4c: ldur            w0, [x1, #0xf]
    // 0x979b50: DecompressPointer r0
    //     0x979b50: add             x0, x0, HEAP, lsl #32
    // 0x979b54: mov             x1, x0
    // 0x979b58: r0 = onCustomViewHidden()
    //     0x979b58: bl              #0x979880  ; [package:webview_flutter_android/src/android_webkit.g.dart] CustomViewCallback::onCustomViewHidden
    // 0x979b5c: LeaveFrame
    //     0x979b5c: mov             SP, fp
    //     0x979b60: ldp             fp, lr, [SP], #0x10
    // 0x979b64: ret
    //     0x979b64: ret             
    // 0x979b68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x979b68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x979b6c: b               #0x979b4c
  }
  [closure] (dynamic, WebChromeClient) => void <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x979b70, size: 0x54
    // 0x979b70: EnterFrame
    //     0x979b70: stp             fp, lr, [SP, #-0x10]!
    //     0x979b74: mov             fp, SP
    // 0x979b78: AllocStack(0x8)
    //     0x979b78: sub             SP, SP, #8
    // 0x979b7c: SetupParameters()
    //     0x979b7c: ldr             x0, [fp, #0x18]
    //     0x979b80: ldur            w1, [x0, #0x17]
    //     0x979b84: add             x1, x1, HEAP, lsl #32
    //     0x979b88: stur            x1, [fp, #-8]
    // 0x979b8c: r1 = 1
    //     0x979b8c: movz            x1, #0x1
    // 0x979b90: r0 = AllocateContext()
    //     0x979b90: bl              #0xec126c  ; AllocateContextStub
    // 0x979b94: mov             x1, x0
    // 0x979b98: ldur            x0, [fp, #-8]
    // 0x979b9c: StoreField: r1->field_b = r0
    //     0x979b9c: stur            w0, [x1, #0xb]
    // 0x979ba0: ldr             x0, [fp, #0x10]
    // 0x979ba4: StoreField: r1->field_f = r0
    //     0x979ba4: stur            w0, [x1, #0xf]
    // 0x979ba8: mov             x2, x1
    // 0x979bac: r1 = Function '<anonymous closure>':.
    //     0x979bac: add             x1, PP, #0x49, lsl #12  ; [pp+0x495c0] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x979bb0: ldr             x1, [x1, #0x5c0]
    // 0x979bb4: r0 = AllocateClosure()
    //     0x979bb4: bl              #0xec1630  ; AllocateClosureStub
    // 0x979bb8: LeaveFrame
    //     0x979bb8: mov             SP, fp
    //     0x979bbc: ldp             fp, lr, [SP], #0x10
    // 0x979bc0: ret
    //     0x979bc0: ret             
  }
  [closure] (dynamic, WebChromeClient, String, GeolocationPermissionsCallback) => Future<void> <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x979bc4, size: 0x54
    // 0x979bc4: EnterFrame
    //     0x979bc4: stp             fp, lr, [SP, #-0x10]!
    //     0x979bc8: mov             fp, SP
    // 0x979bcc: AllocStack(0x8)
    //     0x979bcc: sub             SP, SP, #8
    // 0x979bd0: SetupParameters()
    //     0x979bd0: ldr             x0, [fp, #0x18]
    //     0x979bd4: ldur            w1, [x0, #0x17]
    //     0x979bd8: add             x1, x1, HEAP, lsl #32
    //     0x979bdc: stur            x1, [fp, #-8]
    // 0x979be0: r1 = 1
    //     0x979be0: movz            x1, #0x1
    // 0x979be4: r0 = AllocateContext()
    //     0x979be4: bl              #0xec126c  ; AllocateContextStub
    // 0x979be8: mov             x1, x0
    // 0x979bec: ldur            x0, [fp, #-8]
    // 0x979bf0: StoreField: r1->field_b = r0
    //     0x979bf0: stur            w0, [x1, #0xb]
    // 0x979bf4: ldr             x0, [fp, #0x10]
    // 0x979bf8: StoreField: r1->field_f = r0
    //     0x979bf8: stur            w0, [x1, #0xf]
    // 0x979bfc: mov             x2, x1
    // 0x979c00: r1 = Function '<anonymous closure>':.
    //     0x979c00: add             x1, PP, #0x49, lsl #12  ; [pp+0x495c8] AnonymousClosure: (0x979c18), in [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::_webChromeClient (0x978d04)
    //     0x979c04: ldr             x1, [x1, #0x5c8]
    // 0x979c08: r0 = AllocateClosure()
    //     0x979c08: bl              #0xec1630  ; AllocateClosureStub
    // 0x979c0c: LeaveFrame
    //     0x979c0c: mov             SP, fp
    //     0x979c10: ldp             fp, lr, [SP], #0x10
    // 0x979c14: ret
    //     0x979c14: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic, WebChromeClient, String, GeolocationPermissionsCallback) async {
    // ** addr: 0x979c18, size: 0x6c
    // 0x979c18: EnterFrame
    //     0x979c18: stp             fp, lr, [SP, #-0x10]!
    //     0x979c1c: mov             fp, SP
    // 0x979c20: AllocStack(0x20)
    //     0x979c20: sub             SP, SP, #0x20
    // 0x979c24: SetupParameters(AndroidWebViewController this /* r1 */, dynamic _ /* r2, fp-0x20 */, dynamic _ /* r3, fp-0x18 */)
    //     0x979c24: stur            NULL, [fp, #-8]
    //     0x979c28: movz            x0, #0
    //     0x979c2c: add             x1, fp, w0, sxtw #2
    //     0x979c30: ldr             x1, [x1, #0x28]
    //     0x979c34: add             x2, fp, w0, sxtw #2
    //     0x979c38: ldr             x2, [x2, #0x18]
    //     0x979c3c: stur            x2, [fp, #-0x20]
    //     0x979c40: add             x3, fp, w0, sxtw #2
    //     0x979c44: ldr             x3, [x3, #0x10]
    //     0x979c48: stur            x3, [fp, #-0x18]
    //     0x979c4c: ldur            w4, [x1, #0x17]
    //     0x979c50: add             x4, x4, HEAP, lsl #32
    //     0x979c54: stur            x4, [fp, #-0x10]
    // 0x979c58: CheckStackOverflow
    //     0x979c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x979c5c: cmp             SP, x16
    //     0x979c60: b.ls            #0x979c7c
    // 0x979c64: InitAsync() -> Future<void?>
    //     0x979c64: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x979c68: bl              #0x661298  ; InitAsyncStub
    // 0x979c6c: ldur            x1, [fp, #-0x18]
    // 0x979c70: ldur            x2, [fp, #-0x20]
    // 0x979c74: r0 = invoke()
    //     0x979c74: bl              #0x979c84  ; [package:webview_flutter_android/src/android_webkit.g.dart] GeolocationPermissionsCallback::invoke
    // 0x979c78: r0 = ReturnAsync()
    //     0x979c78: b               #0x6576a4  ; ReturnAsyncStub
    // 0x979c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x979c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x979c80: b               #0x979c64
  }
  [closure] (dynamic, WebChromeClient, WebView, int) => void <anonymous closure>(dynamic, WeakReference<AndroidWebViewController>) {
    // ** addr: 0x979f4c, size: 0x54
    // 0x979f4c: EnterFrame
    //     0x979f4c: stp             fp, lr, [SP, #-0x10]!
    //     0x979f50: mov             fp, SP
    // 0x979f54: AllocStack(0x8)
    //     0x979f54: sub             SP, SP, #8
    // 0x979f58: SetupParameters()
    //     0x979f58: ldr             x0, [fp, #0x18]
    //     0x979f5c: ldur            w1, [x0, #0x17]
    //     0x979f60: add             x1, x1, HEAP, lsl #32
    //     0x979f64: stur            x1, [fp, #-8]
    // 0x979f68: r1 = 1
    //     0x979f68: movz            x1, #0x1
    // 0x979f6c: r0 = AllocateContext()
    //     0x979f6c: bl              #0xec126c  ; AllocateContextStub
    // 0x979f70: mov             x1, x0
    // 0x979f74: ldur            x0, [fp, #-8]
    // 0x979f78: StoreField: r1->field_b = r0
    //     0x979f78: stur            w0, [x1, #0xb]
    // 0x979f7c: ldr             x0, [fp, #0x10]
    // 0x979f80: StoreField: r1->field_f = r0
    //     0x979f80: stur            w0, [x1, #0xf]
    // 0x979f84: mov             x2, x1
    // 0x979f88: r1 = Function '<anonymous closure>':.
    //     0x979f88: add             x1, PP, #0x49, lsl #12  ; [pp+0x49610] Function: [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking (0xeb8ce0)
    //     0x979f8c: ldr             x1, [x1, #0x610]
    // 0x979f90: r0 = AllocateClosure()
    //     0x979f90: bl              #0xec1630  ; AllocateClosureStub
    // 0x979f94: LeaveFrame
    //     0x979f94: mov             SP, fp
    //     0x979f98: ldp             fp, lr, [SP], #0x10
    // 0x979f9c: ret
    //     0x979f9c: ret             
  }
  _ runJavaScriptReturningResult(/* No info */) async {
    // ** addr: 0x97c8e8, size: 0xf4
    // 0x97c8e8: EnterFrame
    //     0x97c8e8: stp             fp, lr, [SP, #-0x10]!
    //     0x97c8ec: mov             fp, SP
    // 0x97c8f0: AllocStack(0x28)
    //     0x97c8f0: sub             SP, SP, #0x28
    // 0x97c8f4: SetupParameters(AndroidWebViewController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x97c8f4: stur            NULL, [fp, #-8]
    //     0x97c8f8: stur            x1, [fp, #-0x10]
    //     0x97c8fc: stur            x2, [fp, #-0x18]
    // 0x97c900: CheckStackOverflow
    //     0x97c900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c904: cmp             SP, x16
    //     0x97c908: b.ls            #0x97c9d4
    // 0x97c90c: InitAsync() -> Future<Object>
    //     0x97c90c: ldr             x0, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    //     0x97c910: bl              #0x661298  ; InitAsyncStub
    // 0x97c914: ldur            x1, [fp, #-0x10]
    // 0x97c918: LoadField: r0 = r1->field_b
    //     0x97c918: ldur            w0, [x1, #0xb]
    // 0x97c91c: DecompressPointer r0
    //     0x97c91c: add             x0, x0, HEAP, lsl #32
    // 0x97c920: r16 = Sentinel
    //     0x97c920: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97c924: cmp             w0, w16
    // 0x97c928: b.ne            #0x97c938
    // 0x97c92c: r2 = _webView
    //     0x97c92c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x97c930: ldr             x2, [x2, #0xf30]
    // 0x97c934: r0 = InitLateFinalInstanceField()
    //     0x97c934: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x97c938: mov             x1, x0
    // 0x97c93c: ldur            x2, [fp, #-0x18]
    // 0x97c940: r0 = evaluateJavascript()
    //     0x97c940: bl              #0x965c40  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::evaluateJavascript
    // 0x97c944: mov             x1, x0
    // 0x97c948: stur            x1, [fp, #-0x10]
    // 0x97c94c: r0 = Await()
    //     0x97c94c: bl              #0x661044  ; AwaitStub
    // 0x97c950: mov             x1, x0
    // 0x97c954: stur            x1, [fp, #-0x10]
    // 0x97c958: cmp             w1, NULL
    // 0x97c95c: b.ne            #0x97c968
    // 0x97c960: r0 = ""
    //     0x97c960: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x97c964: r0 = ReturnAsyncNotFuture()
    //     0x97c964: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97c968: r0 = LoadClassIdInstr(r1)
    //     0x97c968: ldur            x0, [x1, #-1]
    //     0x97c96c: ubfx            x0, x0, #0xc, #0x14
    // 0x97c970: r16 = "true"
    //     0x97c970: ldr             x16, [PP, #0x13a0]  ; [pp+0x13a0] "true"
    // 0x97c974: stp             x16, x1, [SP]
    // 0x97c978: mov             lr, x0
    // 0x97c97c: ldr             lr, [x21, lr, lsl #3]
    // 0x97c980: blr             lr
    // 0x97c984: tbnz            w0, #4, #0x97c990
    // 0x97c988: r0 = true
    //     0x97c988: add             x0, NULL, #0x20  ; true
    // 0x97c98c: r0 = ReturnAsyncNotFuture()
    //     0x97c98c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97c990: ldur            x1, [fp, #-0x10]
    // 0x97c994: r0 = LoadClassIdInstr(r1)
    //     0x97c994: ldur            x0, [x1, #-1]
    //     0x97c998: ubfx            x0, x0, #0xc, #0x14
    // 0x97c99c: r16 = "false"
    //     0x97c99c: ldr             x16, [PP, #0x13a8]  ; [pp+0x13a8] "false"
    // 0x97c9a0: stp             x16, x1, [SP]
    // 0x97c9a4: mov             lr, x0
    // 0x97c9a8: ldr             lr, [x21, lr, lsl #3]
    // 0x97c9ac: blr             lr
    // 0x97c9b0: tbnz            w0, #4, #0x97c9bc
    // 0x97c9b4: r0 = false
    //     0x97c9b4: add             x0, NULL, #0x30  ; false
    // 0x97c9b8: r0 = ReturnAsyncNotFuture()
    //     0x97c9b8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97c9bc: ldur            x1, [fp, #-0x10]
    // 0x97c9c0: r0 = tryParse()
    //     0x97c9c0: bl              #0x61d31c  ; [dart:core] num::tryParse
    // 0x97c9c4: cmp             w0, NULL
    // 0x97c9c8: b.ne            #0x97c9d0
    // 0x97c9cc: ldur            x0, [fp, #-0x10]
    // 0x97c9d0: r0 = ReturnAsync()
    //     0x97c9d0: b               #0x6576a4  ; ReturnAsyncStub
    // 0x97c9d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c9d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c9d8: b               #0x97c90c
  }
  _ loadHtmlString(/* No info */) {
    // ** addr: 0x981f14, size: 0x5c
    // 0x981f14: EnterFrame
    //     0x981f14: stp             fp, lr, [SP, #-0x10]!
    //     0x981f18: mov             fp, SP
    // 0x981f1c: AllocStack(0x8)
    //     0x981f1c: sub             SP, SP, #8
    // 0x981f20: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x981f20: stur            x2, [fp, #-8]
    // 0x981f24: CheckStackOverflow
    //     0x981f24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981f28: cmp             SP, x16
    //     0x981f2c: b.ls            #0x981f68
    // 0x981f30: LoadField: r0 = r1->field_b
    //     0x981f30: ldur            w0, [x1, #0xb]
    // 0x981f34: DecompressPointer r0
    //     0x981f34: add             x0, x0, HEAP, lsl #32
    // 0x981f38: r16 = Sentinel
    //     0x981f38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x981f3c: cmp             w0, w16
    // 0x981f40: b.ne            #0x981f50
    // 0x981f44: r2 = _webView
    //     0x981f44: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x981f48: ldr             x2, [x2, #0xf30]
    // 0x981f4c: r0 = InitLateFinalInstanceField()
    //     0x981f4c: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x981f50: mov             x1, x0
    // 0x981f54: ldur            x2, [fp, #-8]
    // 0x981f58: r0 = loadDataWithBaseUrl()
    //     0x981f58: bl              #0x981f70  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::loadDataWithBaseUrl
    // 0x981f5c: LeaveFrame
    //     0x981f5c: mov             SP, fp
    //     0x981f60: ldp             fp, lr, [SP], #0x10
    // 0x981f64: ret
    //     0x981f64: ret             
    // 0x981f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981f68: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981f6c: b               #0x981f30
  }
  _ setBackgroundColor(/* No info */) {
    // ** addr: 0x982528, size: 0x80
    // 0x982528: EnterFrame
    //     0x982528: stp             fp, lr, [SP, #-0x10]!
    //     0x98252c: mov             fp, SP
    // 0x982530: AllocStack(0x10)
    //     0x982530: sub             SP, SP, #0x10
    // 0x982534: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x982534: mov             x0, x2
    //     0x982538: stur            x2, [fp, #-8]
    // 0x98253c: CheckStackOverflow
    //     0x98253c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x982540: cmp             SP, x16
    //     0x982544: b.ls            #0x9825a0
    // 0x982548: LoadField: r0 = r1->field_b
    //     0x982548: ldur            w0, [x1, #0xb]
    // 0x98254c: DecompressPointer r0
    //     0x98254c: add             x0, x0, HEAP, lsl #32
    // 0x982550: r16 = Sentinel
    //     0x982550: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x982554: cmp             w0, w16
    // 0x982558: b.ne            #0x982568
    // 0x98255c: r2 = _webView
    //     0x98255c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0x982560: ldr             x2, [x2, #0xf30]
    // 0x982564: r0 = InitLateFinalInstanceField()
    //     0x982564: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0x982568: mov             x2, x0
    // 0x98256c: ldur            x1, [fp, #-8]
    // 0x982570: stur            x2, [fp, #-0x10]
    // 0x982574: r0 = LoadClassIdInstr(r1)
    //     0x982574: ldur            x0, [x1, #-1]
    //     0x982578: ubfx            x0, x0, #0xc, #0x14
    // 0x98257c: r0 = GDT[cid_x0 + -0xd99]()
    //     0x98257c: sub             lr, x0, #0xd99
    //     0x982580: ldr             lr, [x21, lr, lsl #3]
    //     0x982584: blr             lr
    // 0x982588: ldur            x1, [fp, #-0x10]
    // 0x98258c: mov             x2, x0
    // 0x982590: r0 = setBackgroundColor()
    //     0x982590: bl              #0x9825a8  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::setBackgroundColor
    // 0x982594: LeaveFrame
    //     0x982594: mov             SP, fp
    //     0x982598: ldp             fp, lr, [SP], #0x10
    // 0x98259c: ret
    //     0x98259c: ret             
    // 0x9825a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9825a0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9825a4: b               #0x982548
  }
  _ removeJavaScriptChannel(/* No info */) async {
    // ** addr: 0xa83898, size: 0xc0
    // 0xa83898: EnterFrame
    //     0xa83898: stp             fp, lr, [SP, #-0x10]!
    //     0xa8389c: mov             fp, SP
    // 0xa838a0: AllocStack(0x28)
    //     0xa838a0: sub             SP, SP, #0x28
    // 0xa838a4: SetupParameters(AndroidWebViewController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa838a4: stur            NULL, [fp, #-8]
    //     0xa838a8: stur            x1, [fp, #-0x10]
    //     0xa838ac: stur            x2, [fp, #-0x18]
    // 0xa838b0: CheckStackOverflow
    //     0xa838b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa838b4: cmp             SP, x16
    //     0xa838b8: b.ls            #0xa83950
    // 0xa838bc: InitAsync() -> Future<void?>
    //     0xa838bc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa838c0: bl              #0x661298  ; InitAsyncStub
    // 0xa838c4: ldur            x0, [fp, #-0x10]
    // 0xa838c8: LoadField: r3 = r0->field_13
    //     0xa838c8: ldur            w3, [x0, #0x13]
    // 0xa838cc: DecompressPointer r3
    //     0xa838cc: add             x3, x3, HEAP, lsl #32
    // 0xa838d0: mov             x1, x3
    // 0xa838d4: ldur            x2, [fp, #-0x18]
    // 0xa838d8: stur            x3, [fp, #-0x20]
    // 0xa838dc: r0 = _getValueOrData()
    //     0xa838dc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa838e0: ldur            x1, [fp, #-0x20]
    // 0xa838e4: LoadField: r2 = r1->field_f
    //     0xa838e4: ldur            w2, [x1, #0xf]
    // 0xa838e8: DecompressPointer r2
    //     0xa838e8: add             x2, x2, HEAP, lsl #32
    // 0xa838ec: cmp             w2, w0
    // 0xa838f0: b.ne            #0xa838f8
    // 0xa838f4: r0 = Null
    //     0xa838f4: mov             x0, NULL
    // 0xa838f8: stur            x0, [fp, #-0x28]
    // 0xa838fc: cmp             w0, NULL
    // 0xa83900: b.ne            #0xa8390c
    // 0xa83904: r0 = Null
    //     0xa83904: mov             x0, NULL
    // 0xa83908: r0 = ReturnAsyncNotFuture()
    //     0xa83908: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa8390c: ldur            x2, [fp, #-0x18]
    // 0xa83910: r0 = remove()
    //     0xa83910: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xa83914: ldur            x1, [fp, #-0x10]
    // 0xa83918: LoadField: r0 = r1->field_b
    //     0xa83918: ldur            w0, [x1, #0xb]
    // 0xa8391c: DecompressPointer r0
    //     0xa8391c: add             x0, x0, HEAP, lsl #32
    // 0xa83920: r16 = Sentinel
    //     0xa83920: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa83924: cmp             w0, w16
    // 0xa83928: b.ne            #0xa83938
    // 0xa8392c: r2 = _webView
    //     0xa8392c: add             x2, PP, #0x47, lsl #12  ; [pp+0x47f30] Field <AndroidWebViewController._webView@470193571>: late final (offset: 0xc)
    //     0xa83930: ldr             x2, [x2, #0xf30]
    // 0xa83934: r0 = InitLateFinalInstanceField()
    //     0xa83934: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xa83938: mov             x1, x0
    // 0xa8393c: ldur            x0, [fp, #-0x28]
    // 0xa83940: LoadField: r2 = r0->field_7
    //     0xa83940: ldur            w2, [x0, #7]
    // 0xa83944: DecompressPointer r2
    //     0xa83944: add             x2, x2, HEAP, lsl #32
    // 0xa83948: r0 = removeJavaScriptChannel()
    //     0xa83948: bl              #0x9721e8  ; [package:webview_flutter_android/src/android_webkit.g.dart] WebView::removeJavaScriptChannel
    // 0xa8394c: r0 = ReturnAsync()
    //     0xa8394c: b               #0x6576a4  ; ReturnAsyncStub
    // 0xa83950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83950: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83954: b               #0xa838bc
  }
  _ setCustomWidgetCallbacks(/* No info */) async {
    // ** addr: 0xbbcc74, size: 0x90
    // 0xbbcc74: EnterFrame
    //     0xbbcc74: stp             fp, lr, [SP, #-0x10]!
    //     0xbbcc78: mov             fp, SP
    // 0xbbcc7c: AllocStack(0x20)
    //     0xbbcc7c: sub             SP, SP, #0x20
    // 0xbbcc80: SetupParameters(AndroidWebViewController this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0xbbcc80: stur            NULL, [fp, #-8]
    //     0xbbcc84: stur            x1, [fp, #-0x10]
    //     0xbbcc88: mov             x16, x3
    //     0xbbcc8c: mov             x3, x1
    //     0xbbcc90: mov             x1, x16
    //     0xbbcc94: stur            x2, [fp, #-0x18]
    //     0xbbcc98: stur            x1, [fp, #-0x20]
    // 0xbbcc9c: CheckStackOverflow
    //     0xbbcc9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbcca0: cmp             SP, x16
    //     0xbbcca4: b.ls            #0xbbccfc
    // 0xbbcca8: InitAsync() -> Future<void?>
    //     0xbbcca8: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xbbccac: bl              #0x661298  ; InitAsyncStub
    // 0xbbccb0: ldur            x0, [fp, #-0x20]
    // 0xbbccb4: ldur            x1, [fp, #-0x10]
    // 0xbbccb8: StoreField: r1->field_27 = r0
    //     0xbbccb8: stur            w0, [x1, #0x27]
    //     0xbbccbc: ldurb           w16, [x1, #-1]
    //     0xbbccc0: ldurb           w17, [x0, #-1]
    //     0xbbccc4: and             x16, x17, x16, lsr #2
    //     0xbbccc8: tst             x16, HEAP, lsr #32
    //     0xbbcccc: b.eq            #0xbbccd4
    //     0xbbccd0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbbccd4: ldur            x0, [fp, #-0x18]
    // 0xbbccd8: StoreField: r1->field_2b = r0
    //     0xbbccd8: stur            w0, [x1, #0x2b]
    //     0xbbccdc: ldurb           w16, [x1, #-1]
    //     0xbbcce0: ldurb           w17, [x0, #-1]
    //     0xbbcce4: and             x16, x17, x16, lsr #2
    //     0xbbcce8: tst             x16, HEAP, lsr #32
    //     0xbbccec: b.eq            #0xbbccf4
    //     0xbbccf0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xbbccf4: r0 = Null
    //     0xbbccf4: mov             x0, NULL
    // 0xbbccf8: r0 = ReturnAsyncNotFuture()
    //     0xbbccf8: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xbbccfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbccfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbcd00: b               #0xbbcca8
  }
}
