// lib: , url: package:webview_flutter_android/src/android_webview_platform.dart

// class id: 1051250, size: 0x8
class :: {
}

// class id: 5850, size: 0x8, field offset: 0x8
class AndroidWebViewPlatform extends WebViewPlatform {

  _ createPlatformWebViewController(/* No info */) {
    // ** addr: 0x9743d8, size: 0x40
    // 0x9743d8: EnterFrame
    //     0x9743d8: stp             fp, lr, [SP, #-0x10]!
    //     0x9743dc: mov             fp, SP
    // 0x9743e0: AllocStack(0x8)
    //     0x9743e0: sub             SP, SP, #8
    // 0x9743e4: CheckStackOverflow
    //     0x9743e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9743e8: cmp             SP, x16
    //     0x9743ec: b.ls            #0x974410
    // 0x9743f0: r0 = AndroidWebViewController()
    //     0x9743f0: bl              #0x979fa0  ; AllocateAndroidWebViewControllerStub -> AndroidWebViewController (size=0x48)
    // 0x9743f4: mov             x1, x0
    // 0x9743f8: stur            x0, [fp, #-8]
    // 0x9743fc: r0 = AndroidWebViewController()
    //     0x9743fc: bl              #0x974418  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::AndroidWebViewController
    // 0x974400: ldur            x0, [fp, #-8]
    // 0x974404: LeaveFrame
    //     0x974404: mov             SP, fp
    //     0x974408: ldp             fp, lr, [SP], #0x10
    // 0x97440c: ret
    //     0x97440c: ret             
    // 0x974410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x974410: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x974414: b               #0x9743f0
  }
  _ createPlatformNavigationDelegate(/* No info */) {
    // ** addr: 0x97a4a0, size: 0x40
    // 0x97a4a0: EnterFrame
    //     0x97a4a0: stp             fp, lr, [SP, #-0x10]!
    //     0x97a4a4: mov             fp, SP
    // 0x97a4a8: AllocStack(0x8)
    //     0x97a4a8: sub             SP, SP, #8
    // 0x97a4ac: CheckStackOverflow
    //     0x97a4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a4b0: cmp             SP, x16
    //     0x97a4b4: b.ls            #0x97a4d8
    // 0x97a4b8: r0 = AndroidNavigationDelegate()
    //     0x97a4b8: bl              #0x97b300  ; AllocateAndroidNavigationDelegateStub -> AndroidNavigationDelegate (size=0x38)
    // 0x97a4bc: mov             x1, x0
    // 0x97a4c0: stur            x0, [fp, #-8]
    // 0x97a4c4: r0 = AndroidNavigationDelegate()
    //     0x97a4c4: bl              #0x97a4e0  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidNavigationDelegate::AndroidNavigationDelegate
    // 0x97a4c8: ldur            x0, [fp, #-8]
    // 0x97a4cc: LeaveFrame
    //     0x97a4cc: mov             SP, fp
    //     0x97a4d0: ldp             fp, lr, [SP], #0x10
    // 0x97a4d4: ret
    //     0x97a4d4: ret             
    // 0x97a4d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a4d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a4dc: b               #0x97a4b8
  }
  _ createPlatformWebViewWidget(/* No info */) {
    // ** addr: 0xa516d8, size: 0xac
    // 0xa516d8: EnterFrame
    //     0xa516d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa516dc: mov             fp, SP
    // 0xa516e0: AllocStack(0x10)
    //     0xa516e0: sub             SP, SP, #0x10
    // 0xa516e4: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xa516e4: stur            x2, [fp, #-8]
    // 0xa516e8: CheckStackOverflow
    //     0xa516e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa516ec: cmp             SP, x16
    //     0xa516f0: b.ls            #0xa5177c
    // 0xa516f4: r0 = AndroidWebViewWidgetCreationParams()
    //     0xa516f4: bl              #0xa51878  ; AllocateAndroidWebViewWidgetCreationParamsStub -> AndroidWebViewWidgetCreationParams (size=0x24)
    // 0xa516f8: mov             x1, x0
    // 0xa516fc: ldur            x2, [fp, #-8]
    // 0xa51700: stur            x0, [fp, #-8]
    // 0xa51704: r0 = AndroidWebViewWidgetCreationParams.fromPlatformWebViewWidgetCreationParams()
    //     0xa51704: bl              #0xa51790  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewWidgetCreationParams::AndroidWebViewWidgetCreationParams.fromPlatformWebViewWidgetCreationParams
    // 0xa51708: r0 = AndroidWebViewWidget()
    //     0xa51708: bl              #0xa51784  ; AllocateAndroidWebViewWidgetStub -> AndroidWebViewWidget (size=0xc)
    // 0xa5170c: mov             x1, x0
    // 0xa51710: ldur            x0, [fp, #-8]
    // 0xa51714: stur            x1, [fp, #-0x10]
    // 0xa51718: StoreField: r1->field_7 = r0
    //     0xa51718: stur            w0, [x1, #7]
    // 0xa5171c: r0 = InitLateStaticField(0x9ec) // [package:webview_flutter_platform_interface/src/platform_webview_widget.dart] PlatformWebViewWidget::_token
    //     0xa5171c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa51720: ldr             x0, [x0, #0x13d8]
    //     0xa51724: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa51728: cmp             w0, w16
    //     0xa5172c: b.ne            #0xa5173c
    //     0xa51730: add             x2, PP, #0x57, lsl #12  ; [pp+0x57aa8] Field <PlatformWebViewWidget._token@475459506>: static late final (offset: 0x9ec)
    //     0xa51734: ldr             x2, [x2, #0xaa8]
    //     0xa51738: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa5173c: stur            x0, [fp, #-8]
    // 0xa51740: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xa51740: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa51744: ldr             x0, [x0, #0xc08]
    //     0xa51748: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa5174c: cmp             w0, w16
    //     0xa51750: b.ne            #0xa5175c
    //     0xa51754: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xa51758: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa5175c: mov             x1, x0
    // 0xa51760: ldur            x2, [fp, #-0x10]
    // 0xa51764: ldur            x3, [fp, #-8]
    // 0xa51768: r0 = []=()
    //     0xa51768: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xa5176c: ldur            x0, [fp, #-0x10]
    // 0xa51770: LeaveFrame
    //     0xa51770: mov             SP, fp
    //     0xa51774: ldp             fp, lr, [SP], #0x10
    // 0xa51778: ret
    //     0xa51778: ret             
    // 0xa5177c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5177c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa51780: b               #0xa516f4
  }
  static void registerWith() {
    // ** addr: 0xec6d40, size: 0x94
    // 0xec6d40: EnterFrame
    //     0xec6d40: stp             fp, lr, [SP, #-0x10]!
    //     0xec6d44: mov             fp, SP
    // 0xec6d48: AllocStack(0x10)
    //     0xec6d48: sub             SP, SP, #0x10
    // 0xec6d4c: CheckStackOverflow
    //     0xec6d4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec6d50: cmp             SP, x16
    //     0xec6d54: b.ls            #0xec6dcc
    // 0xec6d58: r0 = InitLateStaticField(0x9f8) // [package:webview_flutter_platform_interface/src/webview_platform.dart] WebViewPlatform::_token
    //     0xec6d58: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec6d5c: ldr             x0, [x0, #0x13f0]
    //     0xec6d60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec6d64: cmp             w0, w16
    //     0xec6d68: b.ne            #0xec6d78
    //     0xec6d6c: add             x2, PP, #0xc, lsl #12  ; [pp+0xc738] Field <WebViewPlatform._token@476513057>: static late final (offset: 0x9f8)
    //     0xec6d70: ldr             x2, [x2, #0x738]
    //     0xec6d74: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec6d78: stur            x0, [fp, #-8]
    // 0xec6d7c: r0 = InitLateStaticField(0x604) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xec6d7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec6d80: ldr             x0, [x0, #0xc08]
    //     0xec6d84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec6d88: cmp             w0, w16
    //     0xec6d8c: b.ne            #0xec6d98
    //     0xec6d90: ldr             x2, [PP, #0x11d8]  ; [pp+0x11d8] Field <PlatformInterface._instanceTokens@220304592>: static late final (offset: 0x604)
    //     0xec6d94: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec6d98: stur            x0, [fp, #-0x10]
    // 0xec6d9c: r0 = AndroidWebViewPlatform()
    //     0xec6d9c: bl              #0xec6e40  ; AllocateAndroidWebViewPlatformStub -> AndroidWebViewPlatform (size=0x8)
    // 0xec6da0: ldur            x1, [fp, #-0x10]
    // 0xec6da4: mov             x2, x0
    // 0xec6da8: ldur            x3, [fp, #-8]
    // 0xec6dac: stur            x0, [fp, #-8]
    // 0xec6db0: r0 = []=()
    //     0xec6db0: bl              #0x6616d0  ; [dart:core] Expando::[]=
    // 0xec6db4: ldur            x1, [fp, #-8]
    // 0xec6db8: r0 = instance=()
    //     0xec6db8: bl              #0xec6dd4  ; [package:webview_flutter_platform_interface/src/webview_platform.dart] WebViewPlatform::instance=
    // 0xec6dbc: r0 = Null
    //     0xec6dbc: mov             x0, NULL
    // 0xec6dc0: LeaveFrame
    //     0xec6dc0: mov             SP, fp
    //     0xec6dc4: ldp             fp, lr, [SP], #0x10
    // 0xec6dc8: ret
    //     0xec6dc8: ret             
    // 0xec6dcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec6dcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec6dd0: b               #0xec6d58
  }
}
