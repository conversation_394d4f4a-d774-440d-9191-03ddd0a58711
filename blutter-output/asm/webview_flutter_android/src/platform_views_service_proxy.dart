// lib: , url: package:webview_flutter_android/src/platform_views_service_proxy.dart

// class id: 1051251, size: 0x8
class :: {
}

// class id: 326, size: 0x8, field offset: 0x8
//   const constructor, 
class PlatformViewsServiceProxy extends Object {

  _ initSurfaceAndroidView(/* No info */) {
    // ** addr: 0xbbd020, size: 0x5c
    // 0xbbd020: EnterFrame
    //     0xbbd020: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd024: mov             fp, SP
    // 0xbbd028: mov             x16, x3
    // 0xbbd02c: mov             x3, x2
    // 0xbbd030: mov             x2, x16
    // 0xbbd034: CheckStackOverflow
    //     0xbbd034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbd038: cmp             SP, x16
    //     0xbbd03c: b.ls            #0xbbd074
    // 0xbbd040: r0 = BoxInt64Instr(r3)
    //     0xbbd040: sbfiz           x0, x3, #1, #0x1f
    //     0xbbd044: cmp             x3, x0, asr #1
    //     0xbbd048: b.eq            #0xbbd054
    //     0xbbd04c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbd050: stur            x3, [x0, #7]
    // 0xbbd054: mov             x1, x0
    // 0xbbd058: r3 = Instance_TextDirection
    //     0xbbd058: ldr             x3, [PP, #0x28b8]  ; [pp+0x28b8] Obj!TextDirection@e392c1
    // 0xbbd05c: r5 = "plugins.flutter.io/webview"
    //     0xbbd05c: add             x5, PP, #0x55, lsl #12  ; [pp+0x55240] "plugins.flutter.io/webview"
    //     0xbbd060: ldr             x5, [x5, #0x240]
    // 0xbbd064: r0 = initSurfaceAndroidView()
    //     0xbbd064: bl              #0xa2d16c  ; [package:flutter/src/services/platform_views.dart] PlatformViewsService::initSurfaceAndroidView
    // 0xbbd068: LeaveFrame
    //     0xbbd068: mov             SP, fp
    //     0xbbd06c: ldp             fp, lr, [SP], #0x10
    // 0xbbd070: ret
    //     0xbbd070: ret             
    // 0xbbd074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbd074: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbd078: b               #0xbbd040
  }
}
