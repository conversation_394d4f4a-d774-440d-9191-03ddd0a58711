// lib: , url: package:webview_flutter_android/src/weak_reference_utils.dart

// class id: 1051252, size: 0x8
class :: {

  static _ withWeakReferenceTo(/* No info */) {
    // ** addr: 0x970890, size: 0xa4
    // 0x970890: EnterFrame
    //     0x970890: stp             fp, lr, [SP, #-0x10]!
    //     0x970894: mov             fp, SP
    // 0x970898: AllocStack(0x18)
    //     0x970898: sub             SP, SP, #0x18
    // 0x97089c: SetupParameters()
    //     0x97089c: ldur            w0, [x4, #0xf]
    //     0x9708a0: cbnz            w0, #0x9708ac
    //     0x9708a4: mov             x1, NULL
    //     0x9708a8: b               #0x9708bc
    //     0x9708ac: ldur            w1, [x4, #0x17]
    //     0x9708b0: add             x2, fp, w1, sxtw #2
    //     0x9708b4: ldr             x2, [x2, #0x10]
    //     0x9708b8: mov             x1, x2
    // 0x9708bc: CheckStackOverflow
    //     0x9708bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9708c0: cmp             SP, x16
    //     0x9708c4: b.ls            #0x97092c
    // 0x9708c8: cbnz            w0, #0x9708d8
    // 0x9708cc: r3 = <Object, Object>
    //     0x9708cc: add             x3, PP, #0x48, lsl #12  ; [pp+0x48ef0] TypeArguments: <Object, Object>
    //     0x9708d0: ldr             x3, [x3, #0xef0]
    // 0x9708d4: b               #0x9708dc
    // 0x9708d8: mov             x3, x1
    // 0x9708dc: ldr             x0, [fp, #0x18]
    // 0x9708e0: mov             x1, x0
    // 0x9708e4: stur            x3, [fp, #-8]
    // 0x9708e8: r2 = "target"
    //     0x9708e8: add             x2, PP, #0x1a, lsl #12  ; [pp+0x1a9a8] "target"
    //     0x9708ec: ldr             x2, [x2, #0x9a8]
    // 0x9708f0: r0 = checkValidWeakTarget()
    //     0x9708f0: bl              #0x65740c  ; [dart:_internal] ::checkValidWeakTarget
    // 0x9708f4: ldur            x1, [fp, #-8]
    // 0x9708f8: r0 = _WeakReference()
    //     0x9708f8: bl              #0x657400  ; Allocate_WeakReferenceStub -> _WeakReference<X0> (size=-0x8)
    // 0x9708fc: mov             x1, x0
    // 0x970900: ldr             x0, [fp, #0x18]
    // 0x970904: StoreField: r1->field_7 = r0
    //     0x970904: stur            w0, [x1, #7]
    // 0x970908: ldr             x16, [fp, #0x10]
    // 0x97090c: stp             x1, x16, [SP]
    // 0x970910: ldr             x0, [fp, #0x10]
    // 0x970914: ClosureCall
    //     0x970914: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x970918: ldur            x2, [x0, #0x1f]
    //     0x97091c: blr             x2
    // 0x970920: LeaveFrame
    //     0x970920: mov             SP, fp
    //     0x970924: ldp             fp, lr, [SP], #0x10
    // 0x970928: ret
    //     0x970928: ret             
    // 0x97092c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97092c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x970930: b               #0x9708c8
  }
}
