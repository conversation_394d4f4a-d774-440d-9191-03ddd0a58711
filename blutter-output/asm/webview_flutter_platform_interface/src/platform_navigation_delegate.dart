// lib: , url: package:webview_flutter_platform_interface/src/platform_navigation_delegate.dart

// class id: 1051253, size: 0x8
class :: {
}

// class id: 5851, size: 0xc, field offset: 0x8
abstract class PlatformNavigationDelegate extends PlatformInterface {

  static late final Object _token; // offset: 0x9f0

  factory _ PlatformNavigationDelegate(/* No info */) {
    // ** addr: 0x97a428, size: 0x78
    // 0x97a428: EnterFrame
    //     0x97a428: stp             fp, lr, [SP, #-0x10]!
    //     0x97a42c: mov             fp, SP
    // 0x97a430: AllocStack(0x8)
    //     0x97a430: sub             SP, SP, #8
    // 0x97a434: CheckStackOverflow
    //     0x97a434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97a438: cmp             SP, x16
    //     0x97a43c: b.ls            #0x97a494
    // 0x97a440: r1 = LoadStaticField(0x9fc)
    //     0x97a440: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x97a444: ldr             x1, [x1, #0x13f8]
    // 0x97a448: cmp             w1, NULL
    // 0x97a44c: b.eq            #0x97a49c
    // 0x97a450: r0 = createPlatformNavigationDelegate()
    //     0x97a450: bl              #0x97a4a0  ; [package:webview_flutter_android/src/android_webview_platform.dart] AndroidWebViewPlatform::createPlatformNavigationDelegate
    // 0x97a454: stur            x0, [fp, #-8]
    // 0x97a458: r0 = InitLateStaticField(0x9f0) // [package:webview_flutter_platform_interface/src/platform_navigation_delegate.dart] PlatformNavigationDelegate::_token
    //     0x97a458: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97a45c: ldr             x0, [x0, #0x13e0]
    //     0x97a460: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x97a464: cmp             w0, w16
    //     0x97a468: b.ne            #0x97a478
    //     0x97a46c: add             x2, PP, #0x49, lsl #12  ; [pp+0x499d8] Field <PlatformNavigationDelegate._token@473149494>: static late final (offset: 0x9f0)
    //     0x97a470: ldr             x2, [x2, #0x9d8]
    //     0x97a474: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x97a478: ldur            x1, [fp, #-8]
    // 0x97a47c: mov             x2, x0
    // 0x97a480: r0 = verify()
    //     0x97a480: bl              #0x833b94  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0x97a484: ldur            x0, [fp, #-8]
    // 0x97a488: LeaveFrame
    //     0x97a488: mov             SP, fp
    //     0x97a48c: ldp             fp, lr, [SP], #0x10
    // 0x97a490: ret
    //     0x97a490: ret             
    // 0x97a494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97a494: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97a498: b               #0x97a440
    // 0x97a49c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x97a49c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
