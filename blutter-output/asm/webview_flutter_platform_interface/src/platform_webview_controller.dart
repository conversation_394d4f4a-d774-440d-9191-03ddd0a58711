// lib: , url: package:webview_flutter_platform_interface/src/platform_webview_controller.dart

// class id: 1051254, size: 0x8
class :: {
}

// class id: 335, size: 0x10, field offset: 0x8
//   const constructor, 
class JavaScriptChannelParams extends Object {
}

// class id: 5855, size: 0xc, field offset: 0x8
abstract class PlatformWebViewController extends PlatformInterface {

  static late final Object _token; // offset: 0x9e8

  factory _ PlatformWebViewController(/* No info */) {
    // ** addr: 0x974360, size: 0x78
    // 0x974360: EnterFrame
    //     0x974360: stp             fp, lr, [SP, #-0x10]!
    //     0x974364: mov             fp, SP
    // 0x974368: AllocStack(0x8)
    //     0x974368: sub             SP, SP, #8
    // 0x97436c: CheckStackOverflow
    //     0x97436c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x974370: cmp             SP, x16
    //     0x974374: b.ls            #0x9743cc
    // 0x974378: r1 = LoadStaticField(0x9fc)
    //     0x974378: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x97437c: ldr             x1, [x1, #0x13f8]
    // 0x974380: cmp             w1, NULL
    // 0x974384: b.eq            #0x9743d4
    // 0x974388: r0 = createPlatformWebViewController()
    //     0x974388: bl              #0x9743d8  ; [package:webview_flutter_android/src/android_webview_platform.dart] AndroidWebViewPlatform::createPlatformWebViewController
    // 0x97438c: stur            x0, [fp, #-8]
    // 0x974390: r0 = InitLateStaticField(0x9e8) // [package:webview_flutter_platform_interface/src/platform_webview_controller.dart] PlatformWebViewController::_token
    //     0x974390: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x974394: ldr             x0, [x0, #0x13d0]
    //     0x974398: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x97439c: cmp             w0, w16
    //     0x9743a0: b.ne            #0x9743b0
    //     0x9743a4: add             x2, PP, #0x49, lsl #12  ; [pp+0x49428] Field <PlatformWebViewController._token@474166800>: static late final (offset: 0x9e8)
    //     0x9743a8: ldr             x2, [x2, #0x428]
    //     0x9743ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9743b0: ldur            x1, [fp, #-8]
    // 0x9743b4: mov             x2, x0
    // 0x9743b8: r0 = verify()
    //     0x9743b8: bl              #0x833b94  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0x9743bc: ldur            x0, [fp, #-8]
    // 0x9743c0: LeaveFrame
    //     0x9743c0: mov             SP, fp
    //     0x9743c4: ldp             fp, lr, [SP], #0x10
    // 0x9743c8: ret
    //     0x9743c8: ret             
    // 0x9743cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9743cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9743d0: b               #0x974378
    // 0x9743d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9743d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
