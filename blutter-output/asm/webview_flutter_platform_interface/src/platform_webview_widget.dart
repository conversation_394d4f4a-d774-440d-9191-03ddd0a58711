// lib: , url: package:webview_flutter_platform_interface/src/platform_webview_widget.dart

// class id: 1051255, size: 0x8
class :: {
}

// class id: 5853, size: 0xc, field offset: 0x8
abstract class PlatformWebViewWidget extends PlatformInterface {

  static late final Object _token; // offset: 0x9ec

  factory _ PlatformWebViewWidget(/* No info */) {
    // ** addr: 0xa51660, size: 0x78
    // 0xa51660: EnterFrame
    //     0xa51660: stp             fp, lr, [SP, #-0x10]!
    //     0xa51664: mov             fp, SP
    // 0xa51668: AllocStack(0x8)
    //     0xa51668: sub             SP, SP, #8
    // 0xa5166c: CheckStackOverflow
    //     0xa5166c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa51670: cmp             SP, x16
    //     0xa51674: b.ls            #0xa516cc
    // 0xa51678: r1 = LoadStaticField(0x9fc)
    //     0xa51678: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xa5167c: ldr             x1, [x1, #0x13f8]
    // 0xa51680: cmp             w1, NULL
    // 0xa51684: b.eq            #0xa516d4
    // 0xa51688: r0 = createPlatformWebViewWidget()
    //     0xa51688: bl              #0xa516d8  ; [package:webview_flutter_android/src/android_webview_platform.dart] AndroidWebViewPlatform::createPlatformWebViewWidget
    // 0xa5168c: stur            x0, [fp, #-8]
    // 0xa51690: r0 = InitLateStaticField(0x9ec) // [package:webview_flutter_platform_interface/src/platform_webview_widget.dart] PlatformWebViewWidget::_token
    //     0xa51690: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa51694: ldr             x0, [x0, #0x13d8]
    //     0xa51698: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa5169c: cmp             w0, w16
    //     0xa516a0: b.ne            #0xa516b0
    //     0xa516a4: add             x2, PP, #0x57, lsl #12  ; [pp+0x57aa8] Field <PlatformWebViewWidget._token@475459506>: static late final (offset: 0x9ec)
    //     0xa516a8: ldr             x2, [x2, #0xaa8]
    //     0xa516ac: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xa516b0: ldur            x1, [fp, #-8]
    // 0xa516b4: mov             x2, x0
    // 0xa516b8: r0 = verify()
    //     0xa516b8: bl              #0x833b94  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0xa516bc: ldur            x0, [fp, #-8]
    // 0xa516c0: LeaveFrame
    //     0xa516c0: mov             SP, fp
    //     0xa516c4: ldp             fp, lr, [SP], #0x10
    // 0xa516c8: ret
    //     0xa516c8: ret             
    // 0xa516cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa516cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa516d0: b               #0xa51678
    // 0xa516d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa516d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
