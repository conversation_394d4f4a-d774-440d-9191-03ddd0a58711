// lib: , url: package:webview_flutter_platform_interface/src/types/javascript_mode.dart

// class id: 1051262, size: 0x8
class :: {
}

// class id: 6755, size: 0x14, field offset: 0x14
enum JavaScriptMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ee64, size: 0x64
    // 0xc4ee64: EnterFrame
    //     0xc4ee64: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ee68: mov             fp, SP
    // 0xc4ee6c: AllocStack(0x10)
    //     0xc4ee6c: sub             SP, SP, #0x10
    // 0xc4ee70: SetupParameters(JavaScriptMode this /* r1 => r0, fp-0x8 */)
    //     0xc4ee70: mov             x0, x1
    //     0xc4ee74: stur            x1, [fp, #-8]
    // 0xc4ee78: CheckStackOverflow
    //     0xc4ee78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ee7c: cmp             SP, x16
    //     0xc4ee80: b.ls            #0xc4eec0
    // 0xc4ee84: r1 = Null
    //     0xc4ee84: mov             x1, NULL
    // 0xc4ee88: r2 = 4
    //     0xc4ee88: movz            x2, #0x4
    // 0xc4ee8c: r0 = AllocateArray()
    //     0xc4ee8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ee90: r16 = "JavaScriptMode."
    //     0xc4ee90: add             x16, PP, #0x55, lsl #12  ; [pp+0x551b0] "JavaScriptMode."
    //     0xc4ee94: ldr             x16, [x16, #0x1b0]
    // 0xc4ee98: StoreField: r0->field_f = r16
    //     0xc4ee98: stur            w16, [x0, #0xf]
    // 0xc4ee9c: ldur            x1, [fp, #-8]
    // 0xc4eea0: LoadField: r2 = r1->field_f
    //     0xc4eea0: ldur            w2, [x1, #0xf]
    // 0xc4eea4: DecompressPointer r2
    //     0xc4eea4: add             x2, x2, HEAP, lsl #32
    // 0xc4eea8: StoreField: r0->field_13 = r2
    //     0xc4eea8: stur            w2, [x0, #0x13]
    // 0xc4eeac: str             x0, [SP]
    // 0xc4eeb0: r0 = _interpolate()
    //     0xc4eeb0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4eeb4: LeaveFrame
    //     0xc4eeb4: mov             SP, fp
    //     0xc4eeb8: ldp             fp, lr, [SP], #0x10
    // 0xc4eebc: ret
    //     0xc4eebc: ret             
    // 0xc4eec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4eec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4eec4: b               #0xc4ee84
  }
}
