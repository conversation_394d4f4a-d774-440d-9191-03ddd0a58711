// lib: , url: package:webview_flutter_platform_interface/src/types/load_request_params.dart

// class id: 1051263, size: 0x8
class :: {
}

// class id: 318, size: 0x18, field offset: 0x8
//   const constructor, 
class LoadRequestParams extends Object {
}

// class id: 6754, size: 0x14, field offset: 0x14
enum LoadRequestMethod extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4eec8, size: 0x64
    // 0xc4eec8: EnterFrame
    //     0xc4eec8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4eecc: mov             fp, SP
    // 0xc4eed0: AllocStack(0x10)
    //     0xc4eed0: sub             SP, SP, #0x10
    // 0xc4eed4: SetupParameters(LoadRequestMethod this /* r1 => r0, fp-0x8 */)
    //     0xc4eed4: mov             x0, x1
    //     0xc4eed8: stur            x1, [fp, #-8]
    // 0xc4eedc: CheckStackOverflow
    //     0xc4eedc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4eee0: cmp             SP, x16
    //     0xc4eee4: b.ls            #0xc4ef24
    // 0xc4eee8: r1 = Null
    //     0xc4eee8: mov             x1, NULL
    // 0xc4eeec: r2 = 4
    //     0xc4eeec: movz            x2, #0x4
    // 0xc4eef0: r0 = AllocateArray()
    //     0xc4eef0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4eef4: r16 = "LoadRequestMethod."
    //     0xc4eef4: add             x16, PP, #0x55, lsl #12  ; [pp+0x551a8] "LoadRequestMethod."
    //     0xc4eef8: ldr             x16, [x16, #0x1a8]
    // 0xc4eefc: StoreField: r0->field_f = r16
    //     0xc4eefc: stur            w16, [x0, #0xf]
    // 0xc4ef00: ldur            x1, [fp, #-8]
    // 0xc4ef04: LoadField: r2 = r1->field_f
    //     0xc4ef04: ldur            w2, [x1, #0xf]
    // 0xc4ef08: DecompressPointer r2
    //     0xc4ef08: add             x2, x2, HEAP, lsl #32
    // 0xc4ef0c: StoreField: r0->field_13 = r2
    //     0xc4ef0c: stur            w2, [x0, #0x13]
    // 0xc4ef10: str             x0, [SP]
    // 0xc4ef14: r0 = _interpolate()
    //     0xc4ef14: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ef18: LeaveFrame
    //     0xc4ef18: mov             SP, fp
    //     0xc4ef1c: ldp             fp, lr, [SP], #0x10
    // 0xc4ef20: ret
    //     0xc4ef20: ret             
    // 0xc4ef24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ef24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ef28: b               #0xc4eee8
  }
}
