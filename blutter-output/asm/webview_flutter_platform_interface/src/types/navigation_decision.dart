// lib: , url: package:webview_flutter_platform_interface/src/types/navigation_decision.dart

// class id: 1051264, size: 0x8
class :: {
}

// class id: 6753, size: 0x14, field offset: 0x14
enum NavigationDecision extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ef2c, size: 0x64
    // 0xc4ef2c: EnterFrame
    //     0xc4ef2c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ef30: mov             fp, SP
    // 0xc4ef34: AllocStack(0x10)
    //     0xc4ef34: sub             SP, SP, #0x10
    // 0xc4ef38: SetupParameters(NavigationDecision this /* r1 => r0, fp-0x8 */)
    //     0xc4ef38: mov             x0, x1
    //     0xc4ef3c: stur            x1, [fp, #-8]
    // 0xc4ef40: CheckStackOverflow
    //     0xc4ef40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4ef44: cmp             SP, x16
    //     0xc4ef48: b.ls            #0xc4ef88
    // 0xc4ef4c: r1 = Null
    //     0xc4ef4c: mov             x1, NULL
    // 0xc4ef50: r2 = 4
    //     0xc4ef50: movz            x2, #0x4
    // 0xc4ef54: r0 = AllocateArray()
    //     0xc4ef54: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4ef58: r16 = "NavigationDecision."
    //     0xc4ef58: add             x16, PP, #0x55, lsl #12  ; [pp+0x551a0] "NavigationDecision."
    //     0xc4ef5c: ldr             x16, [x16, #0x1a0]
    // 0xc4ef60: StoreField: r0->field_f = r16
    //     0xc4ef60: stur            w16, [x0, #0xf]
    // 0xc4ef64: ldur            x1, [fp, #-8]
    // 0xc4ef68: LoadField: r2 = r1->field_f
    //     0xc4ef68: ldur            w2, [x1, #0xf]
    // 0xc4ef6c: DecompressPointer r2
    //     0xc4ef6c: add             x2, x2, HEAP, lsl #32
    // 0xc4ef70: StoreField: r0->field_13 = r2
    //     0xc4ef70: stur            w2, [x0, #0x13]
    // 0xc4ef74: str             x0, [SP]
    // 0xc4ef78: r0 = _interpolate()
    //     0xc4ef78: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4ef7c: LeaveFrame
    //     0xc4ef7c: mov             SP, fp
    //     0xc4ef80: ldp             fp, lr, [SP], #0x10
    // 0xc4ef84: ret
    //     0xc4ef84: ret             
    // 0xc4ef88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4ef88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4ef8c: b               #0xc4ef4c
  }
}
