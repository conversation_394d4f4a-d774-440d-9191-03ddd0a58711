// lib: , url: package:webview_flutter_platform_interface/src/types/web_resource_error.dart

// class id: 1051272, size: 0x8
class :: {
}

// class id: 331, size: 0x10, field offset: 0x8
//   const constructor, 
abstract class WebResourceError extends Object {
}

// class id: 6752, size: 0x14, field offset: 0x14
enum WebResourceErrorType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4ef90, size: 0x64
    // 0xc4ef90: EnterFrame
    //     0xc4ef90: stp             fp, lr, [SP, #-0x10]!
    //     0xc4ef94: mov             fp, SP
    // 0xc4ef98: AllocStack(0x10)
    //     0xc4ef98: sub             SP, SP, #0x10
    // 0xc4ef9c: SetupParameters(WebResourceErrorType this /* r1 => r0, fp-0x8 */)
    //     0xc4ef9c: mov             x0, x1
    //     0xc4efa0: stur            x1, [fp, #-8]
    // 0xc4efa4: CheckStackOverflow
    //     0xc4efa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4efa8: cmp             SP, x16
    //     0xc4efac: b.ls            #0xc4efec
    // 0xc4efb0: r1 = Null
    //     0xc4efb0: mov             x1, NULL
    // 0xc4efb4: r2 = 4
    //     0xc4efb4: movz            x2, #0x4
    // 0xc4efb8: r0 = AllocateArray()
    //     0xc4efb8: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4efbc: r16 = "WebResourceErrorType."
    //     0xc4efbc: add             x16, PP, #0x49, lsl #12  ; [pp+0x49108] "WebResourceErrorType."
    //     0xc4efc0: ldr             x16, [x16, #0x108]
    // 0xc4efc4: StoreField: r0->field_f = r16
    //     0xc4efc4: stur            w16, [x0, #0xf]
    // 0xc4efc8: ldur            x1, [fp, #-8]
    // 0xc4efcc: LoadField: r2 = r1->field_f
    //     0xc4efcc: ldur            w2, [x1, #0xf]
    // 0xc4efd0: DecompressPointer r2
    //     0xc4efd0: add             x2, x2, HEAP, lsl #32
    // 0xc4efd4: StoreField: r0->field_13 = r2
    //     0xc4efd4: stur            w2, [x0, #0x13]
    // 0xc4efd8: str             x0, [SP]
    // 0xc4efdc: r0 = _interpolate()
    //     0xc4efdc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4efe0: LeaveFrame
    //     0xc4efe0: mov             SP, fp
    //     0xc4efe4: ldp             fp, lr, [SP], #0x10
    // 0xc4efe8: ret
    //     0xc4efe8: ret             
    // 0xc4efec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4efec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4eff0: b               #0xc4efb0
  }
}
