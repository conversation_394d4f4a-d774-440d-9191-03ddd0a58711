// lib: , url: package:webview_flutter_platform_interface/src/webview_platform.dart

// class id: 1051275, size: 0x8
class :: {
}

// class id: 5849, size: 0x8, field offset: 0x8
abstract class WebViewPlatform extends PlatformInterface {

  static late final Object _token; // offset: 0x9f8

  set _ instance=(/* No info */) {
    // ** addr: 0xec6dd4, size: 0x6c
    // 0xec6dd4: EnterFrame
    //     0xec6dd4: stp             fp, lr, [SP, #-0x10]!
    //     0xec6dd8: mov             fp, SP
    // 0xec6ddc: AllocStack(0x8)
    //     0xec6ddc: sub             SP, SP, #8
    // 0xec6de0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xec6de0: stur            x1, [fp, #-8]
    // 0xec6de4: CheckStackOverflow
    //     0xec6de4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xec6de8: cmp             SP, x16
    //     0xec6dec: b.ls            #0xec6e38
    // 0xec6df0: r0 = InitLateStaticField(0x9f8) // [package:webview_flutter_platform_interface/src/webview_platform.dart] WebViewPlatform::_token
    //     0xec6df0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xec6df4: ldr             x0, [x0, #0x13f0]
    //     0xec6df8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xec6dfc: cmp             w0, w16
    //     0xec6e00: b.ne            #0xec6e10
    //     0xec6e04: add             x2, PP, #0xc, lsl #12  ; [pp+0xc738] Field <WebViewPlatform._token@476513057>: static late final (offset: 0x9f8)
    //     0xec6e08: ldr             x2, [x2, #0x738]
    //     0xec6e0c: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xec6e10: ldur            x1, [fp, #-8]
    // 0xec6e14: mov             x2, x0
    // 0xec6e18: r0 = verify()
    //     0xec6e18: bl              #0x833b94  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0xec6e1c: ldur            x1, [fp, #-8]
    // 0xec6e20: StoreStaticField(0x9fc, r1)
    //     0xec6e20: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0xec6e24: str             x1, [x2, #0x13f8]
    // 0xec6e28: r0 = Null
    //     0xec6e28: mov             x0, NULL
    // 0xec6e2c: LeaveFrame
    //     0xec6e2c: mov             SP, fp
    //     0xec6e30: ldp             fp, lr, [SP], #0x10
    // 0xec6e34: ret
    //     0xec6e34: ret             
    // 0xec6e38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xec6e38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xec6e3c: b               #0xec6df0
  }
}
