// lib: , url: package:workmanager/src/options.dart

// class id: 1051280, size: 0x8
class :: {
}

// class id: 6736, size: 0x14, field offset: 0x14
enum ExistingWorkPolicy extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4eff4, size: 0x64
    // 0xc4eff4: EnterFrame
    //     0xc4eff4: stp             fp, lr, [SP, #-0x10]!
    //     0xc4eff8: mov             fp, SP
    // 0xc4effc: AllocStack(0x10)
    //     0xc4effc: sub             SP, SP, #0x10
    // 0xc4f000: SetupParameters(ExistingWorkPolicy this /* r1 => r0, fp-0x8 */)
    //     0xc4f000: mov             x0, x1
    //     0xc4f004: stur            x1, [fp, #-8]
    // 0xc4f008: CheckStackOverflow
    //     0xc4f008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4f00c: cmp             SP, x16
    //     0xc4f010: b.ls            #0xc4f050
    // 0xc4f014: r1 = Null
    //     0xc4f014: mov             x1, NULL
    // 0xc4f018: r2 = 4
    //     0xc4f018: movz            x2, #0x4
    // 0xc4f01c: r0 = AllocateArray()
    //     0xc4f01c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4f020: r16 = "ExistingWorkPolicy."
    //     0xc4f020: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf60] "ExistingWorkPolicy."
    //     0xc4f024: ldr             x16, [x16, #0xf60]
    // 0xc4f028: StoreField: r0->field_f = r16
    //     0xc4f028: stur            w16, [x0, #0xf]
    // 0xc4f02c: ldur            x1, [fp, #-8]
    // 0xc4f030: LoadField: r2 = r1->field_f
    //     0xc4f030: ldur            w2, [x1, #0xf]
    // 0xc4f034: DecompressPointer r2
    //     0xc4f034: add             x2, x2, HEAP, lsl #32
    // 0xc4f038: StoreField: r0->field_13 = r2
    //     0xc4f038: stur            w2, [x0, #0x13]
    // 0xc4f03c: str             x0, [SP]
    // 0xc4f040: r0 = _interpolate()
    //     0xc4f040: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4f044: LeaveFrame
    //     0xc4f044: mov             SP, fp
    //     0xc4f048: ldp             fp, lr, [SP], #0x10
    // 0xc4f04c: ret
    //     0xc4f04c: ret             
    // 0xc4f050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4f050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4f054: b               #0xc4f014
  }
}
