// lib: , url: package:workmanager/src/workmanager.dart

// class id: 1051281, size: 0x8
class :: {
}

// class id: 274, size: 0x8, field offset: 0x8
abstract class JsonMapperHelper extends Object {

  static _ toRegisterMethodArgument(/* No info */) {
    // ** addr: 0x836e64, size: 0x23c
    // 0x836e64: EnterFrame
    //     0x836e64: stp             fp, lr, [SP, #-0x10]!
    //     0x836e68: mov             fp, SP
    // 0x836e6c: AllocStack(0x38)
    //     0x836e6c: sub             SP, SP, #0x38
    // 0x836e70: SetupParameters(dynamic _ /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, {dynamic frequency = Null /* r4, fp-0x8 */})
    //     0x836e70: mov             x5, x1
    //     0x836e74: mov             x0, x2
    //     0x836e78: stur            x1, [fp, #-0x10]
    //     0x836e7c: stur            x2, [fp, #-0x18]
    //     0x836e80: stur            x3, [fp, #-0x20]
    //     0x836e84: ldur            w1, [x4, #0x13]
    //     0x836e88: ldur            w2, [x4, #0x1f]
    //     0x836e8c: add             x2, x2, HEAP, lsl #32
    //     0x836e90: add             x16, PP, #0xb, lsl #12  ; [pp+0xbee8] "frequency"
    //     0x836e94: ldr             x16, [x16, #0xee8]
    //     0x836e98: cmp             w2, w16
    //     0x836e9c: b.ne            #0x836ebc
    //     0x836ea0: ldur            w2, [x4, #0x23]
    //     0x836ea4: add             x2, x2, HEAP, lsl #32
    //     0x836ea8: sub             w4, w1, w2
    //     0x836eac: add             x1, fp, w4, sxtw #2
    //     0x836eb0: ldr             x1, [x1, #8]
    //     0x836eb4: mov             x4, x1
    //     0x836eb8: b               #0x836ec0
    //     0x836ebc: mov             x4, NULL
    //     0x836ec0: stur            x4, [fp, #-8]
    // 0x836ec4: CheckStackOverflow
    //     0x836ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x836ec8: cmp             SP, x16
    //     0x836ecc: b.ls            #0x837098
    // 0x836ed0: r1 = Null
    //     0x836ed0: mov             x1, NULL
    // 0x836ed4: r2 = 64
    //     0x836ed4: movz            x2, #0x40
    // 0x836ed8: r0 = AllocateArray()
    //     0x836ed8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x836edc: mov             x2, x0
    // 0x836ee0: stur            x2, [fp, #-0x28]
    // 0x836ee4: r16 = "isInDebugMode"
    //     0x836ee4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbef0] "isInDebugMode"
    //     0x836ee8: ldr             x16, [x16, #0xef0]
    // 0x836eec: StoreField: r2->field_f = r16
    //     0x836eec: stur            w16, [x2, #0xf]
    // 0x836ef0: r16 = false
    //     0x836ef0: add             x16, NULL, #0x30  ; false
    // 0x836ef4: StoreField: r2->field_13 = r16
    //     0x836ef4: stur            w16, [x2, #0x13]
    // 0x836ef8: r16 = "uniqueName"
    //     0x836ef8: add             x16, PP, #0xb, lsl #12  ; [pp+0xbef8] "uniqueName"
    //     0x836efc: ldr             x16, [x16, #0xef8]
    // 0x836f00: ArrayStore: r2[0] = r16  ; List_4
    //     0x836f00: stur            w16, [x2, #0x17]
    // 0x836f04: ldur            x0, [fp, #-0x20]
    // 0x836f08: StoreField: r2->field_1b = r0
    //     0x836f08: stur            w0, [x2, #0x1b]
    // 0x836f0c: r16 = "taskName"
    //     0x836f0c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf00] "taskName"
    //     0x836f10: ldr             x16, [x16, #0xf00]
    // 0x836f14: StoreField: r2->field_1f = r16
    //     0x836f14: stur            w16, [x2, #0x1f]
    // 0x836f18: ldur            x0, [fp, #-0x18]
    // 0x836f1c: StoreField: r2->field_23 = r0
    //     0x836f1c: stur            w0, [x2, #0x23]
    // 0x836f20: r16 = "tag"
    //     0x836f20: add             x16, PP, #8, lsl #12  ; [pp+0x8988] "tag"
    //     0x836f24: ldr             x16, [x16, #0x988]
    // 0x836f28: StoreField: r2->field_27 = r16
    //     0x836f28: stur            w16, [x2, #0x27]
    // 0x836f2c: StoreField: r2->field_2b = rNULL
    //     0x836f2c: stur            NULL, [x2, #0x2b]
    // 0x836f30: r16 = "frequency"
    //     0x836f30: add             x16, PP, #0xb, lsl #12  ; [pp+0xbee8] "frequency"
    //     0x836f34: ldr             x16, [x16, #0xee8]
    // 0x836f38: StoreField: r2->field_2f = r16
    //     0x836f38: stur            w16, [x2, #0x2f]
    // 0x836f3c: ldur            x0, [fp, #-8]
    // 0x836f40: cmp             w0, NULL
    // 0x836f44: b.ne            #0x836f50
    // 0x836f48: r0 = Null
    //     0x836f48: mov             x0, NULL
    // 0x836f4c: b               #0x836f74
    // 0x836f50: r1 = 1000000
    //     0x836f50: movz            x1, #0x4240
    //     0x836f54: movk            x1, #0xf, lsl #16
    // 0x836f58: LoadField: r3 = r0->field_7
    //     0x836f58: ldur            x3, [x0, #7]
    // 0x836f5c: sdiv            x4, x3, x1
    // 0x836f60: r0 = BoxInt64Instr(r4)
    //     0x836f60: sbfiz           x0, x4, #1, #0x1f
    //     0x836f64: cmp             x4, x0, asr #1
    //     0x836f68: b.eq            #0x836f74
    //     0x836f6c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x836f70: stur            x4, [x0, #7]
    // 0x836f74: mov             x1, x2
    // 0x836f78: ArrayStore: r1[9] = r0  ; List_4
    //     0x836f78: add             x25, x1, #0x33
    //     0x836f7c: str             w0, [x25]
    //     0x836f80: tbz             w0, #0, #0x836f9c
    //     0x836f84: ldurb           w16, [x1, #-1]
    //     0x836f88: ldurb           w17, [x0, #-1]
    //     0x836f8c: and             x16, x17, x16, lsr #2
    //     0x836f90: tst             x16, HEAP, lsr #32
    //     0x836f94: b.eq            #0x836f9c
    //     0x836f98: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x836f9c: r16 = "existingWorkPolicy"
    //     0x836f9c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf08] "existingWorkPolicy"
    //     0x836fa0: ldr             x16, [x16, #0xf08]
    // 0x836fa4: StoreField: r2->field_37 = r16
    //     0x836fa4: stur            w16, [x2, #0x37]
    // 0x836fa8: ldur            x1, [fp, #-0x10]
    // 0x836fac: r0 = _enumToString()
    //     0x836fac: bl              #0x8370a0  ; [package:workmanager/src/workmanager.dart] JsonMapperHelper::_enumToString
    // 0x836fb0: ldur            x1, [fp, #-0x28]
    // 0x836fb4: ArrayStore: r1[11] = r0  ; List_4
    //     0x836fb4: add             x25, x1, #0x3b
    //     0x836fb8: str             w0, [x25]
    //     0x836fbc: tbz             w0, #0, #0x836fd8
    //     0x836fc0: ldurb           w16, [x1, #-1]
    //     0x836fc4: ldurb           w17, [x0, #-1]
    //     0x836fc8: and             x16, x17, x16, lsr #2
    //     0x836fcc: tst             x16, HEAP, lsr #32
    //     0x836fd0: b.eq            #0x836fd8
    //     0x836fd4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x836fd8: ldur            x0, [fp, #-0x28]
    // 0x836fdc: r16 = "initialDelaySeconds"
    //     0x836fdc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf10] "initialDelaySeconds"
    //     0x836fe0: ldr             x16, [x16, #0xf10]
    // 0x836fe4: StoreField: r0->field_3f = r16
    //     0x836fe4: stur            w16, [x0, #0x3f]
    // 0x836fe8: StoreField: r0->field_43 = rZR
    //     0x836fe8: stur            wzr, [x0, #0x43]
    // 0x836fec: r16 = "networkType"
    //     0x836fec: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf18] "networkType"
    //     0x836ff0: ldr             x16, [x16, #0xf18]
    // 0x836ff4: StoreField: r0->field_47 = r16
    //     0x836ff4: stur            w16, [x0, #0x47]
    // 0x836ff8: StoreField: r0->field_4b = rNULL
    //     0x836ff8: stur            NULL, [x0, #0x4b]
    // 0x836ffc: r16 = "requiresBatteryNotLow"
    //     0x836ffc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf20] "requiresBatteryNotLow"
    //     0x837000: ldr             x16, [x16, #0xf20]
    // 0x837004: StoreField: r0->field_4f = r16
    //     0x837004: stur            w16, [x0, #0x4f]
    // 0x837008: StoreField: r0->field_53 = rNULL
    //     0x837008: stur            NULL, [x0, #0x53]
    // 0x83700c: r16 = "requiresCharging"
    //     0x83700c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf28] "requiresCharging"
    //     0x837010: ldr             x16, [x16, #0xf28]
    // 0x837014: StoreField: r0->field_57 = r16
    //     0x837014: stur            w16, [x0, #0x57]
    // 0x837018: StoreField: r0->field_5b = rNULL
    //     0x837018: stur            NULL, [x0, #0x5b]
    // 0x83701c: r16 = "requiresDeviceIdle"
    //     0x83701c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf30] "requiresDeviceIdle"
    //     0x837020: ldr             x16, [x16, #0xf30]
    // 0x837024: StoreField: r0->field_5f = r16
    //     0x837024: stur            w16, [x0, #0x5f]
    // 0x837028: StoreField: r0->field_63 = rNULL
    //     0x837028: stur            NULL, [x0, #0x63]
    // 0x83702c: r16 = "requiresStorageNotLow"
    //     0x83702c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf38] "requiresStorageNotLow"
    //     0x837030: ldr             x16, [x16, #0xf38]
    // 0x837034: StoreField: r0->field_67 = r16
    //     0x837034: stur            w16, [x0, #0x67]
    // 0x837038: StoreField: r0->field_6b = rNULL
    //     0x837038: stur            NULL, [x0, #0x6b]
    // 0x83703c: r16 = "backoffPolicyType"
    //     0x83703c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf40] "backoffPolicyType"
    //     0x837040: ldr             x16, [x16, #0xf40]
    // 0x837044: StoreField: r0->field_6f = r16
    //     0x837044: stur            w16, [x0, #0x6f]
    // 0x837048: StoreField: r0->field_73 = rNULL
    //     0x837048: stur            NULL, [x0, #0x73]
    // 0x83704c: r16 = "backoffDelayInMilliseconds"
    //     0x83704c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf48] "backoffDelayInMilliseconds"
    //     0x837050: ldr             x16, [x16, #0xf48]
    // 0x837054: StoreField: r0->field_77 = r16
    //     0x837054: stur            w16, [x0, #0x77]
    // 0x837058: StoreField: r0->field_7b = rZR
    //     0x837058: stur            wzr, [x0, #0x7b]
    // 0x83705c: r16 = "outOfQuotaPolicy"
    //     0x83705c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf50] "outOfQuotaPolicy"
    //     0x837060: ldr             x16, [x16, #0xf50]
    // 0x837064: StoreField: r0->field_7f = r16
    //     0x837064: stur            w16, [x0, #0x7f]
    // 0x837068: StoreField: r0->field_83 = rNULL
    //     0x837068: stur            NULL, [x0, #0x83]
    // 0x83706c: r16 = "inputData"
    //     0x83706c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf58] "inputData"
    //     0x837070: ldr             x16, [x16, #0xf58]
    // 0x837074: StoreField: r0->field_87 = r16
    //     0x837074: stur            w16, [x0, #0x87]
    // 0x837078: StoreField: r0->field_8b = rNULL
    //     0x837078: stur            NULL, [x0, #0x8b]
    // 0x83707c: r16 = <String, Object?>
    //     0x83707c: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0x837080: ldr             x16, [x16, #0x738]
    // 0x837084: stp             x0, x16, [SP]
    // 0x837088: r0 = Map._fromLiteral()
    //     0x837088: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x83708c: LeaveFrame
    //     0x83708c: mov             SP, fp
    //     0x837090: ldp             fp, lr, [SP], #0x10
    // 0x837094: ret
    //     0x837094: ret             
    // 0x837098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x837098: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83709c: b               #0x836ed0
  }
  static _ _enumToString(/* No info */) {
    // ** addr: 0x8370a0, size: 0xa0
    // 0x8370a0: EnterFrame
    //     0x8370a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8370a4: mov             fp, SP
    // 0x8370a8: AllocStack(0x10)
    //     0x8370a8: sub             SP, SP, #0x10
    // 0x8370ac: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x8370ac: mov             x0, x1
    //     0x8370b0: stur            x1, [fp, #-8]
    // 0x8370b4: CheckStackOverflow
    //     0x8370b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8370b8: cmp             SP, x16
    //     0x8370bc: b.ls            #0x837138
    // 0x8370c0: cmp             w0, NULL
    // 0x8370c4: b.ne            #0x8370d0
    // 0x8370c8: r0 = Null
    //     0x8370c8: mov             x0, NULL
    // 0x8370cc: b               #0x83712c
    // 0x8370d0: r1 = Null
    //     0x8370d0: mov             x1, NULL
    // 0x8370d4: r2 = 4
    //     0x8370d4: movz            x2, #0x4
    // 0x8370d8: r0 = AllocateArray()
    //     0x8370d8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x8370dc: r16 = "ExistingWorkPolicy."
    //     0x8370dc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf60] "ExistingWorkPolicy."
    //     0x8370e0: ldr             x16, [x16, #0xf60]
    // 0x8370e4: StoreField: r0->field_f = r16
    //     0x8370e4: stur            w16, [x0, #0xf]
    // 0x8370e8: ldur            x1, [fp, #-8]
    // 0x8370ec: LoadField: r2 = r1->field_f
    //     0x8370ec: ldur            w2, [x1, #0xf]
    // 0x8370f0: DecompressPointer r2
    //     0x8370f0: add             x2, x2, HEAP, lsl #32
    // 0x8370f4: StoreField: r0->field_13 = r2
    //     0x8370f4: stur            w2, [x0, #0x13]
    // 0x8370f8: str             x0, [SP]
    // 0x8370fc: r0 = _interpolate()
    //     0x8370fc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x837100: r1 = LoadClassIdInstr(r0)
    //     0x837100: ldur            x1, [x0, #-1]
    //     0x837104: ubfx            x1, x1, #0xc, #0x14
    // 0x837108: mov             x16, x0
    // 0x83710c: mov             x0, x1
    // 0x837110: mov             x1, x16
    // 0x837114: r2 = "."
    //     0x837114: ldr             x2, [PP, #0xb30]  ; [pp+0xb30] "."
    // 0x837118: r0 = GDT[cid_x0 + -0x1000]()
    //     0x837118: sub             lr, x0, #1, lsl #12
    //     0x83711c: ldr             lr, [x21, lr, lsl #3]
    //     0x837120: blr             lr
    // 0x837124: mov             x1, x0
    // 0x837128: r0 = last()
    //     0x837128: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x83712c: LeaveFrame
    //     0x83712c: mov             SP, fp
    //     0x837130: ldp             fp, lr, [SP], #0x10
    // 0x837134: ret
    //     0x837134: ret             
    // 0x837138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x837138: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83713c: b               #0x8370c0
  }
  static _ toInitializeMethodArgument(/* No info */) {
    // ** addr: 0x83732c, size: 0x90
    // 0x83732c: EnterFrame
    //     0x83732c: stp             fp, lr, [SP, #-0x10]!
    //     0x837330: mov             fp, SP
    // 0x837334: AllocStack(0x18)
    //     0x837334: sub             SP, SP, #0x18
    // 0x837338: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x837338: mov             x0, x1
    //     0x83733c: stur            x1, [fp, #-8]
    // 0x837340: CheckStackOverflow
    //     0x837340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x837344: cmp             SP, x16
    //     0x837348: b.ls            #0x8373b4
    // 0x83734c: r1 = Null
    //     0x83734c: mov             x1, NULL
    // 0x837350: r2 = 8
    //     0x837350: movz            x2, #0x8
    // 0x837354: r0 = AllocateArray()
    //     0x837354: bl              #0xec22fc  ; AllocateArrayStub
    // 0x837358: mov             x2, x0
    // 0x83735c: r16 = "isInDebugMode"
    //     0x83735c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbef0] "isInDebugMode"
    //     0x837360: ldr             x16, [x16, #0xef0]
    // 0x837364: StoreField: r2->field_f = r16
    //     0x837364: stur            w16, [x2, #0xf]
    // 0x837368: r16 = false
    //     0x837368: add             x16, NULL, #0x30  ; false
    // 0x83736c: StoreField: r2->field_13 = r16
    //     0x83736c: stur            w16, [x2, #0x13]
    // 0x837370: r16 = "callbackHandle"
    //     0x837370: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf98] "callbackHandle"
    //     0x837374: ldr             x16, [x16, #0xf98]
    // 0x837378: ArrayStore: r2[0] = r16  ; List_4
    //     0x837378: stur            w16, [x2, #0x17]
    // 0x83737c: ldur            x3, [fp, #-8]
    // 0x837380: r0 = BoxInt64Instr(r3)
    //     0x837380: sbfiz           x0, x3, #1, #0x1f
    //     0x837384: cmp             x3, x0, asr #1
    //     0x837388: b.eq            #0x837394
    //     0x83738c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x837390: stur            x3, [x0, #7]
    // 0x837394: StoreField: r2->field_1b = r0
    //     0x837394: stur            w0, [x2, #0x1b]
    // 0x837398: r16 = <String, Object?>
    //     0x837398: add             x16, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <String, Object?>
    //     0x83739c: ldr             x16, [x16, #0x738]
    // 0x8373a0: stp             x2, x16, [SP]
    // 0x8373a4: r0 = Map._fromLiteral()
    //     0x8373a4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8373a8: LeaveFrame
    //     0x8373a8: mov             SP, fp
    //     0x8373ac: ldp             fp, lr, [SP], #0x10
    // 0x8373b0: ret
    //     0x8373b0: ret             
    // 0x8373b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8373b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8373b8: b               #0x83734c
  }
}

// class id: 275, size: 0x10, field offset: 0x8
class Workmanager extends Object {

  static late final Workmanager _instance; // offset: 0x16ac

  _ executeTask(/* No info */) {
    // ** addr: 0x817b8c, size: 0x94
    // 0x817b8c: EnterFrame
    //     0x817b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x817b90: mov             fp, SP
    // 0x817b94: AllocStack(0x28)
    //     0x817b94: sub             SP, SP, #0x28
    // 0x817b98: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x817b98: stur            x2, [fp, #-8]
    // 0x817b9c: CheckStackOverflow
    //     0x817b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x817ba0: cmp             SP, x16
    //     0x817ba4: b.ls            #0x817c18
    // 0x817ba8: r1 = 1
    //     0x817ba8: movz            x1, #0x1
    // 0x817bac: r0 = AllocateContext()
    //     0x817bac: bl              #0xec126c  ; AllocateContextStub
    // 0x817bb0: mov             x1, x0
    // 0x817bb4: ldur            x0, [fp, #-8]
    // 0x817bb8: stur            x1, [fp, #-0x10]
    // 0x817bbc: StoreField: r1->field_f = r0
    //     0x817bbc: stur            w0, [x1, #0xf]
    // 0x817bc0: r0 = ensureInitialized()
    //     0x817bc0: bl              #0x6916f4  ; [package:flutter/src/widgets/binding.dart] WidgetsFlutterBinding::ensureInitialized
    // 0x817bc4: r0 = ensureInitialized()
    //     0x817bc4: bl              #0x817c20  ; [dart:ui] DartPluginRegistrant::ensureInitialized
    // 0x817bc8: ldur            x2, [fp, #-0x10]
    // 0x817bcc: r1 = Function '<anonymous closure>':.
    //     0x817bcc: add             x1, PP, #0xb, lsl #12  ; [pp+0xbe18] AnonymousClosure: (0x817d14), in [package:workmanager/src/workmanager.dart] Workmanager::executeTask (0x817b8c)
    //     0x817bd0: ldr             x1, [x1, #0xe18]
    // 0x817bd4: r0 = AllocateClosure()
    //     0x817bd4: bl              #0xec1630  ; AllocateClosureStub
    // 0x817bd8: mov             x2, x0
    // 0x817bdc: r1 = Instance_MethodChannel
    //     0x817bdc: add             x1, PP, #0xb, lsl #12  ; [pp+0xbe20] Obj!MethodChannel@e11091
    //     0x817be0: ldr             x1, [x1, #0xe20]
    // 0x817be4: r0 = setMethodCallHandler()
    //     0x817be4: bl              #0x6921f4  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::setMethodCallHandler
    // 0x817be8: r16 = Instance_MethodChannel
    //     0x817be8: add             x16, PP, #0xb, lsl #12  ; [pp+0xbe20] Obj!MethodChannel@e11091
    //     0x817bec: ldr             x16, [x16, #0xe20]
    // 0x817bf0: stp             x16, NULL, [SP, #8]
    // 0x817bf4: r16 = "backgroundChannelInitialized"
    //     0x817bf4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbe28] "backgroundChannelInitialized"
    //     0x817bf8: ldr             x16, [x16, #0xe28]
    // 0x817bfc: str             x16, [SP]
    // 0x817c00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x817c00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x817c04: r0 = invokeMethod()
    //     0x817c04: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x817c08: r0 = Null
    //     0x817c08: mov             x0, NULL
    // 0x817c0c: LeaveFrame
    //     0x817c0c: mov             SP, fp
    //     0x817c10: ldp             fp, lr, [SP], #0x10
    // 0x817c14: ret
    //     0x817c14: ret             
    // 0x817c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x817c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x817c1c: b               #0x817ba8
  }
  [closure] Future<bool> <anonymous closure>(dynamic, MethodCall) async {
    // ** addr: 0x817d14, size: 0x1a0
    // 0x817d14: EnterFrame
    //     0x817d14: stp             fp, lr, [SP, #-0x10]!
    //     0x817d18: mov             fp, SP
    // 0x817d1c: AllocStack(0x40)
    //     0x817d1c: sub             SP, SP, #0x40
    // 0x817d20: SetupParameters(Workmanager this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x817d20: stur            NULL, [fp, #-8]
    //     0x817d24: movz            x0, #0
    //     0x817d28: add             x1, fp, w0, sxtw #2
    //     0x817d2c: ldr             x1, [x1, #0x18]
    //     0x817d30: add             x2, fp, w0, sxtw #2
    //     0x817d34: ldr             x2, [x2, #0x10]
    //     0x817d38: stur            x2, [fp, #-0x18]
    //     0x817d3c: ldur            w3, [x1, #0x17]
    //     0x817d40: add             x3, x3, HEAP, lsl #32
    //     0x817d44: stur            x3, [fp, #-0x10]
    // 0x817d48: CheckStackOverflow
    //     0x817d48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x817d4c: cmp             SP, x16
    //     0x817d50: b.ls            #0x817eac
    // 0x817d54: InitAsync() -> Future<bool>
    //     0x817d54: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <bool>
    //     0x817d58: bl              #0x661298  ; InitAsyncStub
    // 0x817d5c: ldur            x0, [fp, #-0x18]
    // 0x817d60: LoadField: r1 = r0->field_b
    //     0x817d60: ldur            w1, [x0, #0xb]
    // 0x817d64: DecompressPointer r1
    //     0x817d64: add             x1, x1, HEAP, lsl #32
    // 0x817d68: stur            x1, [fp, #-0x20]
    // 0x817d6c: r16 = "be.tramckrijte.workmanager.INPUT_DATA"
    //     0x817d6c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbe30] "be.tramckrijte.workmanager.INPUT_DATA"
    //     0x817d70: ldr             x16, [x16, #0xe30]
    // 0x817d74: stp             x16, x1, [SP]
    // 0x817d78: r4 = 0
    //     0x817d78: movz            x4, #0
    // 0x817d7c: ldr             x0, [SP, #8]
    // 0x817d80: r16 = UnlinkedCall_0x5f3c08
    //     0x817d80: add             x16, PP, #0xb, lsl #12  ; [pp+0xbe38] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x817d84: add             x16, x16, #0xe38
    // 0x817d88: ldp             x5, lr, [x16]
    // 0x817d8c: blr             lr
    // 0x817d90: mov             x1, x0
    // 0x817d94: ldur            x0, [fp, #-0x10]
    // 0x817d98: stur            x1, [fp, #-0x28]
    // 0x817d9c: LoadField: r2 = r0->field_f
    //     0x817d9c: ldur            w2, [x0, #0xf]
    // 0x817da0: DecompressPointer r2
    //     0x817da0: add             x2, x2, HEAP, lsl #32
    // 0x817da4: stur            x2, [fp, #-0x18]
    // 0x817da8: ldur            x16, [fp, #-0x20]
    // 0x817dac: r30 = "be.tramckrijte.workmanager.DART_TASK"
    //     0x817dac: add             lr, PP, #0xb, lsl #12  ; [pp+0xbe48] "be.tramckrijte.workmanager.DART_TASK"
    //     0x817db0: ldr             lr, [lr, #0xe48]
    // 0x817db4: stp             lr, x16, [SP]
    // 0x817db8: r4 = 0
    //     0x817db8: movz            x4, #0
    // 0x817dbc: ldr             x0, [SP, #8]
    // 0x817dc0: r16 = UnlinkedCall_0x5f3c08
    //     0x817dc0: add             x16, PP, #0xb, lsl #12  ; [pp+0xbe50] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x817dc4: add             x16, x16, #0xe50
    // 0x817dc8: ldp             x5, lr, [x16]
    // 0x817dcc: blr             lr
    // 0x817dd0: mov             x3, x0
    // 0x817dd4: r2 = Null
    //     0x817dd4: mov             x2, NULL
    // 0x817dd8: r1 = Null
    //     0x817dd8: mov             x1, NULL
    // 0x817ddc: stur            x3, [fp, #-0x10]
    // 0x817de0: r4 = 60
    //     0x817de0: movz            x4, #0x3c
    // 0x817de4: branchIfSmi(r0, 0x817df0)
    //     0x817de4: tbz             w0, #0, #0x817df0
    // 0x817de8: r4 = LoadClassIdInstr(r0)
    //     0x817de8: ldur            x4, [x0, #-1]
    //     0x817dec: ubfx            x4, x4, #0xc, #0x14
    // 0x817df0: sub             x4, x4, #0x5e
    // 0x817df4: cmp             x4, #1
    // 0x817df8: b.ls            #0x817e0c
    // 0x817dfc: r8 = String
    //     0x817dfc: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x817e00: r3 = Null
    //     0x817e00: add             x3, PP, #0xb, lsl #12  ; [pp+0xbe60] Null
    //     0x817e04: ldr             x3, [x3, #0xe60]
    // 0x817e08: r0 = String()
    //     0x817e08: bl              #0xed43b0  ; IsType_String_Stub
    // 0x817e0c: ldur            x3, [fp, #-0x28]
    // 0x817e10: cmp             w3, NULL
    // 0x817e14: b.ne            #0x817e20
    // 0x817e18: r3 = Null
    //     0x817e18: mov             x3, NULL
    // 0x817e1c: b               #0x817e64
    // 0x817e20: mov             x0, x3
    // 0x817e24: r2 = Null
    //     0x817e24: mov             x2, NULL
    // 0x817e28: r1 = Null
    //     0x817e28: mov             x1, NULL
    // 0x817e2c: r4 = 60
    //     0x817e2c: movz            x4, #0x3c
    // 0x817e30: branchIfSmi(r0, 0x817e3c)
    //     0x817e30: tbz             w0, #0, #0x817e3c
    // 0x817e34: r4 = LoadClassIdInstr(r0)
    //     0x817e34: ldur            x4, [x0, #-1]
    //     0x817e38: ubfx            x4, x4, #0xc, #0x14
    // 0x817e3c: sub             x4, x4, #0x5e
    // 0x817e40: cmp             x4, #1
    // 0x817e44: b.ls            #0x817e58
    // 0x817e48: r8 = String
    //     0x817e48: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x817e4c: r3 = Null
    //     0x817e4c: add             x3, PP, #0xb, lsl #12  ; [pp+0xbe70] Null
    //     0x817e50: ldr             x3, [x3, #0xe70]
    // 0x817e54: r0 = String()
    //     0x817e54: bl              #0xed43b0  ; IsType_String_Stub
    // 0x817e58: ldur            x1, [fp, #-0x28]
    // 0x817e5c: r0 = jsonDecode()
    //     0x817e5c: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x817e60: mov             x3, x0
    // 0x817e64: mov             x0, x3
    // 0x817e68: stur            x3, [fp, #-0x20]
    // 0x817e6c: r2 = Null
    //     0x817e6c: mov             x2, NULL
    // 0x817e70: r1 = Null
    //     0x817e70: mov             x1, NULL
    // 0x817e74: r8 = Map<String, dynamic>?
    //     0x817e74: ldr             x8, [PP, #0x258]  ; [pp+0x258] Type: Map<String, dynamic>?
    // 0x817e78: r3 = Null
    //     0x817e78: add             x3, PP, #0xb, lsl #12  ; [pp+0xbe80] Null
    //     0x817e7c: ldr             x3, [x3, #0xe80]
    // 0x817e80: r0 = Map<String, dynamic>?()
    //     0x817e80: bl              #0x6b2838  ; IsType_Map<String, dynamic>?_Stub
    // 0x817e84: ldur            x16, [fp, #-0x18]
    // 0x817e88: ldur            lr, [fp, #-0x10]
    // 0x817e8c: stp             lr, x16, [SP, #8]
    // 0x817e90: ldur            x16, [fp, #-0x20]
    // 0x817e94: str             x16, [SP]
    // 0x817e98: ldur            x0, [fp, #-0x18]
    // 0x817e9c: ClosureCall
    //     0x817e9c: ldr             x4, [PP, #0xa30]  ; [pp+0xa30] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x817ea0: ldur            x2, [x0, #0x1f]
    //     0x817ea4: blr             x2
    // 0x817ea8: r0 = ReturnAsync()
    //     0x817ea8: b               #0x6576a4  ; ReturnAsyncStub
    // 0x817eac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x817eac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x817eb0: b               #0x817d54
  }
  static Workmanager _instance() {
    // ** addr: 0x836c8c, size: 0x30
    // 0x836c8c: EnterFrame
    //     0x836c8c: stp             fp, lr, [SP, #-0x10]!
    //     0x836c90: mov             fp, SP
    // 0x836c94: r0 = Workmanager()
    //     0x836c94: bl              #0x836cbc  ; AllocateWorkmanagerStub -> Workmanager (size=0x10)
    // 0x836c98: r1 = Instance_MethodChannel
    //     0x836c98: add             x1, PP, #0xb, lsl #12  ; [pp+0xbe20] Obj!MethodChannel@e11091
    //     0x836c9c: ldr             x1, [x1, #0xe20]
    // 0x836ca0: StoreField: r0->field_7 = r1
    //     0x836ca0: stur            w1, [x0, #7]
    // 0x836ca4: r1 = Instance_MethodChannel
    //     0x836ca4: add             x1, PP, #0xb, lsl #12  ; [pp+0xbea8] Obj!MethodChannel@e110f1
    //     0x836ca8: ldr             x1, [x1, #0xea8]
    // 0x836cac: StoreField: r0->field_b = r1
    //     0x836cac: stur            w1, [x0, #0xb]
    // 0x836cb0: LeaveFrame
    //     0x836cb0: mov             SP, fp
    //     0x836cb4: ldp             fp, lr, [SP], #0x10
    // 0x836cb8: ret
    //     0x836cb8: ret             
  }
  _ registerPeriodicTask(/* No info */) async {
    // ** addr: 0x836dd4, size: 0x90
    // 0x836dd4: EnterFrame
    //     0x836dd4: stp             fp, lr, [SP, #-0x10]!
    //     0x836dd8: mov             fp, SP
    // 0x836ddc: AllocStack(0x30)
    //     0x836ddc: sub             SP, SP, #0x30
    // 0x836de0: SetupParameters(Workmanager this /* r1 => r1, fp-0x10 */)
    //     0x836de0: stur            NULL, [fp, #-8]
    //     0x836de4: stur            x1, [fp, #-0x10]
    // 0x836de8: CheckStackOverflow
    //     0x836de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x836dec: cmp             SP, x16
    //     0x836df0: b.ls            #0x836e5c
    // 0x836df4: InitAsync() -> Future<void?>
    //     0x836df4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x836df8: bl              #0x661298  ; InitAsyncStub
    // 0x836dfc: r16 = Instance_Duration
    //     0x836dfc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbec0] Obj!Duration@e3a151
    //     0x836e00: ldr             x16, [x16, #0xec0]
    // 0x836e04: str             x16, [SP]
    // 0x836e08: r1 = Null
    //     0x836e08: mov             x1, NULL
    // 0x836e0c: r2 = "Reschedule Adzan Notification"
    //     0x836e0c: add             x2, PP, #0xb, lsl #12  ; [pp+0xbec8] "Reschedule Adzan Notification"
    //     0x836e10: ldr             x2, [x2, #0xec8]
    // 0x836e14: r3 = "reschedule-adzan-notification"
    //     0x836e14: add             x3, PP, #0xb, lsl #12  ; [pp+0xbed0] "reschedule-adzan-notification"
    //     0x836e18: ldr             x3, [x3, #0xed0]
    // 0x836e1c: r4 = const [0, 0x4, 0x1, 0x3, frequency, 0x3, null]
    //     0x836e1c: add             x4, PP, #0xb, lsl #12  ; [pp+0xbed8] List(7) [0, 0x4, 0x1, 0x3, "frequency", 0x3, Null]
    //     0x836e20: ldr             x4, [x4, #0xed8]
    // 0x836e24: r0 = toRegisterMethodArgument()
    //     0x836e24: bl              #0x836e64  ; [package:workmanager/src/workmanager.dart] JsonMapperHelper::toRegisterMethodArgument
    // 0x836e28: r16 = <void?>
    //     0x836e28: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x836e2c: r30 = Instance_MethodChannel
    //     0x836e2c: add             lr, PP, #0xb, lsl #12  ; [pp+0xbea8] Obj!MethodChannel@e110f1
    //     0x836e30: ldr             lr, [lr, #0xea8]
    // 0x836e34: stp             lr, x16, [SP, #0x10]
    // 0x836e38: r16 = "registerPeriodicTask"
    //     0x836e38: add             x16, PP, #0xb, lsl #12  ; [pp+0xbee0] "registerPeriodicTask"
    //     0x836e3c: ldr             x16, [x16, #0xee0]
    // 0x836e40: stp             x0, x16, [SP]
    // 0x836e44: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x836e44: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x836e48: r0 = invokeMethod()
    //     0x836e48: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x836e4c: mov             x1, x0
    // 0x836e50: stur            x1, [fp, #-0x10]
    // 0x836e54: r0 = Await()
    //     0x836e54: bl              #0x661044  ; AwaitStub
    // 0x836e58: r0 = ReturnAsync()
    //     0x836e58: b               #0x6576a4  ; ReturnAsyncStub
    // 0x836e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x836e5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x836e60: b               #0x836df4
  }
  _ registerOneOffTask(/* No info */) async {
    // ** addr: 0x837140, size: 0x84
    // 0x837140: EnterFrame
    //     0x837140: stp             fp, lr, [SP, #-0x10]!
    //     0x837144: mov             fp, SP
    // 0x837148: AllocStack(0x30)
    //     0x837148: sub             SP, SP, #0x30
    // 0x83714c: SetupParameters(Workmanager this /* r1 => r1, fp-0x10 */)
    //     0x83714c: stur            NULL, [fp, #-8]
    //     0x837150: stur            x1, [fp, #-0x10]
    // 0x837154: CheckStackOverflow
    //     0x837154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x837158: cmp             SP, x16
    //     0x83715c: b.ls            #0x8371bc
    // 0x837160: InitAsync() -> Future<void?>
    //     0x837160: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x837164: bl              #0x661298  ; InitAsyncStub
    // 0x837168: r1 = Instance_ExistingWorkPolicy
    //     0x837168: add             x1, PP, #0xb, lsl #12  ; [pp+0xbf68] Obj!ExistingWorkPolicy@e2d4f1
    //     0x83716c: ldr             x1, [x1, #0xf68]
    // 0x837170: r2 = "Reschedule Adzan Notification Once"
    //     0x837170: add             x2, PP, #0xb, lsl #12  ; [pp+0xbf70] "Reschedule Adzan Notification Once"
    //     0x837174: ldr             x2, [x2, #0xf70]
    // 0x837178: r3 = "reschedule-adzan-notification-once"
    //     0x837178: add             x3, PP, #0xb, lsl #12  ; [pp+0xbf78] "reschedule-adzan-notification-once"
    //     0x83717c: ldr             x3, [x3, #0xf78]
    // 0x837180: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x837180: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x837184: r0 = toRegisterMethodArgument()
    //     0x837184: bl              #0x836e64  ; [package:workmanager/src/workmanager.dart] JsonMapperHelper::toRegisterMethodArgument
    // 0x837188: r16 = <void?>
    //     0x837188: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x83718c: r30 = Instance_MethodChannel
    //     0x83718c: add             lr, PP, #0xb, lsl #12  ; [pp+0xbea8] Obj!MethodChannel@e110f1
    //     0x837190: ldr             lr, [lr, #0xea8]
    // 0x837194: stp             lr, x16, [SP, #0x10]
    // 0x837198: r16 = "registerOneOffTask"
    //     0x837198: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf80] "registerOneOffTask"
    //     0x83719c: ldr             x16, [x16, #0xf80]
    // 0x8371a0: stp             x0, x16, [SP]
    // 0x8371a4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x8371a4: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x8371a8: r0 = invokeMethod()
    //     0x8371a8: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x8371ac: mov             x1, x0
    // 0x8371b0: stur            x1, [fp, #-0x10]
    // 0x8371b4: r0 = Await()
    //     0x8371b4: bl              #0x661044  ; AwaitStub
    // 0x8371b8: r0 = ReturnAsync()
    //     0x8371b8: b               #0x6576a4  ; ReturnAsyncStub
    // 0x8371bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8371bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8371c0: b               #0x837160
  }
  _ initialize(/* No info */) async {
    // ** addr: 0x83729c, size: 0x90
    // 0x83729c: EnterFrame
    //     0x83729c: stp             fp, lr, [SP, #-0x10]!
    //     0x8372a0: mov             fp, SP
    // 0x8372a4: AllocStack(0x30)
    //     0x8372a4: sub             SP, SP, #0x30
    // 0x8372a8: SetupParameters(Workmanager this /* r1 => r1, fp-0x10 */)
    //     0x8372a8: stur            NULL, [fp, #-8]
    //     0x8372ac: stur            x1, [fp, #-0x10]
    // 0x8372b0: CheckStackOverflow
    //     0x8372b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8372b4: cmp             SP, x16
    //     0x8372b8: b.ls            #0x837324
    // 0x8372bc: InitAsync() -> Future<void?>
    //     0x8372bc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x8372c0: bl              #0x661298  ; InitAsyncStub
    // 0x8372c4: r0 = false
    //     0x8372c4: add             x0, NULL, #0x30  ; false
    // 0x8372c8: StoreStaticField(0x16b0, r0)
    //     0x8372c8: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x8372cc: str             x0, [x1, #0x2d60]
    // 0x8372d0: r1 = Closure: () => void from Function 'backgroundNotificationCallbackDispatcher': static.
    //     0x8372d0: add             x1, PP, #0xb, lsl #12  ; [pp+0xbf88] Closure: () => void from Function 'backgroundNotificationCallbackDispatcher': static. (0x7e54fb236cc8)
    //     0x8372d4: ldr             x1, [x1, #0xf88]
    // 0x8372d8: r0 = getCallbackHandle()
    //     0x8372d8: bl              #0x8373bc  ; [dart:ui] PluginUtilities::getCallbackHandle
    // 0x8372dc: cmp             w0, NULL
    // 0x8372e0: b.eq            #0x83731c
    // 0x8372e4: LoadField: r1 = r0->field_7
    //     0x8372e4: ldur            x1, [x0, #7]
    // 0x8372e8: r0 = toInitializeMethodArgument()
    //     0x8372e8: bl              #0x83732c  ; [package:workmanager/src/workmanager.dart] JsonMapperHelper::toInitializeMethodArgument
    // 0x8372ec: r16 = <void?>
    //     0x8372ec: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x8372f0: r30 = Instance_MethodChannel
    //     0x8372f0: add             lr, PP, #0xb, lsl #12  ; [pp+0xbea8] Obj!MethodChannel@e110f1
    //     0x8372f4: ldr             lr, [lr, #0xea8]
    // 0x8372f8: stp             lr, x16, [SP, #0x10]
    // 0x8372fc: r16 = "initialize"
    //     0x8372fc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbf90] "initialize"
    //     0x837300: ldr             x16, [x16, #0xf90]
    // 0x837304: stp             x0, x16, [SP]
    // 0x837308: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x837308: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x83730c: r0 = invokeMethod()
    //     0x83730c: bl              #0xdb4294  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x837310: mov             x1, x0
    // 0x837314: stur            x1, [fp, #-0x10]
    // 0x837318: r0 = Await()
    //     0x837318: bl              #0x661044  ; AwaitStub
    // 0x83731c: r0 = Null
    //     0x83731c: mov             x0, NULL
    // 0x837320: r0 = ReturnAsyncNotFuture()
    //     0x837320: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x837324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x837324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x837328: b               #0x8372bc
  }
}
