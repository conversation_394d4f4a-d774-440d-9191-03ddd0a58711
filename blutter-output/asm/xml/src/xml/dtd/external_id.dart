// lib: , url: package:xml/src/xml/dtd/external_id.dart

// class id: 1051282, size: 0x8
class :: {
}

// class id: 273, size: 0x18, field offset: 0x8
class DtdExternalId extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf41f8, size: 0x5c
    // 0xbf41f8: EnterFrame
    //     0xbf41f8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf41fc: mov             fp, SP
    // 0xbf4200: CheckStackOverflow
    //     0xbf4200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4204: cmp             SP, x16
    //     0xbf4208: b.ls            #0xbf424c
    // 0xbf420c: ldr             x0, [fp, #0x10]
    // 0xbf4210: LoadField: r1 = r0->field_f
    //     0xbf4210: ldur            w1, [x0, #0xf]
    // 0xbf4214: DecompressPointer r1
    //     0xbf4214: add             x1, x1, HEAP, lsl #32
    // 0xbf4218: LoadField: r2 = r0->field_7
    //     0xbf4218: ldur            w2, [x0, #7]
    // 0xbf421c: DecompressPointer r2
    //     0xbf421c: add             x2, x2, HEAP, lsl #32
    // 0xbf4220: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf4220: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf4224: r0 = hash()
    //     0xbf4224: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4228: mov             x2, x0
    // 0xbf422c: r0 = BoxInt64Instr(r2)
    //     0xbf422c: sbfiz           x0, x2, #1, #0x1f
    //     0xbf4230: cmp             x2, x0, asr #1
    //     0xbf4234: b.eq            #0xbf4240
    //     0xbf4238: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf423c: stur            x2, [x0, #7]
    // 0xbf4240: LeaveFrame
    //     0xbf4240: mov             SP, fp
    //     0xbf4244: ldp             fp, lr, [SP], #0x10
    // 0xbf4248: ret
    //     0xbf4248: ret             
    // 0xbf424c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf424c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4250: b               #0xbf420c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4345c, size: 0x130
    // 0xc4345c: EnterFrame
    //     0xc4345c: stp             fp, lr, [SP, #-0x10]!
    //     0xc43460: mov             fp, SP
    // 0xc43464: AllocStack(0x20)
    //     0xc43464: sub             SP, SP, #0x20
    // 0xc43468: CheckStackOverflow
    //     0xc43468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4346c: cmp             SP, x16
    //     0xc43470: b.ls            #0xc43580
    // 0xc43474: r0 = StringBuffer()
    //     0xc43474: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc43478: mov             x1, x0
    // 0xc4347c: stur            x0, [fp, #-8]
    // 0xc43480: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc43480: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc43484: r0 = StringBuffer()
    //     0xc43484: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc43488: ldr             x0, [fp, #0x10]
    // 0xc4348c: LoadField: r3 = r0->field_7
    //     0xc4348c: ldur            w3, [x0, #7]
    // 0xc43490: DecompressPointer r3
    //     0xc43490: add             x3, x3, HEAP, lsl #32
    // 0xc43494: stur            x3, [fp, #-0x10]
    // 0xc43498: cmp             w3, NULL
    // 0xc4349c: b.eq            #0xc43504
    // 0xc434a0: ldur            x1, [fp, #-8]
    // 0xc434a4: r2 = "PUBLIC"
    //     0xc434a4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26818] "PUBLIC"
    //     0xc434a8: ldr             x2, [x2, #0x818]
    // 0xc434ac: r0 = write()
    //     0xc434ac: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc434b0: ldur            x1, [fp, #-8]
    // 0xc434b4: r2 = " "
    //     0xc434b4: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc434b8: r0 = write()
    //     0xc434b8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc434bc: ldr             x0, [fp, #0x10]
    // 0xc434c0: LoadField: r1 = r0->field_b
    //     0xc434c0: ldur            w1, [x0, #0xb]
    // 0xc434c4: DecompressPointer r1
    //     0xc434c4: add             x1, x1, HEAP, lsl #32
    // 0xc434c8: cmp             w1, NULL
    // 0xc434cc: b.eq            #0xc43588
    // 0xc434d0: LoadField: r3 = r1->field_13
    //     0xc434d0: ldur            w3, [x1, #0x13]
    // 0xc434d4: DecompressPointer r3
    //     0xc434d4: add             x3, x3, HEAP, lsl #32
    // 0xc434d8: ldur            x1, [fp, #-8]
    // 0xc434dc: mov             x2, x3
    // 0xc434e0: stur            x3, [fp, #-0x18]
    // 0xc434e4: r0 = write()
    //     0xc434e4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc434e8: ldur            x1, [fp, #-8]
    // 0xc434ec: ldur            x2, [fp, #-0x10]
    // 0xc434f0: r0 = write()
    //     0xc434f0: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc434f4: ldur            x1, [fp, #-8]
    // 0xc434f8: ldur            x2, [fp, #-0x18]
    // 0xc434fc: r0 = write()
    //     0xc434fc: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc43500: b               #0xc43514
    // 0xc43504: ldur            x1, [fp, #-8]
    // 0xc43508: r2 = "SYSTEM"
    //     0xc43508: add             x2, PP, #0x26, lsl #12  ; [pp+0x26860] "SYSTEM"
    //     0xc4350c: ldr             x2, [x2, #0x860]
    // 0xc43510: r0 = write()
    //     0xc43510: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc43514: ldr             x0, [fp, #0x10]
    // 0xc43518: ldur            x1, [fp, #-8]
    // 0xc4351c: r2 = " "
    //     0xc4351c: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xc43520: r0 = write()
    //     0xc43520: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc43524: ldr             x0, [fp, #0x10]
    // 0xc43528: LoadField: r1 = r0->field_13
    //     0xc43528: ldur            w1, [x0, #0x13]
    // 0xc4352c: DecompressPointer r1
    //     0xc4352c: add             x1, x1, HEAP, lsl #32
    // 0xc43530: LoadField: r3 = r1->field_13
    //     0xc43530: ldur            w3, [x1, #0x13]
    // 0xc43534: DecompressPointer r3
    //     0xc43534: add             x3, x3, HEAP, lsl #32
    // 0xc43538: ldur            x1, [fp, #-8]
    // 0xc4353c: mov             x2, x3
    // 0xc43540: stur            x3, [fp, #-0x10]
    // 0xc43544: r0 = write()
    //     0xc43544: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc43548: ldr             x0, [fp, #0x10]
    // 0xc4354c: LoadField: r2 = r0->field_f
    //     0xc4354c: ldur            w2, [x0, #0xf]
    // 0xc43550: DecompressPointer r2
    //     0xc43550: add             x2, x2, HEAP, lsl #32
    // 0xc43554: ldur            x1, [fp, #-8]
    // 0xc43558: r0 = write()
    //     0xc43558: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc4355c: ldur            x1, [fp, #-8]
    // 0xc43560: ldur            x2, [fp, #-0x10]
    // 0xc43564: r0 = write()
    //     0xc43564: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc43568: ldur            x16, [fp, #-8]
    // 0xc4356c: str             x16, [SP]
    // 0xc43570: r0 = toString()
    //     0xc43570: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc43574: LeaveFrame
    //     0xc43574: mov             SP, fp
    //     0xc43578: ldp             fp, lr, [SP], #0x10
    // 0xc4357c: ret
    //     0xc4357c: ret             
    // 0xc43580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43580: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc43584: b               #0xc43474
    // 0xc43588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc43588: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7f99c, size: 0xb4
    // 0xd7f99c: EnterFrame
    //     0xd7f99c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7f9a0: mov             fp, SP
    // 0xd7f9a4: AllocStack(0x10)
    //     0xd7f9a4: sub             SP, SP, #0x10
    // 0xd7f9a8: CheckStackOverflow
    //     0xd7f9a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7f9ac: cmp             SP, x16
    //     0xd7f9b0: b.ls            #0xd7fa48
    // 0xd7f9b4: ldr             x1, [fp, #0x10]
    // 0xd7f9b8: cmp             w1, NULL
    // 0xd7f9bc: b.ne            #0xd7f9d0
    // 0xd7f9c0: r0 = false
    //     0xd7f9c0: add             x0, NULL, #0x30  ; false
    // 0xd7f9c4: LeaveFrame
    //     0xd7f9c4: mov             SP, fp
    //     0xd7f9c8: ldp             fp, lr, [SP], #0x10
    // 0xd7f9cc: ret
    //     0xd7f9cc: ret             
    // 0xd7f9d0: r0 = 60
    //     0xd7f9d0: movz            x0, #0x3c
    // 0xd7f9d4: branchIfSmi(r1, 0xd7f9e0)
    //     0xd7f9d4: tbz             w1, #0, #0xd7f9e0
    // 0xd7f9d8: r0 = LoadClassIdInstr(r1)
    //     0xd7f9d8: ldur            x0, [x1, #-1]
    //     0xd7f9dc: ubfx            x0, x0, #0xc, #0x14
    // 0xd7f9e0: cmp             x0, #0x111
    // 0xd7f9e4: b.ne            #0xd7fa38
    // 0xd7f9e8: LoadField: r0 = r1->field_7
    //     0xd7f9e8: ldur            w0, [x1, #7]
    // 0xd7f9ec: DecompressPointer r0
    //     0xd7f9ec: add             x0, x0, HEAP, lsl #32
    // 0xd7f9f0: r2 = LoadClassIdInstr(r0)
    //     0xd7f9f0: ldur            x2, [x0, #-1]
    //     0xd7f9f4: ubfx            x2, x2, #0xc, #0x14
    // 0xd7f9f8: stp             x0, x0, [SP]
    // 0xd7f9fc: mov             x0, x2
    // 0xd7fa00: mov             lr, x0
    // 0xd7fa04: ldr             lr, [x21, lr, lsl #3]
    // 0xd7fa08: blr             lr
    // 0xd7fa0c: tbnz            w0, #4, #0xd7fa38
    // 0xd7fa10: ldr             x0, [fp, #0x10]
    // 0xd7fa14: LoadField: r1 = r0->field_f
    //     0xd7fa14: ldur            w1, [x0, #0xf]
    // 0xd7fa18: DecompressPointer r1
    //     0xd7fa18: add             x1, x1, HEAP, lsl #32
    // 0xd7fa1c: r0 = LoadClassIdInstr(r1)
    //     0xd7fa1c: ldur            x0, [x1, #-1]
    //     0xd7fa20: ubfx            x0, x0, #0xc, #0x14
    // 0xd7fa24: stp             x1, x1, [SP]
    // 0xd7fa28: mov             lr, x0
    // 0xd7fa2c: ldr             lr, [x21, lr, lsl #3]
    // 0xd7fa30: blr             lr
    // 0xd7fa34: b               #0xd7fa3c
    // 0xd7fa38: r0 = false
    //     0xd7fa38: add             x0, NULL, #0x30  ; false
    // 0xd7fa3c: LeaveFrame
    //     0xd7fa3c: mov             SP, fp
    //     0xd7fa40: ldp             fp, lr, [SP], #0x10
    // 0xd7fa44: ret
    //     0xd7fa44: ret             
    // 0xd7fa48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7fa48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7fa4c: b               #0xd7f9b4
  }
}
