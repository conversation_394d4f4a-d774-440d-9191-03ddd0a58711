// lib: , url: package:xml/src/xml/entities/default_mapping.dart

// class id: 1051283, size: 0x8
class :: {

  static late XmlEntityMapping defaultEntityMapping; // offset: 0xc04
  static late final RegExp _textPattern; // offset: 0xc08
  static late final RegExp _singeQuoteAttributePattern; // offset: 0xc0c
  static late final RegExp _doubleQuoteAttributePattern; // offset: 0xc10

  static XmlEntityMapping defaultEntityMapping() {
    // ** addr: 0xacfabc, size: 0xc
    // 0xacfabc: r0 = Instance_XmlDefaultEntityMapping
    //     0xacfabc: add             x0, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xacfac0: ldr             x0, [x0, #0x5c0]
    // 0xacfac4: ret
    //     0xacfac4: ret             
  }
  [closure] static String _doubleQuoteAttributeReplace(dynamic, Match) {
    // ** addr: 0xce0664, size: 0x30
    // 0xce0664: EnterFrame
    //     0xce0664: stp             fp, lr, [SP, #-0x10]!
    //     0xce0668: mov             fp, SP
    // 0xce066c: CheckStackOverflow
    //     0xce066c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0670: cmp             SP, x16
    //     0xce0674: b.ls            #0xce068c
    // 0xce0678: ldr             x1, [fp, #0x10]
    // 0xce067c: r0 = _doubleQuoteAttributeReplace()
    //     0xce067c: bl              #0xce0694  ; [package:xml/src/xml/entities/default_mapping.dart] ::_doubleQuoteAttributeReplace
    // 0xce0680: LeaveFrame
    //     0xce0680: mov             SP, fp
    //     0xce0684: ldp             fp, lr, [SP], #0x10
    // 0xce0688: ret
    //     0xce0688: ret             
    // 0xce068c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce068c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0690: b               #0xce0678
  }
  static _ _doubleQuoteAttributeReplace(/* No info */) {
    // ** addr: 0xce0694, size: 0xd0
    // 0xce0694: EnterFrame
    //     0xce0694: stp             fp, lr, [SP, #-0x10]!
    //     0xce0698: mov             fp, SP
    // 0xce069c: AllocStack(0x18)
    //     0xce069c: sub             SP, SP, #0x18
    // 0xce06a0: CheckStackOverflow
    //     0xce06a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce06a4: cmp             SP, x16
    //     0xce06a8: b.ls            #0xce0758
    // 0xce06ac: r0 = LoadClassIdInstr(r1)
    //     0xce06ac: ldur            x0, [x1, #-1]
    //     0xce06b0: ubfx            x0, x0, #0xc, #0x14
    // 0xce06b4: r2 = 0
    //     0xce06b4: movz            x2, #0
    // 0xce06b8: r0 = GDT[cid_x0 + -0xfdd]()
    //     0xce06b8: sub             lr, x0, #0xfdd
    //     0xce06bc: ldr             lr, [x21, lr, lsl #3]
    //     0xce06c0: blr             lr
    // 0xce06c4: stur            x0, [fp, #-8]
    // 0xce06c8: cmp             w0, NULL
    // 0xce06cc: b.eq            #0xce0760
    // 0xce06d0: r16 = "\""
    //     0xce06d0: ldr             x16, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0xce06d4: stp             x0, x16, [SP]
    // 0xce06d8: r0 = ==()
    //     0xce06d8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce06dc: tbnz            w0, #4, #0xce06f4
    // 0xce06e0: r0 = "&quot;"
    //     0xce06e0: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf88] "&quot;"
    //     0xce06e4: ldr             x0, [x0, #0xf88]
    // 0xce06e8: LeaveFrame
    //     0xce06e8: mov             SP, fp
    //     0xce06ec: ldp             fp, lr, [SP], #0x10
    // 0xce06f0: ret
    //     0xce06f0: ret             
    // 0xce06f4: r16 = "&"
    //     0xce06f4: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0xce06f8: ldur            lr, [fp, #-8]
    // 0xce06fc: stp             lr, x16, [SP]
    // 0xce0700: r0 = ==()
    //     0xce0700: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce0704: tbnz            w0, #4, #0xce071c
    // 0xce0708: r0 = "&amp;"
    //     0xce0708: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf30] "&amp;"
    //     0xce070c: ldr             x0, [x0, #0xf30]
    // 0xce0710: LeaveFrame
    //     0xce0710: mov             SP, fp
    //     0xce0714: ldp             fp, lr, [SP], #0x10
    // 0xce0718: ret
    //     0xce0718: ret             
    // 0xce071c: r16 = "<"
    //     0xce071c: ldr             x16, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xce0720: ldur            lr, [fp, #-8]
    // 0xce0724: stp             lr, x16, [SP]
    // 0xce0728: r0 = ==()
    //     0xce0728: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce072c: tbnz            w0, #4, #0xce0744
    // 0xce0730: r0 = "&lt;"
    //     0xce0730: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf28] "&lt;"
    //     0xce0734: ldr             x0, [x0, #0xf28]
    // 0xce0738: LeaveFrame
    //     0xce0738: mov             SP, fp
    //     0xce073c: ldp             fp, lr, [SP], #0x10
    // 0xce0740: ret
    //     0xce0740: ret             
    // 0xce0744: ldur            x1, [fp, #-8]
    // 0xce0748: r0 = _asNumericCharacterReferences()
    //     0xce0748: bl              #0xce0764  ; [package:xml/src/xml/entities/default_mapping.dart] ::_asNumericCharacterReferences
    // 0xce074c: LeaveFrame
    //     0xce074c: mov             SP, fp
    //     0xce0750: ldp             fp, lr, [SP], #0x10
    // 0xce0754: ret
    //     0xce0754: ret             
    // 0xce0758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0758: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce075c: b               #0xce06ac
    // 0xce0760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce0760: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _asNumericCharacterReferences(/* No info */) {
    // ** addr: 0xce0764, size: 0x80
    // 0xce0764: EnterFrame
    //     0xce0764: stp             fp, lr, [SP, #-0x10]!
    //     0xce0768: mov             fp, SP
    // 0xce076c: AllocStack(0x28)
    //     0xce076c: sub             SP, SP, #0x28
    // 0xce0770: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xce0770: mov             x0, x1
    //     0xce0774: stur            x1, [fp, #-8]
    // 0xce0778: CheckStackOverflow
    //     0xce0778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce077c: cmp             SP, x16
    //     0xce0780: b.ls            #0xce07dc
    // 0xce0784: r1 = <int>
    //     0xce0784: ldr             x1, [PP, #0xc20]  ; [pp+0xc20] TypeArguments: <int>
    // 0xce0788: r0 = Runes()
    //     0xce0788: bl              #0x7bc644  ; AllocateRunesStub -> Runes (size=0x10)
    // 0xce078c: mov             x3, x0
    // 0xce0790: ldur            x0, [fp, #-8]
    // 0xce0794: stur            x3, [fp, #-0x10]
    // 0xce0798: StoreField: r3->field_b = r0
    //     0xce0798: stur            w0, [x3, #0xb]
    // 0xce079c: r1 = Function '<anonymous closure>': static.
    //     0xce079c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bf40] AnonymousClosure: static (0xce07e4), in [package:xml/src/xml/entities/default_mapping.dart] ::_asNumericCharacterReferences (0xce0764)
    //     0xce07a0: ldr             x1, [x1, #0xf40]
    // 0xce07a4: r2 = Null
    //     0xce07a4: mov             x2, NULL
    // 0xce07a8: r0 = AllocateClosure()
    //     0xce07a8: bl              #0xec1630  ; AllocateClosureStub
    // 0xce07ac: r16 = <String>
    //     0xce07ac: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xce07b0: ldur            lr, [fp, #-0x10]
    // 0xce07b4: stp             lr, x16, [SP, #8]
    // 0xce07b8: str             x0, [SP]
    // 0xce07bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xce07bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xce07c0: r0 = map()
    //     0xce07c0: bl              #0x7ac028  ; [dart:core] Iterable::map
    // 0xce07c4: mov             x1, x0
    // 0xce07c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xce07c8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xce07cc: r0 = join()
    //     0xce07cc: bl              #0x7ae598  ; [dart:core] Iterable::join
    // 0xce07d0: LeaveFrame
    //     0xce07d0: mov             SP, fp
    //     0xce07d4: ldp             fp, lr, [SP], #0x10
    // 0xce07d8: ret
    //     0xce07d8: ret             
    // 0xce07dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce07dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce07e0: b               #0xce0784
  }
  [closure] static String <anonymous closure>(dynamic, int) {
    // ** addr: 0xce07e4, size: 0x98
    // 0xce07e4: EnterFrame
    //     0xce07e4: stp             fp, lr, [SP, #-0x10]!
    //     0xce07e8: mov             fp, SP
    // 0xce07ec: AllocStack(0x10)
    //     0xce07ec: sub             SP, SP, #0x10
    // 0xce07f0: CheckStackOverflow
    //     0xce07f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce07f4: cmp             SP, x16
    //     0xce07f8: b.ls            #0xce0874
    // 0xce07fc: r1 = Null
    //     0xce07fc: mov             x1, NULL
    // 0xce0800: r2 = 6
    //     0xce0800: movz            x2, #0x6
    // 0xce0804: r0 = AllocateArray()
    //     0xce0804: bl              #0xec22fc  ; AllocateArrayStub
    // 0xce0808: stur            x0, [fp, #-8]
    // 0xce080c: r16 = "&#x"
    //     0xce080c: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bf48] "&#x"
    //     0xce0810: ldr             x16, [x16, #0xf48]
    // 0xce0814: StoreField: r0->field_f = r16
    //     0xce0814: stur            w16, [x0, #0xf]
    // 0xce0818: ldr             x1, [fp, #0x10]
    // 0xce081c: r0 = _toPow2String()
    //     0xce081c: bl              #0x67f220  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0xce0820: str             x0, [SP]
    // 0xce0824: r0 = toUpperCase()
    //     0xce0824: bl              #0xebe0d0  ; [dart:core] _OneByteString::toUpperCase
    // 0xce0828: ldur            x1, [fp, #-8]
    // 0xce082c: ArrayStore: r1[1] = r0  ; List_4
    //     0xce082c: add             x25, x1, #0x13
    //     0xce0830: str             w0, [x25]
    //     0xce0834: tbz             w0, #0, #0xce0850
    //     0xce0838: ldurb           w16, [x1, #-1]
    //     0xce083c: ldurb           w17, [x0, #-1]
    //     0xce0840: and             x16, x17, x16, lsr #2
    //     0xce0844: tst             x16, HEAP, lsr #32
    //     0xce0848: b.eq            #0xce0850
    //     0xce084c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xce0850: ldur            x0, [fp, #-8]
    // 0xce0854: r16 = ";"
    //     0xce0854: add             x16, PP, #0x10, lsl #12  ; [pp+0x107f8] ";"
    //     0xce0858: ldr             x16, [x16, #0x7f8]
    // 0xce085c: ArrayStore: r0[0] = r16  ; List_4
    //     0xce085c: stur            w16, [x0, #0x17]
    // 0xce0860: str             x0, [SP]
    // 0xce0864: r0 = _interpolate()
    //     0xce0864: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xce0868: LeaveFrame
    //     0xce0868: mov             SP, fp
    //     0xce086c: ldp             fp, lr, [SP], #0x10
    // 0xce0870: ret
    //     0xce0870: ret             
    // 0xce0874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0878: b               #0xce07fc
  }
  static RegExp _doubleQuoteAttributePattern() {
    // ** addr: 0xce087c, size: 0x74
    // 0xce087c: EnterFrame
    //     0xce087c: stp             fp, lr, [SP, #-0x10]!
    //     0xce0880: mov             fp, SP
    // 0xce0884: AllocStack(0x30)
    //     0xce0884: sub             SP, SP, #0x30
    // 0xce0888: CheckStackOverflow
    //     0xce0888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce088c: cmp             SP, x16
    //     0xce0890: b.ls            #0xce08e8
    // 0xce0894: r16 = "[\"&<\\n\\r\\t"
    //     0xce0894: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bf98] "[\"&<\\n\\r\\t"
    //     0xce0898: ldr             x16, [x16, #0xf98]
    // 0xce089c: r30 = "\\u0001-\\u0008\\u000b\\u000c\\u000e-\\u001f\\u007f-\\u0084\\u0086-\\u009f"
    //     0xce089c: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3bf58] "\\u0001-\\u0008\\u000b\\u000c\\u000e-\\u001f\\u007f-\\u0084\\u0086-\\u009f"
    //     0xce08a0: ldr             lr, [lr, #0xf58]
    // 0xce08a4: stp             lr, x16, [SP]
    // 0xce08a8: r0 = +()
    //     0xce08a8: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xce08ac: r16 = "]"
    //     0xce08ac: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xce08b0: stp             x16, x0, [SP]
    // 0xce08b4: r0 = +()
    //     0xce08b4: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xce08b8: stp             x0, NULL, [SP, #0x20]
    // 0xce08bc: r16 = false
    //     0xce08bc: add             x16, NULL, #0x30  ; false
    // 0xce08c0: r30 = true
    //     0xce08c0: add             lr, NULL, #0x20  ; true
    // 0xce08c4: stp             lr, x16, [SP, #0x10]
    // 0xce08c8: r16 = false
    //     0xce08c8: add             x16, NULL, #0x30  ; false
    // 0xce08cc: r30 = false
    //     0xce08cc: add             lr, NULL, #0x30  ; false
    // 0xce08d0: stp             lr, x16, [SP]
    // 0xce08d4: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xce08d4: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xce08d8: r0 = _RegExp()
    //     0xce08d8: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xce08dc: LeaveFrame
    //     0xce08dc: mov             SP, fp
    //     0xce08e0: ldp             fp, lr, [SP], #0x10
    // 0xce08e4: ret
    //     0xce08e4: ret             
    // 0xce08e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce08e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce08ec: b               #0xce0894
  }
  [closure] static String _singeQuoteAttributeReplace(dynamic, Match) {
    // ** addr: 0xce08f0, size: 0x30
    // 0xce08f0: EnterFrame
    //     0xce08f0: stp             fp, lr, [SP, #-0x10]!
    //     0xce08f4: mov             fp, SP
    // 0xce08f8: CheckStackOverflow
    //     0xce08f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce08fc: cmp             SP, x16
    //     0xce0900: b.ls            #0xce0918
    // 0xce0904: ldr             x1, [fp, #0x10]
    // 0xce0908: r0 = _singeQuoteAttributeReplace()
    //     0xce0908: bl              #0xce0920  ; [package:xml/src/xml/entities/default_mapping.dart] ::_singeQuoteAttributeReplace
    // 0xce090c: LeaveFrame
    //     0xce090c: mov             SP, fp
    //     0xce0910: ldp             fp, lr, [SP], #0x10
    // 0xce0914: ret
    //     0xce0914: ret             
    // 0xce0918: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0918: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce091c: b               #0xce0904
  }
  static _ _singeQuoteAttributeReplace(/* No info */) {
    // ** addr: 0xce0920, size: 0xd0
    // 0xce0920: EnterFrame
    //     0xce0920: stp             fp, lr, [SP, #-0x10]!
    //     0xce0924: mov             fp, SP
    // 0xce0928: AllocStack(0x18)
    //     0xce0928: sub             SP, SP, #0x18
    // 0xce092c: CheckStackOverflow
    //     0xce092c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0930: cmp             SP, x16
    //     0xce0934: b.ls            #0xce09e4
    // 0xce0938: r0 = LoadClassIdInstr(r1)
    //     0xce0938: ldur            x0, [x1, #-1]
    //     0xce093c: ubfx            x0, x0, #0xc, #0x14
    // 0xce0940: r2 = 0
    //     0xce0940: movz            x2, #0
    // 0xce0944: r0 = GDT[cid_x0 + -0xfdd]()
    //     0xce0944: sub             lr, x0, #0xfdd
    //     0xce0948: ldr             lr, [x21, lr, lsl #3]
    //     0xce094c: blr             lr
    // 0xce0950: stur            x0, [fp, #-8]
    // 0xce0954: cmp             w0, NULL
    // 0xce0958: b.eq            #0xce09ec
    // 0xce095c: r16 = "\'"
    //     0xce095c: ldr             x16, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0xce0960: stp             x0, x16, [SP]
    // 0xce0964: r0 = ==()
    //     0xce0964: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce0968: tbnz            w0, #4, #0xce0980
    // 0xce096c: r0 = "&apos;"
    //     0xce096c: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf90] "&apos;"
    //     0xce0970: ldr             x0, [x0, #0xf90]
    // 0xce0974: LeaveFrame
    //     0xce0974: mov             SP, fp
    //     0xce0978: ldp             fp, lr, [SP], #0x10
    // 0xce097c: ret
    //     0xce097c: ret             
    // 0xce0980: r16 = "&"
    //     0xce0980: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0xce0984: ldur            lr, [fp, #-8]
    // 0xce0988: stp             lr, x16, [SP]
    // 0xce098c: r0 = ==()
    //     0xce098c: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce0990: tbnz            w0, #4, #0xce09a8
    // 0xce0994: r0 = "&amp;"
    //     0xce0994: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf30] "&amp;"
    //     0xce0998: ldr             x0, [x0, #0xf30]
    // 0xce099c: LeaveFrame
    //     0xce099c: mov             SP, fp
    //     0xce09a0: ldp             fp, lr, [SP], #0x10
    // 0xce09a4: ret
    //     0xce09a4: ret             
    // 0xce09a8: r16 = "<"
    //     0xce09a8: ldr             x16, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xce09ac: ldur            lr, [fp, #-8]
    // 0xce09b0: stp             lr, x16, [SP]
    // 0xce09b4: r0 = ==()
    //     0xce09b4: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce09b8: tbnz            w0, #4, #0xce09d0
    // 0xce09bc: r0 = "&lt;"
    //     0xce09bc: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf28] "&lt;"
    //     0xce09c0: ldr             x0, [x0, #0xf28]
    // 0xce09c4: LeaveFrame
    //     0xce09c4: mov             SP, fp
    //     0xce09c8: ldp             fp, lr, [SP], #0x10
    // 0xce09cc: ret
    //     0xce09cc: ret             
    // 0xce09d0: ldur            x1, [fp, #-8]
    // 0xce09d4: r0 = _asNumericCharacterReferences()
    //     0xce09d4: bl              #0xce0764  ; [package:xml/src/xml/entities/default_mapping.dart] ::_asNumericCharacterReferences
    // 0xce09d8: LeaveFrame
    //     0xce09d8: mov             SP, fp
    //     0xce09dc: ldp             fp, lr, [SP], #0x10
    // 0xce09e0: ret
    //     0xce09e0: ret             
    // 0xce09e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce09e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce09e8: b               #0xce0938
    // 0xce09ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce09ec: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static RegExp _singeQuoteAttributePattern() {
    // ** addr: 0xce09f0, size: 0x74
    // 0xce09f0: EnterFrame
    //     0xce09f0: stp             fp, lr, [SP, #-0x10]!
    //     0xce09f4: mov             fp, SP
    // 0xce09f8: AllocStack(0x30)
    //     0xce09f8: sub             SP, SP, #0x30
    // 0xce09fc: CheckStackOverflow
    //     0xce09fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0a00: cmp             SP, x16
    //     0xce0a04: b.ls            #0xce0a5c
    // 0xce0a08: r16 = "[\'&<\\n\\r\\t"
    //     0xce0a08: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bfa0] "[\'&<\\n\\r\\t"
    //     0xce0a0c: ldr             x16, [x16, #0xfa0]
    // 0xce0a10: r30 = "\\u0001-\\u0008\\u000b\\u000c\\u000e-\\u001f\\u007f-\\u0084\\u0086-\\u009f"
    //     0xce0a10: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3bf58] "\\u0001-\\u0008\\u000b\\u000c\\u000e-\\u001f\\u007f-\\u0084\\u0086-\\u009f"
    //     0xce0a14: ldr             lr, [lr, #0xf58]
    // 0xce0a18: stp             lr, x16, [SP]
    // 0xce0a1c: r0 = +()
    //     0xce0a1c: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xce0a20: r16 = "]"
    //     0xce0a20: ldr             x16, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xce0a24: stp             x16, x0, [SP]
    // 0xce0a28: r0 = +()
    //     0xce0a28: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xce0a2c: stp             x0, NULL, [SP, #0x20]
    // 0xce0a30: r16 = false
    //     0xce0a30: add             x16, NULL, #0x30  ; false
    // 0xce0a34: r30 = true
    //     0xce0a34: add             lr, NULL, #0x20  ; true
    // 0xce0a38: stp             lr, x16, [SP, #0x10]
    // 0xce0a3c: r16 = false
    //     0xce0a3c: add             x16, NULL, #0x30  ; false
    // 0xce0a40: r30 = false
    //     0xce0a40: add             lr, NULL, #0x30  ; false
    // 0xce0a44: stp             lr, x16, [SP]
    // 0xce0a48: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xce0a48: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xce0a4c: r0 = _RegExp()
    //     0xce0a4c: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xce0a50: LeaveFrame
    //     0xce0a50: mov             SP, fp
    //     0xce0a54: ldp             fp, lr, [SP], #0x10
    // 0xce0a58: ret
    //     0xce0a58: ret             
    // 0xce0a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0a5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0a60: b               #0xce0a08
  }
  [closure] static String _textReplace(dynamic, Match) {
    // ** addr: 0xce1424, size: 0x30
    // 0xce1424: EnterFrame
    //     0xce1424: stp             fp, lr, [SP, #-0x10]!
    //     0xce1428: mov             fp, SP
    // 0xce142c: CheckStackOverflow
    //     0xce142c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1430: cmp             SP, x16
    //     0xce1434: b.ls            #0xce144c
    // 0xce1438: ldr             x1, [fp, #0x10]
    // 0xce143c: r0 = _textReplace()
    //     0xce143c: bl              #0xce1454  ; [package:xml/src/xml/entities/default_mapping.dart] ::_textReplace
    // 0xce1440: LeaveFrame
    //     0xce1440: mov             SP, fp
    //     0xce1444: ldp             fp, lr, [SP], #0x10
    // 0xce1448: ret
    //     0xce1448: ret             
    // 0xce144c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce144c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1450: b               #0xce1438
  }
  static _ _textReplace(/* No info */) {
    // ** addr: 0xce1454, size: 0xd4
    // 0xce1454: EnterFrame
    //     0xce1454: stp             fp, lr, [SP, #-0x10]!
    //     0xce1458: mov             fp, SP
    // 0xce145c: AllocStack(0x18)
    //     0xce145c: sub             SP, SP, #0x18
    // 0xce1460: CheckStackOverflow
    //     0xce1460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1464: cmp             SP, x16
    //     0xce1468: b.ls            #0xce151c
    // 0xce146c: r0 = LoadClassIdInstr(r1)
    //     0xce146c: ldur            x0, [x1, #-1]
    //     0xce1470: ubfx            x0, x0, #0xc, #0x14
    // 0xce1474: r2 = 0
    //     0xce1474: movz            x2, #0
    // 0xce1478: r0 = GDT[cid_x0 + -0xfdd]()
    //     0xce1478: sub             lr, x0, #0xfdd
    //     0xce147c: ldr             lr, [x21, lr, lsl #3]
    //     0xce1480: blr             lr
    // 0xce1484: stur            x0, [fp, #-8]
    // 0xce1488: cmp             w0, NULL
    // 0xce148c: b.eq            #0xce1524
    // 0xce1490: r16 = "<"
    //     0xce1490: ldr             x16, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xce1494: stp             x0, x16, [SP]
    // 0xce1498: r0 = ==()
    //     0xce1498: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce149c: tbnz            w0, #4, #0xce14b4
    // 0xce14a0: r0 = "&lt;"
    //     0xce14a0: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf28] "&lt;"
    //     0xce14a4: ldr             x0, [x0, #0xf28]
    // 0xce14a8: LeaveFrame
    //     0xce14a8: mov             SP, fp
    //     0xce14ac: ldp             fp, lr, [SP], #0x10
    // 0xce14b0: ret
    //     0xce14b0: ret             
    // 0xce14b4: r16 = "&"
    //     0xce14b4: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0xce14b8: ldur            lr, [fp, #-8]
    // 0xce14bc: stp             lr, x16, [SP]
    // 0xce14c0: r0 = ==()
    //     0xce14c0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce14c4: tbnz            w0, #4, #0xce14dc
    // 0xce14c8: r0 = "&amp;"
    //     0xce14c8: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf30] "&amp;"
    //     0xce14cc: ldr             x0, [x0, #0xf30]
    // 0xce14d0: LeaveFrame
    //     0xce14d0: mov             SP, fp
    //     0xce14d4: ldp             fp, lr, [SP], #0x10
    // 0xce14d8: ret
    //     0xce14d8: ret             
    // 0xce14dc: r16 = "]]>"
    //     0xce14dc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b28] "]]>"
    //     0xce14e0: ldr             x16, [x16, #0xb28]
    // 0xce14e4: ldur            lr, [fp, #-8]
    // 0xce14e8: stp             lr, x16, [SP]
    // 0xce14ec: r0 = ==()
    //     0xce14ec: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xce14f0: tbnz            w0, #4, #0xce1508
    // 0xce14f4: r0 = "]]&gt;"
    //     0xce14f4: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bf38] "]]&gt;"
    //     0xce14f8: ldr             x0, [x0, #0xf38]
    // 0xce14fc: LeaveFrame
    //     0xce14fc: mov             SP, fp
    //     0xce1500: ldp             fp, lr, [SP], #0x10
    // 0xce1504: ret
    //     0xce1504: ret             
    // 0xce1508: ldur            x1, [fp, #-8]
    // 0xce150c: r0 = _asNumericCharacterReferences()
    //     0xce150c: bl              #0xce0764  ; [package:xml/src/xml/entities/default_mapping.dart] ::_asNumericCharacterReferences
    // 0xce1510: LeaveFrame
    //     0xce1510: mov             SP, fp
    //     0xce1514: ldp             fp, lr, [SP], #0x10
    // 0xce1518: ret
    //     0xce1518: ret             
    // 0xce151c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce151c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1520: b               #0xce146c
    // 0xce1524: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xce1524: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static RegExp _textPattern() {
    // ** addr: 0xce1528, size: 0x78
    // 0xce1528: EnterFrame
    //     0xce1528: stp             fp, lr, [SP, #-0x10]!
    //     0xce152c: mov             fp, SP
    // 0xce1530: AllocStack(0x30)
    //     0xce1530: sub             SP, SP, #0x30
    // 0xce1534: CheckStackOverflow
    //     0xce1534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1538: cmp             SP, x16
    //     0xce153c: b.ls            #0xce1598
    // 0xce1540: r16 = "[&<"
    //     0xce1540: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bf50] "[&<"
    //     0xce1544: ldr             x16, [x16, #0xf50]
    // 0xce1548: r30 = "\\u0001-\\u0008\\u000b\\u000c\\u000e-\\u001f\\u007f-\\u0084\\u0086-\\u009f"
    //     0xce1548: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3bf58] "\\u0001-\\u0008\\u000b\\u000c\\u000e-\\u001f\\u007f-\\u0084\\u0086-\\u009f"
    //     0xce154c: ldr             lr, [lr, #0xf58]
    // 0xce1550: stp             lr, x16, [SP]
    // 0xce1554: r0 = +()
    //     0xce1554: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xce1558: r16 = "]|]]>"
    //     0xce1558: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bf60] "]|]]>"
    //     0xce155c: ldr             x16, [x16, #0xf60]
    // 0xce1560: stp             x16, x0, [SP]
    // 0xce1564: r0 = +()
    //     0xce1564: bl              #0x5ffc9c  ; [dart:core] _StringBase::+
    // 0xce1568: stp             x0, NULL, [SP, #0x20]
    // 0xce156c: r16 = false
    //     0xce156c: add             x16, NULL, #0x30  ; false
    // 0xce1570: r30 = true
    //     0xce1570: add             lr, NULL, #0x20  ; true
    // 0xce1574: stp             lr, x16, [SP, #0x10]
    // 0xce1578: r16 = false
    //     0xce1578: add             x16, NULL, #0x30  ; false
    // 0xce157c: r30 = false
    //     0xce157c: add             lr, NULL, #0x30  ; false
    // 0xce1580: stp             lr, x16, [SP]
    // 0xce1584: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xce1584: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xce1588: r0 = _RegExp()
    //     0xce1588: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xce158c: LeaveFrame
    //     0xce158c: mov             SP, fp
    //     0xce1590: ldp             fp, lr, [SP], #0x10
    // 0xce1594: ret
    //     0xce1594: ret             
    // 0xce1598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1598: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce159c: b               #0xce1540
  }
}

// class id: 272, size: 0xc, field offset: 0x8
//   const constructor, 
class XmlDefaultEntityMapping extends XmlEntityMapping {

  _ConstMap<String, String> field_8;

  _ decodeEntity(/* No info */) {
    // ** addr: 0x88eca8, size: 0x158
    // 0x88eca8: EnterFrame
    //     0x88eca8: stp             fp, lr, [SP, #-0x10]!
    //     0x88ecac: mov             fp, SP
    // 0x88ecb0: AllocStack(0x28)
    //     0x88ecb0: sub             SP, SP, #0x28
    // 0x88ecb4: SetupParameters(XmlDefaultEntityMapping this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x88ecb4: mov             x0, x1
    //     0x88ecb8: stur            x1, [fp, #-0x10]
    //     0x88ecbc: mov             x1, x2
    //     0x88ecc0: stur            x2, [fp, #-0x18]
    // 0x88ecc4: CheckStackOverflow
    //     0x88ecc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ecc8: cmp             SP, x16
    //     0x88eccc: b.ls            #0x88edf8
    // 0x88ecd0: LoadField: r2 = r1->field_7
    //     0x88ecd0: ldur            w2, [x1, #7]
    // 0x88ecd4: r3 = LoadInt32Instr(r2)
    //     0x88ecd4: sbfx            x3, x2, #1, #0x1f
    // 0x88ecd8: stur            x3, [fp, #-8]
    // 0x88ecdc: cmp             x3, #1
    // 0x88ece0: b.le            #0x88eddc
    // 0x88ece4: stp             xzr, x1, [SP]
    // 0x88ece8: r0 = []()
    //     0x88ece8: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x88ecec: r1 = LoadClassIdInstr(r0)
    //     0x88ecec: ldur            x1, [x0, #-1]
    //     0x88ecf0: ubfx            x1, x1, #0xc, #0x14
    // 0x88ecf4: r16 = "#"
    //     0x88ecf4: ldr             x16, [PP, #0x4d0]  ; [pp+0x4d0] "#"
    // 0x88ecf8: stp             x16, x0, [SP]
    // 0x88ecfc: mov             x0, x1
    // 0x88ed00: mov             lr, x0
    // 0x88ed04: ldr             lr, [x21, lr, lsl #3]
    // 0x88ed08: blr             lr
    // 0x88ed0c: tbnz            w0, #4, #0x88eddc
    // 0x88ed10: ldur            x0, [fp, #-8]
    // 0x88ed14: cmp             x0, #2
    // 0x88ed18: b.le            #0x88edb0
    // 0x88ed1c: ldur            x16, [fp, #-0x18]
    // 0x88ed20: r30 = 2
    //     0x88ed20: movz            lr, #0x2
    // 0x88ed24: stp             lr, x16, [SP]
    // 0x88ed28: r0 = []()
    //     0x88ed28: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x88ed2c: r1 = LoadClassIdInstr(r0)
    //     0x88ed2c: ldur            x1, [x0, #-1]
    //     0x88ed30: ubfx            x1, x1, #0xc, #0x14
    // 0x88ed34: r16 = "x"
    //     0x88ed34: ldr             x16, [PP, #0x71a0]  ; [pp+0x71a0] "x"
    // 0x88ed38: stp             x16, x0, [SP]
    // 0x88ed3c: mov             x0, x1
    // 0x88ed40: mov             lr, x0
    // 0x88ed44: ldr             lr, [x21, lr, lsl #3]
    // 0x88ed48: blr             lr
    // 0x88ed4c: tbz             w0, #4, #0x88ed84
    // 0x88ed50: ldur            x16, [fp, #-0x18]
    // 0x88ed54: r30 = 2
    //     0x88ed54: movz            lr, #0x2
    // 0x88ed58: stp             lr, x16, [SP]
    // 0x88ed5c: r0 = []()
    //     0x88ed5c: bl              #0x5ffe18  ; [dart:core] _StringBase::[]
    // 0x88ed60: r1 = LoadClassIdInstr(r0)
    //     0x88ed60: ldur            x1, [x0, #-1]
    //     0x88ed64: ubfx            x1, x1, #0xc, #0x14
    // 0x88ed68: r16 = "X"
    //     0x88ed68: ldr             x16, [PP, #0x72b0]  ; [pp+0x72b0] "X"
    // 0x88ed6c: stp             x16, x0, [SP]
    // 0x88ed70: mov             x0, x1
    // 0x88ed74: mov             lr, x0
    // 0x88ed78: ldr             lr, [x21, lr, lsl #3]
    // 0x88ed7c: blr             lr
    // 0x88ed80: tbnz            w0, #4, #0x88edb0
    // 0x88ed84: ldur            x1, [fp, #-0x18]
    // 0x88ed88: r2 = 2
    //     0x88ed88: movz            x2, #0x2
    // 0x88ed8c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x88ed8c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x88ed90: r0 = substring()
    //     0x88ed90: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x88ed94: ldur            x1, [fp, #-0x10]
    // 0x88ed98: mov             x2, x0
    // 0x88ed9c: r3 = 16
    //     0x88ed9c: movz            x3, #0x10
    // 0x88eda0: r0 = _decodeNumericEntity()
    //     0x88eda0: bl              #0x88ee00  ; [package:xml/src/xml/entities/default_mapping.dart] XmlDefaultEntityMapping::_decodeNumericEntity
    // 0x88eda4: LeaveFrame
    //     0x88eda4: mov             SP, fp
    //     0x88eda8: ldp             fp, lr, [SP], #0x10
    // 0x88edac: ret
    //     0x88edac: ret             
    // 0x88edb0: ldur            x1, [fp, #-0x18]
    // 0x88edb4: r2 = 1
    //     0x88edb4: movz            x2, #0x1
    // 0x88edb8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x88edb8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x88edbc: r0 = substring()
    //     0x88edbc: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x88edc0: ldur            x1, [fp, #-0x10]
    // 0x88edc4: mov             x2, x0
    // 0x88edc8: r3 = 10
    //     0x88edc8: movz            x3, #0xa
    // 0x88edcc: r0 = _decodeNumericEntity()
    //     0x88edcc: bl              #0x88ee00  ; [package:xml/src/xml/entities/default_mapping.dart] XmlDefaultEntityMapping::_decodeNumericEntity
    // 0x88edd0: LeaveFrame
    //     0x88edd0: mov             SP, fp
    //     0x88edd4: ldp             fp, lr, [SP], #0x10
    // 0x88edd8: ret
    //     0x88edd8: ret             
    // 0x88eddc: ldur            x2, [fp, #-0x18]
    // 0x88ede0: r1 = _ConstMap len:5
    //     0x88ede0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25a38] Map<String, String>(5)
    //     0x88ede4: ldr             x1, [x1, #0xa38]
    // 0x88ede8: r0 = []()
    //     0x88ede8: bl              #0xd826d8  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x88edec: LeaveFrame
    //     0x88edec: mov             SP, fp
    //     0x88edf0: ldp             fp, lr, [SP], #0x10
    // 0x88edf4: ret
    //     0x88edf4: ret             
    // 0x88edf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88edf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88edfc: b               #0x88ecd0
  }
  _ _decodeNumericEntity(/* No info */) {
    // ** addr: 0x88ee00, size: 0x88
    // 0x88ee00: EnterFrame
    //     0x88ee00: stp             fp, lr, [SP, #-0x10]!
    //     0x88ee04: mov             fp, SP
    // 0x88ee08: AllocStack(0x8)
    //     0x88ee08: sub             SP, SP, #8
    // 0x88ee0c: SetupParameters(XmlDefaultEntityMapping this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x88ee0c: mov             x0, x1
    //     0x88ee10: mov             x1, x2
    // 0x88ee14: CheckStackOverflow
    //     0x88ee14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ee18: cmp             SP, x16
    //     0x88ee1c: b.ls            #0x88ee80
    // 0x88ee20: lsl             x0, x3, #1
    // 0x88ee24: str             x0, [SP]
    // 0x88ee28: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0x88ee28: ldr             x4, [PP, #0xf20]  ; [pp+0xf20] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0x88ee2c: r0 = tryParse()
    //     0x88ee2c: bl              #0x60e098  ; [dart:core] int::tryParse
    // 0x88ee30: cmp             w0, NULL
    // 0x88ee34: b.eq            #0x88ee58
    // 0x88ee38: r1 = LoadInt32Instr(r0)
    //     0x88ee38: sbfx            x1, x0, #1, #0x1f
    //     0x88ee3c: tbz             w0, #0, #0x88ee44
    //     0x88ee40: ldur            x1, [x0, #7]
    // 0x88ee44: tbnz            x1, #0x3f, #0x88ee58
    // 0x88ee48: r17 = 1114111
    //     0x88ee48: movz            x17, #0xffff
    //     0x88ee4c: movk            x17, #0x10, lsl #16
    // 0x88ee50: cmp             x1, x17
    // 0x88ee54: b.le            #0x88ee68
    // 0x88ee58: r0 = Null
    //     0x88ee58: mov             x0, NULL
    // 0x88ee5c: LeaveFrame
    //     0x88ee5c: mov             SP, fp
    //     0x88ee60: ldp             fp, lr, [SP], #0x10
    // 0x88ee64: ret
    //     0x88ee64: ret             
    // 0x88ee68: mov             x2, x0
    // 0x88ee6c: r1 = Null
    //     0x88ee6c: mov             x1, NULL
    // 0x88ee70: r0 = String.fromCharCode()
    //     0x88ee70: bl              #0x602bac  ; [dart:core] String::String.fromCharCode
    // 0x88ee74: LeaveFrame
    //     0x88ee74: mov             SP, fp
    //     0x88ee78: ldp             fp, lr, [SP], #0x10
    // 0x88ee7c: ret
    //     0x88ee7c: ret             
    // 0x88ee80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ee80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ee84: b               #0x88ee20
  }
  _ encodeAttributeValue(/* No info */) {
    // ** addr: 0xce05ac, size: 0xb8
    // 0xce05ac: EnterFrame
    //     0xce05ac: stp             fp, lr, [SP, #-0x10]!
    //     0xce05b0: mov             fp, SP
    // 0xce05b4: AllocStack(0x8)
    //     0xce05b4: sub             SP, SP, #8
    // 0xce05b8: SetupParameters(XmlDefaultEntityMapping this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0xce05b8: mov             x0, x1
    //     0xce05bc: mov             x1, x2
    //     0xce05c0: stur            x2, [fp, #-8]
    // 0xce05c4: CheckStackOverflow
    //     0xce05c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce05c8: cmp             SP, x16
    //     0xce05cc: b.ls            #0xce065c
    // 0xce05d0: LoadField: r0 = r3->field_7
    //     0xce05d0: ldur            x0, [x3, #7]
    // 0xce05d4: cmp             x0, #0
    // 0xce05d8: b.gt            #0xce061c
    // 0xce05dc: r0 = InitLateStaticField(0xc0c) // [package:xml/src/xml/entities/default_mapping.dart] ::_singeQuoteAttributePattern
    //     0xce05dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xce05e0: ldr             x0, [x0, #0x1818]
    //     0xce05e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xce05e8: cmp             w0, w16
    //     0xce05ec: b.ne            #0xce05fc
    //     0xce05f0: add             x2, PP, #0x3b, lsl #12  ; [pp+0x3bf68] Field <::._singeQuoteAttributePattern@686209047>: static late final (offset: 0xc0c)
    //     0xce05f4: ldr             x2, [x2, #0xf68]
    //     0xce05f8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xce05fc: ldur            x1, [fp, #-8]
    // 0xce0600: mov             x2, x0
    // 0xce0604: r3 = Closure: (Match) => String from Function '_singeQuoteAttributeReplace@686209047': static.
    //     0xce0604: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bf70] Closure: (Match) => String from Function '_singeQuoteAttributeReplace@686209047': static. (0x7e54fb6e08f0)
    //     0xce0608: ldr             x3, [x3, #0xf70]
    // 0xce060c: r0 = replaceAllMapped()
    //     0xce060c: bl              #0x65c46c  ; [dart:core] _StringBase::replaceAllMapped
    // 0xce0610: LeaveFrame
    //     0xce0610: mov             SP, fp
    //     0xce0614: ldp             fp, lr, [SP], #0x10
    // 0xce0618: ret
    //     0xce0618: ret             
    // 0xce061c: r0 = InitLateStaticField(0xc10) // [package:xml/src/xml/entities/default_mapping.dart] ::_doubleQuoteAttributePattern
    //     0xce061c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xce0620: ldr             x0, [x0, #0x1820]
    //     0xce0624: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xce0628: cmp             w0, w16
    //     0xce062c: b.ne            #0xce063c
    //     0xce0630: add             x2, PP, #0x3b, lsl #12  ; [pp+0x3bf78] Field <::._doubleQuoteAttributePattern@686209047>: static late final (offset: 0xc10)
    //     0xce0634: ldr             x2, [x2, #0xf78]
    //     0xce0638: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xce063c: ldur            x1, [fp, #-8]
    // 0xce0640: mov             x2, x0
    // 0xce0644: r3 = Closure: (Match) => String from Function '_doubleQuoteAttributeReplace@686209047': static.
    //     0xce0644: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bf80] Closure: (Match) => String from Function '_doubleQuoteAttributeReplace@686209047': static. (0x7e54fb6e0664)
    //     0xce0648: ldr             x3, [x3, #0xf80]
    // 0xce064c: r0 = replaceAllMapped()
    //     0xce064c: bl              #0x65c46c  ; [dart:core] _StringBase::replaceAllMapped
    // 0xce0650: LeaveFrame
    //     0xce0650: mov             SP, fp
    //     0xce0654: ldp             fp, lr, [SP], #0x10
    // 0xce0658: ret
    //     0xce0658: ret             
    // 0xce065c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce065c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0660: b               #0xce05d0
  }
  _ encodeText(/* No info */) {
    // ** addr: 0xce13b8, size: 0x6c
    // 0xce13b8: EnterFrame
    //     0xce13b8: stp             fp, lr, [SP, #-0x10]!
    //     0xce13bc: mov             fp, SP
    // 0xce13c0: AllocStack(0x8)
    //     0xce13c0: sub             SP, SP, #8
    // 0xce13c4: SetupParameters(XmlDefaultEntityMapping this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0xce13c4: mov             x0, x1
    //     0xce13c8: mov             x1, x2
    //     0xce13cc: stur            x2, [fp, #-8]
    // 0xce13d0: CheckStackOverflow
    //     0xce13d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce13d4: cmp             SP, x16
    //     0xce13d8: b.ls            #0xce141c
    // 0xce13dc: r0 = InitLateStaticField(0xc08) // [package:xml/src/xml/entities/default_mapping.dart] ::_textPattern
    //     0xce13dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xce13e0: ldr             x0, [x0, #0x1810]
    //     0xce13e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xce13e8: cmp             w0, w16
    //     0xce13ec: b.ne            #0xce13fc
    //     0xce13f0: add             x2, PP, #0x3b, lsl #12  ; [pp+0x3bf18] Field <::._textPattern@686209047>: static late final (offset: 0xc08)
    //     0xce13f4: ldr             x2, [x2, #0xf18]
    //     0xce13f8: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0xce13fc: ldur            x1, [fp, #-8]
    // 0xce1400: mov             x2, x0
    // 0xce1404: r3 = Closure: (Match) => String from Function '_textReplace@686209047': static.
    //     0xce1404: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bf20] Closure: (Match) => String from Function '_textReplace@686209047': static. (0x7e54fb6e1424)
    //     0xce1408: ldr             x3, [x3, #0xf20]
    // 0xce140c: r0 = replaceAllMapped()
    //     0xce140c: bl              #0x65c46c  ; [dart:core] _StringBase::replaceAllMapped
    // 0xce1410: LeaveFrame
    //     0xce1410: mov             SP, fp
    //     0xce1414: ldp             fp, lr, [SP], #0x10
    // 0xce1418: ret
    //     0xce1418: ret             
    // 0xce141c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce141c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1420: b               #0xce13dc
  }
}
