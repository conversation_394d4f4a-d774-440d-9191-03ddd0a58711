// lib: , url: package:xml/src/xml/entities/entity_mapping.dart

// class id: 1051284, size: 0x8
class :: {
}

// class id: 271, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class XmlEntityMapping extends Object {

  _ decode(/* No info */) {
    // ** addr: 0x88e99c, size: 0x30c
    // 0x88e99c: EnterFrame
    //     0x88e99c: stp             fp, lr, [SP, #-0x10]!
    //     0x88e9a0: mov             fp, SP
    // 0x88e9a4: AllocStack(0x48)
    //     0x88e9a4: sub             SP, SP, #0x48
    // 0x88e9a8: SetupParameters(XmlEntityMapping this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x88e9a8: mov             x4, x1
    //     0x88e9ac: mov             x3, x2
    //     0x88e9b0: stur            x1, [fp, #-8]
    //     0x88e9b4: stur            x2, [fp, #-0x10]
    // 0x88e9b8: CheckStackOverflow
    //     0x88e9b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e9bc: cmp             SP, x16
    //     0x88e9c0: b.ls            #0x88ec98
    // 0x88e9c4: r0 = LoadClassIdInstr(r3)
    //     0x88e9c4: ldur            x0, [x3, #-1]
    //     0x88e9c8: ubfx            x0, x0, #0xc, #0x14
    // 0x88e9cc: str             xzr, [SP]
    // 0x88e9d0: mov             x1, x3
    // 0x88e9d4: r2 = "&"
    //     0x88e9d4: ldr             x2, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0x88e9d8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x88e9d8: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x88e9dc: r0 = GDT[cid_x0 + -0xffa]()
    //     0x88e9dc: sub             lr, x0, #0xffa
    //     0x88e9e0: ldr             lr, [x21, lr, lsl #3]
    //     0x88e9e4: blr             lr
    // 0x88e9e8: mov             x3, x0
    // 0x88e9ec: stur            x3, [fp, #-0x18]
    // 0x88e9f0: tbz             x3, #0x3f, #0x88ea04
    // 0x88e9f4: ldur            x0, [fp, #-0x10]
    // 0x88e9f8: LeaveFrame
    //     0x88e9f8: mov             SP, fp
    //     0x88e9fc: ldp             fp, lr, [SP], #0x10
    // 0x88ea00: ret
    //     0x88ea00: ret             
    // 0x88ea04: ldur            x4, [fp, #-0x10]
    // 0x88ea08: r0 = BoxInt64Instr(r3)
    //     0x88ea08: sbfiz           x0, x3, #1, #0x1f
    //     0x88ea0c: cmp             x3, x0, asr #1
    //     0x88ea10: b.eq            #0x88ea1c
    //     0x88ea14: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88ea18: stur            x3, [x0, #7]
    // 0x88ea1c: str             x0, [SP]
    // 0x88ea20: mov             x1, x4
    // 0x88ea24: r2 = 0
    //     0x88ea24: movz            x2, #0
    // 0x88ea28: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x88ea28: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x88ea2c: r0 = substring()
    //     0x88ea2c: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x88ea30: stur            x0, [fp, #-0x20]
    // 0x88ea34: r0 = StringBuffer()
    //     0x88ea34: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x88ea38: stur            x0, [fp, #-0x28]
    // 0x88ea3c: ldur            x16, [fp, #-0x20]
    // 0x88ea40: str             x16, [SP]
    // 0x88ea44: mov             x1, x0
    // 0x88ea48: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x88ea48: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x88ea4c: r0 = StringBuffer()
    //     0x88ea4c: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0x88ea50: ldur            x3, [fp, #-0x10]
    // 0x88ea54: LoadField: r0 = r3->field_7
    //     0x88ea54: ldur            w0, [x3, #7]
    // 0x88ea58: r4 = LoadInt32Instr(r0)
    //     0x88ea58: sbfx            x4, x0, #1, #0x1f
    // 0x88ea5c: stur            x4, [fp, #-0x38]
    // 0x88ea60: r5 = LoadInt32Instr(r0)
    //     0x88ea60: sbfx            x5, x0, #1, #0x1f
    // 0x88ea64: stur            x5, [fp, #-0x30]
    // 0x88ea68: ldur            x0, [fp, #-0x18]
    // 0x88ea6c: CheckStackOverflow
    //     0x88ea6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ea70: cmp             SP, x16
    //     0x88ea74: b.ls            #0x88eca0
    // 0x88ea78: add             x6, x0, #1
    // 0x88ea7c: stur            x6, [fp, #-0x18]
    // 0x88ea80: r0 = BoxInt64Instr(r6)
    //     0x88ea80: sbfiz           x0, x6, #1, #0x1f
    //     0x88ea84: cmp             x6, x0, asr #1
    //     0x88ea88: b.eq            #0x88ea94
    //     0x88ea8c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88ea90: stur            x6, [x0, #7]
    // 0x88ea94: r1 = LoadClassIdInstr(r3)
    //     0x88ea94: ldur            x1, [x3, #-1]
    //     0x88ea98: ubfx            x1, x1, #0xc, #0x14
    // 0x88ea9c: str             x0, [SP]
    // 0x88eaa0: mov             x0, x1
    // 0x88eaa4: mov             x1, x3
    // 0x88eaa8: r2 = ";"
    //     0x88eaa8: add             x2, PP, #0x10, lsl #12  ; [pp+0x107f8] ";"
    //     0x88eaac: ldr             x2, [x2, #0x7f8]
    // 0x88eab0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x88eab0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x88eab4: r0 = GDT[cid_x0 + -0xffa]()
    //     0x88eab4: sub             lr, x0, #0xffa
    //     0x88eab8: ldr             lr, [x21, lr, lsl #3]
    //     0x88eabc: blr             lr
    // 0x88eac0: mov             x5, x0
    // 0x88eac4: ldur            x4, [fp, #-0x18]
    // 0x88eac8: stur            x5, [fp, #-0x40]
    // 0x88eacc: cmp             x4, x5
    // 0x88ead0: b.ge            #0x88eb7c
    // 0x88ead4: r0 = BoxInt64Instr(r5)
    //     0x88ead4: sbfiz           x0, x5, #1, #0x1f
    //     0x88ead8: cmp             x5, x0, asr #1
    //     0x88eadc: b.eq            #0x88eae8
    //     0x88eae0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88eae4: stur            x5, [x0, #7]
    // 0x88eae8: mov             x1, x4
    // 0x88eaec: mov             x2, x0
    // 0x88eaf0: ldur            x3, [fp, #-0x30]
    // 0x88eaf4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x88eaf4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x88eaf8: r0 = checkValidRange()
    //     0x88eaf8: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x88eafc: ldur            x1, [fp, #-0x10]
    // 0x88eb00: ldur            x2, [fp, #-0x18]
    // 0x88eb04: mov             x3, x0
    // 0x88eb08: r0 = _substringUnchecked()
    //     0x88eb08: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0x88eb0c: ldur            x1, [fp, #-8]
    // 0x88eb10: mov             x2, x0
    // 0x88eb14: r0 = decodeEntity()
    //     0x88eb14: bl              #0x88eca8  ; [package:xml/src/xml/entities/default_mapping.dart] XmlDefaultEntityMapping::decodeEntity
    // 0x88eb18: cmp             w0, NULL
    // 0x88eb1c: b.eq            #0x88eb64
    // 0x88eb20: r1 = LoadClassIdInstr(r0)
    //     0x88eb20: ldur            x1, [x0, #-1]
    //     0x88eb24: ubfx            x1, x1, #0xc, #0x14
    // 0x88eb28: str             x0, [SP]
    // 0x88eb2c: mov             x0, x1
    // 0x88eb30: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x88eb30: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x88eb34: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x88eb34: movz            x17, #0x2b03
    //     0x88eb38: add             lr, x0, x17
    //     0x88eb3c: ldr             lr, [x21, lr, lsl #3]
    //     0x88eb40: blr             lr
    // 0x88eb44: LoadField: r1 = r0->field_7
    //     0x88eb44: ldur            w1, [x0, #7]
    // 0x88eb48: cbz             w1, #0x88eb58
    // 0x88eb4c: ldur            x1, [fp, #-0x28]
    // 0x88eb50: mov             x2, x0
    // 0x88eb54: r0 = _writeString()
    //     0x88eb54: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x88eb58: ldur            x0, [fp, #-0x40]
    // 0x88eb5c: add             x1, x0, #1
    // 0x88eb60: b               #0x88eb74
    // 0x88eb64: ldur            x1, [fp, #-0x28]
    // 0x88eb68: r2 = "&"
    //     0x88eb68: ldr             x2, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0x88eb6c: r0 = _writeString()
    //     0x88eb6c: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x88eb70: ldur            x1, [fp, #-0x18]
    // 0x88eb74: mov             x4, x1
    // 0x88eb78: b               #0x88eb8c
    // 0x88eb7c: ldur            x1, [fp, #-0x28]
    // 0x88eb80: r2 = "&"
    //     0x88eb80: ldr             x2, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0x88eb84: r0 = _writeString()
    //     0x88eb84: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x88eb88: ldur            x4, [fp, #-0x18]
    // 0x88eb8c: ldur            x3, [fp, #-0x10]
    // 0x88eb90: stur            x4, [fp, #-0x18]
    // 0x88eb94: r0 = BoxInt64Instr(r4)
    //     0x88eb94: sbfiz           x0, x4, #1, #0x1f
    //     0x88eb98: cmp             x4, x0, asr #1
    //     0x88eb9c: b.eq            #0x88eba8
    //     0x88eba0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88eba4: stur            x4, [x0, #7]
    // 0x88eba8: r1 = LoadClassIdInstr(r3)
    //     0x88eba8: ldur            x1, [x3, #-1]
    //     0x88ebac: ubfx            x1, x1, #0xc, #0x14
    // 0x88ebb0: str             x0, [SP]
    // 0x88ebb4: mov             x0, x1
    // 0x88ebb8: mov             x1, x3
    // 0x88ebbc: r2 = "&"
    //     0x88ebbc: ldr             x2, [PP, #0xde0]  ; [pp+0xde0] "&"
    // 0x88ebc0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x88ebc0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x88ebc4: r0 = GDT[cid_x0 + -0xffa]()
    //     0x88ebc4: sub             lr, x0, #0xffa
    //     0x88ebc8: ldr             lr, [x21, lr, lsl #3]
    //     0x88ebcc: blr             lr
    // 0x88ebd0: mov             x4, x0
    // 0x88ebd4: stur            x4, [fp, #-0x40]
    // 0x88ebd8: cmn             x4, #1
    // 0x88ebdc: b.eq            #0x88ec64
    // 0x88ebe0: r0 = BoxInt64Instr(r4)
    //     0x88ebe0: sbfiz           x0, x4, #1, #0x1f
    //     0x88ebe4: cmp             x4, x0, asr #1
    //     0x88ebe8: b.eq            #0x88ebf4
    //     0x88ebec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x88ebf0: stur            x4, [x0, #7]
    // 0x88ebf4: ldur            x1, [fp, #-0x18]
    // 0x88ebf8: mov             x2, x0
    // 0x88ebfc: ldur            x3, [fp, #-0x38]
    // 0x88ec00: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x88ec00: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x88ec04: r0 = checkValidRange()
    //     0x88ec04: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x88ec08: ldur            x1, [fp, #-0x10]
    // 0x88ec0c: ldur            x2, [fp, #-0x18]
    // 0x88ec10: mov             x3, x0
    // 0x88ec14: r0 = _substringUnchecked()
    //     0x88ec14: bl              #0x5fff90  ; [dart:core] _StringBase::_substringUnchecked
    // 0x88ec18: r1 = LoadClassIdInstr(r0)
    //     0x88ec18: ldur            x1, [x0, #-1]
    //     0x88ec1c: ubfx            x1, x1, #0xc, #0x14
    // 0x88ec20: str             x0, [SP]
    // 0x88ec24: mov             x0, x1
    // 0x88ec28: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x88ec28: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x88ec2c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x88ec2c: movz            x17, #0x2b03
    //     0x88ec30: add             lr, x0, x17
    //     0x88ec34: ldr             lr, [x21, lr, lsl #3]
    //     0x88ec38: blr             lr
    // 0x88ec3c: LoadField: r1 = r0->field_7
    //     0x88ec3c: ldur            w1, [x0, #7]
    // 0x88ec40: cbz             w1, #0x88ec50
    // 0x88ec44: ldur            x1, [fp, #-0x28]
    // 0x88ec48: mov             x2, x0
    // 0x88ec4c: r0 = _writeString()
    //     0x88ec4c: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0x88ec50: ldur            x0, [fp, #-0x40]
    // 0x88ec54: ldur            x3, [fp, #-0x10]
    // 0x88ec58: ldur            x5, [fp, #-0x30]
    // 0x88ec5c: ldur            x4, [fp, #-0x38]
    // 0x88ec60: b               #0x88ea6c
    // 0x88ec64: ldur            x1, [fp, #-0x10]
    // 0x88ec68: ldur            x2, [fp, #-0x18]
    // 0x88ec6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x88ec6c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x88ec70: r0 = substring()
    //     0x88ec70: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0x88ec74: ldur            x1, [fp, #-0x28]
    // 0x88ec78: mov             x2, x0
    // 0x88ec7c: r0 = write()
    //     0x88ec7c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0x88ec80: ldur            x16, [fp, #-0x28]
    // 0x88ec84: str             x16, [SP]
    // 0x88ec88: r0 = toString()
    //     0x88ec88: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0x88ec8c: LeaveFrame
    //     0x88ec8c: mov             SP, fp
    //     0x88ec90: ldp             fp, lr, [SP], #0x10
    // 0x88ec94: ret
    //     0x88ec94: ret             
    // 0x88ec98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ec98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ec9c: b               #0x88e9c4
    // 0x88eca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88eca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88eca4: b               #0x88ea78
  }
  _ encodeAttributeValueWithQuotes(/* No info */) {
    // ** addr: 0xce04d4, size: 0xd8
    // 0xce04d4: EnterFrame
    //     0xce04d4: stp             fp, lr, [SP, #-0x10]!
    //     0xce04d8: mov             fp, SP
    // 0xce04dc: AllocStack(0x30)
    //     0xce04dc: sub             SP, SP, #0x30
    // 0xce04e0: SetupParameters(XmlEntityMapping this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xce04e0: mov             x4, x1
    //     0xce04e4: mov             x0, x2
    //     0xce04e8: stur            x1, [fp, #-0x10]
    //     0xce04ec: stur            x2, [fp, #-0x18]
    //     0xce04f0: stur            x3, [fp, #-0x20]
    // 0xce04f4: CheckStackOverflow
    //     0xce04f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce04f8: cmp             SP, x16
    //     0xce04fc: b.ls            #0xce05a4
    // 0xce0500: LoadField: r5 = r3->field_13
    //     0xce0500: ldur            w5, [x3, #0x13]
    // 0xce0504: DecompressPointer r5
    //     0xce0504: add             x5, x5, HEAP, lsl #32
    // 0xce0508: stur            x5, [fp, #-8]
    // 0xce050c: r1 = Null
    //     0xce050c: mov             x1, NULL
    // 0xce0510: r2 = 6
    //     0xce0510: movz            x2, #0x6
    // 0xce0514: r0 = AllocateArray()
    //     0xce0514: bl              #0xec22fc  ; AllocateArrayStub
    // 0xce0518: mov             x4, x0
    // 0xce051c: ldur            x0, [fp, #-8]
    // 0xce0520: stur            x4, [fp, #-0x28]
    // 0xce0524: StoreField: r4->field_f = r0
    //     0xce0524: stur            w0, [x4, #0xf]
    // 0xce0528: ldur            x1, [fp, #-0x10]
    // 0xce052c: ldur            x2, [fp, #-0x18]
    // 0xce0530: ldur            x3, [fp, #-0x20]
    // 0xce0534: r0 = encodeAttributeValue()
    //     0xce0534: bl              #0xce05ac  ; [package:xml/src/xml/entities/default_mapping.dart] XmlDefaultEntityMapping::encodeAttributeValue
    // 0xce0538: ldur            x1, [fp, #-0x28]
    // 0xce053c: ArrayStore: r1[1] = r0  ; List_4
    //     0xce053c: add             x25, x1, #0x13
    //     0xce0540: str             w0, [x25]
    //     0xce0544: tbz             w0, #0, #0xce0560
    //     0xce0548: ldurb           w16, [x1, #-1]
    //     0xce054c: ldurb           w17, [x0, #-1]
    //     0xce0550: and             x16, x17, x16, lsr #2
    //     0xce0554: tst             x16, HEAP, lsr #32
    //     0xce0558: b.eq            #0xce0560
    //     0xce055c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xce0560: ldur            x1, [fp, #-0x28]
    // 0xce0564: ldur            x0, [fp, #-8]
    // 0xce0568: ArrayStore: r1[2] = r0  ; List_4
    //     0xce0568: add             x25, x1, #0x17
    //     0xce056c: str             w0, [x25]
    //     0xce0570: tbz             w0, #0, #0xce058c
    //     0xce0574: ldurb           w16, [x1, #-1]
    //     0xce0578: ldurb           w17, [x0, #-1]
    //     0xce057c: and             x16, x17, x16, lsr #2
    //     0xce0580: tst             x16, HEAP, lsr #32
    //     0xce0584: b.eq            #0xce058c
    //     0xce0588: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xce058c: ldur            x16, [fp, #-0x28]
    // 0xce0590: str             x16, [SP]
    // 0xce0594: r0 = _interpolate()
    //     0xce0594: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xce0598: LeaveFrame
    //     0xce0598: mov             SP, fp
    //     0xce059c: ldp             fp, lr, [SP], #0x10
    // 0xce05a0: ret
    //     0xce05a0: ret             
    // 0xce05a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce05a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce05a8: b               #0xce0500
  }
}
