// lib: , url: package:xml/src/xml/enums/attribute_type.dart

// class id: 1051285, size: 0x8
class :: {
}

// class id: 6732, size: 0x18, field offset: 0x14
enum XmlAttributeType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  _OneByteString field_14;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4fc38, size: 0x64
    // 0xc4fc38: EnterFrame
    //     0xc4fc38: stp             fp, lr, [SP, #-0x10]!
    //     0xc4fc3c: mov             fp, SP
    // 0xc4fc40: AllocStack(0x10)
    //     0xc4fc40: sub             SP, SP, #0x10
    // 0xc4fc44: SetupParameters(XmlAttributeType this /* r1 => r0, fp-0x8 */)
    //     0xc4fc44: mov             x0, x1
    //     0xc4fc48: stur            x1, [fp, #-8]
    // 0xc4fc4c: CheckStackOverflow
    //     0xc4fc4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4fc50: cmp             SP, x16
    //     0xc4fc54: b.ls            #0xc4fc94
    // 0xc4fc58: r1 = Null
    //     0xc4fc58: mov             x1, NULL
    // 0xc4fc5c: r2 = 4
    //     0xc4fc5c: movz            x2, #0x4
    // 0xc4fc60: r0 = AllocateArray()
    //     0xc4fc60: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4fc64: r16 = "XmlAttributeType."
    //     0xc4fc64: add             x16, PP, #0x39, lsl #12  ; [pp+0x394e8] "XmlAttributeType."
    //     0xc4fc68: ldr             x16, [x16, #0x4e8]
    // 0xc4fc6c: StoreField: r0->field_f = r16
    //     0xc4fc6c: stur            w16, [x0, #0xf]
    // 0xc4fc70: ldur            x1, [fp, #-8]
    // 0xc4fc74: LoadField: r2 = r1->field_f
    //     0xc4fc74: ldur            w2, [x1, #0xf]
    // 0xc4fc78: DecompressPointer r2
    //     0xc4fc78: add             x2, x2, HEAP, lsl #32
    // 0xc4fc7c: StoreField: r0->field_13 = r2
    //     0xc4fc7c: stur            w2, [x0, #0x13]
    // 0xc4fc80: str             x0, [SP]
    // 0xc4fc84: r0 = _interpolate()
    //     0xc4fc84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4fc88: LeaveFrame
    //     0xc4fc88: mov             SP, fp
    //     0xc4fc8c: ldp             fp, lr, [SP], #0x10
    // 0xc4fc90: ret
    //     0xc4fc90: ret             
    // 0xc4fc94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4fc94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4fc98: b               #0xc4fc58
  }
}
