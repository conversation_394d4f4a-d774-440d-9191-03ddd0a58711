// lib: , url: package:xml/src/xml/enums/node_type.dart

// class id: 1051286, size: 0x8
class :: {
}

// class id: 6731, size: 0x14, field offset: 0x14
enum XmlNodeType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4fc9c, size: 0x64
    // 0xc4fc9c: EnterFrame
    //     0xc4fc9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc4fca0: mov             fp, SP
    // 0xc4fca4: AllocStack(0x10)
    //     0xc4fca4: sub             SP, SP, #0x10
    // 0xc4fca8: SetupParameters(XmlNodeType this /* r1 => r0, fp-0x8 */)
    //     0xc4fca8: mov             x0, x1
    //     0xc4fcac: stur            x1, [fp, #-8]
    // 0xc4fcb0: CheckStackOverflow
    //     0xc4fcb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4fcb4: cmp             SP, x16
    //     0xc4fcb8: b.ls            #0xc4fcf8
    // 0xc4fcbc: r1 = Null
    //     0xc4fcbc: mov             x1, NULL
    // 0xc4fcc0: r2 = 4
    //     0xc4fcc0: movz            x2, #0x4
    // 0xc4fcc4: r0 = AllocateArray()
    //     0xc4fcc4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4fcc8: r16 = "XmlNodeType."
    //     0xc4fcc8: add             x16, PP, #0x39, lsl #12  ; [pp+0x394e0] "XmlNodeType."
    //     0xc4fccc: ldr             x16, [x16, #0x4e0]
    // 0xc4fcd0: StoreField: r0->field_f = r16
    //     0xc4fcd0: stur            w16, [x0, #0xf]
    // 0xc4fcd4: ldur            x1, [fp, #-8]
    // 0xc4fcd8: LoadField: r2 = r1->field_f
    //     0xc4fcd8: ldur            w2, [x1, #0xf]
    // 0xc4fcdc: DecompressPointer r2
    //     0xc4fcdc: add             x2, x2, HEAP, lsl #32
    // 0xc4fce0: StoreField: r0->field_13 = r2
    //     0xc4fce0: stur            w2, [x0, #0x13]
    // 0xc4fce4: str             x0, [SP]
    // 0xc4fce8: r0 = _interpolate()
    //     0xc4fce8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4fcec: LeaveFrame
    //     0xc4fcec: mov             SP, fp
    //     0xc4fcf0: ldp             fp, lr, [SP], #0x10
    // 0xc4fcf4: ret
    //     0xc4fcf4: ret             
    // 0xc4fcf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4fcf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4fcfc: b               #0xc4fcbc
  }
}
