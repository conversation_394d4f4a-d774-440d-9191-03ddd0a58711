// lib: , url: package:xml/src/xml/exceptions/parent_exception.dart

// class id: 1051289, size: 0x8
class :: {
}

// class id: 270, size: 0xc, field offset: 0xc
class XmlParentException extends XmlException {

  static _ checkNoParent(/* No info */) {
    // ** addr: 0x66f094, size: 0x90
    // 0x66f094: EnterFrame
    //     0x66f094: stp             fp, lr, [SP, #-0x10]!
    //     0x66f098: mov             fp, SP
    // 0x66f09c: AllocStack(0x8)
    //     0x66f09c: sub             SP, SP, #8
    // 0x66f0a0: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x66f0a0: mov             x2, x1
    //     0x66f0a4: stur            x1, [fp, #-8]
    // 0x66f0a8: CheckStackOverflow
    //     0x66f0a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66f0ac: cmp             SP, x16
    //     0x66f0b0: b.ls            #0x66f11c
    // 0x66f0b4: r0 = LoadClassIdInstr(r2)
    //     0x66f0b4: ldur            x0, [x2, #-1]
    //     0x66f0b8: ubfx            x0, x0, #0xc, #0x14
    // 0x66f0bc: mov             x1, x2
    // 0x66f0c0: r0 = GDT[cid_x0 + -0xf4e]()
    //     0x66f0c0: sub             lr, x0, #0xf4e
    //     0x66f0c4: ldr             lr, [x21, lr, lsl #3]
    //     0x66f0c8: blr             lr
    // 0x66f0cc: cmp             w0, NULL
    // 0x66f0d0: b.ne            #0x66f0e4
    // 0x66f0d4: r0 = Null
    //     0x66f0d4: mov             x0, NULL
    // 0x66f0d8: LeaveFrame
    //     0x66f0d8: mov             SP, fp
    //     0x66f0dc: ldp             fp, lr, [SP], #0x10
    // 0x66f0e0: ret
    //     0x66f0e0: ret             
    // 0x66f0e4: ldur            x1, [fp, #-8]
    // 0x66f0e8: r0 = LoadClassIdInstr(r1)
    //     0x66f0e8: ldur            x0, [x1, #-1]
    //     0x66f0ec: ubfx            x0, x0, #0xc, #0x14
    // 0x66f0f0: r0 = GDT[cid_x0 + -0xf4e]()
    //     0x66f0f0: sub             lr, x0, #0xf4e
    //     0x66f0f4: ldr             lr, [x21, lr, lsl #3]
    //     0x66f0f8: blr             lr
    // 0x66f0fc: r0 = XmlParentException()
    //     0x66f0fc: bl              #0x66f334  ; AllocateXmlParentExceptionStub -> XmlParentException (size=0xc)
    // 0x66f100: mov             x1, x0
    // 0x66f104: r0 = "Node already has a parent, copy or remove it first"
    //     0x66f104: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e478] "Node already has a parent, copy or remove it first"
    //     0x66f108: ldr             x0, [x0, #0x478]
    // 0x66f10c: StoreField: r1->field_7 = r0
    //     0x66f10c: stur            w0, [x1, #7]
    // 0x66f110: mov             x0, x1
    // 0x66f114: r0 = Throw()
    //     0x66f114: bl              #0xec04b8  ; ThrowStub
    // 0x66f118: brk             #0
    // 0x66f11c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f11c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f120: b               #0x66f0b4
  }
  _ toString(/* No info */) {
    // ** addr: 0xc4358c, size: 0x5c
    // 0xc4358c: EnterFrame
    //     0xc4358c: stp             fp, lr, [SP, #-0x10]!
    //     0xc43590: mov             fp, SP
    // 0xc43594: AllocStack(0x8)
    //     0xc43594: sub             SP, SP, #8
    // 0xc43598: CheckStackOverflow
    //     0xc43598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4359c: cmp             SP, x16
    //     0xc435a0: b.ls            #0xc435e0
    // 0xc435a4: r1 = Null
    //     0xc435a4: mov             x1, NULL
    // 0xc435a8: r2 = 4
    //     0xc435a8: movz            x2, #0x4
    // 0xc435ac: r0 = AllocateArray()
    //     0xc435ac: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc435b0: r16 = "XmlParentException: "
    //     0xc435b0: add             x16, PP, #0x39, lsl #12  ; [pp+0x394d8] "XmlParentException: "
    //     0xc435b4: ldr             x16, [x16, #0x4d8]
    // 0xc435b8: StoreField: r0->field_f = r16
    //     0xc435b8: stur            w16, [x0, #0xf]
    // 0xc435bc: ldr             x1, [fp, #0x10]
    // 0xc435c0: LoadField: r2 = r1->field_7
    //     0xc435c0: ldur            w2, [x1, #7]
    // 0xc435c4: DecompressPointer r2
    //     0xc435c4: add             x2, x2, HEAP, lsl #32
    // 0xc435c8: StoreField: r0->field_13 = r2
    //     0xc435c8: stur            w2, [x0, #0x13]
    // 0xc435cc: str             x0, [SP]
    // 0xc435d0: r0 = _interpolate()
    //     0xc435d0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc435d4: LeaveFrame
    //     0xc435d4: mov             SP, fp
    //     0xc435d8: ldp             fp, lr, [SP], #0x10
    // 0xc435dc: ret
    //     0xc435dc: ret             
    // 0xc435e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc435e0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc435e4: b               #0xc435a4
  }
  static _ checkMatchingParent(/* No info */) {
    // ** addr: 0xeb9af4, size: 0x8c
    // 0xeb9af4: EnterFrame
    //     0xeb9af4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9af8: mov             fp, SP
    // 0xeb9afc: AllocStack(0x18)
    //     0xeb9afc: sub             SP, SP, #0x18
    // 0xeb9b00: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xeb9b00: stur            x2, [fp, #-8]
    // 0xeb9b04: CheckStackOverflow
    //     0xeb9b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9b08: cmp             SP, x16
    //     0xeb9b0c: b.ls            #0xeb9b78
    // 0xeb9b10: r0 = LoadClassIdInstr(r1)
    //     0xeb9b10: ldur            x0, [x1, #-1]
    //     0xeb9b14: ubfx            x0, x0, #0xc, #0x14
    // 0xeb9b18: r0 = GDT[cid_x0 + -0xf4e]()
    //     0xeb9b18: sub             lr, x0, #0xf4e
    //     0xeb9b1c: ldr             lr, [x21, lr, lsl #3]
    //     0xeb9b20: blr             lr
    // 0xeb9b24: r1 = LoadClassIdInstr(r0)
    //     0xeb9b24: ldur            x1, [x0, #-1]
    //     0xeb9b28: ubfx            x1, x1, #0xc, #0x14
    // 0xeb9b2c: ldur            x16, [fp, #-8]
    // 0xeb9b30: stp             x16, x0, [SP]
    // 0xeb9b34: mov             x0, x1
    // 0xeb9b38: mov             lr, x0
    // 0xeb9b3c: ldr             lr, [x21, lr, lsl #3]
    // 0xeb9b40: blr             lr
    // 0xeb9b44: tbnz            w0, #4, #0xeb9b58
    // 0xeb9b48: r0 = Null
    //     0xeb9b48: mov             x0, NULL
    // 0xeb9b4c: LeaveFrame
    //     0xeb9b4c: mov             SP, fp
    //     0xeb9b50: ldp             fp, lr, [SP], #0x10
    // 0xeb9b54: ret
    //     0xeb9b54: ret             
    // 0xeb9b58: r0 = XmlParentException()
    //     0xeb9b58: bl              #0x66f334  ; AllocateXmlParentExceptionStub -> XmlParentException (size=0xc)
    // 0xeb9b5c: mov             x1, x0
    // 0xeb9b60: r0 = "Node already has a non-matching parent"
    //     0xeb9b60: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4d5f8] "Node already has a non-matching parent"
    //     0xeb9b64: ldr             x0, [x0, #0x5f8]
    // 0xeb9b68: StoreField: r1->field_7 = r0
    //     0xeb9b68: stur            w0, [x1, #7]
    // 0xeb9b6c: mov             x0, x1
    // 0xeb9b70: r0 = Throw()
    //     0xeb9b70: bl              #0xec04b8  ; ThrowStub
    // 0xeb9b74: brk             #0
    // 0xeb9b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9b78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9b7c: b               #0xeb9b10
  }
}
