// lib: , url: package:xml/src/xml/exceptions/parser_exception.dart

// class id: 1051290, size: 0x8
class :: {
}

// class id: 267, size: 0x18, field offset: 0xc
//   transformed mixin,
abstract class _XmlParserException&XmlException&XmlFormatException extends XmlException
     with XmlFormatException {

  late final int line; // offset: 0xc
  late final int column; // offset: 0x10
  late final List<int> _lineAndColumn; // offset: 0x14

  get _ locationString(/* No info */) {
    // ** addr: 0xc43678, size: 0x114
    // 0xc43678: EnterFrame
    //     0xc43678: stp             fp, lr, [SP, #-0x10]!
    //     0xc4367c: mov             fp, SP
    // 0xc43680: AllocStack(0x18)
    //     0xc43680: sub             SP, SP, #0x18
    // 0xc43684: SetupParameters(_XmlParserException&XmlException&XmlFormatException this /* r1 => r0, fp-0x8 */)
    //     0xc43684: mov             x0, x1
    //     0xc43688: stur            x1, [fp, #-8]
    // 0xc4368c: CheckStackOverflow
    //     0xc4368c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc43690: cmp             SP, x16
    //     0xc43694: b.ls            #0xc43784
    // 0xc43698: r1 = LoadClassIdInstr(r0)
    //     0xc43698: ldur            x1, [x0, #-1]
    //     0xc4369c: ubfx            x1, x1, #0xc, #0x14
    // 0xc436a0: cmp             x1, #0x10c
    // 0xc436a4: b.ne            #0xc436bc
    // 0xc436a8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc436a8: ldur            w2, [x0, #0x17]
    // 0xc436ac: DecompressPointer r2
    //     0xc436ac: add             x2, x2, HEAP, lsl #32
    // 0xc436b0: cmp             w2, NULL
    // 0xc436b4: b.ne            #0xc436cc
    // 0xc436b8: b               #0xc436e4
    // 0xc436bc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc436bc: ldur            w2, [x0, #0x17]
    // 0xc436c0: DecompressPointer r2
    //     0xc436c0: add             x2, x2, HEAP, lsl #32
    // 0xc436c4: cmp             w2, NULL
    // 0xc436c8: b.eq            #0xc436e4
    // 0xc436cc: cmp             x1, #0x10c
    // 0xc436d0: b.ne            #0xc436ec
    // 0xc436d4: LoadField: r1 = r0->field_1b
    //     0xc436d4: ldur            w1, [x0, #0x1b]
    // 0xc436d8: DecompressPointer r1
    //     0xc436d8: add             x1, x1, HEAP, lsl #32
    // 0xc436dc: cmp             w1, NULL
    // 0xc436e0: b.ne            #0xc436ec
    // 0xc436e4: r0 = ""
    //     0xc436e4: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xc436e8: b               #0xc43778
    // 0xc436ec: r1 = Null
    //     0xc436ec: mov             x1, NULL
    // 0xc436f0: r2 = 8
    //     0xc436f0: movz            x2, #0x8
    // 0xc436f4: r0 = AllocateArray()
    //     0xc436f4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc436f8: stur            x0, [fp, #-0x10]
    // 0xc436fc: r16 = " at "
    //     0xc436fc: add             x16, PP, #0x38, lsl #12  ; [pp+0x38aa8] " at "
    //     0xc43700: ldr             x16, [x16, #0xaa8]
    // 0xc43704: StoreField: r0->field_f = r16
    //     0xc43704: stur            w16, [x0, #0xf]
    // 0xc43708: ldur            x1, [fp, #-8]
    // 0xc4370c: LoadField: r0 = r1->field_b
    //     0xc4370c: ldur            w0, [x1, #0xb]
    // 0xc43710: DecompressPointer r0
    //     0xc43710: add             x0, x0, HEAP, lsl #32
    // 0xc43714: r16 = Sentinel
    //     0xc43714: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc43718: cmp             w0, w16
    // 0xc4371c: b.ne            #0xc4372c
    // 0xc43720: r2 = line
    //     0xc43720: add             x2, PP, #0x39, lsl #12  ; [pp+0x394b0] Field <_XmlParserException&XmlException&<EMAIL>>: late final (offset: 0xc)
    //     0xc43724: ldr             x2, [x2, #0x4b0]
    // 0xc43728: r0 = InitLateFinalInstanceField()
    //     0xc43728: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xc4372c: mov             x1, x0
    // 0xc43730: ldur            x0, [fp, #-0x10]
    // 0xc43734: StoreField: r0->field_13 = r1
    //     0xc43734: stur            w1, [x0, #0x13]
    // 0xc43738: r16 = ":"
    //     0xc43738: ldr             x16, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xc4373c: ArrayStore: r0[0] = r16  ; List_4
    //     0xc4373c: stur            w16, [x0, #0x17]
    // 0xc43740: ldur            x1, [fp, #-8]
    // 0xc43744: LoadField: r0 = r1->field_f
    //     0xc43744: ldur            w0, [x1, #0xf]
    // 0xc43748: DecompressPointer r0
    //     0xc43748: add             x0, x0, HEAP, lsl #32
    // 0xc4374c: r16 = Sentinel
    //     0xc4374c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc43750: cmp             w0, w16
    // 0xc43754: b.ne            #0xc43764
    // 0xc43758: r2 = column
    //     0xc43758: add             x2, PP, #0x39, lsl #12  ; [pp+0x394b8] Field <_XmlParserException&XmlException&<EMAIL>>: late final (offset: 0x10)
    //     0xc4375c: ldr             x2, [x2, #0x4b8]
    // 0xc43760: r0 = InitLateFinalInstanceField()
    //     0xc43760: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xc43764: mov             x1, x0
    // 0xc43768: ldur            x0, [fp, #-0x10]
    // 0xc4376c: StoreField: r0->field_1b = r1
    //     0xc4376c: stur            w1, [x0, #0x1b]
    // 0xc43770: str             x0, [SP]
    // 0xc43774: r0 = _interpolate()
    //     0xc43774: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc43778: LeaveFrame
    //     0xc43778: mov             SP, fp
    //     0xc4377c: ldp             fp, lr, [SP], #0x10
    // 0xc43780: ret
    //     0xc43780: ret             
    // 0xc43784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43784: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc43788: b               #0xc43698
  }
  int column(_XmlParserException&XmlException&XmlFormatException) {
    // ** addr: 0xc4378c, size: 0x78
    // 0xc4378c: EnterFrame
    //     0xc4378c: stp             fp, lr, [SP, #-0x10]!
    //     0xc43790: mov             fp, SP
    // 0xc43794: AllocStack(0x10)
    //     0xc43794: sub             SP, SP, #0x10
    // 0xc43798: CheckStackOverflow
    //     0xc43798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4379c: cmp             SP, x16
    //     0xc437a0: b.ls            #0xc437fc
    // 0xc437a4: ldr             x1, [fp, #0x10]
    // 0xc437a8: LoadField: r0 = r1->field_13
    //     0xc437a8: ldur            w0, [x1, #0x13]
    // 0xc437ac: DecompressPointer r0
    //     0xc437ac: add             x0, x0, HEAP, lsl #32
    // 0xc437b0: r16 = Sentinel
    //     0xc437b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc437b4: cmp             w0, w16
    // 0xc437b8: b.ne            #0xc437c8
    // 0xc437bc: r2 = _lineAndColumn
    //     0xc437bc: add             x2, PP, #0x39, lsl #12  ; [pp+0x394c0] Field <_XmlParserException&XmlException&XmlFormatException@693287657._lineAndColumn@691034289>: late final (offset: 0x14)
    //     0xc437c0: ldr             x2, [x2, #0x4c0]
    // 0xc437c4: r0 = InitLateFinalInstanceField()
    //     0xc437c4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xc437c8: r1 = LoadClassIdInstr(r0)
    //     0xc437c8: ldur            x1, [x0, #-1]
    //     0xc437cc: ubfx            x1, x1, #0xc, #0x14
    // 0xc437d0: r16 = 2
    //     0xc437d0: movz            x16, #0x2
    // 0xc437d4: stp             x16, x0, [SP]
    // 0xc437d8: mov             x0, x1
    // 0xc437dc: r0 = GDT[cid_x0 + 0x13037]()
    //     0xc437dc: movz            x17, #0x3037
    //     0xc437e0: movk            x17, #0x1, lsl #16
    //     0xc437e4: add             lr, x0, x17
    //     0xc437e8: ldr             lr, [x21, lr, lsl #3]
    //     0xc437ec: blr             lr
    // 0xc437f0: LeaveFrame
    //     0xc437f0: mov             SP, fp
    //     0xc437f4: ldp             fp, lr, [SP], #0x10
    // 0xc437f8: ret
    //     0xc437f8: ret             
    // 0xc437fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc437fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc43800: b               #0xc437a4
  }
  List<int> _lineAndColumn(_XmlParserException&XmlException&XmlFormatException) {
    // ** addr: 0xc43804, size: 0xe8
    // 0xc43804: EnterFrame
    //     0xc43804: stp             fp, lr, [SP, #-0x10]!
    //     0xc43808: mov             fp, SP
    // 0xc4380c: AllocStack(0x8)
    //     0xc4380c: sub             SP, SP, #8
    // 0xc43810: CheckStackOverflow
    //     0xc43810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc43814: cmp             SP, x16
    //     0xc43818: b.ls            #0xc438dc
    // 0xc4381c: ldr             x2, [fp, #0x10]
    // 0xc43820: r0 = LoadClassIdInstr(r2)
    //     0xc43820: ldur            x0, [x2, #-1]
    //     0xc43824: ubfx            x0, x0, #0xc, #0x14
    // 0xc43828: mov             x1, x2
    // 0xc4382c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc4382c: sub             lr, x0, #1, lsl #12
    //     0xc43830: ldr             lr, [x21, lr, lsl #3]
    //     0xc43834: blr             lr
    // 0xc43838: cmp             w0, NULL
    // 0xc4383c: b.eq            #0xc438c8
    // 0xc43840: ldr             x2, [fp, #0x10]
    // 0xc43844: r0 = LoadClassIdInstr(r2)
    //     0xc43844: ldur            x0, [x2, #-1]
    //     0xc43848: ubfx            x0, x0, #0xc, #0x14
    // 0xc4384c: mov             x1, x2
    // 0xc43850: r0 = GDT[cid_x0 + -0xffe]()
    //     0xc43850: sub             lr, x0, #0xffe
    //     0xc43854: ldr             lr, [x21, lr, lsl #3]
    //     0xc43858: blr             lr
    // 0xc4385c: cmp             w0, NULL
    // 0xc43860: b.eq            #0xc438c8
    // 0xc43864: ldr             x2, [fp, #0x10]
    // 0xc43868: r0 = LoadClassIdInstr(r2)
    //     0xc43868: ldur            x0, [x2, #-1]
    //     0xc4386c: ubfx            x0, x0, #0xc, #0x14
    // 0xc43870: mov             x1, x2
    // 0xc43874: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc43874: sub             lr, x0, #1, lsl #12
    //     0xc43878: ldr             lr, [x21, lr, lsl #3]
    //     0xc4387c: blr             lr
    // 0xc43880: mov             x2, x0
    // 0xc43884: stur            x2, [fp, #-8]
    // 0xc43888: cmp             w2, NULL
    // 0xc4388c: b.eq            #0xc438e4
    // 0xc43890: ldr             x1, [fp, #0x10]
    // 0xc43894: r0 = LoadClassIdInstr(r1)
    //     0xc43894: ldur            x0, [x1, #-1]
    //     0xc43898: ubfx            x0, x0, #0xc, #0x14
    // 0xc4389c: r0 = GDT[cid_x0 + -0xffe]()
    //     0xc4389c: sub             lr, x0, #0xffe
    //     0xc438a0: ldr             lr, [x21, lr, lsl #3]
    //     0xc438a4: blr             lr
    // 0xc438a8: cmp             w0, NULL
    // 0xc438ac: b.eq            #0xc438e8
    // 0xc438b0: r2 = LoadInt32Instr(r0)
    //     0xc438b0: sbfx            x2, x0, #1, #0x1f
    //     0xc438b4: tbz             w0, #0, #0xc438bc
    //     0xc438b8: ldur            x2, [x0, #7]
    // 0xc438bc: ldur            x1, [fp, #-8]
    // 0xc438c0: r0 = lineAndColumnOf()
    //     0xc438c0: bl              #0xc3b80c  ; [package:petitparser/src/core/token.dart] Token::lineAndColumnOf
    // 0xc438c4: b               #0xc438d0
    // 0xc438c8: r0 = const [0, 0]
    //     0xc438c8: add             x0, PP, #0x39, lsl #12  ; [pp+0x394c8] List<int>(2)
    //     0xc438cc: ldr             x0, [x0, #0x4c8]
    // 0xc438d0: LeaveFrame
    //     0xc438d0: mov             SP, fp
    //     0xc438d4: ldp             fp, lr, [SP], #0x10
    // 0xc438d8: ret
    //     0xc438d8: ret             
    // 0xc438dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc438dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc438e0: b               #0xc4381c
    // 0xc438e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc438e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc438e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc438e8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  int line(_XmlParserException&XmlException&XmlFormatException) {
    // ** addr: 0xc438ec, size: 0x74
    // 0xc438ec: EnterFrame
    //     0xc438ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc438f0: mov             fp, SP
    // 0xc438f4: AllocStack(0x10)
    //     0xc438f4: sub             SP, SP, #0x10
    // 0xc438f8: CheckStackOverflow
    //     0xc438f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc438fc: cmp             SP, x16
    //     0xc43900: b.ls            #0xc43958
    // 0xc43904: ldr             x1, [fp, #0x10]
    // 0xc43908: LoadField: r0 = r1->field_13
    //     0xc43908: ldur            w0, [x1, #0x13]
    // 0xc4390c: DecompressPointer r0
    //     0xc4390c: add             x0, x0, HEAP, lsl #32
    // 0xc43910: r16 = Sentinel
    //     0xc43910: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc43914: cmp             w0, w16
    // 0xc43918: b.ne            #0xc43928
    // 0xc4391c: r2 = _lineAndColumn
    //     0xc4391c: add             x2, PP, #0x39, lsl #12  ; [pp+0x394c0] Field <_XmlParserException&XmlException&XmlFormatException@693287657._lineAndColumn@691034289>: late final (offset: 0x14)
    //     0xc43920: ldr             x2, [x2, #0x4c0]
    // 0xc43924: r0 = InitLateFinalInstanceField()
    //     0xc43924: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xc43928: r1 = LoadClassIdInstr(r0)
    //     0xc43928: ldur            x1, [x0, #-1]
    //     0xc4392c: ubfx            x1, x1, #0xc, #0x14
    // 0xc43930: stp             xzr, x0, [SP]
    // 0xc43934: mov             x0, x1
    // 0xc43938: r0 = GDT[cid_x0 + 0x13037]()
    //     0xc43938: movz            x17, #0x3037
    //     0xc4393c: movk            x17, #0x1, lsl #16
    //     0xc43940: add             lr, x0, x17
    //     0xc43944: ldr             lr, [x21, lr, lsl #3]
    //     0xc43948: blr             lr
    // 0xc4394c: LeaveFrame
    //     0xc4394c: mov             SP, fp
    //     0xc43950: ldp             fp, lr, [SP], #0x10
    // 0xc43954: ret
    //     0xc43954: ret             
    // 0xc43958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43958: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4395c: b               #0xc43904
  }
  get _ offset(/* No info */) {
    // ** addr: 0xe3fff0, size: 0x4c
    // 0xe3fff0: r2 = LoadClassIdInstr(r1)
    //     0xe3fff0: ldur            x2, [x1, #-1]
    //     0xe3fff4: ubfx            x2, x2, #0xc, #0x14
    // 0xe3fff8: cmp             x2, #0x10c
    // 0xe3fffc: b.ne            #0xe40010
    // 0xe40000: LoadField: r2 = r1->field_1b
    //     0xe40000: ldur            w2, [x1, #0x1b]
    // 0xe40004: DecompressPointer r2
    //     0xe40004: add             x2, x2, HEAP, lsl #32
    // 0xe40008: mov             x0, x2
    // 0xe4000c: b               #0xe40038
    // 0xe40010: LoadField: r2 = r1->field_1b
    //     0xe40010: ldur            x2, [x1, #0x1b]
    // 0xe40014: r0 = BoxInt64Instr(r2)
    //     0xe40014: sbfiz           x0, x2, #1, #0x1f
    //     0xe40018: cmp             x2, x0, asr #1
    //     0xe4001c: b.eq            #0xe40038
    //     0xe40020: stp             fp, lr, [SP, #-0x10]!
    //     0xe40024: mov             fp, SP
    //     0xe40028: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe4002c: mov             SP, fp
    //     0xe40030: ldp             fp, lr, [SP], #0x10
    //     0xe40034: stur            x2, [x0, #7]
    // 0xe40038: ret
    //     0xe40038: ret             
  }
  get _ source(/* No info */) {
    // ** addr: 0xea1b58, size: 0x30
    // 0xea1b58: r2 = LoadClassIdInstr(r1)
    //     0xea1b58: ldur            x2, [x1, #-1]
    //     0xea1b5c: ubfx            x2, x2, #0xc, #0x14
    // 0xea1b60: cmp             x2, #0x10c
    // 0xea1b64: b.ne            #0xea1b78
    // 0xea1b68: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xea1b68: ldur            w2, [x1, #0x17]
    // 0xea1b6c: DecompressPointer r2
    //     0xea1b6c: add             x2, x2, HEAP, lsl #32
    // 0xea1b70: mov             x0, x2
    // 0xea1b74: b               #0xea1b84
    // 0xea1b78: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xea1b78: ldur            w2, [x1, #0x17]
    // 0xea1b7c: DecompressPointer r2
    //     0xea1b7c: add             x2, x2, HEAP, lsl #32
    // 0xea1b80: mov             x0, x2
    // 0xea1b84: ret
    //     0xea1b84: ret             
  }
}

// class id: 269, size: 0x24, field offset: 0x18
class XmlParserException extends _XmlParserException&XmlException&XmlFormatException {

  _ toString(/* No info */) {
    // ** addr: 0xc435e8, size: 0x90
    // 0xc435e8: EnterFrame
    //     0xc435e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc435ec: mov             fp, SP
    // 0xc435f0: AllocStack(0x10)
    //     0xc435f0: sub             SP, SP, #0x10
    // 0xc435f4: CheckStackOverflow
    //     0xc435f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc435f8: cmp             SP, x16
    //     0xc435fc: b.ls            #0xc43670
    // 0xc43600: r1 = Null
    //     0xc43600: mov             x1, NULL
    // 0xc43604: r2 = 6
    //     0xc43604: movz            x2, #0x6
    // 0xc43608: r0 = AllocateArray()
    //     0xc43608: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4360c: stur            x0, [fp, #-8]
    // 0xc43610: r16 = "XmlParserException: "
    //     0xc43610: add             x16, PP, #0x39, lsl #12  ; [pp+0x394d0] "XmlParserException: "
    //     0xc43614: ldr             x16, [x16, #0x4d0]
    // 0xc43618: StoreField: r0->field_f = r16
    //     0xc43618: stur            w16, [x0, #0xf]
    // 0xc4361c: ldr             x1, [fp, #0x10]
    // 0xc43620: LoadField: r2 = r1->field_7
    //     0xc43620: ldur            w2, [x1, #7]
    // 0xc43624: DecompressPointer r2
    //     0xc43624: add             x2, x2, HEAP, lsl #32
    // 0xc43628: StoreField: r0->field_13 = r2
    //     0xc43628: stur            w2, [x0, #0x13]
    // 0xc4362c: r0 = locationString()
    //     0xc4362c: bl              #0xc43678  ; [package:xml/src/xml/exceptions/parser_exception.dart] _XmlParserException&XmlException&XmlFormatException::locationString
    // 0xc43630: ldur            x1, [fp, #-8]
    // 0xc43634: ArrayStore: r1[2] = r0  ; List_4
    //     0xc43634: add             x25, x1, #0x17
    //     0xc43638: str             w0, [x25]
    //     0xc4363c: tbz             w0, #0, #0xc43658
    //     0xc43640: ldurb           w16, [x1, #-1]
    //     0xc43644: ldurb           w17, [x0, #-1]
    //     0xc43648: and             x16, x17, x16, lsr #2
    //     0xc4364c: tst             x16, HEAP, lsr #32
    //     0xc43650: b.eq            #0xc43658
    //     0xc43654: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc43658: ldur            x16, [fp, #-8]
    // 0xc4365c: str             x16, [SP]
    // 0xc43660: r0 = _interpolate()
    //     0xc43660: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc43664: LeaveFrame
    //     0xc43664: mov             SP, fp
    //     0xc43668: ldp             fp, lr, [SP], #0x10
    // 0xc4366c: ret
    //     0xc4366c: ret             
    // 0xc43670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43670: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc43674: b               #0xc43600
  }
  get _ position(/* No info */) {
    // ** addr: 0xeb9cb4, size: 0x2c
    // 0xeb9cb4: LoadField: r2 = r1->field_1b
    //     0xeb9cb4: ldur            x2, [x1, #0x1b]
    // 0xeb9cb8: r0 = BoxInt64Instr(r2)
    //     0xeb9cb8: sbfiz           x0, x2, #1, #0x1f
    //     0xeb9cbc: cmp             x2, x0, asr #1
    //     0xeb9cc0: b.eq            #0xeb9cdc
    //     0xeb9cc4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9cc8: mov             fp, SP
    //     0xeb9ccc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb9cd0: mov             SP, fp
    //     0xeb9cd4: ldp             fp, lr, [SP], #0x10
    //     0xeb9cd8: stur            x2, [x0, #7]
    // 0xeb9cdc: ret
    //     0xeb9cdc: ret             
  }
}
