// lib: , url: package:xml/src/xml/exceptions/tag_exception.dart

// class id: 1051291, size: 0x8
class :: {
}

// class id: 268, size: 0x20, field offset: 0x18
class XmlTagException extends _XmlParserException&XmlException&XmlFormatException {

  factory _ XmlTagException.missingClosingTag(/* No info */) {
    // ** addr: 0x7515d4, size: 0xb8
    // 0x7515d4: EnterFrame
    //     0x7515d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7515d8: mov             fp, SP
    // 0x7515dc: AllocStack(0x20)
    //     0x7515dc: sub             SP, SP, #0x20
    // 0x7515e0: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0x7515e0: mov             x0, x2
    //     0x7515e4: stur            x2, [fp, #-8]
    //     0x7515e8: stur            x3, [fp, #-0x10]
    //     0x7515ec: stur            x5, [fp, #-0x18]
    // 0x7515f0: CheckStackOverflow
    //     0x7515f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7515f4: cmp             SP, x16
    //     0x7515f8: b.ls            #0x751684
    // 0x7515fc: r1 = Null
    //     0x7515fc: mov             x1, NULL
    // 0x751600: r2 = 6
    //     0x751600: movz            x2, #0x6
    // 0x751604: r0 = AllocateArray()
    //     0x751604: bl              #0xec22fc  ; AllocateArrayStub
    // 0x751608: r16 = "Missing </"
    //     0x751608: add             x16, PP, #0x26, lsl #12  ; [pp+0x264b0] "Missing </"
    //     0x75160c: ldr             x16, [x16, #0x4b0]
    // 0x751610: StoreField: r0->field_f = r16
    //     0x751610: stur            w16, [x0, #0xf]
    // 0x751614: ldur            x1, [fp, #-8]
    // 0x751618: StoreField: r0->field_13 = r1
    //     0x751618: stur            w1, [x0, #0x13]
    // 0x75161c: r16 = ">"
    //     0x75161c: ldr             x16, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x751620: ArrayStore: r0[0] = r16  ; List_4
    //     0x751620: stur            w16, [x0, #0x17]
    // 0x751624: str             x0, [SP]
    // 0x751628: r0 = _interpolate()
    //     0x751628: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x75162c: stur            x0, [fp, #-8]
    // 0x751630: r0 = XmlTagException()
    //     0x751630: bl              #0x75168c  ; AllocateXmlTagExceptionStub -> XmlTagException (size=0x20)
    // 0x751634: mov             x3, x0
    // 0x751638: ldur            x2, [fp, #-0x10]
    // 0x75163c: ArrayStore: r3[0] = r2  ; List_4
    //     0x75163c: stur            w2, [x3, #0x17]
    // 0x751640: ldur            x2, [fp, #-0x18]
    // 0x751644: r0 = BoxInt64Instr(r2)
    //     0x751644: sbfiz           x0, x2, #1, #0x1f
    //     0x751648: cmp             x2, x0, asr #1
    //     0x75164c: b.eq            #0x751658
    //     0x751650: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x751654: stur            x2, [x0, #7]
    // 0x751658: StoreField: r3->field_1b = r0
    //     0x751658: stur            w0, [x3, #0x1b]
    // 0x75165c: r1 = Sentinel
    //     0x75165c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751660: StoreField: r3->field_b = r1
    //     0x751660: stur            w1, [x3, #0xb]
    // 0x751664: StoreField: r3->field_f = r1
    //     0x751664: stur            w1, [x3, #0xf]
    // 0x751668: StoreField: r3->field_13 = r1
    //     0x751668: stur            w1, [x3, #0x13]
    // 0x75166c: ldur            x1, [fp, #-8]
    // 0x751670: StoreField: r3->field_7 = r1
    //     0x751670: stur            w1, [x3, #7]
    // 0x751674: mov             x0, x3
    // 0x751678: LeaveFrame
    //     0x751678: mov             SP, fp
    //     0x75167c: ldp             fp, lr, [SP], #0x10
    // 0x751680: ret
    //     0x751680: ret             
    // 0x751684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x751684: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x751688: b               #0x7515fc
  }
  factory _ XmlTagException.mismatchClosingTag(/* No info */) {
    // ** addr: 0x751e40, size: 0xb4
    // 0x751e40: EnterFrame
    //     0x751e40: stp             fp, lr, [SP, #-0x10]!
    //     0x751e44: mov             fp, SP
    // 0x751e48: AllocStack(0x28)
    //     0x751e48: sub             SP, SP, #0x28
    // 0x751e4c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */, dynamic _ /* r6 => r6, fp-0x20 */)
    //     0x751e4c: mov             x0, x2
    //     0x751e50: stur            x2, [fp, #-8]
    //     0x751e54: stur            x3, [fp, #-0x10]
    //     0x751e58: stur            x5, [fp, #-0x18]
    //     0x751e5c: stur            x6, [fp, #-0x20]
    // 0x751e60: CheckStackOverflow
    //     0x751e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x751e64: cmp             SP, x16
    //     0x751e68: b.ls            #0x751eec
    // 0x751e6c: r1 = Null
    //     0x751e6c: mov             x1, NULL
    // 0x751e70: r2 = 10
    //     0x751e70: movz            x2, #0xa
    // 0x751e74: r0 = AllocateArray()
    //     0x751e74: bl              #0xec22fc  ; AllocateArrayStub
    // 0x751e78: r16 = "Expected </"
    //     0x751e78: add             x16, PP, #0x26, lsl #12  ; [pp+0x26530] "Expected </"
    //     0x751e7c: ldr             x16, [x16, #0x530]
    // 0x751e80: StoreField: r0->field_f = r16
    //     0x751e80: stur            w16, [x0, #0xf]
    // 0x751e84: ldur            x1, [fp, #-8]
    // 0x751e88: StoreField: r0->field_13 = r1
    //     0x751e88: stur            w1, [x0, #0x13]
    // 0x751e8c: r16 = ">, but found </"
    //     0x751e8c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26538] ">, but found </"
    //     0x751e90: ldr             x16, [x16, #0x538]
    // 0x751e94: ArrayStore: r0[0] = r16  ; List_4
    //     0x751e94: stur            w16, [x0, #0x17]
    // 0x751e98: ldur            x1, [fp, #-0x10]
    // 0x751e9c: StoreField: r0->field_1b = r1
    //     0x751e9c: stur            w1, [x0, #0x1b]
    // 0x751ea0: r16 = ">"
    //     0x751ea0: ldr             x16, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x751ea4: StoreField: r0->field_1f = r16
    //     0x751ea4: stur            w16, [x0, #0x1f]
    // 0x751ea8: str             x0, [SP]
    // 0x751eac: r0 = _interpolate()
    //     0x751eac: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x751eb0: stur            x0, [fp, #-8]
    // 0x751eb4: r0 = XmlTagException()
    //     0x751eb4: bl              #0x75168c  ; AllocateXmlTagExceptionStub -> XmlTagException (size=0x20)
    // 0x751eb8: ldur            x1, [fp, #-0x18]
    // 0x751ebc: ArrayStore: r0[0] = r1  ; List_4
    //     0x751ebc: stur            w1, [x0, #0x17]
    // 0x751ec0: ldur            x1, [fp, #-0x20]
    // 0x751ec4: StoreField: r0->field_1b = r1
    //     0x751ec4: stur            w1, [x0, #0x1b]
    // 0x751ec8: r1 = Sentinel
    //     0x751ec8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751ecc: StoreField: r0->field_b = r1
    //     0x751ecc: stur            w1, [x0, #0xb]
    // 0x751ed0: StoreField: r0->field_f = r1
    //     0x751ed0: stur            w1, [x0, #0xf]
    // 0x751ed4: StoreField: r0->field_13 = r1
    //     0x751ed4: stur            w1, [x0, #0x13]
    // 0x751ed8: ldur            x1, [fp, #-8]
    // 0x751edc: StoreField: r0->field_7 = r1
    //     0x751edc: stur            w1, [x0, #7]
    // 0x751ee0: LeaveFrame
    //     0x751ee0: mov             SP, fp
    //     0x751ee4: ldp             fp, lr, [SP], #0x10
    // 0x751ee8: ret
    //     0x751ee8: ret             
    // 0x751eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x751eec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x751ef0: b               #0x751e6c
  }
  factory _ XmlTagException.unexpectedClosingTag(/* No info */) {
    // ** addr: 0x751ef4, size: 0x9c
    // 0x751ef4: EnterFrame
    //     0x751ef4: stp             fp, lr, [SP, #-0x10]!
    //     0x751ef8: mov             fp, SP
    // 0x751efc: AllocStack(0x20)
    //     0x751efc: sub             SP, SP, #0x20
    // 0x751f00: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0x751f00: mov             x0, x2
    //     0x751f04: stur            x2, [fp, #-8]
    //     0x751f08: stur            x3, [fp, #-0x10]
    //     0x751f0c: stur            x5, [fp, #-0x18]
    // 0x751f10: CheckStackOverflow
    //     0x751f10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x751f14: cmp             SP, x16
    //     0x751f18: b.ls            #0x751f88
    // 0x751f1c: r1 = Null
    //     0x751f1c: mov             x1, NULL
    // 0x751f20: r2 = 6
    //     0x751f20: movz            x2, #0x6
    // 0x751f24: r0 = AllocateArray()
    //     0x751f24: bl              #0xec22fc  ; AllocateArrayStub
    // 0x751f28: r16 = "Unexpected </"
    //     0x751f28: add             x16, PP, #0x26, lsl #12  ; [pp+0x26540] "Unexpected </"
    //     0x751f2c: ldr             x16, [x16, #0x540]
    // 0x751f30: StoreField: r0->field_f = r16
    //     0x751f30: stur            w16, [x0, #0xf]
    // 0x751f34: ldur            x1, [fp, #-8]
    // 0x751f38: StoreField: r0->field_13 = r1
    //     0x751f38: stur            w1, [x0, #0x13]
    // 0x751f3c: r16 = ">"
    //     0x751f3c: ldr             x16, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x751f40: ArrayStore: r0[0] = r16  ; List_4
    //     0x751f40: stur            w16, [x0, #0x17]
    // 0x751f44: str             x0, [SP]
    // 0x751f48: r0 = _interpolate()
    //     0x751f48: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x751f4c: stur            x0, [fp, #-8]
    // 0x751f50: r0 = XmlTagException()
    //     0x751f50: bl              #0x75168c  ; AllocateXmlTagExceptionStub -> XmlTagException (size=0x20)
    // 0x751f54: ldur            x1, [fp, #-0x10]
    // 0x751f58: ArrayStore: r0[0] = r1  ; List_4
    //     0x751f58: stur            w1, [x0, #0x17]
    // 0x751f5c: ldur            x1, [fp, #-0x18]
    // 0x751f60: StoreField: r0->field_1b = r1
    //     0x751f60: stur            w1, [x0, #0x1b]
    // 0x751f64: r1 = Sentinel
    //     0x751f64: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751f68: StoreField: r0->field_b = r1
    //     0x751f68: stur            w1, [x0, #0xb]
    // 0x751f6c: StoreField: r0->field_f = r1
    //     0x751f6c: stur            w1, [x0, #0xf]
    // 0x751f70: StoreField: r0->field_13 = r1
    //     0x751f70: stur            w1, [x0, #0x13]
    // 0x751f74: ldur            x1, [fp, #-8]
    // 0x751f78: StoreField: r0->field_7 = r1
    //     0x751f78: stur            w1, [x0, #7]
    // 0x751f7c: LeaveFrame
    //     0x751f7c: mov             SP, fp
    //     0x751f80: ldp             fp, lr, [SP], #0x10
    // 0x751f84: ret
    //     0x751f84: ret             
    // 0x751f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x751f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x751f8c: b               #0x751f1c
  }
  _ toString(/* No info */) {
    // ** addr: 0xc43960, size: 0x90
    // 0xc43960: EnterFrame
    //     0xc43960: stp             fp, lr, [SP, #-0x10]!
    //     0xc43964: mov             fp, SP
    // 0xc43968: AllocStack(0x10)
    //     0xc43968: sub             SP, SP, #0x10
    // 0xc4396c: CheckStackOverflow
    //     0xc4396c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc43970: cmp             SP, x16
    //     0xc43974: b.ls            #0xc439e8
    // 0xc43978: r1 = Null
    //     0xc43978: mov             x1, NULL
    // 0xc4397c: r2 = 6
    //     0xc4397c: movz            x2, #0x6
    // 0xc43980: r0 = AllocateArray()
    //     0xc43980: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc43984: stur            x0, [fp, #-8]
    // 0xc43988: r16 = "XmlTagException: "
    //     0xc43988: add             x16, PP, #0x39, lsl #12  ; [pp+0x394a8] "XmlTagException: "
    //     0xc4398c: ldr             x16, [x16, #0x4a8]
    // 0xc43990: StoreField: r0->field_f = r16
    //     0xc43990: stur            w16, [x0, #0xf]
    // 0xc43994: ldr             x1, [fp, #0x10]
    // 0xc43998: LoadField: r2 = r1->field_7
    //     0xc43998: ldur            w2, [x1, #7]
    // 0xc4399c: DecompressPointer r2
    //     0xc4399c: add             x2, x2, HEAP, lsl #32
    // 0xc439a0: StoreField: r0->field_13 = r2
    //     0xc439a0: stur            w2, [x0, #0x13]
    // 0xc439a4: r0 = locationString()
    //     0xc439a4: bl              #0xc43678  ; [package:xml/src/xml/exceptions/parser_exception.dart] _XmlParserException&XmlException&XmlFormatException::locationString
    // 0xc439a8: ldur            x1, [fp, #-8]
    // 0xc439ac: ArrayStore: r1[2] = r0  ; List_4
    //     0xc439ac: add             x25, x1, #0x17
    //     0xc439b0: str             w0, [x25]
    //     0xc439b4: tbz             w0, #0, #0xc439d0
    //     0xc439b8: ldurb           w16, [x1, #-1]
    //     0xc439bc: ldurb           w17, [x0, #-1]
    //     0xc439c0: and             x16, x17, x16, lsr #2
    //     0xc439c4: tst             x16, HEAP, lsr #32
    //     0xc439c8: b.eq            #0xc439d0
    //     0xc439cc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc439d0: ldur            x16, [fp, #-8]
    // 0xc439d4: str             x16, [SP]
    // 0xc439d8: r0 = _interpolate()
    //     0xc439d8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc439dc: LeaveFrame
    //     0xc439dc: mov             SP, fp
    //     0xc439e0: ldp             fp, lr, [SP], #0x10
    // 0xc439e4: ret
    //     0xc439e4: ret             
    // 0xc439e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc439e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc439ec: b               #0xc43978
  }
  get _ position(/* No info */) {
    // ** addr: 0xeb9ce0, size: 0xc
    // 0xeb9ce0: LoadField: r0 = r1->field_1b
    //     0xeb9ce0: ldur            w0, [x1, #0x1b]
    // 0xeb9ce4: DecompressPointer r0
    //     0xeb9ce4: add             x0, x0, HEAP, lsl #32
    // 0xeb9ce8: ret
    //     0xeb9ce8: ret             
  }
  static _ checkClosingTag(/* No info */) {
    // ** addr: 0xebafac, size: 0x7c
    // 0xebafac: EnterFrame
    //     0xebafac: stp             fp, lr, [SP, #-0x10]!
    //     0xebafb0: mov             fp, SP
    // 0xebafb4: AllocStack(0x20)
    //     0xebafb4: sub             SP, SP, #0x20
    // 0xebafb8: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xebafb8: mov             x3, x2
    //     0xebafbc: stur            x2, [fp, #-0x10]
    //     0xebafc0: mov             x2, x1
    //     0xebafc4: stur            x1, [fp, #-8]
    // 0xebafc8: CheckStackOverflow
    //     0xebafc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebafcc: cmp             SP, x16
    //     0xebafd0: b.ls            #0xebb020
    // 0xebafd4: r0 = LoadClassIdInstr(r2)
    //     0xebafd4: ldur            x0, [x2, #-1]
    //     0xebafd8: ubfx            x0, x0, #0xc, #0x14
    // 0xebafdc: stp             x3, x2, [SP]
    // 0xebafe0: mov             lr, x0
    // 0xebafe4: ldr             lr, [x21, lr, lsl #3]
    // 0xebafe8: blr             lr
    // 0xebafec: tbnz            w0, #4, #0xebb000
    // 0xebaff0: r0 = Null
    //     0xebaff0: mov             x0, NULL
    // 0xebaff4: LeaveFrame
    //     0xebaff4: mov             SP, fp
    //     0xebaff8: ldp             fp, lr, [SP], #0x10
    // 0xebaffc: ret
    //     0xebaffc: ret             
    // 0xebb000: ldur            x2, [fp, #-8]
    // 0xebb004: ldur            x3, [fp, #-0x10]
    // 0xebb008: r1 = Null
    //     0xebb008: mov             x1, NULL
    // 0xebb00c: r5 = Null
    //     0xebb00c: mov             x5, NULL
    // 0xebb010: r6 = Null
    //     0xebb010: mov             x6, NULL
    // 0xebb014: r0 = XmlTagException.mismatchClosingTag()
    //     0xebb014: bl              #0x751e40  ; [package:xml/src/xml/exceptions/tag_exception.dart] XmlTagException::XmlTagException.mismatchClosingTag
    // 0xebb018: r0 = Throw()
    //     0xebb018: bl              #0xec04b8  ; ThrowStub
    // 0xebb01c: brk             #0
    // 0xebb020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb020: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb024: b               #0xebafd4
  }
}
