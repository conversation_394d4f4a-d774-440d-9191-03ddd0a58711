// lib: , url: package:xml/src/xml/exceptions/type_exception.dart

// class id: 1051292, size: 0x8
class :: {
}

// class id: 266, size: 0xc, field offset: 0xc
class XmlNodeTypeException extends XmlException {

  static _ checkValidType(/* No info */) {
    // ** addr: 0x66f124, size: 0x140
    // 0x66f124: EnterFrame
    //     0x66f124: stp             fp, lr, [SP, #-0x10]!
    //     0x66f128: mov             fp, SP
    // 0x66f12c: AllocStack(0x20)
    //     0x66f12c: sub             SP, SP, #0x20
    // 0x66f130: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x66f130: mov             x3, x1
    //     0x66f134: stur            x1, [fp, #-8]
    //     0x66f138: stur            x2, [fp, #-0x10]
    // 0x66f13c: CheckStackOverflow
    //     0x66f13c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66f140: cmp             SP, x16
    //     0x66f144: b.ls            #0x66f25c
    // 0x66f148: r0 = LoadClassIdInstr(r3)
    //     0x66f148: ldur            x0, [x3, #-1]
    //     0x66f14c: ubfx            x0, x0, #0xc, #0x14
    // 0x66f150: mov             x1, x3
    // 0x66f154: r0 = GDT[cid_x0 + -0xf93]()
    //     0x66f154: sub             lr, x0, #0xf93
    //     0x66f158: ldr             lr, [x21, lr, lsl #3]
    //     0x66f15c: blr             lr
    // 0x66f160: ldur            x1, [fp, #-0x10]
    // 0x66f164: mov             x2, x0
    // 0x66f168: r0 = contains()
    //     0x66f168: bl              #0x86aab8  ; [dart:_compact_hash] __ConstSet&_HashVMImmutableBase&SetMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashSetMixin&_UnmodifiableSetMixin&_ImmutableLinkedHashSetMixin::contains
    // 0x66f16c: tbnz            w0, #4, #0x66f180
    // 0x66f170: r0 = Null
    //     0x66f170: mov             x0, NULL
    // 0x66f174: LeaveFrame
    //     0x66f174: mov             SP, fp
    //     0x66f178: ldp             fp, lr, [SP], #0x10
    // 0x66f17c: ret
    //     0x66f17c: ret             
    // 0x66f180: ldur            x0, [fp, #-8]
    // 0x66f184: r1 = Null
    //     0x66f184: mov             x1, NULL
    // 0x66f188: r2 = 8
    //     0x66f188: movz            x2, #0x8
    // 0x66f18c: r0 = AllocateArray()
    //     0x66f18c: bl              #0xec22fc  ; AllocateArrayStub
    // 0x66f190: mov             x2, x0
    // 0x66f194: stur            x2, [fp, #-0x18]
    // 0x66f198: r16 = "Got "
    //     0x66f198: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e480] "Got "
    //     0x66f19c: ldr             x16, [x16, #0x480]
    // 0x66f1a0: StoreField: r2->field_f = r16
    //     0x66f1a0: stur            w16, [x2, #0xf]
    // 0x66f1a4: ldur            x1, [fp, #-8]
    // 0x66f1a8: r0 = LoadClassIdInstr(r1)
    //     0x66f1a8: ldur            x0, [x1, #-1]
    //     0x66f1ac: ubfx            x0, x0, #0xc, #0x14
    // 0x66f1b0: r0 = GDT[cid_x0 + -0xf93]()
    //     0x66f1b0: sub             lr, x0, #0xf93
    //     0x66f1b4: ldr             lr, [x21, lr, lsl #3]
    //     0x66f1b8: blr             lr
    // 0x66f1bc: ldur            x1, [fp, #-0x18]
    // 0x66f1c0: ArrayStore: r1[1] = r0  ; List_4
    //     0x66f1c0: add             x25, x1, #0x13
    //     0x66f1c4: str             w0, [x25]
    //     0x66f1c8: tbz             w0, #0, #0x66f1e4
    //     0x66f1cc: ldurb           w16, [x1, #-1]
    //     0x66f1d0: ldurb           w17, [x0, #-1]
    //     0x66f1d4: and             x16, x17, x16, lsr #2
    //     0x66f1d8: tst             x16, HEAP, lsr #32
    //     0x66f1dc: b.eq            #0x66f1e4
    //     0x66f1e0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x66f1e4: ldur            x0, [fp, #-0x18]
    // 0x66f1e8: r16 = ", but expected one of "
    //     0x66f1e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e488] ", but expected one of "
    //     0x66f1ec: ldr             x16, [x16, #0x488]
    // 0x66f1f0: ArrayStore: r0[0] = r16  ; List_4
    //     0x66f1f0: stur            w16, [x0, #0x17]
    // 0x66f1f4: r16 = ", "
    //     0x66f1f4: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0x66f1f8: str             x16, [SP]
    // 0x66f1fc: ldur            x1, [fp, #-0x10]
    // 0x66f200: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x66f200: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x66f204: r0 = join()
    //     0x66f204: bl              #0x86c3ac  ; [dart:_compact_hash] __ConstSet&_HashVMImmutableBase&SetMixin::join
    // 0x66f208: ldur            x1, [fp, #-0x18]
    // 0x66f20c: ArrayStore: r1[3] = r0  ; List_4
    //     0x66f20c: add             x25, x1, #0x1b
    //     0x66f210: str             w0, [x25]
    //     0x66f214: tbz             w0, #0, #0x66f230
    //     0x66f218: ldurb           w16, [x1, #-1]
    //     0x66f21c: ldurb           w17, [x0, #-1]
    //     0x66f220: and             x16, x17, x16, lsr #2
    //     0x66f224: tst             x16, HEAP, lsr #32
    //     0x66f228: b.eq            #0x66f230
    //     0x66f22c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x66f230: ldur            x16, [fp, #-0x18]
    // 0x66f234: str             x16, [SP]
    // 0x66f238: r0 = _interpolate()
    //     0x66f238: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x66f23c: stur            x0, [fp, #-8]
    // 0x66f240: r0 = XmlNodeTypeException()
    //     0x66f240: bl              #0x66f328  ; AllocateXmlNodeTypeExceptionStub -> XmlNodeTypeException (size=0xc)
    // 0x66f244: mov             x1, x0
    // 0x66f248: ldur            x0, [fp, #-8]
    // 0x66f24c: StoreField: r1->field_7 = r0
    //     0x66f24c: stur            w0, [x1, #7]
    // 0x66f250: mov             x0, x1
    // 0x66f254: r0 = Throw()
    //     0x66f254: bl              #0xec04b8  ; ThrowStub
    // 0x66f258: brk             #0
    // 0x66f25c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f25c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f260: b               #0x66f148
  }
  _ toString(/* No info */) {
    // ** addr: 0xc439f0, size: 0x5c
    // 0xc439f0: EnterFrame
    //     0xc439f0: stp             fp, lr, [SP, #-0x10]!
    //     0xc439f4: mov             fp, SP
    // 0xc439f8: AllocStack(0x8)
    //     0xc439f8: sub             SP, SP, #8
    // 0xc439fc: CheckStackOverflow
    //     0xc439fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc43a00: cmp             SP, x16
    //     0xc43a04: b.ls            #0xc43a44
    // 0xc43a08: r1 = Null
    //     0xc43a08: mov             x1, NULL
    // 0xc43a0c: r2 = 4
    //     0xc43a0c: movz            x2, #0x4
    // 0xc43a10: r0 = AllocateArray()
    //     0xc43a10: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc43a14: r16 = "XmlNodeTypeException: "
    //     0xc43a14: add             x16, PP, #0x39, lsl #12  ; [pp+0x394a0] "XmlNodeTypeException: "
    //     0xc43a18: ldr             x16, [x16, #0x4a0]
    // 0xc43a1c: StoreField: r0->field_f = r16
    //     0xc43a1c: stur            w16, [x0, #0xf]
    // 0xc43a20: ldr             x1, [fp, #0x10]
    // 0xc43a24: LoadField: r2 = r1->field_7
    //     0xc43a24: ldur            w2, [x1, #7]
    // 0xc43a28: DecompressPointer r2
    //     0xc43a28: add             x2, x2, HEAP, lsl #32
    // 0xc43a2c: StoreField: r0->field_13 = r2
    //     0xc43a2c: stur            w2, [x0, #0x13]
    // 0xc43a30: str             x0, [SP]
    // 0xc43a34: r0 = _interpolate()
    //     0xc43a34: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc43a38: LeaveFrame
    //     0xc43a38: mov             SP, fp
    //     0xc43a3c: ldp             fp, lr, [SP], #0x10
    // 0xc43a40: ret
    //     0xc43a40: ret             
    // 0xc43a44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc43a44: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc43a48: b               #0xc43a08
  }
}
