// lib: , url: package:xml/src/xml/extensions/descendants.dart

// class id: 1051293, size: 0x8
class :: {

  static _ XmlDescendantsExtension.descendants(/* No info */) {
    // ** addr: 0xe6f204, size: 0x34
    // 0xe6f204: EnterFrame
    //     0xe6f204: stp             fp, lr, [SP, #-0x10]!
    //     0xe6f208: mov             fp, SP
    // 0xe6f20c: AllocStack(0x8)
    //     0xe6f20c: sub             SP, SP, #8
    // 0xe6f210: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xe6f210: mov             x0, x1
    //     0xe6f214: stur            x1, [fp, #-8]
    // 0xe6f218: r1 = <XmlNode>
    //     0xe6f218: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xe6f21c: ldr             x1, [x1, #0x418]
    // 0xe6f220: r0 = XmlDescendantsIterable()
    //     0xe6f220: bl              #0xe6f238  ; AllocateXmlDescendantsIterableStub -> XmlDescendantsIterable (size=0x10)
    // 0xe6f224: ldur            x1, [fp, #-8]
    // 0xe6f228: StoreField: r0->field_b = r1
    //     0xe6f228: stur            w1, [x0, #0xb]
    // 0xe6f22c: LeaveFrame
    //     0xe6f22c: mov             SP, fp
    //     0xe6f230: ldp             fp, lr, [SP], #0x10
    // 0xe6f234: ret
    //     0xe6f234: ret             
  }
}

// class id: 263, size: 0x10, field offset: 0x8
class XmlDescendantsIterator extends Object
    implements Iterator<X0> {

  late XmlNode _current; // offset: 0xc

  get _ current(/* No info */) {
    // ** addr: 0x6da308, size: 0x2c
    // 0x6da308: LoadField: r0 = r1->field_b
    //     0x6da308: ldur            w0, [x1, #0xb]
    // 0x6da30c: DecompressPointer r0
    //     0x6da30c: add             x0, x0, HEAP, lsl #32
    // 0x6da310: r16 = Sentinel
    //     0x6da310: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6da314: cmp             w0, w16
    // 0x6da318: b.eq            #0x6da320
    // 0x6da31c: ret
    //     0x6da31c: ret             
    // 0x6da320: EnterFrame
    //     0x6da320: stp             fp, lr, [SP, #-0x10]!
    //     0x6da324: mov             fp, SP
    // 0x6da328: r9 = _current
    //     0x6da328: add             x9, PP, #0x54, lsl #12  ; [pp+0x54790] Field <XmlDescendantsIterator._current@696329576>: late (offset: 0xc)
    //     0x6da32c: ldr             x9, [x9, #0x790]
    // 0x6da330: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6da330: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ moveNext(/* No info */) {
    // ** addr: 0x7502a4, size: 0xc4
    // 0x7502a4: EnterFrame
    //     0x7502a4: stp             fp, lr, [SP, #-0x10]!
    //     0x7502a8: mov             fp, SP
    // 0x7502ac: AllocStack(0x10)
    //     0x7502ac: sub             SP, SP, #0x10
    // 0x7502b0: SetupParameters(XmlDescendantsIterator this /* r1 => r3, fp-0x10 */)
    //     0x7502b0: mov             x3, x1
    //     0x7502b4: stur            x1, [fp, #-0x10]
    // 0x7502b8: CheckStackOverflow
    //     0x7502b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7502bc: cmp             SP, x16
    //     0x7502c0: b.ls            #0x75035c
    // 0x7502c4: LoadField: r2 = r3->field_7
    //     0x7502c4: ldur            w2, [x3, #7]
    // 0x7502c8: DecompressPointer r2
    //     0x7502c8: add             x2, x2, HEAP, lsl #32
    // 0x7502cc: LoadField: r0 = r2->field_b
    //     0x7502cc: ldur            w0, [x2, #0xb]
    // 0x7502d0: r1 = LoadInt32Instr(r0)
    //     0x7502d0: sbfx            x1, x0, #1, #0x1f
    // 0x7502d4: cbnz            w0, #0x7502e8
    // 0x7502d8: r0 = false
    //     0x7502d8: add             x0, NULL, #0x30  ; false
    // 0x7502dc: LeaveFrame
    //     0x7502dc: mov             SP, fp
    //     0x7502e0: ldp             fp, lr, [SP], #0x10
    // 0x7502e4: ret
    //     0x7502e4: ret             
    // 0x7502e8: sub             x4, x1, #1
    // 0x7502ec: mov             x0, x1
    // 0x7502f0: mov             x1, x4
    // 0x7502f4: cmp             x1, x0
    // 0x7502f8: b.hs            #0x750364
    // 0x7502fc: LoadField: r0 = r2->field_f
    //     0x7502fc: ldur            w0, [x2, #0xf]
    // 0x750300: DecompressPointer r0
    //     0x750300: add             x0, x0, HEAP, lsl #32
    // 0x750304: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0x750304: add             x16, x0, x4, lsl #2
    //     0x750308: ldur            w5, [x16, #0xf]
    // 0x75030c: DecompressPointer r5
    //     0x75030c: add             x5, x5, HEAP, lsl #32
    // 0x750310: mov             x1, x2
    // 0x750314: mov             x2, x4
    // 0x750318: stur            x5, [fp, #-8]
    // 0x75031c: r0 = length=()
    //     0x75031c: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x750320: ldur            x0, [fp, #-8]
    // 0x750324: ldur            x1, [fp, #-0x10]
    // 0x750328: StoreField: r1->field_b = r0
    //     0x750328: stur            w0, [x1, #0xb]
    //     0x75032c: ldurb           w16, [x1, #-1]
    //     0x750330: ldurb           w17, [x0, #-1]
    //     0x750334: and             x16, x17, x16, lsr #2
    //     0x750338: tst             x16, HEAP, lsr #32
    //     0x75033c: b.eq            #0x750344
    //     0x750340: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x750344: ldur            x2, [fp, #-8]
    // 0x750348: r0 = push()
    //     0x750348: bl              #0x750368  ; [package:xml/src/xml/extensions/descendants.dart] XmlDescendantsIterator::push
    // 0x75034c: r0 = true
    //     0x75034c: add             x0, NULL, #0x20  ; true
    // 0x750350: LeaveFrame
    //     0x750350: mov             SP, fp
    //     0x750354: ldp             fp, lr, [SP], #0x10
    // 0x750358: ret
    //     0x750358: ret             
    // 0x75035c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x75035c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x750360: b               #0x7502c4
    // 0x750364: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x750364: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ push(/* No info */) {
    // ** addr: 0x750368, size: 0xd8
    // 0x750368: EnterFrame
    //     0x750368: stp             fp, lr, [SP, #-0x10]!
    //     0x75036c: mov             fp, SP
    // 0x750370: AllocStack(0x10)
    //     0x750370: sub             SP, SP, #0x10
    // 0x750374: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x750374: stur            x2, [fp, #-0x10]
    // 0x750378: CheckStackOverflow
    //     0x750378: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75037c: cmp             SP, x16
    //     0x750380: b.ls            #0x750438
    // 0x750384: LoadField: r3 = r1->field_7
    //     0x750384: ldur            w3, [x1, #7]
    // 0x750388: DecompressPointer r3
    //     0x750388: add             x3, x3, HEAP, lsl #32
    // 0x75038c: stur            x3, [fp, #-8]
    // 0x750390: r0 = LoadClassIdInstr(r2)
    //     0x750390: ldur            x0, [x2, #-1]
    //     0x750394: ubfx            x0, x0, #0xc, #0x14
    // 0x750398: mov             x1, x2
    // 0x75039c: r0 = GDT[cid_x0 + -0xecd]()
    //     0x75039c: sub             lr, x0, #0xecd
    //     0x7503a0: ldr             lr, [x21, lr, lsl #3]
    //     0x7503a4: blr             lr
    // 0x7503a8: r1 = LoadClassIdInstr(r0)
    //     0x7503a8: ldur            x1, [x0, #-1]
    //     0x7503ac: ubfx            x1, x1, #0xc, #0x14
    // 0x7503b0: mov             x16, x0
    // 0x7503b4: mov             x0, x1
    // 0x7503b8: mov             x1, x16
    // 0x7503bc: r0 = GDT[cid_x0 + 0x131af]()
    //     0x7503bc: movz            x17, #0x31af
    //     0x7503c0: movk            x17, #0x1, lsl #16
    //     0x7503c4: add             lr, x0, x17
    //     0x7503c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7503cc: blr             lr
    // 0x7503d0: ldur            x1, [fp, #-8]
    // 0x7503d4: mov             x2, x0
    // 0x7503d8: r0 = addAll()
    //     0x7503d8: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x7503dc: ldur            x1, [fp, #-0x10]
    // 0x7503e0: r0 = LoadClassIdInstr(r1)
    //     0x7503e0: ldur            x0, [x1, #-1]
    //     0x7503e4: ubfx            x0, x0, #0xc, #0x14
    // 0x7503e8: r0 = GDT[cid_x0 + -0xf3a]()
    //     0x7503e8: sub             lr, x0, #0xf3a
    //     0x7503ec: ldr             lr, [x21, lr, lsl #3]
    //     0x7503f0: blr             lr
    // 0x7503f4: r1 = LoadClassIdInstr(r0)
    //     0x7503f4: ldur            x1, [x0, #-1]
    //     0x7503f8: ubfx            x1, x1, #0xc, #0x14
    // 0x7503fc: mov             x16, x0
    // 0x750400: mov             x0, x1
    // 0x750404: mov             x1, x16
    // 0x750408: r0 = GDT[cid_x0 + 0x131af]()
    //     0x750408: movz            x17, #0x31af
    //     0x75040c: movk            x17, #0x1, lsl #16
    //     0x750410: add             lr, x0, x17
    //     0x750414: ldr             lr, [x21, lr, lsl #3]
    //     0x750418: blr             lr
    // 0x75041c: ldur            x1, [fp, #-8]
    // 0x750420: mov             x2, x0
    // 0x750424: r0 = addAll()
    //     0x750424: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x750428: r0 = Null
    //     0x750428: mov             x0, NULL
    // 0x75042c: LeaveFrame
    //     0x75042c: mov             SP, fp
    //     0x750430: ldp             fp, lr, [SP], #0x10
    // 0x750434: ret
    //     0x750434: ret             
    // 0x750438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x750438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75043c: b               #0x750384
  }
  _ XmlDescendantsIterator(/* No info */) {
    // ** addr: 0x888f74, size: 0x80
    // 0x888f74: EnterFrame
    //     0x888f74: stp             fp, lr, [SP, #-0x10]!
    //     0x888f78: mov             fp, SP
    // 0x888f7c: AllocStack(0x10)
    //     0x888f7c: sub             SP, SP, #0x10
    // 0x888f80: r0 = Sentinel
    //     0x888f80: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x888f84: mov             x4, x1
    // 0x888f88: mov             x3, x2
    // 0x888f8c: stur            x1, [fp, #-8]
    // 0x888f90: stur            x2, [fp, #-0x10]
    // 0x888f94: CheckStackOverflow
    //     0x888f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x888f98: cmp             SP, x16
    //     0x888f9c: b.ls            #0x888fec
    // 0x888fa0: StoreField: r4->field_b = r0
    //     0x888fa0: stur            w0, [x4, #0xb]
    // 0x888fa4: r1 = <XmlNode>
    //     0x888fa4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0x888fa8: ldr             x1, [x1, #0x418]
    // 0x888fac: r2 = 0
    //     0x888fac: movz            x2, #0
    // 0x888fb0: r0 = _GrowableList()
    //     0x888fb0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x888fb4: ldur            x1, [fp, #-8]
    // 0x888fb8: StoreField: r1->field_7 = r0
    //     0x888fb8: stur            w0, [x1, #7]
    //     0x888fbc: ldurb           w16, [x1, #-1]
    //     0x888fc0: ldurb           w17, [x0, #-1]
    //     0x888fc4: and             x16, x17, x16, lsr #2
    //     0x888fc8: tst             x16, HEAP, lsr #32
    //     0x888fcc: b.eq            #0x888fd4
    //     0x888fd0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x888fd4: ldur            x2, [fp, #-0x10]
    // 0x888fd8: r0 = push()
    //     0x888fd8: bl              #0x750368  ; [package:xml/src/xml/extensions/descendants.dart] XmlDescendantsIterator::push
    // 0x888fdc: r0 = Null
    //     0x888fdc: mov             x0, NULL
    // 0x888fe0: LeaveFrame
    //     0x888fe0: mov             SP, fp
    //     0x888fe4: ldp             fp, lr, [SP], #0x10
    // 0x888fe8: ret
    //     0x888fe8: ret             
    // 0x888fec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x888fec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x888ff0: b               #0x888fa0
  }
}

// class id: 7207, size: 0x10, field offset: 0xc
class XmlDescendantsIterable extends Iterable<dynamic> {

  get _ iterator(/* No info */) {
    // ** addr: 0x888f24, size: 0x50
    // 0x888f24: EnterFrame
    //     0x888f24: stp             fp, lr, [SP, #-0x10]!
    //     0x888f28: mov             fp, SP
    // 0x888f2c: AllocStack(0x8)
    //     0x888f2c: sub             SP, SP, #8
    // 0x888f30: CheckStackOverflow
    //     0x888f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x888f34: cmp             SP, x16
    //     0x888f38: b.ls            #0x888f6c
    // 0x888f3c: LoadField: r2 = r1->field_b
    //     0x888f3c: ldur            w2, [x1, #0xb]
    // 0x888f40: DecompressPointer r2
    //     0x888f40: add             x2, x2, HEAP, lsl #32
    // 0x888f44: stur            x2, [fp, #-8]
    // 0x888f48: r0 = XmlDescendantsIterator()
    //     0x888f48: bl              #0x888ff4  ; AllocateXmlDescendantsIteratorStub -> XmlDescendantsIterator (size=0x10)
    // 0x888f4c: mov             x1, x0
    // 0x888f50: ldur            x2, [fp, #-8]
    // 0x888f54: stur            x0, [fp, #-8]
    // 0x888f58: r0 = XmlDescendantsIterator()
    //     0x888f58: bl              #0x888f74  ; [package:xml/src/xml/extensions/descendants.dart] XmlDescendantsIterator::XmlDescendantsIterator
    // 0x888f5c: ldur            x0, [fp, #-8]
    // 0x888f60: LeaveFrame
    //     0x888f60: mov             SP, fp
    //     0x888f64: ldp             fp, lr, [SP], #0x10
    // 0x888f68: ret
    //     0x888f68: ret             
    // 0x888f6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x888f6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x888f70: b               #0x888f3c
  }
}
