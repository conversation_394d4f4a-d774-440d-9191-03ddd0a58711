// lib: , url: package:xml/src/xml/extensions/parent.dart

// class id: 1051294, size: 0x8
class :: {

  static _ XmlParentExtension.parentElement(/* No info */) {
    // ** addr: 0xebaf1c, size: 0x90
    // 0xebaf1c: EnterFrame
    //     0xebaf1c: stp             fp, lr, [SP, #-0x10]!
    //     0xebaf20: mov             fp, SP
    // 0xebaf24: CheckStackOverflow
    //     0xebaf24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebaf28: cmp             SP, x16
    //     0xebaf2c: b.ls            #0xebaf9c
    // 0xebaf30: LoadField: r0 = r1->field_7
    //     0xebaf30: ldur            w0, [x1, #7]
    // 0xebaf34: DecompressPointer r0
    //     0xebaf34: add             x0, x0, HEAP, lsl #32
    // 0xebaf38: mov             x1, x0
    // 0xebaf3c: CheckStackOverflow
    //     0xebaf3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebaf40: cmp             SP, x16
    //     0xebaf44: b.ls            #0xebafa4
    // 0xebaf48: cmp             w1, NULL
    // 0xebaf4c: b.eq            #0xebaf8c
    // 0xebaf50: r0 = LoadClassIdInstr(r1)
    //     0xebaf50: ldur            x0, [x1, #-1]
    //     0xebaf54: ubfx            x0, x0, #0xc, #0x14
    // 0xebaf58: cmp             x0, #0xfb
    // 0xebaf5c: b.eq            #0xebaf7c
    // 0xebaf60: r0 = LoadClassIdInstr(r1)
    //     0xebaf60: ldur            x0, [x1, #-1]
    //     0xebaf64: ubfx            x0, x0, #0xc, #0x14
    // 0xebaf68: r0 = GDT[cid_x0 + -0xf4e]()
    //     0xebaf68: sub             lr, x0, #0xf4e
    //     0xebaf6c: ldr             lr, [x21, lr, lsl #3]
    //     0xebaf70: blr             lr
    // 0xebaf74: mov             x1, x0
    // 0xebaf78: b               #0xebaf3c
    // 0xebaf7c: mov             x0, x1
    // 0xebaf80: LeaveFrame
    //     0xebaf80: mov             SP, fp
    //     0xebaf84: ldp             fp, lr, [SP], #0x10
    // 0xebaf88: ret
    //     0xebaf88: ret             
    // 0xebaf8c: r0 = Null
    //     0xebaf8c: mov             x0, NULL
    // 0xebaf90: LeaveFrame
    //     0xebaf90: mov             SP, fp
    //     0xebaf94: ldp             fp, lr, [SP], #0x10
    // 0xebaf98: ret
    //     0xebaf98: ret             
    // 0xebaf9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebaf9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebafa0: b               #0xebaf30
    // 0xebafa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebafa4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebafa8: b               #0xebaf48
  }
}
