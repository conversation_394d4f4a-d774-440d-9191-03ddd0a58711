// lib: , url: package:xml/src/xml/nodes/attribute.dart

// class id: 1051302, size: 0x8
class :: {
}

// class id: 247, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _XmlAttribute&XmlNode&XmlHasName extends XmlNode
     with XmlHasName {
}

// class id: 248, size: 0xc, field offset: 0x8
//   transformed mixin,
abstract class _XmlAttribute&XmlNode&XmlHasName&XmlHasParent extends _XmlAttribute&XmlNode&XmlHasName
     with XmlHasParent<X0 bound XmlNode> {
}

// class id: 252, size: 0x18, field offset: 0xc
class XmlAttribute extends _XmlAttribute&XmlNode&XmlHasName&XmlHasParent {

  _ accept(/* No info */) {
    // ** addr: 0xce03e0, size: 0x3c
    // 0xce03e0: EnterFrame
    //     0xce03e0: stp             fp, lr, [SP, #-0x10]!
    //     0xce03e4: mov             fp, SP
    // 0xce03e8: mov             x16, x2
    // 0xce03ec: mov             x2, x1
    // 0xce03f0: mov             x1, x16
    // 0xce03f4: CheckStackOverflow
    //     0xce03f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce03f8: cmp             SP, x16
    //     0xce03fc: b.ls            #0xce0414
    // 0xce0400: r0 = visitAttribute()
    //     0xce0400: bl              #0xce043c  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitAttribute
    // 0xce0404: r0 = Null
    //     0xce0404: mov             x0, NULL
    // 0xce0408: LeaveFrame
    //     0xce0408: mov             SP, fp
    //     0xce040c: ldp             fp, lr, [SP], #0x10
    // 0xce0410: ret
    //     0xce0410: ret             
    // 0xce0414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0418: b               #0xce0400
  }
  _ XmlAttribute(/* No info */) {
    // ** addr: 0xe76cac, size: 0xe0
    // 0xe76cac: EnterFrame
    //     0xe76cac: stp             fp, lr, [SP, #-0x10]!
    //     0xe76cb0: mov             fp, SP
    // 0xe76cb4: AllocStack(0x10)
    //     0xe76cb4: sub             SP, SP, #0x10
    // 0xe76cb8: SetupParameters(XmlAttribute this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1 */, [dynamic _ = Instance_XmlAttributeType /* r4 */])
    //     0xe76cb8: stur            x1, [fp, #-8]
    //     0xe76cbc: mov             x16, x3
    //     0xe76cc0: mov             x3, x1
    //     0xe76cc4: mov             x1, x16
    //     0xe76cc8: stur            x2, [fp, #-0x10]
    //     0xe76ccc: ldur            w0, [x4, #0x13]
    //     0xe76cd0: sub             x4, x0, #6
    //     0xe76cd4: cmp             w4, #2
    //     0xe76cd8: b.lt            #0xe76cec
    //     0xe76cdc: add             x0, fp, w4, sxtw #2
    //     0xe76ce0: ldr             x0, [x0, #8]
    //     0xe76ce4: mov             x4, x0
    //     0xe76ce8: b               #0xe76cf4
    //     0xe76cec: add             x4, PP, #0x26, lsl #12  ; [pp+0x26778] Obj!XmlAttributeType@e2d4b1
    //     0xe76cf0: ldr             x4, [x4, #0x778]
    // 0xe76cf4: CheckStackOverflow
    //     0xe76cf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe76cf8: cmp             SP, x16
    //     0xe76cfc: b.ls            #0xe76d84
    // 0xe76d00: mov             x0, x2
    // 0xe76d04: StoreField: r3->field_b = r0
    //     0xe76d04: stur            w0, [x3, #0xb]
    //     0xe76d08: ldurb           w16, [x3, #-1]
    //     0xe76d0c: ldurb           w17, [x0, #-1]
    //     0xe76d10: and             x16, x17, x16, lsr #2
    //     0xe76d14: tst             x16, HEAP, lsr #32
    //     0xe76d18: b.eq            #0xe76d20
    //     0xe76d1c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe76d20: mov             x0, x1
    // 0xe76d24: StoreField: r3->field_f = r0
    //     0xe76d24: stur            w0, [x3, #0xf]
    //     0xe76d28: ldurb           w16, [x3, #-1]
    //     0xe76d2c: ldurb           w17, [x0, #-1]
    //     0xe76d30: and             x16, x17, x16, lsr #2
    //     0xe76d34: tst             x16, HEAP, lsr #32
    //     0xe76d38: b.eq            #0xe76d40
    //     0xe76d3c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe76d40: mov             x0, x4
    // 0xe76d44: StoreField: r3->field_13 = r0
    //     0xe76d44: stur            w0, [x3, #0x13]
    //     0xe76d48: ldurb           w16, [x3, #-1]
    //     0xe76d4c: ldurb           w17, [x0, #-1]
    //     0xe76d50: and             x16, x17, x16, lsr #2
    //     0xe76d54: tst             x16, HEAP, lsr #32
    //     0xe76d58: b.eq            #0xe76d60
    //     0xe76d5c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe76d60: mov             x1, x3
    // 0xe76d64: r0 = forceCompileTimeTreeShaking()
    //     0xe76d64: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xe76d68: ldur            x1, [fp, #-0x10]
    // 0xe76d6c: ldur            x2, [fp, #-8]
    // 0xe76d70: r0 = attachParent()
    //     0xe76d70: bl              #0xeb99d4  ; [package:xml/src/xml/nodes/data.dart] _XmlData&XmlNode&XmlHasParent::attachParent
    // 0xe76d74: r0 = Null
    //     0xe76d74: mov             x0, NULL
    // 0xe76d78: LeaveFrame
    //     0xe76d78: mov             SP, fp
    //     0xe76d7c: ldp             fp, lr, [SP], #0x10
    // 0xe76d80: ret
    //     0xe76d80: ret             
    // 0xe76d84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76d84: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76d88: b               #0xe76d00
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb7a90, size: 0x100
    // 0xeb7a90: EnterFrame
    //     0xeb7a90: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7a94: mov             fp, SP
    // 0xeb7a98: AllocStack(0x28)
    //     0xeb7a98: sub             SP, SP, #0x28
    // 0xeb7a9c: SetupParameters(XmlAttribute this /* r1 => r1, fp-0x10 */)
    //     0xeb7a9c: stur            x1, [fp, #-0x10]
    // 0xeb7aa0: CheckStackOverflow
    //     0xeb7aa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7aa4: cmp             SP, x16
    //     0xeb7aa8: b.ls            #0xeb7b88
    // 0xeb7aac: LoadField: r0 = r1->field_b
    //     0xeb7aac: ldur            w0, [x1, #0xb]
    // 0xeb7ab0: DecompressPointer r0
    //     0xeb7ab0: add             x0, x0, HEAP, lsl #32
    // 0xeb7ab4: r2 = LoadClassIdInstr(r0)
    //     0xeb7ab4: ldur            x2, [x0, #-1]
    //     0xeb7ab8: ubfx            x2, x2, #0xc, #0x14
    // 0xeb7abc: cmp             x2, #0xe2
    // 0xeb7ac0: b.ne            #0xeb7ae8
    // 0xeb7ac4: LoadField: r2 = r0->field_b
    //     0xeb7ac4: ldur            w2, [x0, #0xb]
    // 0xeb7ac8: DecompressPointer r2
    //     0xeb7ac8: add             x2, x2, HEAP, lsl #32
    // 0xeb7acc: stur            x2, [fp, #-8]
    // 0xeb7ad0: r0 = XmlSimpleName()
    //     0xeb7ad0: bl              #0xe76dc0  ; AllocateXmlSimpleNameStub -> XmlSimpleName (size=0x10)
    // 0xeb7ad4: mov             x1, x0
    // 0xeb7ad8: ldur            x0, [fp, #-8]
    // 0xeb7adc: StoreField: r1->field_b = r0
    //     0xeb7adc: stur            w0, [x1, #0xb]
    // 0xeb7ae0: mov             x2, x1
    // 0xeb7ae4: b               #0xeb7b30
    // 0xeb7ae8: LoadField: r1 = r0->field_b
    //     0xeb7ae8: ldur            w1, [x0, #0xb]
    // 0xeb7aec: DecompressPointer r1
    //     0xeb7aec: add             x1, x1, HEAP, lsl #32
    // 0xeb7af0: stur            x1, [fp, #-0x20]
    // 0xeb7af4: LoadField: r2 = r0->field_f
    //     0xeb7af4: ldur            w2, [x0, #0xf]
    // 0xeb7af8: DecompressPointer r2
    //     0xeb7af8: add             x2, x2, HEAP, lsl #32
    // 0xeb7afc: stur            x2, [fp, #-0x18]
    // 0xeb7b00: LoadField: r3 = r0->field_13
    //     0xeb7b00: ldur            w3, [x0, #0x13]
    // 0xeb7b04: DecompressPointer r3
    //     0xeb7b04: add             x3, x3, HEAP, lsl #32
    // 0xeb7b08: stur            x3, [fp, #-8]
    // 0xeb7b0c: r0 = XmlPrefixName()
    //     0xeb7b0c: bl              #0xeb7b90  ; AllocateXmlPrefixNameStub -> XmlPrefixName (size=0x18)
    // 0xeb7b10: mov             x1, x0
    // 0xeb7b14: ldur            x0, [fp, #-0x20]
    // 0xeb7b18: StoreField: r1->field_b = r0
    //     0xeb7b18: stur            w0, [x1, #0xb]
    // 0xeb7b1c: ldur            x0, [fp, #-0x18]
    // 0xeb7b20: StoreField: r1->field_f = r0
    //     0xeb7b20: stur            w0, [x1, #0xf]
    // 0xeb7b24: ldur            x0, [fp, #-8]
    // 0xeb7b28: StoreField: r1->field_13 = r0
    //     0xeb7b28: stur            w0, [x1, #0x13]
    // 0xeb7b2c: mov             x2, x1
    // 0xeb7b30: ldur            x0, [fp, #-0x10]
    // 0xeb7b34: stur            x2, [fp, #-0x20]
    // 0xeb7b38: LoadField: r3 = r0->field_f
    //     0xeb7b38: ldur            w3, [x0, #0xf]
    // 0xeb7b3c: DecompressPointer r3
    //     0xeb7b3c: add             x3, x3, HEAP, lsl #32
    // 0xeb7b40: stur            x3, [fp, #-0x18]
    // 0xeb7b44: LoadField: r1 = r0->field_13
    //     0xeb7b44: ldur            w1, [x0, #0x13]
    // 0xeb7b48: DecompressPointer r1
    //     0xeb7b48: add             x1, x1, HEAP, lsl #32
    // 0xeb7b4c: stur            x1, [fp, #-8]
    // 0xeb7b50: r0 = XmlAttribute()
    //     0xeb7b50: bl              #0xe76d8c  ; AllocateXmlAttributeStub -> XmlAttribute (size=0x18)
    // 0xeb7b54: stur            x0, [fp, #-0x10]
    // 0xeb7b58: ldur            x16, [fp, #-8]
    // 0xeb7b5c: str             x16, [SP]
    // 0xeb7b60: mov             x1, x0
    // 0xeb7b64: ldur            x2, [fp, #-0x20]
    // 0xeb7b68: ldur            x3, [fp, #-0x18]
    // 0xeb7b6c: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0xeb7b6c: add             x4, PP, #0xe, lsl #12  ; [pp+0xe5a8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0xeb7b70: ldr             x4, [x4, #0x5a8]
    // 0xeb7b74: r0 = XmlAttribute()
    //     0xeb7b74: bl              #0xe76cac  ; [package:xml/src/xml/nodes/attribute.dart] XmlAttribute::XmlAttribute
    // 0xeb7b78: ldur            x0, [fp, #-0x10]
    // 0xeb7b7c: LeaveFrame
    //     0xeb7b7c: mov             SP, fp
    //     0xeb7b80: ldp             fp, lr, [SP], #0x10
    // 0xeb7b84: ret
    //     0xeb7b84: ret             
    // 0xeb7b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7b88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7b8c: b               #0xeb7aac
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a40, size: 0xc
    // 0xeb9a40: r0 = Instance_XmlNodeType
    //     0xeb9a40: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4d618] Obj!XmlNodeType@e2d471
    //     0xeb9a44: ldr             x0, [x0, #0x618]
    // 0xeb9a48: ret
    //     0xeb9a48: ret             
  }
  const get _ name(/* No info */) {
    // ** addr: 0xebac00, size: 0xc
    // 0xebac00: LoadField: r0 = r1->field_b
    //     0xebac00: ldur            w0, [x1, #0xb]
    // 0xebac04: DecompressPointer r0
    //     0xebac04: add             x0, x0, HEAP, lsl #32
    // 0xebac08: ret
    //     0xebac08: ret             
  }
}
