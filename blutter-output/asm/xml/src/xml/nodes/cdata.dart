// lib: , url: package:xml/src/xml/nodes/cdata.dart

// class id: 1051303, size: 0x8
class :: {
}

// class id: 246, size: 0x10, field offset: 0x10
class XmlCDATA extends XmlData {

  _ accept(/* No info */) {
    // ** addr: 0xce10cc, size: 0x3c
    // 0xce10cc: EnterFrame
    //     0xce10cc: stp             fp, lr, [SP, #-0x10]!
    //     0xce10d0: mov             fp, SP
    // 0xce10d4: mov             x16, x2
    // 0xce10d8: mov             x2, x1
    // 0xce10dc: mov             x1, x16
    // 0xce10e0: CheckStackOverflow
    //     0xce10e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce10e4: cmp             SP, x16
    //     0xce10e8: b.ls            #0xce1100
    // 0xce10ec: r0 = visitCDATA()
    //     0xce10ec: bl              #0xce1108  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitCDATA
    // 0xce10f0: r0 = Null
    //     0xce10f0: mov             x0, NULL
    // 0xce10f4: LeaveFrame
    //     0xce10f4: mov             SP, fp
    //     0xce10f8: ldp             fp, lr, [SP], #0x10
    // 0xce10fc: ret
    //     0xce10fc: ret             
    // 0xce1100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1100: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1104: b               #0xce10ec
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb7f90, size: 0x58
    // 0xeb7f90: EnterFrame
    //     0xeb7f90: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7f94: mov             fp, SP
    // 0xeb7f98: AllocStack(0x10)
    //     0xeb7f98: sub             SP, SP, #0x10
    // 0xeb7f9c: CheckStackOverflow
    //     0xeb7f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7fa0: cmp             SP, x16
    //     0xeb7fa4: b.ls            #0xeb7fe0
    // 0xeb7fa8: LoadField: r0 = r1->field_b
    //     0xeb7fa8: ldur            w0, [x1, #0xb]
    // 0xeb7fac: DecompressPointer r0
    //     0xeb7fac: add             x0, x0, HEAP, lsl #32
    // 0xeb7fb0: stur            x0, [fp, #-8]
    // 0xeb7fb4: r0 = XmlCDATA()
    //     0xeb7fb4: bl              #0xeb7fe8  ; AllocateXmlCDATAStub -> XmlCDATA (size=0x10)
    // 0xeb7fb8: mov             x2, x0
    // 0xeb7fbc: ldur            x0, [fp, #-8]
    // 0xeb7fc0: stur            x2, [fp, #-0x10]
    // 0xeb7fc4: StoreField: r2->field_b = r0
    //     0xeb7fc4: stur            w0, [x2, #0xb]
    // 0xeb7fc8: mov             x1, x2
    // 0xeb7fcc: r0 = forceCompileTimeTreeShaking()
    //     0xeb7fcc: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb7fd0: ldur            x0, [fp, #-0x10]
    // 0xeb7fd4: LeaveFrame
    //     0xeb7fd4: mov             SP, fp
    //     0xeb7fd8: ldp             fp, lr, [SP], #0x10
    // 0xeb7fdc: ret
    //     0xeb7fdc: ret             
    // 0xeb7fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7fe0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7fe4: b               #0xeb7fa8
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a58, size: 0xc
    // 0xeb9a58: r0 = Instance_XmlNodeType
    //     0xeb9a58: add             x0, PP, #0x31, lsl #12  ; [pp+0x310b8] Obj!XmlNodeType@e2d451
    //     0xeb9a5c: ldr             x0, [x0, #0xb8]
    // 0xeb9a60: ret
    //     0xeb9a60: ret             
  }
}
