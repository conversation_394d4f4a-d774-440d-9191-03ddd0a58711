// lib: , url: package:xml/src/xml/nodes/comment.dart

// class id: 1051304, size: 0x8
class :: {
}

// class id: 245, size: 0x10, field offset: 0x10
class XmlComment extends XmlData {

  _ accept(/* No info */) {
    // ** addr: 0xce1180, size: 0x3c
    // 0xce1180: EnterFrame
    //     0xce1180: stp             fp, lr, [SP, #-0x10]!
    //     0xce1184: mov             fp, SP
    // 0xce1188: mov             x16, x2
    // 0xce118c: mov             x2, x1
    // 0xce1190: mov             x1, x16
    // 0xce1194: CheckStackOverflow
    //     0xce1194: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1198: cmp             SP, x16
    //     0xce119c: b.ls            #0xce11b4
    // 0xce11a0: r0 = visitComment()
    //     0xce11a0: bl              #0xce11bc  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitComment
    // 0xce11a4: r0 = Null
    //     0xce11a4: mov             x0, NULL
    // 0xce11a8: LeaveFrame
    //     0xce11a8: mov             SP, fp
    //     0xce11ac: ldp             fp, lr, [SP], #0x10
    // 0xce11b0: ret
    //     0xce11b0: ret             
    // 0xce11b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce11b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce11b8: b               #0xce11a0
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb7ff4, size: 0x58
    // 0xeb7ff4: EnterFrame
    //     0xeb7ff4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7ff8: mov             fp, SP
    // 0xeb7ffc: AllocStack(0x10)
    //     0xeb7ffc: sub             SP, SP, #0x10
    // 0xeb8000: CheckStackOverflow
    //     0xeb8000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8004: cmp             SP, x16
    //     0xeb8008: b.ls            #0xeb8044
    // 0xeb800c: LoadField: r0 = r1->field_b
    //     0xeb800c: ldur            w0, [x1, #0xb]
    // 0xeb8010: DecompressPointer r0
    //     0xeb8010: add             x0, x0, HEAP, lsl #32
    // 0xeb8014: stur            x0, [fp, #-8]
    // 0xeb8018: r0 = XmlComment()
    //     0xeb8018: bl              #0xeb804c  ; AllocateXmlCommentStub -> XmlComment (size=0x10)
    // 0xeb801c: mov             x2, x0
    // 0xeb8020: ldur            x0, [fp, #-8]
    // 0xeb8024: stur            x2, [fp, #-0x10]
    // 0xeb8028: StoreField: r2->field_b = r0
    //     0xeb8028: stur            w0, [x2, #0xb]
    // 0xeb802c: mov             x1, x2
    // 0xeb8030: r0 = forceCompileTimeTreeShaking()
    //     0xeb8030: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb8034: ldur            x0, [fp, #-0x10]
    // 0xeb8038: LeaveFrame
    //     0xeb8038: mov             SP, fp
    //     0xeb803c: ldp             fp, lr, [SP], #0x10
    // 0xeb8040: ret
    //     0xeb8040: ret             
    // 0xeb8044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb8044: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb8048: b               #0xeb800c
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a64, size: 0xc
    // 0xeb9a64: r0 = Instance_XmlNodeType
    //     0xeb9a64: add             x0, PP, #0x31, lsl #12  ; [pp+0x310b0] Obj!XmlNodeType@e2d431
    //     0xeb9a68: ldr             x0, [x0, #0xb0]
    // 0xeb9a6c: ret
    //     0xeb9a6c: ret             
  }
}
