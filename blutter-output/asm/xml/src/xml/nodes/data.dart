// lib: , url: package:xml/src/xml/nodes/data.dart

// class id: 1051305, size: 0x8
class :: {
}

// class id: 238, size: 0xc, field offset: 0x8
//   transformed mixin,
abstract class _XmlData&XmlNode&XmlHasParent extends XmlNode
     with XmlHasParent<X0 bound XmlNode> {

  _ attachParent(/* No info */) {
    // ** addr: 0xeb99d4, size: 0x6c
    // 0xeb99d4: EnterFrame
    //     0xeb99d4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb99d8: mov             fp, SP
    // 0xeb99dc: AllocStack(0x10)
    //     0xeb99dc: sub             SP, SP, #0x10
    // 0xeb99e0: SetupParameters(_XmlData&XmlNode&XmlHasParent this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xeb99e0: mov             x0, x2
    //     0xeb99e4: stur            x2, [fp, #-0x10]
    //     0xeb99e8: mov             x2, x1
    //     0xeb99ec: stur            x1, [fp, #-8]
    // 0xeb99f0: CheckStackOverflow
    //     0xeb99f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb99f4: cmp             SP, x16
    //     0xeb99f8: b.ls            #0xeb9a38
    // 0xeb99fc: mov             x1, x2
    // 0xeb9a00: r0 = checkNoParent()
    //     0xeb9a00: bl              #0x66f094  ; [package:xml/src/xml/exceptions/parent_exception.dart] XmlParentException::checkNoParent
    // 0xeb9a04: ldur            x0, [fp, #-0x10]
    // 0xeb9a08: ldur            x1, [fp, #-8]
    // 0xeb9a0c: StoreField: r1->field_7 = r0
    //     0xeb9a0c: stur            w0, [x1, #7]
    //     0xeb9a10: ldurb           w16, [x1, #-1]
    //     0xeb9a14: ldurb           w17, [x0, #-1]
    //     0xeb9a18: and             x16, x17, x16, lsr #2
    //     0xeb9a1c: tst             x16, HEAP, lsr #32
    //     0xeb9a20: b.eq            #0xeb9a28
    //     0xeb9a24: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeb9a28: r0 = Null
    //     0xeb9a28: mov             x0, NULL
    // 0xeb9a2c: LeaveFrame
    //     0xeb9a2c: mov             SP, fp
    //     0xeb9a30: ldp             fp, lr, [SP], #0x10
    // 0xeb9a34: ret
    //     0xeb9a34: ret             
    // 0xeb9a38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9a38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9a3c: b               #0xeb99fc
  }
  _ detachParent(/* No info */) {
    // ** addr: 0xeb9aac, size: 0x48
    // 0xeb9aac: EnterFrame
    //     0xeb9aac: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9ab0: mov             fp, SP
    // 0xeb9ab4: AllocStack(0x8)
    //     0xeb9ab4: sub             SP, SP, #8
    // 0xeb9ab8: SetupParameters(_XmlData&XmlNode&XmlHasParent this /* r1 => r0, fp-0x8 */)
    //     0xeb9ab8: mov             x0, x1
    //     0xeb9abc: stur            x1, [fp, #-8]
    // 0xeb9ac0: CheckStackOverflow
    //     0xeb9ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9ac4: cmp             SP, x16
    //     0xeb9ac8: b.ls            #0xeb9aec
    // 0xeb9acc: mov             x1, x0
    // 0xeb9ad0: r0 = checkMatchingParent()
    //     0xeb9ad0: bl              #0xeb9af4  ; [package:xml/src/xml/exceptions/parent_exception.dart] XmlParentException::checkMatchingParent
    // 0xeb9ad4: ldur            x1, [fp, #-8]
    // 0xeb9ad8: StoreField: r1->field_7 = rNULL
    //     0xeb9ad8: stur            NULL, [x1, #7]
    // 0xeb9adc: r0 = Null
    //     0xeb9adc: mov             x0, NULL
    // 0xeb9ae0: LeaveFrame
    //     0xeb9ae0: mov             SP, fp
    //     0xeb9ae4: ldp             fp, lr, [SP], #0x10
    // 0xeb9ae8: ret
    //     0xeb9ae8: ret             
    // 0xeb9aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9aec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9af0: b               #0xeb9acc
  }
}

// class id: 242, size: 0x10, field offset: 0xc
abstract class XmlData extends _XmlData&XmlNode&XmlHasParent {
}
