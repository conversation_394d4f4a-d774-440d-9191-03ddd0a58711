// lib: , url: package:xml/src/xml/nodes/declaration.dart

// class id: 1051306, size: 0x8
class :: {
}

// class id: 240, size: 0x10, field offset: 0xc
//   transformed mixin,
abstract class _XmlDeclaration&XmlNode&XmlHasParent&XmlHasAttributes extends _XmlData&XmlNode&XmlHasParent
     with XmlHasAttributes {
}

// class id: 241, size: 0x10, field offset: 0x10
class XmlDeclaration extends _XmlDeclaration&XmlNode&XmlHasParent&XmlHasAttributes {

  _ accept(/* No info */) {
    // ** addr: 0xce15a0, size: 0x3c
    // 0xce15a0: EnterFrame
    //     0xce15a0: stp             fp, lr, [SP, #-0x10]!
    //     0xce15a4: mov             fp, SP
    // 0xce15a8: mov             x16, x2
    // 0xce15ac: mov             x2, x1
    // 0xce15b0: mov             x1, x16
    // 0xce15b4: CheckStackOverflow
    //     0xce15b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce15b8: cmp             SP, x16
    //     0xce15bc: b.ls            #0xce15d4
    // 0xce15c0: r0 = visitDeclaration()
    //     0xce15c0: bl              #0xce15dc  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitDeclaration
    // 0xce15c4: r0 = Null
    //     0xce15c4: mov             x0, NULL
    // 0xce15c8: LeaveFrame
    //     0xce15c8: mov             SP, fp
    //     0xce15cc: ldp             fp, lr, [SP], #0x10
    // 0xce15d0: ret
    //     0xce15d0: ret             
    // 0xce15d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce15d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce15d8: b               #0xce15c0
  }
  get _ value(/* No info */) {
    // ** addr: 0xe3419c, size: 0x7c
    // 0xe3419c: EnterFrame
    //     0xe3419c: stp             fp, lr, [SP, #-0x10]!
    //     0xe341a0: mov             fp, SP
    // 0xe341a4: AllocStack(0x8)
    //     0xe341a4: sub             SP, SP, #8
    // 0xe341a8: CheckStackOverflow
    //     0xe341a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe341ac: cmp             SP, x16
    //     0xe341b0: b.ls            #0xe34210
    // 0xe341b4: LoadField: r0 = r1->field_b
    //     0xe341b4: ldur            w0, [x1, #0xb]
    // 0xe341b8: DecompressPointer r0
    //     0xe341b8: add             x0, x0, HEAP, lsl #32
    // 0xe341bc: LoadField: r2 = r0->field_b
    //     0xe341bc: ldur            w2, [x0, #0xb]
    // 0xe341c0: DecompressPointer r2
    //     0xe341c0: add             x2, x2, HEAP, lsl #32
    // 0xe341c4: LoadField: r0 = r2->field_b
    //     0xe341c4: ldur            w0, [x2, #0xb]
    // 0xe341c8: cbnz            w0, #0xe341dc
    // 0xe341cc: r0 = ""
    //     0xe341cc: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0xe341d0: LeaveFrame
    //     0xe341d0: mov             SP, fp
    //     0xe341d4: ldp             fp, lr, [SP], #0x10
    // 0xe341d8: ret
    //     0xe341d8: ret             
    // 0xe341dc: r0 = toXmlString()
    //     0xe341dc: bl              #0xc44804  ; [package:xml/src/xml/nodes/node.dart] _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter::toXmlString
    // 0xe341e0: LoadField: r1 = r0->field_7
    //     0xe341e0: ldur            w1, [x0, #7]
    // 0xe341e4: r2 = LoadInt32Instr(r1)
    //     0xe341e4: sbfx            x2, x1, #1, #0x1f
    // 0xe341e8: sub             x1, x2, #2
    // 0xe341ec: lsl             x2, x1, #1
    // 0xe341f0: str             x2, [SP]
    // 0xe341f4: mov             x1, x0
    // 0xe341f8: r2 = 6
    //     0xe341f8: movz            x2, #0x6
    // 0xe341fc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xe341fc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xe34200: r0 = substring()
    //     0xe34200: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xe34204: LeaveFrame
    //     0xe34204: mov             SP, fp
    //     0xe34208: ldp             fp, lr, [SP], #0x10
    // 0xe3420c: ret
    //     0xe3420c: ret             
    // 0xe34210: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34210: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34214: b               #0xe341b4
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb8134, size: 0x80
    // 0xeb8134: EnterFrame
    //     0xeb8134: stp             fp, lr, [SP, #-0x10]!
    //     0xeb8138: mov             fp, SP
    // 0xeb813c: AllocStack(0x20)
    //     0xeb813c: sub             SP, SP, #0x20
    // 0xeb8140: CheckStackOverflow
    //     0xeb8140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8144: cmp             SP, x16
    //     0xeb8148: b.ls            #0xeb81ac
    // 0xeb814c: LoadField: r0 = r1->field_b
    //     0xeb814c: ldur            w0, [x1, #0xb]
    // 0xeb8150: DecompressPointer r0
    //     0xeb8150: add             x0, x0, HEAP, lsl #32
    // 0xeb8154: stur            x0, [fp, #-8]
    // 0xeb8158: r1 = Function '<anonymous closure>':.
    //     0xeb8158: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d610] AnonymousClosure: (0xeb7f60), in [package:xml/src/xml/nodes/element.dart] XmlElement::copy (0xeb7b9c)
    //     0xeb815c: ldr             x1, [x1, #0x610]
    // 0xeb8160: r2 = Null
    //     0xeb8160: mov             x2, NULL
    // 0xeb8164: r0 = AllocateClosure()
    //     0xeb8164: bl              #0xec1630  ; AllocateClosureStub
    // 0xeb8168: r16 = <XmlAttribute>
    //     0xeb8168: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bf00] TypeArguments: <XmlAttribute>
    //     0xeb816c: ldr             x16, [x16, #0xf00]
    // 0xeb8170: ldur            lr, [fp, #-8]
    // 0xeb8174: stp             lr, x16, [SP, #8]
    // 0xeb8178: str             x0, [SP]
    // 0xeb817c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xeb817c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xeb8180: r0 = map()
    //     0xeb8180: bl              #0x7d8994  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::map
    // 0xeb8184: stur            x0, [fp, #-8]
    // 0xeb8188: r0 = XmlDeclaration()
    //     0xeb8188: bl              #0xeb828c  ; AllocateXmlDeclarationStub -> XmlDeclaration (size=0x10)
    // 0xeb818c: mov             x1, x0
    // 0xeb8190: ldur            x2, [fp, #-8]
    // 0xeb8194: stur            x0, [fp, #-8]
    // 0xeb8198: r0 = XmlDeclaration()
    //     0xeb8198: bl              #0xeb81b4  ; [package:xml/src/xml/nodes/declaration.dart] XmlDeclaration::XmlDeclaration
    // 0xeb819c: ldur            x0, [fp, #-8]
    // 0xeb81a0: LeaveFrame
    //     0xeb81a0: mov             SP, fp
    //     0xeb81a4: ldp             fp, lr, [SP], #0x10
    // 0xeb81a8: ret
    //     0xeb81a8: ret             
    // 0xeb81ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb81ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb81b0: b               #0xeb814c
  }
  _ XmlDeclaration(/* No info */) {
    // ** addr: 0xeb81b4, size: 0xd8
    // 0xeb81b4: EnterFrame
    //     0xeb81b4: stp             fp, lr, [SP, #-0x10]!
    //     0xeb81b8: mov             fp, SP
    // 0xeb81bc: AllocStack(0x18)
    //     0xeb81bc: sub             SP, SP, #0x18
    // 0xeb81c0: SetupParameters(XmlDeclaration this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xeb81c0: mov             x0, x1
    //     0xeb81c4: stur            x1, [fp, #-8]
    //     0xeb81c8: stur            x2, [fp, #-0x10]
    // 0xeb81cc: CheckStackOverflow
    //     0xeb81cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb81d0: cmp             SP, x16
    //     0xeb81d4: b.ls            #0xeb8284
    // 0xeb81d8: r1 = <XmlAttribute>
    //     0xeb81d8: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bf00] TypeArguments: <XmlAttribute>
    //     0xeb81dc: ldr             x1, [x1, #0xf00]
    // 0xeb81e0: r0 = XmlNodeList()
    //     0xeb81e0: bl              #0xb15398  ; AllocateXmlNodeListStub -> XmlNodeList<X0 bound XmlNode> (size=0x18)
    // 0xeb81e4: mov             x3, x0
    // 0xeb81e8: r0 = Sentinel
    //     0xeb81e8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb81ec: stur            x3, [fp, #-0x18]
    // 0xeb81f0: StoreField: r3->field_f = r0
    //     0xeb81f0: stur            w0, [x3, #0xf]
    // 0xeb81f4: StoreField: r3->field_13 = r0
    //     0xeb81f4: stur            w0, [x3, #0x13]
    // 0xeb81f8: r1 = <XmlAttribute>
    //     0xeb81f8: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bf00] TypeArguments: <XmlAttribute>
    //     0xeb81fc: ldr             x1, [x1, #0xf00]
    // 0xeb8200: r2 = 0
    //     0xeb8200: movz            x2, #0
    // 0xeb8204: r0 = _GrowableList()
    //     0xeb8204: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xeb8208: ldur            x2, [fp, #-0x18]
    // 0xeb820c: StoreField: r2->field_b = r0
    //     0xeb820c: stur            w0, [x2, #0xb]
    //     0xeb8210: ldurb           w16, [x2, #-1]
    //     0xeb8214: ldurb           w17, [x0, #-1]
    //     0xeb8218: and             x16, x17, x16, lsr #2
    //     0xeb821c: tst             x16, HEAP, lsr #32
    //     0xeb8220: b.eq            #0xeb8228
    //     0xeb8224: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xeb8228: mov             x0, x2
    // 0xeb822c: ldur            x3, [fp, #-8]
    // 0xeb8230: StoreField: r3->field_b = r0
    //     0xeb8230: stur            w0, [x3, #0xb]
    //     0xeb8234: ldurb           w16, [x3, #-1]
    //     0xeb8238: ldurb           w17, [x0, #-1]
    //     0xeb823c: and             x16, x17, x16, lsr #2
    //     0xeb8240: tst             x16, HEAP, lsr #32
    //     0xeb8244: b.eq            #0xeb824c
    //     0xeb8248: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xeb824c: mov             x1, x3
    // 0xeb8250: r0 = forceCompileTimeTreeShaking()
    //     0xeb8250: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb8254: ldur            x1, [fp, #-0x18]
    // 0xeb8258: ldur            x2, [fp, #-8]
    // 0xeb825c: r3 = _ConstSet len:1
    //     0xeb825c: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bef0] Set<XmlNodeType>(1)
    //     0xeb8260: ldr             x3, [x3, #0xef0]
    // 0xeb8264: r0 = initialize()
    //     0xeb8264: bl              #0xb152b4  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::initialize
    // 0xeb8268: ldur            x1, [fp, #-0x18]
    // 0xeb826c: ldur            x2, [fp, #-0x10]
    // 0xeb8270: r0 = addAll()
    //     0xeb8270: bl              #0x66f670  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::addAll
    // 0xeb8274: r0 = Null
    //     0xeb8274: mov             x0, NULL
    // 0xeb8278: LeaveFrame
    //     0xeb8278: mov             SP, fp
    //     0xeb827c: ldp             fp, lr, [SP], #0x10
    // 0xeb8280: ret
    //     0xeb8280: ret             
    // 0xeb8284: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb8284: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb8288: b               #0xeb81d8
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a88, size: 0xc
    // 0xeb9a88: r0 = Instance_XmlNodeType
    //     0xeb9a88: add             x0, PP, #0x31, lsl #12  ; [pp+0x310c8] Obj!XmlNodeType@e2d411
    //     0xeb9a8c: ldr             x0, [x0, #0xc8]
    // 0xeb9a90: ret
    //     0xeb9a90: ret             
  }
}
