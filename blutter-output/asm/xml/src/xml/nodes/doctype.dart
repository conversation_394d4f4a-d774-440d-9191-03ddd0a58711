// lib: , url: package:xml/src/xml/nodes/doctype.dart

// class id: 1051307, size: 0x8
class :: {
}

// class id: 239, size: 0x18, field offset: 0xc
class XmlDoctype extends _XmlData&XmlNode&XmlHasParent {

  _ accept(/* No info */) {
    // ** addr: 0xce1654, size: 0x3c
    // 0xce1654: EnterFrame
    //     0xce1654: stp             fp, lr, [SP, #-0x10]!
    //     0xce1658: mov             fp, SP
    // 0xce165c: mov             x16, x2
    // 0xce1660: mov             x2, x1
    // 0xce1664: mov             x1, x16
    // 0xce1668: CheckStackOverflow
    //     0xce1668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce166c: cmp             SP, x16
    //     0xce1670: b.ls            #0xce1688
    // 0xce1674: r0 = visitDoctype()
    //     0xce1674: bl              #0xce1690  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitDoctype
    // 0xce1678: r0 = Null
    //     0xce1678: mov             x0, NULL
    // 0xce167c: LeaveFrame
    //     0xce167c: mov             SP, fp
    //     0xce1680: ldp             fp, lr, [SP], #0x10
    // 0xce1684: ret
    //     0xce1684: ret             
    // 0xce1688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1688: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce168c: b               #0xce1674
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb8298, size: 0x80
    // 0xeb8298: EnterFrame
    //     0xeb8298: stp             fp, lr, [SP, #-0x10]!
    //     0xeb829c: mov             fp, SP
    // 0xeb82a0: AllocStack(0x20)
    //     0xeb82a0: sub             SP, SP, #0x20
    // 0xeb82a4: CheckStackOverflow
    //     0xeb82a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb82a8: cmp             SP, x16
    //     0xeb82ac: b.ls            #0xeb8310
    // 0xeb82b0: LoadField: r0 = r1->field_b
    //     0xeb82b0: ldur            w0, [x1, #0xb]
    // 0xeb82b4: DecompressPointer r0
    //     0xeb82b4: add             x0, x0, HEAP, lsl #32
    // 0xeb82b8: stur            x0, [fp, #-0x18]
    // 0xeb82bc: LoadField: r2 = r1->field_f
    //     0xeb82bc: ldur            w2, [x1, #0xf]
    // 0xeb82c0: DecompressPointer r2
    //     0xeb82c0: add             x2, x2, HEAP, lsl #32
    // 0xeb82c4: stur            x2, [fp, #-0x10]
    // 0xeb82c8: LoadField: r3 = r1->field_13
    //     0xeb82c8: ldur            w3, [x1, #0x13]
    // 0xeb82cc: DecompressPointer r3
    //     0xeb82cc: add             x3, x3, HEAP, lsl #32
    // 0xeb82d0: stur            x3, [fp, #-8]
    // 0xeb82d4: r0 = XmlDoctype()
    //     0xeb82d4: bl              #0xeb8318  ; AllocateXmlDoctypeStub -> XmlDoctype (size=0x18)
    // 0xeb82d8: mov             x2, x0
    // 0xeb82dc: ldur            x0, [fp, #-0x18]
    // 0xeb82e0: stur            x2, [fp, #-0x20]
    // 0xeb82e4: StoreField: r2->field_b = r0
    //     0xeb82e4: stur            w0, [x2, #0xb]
    // 0xeb82e8: ldur            x0, [fp, #-0x10]
    // 0xeb82ec: StoreField: r2->field_f = r0
    //     0xeb82ec: stur            w0, [x2, #0xf]
    // 0xeb82f0: ldur            x0, [fp, #-8]
    // 0xeb82f4: StoreField: r2->field_13 = r0
    //     0xeb82f4: stur            w0, [x2, #0x13]
    // 0xeb82f8: mov             x1, x2
    // 0xeb82fc: r0 = forceCompileTimeTreeShaking()
    //     0xeb82fc: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb8300: ldur            x0, [fp, #-0x20]
    // 0xeb8304: LeaveFrame
    //     0xeb8304: mov             SP, fp
    //     0xeb8308: ldp             fp, lr, [SP], #0x10
    // 0xeb830c: ret
    //     0xeb830c: ret             
    // 0xeb8310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb8310: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb8314: b               #0xeb82b0
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a94, size: 0xc
    // 0xeb9a94: r0 = Instance_XmlNodeType
    //     0xeb9a94: add             x0, PP, #0x31, lsl #12  ; [pp+0x310c0] Obj!XmlNodeType@e2d3f1
    //     0xeb9a98: ldr             x0, [x0, #0xc0]
    // 0xeb9a9c: ret
    //     0xeb9a9c: ret             
  }
}
