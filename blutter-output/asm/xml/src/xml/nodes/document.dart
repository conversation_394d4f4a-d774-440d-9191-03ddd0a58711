// lib: , url: package:xml/src/xml/nodes/document.dart

// class id: 1051308, size: 0x8
class :: {
}

// class id: 236, size: 0xc, field offset: 0x8
//   transformed mixin,
abstract class _XmlDocument&XmlNode&XmlHasChildren extends XmlNode
     with XmlHasChildren<X0 bound XmlNode> {
}

// class id: 237, size: 0xc, field offset: 0xc
class XmlDocument extends _XmlDocument&XmlNode&XmlHasChildren {

  get _ rootElement(/* No info */) {
    // ** addr: 0xb15058, size: 0x10c
    // 0xb15058: EnterFrame
    //     0xb15058: stp             fp, lr, [SP, #-0x10]!
    //     0xb1505c: mov             fp, SP
    // 0xb15060: AllocStack(0x28)
    //     0xb15060: sub             SP, SP, #0x28
    // 0xb15064: LoadField: r0 = r1->field_7
    //     0xb15064: ldur            w0, [x1, #7]
    // 0xb15068: DecompressPointer r0
    //     0xb15068: add             x0, x0, HEAP, lsl #32
    // 0xb1506c: LoadField: r1 = r0->field_b
    //     0xb1506c: ldur            w1, [x0, #0xb]
    // 0xb15070: DecompressPointer r1
    //     0xb15070: add             x1, x1, HEAP, lsl #32
    // 0xb15074: LoadField: r3 = r1->field_7
    //     0xb15074: ldur            w3, [x1, #7]
    // 0xb15078: DecompressPointer r3
    //     0xb15078: add             x3, x3, HEAP, lsl #32
    // 0xb1507c: stur            x3, [fp, #-0x28]
    // 0xb15080: LoadField: r0 = r1->field_b
    //     0xb15080: ldur            w0, [x1, #0xb]
    // 0xb15084: r4 = LoadInt32Instr(r0)
    //     0xb15084: sbfx            x4, x0, #1, #0x1f
    // 0xb15088: stur            x4, [fp, #-0x20]
    // 0xb1508c: LoadField: r5 = r1->field_f
    //     0xb1508c: ldur            w5, [x1, #0xf]
    // 0xb15090: DecompressPointer r5
    //     0xb15090: add             x5, x5, HEAP, lsl #32
    // 0xb15094: stur            x5, [fp, #-0x18]
    // 0xb15098: r0 = 0
    //     0xb15098: movz            x0, #0
    // 0xb1509c: CheckStackOverflow
    //     0xb1509c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb150a0: cmp             SP, x16
    //     0xb150a4: b.ls            #0xb1515c
    // 0xb150a8: cmp             x0, x4
    // 0xb150ac: b.ge            #0xb1513c
    // 0xb150b0: ArrayLoad: r6 = r5[r0]  ; Unknown_4
    //     0xb150b0: add             x16, x5, x0, lsl #2
    //     0xb150b4: ldur            w6, [x16, #0xf]
    // 0xb150b8: DecompressPointer r6
    //     0xb150b8: add             x6, x6, HEAP, lsl #32
    // 0xb150bc: stur            x6, [fp, #-0x10]
    // 0xb150c0: add             x7, x0, #1
    // 0xb150c4: stur            x7, [fp, #-8]
    // 0xb150c8: cmp             w6, NULL
    // 0xb150cc: b.ne            #0xb15100
    // 0xb150d0: mov             x0, x6
    // 0xb150d4: mov             x2, x3
    // 0xb150d8: r1 = Null
    //     0xb150d8: mov             x1, NULL
    // 0xb150dc: cmp             w2, NULL
    // 0xb150e0: b.eq            #0xb15100
    // 0xb150e4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb150e4: ldur            w4, [x2, #0x17]
    // 0xb150e8: DecompressPointer r4
    //     0xb150e8: add             x4, x4, HEAP, lsl #32
    // 0xb150ec: r8 = X0
    //     0xb150ec: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb150f0: LoadField: r9 = r4->field_7
    //     0xb150f0: ldur            x9, [x4, #7]
    // 0xb150f4: r3 = Null
    //     0xb150f4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e3f0] Null
    //     0xb150f8: ldr             x3, [x3, #0x3f0]
    // 0xb150fc: blr             x9
    // 0xb15100: ldur            x0, [fp, #-0x10]
    // 0xb15104: r1 = 60
    //     0xb15104: movz            x1, #0x3c
    // 0xb15108: branchIfSmi(r0, 0xb15114)
    //     0xb15108: tbz             w0, #0, #0xb15114
    // 0xb1510c: r1 = LoadClassIdInstr(r0)
    //     0xb1510c: ldur            x1, [x0, #-1]
    //     0xb15110: ubfx            x1, x1, #0xc, #0x14
    // 0xb15114: cmp             x1, #0xfb
    // 0xb15118: b.eq            #0xb15130
    // 0xb1511c: ldur            x0, [fp, #-8]
    // 0xb15120: ldur            x3, [fp, #-0x28]
    // 0xb15124: ldur            x5, [fp, #-0x18]
    // 0xb15128: ldur            x4, [fp, #-0x20]
    // 0xb1512c: b               #0xb1509c
    // 0xb15130: LeaveFrame
    //     0xb15130: mov             SP, fp
    //     0xb15134: ldp             fp, lr, [SP], #0x10
    // 0xb15138: ret
    //     0xb15138: ret             
    // 0xb1513c: r0 = StateError()
    //     0xb1513c: bl              #0x5f7878  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xb15140: mov             x1, x0
    // 0xb15144: r0 = "Empty XML document"
    //     0xb15144: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e400] "Empty XML document"
    //     0xb15148: ldr             x0, [x0, #0x400]
    // 0xb1514c: StoreField: r1->field_b = r0
    //     0xb1514c: stur            w0, [x1, #0xb]
    // 0xb15150: mov             x0, x1
    // 0xb15154: r0 = Throw()
    //     0xb15154: bl              #0xec04b8  ; ThrowStub
    // 0xb15158: brk             #0
    // 0xb1515c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1515c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15160: b               #0xb150a8
  }
  factory _ XmlDocument.parse(/* No info */) {
    // ** addr: 0xb15164, size: 0x78
    // 0xb15164: EnterFrame
    //     0xb15164: stp             fp, lr, [SP, #-0x10]!
    //     0xb15168: mov             fp, SP
    // 0xb1516c: AllocStack(0x18)
    //     0xb1516c: sub             SP, SP, #0x18
    // 0xb15170: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xb15170: mov             x0, x1
    //     0xb15174: mov             x1, x2
    // 0xb15178: CheckStackOverflow
    //     0xb15178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1517c: cmp             SP, x16
    //     0xb15180: b.ls            #0xb151d4
    // 0xb15184: r16 = true
    //     0xb15184: add             x16, NULL, #0x20  ; true
    // 0xb15188: r30 = true
    //     0xb15188: add             lr, NULL, #0x20  ; true
    // 0xb1518c: stp             lr, x16, [SP]
    // 0xb15190: r4 = const [0, 0x3, 0x2, 0x1, validateDocument, 0x2, validateNesting, 0x1, null]
    //     0xb15190: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e408] List(9) [0, 0x3, 0x2, 0x1, "validateDocument", 0x2, "validateNesting", 0x1, Null]
    //     0xb15194: ldr             x4, [x4, #0x408]
    // 0xb15198: r0 = parseEvents()
    //     0xb15198: bl              #0xacf988  ; [package:xml/xml_events.dart] ::parseEvents
    // 0xb1519c: mov             x2, x0
    // 0xb151a0: r1 = Instance_XmlNodeDecoder
    //     0xb151a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e410] Obj!XmlNodeDecoder@e2cd21
    //     0xb151a4: ldr             x1, [x1, #0x410]
    // 0xb151a8: r0 = convertIterable()
    //     0xb151a8: bl              #0xb153b0  ; [package:xml/src/xml_events/converters/node_decoder.dart] XmlNodeDecoder::convertIterable
    // 0xb151ac: stur            x0, [fp, #-8]
    // 0xb151b0: r0 = XmlDocument()
    //     0xb151b0: bl              #0xb153a4  ; AllocateXmlDocumentStub -> XmlDocument (size=0xc)
    // 0xb151b4: mov             x1, x0
    // 0xb151b8: ldur            x2, [fp, #-8]
    // 0xb151bc: stur            x0, [fp, #-8]
    // 0xb151c0: r0 = XmlDocument()
    //     0xb151c0: bl              #0xb151dc  ; [package:xml/src/xml/nodes/document.dart] XmlDocument::XmlDocument
    // 0xb151c4: ldur            x0, [fp, #-8]
    // 0xb151c8: LeaveFrame
    //     0xb151c8: mov             SP, fp
    //     0xb151cc: ldp             fp, lr, [SP], #0x10
    // 0xb151d0: ret
    //     0xb151d0: ret             
    // 0xb151d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb151d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb151d8: b               #0xb15184
  }
  _ XmlDocument(/* No info */) {
    // ** addr: 0xb151dc, size: 0xd8
    // 0xb151dc: EnterFrame
    //     0xb151dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb151e0: mov             fp, SP
    // 0xb151e4: AllocStack(0x18)
    //     0xb151e4: sub             SP, SP, #0x18
    // 0xb151e8: SetupParameters(XmlDocument this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb151e8: mov             x0, x1
    //     0xb151ec: stur            x1, [fp, #-8]
    //     0xb151f0: stur            x2, [fp, #-0x10]
    // 0xb151f4: CheckStackOverflow
    //     0xb151f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb151f8: cmp             SP, x16
    //     0xb151fc: b.ls            #0xb152ac
    // 0xb15200: r1 = <XmlNode>
    //     0xb15200: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xb15204: ldr             x1, [x1, #0x418]
    // 0xb15208: r0 = XmlNodeList()
    //     0xb15208: bl              #0xb15398  ; AllocateXmlNodeListStub -> XmlNodeList<X0 bound XmlNode> (size=0x18)
    // 0xb1520c: mov             x3, x0
    // 0xb15210: r0 = Sentinel
    //     0xb15210: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb15214: stur            x3, [fp, #-0x18]
    // 0xb15218: StoreField: r3->field_f = r0
    //     0xb15218: stur            w0, [x3, #0xf]
    // 0xb1521c: StoreField: r3->field_13 = r0
    //     0xb1521c: stur            w0, [x3, #0x13]
    // 0xb15220: r1 = <XmlNode>
    //     0xb15220: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xb15224: ldr             x1, [x1, #0x418]
    // 0xb15228: r2 = 0
    //     0xb15228: movz            x2, #0
    // 0xb1522c: r0 = _GrowableList()
    //     0xb1522c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb15230: ldur            x2, [fp, #-0x18]
    // 0xb15234: StoreField: r2->field_b = r0
    //     0xb15234: stur            w0, [x2, #0xb]
    //     0xb15238: ldurb           w16, [x2, #-1]
    //     0xb1523c: ldurb           w17, [x0, #-1]
    //     0xb15240: and             x16, x17, x16, lsr #2
    //     0xb15244: tst             x16, HEAP, lsr #32
    //     0xb15248: b.eq            #0xb15250
    //     0xb1524c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xb15250: mov             x0, x2
    // 0xb15254: ldur            x3, [fp, #-8]
    // 0xb15258: StoreField: r3->field_7 = r0
    //     0xb15258: stur            w0, [x3, #7]
    //     0xb1525c: ldurb           w16, [x3, #-1]
    //     0xb15260: ldurb           w17, [x0, #-1]
    //     0xb15264: and             x16, x17, x16, lsr #2
    //     0xb15268: tst             x16, HEAP, lsr #32
    //     0xb1526c: b.eq            #0xb15274
    //     0xb15270: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xb15274: mov             x1, x3
    // 0xb15278: r0 = forceCompileTimeTreeShaking()
    //     0xb15278: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xb1527c: ldur            x1, [fp, #-0x18]
    // 0xb15280: ldur            x2, [fp, #-8]
    // 0xb15284: r3 = _ConstSet len:7
    //     0xb15284: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e420] Set<XmlNodeType>(7)
    //     0xb15288: ldr             x3, [x3, #0x420]
    // 0xb1528c: r0 = initialize()
    //     0xb1528c: bl              #0xb152b4  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::initialize
    // 0xb15290: ldur            x1, [fp, #-0x18]
    // 0xb15294: ldur            x2, [fp, #-0x10]
    // 0xb15298: r0 = addAll()
    //     0xb15298: bl              #0x66f670  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::addAll
    // 0xb1529c: r0 = Null
    //     0xb1529c: mov             x0, NULL
    // 0xb152a0: LeaveFrame
    //     0xb152a0: mov             SP, fp
    //     0xb152a4: ldp             fp, lr, [SP], #0x10
    // 0xb152a8: ret
    //     0xb152a8: ret             
    // 0xb152ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb152ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb152b0: b               #0xb15200
  }
  _ accept(/* No info */) {
    // ** addr: 0xce1788, size: 0x3c
    // 0xce1788: EnterFrame
    //     0xce1788: stp             fp, lr, [SP, #-0x10]!
    //     0xce178c: mov             fp, SP
    // 0xce1790: mov             x16, x2
    // 0xce1794: mov             x2, x1
    // 0xce1798: mov             x1, x16
    // 0xce179c: CheckStackOverflow
    //     0xce179c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce17a0: cmp             SP, x16
    //     0xce17a4: b.ls            #0xce17bc
    // 0xce17a8: r0 = visitDocument()
    //     0xce17a8: bl              #0xce17c4  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitDocument
    // 0xce17ac: r0 = Null
    //     0xce17ac: mov             x0, NULL
    // 0xce17b0: LeaveFrame
    //     0xce17b0: mov             SP, fp
    //     0xce17b4: ldp             fp, lr, [SP], #0x10
    // 0xce17b8: ret
    //     0xce17b8: ret             
    // 0xce17bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce17bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce17c0: b               #0xce17a8
  }
  [closure] XmlNode <anonymous closure>(dynamic, XmlNode) {
    // ** addr: 0xeb7f20, size: 0x40
    // 0xeb7f20: EnterFrame
    //     0xeb7f20: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7f24: mov             fp, SP
    // 0xeb7f28: CheckStackOverflow
    //     0xeb7f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7f2c: cmp             SP, x16
    //     0xeb7f30: b.ls            #0xeb7f58
    // 0xeb7f34: ldr             x1, [fp, #0x10]
    // 0xeb7f38: r0 = LoadClassIdInstr(r1)
    //     0xeb7f38: ldur            x0, [x1, #-1]
    //     0xeb7f3c: ubfx            x0, x0, #0xc, #0x14
    // 0xeb7f40: r0 = GDT[cid_x0 + -0xf17]()
    //     0xeb7f40: sub             lr, x0, #0xf17
    //     0xeb7f44: ldr             lr, [x21, lr, lsl #3]
    //     0xeb7f48: blr             lr
    // 0xeb7f4c: LeaveFrame
    //     0xeb7f4c: mov             SP, fp
    //     0xeb7f50: ldp             fp, lr, [SP], #0x10
    // 0xeb7f54: ret
    //     0xeb7f54: ret             
    // 0xeb7f58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7f58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7f5c: b               #0xeb7f34
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb8324, size: 0x80
    // 0xeb8324: EnterFrame
    //     0xeb8324: stp             fp, lr, [SP, #-0x10]!
    //     0xeb8328: mov             fp, SP
    // 0xeb832c: AllocStack(0x20)
    //     0xeb832c: sub             SP, SP, #0x20
    // 0xeb8330: CheckStackOverflow
    //     0xeb8330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8334: cmp             SP, x16
    //     0xeb8338: b.ls            #0xeb839c
    // 0xeb833c: LoadField: r0 = r1->field_7
    //     0xeb833c: ldur            w0, [x1, #7]
    // 0xeb8340: DecompressPointer r0
    //     0xeb8340: add             x0, x0, HEAP, lsl #32
    // 0xeb8344: stur            x0, [fp, #-8]
    // 0xeb8348: r1 = Function '<anonymous closure>':.
    //     0xeb8348: add             x1, PP, #0x39, lsl #12  ; [pp+0x39490] AnonymousClosure: (0xeb7f20), in [package:xml/src/xml/nodes/document.dart] XmlDocument::copy (0xeb8324)
    //     0xeb834c: ldr             x1, [x1, #0x490]
    // 0xeb8350: r2 = Null
    //     0xeb8350: mov             x2, NULL
    // 0xeb8354: r0 = AllocateClosure()
    //     0xeb8354: bl              #0xec1630  ; AllocateClosureStub
    // 0xeb8358: r16 = <XmlNode>
    //     0xeb8358: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xeb835c: ldr             x16, [x16, #0x418]
    // 0xeb8360: ldur            lr, [fp, #-8]
    // 0xeb8364: stp             lr, x16, [SP, #8]
    // 0xeb8368: str             x0, [SP]
    // 0xeb836c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xeb836c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xeb8370: r0 = map()
    //     0xeb8370: bl              #0x7d8994  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::map
    // 0xeb8374: stur            x0, [fp, #-8]
    // 0xeb8378: r0 = XmlDocument()
    //     0xeb8378: bl              #0xb153a4  ; AllocateXmlDocumentStub -> XmlDocument (size=0xc)
    // 0xeb837c: mov             x1, x0
    // 0xeb8380: ldur            x2, [fp, #-8]
    // 0xeb8384: stur            x0, [fp, #-8]
    // 0xeb8388: r0 = XmlDocument()
    //     0xeb8388: bl              #0xb151dc  ; [package:xml/src/xml/nodes/document.dart] XmlDocument::XmlDocument
    // 0xeb838c: ldur            x0, [fp, #-8]
    // 0xeb8390: LeaveFrame
    //     0xeb8390: mov             SP, fp
    //     0xeb8394: ldp             fp, lr, [SP], #0x10
    // 0xeb8398: ret
    //     0xeb8398: ret             
    // 0xeb839c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb839c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb83a0: b               #0xeb833c
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9aa0, size: 0xc
    // 0xeb9aa0: r0 = Instance_XmlNodeType
    //     0xeb9aa0: add             x0, PP, #0x39, lsl #12  ; [pp+0x39498] Obj!XmlNodeType@e2d491
    //     0xeb9aa4: ldr             x0, [x0, #0x498]
    // 0xeb9aa8: ret
    //     0xeb9aa8: ret             
  }
}
