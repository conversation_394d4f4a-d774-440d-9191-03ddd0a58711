// lib: , url: package:xml/src/xml/nodes/element.dart

// class id: 1051309, size: 0x8
class :: {
}

// class id: 249, size: 0x10, field offset: 0xc
//   transformed mixin,
abstract class _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes extends _XmlAttribute&XmlNode&XmlHasName&XmlHasParent
     with XmlHasAttributes {

  _ getAttribute(/* No info */) {
    // ** addr: 0xb14798, size: 0x84
    // 0xb14798: EnterFrame
    //     0xb14798: stp             fp, lr, [SP, #-0x10]!
    //     0xb1479c: mov             fp, SP
    // 0xb147a0: LoadField: r0 = r4->field_13
    //     0xb147a0: ldur            w0, [x4, #0x13]
    // 0xb147a4: LoadField: r3 = r4->field_1f
    //     0xb147a4: ldur            w3, [x4, #0x1f]
    // 0xb147a8: DecompressPointer r3
    //     0xb147a8: add             x3, x3, HEAP, lsl #32
    // 0xb147ac: r16 = "namespace"
    //     0xb147ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e3a8] "namespace"
    //     0xb147b0: ldr             x16, [x16, #0x3a8]
    // 0xb147b4: cmp             w3, w16
    // 0xb147b8: b.ne            #0xb147d8
    // 0xb147bc: LoadField: r3 = r4->field_23
    //     0xb147bc: ldur            w3, [x4, #0x23]
    // 0xb147c0: DecompressPointer r3
    //     0xb147c0: add             x3, x3, HEAP, lsl #32
    // 0xb147c4: sub             w4, w0, w3
    // 0xb147c8: add             x0, fp, w4, sxtw #2
    // 0xb147cc: ldr             x0, [x0, #8]
    // 0xb147d0: mov             x3, x0
    // 0xb147d4: b               #0xb147dc
    // 0xb147d8: r3 = Null
    //     0xb147d8: mov             x3, NULL
    // 0xb147dc: CheckStackOverflow
    //     0xb147dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb147e0: cmp             SP, x16
    //     0xb147e4: b.ls            #0xb14814
    // 0xb147e8: r0 = getAttributeNode()
    //     0xb147e8: bl              #0xb1481c  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttributeNode
    // 0xb147ec: cmp             w0, NULL
    // 0xb147f0: b.ne            #0xb147fc
    // 0xb147f4: r0 = Null
    //     0xb147f4: mov             x0, NULL
    // 0xb147f8: b               #0xb14808
    // 0xb147fc: LoadField: r1 = r0->field_f
    //     0xb147fc: ldur            w1, [x0, #0xf]
    // 0xb14800: DecompressPointer r1
    //     0xb14800: add             x1, x1, HEAP, lsl #32
    // 0xb14804: mov             x0, x1
    // 0xb14808: LeaveFrame
    //     0xb14808: mov             SP, fp
    //     0xb1480c: ldp             fp, lr, [SP], #0x10
    // 0xb14810: ret
    //     0xb14810: ret             
    // 0xb14814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14814: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14818: b               #0xb147e8
  }
  _ getAttributeNode(/* No info */) {
    // ** addr: 0xb1481c, size: 0x174
    // 0xb1481c: EnterFrame
    //     0xb1481c: stp             fp, lr, [SP, #-0x10]!
    //     0xb14820: mov             fp, SP
    // 0xb14824: AllocStack(0x40)
    //     0xb14824: sub             SP, SP, #0x40
    // 0xb14828: SetupParameters(_XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */, dynamic _ /* r3 => r2 */)
    //     0xb14828: mov             x0, x1
    //     0xb1482c: stur            x1, [fp, #-8]
    //     0xb14830: mov             x1, x2
    //     0xb14834: mov             x2, x3
    // 0xb14838: CheckStackOverflow
    //     0xb14838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1483c: cmp             SP, x16
    //     0xb14840: b.ls            #0xb14980
    // 0xb14844: r0 = createNameMatcher()
    //     0xb14844: bl              #0xb14990  ; [package:xml/src/xml/utils/name_matcher.dart] ::createNameMatcher
    // 0xb14848: mov             x3, x0
    // 0xb1484c: ldur            x0, [fp, #-8]
    // 0xb14850: stur            x3, [fp, #-0x30]
    // 0xb14854: LoadField: r1 = r0->field_b
    //     0xb14854: ldur            w1, [x0, #0xb]
    // 0xb14858: DecompressPointer r1
    //     0xb14858: add             x1, x1, HEAP, lsl #32
    // 0xb1485c: LoadField: r4 = r1->field_b
    //     0xb1485c: ldur            w4, [x1, #0xb]
    // 0xb14860: DecompressPointer r4
    //     0xb14860: add             x4, x4, HEAP, lsl #32
    // 0xb14864: stur            x4, [fp, #-0x28]
    // 0xb14868: LoadField: r5 = r4->field_7
    //     0xb14868: ldur            w5, [x4, #7]
    // 0xb1486c: DecompressPointer r5
    //     0xb1486c: add             x5, x5, HEAP, lsl #32
    // 0xb14870: stur            x5, [fp, #-0x20]
    // 0xb14874: LoadField: r0 = r4->field_b
    //     0xb14874: ldur            w0, [x4, #0xb]
    // 0xb14878: r6 = LoadInt32Instr(r0)
    //     0xb14878: sbfx            x6, x0, #1, #0x1f
    // 0xb1487c: stur            x6, [fp, #-0x18]
    // 0xb14880: r0 = 0
    //     0xb14880: movz            x0, #0
    // 0xb14884: CheckStackOverflow
    //     0xb14884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14888: cmp             SP, x16
    //     0xb1488c: b.ls            #0xb14988
    // 0xb14890: LoadField: r1 = r4->field_b
    //     0xb14890: ldur            w1, [x4, #0xb]
    // 0xb14894: r2 = LoadInt32Instr(r1)
    //     0xb14894: sbfx            x2, x1, #1, #0x1f
    // 0xb14898: cmp             x6, x2
    // 0xb1489c: b.ne            #0xb14960
    // 0xb148a0: cmp             x0, x2
    // 0xb148a4: b.ge            #0xb14950
    // 0xb148a8: LoadField: r1 = r4->field_f
    //     0xb148a8: ldur            w1, [x4, #0xf]
    // 0xb148ac: DecompressPointer r1
    //     0xb148ac: add             x1, x1, HEAP, lsl #32
    // 0xb148b0: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0xb148b0: add             x16, x1, x0, lsl #2
    //     0xb148b4: ldur            w7, [x16, #0xf]
    // 0xb148b8: DecompressPointer r7
    //     0xb148b8: add             x7, x7, HEAP, lsl #32
    // 0xb148bc: stur            x7, [fp, #-8]
    // 0xb148c0: add             x8, x0, #1
    // 0xb148c4: stur            x8, [fp, #-0x10]
    // 0xb148c8: cmp             w7, NULL
    // 0xb148cc: b.ne            #0xb14900
    // 0xb148d0: mov             x0, x7
    // 0xb148d4: mov             x2, x5
    // 0xb148d8: r1 = Null
    //     0xb148d8: mov             x1, NULL
    // 0xb148dc: cmp             w2, NULL
    // 0xb148e0: b.eq            #0xb14900
    // 0xb148e4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb148e4: ldur            w4, [x2, #0x17]
    // 0xb148e8: DecompressPointer r4
    //     0xb148e8: add             x4, x4, HEAP, lsl #32
    // 0xb148ec: r8 = X0
    //     0xb148ec: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xb148f0: LoadField: r9 = r4->field_7
    //     0xb148f0: ldur            x9, [x4, #7]
    // 0xb148f4: r3 = Null
    //     0xb148f4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e3b0] Null
    //     0xb148f8: ldr             x3, [x3, #0x3b0]
    // 0xb148fc: blr             x9
    // 0xb14900: ldur            x16, [fp, #-0x30]
    // 0xb14904: ldur            lr, [fp, #-8]
    // 0xb14908: stp             lr, x16, [SP]
    // 0xb1490c: ldur            x0, [fp, #-0x30]
    // 0xb14910: ClosureCall
    //     0xb14910: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb14914: ldur            x2, [x0, #0x1f]
    //     0xb14918: blr             x2
    // 0xb1491c: r16 = true
    //     0xb1491c: add             x16, NULL, #0x20  ; true
    // 0xb14920: cmp             w0, w16
    // 0xb14924: b.eq            #0xb14940
    // 0xb14928: ldur            x0, [fp, #-0x10]
    // 0xb1492c: ldur            x3, [fp, #-0x30]
    // 0xb14930: ldur            x4, [fp, #-0x28]
    // 0xb14934: ldur            x5, [fp, #-0x20]
    // 0xb14938: ldur            x6, [fp, #-0x18]
    // 0xb1493c: b               #0xb14884
    // 0xb14940: ldur            x0, [fp, #-8]
    // 0xb14944: LeaveFrame
    //     0xb14944: mov             SP, fp
    //     0xb14948: ldp             fp, lr, [SP], #0x10
    // 0xb1494c: ret
    //     0xb1494c: ret             
    // 0xb14950: r0 = Null
    //     0xb14950: mov             x0, NULL
    // 0xb14954: LeaveFrame
    //     0xb14954: mov             SP, fp
    //     0xb14958: ldp             fp, lr, [SP], #0x10
    // 0xb1495c: ret
    //     0xb1495c: ret             
    // 0xb14960: mov             x0, x4
    // 0xb14964: r0 = ConcurrentModificationError()
    //     0xb14964: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xb14968: mov             x1, x0
    // 0xb1496c: ldur            x0, [fp, #-0x28]
    // 0xb14970: StoreField: r1->field_b = r0
    //     0xb14970: stur            w0, [x1, #0xb]
    // 0xb14974: mov             x0, x1
    // 0xb14978: r0 = Throw()
    //     0xb14978: bl              #0xec04b8  ; ThrowStub
    // 0xb1497c: brk             #0
    // 0xb14980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14980: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14984: b               #0xb14844
    // 0xb14988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1498c: b               #0xb14890
  }
  _ setAttribute(/* No info */) {
    // ** addr: 0xe76b94, size: 0x118
    // 0xe76b94: EnterFrame
    //     0xe76b94: stp             fp, lr, [SP, #-0x10]!
    //     0xe76b98: mov             fp, SP
    // 0xe76b9c: AllocStack(0x28)
    //     0xe76b9c: sub             SP, SP, #0x28
    // 0xe76ba0: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xe76ba0: stur            x2, [fp, #-0x10]
    //     0xe76ba4: stur            x3, [fp, #-0x18]
    // 0xe76ba8: CheckStackOverflow
    //     0xe76ba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe76bac: cmp             SP, x16
    //     0xe76bb0: b.ls            #0xe76ca0
    // 0xe76bb4: LoadField: r0 = r1->field_b
    //     0xe76bb4: ldur            w0, [x1, #0xb]
    // 0xe76bb8: DecompressPointer r0
    //     0xe76bb8: add             x0, x0, HEAP, lsl #32
    // 0xe76bbc: stur            x0, [fp, #-8]
    // 0xe76bc0: r1 = 1
    //     0xe76bc0: movz            x1, #0x1
    // 0xe76bc4: r0 = AllocateContext()
    //     0xe76bc4: bl              #0xec126c  ; AllocateContextStub
    // 0xe76bc8: mov             x1, x0
    // 0xe76bcc: ldur            x0, [fp, #-0x10]
    // 0xe76bd0: StoreField: r1->field_f = r0
    //     0xe76bd0: stur            w0, [x1, #0xf]
    // 0xe76bd4: mov             x2, x1
    // 0xe76bd8: r1 = Function '<anonymous closure>': static.
    //     0xe76bd8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ed88] AnonymousClosure: static (0xb14ebc), in [package:xml/src/xml/utils/name_matcher.dart] ::createNameMatcher (0xb14990)
    //     0xe76bdc: ldr             x1, [x1, #0xd88]
    // 0xe76be0: r0 = AllocateClosure()
    //     0xe76be0: bl              #0xec1630  ; AllocateClosureStub
    // 0xe76be4: ldur            x1, [fp, #-8]
    // 0xe76be8: mov             x2, x0
    // 0xe76bec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe76bec: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe76bf0: r0 = indexWhere()
    //     0xe76bf0: bl              #0x66e3c4  ; [package:collection/src/wrappers.dart] DelegatingList::indexWhere
    // 0xe76bf4: mov             x2, x0
    // 0xe76bf8: tbz             x2, #0x3f, #0xe76c3c
    // 0xe76bfc: ldur            x2, [fp, #-0x10]
    // 0xe76c00: r1 = Null
    //     0xe76c00: mov             x1, NULL
    // 0xe76c04: r0 = XmlName()
    //     0xe76c04: bl              #0xe76d98  ; [package:xml/src/xml/utils/name.dart] XmlName::XmlName
    // 0xe76c08: stur            x0, [fp, #-0x10]
    // 0xe76c0c: r0 = XmlAttribute()
    //     0xe76c0c: bl              #0xe76d8c  ; AllocateXmlAttributeStub -> XmlAttribute (size=0x18)
    // 0xe76c10: mov             x1, x0
    // 0xe76c14: ldur            x2, [fp, #-0x10]
    // 0xe76c18: ldur            x3, [fp, #-0x18]
    // 0xe76c1c: stur            x0, [fp, #-0x10]
    // 0xe76c20: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xe76c20: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xe76c24: r0 = XmlAttribute()
    //     0xe76c24: bl              #0xe76cac  ; [package:xml/src/xml/nodes/attribute.dart] XmlAttribute::XmlAttribute
    // 0xe76c28: ldur            x16, [fp, #-8]
    // 0xe76c2c: ldur            lr, [fp, #-0x10]
    // 0xe76c30: stp             lr, x16, [SP]
    // 0xe76c34: r0 = add()
    //     0xe76c34: bl              #0x671d18  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::add
    // 0xe76c38: b               #0xe76c90
    // 0xe76c3c: ldur            x3, [fp, #-8]
    // 0xe76c40: LoadField: r4 = r3->field_b
    //     0xe76c40: ldur            w4, [x3, #0xb]
    // 0xe76c44: DecompressPointer r4
    //     0xe76c44: add             x4, x4, HEAP, lsl #32
    // 0xe76c48: LoadField: r3 = r4->field_b
    //     0xe76c48: ldur            w3, [x4, #0xb]
    // 0xe76c4c: r0 = LoadInt32Instr(r3)
    //     0xe76c4c: sbfx            x0, x3, #1, #0x1f
    // 0xe76c50: mov             x1, x2
    // 0xe76c54: cmp             x1, x0
    // 0xe76c58: b.hs            #0xe76ca8
    // 0xe76c5c: LoadField: r1 = r4->field_f
    //     0xe76c5c: ldur            w1, [x4, #0xf]
    // 0xe76c60: DecompressPointer r1
    //     0xe76c60: add             x1, x1, HEAP, lsl #32
    // 0xe76c64: ArrayLoad: r3 = r1[r2]  ; Unknown_4
    //     0xe76c64: add             x16, x1, x2, lsl #2
    //     0xe76c68: ldur            w3, [x16, #0xf]
    // 0xe76c6c: DecompressPointer r3
    //     0xe76c6c: add             x3, x3, HEAP, lsl #32
    // 0xe76c70: ldur            x0, [fp, #-0x18]
    // 0xe76c74: StoreField: r3->field_f = r0
    //     0xe76c74: stur            w0, [x3, #0xf]
    //     0xe76c78: ldurb           w16, [x3, #-1]
    //     0xe76c7c: ldurb           w17, [x0, #-1]
    //     0xe76c80: and             x16, x17, x16, lsr #2
    //     0xe76c84: tst             x16, HEAP, lsr #32
    //     0xe76c88: b.eq            #0xe76c90
    //     0xe76c8c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0xe76c90: r0 = Null
    //     0xe76c90: mov             x0, NULL
    // 0xe76c94: LeaveFrame
    //     0xe76c94: mov             SP, fp
    //     0xe76c98: ldp             fp, lr, [SP], #0x10
    // 0xe76c9c: ret
    //     0xe76c9c: ret             
    // 0xe76ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe76ca0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe76ca4: b               #0xe76bb4
    // 0xe76ca8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe76ca8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 250, size: 0x14, field offset: 0x10
//   transformed mixin,
abstract class _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren extends _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes
     with XmlHasChildren<X0 bound XmlNode> {

  _ _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren(/* No info */) {
    // ** addr: 0xeb7df0, size: 0x124
    // 0xeb7df0: EnterFrame
    //     0xeb7df0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7df4: mov             fp, SP
    // 0xeb7df8: AllocStack(0x10)
    //     0xeb7df8: sub             SP, SP, #0x10
    // 0xeb7dfc: SetupParameters(_XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren this /* r1 => r0, fp-0x8 */)
    //     0xeb7dfc: mov             x0, x1
    //     0xeb7e00: stur            x1, [fp, #-8]
    // 0xeb7e04: CheckStackOverflow
    //     0xeb7e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7e08: cmp             SP, x16
    //     0xeb7e0c: b.ls            #0xeb7f0c
    // 0xeb7e10: r1 = <XmlNode>
    //     0xeb7e10: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xeb7e14: ldr             x1, [x1, #0x418]
    // 0xeb7e18: r0 = XmlNodeList()
    //     0xeb7e18: bl              #0xb15398  ; AllocateXmlNodeListStub -> XmlNodeList<X0 bound XmlNode> (size=0x18)
    // 0xeb7e1c: mov             x3, x0
    // 0xeb7e20: r0 = Sentinel
    //     0xeb7e20: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb7e24: stur            x3, [fp, #-0x10]
    // 0xeb7e28: StoreField: r3->field_f = r0
    //     0xeb7e28: stur            w0, [x3, #0xf]
    // 0xeb7e2c: StoreField: r3->field_13 = r0
    //     0xeb7e2c: stur            w0, [x3, #0x13]
    // 0xeb7e30: r1 = <XmlNode>
    //     0xeb7e30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xeb7e34: ldr             x1, [x1, #0x418]
    // 0xeb7e38: r2 = 0
    //     0xeb7e38: movz            x2, #0
    // 0xeb7e3c: r0 = _GrowableList()
    //     0xeb7e3c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xeb7e40: ldur            x1, [fp, #-0x10]
    // 0xeb7e44: StoreField: r1->field_b = r0
    //     0xeb7e44: stur            w0, [x1, #0xb]
    //     0xeb7e48: ldurb           w16, [x1, #-1]
    //     0xeb7e4c: ldurb           w17, [x0, #-1]
    //     0xeb7e50: and             x16, x17, x16, lsr #2
    //     0xeb7e54: tst             x16, HEAP, lsr #32
    //     0xeb7e58: b.eq            #0xeb7e60
    //     0xeb7e5c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeb7e60: mov             x0, x1
    // 0xeb7e64: ldur            x2, [fp, #-8]
    // 0xeb7e68: StoreField: r2->field_f = r0
    //     0xeb7e68: stur            w0, [x2, #0xf]
    //     0xeb7e6c: ldurb           w16, [x2, #-1]
    //     0xeb7e70: ldurb           w17, [x0, #-1]
    //     0xeb7e74: and             x16, x17, x16, lsr #2
    //     0xeb7e78: tst             x16, HEAP, lsr #32
    //     0xeb7e7c: b.eq            #0xeb7e84
    //     0xeb7e80: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xeb7e84: r1 = <XmlAttribute>
    //     0xeb7e84: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bf00] TypeArguments: <XmlAttribute>
    //     0xeb7e88: ldr             x1, [x1, #0xf00]
    // 0xeb7e8c: r0 = XmlNodeList()
    //     0xeb7e8c: bl              #0xb15398  ; AllocateXmlNodeListStub -> XmlNodeList<X0 bound XmlNode> (size=0x18)
    // 0xeb7e90: mov             x3, x0
    // 0xeb7e94: r0 = Sentinel
    //     0xeb7e94: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xeb7e98: stur            x3, [fp, #-0x10]
    // 0xeb7e9c: StoreField: r3->field_f = r0
    //     0xeb7e9c: stur            w0, [x3, #0xf]
    // 0xeb7ea0: StoreField: r3->field_13 = r0
    //     0xeb7ea0: stur            w0, [x3, #0x13]
    // 0xeb7ea4: r1 = <XmlAttribute>
    //     0xeb7ea4: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bf00] TypeArguments: <XmlAttribute>
    //     0xeb7ea8: ldr             x1, [x1, #0xf00]
    // 0xeb7eac: r2 = 0
    //     0xeb7eac: movz            x2, #0
    // 0xeb7eb0: r0 = _GrowableList()
    //     0xeb7eb0: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xeb7eb4: ldur            x1, [fp, #-0x10]
    // 0xeb7eb8: StoreField: r1->field_b = r0
    //     0xeb7eb8: stur            w0, [x1, #0xb]
    //     0xeb7ebc: ldurb           w16, [x1, #-1]
    //     0xeb7ec0: ldurb           w17, [x0, #-1]
    //     0xeb7ec4: and             x16, x17, x16, lsr #2
    //     0xeb7ec8: tst             x16, HEAP, lsr #32
    //     0xeb7ecc: b.eq            #0xeb7ed4
    //     0xeb7ed0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeb7ed4: mov             x0, x1
    // 0xeb7ed8: ldur            x1, [fp, #-8]
    // 0xeb7edc: StoreField: r1->field_b = r0
    //     0xeb7edc: stur            w0, [x1, #0xb]
    //     0xeb7ee0: ldurb           w16, [x1, #-1]
    //     0xeb7ee4: ldurb           w17, [x0, #-1]
    //     0xeb7ee8: and             x16, x17, x16, lsr #2
    //     0xeb7eec: tst             x16, HEAP, lsr #32
    //     0xeb7ef0: b.eq            #0xeb7ef8
    //     0xeb7ef4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xeb7ef8: r0 = forceCompileTimeTreeShaking()
    //     0xeb7ef8: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb7efc: r0 = Null
    //     0xeb7efc: mov             x0, NULL
    // 0xeb7f00: LeaveFrame
    //     0xeb7f00: mov             SP, fp
    //     0xeb7f04: ldp             fp, lr, [SP], #0x10
    // 0xeb7f08: ret
    //     0xeb7f08: ret             
    // 0xeb7f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7f0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7f10: b               #0xeb7e10
  }
}

// class id: 251, size: 0x1c, field offset: 0x14
class XmlElement extends _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren {

  _ accept(/* No info */) {
    // ** addr: 0xce0a64, size: 0x3c
    // 0xce0a64: EnterFrame
    //     0xce0a64: stp             fp, lr, [SP, #-0x10]!
    //     0xce0a68: mov             fp, SP
    // 0xce0a6c: mov             x16, x2
    // 0xce0a70: mov             x2, x1
    // 0xce0a74: mov             x1, x16
    // 0xce0a78: CheckStackOverflow
    //     0xce0a78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0a7c: cmp             SP, x16
    //     0xce0a80: b.ls            #0xce0a98
    // 0xce0a84: r0 = visitElement()
    //     0xce0a84: bl              #0xce0aa0  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitElement
    // 0xce0a88: r0 = Null
    //     0xce0a88: mov             x0, NULL
    // 0xce0a8c: LeaveFrame
    //     0xce0a8c: mov             SP, fp
    //     0xce0a90: ldp             fp, lr, [SP], #0x10
    // 0xce0a94: ret
    //     0xce0a94: ret             
    // 0xce0a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0a98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0a9c: b               #0xce0a84
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb7b9c, size: 0x174
    // 0xeb7b9c: EnterFrame
    //     0xeb7b9c: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7ba0: mov             fp, SP
    // 0xeb7ba4: AllocStack(0x40)
    //     0xeb7ba4: sub             SP, SP, #0x40
    // 0xeb7ba8: SetupParameters(XmlElement this /* r1 => r1, fp-0x10 */)
    //     0xeb7ba8: stur            x1, [fp, #-0x10]
    // 0xeb7bac: CheckStackOverflow
    //     0xeb7bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7bb0: cmp             SP, x16
    //     0xeb7bb4: b.ls            #0xeb7d08
    // 0xeb7bb8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xeb7bb8: ldur            w0, [x1, #0x17]
    // 0xeb7bbc: DecompressPointer r0
    //     0xeb7bbc: add             x0, x0, HEAP, lsl #32
    // 0xeb7bc0: r2 = LoadClassIdInstr(r0)
    //     0xeb7bc0: ldur            x2, [x0, #-1]
    //     0xeb7bc4: ubfx            x2, x2, #0xc, #0x14
    // 0xeb7bc8: cmp             x2, #0xe2
    // 0xeb7bcc: b.ne            #0xeb7bf4
    // 0xeb7bd0: LoadField: r2 = r0->field_b
    //     0xeb7bd0: ldur            w2, [x0, #0xb]
    // 0xeb7bd4: DecompressPointer r2
    //     0xeb7bd4: add             x2, x2, HEAP, lsl #32
    // 0xeb7bd8: stur            x2, [fp, #-8]
    // 0xeb7bdc: r0 = XmlSimpleName()
    //     0xeb7bdc: bl              #0xe76dc0  ; AllocateXmlSimpleNameStub -> XmlSimpleName (size=0x10)
    // 0xeb7be0: mov             x1, x0
    // 0xeb7be4: ldur            x0, [fp, #-8]
    // 0xeb7be8: StoreField: r1->field_b = r0
    //     0xeb7be8: stur            w0, [x1, #0xb]
    // 0xeb7bec: mov             x3, x1
    // 0xeb7bf0: b               #0xeb7c3c
    // 0xeb7bf4: LoadField: r1 = r0->field_b
    //     0xeb7bf4: ldur            w1, [x0, #0xb]
    // 0xeb7bf8: DecompressPointer r1
    //     0xeb7bf8: add             x1, x1, HEAP, lsl #32
    // 0xeb7bfc: stur            x1, [fp, #-0x20]
    // 0xeb7c00: LoadField: r2 = r0->field_f
    //     0xeb7c00: ldur            w2, [x0, #0xf]
    // 0xeb7c04: DecompressPointer r2
    //     0xeb7c04: add             x2, x2, HEAP, lsl #32
    // 0xeb7c08: stur            x2, [fp, #-0x18]
    // 0xeb7c0c: LoadField: r3 = r0->field_13
    //     0xeb7c0c: ldur            w3, [x0, #0x13]
    // 0xeb7c10: DecompressPointer r3
    //     0xeb7c10: add             x3, x3, HEAP, lsl #32
    // 0xeb7c14: stur            x3, [fp, #-8]
    // 0xeb7c18: r0 = XmlPrefixName()
    //     0xeb7c18: bl              #0xeb7b90  ; AllocateXmlPrefixNameStub -> XmlPrefixName (size=0x18)
    // 0xeb7c1c: mov             x1, x0
    // 0xeb7c20: ldur            x0, [fp, #-0x20]
    // 0xeb7c24: StoreField: r1->field_b = r0
    //     0xeb7c24: stur            w0, [x1, #0xb]
    // 0xeb7c28: ldur            x0, [fp, #-0x18]
    // 0xeb7c2c: StoreField: r1->field_f = r0
    //     0xeb7c2c: stur            w0, [x1, #0xf]
    // 0xeb7c30: ldur            x0, [fp, #-8]
    // 0xeb7c34: StoreField: r1->field_13 = r0
    //     0xeb7c34: stur            w0, [x1, #0x13]
    // 0xeb7c38: mov             x3, x1
    // 0xeb7c3c: ldur            x0, [fp, #-0x10]
    // 0xeb7c40: stur            x3, [fp, #-0x18]
    // 0xeb7c44: LoadField: r4 = r0->field_b
    //     0xeb7c44: ldur            w4, [x0, #0xb]
    // 0xeb7c48: DecompressPointer r4
    //     0xeb7c48: add             x4, x4, HEAP, lsl #32
    // 0xeb7c4c: stur            x4, [fp, #-8]
    // 0xeb7c50: r1 = Function '<anonymous closure>':.
    //     0xeb7c50: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d600] AnonymousClosure: (0xeb7f60), in [package:xml/src/xml/nodes/element.dart] XmlElement::copy (0xeb7b9c)
    //     0xeb7c54: ldr             x1, [x1, #0x600]
    // 0xeb7c58: r2 = Null
    //     0xeb7c58: mov             x2, NULL
    // 0xeb7c5c: r0 = AllocateClosure()
    //     0xeb7c5c: bl              #0xec1630  ; AllocateClosureStub
    // 0xeb7c60: r16 = <XmlAttribute>
    //     0xeb7c60: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bf00] TypeArguments: <XmlAttribute>
    //     0xeb7c64: ldr             x16, [x16, #0xf00]
    // 0xeb7c68: ldur            lr, [fp, #-8]
    // 0xeb7c6c: stp             lr, x16, [SP, #8]
    // 0xeb7c70: str             x0, [SP]
    // 0xeb7c74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xeb7c74: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xeb7c78: r0 = map()
    //     0xeb7c78: bl              #0x7d8994  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::map
    // 0xeb7c7c: mov             x3, x0
    // 0xeb7c80: ldur            x0, [fp, #-0x10]
    // 0xeb7c84: stur            x3, [fp, #-0x20]
    // 0xeb7c88: LoadField: r4 = r0->field_f
    //     0xeb7c88: ldur            w4, [x0, #0xf]
    // 0xeb7c8c: DecompressPointer r4
    //     0xeb7c8c: add             x4, x4, HEAP, lsl #32
    // 0xeb7c90: stur            x4, [fp, #-8]
    // 0xeb7c94: r1 = Function '<anonymous closure>':.
    //     0xeb7c94: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d608] AnonymousClosure: (0xeb7f20), in [package:xml/src/xml/nodes/document.dart] XmlDocument::copy (0xeb8324)
    //     0xeb7c98: ldr             x1, [x1, #0x608]
    // 0xeb7c9c: r2 = Null
    //     0xeb7c9c: mov             x2, NULL
    // 0xeb7ca0: r0 = AllocateClosure()
    //     0xeb7ca0: bl              #0xec1630  ; AllocateClosureStub
    // 0xeb7ca4: r16 = <XmlNode>
    //     0xeb7ca4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xeb7ca8: ldr             x16, [x16, #0x418]
    // 0xeb7cac: ldur            lr, [fp, #-8]
    // 0xeb7cb0: stp             lr, x16, [SP, #8]
    // 0xeb7cb4: str             x0, [SP]
    // 0xeb7cb8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xeb7cb8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xeb7cbc: r0 = map()
    //     0xeb7cbc: bl              #0x7d8994  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::map
    // 0xeb7cc0: mov             x1, x0
    // 0xeb7cc4: ldur            x0, [fp, #-0x10]
    // 0xeb7cc8: stur            x1, [fp, #-0x28]
    // 0xeb7ccc: LoadField: r6 = r0->field_13
    //     0xeb7ccc: ldur            w6, [x0, #0x13]
    // 0xeb7cd0: DecompressPointer r6
    //     0xeb7cd0: add             x6, x6, HEAP, lsl #32
    // 0xeb7cd4: stur            x6, [fp, #-8]
    // 0xeb7cd8: r0 = XmlElement()
    //     0xeb7cd8: bl              #0xeb7f14  ; AllocateXmlElementStub -> XmlElement (size=0x1c)
    // 0xeb7cdc: mov             x1, x0
    // 0xeb7ce0: ldur            x2, [fp, #-0x18]
    // 0xeb7ce4: ldur            x3, [fp, #-0x20]
    // 0xeb7ce8: ldur            x5, [fp, #-0x28]
    // 0xeb7cec: ldur            x6, [fp, #-8]
    // 0xeb7cf0: stur            x0, [fp, #-8]
    // 0xeb7cf4: r0 = XmlElement()
    //     0xeb7cf4: bl              #0xeb7d10  ; [package:xml/src/xml/nodes/element.dart] XmlElement::XmlElement
    // 0xeb7cf8: ldur            x0, [fp, #-8]
    // 0xeb7cfc: LeaveFrame
    //     0xeb7cfc: mov             SP, fp
    //     0xeb7d00: ldp             fp, lr, [SP], #0x10
    // 0xeb7d04: ret
    //     0xeb7d04: ret             
    // 0xeb7d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7d08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7d0c: b               #0xeb7bb8
  }
  _ XmlElement(/* No info */) {
    // ** addr: 0xeb7d10, size: 0xe0
    // 0xeb7d10: EnterFrame
    //     0xeb7d10: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7d14: mov             fp, SP
    // 0xeb7d18: AllocStack(0x20)
    //     0xeb7d18: sub             SP, SP, #0x20
    // 0xeb7d1c: SetupParameters(XmlElement this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xeb7d1c: mov             x4, x2
    //     0xeb7d20: stur            x2, [fp, #-0x10]
    //     0xeb7d24: mov             x2, x5
    //     0xeb7d28: stur            x5, [fp, #-0x20]
    //     0xeb7d2c: mov             x5, x1
    //     0xeb7d30: stur            x1, [fp, #-8]
    //     0xeb7d34: stur            x3, [fp, #-0x18]
    // 0xeb7d38: CheckStackOverflow
    //     0xeb7d38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7d3c: cmp             SP, x16
    //     0xeb7d40: b.ls            #0xeb7de8
    // 0xeb7d44: mov             x0, x4
    // 0xeb7d48: ArrayStore: r5[0] = r0  ; List_4
    //     0xeb7d48: stur            w0, [x5, #0x17]
    //     0xeb7d4c: ldurb           w16, [x5, #-1]
    //     0xeb7d50: ldurb           w17, [x0, #-1]
    //     0xeb7d54: and             x16, x17, x16, lsr #2
    //     0xeb7d58: tst             x16, HEAP, lsr #32
    //     0xeb7d5c: b.eq            #0xeb7d64
    //     0xeb7d60: bl              #0xec0aa8  ; WriteBarrierWrappersStub
    // 0xeb7d64: StoreField: r5->field_13 = r6
    //     0xeb7d64: stur            w6, [x5, #0x13]
    // 0xeb7d68: mov             x1, x5
    // 0xeb7d6c: r0 = _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren()
    //     0xeb7d6c: bl              #0xeb7df0  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren::_XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren
    // 0xeb7d70: ldur            x1, [fp, #-0x10]
    // 0xeb7d74: ldur            x2, [fp, #-8]
    // 0xeb7d78: r0 = attachParent()
    //     0xeb7d78: bl              #0xeb99d4  ; [package:xml/src/xml/nodes/data.dart] _XmlData&XmlNode&XmlHasParent::attachParent
    // 0xeb7d7c: ldur            x0, [fp, #-8]
    // 0xeb7d80: LoadField: r4 = r0->field_b
    //     0xeb7d80: ldur            w4, [x0, #0xb]
    // 0xeb7d84: DecompressPointer r4
    //     0xeb7d84: add             x4, x4, HEAP, lsl #32
    // 0xeb7d88: mov             x1, x4
    // 0xeb7d8c: mov             x2, x0
    // 0xeb7d90: stur            x4, [fp, #-0x10]
    // 0xeb7d94: r3 = _ConstSet len:1
    //     0xeb7d94: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bef0] Set<XmlNodeType>(1)
    //     0xeb7d98: ldr             x3, [x3, #0xef0]
    // 0xeb7d9c: r0 = initialize()
    //     0xeb7d9c: bl              #0xb152b4  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::initialize
    // 0xeb7da0: ldur            x1, [fp, #-0x10]
    // 0xeb7da4: ldur            x2, [fp, #-0x18]
    // 0xeb7da8: r0 = addAll()
    //     0xeb7da8: bl              #0x66f670  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::addAll
    // 0xeb7dac: ldur            x2, [fp, #-8]
    // 0xeb7db0: LoadField: r0 = r2->field_f
    //     0xeb7db0: ldur            w0, [x2, #0xf]
    // 0xeb7db4: DecompressPointer r0
    //     0xeb7db4: add             x0, x0, HEAP, lsl #32
    // 0xeb7db8: mov             x1, x0
    // 0xeb7dbc: stur            x0, [fp, #-0x10]
    // 0xeb7dc0: r3 = _ConstSet len:5
    //     0xeb7dc0: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bef8] Set<XmlNodeType>(5)
    //     0xeb7dc4: ldr             x3, [x3, #0xef8]
    // 0xeb7dc8: r0 = initialize()
    //     0xeb7dc8: bl              #0xb152b4  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::initialize
    // 0xeb7dcc: ldur            x1, [fp, #-0x10]
    // 0xeb7dd0: ldur            x2, [fp, #-0x20]
    // 0xeb7dd4: r0 = addAll()
    //     0xeb7dd4: bl              #0x66f670  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::addAll
    // 0xeb7dd8: r0 = Null
    //     0xeb7dd8: mov             x0, NULL
    // 0xeb7ddc: LeaveFrame
    //     0xeb7ddc: mov             SP, fp
    //     0xeb7de0: ldp             fp, lr, [SP], #0x10
    // 0xeb7de4: ret
    //     0xeb7de4: ret             
    // 0xeb7de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7de8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7dec: b               #0xeb7d44
  }
  [closure] XmlAttribute <anonymous closure>(dynamic, XmlAttribute) {
    // ** addr: 0xeb7f60, size: 0x30
    // 0xeb7f60: EnterFrame
    //     0xeb7f60: stp             fp, lr, [SP, #-0x10]!
    //     0xeb7f64: mov             fp, SP
    // 0xeb7f68: CheckStackOverflow
    //     0xeb7f68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb7f6c: cmp             SP, x16
    //     0xeb7f70: b.ls            #0xeb7f88
    // 0xeb7f74: ldr             x1, [fp, #0x10]
    // 0xeb7f78: r0 = copy()
    //     0xeb7f78: bl              #0xeb7a90  ; [package:xml/src/xml/nodes/attribute.dart] XmlAttribute::copy
    // 0xeb7f7c: LeaveFrame
    //     0xeb7f7c: mov             SP, fp
    //     0xeb7f80: ldp             fp, lr, [SP], #0x10
    // 0xeb7f84: ret
    //     0xeb7f84: ret             
    // 0xeb7f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb7f88: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb7f8c: b               #0xeb7f74
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a4c, size: 0xc
    // 0xeb9a4c: r0 = Instance_XmlNodeType
    //     0xeb9a4c: add             x0, PP, #0x26, lsl #12  ; [pp+0x26588] Obj!XmlNodeType@e2d3d1
    //     0xeb9a50: ldr             x0, [x0, #0x588]
    // 0xeb9a54: ret
    //     0xeb9a54: ret             
  }
  const get _ name(/* No info */) {
    // ** addr: 0xebac0c, size: 0xc
    // 0xebac0c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xebac0c: ldur            w0, [x1, #0x17]
    // 0xebac10: DecompressPointer r0
    //     0xebac10: add             x0, x0, HEAP, lsl #32
    // 0xebac14: ret
    //     0xebac14: ret             
  }
  _ XmlElement.tag(/* No info */) {
    // ** addr: 0xebb2f0, size: 0xfc
    // 0xebb2f0: EnterFrame
    //     0xebb2f0: stp             fp, lr, [SP, #-0x10]!
    //     0xebb2f4: mov             fp, SP
    // 0xebb2f8: AllocStack(0x20)
    //     0xebb2f8: sub             SP, SP, #0x20
    // 0xebb2fc: SetupParameters(XmlElement this /* r1 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, {dynamic children = const [] /* r6, fp-0x10 */, dynamic isSelfClosing = true /* r4, fp-0x8 */})
    //     0xebb2fc: mov             x0, x1
    //     0xebb300: stur            x1, [fp, #-0x18]
    //     0xebb304: stur            x3, [fp, #-0x20]
    //     0xebb308: ldur            w1, [x4, #0x13]
    //     0xebb30c: ldur            w5, [x4, #0x1f]
    //     0xebb310: add             x5, x5, HEAP, lsl #32
    //     0xebb314: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ab58] "children"
    //     0xebb318: ldr             x16, [x16, #0xb58]
    //     0xebb31c: cmp             w5, w16
    //     0xebb320: b.ne            #0xebb344
    //     0xebb324: ldur            w5, [x4, #0x23]
    //     0xebb328: add             x5, x5, HEAP, lsl #32
    //     0xebb32c: sub             w6, w1, w5
    //     0xebb330: add             x5, fp, w6, sxtw #2
    //     0xebb334: ldr             x5, [x5, #8]
    //     0xebb338: mov             x6, x5
    //     0xebb33c: movz            x5, #0x1
    //     0xebb340: b               #0xebb350
    //     0xebb344: add             x6, PP, #0x39, lsl #12  ; [pp+0x39480] List<XmlNode>(0)
    //     0xebb348: ldr             x6, [x6, #0x480]
    //     0xebb34c: movz            x5, #0
    //     0xebb350: stur            x6, [fp, #-0x10]
    //     0xebb354: lsl             x7, x5, #1
    //     0xebb358: lsl             w5, w7, #1
    //     0xebb35c: add             w7, w5, #8
    //     0xebb360: add             x16, x4, w7, sxtw #1
    //     0xebb364: ldur            w8, [x16, #0xf]
    //     0xebb368: add             x8, x8, HEAP, lsl #32
    //     0xebb36c: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bee8] "isSelfClosing"
    //     0xebb370: ldr             x16, [x16, #0xee8]
    //     0xebb374: cmp             w8, w16
    //     0xebb378: b.ne            #0xebb3a0
    //     0xebb37c: add             w7, w5, #0xa
    //     0xebb380: add             x16, x4, w7, sxtw #1
    //     0xebb384: ldur            w5, [x16, #0xf]
    //     0xebb388: add             x5, x5, HEAP, lsl #32
    //     0xebb38c: sub             w4, w1, w5
    //     0xebb390: add             x1, fp, w4, sxtw #2
    //     0xebb394: ldr             x1, [x1, #8]
    //     0xebb398: mov             x4, x1
    //     0xebb39c: b               #0xebb3a4
    //     0xebb3a0: add             x4, NULL, #0x20  ; true
    //     0xebb3a4: stur            x4, [fp, #-8]
    // 0xebb3a8: CheckStackOverflow
    //     0xebb3a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebb3ac: cmp             SP, x16
    //     0xebb3b0: b.ls            #0xebb3e4
    // 0xebb3b4: r1 = Null
    //     0xebb3b4: mov             x1, NULL
    // 0xebb3b8: r0 = XmlName.fromString()
    //     0xebb3b8: bl              #0xeba970  ; [package:xml/src/xml/utils/name.dart] XmlName::XmlName.fromString
    // 0xebb3bc: ldur            x1, [fp, #-0x18]
    // 0xebb3c0: mov             x2, x0
    // 0xebb3c4: ldur            x3, [fp, #-0x20]
    // 0xebb3c8: ldur            x5, [fp, #-0x10]
    // 0xebb3cc: ldur            x6, [fp, #-8]
    // 0xebb3d0: r0 = XmlElement()
    //     0xebb3d0: bl              #0xeb7d10  ; [package:xml/src/xml/nodes/element.dart] XmlElement::XmlElement
    // 0xebb3d4: r0 = Null
    //     0xebb3d4: mov             x0, NULL
    // 0xebb3d8: LeaveFrame
    //     0xebb3d8: mov             SP, fp
    //     0xebb3dc: ldp             fp, lr, [SP], #0x10
    // 0xebb3e0: ret
    //     0xebb3e0: ret             
    // 0xebb3e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb3e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb3e8: b               #0xebb3b4
  }
}
