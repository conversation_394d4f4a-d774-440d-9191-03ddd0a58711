// lib: , url: package:xml/src/xml/nodes/node.dart

// class id: 1051310, size: 0x8
class :: {
}

// class id: 229, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlNode&Object&XmlAttributesBase extends Object
     with XmlAttributesBase {

  get _ attributes(/* No info */) {
    // ** addr: 0xeb8880, size: 0xc
    // 0xeb8880: r0 = const []
    //     0xeb8880: add             x0, PP, #0x39, lsl #12  ; [pp+0x39478] List<XmlAttribute>(0)
    //     0xeb8884: ldr             x0, [x0, #0x478]
    // 0xeb8888: ret
    //     0xeb8888: ret             
  }
}

// class id: 230, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlNode&Object&XmlAttributesBase&XmlChildrenBase extends _XmlNode&Object&XmlAttributesBase
     with XmlChildrenBase {

  get _ children(/* No info */) {
    // ** addr: 0xeb7054, size: 0xc
    // 0xeb7054: r0 = const []
    //     0xeb7054: add             x0, PP, #0x39, lsl #12  ; [pp+0x39480] List<XmlNode>(0)
    //     0xeb7058: ldr             x0, [x0, #0x480]
    // 0xeb705c: ret
    //     0xeb705c: ret             
  }
}

// class id: 231, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor extends _XmlNode&Object&XmlAttributesBase&XmlChildrenBase
     with XmlHasVisitor {
}

// class id: 232, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter extends _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor
     with XmlHasWriter {

  _ toString(/* No info */) {
    // ** addr: 0xc447d4, size: 0x30
    // 0xc447d4: EnterFrame
    //     0xc447d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc447d8: mov             fp, SP
    // 0xc447dc: CheckStackOverflow
    //     0xc447dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc447e0: cmp             SP, x16
    //     0xc447e4: b.ls            #0xc447fc
    // 0xc447e8: ldr             x1, [fp, #0x10]
    // 0xc447ec: r0 = toXmlString()
    //     0xc447ec: bl              #0xc44804  ; [package:xml/src/xml/nodes/node.dart] _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter::toXmlString
    // 0xc447f0: LeaveFrame
    //     0xc447f0: mov             SP, fp
    //     0xc447f4: ldp             fp, lr, [SP], #0x10
    // 0xc447f8: ret
    //     0xc447f8: ret             
    // 0xc447fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc447fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc44800: b               #0xc447e8
  }
  _ toXmlString(/* No info */) {
    // ** addr: 0xc44804, size: 0xa0
    // 0xc44804: EnterFrame
    //     0xc44804: stp             fp, lr, [SP, #-0x10]!
    //     0xc44808: mov             fp, SP
    // 0xc4480c: AllocStack(0x20)
    //     0xc4480c: sub             SP, SP, #0x20
    // 0xc44810: SetupParameters(_XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter this /* r1 => r2, fp-0x8 */)
    //     0xc44810: mov             x2, x1
    //     0xc44814: stur            x1, [fp, #-8]
    // 0xc44818: CheckStackOverflow
    //     0xc44818: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4481c: cmp             SP, x16
    //     0xc44820: b.ls            #0xc4489c
    // 0xc44824: r0 = StringBuffer()
    //     0xc44824: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc44828: mov             x1, x0
    // 0xc4482c: stur            x0, [fp, #-0x10]
    // 0xc44830: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc44830: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc44834: r0 = StringBuffer()
    //     0xc44834: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc44838: r0 = XmlWriter()
    //     0xc44838: bl              #0xc448f4  ; AllocateXmlWriterStub -> XmlWriter (size=0x10)
    // 0xc4483c: mov             x1, x0
    // 0xc44840: ldur            x0, [fp, #-0x10]
    // 0xc44844: stur            x1, [fp, #-0x18]
    // 0xc44848: StoreField: r1->field_7 = r0
    //     0xc44848: stur            w0, [x1, #7]
    // 0xc4484c: r0 = InitLateStaticField(0xc04) // [package:xml/src/xml/entities/default_mapping.dart] ::defaultEntityMapping
    //     0xc4484c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc44850: ldr             x0, [x0, #0x1808]
    //     0xc44854: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc44858: cmp             w0, w16
    //     0xc4485c: b.ne            #0xc4486c
    //     0xc44860: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cc8] Field <::.defaultEntityMapping>: static late (offset: 0xc04)
    //     0xc44864: ldr             x2, [x2, #0xcc8]
    //     0xc44868: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc4486c: ldur            x1, [fp, #-0x18]
    // 0xc44870: r0 = Instance_XmlDefaultEntityMapping
    //     0xc44870: add             x0, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xc44874: ldr             x0, [x0, #0x5c0]
    // 0xc44878: StoreField: r1->field_b = r0
    //     0xc44878: stur            w0, [x1, #0xb]
    // 0xc4487c: ldur            x2, [fp, #-8]
    // 0xc44880: r0 = visit()
    //     0xc44880: bl              #0xc448a4  ; [package:xml/src/xml/visitors/normalizer.dart] _XmlNormalizer&Object&XmlVisitor::visit
    // 0xc44884: ldur            x16, [fp, #-0x10]
    // 0xc44888: str             x16, [SP]
    // 0xc4488c: r0 = toString()
    //     0xc4488c: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc44890: LeaveFrame
    //     0xc44890: mov             SP, fp
    //     0xc44894: ldp             fp, lr, [SP], #0x10
    // 0xc44898: ret
    //     0xc44898: ret             
    // 0xc4489c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4489c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc448a0: b               #0xc44824
  }
}

// class id: 233, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter&XmlParentBase extends _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter
     with XmlParentBase {

  _ detachParent(/* No info */) {
    // ** addr: 0xeb9b80, size: 0x2c
    // 0xeb9b80: EnterFrame
    //     0xeb9b80: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9b84: mov             fp, SP
    // 0xeb9b88: CheckStackOverflow
    //     0xeb9b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9b8c: cmp             SP, x16
    //     0xeb9b90: b.ls            #0xeb9ba4
    // 0xeb9b94: r0 = _throwNoParent()
    //     0xeb9b94: bl              #0xeb9bac  ; [package:xml/src/xml/nodes/node.dart] _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter&XmlParentBase::_throwNoParent
    // 0xeb9b98: LeaveFrame
    //     0xeb9b98: mov             SP, fp
    //     0xeb9b9c: ldp             fp, lr, [SP], #0x10
    // 0xeb9ba0: ret
    //     0xeb9ba0: ret             
    // 0xeb9ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9ba4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9ba8: b               #0xeb9b94
  }
  _ _throwNoParent(/* No info */) {
    // ** addr: 0xeb9bac, size: 0x74
    // 0xeb9bac: EnterFrame
    //     0xeb9bac: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9bb0: mov             fp, SP
    // 0xeb9bb4: AllocStack(0x10)
    //     0xeb9bb4: sub             SP, SP, #0x10
    // 0xeb9bb8: SetupParameters(_XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter&XmlParentBase this /* r1 => r0, fp-0x8 */)
    //     0xeb9bb8: mov             x0, x1
    //     0xeb9bbc: stur            x1, [fp, #-8]
    // 0xeb9bc0: CheckStackOverflow
    //     0xeb9bc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9bc4: cmp             SP, x16
    //     0xeb9bc8: b.ls            #0xeb9c18
    // 0xeb9bcc: r1 = Null
    //     0xeb9bcc: mov             x1, NULL
    // 0xeb9bd0: r2 = 4
    //     0xeb9bd0: movz            x2, #0x4
    // 0xeb9bd4: r0 = AllocateArray()
    //     0xeb9bd4: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb9bd8: mov             x1, x0
    // 0xeb9bdc: ldur            x0, [fp, #-8]
    // 0xeb9be0: StoreField: r1->field_f = r0
    //     0xeb9be0: stur            w0, [x1, #0xf]
    // 0xeb9be4: r16 = " does not have a parent"
    //     0xeb9be4: add             x16, PP, #0x39, lsl #12  ; [pp+0x39488] " does not have a parent"
    //     0xeb9be8: ldr             x16, [x16, #0x488]
    // 0xeb9bec: StoreField: r1->field_13 = r16
    //     0xeb9bec: stur            w16, [x1, #0x13]
    // 0xeb9bf0: str             x1, [SP]
    // 0xeb9bf4: r0 = _interpolate()
    //     0xeb9bf4: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeb9bf8: stur            x0, [fp, #-8]
    // 0xeb9bfc: r0 = UnsupportedError()
    //     0xeb9bfc: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0xeb9c00: mov             x1, x0
    // 0xeb9c04: ldur            x0, [fp, #-8]
    // 0xeb9c08: StoreField: r1->field_b = r0
    //     0xeb9c08: stur            w0, [x1, #0xb]
    // 0xeb9c0c: mov             x0, x1
    // 0xeb9c10: r0 = Throw()
    //     0xeb9c10: bl              #0xec04b8  ; ThrowStub
    // 0xeb9c14: brk             #0
    // 0xeb9c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9c1c: b               #0xeb9bcc
  }
}

// class id: 234, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter&XmlParentBase&XmlValueBase extends _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter&XmlParentBase
     with XmlValueBase {
}

// class id: 235, size: 0x8, field offset: 0x8
abstract class XmlNode extends _XmlNode&Object&XmlAttributesBase&XmlChildrenBase&XmlHasVisitor&XmlHasWriter&XmlParentBase&XmlValueBase {
}
