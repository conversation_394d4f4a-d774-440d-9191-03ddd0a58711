// lib: , url: package:xml/src/xml/nodes/processing.dart

// class id: 1051311, size: 0x8
class :: {
}

// class id: 244, size: 0x14, field offset: 0x10
class XmlProcessing extends XmlData {

  _ accept(/* No info */) {
    // ** addr: 0xce1234, size: 0x3c
    // 0xce1234: EnterFrame
    //     0xce1234: stp             fp, lr, [SP, #-0x10]!
    //     0xce1238: mov             fp, SP
    // 0xce123c: mov             x16, x2
    // 0xce1240: mov             x2, x1
    // 0xce1244: mov             x1, x16
    // 0xce1248: CheckStackOverflow
    //     0xce1248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce124c: cmp             SP, x16
    //     0xce1250: b.ls            #0xce1268
    // 0xce1254: r0 = visitProcessing()
    //     0xce1254: bl              #0xce1270  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitProcessing
    // 0xce1258: r0 = Null
    //     0xce1258: mov             x0, NULL
    // 0xce125c: LeaveFrame
    //     0xce125c: mov             SP, fp
    //     0xce1260: ldp             fp, lr, [SP], #0x10
    // 0xce1264: ret
    //     0xce1264: ret             
    // 0xce1268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1268: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce126c: b               #0xce1254
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb8058, size: 0x6c
    // 0xeb8058: EnterFrame
    //     0xeb8058: stp             fp, lr, [SP, #-0x10]!
    //     0xeb805c: mov             fp, SP
    // 0xeb8060: AllocStack(0x18)
    //     0xeb8060: sub             SP, SP, #0x18
    // 0xeb8064: CheckStackOverflow
    //     0xeb8064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb8068: cmp             SP, x16
    //     0xeb806c: b.ls            #0xeb80bc
    // 0xeb8070: LoadField: r0 = r1->field_f
    //     0xeb8070: ldur            w0, [x1, #0xf]
    // 0xeb8074: DecompressPointer r0
    //     0xeb8074: add             x0, x0, HEAP, lsl #32
    // 0xeb8078: stur            x0, [fp, #-0x10]
    // 0xeb807c: LoadField: r2 = r1->field_b
    //     0xeb807c: ldur            w2, [x1, #0xb]
    // 0xeb8080: DecompressPointer r2
    //     0xeb8080: add             x2, x2, HEAP, lsl #32
    // 0xeb8084: stur            x2, [fp, #-8]
    // 0xeb8088: r0 = XmlProcessing()
    //     0xeb8088: bl              #0xeb80c4  ; AllocateXmlProcessingStub -> XmlProcessing (size=0x14)
    // 0xeb808c: mov             x2, x0
    // 0xeb8090: ldur            x0, [fp, #-0x10]
    // 0xeb8094: stur            x2, [fp, #-0x18]
    // 0xeb8098: StoreField: r2->field_f = r0
    //     0xeb8098: stur            w0, [x2, #0xf]
    // 0xeb809c: ldur            x0, [fp, #-8]
    // 0xeb80a0: StoreField: r2->field_b = r0
    //     0xeb80a0: stur            w0, [x2, #0xb]
    // 0xeb80a4: mov             x1, x2
    // 0xeb80a8: r0 = forceCompileTimeTreeShaking()
    //     0xeb80a8: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb80ac: ldur            x0, [fp, #-0x18]
    // 0xeb80b0: LeaveFrame
    //     0xeb80b0: mov             SP, fp
    //     0xeb80b4: ldp             fp, lr, [SP], #0x10
    // 0xeb80b8: ret
    //     0xeb80b8: ret             
    // 0xeb80bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb80bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb80c0: b               #0xeb8070
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a70, size: 0xc
    // 0xeb9a70: r0 = Instance_XmlNodeType
    //     0xeb9a70: add             x0, PP, #0x31, lsl #12  ; [pp+0x310a8] Obj!XmlNodeType@e2d3b1
    //     0xeb9a74: ldr             x0, [x0, #0xa8]
    // 0xeb9a78: ret
    //     0xeb9a78: ret             
  }
}
