// lib: , url: package:xml/src/xml/nodes/text.dart

// class id: 1051312, size: 0x8
class :: {
}

// class id: 243, size: 0x10, field offset: 0x10
class XmlText extends XmlData {

  _ accept(/* No info */) {
    // ** addr: 0xce131c, size: 0x3c
    // 0xce131c: EnterFrame
    //     0xce131c: stp             fp, lr, [SP, #-0x10]!
    //     0xce1320: mov             fp, SP
    // 0xce1324: mov             x16, x2
    // 0xce1328: mov             x2, x1
    // 0xce132c: mov             x1, x16
    // 0xce1330: CheckStackOverflow
    //     0xce1330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1334: cmp             SP, x16
    //     0xce1338: b.ls            #0xce1350
    // 0xce133c: r0 = visitText()
    //     0xce133c: bl              #0xce1358  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitText
    // 0xce1340: r0 = Null
    //     0xce1340: mov             x0, NULL
    // 0xce1344: LeaveFrame
    //     0xce1344: mov             SP, fp
    //     0xce1348: ldp             fp, lr, [SP], #0x10
    // 0xce134c: ret
    //     0xce134c: ret             
    // 0xce1350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1350: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1354: b               #0xce133c
  }
  _ copy(/* No info */) {
    // ** addr: 0xeb80d0, size: 0x58
    // 0xeb80d0: EnterFrame
    //     0xeb80d0: stp             fp, lr, [SP, #-0x10]!
    //     0xeb80d4: mov             fp, SP
    // 0xeb80d8: AllocStack(0x10)
    //     0xeb80d8: sub             SP, SP, #0x10
    // 0xeb80dc: CheckStackOverflow
    //     0xeb80dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb80e0: cmp             SP, x16
    //     0xeb80e4: b.ls            #0xeb8120
    // 0xeb80e8: LoadField: r0 = r1->field_b
    //     0xeb80e8: ldur            w0, [x1, #0xb]
    // 0xeb80ec: DecompressPointer r0
    //     0xeb80ec: add             x0, x0, HEAP, lsl #32
    // 0xeb80f0: stur            x0, [fp, #-8]
    // 0xeb80f4: r0 = XmlText()
    //     0xeb80f4: bl              #0xeb8128  ; AllocateXmlTextStub -> XmlText (size=0x10)
    // 0xeb80f8: mov             x2, x0
    // 0xeb80fc: ldur            x0, [fp, #-8]
    // 0xeb8100: stur            x2, [fp, #-0x10]
    // 0xeb8104: StoreField: r2->field_b = r0
    //     0xeb8104: stur            w0, [x2, #0xb]
    // 0xeb8108: mov             x1, x2
    // 0xeb810c: r0 = forceCompileTimeTreeShaking()
    //     0xeb810c: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb8110: ldur            x0, [fp, #-0x10]
    // 0xeb8114: LeaveFrame
    //     0xeb8114: mov             SP, fp
    //     0xeb8118: ldp             fp, lr, [SP], #0x10
    // 0xeb811c: ret
    //     0xeb811c: ret             
    // 0xeb8120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb8120: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb8124: b               #0xeb80e8
  }
  get _ nodeType(/* No info */) {
    // ** addr: 0xeb9a7c, size: 0xc
    // 0xeb9a7c: r0 = Instance_XmlNodeType
    //     0xeb9a7c: add             x0, PP, #0x31, lsl #12  ; [pp+0x310a0] Obj!XmlNodeType@e2d391
    //     0xeb9a80: ldr             x0, [x0, #0xa0]
    // 0xeb9a84: ret
    //     0xeb9a84: ret             
  }
}
