// lib: , url: package:xml/src/xml/utils/cache.dart

// class id: 1051313, size: 0x8
class :: {
}

// class id: 228, size: 0x1c, field offset: 0x8
class XmlCache<X0, X1> extends Object {

  X1 [](XmlCache<X0, X1>, X0) {
    // ** addr: 0x88916c, size: 0x1e4
    // 0x88916c: EnterFrame
    //     0x88916c: stp             fp, lr, [SP, #-0x10]!
    //     0x889170: mov             fp, SP
    // 0x889174: AllocStack(0x30)
    //     0x889174: sub             SP, SP, #0x30
    // 0x889178: SetupParameters(XmlCache<X0, X1> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x889178: mov             x4, x1
    //     0x88917c: mov             x3, x2
    //     0x889180: stur            x1, [fp, #-8]
    //     0x889184: stur            x2, [fp, #-0x10]
    // 0x889188: CheckStackOverflow
    //     0x889188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88918c: cmp             SP, x16
    //     0x889190: b.ls            #0x88933c
    // 0x889194: LoadField: r2 = r4->field_7
    //     0x889194: ldur            w2, [x4, #7]
    // 0x889198: DecompressPointer r2
    //     0x889198: add             x2, x2, HEAP, lsl #32
    // 0x88919c: mov             x0, x3
    // 0x8891a0: r1 = Null
    //     0x8891a0: mov             x1, NULL
    // 0x8891a4: cmp             w2, NULL
    // 0x8891a8: b.eq            #0x8891c8
    // 0x8891ac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8891ac: ldur            w4, [x2, #0x17]
    // 0x8891b0: DecompressPointer r4
    //     0x8891b0: add             x4, x4, HEAP, lsl #32
    // 0x8891b4: r8 = X0
    //     0x8891b4: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x8891b8: LoadField: r9 = r4->field_7
    //     0x8891b8: ldur            x9, [x4, #7]
    // 0x8891bc: r3 = Null
    //     0x8891bc: add             x3, PP, #0x26, lsl #12  ; [pp+0x265c8] Null
    //     0x8891c0: ldr             x3, [x3, #0x5c8]
    // 0x8891c4: blr             x9
    // 0x8891c8: ldur            x0, [fp, #-8]
    // 0x8891cc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8891cc: ldur            w3, [x0, #0x17]
    // 0x8891d0: DecompressPointer r3
    //     0x8891d0: add             x3, x3, HEAP, lsl #32
    // 0x8891d4: mov             x1, x3
    // 0x8891d8: ldur            x2, [fp, #-0x10]
    // 0x8891dc: stur            x3, [fp, #-0x18]
    // 0x8891e0: r0 = containsKey()
    //     0x8891e0: bl              #0xd784d8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x8891e4: tbz             w0, #4, #0x8892e8
    // 0x8891e8: ldur            x0, [fp, #-8]
    // 0x8891ec: ldur            x1, [fp, #-0x18]
    // 0x8891f0: LoadField: r2 = r0->field_b
    //     0x8891f0: ldur            w2, [x0, #0xb]
    // 0x8891f4: DecompressPointer r2
    //     0x8891f4: add             x2, x2, HEAP, lsl #32
    // 0x8891f8: ldur            x16, [fp, #-0x10]
    // 0x8891fc: stp             x16, x2, [SP]
    // 0x889200: mov             x0, x2
    // 0x889204: ClosureCall
    //     0x889204: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x889208: ldur            x2, [x0, #0x1f]
    //     0x88920c: blr             x2
    // 0x889210: ldur            x1, [fp, #-0x18]
    // 0x889214: ldur            x2, [fp, #-0x10]
    // 0x889218: mov             x3, x0
    // 0x88921c: r0 = []=()
    //     0x88921c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x889220: ldur            x0, [fp, #-0x18]
    // 0x889224: LoadField: r2 = r0->field_7
    //     0x889224: ldur            w2, [x0, #7]
    // 0x889228: DecompressPointer r2
    //     0x889228: add             x2, x2, HEAP, lsl #32
    // 0x88922c: stur            x2, [fp, #-8]
    // 0x889230: CheckStackOverflow
    //     0x889230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889234: cmp             SP, x16
    //     0x889238: b.ls            #0x889344
    // 0x88923c: LoadField: r1 = r0->field_13
    //     0x88923c: ldur            w1, [x0, #0x13]
    // 0x889240: r3 = LoadInt32Instr(r1)
    //     0x889240: sbfx            x3, x1, #1, #0x1f
    // 0x889244: asr             x1, x3, #1
    // 0x889248: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x889248: ldur            w3, [x0, #0x17]
    // 0x88924c: r4 = LoadInt32Instr(r3)
    //     0x88924c: sbfx            x4, x3, #1, #0x1f
    // 0x889250: sub             x3, x1, x4
    // 0x889254: cmp             x3, #5
    // 0x889258: b.le            #0x8892e8
    // 0x88925c: mov             x1, x2
    // 0x889260: r0 = _CompactIterable()
    //     0x889260: bl              #0x6251ec  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x889264: mov             x1, x0
    // 0x889268: ldur            x0, [fp, #-0x18]
    // 0x88926c: StoreField: r1->field_b = r0
    //     0x88926c: stur            w0, [x1, #0xb]
    // 0x889270: r2 = -2
    //     0x889270: orr             x2, xzr, #0xfffffffffffffffe
    // 0x889274: StoreField: r1->field_f = r2
    //     0x889274: stur            x2, [x1, #0xf]
    // 0x889278: r3 = 2
    //     0x889278: movz            x3, #0x2
    // 0x88927c: ArrayStore: r1[0] = r3  ; List_8
    //     0x88927c: stur            x3, [x1, #0x17]
    // 0x889280: r0 = iterator()
    //     0x889280: bl              #0x8879e8  ; [dart:_compact_hash] _CompactIterable::iterator
    // 0x889284: mov             x2, x0
    // 0x889288: stur            x2, [fp, #-0x20]
    // 0x88928c: r0 = LoadClassIdInstr(r2)
    //     0x88928c: ldur            x0, [x2, #-1]
    //     0x889290: ubfx            x0, x0, #0xc, #0x14
    // 0x889294: mov             x1, x2
    // 0x889298: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x889298: movz            x17, #0x292d
    //     0x88929c: movk            x17, #0x1, lsl #16
    //     0x8892a0: add             lr, x0, x17
    //     0x8892a4: ldr             lr, [x21, lr, lsl #3]
    //     0x8892a8: blr             lr
    // 0x8892ac: tbnz            w0, #4, #0x889330
    // 0x8892b0: ldur            x1, [fp, #-0x20]
    // 0x8892b4: r0 = LoadClassIdInstr(r1)
    //     0x8892b4: ldur            x0, [x1, #-1]
    //     0x8892b8: ubfx            x0, x0, #0xc, #0x14
    // 0x8892bc: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x8892bc: movz            x17, #0x384d
    //     0x8892c0: movk            x17, #0x1, lsl #16
    //     0x8892c4: add             lr, x0, x17
    //     0x8892c8: ldr             lr, [x21, lr, lsl #3]
    //     0x8892cc: blr             lr
    // 0x8892d0: ldur            x1, [fp, #-0x18]
    // 0x8892d4: mov             x2, x0
    // 0x8892d8: r0 = remove()
    //     0x8892d8: bl              #0xd73e08  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x8892dc: ldur            x0, [fp, #-0x18]
    // 0x8892e0: ldur            x2, [fp, #-8]
    // 0x8892e4: b               #0x889230
    // 0x8892e8: ldur            x0, [fp, #-0x18]
    // 0x8892ec: mov             x1, x0
    // 0x8892f0: ldur            x2, [fp, #-0x10]
    // 0x8892f4: r0 = _getValueOrData()
    //     0x8892f4: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8892f8: mov             x1, x0
    // 0x8892fc: ldur            x0, [fp, #-0x18]
    // 0x889300: LoadField: r2 = r0->field_f
    //     0x889300: ldur            w2, [x0, #0xf]
    // 0x889304: DecompressPointer r2
    //     0x889304: add             x2, x2, HEAP, lsl #32
    // 0x889308: cmp             w2, w1
    // 0x88930c: b.ne            #0x889318
    // 0x889310: r0 = Null
    //     0x889310: mov             x0, NULL
    // 0x889314: b               #0x88931c
    // 0x889318: mov             x0, x1
    // 0x88931c: cmp             w0, NULL
    // 0x889320: b.eq            #0x88934c
    // 0x889324: LeaveFrame
    //     0x889324: mov             SP, fp
    //     0x889328: ldp             fp, lr, [SP], #0x10
    // 0x88932c: ret
    //     0x88932c: ret             
    // 0x889330: r0 = noElement()
    //     0x889330: bl              #0x60361c  ; [dart:_internal] IterableElementError::noElement
    // 0x889334: r0 = Throw()
    //     0x889334: bl              #0xec04b8  ; ThrowStub
    // 0x889338: brk             #0
    // 0x88933c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88933c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889340: b               #0x889194
    // 0x889344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889348: b               #0x88923c
    // 0x88934c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x88934c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  X1 [](XmlCache<X0, X1>, X0) {
    // ** addr: 0x889368, size: 0x4c
    // 0x889368: EnterFrame
    //     0x889368: stp             fp, lr, [SP, #-0x10]!
    //     0x88936c: mov             fp, SP
    // 0x889370: CheckStackOverflow
    //     0x889370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889374: cmp             SP, x16
    //     0x889378: b.ls            #0x889394
    // 0x88937c: ldr             x1, [fp, #0x18]
    // 0x889380: ldr             x2, [fp, #0x10]
    // 0x889384: r0 = []()
    //     0x889384: bl              #0x88916c  ; [package:xml/src/xml/utils/cache.dart] XmlCache::[]
    // 0x889388: LeaveFrame
    //     0x889388: mov             SP, fp
    //     0x88938c: ldp             fp, lr, [SP], #0x10
    // 0x889390: ret
    //     0x889390: ret             
    // 0x889394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889394: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889398: b               #0x88937c
  }
}
