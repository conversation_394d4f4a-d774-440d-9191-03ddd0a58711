// lib: , url: package:xml/src/xml/utils/character_data_parser.dart

// class id: 1051314, size: 0x8
class :: {
}

// class id: 727, size: 0x18, field offset: 0xc
class XmlCharacterDataParser extends Parser<dynamic> {

  _ fastParseOn(/* No info */) {
    // ** addr: 0xeb0b70, size: 0xe8
    // 0xeb0b70: EnterFrame
    //     0xeb0b70: stp             fp, lr, [SP, #-0x10]!
    //     0xeb0b74: mov             fp, SP
    // 0xeb0b78: AllocStack(0x20)
    //     0xeb0b78: sub             SP, SP, #0x20
    // 0xeb0b7c: SetupParameters(XmlCharacterDataParser this /* r1 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xeb0b7c: mov             x4, x1
    //     0xeb0b80: stur            x1, [fp, #-0x10]
    //     0xeb0b84: stur            x3, [fp, #-0x18]
    // 0xeb0b88: CheckStackOverflow
    //     0xeb0b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb0b8c: cmp             SP, x16
    //     0xeb0b90: b.ls            #0xeb0c50
    // 0xeb0b94: LoadField: r0 = r2->field_7
    //     0xeb0b94: ldur            w0, [x2, #7]
    // 0xeb0b98: r5 = LoadInt32Instr(r0)
    //     0xeb0b98: sbfx            x5, x0, #1, #0x1f
    // 0xeb0b9c: stur            x5, [fp, #-8]
    // 0xeb0ba0: cmp             x3, x5
    // 0xeb0ba4: b.ge            #0xeb0bf4
    // 0xeb0ba8: LoadField: r6 = r4->field_b
    //     0xeb0ba8: ldur            w6, [x4, #0xb]
    // 0xeb0bac: DecompressPointer r6
    //     0xeb0bac: add             x6, x6, HEAP, lsl #32
    // 0xeb0bb0: r0 = BoxInt64Instr(r3)
    //     0xeb0bb0: sbfiz           x0, x3, #1, #0x1f
    //     0xeb0bb4: cmp             x3, x0, asr #1
    //     0xeb0bb8: b.eq            #0xeb0bc4
    //     0xeb0bbc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb0bc0: stur            x3, [x0, #7]
    // 0xeb0bc4: r1 = LoadClassIdInstr(r2)
    //     0xeb0bc4: ldur            x1, [x2, #-1]
    //     0xeb0bc8: ubfx            x1, x1, #0xc, #0x14
    // 0xeb0bcc: str             x0, [SP]
    // 0xeb0bd0: mov             x0, x1
    // 0xeb0bd4: mov             x1, x2
    // 0xeb0bd8: mov             x2, x6
    // 0xeb0bdc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb0bdc: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb0be0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xeb0be0: sub             lr, x0, #0xffa
    //     0xeb0be4: ldr             lr, [x21, lr, lsl #3]
    //     0xeb0be8: blr             lr
    // 0xeb0bec: mov             x2, x0
    // 0xeb0bf0: b               #0xeb0bf8
    // 0xeb0bf4: ldur            x2, [fp, #-8]
    // 0xeb0bf8: cmn             x2, #1
    // 0xeb0bfc: b.ne            #0xeb0c08
    // 0xeb0c00: ldur            x4, [fp, #-8]
    // 0xeb0c04: b               #0xeb0c0c
    // 0xeb0c08: mov             x4, x2
    // 0xeb0c0c: ldur            x3, [fp, #-0x10]
    // 0xeb0c10: ldur            x2, [fp, #-0x18]
    // 0xeb0c14: sub             x5, x4, x2
    // 0xeb0c18: LoadField: r2 = r3->field_f
    //     0xeb0c18: ldur            x2, [x3, #0xf]
    // 0xeb0c1c: cmp             x5, x2
    // 0xeb0c20: b.ge            #0xeb0c2c
    // 0xeb0c24: r2 = -1
    //     0xeb0c24: movn            x2, #0
    // 0xeb0c28: b               #0xeb0c30
    // 0xeb0c2c: mov             x2, x4
    // 0xeb0c30: r0 = BoxInt64Instr(r2)
    //     0xeb0c30: sbfiz           x0, x2, #1, #0x1f
    //     0xeb0c34: cmp             x2, x0, asr #1
    //     0xeb0c38: b.eq            #0xeb0c44
    //     0xeb0c3c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb0c40: stur            x2, [x0, #7]
    // 0xeb0c44: LeaveFrame
    //     0xeb0c44: mov             SP, fp
    //     0xeb0c48: ldp             fp, lr, [SP], #0x10
    // 0xeb0c4c: ret
    //     0xeb0c4c: ret             
    // 0xeb0c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb0c50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb0c54: b               #0xeb0b94
  }
  _ parseOn(/* No info */) {
    // ** addr: 0xeb35c8, size: 0x160
    // 0xeb35c8: EnterFrame
    //     0xeb35c8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb35cc: mov             fp, SP
    // 0xeb35d0: AllocStack(0x50)
    //     0xeb35d0: sub             SP, SP, #0x50
    // 0xeb35d4: SetupParameters(XmlCharacterDataParser this /* r1 => r4, fp-0x20 */, dynamic _ /* r2 => r3, fp-0x28 */)
    //     0xeb35d4: mov             x4, x1
    //     0xeb35d8: mov             x3, x2
    //     0xeb35dc: stur            x1, [fp, #-0x20]
    //     0xeb35e0: stur            x2, [fp, #-0x28]
    // 0xeb35e4: CheckStackOverflow
    //     0xeb35e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb35e8: cmp             SP, x16
    //     0xeb35ec: b.ls            #0xeb3720
    // 0xeb35f0: LoadField: r5 = r3->field_7
    //     0xeb35f0: ldur            w5, [x3, #7]
    // 0xeb35f4: DecompressPointer r5
    //     0xeb35f4: add             x5, x5, HEAP, lsl #32
    // 0xeb35f8: stur            x5, [fp, #-0x18]
    // 0xeb35fc: LoadField: r6 = r3->field_b
    //     0xeb35fc: ldur            x6, [x3, #0xb]
    // 0xeb3600: stur            x6, [fp, #-0x10]
    // 0xeb3604: LoadField: r0 = r5->field_7
    //     0xeb3604: ldur            w0, [x5, #7]
    // 0xeb3608: r7 = LoadInt32Instr(r0)
    //     0xeb3608: sbfx            x7, x0, #1, #0x1f
    // 0xeb360c: stur            x7, [fp, #-8]
    // 0xeb3610: cmp             x6, x7
    // 0xeb3614: b.ge            #0xeb365c
    // 0xeb3618: LoadField: r2 = r4->field_b
    //     0xeb3618: ldur            w2, [x4, #0xb]
    // 0xeb361c: DecompressPointer r2
    //     0xeb361c: add             x2, x2, HEAP, lsl #32
    // 0xeb3620: r0 = BoxInt64Instr(r6)
    //     0xeb3620: sbfiz           x0, x6, #1, #0x1f
    //     0xeb3624: cmp             x6, x0, asr #1
    //     0xeb3628: b.eq            #0xeb3634
    //     0xeb362c: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb3630: stur            x6, [x0, #7]
    // 0xeb3634: r1 = LoadClassIdInstr(r5)
    //     0xeb3634: ldur            x1, [x5, #-1]
    //     0xeb3638: ubfx            x1, x1, #0xc, #0x14
    // 0xeb363c: str             x0, [SP]
    // 0xeb3640: mov             x0, x1
    // 0xeb3644: mov             x1, x5
    // 0xeb3648: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb3648: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb364c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xeb364c: sub             lr, x0, #0xffa
    //     0xeb3650: ldr             lr, [x21, lr, lsl #3]
    //     0xeb3654: blr             lr
    // 0xeb3658: b               #0xeb3660
    // 0xeb365c: ldur            x0, [fp, #-8]
    // 0xeb3660: cmn             x0, #1
    // 0xeb3664: b.ne            #0xeb3670
    // 0xeb3668: ldur            x4, [fp, #-8]
    // 0xeb366c: b               #0xeb3674
    // 0xeb3670: mov             x4, x0
    // 0xeb3674: ldur            x2, [fp, #-0x20]
    // 0xeb3678: ldur            x3, [fp, #-0x10]
    // 0xeb367c: r0 = BoxInt64Instr(r4)
    //     0xeb367c: sbfiz           x0, x4, #1, #0x1f
    //     0xeb3680: cmp             x4, x0, asr #1
    //     0xeb3684: b.eq            #0xeb3690
    //     0xeb3688: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeb368c: stur            x4, [x0, #7]
    // 0xeb3690: stur            x0, [fp, #-0x30]
    // 0xeb3694: sub             x1, x4, x3
    // 0xeb3698: LoadField: r4 = r2->field_f
    //     0xeb3698: ldur            x4, [x2, #0xf]
    // 0xeb369c: cmp             x1, x4
    // 0xeb36a0: b.ge            #0xeb36e0
    // 0xeb36a4: ldur            x0, [fp, #-0x18]
    // 0xeb36a8: r1 = <Never>
    //     0xeb36a8: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0xeb36ac: r0 = Failure()
    //     0xeb36ac: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0xeb36b0: mov             x1, x0
    // 0xeb36b4: r0 = "Unable to parse character data."
    //     0xeb36b4: add             x0, PP, #0x31, lsl #12  ; [pp+0x310f8] "Unable to parse character data."
    //     0xeb36b8: ldr             x0, [x0, #0xf8]
    // 0xeb36bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xeb36bc: stur            w0, [x1, #0x17]
    // 0xeb36c0: ldur            x2, [fp, #-0x18]
    // 0xeb36c4: StoreField: r1->field_7 = r2
    //     0xeb36c4: stur            w2, [x1, #7]
    // 0xeb36c8: ldur            x3, [fp, #-0x10]
    // 0xeb36cc: StoreField: r1->field_b = r3
    //     0xeb36cc: stur            x3, [x1, #0xb]
    // 0xeb36d0: mov             x0, x1
    // 0xeb36d4: LeaveFrame
    //     0xeb36d4: mov             SP, fp
    //     0xeb36d8: ldp             fp, lr, [SP], #0x10
    // 0xeb36dc: ret
    //     0xeb36dc: ret             
    // 0xeb36e0: ldur            x2, [fp, #-0x18]
    // 0xeb36e4: str             x0, [SP]
    // 0xeb36e8: mov             x1, x2
    // 0xeb36ec: mov             x2, x3
    // 0xeb36f0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeb36f0: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeb36f4: r0 = substring()
    //     0xeb36f4: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xeb36f8: r16 = <String>
    //     0xeb36f8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xeb36fc: ldur            lr, [fp, #-0x28]
    // 0xeb3700: stp             lr, x16, [SP, #0x10]
    // 0xeb3704: ldur            x16, [fp, #-0x30]
    // 0xeb3708: stp             x16, x0, [SP]
    // 0xeb370c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xeb370c: ldr             x4, [PP, #0x1190]  ; [pp+0x1190] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xeb3710: r0 = success()
    //     0xeb3710: bl              #0xeb1004  ; [package:petitparser/src/core/context.dart] Context::success
    // 0xeb3714: LeaveFrame
    //     0xeb3714: mov             SP, fp
    //     0xeb3718: ldp             fp, lr, [SP], #0x10
    // 0xeb371c: ret
    //     0xeb371c: ret             
    // 0xeb3720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb3720: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb3724: b               #0xeb35f0
  }
}
