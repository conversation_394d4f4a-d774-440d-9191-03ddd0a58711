// lib: , url: package:xml/src/xml/utils/name.dart

// class id: 1051315, size: 0x8
class :: {
}

// class id: 222, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlName&Object&XmlHasVisitor extends Object
     with XmlHasVisitor {
}

// class id: 223, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlName&Object&XmlHasVisitor&XmlHasWriter extends _XmlName&Object&XmlHasVisitor
     with XmlHasWriter {

  _ toString(/* No info */) {
    // ** addr: 0xc452e8, size: 0x30
    // 0xc452e8: EnterFrame
    //     0xc452e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc452ec: mov             fp, SP
    // 0xc452f0: CheckStackOverflow
    //     0xc452f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc452f4: cmp             SP, x16
    //     0xc452f8: b.ls            #0xc45310
    // 0xc452fc: ldr             x1, [fp, #0x10]
    // 0xc45300: r0 = toXmlString()
    //     0xc45300: bl              #0xc45318  ; [package:xml/src/xml/utils/name.dart] _XmlName&Object&XmlHasVisitor&XmlHasWriter::toXmlString
    // 0xc45304: LeaveFrame
    //     0xc45304: mov             SP, fp
    //     0xc45308: ldp             fp, lr, [SP], #0x10
    // 0xc4530c: ret
    //     0xc4530c: ret             
    // 0xc45310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc45310: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc45314: b               #0xc452fc
  }
  _ toXmlString(/* No info */) {
    // ** addr: 0xc45318, size: 0xa0
    // 0xc45318: EnterFrame
    //     0xc45318: stp             fp, lr, [SP, #-0x10]!
    //     0xc4531c: mov             fp, SP
    // 0xc45320: AllocStack(0x20)
    //     0xc45320: sub             SP, SP, #0x20
    // 0xc45324: SetupParameters(_XmlName&Object&XmlHasVisitor&XmlHasWriter this /* r1 => r2, fp-0x8 */)
    //     0xc45324: mov             x2, x1
    //     0xc45328: stur            x1, [fp, #-8]
    // 0xc4532c: CheckStackOverflow
    //     0xc4532c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc45330: cmp             SP, x16
    //     0xc45334: b.ls            #0xc453b0
    // 0xc45338: r0 = StringBuffer()
    //     0xc45338: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xc4533c: mov             x1, x0
    // 0xc45340: stur            x0, [fp, #-0x10]
    // 0xc45344: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc45344: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc45348: r0 = StringBuffer()
    //     0xc45348: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xc4534c: r0 = XmlWriter()
    //     0xc4534c: bl              #0xc448f4  ; AllocateXmlWriterStub -> XmlWriter (size=0x10)
    // 0xc45350: mov             x1, x0
    // 0xc45354: ldur            x0, [fp, #-0x10]
    // 0xc45358: stur            x1, [fp, #-0x18]
    // 0xc4535c: StoreField: r1->field_7 = r0
    //     0xc4535c: stur            w0, [x1, #7]
    // 0xc45360: r0 = InitLateStaticField(0xc04) // [package:xml/src/xml/entities/default_mapping.dart] ::defaultEntityMapping
    //     0xc45360: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc45364: ldr             x0, [x0, #0x1808]
    //     0xc45368: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc4536c: cmp             w0, w16
    //     0xc45370: b.ne            #0xc45380
    //     0xc45374: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cc8] Field <::.defaultEntityMapping>: static late (offset: 0xc04)
    //     0xc45378: ldr             x2, [x2, #0xcc8]
    //     0xc4537c: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc45380: ldur            x1, [fp, #-0x18]
    // 0xc45384: r0 = Instance_XmlDefaultEntityMapping
    //     0xc45384: add             x0, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xc45388: ldr             x0, [x0, #0x5c0]
    // 0xc4538c: StoreField: r1->field_b = r0
    //     0xc4538c: stur            w0, [x1, #0xb]
    // 0xc45390: ldur            x2, [fp, #-8]
    // 0xc45394: r0 = visitName()
    //     0xc45394: bl              #0xc453b8  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitName
    // 0xc45398: ldur            x16, [fp, #-0x10]
    // 0xc4539c: str             x16, [SP]
    // 0xc453a0: r0 = toString()
    //     0xc453a0: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xc453a4: LeaveFrame
    //     0xc453a4: mov             SP, fp
    //     0xc453a8: ldp             fp, lr, [SP], #0x10
    // 0xc453ac: ret
    //     0xc453ac: ret             
    // 0xc453b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc453b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc453b4: b               #0xc45338
  }
}

// class id: 224, size: 0xc, field offset: 0x8
//   transformed mixin,
abstract class _XmlName&Object&XmlHasVisitor&XmlHasWriter&XmlHasParent extends _XmlName&Object&XmlHasVisitor&XmlHasWriter
     with XmlHasParent<X0 bound XmlNode> {

  get _ parent(/* No info */) {
    // ** addr: 0xeb9048, size: 0xc
    // 0xeb9048: LoadField: r0 = r1->field_7
    //     0xeb9048: ldur            w0, [x1, #7]
    // 0xeb904c: DecompressPointer r0
    //     0xeb904c: add             x0, x0, HEAP, lsl #32
    // 0xeb9050: ret
    //     0xeb9050: ret             
  }
}

// class id: 225, size: 0xc, field offset: 0xc
abstract class XmlName extends _XmlName&Object&XmlHasVisitor&XmlHasWriter&XmlHasParent {

  _ accept(/* No info */) {
    // ** addr: 0xce1d40, size: 0x3c
    // 0xce1d40: EnterFrame
    //     0xce1d40: stp             fp, lr, [SP, #-0x10]!
    //     0xce1d44: mov             fp, SP
    // 0xce1d48: mov             x16, x2
    // 0xce1d4c: mov             x2, x1
    // 0xce1d50: mov             x1, x16
    // 0xce1d54: CheckStackOverflow
    //     0xce1d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1d58: cmp             SP, x16
    //     0xce1d5c: b.ls            #0xce1d74
    // 0xce1d60: r0 = visitName()
    //     0xce1d60: bl              #0xc453b8  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitName
    // 0xce1d64: r0 = Null
    //     0xce1d64: mov             x0, NULL
    // 0xce1d68: LeaveFrame
    //     0xce1d68: mov             SP, fp
    //     0xce1d6c: ldp             fp, lr, [SP], #0x10
    // 0xce1d70: ret
    //     0xce1d70: ret             
    // 0xce1d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1d74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1d78: b               #0xce1d60
  }
  factory _ XmlName(/* No info */) {
    // ** addr: 0xe76d98, size: 0x28
    // 0xe76d98: EnterFrame
    //     0xe76d98: stp             fp, lr, [SP, #-0x10]!
    //     0xe76d9c: mov             fp, SP
    // 0xe76da0: AllocStack(0x8)
    //     0xe76da0: sub             SP, SP, #8
    // 0xe76da4: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe76da4: stur            x2, [fp, #-8]
    // 0xe76da8: r0 = XmlSimpleName()
    //     0xe76da8: bl              #0xe76dc0  ; AllocateXmlSimpleNameStub -> XmlSimpleName (size=0x10)
    // 0xe76dac: ldur            x1, [fp, #-8]
    // 0xe76db0: StoreField: r0->field_b = r1
    //     0xe76db0: stur            w1, [x0, #0xb]
    // 0xe76db4: LeaveFrame
    //     0xe76db4: mov             SP, fp
    //     0xe76db8: ldp             fp, lr, [SP], #0x10
    // 0xe76dbc: ret
    //     0xe76dbc: ret             
  }
  factory _ XmlName.fromString(/* No info */) {
    // ** addr: 0xeba970, size: 0xf0
    // 0xeba970: EnterFrame
    //     0xeba970: stp             fp, lr, [SP, #-0x10]!
    //     0xeba974: mov             fp, SP
    // 0xeba978: AllocStack(0x28)
    //     0xeba978: sub             SP, SP, #0x28
    // 0xeba97c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0xeba97c: mov             x3, x2
    //     0xeba980: stur            x2, [fp, #-8]
    // 0xeba984: CheckStackOverflow
    //     0xeba984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba988: cmp             SP, x16
    //     0xeba98c: b.ls            #0xebaa58
    // 0xeba990: r0 = LoadClassIdInstr(r3)
    //     0xeba990: ldur            x0, [x3, #-1]
    //     0xeba994: ubfx            x0, x0, #0xc, #0x14
    // 0xeba998: mov             x1, x3
    // 0xeba99c: r2 = ":"
    //     0xeba99c: ldr             x2, [PP, #0x920]  ; [pp+0x920] ":"
    // 0xeba9a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xeba9a0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xeba9a4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xeba9a4: sub             lr, x0, #0xffa
    //     0xeba9a8: ldr             lr, [x21, lr, lsl #3]
    //     0xeba9ac: blr             lr
    // 0xeba9b0: mov             x3, x0
    // 0xeba9b4: stur            x3, [fp, #-0x10]
    // 0xeba9b8: cmp             x3, #0
    // 0xeba9bc: b.le            #0xebaa3c
    // 0xeba9c0: ldur            x4, [fp, #-8]
    // 0xeba9c4: r0 = BoxInt64Instr(r3)
    //     0xeba9c4: sbfiz           x0, x3, #1, #0x1f
    //     0xeba9c8: cmp             x3, x0, asr #1
    //     0xeba9cc: b.eq            #0xeba9d8
    //     0xeba9d0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeba9d4: stur            x3, [x0, #7]
    // 0xeba9d8: str             x0, [SP]
    // 0xeba9dc: mov             x1, x4
    // 0xeba9e0: r2 = 0
    //     0xeba9e0: movz            x2, #0
    // 0xeba9e4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xeba9e4: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xeba9e8: r0 = substring()
    //     0xeba9e8: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xeba9ec: mov             x3, x0
    // 0xeba9f0: ldur            x0, [fp, #-0x10]
    // 0xeba9f4: stur            x3, [fp, #-0x18]
    // 0xeba9f8: add             x2, x0, #1
    // 0xeba9fc: ldur            x1, [fp, #-8]
    // 0xebaa00: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xebaa00: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xebaa04: r0 = substring()
    //     0xebaa04: bl              #0x5ffb8c  ; [dart:core] _StringBase::substring
    // 0xebaa08: stur            x0, [fp, #-0x20]
    // 0xebaa0c: r0 = XmlPrefixName()
    //     0xebaa0c: bl              #0xeb7b90  ; AllocateXmlPrefixNameStub -> XmlPrefixName (size=0x18)
    // 0xebaa10: mov             x1, x0
    // 0xebaa14: ldur            x0, [fp, #-0x18]
    // 0xebaa18: StoreField: r1->field_b = r0
    //     0xebaa18: stur            w0, [x1, #0xb]
    // 0xebaa1c: ldur            x0, [fp, #-0x20]
    // 0xebaa20: StoreField: r1->field_f = r0
    //     0xebaa20: stur            w0, [x1, #0xf]
    // 0xebaa24: ldur            x0, [fp, #-8]
    // 0xebaa28: StoreField: r1->field_13 = r0
    //     0xebaa28: stur            w0, [x1, #0x13]
    // 0xebaa2c: mov             x0, x1
    // 0xebaa30: LeaveFrame
    //     0xebaa30: mov             SP, fp
    //     0xebaa34: ldp             fp, lr, [SP], #0x10
    // 0xebaa38: ret
    //     0xebaa38: ret             
    // 0xebaa3c: ldur            x0, [fp, #-8]
    // 0xebaa40: r0 = XmlSimpleName()
    //     0xebaa40: bl              #0xe76dc0  ; AllocateXmlSimpleNameStub -> XmlSimpleName (size=0x10)
    // 0xebaa44: ldur            x1, [fp, #-8]
    // 0xebaa48: StoreField: r0->field_b = r1
    //     0xebaa48: stur            w1, [x0, #0xb]
    // 0xebaa4c: LeaveFrame
    //     0xebaa4c: mov             SP, fp
    //     0xebaa50: ldp             fp, lr, [SP], #0x10
    // 0xebaa54: ret
    //     0xebaa54: ret             
    // 0xebaa58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebaa58: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebaa5c: b               #0xeba990
  }
}
