// lib: , url: package:xml/src/xml/utils/name_matcher.dart

// class id: 1051316, size: 0x8
class :: {

  static _ createNameMatcher(/* No info */) {
    // ** addr: 0xb14990, size: 0x128
    // 0xb14990: EnterFrame
    //     0xb14990: stp             fp, lr, [SP, #-0x10]!
    //     0xb14994: mov             fp, SP
    // 0xb14998: AllocStack(0x28)
    //     0xb14998: sub             SP, SP, #0x28
    // 0xb1499c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb1499c: stur            x1, [fp, #-8]
    //     0xb149a0: stur            x2, [fp, #-0x10]
    // 0xb149a4: CheckStackOverflow
    //     0xb149a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb149a8: cmp             SP, x16
    //     0xb149ac: b.ls            #0xb14ab0
    // 0xb149b0: r1 = 2
    //     0xb149b0: movz            x1, #0x2
    // 0xb149b4: r0 = AllocateContext()
    //     0xb149b4: bl              #0xec126c  ; AllocateContextStub
    // 0xb149b8: mov             x1, x0
    // 0xb149bc: ldur            x0, [fp, #-8]
    // 0xb149c0: stur            x1, [fp, #-0x18]
    // 0xb149c4: StoreField: r1->field_f = r0
    //     0xb149c4: stur            w0, [x1, #0xf]
    // 0xb149c8: ldur            x2, [fp, #-0x10]
    // 0xb149cc: StoreField: r1->field_13 = r2
    //     0xb149cc: stur            w2, [x1, #0x13]
    // 0xb149d0: r16 = "*"
    //     0xb149d0: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ad20] "*"
    //     0xb149d4: ldr             x16, [x16, #0xd20]
    // 0xb149d8: stp             x16, x0, [SP]
    // 0xb149dc: r0 = ==()
    //     0xb149dc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb149e0: tbnz            w0, #4, #0xb14a3c
    // 0xb149e4: ldur            x0, [fp, #-0x10]
    // 0xb149e8: cmp             w0, NULL
    // 0xb149ec: b.eq            #0xb14a04
    // 0xb149f0: r16 = "*"
    //     0xb149f0: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ad20] "*"
    //     0xb149f4: ldr             x16, [x16, #0xd20]
    // 0xb149f8: stp             x16, x0, [SP]
    // 0xb149fc: r0 = ==()
    //     0xb149fc: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb14a00: tbnz            w0, #4, #0xb14a20
    // 0xb14a04: r1 = Function '<anonymous closure>': static.
    //     0xb14a04: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e3c0] Function: [dart:core] Object::_simpleInstanceOfTrue (0xebbd8c)
    //     0xb14a08: ldr             x1, [x1, #0x3c0]
    // 0xb14a0c: r2 = Null
    //     0xb14a0c: mov             x2, NULL
    // 0xb14a10: r0 = AllocateClosure()
    //     0xb14a10: bl              #0xec1630  ; AllocateClosureStub
    // 0xb14a14: LeaveFrame
    //     0xb14a14: mov             SP, fp
    //     0xb14a18: ldp             fp, lr, [SP], #0x10
    // 0xb14a1c: ret
    //     0xb14a1c: ret             
    // 0xb14a20: ldur            x2, [fp, #-0x18]
    // 0xb14a24: r1 = Function '<anonymous closure>': static.
    //     0xb14a24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e3c8] AnonymousClosure: static (0xb14f58), in [package:xml/src/xml/utils/name_matcher.dart] ::createNameMatcher (0xb14990)
    //     0xb14a28: ldr             x1, [x1, #0x3c8]
    // 0xb14a2c: r0 = AllocateClosure()
    //     0xb14a2c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb14a30: LeaveFrame
    //     0xb14a30: mov             SP, fp
    //     0xb14a34: ldp             fp, lr, [SP], #0x10
    // 0xb14a38: ret
    //     0xb14a38: ret             
    // 0xb14a3c: ldur            x0, [fp, #-0x10]
    // 0xb14a40: cmp             w0, NULL
    // 0xb14a44: b.ne            #0xb14a64
    // 0xb14a48: ldur            x2, [fp, #-0x18]
    // 0xb14a4c: r1 = Function '<anonymous closure>': static.
    //     0xb14a4c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e3d0] AnonymousClosure: static (0xb14ebc), in [package:xml/src/xml/utils/name_matcher.dart] ::createNameMatcher (0xb14990)
    //     0xb14a50: ldr             x1, [x1, #0x3d0]
    // 0xb14a54: r0 = AllocateClosure()
    //     0xb14a54: bl              #0xec1630  ; AllocateClosureStub
    // 0xb14a58: LeaveFrame
    //     0xb14a58: mov             SP, fp
    //     0xb14a5c: ldp             fp, lr, [SP], #0x10
    // 0xb14a60: ret
    //     0xb14a60: ret             
    // 0xb14a64: r16 = "*"
    //     0xb14a64: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2ad20] "*"
    //     0xb14a68: ldr             x16, [x16, #0xd20]
    // 0xb14a6c: stp             x16, x0, [SP]
    // 0xb14a70: r0 = ==()
    //     0xb14a70: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0xb14a74: tbnz            w0, #4, #0xb14a94
    // 0xb14a78: ldur            x2, [fp, #-0x18]
    // 0xb14a7c: r1 = Function '<anonymous closure>': static.
    //     0xb14a7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e3d8] AnonymousClosure: static (0xb14e20), in [package:xml/src/xml/utils/name_matcher.dart] ::createNameMatcher (0xb14990)
    //     0xb14a80: ldr             x1, [x1, #0x3d8]
    // 0xb14a84: r0 = AllocateClosure()
    //     0xb14a84: bl              #0xec1630  ; AllocateClosureStub
    // 0xb14a88: LeaveFrame
    //     0xb14a88: mov             SP, fp
    //     0xb14a8c: ldp             fp, lr, [SP], #0x10
    // 0xb14a90: ret
    //     0xb14a90: ret             
    // 0xb14a94: ldur            x2, [fp, #-0x18]
    // 0xb14a98: r1 = Function '<anonymous closure>': static.
    //     0xb14a98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e3e0] AnonymousClosure: static (0xb14ab8), in [package:xml/src/xml/utils/name_matcher.dart] ::createNameMatcher (0xb14990)
    //     0xb14a9c: ldr             x1, [x1, #0x3e0]
    // 0xb14aa0: r0 = AllocateClosure()
    //     0xb14aa0: bl              #0xec1630  ; AllocateClosureStub
    // 0xb14aa4: LeaveFrame
    //     0xb14aa4: mov             SP, fp
    //     0xb14aa8: ldp             fp, lr, [SP], #0x10
    // 0xb14aac: ret
    //     0xb14aac: ret             
    // 0xb14ab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14ab0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14ab4: b               #0xb149b0
  }
  [closure] static bool <anonymous closure>(dynamic, XmlHasName) {
    // ** addr: 0xb14ab8, size: 0x17c
    // 0xb14ab8: EnterFrame
    //     0xb14ab8: stp             fp, lr, [SP, #-0x10]!
    //     0xb14abc: mov             fp, SP
    // 0xb14ac0: AllocStack(0x18)
    //     0xb14ac0: sub             SP, SP, #0x18
    // 0xb14ac4: SetupParameters()
    //     0xb14ac4: ldr             x0, [fp, #0x18]
    //     0xb14ac8: ldur            w2, [x0, #0x17]
    //     0xb14acc: add             x2, x2, HEAP, lsl #32
    //     0xb14ad0: stur            x2, [fp, #-8]
    // 0xb14ad4: CheckStackOverflow
    //     0xb14ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14ad8: cmp             SP, x16
    //     0xb14adc: b.ls            #0xb14c2c
    // 0xb14ae0: ldr             x3, [fp, #0x10]
    // 0xb14ae4: r0 = LoadClassIdInstr(r3)
    //     0xb14ae4: ldur            x0, [x3, #-1]
    //     0xb14ae8: ubfx            x0, x0, #0xc, #0x14
    // 0xb14aec: mov             x1, x3
    // 0xb14af0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb14af0: sub             lr, x0, #1, lsl #12
    //     0xb14af4: ldr             lr, [x21, lr, lsl #3]
    //     0xb14af8: blr             lr
    // 0xb14afc: r1 = LoadClassIdInstr(r0)
    //     0xb14afc: ldur            x1, [x0, #-1]
    //     0xb14b00: ubfx            x1, x1, #0xc, #0x14
    // 0xb14b04: cmp             x1, #0xe2
    // 0xb14b08: b.ne            #0xb14b1c
    // 0xb14b0c: LoadField: r1 = r0->field_b
    //     0xb14b0c: ldur            w1, [x0, #0xb]
    // 0xb14b10: DecompressPointer r1
    //     0xb14b10: add             x1, x1, HEAP, lsl #32
    // 0xb14b14: mov             x0, x1
    // 0xb14b18: b               #0xb14b28
    // 0xb14b1c: LoadField: r1 = r0->field_f
    //     0xb14b1c: ldur            w1, [x0, #0xf]
    // 0xb14b20: DecompressPointer r1
    //     0xb14b20: add             x1, x1, HEAP, lsl #32
    // 0xb14b24: mov             x0, x1
    // 0xb14b28: ldur            x1, [fp, #-8]
    // 0xb14b2c: LoadField: r2 = r1->field_f
    //     0xb14b2c: ldur            w2, [x1, #0xf]
    // 0xb14b30: DecompressPointer r2
    //     0xb14b30: add             x2, x2, HEAP, lsl #32
    // 0xb14b34: r3 = LoadClassIdInstr(r0)
    //     0xb14b34: ldur            x3, [x0, #-1]
    //     0xb14b38: ubfx            x3, x3, #0xc, #0x14
    // 0xb14b3c: stp             x2, x0, [SP]
    // 0xb14b40: mov             x0, x3
    // 0xb14b44: mov             lr, x0
    // 0xb14b48: ldr             lr, [x21, lr, lsl #3]
    // 0xb14b4c: blr             lr
    // 0xb14b50: tbnz            w0, #4, #0xb14c1c
    // 0xb14b54: ldr             x1, [fp, #0x10]
    // 0xb14b58: r0 = LoadClassIdInstr(r1)
    //     0xb14b58: ldur            x0, [x1, #-1]
    //     0xb14b5c: ubfx            x0, x0, #0xc, #0x14
    // 0xb14b60: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb14b60: sub             lr, x0, #1, lsl #12
    //     0xb14b64: ldr             lr, [x21, lr, lsl #3]
    //     0xb14b68: blr             lr
    // 0xb14b6c: r1 = LoadClassIdInstr(r0)
    //     0xb14b6c: ldur            x1, [x0, #-1]
    //     0xb14b70: ubfx            x1, x1, #0xc, #0x14
    // 0xb14b74: cmp             x1, #0xe2
    // 0xb14b78: b.ne            #0xb14bb8
    // 0xb14b7c: LoadField: r1 = r0->field_7
    //     0xb14b7c: ldur            w1, [x0, #7]
    // 0xb14b80: DecompressPointer r1
    //     0xb14b80: add             x1, x1, HEAP, lsl #32
    // 0xb14b84: r2 = Null
    //     0xb14b84: mov             x2, NULL
    // 0xb14b88: r3 = "xmlns"
    //     0xb14b88: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e3e8] "xmlns"
    //     0xb14b8c: ldr             x3, [x3, #0x3e8]
    // 0xb14b90: r0 = lookupAttribute()
    //     0xb14b90: bl              #0xb14c34  ; [package:xml/src/xml/utils/namespace.dart] ::lookupAttribute
    // 0xb14b94: cmp             w0, NULL
    // 0xb14b98: b.ne            #0xb14ba4
    // 0xb14b9c: r0 = Null
    //     0xb14b9c: mov             x0, NULL
    // 0xb14ba0: b               #0xb14bb0
    // 0xb14ba4: LoadField: r1 = r0->field_f
    //     0xb14ba4: ldur            w1, [x0, #0xf]
    // 0xb14ba8: DecompressPointer r1
    //     0xb14ba8: add             x1, x1, HEAP, lsl #32
    // 0xb14bac: mov             x0, x1
    // 0xb14bb0: mov             x1, x0
    // 0xb14bb4: b               #0xb14bf4
    // 0xb14bb8: LoadField: r1 = r0->field_7
    //     0xb14bb8: ldur            w1, [x0, #7]
    // 0xb14bbc: DecompressPointer r1
    //     0xb14bbc: add             x1, x1, HEAP, lsl #32
    // 0xb14bc0: LoadField: r3 = r0->field_b
    //     0xb14bc0: ldur            w3, [x0, #0xb]
    // 0xb14bc4: DecompressPointer r3
    //     0xb14bc4: add             x3, x3, HEAP, lsl #32
    // 0xb14bc8: r2 = "xmlns"
    //     0xb14bc8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e3e8] "xmlns"
    //     0xb14bcc: ldr             x2, [x2, #0x3e8]
    // 0xb14bd0: r0 = lookupAttribute()
    //     0xb14bd0: bl              #0xb14c34  ; [package:xml/src/xml/utils/namespace.dart] ::lookupAttribute
    // 0xb14bd4: cmp             w0, NULL
    // 0xb14bd8: b.ne            #0xb14be4
    // 0xb14bdc: r0 = Null
    //     0xb14bdc: mov             x0, NULL
    // 0xb14be0: b               #0xb14bf0
    // 0xb14be4: LoadField: r1 = r0->field_f
    //     0xb14be4: ldur            w1, [x0, #0xf]
    // 0xb14be8: DecompressPointer r1
    //     0xb14be8: add             x1, x1, HEAP, lsl #32
    // 0xb14bec: mov             x0, x1
    // 0xb14bf0: mov             x1, x0
    // 0xb14bf4: ldur            x0, [fp, #-8]
    // 0xb14bf8: LoadField: r2 = r0->field_13
    //     0xb14bf8: ldur            w2, [x0, #0x13]
    // 0xb14bfc: DecompressPointer r2
    //     0xb14bfc: add             x2, x2, HEAP, lsl #32
    // 0xb14c00: r0 = LoadClassIdInstr(r1)
    //     0xb14c00: ldur            x0, [x1, #-1]
    //     0xb14c04: ubfx            x0, x0, #0xc, #0x14
    // 0xb14c08: stp             x2, x1, [SP]
    // 0xb14c0c: mov             lr, x0
    // 0xb14c10: ldr             lr, [x21, lr, lsl #3]
    // 0xb14c14: blr             lr
    // 0xb14c18: b               #0xb14c20
    // 0xb14c1c: r0 = false
    //     0xb14c1c: add             x0, NULL, #0x30  ; false
    // 0xb14c20: LeaveFrame
    //     0xb14c20: mov             SP, fp
    //     0xb14c24: ldp             fp, lr, [SP], #0x10
    // 0xb14c28: ret
    //     0xb14c28: ret             
    // 0xb14c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14c2c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14c30: b               #0xb14ae0
  }
  [closure] static bool <anonymous closure>(dynamic, XmlHasName) {
    // ** addr: 0xb14e20, size: 0x9c
    // 0xb14e20: EnterFrame
    //     0xb14e20: stp             fp, lr, [SP, #-0x10]!
    //     0xb14e24: mov             fp, SP
    // 0xb14e28: AllocStack(0x18)
    //     0xb14e28: sub             SP, SP, #0x18
    // 0xb14e2c: SetupParameters()
    //     0xb14e2c: ldr             x0, [fp, #0x18]
    //     0xb14e30: ldur            w2, [x0, #0x17]
    //     0xb14e34: add             x2, x2, HEAP, lsl #32
    //     0xb14e38: stur            x2, [fp, #-8]
    // 0xb14e3c: CheckStackOverflow
    //     0xb14e3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14e40: cmp             SP, x16
    //     0xb14e44: b.ls            #0xb14eb4
    // 0xb14e48: ldr             x1, [fp, #0x10]
    // 0xb14e4c: r0 = LoadClassIdInstr(r1)
    //     0xb14e4c: ldur            x0, [x1, #-1]
    //     0xb14e50: ubfx            x0, x0, #0xc, #0x14
    // 0xb14e54: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb14e54: sub             lr, x0, #1, lsl #12
    //     0xb14e58: ldr             lr, [x21, lr, lsl #3]
    //     0xb14e5c: blr             lr
    // 0xb14e60: r1 = LoadClassIdInstr(r0)
    //     0xb14e60: ldur            x1, [x0, #-1]
    //     0xb14e64: ubfx            x1, x1, #0xc, #0x14
    // 0xb14e68: cmp             x1, #0xe2
    // 0xb14e6c: b.ne            #0xb14e7c
    // 0xb14e70: LoadField: r1 = r0->field_b
    //     0xb14e70: ldur            w1, [x0, #0xb]
    // 0xb14e74: DecompressPointer r1
    //     0xb14e74: add             x1, x1, HEAP, lsl #32
    // 0xb14e78: b               #0xb14e84
    // 0xb14e7c: LoadField: r1 = r0->field_f
    //     0xb14e7c: ldur            w1, [x0, #0xf]
    // 0xb14e80: DecompressPointer r1
    //     0xb14e80: add             x1, x1, HEAP, lsl #32
    // 0xb14e84: ldur            x0, [fp, #-8]
    // 0xb14e88: LoadField: r2 = r0->field_f
    //     0xb14e88: ldur            w2, [x0, #0xf]
    // 0xb14e8c: DecompressPointer r2
    //     0xb14e8c: add             x2, x2, HEAP, lsl #32
    // 0xb14e90: r0 = LoadClassIdInstr(r1)
    //     0xb14e90: ldur            x0, [x1, #-1]
    //     0xb14e94: ubfx            x0, x0, #0xc, #0x14
    // 0xb14e98: stp             x2, x1, [SP]
    // 0xb14e9c: mov             lr, x0
    // 0xb14ea0: ldr             lr, [x21, lr, lsl #3]
    // 0xb14ea4: blr             lr
    // 0xb14ea8: LeaveFrame
    //     0xb14ea8: mov             SP, fp
    //     0xb14eac: ldp             fp, lr, [SP], #0x10
    // 0xb14eb0: ret
    //     0xb14eb0: ret             
    // 0xb14eb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14eb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14eb8: b               #0xb14e48
  }
  [closure] static bool <anonymous closure>(dynamic, XmlHasName) {
    // ** addr: 0xb14ebc, size: 0x9c
    // 0xb14ebc: EnterFrame
    //     0xb14ebc: stp             fp, lr, [SP, #-0x10]!
    //     0xb14ec0: mov             fp, SP
    // 0xb14ec4: AllocStack(0x18)
    //     0xb14ec4: sub             SP, SP, #0x18
    // 0xb14ec8: SetupParameters()
    //     0xb14ec8: ldr             x0, [fp, #0x18]
    //     0xb14ecc: ldur            w2, [x0, #0x17]
    //     0xb14ed0: add             x2, x2, HEAP, lsl #32
    //     0xb14ed4: stur            x2, [fp, #-8]
    // 0xb14ed8: CheckStackOverflow
    //     0xb14ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14edc: cmp             SP, x16
    //     0xb14ee0: b.ls            #0xb14f50
    // 0xb14ee4: ldr             x1, [fp, #0x10]
    // 0xb14ee8: r0 = LoadClassIdInstr(r1)
    //     0xb14ee8: ldur            x0, [x1, #-1]
    //     0xb14eec: ubfx            x0, x0, #0xc, #0x14
    // 0xb14ef0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb14ef0: sub             lr, x0, #1, lsl #12
    //     0xb14ef4: ldr             lr, [x21, lr, lsl #3]
    //     0xb14ef8: blr             lr
    // 0xb14efc: r1 = LoadClassIdInstr(r0)
    //     0xb14efc: ldur            x1, [x0, #-1]
    //     0xb14f00: ubfx            x1, x1, #0xc, #0x14
    // 0xb14f04: cmp             x1, #0xe2
    // 0xb14f08: b.ne            #0xb14f18
    // 0xb14f0c: LoadField: r1 = r0->field_b
    //     0xb14f0c: ldur            w1, [x0, #0xb]
    // 0xb14f10: DecompressPointer r1
    //     0xb14f10: add             x1, x1, HEAP, lsl #32
    // 0xb14f14: b               #0xb14f20
    // 0xb14f18: LoadField: r1 = r0->field_13
    //     0xb14f18: ldur            w1, [x0, #0x13]
    // 0xb14f1c: DecompressPointer r1
    //     0xb14f1c: add             x1, x1, HEAP, lsl #32
    // 0xb14f20: ldur            x0, [fp, #-8]
    // 0xb14f24: LoadField: r2 = r0->field_f
    //     0xb14f24: ldur            w2, [x0, #0xf]
    // 0xb14f28: DecompressPointer r2
    //     0xb14f28: add             x2, x2, HEAP, lsl #32
    // 0xb14f2c: r0 = LoadClassIdInstr(r1)
    //     0xb14f2c: ldur            x0, [x1, #-1]
    //     0xb14f30: ubfx            x0, x0, #0xc, #0x14
    // 0xb14f34: stp             x2, x1, [SP]
    // 0xb14f38: mov             lr, x0
    // 0xb14f3c: ldr             lr, [x21, lr, lsl #3]
    // 0xb14f40: blr             lr
    // 0xb14f44: LeaveFrame
    //     0xb14f44: mov             SP, fp
    //     0xb14f48: ldp             fp, lr, [SP], #0x10
    // 0xb14f4c: ret
    //     0xb14f4c: ret             
    // 0xb14f50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14f50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14f54: b               #0xb14ee4
  }
  [closure] static bool <anonymous closure>(dynamic, XmlHasName) {
    // ** addr: 0xb14f58, size: 0x100
    // 0xb14f58: EnterFrame
    //     0xb14f58: stp             fp, lr, [SP, #-0x10]!
    //     0xb14f5c: mov             fp, SP
    // 0xb14f60: AllocStack(0x18)
    //     0xb14f60: sub             SP, SP, #0x18
    // 0xb14f64: SetupParameters()
    //     0xb14f64: ldr             x0, [fp, #0x18]
    //     0xb14f68: ldur            w2, [x0, #0x17]
    //     0xb14f6c: add             x2, x2, HEAP, lsl #32
    //     0xb14f70: stur            x2, [fp, #-8]
    // 0xb14f74: CheckStackOverflow
    //     0xb14f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14f78: cmp             SP, x16
    //     0xb14f7c: b.ls            #0xb15050
    // 0xb14f80: ldr             x1, [fp, #0x10]
    // 0xb14f84: r0 = LoadClassIdInstr(r1)
    //     0xb14f84: ldur            x0, [x1, #-1]
    //     0xb14f88: ubfx            x0, x0, #0xc, #0x14
    // 0xb14f8c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb14f8c: sub             lr, x0, #1, lsl #12
    //     0xb14f90: ldr             lr, [x21, lr, lsl #3]
    //     0xb14f94: blr             lr
    // 0xb14f98: r1 = LoadClassIdInstr(r0)
    //     0xb14f98: ldur            x1, [x0, #-1]
    //     0xb14f9c: ubfx            x1, x1, #0xc, #0x14
    // 0xb14fa0: cmp             x1, #0xe2
    // 0xb14fa4: b.ne            #0xb14fe4
    // 0xb14fa8: LoadField: r1 = r0->field_7
    //     0xb14fa8: ldur            w1, [x0, #7]
    // 0xb14fac: DecompressPointer r1
    //     0xb14fac: add             x1, x1, HEAP, lsl #32
    // 0xb14fb0: r2 = Null
    //     0xb14fb0: mov             x2, NULL
    // 0xb14fb4: r3 = "xmlns"
    //     0xb14fb4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e3e8] "xmlns"
    //     0xb14fb8: ldr             x3, [x3, #0x3e8]
    // 0xb14fbc: r0 = lookupAttribute()
    //     0xb14fbc: bl              #0xb14c34  ; [package:xml/src/xml/utils/namespace.dart] ::lookupAttribute
    // 0xb14fc0: cmp             w0, NULL
    // 0xb14fc4: b.ne            #0xb14fd0
    // 0xb14fc8: r0 = Null
    //     0xb14fc8: mov             x0, NULL
    // 0xb14fcc: b               #0xb14fdc
    // 0xb14fd0: LoadField: r1 = r0->field_f
    //     0xb14fd0: ldur            w1, [x0, #0xf]
    // 0xb14fd4: DecompressPointer r1
    //     0xb14fd4: add             x1, x1, HEAP, lsl #32
    // 0xb14fd8: mov             x0, x1
    // 0xb14fdc: mov             x1, x0
    // 0xb14fe0: b               #0xb15020
    // 0xb14fe4: LoadField: r1 = r0->field_7
    //     0xb14fe4: ldur            w1, [x0, #7]
    // 0xb14fe8: DecompressPointer r1
    //     0xb14fe8: add             x1, x1, HEAP, lsl #32
    // 0xb14fec: LoadField: r3 = r0->field_b
    //     0xb14fec: ldur            w3, [x0, #0xb]
    // 0xb14ff0: DecompressPointer r3
    //     0xb14ff0: add             x3, x3, HEAP, lsl #32
    // 0xb14ff4: r2 = "xmlns"
    //     0xb14ff4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e3e8] "xmlns"
    //     0xb14ff8: ldr             x2, [x2, #0x3e8]
    // 0xb14ffc: r0 = lookupAttribute()
    //     0xb14ffc: bl              #0xb14c34  ; [package:xml/src/xml/utils/namespace.dart] ::lookupAttribute
    // 0xb15000: cmp             w0, NULL
    // 0xb15004: b.ne            #0xb15010
    // 0xb15008: r0 = Null
    //     0xb15008: mov             x0, NULL
    // 0xb1500c: b               #0xb1501c
    // 0xb15010: LoadField: r1 = r0->field_f
    //     0xb15010: ldur            w1, [x0, #0xf]
    // 0xb15014: DecompressPointer r1
    //     0xb15014: add             x1, x1, HEAP, lsl #32
    // 0xb15018: mov             x0, x1
    // 0xb1501c: mov             x1, x0
    // 0xb15020: ldur            x0, [fp, #-8]
    // 0xb15024: LoadField: r2 = r0->field_13
    //     0xb15024: ldur            w2, [x0, #0x13]
    // 0xb15028: DecompressPointer r2
    //     0xb15028: add             x2, x2, HEAP, lsl #32
    // 0xb1502c: r0 = LoadClassIdInstr(r1)
    //     0xb1502c: ldur            x0, [x1, #-1]
    //     0xb15030: ubfx            x0, x0, #0xc, #0x14
    // 0xb15034: stp             x2, x1, [SP]
    // 0xb15038: mov             lr, x0
    // 0xb1503c: ldr             lr, [x21, lr, lsl #3]
    // 0xb15040: blr             lr
    // 0xb15044: LeaveFrame
    //     0xb15044: mov             SP, fp
    //     0xb15048: ldp             fp, lr, [SP], #0x10
    // 0xb1504c: ret
    //     0xb1504c: ret             
    // 0xb15050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb15050: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15054: b               #0xb14f80
  }
}
