// lib: , url: package:xml/src/xml/utils/namespace.dart

// class id: 1051317, size: 0x8
class :: {

  static _ lookupAttribute(/* No info */) {
    // ** addr: 0xb14c34, size: 0x1ec
    // 0xb14c34: EnterFrame
    //     0xb14c34: stp             fp, lr, [SP, #-0x10]!
    //     0xb14c38: mov             fp, SP
    // 0xb14c3c: AllocStack(0x48)
    //     0xb14c3c: sub             SP, SP, #0x48
    // 0xb14c40: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb14c40: stur            x2, [fp, #-0x10]
    //     0xb14c44: stur            x3, [fp, #-0x18]
    // 0xb14c48: CheckStackOverflow
    //     0xb14c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14c4c: cmp             SP, x16
    //     0xb14c50: b.ls            #0xb14e08
    // 0xb14c54: mov             x4, x1
    // 0xb14c58: stur            x4, [fp, #-8]
    // 0xb14c5c: CheckStackOverflow
    //     0xb14c5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14c60: cmp             SP, x16
    //     0xb14c64: b.ls            #0xb14e10
    // 0xb14c68: cmp             w4, NULL
    // 0xb14c6c: b.eq            #0xb14df8
    // 0xb14c70: r0 = LoadClassIdInstr(r4)
    //     0xb14c70: ldur            x0, [x4, #-1]
    //     0xb14c74: ubfx            x0, x0, #0xc, #0x14
    // 0xb14c78: mov             x1, x4
    // 0xb14c7c: r0 = GDT[cid_x0 + -0xf3a]()
    //     0xb14c7c: sub             lr, x0, #0xf3a
    //     0xb14c80: ldr             lr, [x21, lr, lsl #3]
    //     0xb14c84: blr             lr
    // 0xb14c88: r1 = LoadClassIdInstr(r0)
    //     0xb14c88: ldur            x1, [x0, #-1]
    //     0xb14c8c: ubfx            x1, x1, #0xc, #0x14
    // 0xb14c90: mov             x16, x0
    // 0xb14c94: mov             x0, x1
    // 0xb14c98: mov             x1, x16
    // 0xb14c9c: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xb14c9c: movz            x17, #0xd35d
    //     0xb14ca0: add             lr, x0, x17
    //     0xb14ca4: ldr             lr, [x21, lr, lsl #3]
    //     0xb14ca8: blr             lr
    // 0xb14cac: mov             x2, x0
    // 0xb14cb0: stur            x2, [fp, #-0x20]
    // 0xb14cb4: CheckStackOverflow
    //     0xb14cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb14cb8: cmp             SP, x16
    //     0xb14cbc: b.ls            #0xb14e18
    // 0xb14cc0: r0 = LoadClassIdInstr(r2)
    //     0xb14cc0: ldur            x0, [x2, #-1]
    //     0xb14cc4: ubfx            x0, x0, #0xc, #0x14
    // 0xb14cc8: mov             x1, x2
    // 0xb14ccc: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xb14ccc: movz            x17, #0x292d
    //     0xb14cd0: movk            x17, #0x1, lsl #16
    //     0xb14cd4: add             lr, x0, x17
    //     0xb14cd8: ldr             lr, [x21, lr, lsl #3]
    //     0xb14cdc: blr             lr
    // 0xb14ce0: tbnz            w0, #4, #0xb14dd0
    // 0xb14ce4: ldur            x2, [fp, #-0x20]
    // 0xb14ce8: r0 = LoadClassIdInstr(r2)
    //     0xb14ce8: ldur            x0, [x2, #-1]
    //     0xb14cec: ubfx            x0, x0, #0xc, #0x14
    // 0xb14cf0: mov             x1, x2
    // 0xb14cf4: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xb14cf4: movz            x17, #0x384d
    //     0xb14cf8: movk            x17, #0x1, lsl #16
    //     0xb14cfc: add             lr, x0, x17
    //     0xb14d00: ldr             lr, [x21, lr, lsl #3]
    //     0xb14d04: blr             lr
    // 0xb14d08: mov             x1, x0
    // 0xb14d0c: stur            x1, [fp, #-0x38]
    // 0xb14d10: LoadField: r2 = r1->field_b
    //     0xb14d10: ldur            w2, [x1, #0xb]
    // 0xb14d14: DecompressPointer r2
    //     0xb14d14: add             x2, x2, HEAP, lsl #32
    // 0xb14d18: stur            x2, [fp, #-0x30]
    // 0xb14d1c: r3 = LoadClassIdInstr(r2)
    //     0xb14d1c: ldur            x3, [x2, #-1]
    //     0xb14d20: ubfx            x3, x3, #0xc, #0x14
    // 0xb14d24: stur            x3, [fp, #-0x28]
    // 0xb14d28: cmp             x3, #0xe2
    // 0xb14d2c: b.ne            #0xb14d38
    // 0xb14d30: r0 = Null
    //     0xb14d30: mov             x0, NULL
    // 0xb14d34: b               #0xb14d40
    // 0xb14d38: LoadField: r0 = r2->field_b
    //     0xb14d38: ldur            w0, [x2, #0xb]
    // 0xb14d3c: DecompressPointer r0
    //     0xb14d3c: add             x0, x0, HEAP, lsl #32
    // 0xb14d40: r4 = LoadClassIdInstr(r0)
    //     0xb14d40: ldur            x4, [x0, #-1]
    //     0xb14d44: ubfx            x4, x4, #0xc, #0x14
    // 0xb14d48: ldur            x16, [fp, #-0x10]
    // 0xb14d4c: stp             x16, x0, [SP]
    // 0xb14d50: mov             x0, x4
    // 0xb14d54: mov             lr, x0
    // 0xb14d58: ldr             lr, [x21, lr, lsl #3]
    // 0xb14d5c: blr             lr
    // 0xb14d60: tbnz            w0, #4, #0xb14dc8
    // 0xb14d64: ldur            x0, [fp, #-0x28]
    // 0xb14d68: cmp             x0, #0xe2
    // 0xb14d6c: b.ne            #0xb14d84
    // 0xb14d70: ldur            x0, [fp, #-0x30]
    // 0xb14d74: LoadField: r1 = r0->field_b
    //     0xb14d74: ldur            w1, [x0, #0xb]
    // 0xb14d78: DecompressPointer r1
    //     0xb14d78: add             x1, x1, HEAP, lsl #32
    // 0xb14d7c: mov             x0, x1
    // 0xb14d80: b               #0xb14d94
    // 0xb14d84: ldur            x0, [fp, #-0x30]
    // 0xb14d88: LoadField: r1 = r0->field_f
    //     0xb14d88: ldur            w1, [x0, #0xf]
    // 0xb14d8c: DecompressPointer r1
    //     0xb14d8c: add             x1, x1, HEAP, lsl #32
    // 0xb14d90: mov             x0, x1
    // 0xb14d94: r1 = LoadClassIdInstr(r0)
    //     0xb14d94: ldur            x1, [x0, #-1]
    //     0xb14d98: ubfx            x1, x1, #0xc, #0x14
    // 0xb14d9c: ldur            x16, [fp, #-0x18]
    // 0xb14da0: stp             x16, x0, [SP]
    // 0xb14da4: mov             x0, x1
    // 0xb14da8: mov             lr, x0
    // 0xb14dac: ldr             lr, [x21, lr, lsl #3]
    // 0xb14db0: blr             lr
    // 0xb14db4: tbnz            w0, #4, #0xb14dc8
    // 0xb14db8: ldur            x0, [fp, #-0x38]
    // 0xb14dbc: LeaveFrame
    //     0xb14dbc: mov             SP, fp
    //     0xb14dc0: ldp             fp, lr, [SP], #0x10
    // 0xb14dc4: ret
    //     0xb14dc4: ret             
    // 0xb14dc8: ldur            x2, [fp, #-0x20]
    // 0xb14dcc: b               #0xb14cb4
    // 0xb14dd0: ldur            x1, [fp, #-8]
    // 0xb14dd4: r0 = LoadClassIdInstr(r1)
    //     0xb14dd4: ldur            x0, [x1, #-1]
    //     0xb14dd8: ubfx            x0, x0, #0xc, #0x14
    // 0xb14ddc: r0 = GDT[cid_x0 + -0xf4e]()
    //     0xb14ddc: sub             lr, x0, #0xf4e
    //     0xb14de0: ldr             lr, [x21, lr, lsl #3]
    //     0xb14de4: blr             lr
    // 0xb14de8: mov             x4, x0
    // 0xb14dec: ldur            x2, [fp, #-0x10]
    // 0xb14df0: ldur            x3, [fp, #-0x18]
    // 0xb14df4: b               #0xb14c58
    // 0xb14df8: r0 = Null
    //     0xb14df8: mov             x0, NULL
    // 0xb14dfc: LeaveFrame
    //     0xb14dfc: mov             SP, fp
    //     0xb14e00: ldp             fp, lr, [SP], #0x10
    // 0xb14e04: ret
    //     0xb14e04: ret             
    // 0xb14e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14e08: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14e0c: b               #0xb14c54
    // 0xb14e10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14e10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14e14: b               #0xb14c68
    // 0xb14e18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb14e18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb14e1c: b               #0xb14cc0
  }
}
