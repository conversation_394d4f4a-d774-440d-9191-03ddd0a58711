// lib: , url: package:xml/src/xml/utils/node_list.dart

// class id: 1051318, size: 0x8
class :: {
}

// class id: 5797, size: 0x18, field offset: 0x10
class XmlNodeList<X0 bound XmlNode> extends DelegatingList<X0 bound XmlNode> {

  late final XmlNode _parent; // offset: 0x10
  late final Set<XmlNodeType> _nodeTypes; // offset: 0x14

  _ fillRange(/* No info */) {
    // ** addr: 0x66e4f8, size: 0x68
    // 0x66e4f8: EnterFrame
    //     0x66e4f8: stp             fp, lr, [SP, #-0x10]!
    //     0x66e4fc: mov             fp, SP
    // 0x66e500: mov             x0, x5
    // 0x66e504: LoadField: r2 = r1->field_7
    //     0x66e504: ldur            w2, [x1, #7]
    // 0x66e508: DecompressPointer r2
    //     0x66e508: add             x2, x2, HEAP, lsl #32
    // 0x66e50c: r1 = Null
    //     0x66e50c: mov             x1, NULL
    // 0x66e510: cmp             w0, NULL
    // 0x66e514: b.eq            #0x66e540
    // 0x66e518: cmp             w2, NULL
    // 0x66e51c: b.eq            #0x66e540
    // 0x66e520: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66e520: ldur            w4, [x2, #0x17]
    // 0x66e524: DecompressPointer r4
    //     0x66e524: add             x4, x4, HEAP, lsl #32
    // 0x66e528: r8 = X0? bound XmlNode
    //     0x66e528: add             x8, PP, #0x31, lsl #12  ; [pp+0x31110] TypeParameter: X0? bound XmlNode
    //     0x66e52c: ldr             x8, [x8, #0x110]
    // 0x66e530: LoadField: r9 = r4->field_7
    //     0x66e530: ldur            x9, [x4, #7]
    // 0x66e534: r3 = Null
    //     0x66e534: add             x3, PP, #0x31, lsl #12  ; [pp+0x31118] Null
    //     0x66e538: ldr             x3, [x3, #0x118]
    // 0x66e53c: blr             x9
    // 0x66e540: r0 = UnsupportedError()
    //     0x66e540: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x66e544: mov             x1, x0
    // 0x66e548: r0 = "Unsupported range filling of node list"
    //     0x66e548: add             x0, PP, #0x31, lsl #12  ; [pp+0x31128] "Unsupported range filling of node list"
    //     0x66e54c: ldr             x0, [x0, #0x128]
    // 0x66e550: StoreField: r1->field_b = r0
    //     0x66e550: stur            w0, [x1, #0xb]
    // 0x66e554: mov             x0, x1
    // 0x66e558: r0 = Throw()
    //     0x66e558: bl              #0xec04b8  ; ThrowStub
    // 0x66e55c: brk             #0
  }
  _ setRange(/* No info */) {
    // ** addr: 0x66e988, size: 0x27c
    // 0x66e988: EnterFrame
    //     0x66e988: stp             fp, lr, [SP, #-0x10]!
    //     0x66e98c: mov             fp, SP
    // 0x66e990: AllocStack(0x40)
    //     0x66e990: sub             SP, SP, #0x40
    // 0x66e994: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r7, fp-0x10 */, dynamic _ /* r2 => r6, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */, dynamic _ /* r5 => r3, fp-0x28 */, [int _ = 0 /* r4, fp-0x8 */])
    //     0x66e994: mov             x7, x1
    //     0x66e998: mov             x6, x2
    //     0x66e99c: stur            x3, [fp, #-0x20]
    //     0x66e9a0: mov             x16, x5
    //     0x66e9a4: mov             x5, x3
    //     0x66e9a8: mov             x3, x16
    //     0x66e9ac: stur            x1, [fp, #-0x10]
    //     0x66e9b0: stur            x2, [fp, #-0x18]
    //     0x66e9b4: stur            x3, [fp, #-0x28]
    //     0x66e9b8: ldur            w0, [x4, #0x13]
    //     0x66e9bc: sub             x1, x0, #8
    //     0x66e9c0: cmp             w1, #2
    //     0x66e9c4: b.lt            #0x66e9e4
    //     0x66e9c8: add             x0, fp, w1, sxtw #2
    //     0x66e9cc: ldr             x0, [x0, #8]
    //     0x66e9d0: sbfx            x1, x0, #1, #0x1f
    //     0x66e9d4: tbz             w0, #0, #0x66e9dc
    //     0x66e9d8: ldur            x1, [x0, #7]
    //     0x66e9dc: mov             x4, x1
    //     0x66e9e0: b               #0x66e9e8
    //     0x66e9e4: movz            x4, #0
    //     0x66e9e8: stur            x4, [fp, #-8]
    // 0x66e9ec: CheckStackOverflow
    //     0x66e9ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66e9f0: cmp             SP, x16
    //     0x66e9f4: b.ls            #0x66ebcc
    // 0x66e9f8: LoadField: r2 = r7->field_7
    //     0x66e9f8: ldur            w2, [x7, #7]
    // 0x66e9fc: DecompressPointer r2
    //     0x66e9fc: add             x2, x2, HEAP, lsl #32
    // 0x66ea00: mov             x0, x3
    // 0x66ea04: r1 = Null
    //     0x66ea04: mov             x1, NULL
    // 0x66ea08: r8 = Iterable<X0 bound XmlNode>
    //     0x66ea08: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e428] Type: Iterable<X0 bound XmlNode>
    //     0x66ea0c: ldr             x8, [x8, #0x428]
    // 0x66ea10: LoadField: r9 = r8->field_7
    //     0x66ea10: ldur            x9, [x8, #7]
    // 0x66ea14: r3 = Null
    //     0x66ea14: add             x3, PP, #0x31, lsl #12  ; [pp+0x31100] Null
    //     0x66ea18: ldr             x3, [x3, #0x100]
    // 0x66ea1c: blr             x9
    // 0x66ea20: ldur            x4, [fp, #-0x10]
    // 0x66ea24: LoadField: r5 = r4->field_b
    //     0x66ea24: ldur            w5, [x4, #0xb]
    // 0x66ea28: DecompressPointer r5
    //     0x66ea28: add             x5, x5, HEAP, lsl #32
    // 0x66ea2c: stur            x5, [fp, #-0x30]
    // 0x66ea30: LoadField: r2 = r5->field_b
    //     0x66ea30: ldur            w2, [x5, #0xb]
    // 0x66ea34: ldur            x6, [fp, #-0x20]
    // 0x66ea38: r0 = BoxInt64Instr(r6)
    //     0x66ea38: sbfiz           x0, x6, #1, #0x1f
    //     0x66ea3c: cmp             x6, x0, asr #1
    //     0x66ea40: b.eq            #0x66ea4c
    //     0x66ea44: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66ea48: stur            x6, [x0, #7]
    // 0x66ea4c: r3 = LoadInt32Instr(r2)
    //     0x66ea4c: sbfx            x3, x2, #1, #0x1f
    // 0x66ea50: ldur            x1, [fp, #-0x18]
    // 0x66ea54: mov             x2, x0
    // 0x66ea58: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x66ea58: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x66ea5c: r0 = checkValidRange()
    //     0x66ea5c: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x66ea60: ldur            x1, [fp, #-0x10]
    // 0x66ea64: ldur            x2, [fp, #-0x28]
    // 0x66ea68: r0 = _expandNodes()
    //     0x66ea68: bl              #0x66ec8c  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::_expandNodes
    // 0x66ea6c: mov             x3, x0
    // 0x66ea70: stur            x3, [fp, #-0x28]
    // 0x66ea74: ldur            x7, [fp, #-0x18]
    // 0x66ea78: ldur            x4, [fp, #-0x10]
    // 0x66ea7c: ldur            x6, [fp, #-0x20]
    // 0x66ea80: ldur            x5, [fp, #-0x30]
    // 0x66ea84: stur            x7, [fp, #-0x38]
    // 0x66ea88: CheckStackOverflow
    //     0x66ea88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66ea8c: cmp             SP, x16
    //     0x66ea90: b.ls            #0x66ebd4
    // 0x66ea94: cmp             x7, x6
    // 0x66ea98: b.ge            #0x66eb00
    // 0x66ea9c: LoadField: r0 = r5->field_b
    //     0x66ea9c: ldur            w0, [x5, #0xb]
    // 0x66eaa0: r1 = LoadInt32Instr(r0)
    //     0x66eaa0: sbfx            x1, x0, #1, #0x1f
    // 0x66eaa4: mov             x0, x1
    // 0x66eaa8: mov             x1, x7
    // 0x66eaac: cmp             x1, x0
    // 0x66eab0: b.hs            #0x66ebdc
    // 0x66eab4: LoadField: r0 = r5->field_f
    //     0x66eab4: ldur            w0, [x5, #0xf]
    // 0x66eab8: DecompressPointer r0
    //     0x66eab8: add             x0, x0, HEAP, lsl #32
    // 0x66eabc: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x66eabc: add             x16, x0, x7, lsl #2
    //     0x66eac0: ldur            w1, [x16, #0xf]
    // 0x66eac4: DecompressPointer r1
    //     0x66eac4: add             x1, x1, HEAP, lsl #32
    // 0x66eac8: LoadField: r2 = r4->field_f
    //     0x66eac8: ldur            w2, [x4, #0xf]
    // 0x66eacc: DecompressPointer r2
    //     0x66eacc: add             x2, x2, HEAP, lsl #32
    // 0x66ead0: r16 = Sentinel
    //     0x66ead0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x66ead4: cmp             w2, w16
    // 0x66ead8: b.eq            #0x66ebe0
    // 0x66eadc: r0 = LoadClassIdInstr(r1)
    //     0x66eadc: ldur            x0, [x1, #-1]
    //     0x66eae0: ubfx            x0, x0, #0xc, #0x14
    // 0x66eae4: r0 = GDT[cid_x0 + -0xfa3]()
    //     0x66eae4: sub             lr, x0, #0xfa3
    //     0x66eae8: ldr             lr, [x21, lr, lsl #3]
    //     0x66eaec: blr             lr
    // 0x66eaf0: ldur            x0, [fp, #-0x38]
    // 0x66eaf4: add             x7, x0, #1
    // 0x66eaf8: ldur            x3, [fp, #-0x28]
    // 0x66eafc: b               #0x66ea78
    // 0x66eb00: ldur            x2, [fp, #-8]
    // 0x66eb04: r0 = BoxInt64Instr(r2)
    //     0x66eb04: sbfiz           x0, x2, #1, #0x1f
    //     0x66eb08: cmp             x2, x0, asr #1
    //     0x66eb0c: b.eq            #0x66eb18
    //     0x66eb10: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x66eb14: stur            x2, [x0, #7]
    // 0x66eb18: str             x0, [SP]
    // 0x66eb1c: ldur            x1, [fp, #-0x10]
    // 0x66eb20: ldur            x2, [fp, #-0x18]
    // 0x66eb24: ldur            x3, [fp, #-0x20]
    // 0x66eb28: ldur            x5, [fp, #-0x28]
    // 0x66eb2c: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0x66eb2c: ldr             x4, [PP, #0x718]  ; [pp+0x718] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0x66eb30: r0 = setRange()
    //     0x66eb30: bl              #0x66ec04  ; [package:collection/src/wrappers.dart] DelegatingList::setRange
    // 0x66eb34: ldur            x6, [fp, #-0x18]
    // 0x66eb38: ldur            x3, [fp, #-0x10]
    // 0x66eb3c: ldur            x5, [fp, #-0x20]
    // 0x66eb40: ldur            x4, [fp, #-0x30]
    // 0x66eb44: stur            x6, [fp, #-8]
    // 0x66eb48: CheckStackOverflow
    //     0x66eb48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66eb4c: cmp             SP, x16
    //     0x66eb50: b.ls            #0x66ebec
    // 0x66eb54: cmp             x6, x5
    // 0x66eb58: b.ge            #0x66ebbc
    // 0x66eb5c: LoadField: r0 = r4->field_b
    //     0x66eb5c: ldur            w0, [x4, #0xb]
    // 0x66eb60: r1 = LoadInt32Instr(r0)
    //     0x66eb60: sbfx            x1, x0, #1, #0x1f
    // 0x66eb64: mov             x0, x1
    // 0x66eb68: mov             x1, x6
    // 0x66eb6c: cmp             x1, x0
    // 0x66eb70: b.hs            #0x66ebf4
    // 0x66eb74: LoadField: r0 = r4->field_f
    //     0x66eb74: ldur            w0, [x4, #0xf]
    // 0x66eb78: DecompressPointer r0
    //     0x66eb78: add             x0, x0, HEAP, lsl #32
    // 0x66eb7c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x66eb7c: add             x16, x0, x6, lsl #2
    //     0x66eb80: ldur            w1, [x16, #0xf]
    // 0x66eb84: DecompressPointer r1
    //     0x66eb84: add             x1, x1, HEAP, lsl #32
    // 0x66eb88: LoadField: r2 = r3->field_f
    //     0x66eb88: ldur            w2, [x3, #0xf]
    // 0x66eb8c: DecompressPointer r2
    //     0x66eb8c: add             x2, x2, HEAP, lsl #32
    // 0x66eb90: r16 = Sentinel
    //     0x66eb90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x66eb94: cmp             w2, w16
    // 0x66eb98: b.eq            #0x66ebf8
    // 0x66eb9c: r0 = LoadClassIdInstr(r1)
    //     0x66eb9c: ldur            x0, [x1, #-1]
    //     0x66eba0: ubfx            x0, x0, #0xc, #0x14
    // 0x66eba4: r0 = GDT[cid_x0 + -0xf83]()
    //     0x66eba4: sub             lr, x0, #0xf83
    //     0x66eba8: ldr             lr, [x21, lr, lsl #3]
    //     0x66ebac: blr             lr
    // 0x66ebb0: ldur            x1, [fp, #-8]
    // 0x66ebb4: add             x6, x1, #1
    // 0x66ebb8: b               #0x66eb38
    // 0x66ebbc: r0 = Null
    //     0x66ebbc: mov             x0, NULL
    // 0x66ebc0: LeaveFrame
    //     0x66ebc0: mov             SP, fp
    //     0x66ebc4: ldp             fp, lr, [SP], #0x10
    // 0x66ebc8: ret
    //     0x66ebc8: ret             
    // 0x66ebcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66ebcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66ebd0: b               #0x66e9f8
    // 0x66ebd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66ebd4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66ebd8: b               #0x66ea94
    // 0x66ebdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66ebdc: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x66ebe0: r9 = _parent
    //     0x66ebe0: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x66ebe4: ldr             x9, [x9, #0x450]
    // 0x66ebe8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x66ebe8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x66ebec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66ebec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66ebf0: b               #0x66eb54
    // 0x66ebf4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66ebf4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x66ebf8: r9 = _parent
    //     0x66ebf8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x66ebfc: ldr             x9, [x9, #0x450]
    // 0x66ec00: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x66ec00: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _expandNodes(/* No info */) {
    // ** addr: 0x66ec8c, size: 0x408
    // 0x66ec8c: EnterFrame
    //     0x66ec8c: stp             fp, lr, [SP, #-0x10]!
    //     0x66ec90: mov             fp, SP
    // 0x66ec94: AllocStack(0x50)
    //     0x66ec94: sub             SP, SP, #0x50
    // 0x66ec98: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x66ec98: mov             x3, x1
    //     0x66ec9c: mov             x0, x2
    //     0x66eca0: stur            x1, [fp, #-0x10]
    //     0x66eca4: stur            x2, [fp, #-0x18]
    // 0x66eca8: CheckStackOverflow
    //     0x66eca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66ecac: cmp             SP, x16
    //     0x66ecb0: b.ls            #0x66f078
    // 0x66ecb4: LoadField: r4 = r3->field_7
    //     0x66ecb4: ldur            w4, [x3, #7]
    // 0x66ecb8: DecompressPointer r4
    //     0x66ecb8: add             x4, x4, HEAP, lsl #32
    // 0x66ecbc: mov             x1, x4
    // 0x66ecc0: stur            x4, [fp, #-8]
    // 0x66ecc4: r2 = 0
    //     0x66ecc4: movz            x2, #0
    // 0x66ecc8: r0 = _GrowableList()
    //     0x66ecc8: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x66eccc: mov             x2, x0
    // 0x66ecd0: ldur            x1, [fp, #-0x18]
    // 0x66ecd4: stur            x2, [fp, #-0x20]
    // 0x66ecd8: r0 = LoadClassIdInstr(r1)
    //     0x66ecd8: ldur            x0, [x1, #-1]
    //     0x66ecdc: ubfx            x0, x0, #0xc, #0x14
    // 0x66ece0: r0 = GDT[cid_x0 + 0xd35d]()
    //     0x66ece0: movz            x17, #0xd35d
    //     0x66ece4: add             lr, x0, x17
    //     0x66ece8: ldr             lr, [x21, lr, lsl #3]
    //     0x66ecec: blr             lr
    // 0x66ecf0: mov             x2, x0
    // 0x66ecf4: stur            x2, [fp, #-0x18]
    // 0x66ecf8: ldur            x3, [fp, #-0x20]
    // 0x66ecfc: ldur            x4, [fp, #-0x10]
    // 0x66ed00: CheckStackOverflow
    //     0x66ed00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66ed04: cmp             SP, x16
    //     0x66ed08: b.ls            #0x66f080
    // 0x66ed0c: r0 = LoadClassIdInstr(r2)
    //     0x66ed0c: ldur            x0, [x2, #-1]
    //     0x66ed10: ubfx            x0, x0, #0xc, #0x14
    // 0x66ed14: mov             x1, x2
    // 0x66ed18: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x66ed18: movz            x17, #0x292d
    //     0x66ed1c: movk            x17, #0x1, lsl #16
    //     0x66ed20: add             lr, x0, x17
    //     0x66ed24: ldr             lr, [x21, lr, lsl #3]
    //     0x66ed28: blr             lr
    // 0x66ed2c: tbnz            w0, #4, #0x66ef44
    // 0x66ed30: ldur            x2, [fp, #-0x18]
    // 0x66ed34: r0 = LoadClassIdInstr(r2)
    //     0x66ed34: ldur            x0, [x2, #-1]
    //     0x66ed38: ubfx            x0, x0, #0xc, #0x14
    // 0x66ed3c: mov             x1, x2
    // 0x66ed40: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x66ed40: movz            x17, #0x384d
    //     0x66ed44: movk            x17, #0x1, lsl #16
    //     0x66ed48: add             lr, x0, x17
    //     0x66ed4c: ldr             lr, [x21, lr, lsl #3]
    //     0x66ed50: blr             lr
    // 0x66ed54: mov             x2, x0
    // 0x66ed58: stur            x2, [fp, #-0x28]
    // 0x66ed5c: r0 = LoadClassIdInstr(r2)
    //     0x66ed5c: ldur            x0, [x2, #-1]
    //     0x66ed60: ubfx            x0, x0, #0xc, #0x14
    // 0x66ed64: mov             x1, x2
    // 0x66ed68: r0 = GDT[cid_x0 + -0xf93]()
    //     0x66ed68: sub             lr, x0, #0xf93
    //     0x66ed6c: ldr             lr, [x21, lr, lsl #3]
    //     0x66ed70: blr             lr
    // 0x66ed74: r16 = Instance_XmlNodeType
    //     0x66ed74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e458] Obj!XmlNodeType@e2d371
    //     0x66ed78: ldr             x16, [x16, #0x458]
    // 0x66ed7c: cmp             w0, w16
    // 0x66ed80: b.ne            #0x66ee20
    // 0x66ed84: ldur            x0, [fp, #-0x10]
    // 0x66ed88: ldur            x1, [fp, #-0x28]
    // 0x66ed8c: r1 = 1
    //     0x66ed8c: movz            x1, #0x1
    // 0x66ed90: r0 = AllocateContext()
    //     0x66ed90: bl              #0xec126c  ; AllocateContextStub
    // 0x66ed94: mov             x3, x0
    // 0x66ed98: ldur            x2, [fp, #-0x10]
    // 0x66ed9c: stur            x3, [fp, #-0x30]
    // 0x66eda0: StoreField: r3->field_f = r2
    //     0x66eda0: stur            w2, [x3, #0xf]
    // 0x66eda4: ldur            x4, [fp, #-0x28]
    // 0x66eda8: r0 = LoadClassIdInstr(r4)
    //     0x66eda8: ldur            x0, [x4, #-1]
    //     0x66edac: ubfx            x0, x0, #0xc, #0x14
    // 0x66edb0: mov             x1, x4
    // 0x66edb4: r0 = GDT[cid_x0 + -0xecd]()
    //     0x66edb4: sub             lr, x0, #0xecd
    //     0x66edb8: ldr             lr, [x21, lr, lsl #3]
    //     0x66edbc: blr             lr
    // 0x66edc0: ldur            x2, [fp, #-0x30]
    // 0x66edc4: ldur            x3, [fp, #-8]
    // 0x66edc8: r1 = Function '<anonymous closure>':.
    //     0x66edc8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e460] AnonymousClosure: (0x66f340), in [package:xml/src/xml/utils/node_list.dart] XmlNodeList::_expandFragment (0x66f264)
    //     0x66edcc: ldr             x1, [x1, #0x460]
    // 0x66edd0: stur            x0, [fp, #-0x30]
    // 0x66edd4: r0 = AllocateClosureTA()
    //     0x66edd4: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x66edd8: mov             x1, x0
    // 0x66eddc: ldur            x0, [fp, #-0x30]
    // 0x66ede0: r2 = LoadClassIdInstr(r0)
    //     0x66ede0: ldur            x2, [x0, #-1]
    //     0x66ede4: ubfx            x2, x2, #0xc, #0x14
    // 0x66ede8: ldur            x16, [fp, #-8]
    // 0x66edec: stp             x0, x16, [SP, #8]
    // 0x66edf0: str             x1, [SP]
    // 0x66edf4: mov             x0, x2
    // 0x66edf8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x66edf8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x66edfc: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x66edfc: movz            x17, #0xf28c
    //     0x66ee00: add             lr, x0, x17
    //     0x66ee04: ldr             lr, [x21, lr, lsl #3]
    //     0x66ee08: blr             lr
    // 0x66ee0c: ldur            x1, [fp, #-0x20]
    // 0x66ee10: mov             x2, x0
    // 0x66ee14: r0 = addAll()
    //     0x66ee14: bl              #0x6e2fa8  ; [dart:core] _GrowableList::addAll
    // 0x66ee18: ldur            x2, [fp, #-0x20]
    // 0x66ee1c: b               #0x66ef38
    // 0x66ee20: ldur            x2, [fp, #-0x10]
    // 0x66ee24: ldur            x4, [fp, #-0x28]
    // 0x66ee28: LoadField: r3 = r2->field_13
    //     0x66ee28: ldur            w3, [x2, #0x13]
    // 0x66ee2c: DecompressPointer r3
    //     0x66ee2c: add             x3, x3, HEAP, lsl #32
    // 0x66ee30: r16 = Sentinel
    //     0x66ee30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x66ee34: cmp             w3, w16
    // 0x66ee38: b.eq            #0x66f088
    // 0x66ee3c: stur            x3, [fp, #-0x30]
    // 0x66ee40: r0 = LoadClassIdInstr(r4)
    //     0x66ee40: ldur            x0, [x4, #-1]
    //     0x66ee44: ubfx            x0, x0, #0xc, #0x14
    // 0x66ee48: mov             x1, x4
    // 0x66ee4c: r0 = GDT[cid_x0 + -0xf93]()
    //     0x66ee4c: sub             lr, x0, #0xf93
    //     0x66ee50: ldr             lr, [x21, lr, lsl #3]
    //     0x66ee54: blr             lr
    // 0x66ee58: ldur            x1, [fp, #-0x30]
    // 0x66ee5c: mov             x2, x0
    // 0x66ee60: r0 = contains()
    //     0x66ee60: bl              #0x86aab8  ; [dart:_compact_hash] __ConstSet&_HashVMImmutableBase&SetMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashSetMixin&_UnmodifiableSetMixin&_ImmutableLinkedHashSetMixin::contains
    // 0x66ee64: tbnz            w0, #4, #0x66ef9c
    // 0x66ee68: ldur            x2, [fp, #-0x28]
    // 0x66ee6c: r0 = LoadClassIdInstr(r2)
    //     0x66ee6c: ldur            x0, [x2, #-1]
    //     0x66ee70: ubfx            x0, x0, #0xc, #0x14
    // 0x66ee74: mov             x1, x2
    // 0x66ee78: r0 = GDT[cid_x0 + -0xf4e]()
    //     0x66ee78: sub             lr, x0, #0xf4e
    //     0x66ee7c: ldr             lr, [x21, lr, lsl #3]
    //     0x66ee80: blr             lr
    // 0x66ee84: cmp             w0, NULL
    // 0x66ee88: b.ne            #0x66ef58
    // 0x66ee8c: ldur            x3, [fp, #-0x20]
    // 0x66ee90: ldur            x0, [fp, #-0x28]
    // 0x66ee94: ldur            x2, [fp, #-8]
    // 0x66ee98: r1 = Null
    //     0x66ee98: mov             x1, NULL
    // 0x66ee9c: cmp             w2, NULL
    // 0x66eea0: b.eq            #0x66eec0
    // 0x66eea4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66eea4: ldur            w4, [x2, #0x17]
    // 0x66eea8: DecompressPointer r4
    //     0x66eea8: add             x4, x4, HEAP, lsl #32
    // 0x66eeac: r8 = X0
    //     0x66eeac: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x66eeb0: LoadField: r9 = r4->field_7
    //     0x66eeb0: ldur            x9, [x4, #7]
    // 0x66eeb4: r3 = Null
    //     0x66eeb4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e468] Null
    //     0x66eeb8: ldr             x3, [x3, #0x468]
    // 0x66eebc: blr             x9
    // 0x66eec0: ldur            x0, [fp, #-0x20]
    // 0x66eec4: LoadField: r1 = r0->field_b
    //     0x66eec4: ldur            w1, [x0, #0xb]
    // 0x66eec8: LoadField: r2 = r0->field_f
    //     0x66eec8: ldur            w2, [x0, #0xf]
    // 0x66eecc: DecompressPointer r2
    //     0x66eecc: add             x2, x2, HEAP, lsl #32
    // 0x66eed0: LoadField: r3 = r2->field_b
    //     0x66eed0: ldur            w3, [x2, #0xb]
    // 0x66eed4: r2 = LoadInt32Instr(r1)
    //     0x66eed4: sbfx            x2, x1, #1, #0x1f
    // 0x66eed8: stur            x2, [fp, #-0x38]
    // 0x66eedc: r1 = LoadInt32Instr(r3)
    //     0x66eedc: sbfx            x1, x3, #1, #0x1f
    // 0x66eee0: cmp             x2, x1
    // 0x66eee4: b.ne            #0x66eef0
    // 0x66eee8: mov             x1, x0
    // 0x66eeec: r0 = _growToNextCapacity()
    //     0x66eeec: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x66eef0: ldur            x2, [fp, #-0x20]
    // 0x66eef4: ldur            x3, [fp, #-0x38]
    // 0x66eef8: add             x0, x3, #1
    // 0x66eefc: lsl             x1, x0, #1
    // 0x66ef00: StoreField: r2->field_b = r1
    //     0x66ef00: stur            w1, [x2, #0xb]
    // 0x66ef04: LoadField: r1 = r2->field_f
    //     0x66ef04: ldur            w1, [x2, #0xf]
    // 0x66ef08: DecompressPointer r1
    //     0x66ef08: add             x1, x1, HEAP, lsl #32
    // 0x66ef0c: ldur            x0, [fp, #-0x28]
    // 0x66ef10: ArrayStore: r1[r3] = r0  ; List_4
    //     0x66ef10: add             x25, x1, x3, lsl #2
    //     0x66ef14: add             x25, x25, #0xf
    //     0x66ef18: str             w0, [x25]
    //     0x66ef1c: tbz             w0, #0, #0x66ef38
    //     0x66ef20: ldurb           w16, [x1, #-1]
    //     0x66ef24: ldurb           w17, [x0, #-1]
    //     0x66ef28: and             x16, x17, x16, lsr #2
    //     0x66ef2c: tst             x16, HEAP, lsr #32
    //     0x66ef30: b.eq            #0x66ef38
    //     0x66ef34: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x66ef38: mov             x3, x2
    // 0x66ef3c: ldur            x2, [fp, #-0x18]
    // 0x66ef40: b               #0x66ecfc
    // 0x66ef44: ldur            x2, [fp, #-0x20]
    // 0x66ef48: mov             x0, x2
    // 0x66ef4c: LeaveFrame
    //     0x66ef4c: mov             SP, fp
    //     0x66ef50: ldp             fp, lr, [SP], #0x10
    // 0x66ef54: ret
    //     0x66ef54: ret             
    // 0x66ef58: ldur            x0, [fp, #-0x28]
    // 0x66ef5c: r1 = LoadClassIdInstr(r0)
    //     0x66ef5c: ldur            x1, [x0, #-1]
    //     0x66ef60: ubfx            x1, x1, #0xc, #0x14
    // 0x66ef64: mov             x16, x0
    // 0x66ef68: mov             x0, x1
    // 0x66ef6c: mov             x1, x16
    // 0x66ef70: r0 = GDT[cid_x0 + -0xf4e]()
    //     0x66ef70: sub             lr, x0, #0xf4e
    //     0x66ef74: ldr             lr, [x21, lr, lsl #3]
    //     0x66ef78: blr             lr
    // 0x66ef7c: r0 = XmlParentException()
    //     0x66ef7c: bl              #0x66f334  ; AllocateXmlParentExceptionStub -> XmlParentException (size=0xc)
    // 0x66ef80: mov             x1, x0
    // 0x66ef84: r0 = "Node already has a parent, copy or remove it first"
    //     0x66ef84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e478] "Node already has a parent, copy or remove it first"
    //     0x66ef88: ldr             x0, [x0, #0x478]
    // 0x66ef8c: StoreField: r1->field_7 = r0
    //     0x66ef8c: stur            w0, [x1, #7]
    // 0x66ef90: mov             x0, x1
    // 0x66ef94: r0 = Throw()
    //     0x66ef94: bl              #0xec04b8  ; ThrowStub
    // 0x66ef98: brk             #0
    // 0x66ef9c: ldur            x0, [fp, #-0x28]
    // 0x66efa0: r1 = Null
    //     0x66efa0: mov             x1, NULL
    // 0x66efa4: r2 = 8
    //     0x66efa4: movz            x2, #0x8
    // 0x66efa8: r0 = AllocateArray()
    //     0x66efa8: bl              #0xec22fc  ; AllocateArrayStub
    // 0x66efac: mov             x2, x0
    // 0x66efb0: stur            x2, [fp, #-8]
    // 0x66efb4: r16 = "Got "
    //     0x66efb4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e480] "Got "
    //     0x66efb8: ldr             x16, [x16, #0x480]
    // 0x66efbc: StoreField: r2->field_f = r16
    //     0x66efbc: stur            w16, [x2, #0xf]
    // 0x66efc0: ldur            x1, [fp, #-0x28]
    // 0x66efc4: r0 = LoadClassIdInstr(r1)
    //     0x66efc4: ldur            x0, [x1, #-1]
    //     0x66efc8: ubfx            x0, x0, #0xc, #0x14
    // 0x66efcc: r0 = GDT[cid_x0 + -0xf93]()
    //     0x66efcc: sub             lr, x0, #0xf93
    //     0x66efd0: ldr             lr, [x21, lr, lsl #3]
    //     0x66efd4: blr             lr
    // 0x66efd8: ldur            x1, [fp, #-8]
    // 0x66efdc: ArrayStore: r1[1] = r0  ; List_4
    //     0x66efdc: add             x25, x1, #0x13
    //     0x66efe0: str             w0, [x25]
    //     0x66efe4: tbz             w0, #0, #0x66f000
    //     0x66efe8: ldurb           w16, [x1, #-1]
    //     0x66efec: ldurb           w17, [x0, #-1]
    //     0x66eff0: and             x16, x17, x16, lsr #2
    //     0x66eff4: tst             x16, HEAP, lsr #32
    //     0x66eff8: b.eq            #0x66f000
    //     0x66effc: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x66f000: ldur            x0, [fp, #-8]
    // 0x66f004: r16 = ", but expected one of "
    //     0x66f004: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e488] ", but expected one of "
    //     0x66f008: ldr             x16, [x16, #0x488]
    // 0x66f00c: ArrayStore: r0[0] = r16  ; List_4
    //     0x66f00c: stur            w16, [x0, #0x17]
    // 0x66f010: r16 = ", "
    //     0x66f010: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] ", "
    // 0x66f014: str             x16, [SP]
    // 0x66f018: ldur            x1, [fp, #-0x30]
    // 0x66f01c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x66f01c: ldr             x4, [PP, #0x438]  ; [pp+0x438] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x66f020: r0 = join()
    //     0x66f020: bl              #0x86c3ac  ; [dart:_compact_hash] __ConstSet&_HashVMImmutableBase&SetMixin::join
    // 0x66f024: ldur            x1, [fp, #-8]
    // 0x66f028: ArrayStore: r1[3] = r0  ; List_4
    //     0x66f028: add             x25, x1, #0x1b
    //     0x66f02c: str             w0, [x25]
    //     0x66f030: tbz             w0, #0, #0x66f04c
    //     0x66f034: ldurb           w16, [x1, #-1]
    //     0x66f038: ldurb           w17, [x0, #-1]
    //     0x66f03c: and             x16, x17, x16, lsr #2
    //     0x66f040: tst             x16, HEAP, lsr #32
    //     0x66f044: b.eq            #0x66f04c
    //     0x66f048: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x66f04c: ldur            x16, [fp, #-8]
    // 0x66f050: str             x16, [SP]
    // 0x66f054: r0 = _interpolate()
    //     0x66f054: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x66f058: stur            x0, [fp, #-8]
    // 0x66f05c: r0 = XmlNodeTypeException()
    //     0x66f05c: bl              #0x66f328  ; AllocateXmlNodeTypeExceptionStub -> XmlNodeTypeException (size=0xc)
    // 0x66f060: mov             x1, x0
    // 0x66f064: ldur            x0, [fp, #-8]
    // 0x66f068: StoreField: r1->field_7 = r0
    //     0x66f068: stur            w0, [x1, #7]
    // 0x66f06c: mov             x0, x1
    // 0x66f070: r0 = Throw()
    //     0x66f070: bl              #0xec04b8  ; ThrowStub
    // 0x66f074: brk             #0
    // 0x66f078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f078: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f07c: b               #0x66ecb4
    // 0x66f080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f080: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f084: b               #0x66ed0c
    // 0x66f088: r9 = _nodeTypes
    //     0x66f088: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e490] Field <XmlNodeList._nodeTypes@2752054576>: late final (offset: 0x14)
    //     0x66f08c: ldr             x9, [x9, #0x490]
    // 0x66f090: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x66f090: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _expandFragment(/* No info */) {
    // ** addr: 0x66f264, size: 0xc4
    // 0x66f264: EnterFrame
    //     0x66f264: stp             fp, lr, [SP, #-0x10]!
    //     0x66f268: mov             fp, SP
    // 0x66f26c: AllocStack(0x38)
    //     0x66f26c: sub             SP, SP, #0x38
    // 0x66f270: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x66f270: mov             x0, x1
    //     0x66f274: stur            x1, [fp, #-8]
    //     0x66f278: mov             x1, x2
    //     0x66f27c: stur            x2, [fp, #-0x10]
    // 0x66f280: CheckStackOverflow
    //     0x66f280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66f284: cmp             SP, x16
    //     0x66f288: b.ls            #0x66f320
    // 0x66f28c: r1 = 1
    //     0x66f28c: movz            x1, #0x1
    // 0x66f290: r0 = AllocateContext()
    //     0x66f290: bl              #0xec126c  ; AllocateContextStub
    // 0x66f294: mov             x2, x0
    // 0x66f298: ldur            x0, [fp, #-8]
    // 0x66f29c: stur            x2, [fp, #-0x20]
    // 0x66f2a0: StoreField: r2->field_f = r0
    //     0x66f2a0: stur            w0, [x2, #0xf]
    // 0x66f2a4: LoadField: r3 = r0->field_7
    //     0x66f2a4: ldur            w3, [x0, #7]
    // 0x66f2a8: DecompressPointer r3
    //     0x66f2a8: add             x3, x3, HEAP, lsl #32
    // 0x66f2ac: ldur            x1, [fp, #-0x10]
    // 0x66f2b0: stur            x3, [fp, #-0x18]
    // 0x66f2b4: r0 = LoadClassIdInstr(r1)
    //     0x66f2b4: ldur            x0, [x1, #-1]
    //     0x66f2b8: ubfx            x0, x0, #0xc, #0x14
    // 0x66f2bc: r0 = GDT[cid_x0 + -0xecd]()
    //     0x66f2bc: sub             lr, x0, #0xecd
    //     0x66f2c0: ldr             lr, [x21, lr, lsl #3]
    //     0x66f2c4: blr             lr
    // 0x66f2c8: ldur            x2, [fp, #-0x20]
    // 0x66f2cc: ldur            x3, [fp, #-0x18]
    // 0x66f2d0: r1 = Function '<anonymous closure>':.
    //     0x66f2d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e460] AnonymousClosure: (0x66f340), in [package:xml/src/xml/utils/node_list.dart] XmlNodeList::_expandFragment (0x66f264)
    //     0x66f2d4: ldr             x1, [x1, #0x460]
    // 0x66f2d8: stur            x0, [fp, #-8]
    // 0x66f2dc: r0 = AllocateClosureTA()
    //     0x66f2dc: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x66f2e0: mov             x1, x0
    // 0x66f2e4: ldur            x0, [fp, #-8]
    // 0x66f2e8: r2 = LoadClassIdInstr(r0)
    //     0x66f2e8: ldur            x2, [x0, #-1]
    //     0x66f2ec: ubfx            x2, x2, #0xc, #0x14
    // 0x66f2f0: ldur            x16, [fp, #-0x18]
    // 0x66f2f4: stp             x0, x16, [SP, #8]
    // 0x66f2f8: str             x1, [SP]
    // 0x66f2fc: mov             x0, x2
    // 0x66f300: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x66f300: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x66f304: r0 = GDT[cid_x0 + 0xf28c]()
    //     0x66f304: movz            x17, #0xf28c
    //     0x66f308: add             lr, x0, x17
    //     0x66f30c: ldr             lr, [x21, lr, lsl #3]
    //     0x66f310: blr             lr
    // 0x66f314: LeaveFrame
    //     0x66f314: mov             SP, fp
    //     0x66f318: ldp             fp, lr, [SP], #0x10
    // 0x66f31c: ret
    //     0x66f31c: ret             
    // 0x66f320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f320: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f324: b               #0x66f28c
  }
  [closure] X0 <anonymous closure>(dynamic, XmlNode) {
    // ** addr: 0x66f340, size: 0xd4
    // 0x66f340: EnterFrame
    //     0x66f340: stp             fp, lr, [SP, #-0x10]!
    //     0x66f344: mov             fp, SP
    // 0x66f348: AllocStack(0x10)
    //     0x66f348: sub             SP, SP, #0x10
    // 0x66f34c: SetupParameters()
    //     0x66f34c: ldr             x0, [fp, #0x18]
    //     0x66f350: ldur            w3, [x0, #0x17]
    //     0x66f354: add             x3, x3, HEAP, lsl #32
    //     0x66f358: stur            x3, [fp, #-8]
    // 0x66f35c: CheckStackOverflow
    //     0x66f35c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66f360: cmp             SP, x16
    //     0x66f364: b.ls            #0x66f400
    // 0x66f368: LoadField: r0 = r3->field_f
    //     0x66f368: ldur            w0, [x3, #0xf]
    // 0x66f36c: DecompressPointer r0
    //     0x66f36c: add             x0, x0, HEAP, lsl #32
    // 0x66f370: LoadField: r2 = r0->field_13
    //     0x66f370: ldur            w2, [x0, #0x13]
    // 0x66f374: DecompressPointer r2
    //     0x66f374: add             x2, x2, HEAP, lsl #32
    // 0x66f378: r16 = Sentinel
    //     0x66f378: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x66f37c: cmp             w2, w16
    // 0x66f380: b.eq            #0x66f408
    // 0x66f384: ldr             x1, [fp, #0x10]
    // 0x66f388: r0 = checkValidType()
    //     0x66f388: bl              #0x66f124  ; [package:xml/src/xml/exceptions/type_exception.dart] XmlNodeTypeException::checkValidType
    // 0x66f38c: ldr             x1, [fp, #0x10]
    // 0x66f390: r0 = LoadClassIdInstr(r1)
    //     0x66f390: ldur            x0, [x1, #-1]
    //     0x66f394: ubfx            x0, x0, #0xc, #0x14
    // 0x66f398: r0 = GDT[cid_x0 + -0xf17]()
    //     0x66f398: sub             lr, x0, #0xf17
    //     0x66f39c: ldr             lr, [x21, lr, lsl #3]
    //     0x66f3a0: blr             lr
    // 0x66f3a4: mov             x3, x0
    // 0x66f3a8: ldur            x0, [fp, #-8]
    // 0x66f3ac: stur            x3, [fp, #-0x10]
    // 0x66f3b0: LoadField: r1 = r0->field_f
    //     0x66f3b0: ldur            w1, [x0, #0xf]
    // 0x66f3b4: DecompressPointer r1
    //     0x66f3b4: add             x1, x1, HEAP, lsl #32
    // 0x66f3b8: LoadField: r2 = r1->field_7
    //     0x66f3b8: ldur            w2, [x1, #7]
    // 0x66f3bc: DecompressPointer r2
    //     0x66f3bc: add             x2, x2, HEAP, lsl #32
    // 0x66f3c0: mov             x0, x3
    // 0x66f3c4: r1 = Null
    //     0x66f3c4: mov             x1, NULL
    // 0x66f3c8: cmp             w2, NULL
    // 0x66f3cc: b.eq            #0x66f3f0
    // 0x66f3d0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66f3d0: ldur            w4, [x2, #0x17]
    // 0x66f3d4: DecompressPointer r4
    //     0x66f3d4: add             x4, x4, HEAP, lsl #32
    // 0x66f3d8: r8 = X0 bound XmlNode
    //     0x66f3d8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e498] TypeParameter: X0 bound XmlNode
    //     0x66f3dc: ldr             x8, [x8, #0x498]
    // 0x66f3e0: LoadField: r9 = r4->field_7
    //     0x66f3e0: ldur            x9, [x4, #7]
    // 0x66f3e4: r3 = Null
    //     0x66f3e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e4a0] Null
    //     0x66f3e8: ldr             x3, [x3, #0x4a0]
    // 0x66f3ec: blr             x9
    // 0x66f3f0: ldur            x0, [fp, #-0x10]
    // 0x66f3f4: LeaveFrame
    //     0x66f3f4: mov             SP, fp
    //     0x66f3f8: ldp             fp, lr, [SP], #0x10
    // 0x66f3fc: ret
    //     0x66f3fc: ret             
    // 0x66f400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f404: b               #0x66f368
    // 0x66f408: r9 = _nodeTypes
    //     0x66f408: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e490] Field <XmlNodeList._nodeTypes@2752054576>: late final (offset: 0x14)
    //     0x66f40c: ldr             x9, [x9, #0x490]
    // 0x66f410: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x66f410: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ addAll(/* No info */) {
    // ** addr: 0x66f670, size: 0x19c
    // 0x66f670: EnterFrame
    //     0x66f670: stp             fp, lr, [SP, #-0x10]!
    //     0x66f674: mov             fp, SP
    // 0x66f678: AllocStack(0x30)
    //     0x66f678: sub             SP, SP, #0x30
    // 0x66f67c: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x66f67c: mov             x4, x1
    //     0x66f680: mov             x3, x2
    //     0x66f684: stur            x1, [fp, #-8]
    //     0x66f688: stur            x2, [fp, #-0x10]
    // 0x66f68c: CheckStackOverflow
    //     0x66f68c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66f690: cmp             SP, x16
    //     0x66f694: b.ls            #0x66f7f0
    // 0x66f698: LoadField: r2 = r4->field_7
    //     0x66f698: ldur            w2, [x4, #7]
    // 0x66f69c: DecompressPointer r2
    //     0x66f69c: add             x2, x2, HEAP, lsl #32
    // 0x66f6a0: mov             x0, x3
    // 0x66f6a4: r1 = Null
    //     0x66f6a4: mov             x1, NULL
    // 0x66f6a8: r8 = Iterable<X0 bound XmlNode>
    //     0x66f6a8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e428] Type: Iterable<X0 bound XmlNode>
    //     0x66f6ac: ldr             x8, [x8, #0x428]
    // 0x66f6b0: LoadField: r9 = r8->field_7
    //     0x66f6b0: ldur            x9, [x8, #7]
    // 0x66f6b4: r3 = Null
    //     0x66f6b4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e430] Null
    //     0x66f6b8: ldr             x3, [x3, #0x430]
    // 0x66f6bc: blr             x9
    // 0x66f6c0: ldur            x1, [fp, #-8]
    // 0x66f6c4: ldur            x2, [fp, #-0x10]
    // 0x66f6c8: r0 = _expandNodes()
    //     0x66f6c8: bl              #0x66ec8c  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::_expandNodes
    // 0x66f6cc: ldur            x1, [fp, #-8]
    // 0x66f6d0: mov             x2, x0
    // 0x66f6d4: stur            x0, [fp, #-0x10]
    // 0x66f6d8: r0 = addAll()
    //     0x66f6d8: bl              #0x66f80c  ; [package:collection/src/wrappers.dart] DelegatingList::addAll
    // 0x66f6dc: ldur            x3, [fp, #-0x10]
    // 0x66f6e0: LoadField: r4 = r3->field_7
    //     0x66f6e0: ldur            w4, [x3, #7]
    // 0x66f6e4: DecompressPointer r4
    //     0x66f6e4: add             x4, x4, HEAP, lsl #32
    // 0x66f6e8: stur            x4, [fp, #-0x30]
    // 0x66f6ec: LoadField: r0 = r3->field_b
    //     0x66f6ec: ldur            w0, [x3, #0xb]
    // 0x66f6f0: r5 = LoadInt32Instr(r0)
    //     0x66f6f0: sbfx            x5, x0, #1, #0x1f
    // 0x66f6f4: stur            x5, [fp, #-0x28]
    // 0x66f6f8: r0 = 0
    //     0x66f6f8: movz            x0, #0
    // 0x66f6fc: ldur            x6, [fp, #-8]
    // 0x66f700: CheckStackOverflow
    //     0x66f700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x66f704: cmp             SP, x16
    //     0x66f708: b.ls            #0x66f7f8
    // 0x66f70c: LoadField: r1 = r3->field_b
    //     0x66f70c: ldur            w1, [x3, #0xb]
    // 0x66f710: r2 = LoadInt32Instr(r1)
    //     0x66f710: sbfx            x2, x1, #1, #0x1f
    // 0x66f714: cmp             x5, x2
    // 0x66f718: b.ne            #0x66f7d0
    // 0x66f71c: cmp             x0, x2
    // 0x66f720: b.ge            #0x66f7c0
    // 0x66f724: LoadField: r1 = r3->field_f
    //     0x66f724: ldur            w1, [x3, #0xf]
    // 0x66f728: DecompressPointer r1
    //     0x66f728: add             x1, x1, HEAP, lsl #32
    // 0x66f72c: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0x66f72c: add             x16, x1, x0, lsl #2
    //     0x66f730: ldur            w7, [x16, #0xf]
    // 0x66f734: DecompressPointer r7
    //     0x66f734: add             x7, x7, HEAP, lsl #32
    // 0x66f738: stur            x7, [fp, #-0x20]
    // 0x66f73c: add             x8, x0, #1
    // 0x66f740: stur            x8, [fp, #-0x18]
    // 0x66f744: cmp             w7, NULL
    // 0x66f748: b.ne            #0x66f77c
    // 0x66f74c: mov             x0, x7
    // 0x66f750: mov             x2, x4
    // 0x66f754: r1 = Null
    //     0x66f754: mov             x1, NULL
    // 0x66f758: cmp             w2, NULL
    // 0x66f75c: b.eq            #0x66f77c
    // 0x66f760: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66f760: ldur            w4, [x2, #0x17]
    // 0x66f764: DecompressPointer r4
    //     0x66f764: add             x4, x4, HEAP, lsl #32
    // 0x66f768: r8 = X0
    //     0x66f768: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x66f76c: LoadField: r9 = r4->field_7
    //     0x66f76c: ldur            x9, [x4, #7]
    // 0x66f770: r3 = Null
    //     0x66f770: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e440] Null
    //     0x66f774: ldr             x3, [x3, #0x440]
    // 0x66f778: blr             x9
    // 0x66f77c: ldur            x3, [fp, #-8]
    // 0x66f780: ldur            x1, [fp, #-0x20]
    // 0x66f784: LoadField: r2 = r3->field_f
    //     0x66f784: ldur            w2, [x3, #0xf]
    // 0x66f788: DecompressPointer r2
    //     0x66f788: add             x2, x2, HEAP, lsl #32
    // 0x66f78c: r16 = Sentinel
    //     0x66f78c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x66f790: cmp             w2, w16
    // 0x66f794: b.eq            #0x66f800
    // 0x66f798: r0 = LoadClassIdInstr(r1)
    //     0x66f798: ldur            x0, [x1, #-1]
    //     0x66f79c: ubfx            x0, x0, #0xc, #0x14
    // 0x66f7a0: r0 = GDT[cid_x0 + -0xf83]()
    //     0x66f7a0: sub             lr, x0, #0xf83
    //     0x66f7a4: ldr             lr, [x21, lr, lsl #3]
    //     0x66f7a8: blr             lr
    // 0x66f7ac: ldur            x0, [fp, #-0x18]
    // 0x66f7b0: ldur            x3, [fp, #-0x10]
    // 0x66f7b4: ldur            x4, [fp, #-0x30]
    // 0x66f7b8: ldur            x5, [fp, #-0x28]
    // 0x66f7bc: b               #0x66f6fc
    // 0x66f7c0: r0 = Null
    //     0x66f7c0: mov             x0, NULL
    // 0x66f7c4: LeaveFrame
    //     0x66f7c4: mov             SP, fp
    //     0x66f7c8: ldp             fp, lr, [SP], #0x10
    // 0x66f7cc: ret
    //     0x66f7cc: ret             
    // 0x66f7d0: mov             x0, x3
    // 0x66f7d4: r0 = ConcurrentModificationError()
    //     0x66f7d4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x66f7d8: mov             x1, x0
    // 0x66f7dc: ldur            x0, [fp, #-0x10]
    // 0x66f7e0: StoreField: r1->field_b = r0
    //     0x66f7e0: stur            w0, [x1, #0xb]
    // 0x66f7e4: mov             x0, x1
    // 0x66f7e8: r0 = Throw()
    //     0x66f7e8: bl              #0xec04b8  ; ThrowStub
    // 0x66f7ec: brk             #0
    // 0x66f7f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f7f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f7f4: b               #0x66f698
    // 0x66f7f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x66f7f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x66f7fc: b               #0x66f70c
    // 0x66f800: r9 = _parent
    //     0x66f800: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x66f804: ldr             x9, [x9, #0x450]
    // 0x66f808: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x66f808: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ insert(/* No info */) {
    // ** addr: 0x66f8d0, size: 0x4c
    // 0x66f8d0: EnterFrame
    //     0x66f8d0: stp             fp, lr, [SP, #-0x10]!
    //     0x66f8d4: mov             fp, SP
    // 0x66f8d8: mov             x0, x3
    // 0x66f8dc: LoadField: r2 = r1->field_7
    //     0x66f8dc: ldur            w2, [x1, #7]
    // 0x66f8e0: DecompressPointer r2
    //     0x66f8e0: add             x2, x2, HEAP, lsl #32
    // 0x66f8e4: r1 = Null
    //     0x66f8e4: mov             x1, NULL
    // 0x66f8e8: cmp             w2, NULL
    // 0x66f8ec: b.eq            #0x66f910
    // 0x66f8f0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66f8f0: ldur            w4, [x2, #0x17]
    // 0x66f8f4: DecompressPointer r4
    //     0x66f8f4: add             x4, x4, HEAP, lsl #32
    // 0x66f8f8: r8 = X0 bound XmlNode
    //     0x66f8f8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e498] TypeParameter: X0 bound XmlNode
    //     0x66f8fc: ldr             x8, [x8, #0x498]
    // 0x66f900: LoadField: r9 = r4->field_7
    //     0x66f900: ldur            x9, [x4, #7]
    // 0x66f904: r3 = Null
    //     0x66f904: add             x3, PP, #0x46, lsl #12  ; [pp+0x46c78] Null
    //     0x66f908: ldr             x3, [x3, #0xc78]
    // 0x66f90c: blr             x9
    // 0x66f910: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x66f910: ldr             x0, [PP, #0x9f0]  ; [pp+0x9f0] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x66f914: r0 = Throw()
    //     0x66f914: bl              #0xec04b8  ; ThrowStub
    // 0x66f918: brk             #0
  }
  set _ length=(/* No info */) {
    // ** addr: 0x6715c8, size: 0x28
    // 0x6715c8: EnterFrame
    //     0x6715c8: stp             fp, lr, [SP, #-0x10]!
    //     0x6715cc: mov             fp, SP
    // 0x6715d0: r0 = UnsupportedError()
    //     0x6715d0: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x6715d4: mov             x1, x0
    // 0x6715d8: r0 = "Unsupported length change of node list"
    //     0x6715d8: add             x0, PP, #0x31, lsl #12  ; [pp+0x31158] "Unsupported length change of node list"
    //     0x6715dc: ldr             x0, [x0, #0x158]
    // 0x6715e0: StoreField: r1->field_b = r0
    //     0x6715e0: stur            w0, [x1, #0xb]
    // 0x6715e4: mov             x0, x1
    // 0x6715e8: r0 = Throw()
    //     0x6715e8: bl              #0xec04b8  ; ThrowStub
    // 0x6715ec: brk             #0
  }
  _ removeWhere(/* No info */) {
    // ** addr: 0x671988, size: 0x78
    // 0x671988: EnterFrame
    //     0x671988: stp             fp, lr, [SP, #-0x10]!
    //     0x67198c: mov             fp, SP
    // 0x671990: AllocStack(0x10)
    //     0x671990: sub             SP, SP, #0x10
    // 0x671994: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x671994: stur            x1, [fp, #-8]
    //     0x671998: stur            x2, [fp, #-0x10]
    // 0x67199c: CheckStackOverflow
    //     0x67199c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6719a0: cmp             SP, x16
    //     0x6719a4: b.ls            #0x6719f8
    // 0x6719a8: r1 = 2
    //     0x6719a8: movz            x1, #0x2
    // 0x6719ac: r0 = AllocateContext()
    //     0x6719ac: bl              #0xec126c  ; AllocateContextStub
    // 0x6719b0: mov             x1, x0
    // 0x6719b4: ldur            x0, [fp, #-8]
    // 0x6719b8: StoreField: r1->field_f = r0
    //     0x6719b8: stur            w0, [x1, #0xf]
    // 0x6719bc: ldur            x2, [fp, #-0x10]
    // 0x6719c0: StoreField: r1->field_13 = r2
    //     0x6719c0: stur            w2, [x1, #0x13]
    // 0x6719c4: LoadField: r3 = r0->field_7
    //     0x6719c4: ldur            w3, [x0, #7]
    // 0x6719c8: DecompressPointer r3
    //     0x6719c8: add             x3, x3, HEAP, lsl #32
    // 0x6719cc: mov             x2, x1
    // 0x6719d0: r1 = Function '<anonymous closure>':.
    //     0x6719d0: add             x1, PP, #0x31, lsl #12  ; [pp+0x31130] AnonymousClosure: (0x671a3c), in [package:xml/src/xml/utils/node_list.dart] XmlNodeList::removeWhere (0x671988)
    //     0x6719d4: ldr             x1, [x1, #0x130]
    // 0x6719d8: r0 = AllocateClosureTA()
    //     0x6719d8: bl              #0xec1474  ; AllocateClosureTAStub
    // 0x6719dc: ldur            x1, [fp, #-8]
    // 0x6719e0: mov             x2, x0
    // 0x6719e4: r0 = removeWhere()
    //     0x6719e4: bl              #0x671a00  ; [package:collection/src/wrappers.dart] DelegatingList::removeWhere
    // 0x6719e8: r0 = Null
    //     0x6719e8: mov             x0, NULL
    // 0x6719ec: LeaveFrame
    //     0x6719ec: mov             SP, fp
    //     0x6719f0: ldp             fp, lr, [SP], #0x10
    // 0x6719f4: ret
    //     0x6719f4: ret             
    // 0x6719f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6719f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6719fc: b               #0x6719a8
  }
  [closure] bool <anonymous closure>(dynamic, X0) {
    // ** addr: 0x671a3c, size: 0xc0
    // 0x671a3c: EnterFrame
    //     0x671a3c: stp             fp, lr, [SP, #-0x10]!
    //     0x671a40: mov             fp, SP
    // 0x671a44: AllocStack(0x20)
    //     0x671a44: sub             SP, SP, #0x20
    // 0x671a48: SetupParameters()
    //     0x671a48: ldr             x0, [fp, #0x18]
    //     0x671a4c: ldur            w1, [x0, #0x17]
    //     0x671a50: add             x1, x1, HEAP, lsl #32
    //     0x671a54: stur            x1, [fp, #-8]
    // 0x671a58: CheckStackOverflow
    //     0x671a58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x671a5c: cmp             SP, x16
    //     0x671a60: b.ls            #0x671ae8
    // 0x671a64: LoadField: r0 = r1->field_13
    //     0x671a64: ldur            w0, [x1, #0x13]
    // 0x671a68: DecompressPointer r0
    //     0x671a68: add             x0, x0, HEAP, lsl #32
    // 0x671a6c: ldr             x16, [fp, #0x10]
    // 0x671a70: stp             x16, x0, [SP]
    // 0x671a74: ClosureCall
    //     0x671a74: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x671a78: ldur            x2, [x0, #0x1f]
    //     0x671a7c: blr             x2
    // 0x671a80: mov             x3, x0
    // 0x671a84: stur            x3, [fp, #-0x10]
    // 0x671a88: r16 = true
    //     0x671a88: add             x16, NULL, #0x20  ; true
    // 0x671a8c: cmp             w3, w16
    // 0x671a90: b.ne            #0x671ad8
    // 0x671a94: ldr             x1, [fp, #0x10]
    // 0x671a98: ldur            x0, [fp, #-8]
    // 0x671a9c: LoadField: r2 = r0->field_f
    //     0x671a9c: ldur            w2, [x0, #0xf]
    // 0x671aa0: DecompressPointer r2
    //     0x671aa0: add             x2, x2, HEAP, lsl #32
    // 0x671aa4: LoadField: r0 = r2->field_f
    //     0x671aa4: ldur            w0, [x2, #0xf]
    // 0x671aa8: DecompressPointer r0
    //     0x671aa8: add             x0, x0, HEAP, lsl #32
    // 0x671aac: r16 = Sentinel
    //     0x671aac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x671ab0: cmp             w0, w16
    // 0x671ab4: b.eq            #0x671af0
    // 0x671ab8: r2 = LoadClassIdInstr(r1)
    //     0x671ab8: ldur            x2, [x1, #-1]
    //     0x671abc: ubfx            x2, x2, #0xc, #0x14
    // 0x671ac0: mov             x16, x0
    // 0x671ac4: mov             x0, x2
    // 0x671ac8: mov             x2, x16
    // 0x671acc: r0 = GDT[cid_x0 + -0xfa3]()
    //     0x671acc: sub             lr, x0, #0xfa3
    //     0x671ad0: ldr             lr, [x21, lr, lsl #3]
    //     0x671ad4: blr             lr
    // 0x671ad8: ldur            x0, [fp, #-0x10]
    // 0x671adc: LeaveFrame
    //     0x671adc: mov             SP, fp
    //     0x671ae0: ldp             fp, lr, [SP], #0x10
    // 0x671ae4: ret
    //     0x671ae4: ret             
    // 0x671ae8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x671ae8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x671aec: b               #0x671a64
    // 0x671af0: r9 = _parent
    //     0x671af0: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x671af4: ldr             x9, [x9, #0x450]
    // 0x671af8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x671af8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ removeLast(/* No info */) {
    // ** addr: 0x671b7c, size: 0xc4
    // 0x671b7c: EnterFrame
    //     0x671b7c: stp             fp, lr, [SP, #-0x10]!
    //     0x671b80: mov             fp, SP
    // 0x671b84: AllocStack(0x10)
    //     0x671b84: sub             SP, SP, #0x10
    // 0x671b88: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r3, fp-0x10 */)
    //     0x671b88: mov             x3, x1
    //     0x671b8c: stur            x1, [fp, #-0x10]
    // 0x671b90: CheckStackOverflow
    //     0x671b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x671b94: cmp             SP, x16
    //     0x671b98: b.ls            #0x671c28
    // 0x671b9c: LoadField: r2 = r3->field_b
    //     0x671b9c: ldur            w2, [x3, #0xb]
    // 0x671ba0: DecompressPointer r2
    //     0x671ba0: add             x2, x2, HEAP, lsl #32
    // 0x671ba4: LoadField: r0 = r2->field_b
    //     0x671ba4: ldur            w0, [x2, #0xb]
    // 0x671ba8: r1 = LoadInt32Instr(r0)
    //     0x671ba8: sbfx            x1, x0, #1, #0x1f
    // 0x671bac: sub             x4, x1, #1
    // 0x671bb0: mov             x0, x1
    // 0x671bb4: mov             x1, x4
    // 0x671bb8: cmp             x1, x0
    // 0x671bbc: b.hs            #0x671c30
    // 0x671bc0: LoadField: r0 = r2->field_f
    //     0x671bc0: ldur            w0, [x2, #0xf]
    // 0x671bc4: DecompressPointer r0
    //     0x671bc4: add             x0, x0, HEAP, lsl #32
    // 0x671bc8: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0x671bc8: add             x16, x0, x4, lsl #2
    //     0x671bcc: ldur            w5, [x16, #0xf]
    // 0x671bd0: DecompressPointer r5
    //     0x671bd0: add             x5, x5, HEAP, lsl #32
    // 0x671bd4: mov             x1, x2
    // 0x671bd8: mov             x2, x4
    // 0x671bdc: stur            x5, [fp, #-8]
    // 0x671be0: r0 = length=()
    //     0x671be0: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x671be4: ldur            x0, [fp, #-0x10]
    // 0x671be8: LoadField: r2 = r0->field_f
    //     0x671be8: ldur            w2, [x0, #0xf]
    // 0x671bec: DecompressPointer r2
    //     0x671bec: add             x2, x2, HEAP, lsl #32
    // 0x671bf0: r16 = Sentinel
    //     0x671bf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x671bf4: cmp             w2, w16
    // 0x671bf8: b.eq            #0x671c34
    // 0x671bfc: ldur            x3, [fp, #-8]
    // 0x671c00: r0 = LoadClassIdInstr(r3)
    //     0x671c00: ldur            x0, [x3, #-1]
    //     0x671c04: ubfx            x0, x0, #0xc, #0x14
    // 0x671c08: mov             x1, x3
    // 0x671c0c: r0 = GDT[cid_x0 + -0xfa3]()
    //     0x671c0c: sub             lr, x0, #0xfa3
    //     0x671c10: ldr             lr, [x21, lr, lsl #3]
    //     0x671c14: blr             lr
    // 0x671c18: ldur            x0, [fp, #-8]
    // 0x671c1c: LeaveFrame
    //     0x671c1c: mov             SP, fp
    //     0x671c20: ldp             fp, lr, [SP], #0x10
    // 0x671c24: ret
    //     0x671c24: ret             
    // 0x671c28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x671c28: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x671c2c: b               #0x671b9c
    // 0x671c30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x671c30: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x671c34: r9 = _parent
    //     0x671c34: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x671c38: ldr             x9, [x9, #0x450]
    // 0x671c3c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x671c3c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ add(/* No info */) {
    // ** addr: 0x671d18, size: 0x1e4
    // 0x671d18: EnterFrame
    //     0x671d18: stp             fp, lr, [SP, #-0x10]!
    //     0x671d1c: mov             fp, SP
    // 0x671d20: AllocStack(0x10)
    //     0x671d20: sub             SP, SP, #0x10
    // 0x671d24: CheckStackOverflow
    //     0x671d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x671d28: cmp             SP, x16
    //     0x671d2c: b.ls            #0x671edc
    // 0x671d30: ldr             x3, [fp, #0x18]
    // 0x671d34: LoadField: r2 = r3->field_7
    //     0x671d34: ldur            w2, [x3, #7]
    // 0x671d38: DecompressPointer r2
    //     0x671d38: add             x2, x2, HEAP, lsl #32
    // 0x671d3c: ldr             x0, [fp, #0x10]
    // 0x671d40: r1 = Null
    //     0x671d40: mov             x1, NULL
    // 0x671d44: cmp             w2, NULL
    // 0x671d48: b.eq            #0x671d6c
    // 0x671d4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x671d4c: ldur            w4, [x2, #0x17]
    // 0x671d50: DecompressPointer r4
    //     0x671d50: add             x4, x4, HEAP, lsl #32
    // 0x671d54: r8 = X0 bound XmlNode
    //     0x671d54: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e498] TypeParameter: X0 bound XmlNode
    //     0x671d58: ldr             x8, [x8, #0x498]
    // 0x671d5c: LoadField: r9 = r4->field_7
    //     0x671d5c: ldur            x9, [x4, #7]
    // 0x671d60: r3 = Null
    //     0x671d60: add             x3, PP, #0x31, lsl #12  ; [pp+0x31138] Null
    //     0x671d64: ldr             x3, [x3, #0x138]
    // 0x671d68: blr             x9
    // 0x671d6c: ldr             x2, [fp, #0x10]
    // 0x671d70: r0 = LoadClassIdInstr(r2)
    //     0x671d70: ldur            x0, [x2, #-1]
    //     0x671d74: ubfx            x0, x0, #0xc, #0x14
    // 0x671d78: mov             x1, x2
    // 0x671d7c: r0 = GDT[cid_x0 + -0xf93]()
    //     0x671d7c: sub             lr, x0, #0xf93
    //     0x671d80: ldr             lr, [x21, lr, lsl #3]
    //     0x671d84: blr             lr
    // 0x671d88: r16 = Instance_XmlNodeType
    //     0x671d88: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e458] Obj!XmlNodeType@e2d371
    //     0x671d8c: ldr             x16, [x16, #0x458]
    // 0x671d90: cmp             w0, w16
    // 0x671d94: b.ne            #0x671db4
    // 0x671d98: ldr             x1, [fp, #0x18]
    // 0x671d9c: ldr             x2, [fp, #0x10]
    // 0x671da0: r0 = _expandFragment()
    //     0x671da0: bl              #0x66f264  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::_expandFragment
    // 0x671da4: ldr             x1, [fp, #0x18]
    // 0x671da8: mov             x2, x0
    // 0x671dac: r0 = addAll()
    //     0x671dac: bl              #0x66f670  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::addAll
    // 0x671db0: b               #0x671ecc
    // 0x671db4: ldr             x0, [fp, #0x18]
    // 0x671db8: LoadField: r2 = r0->field_13
    //     0x671db8: ldur            w2, [x0, #0x13]
    // 0x671dbc: DecompressPointer r2
    //     0x671dbc: add             x2, x2, HEAP, lsl #32
    // 0x671dc0: r16 = Sentinel
    //     0x671dc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x671dc4: cmp             w2, w16
    // 0x671dc8: b.eq            #0x671ee4
    // 0x671dcc: ldr             x1, [fp, #0x10]
    // 0x671dd0: r0 = checkValidType()
    //     0x671dd0: bl              #0x66f124  ; [package:xml/src/xml/exceptions/type_exception.dart] XmlNodeTypeException::checkValidType
    // 0x671dd4: ldr             x1, [fp, #0x10]
    // 0x671dd8: r0 = checkNoParent()
    //     0x671dd8: bl              #0x66f094  ; [package:xml/src/xml/exceptions/parent_exception.dart] XmlParentException::checkNoParent
    // 0x671ddc: ldr             x3, [fp, #0x18]
    // 0x671de0: LoadField: r4 = r3->field_b
    //     0x671de0: ldur            w4, [x3, #0xb]
    // 0x671de4: DecompressPointer r4
    //     0x671de4: add             x4, x4, HEAP, lsl #32
    // 0x671de8: stur            x4, [fp, #-8]
    // 0x671dec: LoadField: r2 = r4->field_7
    //     0x671dec: ldur            w2, [x4, #7]
    // 0x671df0: DecompressPointer r2
    //     0x671df0: add             x2, x2, HEAP, lsl #32
    // 0x671df4: ldr             x0, [fp, #0x10]
    // 0x671df8: r1 = Null
    //     0x671df8: mov             x1, NULL
    // 0x671dfc: cmp             w2, NULL
    // 0x671e00: b.eq            #0x671e20
    // 0x671e04: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x671e04: ldur            w4, [x2, #0x17]
    // 0x671e08: DecompressPointer r4
    //     0x671e08: add             x4, x4, HEAP, lsl #32
    // 0x671e0c: r8 = X0
    //     0x671e0c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x671e10: LoadField: r9 = r4->field_7
    //     0x671e10: ldur            x9, [x4, #7]
    // 0x671e14: r3 = Null
    //     0x671e14: add             x3, PP, #0x31, lsl #12  ; [pp+0x31148] Null
    //     0x671e18: ldr             x3, [x3, #0x148]
    // 0x671e1c: blr             x9
    // 0x671e20: ldur            x0, [fp, #-8]
    // 0x671e24: LoadField: r1 = r0->field_b
    //     0x671e24: ldur            w1, [x0, #0xb]
    // 0x671e28: LoadField: r2 = r0->field_f
    //     0x671e28: ldur            w2, [x0, #0xf]
    // 0x671e2c: DecompressPointer r2
    //     0x671e2c: add             x2, x2, HEAP, lsl #32
    // 0x671e30: LoadField: r3 = r2->field_b
    //     0x671e30: ldur            w3, [x2, #0xb]
    // 0x671e34: r2 = LoadInt32Instr(r1)
    //     0x671e34: sbfx            x2, x1, #1, #0x1f
    // 0x671e38: stur            x2, [fp, #-0x10]
    // 0x671e3c: r1 = LoadInt32Instr(r3)
    //     0x671e3c: sbfx            x1, x3, #1, #0x1f
    // 0x671e40: cmp             x2, x1
    // 0x671e44: b.ne            #0x671e50
    // 0x671e48: mov             x1, x0
    // 0x671e4c: r0 = _growToNextCapacity()
    //     0x671e4c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x671e50: ldr             x3, [fp, #0x18]
    // 0x671e54: ldr             x4, [fp, #0x10]
    // 0x671e58: ldur            x0, [fp, #-8]
    // 0x671e5c: ldur            x2, [fp, #-0x10]
    // 0x671e60: add             x1, x2, #1
    // 0x671e64: lsl             x5, x1, #1
    // 0x671e68: StoreField: r0->field_b = r5
    //     0x671e68: stur            w5, [x0, #0xb]
    // 0x671e6c: LoadField: r1 = r0->field_f
    //     0x671e6c: ldur            w1, [x0, #0xf]
    // 0x671e70: DecompressPointer r1
    //     0x671e70: add             x1, x1, HEAP, lsl #32
    // 0x671e74: mov             x0, x4
    // 0x671e78: ArrayStore: r1[r2] = r0  ; List_4
    //     0x671e78: add             x25, x1, x2, lsl #2
    //     0x671e7c: add             x25, x25, #0xf
    //     0x671e80: str             w0, [x25]
    //     0x671e84: tbz             w0, #0, #0x671ea0
    //     0x671e88: ldurb           w16, [x1, #-1]
    //     0x671e8c: ldurb           w17, [x0, #-1]
    //     0x671e90: and             x16, x17, x16, lsr #2
    //     0x671e94: tst             x16, HEAP, lsr #32
    //     0x671e98: b.eq            #0x671ea0
    //     0x671e9c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x671ea0: LoadField: r2 = r3->field_f
    //     0x671ea0: ldur            w2, [x3, #0xf]
    // 0x671ea4: DecompressPointer r2
    //     0x671ea4: add             x2, x2, HEAP, lsl #32
    // 0x671ea8: r16 = Sentinel
    //     0x671ea8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x671eac: cmp             w2, w16
    // 0x671eb0: b.eq            #0x671ef0
    // 0x671eb4: r0 = LoadClassIdInstr(r4)
    //     0x671eb4: ldur            x0, [x4, #-1]
    //     0x671eb8: ubfx            x0, x0, #0xc, #0x14
    // 0x671ebc: mov             x1, x4
    // 0x671ec0: r0 = GDT[cid_x0 + -0xf83]()
    //     0x671ec0: sub             lr, x0, #0xf83
    //     0x671ec4: ldr             lr, [x21, lr, lsl #3]
    //     0x671ec8: blr             lr
    // 0x671ecc: r0 = Null
    //     0x671ecc: mov             x0, NULL
    // 0x671ed0: LeaveFrame
    //     0x671ed0: mov             SP, fp
    //     0x671ed4: ldp             fp, lr, [SP], #0x10
    // 0x671ed8: ret
    //     0x671ed8: ret             
    // 0x671edc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x671edc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x671ee0: b               #0x671d30
    // 0x671ee4: r9 = _nodeTypes
    //     0x671ee4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e490] Field <XmlNodeList._nodeTypes@2752054576>: late final (offset: 0x14)
    //     0x671ee8: ldr             x9, [x9, #0x490]
    // 0x671eec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x671eec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x671ef0: r9 = _parent
    //     0x671ef0: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x671ef4: ldr             x9, [x9, #0x450]
    // 0x671ef8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x671ef8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x6720dc, size: 0x44
    // 0x6720dc: EnterFrame
    //     0x6720dc: stp             fp, lr, [SP, #-0x10]!
    //     0x6720e0: mov             fp, SP
    // 0x6720e4: AllocStack(0x10)
    //     0x6720e4: sub             SP, SP, #0x10
    // 0x6720e8: SetupParameters()
    //     0x6720e8: ldr             x0, [fp, #0x18]
    //     0x6720ec: ldur            w1, [x0, #0x17]
    //     0x6720f0: add             x1, x1, HEAP, lsl #32
    // 0x6720f4: CheckStackOverflow
    //     0x6720f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6720f8: cmp             SP, x16
    //     0x6720fc: b.ls            #0x672118
    // 0x672100: ldr             x16, [fp, #0x10]
    // 0x672104: stp             x16, x1, [SP]
    // 0x672108: r0 = add()
    //     0x672108: bl              #0x671d18  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::add
    // 0x67210c: LeaveFrame
    //     0x67210c: mov             SP, fp
    //     0x672110: ldp             fp, lr, [SP], #0x10
    // 0x672114: ret
    //     0x672114: ret             
    // 0x672118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x672118: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67211c: b               #0x672100
  }
  _ remove(/* No info */) {
    // ** addr: 0x6724b8, size: 0x2c
    // 0x6724b8: EnterFrame
    //     0x6724b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6724bc: mov             fp, SP
    // 0x6724c0: CheckStackOverflow
    //     0x6724c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6724c4: cmp             SP, x16
    //     0x6724c8: b.ls            #0x6724dc
    // 0x6724cc: r0 = remove()
    //     0x6724cc: bl              #0x66cbd0  ; [package:html/src/list_proxy.dart] ListProxy::remove
    // 0x6724d0: LeaveFrame
    //     0x6724d0: mov             SP, fp
    //     0x6724d4: ldp             fp, lr, [SP], #0x10
    // 0x6724d8: ret
    //     0x6724d8: ret             
    // 0x6724dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6724dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6724e0: b               #0x6724cc
  }
  _ []=(/* No info */) {
    // ** addr: 0x672768, size: 0x254
    // 0x672768: EnterFrame
    //     0x672768: stp             fp, lr, [SP, #-0x10]!
    //     0x67276c: mov             fp, SP
    // 0x672770: AllocStack(0x18)
    //     0x672770: sub             SP, SP, #0x18
    // 0x672774: CheckStackOverflow
    //     0x672774: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x672778: cmp             SP, x16
    //     0x67277c: b.ls            #0x672994
    // 0x672780: ldr             x3, [fp, #0x20]
    // 0x672784: LoadField: r2 = r3->field_7
    //     0x672784: ldur            w2, [x3, #7]
    // 0x672788: DecompressPointer r2
    //     0x672788: add             x2, x2, HEAP, lsl #32
    // 0x67278c: ldr             x0, [fp, #0x10]
    // 0x672790: r1 = Null
    //     0x672790: mov             x1, NULL
    // 0x672794: cmp             w2, NULL
    // 0x672798: b.eq            #0x6727bc
    // 0x67279c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x67279c: ldur            w4, [x2, #0x17]
    // 0x6727a0: DecompressPointer r4
    //     0x6727a0: add             x4, x4, HEAP, lsl #32
    // 0x6727a4: r8 = X0 bound XmlNode
    //     0x6727a4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e498] TypeParameter: X0 bound XmlNode
    //     0x6727a8: ldr             x8, [x8, #0x498]
    // 0x6727ac: LoadField: r9 = r4->field_7
    //     0x6727ac: ldur            x9, [x4, #7]
    // 0x6727b0: r3 = Null
    //     0x6727b0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31160] Null
    //     0x6727b4: ldr             x3, [x3, #0x160]
    // 0x6727b8: blr             x9
    // 0x6727bc: ldr             x0, [fp, #0x18]
    // 0x6727c0: r3 = LoadInt32Instr(r0)
    //     0x6727c0: sbfx            x3, x0, #1, #0x1f
    //     0x6727c4: tbz             w0, #0, #0x6727cc
    //     0x6727c8: ldur            x3, [x0, #7]
    // 0x6727cc: mov             x1, x3
    // 0x6727d0: ldr             x2, [fp, #0x20]
    // 0x6727d4: stur            x3, [fp, #-8]
    // 0x6727d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6727d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6727dc: r0 = checkValidIndex()
    //     0x6727dc: bl              #0x6732dc  ; [dart:core] RangeError::checkValidIndex
    // 0x6727e0: ldr             x2, [fp, #0x10]
    // 0x6727e4: r0 = LoadClassIdInstr(r2)
    //     0x6727e4: ldur            x0, [x2, #-1]
    //     0x6727e8: ubfx            x0, x0, #0xc, #0x14
    // 0x6727ec: mov             x1, x2
    // 0x6727f0: r0 = GDT[cid_x0 + -0xf93]()
    //     0x6727f0: sub             lr, x0, #0xf93
    //     0x6727f4: ldr             lr, [x21, lr, lsl #3]
    //     0x6727f8: blr             lr
    // 0x6727fc: r16 = Instance_XmlNodeType
    //     0x6727fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e458] Obj!XmlNodeType@e2d371
    //     0x672800: ldr             x16, [x16, #0x458]
    // 0x672804: cmp             w0, w16
    // 0x672808: b.ne            #0x67283c
    // 0x67280c: ldur            x0, [fp, #-8]
    // 0x672810: add             x3, x0, #1
    // 0x672814: ldr             x1, [fp, #0x20]
    // 0x672818: ldr             x2, [fp, #0x10]
    // 0x67281c: stur            x3, [fp, #-0x10]
    // 0x672820: r0 = _expandFragment()
    //     0x672820: bl              #0x66f264  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::_expandFragment
    // 0x672824: ldr             x1, [fp, #0x20]
    // 0x672828: ldur            x2, [fp, #-8]
    // 0x67282c: ldur            x3, [fp, #-0x10]
    // 0x672830: mov             x5, x0
    // 0x672834: r0 = replaceRange()
    //     0x672834: bl              #0x672a7c  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::replaceRange
    // 0x672838: b               #0x672984
    // 0x67283c: ldr             x4, [fp, #0x20]
    // 0x672840: ldr             x3, [fp, #0x10]
    // 0x672844: ldur            x0, [fp, #-8]
    // 0x672848: LoadField: r2 = r4->field_13
    //     0x672848: ldur            w2, [x4, #0x13]
    // 0x67284c: DecompressPointer r2
    //     0x67284c: add             x2, x2, HEAP, lsl #32
    // 0x672850: r16 = Sentinel
    //     0x672850: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x672854: cmp             w2, w16
    // 0x672858: b.eq            #0x67299c
    // 0x67285c: mov             x1, x3
    // 0x672860: r0 = checkValidType()
    //     0x672860: bl              #0x66f124  ; [package:xml/src/xml/exceptions/type_exception.dart] XmlNodeTypeException::checkValidType
    // 0x672864: ldr             x1, [fp, #0x10]
    // 0x672868: r0 = checkNoParent()
    //     0x672868: bl              #0x66f094  ; [package:xml/src/xml/exceptions/parent_exception.dart] XmlParentException::checkNoParent
    // 0x67286c: ldr             x3, [fp, #0x20]
    // 0x672870: LoadField: r4 = r3->field_b
    //     0x672870: ldur            w4, [x3, #0xb]
    // 0x672874: DecompressPointer r4
    //     0x672874: add             x4, x4, HEAP, lsl #32
    // 0x672878: stur            x4, [fp, #-0x18]
    // 0x67287c: LoadField: r0 = r4->field_b
    //     0x67287c: ldur            w0, [x4, #0xb]
    // 0x672880: r1 = LoadInt32Instr(r0)
    //     0x672880: sbfx            x1, x0, #1, #0x1f
    // 0x672884: mov             x0, x1
    // 0x672888: ldur            x1, [fp, #-8]
    // 0x67288c: cmp             x1, x0
    // 0x672890: b.hs            #0x6729a8
    // 0x672894: LoadField: r0 = r4->field_f
    //     0x672894: ldur            w0, [x4, #0xf]
    // 0x672898: DecompressPointer r0
    //     0x672898: add             x0, x0, HEAP, lsl #32
    // 0x67289c: ldur            x5, [fp, #-8]
    // 0x6728a0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x6728a0: add             x16, x0, x5, lsl #2
    //     0x6728a4: ldur            w1, [x16, #0xf]
    // 0x6728a8: DecompressPointer r1
    //     0x6728a8: add             x1, x1, HEAP, lsl #32
    // 0x6728ac: LoadField: r2 = r3->field_f
    //     0x6728ac: ldur            w2, [x3, #0xf]
    // 0x6728b0: DecompressPointer r2
    //     0x6728b0: add             x2, x2, HEAP, lsl #32
    // 0x6728b4: r16 = Sentinel
    //     0x6728b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6728b8: cmp             w2, w16
    // 0x6728bc: b.eq            #0x6729ac
    // 0x6728c0: r0 = LoadClassIdInstr(r1)
    //     0x6728c0: ldur            x0, [x1, #-1]
    //     0x6728c4: ubfx            x0, x0, #0xc, #0x14
    // 0x6728c8: r0 = GDT[cid_x0 + -0xfa3]()
    //     0x6728c8: sub             lr, x0, #0xfa3
    //     0x6728cc: ldr             lr, [x21, lr, lsl #3]
    //     0x6728d0: blr             lr
    // 0x6728d4: ldur            x3, [fp, #-0x18]
    // 0x6728d8: LoadField: r2 = r3->field_7
    //     0x6728d8: ldur            w2, [x3, #7]
    // 0x6728dc: DecompressPointer r2
    //     0x6728dc: add             x2, x2, HEAP, lsl #32
    // 0x6728e0: ldr             x0, [fp, #0x10]
    // 0x6728e4: r1 = Null
    //     0x6728e4: mov             x1, NULL
    // 0x6728e8: cmp             w2, NULL
    // 0x6728ec: b.eq            #0x67290c
    // 0x6728f0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6728f0: ldur            w4, [x2, #0x17]
    // 0x6728f4: DecompressPointer r4
    //     0x6728f4: add             x4, x4, HEAP, lsl #32
    // 0x6728f8: r8 = X0
    //     0x6728f8: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x6728fc: LoadField: r9 = r4->field_7
    //     0x6728fc: ldur            x9, [x4, #7]
    // 0x672900: r3 = Null
    //     0x672900: add             x3, PP, #0x31, lsl #12  ; [pp+0x31170] Null
    //     0x672904: ldr             x3, [x3, #0x170]
    // 0x672908: blr             x9
    // 0x67290c: ldur            x2, [fp, #-0x18]
    // 0x672910: LoadField: r0 = r2->field_b
    //     0x672910: ldur            w0, [x2, #0xb]
    // 0x672914: r1 = LoadInt32Instr(r0)
    //     0x672914: sbfx            x1, x0, #1, #0x1f
    // 0x672918: mov             x0, x1
    // 0x67291c: ldur            x1, [fp, #-8]
    // 0x672920: cmp             x1, x0
    // 0x672924: b.hs            #0x6729b8
    // 0x672928: LoadField: r1 = r2->field_f
    //     0x672928: ldur            w1, [x2, #0xf]
    // 0x67292c: DecompressPointer r1
    //     0x67292c: add             x1, x1, HEAP, lsl #32
    // 0x672930: ldr             x0, [fp, #0x10]
    // 0x672934: ldur            x2, [fp, #-8]
    // 0x672938: ArrayStore: r1[r2] = r0  ; List_4
    //     0x672938: add             x25, x1, x2, lsl #2
    //     0x67293c: add             x25, x25, #0xf
    //     0x672940: str             w0, [x25]
    //     0x672944: tbz             w0, #0, #0x672960
    //     0x672948: ldurb           w16, [x1, #-1]
    //     0x67294c: ldurb           w17, [x0, #-1]
    //     0x672950: and             x16, x17, x16, lsr #2
    //     0x672954: tst             x16, HEAP, lsr #32
    //     0x672958: b.eq            #0x672960
    //     0x67295c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x672960: ldr             x0, [fp, #0x20]
    // 0x672964: LoadField: r2 = r0->field_f
    //     0x672964: ldur            w2, [x0, #0xf]
    // 0x672968: DecompressPointer r2
    //     0x672968: add             x2, x2, HEAP, lsl #32
    // 0x67296c: ldr             x1, [fp, #0x10]
    // 0x672970: r0 = LoadClassIdInstr(r1)
    //     0x672970: ldur            x0, [x1, #-1]
    //     0x672974: ubfx            x0, x0, #0xc, #0x14
    // 0x672978: r0 = GDT[cid_x0 + -0xf83]()
    //     0x672978: sub             lr, x0, #0xf83
    //     0x67297c: ldr             lr, [x21, lr, lsl #3]
    //     0x672980: blr             lr
    // 0x672984: r0 = Null
    //     0x672984: mov             x0, NULL
    // 0x672988: LeaveFrame
    //     0x672988: mov             SP, fp
    //     0x67298c: ldp             fp, lr, [SP], #0x10
    // 0x672990: ret
    //     0x672990: ret             
    // 0x672994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x672994: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x672998: b               #0x672780
    // 0x67299c: r9 = _nodeTypes
    //     0x67299c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e490] Field <XmlNodeList._nodeTypes@2752054576>: late final (offset: 0x14)
    //     0x6729a0: ldr             x9, [x9, #0x490]
    // 0x6729a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6729a4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x6729a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6729a8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6729ac: r9 = _parent
    //     0x6729ac: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x6729b0: ldr             x9, [x9, #0x450]
    // 0x6729b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6729b4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x6729b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6729b8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ replaceRange(/* No info */) {
    // ** addr: 0x672a7c, size: 0x270
    // 0x672a7c: EnterFrame
    //     0x672a7c: stp             fp, lr, [SP, #-0x10]!
    //     0x672a80: mov             fp, SP
    // 0x672a84: AllocStack(0x38)
    //     0x672a84: sub             SP, SP, #0x38
    // 0x672a88: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r7, fp-0x10 */, dynamic _ /* r2 => r6, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */, dynamic _ /* r5 => r4, fp-0x28 */)
    //     0x672a88: mov             x7, x1
    //     0x672a8c: mov             x6, x2
    //     0x672a90: mov             x4, x5
    //     0x672a94: stur            x5, [fp, #-0x28]
    //     0x672a98: mov             x5, x3
    //     0x672a9c: stur            x1, [fp, #-0x10]
    //     0x672aa0: stur            x2, [fp, #-0x18]
    //     0x672aa4: stur            x3, [fp, #-0x20]
    // 0x672aa8: CheckStackOverflow
    //     0x672aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x672aac: cmp             SP, x16
    //     0x672ab0: b.ls            #0x672cb8
    // 0x672ab4: LoadField: r8 = r7->field_b
    //     0x672ab4: ldur            w8, [x7, #0xb]
    // 0x672ab8: DecompressPointer r8
    //     0x672ab8: add             x8, x8, HEAP, lsl #32
    // 0x672abc: stur            x8, [fp, #-8]
    // 0x672ac0: LoadField: r2 = r8->field_b
    //     0x672ac0: ldur            w2, [x8, #0xb]
    // 0x672ac4: r0 = BoxInt64Instr(r5)
    //     0x672ac4: sbfiz           x0, x5, #1, #0x1f
    //     0x672ac8: cmp             x5, x0, asr #1
    //     0x672acc: b.eq            #0x672ad8
    //     0x672ad0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x672ad4: stur            x5, [x0, #7]
    // 0x672ad8: r3 = LoadInt32Instr(r2)
    //     0x672ad8: sbfx            x3, x2, #1, #0x1f
    // 0x672adc: mov             x1, x6
    // 0x672ae0: mov             x2, x0
    // 0x672ae4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x672ae4: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x672ae8: r0 = checkValidRange()
    //     0x672ae8: bl              #0x60007c  ; [dart:core] RangeError::checkValidRange
    // 0x672aec: ldur            x1, [fp, #-0x10]
    // 0x672af0: ldur            x2, [fp, #-0x28]
    // 0x672af4: r0 = _expandNodes()
    //     0x672af4: bl              #0x66ec8c  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::_expandNodes
    // 0x672af8: mov             x3, x0
    // 0x672afc: stur            x3, [fp, #-0x28]
    // 0x672b00: ldur            x7, [fp, #-0x18]
    // 0x672b04: ldur            x5, [fp, #-0x10]
    // 0x672b08: ldur            x4, [fp, #-0x20]
    // 0x672b0c: ldur            x6, [fp, #-8]
    // 0x672b10: stur            x7, [fp, #-0x30]
    // 0x672b14: CheckStackOverflow
    //     0x672b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x672b18: cmp             SP, x16
    //     0x672b1c: b.ls            #0x672cc0
    // 0x672b20: cmp             x7, x4
    // 0x672b24: b.ge            #0x672b8c
    // 0x672b28: LoadField: r0 = r6->field_b
    //     0x672b28: ldur            w0, [x6, #0xb]
    // 0x672b2c: r1 = LoadInt32Instr(r0)
    //     0x672b2c: sbfx            x1, x0, #1, #0x1f
    // 0x672b30: mov             x0, x1
    // 0x672b34: mov             x1, x7
    // 0x672b38: cmp             x1, x0
    // 0x672b3c: b.hs            #0x672cc8
    // 0x672b40: LoadField: r0 = r6->field_f
    //     0x672b40: ldur            w0, [x6, #0xf]
    // 0x672b44: DecompressPointer r0
    //     0x672b44: add             x0, x0, HEAP, lsl #32
    // 0x672b48: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x672b48: add             x16, x0, x7, lsl #2
    //     0x672b4c: ldur            w1, [x16, #0xf]
    // 0x672b50: DecompressPointer r1
    //     0x672b50: add             x1, x1, HEAP, lsl #32
    // 0x672b54: LoadField: r2 = r5->field_f
    //     0x672b54: ldur            w2, [x5, #0xf]
    // 0x672b58: DecompressPointer r2
    //     0x672b58: add             x2, x2, HEAP, lsl #32
    // 0x672b5c: r16 = Sentinel
    //     0x672b5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x672b60: cmp             w2, w16
    // 0x672b64: b.eq            #0x672ccc
    // 0x672b68: r0 = LoadClassIdInstr(r1)
    //     0x672b68: ldur            x0, [x1, #-1]
    //     0x672b6c: ubfx            x0, x0, #0xc, #0x14
    // 0x672b70: r0 = GDT[cid_x0 + -0xfa3]()
    //     0x672b70: sub             lr, x0, #0xfa3
    //     0x672b74: ldr             lr, [x21, lr, lsl #3]
    //     0x672b78: blr             lr
    // 0x672b7c: ldur            x0, [fp, #-0x30]
    // 0x672b80: add             x7, x0, #1
    // 0x672b84: ldur            x3, [fp, #-0x28]
    // 0x672b88: b               #0x672b04
    // 0x672b8c: mov             x0, x3
    // 0x672b90: ldur            x1, [fp, #-0x10]
    // 0x672b94: ldur            x2, [fp, #-0x18]
    // 0x672b98: ldur            x3, [fp, #-0x20]
    // 0x672b9c: mov             x5, x0
    // 0x672ba0: r0 = replaceRange()
    //     0x672ba0: bl              #0x672cec  ; [package:collection/src/wrappers.dart] DelegatingList::replaceRange
    // 0x672ba4: ldur            x3, [fp, #-0x28]
    // 0x672ba8: LoadField: r4 = r3->field_7
    //     0x672ba8: ldur            w4, [x3, #7]
    // 0x672bac: DecompressPointer r4
    //     0x672bac: add             x4, x4, HEAP, lsl #32
    // 0x672bb0: stur            x4, [fp, #-0x38]
    // 0x672bb4: LoadField: r0 = r3->field_b
    //     0x672bb4: ldur            w0, [x3, #0xb]
    // 0x672bb8: r5 = LoadInt32Instr(r0)
    //     0x672bb8: sbfx            x5, x0, #1, #0x1f
    // 0x672bbc: stur            x5, [fp, #-0x20]
    // 0x672bc0: r0 = 0
    //     0x672bc0: movz            x0, #0
    // 0x672bc4: ldur            x6, [fp, #-0x10]
    // 0x672bc8: CheckStackOverflow
    //     0x672bc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x672bcc: cmp             SP, x16
    //     0x672bd0: b.ls            #0x672cd8
    // 0x672bd4: LoadField: r1 = r3->field_b
    //     0x672bd4: ldur            w1, [x3, #0xb]
    // 0x672bd8: r2 = LoadInt32Instr(r1)
    //     0x672bd8: sbfx            x2, x1, #1, #0x1f
    // 0x672bdc: cmp             x5, x2
    // 0x672be0: b.ne            #0x672c98
    // 0x672be4: cmp             x0, x2
    // 0x672be8: b.ge            #0x672c88
    // 0x672bec: LoadField: r1 = r3->field_f
    //     0x672bec: ldur            w1, [x3, #0xf]
    // 0x672bf0: DecompressPointer r1
    //     0x672bf0: add             x1, x1, HEAP, lsl #32
    // 0x672bf4: ArrayLoad: r7 = r1[r0]  ; Unknown_4
    //     0x672bf4: add             x16, x1, x0, lsl #2
    //     0x672bf8: ldur            w7, [x16, #0xf]
    // 0x672bfc: DecompressPointer r7
    //     0x672bfc: add             x7, x7, HEAP, lsl #32
    // 0x672c00: stur            x7, [fp, #-8]
    // 0x672c04: add             x8, x0, #1
    // 0x672c08: stur            x8, [fp, #-0x18]
    // 0x672c0c: cmp             w7, NULL
    // 0x672c10: b.ne            #0x672c44
    // 0x672c14: mov             x0, x7
    // 0x672c18: mov             x2, x4
    // 0x672c1c: r1 = Null
    //     0x672c1c: mov             x1, NULL
    // 0x672c20: cmp             w2, NULL
    // 0x672c24: b.eq            #0x672c44
    // 0x672c28: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x672c28: ldur            w4, [x2, #0x17]
    // 0x672c2c: DecompressPointer r4
    //     0x672c2c: add             x4, x4, HEAP, lsl #32
    // 0x672c30: r8 = X0
    //     0x672c30: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x672c34: LoadField: r9 = r4->field_7
    //     0x672c34: ldur            x9, [x4, #7]
    // 0x672c38: r3 = Null
    //     0x672c38: add             x3, PP, #0x31, lsl #12  ; [pp+0x31180] Null
    //     0x672c3c: ldr             x3, [x3, #0x180]
    // 0x672c40: blr             x9
    // 0x672c44: ldur            x3, [fp, #-0x10]
    // 0x672c48: ldur            x1, [fp, #-8]
    // 0x672c4c: LoadField: r2 = r3->field_f
    //     0x672c4c: ldur            w2, [x3, #0xf]
    // 0x672c50: DecompressPointer r2
    //     0x672c50: add             x2, x2, HEAP, lsl #32
    // 0x672c54: r16 = Sentinel
    //     0x672c54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x672c58: cmp             w2, w16
    // 0x672c5c: b.eq            #0x672ce0
    // 0x672c60: r0 = LoadClassIdInstr(r1)
    //     0x672c60: ldur            x0, [x1, #-1]
    //     0x672c64: ubfx            x0, x0, #0xc, #0x14
    // 0x672c68: r0 = GDT[cid_x0 + -0xf83]()
    //     0x672c68: sub             lr, x0, #0xf83
    //     0x672c6c: ldr             lr, [x21, lr, lsl #3]
    //     0x672c70: blr             lr
    // 0x672c74: ldur            x0, [fp, #-0x18]
    // 0x672c78: ldur            x3, [fp, #-0x28]
    // 0x672c7c: ldur            x4, [fp, #-0x38]
    // 0x672c80: ldur            x5, [fp, #-0x20]
    // 0x672c84: b               #0x672bc4
    // 0x672c88: r0 = Null
    //     0x672c88: mov             x0, NULL
    // 0x672c8c: LeaveFrame
    //     0x672c8c: mov             SP, fp
    //     0x672c90: ldp             fp, lr, [SP], #0x10
    // 0x672c94: ret
    //     0x672c94: ret             
    // 0x672c98: mov             x0, x3
    // 0x672c9c: r0 = ConcurrentModificationError()
    //     0x672c9c: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x672ca0: mov             x1, x0
    // 0x672ca4: ldur            x0, [fp, #-0x28]
    // 0x672ca8: StoreField: r1->field_b = r0
    //     0x672ca8: stur            w0, [x1, #0xb]
    // 0x672cac: mov             x0, x1
    // 0x672cb0: r0 = Throw()
    //     0x672cb0: bl              #0xec04b8  ; ThrowStub
    // 0x672cb4: brk             #0
    // 0x672cb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x672cb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x672cbc: b               #0x672ab4
    // 0x672cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x672cc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x672cc4: b               #0x672b20
    // 0x672cc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x672cc8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0x672ccc: r9 = _parent
    //     0x672ccc: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x672cd0: ldr             x9, [x9, #0x450]
    // 0x672cd4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x672cd4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x672cd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x672cd8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x672cdc: b               #0x672bd4
    // 0x672ce0: r9 = _parent
    //     0x672ce0: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e450] Field <XmlNodeList._parent@2752054576>: late final (offset: 0x10)
    //     0x672ce4: ldr             x9, [x9, #0x450]
    // 0x672ce8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x672ce8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  dynamic add(dynamic) {
    // ** addr: 0x673460, size: 0x24
    // 0x673460: EnterFrame
    //     0x673460: stp             fp, lr, [SP, #-0x10]!
    //     0x673464: mov             fp, SP
    // 0x673468: ldr             x2, [fp, #0x10]
    // 0x67346c: r1 = Function 'add':.
    //     0x67346c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bfa8] AnonymousClosure: (0x6720dc), in [package:xml/src/xml/utils/node_list.dart] XmlNodeList::add (0x671d18)
    //     0x673470: ldr             x1, [x1, #0xfa8]
    // 0x673474: r0 = AllocateClosure()
    //     0x673474: bl              #0xec1630  ; AllocateClosureStub
    // 0x673478: LeaveFrame
    //     0x673478: mov             SP, fp
    //     0x67347c: ldp             fp, lr, [SP], #0x10
    // 0x673480: ret
    //     0x673480: ret             
  }
  _ initialize(/* No info */) {
    // ** addr: 0xb152b4, size: 0xe4
    // 0xb152b4: EnterFrame
    //     0xb152b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb152b8: mov             fp, SP
    // 0xb152bc: AllocStack(0x20)
    //     0xb152bc: sub             SP, SP, #0x20
    // 0xb152c0: SetupParameters(XmlNodeList<X0 bound XmlNode> this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xb152c0: stur            x1, [fp, #-8]
    //     0xb152c4: mov             x16, x2
    //     0xb152c8: mov             x2, x1
    //     0xb152cc: mov             x1, x16
    //     0xb152d0: mov             x0, x3
    //     0xb152d4: stur            x1, [fp, #-0x10]
    //     0xb152d8: stur            x3, [fp, #-0x18]
    // 0xb152dc: CheckStackOverflow
    //     0xb152dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb152e0: cmp             SP, x16
    //     0xb152e4: b.ls            #0xb15390
    // 0xb152e8: LoadField: r3 = r2->field_f
    //     0xb152e8: ldur            w3, [x2, #0xf]
    // 0xb152ec: DecompressPointer r3
    //     0xb152ec: add             x3, x3, HEAP, lsl #32
    // 0xb152f0: r16 = Sentinel
    //     0xb152f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb152f4: cmp             w3, w16
    // 0xb152f8: b.ne            #0xb15304
    // 0xb152fc: mov             x1, x2
    // 0xb15300: b               #0xb15318
    // 0xb15304: r16 = "_parent@2752054576"
    //     0xb15304: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e4b0] "_parent@2752054576"
    //     0xb15308: ldr             x16, [x16, #0x4b0]
    // 0xb1530c: str             x16, [SP]
    // 0xb15310: r0 = _throwFieldAlreadyInitialized()
    //     0xb15310: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xb15314: ldur            x1, [fp, #-8]
    // 0xb15318: ldur            x0, [fp, #-0x10]
    // 0xb1531c: StoreField: r1->field_f = r0
    //     0xb1531c: stur            w0, [x1, #0xf]
    //     0xb15320: ldurb           w16, [x1, #-1]
    //     0xb15324: ldurb           w17, [x0, #-1]
    //     0xb15328: and             x16, x17, x16, lsr #2
    //     0xb1532c: tst             x16, HEAP, lsr #32
    //     0xb15330: b.eq            #0xb15338
    //     0xb15334: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb15338: LoadField: r0 = r1->field_13
    //     0xb15338: ldur            w0, [x1, #0x13]
    // 0xb1533c: DecompressPointer r0
    //     0xb1533c: add             x0, x0, HEAP, lsl #32
    // 0xb15340: r16 = Sentinel
    //     0xb15340: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb15344: cmp             w0, w16
    // 0xb15348: b.eq            #0xb15360
    // 0xb1534c: r16 = "_nodeTypes@2752054576"
    //     0xb1534c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e4b8] "_nodeTypes@2752054576"
    //     0xb15350: ldr             x16, [x16, #0x4b8]
    // 0xb15354: str             x16, [SP]
    // 0xb15358: r0 = _throwFieldAlreadyInitialized()
    //     0xb15358: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xb1535c: ldur            x1, [fp, #-8]
    // 0xb15360: ldur            x0, [fp, #-0x18]
    // 0xb15364: StoreField: r1->field_13 = r0
    //     0xb15364: stur            w0, [x1, #0x13]
    //     0xb15368: ldurb           w16, [x1, #-1]
    //     0xb1536c: ldurb           w17, [x0, #-1]
    //     0xb15370: and             x16, x17, x16, lsr #2
    //     0xb15374: tst             x16, HEAP, lsr #32
    //     0xb15378: b.eq            #0xb15380
    //     0xb1537c: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xb15380: r0 = Null
    //     0xb15380: mov             x0, NULL
    // 0xb15384: LeaveFrame
    //     0xb15384: mov             SP, fp
    //     0xb15388: ldp             fp, lr, [SP], #0x10
    // 0xb1538c: ret
    //     0xb1538c: ret             
    // 0xb15390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb15390: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15394: b               #0xb152e8
  }
}
