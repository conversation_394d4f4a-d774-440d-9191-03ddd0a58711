// lib: , url: package:xml/src/xml/visitors/normalizer.dart

// class id: 1051321, size: 0x8
class :: {
}

// class id: 219, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlNormalizer&Object&XmlVisitor extends Object
     with XmlVisitor {

  _ visit(/* No info */) {
    // ** addr: 0xc448a4, size: 0x50
    // 0xc448a4: EnterFrame
    //     0xc448a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc448a8: mov             fp, SP
    // 0xc448ac: mov             x16, x2
    // 0xc448b0: mov             x2, x1
    // 0xc448b4: mov             x1, x16
    // 0xc448b8: CheckStackOverflow
    //     0xc448b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc448bc: cmp             SP, x16
    //     0xc448c0: b.ls            #0xc448ec
    // 0xc448c4: r0 = LoadClassIdInstr(r1)
    //     0xc448c4: ldur            x0, [x1, #-1]
    //     0xc448c8: ubfx            x0, x0, #0xc, #0x14
    // 0xc448cc: r0 = GDT[cid_x0 + 0x24d0]()
    //     0xc448cc: movz            x17, #0x24d0
    //     0xc448d0: add             lr, x0, x17
    //     0xc448d4: ldr             lr, [x21, lr, lsl #3]
    //     0xc448d8: blr             lr
    // 0xc448dc: r0 = Null
    //     0xc448dc: mov             x0, NULL
    // 0xc448e0: LeaveFrame
    //     0xc448e0: mov             SP, fp
    //     0xc448e4: ldp             fp, lr, [SP], #0x10
    // 0xc448e8: ret
    //     0xc448e8: ret             
    // 0xc448ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc448ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc448f0: b               #0xc448c4
  }
}
