// lib: , url: package:xml/src/xml/visitors/writer.dart

// class id: 1051324, size: 0x8
class :: {
}

// class id: 220, size: 0x10, field offset: 0x8
class XmlWriter extends _XmlNormalizer&Object&XmlVisitor {

  _ visitName(/* No info */) {
    // ** addr: 0xc453b8, size: 0x68
    // 0xc453b8: EnterFrame
    //     0xc453b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc453bc: mov             fp, SP
    // 0xc453c0: CheckStackOverflow
    //     0xc453c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc453c4: cmp             SP, x16
    //     0xc453c8: b.ls            #0xc45418
    // 0xc453cc: LoadField: r0 = r1->field_7
    //     0xc453cc: ldur            w0, [x1, #7]
    // 0xc453d0: DecompressPointer r0
    //     0xc453d0: add             x0, x0, HEAP, lsl #32
    // 0xc453d4: r1 = LoadClassIdInstr(r2)
    //     0xc453d4: ldur            x1, [x2, #-1]
    //     0xc453d8: ubfx            x1, x1, #0xc, #0x14
    // 0xc453dc: cmp             x1, #0xe2
    // 0xc453e0: b.ne            #0xc453f4
    // 0xc453e4: LoadField: r1 = r2->field_b
    //     0xc453e4: ldur            w1, [x2, #0xb]
    // 0xc453e8: DecompressPointer r1
    //     0xc453e8: add             x1, x1, HEAP, lsl #32
    // 0xc453ec: mov             x2, x1
    // 0xc453f0: b               #0xc45400
    // 0xc453f4: LoadField: r1 = r2->field_13
    //     0xc453f4: ldur            w1, [x2, #0x13]
    // 0xc453f8: DecompressPointer r1
    //     0xc453f8: add             x1, x1, HEAP, lsl #32
    // 0xc453fc: mov             x2, x1
    // 0xc45400: mov             x1, x0
    // 0xc45404: r0 = write()
    //     0xc45404: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xc45408: r0 = Null
    //     0xc45408: mov             x0, NULL
    // 0xc4540c: LeaveFrame
    //     0xc4540c: mov             SP, fp
    //     0xc45410: ldp             fp, lr, [SP], #0x10
    // 0xc45414: ret
    //     0xc45414: ret             
    // 0xc45418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc45418: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4541c: b               #0xc453cc
  }
  _ visitAttribute(/* No info */) {
    // ** addr: 0xce043c, size: 0x98
    // 0xce043c: EnterFrame
    //     0xce043c: stp             fp, lr, [SP, #-0x10]!
    //     0xce0440: mov             fp, SP
    // 0xce0444: AllocStack(0x18)
    //     0xce0444: sub             SP, SP, #0x18
    // 0xce0448: SetupParameters(XmlWriter this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xce0448: mov             x3, x1
    //     0xce044c: mov             x0, x2
    //     0xce0450: stur            x1, [fp, #-8]
    //     0xce0454: stur            x2, [fp, #-0x10]
    // 0xce0458: CheckStackOverflow
    //     0xce0458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce045c: cmp             SP, x16
    //     0xce0460: b.ls            #0xce04cc
    // 0xce0464: LoadField: r2 = r0->field_b
    //     0xce0464: ldur            w2, [x0, #0xb]
    // 0xce0468: DecompressPointer r2
    //     0xce0468: add             x2, x2, HEAP, lsl #32
    // 0xce046c: mov             x1, x3
    // 0xce0470: r0 = visitName()
    //     0xce0470: bl              #0xc453b8  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitName
    // 0xce0474: ldur            x0, [fp, #-8]
    // 0xce0478: LoadField: r3 = r0->field_7
    //     0xce0478: ldur            w3, [x0, #7]
    // 0xce047c: DecompressPointer r3
    //     0xce047c: add             x3, x3, HEAP, lsl #32
    // 0xce0480: mov             x1, x3
    // 0xce0484: stur            x3, [fp, #-0x18]
    // 0xce0488: r2 = "="
    //     0xce0488: ldr             x2, [PP, #0xde8]  ; [pp+0xde8] "="
    // 0xce048c: r0 = write()
    //     0xce048c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce0490: ldur            x0, [fp, #-0x10]
    // 0xce0494: LoadField: r2 = r0->field_f
    //     0xce0494: ldur            w2, [x0, #0xf]
    // 0xce0498: DecompressPointer r2
    //     0xce0498: add             x2, x2, HEAP, lsl #32
    // 0xce049c: LoadField: r3 = r0->field_13
    //     0xce049c: ldur            w3, [x0, #0x13]
    // 0xce04a0: DecompressPointer r3
    //     0xce04a0: add             x3, x3, HEAP, lsl #32
    // 0xce04a4: r1 = Instance_XmlDefaultEntityMapping
    //     0xce04a4: add             x1, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xce04a8: ldr             x1, [x1, #0x5c0]
    // 0xce04ac: r0 = encodeAttributeValueWithQuotes()
    //     0xce04ac: bl              #0xce04d4  ; [package:xml/src/xml/entities/entity_mapping.dart] XmlEntityMapping::encodeAttributeValueWithQuotes
    // 0xce04b0: ldur            x1, [fp, #-0x18]
    // 0xce04b4: mov             x2, x0
    // 0xce04b8: r0 = write()
    //     0xce04b8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce04bc: r0 = Null
    //     0xce04bc: mov             x0, NULL
    // 0xce04c0: LeaveFrame
    //     0xce04c0: mov             SP, fp
    //     0xce04c4: ldp             fp, lr, [SP], #0x10
    // 0xce04c8: ret
    //     0xce04c8: ret             
    // 0xce04cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce04cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce04d0: b               #0xce0464
  }
  _ visitElement(/* No info */) {
    // ** addr: 0xce0aa0, size: 0x104
    // 0xce0aa0: EnterFrame
    //     0xce0aa0: stp             fp, lr, [SP, #-0x10]!
    //     0xce0aa4: mov             fp, SP
    // 0xce0aa8: AllocStack(0x28)
    //     0xce0aa8: sub             SP, SP, #0x28
    // 0xce0aac: SetupParameters(XmlWriter this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xce0aac: mov             x3, x1
    //     0xce0ab0: mov             x0, x2
    //     0xce0ab4: stur            x1, [fp, #-0x10]
    //     0xce0ab8: stur            x2, [fp, #-0x18]
    // 0xce0abc: CheckStackOverflow
    //     0xce0abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0ac0: cmp             SP, x16
    //     0xce0ac4: b.ls            #0xce0b9c
    // 0xce0ac8: LoadField: r4 = r3->field_7
    //     0xce0ac8: ldur            w4, [x3, #7]
    // 0xce0acc: DecompressPointer r4
    //     0xce0acc: add             x4, x4, HEAP, lsl #32
    // 0xce0ad0: mov             x1, x4
    // 0xce0ad4: stur            x4, [fp, #-8]
    // 0xce0ad8: r2 = "<"
    //     0xce0ad8: ldr             x2, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xce0adc: r0 = write()
    //     0xce0adc: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce0ae0: ldur            x0, [fp, #-0x18]
    // 0xce0ae4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xce0ae4: ldur            w3, [x0, #0x17]
    // 0xce0ae8: DecompressPointer r3
    //     0xce0ae8: add             x3, x3, HEAP, lsl #32
    // 0xce0aec: ldur            x1, [fp, #-0x10]
    // 0xce0af0: mov             x2, x3
    // 0xce0af4: stur            x3, [fp, #-0x20]
    // 0xce0af8: r0 = visitName()
    //     0xce0af8: bl              #0xc453b8  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitName
    // 0xce0afc: ldur            x1, [fp, #-0x10]
    // 0xce0b00: ldur            x2, [fp, #-0x18]
    // 0xce0b04: r0 = writeAttributes()
    //     0xce0b04: bl              #0xce0ed8  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::writeAttributes
    // 0xce0b08: ldur            x0, [fp, #-0x18]
    // 0xce0b0c: LoadField: r3 = r0->field_f
    //     0xce0b0c: ldur            w3, [x0, #0xf]
    // 0xce0b10: DecompressPointer r3
    //     0xce0b10: add             x3, x3, HEAP, lsl #32
    // 0xce0b14: stur            x3, [fp, #-0x28]
    // 0xce0b18: LoadField: r1 = r3->field_b
    //     0xce0b18: ldur            w1, [x3, #0xb]
    // 0xce0b1c: DecompressPointer r1
    //     0xce0b1c: add             x1, x1, HEAP, lsl #32
    // 0xce0b20: LoadField: r2 = r1->field_b
    //     0xce0b20: ldur            w2, [x1, #0xb]
    // 0xce0b24: cbnz            w2, #0xce0b48
    // 0xce0b28: LoadField: r1 = r0->field_13
    //     0xce0b28: ldur            w1, [x0, #0x13]
    // 0xce0b2c: DecompressPointer r1
    //     0xce0b2c: add             x1, x1, HEAP, lsl #32
    // 0xce0b30: tbnz            w1, #4, #0xce0b48
    // 0xce0b34: ldur            x1, [fp, #-8]
    // 0xce0b38: r2 = "/>"
    //     0xce0b38: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b88] "/>"
    //     0xce0b3c: ldr             x2, [x2, #0xb88]
    // 0xce0b40: r0 = write()
    //     0xce0b40: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce0b44: b               #0xce0b8c
    // 0xce0b48: ldur            x1, [fp, #-8]
    // 0xce0b4c: r2 = ">"
    //     0xce0b4c: ldr             x2, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xce0b50: r0 = write()
    //     0xce0b50: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce0b54: ldur            x1, [fp, #-0x10]
    // 0xce0b58: ldur            x2, [fp, #-0x28]
    // 0xce0b5c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xce0b5c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xce0b60: r0 = writeIterable()
    //     0xce0b60: bl              #0xce0ba4  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::writeIterable
    // 0xce0b64: ldur            x1, [fp, #-8]
    // 0xce0b68: r2 = "</"
    //     0xce0b68: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b70] "</"
    //     0xce0b6c: ldr             x2, [x2, #0xb70]
    // 0xce0b70: r0 = write()
    //     0xce0b70: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce0b74: ldur            x1, [fp, #-0x10]
    // 0xce0b78: ldur            x2, [fp, #-0x20]
    // 0xce0b7c: r0 = visitName()
    //     0xce0b7c: bl              #0xc453b8  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::visitName
    // 0xce0b80: ldur            x1, [fp, #-8]
    // 0xce0b84: r2 = ">"
    //     0xce0b84: ldr             x2, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xce0b88: r0 = write()
    //     0xce0b88: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce0b8c: r0 = Null
    //     0xce0b8c: mov             x0, NULL
    // 0xce0b90: LeaveFrame
    //     0xce0b90: mov             SP, fp
    //     0xce0b94: ldp             fp, lr, [SP], #0x10
    // 0xce0b98: ret
    //     0xce0b98: ret             
    // 0xce0b9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0b9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0ba0: b               #0xce0ac8
  }
  _ writeIterable(/* No info */) {
    // ** addr: 0xce0ba4, size: 0x334
    // 0xce0ba4: EnterFrame
    //     0xce0ba4: stp             fp, lr, [SP, #-0x10]!
    //     0xce0ba8: mov             fp, SP
    // 0xce0bac: AllocStack(0x48)
    //     0xce0bac: sub             SP, SP, #0x48
    // 0xce0bb0: SetupParameters(XmlWriter this /* r1 => r3, fp-0x30 */, [dynamic _ = Null /* r4, fp-0x40 */])
    //     0xce0bb0: mov             x3, x1
    //     0xce0bb4: stur            x1, [fp, #-0x30]
    //     0xce0bb8: ldur            w0, [x4, #0x13]
    //     0xce0bbc: sub             x1, x0, #4
    //     0xce0bc0: cmp             w1, #2
    //     0xce0bc4: b.lt            #0xce0bd8
    //     0xce0bc8: add             x0, fp, w1, sxtw #2
    //     0xce0bcc: ldr             x0, [x0, #8]
    //     0xce0bd0: mov             x4, x0
    //     0xce0bd4: b               #0xce0bdc
    //     0xce0bd8: mov             x4, NULL
    //     0xce0bdc: stur            x4, [fp, #-0x40]
    // 0xce0be0: CheckStackOverflow
    //     0xce0be0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0be4: cmp             SP, x16
    //     0xce0be8: b.ls            #0xce0ebc
    // 0xce0bec: LoadField: r5 = r2->field_b
    //     0xce0bec: ldur            w5, [x2, #0xb]
    // 0xce0bf0: DecompressPointer r5
    //     0xce0bf0: add             x5, x5, HEAP, lsl #32
    // 0xce0bf4: stur            x5, [fp, #-0x28]
    // 0xce0bf8: LoadField: r6 = r5->field_7
    //     0xce0bf8: ldur            w6, [x5, #7]
    // 0xce0bfc: DecompressPointer r6
    //     0xce0bfc: add             x6, x6, HEAP, lsl #32
    // 0xce0c00: stur            x6, [fp, #-0x20]
    // 0xce0c04: LoadField: r0 = r5->field_b
    //     0xce0c04: ldur            w0, [x5, #0xb]
    // 0xce0c08: r7 = LoadInt32Instr(r0)
    //     0xce0c08: sbfx            x7, x0, #1, #0x1f
    // 0xce0c0c: stur            x7, [fp, #-0x18]
    // 0xce0c10: cmp             x7, #0
    // 0xce0c14: b.le            #0xce0e6c
    // 0xce0c18: mov             x0, x7
    // 0xce0c1c: r1 = 0
    //     0xce0c1c: movz            x1, #0
    // 0xce0c20: cmp             x1, x0
    // 0xce0c24: b.hs            #0xce0ec4
    // 0xce0c28: LoadField: r0 = r5->field_f
    //     0xce0c28: ldur            w0, [x5, #0xf]
    // 0xce0c2c: DecompressPointer r0
    //     0xce0c2c: add             x0, x0, HEAP, lsl #32
    // 0xce0c30: LoadField: r8 = r0->field_f
    //     0xce0c30: ldur            w8, [x0, #0xf]
    // 0xce0c34: DecompressPointer r8
    //     0xce0c34: add             x8, x8, HEAP, lsl #32
    // 0xce0c38: stur            x8, [fp, #-0x38]
    // 0xce0c3c: cmp             w4, NULL
    // 0xce0c40: b.eq            #0xce0c4c
    // 0xce0c44: LoadField: r0 = r4->field_7
    //     0xce0c44: ldur            w0, [x4, #7]
    // 0xce0c48: cbnz            w0, #0xce0d18
    // 0xce0c4c: mov             x4, x8
    // 0xce0c50: r8 = 1
    //     0xce0c50: movz            x8, #0x1
    // 0xce0c54: stur            x8, [fp, #-8]
    // 0xce0c58: stur            x4, [fp, #-0x10]
    // 0xce0c5c: CheckStackOverflow
    //     0xce0c5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0c60: cmp             SP, x16
    //     0xce0c64: b.ls            #0xce0ec8
    // 0xce0c68: cmp             w4, NULL
    // 0xce0c6c: b.ne            #0xce0ca0
    // 0xce0c70: mov             x0, x4
    // 0xce0c74: mov             x2, x6
    // 0xce0c78: r1 = Null
    //     0xce0c78: mov             x1, NULL
    // 0xce0c7c: cmp             w2, NULL
    // 0xce0c80: b.eq            #0xce0ca0
    // 0xce0c84: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xce0c84: ldur            w4, [x2, #0x17]
    // 0xce0c88: DecompressPointer r4
    //     0xce0c88: add             x4, x4, HEAP, lsl #32
    // 0xce0c8c: r8 = X0
    //     0xce0c8c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xce0c90: LoadField: r9 = r4->field_7
    //     0xce0c90: ldur            x9, [x4, #7]
    // 0xce0c94: r3 = Null
    //     0xce0c94: add             x3, PP, #0x43, lsl #12  ; [pp+0x43830] Null
    //     0xce0c98: ldr             x3, [x3, #0x830]
    // 0xce0c9c: blr             x9
    // 0xce0ca0: ldur            x3, [fp, #-0x28]
    // 0xce0ca4: ldur            x4, [fp, #-0x18]
    // 0xce0ca8: ldur            x1, [fp, #-0x10]
    // 0xce0cac: r0 = LoadClassIdInstr(r1)
    //     0xce0cac: ldur            x0, [x1, #-1]
    //     0xce0cb0: ubfx            x0, x0, #0xc, #0x14
    // 0xce0cb4: ldur            x2, [fp, #-0x30]
    // 0xce0cb8: r0 = GDT[cid_x0 + 0x24d0]()
    //     0xce0cb8: movz            x17, #0x24d0
    //     0xce0cbc: add             lr, x0, x17
    //     0xce0cc0: ldr             lr, [x21, lr, lsl #3]
    //     0xce0cc4: blr             lr
    // 0xce0cc8: ldur            x3, [fp, #-0x28]
    // 0xce0ccc: LoadField: r0 = r3->field_b
    //     0xce0ccc: ldur            w0, [x3, #0xb]
    // 0xce0cd0: r1 = LoadInt32Instr(r0)
    //     0xce0cd0: sbfx            x1, x0, #1, #0x1f
    // 0xce0cd4: ldur            x5, [fp, #-0x18]
    // 0xce0cd8: cmp             x5, x1
    // 0xce0cdc: b.ne            #0xce0e7c
    // 0xce0ce0: ldur            x0, [fp, #-8]
    // 0xce0ce4: cmp             x0, x1
    // 0xce0ce8: b.ge            #0xce0e6c
    // 0xce0cec: LoadField: r1 = r3->field_f
    //     0xce0cec: ldur            w1, [x3, #0xf]
    // 0xce0cf0: DecompressPointer r1
    //     0xce0cf0: add             x1, x1, HEAP, lsl #32
    // 0xce0cf4: ArrayLoad: r4 = r1[r0]  ; Unknown_4
    //     0xce0cf4: add             x16, x1, x0, lsl #2
    //     0xce0cf8: ldur            w4, [x16, #0xf]
    // 0xce0cfc: DecompressPointer r4
    //     0xce0cfc: add             x4, x4, HEAP, lsl #32
    // 0xce0d00: add             x8, x0, #1
    // 0xce0d04: mov             x7, x5
    // 0xce0d08: mov             x5, x3
    // 0xce0d0c: ldur            x3, [fp, #-0x30]
    // 0xce0d10: ldur            x6, [fp, #-0x20]
    // 0xce0d14: b               #0xce0c54
    // 0xce0d18: mov             x3, x5
    // 0xce0d1c: mov             x5, x7
    // 0xce0d20: cmp             w8, NULL
    // 0xce0d24: b.ne            #0xce0d58
    // 0xce0d28: mov             x0, x8
    // 0xce0d2c: ldur            x2, [fp, #-0x20]
    // 0xce0d30: r1 = Null
    //     0xce0d30: mov             x1, NULL
    // 0xce0d34: cmp             w2, NULL
    // 0xce0d38: b.eq            #0xce0d58
    // 0xce0d3c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xce0d3c: ldur            w4, [x2, #0x17]
    // 0xce0d40: DecompressPointer r4
    //     0xce0d40: add             x4, x4, HEAP, lsl #32
    // 0xce0d44: r8 = X0
    //     0xce0d44: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xce0d48: LoadField: r9 = r4->field_7
    //     0xce0d48: ldur            x9, [x4, #7]
    // 0xce0d4c: r3 = Null
    //     0xce0d4c: add             x3, PP, #0x43, lsl #12  ; [pp+0x43840] Null
    //     0xce0d50: ldr             x3, [x3, #0x840]
    // 0xce0d54: blr             x9
    // 0xce0d58: ldur            x0, [fp, #-0x30]
    // 0xce0d5c: mov             x1, x0
    // 0xce0d60: ldur            x2, [fp, #-0x38]
    // 0xce0d64: r0 = visit()
    //     0xce0d64: bl              #0xc448a4  ; [package:xml/src/xml/visitors/normalizer.dart] _XmlNormalizer&Object&XmlVisitor::visit
    // 0xce0d68: ldur            x2, [fp, #-0x30]
    // 0xce0d6c: LoadField: r1 = r2->field_7
    //     0xce0d6c: ldur            w1, [x2, #7]
    // 0xce0d70: DecompressPointer r1
    //     0xce0d70: add             x1, x1, HEAP, lsl #32
    // 0xce0d74: stur            x1, [fp, #-0x38]
    // 0xce0d78: r0 = 1
    //     0xce0d78: movz            x0, #0x1
    // 0xce0d7c: ldur            x3, [fp, #-0x40]
    // 0xce0d80: ldur            x4, [fp, #-0x28]
    // 0xce0d84: ldur            x5, [fp, #-0x18]
    // 0xce0d88: CheckStackOverflow
    //     0xce0d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0d8c: cmp             SP, x16
    //     0xce0d90: b.ls            #0xce0ed0
    // 0xce0d94: LoadField: r6 = r4->field_b
    //     0xce0d94: ldur            w6, [x4, #0xb]
    // 0xce0d98: r7 = LoadInt32Instr(r6)
    //     0xce0d98: sbfx            x7, x6, #1, #0x1f
    // 0xce0d9c: cmp             x5, x7
    // 0xce0da0: b.ne            #0xce0e9c
    // 0xce0da4: cmp             x0, x7
    // 0xce0da8: b.ge            #0xce0e6c
    // 0xce0dac: LoadField: r6 = r4->field_f
    //     0xce0dac: ldur            w6, [x4, #0xf]
    // 0xce0db0: DecompressPointer r6
    //     0xce0db0: add             x6, x6, HEAP, lsl #32
    // 0xce0db4: ArrayLoad: r7 = r6[r0]  ; Unknown_4
    //     0xce0db4: add             x16, x6, x0, lsl #2
    //     0xce0db8: ldur            w7, [x16, #0xf]
    // 0xce0dbc: DecompressPointer r7
    //     0xce0dbc: add             x7, x7, HEAP, lsl #32
    // 0xce0dc0: stur            x7, [fp, #-0x10]
    // 0xce0dc4: add             x6, x0, #1
    // 0xce0dc8: stur            x6, [fp, #-8]
    // 0xce0dcc: r0 = LoadClassIdInstr(r3)
    //     0xce0dcc: ldur            x0, [x3, #-1]
    //     0xce0dd0: ubfx            x0, x0, #0xc, #0x14
    // 0xce0dd4: str             x3, [SP]
    // 0xce0dd8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xce0dd8: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xce0ddc: r0 = GDT[cid_x0 + 0x2b03]()
    //     0xce0ddc: movz            x17, #0x2b03
    //     0xce0de0: add             lr, x0, x17
    //     0xce0de4: ldr             lr, [x21, lr, lsl #3]
    //     0xce0de8: blr             lr
    // 0xce0dec: LoadField: r1 = r0->field_7
    //     0xce0dec: ldur            w1, [x0, #7]
    // 0xce0df0: cbz             w1, #0xce0e00
    // 0xce0df4: ldur            x1, [fp, #-0x38]
    // 0xce0df8: mov             x2, x0
    // 0xce0dfc: r0 = _writeString()
    //     0xce0dfc: bl              #0x600d08  ; [dart:core] StringBuffer::_writeString
    // 0xce0e00: ldur            x3, [fp, #-0x10]
    // 0xce0e04: cmp             w3, NULL
    // 0xce0e08: b.ne            #0xce0e3c
    // 0xce0e0c: mov             x0, x3
    // 0xce0e10: ldur            x2, [fp, #-0x20]
    // 0xce0e14: r1 = Null
    //     0xce0e14: mov             x1, NULL
    // 0xce0e18: cmp             w2, NULL
    // 0xce0e1c: b.eq            #0xce0e3c
    // 0xce0e20: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xce0e20: ldur            w4, [x2, #0x17]
    // 0xce0e24: DecompressPointer r4
    //     0xce0e24: add             x4, x4, HEAP, lsl #32
    // 0xce0e28: r8 = X0
    //     0xce0e28: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0xce0e2c: LoadField: r9 = r4->field_7
    //     0xce0e2c: ldur            x9, [x4, #7]
    // 0xce0e30: r3 = Null
    //     0xce0e30: add             x3, PP, #0x43, lsl #12  ; [pp+0x43850] Null
    //     0xce0e34: ldr             x3, [x3, #0x850]
    // 0xce0e38: blr             x9
    // 0xce0e3c: ldur            x1, [fp, #-0x10]
    // 0xce0e40: r0 = LoadClassIdInstr(r1)
    //     0xce0e40: ldur            x0, [x1, #-1]
    //     0xce0e44: ubfx            x0, x0, #0xc, #0x14
    // 0xce0e48: ldur            x2, [fp, #-0x30]
    // 0xce0e4c: r0 = GDT[cid_x0 + 0x24d0]()
    //     0xce0e4c: movz            x17, #0x24d0
    //     0xce0e50: add             lr, x0, x17
    //     0xce0e54: ldr             lr, [x21, lr, lsl #3]
    //     0xce0e58: blr             lr
    // 0xce0e5c: ldur            x0, [fp, #-8]
    // 0xce0e60: ldur            x2, [fp, #-0x30]
    // 0xce0e64: ldur            x1, [fp, #-0x38]
    // 0xce0e68: b               #0xce0d7c
    // 0xce0e6c: r0 = Null
    //     0xce0e6c: mov             x0, NULL
    // 0xce0e70: LeaveFrame
    //     0xce0e70: mov             SP, fp
    //     0xce0e74: ldp             fp, lr, [SP], #0x10
    // 0xce0e78: ret
    //     0xce0e78: ret             
    // 0xce0e7c: mov             x0, x3
    // 0xce0e80: r0 = ConcurrentModificationError()
    //     0xce0e80: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xce0e84: mov             x1, x0
    // 0xce0e88: ldur            x0, [fp, #-0x28]
    // 0xce0e8c: StoreField: r1->field_b = r0
    //     0xce0e8c: stur            w0, [x1, #0xb]
    // 0xce0e90: mov             x0, x1
    // 0xce0e94: r0 = Throw()
    //     0xce0e94: bl              #0xec04b8  ; ThrowStub
    // 0xce0e98: brk             #0
    // 0xce0e9c: mov             x0, x4
    // 0xce0ea0: r0 = ConcurrentModificationError()
    //     0xce0ea0: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xce0ea4: mov             x1, x0
    // 0xce0ea8: ldur            x0, [fp, #-0x28]
    // 0xce0eac: StoreField: r1->field_b = r0
    //     0xce0eac: stur            w0, [x1, #0xb]
    // 0xce0eb0: mov             x0, x1
    // 0xce0eb4: r0 = Throw()
    //     0xce0eb4: bl              #0xec04b8  ; ThrowStub
    // 0xce0eb8: brk             #0
    // 0xce0ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0ebc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0ec0: b               #0xce0bec
    // 0xce0ec4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xce0ec4: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
    // 0xce0ec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0ec8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0ecc: b               #0xce0c68
    // 0xce0ed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0ed0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0ed4: b               #0xce0d94
  }
  _ writeAttributes(/* No info */) {
    // ** addr: 0xce0ed8, size: 0xac
    // 0xce0ed8: EnterFrame
    //     0xce0ed8: stp             fp, lr, [SP, #-0x10]!
    //     0xce0edc: mov             fp, SP
    // 0xce0ee0: AllocStack(0x18)
    //     0xce0ee0: sub             SP, SP, #0x18
    // 0xce0ee4: SetupParameters(XmlWriter this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xce0ee4: mov             x3, x1
    //     0xce0ee8: stur            x1, [fp, #-8]
    //     0xce0eec: stur            x2, [fp, #-0x10]
    // 0xce0ef0: CheckStackOverflow
    //     0xce0ef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce0ef4: cmp             SP, x16
    //     0xce0ef8: b.ls            #0xce0f7c
    // 0xce0efc: r0 = LoadClassIdInstr(r2)
    //     0xce0efc: ldur            x0, [x2, #-1]
    //     0xce0f00: ubfx            x0, x0, #0xc, #0x14
    // 0xce0f04: mov             x1, x2
    // 0xce0f08: r0 = GDT[cid_x0 + -0xf3a]()
    //     0xce0f08: sub             lr, x0, #0xf3a
    //     0xce0f0c: ldr             lr, [x21, lr, lsl #3]
    //     0xce0f10: blr             lr
    // 0xce0f14: LoadField: r1 = r0->field_b
    //     0xce0f14: ldur            w1, [x0, #0xb]
    // 0xce0f18: DecompressPointer r1
    //     0xce0f18: add             x1, x1, HEAP, lsl #32
    // 0xce0f1c: LoadField: r0 = r1->field_b
    //     0xce0f1c: ldur            w0, [x1, #0xb]
    // 0xce0f20: cbz             w0, #0xce0f6c
    // 0xce0f24: ldur            x3, [fp, #-8]
    // 0xce0f28: ldur            x0, [fp, #-0x10]
    // 0xce0f2c: LoadField: r1 = r3->field_7
    //     0xce0f2c: ldur            w1, [x3, #7]
    // 0xce0f30: DecompressPointer r1
    //     0xce0f30: add             x1, x1, HEAP, lsl #32
    // 0xce0f34: r2 = " "
    //     0xce0f34: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xce0f38: r0 = write()
    //     0xce0f38: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce0f3c: ldur            x1, [fp, #-0x10]
    // 0xce0f40: r0 = LoadClassIdInstr(r1)
    //     0xce0f40: ldur            x0, [x1, #-1]
    //     0xce0f44: ubfx            x0, x0, #0xc, #0x14
    // 0xce0f48: r0 = GDT[cid_x0 + -0xf3a]()
    //     0xce0f48: sub             lr, x0, #0xf3a
    //     0xce0f4c: ldr             lr, [x21, lr, lsl #3]
    //     0xce0f50: blr             lr
    // 0xce0f54: r16 = " "
    //     0xce0f54: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xce0f58: str             x16, [SP]
    // 0xce0f5c: ldur            x1, [fp, #-8]
    // 0xce0f60: mov             x2, x0
    // 0xce0f64: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xce0f64: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xce0f68: r0 = writeIterable()
    //     0xce0f68: bl              #0xce0ba4  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::writeIterable
    // 0xce0f6c: r0 = Null
    //     0xce0f6c: mov             x0, NULL
    // 0xce0f70: LeaveFrame
    //     0xce0f70: mov             SP, fp
    //     0xce0f74: ldp             fp, lr, [SP], #0x10
    // 0xce0f78: ret
    //     0xce0f78: ret             
    // 0xce0f7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce0f7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce0f80: b               #0xce0efc
  }
  _ visitCDATA(/* No info */) {
    // ** addr: 0xce1108, size: 0x78
    // 0xce1108: EnterFrame
    //     0xce1108: stp             fp, lr, [SP, #-0x10]!
    //     0xce110c: mov             fp, SP
    // 0xce1110: AllocStack(0x10)
    //     0xce1110: sub             SP, SP, #0x10
    // 0xce1114: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xce1114: mov             x0, x2
    //     0xce1118: stur            x2, [fp, #-0x10]
    // 0xce111c: CheckStackOverflow
    //     0xce111c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1120: cmp             SP, x16
    //     0xce1124: b.ls            #0xce1178
    // 0xce1128: LoadField: r3 = r1->field_7
    //     0xce1128: ldur            w3, [x1, #7]
    // 0xce112c: DecompressPointer r3
    //     0xce112c: add             x3, x3, HEAP, lsl #32
    // 0xce1130: mov             x1, x3
    // 0xce1134: stur            x3, [fp, #-8]
    // 0xce1138: r2 = "<![CDATA["
    //     0xce1138: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b20] "<![CDATA["
    //     0xce113c: ldr             x2, [x2, #0xb20]
    // 0xce1140: r0 = write()
    //     0xce1140: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1144: ldur            x0, [fp, #-0x10]
    // 0xce1148: LoadField: r2 = r0->field_b
    //     0xce1148: ldur            w2, [x0, #0xb]
    // 0xce114c: DecompressPointer r2
    //     0xce114c: add             x2, x2, HEAP, lsl #32
    // 0xce1150: ldur            x1, [fp, #-8]
    // 0xce1154: r0 = write()
    //     0xce1154: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1158: ldur            x1, [fp, #-8]
    // 0xce115c: r2 = "]]>"
    //     0xce115c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b28] "]]>"
    //     0xce1160: ldr             x2, [x2, #0xb28]
    // 0xce1164: r0 = write()
    //     0xce1164: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1168: r0 = Null
    //     0xce1168: mov             x0, NULL
    // 0xce116c: LeaveFrame
    //     0xce116c: mov             SP, fp
    //     0xce1170: ldp             fp, lr, [SP], #0x10
    // 0xce1174: ret
    //     0xce1174: ret             
    // 0xce1178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1178: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce117c: b               #0xce1128
  }
  _ visitComment(/* No info */) {
    // ** addr: 0xce11bc, size: 0x78
    // 0xce11bc: EnterFrame
    //     0xce11bc: stp             fp, lr, [SP, #-0x10]!
    //     0xce11c0: mov             fp, SP
    // 0xce11c4: AllocStack(0x10)
    //     0xce11c4: sub             SP, SP, #0x10
    // 0xce11c8: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xce11c8: mov             x0, x2
    //     0xce11cc: stur            x2, [fp, #-0x10]
    // 0xce11d0: CheckStackOverflow
    //     0xce11d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce11d4: cmp             SP, x16
    //     0xce11d8: b.ls            #0xce122c
    // 0xce11dc: LoadField: r3 = r1->field_7
    //     0xce11dc: ldur            w3, [x1, #7]
    // 0xce11e0: DecompressPointer r3
    //     0xce11e0: add             x3, x3, HEAP, lsl #32
    // 0xce11e4: mov             x1, x3
    // 0xce11e8: stur            x3, [fp, #-8]
    // 0xce11ec: r2 = "<!--"
    //     0xce11ec: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b48] "<!--"
    //     0xce11f0: ldr             x2, [x2, #0xb48]
    // 0xce11f4: r0 = write()
    //     0xce11f4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce11f8: ldur            x0, [fp, #-0x10]
    // 0xce11fc: LoadField: r2 = r0->field_b
    //     0xce11fc: ldur            w2, [x0, #0xb]
    // 0xce1200: DecompressPointer r2
    //     0xce1200: add             x2, x2, HEAP, lsl #32
    // 0xce1204: ldur            x1, [fp, #-8]
    // 0xce1208: r0 = write()
    //     0xce1208: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce120c: ldur            x1, [fp, #-8]
    // 0xce1210: r2 = "-->"
    //     0xce1210: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b50] "-->"
    //     0xce1214: ldr             x2, [x2, #0xb50]
    // 0xce1218: r0 = write()
    //     0xce1218: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce121c: r0 = Null
    //     0xce121c: mov             x0, NULL
    // 0xce1220: LeaveFrame
    //     0xce1220: mov             SP, fp
    //     0xce1224: ldp             fp, lr, [SP], #0x10
    // 0xce1228: ret
    //     0xce1228: ret             
    // 0xce122c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce122c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1230: b               #0xce11dc
  }
  _ visitProcessing(/* No info */) {
    // ** addr: 0xce1270, size: 0xac
    // 0xce1270: EnterFrame
    //     0xce1270: stp             fp, lr, [SP, #-0x10]!
    //     0xce1274: mov             fp, SP
    // 0xce1278: AllocStack(0x10)
    //     0xce1278: sub             SP, SP, #0x10
    // 0xce127c: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xce127c: mov             x0, x2
    //     0xce1280: stur            x2, [fp, #-0x10]
    // 0xce1284: CheckStackOverflow
    //     0xce1284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1288: cmp             SP, x16
    //     0xce128c: b.ls            #0xce1314
    // 0xce1290: LoadField: r3 = r1->field_7
    //     0xce1290: ldur            w3, [x1, #7]
    // 0xce1294: DecompressPointer r3
    //     0xce1294: add             x3, x3, HEAP, lsl #32
    // 0xce1298: mov             x1, x3
    // 0xce129c: stur            x3, [fp, #-8]
    // 0xce12a0: r2 = "<\?"
    //     0xce12a0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26a50] "<\?"
    //     0xce12a4: ldr             x2, [x2, #0xa50]
    // 0xce12a8: r0 = write()
    //     0xce12a8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce12ac: ldur            x0, [fp, #-0x10]
    // 0xce12b0: LoadField: r2 = r0->field_f
    //     0xce12b0: ldur            w2, [x0, #0xf]
    // 0xce12b4: DecompressPointer r2
    //     0xce12b4: add             x2, x2, HEAP, lsl #32
    // 0xce12b8: ldur            x1, [fp, #-8]
    // 0xce12bc: r0 = write()
    //     0xce12bc: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce12c0: ldur            x0, [fp, #-0x10]
    // 0xce12c4: LoadField: r1 = r0->field_b
    //     0xce12c4: ldur            w1, [x0, #0xb]
    // 0xce12c8: DecompressPointer r1
    //     0xce12c8: add             x1, x1, HEAP, lsl #32
    // 0xce12cc: LoadField: r2 = r1->field_7
    //     0xce12cc: ldur            w2, [x1, #7]
    // 0xce12d0: cbz             w2, #0xce12f4
    // 0xce12d4: ldur            x1, [fp, #-8]
    // 0xce12d8: r2 = " "
    //     0xce12d8: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xce12dc: r0 = write()
    //     0xce12dc: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce12e0: ldur            x0, [fp, #-0x10]
    // 0xce12e4: LoadField: r2 = r0->field_b
    //     0xce12e4: ldur            w2, [x0, #0xb]
    // 0xce12e8: DecompressPointer r2
    //     0xce12e8: add             x2, x2, HEAP, lsl #32
    // 0xce12ec: ldur            x1, [fp, #-8]
    // 0xce12f0: r0 = write()
    //     0xce12f0: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce12f4: ldur            x1, [fp, #-8]
    // 0xce12f8: r2 = "\?>"
    //     0xce12f8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26a58] "\?>"
    //     0xce12fc: ldr             x2, [x2, #0xa58]
    // 0xce1300: r0 = write()
    //     0xce1300: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1304: r0 = Null
    //     0xce1304: mov             x0, NULL
    // 0xce1308: LeaveFrame
    //     0xce1308: mov             SP, fp
    //     0xce130c: ldp             fp, lr, [SP], #0x10
    // 0xce1310: ret
    //     0xce1310: ret             
    // 0xce1314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1314: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1318: b               #0xce1290
  }
  _ visitText(/* No info */) {
    // ** addr: 0xce1358, size: 0x60
    // 0xce1358: EnterFrame
    //     0xce1358: stp             fp, lr, [SP, #-0x10]!
    //     0xce135c: mov             fp, SP
    // 0xce1360: AllocStack(0x8)
    //     0xce1360: sub             SP, SP, #8
    // 0xce1364: CheckStackOverflow
    //     0xce1364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce1368: cmp             SP, x16
    //     0xce136c: b.ls            #0xce13b0
    // 0xce1370: LoadField: r0 = r1->field_7
    //     0xce1370: ldur            w0, [x1, #7]
    // 0xce1374: DecompressPointer r0
    //     0xce1374: add             x0, x0, HEAP, lsl #32
    // 0xce1378: stur            x0, [fp, #-8]
    // 0xce137c: LoadField: r1 = r2->field_b
    //     0xce137c: ldur            w1, [x2, #0xb]
    // 0xce1380: DecompressPointer r1
    //     0xce1380: add             x1, x1, HEAP, lsl #32
    // 0xce1384: mov             x2, x1
    // 0xce1388: r1 = Instance_XmlDefaultEntityMapping
    //     0xce1388: add             x1, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xce138c: ldr             x1, [x1, #0x5c0]
    // 0xce1390: r0 = encodeText()
    //     0xce1390: bl              #0xce13b8  ; [package:xml/src/xml/entities/default_mapping.dart] XmlDefaultEntityMapping::encodeText
    // 0xce1394: ldur            x1, [fp, #-8]
    // 0xce1398: mov             x2, x0
    // 0xce139c: r0 = write()
    //     0xce139c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce13a0: r0 = Null
    //     0xce13a0: mov             x0, NULL
    // 0xce13a4: LeaveFrame
    //     0xce13a4: mov             SP, fp
    //     0xce13a8: ldp             fp, lr, [SP], #0x10
    // 0xce13ac: ret
    //     0xce13ac: ret             
    // 0xce13b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce13b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce13b4: b               #0xce1370
  }
  _ visitDeclaration(/* No info */) {
    // ** addr: 0xce15dc, size: 0x78
    // 0xce15dc: EnterFrame
    //     0xce15dc: stp             fp, lr, [SP, #-0x10]!
    //     0xce15e0: mov             fp, SP
    // 0xce15e4: AllocStack(0x18)
    //     0xce15e4: sub             SP, SP, #0x18
    // 0xce15e8: SetupParameters(XmlWriter this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xce15e8: mov             x3, x1
    //     0xce15ec: mov             x0, x2
    //     0xce15f0: stur            x1, [fp, #-0x10]
    //     0xce15f4: stur            x2, [fp, #-0x18]
    // 0xce15f8: CheckStackOverflow
    //     0xce15f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce15fc: cmp             SP, x16
    //     0xce1600: b.ls            #0xce164c
    // 0xce1604: LoadField: r4 = r3->field_7
    //     0xce1604: ldur            w4, [x3, #7]
    // 0xce1608: DecompressPointer r4
    //     0xce1608: add             x4, x4, HEAP, lsl #32
    // 0xce160c: mov             x1, x4
    // 0xce1610: stur            x4, [fp, #-8]
    // 0xce1614: r2 = "<\?xml"
    //     0xce1614: add             x2, PP, #0x26, lsl #12  ; [pp+0x26ab0] "<\?xml"
    //     0xce1618: ldr             x2, [x2, #0xab0]
    // 0xce161c: r0 = write()
    //     0xce161c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1620: ldur            x1, [fp, #-0x10]
    // 0xce1624: ldur            x2, [fp, #-0x18]
    // 0xce1628: r0 = writeAttributes()
    //     0xce1628: bl              #0xce0ed8  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::writeAttributes
    // 0xce162c: ldur            x1, [fp, #-8]
    // 0xce1630: r2 = "\?>"
    //     0xce1630: add             x2, PP, #0x26, lsl #12  ; [pp+0x26a58] "\?>"
    //     0xce1634: ldr             x2, [x2, #0xa58]
    // 0xce1638: r0 = write()
    //     0xce1638: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce163c: r0 = Null
    //     0xce163c: mov             x0, NULL
    // 0xce1640: LeaveFrame
    //     0xce1640: mov             SP, fp
    //     0xce1644: ldp             fp, lr, [SP], #0x10
    // 0xce1648: ret
    //     0xce1648: ret             
    // 0xce164c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce164c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1650: b               #0xce1604
  }
  _ visitDoctype(/* No info */) {
    // ** addr: 0xce1690, size: 0xf8
    // 0xce1690: EnterFrame
    //     0xce1690: stp             fp, lr, [SP, #-0x10]!
    //     0xce1694: mov             fp, SP
    // 0xce1698: AllocStack(0x18)
    //     0xce1698: sub             SP, SP, #0x18
    // 0xce169c: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xce169c: mov             x0, x2
    //     0xce16a0: stur            x2, [fp, #-0x10]
    // 0xce16a4: CheckStackOverflow
    //     0xce16a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce16a8: cmp             SP, x16
    //     0xce16ac: b.ls            #0xce1780
    // 0xce16b0: LoadField: r3 = r1->field_7
    //     0xce16b0: ldur            w3, [x1, #7]
    // 0xce16b4: DecompressPointer r3
    //     0xce16b4: add             x3, x3, HEAP, lsl #32
    // 0xce16b8: mov             x1, x3
    // 0xce16bc: stur            x3, [fp, #-8]
    // 0xce16c0: r2 = "<!DOCTYPE"
    //     0xce16c0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26670] "<!DOCTYPE"
    //     0xce16c4: ldr             x2, [x2, #0x670]
    // 0xce16c8: r0 = write()
    //     0xce16c8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce16cc: ldur            x1, [fp, #-8]
    // 0xce16d0: r2 = " "
    //     0xce16d0: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xce16d4: r0 = write()
    //     0xce16d4: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce16d8: ldur            x0, [fp, #-0x10]
    // 0xce16dc: LoadField: r2 = r0->field_b
    //     0xce16dc: ldur            w2, [x0, #0xb]
    // 0xce16e0: DecompressPointer r2
    //     0xce16e0: add             x2, x2, HEAP, lsl #32
    // 0xce16e4: ldur            x1, [fp, #-8]
    // 0xce16e8: r0 = write()
    //     0xce16e8: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce16ec: ldur            x0, [fp, #-0x10]
    // 0xce16f0: LoadField: r3 = r0->field_f
    //     0xce16f0: ldur            w3, [x0, #0xf]
    // 0xce16f4: DecompressPointer r3
    //     0xce16f4: add             x3, x3, HEAP, lsl #32
    // 0xce16f8: stur            x3, [fp, #-0x18]
    // 0xce16fc: cmp             w3, NULL
    // 0xce1700: b.eq            #0xce171c
    // 0xce1704: ldur            x1, [fp, #-8]
    // 0xce1708: r2 = " "
    //     0xce1708: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xce170c: r0 = write()
    //     0xce170c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1710: ldur            x1, [fp, #-8]
    // 0xce1714: ldur            x2, [fp, #-0x18]
    // 0xce1718: r0 = write()
    //     0xce1718: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce171c: ldur            x0, [fp, #-0x10]
    // 0xce1720: LoadField: r3 = r0->field_13
    //     0xce1720: ldur            w3, [x0, #0x13]
    // 0xce1724: DecompressPointer r3
    //     0xce1724: add             x3, x3, HEAP, lsl #32
    // 0xce1728: stur            x3, [fp, #-0x18]
    // 0xce172c: cmp             w3, NULL
    // 0xce1730: b.eq            #0xce1764
    // 0xce1734: ldur            x1, [fp, #-8]
    // 0xce1738: r2 = " "
    //     0xce1738: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xce173c: r0 = write()
    //     0xce173c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1740: ldur            x1, [fp, #-8]
    // 0xce1744: r2 = "["
    //     0xce1744: ldr             x2, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xce1748: r0 = write()
    //     0xce1748: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce174c: ldur            x1, [fp, #-8]
    // 0xce1750: ldur            x2, [fp, #-0x18]
    // 0xce1754: r0 = write()
    //     0xce1754: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1758: ldur            x1, [fp, #-8]
    // 0xce175c: r2 = "]"
    //     0xce175c: ldr             x2, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xce1760: r0 = write()
    //     0xce1760: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1764: ldur            x1, [fp, #-8]
    // 0xce1768: r2 = ">"
    //     0xce1768: ldr             x2, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xce176c: r0 = write()
    //     0xce176c: bl              #0xd5bc18  ; [dart:core] StringBuffer::write
    // 0xce1770: r0 = Null
    //     0xce1770: mov             x0, NULL
    // 0xce1774: LeaveFrame
    //     0xce1774: mov             SP, fp
    //     0xce1778: ldp             fp, lr, [SP], #0x10
    // 0xce177c: ret
    //     0xce177c: ret             
    // 0xce1780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce1780: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1784: b               #0xce16b0
  }
  _ visitDocument(/* No info */) {
    // ** addr: 0xce17c4, size: 0x40
    // 0xce17c4: EnterFrame
    //     0xce17c4: stp             fp, lr, [SP, #-0x10]!
    //     0xce17c8: mov             fp, SP
    // 0xce17cc: CheckStackOverflow
    //     0xce17cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xce17d0: cmp             SP, x16
    //     0xce17d4: b.ls            #0xce17fc
    // 0xce17d8: LoadField: r0 = r2->field_7
    //     0xce17d8: ldur            w0, [x2, #7]
    // 0xce17dc: DecompressPointer r0
    //     0xce17dc: add             x0, x0, HEAP, lsl #32
    // 0xce17e0: mov             x2, x0
    // 0xce17e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xce17e4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xce17e8: r0 = writeIterable()
    //     0xce17e8: bl              #0xce0ba4  ; [package:xml/src/xml/visitors/writer.dart] XmlWriter::writeIterable
    // 0xce17ec: r0 = Null
    //     0xce17ec: mov             x0, NULL
    // 0xce17f0: LeaveFrame
    //     0xce17f0: mov             SP, fp
    //     0xce17f4: ldp             fp, lr, [SP], #0x10
    // 0xce17f8: ret
    //     0xce17f8: ret             
    // 0xce17fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xce17fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xce1800: b               #0xce17d8
  }
}
