// lib: , url: package:xml/src/xml_events/annotations/annotator.dart

// class id: 1051325, size: 0x8
class :: {
}

// class id: 217, size: 0x24, field offset: 0x8
class XmlAnnotator extends Object {

  _ close(/* No info */) {
    // ** addr: 0x7514a8, size: 0x12c
    // 0x7514a8: EnterFrame
    //     0x7514a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7514ac: mov             fp, SP
    // 0x7514b0: AllocStack(0x20)
    //     0x7514b0: sub             SP, SP, #0x20
    // 0x7514b4: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */)
    //     0x7514b4: mov             x5, x3
    //     0x7514b8: stur            x3, [fp, #-0x10]
    //     0x7514bc: mov             x3, x2
    //     0x7514c0: stur            x2, [fp, #-8]
    // 0x7514c4: CheckStackOverflow
    //     0x7514c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7514c8: cmp             SP, x16
    //     0x7514cc: b.ls            #0x7515cc
    // 0x7514d0: LoadField: r0 = r1->field_7
    //     0x7514d0: ldur            w0, [x1, #7]
    // 0x7514d4: DecompressPointer r0
    //     0x7514d4: add             x0, x0, HEAP, lsl #32
    // 0x7514d8: tbnz            w0, #4, #0x7514ec
    // 0x7514dc: LoadField: r0 = r1->field_1f
    //     0x7514dc: ldur            w0, [x1, #0x1f]
    // 0x7514e0: DecompressPointer r0
    //     0x7514e0: add             x0, x0, HEAP, lsl #32
    // 0x7514e4: LoadField: r2 = r0->field_b
    //     0x7514e4: ldur            w2, [x0, #0xb]
    // 0x7514e8: cbnz            w2, #0x75155c
    // 0x7514ec: LoadField: r0 = r1->field_b
    //     0x7514ec: ldur            w0, [x1, #0xb]
    // 0x7514f0: DecompressPointer r0
    //     0x7514f0: add             x0, x0, HEAP, lsl #32
    // 0x7514f4: tbnz            w0, #4, #0x75154c
    // 0x7514f8: LoadField: r0 = r1->field_1b
    //     0x7514f8: ldur            w0, [x1, #0x1b]
    // 0x7514fc: DecompressPointer r0
    //     0x7514fc: add             x0, x0, HEAP, lsl #32
    // 0x751500: r16 = <XmlStartElementEvent>
    //     0x751500: add             x16, PP, #0x26, lsl #12  ; [pp+0x264a0] TypeArguments: <XmlStartElementEvent>
    //     0x751504: ldr             x16, [x16, #0x4a0]
    // 0x751508: stp             x0, x16, [SP]
    // 0x75150c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x75150c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x751510: r0 = whereType()
    //     0x751510: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x751514: mov             x1, x0
    // 0x751518: r0 = iterator()
    //     0x751518: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0x75151c: r1 = LoadClassIdInstr(r0)
    //     0x75151c: ldur            x1, [x0, #-1]
    //     0x751520: ubfx            x1, x1, #0xc, #0x14
    // 0x751524: mov             x16, x0
    // 0x751528: mov             x0, x1
    // 0x75152c: mov             x1, x16
    // 0x751530: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x751530: movz            x17, #0x292d
    //     0x751534: movk            x17, #0x1, lsl #16
    //     0x751538: add             lr, x0, x17
    //     0x75153c: ldr             lr, [x21, lr, lsl #3]
    //     0x751540: blr             lr
    // 0x751544: eor             x1, x0, #0x10
    // 0x751548: tbz             w1, #4, #0x751584
    // 0x75154c: r0 = Null
    //     0x75154c: mov             x0, NULL
    // 0x751550: LeaveFrame
    //     0x751550: mov             SP, fp
    //     0x751554: ldp             fp, lr, [SP], #0x10
    // 0x751558: ret
    //     0x751558: ret             
    // 0x75155c: mov             x1, x0
    // 0x751560: r0 = last()
    //     0x751560: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x751564: LoadField: r2 = r0->field_13
    //     0x751564: ldur            w2, [x0, #0x13]
    // 0x751568: DecompressPointer r2
    //     0x751568: add             x2, x2, HEAP, lsl #32
    // 0x75156c: ldur            x3, [fp, #-8]
    // 0x751570: ldur            x5, [fp, #-0x10]
    // 0x751574: r1 = Null
    //     0x751574: mov             x1, NULL
    // 0x751578: r0 = XmlTagException.missingClosingTag()
    //     0x751578: bl              #0x7515d4  ; [package:xml/src/xml/exceptions/tag_exception.dart] XmlTagException::XmlTagException.missingClosingTag
    // 0x75157c: r0 = Throw()
    //     0x75157c: bl              #0xec04b8  ; ThrowStub
    // 0x751580: brk             #0
    // 0x751584: ldur            x1, [fp, #-8]
    // 0x751588: ldur            x0, [fp, #-0x10]
    // 0x75158c: r0 = XmlParserException()
    //     0x75158c: bl              #0x751490  ; AllocateXmlParserExceptionStub -> XmlParserException (size=0x24)
    // 0x751590: mov             x1, x0
    // 0x751594: ldur            x0, [fp, #-8]
    // 0x751598: ArrayStore: r1[0] = r0  ; List_4
    //     0x751598: stur            w0, [x1, #0x17]
    // 0x75159c: ldur            x0, [fp, #-0x10]
    // 0x7515a0: StoreField: r1->field_1b = r0
    //     0x7515a0: stur            x0, [x1, #0x1b]
    // 0x7515a4: r0 = Sentinel
    //     0x7515a4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7515a8: StoreField: r1->field_b = r0
    //     0x7515a8: stur            w0, [x1, #0xb]
    // 0x7515ac: StoreField: r1->field_f = r0
    //     0x7515ac: stur            w0, [x1, #0xf]
    // 0x7515b0: StoreField: r1->field_13 = r0
    //     0x7515b0: stur            w0, [x1, #0x13]
    // 0x7515b4: r0 = "Expected a single root element"
    //     0x7515b4: add             x0, PP, #0x26, lsl #12  ; [pp+0x264a8] "Expected a single root element"
    //     0x7515b8: ldr             x0, [x0, #0x4a8]
    // 0x7515bc: StoreField: r1->field_7 = r0
    //     0x7515bc: stur            w0, [x1, #7]
    // 0x7515c0: mov             x0, x1
    // 0x7515c4: r0 = Throw()
    //     0x7515c4: bl              #0xec04b8  ; ThrowStub
    // 0x7515c8: brk             #0
    // 0x7515cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7515cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7515d0: b               #0x7514d0
  }
  _ annotate(/* No info */) {
    // ** addr: 0x7516b8, size: 0x788
    // 0x7516b8: EnterFrame
    //     0x7516b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7516bc: mov             fp, SP
    // 0x7516c0: AllocStack(0x48)
    //     0x7516c0: sub             SP, SP, #0x48
    // 0x7516c4: SetupParameters(XmlAnnotator this /* r1 => r1, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */, dynamic _ /* r3 => r3, fp-0x28 */, dynamic _ /* r5 => r5, fp-0x30 */)
    //     0x7516c4: mov             x0, x2
    //     0x7516c8: stur            x1, [fp, #-0x18]
    //     0x7516cc: stur            x2, [fp, #-0x20]
    //     0x7516d0: stur            x3, [fp, #-0x28]
    //     0x7516d4: stur            x5, [fp, #-0x30]
    // 0x7516d8: CheckStackOverflow
    //     0x7516d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7516dc: cmp             SP, x16
    //     0x7516e0: b.ls            #0x751e34
    // 0x7516e4: LoadField: r2 = r1->field_7
    //     0x7516e4: ldur            w2, [x1, #7]
    // 0x7516e8: DecompressPointer r2
    //     0x7516e8: add             x2, x2, HEAP, lsl #32
    // 0x7516ec: stur            x2, [fp, #-0x10]
    // 0x7516f0: tbz             w2, #4, #0x751700
    // 0x7516f4: LoadField: r4 = r1->field_b
    //     0x7516f4: ldur            w4, [x1, #0xb]
    // 0x7516f8: DecompressPointer r4
    //     0x7516f8: add             x4, x4, HEAP, lsl #32
    // 0x7516fc: tbnz            w4, #4, #0x751c48
    // 0x751700: LoadField: r4 = r1->field_b
    //     0x751700: ldur            w4, [x1, #0xb]
    // 0x751704: DecompressPointer r4
    //     0x751704: add             x4, x4, HEAP, lsl #32
    // 0x751708: tbnz            w4, #4, #0x751ac8
    // 0x75170c: LoadField: r4 = r1->field_1f
    //     0x75170c: ldur            w4, [x1, #0x1f]
    // 0x751710: DecompressPointer r4
    //     0x751710: add             x4, x4, HEAP, lsl #32
    // 0x751714: LoadField: r6 = r4->field_b
    //     0x751714: ldur            w6, [x4, #0xb]
    // 0x751718: cbnz            w6, #0x751ac8
    // 0x75171c: r4 = LoadClassIdInstr(r0)
    //     0x75171c: ldur            x4, [x0, #-1]
    //     0x751720: ubfx            x4, x4, #0xc, #0x14
    // 0x751724: cmp             x4, #0xcf
    // 0x751728: b.ne            #0x751840
    // 0x75172c: LoadField: r4 = r1->field_1b
    //     0x75172c: ldur            w4, [x1, #0x1b]
    // 0x751730: DecompressPointer r4
    //     0x751730: add             x4, x4, HEAP, lsl #32
    // 0x751734: stur            x4, [fp, #-8]
    // 0x751738: r16 = <XmlDeclarationEvent>
    //     0x751738: add             x16, PP, #0x26, lsl #12  ; [pp+0x264b8] TypeArguments: <XmlDeclarationEvent>
    //     0x75173c: ldr             x16, [x16, #0x4b8]
    // 0x751740: stp             x4, x16, [SP]
    // 0x751744: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x751744: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x751748: r0 = whereType()
    //     0x751748: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x75174c: mov             x1, x0
    // 0x751750: r0 = iterator()
    //     0x751750: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0x751754: r1 = LoadClassIdInstr(r0)
    //     0x751754: ldur            x1, [x0, #-1]
    //     0x751758: ubfx            x1, x1, #0xc, #0x14
    // 0x75175c: mov             x16, x0
    // 0x751760: mov             x0, x1
    // 0x751764: mov             x1, x16
    // 0x751768: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x751768: movz            x17, #0x292d
    //     0x75176c: movk            x17, #0x1, lsl #16
    //     0x751770: add             lr, x0, x17
    //     0x751774: ldr             lr, [x21, lr, lsl #3]
    //     0x751778: blr             lr
    // 0x75177c: eor             x1, x0, #0x10
    // 0x751780: eor             x0, x1, #0x10
    // 0x751784: tbz             w0, #4, #0x751c58
    // 0x751788: ldur            x3, [fp, #-8]
    // 0x75178c: LoadField: r0 = r3->field_b
    //     0x75178c: ldur            w0, [x3, #0xb]
    // 0x751790: r4 = LoadInt32Instr(r0)
    //     0x751790: sbfx            x4, x0, #1, #0x1f
    // 0x751794: stur            x4, [fp, #-0x38]
    // 0x751798: cbnz            w0, #0x751c98
    // 0x75179c: LoadField: r2 = r3->field_7
    //     0x75179c: ldur            w2, [x3, #7]
    // 0x7517a0: DecompressPointer r2
    //     0x7517a0: add             x2, x2, HEAP, lsl #32
    // 0x7517a4: ldur            x0, [fp, #-0x20]
    // 0x7517a8: r1 = Null
    //     0x7517a8: mov             x1, NULL
    // 0x7517ac: cmp             w2, NULL
    // 0x7517b0: b.eq            #0x7517d0
    // 0x7517b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7517b4: ldur            w4, [x2, #0x17]
    // 0x7517b8: DecompressPointer r4
    //     0x7517b8: add             x4, x4, HEAP, lsl #32
    // 0x7517bc: r8 = X0
    //     0x7517bc: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x7517c0: LoadField: r9 = r4->field_7
    //     0x7517c0: ldur            x9, [x4, #7]
    // 0x7517c4: r3 = Null
    //     0x7517c4: add             x3, PP, #0x26, lsl #12  ; [pp+0x264c0] Null
    //     0x7517c8: ldr             x3, [x3, #0x4c0]
    // 0x7517cc: blr             x9
    // 0x7517d0: ldur            x0, [fp, #-8]
    // 0x7517d4: LoadField: r1 = r0->field_f
    //     0x7517d4: ldur            w1, [x0, #0xf]
    // 0x7517d8: DecompressPointer r1
    //     0x7517d8: add             x1, x1, HEAP, lsl #32
    // 0x7517dc: LoadField: r2 = r1->field_b
    //     0x7517dc: ldur            w2, [x1, #0xb]
    // 0x7517e0: r1 = LoadInt32Instr(r2)
    //     0x7517e0: sbfx            x1, x2, #1, #0x1f
    // 0x7517e4: ldur            x2, [fp, #-0x38]
    // 0x7517e8: cmp             x2, x1
    // 0x7517ec: b.ne            #0x7517f8
    // 0x7517f0: mov             x1, x0
    // 0x7517f4: r0 = _growToNextCapacity()
    //     0x7517f4: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7517f8: ldur            x0, [fp, #-8]
    // 0x7517fc: ldur            x2, [fp, #-0x38]
    // 0x751800: r1 = 2
    //     0x751800: movz            x1, #0x2
    // 0x751804: StoreField: r0->field_b = r1
    //     0x751804: stur            w1, [x0, #0xb]
    // 0x751808: LoadField: r1 = r0->field_f
    //     0x751808: ldur            w1, [x0, #0xf]
    // 0x75180c: DecompressPointer r1
    //     0x75180c: add             x1, x1, HEAP, lsl #32
    // 0x751810: ldur            x0, [fp, #-0x20]
    // 0x751814: ArrayStore: r1[r2] = r0  ; List_4
    //     0x751814: add             x25, x1, x2, lsl #2
    //     0x751818: add             x25, x25, #0xf
    //     0x75181c: str             w0, [x25]
    //     0x751820: tbz             w0, #0, #0x75183c
    //     0x751824: ldurb           w16, [x1, #-1]
    //     0x751828: ldurb           w17, [x0, #-1]
    //     0x75182c: and             x16, x17, x16, lsr #2
    //     0x751830: tst             x16, HEAP, lsr #32
    //     0x751834: b.eq            #0x75183c
    //     0x751838: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x75183c: b               #0x751ac8
    // 0x751840: cmp             x4, #0xce
    // 0x751844: b.ne            #0x7519b0
    // 0x751848: ldur            x0, [fp, #-0x18]
    // 0x75184c: LoadField: r1 = r0->field_1b
    //     0x75184c: ldur            w1, [x0, #0x1b]
    // 0x751850: DecompressPointer r1
    //     0x751850: add             x1, x1, HEAP, lsl #32
    // 0x751854: stur            x1, [fp, #-8]
    // 0x751858: r16 = <XmlDoctypeEvent>
    //     0x751858: add             x16, PP, #0x26, lsl #12  ; [pp+0x264d0] TypeArguments: <XmlDoctypeEvent>
    //     0x75185c: ldr             x16, [x16, #0x4d0]
    // 0x751860: stp             x1, x16, [SP]
    // 0x751864: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x751864: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x751868: r0 = whereType()
    //     0x751868: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x75186c: mov             x1, x0
    // 0x751870: r0 = iterator()
    //     0x751870: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0x751874: r1 = LoadClassIdInstr(r0)
    //     0x751874: ldur            x1, [x0, #-1]
    //     0x751878: ubfx            x1, x1, #0xc, #0x14
    // 0x75187c: mov             x16, x0
    // 0x751880: mov             x0, x1
    // 0x751884: mov             x1, x16
    // 0x751888: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x751888: movz            x17, #0x292d
    //     0x75188c: movk            x17, #0x1, lsl #16
    //     0x751890: add             lr, x0, x17
    //     0x751894: ldr             lr, [x21, lr, lsl #3]
    //     0x751898: blr             lr
    // 0x75189c: eor             x1, x0, #0x10
    // 0x7518a0: eor             x0, x1, #0x10
    // 0x7518a4: tbz             w0, #4, #0x751cdc
    // 0x7518a8: r16 = <XmlStartElementEvent>
    //     0x7518a8: add             x16, PP, #0x26, lsl #12  ; [pp+0x264a0] TypeArguments: <XmlStartElementEvent>
    //     0x7518ac: ldr             x16, [x16, #0x4a0]
    // 0x7518b0: ldur            lr, [fp, #-8]
    // 0x7518b4: stp             lr, x16, [SP]
    // 0x7518b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x7518b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x7518bc: r0 = whereType()
    //     0x7518bc: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x7518c0: mov             x1, x0
    // 0x7518c4: r0 = iterator()
    //     0x7518c4: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0x7518c8: r1 = LoadClassIdInstr(r0)
    //     0x7518c8: ldur            x1, [x0, #-1]
    //     0x7518cc: ubfx            x1, x1, #0xc, #0x14
    // 0x7518d0: mov             x16, x0
    // 0x7518d4: mov             x0, x1
    // 0x7518d8: mov             x1, x16
    // 0x7518dc: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x7518dc: movz            x17, #0x292d
    //     0x7518e0: movk            x17, #0x1, lsl #16
    //     0x7518e4: add             lr, x0, x17
    //     0x7518e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7518ec: blr             lr
    // 0x7518f0: eor             x1, x0, #0x10
    // 0x7518f4: eor             x0, x1, #0x10
    // 0x7518f8: tbz             w0, #4, #0x751d20
    // 0x7518fc: ldur            x3, [fp, #-8]
    // 0x751900: LoadField: r2 = r3->field_7
    //     0x751900: ldur            w2, [x3, #7]
    // 0x751904: DecompressPointer r2
    //     0x751904: add             x2, x2, HEAP, lsl #32
    // 0x751908: ldur            x0, [fp, #-0x20]
    // 0x75190c: r1 = Null
    //     0x75190c: mov             x1, NULL
    // 0x751910: cmp             w2, NULL
    // 0x751914: b.eq            #0x751934
    // 0x751918: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x751918: ldur            w4, [x2, #0x17]
    // 0x75191c: DecompressPointer r4
    //     0x75191c: add             x4, x4, HEAP, lsl #32
    // 0x751920: r8 = X0
    //     0x751920: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x751924: LoadField: r9 = r4->field_7
    //     0x751924: ldur            x9, [x4, #7]
    // 0x751928: r3 = Null
    //     0x751928: add             x3, PP, #0x26, lsl #12  ; [pp+0x264d8] Null
    //     0x75192c: ldr             x3, [x3, #0x4d8]
    // 0x751930: blr             x9
    // 0x751934: ldur            x0, [fp, #-8]
    // 0x751938: LoadField: r1 = r0->field_b
    //     0x751938: ldur            w1, [x0, #0xb]
    // 0x75193c: LoadField: r2 = r0->field_f
    //     0x75193c: ldur            w2, [x0, #0xf]
    // 0x751940: DecompressPointer r2
    //     0x751940: add             x2, x2, HEAP, lsl #32
    // 0x751944: LoadField: r3 = r2->field_b
    //     0x751944: ldur            w3, [x2, #0xb]
    // 0x751948: r2 = LoadInt32Instr(r1)
    //     0x751948: sbfx            x2, x1, #1, #0x1f
    // 0x75194c: stur            x2, [fp, #-0x38]
    // 0x751950: r1 = LoadInt32Instr(r3)
    //     0x751950: sbfx            x1, x3, #1, #0x1f
    // 0x751954: cmp             x2, x1
    // 0x751958: b.ne            #0x751964
    // 0x75195c: mov             x1, x0
    // 0x751960: r0 = _growToNextCapacity()
    //     0x751960: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x751964: ldur            x0, [fp, #-8]
    // 0x751968: ldur            x2, [fp, #-0x38]
    // 0x75196c: add             x1, x2, #1
    // 0x751970: lsl             x3, x1, #1
    // 0x751974: StoreField: r0->field_b = r3
    //     0x751974: stur            w3, [x0, #0xb]
    // 0x751978: LoadField: r1 = r0->field_f
    //     0x751978: ldur            w1, [x0, #0xf]
    // 0x75197c: DecompressPointer r1
    //     0x75197c: add             x1, x1, HEAP, lsl #32
    // 0x751980: ldur            x0, [fp, #-0x20]
    // 0x751984: ArrayStore: r1[r2] = r0  ; List_4
    //     0x751984: add             x25, x1, x2, lsl #2
    //     0x751988: add             x25, x25, #0xf
    //     0x75198c: str             w0, [x25]
    //     0x751990: tbz             w0, #0, #0x7519ac
    //     0x751994: ldurb           w16, [x1, #-1]
    //     0x751998: ldurb           w17, [x0, #-1]
    //     0x75199c: and             x16, x17, x16, lsr #2
    //     0x7519a0: tst             x16, HEAP, lsr #32
    //     0x7519a4: b.eq            #0x7519ac
    //     0x7519a8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x7519ac: b               #0x751ac8
    // 0x7519b0: cmp             x4, #0xcc
    // 0x7519b4: b.ne            #0x751ac8
    // 0x7519b8: ldur            x0, [fp, #-0x18]
    // 0x7519bc: LoadField: r1 = r0->field_1b
    //     0x7519bc: ldur            w1, [x0, #0x1b]
    // 0x7519c0: DecompressPointer r1
    //     0x7519c0: add             x1, x1, HEAP, lsl #32
    // 0x7519c4: stur            x1, [fp, #-8]
    // 0x7519c8: r16 = <XmlStartElementEvent>
    //     0x7519c8: add             x16, PP, #0x26, lsl #12  ; [pp+0x264a0] TypeArguments: <XmlStartElementEvent>
    //     0x7519cc: ldr             x16, [x16, #0x4a0]
    // 0x7519d0: stp             x1, x16, [SP]
    // 0x7519d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x7519d4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x7519d8: r0 = whereType()
    //     0x7519d8: bl              #0x8621fc  ; [dart:collection] ListBase::whereType
    // 0x7519dc: mov             x1, x0
    // 0x7519e0: r0 = iterator()
    //     0x7519e0: bl              #0x888158  ; [dart:_internal] WhereTypeIterable::iterator
    // 0x7519e4: r1 = LoadClassIdInstr(r0)
    //     0x7519e4: ldur            x1, [x0, #-1]
    //     0x7519e8: ubfx            x1, x1, #0xc, #0x14
    // 0x7519ec: mov             x16, x0
    // 0x7519f0: mov             x0, x1
    // 0x7519f4: mov             x1, x16
    // 0x7519f8: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x7519f8: movz            x17, #0x292d
    //     0x7519fc: movk            x17, #0x1, lsl #16
    //     0x751a00: add             lr, x0, x17
    //     0x751a04: ldr             lr, [x21, lr, lsl #3]
    //     0x751a08: blr             lr
    // 0x751a0c: eor             x1, x0, #0x10
    // 0x751a10: eor             x0, x1, #0x10
    // 0x751a14: tbz             w0, #4, #0x751d64
    // 0x751a18: ldur            x3, [fp, #-8]
    // 0x751a1c: LoadField: r2 = r3->field_7
    //     0x751a1c: ldur            w2, [x3, #7]
    // 0x751a20: DecompressPointer r2
    //     0x751a20: add             x2, x2, HEAP, lsl #32
    // 0x751a24: ldur            x0, [fp, #-0x20]
    // 0x751a28: r1 = Null
    //     0x751a28: mov             x1, NULL
    // 0x751a2c: cmp             w2, NULL
    // 0x751a30: b.eq            #0x751a50
    // 0x751a34: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x751a34: ldur            w4, [x2, #0x17]
    // 0x751a38: DecompressPointer r4
    //     0x751a38: add             x4, x4, HEAP, lsl #32
    // 0x751a3c: r8 = X0
    //     0x751a3c: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x751a40: LoadField: r9 = r4->field_7
    //     0x751a40: ldur            x9, [x4, #7]
    // 0x751a44: r3 = Null
    //     0x751a44: add             x3, PP, #0x26, lsl #12  ; [pp+0x264e8] Null
    //     0x751a48: ldr             x3, [x3, #0x4e8]
    // 0x751a4c: blr             x9
    // 0x751a50: ldur            x0, [fp, #-8]
    // 0x751a54: LoadField: r1 = r0->field_b
    //     0x751a54: ldur            w1, [x0, #0xb]
    // 0x751a58: LoadField: r2 = r0->field_f
    //     0x751a58: ldur            w2, [x0, #0xf]
    // 0x751a5c: DecompressPointer r2
    //     0x751a5c: add             x2, x2, HEAP, lsl #32
    // 0x751a60: LoadField: r3 = r2->field_b
    //     0x751a60: ldur            w3, [x2, #0xb]
    // 0x751a64: r2 = LoadInt32Instr(r1)
    //     0x751a64: sbfx            x2, x1, #1, #0x1f
    // 0x751a68: stur            x2, [fp, #-0x38]
    // 0x751a6c: r1 = LoadInt32Instr(r3)
    //     0x751a6c: sbfx            x1, x3, #1, #0x1f
    // 0x751a70: cmp             x2, x1
    // 0x751a74: b.ne            #0x751a80
    // 0x751a78: mov             x1, x0
    // 0x751a7c: r0 = _growToNextCapacity()
    //     0x751a7c: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x751a80: ldur            x0, [fp, #-8]
    // 0x751a84: ldur            x2, [fp, #-0x38]
    // 0x751a88: add             x1, x2, #1
    // 0x751a8c: lsl             x3, x1, #1
    // 0x751a90: StoreField: r0->field_b = r3
    //     0x751a90: stur            w3, [x0, #0xb]
    // 0x751a94: LoadField: r1 = r0->field_f
    //     0x751a94: ldur            w1, [x0, #0xf]
    // 0x751a98: DecompressPointer r1
    //     0x751a98: add             x1, x1, HEAP, lsl #32
    // 0x751a9c: ldur            x0, [fp, #-0x20]
    // 0x751aa0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x751aa0: add             x25, x1, x2, lsl #2
    //     0x751aa4: add             x25, x25, #0xf
    //     0x751aa8: str             w0, [x25]
    //     0x751aac: tbz             w0, #0, #0x751ac8
    //     0x751ab0: ldurb           w16, [x1, #-1]
    //     0x751ab4: ldurb           w17, [x0, #-1]
    //     0x751ab8: and             x16, x17, x16, lsr #2
    //     0x751abc: tst             x16, HEAP, lsr #32
    //     0x751ac0: b.eq            #0x751ac8
    //     0x751ac4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x751ac8: ldur            x3, [fp, #-0x20]
    // 0x751acc: r0 = LoadClassIdInstr(r3)
    //     0x751acc: ldur            x0, [x3, #-1]
    //     0x751ad0: ubfx            x0, x0, #0xc, #0x14
    // 0x751ad4: cmp             x0, #0xcc
    // 0x751ad8: b.ne            #0x751ba8
    // 0x751adc: LoadField: r0 = r3->field_1b
    //     0x751adc: ldur            w0, [x3, #0x1b]
    // 0x751ae0: DecompressPointer r0
    //     0x751ae0: add             x0, x0, HEAP, lsl #32
    // 0x751ae4: tbz             w0, #4, #0x751c48
    // 0x751ae8: ldur            x2, [fp, #-0x18]
    // 0x751aec: LoadField: r4 = r2->field_1f
    //     0x751aec: ldur            w4, [x2, #0x1f]
    // 0x751af0: DecompressPointer r4
    //     0x751af0: add             x4, x4, HEAP, lsl #32
    // 0x751af4: stur            x4, [fp, #-8]
    // 0x751af8: LoadField: r2 = r4->field_7
    //     0x751af8: ldur            w2, [x4, #7]
    // 0x751afc: DecompressPointer r2
    //     0x751afc: add             x2, x2, HEAP, lsl #32
    // 0x751b00: mov             x0, x3
    // 0x751b04: r1 = Null
    //     0x751b04: mov             x1, NULL
    // 0x751b08: cmp             w2, NULL
    // 0x751b0c: b.eq            #0x751b2c
    // 0x751b10: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x751b10: ldur            w4, [x2, #0x17]
    // 0x751b14: DecompressPointer r4
    //     0x751b14: add             x4, x4, HEAP, lsl #32
    // 0x751b18: r8 = X0
    //     0x751b18: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x751b1c: LoadField: r9 = r4->field_7
    //     0x751b1c: ldur            x9, [x4, #7]
    // 0x751b20: r3 = Null
    //     0x751b20: add             x3, PP, #0x26, lsl #12  ; [pp+0x264f8] Null
    //     0x751b24: ldr             x3, [x3, #0x4f8]
    // 0x751b28: blr             x9
    // 0x751b2c: ldur            x0, [fp, #-8]
    // 0x751b30: LoadField: r1 = r0->field_b
    //     0x751b30: ldur            w1, [x0, #0xb]
    // 0x751b34: LoadField: r2 = r0->field_f
    //     0x751b34: ldur            w2, [x0, #0xf]
    // 0x751b38: DecompressPointer r2
    //     0x751b38: add             x2, x2, HEAP, lsl #32
    // 0x751b3c: LoadField: r3 = r2->field_b
    //     0x751b3c: ldur            w3, [x2, #0xb]
    // 0x751b40: r2 = LoadInt32Instr(r1)
    //     0x751b40: sbfx            x2, x1, #1, #0x1f
    // 0x751b44: stur            x2, [fp, #-0x38]
    // 0x751b48: r1 = LoadInt32Instr(r3)
    //     0x751b48: sbfx            x1, x3, #1, #0x1f
    // 0x751b4c: cmp             x2, x1
    // 0x751b50: b.ne            #0x751b5c
    // 0x751b54: mov             x1, x0
    // 0x751b58: r0 = _growToNextCapacity()
    //     0x751b58: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x751b5c: ldur            x0, [fp, #-8]
    // 0x751b60: ldur            x2, [fp, #-0x38]
    // 0x751b64: add             x1, x2, #1
    // 0x751b68: lsl             x3, x1, #1
    // 0x751b6c: StoreField: r0->field_b = r3
    //     0x751b6c: stur            w3, [x0, #0xb]
    // 0x751b70: LoadField: r1 = r0->field_f
    //     0x751b70: ldur            w1, [x0, #0xf]
    // 0x751b74: DecompressPointer r1
    //     0x751b74: add             x1, x1, HEAP, lsl #32
    // 0x751b78: ldur            x0, [fp, #-0x20]
    // 0x751b7c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x751b7c: add             x25, x1, x2, lsl #2
    //     0x751b80: add             x25, x25, #0xf
    //     0x751b84: str             w0, [x25]
    //     0x751b88: tbz             w0, #0, #0x751ba4
    //     0x751b8c: ldurb           w16, [x1, #-1]
    //     0x751b90: ldurb           w17, [x0, #-1]
    //     0x751b94: and             x16, x17, x16, lsr #2
    //     0x751b98: tst             x16, HEAP, lsr #32
    //     0x751b9c: b.eq            #0x751ba4
    //     0x751ba0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x751ba4: b               #0x751c48
    // 0x751ba8: ldur            x2, [fp, #-0x18]
    // 0x751bac: cmp             x0, #0xcd
    // 0x751bb0: b.ne            #0x751c48
    // 0x751bb4: ldur            x0, [fp, #-0x10]
    // 0x751bb8: tbnz            w0, #4, #0x751c10
    // 0x751bbc: LoadField: r0 = r2->field_1f
    //     0x751bbc: ldur            w0, [x2, #0x1f]
    // 0x751bc0: DecompressPointer r0
    //     0x751bc0: add             x0, x0, HEAP, lsl #32
    // 0x751bc4: stur            x0, [fp, #-8]
    // 0x751bc8: LoadField: r1 = r0->field_b
    //     0x751bc8: ldur            w1, [x0, #0xb]
    // 0x751bcc: cbz             w1, #0x751da8
    // 0x751bd0: ldur            x3, [fp, #-0x20]
    // 0x751bd4: mov             x1, x0
    // 0x751bd8: r0 = last()
    //     0x751bd8: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x751bdc: LoadField: r1 = r0->field_13
    //     0x751bdc: ldur            w1, [x0, #0x13]
    // 0x751be0: DecompressPointer r1
    //     0x751be0: add             x1, x1, HEAP, lsl #32
    // 0x751be4: ldur            x0, [fp, #-0x20]
    // 0x751be8: LoadField: r3 = r0->field_13
    //     0x751be8: ldur            w3, [x0, #0x13]
    // 0x751bec: DecompressPointer r3
    //     0x751bec: add             x3, x3, HEAP, lsl #32
    // 0x751bf0: stur            x3, [fp, #-0x10]
    // 0x751bf4: r0 = LoadClassIdInstr(r1)
    //     0x751bf4: ldur            x0, [x1, #-1]
    //     0x751bf8: ubfx            x0, x0, #0xc, #0x14
    // 0x751bfc: stp             x3, x1, [SP]
    // 0x751c00: mov             lr, x0
    // 0x751c04: ldr             lr, [x21, lr, lsl #3]
    // 0x751c08: blr             lr
    // 0x751c0c: tbnz            w0, #4, #0x751de8
    // 0x751c10: ldur            x0, [fp, #-0x18]
    // 0x751c14: LoadField: r2 = r0->field_1f
    //     0x751c14: ldur            w2, [x0, #0x1f]
    // 0x751c18: DecompressPointer r2
    //     0x751c18: add             x2, x2, HEAP, lsl #32
    // 0x751c1c: LoadField: r0 = r2->field_b
    //     0x751c1c: ldur            w0, [x2, #0xb]
    // 0x751c20: r1 = LoadInt32Instr(r0)
    //     0x751c20: sbfx            x1, x0, #1, #0x1f
    // 0x751c24: cbz             w0, #0x751c48
    // 0x751c28: sub             x3, x1, #1
    // 0x751c2c: mov             x0, x1
    // 0x751c30: mov             x1, x3
    // 0x751c34: cmp             x1, x0
    // 0x751c38: b.hs            #0x751e3c
    // 0x751c3c: mov             x1, x2
    // 0x751c40: mov             x2, x3
    // 0x751c44: r0 = length=()
    //     0x751c44: bl              #0x6e4110  ; [dart:core] _GrowableList::length=
    // 0x751c48: r0 = Null
    //     0x751c48: mov             x0, NULL
    // 0x751c4c: LeaveFrame
    //     0x751c4c: mov             SP, fp
    //     0x751c50: ldp             fp, lr, [SP], #0x10
    // 0x751c54: ret
    //     0x751c54: ret             
    // 0x751c58: ldur            x3, [fp, #-0x28]
    // 0x751c5c: ldur            x0, [fp, #-0x30]
    // 0x751c60: r0 = XmlParserException()
    //     0x751c60: bl              #0x751490  ; AllocateXmlParserExceptionStub -> XmlParserException (size=0x24)
    // 0x751c64: ldur            x3, [fp, #-0x28]
    // 0x751c68: ArrayStore: r0[0] = r3  ; List_4
    //     0x751c68: stur            w3, [x0, #0x17]
    // 0x751c6c: ldur            x1, [fp, #-0x30]
    // 0x751c70: StoreField: r0->field_1b = r1
    //     0x751c70: stur            x1, [x0, #0x1b]
    // 0x751c74: r2 = Sentinel
    //     0x751c74: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751c78: StoreField: r0->field_b = r2
    //     0x751c78: stur            w2, [x0, #0xb]
    // 0x751c7c: StoreField: r0->field_f = r2
    //     0x751c7c: stur            w2, [x0, #0xf]
    // 0x751c80: StoreField: r0->field_13 = r2
    //     0x751c80: stur            w2, [x0, #0x13]
    // 0x751c84: r1 = "Expected at most one XML declaration"
    //     0x751c84: add             x1, PP, #0x26, lsl #12  ; [pp+0x26508] "Expected at most one XML declaration"
    //     0x751c88: ldr             x1, [x1, #0x508]
    // 0x751c8c: StoreField: r0->field_7 = r1
    //     0x751c8c: stur            w1, [x0, #7]
    // 0x751c90: r0 = Throw()
    //     0x751c90: bl              #0xec04b8  ; ThrowStub
    // 0x751c94: brk             #0
    // 0x751c98: ldur            x3, [fp, #-0x28]
    // 0x751c9c: ldur            x1, [fp, #-0x30]
    // 0x751ca0: r2 = Sentinel
    //     0x751ca0: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751ca4: r0 = XmlParserException()
    //     0x751ca4: bl              #0x751490  ; AllocateXmlParserExceptionStub -> XmlParserException (size=0x24)
    // 0x751ca8: ldur            x3, [fp, #-0x28]
    // 0x751cac: ArrayStore: r0[0] = r3  ; List_4
    //     0x751cac: stur            w3, [x0, #0x17]
    // 0x751cb0: ldur            x1, [fp, #-0x30]
    // 0x751cb4: StoreField: r0->field_1b = r1
    //     0x751cb4: stur            x1, [x0, #0x1b]
    // 0x751cb8: r2 = Sentinel
    //     0x751cb8: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751cbc: StoreField: r0->field_b = r2
    //     0x751cbc: stur            w2, [x0, #0xb]
    // 0x751cc0: StoreField: r0->field_f = r2
    //     0x751cc0: stur            w2, [x0, #0xf]
    // 0x751cc4: StoreField: r0->field_13 = r2
    //     0x751cc4: stur            w2, [x0, #0x13]
    // 0x751cc8: r1 = "Unexpected XML declaration"
    //     0x751cc8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26510] "Unexpected XML declaration"
    //     0x751ccc: ldr             x1, [x1, #0x510]
    // 0x751cd0: StoreField: r0->field_7 = r1
    //     0x751cd0: stur            w1, [x0, #7]
    // 0x751cd4: r0 = Throw()
    //     0x751cd4: bl              #0xec04b8  ; ThrowStub
    // 0x751cd8: brk             #0
    // 0x751cdc: ldur            x3, [fp, #-0x28]
    // 0x751ce0: ldur            x1, [fp, #-0x30]
    // 0x751ce4: r2 = Sentinel
    //     0x751ce4: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751ce8: r0 = XmlParserException()
    //     0x751ce8: bl              #0x751490  ; AllocateXmlParserExceptionStub -> XmlParserException (size=0x24)
    // 0x751cec: ldur            x3, [fp, #-0x28]
    // 0x751cf0: ArrayStore: r0[0] = r3  ; List_4
    //     0x751cf0: stur            w3, [x0, #0x17]
    // 0x751cf4: ldur            x1, [fp, #-0x30]
    // 0x751cf8: StoreField: r0->field_1b = r1
    //     0x751cf8: stur            x1, [x0, #0x1b]
    // 0x751cfc: r2 = Sentinel
    //     0x751cfc: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751d00: StoreField: r0->field_b = r2
    //     0x751d00: stur            w2, [x0, #0xb]
    // 0x751d04: StoreField: r0->field_f = r2
    //     0x751d04: stur            w2, [x0, #0xf]
    // 0x751d08: StoreField: r0->field_13 = r2
    //     0x751d08: stur            w2, [x0, #0x13]
    // 0x751d0c: r1 = "Expected at most one doctype declaration"
    //     0x751d0c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26518] "Expected at most one doctype declaration"
    //     0x751d10: ldr             x1, [x1, #0x518]
    // 0x751d14: StoreField: r0->field_7 = r1
    //     0x751d14: stur            w1, [x0, #7]
    // 0x751d18: r0 = Throw()
    //     0x751d18: bl              #0xec04b8  ; ThrowStub
    // 0x751d1c: brk             #0
    // 0x751d20: ldur            x3, [fp, #-0x28]
    // 0x751d24: ldur            x1, [fp, #-0x30]
    // 0x751d28: r2 = Sentinel
    //     0x751d28: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751d2c: r0 = XmlParserException()
    //     0x751d2c: bl              #0x751490  ; AllocateXmlParserExceptionStub -> XmlParserException (size=0x24)
    // 0x751d30: ldur            x3, [fp, #-0x28]
    // 0x751d34: ArrayStore: r0[0] = r3  ; List_4
    //     0x751d34: stur            w3, [x0, #0x17]
    // 0x751d38: ldur            x1, [fp, #-0x30]
    // 0x751d3c: StoreField: r0->field_1b = r1
    //     0x751d3c: stur            x1, [x0, #0x1b]
    // 0x751d40: r2 = Sentinel
    //     0x751d40: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751d44: StoreField: r0->field_b = r2
    //     0x751d44: stur            w2, [x0, #0xb]
    // 0x751d48: StoreField: r0->field_f = r2
    //     0x751d48: stur            w2, [x0, #0xf]
    // 0x751d4c: StoreField: r0->field_13 = r2
    //     0x751d4c: stur            w2, [x0, #0x13]
    // 0x751d50: r1 = "Unexpected doctype declaration"
    //     0x751d50: add             x1, PP, #0x26, lsl #12  ; [pp+0x26520] "Unexpected doctype declaration"
    //     0x751d54: ldr             x1, [x1, #0x520]
    // 0x751d58: StoreField: r0->field_7 = r1
    //     0x751d58: stur            w1, [x0, #7]
    // 0x751d5c: r0 = Throw()
    //     0x751d5c: bl              #0xec04b8  ; ThrowStub
    // 0x751d60: brk             #0
    // 0x751d64: ldur            x3, [fp, #-0x28]
    // 0x751d68: ldur            x1, [fp, #-0x30]
    // 0x751d6c: r2 = Sentinel
    //     0x751d6c: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751d70: r0 = XmlParserException()
    //     0x751d70: bl              #0x751490  ; AllocateXmlParserExceptionStub -> XmlParserException (size=0x24)
    // 0x751d74: ldur            x3, [fp, #-0x28]
    // 0x751d78: ArrayStore: r0[0] = r3  ; List_4
    //     0x751d78: stur            w3, [x0, #0x17]
    // 0x751d7c: ldur            x2, [fp, #-0x30]
    // 0x751d80: StoreField: r0->field_1b = r2
    //     0x751d80: stur            x2, [x0, #0x1b]
    // 0x751d84: r1 = Sentinel
    //     0x751d84: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751d88: StoreField: r0->field_b = r1
    //     0x751d88: stur            w1, [x0, #0xb]
    // 0x751d8c: StoreField: r0->field_f = r1
    //     0x751d8c: stur            w1, [x0, #0xf]
    // 0x751d90: StoreField: r0->field_13 = r1
    //     0x751d90: stur            w1, [x0, #0x13]
    // 0x751d94: r1 = "Unexpected root element"
    //     0x751d94: add             x1, PP, #0x26, lsl #12  ; [pp+0x26528] "Unexpected root element"
    //     0x751d98: ldr             x1, [x1, #0x528]
    // 0x751d9c: StoreField: r0->field_7 = r1
    //     0x751d9c: stur            w1, [x0, #7]
    // 0x751da0: r0 = Throw()
    //     0x751da0: bl              #0xec04b8  ; ThrowStub
    // 0x751da4: brk             #0
    // 0x751da8: ldur            x0, [fp, #-0x20]
    // 0x751dac: ldur            x3, [fp, #-0x28]
    // 0x751db0: ldur            x2, [fp, #-0x30]
    // 0x751db4: LoadField: r4 = r0->field_13
    //     0x751db4: ldur            w4, [x0, #0x13]
    // 0x751db8: DecompressPointer r4
    //     0x751db8: add             x4, x4, HEAP, lsl #32
    // 0x751dbc: r0 = BoxInt64Instr(r2)
    //     0x751dbc: sbfiz           x0, x2, #1, #0x1f
    //     0x751dc0: cmp             x2, x0, asr #1
    //     0x751dc4: b.eq            #0x751dd0
    //     0x751dc8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x751dcc: stur            x2, [x0, #7]
    // 0x751dd0: mov             x2, x4
    // 0x751dd4: mov             x5, x0
    // 0x751dd8: r1 = Null
    //     0x751dd8: mov             x1, NULL
    // 0x751ddc: r0 = XmlTagException.unexpectedClosingTag()
    //     0x751ddc: bl              #0x751ef4  ; [package:xml/src/xml/exceptions/tag_exception.dart] XmlTagException::XmlTagException.unexpectedClosingTag
    // 0x751de0: r0 = Throw()
    //     0x751de0: bl              #0xec04b8  ; ThrowStub
    // 0x751de4: brk             #0
    // 0x751de8: ldur            x3, [fp, #-0x28]
    // 0x751dec: ldur            x2, [fp, #-0x30]
    // 0x751df0: ldur            x1, [fp, #-8]
    // 0x751df4: r0 = last()
    //     0x751df4: bl              #0x861f60  ; [dart:core] _GrowableList::last
    // 0x751df8: LoadField: r2 = r0->field_13
    //     0x751df8: ldur            w2, [x0, #0x13]
    // 0x751dfc: DecompressPointer r2
    //     0x751dfc: add             x2, x2, HEAP, lsl #32
    // 0x751e00: ldur            x3, [fp, #-0x30]
    // 0x751e04: r0 = BoxInt64Instr(r3)
    //     0x751e04: sbfiz           x0, x3, #1, #0x1f
    //     0x751e08: cmp             x3, x0, asr #1
    //     0x751e0c: b.eq            #0x751e18
    //     0x751e10: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x751e14: stur            x3, [x0, #7]
    // 0x751e18: ldur            x3, [fp, #-0x10]
    // 0x751e1c: ldur            x5, [fp, #-0x28]
    // 0x751e20: mov             x6, x0
    // 0x751e24: r1 = Null
    //     0x751e24: mov             x1, NULL
    // 0x751e28: r0 = XmlTagException.mismatchClosingTag()
    //     0x751e28: bl              #0x751e40  ; [package:xml/src/xml/exceptions/tag_exception.dart] XmlTagException::XmlTagException.mismatchClosingTag
    // 0x751e2c: r0 = Throw()
    //     0x751e2c: bl              #0xec04b8  ; ThrowStub
    // 0x751e30: brk             #0
    // 0x751e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x751e34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x751e38: b               #0x7516e4
    // 0x751e3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x751e3c: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ XmlAnnotator(/* No info */) {
    // ** addr: 0x88f774, size: 0xc4
    // 0x88f774: EnterFrame
    //     0x88f774: stp             fp, lr, [SP, #-0x10]!
    //     0x88f778: mov             fp, SP
    // 0x88f77c: AllocStack(0x18)
    //     0x88f77c: sub             SP, SP, #0x18
    // 0x88f780: SetupParameters(XmlAnnotator this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x88f780: mov             x4, x1
    //     0x88f784: mov             x0, x2
    //     0x88f788: stur            x1, [fp, #-8]
    //     0x88f78c: stur            x2, [fp, #-0x10]
    //     0x88f790: stur            x3, [fp, #-0x18]
    // 0x88f794: CheckStackOverflow
    //     0x88f794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f798: cmp             SP, x16
    //     0x88f79c: b.ls            #0x88f830
    // 0x88f7a0: r1 = <XmlEvent>
    //     0x88f7a0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26590] TypeArguments: <XmlEvent>
    //     0x88f7a4: ldr             x1, [x1, #0x590]
    // 0x88f7a8: r2 = 0
    //     0x88f7a8: movz            x2, #0
    // 0x88f7ac: r0 = _GrowableList()
    //     0x88f7ac: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x88f7b0: ldur            x3, [fp, #-8]
    // 0x88f7b4: StoreField: r3->field_1b = r0
    //     0x88f7b4: stur            w0, [x3, #0x1b]
    //     0x88f7b8: ldurb           w16, [x3, #-1]
    //     0x88f7bc: ldurb           w17, [x0, #-1]
    //     0x88f7c0: and             x16, x17, x16, lsr #2
    //     0x88f7c4: tst             x16, HEAP, lsr #32
    //     0x88f7c8: b.eq            #0x88f7d0
    //     0x88f7cc: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x88f7d0: r1 = <XmlStartElementEvent>
    //     0x88f7d0: add             x1, PP, #0x26, lsl #12  ; [pp+0x264a0] TypeArguments: <XmlStartElementEvent>
    //     0x88f7d4: ldr             x1, [x1, #0x4a0]
    // 0x88f7d8: r2 = 0
    //     0x88f7d8: movz            x2, #0
    // 0x88f7dc: r0 = _GrowableList()
    //     0x88f7dc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x88f7e0: ldur            x1, [fp, #-8]
    // 0x88f7e4: StoreField: r1->field_1f = r0
    //     0x88f7e4: stur            w0, [x1, #0x1f]
    //     0x88f7e8: ldurb           w16, [x1, #-1]
    //     0x88f7ec: ldurb           w17, [x0, #-1]
    //     0x88f7f0: and             x16, x17, x16, lsr #2
    //     0x88f7f4: tst             x16, HEAP, lsr #32
    //     0x88f7f8: b.eq            #0x88f800
    //     0x88f7fc: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x88f800: ldur            x2, [fp, #-0x18]
    // 0x88f804: StoreField: r1->field_7 = r2
    //     0x88f804: stur            w2, [x1, #7]
    // 0x88f808: ldur            x2, [fp, #-0x10]
    // 0x88f80c: StoreField: r1->field_b = r2
    //     0x88f80c: stur            w2, [x1, #0xb]
    // 0x88f810: r2 = false
    //     0x88f810: add             x2, NULL, #0x30  ; false
    // 0x88f814: StoreField: r1->field_f = r2
    //     0x88f814: stur            w2, [x1, #0xf]
    // 0x88f818: StoreField: r1->field_13 = r2
    //     0x88f818: stur            w2, [x1, #0x13]
    // 0x88f81c: ArrayStore: r1[0] = r2  ; List_4
    //     0x88f81c: stur            w2, [x1, #0x17]
    // 0x88f820: r0 = Null
    //     0x88f820: mov             x0, NULL
    // 0x88f824: LeaveFrame
    //     0x88f824: mov             SP, fp
    //     0x88f828: ldp             fp, lr, [SP], #0x10
    // 0x88f82c: ret
    //     0x88f82c: ret             
    // 0x88f830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f830: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f834: b               #0x88f7a0
  }
}
