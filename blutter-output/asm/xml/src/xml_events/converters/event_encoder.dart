// lib: , url: package:xml/src/xml_events/converters/event_encoder.dart

// class id: 1051329, size: 0x8
class :: {
}

// class id: 211, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class __XmlEventEncoderSink&Object&XmlEventVisitor extends Object
     with XmlEventVisitor {

  [closure] void visit(dynamic, XmlEvent) {
    // ** addr: 0x755434, size: 0x3c
    // 0x755434: EnterFrame
    //     0x755434: stp             fp, lr, [SP, #-0x10]!
    //     0x755438: mov             fp, SP
    // 0x75543c: ldr             x0, [fp, #0x18]
    // 0x755440: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x755440: ldur            w1, [x0, #0x17]
    // 0x755444: DecompressPointer r1
    //     0x755444: add             x1, x1, HEAP, lsl #32
    // 0x755448: CheckStackOverflow
    //     0x755448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x75544c: cmp             SP, x16
    //     0x755450: b.ls            #0x755468
    // 0x755454: ldr             x2, [fp, #0x10]
    // 0x755458: r0 = visit()
    //     0x755458: bl              #0x755470  ; [package:xml/src/xml_events/converters/event_encoder.dart] __XmlEventEncoderSink&Object&XmlEventVisitor::visit
    // 0x75545c: LeaveFrame
    //     0x75545c: mov             SP, fp
    //     0x755460: ldp             fp, lr, [SP], #0x10
    // 0x755464: ret
    //     0x755464: ret             
    // 0x755468: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x755468: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75546c: b               #0x755454
  }
  _ visit(/* No info */) {
    // ** addr: 0x755470, size: 0x4c
    // 0x755470: EnterFrame
    //     0x755470: stp             fp, lr, [SP, #-0x10]!
    //     0x755474: mov             fp, SP
    // 0x755478: mov             x16, x2
    // 0x75547c: mov             x2, x1
    // 0x755480: mov             x1, x16
    // 0x755484: CheckStackOverflow
    //     0x755484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755488: cmp             SP, x16
    //     0x75548c: b.ls            #0x7554b4
    // 0x755490: r0 = LoadClassIdInstr(r1)
    //     0x755490: ldur            x0, [x1, #-1]
    //     0x755494: ubfx            x0, x0, #0xc, #0x14
    // 0x755498: r0 = GDT[cid_x0 + -0x780]()
    //     0x755498: sub             lr, x0, #0x780
    //     0x75549c: ldr             lr, [x21, lr, lsl #3]
    //     0x7554a0: blr             lr
    // 0x7554a4: r0 = Null
    //     0x7554a4: mov             x0, NULL
    // 0x7554a8: LeaveFrame
    //     0x7554a8: mov             SP, fp
    //     0x7554ac: ldp             fp, lr, [SP], #0x10
    // 0x7554b0: ret
    //     0x7554b0: ret             
    // 0x7554b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7554b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7554b8: b               #0x755490
  }
}

// class id: 213, size: 0x10, field offset: 0x8
class _XmlEventEncoderSink extends __XmlEventEncoderSink&Object&XmlEventVisitor
    implements ChunkedConversionSink<X0> {

  dynamic add(dynamic) {
    // ** addr: 0x755278, size: 0x24
    // 0x755278: EnterFrame
    //     0x755278: stp             fp, lr, [SP, #-0x10]!
    //     0x75527c: mov             fp, SP
    // 0x755280: ldr             x2, [fp, #0x10]
    // 0x755284: r1 = Function 'add':.
    //     0x755284: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bf10] AnonymousClosure: (0x755344), in [package:xml/src/xml_events/converters/event_encoder.dart] _XmlEventEncoderSink::add (0xd5a2f0)
    //     0x755288: ldr             x1, [x1, #0xf10]
    // 0x75528c: r0 = AllocateClosure()
    //     0x75528c: bl              #0xec1630  ; AllocateClosureStub
    // 0x755290: LeaveFrame
    //     0x755290: mov             SP, fp
    //     0x755294: ldp             fp, lr, [SP], #0x10
    // 0x755298: ret
    //     0x755298: ret             
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x755344, size: 0xf0
    // 0x755344: EnterFrame
    //     0x755344: stp             fp, lr, [SP, #-0x10]!
    //     0x755348: mov             fp, SP
    // 0x75534c: AllocStack(0x18)
    //     0x75534c: sub             SP, SP, #0x18
    // 0x755350: SetupParameters()
    //     0x755350: ldr             x0, [fp, #0x18]
    //     0x755354: ldur            w3, [x0, #0x17]
    //     0x755358: add             x3, x3, HEAP, lsl #32
    //     0x75535c: stur            x3, [fp, #-0x18]
    // 0x755360: CheckStackOverflow
    //     0x755360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755364: cmp             SP, x16
    //     0x755368: b.ls            #0x755420
    // 0x75536c: ldr             x4, [fp, #0x10]
    // 0x755370: LoadField: r5 = r4->field_b
    //     0x755370: ldur            w5, [x4, #0xb]
    // 0x755374: stur            x5, [fp, #-0x10]
    // 0x755378: r0 = LoadInt32Instr(r5)
    //     0x755378: sbfx            x0, x5, #1, #0x1f
    // 0x75537c: r6 = 0
    //     0x75537c: movz            x6, #0
    // 0x755380: stur            x6, [fp, #-8]
    // 0x755384: CheckStackOverflow
    //     0x755384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755388: cmp             SP, x16
    //     0x75538c: b.ls            #0x755428
    // 0x755390: cmp             x6, x0
    // 0x755394: b.ge            #0x7553f4
    // 0x755398: mov             x1, x6
    // 0x75539c: cmp             x1, x0
    // 0x7553a0: b.hs            #0x755430
    // 0x7553a4: LoadField: r0 = r4->field_f
    //     0x7553a4: ldur            w0, [x4, #0xf]
    // 0x7553a8: DecompressPointer r0
    //     0x7553a8: add             x0, x0, HEAP, lsl #32
    // 0x7553ac: ArrayLoad: r2 = r0[r6]  ; Unknown_4
    //     0x7553ac: add             x16, x0, x6, lsl #2
    //     0x7553b0: ldur            w2, [x16, #0xf]
    // 0x7553b4: DecompressPointer r2
    //     0x7553b4: add             x2, x2, HEAP, lsl #32
    // 0x7553b8: mov             x1, x3
    // 0x7553bc: r0 = visit()
    //     0x7553bc: bl              #0x755470  ; [package:xml/src/xml_events/converters/event_encoder.dart] __XmlEventEncoderSink&Object&XmlEventVisitor::visit
    // 0x7553c0: ldr             x1, [fp, #0x10]
    // 0x7553c4: LoadField: r0 = r1->field_b
    //     0x7553c4: ldur            w0, [x1, #0xb]
    // 0x7553c8: ldur            x2, [fp, #-0x10]
    // 0x7553cc: cmp             w0, w2
    // 0x7553d0: b.ne            #0x755404
    // 0x7553d4: ldur            x3, [fp, #-8]
    // 0x7553d8: add             x6, x3, #1
    // 0x7553dc: r3 = LoadInt32Instr(r0)
    //     0x7553dc: sbfx            x3, x0, #1, #0x1f
    // 0x7553e0: mov             x0, x3
    // 0x7553e4: mov             x4, x1
    // 0x7553e8: ldur            x3, [fp, #-0x18]
    // 0x7553ec: mov             x5, x2
    // 0x7553f0: b               #0x755380
    // 0x7553f4: r0 = Null
    //     0x7553f4: mov             x0, NULL
    // 0x7553f8: LeaveFrame
    //     0x7553f8: mov             SP, fp
    //     0x7553fc: ldp             fp, lr, [SP], #0x10
    // 0x755400: ret
    //     0x755400: ret             
    // 0x755404: r0 = ConcurrentModificationError()
    //     0x755404: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x755408: mov             x1, x0
    // 0x75540c: ldr             x0, [fp, #0x10]
    // 0x755410: StoreField: r1->field_b = r0
    //     0x755410: stur            w0, [x1, #0xb]
    // 0x755414: mov             x0, x1
    // 0x755418: r0 = Throw()
    //     0x755418: bl              #0xec04b8  ; ThrowStub
    // 0x75541c: brk             #0
    // 0x755420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x755420: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x755424: b               #0x75536c
    // 0x755428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x755428: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75542c: b               #0x755390
    // 0x755430: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x755430: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ add(/* No info */) {
    // ** addr: 0xd5a2f0, size: 0xec
    // 0xd5a2f0: EnterFrame
    //     0xd5a2f0: stp             fp, lr, [SP, #-0x10]!
    //     0xd5a2f4: mov             fp, SP
    // 0xd5a2f8: AllocStack(0x20)
    //     0xd5a2f8: sub             SP, SP, #0x20
    // 0xd5a2fc: SetupParameters(_XmlEventEncoderSink this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0xd5a2fc: mov             x4, x1
    //     0xd5a300: mov             x3, x2
    //     0xd5a304: stur            x1, [fp, #-0x18]
    //     0xd5a308: stur            x2, [fp, #-0x20]
    // 0xd5a30c: CheckStackOverflow
    //     0xd5a30c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd5a310: cmp             SP, x16
    //     0xd5a314: b.ls            #0xd5a3c8
    // 0xd5a318: LoadField: r5 = r3->field_b
    //     0xd5a318: ldur            w5, [x3, #0xb]
    // 0xd5a31c: stur            x5, [fp, #-0x10]
    // 0xd5a320: r0 = LoadInt32Instr(r5)
    //     0xd5a320: sbfx            x0, x5, #1, #0x1f
    // 0xd5a324: r6 = 0
    //     0xd5a324: movz            x6, #0
    // 0xd5a328: stur            x6, [fp, #-8]
    // 0xd5a32c: CheckStackOverflow
    //     0xd5a32c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd5a330: cmp             SP, x16
    //     0xd5a334: b.ls            #0xd5a3d0
    // 0xd5a338: cmp             x6, x0
    // 0xd5a33c: b.ge            #0xd5a39c
    // 0xd5a340: mov             x1, x6
    // 0xd5a344: cmp             x1, x0
    // 0xd5a348: b.hs            #0xd5a3d8
    // 0xd5a34c: LoadField: r0 = r3->field_f
    //     0xd5a34c: ldur            w0, [x3, #0xf]
    // 0xd5a350: DecompressPointer r0
    //     0xd5a350: add             x0, x0, HEAP, lsl #32
    // 0xd5a354: ArrayLoad: r2 = r0[r6]  ; Unknown_4
    //     0xd5a354: add             x16, x0, x6, lsl #2
    //     0xd5a358: ldur            w2, [x16, #0xf]
    // 0xd5a35c: DecompressPointer r2
    //     0xd5a35c: add             x2, x2, HEAP, lsl #32
    // 0xd5a360: mov             x1, x4
    // 0xd5a364: r0 = visit()
    //     0xd5a364: bl              #0x755470  ; [package:xml/src/xml_events/converters/event_encoder.dart] __XmlEventEncoderSink&Object&XmlEventVisitor::visit
    // 0xd5a368: ldur            x1, [fp, #-0x20]
    // 0xd5a36c: LoadField: r0 = r1->field_b
    //     0xd5a36c: ldur            w0, [x1, #0xb]
    // 0xd5a370: ldur            x2, [fp, #-0x10]
    // 0xd5a374: cmp             w0, w2
    // 0xd5a378: b.ne            #0xd5a3ac
    // 0xd5a37c: ldur            x3, [fp, #-8]
    // 0xd5a380: add             x6, x3, #1
    // 0xd5a384: r3 = LoadInt32Instr(r0)
    //     0xd5a384: sbfx            x3, x0, #1, #0x1f
    // 0xd5a388: mov             x0, x3
    // 0xd5a38c: ldur            x4, [fp, #-0x18]
    // 0xd5a390: mov             x3, x1
    // 0xd5a394: mov             x5, x2
    // 0xd5a398: b               #0xd5a328
    // 0xd5a39c: r0 = Null
    //     0xd5a39c: mov             x0, NULL
    // 0xd5a3a0: LeaveFrame
    //     0xd5a3a0: mov             SP, fp
    //     0xd5a3a4: ldp             fp, lr, [SP], #0x10
    // 0xd5a3a8: ret
    //     0xd5a3a8: ret             
    // 0xd5a3ac: r0 = ConcurrentModificationError()
    //     0xd5a3ac: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xd5a3b0: mov             x1, x0
    // 0xd5a3b4: ldur            x0, [fp, #-0x20]
    // 0xd5a3b8: StoreField: r1->field_b = r0
    //     0xd5a3b8: stur            w0, [x1, #0xb]
    // 0xd5a3bc: mov             x0, x1
    // 0xd5a3c0: r0 = Throw()
    //     0xd5a3c0: bl              #0xec04b8  ; ThrowStub
    // 0xd5a3c4: brk             #0
    // 0xd5a3c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd5a3c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd5a3cc: b               #0xd5a318
    // 0xd5a3d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd5a3d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd5a3d4: b               #0xd5a338
    // 0xd5a3d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd5a3d8: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ visitCDATAEvent(/* No info */) {
    // ** addr: 0xeb9dfc, size: 0x78
    // 0xeb9dfc: EnterFrame
    //     0xeb9dfc: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9e00: mov             fp, SP
    // 0xeb9e04: AllocStack(0x10)
    //     0xeb9e04: sub             SP, SP, #0x10
    // 0xeb9e08: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xeb9e08: mov             x0, x2
    //     0xeb9e0c: stur            x2, [fp, #-0x10]
    // 0xeb9e10: CheckStackOverflow
    //     0xeb9e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9e14: cmp             SP, x16
    //     0xeb9e18: b.ls            #0xeb9e6c
    // 0xeb9e1c: LoadField: r3 = r1->field_7
    //     0xeb9e1c: ldur            w3, [x1, #7]
    // 0xeb9e20: DecompressPointer r3
    //     0xeb9e20: add             x3, x3, HEAP, lsl #32
    // 0xeb9e24: mov             x1, x3
    // 0xeb9e28: stur            x3, [fp, #-8]
    // 0xeb9e2c: r2 = "<![CDATA["
    //     0xeb9e2c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b20] "<![CDATA["
    //     0xeb9e30: ldr             x2, [x2, #0xb20]
    // 0xeb9e34: r0 = resolve()
    //     0xeb9e34: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeb9e38: ldur            x0, [fp, #-0x10]
    // 0xeb9e3c: LoadField: r2 = r0->field_13
    //     0xeb9e3c: ldur            w2, [x0, #0x13]
    // 0xeb9e40: DecompressPointer r2
    //     0xeb9e40: add             x2, x2, HEAP, lsl #32
    // 0xeb9e44: ldur            x1, [fp, #-8]
    // 0xeb9e48: r0 = resolve()
    //     0xeb9e48: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeb9e4c: ldur            x1, [fp, #-8]
    // 0xeb9e50: r2 = "]]>"
    //     0xeb9e50: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b28] "]]>"
    //     0xeb9e54: ldr             x2, [x2, #0xb28]
    // 0xeb9e58: r0 = resolve()
    //     0xeb9e58: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeb9e5c: r0 = Null
    //     0xeb9e5c: mov             x0, NULL
    // 0xeb9e60: LeaveFrame
    //     0xeb9e60: mov             SP, fp
    //     0xeb9e64: ldp             fp, lr, [SP], #0x10
    // 0xeb9e68: ret
    //     0xeb9e68: ret             
    // 0xeb9e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9e6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9e70: b               #0xeb9e1c
  }
  _ visitCommentEvent(/* No info */) {
    // ** addr: 0xeba458, size: 0x78
    // 0xeba458: EnterFrame
    //     0xeba458: stp             fp, lr, [SP, #-0x10]!
    //     0xeba45c: mov             fp, SP
    // 0xeba460: AllocStack(0x10)
    //     0xeba460: sub             SP, SP, #0x10
    // 0xeba464: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xeba464: mov             x0, x2
    //     0xeba468: stur            x2, [fp, #-0x10]
    // 0xeba46c: CheckStackOverflow
    //     0xeba46c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba470: cmp             SP, x16
    //     0xeba474: b.ls            #0xeba4c8
    // 0xeba478: LoadField: r3 = r1->field_7
    //     0xeba478: ldur            w3, [x1, #7]
    // 0xeba47c: DecompressPointer r3
    //     0xeba47c: add             x3, x3, HEAP, lsl #32
    // 0xeba480: mov             x1, x3
    // 0xeba484: stur            x3, [fp, #-8]
    // 0xeba488: r2 = "<!--"
    //     0xeba488: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b48] "<!--"
    //     0xeba48c: ldr             x2, [x2, #0xb48]
    // 0xeba490: r0 = resolve()
    //     0xeba490: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeba494: ldur            x0, [fp, #-0x10]
    // 0xeba498: LoadField: r2 = r0->field_13
    //     0xeba498: ldur            w2, [x0, #0x13]
    // 0xeba49c: DecompressPointer r2
    //     0xeba49c: add             x2, x2, HEAP, lsl #32
    // 0xeba4a0: ldur            x1, [fp, #-8]
    // 0xeba4a4: r0 = resolve()
    //     0xeba4a4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeba4a8: ldur            x1, [fp, #-8]
    // 0xeba4ac: r2 = "-->"
    //     0xeba4ac: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b50] "-->"
    //     0xeba4b0: ldr             x2, [x2, #0xb50]
    // 0xeba4b4: r0 = resolve()
    //     0xeba4b4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeba4b8: r0 = Null
    //     0xeba4b8: mov             x0, NULL
    // 0xeba4bc: LeaveFrame
    //     0xeba4bc: mov             SP, fp
    //     0xeba4c0: ldp             fp, lr, [SP], #0x10
    // 0xeba4c4: ret
    //     0xeba4c4: ret             
    // 0xeba4c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba4c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba4cc: b               #0xeba478
  }
  _ visitDeclarationEvent(/* No info */) {
    // ** addr: 0xeba544, size: 0x80
    // 0xeba544: EnterFrame
    //     0xeba544: stp             fp, lr, [SP, #-0x10]!
    //     0xeba548: mov             fp, SP
    // 0xeba54c: AllocStack(0x18)
    //     0xeba54c: sub             SP, SP, #0x18
    // 0xeba550: SetupParameters(_XmlEventEncoderSink this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xeba550: mov             x3, x1
    //     0xeba554: mov             x0, x2
    //     0xeba558: stur            x1, [fp, #-0x10]
    //     0xeba55c: stur            x2, [fp, #-0x18]
    // 0xeba560: CheckStackOverflow
    //     0xeba560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba564: cmp             SP, x16
    //     0xeba568: b.ls            #0xeba5bc
    // 0xeba56c: LoadField: r4 = r3->field_7
    //     0xeba56c: ldur            w4, [x3, #7]
    // 0xeba570: DecompressPointer r4
    //     0xeba570: add             x4, x4, HEAP, lsl #32
    // 0xeba574: mov             x1, x4
    // 0xeba578: stur            x4, [fp, #-8]
    // 0xeba57c: r2 = "<\?xml"
    //     0xeba57c: add             x2, PP, #0x26, lsl #12  ; [pp+0x26ab0] "<\?xml"
    //     0xeba580: ldr             x2, [x2, #0xab0]
    // 0xeba584: r0 = resolve()
    //     0xeba584: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeba588: ldur            x0, [fp, #-0x18]
    // 0xeba58c: LoadField: r2 = r0->field_13
    //     0xeba58c: ldur            w2, [x0, #0x13]
    // 0xeba590: DecompressPointer r2
    //     0xeba590: add             x2, x2, HEAP, lsl #32
    // 0xeba594: ldur            x1, [fp, #-0x10]
    // 0xeba598: r0 = addAttributes()
    //     0xeba598: bl              #0xeba5c4  ; [package:xml/src/xml_events/converters/event_encoder.dart] _XmlEventEncoderSink::addAttributes
    // 0xeba59c: ldur            x1, [fp, #-8]
    // 0xeba5a0: r2 = "\?>"
    //     0xeba5a0: add             x2, PP, #0x26, lsl #12  ; [pp+0x26a58] "\?>"
    //     0xeba5a4: ldr             x2, [x2, #0xa58]
    // 0xeba5a8: r0 = resolve()
    //     0xeba5a8: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeba5ac: r0 = Null
    //     0xeba5ac: mov             x0, NULL
    // 0xeba5b0: LeaveFrame
    //     0xeba5b0: mov             SP, fp
    //     0xeba5b4: ldp             fp, lr, [SP], #0x10
    // 0xeba5b8: ret
    //     0xeba5b8: ret             
    // 0xeba5bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba5bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba5c0: b               #0xeba56c
  }
  _ addAttributes(/* No info */) {
    // ** addr: 0xeba5c4, size: 0x228
    // 0xeba5c4: EnterFrame
    //     0xeba5c4: stp             fp, lr, [SP, #-0x10]!
    //     0xeba5c8: mov             fp, SP
    // 0xeba5cc: AllocStack(0x40)
    //     0xeba5cc: sub             SP, SP, #0x40
    // 0xeba5d0: SetupParameters(_XmlEventEncoderSink this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0xeba5d0: stur            x1, [fp, #-8]
    //     0xeba5d4: mov             x16, x2
    //     0xeba5d8: mov             x2, x1
    //     0xeba5dc: mov             x1, x16
    // 0xeba5e0: CheckStackOverflow
    //     0xeba5e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba5e4: cmp             SP, x16
    //     0xeba5e8: b.ls            #0xeba7dc
    // 0xeba5ec: r0 = LoadClassIdInstr(r1)
    //     0xeba5ec: ldur            x0, [x1, #-1]
    //     0xeba5f0: ubfx            x0, x0, #0xc, #0x14
    // 0xeba5f4: r0 = GDT[cid_x0 + 0xd35d]()
    //     0xeba5f4: movz            x17, #0xd35d
    //     0xeba5f8: add             lr, x0, x17
    //     0xeba5fc: ldr             lr, [x21, lr, lsl #3]
    //     0xeba600: blr             lr
    // 0xeba604: mov             x2, x0
    // 0xeba608: ldur            x0, [fp, #-8]
    // 0xeba60c: stur            x2, [fp, #-0x18]
    // 0xeba610: LoadField: r3 = r0->field_7
    //     0xeba610: ldur            w3, [x0, #7]
    // 0xeba614: DecompressPointer r3
    //     0xeba614: add             x3, x3, HEAP, lsl #32
    // 0xeba618: stur            x3, [fp, #-0x10]
    // 0xeba61c: CheckStackOverflow
    //     0xeba61c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba620: cmp             SP, x16
    //     0xeba624: b.ls            #0xeba7e4
    // 0xeba628: r0 = LoadClassIdInstr(r2)
    //     0xeba628: ldur            x0, [x2, #-1]
    //     0xeba62c: ubfx            x0, x0, #0xc, #0x14
    // 0xeba630: mov             x1, x2
    // 0xeba634: r0 = GDT[cid_x0 + 0x1292d]()
    //     0xeba634: movz            x17, #0x292d
    //     0xeba638: movk            x17, #0x1, lsl #16
    //     0xeba63c: add             lr, x0, x17
    //     0xeba640: ldr             lr, [x21, lr, lsl #3]
    //     0xeba644: blr             lr
    // 0xeba648: tbnz            w0, #4, #0xeba7cc
    // 0xeba64c: ldur            x2, [fp, #-0x18]
    // 0xeba650: ldur            x3, [fp, #-0x10]
    // 0xeba654: r0 = LoadClassIdInstr(r2)
    //     0xeba654: ldur            x0, [x2, #-1]
    //     0xeba658: ubfx            x0, x0, #0xc, #0x14
    // 0xeba65c: mov             x1, x2
    // 0xeba660: r0 = GDT[cid_x0 + 0x1384d]()
    //     0xeba660: movz            x17, #0x384d
    //     0xeba664: movk            x17, #0x1, lsl #16
    //     0xeba668: add             lr, x0, x17
    //     0xeba66c: ldr             lr, [x21, lr, lsl #3]
    //     0xeba670: blr             lr
    // 0xeba674: mov             x2, x0
    // 0xeba678: ldur            x1, [fp, #-0x10]
    // 0xeba67c: stur            x2, [fp, #-8]
    // 0xeba680: LoadField: r0 = r1->field_b
    //     0xeba680: ldur            w0, [x1, #0xb]
    // 0xeba684: DecompressPointer r0
    //     0xeba684: add             x0, x0, HEAP, lsl #32
    // 0xeba688: r16 = " "
    //     0xeba688: ldr             x16, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xeba68c: stp             x16, x0, [SP]
    // 0xeba690: ClosureCall
    //     0xeba690: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeba694: ldur            x2, [x0, #0x1f]
    //     0xeba698: blr             x2
    // 0xeba69c: ldur            x1, [fp, #-8]
    // 0xeba6a0: LoadField: r0 = r1->field_7
    //     0xeba6a0: ldur            w0, [x1, #7]
    // 0xeba6a4: DecompressPointer r0
    //     0xeba6a4: add             x0, x0, HEAP, lsl #32
    // 0xeba6a8: ldur            x2, [fp, #-0x10]
    // 0xeba6ac: LoadField: r3 = r2->field_b
    //     0xeba6ac: ldur            w3, [x2, #0xb]
    // 0xeba6b0: DecompressPointer r3
    //     0xeba6b0: add             x3, x3, HEAP, lsl #32
    // 0xeba6b4: stp             x0, x3, [SP]
    // 0xeba6b8: mov             x0, x3
    // 0xeba6bc: ClosureCall
    //     0xeba6bc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeba6c0: ldur            x2, [x0, #0x1f]
    //     0xeba6c4: blr             x2
    // 0xeba6c8: ldur            x1, [fp, #-0x10]
    // 0xeba6cc: LoadField: r0 = r1->field_b
    //     0xeba6cc: ldur            w0, [x1, #0xb]
    // 0xeba6d0: DecompressPointer r0
    //     0xeba6d0: add             x0, x0, HEAP, lsl #32
    // 0xeba6d4: r16 = "="
    //     0xeba6d4: ldr             x16, [PP, #0xde8]  ; [pp+0xde8] "="
    // 0xeba6d8: stp             x16, x0, [SP]
    // 0xeba6dc: ClosureCall
    //     0xeba6dc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeba6e0: ldur            x2, [x0, #0x1f]
    //     0xeba6e4: blr             x2
    // 0xeba6e8: ldur            x0, [fp, #-8]
    // 0xeba6ec: LoadField: r3 = r0->field_b
    //     0xeba6ec: ldur            w3, [x0, #0xb]
    // 0xeba6f0: DecompressPointer r3
    //     0xeba6f0: add             x3, x3, HEAP, lsl #32
    // 0xeba6f4: stur            x3, [fp, #-0x28]
    // 0xeba6f8: LoadField: r4 = r0->field_f
    //     0xeba6f8: ldur            w4, [x0, #0xf]
    // 0xeba6fc: DecompressPointer r4
    //     0xeba6fc: add             x4, x4, HEAP, lsl #32
    // 0xeba700: stur            x4, [fp, #-0x20]
    // 0xeba704: LoadField: r0 = r4->field_13
    //     0xeba704: ldur            w0, [x4, #0x13]
    // 0xeba708: DecompressPointer r0
    //     0xeba708: add             x0, x0, HEAP, lsl #32
    // 0xeba70c: stur            x0, [fp, #-8]
    // 0xeba710: r1 = Null
    //     0xeba710: mov             x1, NULL
    // 0xeba714: r2 = 6
    //     0xeba714: movz            x2, #0x6
    // 0xeba718: r0 = AllocateArray()
    //     0xeba718: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeba71c: mov             x4, x0
    // 0xeba720: ldur            x0, [fp, #-8]
    // 0xeba724: stur            x4, [fp, #-0x30]
    // 0xeba728: StoreField: r4->field_f = r0
    //     0xeba728: stur            w0, [x4, #0xf]
    // 0xeba72c: ldur            x2, [fp, #-0x28]
    // 0xeba730: ldur            x3, [fp, #-0x20]
    // 0xeba734: r1 = Instance_XmlDefaultEntityMapping
    //     0xeba734: add             x1, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xeba738: ldr             x1, [x1, #0x5c0]
    // 0xeba73c: r0 = encodeAttributeValue()
    //     0xeba73c: bl              #0xce05ac  ; [package:xml/src/xml/entities/default_mapping.dart] XmlDefaultEntityMapping::encodeAttributeValue
    // 0xeba740: ldur            x1, [fp, #-0x30]
    // 0xeba744: ArrayStore: r1[1] = r0  ; List_4
    //     0xeba744: add             x25, x1, #0x13
    //     0xeba748: str             w0, [x25]
    //     0xeba74c: tbz             w0, #0, #0xeba768
    //     0xeba750: ldurb           w16, [x1, #-1]
    //     0xeba754: ldurb           w17, [x0, #-1]
    //     0xeba758: and             x16, x17, x16, lsr #2
    //     0xeba75c: tst             x16, HEAP, lsr #32
    //     0xeba760: b.eq            #0xeba768
    //     0xeba764: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeba768: ldur            x1, [fp, #-0x30]
    // 0xeba76c: ldur            x0, [fp, #-8]
    // 0xeba770: ArrayStore: r1[2] = r0  ; List_4
    //     0xeba770: add             x25, x1, #0x17
    //     0xeba774: str             w0, [x25]
    //     0xeba778: tbz             w0, #0, #0xeba794
    //     0xeba77c: ldurb           w16, [x1, #-1]
    //     0xeba780: ldurb           w17, [x0, #-1]
    //     0xeba784: and             x16, x17, x16, lsr #2
    //     0xeba788: tst             x16, HEAP, lsr #32
    //     0xeba78c: b.eq            #0xeba794
    //     0xeba790: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xeba794: ldur            x16, [fp, #-0x30]
    // 0xeba798: str             x16, [SP]
    // 0xeba79c: r0 = _interpolate()
    //     0xeba79c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xeba7a0: ldur            x1, [fp, #-0x10]
    // 0xeba7a4: LoadField: r2 = r1->field_b
    //     0xeba7a4: ldur            w2, [x1, #0xb]
    // 0xeba7a8: DecompressPointer r2
    //     0xeba7a8: add             x2, x2, HEAP, lsl #32
    // 0xeba7ac: stp             x0, x2, [SP]
    // 0xeba7b0: mov             x0, x2
    // 0xeba7b4: ClosureCall
    //     0xeba7b4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xeba7b8: ldur            x2, [x0, #0x1f]
    //     0xeba7bc: blr             x2
    // 0xeba7c0: ldur            x2, [fp, #-0x18]
    // 0xeba7c4: ldur            x3, [fp, #-0x10]
    // 0xeba7c8: b               #0xeba61c
    // 0xeba7cc: r0 = Null
    //     0xeba7cc: mov             x0, NULL
    // 0xeba7d0: LeaveFrame
    //     0xeba7d0: mov             SP, fp
    //     0xeba7d4: ldp             fp, lr, [SP], #0x10
    // 0xeba7d8: ret
    //     0xeba7d8: ret             
    // 0xeba7dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba7dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba7e0: b               #0xeba5ec
    // 0xeba7e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba7e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba7e8: b               #0xeba628
  }
  _ visitDoctypeEvent(/* No info */) {
    // ** addr: 0xebaa60, size: 0x104
    // 0xebaa60: EnterFrame
    //     0xebaa60: stp             fp, lr, [SP, #-0x10]!
    //     0xebaa64: mov             fp, SP
    // 0xebaa68: AllocStack(0x20)
    //     0xebaa68: sub             SP, SP, #0x20
    // 0xebaa6c: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xebaa6c: mov             x0, x2
    //     0xebaa70: stur            x2, [fp, #-0x10]
    // 0xebaa74: CheckStackOverflow
    //     0xebaa74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebaa78: cmp             SP, x16
    //     0xebaa7c: b.ls            #0xebab5c
    // 0xebaa80: LoadField: r3 = r1->field_7
    //     0xebaa80: ldur            w3, [x1, #7]
    // 0xebaa84: DecompressPointer r3
    //     0xebaa84: add             x3, x3, HEAP, lsl #32
    // 0xebaa88: mov             x1, x3
    // 0xebaa8c: stur            x3, [fp, #-8]
    // 0xebaa90: r2 = "<!DOCTYPE"
    //     0xebaa90: add             x2, PP, #0x26, lsl #12  ; [pp+0x26670] "<!DOCTYPE"
    //     0xebaa94: ldr             x2, [x2, #0x670]
    // 0xebaa98: r0 = resolve()
    //     0xebaa98: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebaa9c: ldur            x1, [fp, #-8]
    // 0xebaaa0: r2 = " "
    //     0xebaaa0: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xebaaa4: r0 = resolve()
    //     0xebaaa4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebaaa8: ldur            x0, [fp, #-0x10]
    // 0xebaaac: LoadField: r2 = r0->field_13
    //     0xebaaac: ldur            w2, [x0, #0x13]
    // 0xebaab0: DecompressPointer r2
    //     0xebaab0: add             x2, x2, HEAP, lsl #32
    // 0xebaab4: ldur            x1, [fp, #-8]
    // 0xebaab8: r0 = resolve()
    //     0xebaab8: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebaabc: ldur            x0, [fp, #-0x10]
    // 0xebaac0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xebaac0: ldur            w3, [x0, #0x17]
    // 0xebaac4: DecompressPointer r3
    //     0xebaac4: add             x3, x3, HEAP, lsl #32
    // 0xebaac8: stur            x3, [fp, #-0x18]
    // 0xebaacc: cmp             w3, NULL
    // 0xebaad0: b.eq            #0xebaaf8
    // 0xebaad4: ldur            x1, [fp, #-8]
    // 0xebaad8: r2 = " "
    //     0xebaad8: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xebaadc: r0 = resolve()
    //     0xebaadc: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebaae0: ldur            x16, [fp, #-0x18]
    // 0xebaae4: str             x16, [SP]
    // 0xebaae8: r0 = toString()
    //     0xebaae8: bl              #0xc4345c  ; [package:xml/src/xml/dtd/external_id.dart] DtdExternalId::toString
    // 0xebaaec: ldur            x1, [fp, #-8]
    // 0xebaaf0: mov             x2, x0
    // 0xebaaf4: r0 = resolve()
    //     0xebaaf4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebaaf8: ldur            x0, [fp, #-0x10]
    // 0xebaafc: LoadField: r3 = r0->field_1b
    //     0xebaafc: ldur            w3, [x0, #0x1b]
    // 0xebab00: DecompressPointer r3
    //     0xebab00: add             x3, x3, HEAP, lsl #32
    // 0xebab04: stur            x3, [fp, #-0x18]
    // 0xebab08: cmp             w3, NULL
    // 0xebab0c: b.eq            #0xebab40
    // 0xebab10: ldur            x1, [fp, #-8]
    // 0xebab14: r2 = " "
    //     0xebab14: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xebab18: r0 = resolve()
    //     0xebab18: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebab1c: ldur            x1, [fp, #-8]
    // 0xebab20: r2 = "["
    //     0xebab20: ldr             x2, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0xebab24: r0 = resolve()
    //     0xebab24: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebab28: ldur            x1, [fp, #-8]
    // 0xebab2c: ldur            x2, [fp, #-0x18]
    // 0xebab30: r0 = resolve()
    //     0xebab30: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebab34: ldur            x1, [fp, #-8]
    // 0xebab38: r2 = "]"
    //     0xebab38: ldr             x2, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0xebab3c: r0 = resolve()
    //     0xebab3c: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebab40: ldur            x1, [fp, #-8]
    // 0xebab44: r2 = ">"
    //     0xebab44: ldr             x2, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xebab48: r0 = resolve()
    //     0xebab48: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebab4c: r0 = Null
    //     0xebab4c: mov             x0, NULL
    // 0xebab50: LeaveFrame
    //     0xebab50: mov             SP, fp
    //     0xebab54: ldp             fp, lr, [SP], #0x10
    // 0xebab58: ret
    //     0xebab58: ret             
    // 0xebab5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebab5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebab60: b               #0xebaa80
  }
  _ visitEndElementEvent(/* No info */) {
    // ** addr: 0xebad88, size: 0x74
    // 0xebad88: EnterFrame
    //     0xebad88: stp             fp, lr, [SP, #-0x10]!
    //     0xebad8c: mov             fp, SP
    // 0xebad90: AllocStack(0x10)
    //     0xebad90: sub             SP, SP, #0x10
    // 0xebad94: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xebad94: mov             x0, x2
    //     0xebad98: stur            x2, [fp, #-0x10]
    // 0xebad9c: CheckStackOverflow
    //     0xebad9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebada0: cmp             SP, x16
    //     0xebada4: b.ls            #0xebadf4
    // 0xebada8: LoadField: r3 = r1->field_7
    //     0xebada8: ldur            w3, [x1, #7]
    // 0xebadac: DecompressPointer r3
    //     0xebadac: add             x3, x3, HEAP, lsl #32
    // 0xebadb0: mov             x1, x3
    // 0xebadb4: stur            x3, [fp, #-8]
    // 0xebadb8: r2 = "</"
    //     0xebadb8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b70] "</"
    //     0xebadbc: ldr             x2, [x2, #0xb70]
    // 0xebadc0: r0 = resolve()
    //     0xebadc0: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebadc4: ldur            x0, [fp, #-0x10]
    // 0xebadc8: LoadField: r2 = r0->field_13
    //     0xebadc8: ldur            w2, [x0, #0x13]
    // 0xebadcc: DecompressPointer r2
    //     0xebadcc: add             x2, x2, HEAP, lsl #32
    // 0xebadd0: ldur            x1, [fp, #-8]
    // 0xebadd4: r0 = resolve()
    //     0xebadd4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebadd8: ldur            x1, [fp, #-8]
    // 0xebaddc: r2 = ">"
    //     0xebaddc: ldr             x2, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xebade0: r0 = resolve()
    //     0xebade0: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebade4: r0 = Null
    //     0xebade4: mov             x0, NULL
    // 0xebade8: LeaveFrame
    //     0xebade8: mov             SP, fp
    //     0xebadec: ldp             fp, lr, [SP], #0x10
    // 0xebadf0: ret
    //     0xebadf0: ret             
    // 0xebadf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebadf4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebadf8: b               #0xebada8
  }
  _ visitProcessingEvent(/* No info */) {
    // ** addr: 0xebb028, size: 0xa8
    // 0xebb028: EnterFrame
    //     0xebb028: stp             fp, lr, [SP, #-0x10]!
    //     0xebb02c: mov             fp, SP
    // 0xebb030: AllocStack(0x18)
    //     0xebb030: sub             SP, SP, #0x18
    // 0xebb034: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xebb034: mov             x0, x2
    //     0xebb038: stur            x2, [fp, #-0x10]
    // 0xebb03c: CheckStackOverflow
    //     0xebb03c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebb040: cmp             SP, x16
    //     0xebb044: b.ls            #0xebb0c8
    // 0xebb048: LoadField: r3 = r1->field_7
    //     0xebb048: ldur            w3, [x1, #7]
    // 0xebb04c: DecompressPointer r3
    //     0xebb04c: add             x3, x3, HEAP, lsl #32
    // 0xebb050: mov             x1, x3
    // 0xebb054: stur            x3, [fp, #-8]
    // 0xebb058: r2 = "<\?"
    //     0xebb058: add             x2, PP, #0x26, lsl #12  ; [pp+0x26a50] "<\?"
    //     0xebb05c: ldr             x2, [x2, #0xa50]
    // 0xebb060: r0 = resolve()
    //     0xebb060: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb064: ldur            x0, [fp, #-0x10]
    // 0xebb068: LoadField: r2 = r0->field_13
    //     0xebb068: ldur            w2, [x0, #0x13]
    // 0xebb06c: DecompressPointer r2
    //     0xebb06c: add             x2, x2, HEAP, lsl #32
    // 0xebb070: ldur            x1, [fp, #-8]
    // 0xebb074: r0 = resolve()
    //     0xebb074: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb078: ldur            x0, [fp, #-0x10]
    // 0xebb07c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xebb07c: ldur            w3, [x0, #0x17]
    // 0xebb080: DecompressPointer r3
    //     0xebb080: add             x3, x3, HEAP, lsl #32
    // 0xebb084: stur            x3, [fp, #-0x18]
    // 0xebb088: LoadField: r0 = r3->field_7
    //     0xebb088: ldur            w0, [x3, #7]
    // 0xebb08c: cbz             w0, #0xebb0a8
    // 0xebb090: ldur            x1, [fp, #-8]
    // 0xebb094: r2 = " "
    //     0xebb094: ldr             x2, [PP, #0x4e0]  ; [pp+0x4e0] " "
    // 0xebb098: r0 = resolve()
    //     0xebb098: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb09c: ldur            x1, [fp, #-8]
    // 0xebb0a0: ldur            x2, [fp, #-0x18]
    // 0xebb0a4: r0 = resolve()
    //     0xebb0a4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb0a8: ldur            x1, [fp, #-8]
    // 0xebb0ac: r2 = "\?>"
    //     0xebb0ac: add             x2, PP, #0x26, lsl #12  ; [pp+0x26a58] "\?>"
    //     0xebb0b0: ldr             x2, [x2, #0xa58]
    // 0xebb0b4: r0 = resolve()
    //     0xebb0b4: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb0b8: r0 = Null
    //     0xebb0b8: mov             x0, NULL
    // 0xebb0bc: LeaveFrame
    //     0xebb0bc: mov             SP, fp
    //     0xebb0c0: ldp             fp, lr, [SP], #0x10
    // 0xebb0c4: ret
    //     0xebb0c4: ret             
    // 0xebb0c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb0c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb0cc: b               #0xebb048
  }
  _ visitStartElementEvent(/* No info */) {
    // ** addr: 0xebb158, size: 0xb0
    // 0xebb158: EnterFrame
    //     0xebb158: stp             fp, lr, [SP, #-0x10]!
    //     0xebb15c: mov             fp, SP
    // 0xebb160: AllocStack(0x18)
    //     0xebb160: sub             SP, SP, #0x18
    // 0xebb164: SetupParameters(_XmlEventEncoderSink this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xebb164: mov             x3, x1
    //     0xebb168: mov             x0, x2
    //     0xebb16c: stur            x1, [fp, #-0x10]
    //     0xebb170: stur            x2, [fp, #-0x18]
    // 0xebb174: CheckStackOverflow
    //     0xebb174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebb178: cmp             SP, x16
    //     0xebb17c: b.ls            #0xebb200
    // 0xebb180: LoadField: r4 = r3->field_7
    //     0xebb180: ldur            w4, [x3, #7]
    // 0xebb184: DecompressPointer r4
    //     0xebb184: add             x4, x4, HEAP, lsl #32
    // 0xebb188: mov             x1, x4
    // 0xebb18c: stur            x4, [fp, #-8]
    // 0xebb190: r2 = "<"
    //     0xebb190: ldr             x2, [PP, #0x510]  ; [pp+0x510] "<"
    // 0xebb194: r0 = resolve()
    //     0xebb194: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb198: ldur            x0, [fp, #-0x18]
    // 0xebb19c: LoadField: r2 = r0->field_13
    //     0xebb19c: ldur            w2, [x0, #0x13]
    // 0xebb1a0: DecompressPointer r2
    //     0xebb1a0: add             x2, x2, HEAP, lsl #32
    // 0xebb1a4: ldur            x1, [fp, #-8]
    // 0xebb1a8: r0 = resolve()
    //     0xebb1a8: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb1ac: ldur            x0, [fp, #-0x18]
    // 0xebb1b0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xebb1b0: ldur            w2, [x0, #0x17]
    // 0xebb1b4: DecompressPointer r2
    //     0xebb1b4: add             x2, x2, HEAP, lsl #32
    // 0xebb1b8: ldur            x1, [fp, #-0x10]
    // 0xebb1bc: r0 = addAttributes()
    //     0xebb1bc: bl              #0xeba5c4  ; [package:xml/src/xml_events/converters/event_encoder.dart] _XmlEventEncoderSink::addAttributes
    // 0xebb1c0: ldur            x0, [fp, #-0x18]
    // 0xebb1c4: LoadField: r1 = r0->field_1b
    //     0xebb1c4: ldur            w1, [x0, #0x1b]
    // 0xebb1c8: DecompressPointer r1
    //     0xebb1c8: add             x1, x1, HEAP, lsl #32
    // 0xebb1cc: tbnz            w1, #4, #0xebb1e4
    // 0xebb1d0: ldur            x1, [fp, #-8]
    // 0xebb1d4: r2 = "/>"
    //     0xebb1d4: add             x2, PP, #0x26, lsl #12  ; [pp+0x26b88] "/>"
    //     0xebb1d8: ldr             x2, [x2, #0xb88]
    // 0xebb1dc: r0 = resolve()
    //     0xebb1dc: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb1e0: b               #0xebb1f0
    // 0xebb1e4: ldur            x1, [fp, #-8]
    // 0xebb1e8: r2 = ">"
    //     0xebb1e8: ldr             x2, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0xebb1ec: r0 = resolve()
    //     0xebb1ec: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb1f0: r0 = Null
    //     0xebb1f0: mov             x0, NULL
    // 0xebb1f4: LeaveFrame
    //     0xebb1f4: mov             SP, fp
    //     0xebb1f8: ldp             fp, lr, [SP], #0x10
    // 0xebb1fc: ret
    //     0xebb1fc: ret             
    // 0xebb200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb200: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb204: b               #0xebb180
  }
  _ visitTextEvent(/* No info */) {
    // ** addr: 0xebb3ec, size: 0x80
    // 0xebb3ec: EnterFrame
    //     0xebb3ec: stp             fp, lr, [SP, #-0x10]!
    //     0xebb3f0: mov             fp, SP
    // 0xebb3f4: AllocStack(0x8)
    //     0xebb3f4: sub             SP, SP, #8
    // 0xebb3f8: SetupParameters(_XmlEventEncoderSink this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xebb3f8: mov             x0, x1
    //     0xebb3fc: mov             x1, x2
    // 0xebb400: CheckStackOverflow
    //     0xebb400: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebb404: cmp             SP, x16
    //     0xebb408: b.ls            #0xebb464
    // 0xebb40c: LoadField: r2 = r0->field_7
    //     0xebb40c: ldur            w2, [x0, #7]
    // 0xebb410: DecompressPointer r2
    //     0xebb410: add             x2, x2, HEAP, lsl #32
    // 0xebb414: stur            x2, [fp, #-8]
    // 0xebb418: LoadField: r0 = r1->field_1b
    //     0xebb418: ldur            w0, [x1, #0x1b]
    // 0xebb41c: DecompressPointer r0
    //     0xebb41c: add             x0, x0, HEAP, lsl #32
    // 0xebb420: r16 = Sentinel
    //     0xebb420: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xebb424: cmp             w0, w16
    // 0xebb428: b.ne            #0xebb438
    // 0xebb42c: r2 = value
    //     0xebb42c: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a20] Field <XmlRawTextEvent.value>: late final (offset: 0x1c)
    //     0xebb430: ldr             x2, [x2, #0xa20]
    // 0xebb434: r0 = InitLateFinalInstanceField()
    //     0xebb434: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xebb438: mov             x2, x0
    // 0xebb43c: r1 = Instance_XmlDefaultEntityMapping
    //     0xebb43c: add             x1, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xebb440: ldr             x1, [x1, #0x5c0]
    // 0xebb444: r0 = encodeText()
    //     0xebb444: bl              #0xce13b8  ; [package:xml/src/xml/entities/default_mapping.dart] XmlDefaultEntityMapping::encodeText
    // 0xebb448: ldur            x1, [fp, #-8]
    // 0xebb44c: mov             x2, x0
    // 0xebb450: r0 = resolve()
    //     0xebb450: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xebb454: r0 = Null
    //     0xebb454: mov             x0, NULL
    // 0xebb458: LeaveFrame
    //     0xebb458: mov             SP, fp
    //     0xebb45c: ldp             fp, lr, [SP], #0x10
    // 0xebb460: ret
    //     0xebb460: ret             
    // 0xebb464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb464: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb468: b               #0xebb40c
  }
}

// class id: 6442, size: 0x10, field offset: 0xc
class XmlEventEncoder extends Converter<dynamic, dynamic> {

  _ startChunkedConversion(/* No info */) {
    // ** addr: 0xce5db0, size: 0x34
    // 0xce5db0: EnterFrame
    //     0xce5db0: stp             fp, lr, [SP, #-0x10]!
    //     0xce5db4: mov             fp, SP
    // 0xce5db8: AllocStack(0x8)
    //     0xce5db8: sub             SP, SP, #8
    // 0xce5dbc: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xce5dbc: stur            x2, [fp, #-8]
    // 0xce5dc0: r0 = _XmlEventEncoderSink()
    //     0xce5dc0: bl              #0xce5de4  ; Allocate_XmlEventEncoderSinkStub -> _XmlEventEncoderSink (size=0x10)
    // 0xce5dc4: ldur            x1, [fp, #-8]
    // 0xce5dc8: StoreField: r0->field_7 = r1
    //     0xce5dc8: stur            w1, [x0, #7]
    // 0xce5dcc: r1 = Instance_XmlDefaultEntityMapping
    //     0xce5dcc: add             x1, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xce5dd0: ldr             x1, [x1, #0x5c0]
    // 0xce5dd4: StoreField: r0->field_b = r1
    //     0xce5dd4: stur            w1, [x0, #0xb]
    // 0xce5dd8: LeaveFrame
    //     0xce5dd8: mov             SP, fp
    //     0xce5ddc: ldp             fp, lr, [SP], #0x10
    // 0xce5de0: ret
    //     0xce5de0: ret             
  }
  _ convert(/* No info */) {
    // ** addr: 0xcfa16c, size: 0x168
    // 0xcfa16c: EnterFrame
    //     0xcfa16c: stp             fp, lr, [SP, #-0x10]!
    //     0xcfa170: mov             fp, SP
    // 0xcfa174: AllocStack(0x30)
    //     0xcfa174: sub             SP, SP, #0x30
    // 0xcfa178: SetupParameters(XmlEventEncoder this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xcfa178: mov             x4, x1
    //     0xcfa17c: mov             x3, x2
    //     0xcfa180: stur            x1, [fp, #-8]
    //     0xcfa184: stur            x2, [fp, #-0x10]
    // 0xcfa188: CheckStackOverflow
    //     0xcfa188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcfa18c: cmp             SP, x16
    //     0xcfa190: b.ls            #0xcfa2c0
    // 0xcfa194: mov             x0, x3
    // 0xcfa198: r2 = Null
    //     0xcfa198: mov             x2, NULL
    // 0xcfa19c: r1 = Null
    //     0xcfa19c: mov             x1, NULL
    // 0xcfa1a0: r8 = List<XmlEvent>
    //     0xcfa1a0: add             x8, PP, #0x31, lsl #12  ; [pp+0x310d8] Type: List<XmlEvent>
    //     0xcfa1a4: ldr             x8, [x8, #0xd8]
    // 0xcfa1a8: r3 = Null
    //     0xcfa1a8: add             x3, PP, #0x31, lsl #12  ; [pp+0x310e0] Null
    //     0xcfa1ac: ldr             x3, [x3, #0xe0]
    // 0xcfa1b0: r0 = List<XmlEvent>()
    //     0xcfa1b0: bl              #0x75529c  ; IsType_List<XmlEvent>_Stub
    // 0xcfa1b4: r0 = StringBuffer()
    //     0xcfa1b4: bl              #0x6013a0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xcfa1b8: mov             x1, x0
    // 0xcfa1bc: stur            x0, [fp, #-0x18]
    // 0xcfa1c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xcfa1c0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xcfa1c4: r0 = StringBuffer()
    //     0xcfa1c4: bl              #0x600b48  ; [dart:core] StringBuffer::StringBuffer
    // 0xcfa1c8: r1 = <String>
    //     0xcfa1c8: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0xcfa1cc: r0 = ConversionSink()
    //     0xcfa1cc: bl              #0xb15460  ; AllocateConversionSinkStub -> ConversionSink<X0> (size=0x10)
    // 0xcfa1d0: ldur            x2, [fp, #-0x18]
    // 0xcfa1d4: r1 = Function 'write':.
    //     0xcfa1d4: add             x1, PP, #0x31, lsl #12  ; [pp+0x310f0] AnonymousClosure: (0x600b0c), in [dart:core] StringBuffer::write (0xd5bc18)
    //     0xcfa1d8: ldr             x1, [x1, #0xf0]
    // 0xcfa1dc: stur            x0, [fp, #-0x20]
    // 0xcfa1e0: r0 = AllocateClosure()
    //     0xcfa1e0: bl              #0xec1630  ; AllocateClosureStub
    // 0xcfa1e4: ldur            x2, [fp, #-0x20]
    // 0xcfa1e8: StoreField: r2->field_b = r0
    //     0xcfa1e8: stur            w0, [x2, #0xb]
    // 0xcfa1ec: ldur            x1, [fp, #-8]
    // 0xcfa1f0: r0 = startChunkedConversion()
    //     0xcfa1f0: bl              #0xce5db0  ; [package:xml/src/xml_events/converters/event_encoder.dart] XmlEventEncoder::startChunkedConversion
    // 0xcfa1f4: mov             x4, x0
    // 0xcfa1f8: ldur            x3, [fp, #-0x10]
    // 0xcfa1fc: stur            x4, [fp, #-0x20]
    // 0xcfa200: LoadField: r5 = r3->field_b
    //     0xcfa200: ldur            w5, [x3, #0xb]
    // 0xcfa204: stur            x5, [fp, #-8]
    // 0xcfa208: r0 = LoadInt32Instr(r5)
    //     0xcfa208: sbfx            x0, x5, #1, #0x1f
    // 0xcfa20c: r6 = 0
    //     0xcfa20c: movz            x6, #0
    // 0xcfa210: stur            x6, [fp, #-0x28]
    // 0xcfa214: CheckStackOverflow
    //     0xcfa214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xcfa218: cmp             SP, x16
    //     0xcfa21c: b.ls            #0xcfa2c8
    // 0xcfa220: cmp             x6, x0
    // 0xcfa224: b.ge            #0xcfa284
    // 0xcfa228: mov             x1, x6
    // 0xcfa22c: cmp             x1, x0
    // 0xcfa230: b.hs            #0xcfa2d0
    // 0xcfa234: LoadField: r0 = r3->field_f
    //     0xcfa234: ldur            w0, [x3, #0xf]
    // 0xcfa238: DecompressPointer r0
    //     0xcfa238: add             x0, x0, HEAP, lsl #32
    // 0xcfa23c: ArrayLoad: r2 = r0[r6]  ; Unknown_4
    //     0xcfa23c: add             x16, x0, x6, lsl #2
    //     0xcfa240: ldur            w2, [x16, #0xf]
    // 0xcfa244: DecompressPointer r2
    //     0xcfa244: add             x2, x2, HEAP, lsl #32
    // 0xcfa248: mov             x1, x4
    // 0xcfa24c: r0 = visit()
    //     0xcfa24c: bl              #0x755470  ; [package:xml/src/xml_events/converters/event_encoder.dart] __XmlEventEncoderSink&Object&XmlEventVisitor::visit
    // 0xcfa250: ldur            x1, [fp, #-0x10]
    // 0xcfa254: LoadField: r0 = r1->field_b
    //     0xcfa254: ldur            w0, [x1, #0xb]
    // 0xcfa258: ldur            x2, [fp, #-8]
    // 0xcfa25c: cmp             w0, w2
    // 0xcfa260: b.ne            #0xcfa2a4
    // 0xcfa264: ldur            x3, [fp, #-0x28]
    // 0xcfa268: add             x6, x3, #1
    // 0xcfa26c: r3 = LoadInt32Instr(r0)
    //     0xcfa26c: sbfx            x3, x0, #1, #0x1f
    // 0xcfa270: mov             x0, x3
    // 0xcfa274: mov             x3, x1
    // 0xcfa278: ldur            x4, [fp, #-0x20]
    // 0xcfa27c: mov             x5, x2
    // 0xcfa280: b               #0xcfa210
    // 0xcfa284: ldur            x1, [fp, #-0x20]
    // 0xcfa288: r0 = forceCompileTimeTreeShaking()
    //     0xcfa288: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xcfa28c: ldur            x16, [fp, #-0x18]
    // 0xcfa290: str             x16, [SP]
    // 0xcfa294: r0 = toString()
    //     0xcfa294: bl              #0xc00b70  ; [dart:core] StringBuffer::toString
    // 0xcfa298: LeaveFrame
    //     0xcfa298: mov             SP, fp
    //     0xcfa29c: ldp             fp, lr, [SP], #0x10
    // 0xcfa2a0: ret
    //     0xcfa2a0: ret             
    // 0xcfa2a4: r0 = ConcurrentModificationError()
    //     0xcfa2a4: bl              #0x6030fc  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xcfa2a8: mov             x1, x0
    // 0xcfa2ac: ldur            x0, [fp, #-0x10]
    // 0xcfa2b0: StoreField: r1->field_b = r0
    //     0xcfa2b0: stur            w0, [x1, #0xb]
    // 0xcfa2b4: mov             x0, x1
    // 0xcfa2b8: r0 = Throw()
    //     0xcfa2b8: bl              #0xec04b8  ; ThrowStub
    // 0xcfa2bc: brk             #0
    // 0xcfa2c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfa2c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfa2c4: b               #0xcfa194
    // 0xcfa2c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xcfa2c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xcfa2cc: b               #0xcfa220
    // 0xcfa2d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xcfa2d0: bl              #0xec284c  ; RangeErrorSharedWithoutFPURegsStub
  }
}
