// lib: , url: package:xml/src/xml_events/converters/node_decoder.dart

// class id: 1051330, size: 0x8
class :: {
}

// class id: 212, size: 0x10, field offset: 0x8
class _XmlNodeDecoderSink extends __XmlEventEncoderSink&Object&XmlEventVisitor
    implements ChunkedConversionSink<X0> {

  _ visitCDATAEvent(/* No info */) {
    // ** addr: 0xeb9e74, size: 0x74
    // 0xeb9e74: EnterFrame
    //     0xeb9e74: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9e78: mov             fp, SP
    // 0xeb9e7c: AllocStack(0x20)
    //     0xeb9e7c: sub             SP, SP, #0x20
    // 0xeb9e80: SetupParameters(_XmlNodeDecoderSink this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xeb9e80: mov             x3, x2
    //     0xeb9e84: stur            x1, [fp, #-0x10]
    //     0xeb9e88: stur            x2, [fp, #-0x18]
    // 0xeb9e8c: CheckStackOverflow
    //     0xeb9e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9e90: cmp             SP, x16
    //     0xeb9e94: b.ls            #0xeb9ee0
    // 0xeb9e98: LoadField: r0 = r3->field_13
    //     0xeb9e98: ldur            w0, [x3, #0x13]
    // 0xeb9e9c: DecompressPointer r0
    //     0xeb9e9c: add             x0, x0, HEAP, lsl #32
    // 0xeb9ea0: stur            x0, [fp, #-8]
    // 0xeb9ea4: r0 = XmlCDATA()
    //     0xeb9ea4: bl              #0xeb7fe8  ; AllocateXmlCDATAStub -> XmlCDATA (size=0x10)
    // 0xeb9ea8: mov             x2, x0
    // 0xeb9eac: ldur            x0, [fp, #-8]
    // 0xeb9eb0: stur            x2, [fp, #-0x20]
    // 0xeb9eb4: StoreField: r2->field_b = r0
    //     0xeb9eb4: stur            w0, [x2, #0xb]
    // 0xeb9eb8: mov             x1, x2
    // 0xeb9ebc: r0 = forceCompileTimeTreeShaking()
    //     0xeb9ebc: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeb9ec0: ldur            x1, [fp, #-0x10]
    // 0xeb9ec4: ldur            x2, [fp, #-0x20]
    // 0xeb9ec8: ldur            x3, [fp, #-0x18]
    // 0xeb9ecc: r0 = commit()
    //     0xeb9ecc: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xeb9ed0: r0 = Null
    //     0xeb9ed0: mov             x0, NULL
    // 0xeb9ed4: LeaveFrame
    //     0xeb9ed4: mov             SP, fp
    //     0xeb9ed8: ldp             fp, lr, [SP], #0x10
    // 0xeb9edc: ret
    //     0xeb9edc: ret             
    // 0xeb9ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9ee0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9ee4: b               #0xeb9e98
  }
  _ commit(/* No info */) {
    // ** addr: 0xeb9ee8, size: 0xc8
    // 0xeb9ee8: EnterFrame
    //     0xeb9ee8: stp             fp, lr, [SP, #-0x10]!
    //     0xeb9eec: mov             fp, SP
    // 0xeb9ef0: AllocStack(0x28)
    //     0xeb9ef0: sub             SP, SP, #0x28
    // 0xeb9ef4: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xeb9ef4: mov             x0, x2
    //     0xeb9ef8: stur            x2, [fp, #-0x10]
    // 0xeb9efc: CheckStackOverflow
    //     0xeb9efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9f00: cmp             SP, x16
    //     0xeb9f04: b.ls            #0xeb9fa0
    // 0xeb9f08: LoadField: r2 = r1->field_b
    //     0xeb9f08: ldur            w2, [x1, #0xb]
    // 0xeb9f0c: DecompressPointer r2
    //     0xeb9f0c: add             x2, x2, HEAP, lsl #32
    // 0xeb9f10: cmp             w2, NULL
    // 0xeb9f14: b.ne            #0xeb9f80
    // 0xeb9f18: r3 = 2
    //     0xeb9f18: movz            x3, #0x2
    // 0xeb9f1c: CheckStackOverflow
    //     0xeb9f1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb9f20: cmp             SP, x16
    //     0xeb9f24: b.ls            #0xeb9fa8
    // 0xeb9f28: LoadField: r4 = r1->field_7
    //     0xeb9f28: ldur            w4, [x1, #7]
    // 0xeb9f2c: DecompressPointer r4
    //     0xeb9f2c: add             x4, x4, HEAP, lsl #32
    // 0xeb9f30: mov             x2, x3
    // 0xeb9f34: stur            x4, [fp, #-8]
    // 0xeb9f38: r1 = Null
    //     0xeb9f38: mov             x1, NULL
    // 0xeb9f3c: r0 = AllocateArray()
    //     0xeb9f3c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xeb9f40: mov             x2, x0
    // 0xeb9f44: ldur            x0, [fp, #-0x10]
    // 0xeb9f48: stur            x2, [fp, #-0x18]
    // 0xeb9f4c: StoreField: r2->field_f = r0
    //     0xeb9f4c: stur            w0, [x2, #0xf]
    // 0xeb9f50: r1 = <XmlNode>
    //     0xeb9f50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xeb9f54: ldr             x1, [x1, #0x418]
    // 0xeb9f58: r0 = AllocateGrowableArray()
    //     0xeb9f58: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xeb9f5c: mov             x1, x0
    // 0xeb9f60: ldur            x0, [fp, #-0x18]
    // 0xeb9f64: StoreField: r1->field_f = r0
    //     0xeb9f64: stur            w0, [x1, #0xf]
    // 0xeb9f68: r0 = 2
    //     0xeb9f68: movz            x0, #0x2
    // 0xeb9f6c: StoreField: r1->field_b = r0
    //     0xeb9f6c: stur            w0, [x1, #0xb]
    // 0xeb9f70: mov             x2, x1
    // 0xeb9f74: ldur            x1, [fp, #-8]
    // 0xeb9f78: r0 = resolve()
    //     0xeb9f78: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0xeb9f7c: b               #0xeb9f90
    // 0xeb9f80: LoadField: r1 = r2->field_f
    //     0xeb9f80: ldur            w1, [x2, #0xf]
    // 0xeb9f84: DecompressPointer r1
    //     0xeb9f84: add             x1, x1, HEAP, lsl #32
    // 0xeb9f88: stp             x0, x1, [SP]
    // 0xeb9f8c: r0 = add()
    //     0xeb9f8c: bl              #0x671d18  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::add
    // 0xeb9f90: r0 = Null
    //     0xeb9f90: mov             x0, NULL
    // 0xeb9f94: LeaveFrame
    //     0xeb9f94: mov             SP, fp
    //     0xeb9f98: ldp             fp, lr, [SP], #0x10
    // 0xeb9f9c: ret
    //     0xeb9f9c: ret             
    // 0xeb9fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9fa0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9fa4: b               #0xeb9f08
    // 0xeb9fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb9fa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb9fac: b               #0xeb9f28
  }
  _ visitCommentEvent(/* No info */) {
    // ** addr: 0xeba4d0, size: 0x74
    // 0xeba4d0: EnterFrame
    //     0xeba4d0: stp             fp, lr, [SP, #-0x10]!
    //     0xeba4d4: mov             fp, SP
    // 0xeba4d8: AllocStack(0x20)
    //     0xeba4d8: sub             SP, SP, #0x20
    // 0xeba4dc: SetupParameters(_XmlNodeDecoderSink this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xeba4dc: mov             x3, x2
    //     0xeba4e0: stur            x1, [fp, #-0x10]
    //     0xeba4e4: stur            x2, [fp, #-0x18]
    // 0xeba4e8: CheckStackOverflow
    //     0xeba4e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba4ec: cmp             SP, x16
    //     0xeba4f0: b.ls            #0xeba53c
    // 0xeba4f4: LoadField: r0 = r3->field_13
    //     0xeba4f4: ldur            w0, [x3, #0x13]
    // 0xeba4f8: DecompressPointer r0
    //     0xeba4f8: add             x0, x0, HEAP, lsl #32
    // 0xeba4fc: stur            x0, [fp, #-8]
    // 0xeba500: r0 = XmlComment()
    //     0xeba500: bl              #0xeb804c  ; AllocateXmlCommentStub -> XmlComment (size=0x10)
    // 0xeba504: mov             x2, x0
    // 0xeba508: ldur            x0, [fp, #-8]
    // 0xeba50c: stur            x2, [fp, #-0x20]
    // 0xeba510: StoreField: r2->field_b = r0
    //     0xeba510: stur            w0, [x2, #0xb]
    // 0xeba514: mov             x1, x2
    // 0xeba518: r0 = forceCompileTimeTreeShaking()
    //     0xeba518: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xeba51c: ldur            x1, [fp, #-0x10]
    // 0xeba520: ldur            x2, [fp, #-0x20]
    // 0xeba524: ldur            x3, [fp, #-0x18]
    // 0xeba528: r0 = commit()
    //     0xeba528: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xeba52c: r0 = Null
    //     0xeba52c: mov             x0, NULL
    // 0xeba530: LeaveFrame
    //     0xeba530: mov             SP, fp
    //     0xeba534: ldp             fp, lr, [SP], #0x10
    // 0xeba538: ret
    //     0xeba538: ret             
    // 0xeba53c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba53c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba540: b               #0xeba4f4
  }
  _ visitDeclarationEvent(/* No info */) {
    // ** addr: 0xeba7ec, size: 0x78
    // 0xeba7ec: EnterFrame
    //     0xeba7ec: stp             fp, lr, [SP, #-0x10]!
    //     0xeba7f0: mov             fp, SP
    // 0xeba7f4: AllocStack(0x18)
    //     0xeba7f4: sub             SP, SP, #0x18
    // 0xeba7f8: SetupParameters(_XmlNodeDecoderSink this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xeba7f8: mov             x0, x1
    //     0xeba7fc: mov             x3, x2
    //     0xeba800: stur            x1, [fp, #-8]
    //     0xeba804: stur            x2, [fp, #-0x10]
    // 0xeba808: CheckStackOverflow
    //     0xeba808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba80c: cmp             SP, x16
    //     0xeba810: b.ls            #0xeba85c
    // 0xeba814: LoadField: r2 = r3->field_13
    //     0xeba814: ldur            w2, [x3, #0x13]
    // 0xeba818: DecompressPointer r2
    //     0xeba818: add             x2, x2, HEAP, lsl #32
    // 0xeba81c: mov             x1, x0
    // 0xeba820: r0 = convertAttributes()
    //     0xeba820: bl              #0xeba864  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::convertAttributes
    // 0xeba824: stur            x0, [fp, #-0x18]
    // 0xeba828: r0 = XmlDeclaration()
    //     0xeba828: bl              #0xeb828c  ; AllocateXmlDeclarationStub -> XmlDeclaration (size=0x10)
    // 0xeba82c: mov             x1, x0
    // 0xeba830: ldur            x2, [fp, #-0x18]
    // 0xeba834: stur            x0, [fp, #-0x18]
    // 0xeba838: r0 = XmlDeclaration()
    //     0xeba838: bl              #0xeb81b4  ; [package:xml/src/xml/nodes/declaration.dart] XmlDeclaration::XmlDeclaration
    // 0xeba83c: ldur            x1, [fp, #-8]
    // 0xeba840: ldur            x2, [fp, #-0x18]
    // 0xeba844: ldur            x3, [fp, #-0x10]
    // 0xeba848: r0 = commit()
    //     0xeba848: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xeba84c: r0 = Null
    //     0xeba84c: mov             x0, NULL
    // 0xeba850: LeaveFrame
    //     0xeba850: mov             SP, fp
    //     0xeba854: ldp             fp, lr, [SP], #0x10
    // 0xeba858: ret
    //     0xeba858: ret             
    // 0xeba85c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba85c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba860: b               #0xeba814
  }
  _ convertAttributes(/* No info */) {
    // ** addr: 0xeba864, size: 0x7c
    // 0xeba864: EnterFrame
    //     0xeba864: stp             fp, lr, [SP, #-0x10]!
    //     0xeba868: mov             fp, SP
    // 0xeba86c: AllocStack(0x20)
    //     0xeba86c: sub             SP, SP, #0x20
    // 0xeba870: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xeba870: mov             x0, x2
    //     0xeba874: stur            x2, [fp, #-8]
    // 0xeba878: CheckStackOverflow
    //     0xeba878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba87c: cmp             SP, x16
    //     0xeba880: b.ls            #0xeba8d8
    // 0xeba884: r1 = Function '<anonymous closure>':.
    //     0xeba884: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bf08] AnonymousClosure: (0xeba8e0), in [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::convertAttributes (0xeba864)
    //     0xeba888: ldr             x1, [x1, #0xf08]
    // 0xeba88c: r2 = Null
    //     0xeba88c: mov             x2, NULL
    // 0xeba890: r0 = AllocateClosure()
    //     0xeba890: bl              #0xec1630  ; AllocateClosureStub
    // 0xeba894: mov             x1, x0
    // 0xeba898: ldur            x0, [fp, #-8]
    // 0xeba89c: r2 = LoadClassIdInstr(r0)
    //     0xeba89c: ldur            x2, [x0, #-1]
    //     0xeba8a0: ubfx            x2, x2, #0xc, #0x14
    // 0xeba8a4: r16 = <XmlAttribute>
    //     0xeba8a4: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bf00] TypeArguments: <XmlAttribute>
    //     0xeba8a8: ldr             x16, [x16, #0xf00]
    // 0xeba8ac: stp             x0, x16, [SP, #8]
    // 0xeba8b0: str             x1, [SP]
    // 0xeba8b4: mov             x0, x2
    // 0xeba8b8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xeba8b8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xeba8bc: r0 = GDT[cid_x0 + 0xf28c]()
    //     0xeba8bc: movz            x17, #0xf28c
    //     0xeba8c0: add             lr, x0, x17
    //     0xeba8c4: ldr             lr, [x21, lr, lsl #3]
    //     0xeba8c8: blr             lr
    // 0xeba8cc: LeaveFrame
    //     0xeba8cc: mov             SP, fp
    //     0xeba8d0: ldp             fp, lr, [SP], #0x10
    // 0xeba8d4: ret
    //     0xeba8d4: ret             
    // 0xeba8d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba8d8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba8dc: b               #0xeba884
  }
  [closure] XmlAttribute <anonymous closure>(dynamic, XmlEventAttribute) {
    // ** addr: 0xeba8e0, size: 0x90
    // 0xeba8e0: EnterFrame
    //     0xeba8e0: stp             fp, lr, [SP, #-0x10]!
    //     0xeba8e4: mov             fp, SP
    // 0xeba8e8: AllocStack(0x28)
    //     0xeba8e8: sub             SP, SP, #0x28
    // 0xeba8ec: CheckStackOverflow
    //     0xeba8ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeba8f0: cmp             SP, x16
    //     0xeba8f4: b.ls            #0xeba968
    // 0xeba8f8: ldr             x0, [fp, #0x10]
    // 0xeba8fc: LoadField: r2 = r0->field_7
    //     0xeba8fc: ldur            w2, [x0, #7]
    // 0xeba900: DecompressPointer r2
    //     0xeba900: add             x2, x2, HEAP, lsl #32
    // 0xeba904: r1 = Null
    //     0xeba904: mov             x1, NULL
    // 0xeba908: r0 = XmlName.fromString()
    //     0xeba908: bl              #0xeba970  ; [package:xml/src/xml/utils/name.dart] XmlName::XmlName.fromString
    // 0xeba90c: mov             x1, x0
    // 0xeba910: ldr             x0, [fp, #0x10]
    // 0xeba914: stur            x1, [fp, #-0x18]
    // 0xeba918: LoadField: r3 = r0->field_b
    //     0xeba918: ldur            w3, [x0, #0xb]
    // 0xeba91c: DecompressPointer r3
    //     0xeba91c: add             x3, x3, HEAP, lsl #32
    // 0xeba920: stur            x3, [fp, #-0x10]
    // 0xeba924: LoadField: r2 = r0->field_f
    //     0xeba924: ldur            w2, [x0, #0xf]
    // 0xeba928: DecompressPointer r2
    //     0xeba928: add             x2, x2, HEAP, lsl #32
    // 0xeba92c: stur            x2, [fp, #-8]
    // 0xeba930: r0 = XmlAttribute()
    //     0xeba930: bl              #0xe76d8c  ; AllocateXmlAttributeStub -> XmlAttribute (size=0x18)
    // 0xeba934: stur            x0, [fp, #-0x20]
    // 0xeba938: ldur            x16, [fp, #-8]
    // 0xeba93c: str             x16, [SP]
    // 0xeba940: mov             x1, x0
    // 0xeba944: ldur            x2, [fp, #-0x18]
    // 0xeba948: ldur            x3, [fp, #-0x10]
    // 0xeba94c: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0xeba94c: add             x4, PP, #0xe, lsl #12  ; [pp+0xe5a8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0xeba950: ldr             x4, [x4, #0x5a8]
    // 0xeba954: r0 = XmlAttribute()
    //     0xeba954: bl              #0xe76cac  ; [package:xml/src/xml/nodes/attribute.dart] XmlAttribute::XmlAttribute
    // 0xeba958: ldur            x0, [fp, #-0x20]
    // 0xeba95c: LeaveFrame
    //     0xeba95c: mov             SP, fp
    //     0xeba960: ldp             fp, lr, [SP], #0x10
    // 0xeba964: ret
    //     0xeba964: ret             
    // 0xeba968: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeba968: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeba96c: b               #0xeba8f8
  }
  _ visitDoctypeEvent(/* No info */) {
    // ** addr: 0xebab64, size: 0x9c
    // 0xebab64: EnterFrame
    //     0xebab64: stp             fp, lr, [SP, #-0x10]!
    //     0xebab68: mov             fp, SP
    // 0xebab6c: AllocStack(0x30)
    //     0xebab6c: sub             SP, SP, #0x30
    // 0xebab70: SetupParameters(_XmlNodeDecoderSink this /* r1 => r1, fp-0x20 */, dynamic _ /* r2 => r3, fp-0x28 */)
    //     0xebab70: mov             x3, x2
    //     0xebab74: stur            x1, [fp, #-0x20]
    //     0xebab78: stur            x2, [fp, #-0x28]
    // 0xebab7c: CheckStackOverflow
    //     0xebab7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebab80: cmp             SP, x16
    //     0xebab84: b.ls            #0xebabf8
    // 0xebab88: LoadField: r0 = r3->field_13
    //     0xebab88: ldur            w0, [x3, #0x13]
    // 0xebab8c: DecompressPointer r0
    //     0xebab8c: add             x0, x0, HEAP, lsl #32
    // 0xebab90: stur            x0, [fp, #-0x18]
    // 0xebab94: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xebab94: ldur            w2, [x3, #0x17]
    // 0xebab98: DecompressPointer r2
    //     0xebab98: add             x2, x2, HEAP, lsl #32
    // 0xebab9c: stur            x2, [fp, #-0x10]
    // 0xebaba0: LoadField: r4 = r3->field_1b
    //     0xebaba0: ldur            w4, [x3, #0x1b]
    // 0xebaba4: DecompressPointer r4
    //     0xebaba4: add             x4, x4, HEAP, lsl #32
    // 0xebaba8: stur            x4, [fp, #-8]
    // 0xebabac: r0 = XmlDoctype()
    //     0xebabac: bl              #0xeb8318  ; AllocateXmlDoctypeStub -> XmlDoctype (size=0x18)
    // 0xebabb0: mov             x2, x0
    // 0xebabb4: ldur            x0, [fp, #-0x18]
    // 0xebabb8: stur            x2, [fp, #-0x30]
    // 0xebabbc: StoreField: r2->field_b = r0
    //     0xebabbc: stur            w0, [x2, #0xb]
    // 0xebabc0: ldur            x0, [fp, #-0x10]
    // 0xebabc4: StoreField: r2->field_f = r0
    //     0xebabc4: stur            w0, [x2, #0xf]
    // 0xebabc8: ldur            x0, [fp, #-8]
    // 0xebabcc: StoreField: r2->field_13 = r0
    //     0xebabcc: stur            w0, [x2, #0x13]
    // 0xebabd0: mov             x1, x2
    // 0xebabd4: r0 = forceCompileTimeTreeShaking()
    //     0xebabd4: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xebabd8: ldur            x1, [fp, #-0x20]
    // 0xebabdc: ldur            x2, [fp, #-0x30]
    // 0xebabe0: ldur            x3, [fp, #-0x28]
    // 0xebabe4: r0 = commit()
    //     0xebabe4: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xebabe8: r0 = Null
    //     0xebabe8: mov             x0, NULL
    // 0xebabec: LeaveFrame
    //     0xebabec: mov             SP, fp
    //     0xebabf0: ldp             fp, lr, [SP], #0x10
    // 0xebabf4: ret
    //     0xebabf4: ret             
    // 0xebabf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebabf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebabfc: b               #0xebab88
  }
  _ visitEndElementEvent(/* No info */) {
    // ** addr: 0xebadfc, size: 0x120
    // 0xebadfc: EnterFrame
    //     0xebadfc: stp             fp, lr, [SP, #-0x10]!
    //     0xebae00: mov             fp, SP
    // 0xebae04: AllocStack(0x10)
    //     0xebae04: sub             SP, SP, #0x10
    // 0xebae08: SetupParameters(_XmlNodeDecoderSink this /* r1 => r0, fp-0x10 */)
    //     0xebae08: mov             x0, x1
    //     0xebae0c: stur            x1, [fp, #-0x10]
    // 0xebae10: CheckStackOverflow
    //     0xebae10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebae14: cmp             SP, x16
    //     0xebae18: b.ls            #0xebaf14
    // 0xebae1c: LoadField: r3 = r0->field_b
    //     0xebae1c: ldur            w3, [x0, #0xb]
    // 0xebae20: DecompressPointer r3
    //     0xebae20: add             x3, x3, HEAP, lsl #32
    // 0xebae24: stur            x3, [fp, #-8]
    // 0xebae28: cmp             w3, NULL
    // 0xebae2c: b.eq            #0xebaef0
    // 0xebae30: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xebae30: ldur            w1, [x3, #0x17]
    // 0xebae34: DecompressPointer r1
    //     0xebae34: add             x1, x1, HEAP, lsl #32
    // 0xebae38: r4 = LoadClassIdInstr(r1)
    //     0xebae38: ldur            x4, [x1, #-1]
    //     0xebae3c: ubfx            x4, x4, #0xc, #0x14
    // 0xebae40: cmp             x4, #0xe2
    // 0xebae44: b.ne            #0xebae58
    // 0xebae48: LoadField: r4 = r1->field_b
    //     0xebae48: ldur            w4, [x1, #0xb]
    // 0xebae4c: DecompressPointer r4
    //     0xebae4c: add             x4, x4, HEAP, lsl #32
    // 0xebae50: mov             x1, x4
    // 0xebae54: b               #0xebae64
    // 0xebae58: LoadField: r4 = r1->field_13
    //     0xebae58: ldur            w4, [x1, #0x13]
    // 0xebae5c: DecompressPointer r4
    //     0xebae5c: add             x4, x4, HEAP, lsl #32
    // 0xebae60: mov             x1, x4
    // 0xebae64: LoadField: r4 = r2->field_13
    //     0xebae64: ldur            w4, [x2, #0x13]
    // 0xebae68: DecompressPointer r4
    //     0xebae68: add             x4, x4, HEAP, lsl #32
    // 0xebae6c: mov             x2, x4
    // 0xebae70: r0 = checkClosingTag()
    //     0xebae70: bl              #0xebafac  ; [package:xml/src/xml/exceptions/tag_exception.dart] XmlTagException::checkClosingTag
    // 0xebae74: ldur            x0, [fp, #-8]
    // 0xebae78: LoadField: r1 = r0->field_f
    //     0xebae78: ldur            w1, [x0, #0xf]
    // 0xebae7c: DecompressPointer r1
    //     0xebae7c: add             x1, x1, HEAP, lsl #32
    // 0xebae80: LoadField: r2 = r1->field_b
    //     0xebae80: ldur            w2, [x1, #0xb]
    // 0xebae84: DecompressPointer r2
    //     0xebae84: add             x2, x2, HEAP, lsl #32
    // 0xebae88: LoadField: r1 = r2->field_b
    //     0xebae88: ldur            w1, [x2, #0xb]
    // 0xebae8c: cbnz            w1, #0xebae98
    // 0xebae90: r2 = false
    //     0xebae90: add             x2, NULL, #0x30  ; false
    // 0xebae94: b               #0xebae9c
    // 0xebae98: r2 = true
    //     0xebae98: add             x2, NULL, #0x20  ; true
    // 0xebae9c: StoreField: r0->field_13 = r2
    //     0xebae9c: stur            w2, [x0, #0x13]
    // 0xebaea0: mov             x1, x0
    // 0xebaea4: r0 = XmlParentExtension.parentElement()
    //     0xebaea4: bl              #0xebaf1c  ; [package:xml/src/xml/extensions/parent.dart] ::XmlParentExtension.parentElement
    // 0xebaea8: mov             x2, x0
    // 0xebaeac: ldur            x1, [fp, #-0x10]
    // 0xebaeb0: StoreField: r1->field_b = r0
    //     0xebaeb0: stur            w0, [x1, #0xb]
    //     0xebaeb4: ldurb           w16, [x1, #-1]
    //     0xebaeb8: ldurb           w17, [x0, #-1]
    //     0xebaebc: and             x16, x17, x16, lsr #2
    //     0xebaec0: tst             x16, HEAP, lsr #32
    //     0xebaec4: b.eq            #0xebaecc
    //     0xebaec8: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xebaecc: cmp             w2, NULL
    // 0xebaed0: b.ne            #0xebaee0
    // 0xebaed4: ldur            x2, [fp, #-8]
    // 0xebaed8: r3 = Null
    //     0xebaed8: mov             x3, NULL
    // 0xebaedc: r0 = commit()
    //     0xebaedc: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xebaee0: r0 = Null
    //     0xebaee0: mov             x0, NULL
    // 0xebaee4: LeaveFrame
    //     0xebaee4: mov             SP, fp
    //     0xebaee8: ldp             fp, lr, [SP], #0x10
    // 0xebaeec: ret
    //     0xebaeec: ret             
    // 0xebaef0: LoadField: r0 = r2->field_13
    //     0xebaef0: ldur            w0, [x2, #0x13]
    // 0xebaef4: DecompressPointer r0
    //     0xebaef4: add             x0, x0, HEAP, lsl #32
    // 0xebaef8: mov             x2, x0
    // 0xebaefc: r1 = Null
    //     0xebaefc: mov             x1, NULL
    // 0xebaf00: r3 = Null
    //     0xebaf00: mov             x3, NULL
    // 0xebaf04: r5 = Null
    //     0xebaf04: mov             x5, NULL
    // 0xebaf08: r0 = XmlTagException.unexpectedClosingTag()
    //     0xebaf08: bl              #0x751ef4  ; [package:xml/src/xml/exceptions/tag_exception.dart] XmlTagException::XmlTagException.unexpectedClosingTag
    // 0xebaf0c: r0 = Throw()
    //     0xebaf0c: bl              #0xec04b8  ; ThrowStub
    // 0xebaf10: brk             #0
    // 0xebaf14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebaf14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebaf18: b               #0xebae1c
  }
  _ visitProcessingEvent(/* No info */) {
    // ** addr: 0xebb0d0, size: 0x88
    // 0xebb0d0: EnterFrame
    //     0xebb0d0: stp             fp, lr, [SP, #-0x10]!
    //     0xebb0d4: mov             fp, SP
    // 0xebb0d8: AllocStack(0x28)
    //     0xebb0d8: sub             SP, SP, #0x28
    // 0xebb0dc: SetupParameters(_XmlNodeDecoderSink this /* r1 => r1, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */)
    //     0xebb0dc: mov             x3, x2
    //     0xebb0e0: stur            x1, [fp, #-0x18]
    //     0xebb0e4: stur            x2, [fp, #-0x20]
    // 0xebb0e8: CheckStackOverflow
    //     0xebb0e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebb0ec: cmp             SP, x16
    //     0xebb0f0: b.ls            #0xebb150
    // 0xebb0f4: LoadField: r0 = r3->field_13
    //     0xebb0f4: ldur            w0, [x3, #0x13]
    // 0xebb0f8: DecompressPointer r0
    //     0xebb0f8: add             x0, x0, HEAP, lsl #32
    // 0xebb0fc: stur            x0, [fp, #-0x10]
    // 0xebb100: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xebb100: ldur            w2, [x3, #0x17]
    // 0xebb104: DecompressPointer r2
    //     0xebb104: add             x2, x2, HEAP, lsl #32
    // 0xebb108: stur            x2, [fp, #-8]
    // 0xebb10c: r0 = XmlProcessing()
    //     0xebb10c: bl              #0xeb80c4  ; AllocateXmlProcessingStub -> XmlProcessing (size=0x14)
    // 0xebb110: mov             x2, x0
    // 0xebb114: ldur            x0, [fp, #-0x10]
    // 0xebb118: stur            x2, [fp, #-0x28]
    // 0xebb11c: StoreField: r2->field_f = r0
    //     0xebb11c: stur            w0, [x2, #0xf]
    // 0xebb120: ldur            x0, [fp, #-8]
    // 0xebb124: StoreField: r2->field_b = r0
    //     0xebb124: stur            w0, [x2, #0xb]
    // 0xebb128: mov             x1, x2
    // 0xebb12c: r0 = forceCompileTimeTreeShaking()
    //     0xebb12c: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xebb130: ldur            x1, [fp, #-0x18]
    // 0xebb134: ldur            x2, [fp, #-0x28]
    // 0xebb138: ldur            x3, [fp, #-0x20]
    // 0xebb13c: r0 = commit()
    //     0xebb13c: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xebb140: r0 = Null
    //     0xebb140: mov             x0, NULL
    // 0xebb144: LeaveFrame
    //     0xebb144: mov             SP, fp
    //     0xebb148: ldp             fp, lr, [SP], #0x10
    // 0xebb14c: ret
    //     0xebb14c: ret             
    // 0xebb150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb150: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb154: b               #0xebb0f4
  }
  _ visitStartElementEvent(/* No info */) {
    // ** addr: 0xebb208, size: 0xe8
    // 0xebb208: EnterFrame
    //     0xebb208: stp             fp, lr, [SP, #-0x10]!
    //     0xebb20c: mov             fp, SP
    // 0xebb210: AllocStack(0x30)
    //     0xebb210: sub             SP, SP, #0x30
    // 0xebb214: SetupParameters(_XmlNodeDecoderSink this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xebb214: mov             x0, x1
    //     0xebb218: mov             x3, x2
    //     0xebb21c: stur            x1, [fp, #-0x10]
    //     0xebb220: stur            x2, [fp, #-0x18]
    // 0xebb224: CheckStackOverflow
    //     0xebb224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebb228: cmp             SP, x16
    //     0xebb22c: b.ls            #0xebb2e8
    // 0xebb230: LoadField: r4 = r3->field_13
    //     0xebb230: ldur            w4, [x3, #0x13]
    // 0xebb234: DecompressPointer r4
    //     0xebb234: add             x4, x4, HEAP, lsl #32
    // 0xebb238: stur            x4, [fp, #-8]
    // 0xebb23c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xebb23c: ldur            w2, [x3, #0x17]
    // 0xebb240: DecompressPointer r2
    //     0xebb240: add             x2, x2, HEAP, lsl #32
    // 0xebb244: mov             x1, x0
    // 0xebb248: r0 = convertAttributes()
    //     0xebb248: bl              #0xeba864  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::convertAttributes
    // 0xebb24c: stur            x0, [fp, #-0x20]
    // 0xebb250: r0 = XmlElement()
    //     0xebb250: bl              #0xeb7f14  ; AllocateXmlElementStub -> XmlElement (size=0x1c)
    // 0xebb254: mov             x1, x0
    // 0xebb258: ldur            x2, [fp, #-8]
    // 0xebb25c: ldur            x3, [fp, #-0x20]
    // 0xebb260: stur            x0, [fp, #-8]
    // 0xebb264: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xebb264: ldr             x4, [PP, #0x500]  ; [pp+0x500] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xebb268: r0 = XmlElement.tag()
    //     0xebb268: bl              #0xebb2f0  ; [package:xml/src/xml/nodes/element.dart] XmlElement::XmlElement.tag
    // 0xebb26c: ldur            x3, [fp, #-0x18]
    // 0xebb270: LoadField: r0 = r3->field_1b
    //     0xebb270: ldur            w0, [x3, #0x1b]
    // 0xebb274: DecompressPointer r0
    //     0xebb274: add             x0, x0, HEAP, lsl #32
    // 0xebb278: tbnz            w0, #4, #0xebb28c
    // 0xebb27c: ldur            x1, [fp, #-0x10]
    // 0xebb280: ldur            x2, [fp, #-8]
    // 0xebb284: r0 = commit()
    //     0xebb284: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xebb288: b               #0xebb2d8
    // 0xebb28c: ldur            x0, [fp, #-0x10]
    // 0xebb290: LoadField: r1 = r0->field_b
    //     0xebb290: ldur            w1, [x0, #0xb]
    // 0xebb294: DecompressPointer r1
    //     0xebb294: add             x1, x1, HEAP, lsl #32
    // 0xebb298: cmp             w1, NULL
    // 0xebb29c: b.eq            #0xebb2b4
    // 0xebb2a0: LoadField: r2 = r1->field_f
    //     0xebb2a0: ldur            w2, [x1, #0xf]
    // 0xebb2a4: DecompressPointer r2
    //     0xebb2a4: add             x2, x2, HEAP, lsl #32
    // 0xebb2a8: ldur            x16, [fp, #-8]
    // 0xebb2ac: stp             x16, x2, [SP]
    // 0xebb2b0: r0 = add()
    //     0xebb2b0: bl              #0x671d18  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::add
    // 0xebb2b4: ldur            x1, [fp, #-0x10]
    // 0xebb2b8: ldur            x0, [fp, #-8]
    // 0xebb2bc: StoreField: r1->field_b = r0
    //     0xebb2bc: stur            w0, [x1, #0xb]
    //     0xebb2c0: ldurb           w16, [x1, #-1]
    //     0xebb2c4: ldurb           w17, [x0, #-1]
    //     0xebb2c8: and             x16, x17, x16, lsr #2
    //     0xebb2cc: tst             x16, HEAP, lsr #32
    //     0xebb2d0: b.eq            #0xebb2d8
    //     0xebb2d4: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0xebb2d8: r0 = Null
    //     0xebb2d8: mov             x0, NULL
    // 0xebb2dc: LeaveFrame
    //     0xebb2dc: mov             SP, fp
    //     0xebb2e0: ldp             fp, lr, [SP], #0x10
    // 0xebb2e4: ret
    //     0xebb2e4: ret             
    // 0xebb2e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb2e8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb2ec: b               #0xebb230
  }
  _ visitTextEvent(/* No info */) {
    // ** addr: 0xebb46c, size: 0x94
    // 0xebb46c: EnterFrame
    //     0xebb46c: stp             fp, lr, [SP, #-0x10]!
    //     0xebb470: mov             fp, SP
    // 0xebb474: AllocStack(0x20)
    //     0xebb474: sub             SP, SP, #0x20
    // 0xebb478: SetupParameters(_XmlNodeDecoderSink this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xebb478: mov             x0, x2
    //     0xebb47c: stur            x2, [fp, #-0x10]
    //     0xebb480: mov             x2, x1
    //     0xebb484: stur            x1, [fp, #-8]
    // 0xebb488: CheckStackOverflow
    //     0xebb488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xebb48c: cmp             SP, x16
    //     0xebb490: b.ls            #0xebb4f8
    // 0xebb494: mov             x1, x0
    // 0xebb498: LoadField: r0 = r1->field_1b
    //     0xebb498: ldur            w0, [x1, #0x1b]
    // 0xebb49c: DecompressPointer r0
    //     0xebb49c: add             x0, x0, HEAP, lsl #32
    // 0xebb4a0: r16 = Sentinel
    //     0xebb4a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xebb4a4: cmp             w0, w16
    // 0xebb4a8: b.ne            #0xebb4b8
    // 0xebb4ac: r2 = value
    //     0xebb4ac: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a20] Field <XmlRawTextEvent.value>: late final (offset: 0x1c)
    //     0xebb4b0: ldr             x2, [x2, #0xa20]
    // 0xebb4b4: r0 = InitLateFinalInstanceField()
    //     0xebb4b4: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xebb4b8: stur            x0, [fp, #-0x18]
    // 0xebb4bc: r0 = XmlText()
    //     0xebb4bc: bl              #0xeb8128  ; AllocateXmlTextStub -> XmlText (size=0x10)
    // 0xebb4c0: mov             x2, x0
    // 0xebb4c4: ldur            x0, [fp, #-0x18]
    // 0xebb4c8: stur            x2, [fp, #-0x20]
    // 0xebb4cc: StoreField: r2->field_b = r0
    //     0xebb4cc: stur            w0, [x2, #0xb]
    // 0xebb4d0: mov             x1, x2
    // 0xebb4d4: r0 = forceCompileTimeTreeShaking()
    //     0xebb4d4: bl              #0xeb8ce0  ; [package:material_symbols_icons/symbols.dart] Symbols::forceCompileTimeTreeShaking
    // 0xebb4d8: ldur            x1, [fp, #-8]
    // 0xebb4dc: ldur            x2, [fp, #-0x20]
    // 0xebb4e0: ldur            x3, [fp, #-0x10]
    // 0xebb4e4: r0 = commit()
    //     0xebb4e4: bl              #0xeb9ee8  ; [package:xml/src/xml_events/converters/node_decoder.dart] _XmlNodeDecoderSink::commit
    // 0xebb4e8: r0 = Null
    //     0xebb4e8: mov             x0, NULL
    // 0xebb4ec: LeaveFrame
    //     0xebb4ec: mov             SP, fp
    //     0xebb4f0: ldp             fp, lr, [SP], #0x10
    // 0xebb4f4: ret
    //     0xebb4f4: ret             
    // 0xebb4f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xebb4f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xebb4fc: b               #0xebb494
  }
}

// class id: 6441, size: 0xc, field offset: 0xc
//   const constructor, 
class XmlNodeDecoder extends XmlListConverter<dynamic, dynamic> {

  _ convertIterable(/* No info */) {
    // ** addr: 0xb153b0, size: 0xa4
    // 0xb153b0: EnterFrame
    //     0xb153b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb153b4: mov             fp, SP
    // 0xb153b8: AllocStack(0x18)
    //     0xb153b8: sub             SP, SP, #0x18
    // 0xb153bc: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xb153bc: mov             x0, x2
    //     0xb153c0: stur            x2, [fp, #-8]
    // 0xb153c4: CheckStackOverflow
    //     0xb153c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb153c8: cmp             SP, x16
    //     0xb153cc: b.ls            #0xb1544c
    // 0xb153d0: r1 = <XmlNode>
    //     0xb153d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e418] TypeArguments: <XmlNode>
    //     0xb153d4: ldr             x1, [x1, #0x418]
    // 0xb153d8: r2 = 0
    //     0xb153d8: movz            x2, #0
    // 0xb153dc: r0 = _GrowableList()
    //     0xb153dc: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0xb153e0: r1 = <List<XmlNode>>
    //     0xb153e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4c0] TypeArguments: <List<XmlNode>>
    //     0xb153e4: ldr             x1, [x1, #0x4c0]
    // 0xb153e8: stur            x0, [fp, #-0x10]
    // 0xb153ec: r0 = ConversionSink()
    //     0xb153ec: bl              #0xb15460  ; AllocateConversionSinkStub -> ConversionSink<X0> (size=0x10)
    // 0xb153f0: ldur            x2, [fp, #-0x10]
    // 0xb153f4: r1 = Function 'addAll':.
    //     0xb153f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4c8] AnonymousClosure: (0x6e3528), in [dart:core] _GrowableList::addAll (0x6e2fa8)
    //     0xb153f8: ldr             x1, [x1, #0x4c8]
    // 0xb153fc: stur            x0, [fp, #-0x18]
    // 0xb15400: r0 = AllocateClosure()
    //     0xb15400: bl              #0xec1630  ; AllocateClosureStub
    // 0xb15404: mov             x1, x0
    // 0xb15408: ldur            x0, [fp, #-0x18]
    // 0xb1540c: StoreField: r0->field_b = r1
    //     0xb1540c: stur            w1, [x0, #0xb]
    // 0xb15410: r0 = _XmlNodeDecoderSink()
    //     0xb15410: bl              #0xb15454  ; Allocate_XmlNodeDecoderSinkStub -> _XmlNodeDecoderSink (size=0x10)
    // 0xb15414: mov             x1, x0
    // 0xb15418: ldur            x0, [fp, #-0x18]
    // 0xb1541c: StoreField: r1->field_7 = r0
    //     0xb1541c: stur            w0, [x1, #7]
    // 0xb15420: mov             x2, x1
    // 0xb15424: r1 = Function 'visit':.
    //     0xb15424: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4d0] AnonymousClosure: (0x755434), in [package:xml/src/xml_events/converters/event_encoder.dart] __XmlEventEncoderSink&Object&XmlEventVisitor::visit (0x755470)
    //     0xb15428: ldr             x1, [x1, #0x4d0]
    // 0xb1542c: r0 = AllocateClosure()
    //     0xb1542c: bl              #0xec1630  ; AllocateClosureStub
    // 0xb15430: ldur            x1, [fp, #-8]
    // 0xb15434: mov             x2, x0
    // 0xb15438: r0 = forEach()
    //     0xb15438: bl              #0x7e1920  ; [dart:core] Iterable::forEach
    // 0xb1543c: ldur            x0, [fp, #-0x10]
    // 0xb15440: LeaveFrame
    //     0xb15440: mov             SP, fp
    //     0xb15444: ldp             fp, lr, [SP], #0x10
    // 0xb15448: ret
    //     0xb15448: ret             
    // 0xb1544c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1544c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb15450: b               #0xb153d0
  }
}
