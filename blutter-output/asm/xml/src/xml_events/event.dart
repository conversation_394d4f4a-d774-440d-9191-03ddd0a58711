// lib: , url: package:xml/src/xml_events/event.dart

// class id: 1051331, size: 0x8
class :: {
}

// class id: 196, size: 0xc, field offset: 0x8
//   transformed mixin,
abstract class _XmlEvent&Object&XmlHasParent extends Object
     with XmlHasParent {
}

// class id: 197, size: 0x10, field offset: 0xc
//   transformed mixin,
abstract class _XmlEvent&Object&XmlHasParent&XmlHasLocation extends _XmlEvent&Object&XmlHasParent
     with XmlHasLocation {
}

// class id: 198, size: 0x14, field offset: 0x10
//   transformed mixin,
abstract class _XmlEvent&Object&XmlHasParent&XmlHasLocation&XmlHasBuffer extends _XmlEvent&Object&XmlHasParent&XmlHasLocation
     with XmlHasBuffer {
}

// class id: 199, size: 0x14, field offset: 0x14
abstract class XmlEvent extends _XmlEvent&Object&XmlHasParent&XmlHasLocation&XmlHasBuffer {

  _ toString(/* No info */) {
    // ** addr: 0xc455e4, size: 0xb4
    // 0xc455e4: EnterFrame
    //     0xc455e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc455e8: mov             fp, SP
    // 0xc455ec: AllocStack(0x10)
    //     0xc455ec: sub             SP, SP, #0x10
    // 0xc455f0: CheckStackOverflow
    //     0xc455f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc455f4: cmp             SP, x16
    //     0xc455f8: b.ls            #0xc45690
    // 0xc455fc: r0 = InitLateStaticField(0xc04) // [package:xml/src/xml/entities/default_mapping.dart] ::defaultEntityMapping
    //     0xc455fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc45600: ldr             x0, [x0, #0x1808]
    //     0xc45604: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc45608: cmp             w0, w16
    //     0xc4560c: b.ne            #0xc4561c
    //     0xc45610: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cc8] Field <::.defaultEntityMapping>: static late (offset: 0xc04)
    //     0xc45614: ldr             x2, [x2, #0xcc8]
    //     0xc45618: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xc4561c: r1 = <List<XmlEvent>, String>
    //     0xc4561c: add             x1, PP, #0x31, lsl #12  ; [pp+0x310d0] TypeArguments: <List<XmlEvent>, String>
    //     0xc45620: ldr             x1, [x1, #0xd0]
    // 0xc45624: r0 = XmlEventEncoder()
    //     0xc45624: bl              #0xc45698  ; AllocateXmlEventEncoderStub -> XmlEventEncoder (size=0x10)
    // 0xc45628: mov             x3, x0
    // 0xc4562c: r0 = Instance_XmlDefaultEntityMapping
    //     0xc4562c: add             x0, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xc45630: ldr             x0, [x0, #0x5c0]
    // 0xc45634: stur            x3, [fp, #-8]
    // 0xc45638: StoreField: r3->field_b = r0
    //     0xc45638: stur            w0, [x3, #0xb]
    // 0xc4563c: r1 = Null
    //     0xc4563c: mov             x1, NULL
    // 0xc45640: r2 = 2
    //     0xc45640: movz            x2, #0x2
    // 0xc45644: r0 = AllocateArray()
    //     0xc45644: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc45648: mov             x2, x0
    // 0xc4564c: ldr             x0, [fp, #0x10]
    // 0xc45650: stur            x2, [fp, #-0x10]
    // 0xc45654: StoreField: r2->field_f = r0
    //     0xc45654: stur            w0, [x2, #0xf]
    // 0xc45658: r1 = <XmlEvent>
    //     0xc45658: add             x1, PP, #0x26, lsl #12  ; [pp+0x26590] TypeArguments: <XmlEvent>
    //     0xc4565c: ldr             x1, [x1, #0x590]
    // 0xc45660: r0 = AllocateGrowableArray()
    //     0xc45660: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0xc45664: mov             x1, x0
    // 0xc45668: ldur            x0, [fp, #-0x10]
    // 0xc4566c: StoreField: r1->field_f = r0
    //     0xc4566c: stur            w0, [x1, #0xf]
    // 0xc45670: r0 = 2
    //     0xc45670: movz            x0, #0x2
    // 0xc45674: StoreField: r1->field_b = r0
    //     0xc45674: stur            w0, [x1, #0xb]
    // 0xc45678: mov             x2, x1
    // 0xc4567c: ldur            x1, [fp, #-8]
    // 0xc45680: r0 = convert()
    //     0xc45680: bl              #0xcfa16c  ; [package:xml/src/xml_events/converters/event_encoder.dart] XmlEventEncoder::convert
    // 0xc45684: LeaveFrame
    //     0xc45684: mov             SP, fp
    //     0xc45688: ldp             fp, lr, [SP], #0x10
    // 0xc4568c: ret
    //     0xc4568c: ret             
    // 0xc45690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc45690: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc45694: b               #0xc455fc
  }
}
