// lib: , url: package:xml/src/xml_events/events/cdata.dart

// class id: 1051332, size: 0x8
class :: {
}

// class id: 209, size: 0x18, field offset: 0x14
class XmlCDATAEvent extends XmlEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf4254, size: 0x5c
    // 0xbf4254: EnterFrame
    //     0xbf4254: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4258: mov             fp, SP
    // 0xbf425c: CheckStackOverflow
    //     0xbf425c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4260: cmp             SP, x16
    //     0xbf4264: b.ls            #0xbf42a8
    // 0xbf4268: ldr             x0, [fp, #0x10]
    // 0xbf426c: LoadField: r2 = r0->field_13
    //     0xbf426c: ldur            w2, [x0, #0x13]
    // 0xbf4270: DecompressPointer r2
    //     0xbf4270: add             x2, x2, HEAP, lsl #32
    // 0xbf4274: r1 = Instance_XmlNodeType
    //     0xbf4274: add             x1, PP, #0x31, lsl #12  ; [pp+0x310b8] Obj!XmlNodeType@e2d451
    //     0xbf4278: ldr             x1, [x1, #0xb8]
    // 0xbf427c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf427c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf4280: r0 = hash()
    //     0xbf4280: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4284: mov             x2, x0
    // 0xbf4288: r0 = BoxInt64Instr(r2)
    //     0xbf4288: sbfiz           x0, x2, #1, #0x1f
    //     0xbf428c: cmp             x2, x0, asr #1
    //     0xbf4290: b.eq            #0xbf429c
    //     0xbf4294: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4298: stur            x2, [x0, #7]
    // 0xbf429c: LeaveFrame
    //     0xbf429c: mov             SP, fp
    //     0xbf42a0: ldp             fp, lr, [SP], #0x10
    // 0xbf42a4: ret
    //     0xbf42a4: ret             
    // 0xbf42a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf42a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf42ac: b               #0xbf4268
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7fd68, size: 0x98
    // 0xd7fd68: EnterFrame
    //     0xd7fd68: stp             fp, lr, [SP, #-0x10]!
    //     0xd7fd6c: mov             fp, SP
    // 0xd7fd70: AllocStack(0x10)
    //     0xd7fd70: sub             SP, SP, #0x10
    // 0xd7fd74: CheckStackOverflow
    //     0xd7fd74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7fd78: cmp             SP, x16
    //     0xd7fd7c: b.ls            #0xd7fdf8
    // 0xd7fd80: ldr             x0, [fp, #0x10]
    // 0xd7fd84: cmp             w0, NULL
    // 0xd7fd88: b.ne            #0xd7fd9c
    // 0xd7fd8c: r0 = false
    //     0xd7fd8c: add             x0, NULL, #0x30  ; false
    // 0xd7fd90: LeaveFrame
    //     0xd7fd90: mov             SP, fp
    //     0xd7fd94: ldp             fp, lr, [SP], #0x10
    // 0xd7fd98: ret
    //     0xd7fd98: ret             
    // 0xd7fd9c: r1 = 60
    //     0xd7fd9c: movz            x1, #0x3c
    // 0xd7fda0: branchIfSmi(r0, 0xd7fdac)
    //     0xd7fda0: tbz             w0, #0, #0xd7fdac
    // 0xd7fda4: r1 = LoadClassIdInstr(r0)
    //     0xd7fda4: ldur            x1, [x0, #-1]
    //     0xd7fda8: ubfx            x1, x1, #0xc, #0x14
    // 0xd7fdac: cmp             x1, #0xd1
    // 0xd7fdb0: b.ne            #0xd7fde8
    // 0xd7fdb4: ldr             x1, [fp, #0x18]
    // 0xd7fdb8: LoadField: r2 = r0->field_13
    //     0xd7fdb8: ldur            w2, [x0, #0x13]
    // 0xd7fdbc: DecompressPointer r2
    //     0xd7fdbc: add             x2, x2, HEAP, lsl #32
    // 0xd7fdc0: LoadField: r0 = r1->field_13
    //     0xd7fdc0: ldur            w0, [x1, #0x13]
    // 0xd7fdc4: DecompressPointer r0
    //     0xd7fdc4: add             x0, x0, HEAP, lsl #32
    // 0xd7fdc8: r1 = LoadClassIdInstr(r2)
    //     0xd7fdc8: ldur            x1, [x2, #-1]
    //     0xd7fdcc: ubfx            x1, x1, #0xc, #0x14
    // 0xd7fdd0: stp             x0, x2, [SP]
    // 0xd7fdd4: mov             x0, x1
    // 0xd7fdd8: mov             lr, x0
    // 0xd7fddc: ldr             lr, [x21, lr, lsl #3]
    // 0xd7fde0: blr             lr
    // 0xd7fde4: b               #0xd7fdec
    // 0xd7fde8: r0 = false
    //     0xd7fde8: add             x0, NULL, #0x30  ; false
    // 0xd7fdec: LeaveFrame
    //     0xd7fdec: mov             SP, fp
    //     0xd7fdf0: ldp             fp, lr, [SP], #0x10
    // 0xd7fdf4: ret
    //     0xd7fdf4: ret             
    // 0xd7fdf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7fdf8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7fdfc: b               #0xd7fd80
  }
  _ accept(/* No info */) {
    // ** addr: 0xe349c0, size: 0x4c
    // 0xe349c0: EnterFrame
    //     0xe349c0: stp             fp, lr, [SP, #-0x10]!
    //     0xe349c4: mov             fp, SP
    // 0xe349c8: mov             x16, x2
    // 0xe349cc: mov             x2, x1
    // 0xe349d0: mov             x1, x16
    // 0xe349d4: CheckStackOverflow
    //     0xe349d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe349d8: cmp             SP, x16
    //     0xe349dc: b.ls            #0xe34a04
    // 0xe349e0: r0 = LoadClassIdInstr(r1)
    //     0xe349e0: ldur            x0, [x1, #-1]
    //     0xe349e4: ubfx            x0, x0, #0xc, #0x14
    // 0xe349e8: r0 = GDT[cid_x0 + -0xfcd]()
    //     0xe349e8: sub             lr, x0, #0xfcd
    //     0xe349ec: ldr             lr, [x21, lr, lsl #3]
    //     0xe349f0: blr             lr
    // 0xe349f4: r0 = Null
    //     0xe349f4: mov             x0, NULL
    // 0xe349f8: LeaveFrame
    //     0xe349f8: mov             SP, fp
    //     0xe349fc: ldp             fp, lr, [SP], #0x10
    // 0xe34a00: ret
    //     0xe34a00: ret             
    // 0xe34a04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34a04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34a08: b               #0xe349e0
  }
}
