// lib: , url: package:xml/src/xml_events/events/comment.dart

// class id: 1051333, size: 0x8
class :: {
}

// class id: 208, size: 0x18, field offset: 0x14
class XmlCommentEvent extends XmlEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf42b0, size: 0x5c
    // 0xbf42b0: EnterFrame
    //     0xbf42b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbf42b4: mov             fp, SP
    // 0xbf42b8: CheckStackOverflow
    //     0xbf42b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf42bc: cmp             SP, x16
    //     0xbf42c0: b.ls            #0xbf4304
    // 0xbf42c4: ldr             x0, [fp, #0x10]
    // 0xbf42c8: LoadField: r2 = r0->field_13
    //     0xbf42c8: ldur            w2, [x0, #0x13]
    // 0xbf42cc: DecompressPointer r2
    //     0xbf42cc: add             x2, x2, HEAP, lsl #32
    // 0xbf42d0: r1 = Instance_XmlNodeType
    //     0xbf42d0: add             x1, PP, #0x31, lsl #12  ; [pp+0x310b0] Obj!XmlNodeType@e2d431
    //     0xbf42d4: ldr             x1, [x1, #0xb0]
    // 0xbf42d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf42d8: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf42dc: r0 = hash()
    //     0xbf42dc: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf42e0: mov             x2, x0
    // 0xbf42e4: r0 = BoxInt64Instr(r2)
    //     0xbf42e4: sbfiz           x0, x2, #1, #0x1f
    //     0xbf42e8: cmp             x2, x0, asr #1
    //     0xbf42ec: b.eq            #0xbf42f8
    //     0xbf42f0: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf42f4: stur            x2, [x0, #7]
    // 0xbf42f8: LeaveFrame
    //     0xbf42f8: mov             SP, fp
    //     0xbf42fc: ldp             fp, lr, [SP], #0x10
    // 0xbf4300: ret
    //     0xbf4300: ret             
    // 0xbf4304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4304: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4308: b               #0xbf42c4
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7fe00, size: 0x98
    // 0xd7fe00: EnterFrame
    //     0xd7fe00: stp             fp, lr, [SP, #-0x10]!
    //     0xd7fe04: mov             fp, SP
    // 0xd7fe08: AllocStack(0x10)
    //     0xd7fe08: sub             SP, SP, #0x10
    // 0xd7fe0c: CheckStackOverflow
    //     0xd7fe0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7fe10: cmp             SP, x16
    //     0xd7fe14: b.ls            #0xd7fe90
    // 0xd7fe18: ldr             x0, [fp, #0x10]
    // 0xd7fe1c: cmp             w0, NULL
    // 0xd7fe20: b.ne            #0xd7fe34
    // 0xd7fe24: r0 = false
    //     0xd7fe24: add             x0, NULL, #0x30  ; false
    // 0xd7fe28: LeaveFrame
    //     0xd7fe28: mov             SP, fp
    //     0xd7fe2c: ldp             fp, lr, [SP], #0x10
    // 0xd7fe30: ret
    //     0xd7fe30: ret             
    // 0xd7fe34: r1 = 60
    //     0xd7fe34: movz            x1, #0x3c
    // 0xd7fe38: branchIfSmi(r0, 0xd7fe44)
    //     0xd7fe38: tbz             w0, #0, #0xd7fe44
    // 0xd7fe3c: r1 = LoadClassIdInstr(r0)
    //     0xd7fe3c: ldur            x1, [x0, #-1]
    //     0xd7fe40: ubfx            x1, x1, #0xc, #0x14
    // 0xd7fe44: cmp             x1, #0xd0
    // 0xd7fe48: b.ne            #0xd7fe80
    // 0xd7fe4c: ldr             x1, [fp, #0x18]
    // 0xd7fe50: LoadField: r2 = r0->field_13
    //     0xd7fe50: ldur            w2, [x0, #0x13]
    // 0xd7fe54: DecompressPointer r2
    //     0xd7fe54: add             x2, x2, HEAP, lsl #32
    // 0xd7fe58: LoadField: r0 = r1->field_13
    //     0xd7fe58: ldur            w0, [x1, #0x13]
    // 0xd7fe5c: DecompressPointer r0
    //     0xd7fe5c: add             x0, x0, HEAP, lsl #32
    // 0xd7fe60: r1 = LoadClassIdInstr(r2)
    //     0xd7fe60: ldur            x1, [x2, #-1]
    //     0xd7fe64: ubfx            x1, x1, #0xc, #0x14
    // 0xd7fe68: stp             x0, x2, [SP]
    // 0xd7fe6c: mov             x0, x1
    // 0xd7fe70: mov             lr, x0
    // 0xd7fe74: ldr             lr, [x21, lr, lsl #3]
    // 0xd7fe78: blr             lr
    // 0xd7fe7c: b               #0xd7fe84
    // 0xd7fe80: r0 = false
    //     0xd7fe80: add             x0, NULL, #0x30  ; false
    // 0xd7fe84: LeaveFrame
    //     0xd7fe84: mov             SP, fp
    //     0xd7fe88: ldp             fp, lr, [SP], #0x10
    // 0xd7fe8c: ret
    //     0xd7fe8c: ret             
    // 0xd7fe90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7fe90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7fe94: b               #0xd7fe18
  }
  _ accept(/* No info */) {
    // ** addr: 0xe34a0c, size: 0x4c
    // 0xe34a0c: EnterFrame
    //     0xe34a0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe34a10: mov             fp, SP
    // 0xe34a14: mov             x16, x2
    // 0xe34a18: mov             x2, x1
    // 0xe34a1c: mov             x1, x16
    // 0xe34a20: CheckStackOverflow
    //     0xe34a20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34a24: cmp             SP, x16
    //     0xe34a28: b.ls            #0xe34a50
    // 0xe34a2c: r0 = LoadClassIdInstr(r1)
    //     0xe34a2c: ldur            x0, [x1, #-1]
    //     0xe34a30: ubfx            x0, x0, #0xc, #0x14
    // 0xe34a34: r0 = GDT[cid_x0 + -0xfd3]()
    //     0xe34a34: sub             lr, x0, #0xfd3
    //     0xe34a38: ldr             lr, [x21, lr, lsl #3]
    //     0xe34a3c: blr             lr
    // 0xe34a40: r0 = Null
    //     0xe34a40: mov             x0, NULL
    // 0xe34a44: LeaveFrame
    //     0xe34a44: mov             SP, fp
    //     0xe34a48: ldp             fp, lr, [SP], #0x10
    // 0xe34a4c: ret
    //     0xe34a4c: ret             
    // 0xe34a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34a50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34a54: b               #0xe34a2c
  }
}
