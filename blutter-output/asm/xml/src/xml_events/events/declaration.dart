// lib: , url: package:xml/src/xml_events/events/declaration.dart

// class id: 1051334, size: 0x8
class :: {
}

// class id: 207, size: 0x18, field offset: 0x14
class XmlDeclarationEvent extends XmlEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf430c, size: 0x84
    // 0xbf430c: EnterFrame
    //     0xbf430c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4310: mov             fp, SP
    // 0xbf4314: CheckStackOverflow
    //     0xbf4314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4318: cmp             SP, x16
    //     0xbf431c: b.ls            #0xbf4388
    // 0xbf4320: ldr             x0, [fp, #0x10]
    // 0xbf4324: LoadField: r2 = r0->field_13
    //     0xbf4324: ldur            w2, [x0, #0x13]
    // 0xbf4328: DecompressPointer r2
    //     0xbf4328: add             x2, x2, HEAP, lsl #32
    // 0xbf432c: r1 = Instance_ListEquality
    //     0xbf432c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26580] Obj!ListEquality<XmlEventAttribute>@e25d21
    //     0xbf4330: ldr             x1, [x1, #0x580]
    // 0xbf4334: r0 = hash()
    //     0xbf4334: bl              #0xd3bd84  ; [package:collection/src/equality.dart] ListEquality::hash
    // 0xbf4338: mov             x2, x0
    // 0xbf433c: r0 = BoxInt64Instr(r2)
    //     0xbf433c: sbfiz           x0, x2, #1, #0x1f
    //     0xbf4340: cmp             x2, x0, asr #1
    //     0xbf4344: b.eq            #0xbf4350
    //     0xbf4348: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf434c: stur            x2, [x0, #7]
    // 0xbf4350: mov             x2, x0
    // 0xbf4354: r1 = Instance_XmlNodeType
    //     0xbf4354: add             x1, PP, #0x31, lsl #12  ; [pp+0x310c8] Obj!XmlNodeType@e2d411
    //     0xbf4358: ldr             x1, [x1, #0xc8]
    // 0xbf435c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf435c: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf4360: r0 = hash()
    //     0xbf4360: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4364: mov             x2, x0
    // 0xbf4368: r0 = BoxInt64Instr(r2)
    //     0xbf4368: sbfiz           x0, x2, #1, #0x1f
    //     0xbf436c: cmp             x2, x0, asr #1
    //     0xbf4370: b.eq            #0xbf437c
    //     0xbf4374: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4378: stur            x2, [x0, #7]
    // 0xbf437c: LeaveFrame
    //     0xbf437c: mov             SP, fp
    //     0xbf4380: ldp             fp, lr, [SP], #0x10
    // 0xbf4384: ret
    //     0xbf4384: ret             
    // 0xbf4388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4388: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf438c: b               #0xbf4320
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7fe98, size: 0x84
    // 0xd7fe98: EnterFrame
    //     0xd7fe98: stp             fp, lr, [SP, #-0x10]!
    //     0xd7fe9c: mov             fp, SP
    // 0xd7fea0: CheckStackOverflow
    //     0xd7fea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7fea4: cmp             SP, x16
    //     0xd7fea8: b.ls            #0xd7ff14
    // 0xd7feac: ldr             x0, [fp, #0x10]
    // 0xd7feb0: cmp             w0, NULL
    // 0xd7feb4: b.ne            #0xd7fec8
    // 0xd7feb8: r0 = false
    //     0xd7feb8: add             x0, NULL, #0x30  ; false
    // 0xd7febc: LeaveFrame
    //     0xd7febc: mov             SP, fp
    //     0xd7fec0: ldp             fp, lr, [SP], #0x10
    // 0xd7fec4: ret
    //     0xd7fec4: ret             
    // 0xd7fec8: r1 = 60
    //     0xd7fec8: movz            x1, #0x3c
    // 0xd7fecc: branchIfSmi(r0, 0xd7fed8)
    //     0xd7fecc: tbz             w0, #0, #0xd7fed8
    // 0xd7fed0: r1 = LoadClassIdInstr(r0)
    //     0xd7fed0: ldur            x1, [x0, #-1]
    //     0xd7fed4: ubfx            x1, x1, #0xc, #0x14
    // 0xd7fed8: cmp             x1, #0xcf
    // 0xd7fedc: b.ne            #0xd7ff04
    // 0xd7fee0: ldr             x1, [fp, #0x18]
    // 0xd7fee4: LoadField: r2 = r0->field_13
    //     0xd7fee4: ldur            w2, [x0, #0x13]
    // 0xd7fee8: DecompressPointer r2
    //     0xd7fee8: add             x2, x2, HEAP, lsl #32
    // 0xd7feec: LoadField: r3 = r1->field_13
    //     0xd7feec: ldur            w3, [x1, #0x13]
    // 0xd7fef0: DecompressPointer r3
    //     0xd7fef0: add             x3, x3, HEAP, lsl #32
    // 0xd7fef4: r1 = Instance_ListEquality
    //     0xd7fef4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26580] Obj!ListEquality<XmlEventAttribute>@e25d21
    //     0xd7fef8: ldr             x1, [x1, #0x580]
    // 0xd7fefc: r0 = equals()
    //     0xd7fefc: bl              #0xd2e3dc  ; [package:collection/src/equality.dart] ListEquality::equals
    // 0xd7ff00: b               #0xd7ff08
    // 0xd7ff04: r0 = false
    //     0xd7ff04: add             x0, NULL, #0x30  ; false
    // 0xd7ff08: LeaveFrame
    //     0xd7ff08: mov             SP, fp
    //     0xd7ff0c: ldp             fp, lr, [SP], #0x10
    // 0xd7ff10: ret
    //     0xd7ff10: ret             
    // 0xd7ff14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd7ff14: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd7ff18: b               #0xd7feac
  }
  _ accept(/* No info */) {
    // ** addr: 0xe34a58, size: 0x4c
    // 0xe34a58: EnterFrame
    //     0xe34a58: stp             fp, lr, [SP, #-0x10]!
    //     0xe34a5c: mov             fp, SP
    // 0xe34a60: mov             x16, x2
    // 0xe34a64: mov             x2, x1
    // 0xe34a68: mov             x1, x16
    // 0xe34a6c: CheckStackOverflow
    //     0xe34a6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34a70: cmp             SP, x16
    //     0xe34a74: b.ls            #0xe34a9c
    // 0xe34a78: r0 = LoadClassIdInstr(r1)
    //     0xe34a78: ldur            x0, [x1, #-1]
    //     0xe34a7c: ubfx            x0, x0, #0xc, #0x14
    // 0xe34a80: r0 = GDT[cid_x0 + -0xfd5]()
    //     0xe34a80: sub             lr, x0, #0xfd5
    //     0xe34a84: ldr             lr, [x21, lr, lsl #3]
    //     0xe34a88: blr             lr
    // 0xe34a8c: r0 = Null
    //     0xe34a8c: mov             x0, NULL
    // 0xe34a90: LeaveFrame
    //     0xe34a90: mov             SP, fp
    //     0xe34a94: ldp             fp, lr, [SP], #0x10
    // 0xe34a98: ret
    //     0xe34a98: ret             
    // 0xe34a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34a9c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34aa0: b               #0xe34a78
  }
}
