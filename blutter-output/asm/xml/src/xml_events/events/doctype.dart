// lib: , url: package:xml/src/xml_events/events/doctype.dart

// class id: 1051335, size: 0x8
class :: {
}

// class id: 206, size: 0x20, field offset: 0x14
class XmlDoctypeEvent extends XmlEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf4390, size: 0x78
    // 0xbf4390: EnterFrame
    //     0xbf4390: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4394: mov             fp, SP
    // 0xbf4398: AllocStack(0x10)
    //     0xbf4398: sub             SP, SP, #0x10
    // 0xbf439c: CheckStackOverflow
    //     0xbf439c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf43a0: cmp             SP, x16
    //     0xbf43a4: b.ls            #0xbf4400
    // 0xbf43a8: ldr             x0, [fp, #0x10]
    // 0xbf43ac: LoadField: r2 = r0->field_13
    //     0xbf43ac: ldur            w2, [x0, #0x13]
    // 0xbf43b0: DecompressPointer r2
    //     0xbf43b0: add             x2, x2, HEAP, lsl #32
    // 0xbf43b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf43b4: ldur            w1, [x0, #0x17]
    // 0xbf43b8: DecompressPointer r1
    //     0xbf43b8: add             x1, x1, HEAP, lsl #32
    // 0xbf43bc: LoadField: r3 = r0->field_1b
    //     0xbf43bc: ldur            w3, [x0, #0x1b]
    // 0xbf43c0: DecompressPointer r3
    //     0xbf43c0: add             x3, x3, HEAP, lsl #32
    // 0xbf43c4: stp             x3, x1, [SP]
    // 0xbf43c8: r1 = Instance_XmlNodeType
    //     0xbf43c8: add             x1, PP, #0x31, lsl #12  ; [pp+0x310c0] Obj!XmlNodeType@e2d3f1
    //     0xbf43cc: ldr             x1, [x1, #0xc0]
    // 0xbf43d0: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xbf43d0: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xbf43d4: ldr             x4, [x4, #0xe00]
    // 0xbf43d8: r0 = hash()
    //     0xbf43d8: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf43dc: mov             x2, x0
    // 0xbf43e0: r0 = BoxInt64Instr(r2)
    //     0xbf43e0: sbfiz           x0, x2, #1, #0x1f
    //     0xbf43e4: cmp             x2, x0, asr #1
    //     0xbf43e8: b.eq            #0xbf43f4
    //     0xbf43ec: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf43f0: stur            x2, [x0, #7]
    // 0xbf43f4: LeaveFrame
    //     0xbf43f4: mov             SP, fp
    //     0xbf43f8: ldp             fp, lr, [SP], #0x10
    // 0xbf43fc: ret
    //     0xbf43fc: ret             
    // 0xbf4400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4400: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4404: b               #0xbf43a8
  }
  _ ==(/* No info */) {
    // ** addr: 0xd7ff1c, size: 0x104
    // 0xd7ff1c: EnterFrame
    //     0xd7ff1c: stp             fp, lr, [SP, #-0x10]!
    //     0xd7ff20: mov             fp, SP
    // 0xd7ff24: AllocStack(0x10)
    //     0xd7ff24: sub             SP, SP, #0x10
    // 0xd7ff28: CheckStackOverflow
    //     0xd7ff28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd7ff2c: cmp             SP, x16
    //     0xd7ff30: b.ls            #0xd80018
    // 0xd7ff34: ldr             x1, [fp, #0x10]
    // 0xd7ff38: cmp             w1, NULL
    // 0xd7ff3c: b.ne            #0xd7ff50
    // 0xd7ff40: r0 = false
    //     0xd7ff40: add             x0, NULL, #0x30  ; false
    // 0xd7ff44: LeaveFrame
    //     0xd7ff44: mov             SP, fp
    //     0xd7ff48: ldp             fp, lr, [SP], #0x10
    // 0xd7ff4c: ret
    //     0xd7ff4c: ret             
    // 0xd7ff50: r0 = 60
    //     0xd7ff50: movz            x0, #0x3c
    // 0xd7ff54: branchIfSmi(r1, 0xd7ff60)
    //     0xd7ff54: tbz             w1, #0, #0xd7ff60
    // 0xd7ff58: r0 = LoadClassIdInstr(r1)
    //     0xd7ff58: ldur            x0, [x1, #-1]
    //     0xd7ff5c: ubfx            x0, x0, #0xc, #0x14
    // 0xd7ff60: cmp             x0, #0xce
    // 0xd7ff64: b.ne            #0xd80008
    // 0xd7ff68: ldr             x2, [fp, #0x18]
    // 0xd7ff6c: LoadField: r0 = r2->field_13
    //     0xd7ff6c: ldur            w0, [x2, #0x13]
    // 0xd7ff70: DecompressPointer r0
    //     0xd7ff70: add             x0, x0, HEAP, lsl #32
    // 0xd7ff74: LoadField: r3 = r1->field_13
    //     0xd7ff74: ldur            w3, [x1, #0x13]
    // 0xd7ff78: DecompressPointer r3
    //     0xd7ff78: add             x3, x3, HEAP, lsl #32
    // 0xd7ff7c: r4 = LoadClassIdInstr(r0)
    //     0xd7ff7c: ldur            x4, [x0, #-1]
    //     0xd7ff80: ubfx            x4, x4, #0xc, #0x14
    // 0xd7ff84: stp             x3, x0, [SP]
    // 0xd7ff88: mov             x0, x4
    // 0xd7ff8c: mov             lr, x0
    // 0xd7ff90: ldr             lr, [x21, lr, lsl #3]
    // 0xd7ff94: blr             lr
    // 0xd7ff98: tbnz            w0, #4, #0xd80008
    // 0xd7ff9c: ldr             x2, [fp, #0x18]
    // 0xd7ffa0: ldr             x1, [fp, #0x10]
    // 0xd7ffa4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xd7ffa4: ldur            w0, [x2, #0x17]
    // 0xd7ffa8: DecompressPointer r0
    //     0xd7ffa8: add             x0, x0, HEAP, lsl #32
    // 0xd7ffac: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xd7ffac: ldur            w3, [x1, #0x17]
    // 0xd7ffb0: DecompressPointer r3
    //     0xd7ffb0: add             x3, x3, HEAP, lsl #32
    // 0xd7ffb4: r4 = LoadClassIdInstr(r0)
    //     0xd7ffb4: ldur            x4, [x0, #-1]
    //     0xd7ffb8: ubfx            x4, x4, #0xc, #0x14
    // 0xd7ffbc: stp             x3, x0, [SP]
    // 0xd7ffc0: mov             x0, x4
    // 0xd7ffc4: mov             lr, x0
    // 0xd7ffc8: ldr             lr, [x21, lr, lsl #3]
    // 0xd7ffcc: blr             lr
    // 0xd7ffd0: tbnz            w0, #4, #0xd80008
    // 0xd7ffd4: ldr             x1, [fp, #0x18]
    // 0xd7ffd8: ldr             x0, [fp, #0x10]
    // 0xd7ffdc: LoadField: r2 = r1->field_1b
    //     0xd7ffdc: ldur            w2, [x1, #0x1b]
    // 0xd7ffe0: DecompressPointer r2
    //     0xd7ffe0: add             x2, x2, HEAP, lsl #32
    // 0xd7ffe4: LoadField: r1 = r0->field_1b
    //     0xd7ffe4: ldur            w1, [x0, #0x1b]
    // 0xd7ffe8: DecompressPointer r1
    //     0xd7ffe8: add             x1, x1, HEAP, lsl #32
    // 0xd7ffec: r0 = LoadClassIdInstr(r2)
    //     0xd7ffec: ldur            x0, [x2, #-1]
    //     0xd7fff0: ubfx            x0, x0, #0xc, #0x14
    // 0xd7fff4: stp             x1, x2, [SP]
    // 0xd7fff8: mov             lr, x0
    // 0xd7fffc: ldr             lr, [x21, lr, lsl #3]
    // 0xd80000: blr             lr
    // 0xd80004: b               #0xd8000c
    // 0xd80008: r0 = false
    //     0xd80008: add             x0, NULL, #0x30  ; false
    // 0xd8000c: LeaveFrame
    //     0xd8000c: mov             SP, fp
    //     0xd80010: ldp             fp, lr, [SP], #0x10
    // 0xd80014: ret
    //     0xd80014: ret             
    // 0xd80018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd80018: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8001c: b               #0xd7ff34
  }
  _ accept(/* No info */) {
    // ** addr: 0xe34aa4, size: 0x4c
    // 0xe34aa4: EnterFrame
    //     0xe34aa4: stp             fp, lr, [SP, #-0x10]!
    //     0xe34aa8: mov             fp, SP
    // 0xe34aac: mov             x16, x2
    // 0xe34ab0: mov             x2, x1
    // 0xe34ab4: mov             x1, x16
    // 0xe34ab8: CheckStackOverflow
    //     0xe34ab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34abc: cmp             SP, x16
    //     0xe34ac0: b.ls            #0xe34ae8
    // 0xe34ac4: r0 = LoadClassIdInstr(r1)
    //     0xe34ac4: ldur            x0, [x1, #-1]
    //     0xe34ac8: ubfx            x0, x0, #0xc, #0x14
    // 0xe34acc: r0 = GDT[cid_x0 + -0xfd7]()
    //     0xe34acc: sub             lr, x0, #0xfd7
    //     0xe34ad0: ldr             lr, [x21, lr, lsl #3]
    //     0xe34ad4: blr             lr
    // 0xe34ad8: r0 = Null
    //     0xe34ad8: mov             x0, NULL
    // 0xe34adc: LeaveFrame
    //     0xe34adc: mov             SP, fp
    //     0xe34ae0: ldp             fp, lr, [SP], #0x10
    // 0xe34ae4: ret
    //     0xe34ae4: ret             
    // 0xe34ae8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34ae8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34aec: b               #0xe34ac4
  }
}
