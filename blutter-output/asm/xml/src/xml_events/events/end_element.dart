// lib: , url: package:xml/src/xml_events/events/end_element.dart

// class id: 1051336, size: 0x8
class :: {
}

// class id: 203, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _XmlEndElementEvent&XmlEvent&XmlNamed extends XmlEvent
     with XmlNamed {
}

// class id: 205, size: 0x18, field offset: 0x14
class XmlEndElementEvent extends _XmlEndElementEvent&XmlEvent&XmlNamed {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf4408, size: 0x5c
    // 0xbf4408: EnterFrame
    //     0xbf4408: stp             fp, lr, [SP, #-0x10]!
    //     0xbf440c: mov             fp, SP
    // 0xbf4410: CheckStackOverflow
    //     0xbf4410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4414: cmp             SP, x16
    //     0xbf4418: b.ls            #0xbf445c
    // 0xbf441c: ldr             x0, [fp, #0x10]
    // 0xbf4420: LoadField: r2 = r0->field_13
    //     0xbf4420: ldur            w2, [x0, #0x13]
    // 0xbf4424: DecompressPointer r2
    //     0xbf4424: add             x2, x2, HEAP, lsl #32
    // 0xbf4428: r1 = Instance_XmlNodeType
    //     0xbf4428: add             x1, PP, #0x26, lsl #12  ; [pp+0x26588] Obj!XmlNodeType@e2d3d1
    //     0xbf442c: ldr             x1, [x1, #0x588]
    // 0xbf4430: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf4430: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf4434: r0 = hash()
    //     0xbf4434: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4438: mov             x2, x0
    // 0xbf443c: r0 = BoxInt64Instr(r2)
    //     0xbf443c: sbfiz           x0, x2, #1, #0x1f
    //     0xbf4440: cmp             x2, x0, asr #1
    //     0xbf4444: b.eq            #0xbf4450
    //     0xbf4448: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf444c: stur            x2, [x0, #7]
    // 0xbf4450: LeaveFrame
    //     0xbf4450: mov             SP, fp
    //     0xbf4454: ldp             fp, lr, [SP], #0x10
    // 0xbf4458: ret
    //     0xbf4458: ret             
    // 0xbf445c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf445c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4460: b               #0xbf441c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd80020, size: 0x98
    // 0xd80020: EnterFrame
    //     0xd80020: stp             fp, lr, [SP, #-0x10]!
    //     0xd80024: mov             fp, SP
    // 0xd80028: AllocStack(0x10)
    //     0xd80028: sub             SP, SP, #0x10
    // 0xd8002c: CheckStackOverflow
    //     0xd8002c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd80030: cmp             SP, x16
    //     0xd80034: b.ls            #0xd800b0
    // 0xd80038: ldr             x0, [fp, #0x10]
    // 0xd8003c: cmp             w0, NULL
    // 0xd80040: b.ne            #0xd80054
    // 0xd80044: r0 = false
    //     0xd80044: add             x0, NULL, #0x30  ; false
    // 0xd80048: LeaveFrame
    //     0xd80048: mov             SP, fp
    //     0xd8004c: ldp             fp, lr, [SP], #0x10
    // 0xd80050: ret
    //     0xd80050: ret             
    // 0xd80054: r1 = 60
    //     0xd80054: movz            x1, #0x3c
    // 0xd80058: branchIfSmi(r0, 0xd80064)
    //     0xd80058: tbz             w0, #0, #0xd80064
    // 0xd8005c: r1 = LoadClassIdInstr(r0)
    //     0xd8005c: ldur            x1, [x0, #-1]
    //     0xd80060: ubfx            x1, x1, #0xc, #0x14
    // 0xd80064: cmp             x1, #0xcd
    // 0xd80068: b.ne            #0xd800a0
    // 0xd8006c: ldr             x1, [fp, #0x18]
    // 0xd80070: LoadField: r2 = r0->field_13
    //     0xd80070: ldur            w2, [x0, #0x13]
    // 0xd80074: DecompressPointer r2
    //     0xd80074: add             x2, x2, HEAP, lsl #32
    // 0xd80078: LoadField: r0 = r1->field_13
    //     0xd80078: ldur            w0, [x1, #0x13]
    // 0xd8007c: DecompressPointer r0
    //     0xd8007c: add             x0, x0, HEAP, lsl #32
    // 0xd80080: r1 = LoadClassIdInstr(r2)
    //     0xd80080: ldur            x1, [x2, #-1]
    //     0xd80084: ubfx            x1, x1, #0xc, #0x14
    // 0xd80088: stp             x0, x2, [SP]
    // 0xd8008c: mov             x0, x1
    // 0xd80090: mov             lr, x0
    // 0xd80094: ldr             lr, [x21, lr, lsl #3]
    // 0xd80098: blr             lr
    // 0xd8009c: b               #0xd800a4
    // 0xd800a0: r0 = false
    //     0xd800a0: add             x0, NULL, #0x30  ; false
    // 0xd800a4: LeaveFrame
    //     0xd800a4: mov             SP, fp
    //     0xd800a8: ldp             fp, lr, [SP], #0x10
    // 0xd800ac: ret
    //     0xd800ac: ret             
    // 0xd800b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd800b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd800b4: b               #0xd80038
  }
  _ accept(/* No info */) {
    // ** addr: 0xe34af0, size: 0x4c
    // 0xe34af0: EnterFrame
    //     0xe34af0: stp             fp, lr, [SP, #-0x10]!
    //     0xe34af4: mov             fp, SP
    // 0xe34af8: mov             x16, x2
    // 0xe34afc: mov             x2, x1
    // 0xe34b00: mov             x1, x16
    // 0xe34b04: CheckStackOverflow
    //     0xe34b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34b08: cmp             SP, x16
    //     0xe34b0c: b.ls            #0xe34b34
    // 0xe34b10: r0 = LoadClassIdInstr(r1)
    //     0xe34b10: ldur            x0, [x1, #-1]
    //     0xe34b14: ubfx            x0, x0, #0xc, #0x14
    // 0xe34b18: r0 = GDT[cid_x0 + -0xfe1]()
    //     0xe34b18: sub             lr, x0, #0xfe1
    //     0xe34b1c: ldr             lr, [x21, lr, lsl #3]
    //     0xe34b20: blr             lr
    // 0xe34b24: r0 = Null
    //     0xe34b24: mov             x0, NULL
    // 0xe34b28: LeaveFrame
    //     0xe34b28: mov             SP, fp
    //     0xe34b2c: ldp             fp, lr, [SP], #0x10
    // 0xe34b30: ret
    //     0xe34b30: ret             
    // 0xe34b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34b34: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34b38: b               #0xe34b10
  }
}
