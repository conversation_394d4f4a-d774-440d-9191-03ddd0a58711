// lib: , url: package:xml/src/xml_events/events/processing.dart

// class id: 1051337, size: 0x8
class :: {
}

// class id: 202, size: 0x1c, field offset: 0x14
class XmlProcessingEvent extends XmlEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf4510, size: 0x6c
    // 0xbf4510: EnterFrame
    //     0xbf4510: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4514: mov             fp, SP
    // 0xbf4518: AllocStack(0x8)
    //     0xbf4518: sub             SP, SP, #8
    // 0xbf451c: CheckStackOverflow
    //     0xbf451c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4520: cmp             SP, x16
    //     0xbf4524: b.ls            #0xbf4574
    // 0xbf4528: ldr             x0, [fp, #0x10]
    // 0xbf452c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbf452c: ldur            w2, [x0, #0x17]
    // 0xbf4530: DecompressPointer r2
    //     0xbf4530: add             x2, x2, HEAP, lsl #32
    // 0xbf4534: LoadField: r1 = r0->field_13
    //     0xbf4534: ldur            w1, [x0, #0x13]
    // 0xbf4538: DecompressPointer r1
    //     0xbf4538: add             x1, x1, HEAP, lsl #32
    // 0xbf453c: str             x1, [SP]
    // 0xbf4540: r1 = Instance_XmlNodeType
    //     0xbf4540: add             x1, PP, #0x31, lsl #12  ; [pp+0x310a8] Obj!XmlNodeType@e2d3b1
    //     0xbf4544: ldr             x1, [x1, #0xa8]
    // 0xbf4548: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbf4548: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbf454c: r0 = hash()
    //     0xbf454c: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4550: mov             x2, x0
    // 0xbf4554: r0 = BoxInt64Instr(r2)
    //     0xbf4554: sbfiz           x0, x2, #1, #0x1f
    //     0xbf4558: cmp             x2, x0, asr #1
    //     0xbf455c: b.eq            #0xbf4568
    //     0xbf4560: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4564: stur            x2, [x0, #7]
    // 0xbf4568: LeaveFrame
    //     0xbf4568: mov             SP, fp
    //     0xbf456c: ldp             fp, lr, [SP], #0x10
    // 0xbf4570: ret
    //     0xbf4570: ret             
    // 0xbf4574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4578: b               #0xbf4528
  }
  _ ==(/* No info */) {
    // ** addr: 0xd80190, size: 0xd0
    // 0xd80190: EnterFrame
    //     0xd80190: stp             fp, lr, [SP, #-0x10]!
    //     0xd80194: mov             fp, SP
    // 0xd80198: AllocStack(0x10)
    //     0xd80198: sub             SP, SP, #0x10
    // 0xd8019c: CheckStackOverflow
    //     0xd8019c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd801a0: cmp             SP, x16
    //     0xd801a4: b.ls            #0xd80258
    // 0xd801a8: ldr             x1, [fp, #0x10]
    // 0xd801ac: cmp             w1, NULL
    // 0xd801b0: b.ne            #0xd801c4
    // 0xd801b4: r0 = false
    //     0xd801b4: add             x0, NULL, #0x30  ; false
    // 0xd801b8: LeaveFrame
    //     0xd801b8: mov             SP, fp
    //     0xd801bc: ldp             fp, lr, [SP], #0x10
    // 0xd801c0: ret
    //     0xd801c0: ret             
    // 0xd801c4: r0 = 60
    //     0xd801c4: movz            x0, #0x3c
    // 0xd801c8: branchIfSmi(r1, 0xd801d4)
    //     0xd801c8: tbz             w1, #0, #0xd801d4
    // 0xd801cc: r0 = LoadClassIdInstr(r1)
    //     0xd801cc: ldur            x0, [x1, #-1]
    //     0xd801d0: ubfx            x0, x0, #0xc, #0x14
    // 0xd801d4: cmp             x0, #0xca
    // 0xd801d8: b.ne            #0xd80248
    // 0xd801dc: ldr             x2, [fp, #0x18]
    // 0xd801e0: LoadField: r0 = r1->field_13
    //     0xd801e0: ldur            w0, [x1, #0x13]
    // 0xd801e4: DecompressPointer r0
    //     0xd801e4: add             x0, x0, HEAP, lsl #32
    // 0xd801e8: LoadField: r3 = r2->field_13
    //     0xd801e8: ldur            w3, [x2, #0x13]
    // 0xd801ec: DecompressPointer r3
    //     0xd801ec: add             x3, x3, HEAP, lsl #32
    // 0xd801f0: r4 = LoadClassIdInstr(r0)
    //     0xd801f0: ldur            x4, [x0, #-1]
    //     0xd801f4: ubfx            x4, x4, #0xc, #0x14
    // 0xd801f8: stp             x3, x0, [SP]
    // 0xd801fc: mov             x0, x4
    // 0xd80200: mov             lr, x0
    // 0xd80204: ldr             lr, [x21, lr, lsl #3]
    // 0xd80208: blr             lr
    // 0xd8020c: tbnz            w0, #4, #0xd80248
    // 0xd80210: ldr             x1, [fp, #0x18]
    // 0xd80214: ldr             x0, [fp, #0x10]
    // 0xd80218: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xd80218: ldur            w2, [x0, #0x17]
    // 0xd8021c: DecompressPointer r2
    //     0xd8021c: add             x2, x2, HEAP, lsl #32
    // 0xd80220: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd80220: ldur            w0, [x1, #0x17]
    // 0xd80224: DecompressPointer r0
    //     0xd80224: add             x0, x0, HEAP, lsl #32
    // 0xd80228: r1 = LoadClassIdInstr(r2)
    //     0xd80228: ldur            x1, [x2, #-1]
    //     0xd8022c: ubfx            x1, x1, #0xc, #0x14
    // 0xd80230: stp             x0, x2, [SP]
    // 0xd80234: mov             x0, x1
    // 0xd80238: mov             lr, x0
    // 0xd8023c: ldr             lr, [x21, lr, lsl #3]
    // 0xd80240: blr             lr
    // 0xd80244: b               #0xd8024c
    // 0xd80248: r0 = false
    //     0xd80248: add             x0, NULL, #0x30  ; false
    // 0xd8024c: LeaveFrame
    //     0xd8024c: mov             SP, fp
    //     0xd80250: ldp             fp, lr, [SP], #0x10
    // 0xd80254: ret
    //     0xd80254: ret             
    // 0xd80258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd80258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8025c: b               #0xd801a8
  }
  _ accept(/* No info */) {
    // ** addr: 0xe34b88, size: 0x4c
    // 0xe34b88: EnterFrame
    //     0xe34b88: stp             fp, lr, [SP, #-0x10]!
    //     0xe34b8c: mov             fp, SP
    // 0xe34b90: mov             x16, x2
    // 0xe34b94: mov             x2, x1
    // 0xe34b98: mov             x1, x16
    // 0xe34b9c: CheckStackOverflow
    //     0xe34b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34ba0: cmp             SP, x16
    //     0xe34ba4: b.ls            #0xe34bcc
    // 0xe34ba8: r0 = LoadClassIdInstr(r1)
    //     0xe34ba8: ldur            x0, [x1, #-1]
    //     0xe34bac: ubfx            x0, x0, #0xc, #0x14
    // 0xe34bb0: r0 = GDT[cid_x0 + -0xfe4]()
    //     0xe34bb0: sub             lr, x0, #0xfe4
    //     0xe34bb4: ldr             lr, [x21, lr, lsl #3]
    //     0xe34bb8: blr             lr
    // 0xe34bbc: r0 = Null
    //     0xe34bbc: mov             x0, NULL
    // 0xe34bc0: LeaveFrame
    //     0xe34bc0: mov             SP, fp
    //     0xe34bc4: ldp             fp, lr, [SP], #0x10
    // 0xe34bc8: ret
    //     0xe34bc8: ret             
    // 0xe34bcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34bcc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34bd0: b               #0xe34ba8
  }
}
