// lib: , url: package:xml/src/xml_events/events/start_element.dart

// class id: 1051338, size: 0x8
class :: {
}

// class id: 204, size: 0x20, field offset: 0x14
class XmlStartElementEvent extends _XmlEndElementEvent&XmlEvent&XmlNamed {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf4464, size: 0xac
    // 0xbf4464: EnterFrame
    //     0xbf4464: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4468: mov             fp, SP
    // 0xbf446c: AllocStack(0x20)
    //     0xbf446c: sub             SP, SP, #0x20
    // 0xbf4470: CheckStackOverflow
    //     0xbf4470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4474: cmp             SP, x16
    //     0xbf4478: b.ls            #0xbf4508
    // 0xbf447c: ldr             x0, [fp, #0x10]
    // 0xbf4480: LoadField: r3 = r0->field_13
    //     0xbf4480: ldur            w3, [x0, #0x13]
    // 0xbf4484: DecompressPointer r3
    //     0xbf4484: add             x3, x3, HEAP, lsl #32
    // 0xbf4488: stur            x3, [fp, #-0x10]
    // 0xbf448c: LoadField: r4 = r0->field_1b
    //     0xbf448c: ldur            w4, [x0, #0x1b]
    // 0xbf4490: DecompressPointer r4
    //     0xbf4490: add             x4, x4, HEAP, lsl #32
    // 0xbf4494: stur            x4, [fp, #-8]
    // 0xbf4498: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbf4498: ldur            w2, [x0, #0x17]
    // 0xbf449c: DecompressPointer r2
    //     0xbf449c: add             x2, x2, HEAP, lsl #32
    // 0xbf44a0: r1 = Instance_ListEquality
    //     0xbf44a0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26580] Obj!ListEquality<XmlEventAttribute>@e25d21
    //     0xbf44a4: ldr             x1, [x1, #0x580]
    // 0xbf44a8: r0 = hash()
    //     0xbf44a8: bl              #0xd3bd84  ; [package:collection/src/equality.dart] ListEquality::hash
    // 0xbf44ac: mov             x2, x0
    // 0xbf44b0: r0 = BoxInt64Instr(r2)
    //     0xbf44b0: sbfiz           x0, x2, #1, #0x1f
    //     0xbf44b4: cmp             x2, x0, asr #1
    //     0xbf44b8: b.eq            #0xbf44c4
    //     0xbf44bc: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf44c0: stur            x2, [x0, #7]
    // 0xbf44c4: ldur            x16, [fp, #-8]
    // 0xbf44c8: stp             x0, x16, [SP]
    // 0xbf44cc: ldur            x2, [fp, #-0x10]
    // 0xbf44d0: r1 = Instance_XmlNodeType
    //     0xbf44d0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26588] Obj!XmlNodeType@e2d3d1
    //     0xbf44d4: ldr             x1, [x1, #0x588]
    // 0xbf44d8: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xbf44d8: add             x4, PP, #8, lsl #12  ; [pp+0x8e00] List(5) [0, 0x4, 0x2, 0x4, Null]
    //     0xbf44dc: ldr             x4, [x4, #0xe00]
    // 0xbf44e0: r0 = hash()
    //     0xbf44e0: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf44e4: mov             x2, x0
    // 0xbf44e8: r0 = BoxInt64Instr(r2)
    //     0xbf44e8: sbfiz           x0, x2, #1, #0x1f
    //     0xbf44ec: cmp             x2, x0, asr #1
    //     0xbf44f0: b.eq            #0xbf44fc
    //     0xbf44f4: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf44f8: stur            x2, [x0, #7]
    // 0xbf44fc: LeaveFrame
    //     0xbf44fc: mov             SP, fp
    //     0xbf4500: ldp             fp, lr, [SP], #0x10
    // 0xbf4504: ret
    //     0xbf4504: ret             
    // 0xbf4508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4508: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf450c: b               #0xbf447c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd800b8, size: 0xd8
    // 0xd800b8: EnterFrame
    //     0xd800b8: stp             fp, lr, [SP, #-0x10]!
    //     0xd800bc: mov             fp, SP
    // 0xd800c0: AllocStack(0x10)
    //     0xd800c0: sub             SP, SP, #0x10
    // 0xd800c4: CheckStackOverflow
    //     0xd800c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd800c8: cmp             SP, x16
    //     0xd800cc: b.ls            #0xd80188
    // 0xd800d0: ldr             x1, [fp, #0x10]
    // 0xd800d4: cmp             w1, NULL
    // 0xd800d8: b.ne            #0xd800ec
    // 0xd800dc: r0 = false
    //     0xd800dc: add             x0, NULL, #0x30  ; false
    // 0xd800e0: LeaveFrame
    //     0xd800e0: mov             SP, fp
    //     0xd800e4: ldp             fp, lr, [SP], #0x10
    // 0xd800e8: ret
    //     0xd800e8: ret             
    // 0xd800ec: r0 = 60
    //     0xd800ec: movz            x0, #0x3c
    // 0xd800f0: branchIfSmi(r1, 0xd800fc)
    //     0xd800f0: tbz             w1, #0, #0xd800fc
    // 0xd800f4: r0 = LoadClassIdInstr(r1)
    //     0xd800f4: ldur            x0, [x1, #-1]
    //     0xd800f8: ubfx            x0, x0, #0xc, #0x14
    // 0xd800fc: cmp             x0, #0xcc
    // 0xd80100: b.ne            #0xd80178
    // 0xd80104: ldr             x2, [fp, #0x18]
    // 0xd80108: LoadField: r0 = r1->field_13
    //     0xd80108: ldur            w0, [x1, #0x13]
    // 0xd8010c: DecompressPointer r0
    //     0xd8010c: add             x0, x0, HEAP, lsl #32
    // 0xd80110: LoadField: r3 = r2->field_13
    //     0xd80110: ldur            w3, [x2, #0x13]
    // 0xd80114: DecompressPointer r3
    //     0xd80114: add             x3, x3, HEAP, lsl #32
    // 0xd80118: r4 = LoadClassIdInstr(r0)
    //     0xd80118: ldur            x4, [x0, #-1]
    //     0xd8011c: ubfx            x4, x4, #0xc, #0x14
    // 0xd80120: stp             x3, x0, [SP]
    // 0xd80124: mov             x0, x4
    // 0xd80128: mov             lr, x0
    // 0xd8012c: ldr             lr, [x21, lr, lsl #3]
    // 0xd80130: blr             lr
    // 0xd80134: tbnz            w0, #4, #0xd80178
    // 0xd80138: ldr             x1, [fp, #0x18]
    // 0xd8013c: ldr             x0, [fp, #0x10]
    // 0xd80140: LoadField: r2 = r0->field_1b
    //     0xd80140: ldur            w2, [x0, #0x1b]
    // 0xd80144: DecompressPointer r2
    //     0xd80144: add             x2, x2, HEAP, lsl #32
    // 0xd80148: LoadField: r3 = r1->field_1b
    //     0xd80148: ldur            w3, [x1, #0x1b]
    // 0xd8014c: DecompressPointer r3
    //     0xd8014c: add             x3, x3, HEAP, lsl #32
    // 0xd80150: cmp             w2, w3
    // 0xd80154: b.ne            #0xd80178
    // 0xd80158: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xd80158: ldur            w2, [x0, #0x17]
    // 0xd8015c: DecompressPointer r2
    //     0xd8015c: add             x2, x2, HEAP, lsl #32
    // 0xd80160: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xd80160: ldur            w3, [x1, #0x17]
    // 0xd80164: DecompressPointer r3
    //     0xd80164: add             x3, x3, HEAP, lsl #32
    // 0xd80168: r1 = Instance_ListEquality
    //     0xd80168: add             x1, PP, #0x26, lsl #12  ; [pp+0x26580] Obj!ListEquality<XmlEventAttribute>@e25d21
    //     0xd8016c: ldr             x1, [x1, #0x580]
    // 0xd80170: r0 = equals()
    //     0xd80170: bl              #0xd2e3dc  ; [package:collection/src/equality.dart] ListEquality::equals
    // 0xd80174: b               #0xd8017c
    // 0xd80178: r0 = false
    //     0xd80178: add             x0, NULL, #0x30  ; false
    // 0xd8017c: LeaveFrame
    //     0xd8017c: mov             SP, fp
    //     0xd80180: ldp             fp, lr, [SP], #0x10
    // 0xd80184: ret
    //     0xd80184: ret             
    // 0xd80188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd80188: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8018c: b               #0xd800d0
  }
  _ accept(/* No info */) {
    // ** addr: 0xe34b3c, size: 0x4c
    // 0xe34b3c: EnterFrame
    //     0xe34b3c: stp             fp, lr, [SP, #-0x10]!
    //     0xe34b40: mov             fp, SP
    // 0xe34b44: mov             x16, x2
    // 0xe34b48: mov             x2, x1
    // 0xe34b4c: mov             x1, x16
    // 0xe34b50: CheckStackOverflow
    //     0xe34b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34b54: cmp             SP, x16
    //     0xe34b58: b.ls            #0xe34b80
    // 0xe34b5c: r0 = LoadClassIdInstr(r1)
    //     0xe34b5c: ldur            x0, [x1, #-1]
    //     0xe34b60: ubfx            x0, x0, #0xc, #0x14
    // 0xe34b64: r0 = GDT[cid_x0 + -0xfe6]()
    //     0xe34b64: sub             lr, x0, #0xfe6
    //     0xe34b68: ldr             lr, [x21, lr, lsl #3]
    //     0xe34b6c: blr             lr
    // 0xe34b70: r0 = Null
    //     0xe34b70: mov             x0, NULL
    // 0xe34b74: LeaveFrame
    //     0xe34b74: mov             SP, fp
    //     0xe34b78: ldp             fp, lr, [SP], #0x10
    // 0xe34b7c: ret
    //     0xe34b7c: ret             
    // 0xe34b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34b80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34b84: b               #0xe34b5c
  }
}
