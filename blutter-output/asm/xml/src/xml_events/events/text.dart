// lib: , url: package:xml/src/xml_events/events/text.dart

// class id: 1051339, size: 0x8
class :: {
}

// class id: 200, size: 0x20, field offset: 0x14
class XmlRawTextEvent extends XmlEvent
    implements XmlTextEvent {

  late final String value; // offset: 0x1c

  String value(XmlRawTextEvent) {
    // ** addr: 0xac9254, size: 0x40
    // 0xac9254: EnterFrame
    //     0xac9254: stp             fp, lr, [SP, #-0x10]!
    //     0xac9258: mov             fp, SP
    // 0xac925c: CheckStackOverflow
    //     0xac925c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xac9260: cmp             SP, x16
    //     0xac9264: b.ls            #0xac928c
    // 0xac9268: ldr             x0, [fp, #0x10]
    // 0xac926c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xac926c: ldur            w1, [x0, #0x17]
    // 0xac9270: DecompressPointer r1
    //     0xac9270: add             x1, x1, HEAP, lsl #32
    // 0xac9274: LoadField: r2 = r0->field_13
    //     0xac9274: ldur            w2, [x0, #0x13]
    // 0xac9278: DecompressPointer r2
    //     0xac9278: add             x2, x2, HEAP, lsl #32
    // 0xac927c: r0 = decode()
    //     0xac927c: bl              #0x88e99c  ; [package:xml/src/xml/entities/entity_mapping.dart] XmlEntityMapping::decode
    // 0xac9280: LeaveFrame
    //     0xac9280: mov             SP, fp
    //     0xac9284: ldp             fp, lr, [SP], #0x10
    // 0xac9288: ret
    //     0xac9288: ret             
    // 0xac928c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xac928c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xac9290: b               #0xac9268
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xbf457c, size: 0x78
    // 0xbf457c: EnterFrame
    //     0xbf457c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4580: mov             fp, SP
    // 0xbf4584: CheckStackOverflow
    //     0xbf4584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4588: cmp             SP, x16
    //     0xbf458c: b.ls            #0xbf45ec
    // 0xbf4590: ldr             x1, [fp, #0x10]
    // 0xbf4594: LoadField: r0 = r1->field_1b
    //     0xbf4594: ldur            w0, [x1, #0x1b]
    // 0xbf4598: DecompressPointer r0
    //     0xbf4598: add             x0, x0, HEAP, lsl #32
    // 0xbf459c: r16 = Sentinel
    //     0xbf459c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf45a0: cmp             w0, w16
    // 0xbf45a4: b.ne            #0xbf45b4
    // 0xbf45a8: r2 = value
    //     0xbf45a8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a20] Field <XmlRawTextEvent.value>: late final (offset: 0x1c)
    //     0xbf45ac: ldr             x2, [x2, #0xa20]
    // 0xbf45b0: r0 = InitLateFinalInstanceField()
    //     0xbf45b0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xbf45b4: mov             x2, x0
    // 0xbf45b8: r1 = Instance_XmlNodeType
    //     0xbf45b8: add             x1, PP, #0x31, lsl #12  ; [pp+0x310a0] Obj!XmlNodeType@e2d391
    //     0xbf45bc: ldr             x1, [x1, #0xa0]
    // 0xbf45c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf45c0: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf45c4: r0 = hash()
    //     0xbf45c4: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf45c8: mov             x2, x0
    // 0xbf45cc: r0 = BoxInt64Instr(r2)
    //     0xbf45cc: sbfiz           x0, x2, #1, #0x1f
    //     0xbf45d0: cmp             x2, x0, asr #1
    //     0xbf45d4: b.eq            #0xbf45e0
    //     0xbf45d8: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf45dc: stur            x2, [x0, #7]
    // 0xbf45e0: LeaveFrame
    //     0xbf45e0: mov             SP, fp
    //     0xbf45e4: ldp             fp, lr, [SP], #0x10
    // 0xbf45e8: ret
    //     0xbf45e8: ret             
    // 0xbf45ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf45ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf45f0: b               #0xbf4590
  }
  _ ==(/* No info */) {
    // ** addr: 0xd80260, size: 0xd4
    // 0xd80260: EnterFrame
    //     0xd80260: stp             fp, lr, [SP, #-0x10]!
    //     0xd80264: mov             fp, SP
    // 0xd80268: AllocStack(0x18)
    //     0xd80268: sub             SP, SP, #0x18
    // 0xd8026c: CheckStackOverflow
    //     0xd8026c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd80270: cmp             SP, x16
    //     0xd80274: b.ls            #0xd8032c
    // 0xd80278: ldr             x1, [fp, #0x10]
    // 0xd8027c: cmp             w1, NULL
    // 0xd80280: b.ne            #0xd80294
    // 0xd80284: r0 = false
    //     0xd80284: add             x0, NULL, #0x30  ; false
    // 0xd80288: LeaveFrame
    //     0xd80288: mov             SP, fp
    //     0xd8028c: ldp             fp, lr, [SP], #0x10
    // 0xd80290: ret
    //     0xd80290: ret             
    // 0xd80294: r0 = 60
    //     0xd80294: movz            x0, #0x3c
    // 0xd80298: branchIfSmi(r1, 0xd802a4)
    //     0xd80298: tbz             w1, #0, #0xd802a4
    // 0xd8029c: r0 = LoadClassIdInstr(r1)
    //     0xd8029c: ldur            x0, [x1, #-1]
    //     0xd802a0: ubfx            x0, x0, #0xc, #0x14
    // 0xd802a4: cmp             x0, #0xc8
    // 0xd802a8: b.ne            #0xd8031c
    // 0xd802ac: LoadField: r0 = r1->field_1b
    //     0xd802ac: ldur            w0, [x1, #0x1b]
    // 0xd802b0: DecompressPointer r0
    //     0xd802b0: add             x0, x0, HEAP, lsl #32
    // 0xd802b4: r16 = Sentinel
    //     0xd802b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd802b8: cmp             w0, w16
    // 0xd802bc: b.ne            #0xd802cc
    // 0xd802c0: r2 = value
    //     0xd802c0: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a20] Field <XmlRawTextEvent.value>: late final (offset: 0x1c)
    //     0xd802c4: ldr             x2, [x2, #0xa20]
    // 0xd802c8: r0 = InitLateFinalInstanceField()
    //     0xd802c8: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xd802cc: ldr             x1, [fp, #0x18]
    // 0xd802d0: stur            x0, [fp, #-8]
    // 0xd802d4: LoadField: r0 = r1->field_1b
    //     0xd802d4: ldur            w0, [x1, #0x1b]
    // 0xd802d8: DecompressPointer r0
    //     0xd802d8: add             x0, x0, HEAP, lsl #32
    // 0xd802dc: r16 = Sentinel
    //     0xd802dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd802e0: cmp             w0, w16
    // 0xd802e4: b.ne            #0xd802f4
    // 0xd802e8: r2 = value
    //     0xd802e8: add             x2, PP, #0x25, lsl #12  ; [pp+0x25a20] Field <XmlRawTextEvent.value>: late final (offset: 0x1c)
    //     0xd802ec: ldr             x2, [x2, #0xa20]
    // 0xd802f0: r0 = InitLateFinalInstanceField()
    //     0xd802f0: bl              #0xec0234  ; InitLateFinalInstanceFieldStub
    // 0xd802f4: mov             x1, x0
    // 0xd802f8: ldur            x0, [fp, #-8]
    // 0xd802fc: r2 = LoadClassIdInstr(r0)
    //     0xd802fc: ldur            x2, [x0, #-1]
    //     0xd80300: ubfx            x2, x2, #0xc, #0x14
    // 0xd80304: stp             x1, x0, [SP]
    // 0xd80308: mov             x0, x2
    // 0xd8030c: mov             lr, x0
    // 0xd80310: ldr             lr, [x21, lr, lsl #3]
    // 0xd80314: blr             lr
    // 0xd80318: b               #0xd80320
    // 0xd8031c: r0 = false
    //     0xd8031c: add             x0, NULL, #0x30  ; false
    // 0xd80320: LeaveFrame
    //     0xd80320: mov             SP, fp
    //     0xd80324: ldp             fp, lr, [SP], #0x10
    // 0xd80328: ret
    //     0xd80328: ret             
    // 0xd8032c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8032c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd80330: b               #0xd80278
  }
  _ accept(/* No info */) {
    // ** addr: 0xe34bd4, size: 0x4c
    // 0xe34bd4: EnterFrame
    //     0xe34bd4: stp             fp, lr, [SP, #-0x10]!
    //     0xe34bd8: mov             fp, SP
    // 0xe34bdc: mov             x16, x2
    // 0xe34be0: mov             x2, x1
    // 0xe34be4: mov             x1, x16
    // 0xe34be8: CheckStackOverflow
    //     0xe34be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe34bec: cmp             SP, x16
    //     0xe34bf0: b.ls            #0xe34c18
    // 0xe34bf4: r0 = LoadClassIdInstr(r1)
    //     0xe34bf4: ldur            x0, [x1, #-1]
    //     0xe34bf8: ubfx            x0, x0, #0xc, #0x14
    // 0xe34bfc: r0 = GDT[cid_x0 + -0xfe8]()
    //     0xe34bfc: sub             lr, x0, #0xfe8
    //     0xe34c00: ldr             lr, [x21, lr, lsl #3]
    //     0xe34c04: blr             lr
    // 0xe34c08: r0 = Null
    //     0xe34c08: mov             x0, NULL
    // 0xe34c0c: LeaveFrame
    //     0xe34c0c: mov             SP, fp
    //     0xe34c10: ldp             fp, lr, [SP], #0x10
    // 0xe34c14: ret
    //     0xe34c14: ret             
    // 0xe34c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe34c18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe34c1c: b               #0xe34bf4
  }
}

// class id: 201, size: 0x14, field offset: 0x14
abstract class XmlTextEvent extends XmlEvent {
}
