// lib: , url: package:xml/src/xml_events/iterable.dart

// class id: 1051340, size: 0x8
class :: {
}

// class id: 7206, size: 0x28, field offset: 0xc
class XmlEventIterable extends Iterable<dynamic> {

  get _ iterator(/* No info */) {
    // ** addr: 0x889000, size: 0x84
    // 0x889000: EnterFrame
    //     0x889000: stp             fp, lr, [SP, #-0x10]!
    //     0x889004: mov             fp, SP
    // 0x889008: AllocStack(0x18)
    //     0x889008: sub             SP, SP, #0x18
    // 0x88900c: CheckStackOverflow
    //     0x88900c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889010: cmp             SP, x16
    //     0x889014: b.ls            #0x88907c
    // 0x889018: LoadField: r2 = r1->field_b
    //     0x889018: ldur            w2, [x1, #0xb]
    // 0x88901c: DecompressPointer r2
    //     0x88901c: add             x2, x2, HEAP, lsl #32
    // 0x889020: stur            x2, [fp, #-0x18]
    // 0x889024: LoadField: r3 = r1->field_13
    //     0x889024: ldur            w3, [x1, #0x13]
    // 0x889028: DecompressPointer r3
    //     0x889028: add             x3, x3, HEAP, lsl #32
    // 0x88902c: stur            x3, [fp, #-0x10]
    // 0x889030: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x889030: ldur            w0, [x1, #0x17]
    // 0x889034: DecompressPointer r0
    //     0x889034: add             x0, x0, HEAP, lsl #32
    // 0x889038: stur            x0, [fp, #-8]
    // 0x88903c: r0 = XmlAnnotator()
    //     0x88903c: bl              #0x88f838  ; AllocateXmlAnnotatorStub -> XmlAnnotator (size=0x24)
    // 0x889040: mov             x1, x0
    // 0x889044: ldur            x2, [fp, #-8]
    // 0x889048: ldur            x3, [fp, #-0x10]
    // 0x88904c: stur            x0, [fp, #-8]
    // 0x889050: r0 = XmlAnnotator()
    //     0x889050: bl              #0x88f774  ; [package:xml/src/xml_events/annotations/annotator.dart] XmlAnnotator::XmlAnnotator
    // 0x889054: r0 = XmlEventIterator()
    //     0x889054: bl              #0x88f768  ; AllocateXmlEventIteratorStub -> XmlEventIterator (size=0x18)
    // 0x889058: mov             x1, x0
    // 0x88905c: ldur            x2, [fp, #-0x18]
    // 0x889060: ldur            x3, [fp, #-8]
    // 0x889064: stur            x0, [fp, #-8]
    // 0x889068: r0 = XmlEventIterator()
    //     0x889068: bl              #0x889084  ; [package:xml/src/xml_events/iterator.dart] XmlEventIterator::XmlEventIterator
    // 0x88906c: ldur            x0, [fp, #-8]
    // 0x889070: LeaveFrame
    //     0x889070: mov             SP, fp
    //     0x889074: ldp             fp, lr, [SP], #0x10
    // 0x889078: ret
    //     0x889078: ret             
    // 0x88907c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88907c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889080: b               #0x889018
  }
}
