// lib: , url: package:xml/src/xml_events/iterator.dart

// class id: 1051341, size: 0x8
class :: {
}

// class id: 194, size: 0x18, field offset: 0x8
class XmlEventIterator extends Object
    implements Iterator<X0> {

  get _ current(/* No info */) {
    // ** addr: 0x6da7a0, size: 0x20
    // 0x6da7a0: LoadField: r0 = r1->field_13
    //     0x6da7a0: ldur            w0, [x1, #0x13]
    // 0x6da7a4: DecompressPointer r0
    //     0x6da7a4: add             x0, x0, HEAP, lsl #32
    // 0x6da7a8: cmp             w0, NULL
    // 0x6da7ac: b.eq            #0x6da7b4
    // 0x6da7b0: ret
    //     0x6da7b0: ret             
    // 0x6da7b4: EnterFrame
    //     0x6da7b4: stp             fp, lr, [SP, #-0x10]!
    //     0x6da7b8: mov             fp, SP
    // 0x6da7bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6da7bc: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ moveNext(/* No info */) {
    // ** addr: 0x751260, size: 0x230
    // 0x751260: EnterFrame
    //     0x751260: stp             fp, lr, [SP, #-0x10]!
    //     0x751264: mov             fp, SP
    // 0x751268: AllocStack(0x28)
    //     0x751268: sub             SP, SP, #0x28
    // 0x75126c: SetupParameters(XmlEventIterator this /* r1 => r3, fp-0x10 */)
    //     0x75126c: mov             x3, x1
    //     0x751270: stur            x1, [fp, #-0x10]
    // 0x751274: CheckStackOverflow
    //     0x751274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x751278: cmp             SP, x16
    //     0x75127c: b.ls            #0x751488
    // 0x751280: LoadField: r4 = r3->field_f
    //     0x751280: ldur            w4, [x3, #0xf]
    // 0x751284: DecompressPointer r4
    //     0x751284: add             x4, x4, HEAP, lsl #32
    // 0x751288: stur            x4, [fp, #-8]
    // 0x75128c: cmp             w4, NULL
    // 0x751290: b.eq            #0x7513a4
    // 0x751294: LoadField: r1 = r3->field_7
    //     0x751294: ldur            w1, [x3, #7]
    // 0x751298: DecompressPointer r1
    //     0x751298: add             x1, x1, HEAP, lsl #32
    // 0x75129c: r0 = LoadClassIdInstr(r1)
    //     0x75129c: ldur            x0, [x1, #-1]
    //     0x7512a0: ubfx            x0, x0, #0xc, #0x14
    // 0x7512a4: mov             x2, x4
    // 0x7512a8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x7512a8: sub             lr, x0, #1, lsl #12
    //     0x7512ac: ldr             lr, [x21, lr, lsl #3]
    //     0x7512b0: blr             lr
    // 0x7512b4: mov             x2, x0
    // 0x7512b8: stur            x2, [fp, #-0x28]
    // 0x7512bc: r0 = LoadClassIdInstr(r2)
    //     0x7512bc: ldur            x0, [x2, #-1]
    //     0x7512c0: ubfx            x0, x0, #0xc, #0x14
    // 0x7512c4: cmp             x0, #0x2f4
    // 0x7512c8: b.ne            #0x751350
    // 0x7512cc: ldur            x3, [fp, #-0x10]
    // 0x7512d0: ldur            x1, [fp, #-8]
    // 0x7512d4: mov             x0, x2
    // 0x7512d8: StoreField: r3->field_f = r0
    //     0x7512d8: stur            w0, [x3, #0xf]
    //     0x7512dc: ldurb           w16, [x3, #-1]
    //     0x7512e0: ldurb           w17, [x0, #-1]
    //     0x7512e4: and             x16, x17, x16, lsr #2
    //     0x7512e8: tst             x16, HEAP, lsr #32
    //     0x7512ec: b.eq            #0x7512f4
    //     0x7512f0: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x7512f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7512f4: ldur            w4, [x2, #0x17]
    // 0x7512f8: DecompressPointer r4
    //     0x7512f8: add             x4, x4, HEAP, lsl #32
    // 0x7512fc: mov             x0, x4
    // 0x751300: StoreField: r3->field_13 = r0
    //     0x751300: stur            w0, [x3, #0x13]
    //     0x751304: tbz             w0, #0, #0x751320
    //     0x751308: ldurb           w16, [x3, #-1]
    //     0x75130c: ldurb           w17, [x0, #-1]
    //     0x751310: and             x16, x17, x16, lsr #2
    //     0x751314: tst             x16, HEAP, lsr #32
    //     0x751318: b.eq            #0x751320
    //     0x75131c: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x751320: LoadField: r0 = r3->field_b
    //     0x751320: ldur            w0, [x3, #0xb]
    // 0x751324: DecompressPointer r0
    //     0x751324: add             x0, x0, HEAP, lsl #32
    // 0x751328: LoadField: r3 = r1->field_7
    //     0x751328: ldur            w3, [x1, #7]
    // 0x75132c: DecompressPointer r3
    //     0x75132c: add             x3, x3, HEAP, lsl #32
    // 0x751330: LoadField: r5 = r1->field_b
    //     0x751330: ldur            x5, [x1, #0xb]
    // 0x751334: mov             x1, x0
    // 0x751338: mov             x2, x4
    // 0x75133c: r0 = annotate()
    //     0x75133c: bl              #0x7516b8  ; [package:xml/src/xml_events/annotations/annotator.dart] XmlAnnotator::annotate
    // 0x751340: r0 = true
    //     0x751340: add             x0, NULL, #0x20  ; true
    // 0x751344: LeaveFrame
    //     0x751344: mov             SP, fp
    //     0x751348: ldp             fp, lr, [SP], #0x10
    // 0x75134c: ret
    //     0x75134c: ret             
    // 0x751350: ldur            x3, [fp, #-0x10]
    // 0x751354: ldur            x1, [fp, #-8]
    // 0x751358: LoadField: r4 = r1->field_b
    //     0x751358: ldur            x4, [x1, #0xb]
    // 0x75135c: LoadField: r5 = r1->field_7
    //     0x75135c: ldur            w5, [x1, #7]
    // 0x751360: DecompressPointer r5
    //     0x751360: add             x5, x5, HEAP, lsl #32
    // 0x751364: stur            x5, [fp, #-0x20]
    // 0x751368: LoadField: r1 = r5->field_7
    //     0x751368: ldur            w1, [x5, #7]
    // 0x75136c: r6 = LoadInt32Instr(r1)
    //     0x75136c: sbfx            x6, x1, #1, #0x1f
    // 0x751370: cmp             x4, x6
    // 0x751374: b.lt            #0x7513b4
    // 0x751378: StoreField: r3->field_f = rNULL
    //     0x751378: stur            NULL, [x3, #0xf]
    // 0x75137c: StoreField: r3->field_13 = rNULL
    //     0x75137c: stur            NULL, [x3, #0x13]
    // 0x751380: LoadField: r1 = r3->field_b
    //     0x751380: ldur            w1, [x3, #0xb]
    // 0x751384: DecompressPointer r1
    //     0x751384: add             x1, x1, HEAP, lsl #32
    // 0x751388: mov             x2, x5
    // 0x75138c: mov             x3, x4
    // 0x751390: r0 = close()
    //     0x751390: bl              #0x7514a8  ; [package:xml/src/xml_events/annotations/annotator.dart] XmlAnnotator::close
    // 0x751394: r0 = false
    //     0x751394: add             x0, NULL, #0x30  ; false
    // 0x751398: LeaveFrame
    //     0x751398: mov             SP, fp
    //     0x75139c: ldp             fp, lr, [SP], #0x10
    // 0x7513a0: ret
    //     0x7513a0: ret             
    // 0x7513a4: r0 = false
    //     0x7513a4: add             x0, NULL, #0x30  ; false
    // 0x7513a8: LeaveFrame
    //     0x7513a8: mov             SP, fp
    //     0x7513ac: ldp             fp, lr, [SP], #0x10
    // 0x7513b0: ret
    //     0x7513b0: ret             
    // 0x7513b4: cmp             x0, #0x2f3
    // 0x7513b8: b.ne            #0x751468
    // 0x7513bc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x7513bc: ldur            w0, [x2, #0x17]
    // 0x7513c0: DecompressPointer r0
    //     0x7513c0: add             x0, x0, HEAP, lsl #32
    // 0x7513c4: stur            x0, [fp, #-8]
    // 0x7513c8: add             x6, x4, #1
    // 0x7513cc: stur            x6, [fp, #-0x18]
    // 0x7513d0: r1 = <Never>
    //     0x7513d0: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x7513d4: r0 = Failure()
    //     0x7513d4: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0x7513d8: ldur            x1, [fp, #-8]
    // 0x7513dc: ArrayStore: r0[0] = r1  ; List_4
    //     0x7513dc: stur            w1, [x0, #0x17]
    // 0x7513e0: ldur            x2, [fp, #-0x20]
    // 0x7513e4: StoreField: r0->field_7 = r2
    //     0x7513e4: stur            w2, [x0, #7]
    // 0x7513e8: ldur            x2, [fp, #-0x18]
    // 0x7513ec: StoreField: r0->field_b = r2
    //     0x7513ec: stur            x2, [x0, #0xb]
    // 0x7513f0: ldur            x2, [fp, #-0x10]
    // 0x7513f4: StoreField: r2->field_f = r0
    //     0x7513f4: stur            w0, [x2, #0xf]
    //     0x7513f8: ldurb           w16, [x2, #-1]
    //     0x7513fc: ldurb           w17, [x0, #-1]
    //     0x751400: and             x16, x17, x16, lsr #2
    //     0x751404: tst             x16, HEAP, lsr #32
    //     0x751408: b.eq            #0x751410
    //     0x75140c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x751410: StoreField: r2->field_13 = rNULL
    //     0x751410: stur            NULL, [x2, #0x13]
    // 0x751414: ldur            x0, [fp, #-0x28]
    // 0x751418: LoadField: r2 = r0->field_7
    //     0x751418: ldur            w2, [x0, #7]
    // 0x75141c: DecompressPointer r2
    //     0x75141c: add             x2, x2, HEAP, lsl #32
    // 0x751420: stur            x2, [fp, #-0x10]
    // 0x751424: LoadField: r3 = r0->field_b
    //     0x751424: ldur            x3, [x0, #0xb]
    // 0x751428: stur            x3, [fp, #-0x18]
    // 0x75142c: r0 = XmlParserException()
    //     0x75142c: bl              #0x751490  ; AllocateXmlParserExceptionStub -> XmlParserException (size=0x24)
    // 0x751430: mov             x1, x0
    // 0x751434: ldur            x0, [fp, #-0x10]
    // 0x751438: ArrayStore: r1[0] = r0  ; List_4
    //     0x751438: stur            w0, [x1, #0x17]
    // 0x75143c: ldur            x0, [fp, #-0x18]
    // 0x751440: StoreField: r1->field_1b = r0
    //     0x751440: stur            x0, [x1, #0x1b]
    // 0x751444: r0 = Sentinel
    //     0x751444: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x751448: StoreField: r1->field_b = r0
    //     0x751448: stur            w0, [x1, #0xb]
    // 0x75144c: StoreField: r1->field_f = r0
    //     0x75144c: stur            w0, [x1, #0xf]
    // 0x751450: StoreField: r1->field_13 = r0
    //     0x751450: stur            w0, [x1, #0x13]
    // 0x751454: ldur            x0, [fp, #-8]
    // 0x751458: StoreField: r1->field_7 = r0
    //     0x751458: stur            w0, [x1, #7]
    // 0x75145c: mov             x0, x1
    // 0x751460: r0 = Throw()
    //     0x751460: bl              #0xec04b8  ; ThrowStub
    // 0x751464: brk             #0
    // 0x751468: r0 = UnsupportedError()
    //     0x751468: bl              #0x5f7814  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x75146c: mov             x1, x0
    // 0x751470: r0 = "Successful parse results do not have a message."
    //     0x751470: add             x0, PP, #0x26, lsl #12  ; [pp+0x26498] "Successful parse results do not have a message."
    //     0x751474: ldr             x0, [x0, #0x498]
    // 0x751478: StoreField: r1->field_b = r0
    //     0x751478: stur            w0, [x1, #0xb]
    // 0x75147c: mov             x0, x1
    // 0x751480: r0 = Throw()
    //     0x751480: bl              #0xec04b8  ; ThrowStub
    // 0x751484: brk             #0
    // 0x751488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x751488: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x75148c: b               #0x751280
  }
  _ XmlEventIterator(/* No info */) {
    // ** addr: 0x889084, size: 0xe8
    // 0x889084: EnterFrame
    //     0x889084: stp             fp, lr, [SP, #-0x10]!
    //     0x889088: mov             fp, SP
    // 0x88908c: AllocStack(0x10)
    //     0x88908c: sub             SP, SP, #0x10
    // 0x889090: SetupParameters(XmlEventIterator this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0 */)
    //     0x889090: mov             x0, x3
    //     0x889094: stur            x1, [fp, #-8]
    //     0x889098: stur            x2, [fp, #-0x10]
    // 0x88909c: CheckStackOverflow
    //     0x88909c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8890a0: cmp             SP, x16
    //     0x8890a4: b.ls            #0x889164
    // 0x8890a8: StoreField: r1->field_b = r0
    //     0x8890a8: stur            w0, [x1, #0xb]
    //     0x8890ac: ldurb           w16, [x1, #-1]
    //     0x8890b0: ldurb           w17, [x0, #-1]
    //     0x8890b4: and             x16, x17, x16, lsr #2
    //     0x8890b8: tst             x16, HEAP, lsr #32
    //     0x8890bc: b.eq            #0x8890c4
    //     0x8890c0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x8890c4: r0 = InitLateStaticField(0x1780) // [package:xml/src/xml_events/parser.dart] ::eventParserCache
    //     0x8890c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8890c8: ldr             x0, [x0, #0x2f00]
    //     0x8890cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8890d0: cmp             w0, w16
    //     0x8890d4: b.ne            #0x8890e4
    //     0x8890d8: add             x2, PP, #0x26, lsl #12  ; [pp+0x265b8] Field <::.eventParserCache>: static late final (offset: 0x1780)
    //     0x8890dc: ldr             x2, [x2, #0x5b8]
    //     0x8890e0: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x8890e4: mov             x1, x0
    // 0x8890e8: r2 = Instance_XmlDefaultEntityMapping
    //     0x8890e8: add             x2, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0x8890ec: ldr             x2, [x2, #0x5c0]
    // 0x8890f0: r0 = []()
    //     0x8890f0: bl              #0x88916c  ; [package:xml/src/xml/utils/cache.dart] XmlCache::[]
    // 0x8890f4: ldur            x2, [fp, #-8]
    // 0x8890f8: StoreField: r2->field_7 = r0
    //     0x8890f8: stur            w0, [x2, #7]
    //     0x8890fc: tbz             w0, #0, #0x889118
    //     0x889100: ldurb           w16, [x2, #-1]
    //     0x889104: ldurb           w17, [x0, #-1]
    //     0x889108: and             x16, x17, x16, lsr #2
    //     0x88910c: tst             x16, HEAP, lsr #32
    //     0x889110: b.eq            #0x889118
    //     0x889114: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x889118: r1 = <Never>
    //     0x889118: ldr             x1, [PP, #0x2808]  ; [pp+0x2808] TypeArguments: <Never>
    // 0x88911c: r0 = Failure()
    //     0x88911c: bl              #0x75149c  ; AllocateFailureStub -> Failure (size=0x1c)
    // 0x889120: r1 = ""
    //     0x889120: ldr             x1, [PP, #0x288]  ; [pp+0x288] ""
    // 0x889124: ArrayStore: r0[0] = r1  ; List_4
    //     0x889124: stur            w1, [x0, #0x17]
    // 0x889128: ldur            x1, [fp, #-0x10]
    // 0x88912c: StoreField: r0->field_7 = r1
    //     0x88912c: stur            w1, [x0, #7]
    // 0x889130: StoreField: r0->field_b = rZR
    //     0x889130: stur            xzr, [x0, #0xb]
    // 0x889134: ldur            x1, [fp, #-8]
    // 0x889138: StoreField: r1->field_f = r0
    //     0x889138: stur            w0, [x1, #0xf]
    //     0x88913c: ldurb           w16, [x1, #-1]
    //     0x889140: ldurb           w17, [x0, #-1]
    //     0x889144: and             x16, x17, x16, lsr #2
    //     0x889148: tst             x16, HEAP, lsr #32
    //     0x88914c: b.eq            #0x889154
    //     0x889150: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x889154: r0 = Null
    //     0x889154: mov             x0, NULL
    // 0x889158: LeaveFrame
    //     0x889158: mov             SP, fp
    //     0x88915c: ldp             fp, lr, [SP], #0x10
    // 0x889160: ret
    //     0x889160: ret             
    // 0x889164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889164: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889168: b               #0x8890a8
  }
}
