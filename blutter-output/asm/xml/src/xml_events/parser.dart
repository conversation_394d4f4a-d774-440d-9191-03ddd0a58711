// lib: , url: package:xml/src/xml_events/parser.dart

// class id: 1051342, size: 0x8
class :: {

  static late final XmlCache<XmlEntityMapping, Parser<XmlEvent>> eventParserCache; // offset: 0x1780

  static XmlCache<XmlEntityMapping, Parser<XmlEvent>> eventParserCache() {
    // ** addr: 0x8893bc, size: 0x84
    // 0x8893bc: EnterFrame
    //     0x8893bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8893c0: mov             fp, SP
    // 0x8893c4: AllocStack(0x20)
    //     0x8893c4: sub             SP, SP, #0x20
    // 0x8893c8: CheckStackOverflow
    //     0x8893c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8893cc: cmp             SP, x16
    //     0x8893d0: b.ls            #0x889438
    // 0x8893d4: r16 = <XmlEntityMapping, Parser<XmlEvent>>
    //     0x8893d4: add             x16, PP, #0x26, lsl #12  ; [pp+0x265d8] TypeArguments: <XmlEntityMapping, Parser<XmlEvent>>
    //     0x8893d8: ldr             x16, [x16, #0x5d8]
    // 0x8893dc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8893e0: stp             lr, x16, [SP]
    // 0x8893e4: r0 = Map._fromLiteral()
    //     0x8893e4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x8893e8: r1 = <XmlEntityMapping, Parser<XmlEvent>>
    //     0x8893e8: add             x1, PP, #0x26, lsl #12  ; [pp+0x265d8] TypeArguments: <XmlEntityMapping, Parser<XmlEvent>>
    //     0x8893ec: ldr             x1, [x1, #0x5d8]
    // 0x8893f0: stur            x0, [fp, #-8]
    // 0x8893f4: r0 = XmlCache()
    //     0x8893f4: bl              #0x889440  ; AllocateXmlCacheStub -> XmlCache<X0, X1> (size=0x1c)
    // 0x8893f8: mov             x3, x0
    // 0x8893fc: ldur            x0, [fp, #-8]
    // 0x889400: stur            x3, [fp, #-0x10]
    // 0x889404: ArrayStore: r3[0] = r0  ; List_4
    //     0x889404: stur            w0, [x3, #0x17]
    // 0x889408: r1 = Function '<anonymous closure>': static.
    //     0x889408: add             x1, PP, #0x26, lsl #12  ; [pp+0x265e0] AnonymousClosure: static (0x88944c), in [package:xml/src/xml_events/parser.dart] ::eventParserCache (0x8893bc)
    //     0x88940c: ldr             x1, [x1, #0x5e0]
    // 0x889410: r2 = Null
    //     0x889410: mov             x2, NULL
    // 0x889414: r0 = AllocateClosure()
    //     0x889414: bl              #0xec1630  ; AllocateClosureStub
    // 0x889418: mov             x1, x0
    // 0x88941c: ldur            x0, [fp, #-0x10]
    // 0x889420: StoreField: r0->field_b = r1
    //     0x889420: stur            w1, [x0, #0xb]
    // 0x889424: r1 = 5
    //     0x889424: movz            x1, #0x5
    // 0x889428: StoreField: r0->field_f = r1
    //     0x889428: stur            x1, [x0, #0xf]
    // 0x88942c: LeaveFrame
    //     0x88942c: mov             SP, fp
    //     0x889430: ldp             fp, lr, [SP], #0x10
    // 0x889434: ret
    //     0x889434: ret             
    // 0x889438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88943c: b               #0x8893d4
  }
  [closure] static Parser<XmlEvent> <anonymous closure>(dynamic, XmlEntityMapping) {
    // ** addr: 0x88944c, size: 0x3c
    // 0x88944c: EnterFrame
    //     0x88944c: stp             fp, lr, [SP, #-0x10]!
    //     0x889450: mov             fp, SP
    // 0x889454: CheckStackOverflow
    //     0x889454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889458: cmp             SP, x16
    //     0x88945c: b.ls            #0x889480
    // 0x889460: r0 = XmlEventParser()
    //     0x889460: bl              #0x88f75c  ; AllocateXmlEventParserStub -> XmlEventParser (size=0xc)
    // 0x889464: mov             x1, x0
    // 0x889468: ldr             x0, [fp, #0x10]
    // 0x88946c: StoreField: r1->field_7 = r0
    //     0x88946c: stur            w0, [x1, #7]
    // 0x889470: r0 = build()
    //     0x889470: bl              #0x889488  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::build
    // 0x889474: LeaveFrame
    //     0x889474: mov             SP, fp
    //     0x889478: ldp             fp, lr, [SP], #0x10
    // 0x88947c: ret
    //     0x88947c: ret             
    // 0x889480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889480: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889484: b               #0x889460
  }
}

// class id: 193, size: 0xc, field offset: 0x8
//   const constructor, 
class XmlEventParser extends Object {

  _ build(/* No info */) {
    // ** addr: 0x889488, size: 0x64
    // 0x889488: EnterFrame
    //     0x889488: stp             fp, lr, [SP, #-0x10]!
    //     0x88948c: mov             fp, SP
    // 0x889490: AllocStack(0x10)
    //     0x889490: sub             SP, SP, #0x10
    // 0x889494: SetupParameters(XmlEventParser this /* r1 => r2 */)
    //     0x889494: mov             x2, x1
    // 0x889498: CheckStackOverflow
    //     0x889498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88949c: cmp             SP, x16
    //     0x8894a0: b.ls            #0x8894e4
    // 0x8894a4: r1 = Function 'event':.
    //     0x8894a4: add             x1, PP, #0x26, lsl #12  ; [pp+0x265e8] AnonymousClosure: (0x88a2f4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::event (0x88a32c)
    //     0x8894a8: ldr             x1, [x1, #0x5e8]
    // 0x8894ac: r0 = AllocateClosure()
    //     0x8894ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x8894b0: r16 = <XmlEvent>
    //     0x8894b0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26590] TypeArguments: <XmlEvent>
    //     0x8894b4: ldr             x16, [x16, #0x590]
    // 0x8894b8: stp             x0, x16, [SP]
    // 0x8894bc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8894bc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8894c0: r0 = ref0()
    //     0x8894c0: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x8894c4: r16 = <XmlEvent>
    //     0x8894c4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26590] TypeArguments: <XmlEvent>
    //     0x8894c8: ldr             x16, [x16, #0x590]
    // 0x8894cc: stp             x0, x16, [SP]
    // 0x8894d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8894d0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8894d4: r0 = resolve()
    //     0x8894d4: bl              #0x8894ec  ; [package:petitparser/src/definition/resolve.dart] ::resolve
    // 0x8894d8: LeaveFrame
    //     0x8894d8: mov             SP, fp
    //     0x8894dc: ldp             fp, lr, [SP], #0x10
    // 0x8894e0: ret
    //     0x8894e0: ret             
    // 0x8894e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8894e4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8894e8: b               #0x8894a4
  }
  [closure] Parser<XmlEvent> event(dynamic) {
    // ** addr: 0x88a2f4, size: 0x38
    // 0x88a2f4: EnterFrame
    //     0x88a2f4: stp             fp, lr, [SP, #-0x10]!
    //     0x88a2f8: mov             fp, SP
    // 0x88a2fc: ldr             x0, [fp, #0x10]
    // 0x88a300: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88a300: ldur            w1, [x0, #0x17]
    // 0x88a304: DecompressPointer r1
    //     0x88a304: add             x1, x1, HEAP, lsl #32
    // 0x88a308: CheckStackOverflow
    //     0x88a308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88a30c: cmp             SP, x16
    //     0x88a310: b.ls            #0x88a324
    // 0x88a314: r0 = event()
    //     0x88a314: bl              #0x88a32c  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::event
    // 0x88a318: LeaveFrame
    //     0x88a318: mov             SP, fp
    //     0x88a31c: ldp             fp, lr, [SP], #0x10
    // 0x88a320: ret
    //     0x88a320: ret             
    // 0x88a324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88a324: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88a328: b               #0x88a314
  }
  _ event(/* No info */) {
    // ** addr: 0x88a32c, size: 0x20c
    // 0x88a32c: EnterFrame
    //     0x88a32c: stp             fp, lr, [SP, #-0x10]!
    //     0x88a330: mov             fp, SP
    // 0x88a334: AllocStack(0x60)
    //     0x88a334: sub             SP, SP, #0x60
    // 0x88a338: SetupParameters(XmlEventParser this /* r1 => r0, fp-0x8 */)
    //     0x88a338: mov             x0, x1
    //     0x88a33c: stur            x1, [fp, #-8]
    // 0x88a340: CheckStackOverflow
    //     0x88a340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88a344: cmp             SP, x16
    //     0x88a348: b.ls            #0x88a530
    // 0x88a34c: mov             x2, x0
    // 0x88a350: r1 = Function 'characterData':.
    //     0x88a350: add             x1, PP, #0x26, lsl #12  ; [pp+0x265f0] AnonymousClosure: (0x88f630), in [package:xml/src/xml_events/parser.dart] XmlEventParser::characterData (0x88f668)
    //     0x88a354: ldr             x1, [x1, #0x5f0]
    // 0x88a358: r0 = AllocateClosure()
    //     0x88a358: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a35c: r16 = <XmlTextEvent>
    //     0x88a35c: add             x16, PP, #0x26, lsl #12  ; [pp+0x265f8] TypeArguments: <XmlTextEvent>
    //     0x88a360: ldr             x16, [x16, #0x5f8]
    // 0x88a364: stp             x0, x16, [SP]
    // 0x88a368: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a368: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a36c: r0 = ref0()
    //     0x88a36c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a370: ldur            x2, [fp, #-8]
    // 0x88a374: r1 = Function 'startElement':.
    //     0x88a374: add             x1, PP, #0x26, lsl #12  ; [pp+0x26600] AnonymousClosure: (0x88f3e4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::startElement (0x88f41c)
    //     0x88a378: ldr             x1, [x1, #0x600]
    // 0x88a37c: stur            x0, [fp, #-0x10]
    // 0x88a380: r0 = AllocateClosure()
    //     0x88a380: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a384: r16 = <XmlStartElementEvent>
    //     0x88a384: add             x16, PP, #0x26, lsl #12  ; [pp+0x264a0] TypeArguments: <XmlStartElementEvent>
    //     0x88a388: ldr             x16, [x16, #0x4a0]
    // 0x88a38c: stp             x0, x16, [SP]
    // 0x88a390: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a390: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a394: r0 = ref0()
    //     0x88a394: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a398: ldur            x2, [fp, #-8]
    // 0x88a39c: r1 = Function 'endElement':.
    //     0x88a39c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26608] AnonymousClosure: (0x88f28c), in [package:xml/src/xml_events/parser.dart] XmlEventParser::endElement (0x88f2c4)
    //     0x88a3a0: ldr             x1, [x1, #0x608]
    // 0x88a3a4: stur            x0, [fp, #-0x18]
    // 0x88a3a8: r0 = AllocateClosure()
    //     0x88a3a8: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a3ac: r16 = <XmlEndElementEvent>
    //     0x88a3ac: add             x16, PP, #0x26, lsl #12  ; [pp+0x26610] TypeArguments: <XmlEndElementEvent>
    //     0x88a3b0: ldr             x16, [x16, #0x610]
    // 0x88a3b4: stp             x0, x16, [SP]
    // 0x88a3b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a3b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a3bc: r0 = ref0()
    //     0x88a3bc: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a3c0: ldur            x2, [fp, #-8]
    // 0x88a3c4: r1 = Function 'comment':.
    //     0x88a3c4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26618] AnonymousClosure: (0x88f138), in [package:xml/src/xml_events/parser.dart] XmlEventParser::comment (0x88f170)
    //     0x88a3c8: ldr             x1, [x1, #0x618]
    // 0x88a3cc: stur            x0, [fp, #-0x20]
    // 0x88a3d0: r0 = AllocateClosure()
    //     0x88a3d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a3d4: r16 = <XmlCommentEvent>
    //     0x88a3d4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26620] TypeArguments: <XmlCommentEvent>
    //     0x88a3d8: ldr             x16, [x16, #0x620]
    // 0x88a3dc: stp             x0, x16, [SP]
    // 0x88a3e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a3e0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a3e4: r0 = ref0()
    //     0x88a3e4: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a3e8: ldur            x2, [fp, #-8]
    // 0x88a3ec: r1 = Function 'cdata':.
    //     0x88a3ec: add             x1, PP, #0x26, lsl #12  ; [pp+0x26628] AnonymousClosure: (0x88efe4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::cdata (0x88f01c)
    //     0x88a3f0: ldr             x1, [x1, #0x628]
    // 0x88a3f4: stur            x0, [fp, #-0x28]
    // 0x88a3f8: r0 = AllocateClosure()
    //     0x88a3f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a3fc: r16 = <XmlCDATAEvent>
    //     0x88a3fc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26630] TypeArguments: <XmlCDATAEvent>
    //     0x88a400: ldr             x16, [x16, #0x630]
    // 0x88a404: stp             x0, x16, [SP]
    // 0x88a408: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a408: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a40c: r0 = ref0()
    //     0x88a40c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a410: ldur            x2, [fp, #-8]
    // 0x88a414: r1 = Function 'declaration':.
    //     0x88a414: add             x1, PP, #0x26, lsl #12  ; [pp+0x26638] AnonymousClosure: (0x88e5c0), in [package:xml/src/xml_events/parser.dart] XmlEventParser::declaration (0x88e5f8)
    //     0x88a418: ldr             x1, [x1, #0x638]
    // 0x88a41c: stur            x0, [fp, #-0x30]
    // 0x88a420: r0 = AllocateClosure()
    //     0x88a420: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a424: r16 = <XmlDeclarationEvent>
    //     0x88a424: add             x16, PP, #0x26, lsl #12  ; [pp+0x264b8] TypeArguments: <XmlDeclarationEvent>
    //     0x88a428: ldr             x16, [x16, #0x4b8]
    // 0x88a42c: stp             x0, x16, [SP]
    // 0x88a430: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a430: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a434: r0 = ref0()
    //     0x88a434: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a438: ldur            x2, [fp, #-8]
    // 0x88a43c: r1 = Function 'processing':.
    //     0x88a43c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26640] AnonymousClosure: (0x88e168), in [package:xml/src/xml_events/parser.dart] XmlEventParser::processing (0x88e1a0)
    //     0x88a440: ldr             x1, [x1, #0x640]
    // 0x88a444: stur            x0, [fp, #-0x38]
    // 0x88a448: r0 = AllocateClosure()
    //     0x88a448: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a44c: r16 = <XmlProcessingEvent>
    //     0x88a44c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26648] TypeArguments: <XmlProcessingEvent>
    //     0x88a450: ldr             x16, [x16, #0x648]
    // 0x88a454: stp             x0, x16, [SP]
    // 0x88a458: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a458: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a45c: r0 = ref0()
    //     0x88a45c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a460: ldur            x2, [fp, #-8]
    // 0x88a464: r1 = Function 'doctype':.
    //     0x88a464: add             x1, PP, #0x26, lsl #12  ; [pp+0x26650] AnonymousClosure: (0x88a854), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctype (0x88a88c)
    //     0x88a468: ldr             x1, [x1, #0x650]
    // 0x88a46c: stur            x0, [fp, #-8]
    // 0x88a470: r0 = AllocateClosure()
    //     0x88a470: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a474: r16 = <XmlDoctypeEvent>
    //     0x88a474: add             x16, PP, #0x26, lsl #12  ; [pp+0x264d0] TypeArguments: <XmlDoctypeEvent>
    //     0x88a478: ldr             x16, [x16, #0x4d0]
    // 0x88a47c: stp             x0, x16, [SP]
    // 0x88a480: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a480: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a484: r0 = ref0()
    //     0x88a484: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a488: r1 = Null
    //     0x88a488: mov             x1, NULL
    // 0x88a48c: r2 = 16
    //     0x88a48c: movz            x2, #0x10
    // 0x88a490: stur            x0, [fp, #-0x40]
    // 0x88a494: r0 = AllocateArray()
    //     0x88a494: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88a498: mov             x2, x0
    // 0x88a49c: ldur            x0, [fp, #-0x10]
    // 0x88a4a0: stur            x2, [fp, #-0x48]
    // 0x88a4a4: StoreField: r2->field_f = r0
    //     0x88a4a4: stur            w0, [x2, #0xf]
    // 0x88a4a8: ldur            x0, [fp, #-0x18]
    // 0x88a4ac: StoreField: r2->field_13 = r0
    //     0x88a4ac: stur            w0, [x2, #0x13]
    // 0x88a4b0: ldur            x0, [fp, #-0x20]
    // 0x88a4b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x88a4b4: stur            w0, [x2, #0x17]
    // 0x88a4b8: ldur            x0, [fp, #-0x28]
    // 0x88a4bc: StoreField: r2->field_1b = r0
    //     0x88a4bc: stur            w0, [x2, #0x1b]
    // 0x88a4c0: ldur            x0, [fp, #-0x30]
    // 0x88a4c4: StoreField: r2->field_1f = r0
    //     0x88a4c4: stur            w0, [x2, #0x1f]
    // 0x88a4c8: ldur            x0, [fp, #-0x38]
    // 0x88a4cc: StoreField: r2->field_23 = r0
    //     0x88a4cc: stur            w0, [x2, #0x23]
    // 0x88a4d0: ldur            x0, [fp, #-8]
    // 0x88a4d4: StoreField: r2->field_27 = r0
    //     0x88a4d4: stur            w0, [x2, #0x27]
    // 0x88a4d8: ldur            x0, [fp, #-0x40]
    // 0x88a4dc: StoreField: r2->field_2b = r0
    //     0x88a4dc: stur            w0, [x2, #0x2b]
    // 0x88a4e0: r1 = <Parser<XmlEvent>>
    //     0x88a4e0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26658] TypeArguments: <Parser<XmlEvent>>
    //     0x88a4e4: ldr             x1, [x1, #0x658]
    // 0x88a4e8: r0 = AllocateGrowableArray()
    //     0x88a4e8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88a4ec: mov             x1, x0
    // 0x88a4f0: ldur            x0, [fp, #-0x48]
    // 0x88a4f4: StoreField: r1->field_f = r0
    //     0x88a4f4: stur            w0, [x1, #0xf]
    // 0x88a4f8: r0 = 16
    //     0x88a4f8: movz            x0, #0x10
    // 0x88a4fc: StoreField: r1->field_b = r0
    //     0x88a4fc: stur            w0, [x1, #0xb]
    // 0x88a500: r16 = <XmlEvent>
    //     0x88a500: add             x16, PP, #0x26, lsl #12  ; [pp+0x26590] TypeArguments: <XmlEvent>
    //     0x88a504: ldr             x16, [x16, #0x590]
    // 0x88a508: stp             x1, x16, [SP, #8]
    // 0x88a50c: r16 = Closure: (Failure, Failure) => Failure from Function 'selectFarthest': static.
    //     0x88a50c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26660] Closure: (Failure, Failure) => Failure from Function 'selectFarthest': static. (0x7e54fb28a7b4)
    //     0x88a510: ldr             x16, [x16, #0x660]
    // 0x88a514: str             x16, [SP]
    // 0x88a518: r4 = const [0x1, 0x2, 0x2, 0x1, failureJoiner, 0x1, null]
    //     0x88a518: add             x4, PP, #0x26, lsl #12  ; [pp+0x26668] List(7) [0x1, 0x2, 0x2, 0x1, "failureJoiner", 0x1, Null]
    //     0x88a51c: ldr             x4, [x4, #0x668]
    // 0x88a520: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88a520: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88a524: LeaveFrame
    //     0x88a524: mov             SP, fp
    //     0x88a528: ldp             fp, lr, [SP], #0x10
    // 0x88a52c: ret
    //     0x88a52c: ret             
    // 0x88a530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88a530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88a534: b               #0x88a34c
  }
  [closure] Parser<XmlDoctypeEvent> doctype(dynamic) {
    // ** addr: 0x88a854, size: 0x38
    // 0x88a854: EnterFrame
    //     0x88a854: stp             fp, lr, [SP, #-0x10]!
    //     0x88a858: mov             fp, SP
    // 0x88a85c: ldr             x0, [fp, #0x10]
    // 0x88a860: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88a860: ldur            w1, [x0, #0x17]
    // 0x88a864: DecompressPointer r1
    //     0x88a864: add             x1, x1, HEAP, lsl #32
    // 0x88a868: CheckStackOverflow
    //     0x88a868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88a86c: cmp             SP, x16
    //     0x88a870: b.ls            #0x88a884
    // 0x88a874: r0 = doctype()
    //     0x88a874: bl              #0x88a88c  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctype
    // 0x88a878: LeaveFrame
    //     0x88a878: mov             SP, fp
    //     0x88a87c: ldp             fp, lr, [SP], #0x10
    // 0x88a880: ret
    //     0x88a880: ret             
    // 0x88a884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88a884: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88a888: b               #0x88a874
  }
  _ doctype(/* No info */) {
    // ** addr: 0x88a88c, size: 0x1f4
    // 0x88a88c: EnterFrame
    //     0x88a88c: stp             fp, lr, [SP, #-0x10]!
    //     0x88a890: mov             fp, SP
    // 0x88a894: AllocStack(0x80)
    //     0x88a894: sub             SP, SP, #0x80
    // 0x88a898: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88a898: mov             x2, x1
    //     0x88a89c: stur            x1, [fp, #-8]
    // 0x88a8a0: CheckStackOverflow
    //     0x88a8a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88a8a4: cmp             SP, x16
    //     0x88a8a8: b.ls            #0x88aa78
    // 0x88a8ac: r1 = "<!DOCTYPE"
    //     0x88a8ac: add             x1, PP, #0x26, lsl #12  ; [pp+0x26670] "<!DOCTYPE"
    //     0x88a8b0: ldr             x1, [x1, #0x670]
    // 0x88a8b4: r0 = PredicateStringExtension.toParser()
    //     0x88a8b4: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88a8b8: ldur            x2, [fp, #-8]
    // 0x88a8bc: r1 = Function 'space':.
    //     0x88a8bc: add             x1, PP, #0x26, lsl #12  ; [pp+0x26678] AnonymousClosure: (0x88e0c8), in [package:xml/src/xml_events/parser.dart] XmlEventParser::space (0x88e100)
    //     0x88a8c0: ldr             x1, [x1, #0x678]
    // 0x88a8c4: stur            x0, [fp, #-0x10]
    // 0x88a8c8: r0 = AllocateClosure()
    //     0x88a8c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a8cc: stur            x0, [fp, #-0x18]
    // 0x88a8d0: r16 = <String>
    //     0x88a8d0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88a8d4: stp             x0, x16, [SP]
    // 0x88a8d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a8d8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a8dc: r0 = ref0()
    //     0x88a8dc: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a8e0: ldur            x2, [fp, #-8]
    // 0x88a8e4: r1 = Function 'nameToken':.
    //     0x88a8e4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88a8e8: ldr             x1, [x1, #0x680]
    // 0x88a8ec: stur            x0, [fp, #-0x20]
    // 0x88a8f0: r0 = AllocateClosure()
    //     0x88a8f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a8f4: r16 = <String>
    //     0x88a8f4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88a8f8: stp             x0, x16, [SP]
    // 0x88a8fc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a8fc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a900: r0 = ref0()
    //     0x88a900: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a904: ldur            x2, [fp, #-8]
    // 0x88a908: r1 = Function 'doctypeExternalId':.
    //     0x88a908: add             x1, PP, #0x26, lsl #12  ; [pp+0x26688] AnonymousClosure: (0x88c788), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalId (0x88c7c0)
    //     0x88a90c: ldr             x1, [x1, #0x688]
    // 0x88a910: stur            x0, [fp, #-0x28]
    // 0x88a914: r0 = AllocateClosure()
    //     0x88a914: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a918: r16 = <DtdExternalId>
    //     0x88a918: add             x16, PP, #0x26, lsl #12  ; [pp+0x26690] TypeArguments: <DtdExternalId>
    //     0x88a91c: ldr             x16, [x16, #0x690]
    // 0x88a920: stp             x0, x16, [SP]
    // 0x88a924: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a924: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a928: r0 = ref0()
    //     0x88a928: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a92c: stur            x0, [fp, #-0x30]
    // 0x88a930: r16 = <void?>
    //     0x88a930: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x88a934: ldur            lr, [fp, #-0x18]
    // 0x88a938: stp             lr, x16, [SP]
    // 0x88a93c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a93c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a940: r0 = ref0()
    //     0x88a940: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a944: r16 = <DtdExternalId>
    //     0x88a944: add             x16, PP, #0x26, lsl #12  ; [pp+0x26690] TypeArguments: <DtdExternalId>
    //     0x88a948: ldr             x16, [x16, #0x690]
    // 0x88a94c: ldur            lr, [fp, #-0x30]
    // 0x88a950: stp             lr, x16, [SP, #8]
    // 0x88a954: str             x0, [SP]
    // 0x88a958: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88a958: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88a95c: r0 = SkipParserExtension.skip()
    //     0x88a95c: bl              #0x88adac  ; [package:petitparser/src/parser/combinator/skip.dart] ::SkipParserExtension.skip
    // 0x88a960: r16 = <DtdExternalId>
    //     0x88a960: add             x16, PP, #0x26, lsl #12  ; [pp+0x26690] TypeArguments: <DtdExternalId>
    //     0x88a964: ldr             x16, [x16, #0x690]
    // 0x88a968: stp             x0, x16, [SP]
    // 0x88a96c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a96c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a970: r0 = OptionalParserExtension.optional()
    //     0x88a970: bl              #0x88ad30  ; [package:petitparser/src/parser/combinator/optional.dart] ::OptionalParserExtension.optional
    // 0x88a974: ldur            x2, [fp, #-8]
    // 0x88a978: r1 = Function 'spaceOptional':.
    //     0x88a978: add             x1, PP, #0x26, lsl #12  ; [pp+0x26698] AnonymousClosure: (0x88c650), in [package:xml/src/xml_events/parser.dart] XmlEventParser::spaceOptional (0x88c688)
    //     0x88a97c: ldr             x1, [x1, #0x698]
    // 0x88a980: stur            x0, [fp, #-0x18]
    // 0x88a984: r0 = AllocateClosure()
    //     0x88a984: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a988: stur            x0, [fp, #-0x30]
    // 0x88a98c: r16 = <String>
    //     0x88a98c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88a990: stp             x0, x16, [SP]
    // 0x88a994: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a994: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a998: r0 = ref0()
    //     0x88a998: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a99c: ldur            x2, [fp, #-8]
    // 0x88a9a0: r1 = Function 'doctypeIntSubset':.
    //     0x88a9a0: add             x1, PP, #0x26, lsl #12  ; [pp+0x266a0] AnonymousClosure: (0x88b500), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeIntSubset (0x88b538)
    //     0x88a9a4: ldr             x1, [x1, #0x6a0]
    // 0x88a9a8: stur            x0, [fp, #-8]
    // 0x88a9ac: r0 = AllocateClosure()
    //     0x88a9ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x88a9b0: r16 = <String>
    //     0x88a9b0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88a9b4: stp             x0, x16, [SP]
    // 0x88a9b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a9b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a9bc: r0 = ref0()
    //     0x88a9bc: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a9c0: r16 = <String>
    //     0x88a9c0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88a9c4: stp             x0, x16, [SP]
    // 0x88a9c8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a9c8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a9cc: r0 = OptionalParserExtension.optional()
    //     0x88a9cc: bl              #0x88ad30  ; [package:petitparser/src/parser/combinator/optional.dart] ::OptionalParserExtension.optional
    // 0x88a9d0: stur            x0, [fp, #-0x38]
    // 0x88a9d4: r16 = <String>
    //     0x88a9d4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88a9d8: ldur            lr, [fp, #-0x30]
    // 0x88a9dc: stp             lr, x16, [SP]
    // 0x88a9e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88a9e0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88a9e4: r0 = ref0()
    //     0x88a9e4: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88a9e8: r1 = ">"
    //     0x88a9e8: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88a9ec: stur            x0, [fp, #-0x30]
    // 0x88a9f0: r0 = PredicateStringExtension.toParser()
    //     0x88a9f0: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88a9f4: r16 = <String, String, String, DtdExternalId?, String, String?, String, String>
    //     0x88a9f4: add             x16, PP, #0x26, lsl #12  ; [pp+0x266a8] TypeArguments: <String, String, String, DtdExternalId?, String, String?, String, String>
    //     0x88a9f8: ldr             x16, [x16, #0x6a8]
    // 0x88a9fc: ldur            lr, [fp, #-0x10]
    // 0x88aa00: stp             lr, x16, [SP, #0x38]
    // 0x88aa04: ldur            x16, [fp, #-0x20]
    // 0x88aa08: ldur            lr, [fp, #-0x28]
    // 0x88aa0c: stp             lr, x16, [SP, #0x28]
    // 0x88aa10: ldur            x16, [fp, #-0x18]
    // 0x88aa14: ldur            lr, [fp, #-8]
    // 0x88aa18: stp             lr, x16, [SP, #0x18]
    // 0x88aa1c: ldur            x16, [fp, #-0x38]
    // 0x88aa20: ldur            lr, [fp, #-0x30]
    // 0x88aa24: stp             lr, x16, [SP, #8]
    // 0x88aa28: str             x0, [SP]
    // 0x88aa2c: r4 = const [0x8, 0x8, 0x8, 0x8, null]
    //     0x88aa2c: add             x4, PP, #0x26, lsl #12  ; [pp+0x266b0] List(5) [0x8, 0x8, 0x8, 0x8, Null]
    //     0x88aa30: ldr             x4, [x4, #0x6b0]
    // 0x88aa34: r0 = seq8()
    //     0x88aa34: bl              #0x88ac74  ; [package:petitparser/src/parser/combinator/generated/sequence_8.dart] ::seq8
    // 0x88aa38: r1 = Function '<anonymous closure>':.
    //     0x88aa38: add             x1, PP, #0x26, lsl #12  ; [pp+0x266b8] AnonymousClosure: (0x88b4c4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctype (0x88a88c)
    //     0x88aa3c: ldr             x1, [x1, #0x6b8]
    // 0x88aa40: r2 = Null
    //     0x88aa40: mov             x2, NULL
    // 0x88aa44: stur            x0, [fp, #-8]
    // 0x88aa48: r0 = AllocateClosure()
    //     0x88aa48: bl              #0xec1630  ; AllocateClosureStub
    // 0x88aa4c: r16 = <String, String, String, DtdExternalId?, String, String?, String, String, XmlDoctypeEvent>
    //     0x88aa4c: add             x16, PP, #0x26, lsl #12  ; [pp+0x266c0] TypeArguments: <String, String, String, DtdExternalId?, String, String?, String, String, XmlDoctypeEvent>
    //     0x88aa50: ldr             x16, [x16, #0x6c0]
    // 0x88aa54: ldur            lr, [fp, #-8]
    // 0x88aa58: stp             lr, x16, [SP, #8]
    // 0x88aa5c: str             x0, [SP]
    // 0x88aa60: r4 = const [0x9, 0x2, 0x2, 0x2, null]
    //     0x88aa60: add             x4, PP, #0x26, lsl #12  ; [pp+0x266c8] List(5) [0x9, 0x2, 0x2, 0x2, Null]
    //     0x88aa64: ldr             x4, [x4, #0x6c8]
    // 0x88aa68: r0 = RecordParserExtension8.map8()
    //     0x88aa68: bl              #0x88aa80  ; [package:petitparser/src/parser/combinator/generated/sequence_8.dart] ::RecordParserExtension8.map8
    // 0x88aa6c: LeaveFrame
    //     0x88aa6c: mov             SP, fp
    //     0x88aa70: ldp             fp, lr, [SP], #0x10
    // 0x88aa74: ret
    //     0x88aa74: ret             
    // 0x88aa78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88aa78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88aa7c: b               #0x88a8ac
  }
  [closure] XmlDoctypeEvent <anonymous closure>(dynamic, String, String, String, DtdExternalId?, String, String?, String, String) {
    // ** addr: 0x88b4c4, size: 0x30
    // 0x88b4c4: EnterFrame
    //     0x88b4c4: stp             fp, lr, [SP, #-0x10]!
    //     0x88b4c8: mov             fp, SP
    // 0x88b4cc: r0 = XmlDoctypeEvent()
    //     0x88b4cc: bl              #0x88b4f4  ; AllocateXmlDoctypeEventStub -> XmlDoctypeEvent (size=0x20)
    // 0x88b4d0: ldr             x1, [fp, #0x38]
    // 0x88b4d4: StoreField: r0->field_13 = r1
    //     0x88b4d4: stur            w1, [x0, #0x13]
    // 0x88b4d8: ldr             x1, [fp, #0x30]
    // 0x88b4dc: ArrayStore: r0[0] = r1  ; List_4
    //     0x88b4dc: stur            w1, [x0, #0x17]
    // 0x88b4e0: ldr             x1, [fp, #0x20]
    // 0x88b4e4: StoreField: r0->field_1b = r1
    //     0x88b4e4: stur            w1, [x0, #0x1b]
    // 0x88b4e8: LeaveFrame
    //     0x88b4e8: mov             SP, fp
    //     0x88b4ec: ldp             fp, lr, [SP], #0x10
    // 0x88b4f0: ret
    //     0x88b4f0: ret             
  }
  [closure] Parser<String> doctypeIntSubset(dynamic) {
    // ** addr: 0x88b500, size: 0x38
    // 0x88b500: EnterFrame
    //     0x88b500: stp             fp, lr, [SP, #-0x10]!
    //     0x88b504: mov             fp, SP
    // 0x88b508: ldr             x0, [fp, #0x10]
    // 0x88b50c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88b50c: ldur            w1, [x0, #0x17]
    // 0x88b510: DecompressPointer r1
    //     0x88b510: add             x1, x1, HEAP, lsl #32
    // 0x88b514: CheckStackOverflow
    //     0x88b514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b518: cmp             SP, x16
    //     0x88b51c: b.ls            #0x88b530
    // 0x88b520: r0 = doctypeIntSubset()
    //     0x88b520: bl              #0x88b538  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeIntSubset
    // 0x88b524: LeaveFrame
    //     0x88b524: mov             SP, fp
    //     0x88b528: ldp             fp, lr, [SP], #0x10
    // 0x88b52c: ret
    //     0x88b52c: ret             
    // 0x88b530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b530: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b534: b               #0x88b520
  }
  _ doctypeIntSubset(/* No info */) {
    // ** addr: 0x88b538, size: 0x258
    // 0x88b538: EnterFrame
    //     0x88b538: stp             fp, lr, [SP, #-0x10]!
    //     0x88b53c: mov             fp, SP
    // 0x88b540: AllocStack(0x70)
    //     0x88b540: sub             SP, SP, #0x70
    // 0x88b544: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88b544: mov             x2, x1
    //     0x88b548: stur            x1, [fp, #-8]
    // 0x88b54c: CheckStackOverflow
    //     0x88b54c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88b550: cmp             SP, x16
    //     0x88b554: b.ls            #0x88b788
    // 0x88b558: r1 = "["
    //     0x88b558: ldr             x1, [PP, #0xec8]  ; [pp+0xec8] "["
    // 0x88b55c: r0 = PredicateStringExtension.toParser()
    //     0x88b55c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88b560: ldur            x2, [fp, #-8]
    // 0x88b564: r1 = Function 'doctypeElementDecl':.
    //     0x88b564: add             x1, PP, #0x26, lsl #12  ; [pp+0x266d0] AnonymousClosure: (0x88c4d0), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeElementDecl (0x88c508)
    //     0x88b568: ldr             x1, [x1, #0x6d0]
    // 0x88b56c: stur            x0, [fp, #-0x10]
    // 0x88b570: r0 = AllocateClosure()
    //     0x88b570: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b574: stp             x0, NULL, [SP]
    // 0x88b578: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b578: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b57c: r0 = ref0()
    //     0x88b57c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88b580: ldur            x2, [fp, #-8]
    // 0x88b584: r1 = Function 'doctypeAttlistDecl':.
    //     0x88b584: add             x1, PP, #0x26, lsl #12  ; [pp+0x266d8] AnonymousClosure: (0x88c350), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeAttlistDecl (0x88c388)
    //     0x88b588: ldr             x1, [x1, #0x6d8]
    // 0x88b58c: stur            x0, [fp, #-0x18]
    // 0x88b590: r0 = AllocateClosure()
    //     0x88b590: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b594: stp             x0, NULL, [SP]
    // 0x88b598: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b598: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b59c: r0 = ref0()
    //     0x88b59c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88b5a0: ldur            x2, [fp, #-8]
    // 0x88b5a4: r1 = Function 'doctypeEntityDecl':.
    //     0x88b5a4: add             x1, PP, #0x26, lsl #12  ; [pp+0x266e0] AnonymousClosure: (0x88c1d0), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeEntityDecl (0x88c208)
    //     0x88b5a8: ldr             x1, [x1, #0x6e0]
    // 0x88b5ac: stur            x0, [fp, #-0x20]
    // 0x88b5b0: r0 = AllocateClosure()
    //     0x88b5b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b5b4: stp             x0, NULL, [SP]
    // 0x88b5b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b5b8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b5bc: r0 = ref0()
    //     0x88b5bc: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88b5c0: ldur            x2, [fp, #-8]
    // 0x88b5c4: r1 = Function 'doctypeNotationDecl':.
    //     0x88b5c4: add             x1, PP, #0x26, lsl #12  ; [pp+0x266e8] AnonymousClosure: (0x88bba0), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeNotationDecl (0x88bbd8)
    //     0x88b5c8: ldr             x1, [x1, #0x6e8]
    // 0x88b5cc: stur            x0, [fp, #-0x28]
    // 0x88b5d0: r0 = AllocateClosure()
    //     0x88b5d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b5d4: stp             x0, NULL, [SP]
    // 0x88b5d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b5d8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b5dc: r0 = ref0()
    //     0x88b5dc: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88b5e0: ldur            x2, [fp, #-8]
    // 0x88b5e4: r1 = Function 'processing':.
    //     0x88b5e4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26640] AnonymousClosure: (0x88e168), in [package:xml/src/xml_events/parser.dart] XmlEventParser::processing (0x88e1a0)
    //     0x88b5e8: ldr             x1, [x1, #0x640]
    // 0x88b5ec: stur            x0, [fp, #-0x30]
    // 0x88b5f0: r0 = AllocateClosure()
    //     0x88b5f0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b5f4: r16 = <XmlProcessingEvent>
    //     0x88b5f4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26648] TypeArguments: <XmlProcessingEvent>
    //     0x88b5f8: ldr             x16, [x16, #0x648]
    // 0x88b5fc: stp             x0, x16, [SP]
    // 0x88b600: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b600: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b604: r0 = ref0()
    //     0x88b604: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88b608: ldur            x2, [fp, #-8]
    // 0x88b60c: r1 = Function 'comment':.
    //     0x88b60c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26618] AnonymousClosure: (0x88f138), in [package:xml/src/xml_events/parser.dart] XmlEventParser::comment (0x88f170)
    //     0x88b610: ldr             x1, [x1, #0x618]
    // 0x88b614: stur            x0, [fp, #-0x38]
    // 0x88b618: r0 = AllocateClosure()
    //     0x88b618: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b61c: r16 = <XmlCommentEvent>
    //     0x88b61c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26620] TypeArguments: <XmlCommentEvent>
    //     0x88b620: ldr             x16, [x16, #0x620]
    // 0x88b624: stp             x0, x16, [SP]
    // 0x88b628: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b628: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b62c: r0 = ref0()
    //     0x88b62c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88b630: ldur            x2, [fp, #-8]
    // 0x88b634: r1 = Function 'doctypeReference':.
    //     0x88b634: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f0] AnonymousClosure: (0x88bad4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeReference (0x88bb0c)
    //     0x88b638: ldr             x1, [x1, #0x6f0]
    // 0x88b63c: stur            x0, [fp, #-8]
    // 0x88b640: r0 = AllocateClosure()
    //     0x88b640: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b644: stp             x0, NULL, [SP]
    // 0x88b648: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b648: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b64c: r0 = ref0()
    //     0x88b64c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88b650: stur            x0, [fp, #-0x40]
    // 0x88b654: r0 = any()
    //     0x88b654: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88b658: r1 = Null
    //     0x88b658: mov             x1, NULL
    // 0x88b65c: r2 = 16
    //     0x88b65c: movz            x2, #0x10
    // 0x88b660: stur            x0, [fp, #-0x48]
    // 0x88b664: r0 = AllocateArray()
    //     0x88b664: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88b668: mov             x2, x0
    // 0x88b66c: ldur            x0, [fp, #-0x18]
    // 0x88b670: stur            x2, [fp, #-0x50]
    // 0x88b674: StoreField: r2->field_f = r0
    //     0x88b674: stur            w0, [x2, #0xf]
    // 0x88b678: ldur            x0, [fp, #-0x20]
    // 0x88b67c: StoreField: r2->field_13 = r0
    //     0x88b67c: stur            w0, [x2, #0x13]
    // 0x88b680: ldur            x0, [fp, #-0x28]
    // 0x88b684: ArrayStore: r2[0] = r0  ; List_4
    //     0x88b684: stur            w0, [x2, #0x17]
    // 0x88b688: ldur            x0, [fp, #-0x30]
    // 0x88b68c: StoreField: r2->field_1b = r0
    //     0x88b68c: stur            w0, [x2, #0x1b]
    // 0x88b690: ldur            x0, [fp, #-0x38]
    // 0x88b694: StoreField: r2->field_1f = r0
    //     0x88b694: stur            w0, [x2, #0x1f]
    // 0x88b698: ldur            x0, [fp, #-8]
    // 0x88b69c: StoreField: r2->field_23 = r0
    //     0x88b69c: stur            w0, [x2, #0x23]
    // 0x88b6a0: ldur            x0, [fp, #-0x40]
    // 0x88b6a4: StoreField: r2->field_27 = r0
    //     0x88b6a4: stur            w0, [x2, #0x27]
    // 0x88b6a8: ldur            x0, [fp, #-0x48]
    // 0x88b6ac: StoreField: r2->field_2b = r0
    //     0x88b6ac: stur            w0, [x2, #0x2b]
    // 0x88b6b0: r1 = <Parser>
    //     0x88b6b0: add             x1, PP, #0x26, lsl #12  ; [pp+0x266f8] TypeArguments: <Parser>
    //     0x88b6b4: ldr             x1, [x1, #0x6f8]
    // 0x88b6b8: r0 = AllocateGrowableArray()
    //     0x88b6b8: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88b6bc: mov             x1, x0
    // 0x88b6c0: ldur            x0, [fp, #-0x50]
    // 0x88b6c4: StoreField: r1->field_f = r0
    //     0x88b6c4: stur            w0, [x1, #0xf]
    // 0x88b6c8: r0 = 16
    //     0x88b6c8: movz            x0, #0x10
    // 0x88b6cc: StoreField: r1->field_b = r0
    //     0x88b6cc: stur            w0, [x1, #0xb]
    // 0x88b6d0: stp             x1, NULL, [SP]
    // 0x88b6d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88b6d4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88b6d8: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88b6d8: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88b6dc: r1 = "]"
    //     0x88b6dc: ldr             x1, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0x88b6e0: stur            x0, [fp, #-8]
    // 0x88b6e4: r0 = PredicateStringExtension.toParser()
    //     0x88b6e4: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88b6e8: ldur            x16, [fp, #-8]
    // 0x88b6ec: stp             x16, NULL, [SP, #8]
    // 0x88b6f0: str             x0, [SP]
    // 0x88b6f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88b6f4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88b6f8: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88b6f8: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88b6fc: r16 = <List>
    //     0x88b6fc: ldr             x16, [PP, #0x4170]  ; [pp+0x4170] TypeArguments: <List>
    // 0x88b700: stp             x0, x16, [SP, #8]
    // 0x88b704: r16 = "\"]\" expected"
    //     0x88b704: add             x16, PP, #0x26, lsl #12  ; [pp+0x26700] "\"]\" expected"
    //     0x88b708: ldr             x16, [x16, #0x700]
    // 0x88b70c: str             x16, [SP]
    // 0x88b710: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88b710: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88b714: r0 = FlattenParserExtension.flatten()
    //     0x88b714: bl              #0x88b93c  ; [package:petitparser/src/parser/action/flatten.dart] ::FlattenParserExtension.flatten
    // 0x88b718: r1 = "]"
    //     0x88b718: ldr             x1, [PP, #0xec0]  ; [pp+0xec0] "]"
    // 0x88b71c: stur            x0, [fp, #-8]
    // 0x88b720: r0 = PredicateStringExtension.toParser()
    //     0x88b720: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88b724: r16 = <String, String, String>
    //     0x88b724: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88b728: ldr             x16, [x16, #0x810]
    // 0x88b72c: ldur            lr, [fp, #-0x10]
    // 0x88b730: stp             lr, x16, [SP, #0x10]
    // 0x88b734: ldur            x16, [fp, #-8]
    // 0x88b738: stp             x0, x16, [SP]
    // 0x88b73c: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88b73c: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88b740: ldr             x4, [x4, #0xe10]
    // 0x88b744: r0 = seq3()
    //     0x88b744: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88b748: r1 = Function '<anonymous closure>':.
    //     0x88b748: add             x1, PP, #0x26, lsl #12  ; [pp+0x26708] AnonymousClosure: (0x88bacc), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeIntSubset (0x88b538)
    //     0x88b74c: ldr             x1, [x1, #0x708]
    // 0x88b750: r2 = Null
    //     0x88b750: mov             x2, NULL
    // 0x88b754: stur            x0, [fp, #-8]
    // 0x88b758: r0 = AllocateClosure()
    //     0x88b758: bl              #0xec1630  ; AllocateClosureStub
    // 0x88b75c: r16 = <String, String, String, String>
    //     0x88b75c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26710] TypeArguments: <String, String, String, String>
    //     0x88b760: ldr             x16, [x16, #0x710]
    // 0x88b764: ldur            lr, [fp, #-8]
    // 0x88b768: stp             lr, x16, [SP, #8]
    // 0x88b76c: str             x0, [SP]
    // 0x88b770: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88b770: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88b774: ldr             x4, [x4, #0x718]
    // 0x88b778: r0 = RecordParserExtension3.map3()
    //     0x88b778: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88b77c: LeaveFrame
    //     0x88b77c: mov             SP, fp
    //     0x88b780: ldp             fp, lr, [SP], #0x10
    // 0x88b784: ret
    //     0x88b784: ret             
    // 0x88b788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88b788: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88b78c: b               #0x88b558
  }
  [closure] String <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x88bacc, size: 0x8
    // 0x88bacc: ldr             x0, [SP, #8]
    // 0x88bad0: ret
    //     0x88bad0: ret             
  }
  [closure] Parser<dynamic> doctypeReference(dynamic) {
    // ** addr: 0x88bad4, size: 0x38
    // 0x88bad4: EnterFrame
    //     0x88bad4: stp             fp, lr, [SP, #-0x10]!
    //     0x88bad8: mov             fp, SP
    // 0x88badc: ldr             x0, [fp, #0x10]
    // 0x88bae0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88bae0: ldur            w1, [x0, #0x17]
    // 0x88bae4: DecompressPointer r1
    //     0x88bae4: add             x1, x1, HEAP, lsl #32
    // 0x88bae8: CheckStackOverflow
    //     0x88bae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88baec: cmp             SP, x16
    //     0x88baf0: b.ls            #0x88bb04
    // 0x88baf4: r0 = doctypeReference()
    //     0x88baf4: bl              #0x88bb0c  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeReference
    // 0x88baf8: LeaveFrame
    //     0x88baf8: mov             SP, fp
    //     0x88bafc: ldp             fp, lr, [SP], #0x10
    // 0x88bb00: ret
    //     0x88bb00: ret             
    // 0x88bb04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bb04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bb08: b               #0x88baf4
  }
  _ doctypeReference(/* No info */) {
    // ** addr: 0x88bb0c, size: 0x94
    // 0x88bb0c: EnterFrame
    //     0x88bb0c: stp             fp, lr, [SP, #-0x10]!
    //     0x88bb10: mov             fp, SP
    // 0x88bb14: AllocStack(0x30)
    //     0x88bb14: sub             SP, SP, #0x30
    // 0x88bb18: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88bb18: mov             x2, x1
    //     0x88bb1c: stur            x1, [fp, #-8]
    // 0x88bb20: CheckStackOverflow
    //     0x88bb20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bb24: cmp             SP, x16
    //     0x88bb28: b.ls            #0x88bb98
    // 0x88bb2c: r1 = "%"
    //     0x88bb2c: ldr             x1, [PP, #0xcb0]  ; [pp+0xcb0] "%"
    // 0x88bb30: r0 = PredicateStringExtension.toParser()
    //     0x88bb30: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88bb34: ldur            x2, [fp, #-8]
    // 0x88bb38: r1 = Function 'nameToken':.
    //     0x88bb38: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88bb3c: ldr             x1, [x1, #0x680]
    // 0x88bb40: stur            x0, [fp, #-8]
    // 0x88bb44: r0 = AllocateClosure()
    //     0x88bb44: bl              #0xec1630  ; AllocateClosureStub
    // 0x88bb48: r16 = <String>
    //     0x88bb48: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88bb4c: stp             x0, x16, [SP]
    // 0x88bb50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88bb50: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88bb54: r0 = ref0()
    //     0x88bb54: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88bb58: r1 = ";"
    //     0x88bb58: add             x1, PP, #0x10, lsl #12  ; [pp+0x107f8] ";"
    //     0x88bb5c: ldr             x1, [x1, #0x7f8]
    // 0x88bb60: stur            x0, [fp, #-0x10]
    // 0x88bb64: r0 = PredicateStringExtension.toParser()
    //     0x88bb64: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88bb68: r16 = <String, String, String>
    //     0x88bb68: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88bb6c: ldr             x16, [x16, #0x810]
    // 0x88bb70: ldur            lr, [fp, #-8]
    // 0x88bb74: stp             lr, x16, [SP, #0x10]
    // 0x88bb78: ldur            x16, [fp, #-0x10]
    // 0x88bb7c: stp             x0, x16, [SP]
    // 0x88bb80: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88bb80: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88bb84: ldr             x4, [x4, #0xe10]
    // 0x88bb88: r0 = seq3()
    //     0x88bb88: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88bb8c: LeaveFrame
    //     0x88bb8c: mov             SP, fp
    //     0x88bb90: ldp             fp, lr, [SP], #0x10
    // 0x88bb94: ret
    //     0x88bb94: ret             
    // 0x88bb98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bb98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bb9c: b               #0x88bb2c
  }
  [closure] Parser<dynamic> doctypeNotationDecl(dynamic) {
    // ** addr: 0x88bba0, size: 0x38
    // 0x88bba0: EnterFrame
    //     0x88bba0: stp             fp, lr, [SP, #-0x10]!
    //     0x88bba4: mov             fp, SP
    // 0x88bba8: ldr             x0, [fp, #0x10]
    // 0x88bbac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88bbac: ldur            w1, [x0, #0x17]
    // 0x88bbb0: DecompressPointer r1
    //     0x88bbb0: add             x1, x1, HEAP, lsl #32
    // 0x88bbb4: CheckStackOverflow
    //     0x88bbb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bbb8: cmp             SP, x16
    //     0x88bbbc: b.ls            #0x88bbd0
    // 0x88bbc0: r0 = doctypeNotationDecl()
    //     0x88bbc0: bl              #0x88bbd8  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeNotationDecl
    // 0x88bbc4: LeaveFrame
    //     0x88bbc4: mov             SP, fp
    //     0x88bbc8: ldp             fp, lr, [SP], #0x10
    // 0x88bbcc: ret
    //     0x88bbcc: ret             
    // 0x88bbd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bbd0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bbd4: b               #0x88bbc0
  }
  _ doctypeNotationDecl(/* No info */) {
    // ** addr: 0x88bbd8, size: 0x148
    // 0x88bbd8: EnterFrame
    //     0x88bbd8: stp             fp, lr, [SP, #-0x10]!
    //     0x88bbdc: mov             fp, SP
    // 0x88bbe0: AllocStack(0x48)
    //     0x88bbe0: sub             SP, SP, #0x48
    // 0x88bbe4: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88bbe4: mov             x2, x1
    //     0x88bbe8: stur            x1, [fp, #-8]
    // 0x88bbec: CheckStackOverflow
    //     0x88bbec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bbf0: cmp             SP, x16
    //     0x88bbf4: b.ls            #0x88bd18
    // 0x88bbf8: r1 = "<!NOTATION"
    //     0x88bbf8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26720] "<!NOTATION"
    //     0x88bbfc: ldr             x1, [x1, #0x720]
    // 0x88bc00: r0 = PredicateStringExtension.toParser()
    //     0x88bc00: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88bc04: ldur            x2, [fp, #-8]
    // 0x88bc08: r1 = Function 'nameToken':.
    //     0x88bc08: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88bc0c: ldr             x1, [x1, #0x680]
    // 0x88bc10: stur            x0, [fp, #-0x10]
    // 0x88bc14: r0 = AllocateClosure()
    //     0x88bc14: bl              #0xec1630  ; AllocateClosureStub
    // 0x88bc18: r16 = <String>
    //     0x88bc18: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88bc1c: stp             x0, x16, [SP]
    // 0x88bc20: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88bc20: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88bc24: r0 = ref0()
    //     0x88bc24: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88bc28: ldur            x2, [fp, #-8]
    // 0x88bc2c: r1 = Function 'attributeValue':.
    //     0x88bc2c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26728] AnonymousClosure: (0x88bd90), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue (0x88bdc8)
    //     0x88bc30: ldr             x1, [x1, #0x728]
    // 0x88bc34: stur            x0, [fp, #-8]
    // 0x88bc38: r0 = AllocateClosure()
    //     0x88bc38: bl              #0xec1630  ; AllocateClosureStub
    // 0x88bc3c: r16 = <(String, XmlAttributeType)>
    //     0x88bc3c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88bc40: ldr             x16, [x16, #0x730]
    // 0x88bc44: stp             x0, x16, [SP]
    // 0x88bc48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88bc48: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88bc4c: r0 = ref0()
    //     0x88bc4c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88bc50: stur            x0, [fp, #-0x18]
    // 0x88bc54: r0 = any()
    //     0x88bc54: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88bc58: r1 = Null
    //     0x88bc58: mov             x1, NULL
    // 0x88bc5c: r2 = 6
    //     0x88bc5c: movz            x2, #0x6
    // 0x88bc60: stur            x0, [fp, #-0x20]
    // 0x88bc64: r0 = AllocateArray()
    //     0x88bc64: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88bc68: mov             x2, x0
    // 0x88bc6c: ldur            x0, [fp, #-8]
    // 0x88bc70: stur            x2, [fp, #-0x28]
    // 0x88bc74: StoreField: r2->field_f = r0
    //     0x88bc74: stur            w0, [x2, #0xf]
    // 0x88bc78: ldur            x0, [fp, #-0x18]
    // 0x88bc7c: StoreField: r2->field_13 = r0
    //     0x88bc7c: stur            w0, [x2, #0x13]
    // 0x88bc80: ldur            x0, [fp, #-0x20]
    // 0x88bc84: ArrayStore: r2[0] = r0  ; List_4
    //     0x88bc84: stur            w0, [x2, #0x17]
    // 0x88bc88: r1 = <Parser<Object>>
    //     0x88bc88: add             x1, PP, #0x26, lsl #12  ; [pp+0x26738] TypeArguments: <Parser<Object>>
    //     0x88bc8c: ldr             x1, [x1, #0x738]
    // 0x88bc90: r0 = AllocateGrowableArray()
    //     0x88bc90: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88bc94: mov             x1, x0
    // 0x88bc98: ldur            x0, [fp, #-0x28]
    // 0x88bc9c: StoreField: r1->field_f = r0
    //     0x88bc9c: stur            w0, [x1, #0xf]
    // 0x88bca0: r0 = 6
    //     0x88bca0: movz            x0, #0x6
    // 0x88bca4: StoreField: r1->field_b = r0
    //     0x88bca4: stur            w0, [x1, #0xb]
    // 0x88bca8: r16 = <Object>
    //     0x88bca8: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88bcac: stp             x1, x16, [SP]
    // 0x88bcb0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88bcb0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88bcb4: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88bcb4: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88bcb8: r1 = ">"
    //     0x88bcb8: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88bcbc: stur            x0, [fp, #-8]
    // 0x88bcc0: r0 = PredicateStringExtension.toParser()
    //     0x88bcc0: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88bcc4: r16 = <Object>
    //     0x88bcc4: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88bcc8: ldur            lr, [fp, #-8]
    // 0x88bccc: stp             lr, x16, [SP, #8]
    // 0x88bcd0: str             x0, [SP]
    // 0x88bcd4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88bcd4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88bcd8: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88bcd8: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88bcdc: r1 = ">"
    //     0x88bcdc: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88bce0: stur            x0, [fp, #-8]
    // 0x88bce4: r0 = PredicateStringExtension.toParser()
    //     0x88bce4: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88bce8: r16 = <String, List<Object>, String>
    //     0x88bce8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26740] TypeArguments: <String, List<Object>, String>
    //     0x88bcec: ldr             x16, [x16, #0x740]
    // 0x88bcf0: ldur            lr, [fp, #-0x10]
    // 0x88bcf4: stp             lr, x16, [SP, #0x10]
    // 0x88bcf8: ldur            x16, [fp, #-8]
    // 0x88bcfc: stp             x0, x16, [SP]
    // 0x88bd00: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88bd00: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88bd04: ldr             x4, [x4, #0xe10]
    // 0x88bd08: r0 = seq3()
    //     0x88bd08: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88bd0c: LeaveFrame
    //     0x88bd0c: mov             SP, fp
    //     0x88bd10: ldp             fp, lr, [SP], #0x10
    // 0x88bd14: ret
    //     0x88bd14: ret             
    // 0x88bd18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bd18: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bd1c: b               #0x88bbf8
  }
  [closure] Parser<(String, XmlAttributeType)> attributeValue(dynamic) {
    // ** addr: 0x88bd90, size: 0x38
    // 0x88bd90: EnterFrame
    //     0x88bd90: stp             fp, lr, [SP, #-0x10]!
    //     0x88bd94: mov             fp, SP
    // 0x88bd98: ldr             x0, [fp, #0x10]
    // 0x88bd9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88bd9c: ldur            w1, [x0, #0x17]
    // 0x88bda0: DecompressPointer r1
    //     0x88bda0: add             x1, x1, HEAP, lsl #32
    // 0x88bda4: CheckStackOverflow
    //     0x88bda4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bda8: cmp             SP, x16
    //     0x88bdac: b.ls            #0x88bdc0
    // 0x88bdb0: r0 = attributeValue()
    //     0x88bdb0: bl              #0x88bdc8  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue
    // 0x88bdb4: LeaveFrame
    //     0x88bdb4: mov             SP, fp
    //     0x88bdb8: ldp             fp, lr, [SP], #0x10
    // 0x88bdbc: ret
    //     0x88bdbc: ret             
    // 0x88bdc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bdc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bdc4: b               #0x88bdb0
  }
  _ attributeValue(/* No info */) {
    // ** addr: 0x88bdc8, size: 0x10c
    // 0x88bdc8: EnterFrame
    //     0x88bdc8: stp             fp, lr, [SP, #-0x10]!
    //     0x88bdcc: mov             fp, SP
    // 0x88bdd0: AllocStack(0x30)
    //     0x88bdd0: sub             SP, SP, #0x30
    // 0x88bdd4: SetupParameters(XmlEventParser this /* r1 => r0, fp-0x8 */)
    //     0x88bdd4: mov             x0, x1
    //     0x88bdd8: stur            x1, [fp, #-8]
    // 0x88bddc: CheckStackOverflow
    //     0x88bddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bde0: cmp             SP, x16
    //     0x88bde4: b.ls            #0x88becc
    // 0x88bde8: mov             x2, x0
    // 0x88bdec: r1 = Function 'attributeValueDoubleQuote':.
    //     0x88bdec: add             x1, PP, #0x26, lsl #12  ; [pp+0x26748] AnonymousClosure: (0x88c0c4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueDoubleQuote (0x88c0fc)
    //     0x88bdf0: ldr             x1, [x1, #0x748]
    // 0x88bdf4: r0 = AllocateClosure()
    //     0x88bdf4: bl              #0xec1630  ; AllocateClosureStub
    // 0x88bdf8: r16 = <(String, XmlAttributeType)>
    //     0x88bdf8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88bdfc: ldr             x16, [x16, #0x730]
    // 0x88be00: stp             x0, x16, [SP]
    // 0x88be04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88be04: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88be08: r0 = ref0()
    //     0x88be08: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88be0c: ldur            x2, [fp, #-8]
    // 0x88be10: r1 = Function 'attributeValueSingleQuote':.
    //     0x88be10: add             x1, PP, #0x26, lsl #12  ; [pp+0x26750] AnonymousClosure: (0x88bfac), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueSingleQuote (0x88bfe4)
    //     0x88be14: ldr             x1, [x1, #0x750]
    // 0x88be18: stur            x0, [fp, #-0x10]
    // 0x88be1c: r0 = AllocateClosure()
    //     0x88be1c: bl              #0xec1630  ; AllocateClosureStub
    // 0x88be20: r16 = <(String, XmlAttributeType)>
    //     0x88be20: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88be24: ldr             x16, [x16, #0x730]
    // 0x88be28: stp             x0, x16, [SP]
    // 0x88be2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88be2c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88be30: r0 = ref0()
    //     0x88be30: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88be34: ldur            x2, [fp, #-8]
    // 0x88be38: r1 = Function 'attributeValueNoQuote':.
    //     0x88be38: add             x1, PP, #0x26, lsl #12  ; [pp+0x26758] AnonymousClosure: (0x88bed4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueNoQuote (0x88bf0c)
    //     0x88be3c: ldr             x1, [x1, #0x758]
    // 0x88be40: stur            x0, [fp, #-8]
    // 0x88be44: r0 = AllocateClosure()
    //     0x88be44: bl              #0xec1630  ; AllocateClosureStub
    // 0x88be48: r16 = <(String, XmlAttributeType)>
    //     0x88be48: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88be4c: ldr             x16, [x16, #0x730]
    // 0x88be50: stp             x0, x16, [SP]
    // 0x88be54: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88be54: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88be58: r0 = ref0()
    //     0x88be58: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88be5c: r1 = Null
    //     0x88be5c: mov             x1, NULL
    // 0x88be60: r2 = 6
    //     0x88be60: movz            x2, #0x6
    // 0x88be64: stur            x0, [fp, #-0x18]
    // 0x88be68: r0 = AllocateArray()
    //     0x88be68: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88be6c: mov             x2, x0
    // 0x88be70: ldur            x0, [fp, #-0x10]
    // 0x88be74: stur            x2, [fp, #-0x20]
    // 0x88be78: StoreField: r2->field_f = r0
    //     0x88be78: stur            w0, [x2, #0xf]
    // 0x88be7c: ldur            x0, [fp, #-8]
    // 0x88be80: StoreField: r2->field_13 = r0
    //     0x88be80: stur            w0, [x2, #0x13]
    // 0x88be84: ldur            x0, [fp, #-0x18]
    // 0x88be88: ArrayStore: r2[0] = r0  ; List_4
    //     0x88be88: stur            w0, [x2, #0x17]
    // 0x88be8c: r1 = <Parser<(String, XmlAttributeType)>>
    //     0x88be8c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26760] TypeArguments: <Parser<(String, XmlAttributeType)>>
    //     0x88be90: ldr             x1, [x1, #0x760]
    // 0x88be94: r0 = AllocateGrowableArray()
    //     0x88be94: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88be98: mov             x1, x0
    // 0x88be9c: ldur            x0, [fp, #-0x20]
    // 0x88bea0: StoreField: r1->field_f = r0
    //     0x88bea0: stur            w0, [x1, #0xf]
    // 0x88bea4: r0 = 6
    //     0x88bea4: movz            x0, #0x6
    // 0x88bea8: StoreField: r1->field_b = r0
    //     0x88bea8: stur            w0, [x1, #0xb]
    // 0x88beac: r16 = <(String, XmlAttributeType)>
    //     0x88beac: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88beb0: ldr             x16, [x16, #0x730]
    // 0x88beb4: stp             x1, x16, [SP]
    // 0x88beb8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88beb8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88bebc: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88bebc: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88bec0: LeaveFrame
    //     0x88bec0: mov             SP, fp
    //     0x88bec4: ldp             fp, lr, [SP], #0x10
    // 0x88bec8: ret
    //     0x88bec8: ret             
    // 0x88becc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88becc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bed0: b               #0x88bde8
  }
  [closure] Parser<(String, XmlAttributeType)> attributeValueNoQuote(dynamic) {
    // ** addr: 0x88bed4, size: 0x38
    // 0x88bed4: EnterFrame
    //     0x88bed4: stp             fp, lr, [SP, #-0x10]!
    //     0x88bed8: mov             fp, SP
    // 0x88bedc: ldr             x0, [fp, #0x10]
    // 0x88bee0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88bee0: ldur            w1, [x0, #0x17]
    // 0x88bee4: DecompressPointer r1
    //     0x88bee4: add             x1, x1, HEAP, lsl #32
    // 0x88bee8: CheckStackOverflow
    //     0x88bee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88beec: cmp             SP, x16
    //     0x88bef0: b.ls            #0x88bf04
    // 0x88bef4: r0 = attributeValueNoQuote()
    //     0x88bef4: bl              #0x88bf0c  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueNoQuote
    // 0x88bef8: LeaveFrame
    //     0x88bef8: mov             SP, fp
    //     0x88befc: ldp             fp, lr, [SP], #0x10
    // 0x88bf00: ret
    //     0x88bf00: ret             
    // 0x88bf04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bf04: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bf08: b               #0x88bef4
  }
  _ attributeValueNoQuote(/* No info */) {
    // ** addr: 0x88bf0c, size: 0x7c
    // 0x88bf0c: EnterFrame
    //     0x88bf0c: stp             fp, lr, [SP, #-0x10]!
    //     0x88bf10: mov             fp, SP
    // 0x88bf14: AllocStack(0x20)
    //     0x88bf14: sub             SP, SP, #0x20
    // 0x88bf18: SetupParameters(XmlEventParser this /* r1 => r2 */)
    //     0x88bf18: mov             x2, x1
    // 0x88bf1c: CheckStackOverflow
    //     0x88bf1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bf20: cmp             SP, x16
    //     0x88bf24: b.ls            #0x88bf80
    // 0x88bf28: r1 = Function 'nameToken':.
    //     0x88bf28: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88bf2c: ldr             x1, [x1, #0x680]
    // 0x88bf30: r0 = AllocateClosure()
    //     0x88bf30: bl              #0xec1630  ; AllocateClosureStub
    // 0x88bf34: r16 = <String>
    //     0x88bf34: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88bf38: stp             x0, x16, [SP]
    // 0x88bf3c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88bf3c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88bf40: r0 = ref0()
    //     0x88bf40: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88bf44: r1 = Function '<anonymous closure>':.
    //     0x88bf44: add             x1, PP, #0x26, lsl #12  ; [pp+0x26768] AnonymousClosure: (0x88bf88), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueNoQuote (0x88bf0c)
    //     0x88bf48: ldr             x1, [x1, #0x768]
    // 0x88bf4c: r2 = Null
    //     0x88bf4c: mov             x2, NULL
    // 0x88bf50: stur            x0, [fp, #-8]
    // 0x88bf54: r0 = AllocateClosure()
    //     0x88bf54: bl              #0xec1630  ; AllocateClosureStub
    // 0x88bf58: r16 = <String, (String, XmlAttributeType)>
    //     0x88bf58: add             x16, PP, #0x26, lsl #12  ; [pp+0x26770] TypeArguments: <String, (String, XmlAttributeType)>
    //     0x88bf5c: ldr             x16, [x16, #0x770]
    // 0x88bf60: ldur            lr, [fp, #-8]
    // 0x88bf64: stp             lr, x16, [SP, #8]
    // 0x88bf68: str             x0, [SP]
    // 0x88bf6c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88bf6c: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88bf70: r0 = MapParserExtension.map()
    //     0x88bf70: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88bf74: LeaveFrame
    //     0x88bf74: mov             SP, fp
    //     0x88bf78: ldp             fp, lr, [SP], #0x10
    // 0x88bf7c: ret
    //     0x88bf7c: ret             
    // 0x88bf80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bf80: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bf84: b               #0x88bf28
  }
  [closure] (String, XmlAttributeType) <anonymous closure>(dynamic, String) {
    // ** addr: 0x88bf88, size: 0x24
    // 0x88bf88: EnterFrame
    //     0x88bf88: stp             fp, lr, [SP, #-0x10]!
    //     0x88bf8c: mov             fp, SP
    // 0x88bf90: ldr             x2, [fp, #0x10]
    // 0x88bf94: r3 = Instance_XmlAttributeType
    //     0x88bf94: add             x3, PP, #0x26, lsl #12  ; [pp+0x26778] Obj!XmlAttributeType@e2d4b1
    //     0x88bf98: ldr             x3, [x3, #0x778]
    // 0x88bf9c: r0 = AllocateRecord2()
    //     0x88bf9c: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x88bfa0: LeaveFrame
    //     0x88bfa0: mov             SP, fp
    //     0x88bfa4: ldp             fp, lr, [SP], #0x10
    // 0x88bfa8: ret
    //     0x88bfa8: ret             
  }
  [closure] Parser<(String, XmlAttributeType)> attributeValueSingleQuote(dynamic) {
    // ** addr: 0x88bfac, size: 0x38
    // 0x88bfac: EnterFrame
    //     0x88bfac: stp             fp, lr, [SP, #-0x10]!
    //     0x88bfb0: mov             fp, SP
    // 0x88bfb4: ldr             x0, [fp, #0x10]
    // 0x88bfb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88bfb8: ldur            w1, [x0, #0x17]
    // 0x88bfbc: DecompressPointer r1
    //     0x88bfbc: add             x1, x1, HEAP, lsl #32
    // 0x88bfc0: CheckStackOverflow
    //     0x88bfc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bfc4: cmp             SP, x16
    //     0x88bfc8: b.ls            #0x88bfdc
    // 0x88bfcc: r0 = attributeValueSingleQuote()
    //     0x88bfcc: bl              #0x88bfe4  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueSingleQuote
    // 0x88bfd0: LeaveFrame
    //     0x88bfd0: mov             SP, fp
    //     0x88bfd4: ldp             fp, lr, [SP], #0x10
    // 0x88bfd8: ret
    //     0x88bfd8: ret             
    // 0x88bfdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88bfdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88bfe0: b               #0x88bfcc
  }
  _ attributeValueSingleQuote(/* No info */) {
    // ** addr: 0x88bfe4, size: 0xb0
    // 0x88bfe4: EnterFrame
    //     0x88bfe4: stp             fp, lr, [SP, #-0x10]!
    //     0x88bfe8: mov             fp, SP
    // 0x88bfec: AllocStack(0x30)
    //     0x88bfec: sub             SP, SP, #0x30
    // 0x88bff0: r0 = "\'"
    //     0x88bff0: ldr             x0, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0x88bff4: CheckStackOverflow
    //     0x88bff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88bff8: cmp             SP, x16
    //     0x88bffc: b.ls            #0x88c08c
    // 0x88c000: mov             x1, x0
    // 0x88c004: r0 = PredicateStringExtension.toParser()
    //     0x88c004: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c008: r1 = <String>
    //     0x88c008: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c00c: stur            x0, [fp, #-8]
    // 0x88c010: r0 = XmlCharacterDataParser()
    //     0x88c010: bl              #0x88c094  ; AllocateXmlCharacterDataParserStub -> XmlCharacterDataParser (size=0x18)
    // 0x88c014: r1 = "\'"
    //     0x88c014: ldr             x1, [PP, #0x35c0]  ; [pp+0x35c0] "\'"
    // 0x88c018: stur            x0, [fp, #-0x10]
    // 0x88c01c: StoreField: r0->field_b = r1
    //     0x88c01c: stur            w1, [x0, #0xb]
    // 0x88c020: StoreField: r0->field_f = rZR
    //     0x88c020: stur            xzr, [x0, #0xf]
    // 0x88c024: r0 = PredicateStringExtension.toParser()
    //     0x88c024: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c028: r16 = <String, String, String>
    //     0x88c028: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88c02c: ldr             x16, [x16, #0x810]
    // 0x88c030: ldur            lr, [fp, #-8]
    // 0x88c034: stp             lr, x16, [SP, #0x10]
    // 0x88c038: ldur            x16, [fp, #-0x10]
    // 0x88c03c: stp             x0, x16, [SP]
    // 0x88c040: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88c040: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88c044: ldr             x4, [x4, #0xe10]
    // 0x88c048: r0 = seq3()
    //     0x88c048: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88c04c: r1 = Function '<anonymous closure>':.
    //     0x88c04c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26788] AnonymousClosure: (0x88c0a0), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueSingleQuote (0x88bfe4)
    //     0x88c050: ldr             x1, [x1, #0x788]
    // 0x88c054: r2 = Null
    //     0x88c054: mov             x2, NULL
    // 0x88c058: stur            x0, [fp, #-8]
    // 0x88c05c: r0 = AllocateClosure()
    //     0x88c05c: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c060: r16 = <String, String, String, (String, XmlAttributeType)>
    //     0x88c060: add             x16, PP, #0x26, lsl #12  ; [pp+0x26790] TypeArguments: <String, String, String, (String, XmlAttributeType)>
    //     0x88c064: ldr             x16, [x16, #0x790]
    // 0x88c068: ldur            lr, [fp, #-8]
    // 0x88c06c: stp             lr, x16, [SP, #8]
    // 0x88c070: str             x0, [SP]
    // 0x88c074: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88c074: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88c078: ldr             x4, [x4, #0x718]
    // 0x88c07c: r0 = RecordParserExtension3.map3()
    //     0x88c07c: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88c080: LeaveFrame
    //     0x88c080: mov             SP, fp
    //     0x88c084: ldp             fp, lr, [SP], #0x10
    // 0x88c088: ret
    //     0x88c088: ret             
    // 0x88c08c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c08c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c090: b               #0x88c000
  }
  [closure] (String, XmlAttributeType) <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x88c0a0, size: 0x24
    // 0x88c0a0: EnterFrame
    //     0x88c0a0: stp             fp, lr, [SP, #-0x10]!
    //     0x88c0a4: mov             fp, SP
    // 0x88c0a8: ldr             x2, [fp, #0x18]
    // 0x88c0ac: r3 = Instance_XmlAttributeType
    //     0x88c0ac: add             x3, PP, #0x26, lsl #12  ; [pp+0x26798] Obj!XmlAttributeType@e2d4d1
    //     0x88c0b0: ldr             x3, [x3, #0x798]
    // 0x88c0b4: r0 = AllocateRecord2()
    //     0x88c0b4: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x88c0b8: LeaveFrame
    //     0x88c0b8: mov             SP, fp
    //     0x88c0bc: ldp             fp, lr, [SP], #0x10
    // 0x88c0c0: ret
    //     0x88c0c0: ret             
  }
  [closure] Parser<(String, XmlAttributeType)> attributeValueDoubleQuote(dynamic) {
    // ** addr: 0x88c0c4, size: 0x38
    // 0x88c0c4: EnterFrame
    //     0x88c0c4: stp             fp, lr, [SP, #-0x10]!
    //     0x88c0c8: mov             fp, SP
    // 0x88c0cc: ldr             x0, [fp, #0x10]
    // 0x88c0d0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88c0d0: ldur            w1, [x0, #0x17]
    // 0x88c0d4: DecompressPointer r1
    //     0x88c0d4: add             x1, x1, HEAP, lsl #32
    // 0x88c0d8: CheckStackOverflow
    //     0x88c0d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c0dc: cmp             SP, x16
    //     0x88c0e0: b.ls            #0x88c0f4
    // 0x88c0e4: r0 = attributeValueDoubleQuote()
    //     0x88c0e4: bl              #0x88c0fc  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueDoubleQuote
    // 0x88c0e8: LeaveFrame
    //     0x88c0e8: mov             SP, fp
    //     0x88c0ec: ldp             fp, lr, [SP], #0x10
    // 0x88c0f0: ret
    //     0x88c0f0: ret             
    // 0x88c0f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c0f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c0f8: b               #0x88c0e4
  }
  _ attributeValueDoubleQuote(/* No info */) {
    // ** addr: 0x88c0fc, size: 0xb0
    // 0x88c0fc: EnterFrame
    //     0x88c0fc: stp             fp, lr, [SP, #-0x10]!
    //     0x88c100: mov             fp, SP
    // 0x88c104: AllocStack(0x30)
    //     0x88c104: sub             SP, SP, #0x30
    // 0x88c108: r0 = "\""
    //     0x88c108: ldr             x0, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x88c10c: CheckStackOverflow
    //     0x88c10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c110: cmp             SP, x16
    //     0x88c114: b.ls            #0x88c1a4
    // 0x88c118: mov             x1, x0
    // 0x88c11c: r0 = PredicateStringExtension.toParser()
    //     0x88c11c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c120: r1 = <String>
    //     0x88c120: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c124: stur            x0, [fp, #-8]
    // 0x88c128: r0 = XmlCharacterDataParser()
    //     0x88c128: bl              #0x88c094  ; AllocateXmlCharacterDataParserStub -> XmlCharacterDataParser (size=0x18)
    // 0x88c12c: r1 = "\""
    //     0x88c12c: ldr             x1, [PP, #0x1c50]  ; [pp+0x1c50] "\""
    // 0x88c130: stur            x0, [fp, #-0x10]
    // 0x88c134: StoreField: r0->field_b = r1
    //     0x88c134: stur            w1, [x0, #0xb]
    // 0x88c138: StoreField: r0->field_f = rZR
    //     0x88c138: stur            xzr, [x0, #0xf]
    // 0x88c13c: r0 = PredicateStringExtension.toParser()
    //     0x88c13c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c140: r16 = <String, String, String>
    //     0x88c140: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88c144: ldr             x16, [x16, #0x810]
    // 0x88c148: ldur            lr, [fp, #-8]
    // 0x88c14c: stp             lr, x16, [SP, #0x10]
    // 0x88c150: ldur            x16, [fp, #-0x10]
    // 0x88c154: stp             x0, x16, [SP]
    // 0x88c158: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88c158: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88c15c: ldr             x4, [x4, #0xe10]
    // 0x88c160: r0 = seq3()
    //     0x88c160: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88c164: r1 = Function '<anonymous closure>':.
    //     0x88c164: add             x1, PP, #0x26, lsl #12  ; [pp+0x267a0] AnonymousClosure: (0x88c1ac), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValueDoubleQuote (0x88c0fc)
    //     0x88c168: ldr             x1, [x1, #0x7a0]
    // 0x88c16c: r2 = Null
    //     0x88c16c: mov             x2, NULL
    // 0x88c170: stur            x0, [fp, #-8]
    // 0x88c174: r0 = AllocateClosure()
    //     0x88c174: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c178: r16 = <String, String, String, (String, XmlAttributeType)>
    //     0x88c178: add             x16, PP, #0x26, lsl #12  ; [pp+0x26790] TypeArguments: <String, String, String, (String, XmlAttributeType)>
    //     0x88c17c: ldr             x16, [x16, #0x790]
    // 0x88c180: ldur            lr, [fp, #-8]
    // 0x88c184: stp             lr, x16, [SP, #8]
    // 0x88c188: str             x0, [SP]
    // 0x88c18c: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88c18c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88c190: ldr             x4, [x4, #0x718]
    // 0x88c194: r0 = RecordParserExtension3.map3()
    //     0x88c194: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88c198: LeaveFrame
    //     0x88c198: mov             SP, fp
    //     0x88c19c: ldp             fp, lr, [SP], #0x10
    // 0x88c1a0: ret
    //     0x88c1a0: ret             
    // 0x88c1a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c1a4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c1a8: b               #0x88c118
  }
  [closure] (String, XmlAttributeType) <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x88c1ac, size: 0x24
    // 0x88c1ac: EnterFrame
    //     0x88c1ac: stp             fp, lr, [SP, #-0x10]!
    //     0x88c1b0: mov             fp, SP
    // 0x88c1b4: ldr             x2, [fp, #0x18]
    // 0x88c1b8: r3 = Instance_XmlAttributeType
    //     0x88c1b8: add             x3, PP, #0x26, lsl #12  ; [pp+0x26778] Obj!XmlAttributeType@e2d4b1
    //     0x88c1bc: ldr             x3, [x3, #0x778]
    // 0x88c1c0: r0 = AllocateRecord2()
    //     0x88c1c0: bl              #0xec0fc0  ; AllocateRecord2Stub
    // 0x88c1c4: LeaveFrame
    //     0x88c1c4: mov             SP, fp
    //     0x88c1c8: ldp             fp, lr, [SP], #0x10
    // 0x88c1cc: ret
    //     0x88c1cc: ret             
  }
  [closure] Parser<dynamic> doctypeEntityDecl(dynamic) {
    // ** addr: 0x88c1d0, size: 0x38
    // 0x88c1d0: EnterFrame
    //     0x88c1d0: stp             fp, lr, [SP, #-0x10]!
    //     0x88c1d4: mov             fp, SP
    // 0x88c1d8: ldr             x0, [fp, #0x10]
    // 0x88c1dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88c1dc: ldur            w1, [x0, #0x17]
    // 0x88c1e0: DecompressPointer r1
    //     0x88c1e0: add             x1, x1, HEAP, lsl #32
    // 0x88c1e4: CheckStackOverflow
    //     0x88c1e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c1e8: cmp             SP, x16
    //     0x88c1ec: b.ls            #0x88c200
    // 0x88c1f0: r0 = doctypeEntityDecl()
    //     0x88c1f0: bl              #0x88c208  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeEntityDecl
    // 0x88c1f4: LeaveFrame
    //     0x88c1f4: mov             SP, fp
    //     0x88c1f8: ldp             fp, lr, [SP], #0x10
    // 0x88c1fc: ret
    //     0x88c1fc: ret             
    // 0x88c200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c200: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c204: b               #0x88c1f0
  }
  _ doctypeEntityDecl(/* No info */) {
    // ** addr: 0x88c208, size: 0x148
    // 0x88c208: EnterFrame
    //     0x88c208: stp             fp, lr, [SP, #-0x10]!
    //     0x88c20c: mov             fp, SP
    // 0x88c210: AllocStack(0x48)
    //     0x88c210: sub             SP, SP, #0x48
    // 0x88c214: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88c214: mov             x2, x1
    //     0x88c218: stur            x1, [fp, #-8]
    // 0x88c21c: CheckStackOverflow
    //     0x88c21c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c220: cmp             SP, x16
    //     0x88c224: b.ls            #0x88c348
    // 0x88c228: r1 = "<!ENTITY"
    //     0x88c228: add             x1, PP, #0x26, lsl #12  ; [pp+0x267a8] "<!ENTITY"
    //     0x88c22c: ldr             x1, [x1, #0x7a8]
    // 0x88c230: r0 = PredicateStringExtension.toParser()
    //     0x88c230: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c234: ldur            x2, [fp, #-8]
    // 0x88c238: r1 = Function 'nameToken':.
    //     0x88c238: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88c23c: ldr             x1, [x1, #0x680]
    // 0x88c240: stur            x0, [fp, #-0x10]
    // 0x88c244: r0 = AllocateClosure()
    //     0x88c244: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c248: r16 = <String>
    //     0x88c248: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c24c: stp             x0, x16, [SP]
    // 0x88c250: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c250: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c254: r0 = ref0()
    //     0x88c254: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c258: ldur            x2, [fp, #-8]
    // 0x88c25c: r1 = Function 'attributeValue':.
    //     0x88c25c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26728] AnonymousClosure: (0x88bd90), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue (0x88bdc8)
    //     0x88c260: ldr             x1, [x1, #0x728]
    // 0x88c264: stur            x0, [fp, #-8]
    // 0x88c268: r0 = AllocateClosure()
    //     0x88c268: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c26c: r16 = <(String, XmlAttributeType)>
    //     0x88c26c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88c270: ldr             x16, [x16, #0x730]
    // 0x88c274: stp             x0, x16, [SP]
    // 0x88c278: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c278: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c27c: r0 = ref0()
    //     0x88c27c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c280: stur            x0, [fp, #-0x18]
    // 0x88c284: r0 = any()
    //     0x88c284: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88c288: r1 = Null
    //     0x88c288: mov             x1, NULL
    // 0x88c28c: r2 = 6
    //     0x88c28c: movz            x2, #0x6
    // 0x88c290: stur            x0, [fp, #-0x20]
    // 0x88c294: r0 = AllocateArray()
    //     0x88c294: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88c298: mov             x2, x0
    // 0x88c29c: ldur            x0, [fp, #-8]
    // 0x88c2a0: stur            x2, [fp, #-0x28]
    // 0x88c2a4: StoreField: r2->field_f = r0
    //     0x88c2a4: stur            w0, [x2, #0xf]
    // 0x88c2a8: ldur            x0, [fp, #-0x18]
    // 0x88c2ac: StoreField: r2->field_13 = r0
    //     0x88c2ac: stur            w0, [x2, #0x13]
    // 0x88c2b0: ldur            x0, [fp, #-0x20]
    // 0x88c2b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x88c2b4: stur            w0, [x2, #0x17]
    // 0x88c2b8: r1 = <Parser<Object>>
    //     0x88c2b8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26738] TypeArguments: <Parser<Object>>
    //     0x88c2bc: ldr             x1, [x1, #0x738]
    // 0x88c2c0: r0 = AllocateGrowableArray()
    //     0x88c2c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88c2c4: mov             x1, x0
    // 0x88c2c8: ldur            x0, [fp, #-0x28]
    // 0x88c2cc: StoreField: r1->field_f = r0
    //     0x88c2cc: stur            w0, [x1, #0xf]
    // 0x88c2d0: r0 = 6
    //     0x88c2d0: movz            x0, #0x6
    // 0x88c2d4: StoreField: r1->field_b = r0
    //     0x88c2d4: stur            w0, [x1, #0xb]
    // 0x88c2d8: r16 = <Object>
    //     0x88c2d8: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88c2dc: stp             x1, x16, [SP]
    // 0x88c2e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c2e0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c2e4: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88c2e4: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88c2e8: r1 = ">"
    //     0x88c2e8: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88c2ec: stur            x0, [fp, #-8]
    // 0x88c2f0: r0 = PredicateStringExtension.toParser()
    //     0x88c2f0: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c2f4: r16 = <Object>
    //     0x88c2f4: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88c2f8: ldur            lr, [fp, #-8]
    // 0x88c2fc: stp             lr, x16, [SP, #8]
    // 0x88c300: str             x0, [SP]
    // 0x88c304: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88c304: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88c308: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88c308: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88c30c: r1 = ">"
    //     0x88c30c: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88c310: stur            x0, [fp, #-8]
    // 0x88c314: r0 = PredicateStringExtension.toParser()
    //     0x88c314: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c318: r16 = <String, List<Object>, String>
    //     0x88c318: add             x16, PP, #0x26, lsl #12  ; [pp+0x26740] TypeArguments: <String, List<Object>, String>
    //     0x88c31c: ldr             x16, [x16, #0x740]
    // 0x88c320: ldur            lr, [fp, #-0x10]
    // 0x88c324: stp             lr, x16, [SP, #0x10]
    // 0x88c328: ldur            x16, [fp, #-8]
    // 0x88c32c: stp             x0, x16, [SP]
    // 0x88c330: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88c330: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88c334: ldr             x4, [x4, #0xe10]
    // 0x88c338: r0 = seq3()
    //     0x88c338: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88c33c: LeaveFrame
    //     0x88c33c: mov             SP, fp
    //     0x88c340: ldp             fp, lr, [SP], #0x10
    // 0x88c344: ret
    //     0x88c344: ret             
    // 0x88c348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c348: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c34c: b               #0x88c228
  }
  [closure] Parser<dynamic> doctypeAttlistDecl(dynamic) {
    // ** addr: 0x88c350, size: 0x38
    // 0x88c350: EnterFrame
    //     0x88c350: stp             fp, lr, [SP, #-0x10]!
    //     0x88c354: mov             fp, SP
    // 0x88c358: ldr             x0, [fp, #0x10]
    // 0x88c35c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88c35c: ldur            w1, [x0, #0x17]
    // 0x88c360: DecompressPointer r1
    //     0x88c360: add             x1, x1, HEAP, lsl #32
    // 0x88c364: CheckStackOverflow
    //     0x88c364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c368: cmp             SP, x16
    //     0x88c36c: b.ls            #0x88c380
    // 0x88c370: r0 = doctypeAttlistDecl()
    //     0x88c370: bl              #0x88c388  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeAttlistDecl
    // 0x88c374: LeaveFrame
    //     0x88c374: mov             SP, fp
    //     0x88c378: ldp             fp, lr, [SP], #0x10
    // 0x88c37c: ret
    //     0x88c37c: ret             
    // 0x88c380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c380: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c384: b               #0x88c370
  }
  _ doctypeAttlistDecl(/* No info */) {
    // ** addr: 0x88c388, size: 0x148
    // 0x88c388: EnterFrame
    //     0x88c388: stp             fp, lr, [SP, #-0x10]!
    //     0x88c38c: mov             fp, SP
    // 0x88c390: AllocStack(0x48)
    //     0x88c390: sub             SP, SP, #0x48
    // 0x88c394: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88c394: mov             x2, x1
    //     0x88c398: stur            x1, [fp, #-8]
    // 0x88c39c: CheckStackOverflow
    //     0x88c39c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c3a0: cmp             SP, x16
    //     0x88c3a4: b.ls            #0x88c4c8
    // 0x88c3a8: r1 = "<!ATTLIST"
    //     0x88c3a8: add             x1, PP, #0x26, lsl #12  ; [pp+0x267b0] "<!ATTLIST"
    //     0x88c3ac: ldr             x1, [x1, #0x7b0]
    // 0x88c3b0: r0 = PredicateStringExtension.toParser()
    //     0x88c3b0: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c3b4: ldur            x2, [fp, #-8]
    // 0x88c3b8: r1 = Function 'nameToken':.
    //     0x88c3b8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88c3bc: ldr             x1, [x1, #0x680]
    // 0x88c3c0: stur            x0, [fp, #-0x10]
    // 0x88c3c4: r0 = AllocateClosure()
    //     0x88c3c4: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c3c8: r16 = <String>
    //     0x88c3c8: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c3cc: stp             x0, x16, [SP]
    // 0x88c3d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c3d0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c3d4: r0 = ref0()
    //     0x88c3d4: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c3d8: ldur            x2, [fp, #-8]
    // 0x88c3dc: r1 = Function 'attributeValue':.
    //     0x88c3dc: add             x1, PP, #0x26, lsl #12  ; [pp+0x26728] AnonymousClosure: (0x88bd90), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue (0x88bdc8)
    //     0x88c3e0: ldr             x1, [x1, #0x728]
    // 0x88c3e4: stur            x0, [fp, #-8]
    // 0x88c3e8: r0 = AllocateClosure()
    //     0x88c3e8: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c3ec: r16 = <(String, XmlAttributeType)>
    //     0x88c3ec: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88c3f0: ldr             x16, [x16, #0x730]
    // 0x88c3f4: stp             x0, x16, [SP]
    // 0x88c3f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c3f8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c3fc: r0 = ref0()
    //     0x88c3fc: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c400: stur            x0, [fp, #-0x18]
    // 0x88c404: r0 = any()
    //     0x88c404: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88c408: r1 = Null
    //     0x88c408: mov             x1, NULL
    // 0x88c40c: r2 = 6
    //     0x88c40c: movz            x2, #0x6
    // 0x88c410: stur            x0, [fp, #-0x20]
    // 0x88c414: r0 = AllocateArray()
    //     0x88c414: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88c418: mov             x2, x0
    // 0x88c41c: ldur            x0, [fp, #-8]
    // 0x88c420: stur            x2, [fp, #-0x28]
    // 0x88c424: StoreField: r2->field_f = r0
    //     0x88c424: stur            w0, [x2, #0xf]
    // 0x88c428: ldur            x0, [fp, #-0x18]
    // 0x88c42c: StoreField: r2->field_13 = r0
    //     0x88c42c: stur            w0, [x2, #0x13]
    // 0x88c430: ldur            x0, [fp, #-0x20]
    // 0x88c434: ArrayStore: r2[0] = r0  ; List_4
    //     0x88c434: stur            w0, [x2, #0x17]
    // 0x88c438: r1 = <Parser<Object>>
    //     0x88c438: add             x1, PP, #0x26, lsl #12  ; [pp+0x26738] TypeArguments: <Parser<Object>>
    //     0x88c43c: ldr             x1, [x1, #0x738]
    // 0x88c440: r0 = AllocateGrowableArray()
    //     0x88c440: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88c444: mov             x1, x0
    // 0x88c448: ldur            x0, [fp, #-0x28]
    // 0x88c44c: StoreField: r1->field_f = r0
    //     0x88c44c: stur            w0, [x1, #0xf]
    // 0x88c450: r0 = 6
    //     0x88c450: movz            x0, #0x6
    // 0x88c454: StoreField: r1->field_b = r0
    //     0x88c454: stur            w0, [x1, #0xb]
    // 0x88c458: r16 = <Object>
    //     0x88c458: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88c45c: stp             x1, x16, [SP]
    // 0x88c460: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c460: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c464: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88c464: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88c468: r1 = ">"
    //     0x88c468: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88c46c: stur            x0, [fp, #-8]
    // 0x88c470: r0 = PredicateStringExtension.toParser()
    //     0x88c470: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c474: r16 = <Object>
    //     0x88c474: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88c478: ldur            lr, [fp, #-8]
    // 0x88c47c: stp             lr, x16, [SP, #8]
    // 0x88c480: str             x0, [SP]
    // 0x88c484: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88c484: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88c488: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88c488: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88c48c: r1 = ">"
    //     0x88c48c: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88c490: stur            x0, [fp, #-8]
    // 0x88c494: r0 = PredicateStringExtension.toParser()
    //     0x88c494: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c498: r16 = <String, List<Object>, String>
    //     0x88c498: add             x16, PP, #0x26, lsl #12  ; [pp+0x26740] TypeArguments: <String, List<Object>, String>
    //     0x88c49c: ldr             x16, [x16, #0x740]
    // 0x88c4a0: ldur            lr, [fp, #-0x10]
    // 0x88c4a4: stp             lr, x16, [SP, #0x10]
    // 0x88c4a8: ldur            x16, [fp, #-8]
    // 0x88c4ac: stp             x0, x16, [SP]
    // 0x88c4b0: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88c4b0: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88c4b4: ldr             x4, [x4, #0xe10]
    // 0x88c4b8: r0 = seq3()
    //     0x88c4b8: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88c4bc: LeaveFrame
    //     0x88c4bc: mov             SP, fp
    //     0x88c4c0: ldp             fp, lr, [SP], #0x10
    // 0x88c4c4: ret
    //     0x88c4c4: ret             
    // 0x88c4c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c4c8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c4cc: b               #0x88c3a8
  }
  [closure] Parser<dynamic> doctypeElementDecl(dynamic) {
    // ** addr: 0x88c4d0, size: 0x38
    // 0x88c4d0: EnterFrame
    //     0x88c4d0: stp             fp, lr, [SP, #-0x10]!
    //     0x88c4d4: mov             fp, SP
    // 0x88c4d8: ldr             x0, [fp, #0x10]
    // 0x88c4dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88c4dc: ldur            w1, [x0, #0x17]
    // 0x88c4e0: DecompressPointer r1
    //     0x88c4e0: add             x1, x1, HEAP, lsl #32
    // 0x88c4e4: CheckStackOverflow
    //     0x88c4e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c4e8: cmp             SP, x16
    //     0x88c4ec: b.ls            #0x88c500
    // 0x88c4f0: r0 = doctypeElementDecl()
    //     0x88c4f0: bl              #0x88c508  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeElementDecl
    // 0x88c4f4: LeaveFrame
    //     0x88c4f4: mov             SP, fp
    //     0x88c4f8: ldp             fp, lr, [SP], #0x10
    // 0x88c4fc: ret
    //     0x88c4fc: ret             
    // 0x88c500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c500: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c504: b               #0x88c4f0
  }
  _ doctypeElementDecl(/* No info */) {
    // ** addr: 0x88c508, size: 0x148
    // 0x88c508: EnterFrame
    //     0x88c508: stp             fp, lr, [SP, #-0x10]!
    //     0x88c50c: mov             fp, SP
    // 0x88c510: AllocStack(0x48)
    //     0x88c510: sub             SP, SP, #0x48
    // 0x88c514: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88c514: mov             x2, x1
    //     0x88c518: stur            x1, [fp, #-8]
    // 0x88c51c: CheckStackOverflow
    //     0x88c51c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c520: cmp             SP, x16
    //     0x88c524: b.ls            #0x88c648
    // 0x88c528: r1 = "<!ELEMENT"
    //     0x88c528: add             x1, PP, #0x26, lsl #12  ; [pp+0x267b8] "<!ELEMENT"
    //     0x88c52c: ldr             x1, [x1, #0x7b8]
    // 0x88c530: r0 = PredicateStringExtension.toParser()
    //     0x88c530: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c534: ldur            x2, [fp, #-8]
    // 0x88c538: r1 = Function 'nameToken':.
    //     0x88c538: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88c53c: ldr             x1, [x1, #0x680]
    // 0x88c540: stur            x0, [fp, #-0x10]
    // 0x88c544: r0 = AllocateClosure()
    //     0x88c544: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c548: r16 = <String>
    //     0x88c548: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c54c: stp             x0, x16, [SP]
    // 0x88c550: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c550: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c554: r0 = ref0()
    //     0x88c554: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c558: ldur            x2, [fp, #-8]
    // 0x88c55c: r1 = Function 'attributeValue':.
    //     0x88c55c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26728] AnonymousClosure: (0x88bd90), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue (0x88bdc8)
    //     0x88c560: ldr             x1, [x1, #0x728]
    // 0x88c564: stur            x0, [fp, #-8]
    // 0x88c568: r0 = AllocateClosure()
    //     0x88c568: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c56c: r16 = <(String, XmlAttributeType)>
    //     0x88c56c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88c570: ldr             x16, [x16, #0x730]
    // 0x88c574: stp             x0, x16, [SP]
    // 0x88c578: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c578: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c57c: r0 = ref0()
    //     0x88c57c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c580: stur            x0, [fp, #-0x18]
    // 0x88c584: r0 = any()
    //     0x88c584: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88c588: r1 = Null
    //     0x88c588: mov             x1, NULL
    // 0x88c58c: r2 = 6
    //     0x88c58c: movz            x2, #0x6
    // 0x88c590: stur            x0, [fp, #-0x20]
    // 0x88c594: r0 = AllocateArray()
    //     0x88c594: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88c598: mov             x2, x0
    // 0x88c59c: ldur            x0, [fp, #-8]
    // 0x88c5a0: stur            x2, [fp, #-0x28]
    // 0x88c5a4: StoreField: r2->field_f = r0
    //     0x88c5a4: stur            w0, [x2, #0xf]
    // 0x88c5a8: ldur            x0, [fp, #-0x18]
    // 0x88c5ac: StoreField: r2->field_13 = r0
    //     0x88c5ac: stur            w0, [x2, #0x13]
    // 0x88c5b0: ldur            x0, [fp, #-0x20]
    // 0x88c5b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x88c5b4: stur            w0, [x2, #0x17]
    // 0x88c5b8: r1 = <Parser<Object>>
    //     0x88c5b8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26738] TypeArguments: <Parser<Object>>
    //     0x88c5bc: ldr             x1, [x1, #0x738]
    // 0x88c5c0: r0 = AllocateGrowableArray()
    //     0x88c5c0: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88c5c4: mov             x1, x0
    // 0x88c5c8: ldur            x0, [fp, #-0x28]
    // 0x88c5cc: StoreField: r1->field_f = r0
    //     0x88c5cc: stur            w0, [x1, #0xf]
    // 0x88c5d0: r0 = 6
    //     0x88c5d0: movz            x0, #0x6
    // 0x88c5d4: StoreField: r1->field_b = r0
    //     0x88c5d4: stur            w0, [x1, #0xb]
    // 0x88c5d8: r16 = <Object>
    //     0x88c5d8: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88c5dc: stp             x1, x16, [SP]
    // 0x88c5e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c5e0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c5e4: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88c5e4: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88c5e8: r1 = ">"
    //     0x88c5e8: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88c5ec: stur            x0, [fp, #-8]
    // 0x88c5f0: r0 = PredicateStringExtension.toParser()
    //     0x88c5f0: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c5f4: r16 = <Object>
    //     0x88c5f4: ldr             x16, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x88c5f8: ldur            lr, [fp, #-8]
    // 0x88c5fc: stp             lr, x16, [SP, #8]
    // 0x88c600: str             x0, [SP]
    // 0x88c604: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88c604: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88c608: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88c608: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88c60c: r1 = ">"
    //     0x88c60c: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88c610: stur            x0, [fp, #-8]
    // 0x88c614: r0 = PredicateStringExtension.toParser()
    //     0x88c614: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c618: r16 = <String, List<Object>, String>
    //     0x88c618: add             x16, PP, #0x26, lsl #12  ; [pp+0x26740] TypeArguments: <String, List<Object>, String>
    //     0x88c61c: ldr             x16, [x16, #0x740]
    // 0x88c620: ldur            lr, [fp, #-0x10]
    // 0x88c624: stp             lr, x16, [SP, #0x10]
    // 0x88c628: ldur            x16, [fp, #-8]
    // 0x88c62c: stp             x0, x16, [SP]
    // 0x88c630: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88c630: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88c634: ldr             x4, [x4, #0xe10]
    // 0x88c638: r0 = seq3()
    //     0x88c638: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88c63c: LeaveFrame
    //     0x88c63c: mov             SP, fp
    //     0x88c640: ldp             fp, lr, [SP], #0x10
    // 0x88c644: ret
    //     0x88c644: ret             
    // 0x88c648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c648: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c64c: b               #0x88c528
  }
  [closure] Parser<String> spaceOptional(dynamic) {
    // ** addr: 0x88c650, size: 0x38
    // 0x88c650: EnterFrame
    //     0x88c650: stp             fp, lr, [SP, #-0x10]!
    //     0x88c654: mov             fp, SP
    // 0x88c658: ldr             x0, [fp, #0x10]
    // 0x88c65c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88c65c: ldur            w1, [x0, #0x17]
    // 0x88c660: DecompressPointer r1
    //     0x88c660: add             x1, x1, HEAP, lsl #32
    // 0x88c664: CheckStackOverflow
    //     0x88c664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c668: cmp             SP, x16
    //     0x88c66c: b.ls            #0x88c680
    // 0x88c670: r0 = spaceOptional()
    //     0x88c670: bl              #0x88c688  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::spaceOptional
    // 0x88c674: LeaveFrame
    //     0x88c674: mov             SP, fp
    //     0x88c678: ldp             fp, lr, [SP], #0x10
    // 0x88c67c: ret
    //     0x88c67c: ret             
    // 0x88c680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c680: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c684: b               #0x88c670
  }
  _ spaceOptional(/* No info */) {
    // ** addr: 0x88c688, size: 0x34
    // 0x88c688: EnterFrame
    //     0x88c688: stp             fp, lr, [SP, #-0x10]!
    //     0x88c68c: mov             fp, SP
    // 0x88c690: CheckStackOverflow
    //     0x88c690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c694: cmp             SP, x16
    //     0x88c698: b.ls            #0x88c6b4
    // 0x88c69c: r0 = whitespace()
    //     0x88c69c: bl              #0x88c754  ; [package:petitparser/src/parser/character/whitespace.dart] ::whitespace
    // 0x88c6a0: mov             x1, x0
    // 0x88c6a4: r0 = RepeatingCharacterParserExtension.starString()
    //     0x88c6a4: bl              #0x88c6bc  ; [package:petitparser/src/parser/repeater/character.dart] ::RepeatingCharacterParserExtension.starString
    // 0x88c6a8: LeaveFrame
    //     0x88c6a8: mov             SP, fp
    //     0x88c6ac: ldp             fp, lr, [SP], #0x10
    // 0x88c6b0: ret
    //     0x88c6b0: ret             
    // 0x88c6b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c6b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c6b8: b               #0x88c69c
  }
  [closure] Parser<DtdExternalId> doctypeExternalId(dynamic) {
    // ** addr: 0x88c788, size: 0x38
    // 0x88c788: EnterFrame
    //     0x88c788: stp             fp, lr, [SP, #-0x10]!
    //     0x88c78c: mov             fp, SP
    // 0x88c790: ldr             x0, [fp, #0x10]
    // 0x88c794: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88c794: ldur            w1, [x0, #0x17]
    // 0x88c798: DecompressPointer r1
    //     0x88c798: add             x1, x1, HEAP, lsl #32
    // 0x88c79c: CheckStackOverflow
    //     0x88c79c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c7a0: cmp             SP, x16
    //     0x88c7a4: b.ls            #0x88c7b8
    // 0x88c7a8: r0 = doctypeExternalId()
    //     0x88c7a8: bl              #0x88c7c0  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalId
    // 0x88c7ac: LeaveFrame
    //     0x88c7ac: mov             SP, fp
    //     0x88c7b0: ldp             fp, lr, [SP], #0x10
    // 0x88c7b4: ret
    //     0x88c7b4: ret             
    // 0x88c7b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c7b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c7bc: b               #0x88c7a8
  }
  _ doctypeExternalId(/* No info */) {
    // ** addr: 0x88c7c0, size: 0xdc
    // 0x88c7c0: EnterFrame
    //     0x88c7c0: stp             fp, lr, [SP, #-0x10]!
    //     0x88c7c4: mov             fp, SP
    // 0x88c7c8: AllocStack(0x28)
    //     0x88c7c8: sub             SP, SP, #0x28
    // 0x88c7cc: SetupParameters(XmlEventParser this /* r1 => r0, fp-0x8 */)
    //     0x88c7cc: mov             x0, x1
    //     0x88c7d0: stur            x1, [fp, #-8]
    // 0x88c7d4: CheckStackOverflow
    //     0x88c7d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c7d8: cmp             SP, x16
    //     0x88c7dc: b.ls            #0x88c894
    // 0x88c7e0: mov             x2, x0
    // 0x88c7e4: r1 = Function 'doctypeExternalIdSystem':.
    //     0x88c7e4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26800] AnonymousClosure: (0x88cccc), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalIdSystem (0x88cd04)
    //     0x88c7e8: ldr             x1, [x1, #0x800]
    // 0x88c7ec: r0 = AllocateClosure()
    //     0x88c7ec: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c7f0: r16 = <DtdExternalId>
    //     0x88c7f0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26690] TypeArguments: <DtdExternalId>
    //     0x88c7f4: ldr             x16, [x16, #0x690]
    // 0x88c7f8: stp             x0, x16, [SP]
    // 0x88c7fc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c7fc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c800: r0 = ref0()
    //     0x88c800: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c804: ldur            x2, [fp, #-8]
    // 0x88c808: r1 = Function 'doctypeExternalIdPublic':.
    //     0x88c808: add             x1, PP, #0x26, lsl #12  ; [pp+0x26808] AnonymousClosure: (0x88c910), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalIdPublic (0x88c948)
    //     0x88c80c: ldr             x1, [x1, #0x808]
    // 0x88c810: stur            x0, [fp, #-8]
    // 0x88c814: r0 = AllocateClosure()
    //     0x88c814: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c818: r16 = <DtdExternalId>
    //     0x88c818: add             x16, PP, #0x26, lsl #12  ; [pp+0x26690] TypeArguments: <DtdExternalId>
    //     0x88c81c: ldr             x16, [x16, #0x690]
    // 0x88c820: stp             x0, x16, [SP]
    // 0x88c824: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c824: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c828: r0 = ref0()
    //     0x88c828: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c82c: r1 = Null
    //     0x88c82c: mov             x1, NULL
    // 0x88c830: r2 = 4
    //     0x88c830: movz            x2, #0x4
    // 0x88c834: stur            x0, [fp, #-0x10]
    // 0x88c838: r0 = AllocateArray()
    //     0x88c838: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88c83c: mov             x2, x0
    // 0x88c840: ldur            x0, [fp, #-8]
    // 0x88c844: stur            x2, [fp, #-0x18]
    // 0x88c848: StoreField: r2->field_f = r0
    //     0x88c848: stur            w0, [x2, #0xf]
    // 0x88c84c: ldur            x0, [fp, #-0x10]
    // 0x88c850: StoreField: r2->field_13 = r0
    //     0x88c850: stur            w0, [x2, #0x13]
    // 0x88c854: r1 = <Parser<DtdExternalId>>
    //     0x88c854: add             x1, PP, #0x26, lsl #12  ; [pp+0x26810] TypeArguments: <Parser<DtdExternalId>>
    //     0x88c858: ldr             x1, [x1, #0x810]
    // 0x88c85c: r0 = AllocateGrowableArray()
    //     0x88c85c: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88c860: mov             x1, x0
    // 0x88c864: ldur            x0, [fp, #-0x18]
    // 0x88c868: StoreField: r1->field_f = r0
    //     0x88c868: stur            w0, [x1, #0xf]
    // 0x88c86c: r0 = 4
    //     0x88c86c: movz            x0, #0x4
    // 0x88c870: StoreField: r1->field_b = r0
    //     0x88c870: stur            w0, [x1, #0xb]
    // 0x88c874: r16 = <DtdExternalId>
    //     0x88c874: add             x16, PP, #0x26, lsl #12  ; [pp+0x26690] TypeArguments: <DtdExternalId>
    //     0x88c878: ldr             x16, [x16, #0x690]
    // 0x88c87c: stp             x1, x16, [SP]
    // 0x88c880: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c880: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c884: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88c884: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88c888: LeaveFrame
    //     0x88c888: mov             SP, fp
    //     0x88c88c: ldp             fp, lr, [SP], #0x10
    // 0x88c890: ret
    //     0x88c890: ret             
    // 0x88c894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c894: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c898: b               #0x88c7e0
  }
  [closure] Parser<DtdExternalId> doctypeExternalIdPublic(dynamic) {
    // ** addr: 0x88c910, size: 0x38
    // 0x88c910: EnterFrame
    //     0x88c910: stp             fp, lr, [SP, #-0x10]!
    //     0x88c914: mov             fp, SP
    // 0x88c918: ldr             x0, [fp, #0x10]
    // 0x88c91c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88c91c: ldur            w1, [x0, #0x17]
    // 0x88c920: DecompressPointer r1
    //     0x88c920: add             x1, x1, HEAP, lsl #32
    // 0x88c924: CheckStackOverflow
    //     0x88c924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c928: cmp             SP, x16
    //     0x88c92c: b.ls            #0x88c940
    // 0x88c930: r0 = doctypeExternalIdPublic()
    //     0x88c930: bl              #0x88c948  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalIdPublic
    // 0x88c934: LeaveFrame
    //     0x88c934: mov             SP, fp
    //     0x88c938: ldp             fp, lr, [SP], #0x10
    // 0x88c93c: ret
    //     0x88c93c: ret             
    // 0x88c940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88c940: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88c944: b               #0x88c930
  }
  _ doctypeExternalIdPublic(/* No info */) {
    // ** addr: 0x88c948, size: 0x12c
    // 0x88c948: EnterFrame
    //     0x88c948: stp             fp, lr, [SP, #-0x10]!
    //     0x88c94c: mov             fp, SP
    // 0x88c950: AllocStack(0x58)
    //     0x88c950: sub             SP, SP, #0x58
    // 0x88c954: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88c954: mov             x2, x1
    //     0x88c958: stur            x1, [fp, #-8]
    // 0x88c95c: CheckStackOverflow
    //     0x88c95c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88c960: cmp             SP, x16
    //     0x88c964: b.ls            #0x88ca6c
    // 0x88c968: r1 = "PUBLIC"
    //     0x88c968: add             x1, PP, #0x26, lsl #12  ; [pp+0x26818] "PUBLIC"
    //     0x88c96c: ldr             x1, [x1, #0x818]
    // 0x88c970: r0 = PredicateStringExtension.toParser()
    //     0x88c970: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88c974: ldur            x2, [fp, #-8]
    // 0x88c978: r1 = Function 'space':.
    //     0x88c978: add             x1, PP, #0x26, lsl #12  ; [pp+0x26678] AnonymousClosure: (0x88e0c8), in [package:xml/src/xml_events/parser.dart] XmlEventParser::space (0x88e100)
    //     0x88c97c: ldr             x1, [x1, #0x678]
    // 0x88c980: stur            x0, [fp, #-0x10]
    // 0x88c984: r0 = AllocateClosure()
    //     0x88c984: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c988: stur            x0, [fp, #-0x18]
    // 0x88c98c: r16 = <String>
    //     0x88c98c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c990: stp             x0, x16, [SP]
    // 0x88c994: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c994: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c998: r0 = ref0()
    //     0x88c998: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c99c: ldur            x2, [fp, #-8]
    // 0x88c9a0: r1 = Function 'attributeValue':.
    //     0x88c9a0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26728] AnonymousClosure: (0x88bd90), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue (0x88bdc8)
    //     0x88c9a4: ldr             x1, [x1, #0x728]
    // 0x88c9a8: stur            x0, [fp, #-8]
    // 0x88c9ac: r0 = AllocateClosure()
    //     0x88c9ac: bl              #0xec1630  ; AllocateClosureStub
    // 0x88c9b0: stur            x0, [fp, #-0x20]
    // 0x88c9b4: r16 = <(String, XmlAttributeType)>
    //     0x88c9b4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88c9b8: ldr             x16, [x16, #0x730]
    // 0x88c9bc: stp             x0, x16, [SP]
    // 0x88c9c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c9c0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c9c4: r0 = ref0()
    //     0x88c9c4: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c9c8: stur            x0, [fp, #-0x28]
    // 0x88c9cc: r16 = <String>
    //     0x88c9cc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88c9d0: ldur            lr, [fp, #-0x18]
    // 0x88c9d4: stp             lr, x16, [SP]
    // 0x88c9d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c9d8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c9dc: r0 = ref0()
    //     0x88c9dc: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c9e0: stur            x0, [fp, #-0x18]
    // 0x88c9e4: r16 = <(String, XmlAttributeType)>
    //     0x88c9e4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88c9e8: ldr             x16, [x16, #0x730]
    // 0x88c9ec: ldur            lr, [fp, #-0x20]
    // 0x88c9f0: stp             lr, x16, [SP]
    // 0x88c9f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88c9f4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88c9f8: r0 = ref0()
    //     0x88c9f8: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88c9fc: r16 = <String, String, (String, XmlAttributeType), String, (String, XmlAttributeType)>
    //     0x88c9fc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26820] TypeArguments: <String, String, (String, XmlAttributeType), String, (String, XmlAttributeType)>
    //     0x88ca00: ldr             x16, [x16, #0x820]
    // 0x88ca04: ldur            lr, [fp, #-0x10]
    // 0x88ca08: stp             lr, x16, [SP, #0x20]
    // 0x88ca0c: ldur            x16, [fp, #-8]
    // 0x88ca10: ldur            lr, [fp, #-0x28]
    // 0x88ca14: stp             lr, x16, [SP, #0x10]
    // 0x88ca18: ldur            x16, [fp, #-0x18]
    // 0x88ca1c: stp             x0, x16, [SP]
    // 0x88ca20: r4 = const [0x5, 0x5, 0x5, 0x5, null]
    //     0x88ca20: add             x4, PP, #0x26, lsl #12  ; [pp+0x26828] List(5) [0x5, 0x5, 0x5, 0x5, Null]
    //     0x88ca24: ldr             x4, [x4, #0x828]
    // 0x88ca28: r0 = seq5()
    //     0x88ca28: bl              #0x88cbb4  ; [package:petitparser/src/parser/combinator/generated/sequence_5.dart] ::seq5
    // 0x88ca2c: r1 = Function '<anonymous closure>':.
    //     0x88ca2c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26830] AnonymousClosure: (0x88cc4c), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalIdPublic (0x88c948)
    //     0x88ca30: ldr             x1, [x1, #0x830]
    // 0x88ca34: r2 = Null
    //     0x88ca34: mov             x2, NULL
    // 0x88ca38: stur            x0, [fp, #-8]
    // 0x88ca3c: r0 = AllocateClosure()
    //     0x88ca3c: bl              #0xec1630  ; AllocateClosureStub
    // 0x88ca40: r16 = <String, String, (String, XmlAttributeType), String, (String, XmlAttributeType), DtdExternalId>
    //     0x88ca40: add             x16, PP, #0x26, lsl #12  ; [pp+0x26838] TypeArguments: <String, String, (String, XmlAttributeType), String, (String, XmlAttributeType), DtdExternalId>
    //     0x88ca44: ldr             x16, [x16, #0x838]
    // 0x88ca48: ldur            lr, [fp, #-8]
    // 0x88ca4c: stp             lr, x16, [SP, #8]
    // 0x88ca50: str             x0, [SP]
    // 0x88ca54: r4 = const [0x6, 0x2, 0x2, 0x2, null]
    //     0x88ca54: add             x4, PP, #0x26, lsl #12  ; [pp+0x26840] List(5) [0x6, 0x2, 0x2, 0x2, Null]
    //     0x88ca58: ldr             x4, [x4, #0x840]
    // 0x88ca5c: r0 = RecordParserExtension5.map5()
    //     0x88ca5c: bl              #0x88ca74  ; [package:petitparser/src/parser/combinator/generated/sequence_5.dart] ::RecordParserExtension5.map5
    // 0x88ca60: LeaveFrame
    //     0x88ca60: mov             SP, fp
    //     0x88ca64: ldp             fp, lr, [SP], #0x10
    // 0x88ca68: ret
    //     0x88ca68: ret             
    // 0x88ca6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ca6c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ca70: b               #0x88c968
  }
  [closure] DtdExternalId <anonymous closure>(dynamic, String, String, (String, XmlAttributeType), String, (String, XmlAttributeType)) {
    // ** addr: 0x88cc4c, size: 0x74
    // 0x88cc4c: EnterFrame
    //     0x88cc4c: stp             fp, lr, [SP, #-0x10]!
    //     0x88cc50: mov             fp, SP
    // 0x88cc54: AllocStack(0x20)
    //     0x88cc54: sub             SP, SP, #0x20
    // 0x88cc58: ldr             x0, [fp, #0x20]
    // 0x88cc5c: LoadField: r1 = r0->field_f
    //     0x88cc5c: ldur            w1, [x0, #0xf]
    // 0x88cc60: DecompressPointer r1
    //     0x88cc60: add             x1, x1, HEAP, lsl #32
    // 0x88cc64: stur            x1, [fp, #-0x20]
    // 0x88cc68: LoadField: r2 = r0->field_13
    //     0x88cc68: ldur            w2, [x0, #0x13]
    // 0x88cc6c: DecompressPointer r2
    //     0x88cc6c: add             x2, x2, HEAP, lsl #32
    // 0x88cc70: ldr             x0, [fp, #0x10]
    // 0x88cc74: stur            x2, [fp, #-0x18]
    // 0x88cc78: LoadField: r3 = r0->field_f
    //     0x88cc78: ldur            w3, [x0, #0xf]
    // 0x88cc7c: DecompressPointer r3
    //     0x88cc7c: add             x3, x3, HEAP, lsl #32
    // 0x88cc80: stur            x3, [fp, #-0x10]
    // 0x88cc84: LoadField: r4 = r0->field_13
    //     0x88cc84: ldur            w4, [x0, #0x13]
    // 0x88cc88: DecompressPointer r4
    //     0x88cc88: add             x4, x4, HEAP, lsl #32
    // 0x88cc8c: stur            x4, [fp, #-8]
    // 0x88cc90: r0 = DtdExternalId()
    //     0x88cc90: bl              #0x88ccc0  ; AllocateDtdExternalIdStub -> DtdExternalId (size=0x18)
    // 0x88cc94: ldur            x1, [fp, #-0x20]
    // 0x88cc98: StoreField: r0->field_7 = r1
    //     0x88cc98: stur            w1, [x0, #7]
    // 0x88cc9c: ldur            x1, [fp, #-0x18]
    // 0x88cca0: StoreField: r0->field_b = r1
    //     0x88cca0: stur            w1, [x0, #0xb]
    // 0x88cca4: ldur            x1, [fp, #-0x10]
    // 0x88cca8: StoreField: r0->field_f = r1
    //     0x88cca8: stur            w1, [x0, #0xf]
    // 0x88ccac: ldur            x1, [fp, #-8]
    // 0x88ccb0: StoreField: r0->field_13 = r1
    //     0x88ccb0: stur            w1, [x0, #0x13]
    // 0x88ccb4: LeaveFrame
    //     0x88ccb4: mov             SP, fp
    //     0x88ccb8: ldp             fp, lr, [SP], #0x10
    // 0x88ccbc: ret
    //     0x88ccbc: ret             
  }
  [closure] Parser<DtdExternalId> doctypeExternalIdSystem(dynamic) {
    // ** addr: 0x88cccc, size: 0x38
    // 0x88cccc: EnterFrame
    //     0x88cccc: stp             fp, lr, [SP, #-0x10]!
    //     0x88ccd0: mov             fp, SP
    // 0x88ccd4: ldr             x0, [fp, #0x10]
    // 0x88ccd8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88ccd8: ldur            w1, [x0, #0x17]
    // 0x88ccdc: DecompressPointer r1
    //     0x88ccdc: add             x1, x1, HEAP, lsl #32
    // 0x88cce0: CheckStackOverflow
    //     0x88cce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88cce4: cmp             SP, x16
    //     0x88cce8: b.ls            #0x88ccfc
    // 0x88ccec: r0 = doctypeExternalIdSystem()
    //     0x88ccec: bl              #0x88cd04  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalIdSystem
    // 0x88ccf0: LeaveFrame
    //     0x88ccf0: mov             SP, fp
    //     0x88ccf4: ldp             fp, lr, [SP], #0x10
    // 0x88ccf8: ret
    //     0x88ccf8: ret             
    // 0x88ccfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ccfc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88cd00: b               #0x88ccec
  }
  _ doctypeExternalIdSystem(/* No info */) {
    // ** addr: 0x88cd04, size: 0xe4
    // 0x88cd04: EnterFrame
    //     0x88cd04: stp             fp, lr, [SP, #-0x10]!
    //     0x88cd08: mov             fp, SP
    // 0x88cd0c: AllocStack(0x30)
    //     0x88cd0c: sub             SP, SP, #0x30
    // 0x88cd10: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88cd10: mov             x2, x1
    //     0x88cd14: stur            x1, [fp, #-8]
    // 0x88cd18: CheckStackOverflow
    //     0x88cd18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88cd1c: cmp             SP, x16
    //     0x88cd20: b.ls            #0x88cde0
    // 0x88cd24: r1 = "SYSTEM"
    //     0x88cd24: add             x1, PP, #0x26, lsl #12  ; [pp+0x26860] "SYSTEM"
    //     0x88cd28: ldr             x1, [x1, #0x860]
    // 0x88cd2c: r0 = PredicateStringExtension.toParser()
    //     0x88cd2c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88cd30: ldur            x2, [fp, #-8]
    // 0x88cd34: r1 = Function 'space':.
    //     0x88cd34: add             x1, PP, #0x26, lsl #12  ; [pp+0x26678] AnonymousClosure: (0x88e0c8), in [package:xml/src/xml_events/parser.dart] XmlEventParser::space (0x88e100)
    //     0x88cd38: ldr             x1, [x1, #0x678]
    // 0x88cd3c: stur            x0, [fp, #-0x10]
    // 0x88cd40: r0 = AllocateClosure()
    //     0x88cd40: bl              #0xec1630  ; AllocateClosureStub
    // 0x88cd44: r16 = <String>
    //     0x88cd44: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88cd48: stp             x0, x16, [SP]
    // 0x88cd4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88cd4c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88cd50: r0 = ref0()
    //     0x88cd50: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88cd54: ldur            x2, [fp, #-8]
    // 0x88cd58: r1 = Function 'attributeValue':.
    //     0x88cd58: add             x1, PP, #0x26, lsl #12  ; [pp+0x26728] AnonymousClosure: (0x88bd90), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue (0x88bdc8)
    //     0x88cd5c: ldr             x1, [x1, #0x728]
    // 0x88cd60: stur            x0, [fp, #-8]
    // 0x88cd64: r0 = AllocateClosure()
    //     0x88cd64: bl              #0xec1630  ; AllocateClosureStub
    // 0x88cd68: r16 = <(String, XmlAttributeType)>
    //     0x88cd68: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88cd6c: ldr             x16, [x16, #0x730]
    // 0x88cd70: stp             x0, x16, [SP]
    // 0x88cd74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88cd74: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88cd78: r0 = ref0()
    //     0x88cd78: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88cd7c: r16 = <String, String, (String, XmlAttributeType)>
    //     0x88cd7c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26868] TypeArguments: <String, String, (String, XmlAttributeType)>
    //     0x88cd80: ldr             x16, [x16, #0x868]
    // 0x88cd84: ldur            lr, [fp, #-0x10]
    // 0x88cd88: stp             lr, x16, [SP, #0x10]
    // 0x88cd8c: ldur            x16, [fp, #-8]
    // 0x88cd90: stp             x0, x16, [SP]
    // 0x88cd94: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88cd94: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88cd98: ldr             x4, [x4, #0xe10]
    // 0x88cd9c: r0 = seq3()
    //     0x88cd9c: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88cda0: r1 = Function '<anonymous closure>':.
    //     0x88cda0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26870] AnonymousClosure: (0x88cde8), in [package:xml/src/xml_events/parser.dart] XmlEventParser::doctypeExternalIdSystem (0x88cd04)
    //     0x88cda4: ldr             x1, [x1, #0x870]
    // 0x88cda8: r2 = Null
    //     0x88cda8: mov             x2, NULL
    // 0x88cdac: stur            x0, [fp, #-8]
    // 0x88cdb0: r0 = AllocateClosure()
    //     0x88cdb0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88cdb4: r16 = <String, String, (String, XmlAttributeType), DtdExternalId>
    //     0x88cdb4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26878] TypeArguments: <String, String, (String, XmlAttributeType), DtdExternalId>
    //     0x88cdb8: ldr             x16, [x16, #0x878]
    // 0x88cdbc: ldur            lr, [fp, #-8]
    // 0x88cdc0: stp             lr, x16, [SP, #8]
    // 0x88cdc4: str             x0, [SP]
    // 0x88cdc8: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88cdc8: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88cdcc: ldr             x4, [x4, #0x718]
    // 0x88cdd0: r0 = RecordParserExtension3.map3()
    //     0x88cdd0: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88cdd4: LeaveFrame
    //     0x88cdd4: mov             SP, fp
    //     0x88cdd8: ldp             fp, lr, [SP], #0x10
    // 0x88cddc: ret
    //     0x88cddc: ret             
    // 0x88cde0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88cde0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88cde4: b               #0x88cd24
  }
  [closure] DtdExternalId <anonymous closure>(dynamic, String, String, (String, XmlAttributeType)) {
    // ** addr: 0x88cde8, size: 0x48
    // 0x88cde8: EnterFrame
    //     0x88cde8: stp             fp, lr, [SP, #-0x10]!
    //     0x88cdec: mov             fp, SP
    // 0x88cdf0: AllocStack(0x10)
    //     0x88cdf0: sub             SP, SP, #0x10
    // 0x88cdf4: ldr             x0, [fp, #0x10]
    // 0x88cdf8: LoadField: r1 = r0->field_f
    //     0x88cdf8: ldur            w1, [x0, #0xf]
    // 0x88cdfc: DecompressPointer r1
    //     0x88cdfc: add             x1, x1, HEAP, lsl #32
    // 0x88ce00: stur            x1, [fp, #-0x10]
    // 0x88ce04: LoadField: r2 = r0->field_13
    //     0x88ce04: ldur            w2, [x0, #0x13]
    // 0x88ce08: DecompressPointer r2
    //     0x88ce08: add             x2, x2, HEAP, lsl #32
    // 0x88ce0c: stur            x2, [fp, #-8]
    // 0x88ce10: r0 = DtdExternalId()
    //     0x88ce10: bl              #0x88ccc0  ; AllocateDtdExternalIdStub -> DtdExternalId (size=0x18)
    // 0x88ce14: ldur            x1, [fp, #-0x10]
    // 0x88ce18: StoreField: r0->field_f = r1
    //     0x88ce18: stur            w1, [x0, #0xf]
    // 0x88ce1c: ldur            x1, [fp, #-8]
    // 0x88ce20: StoreField: r0->field_13 = r1
    //     0x88ce20: stur            w1, [x0, #0x13]
    // 0x88ce24: LeaveFrame
    //     0x88ce24: mov             SP, fp
    //     0x88ce28: ldp             fp, lr, [SP], #0x10
    // 0x88ce2c: ret
    //     0x88ce2c: ret             
  }
  [closure] Parser<String> nameToken(dynamic) {
    // ** addr: 0x88ce30, size: 0x38
    // 0x88ce30: EnterFrame
    //     0x88ce30: stp             fp, lr, [SP, #-0x10]!
    //     0x88ce34: mov             fp, SP
    // 0x88ce38: ldr             x0, [fp, #0x10]
    // 0x88ce3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88ce3c: ldur            w1, [x0, #0x17]
    // 0x88ce40: DecompressPointer r1
    //     0x88ce40: add             x1, x1, HEAP, lsl #32
    // 0x88ce44: CheckStackOverflow
    //     0x88ce44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ce48: cmp             SP, x16
    //     0x88ce4c: b.ls            #0x88ce60
    // 0x88ce50: r0 = nameToken()
    //     0x88ce50: bl              #0x88ce68  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken
    // 0x88ce54: LeaveFrame
    //     0x88ce54: mov             SP, fp
    //     0x88ce58: ldp             fp, lr, [SP], #0x10
    // 0x88ce5c: ret
    //     0x88ce5c: ret             
    // 0x88ce60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88ce60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88ce64: b               #0x88ce50
  }
  _ nameToken(/* No info */) {
    // ** addr: 0x88ce68, size: 0xc4
    // 0x88ce68: EnterFrame
    //     0x88ce68: stp             fp, lr, [SP, #-0x10]!
    //     0x88ce6c: mov             fp, SP
    // 0x88ce70: AllocStack(0x20)
    //     0x88ce70: sub             SP, SP, #0x20
    // 0x88ce74: SetupParameters(XmlEventParser this /* r1 => r0, fp-0x8 */)
    //     0x88ce74: mov             x0, x1
    //     0x88ce78: stur            x1, [fp, #-8]
    // 0x88ce7c: CheckStackOverflow
    //     0x88ce7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88ce80: cmp             SP, x16
    //     0x88ce84: b.ls            #0x88cf24
    // 0x88ce88: mov             x2, x0
    // 0x88ce8c: r1 = Function 'nameStartChar':.
    //     0x88ce8c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26880] AnonymousClosure: (0x88e094), of [package:xml/src/xml_events/parser.dart] XmlEventParser
    //     0x88ce90: ldr             x1, [x1, #0x880]
    // 0x88ce94: r0 = AllocateClosure()
    //     0x88ce94: bl              #0xec1630  ; AllocateClosureStub
    // 0x88ce98: r16 = <String>
    //     0x88ce98: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88ce9c: stp             x0, x16, [SP]
    // 0x88cea0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88cea0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88cea4: r0 = ref0()
    //     0x88cea4: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88cea8: ldur            x2, [fp, #-8]
    // 0x88ceac: r1 = Function 'nameChar':.
    //     0x88ceac: add             x1, PP, #0x26, lsl #12  ; [pp+0x26888] AnonymousClosure: (0x88d080), of [package:xml/src/xml_events/parser.dart] XmlEventParser
    //     0x88ceb0: ldr             x1, [x1, #0x888]
    // 0x88ceb4: stur            x0, [fp, #-8]
    // 0x88ceb8: r0 = AllocateClosure()
    //     0x88ceb8: bl              #0xec1630  ; AllocateClosureStub
    // 0x88cebc: r16 = <String>
    //     0x88cebc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88cec0: stp             x0, x16, [SP]
    // 0x88cec4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88cec4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88cec8: r0 = ref0()
    //     0x88cec8: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88cecc: r16 = <String>
    //     0x88cecc: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88ced0: stp             x0, x16, [SP]
    // 0x88ced4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88ced4: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88ced8: r0 = PossessiveRepeatingParserExtension.star()
    //     0x88ced8: bl              #0x88cfa0  ; [package:petitparser/src/parser/repeater/possessive.dart] ::PossessiveRepeatingParserExtension.star
    // 0x88cedc: r16 = <String, List<String>>
    //     0x88cedc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b38] TypeArguments: <String, List<String>>
    //     0x88cee0: ldr             x16, [x16, #0xb38]
    // 0x88cee4: ldur            lr, [fp, #-8]
    // 0x88cee8: stp             lr, x16, [SP, #8]
    // 0x88ceec: str             x0, [SP]
    // 0x88cef0: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88cef0: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88cef4: r0 = seq2()
    //     0x88cef4: bl              #0x88cf2c  ; [package:petitparser/src/parser/combinator/generated/sequence_2.dart] ::seq2
    // 0x88cef8: r16 = <(String, List<String>)>
    //     0x88cef8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26890] TypeArguments: <(String, List<String>)>
    //     0x88cefc: ldr             x16, [x16, #0x890]
    // 0x88cf00: stp             x0, x16, [SP, #8]
    // 0x88cf04: r16 = "name expected"
    //     0x88cf04: add             x16, PP, #0x26, lsl #12  ; [pp+0x26898] "name expected"
    //     0x88cf08: ldr             x16, [x16, #0x898]
    // 0x88cf0c: str             x16, [SP]
    // 0x88cf10: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88cf10: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88cf14: r0 = FlattenParserExtension.flatten()
    //     0x88cf14: bl              #0x88b93c  ; [package:petitparser/src/parser/action/flatten.dart] ::FlattenParserExtension.flatten
    // 0x88cf18: LeaveFrame
    //     0x88cf18: mov             SP, fp
    //     0x88cf1c: ldp             fp, lr, [SP], #0x10
    // 0x88cf20: ret
    //     0x88cf20: ret             
    // 0x88cf24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88cf24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88cf28: b               #0x88ce88
  }
  [closure] Parser<String> nameChar(dynamic) {
    // ** addr: 0x88d080, size: 0x34
    // 0x88d080: EnterFrame
    //     0x88d080: stp             fp, lr, [SP, #-0x10]!
    //     0x88d084: mov             fp, SP
    // 0x88d088: CheckStackOverflow
    //     0x88d088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88d08c: cmp             SP, x16
    //     0x88d090: b.ls            #0x88d0ac
    // 0x88d094: r1 = ":A-Z_a-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�-.0-9·̀-ͯ‿-⁀"
    //     0x88d094: add             x1, PP, #0x26, lsl #12  ; [pp+0x268a0] ":A-Z_a-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�-.0-9·̀-ͯ‿-⁀"
    //     0x88d098: ldr             x1, [x1, #0x8a0]
    // 0x88d09c: r0 = pattern()
    //     0x88d09c: bl              #0x88d0b4  ; [package:petitparser/src/parser/character/pattern.dart] ::pattern
    // 0x88d0a0: LeaveFrame
    //     0x88d0a0: mov             SP, fp
    //     0x88d0a4: ldp             fp, lr, [SP], #0x10
    // 0x88d0a8: ret
    //     0x88d0a8: ret             
    // 0x88d0ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88d0ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88d0b0: b               #0x88d094
  }
  [closure] Parser<String> nameStartChar(dynamic) {
    // ** addr: 0x88e094, size: 0x34
    // 0x88e094: EnterFrame
    //     0x88e094: stp             fp, lr, [SP, #-0x10]!
    //     0x88e098: mov             fp, SP
    // 0x88e09c: CheckStackOverflow
    //     0x88e09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e0a0: cmp             SP, x16
    //     0x88e0a4: b.ls            #0x88e0c0
    // 0x88e0a8: r1 = ":A-Z_a-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�"
    //     0x88e0a8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a00] ":A-Z_a-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�"
    //     0x88e0ac: ldr             x1, [x1, #0xa00]
    // 0x88e0b0: r0 = pattern()
    //     0x88e0b0: bl              #0x88d0b4  ; [package:petitparser/src/parser/character/pattern.dart] ::pattern
    // 0x88e0b4: LeaveFrame
    //     0x88e0b4: mov             SP, fp
    //     0x88e0b8: ldp             fp, lr, [SP], #0x10
    // 0x88e0bc: ret
    //     0x88e0bc: ret             
    // 0x88e0c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e0c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e0c4: b               #0x88e0a8
  }
  [closure] Parser<String> space(dynamic) {
    // ** addr: 0x88e0c8, size: 0x38
    // 0x88e0c8: EnterFrame
    //     0x88e0c8: stp             fp, lr, [SP, #-0x10]!
    //     0x88e0cc: mov             fp, SP
    // 0x88e0d0: ldr             x0, [fp, #0x10]
    // 0x88e0d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88e0d4: ldur            w1, [x0, #0x17]
    // 0x88e0d8: DecompressPointer r1
    //     0x88e0d8: add             x1, x1, HEAP, lsl #32
    // 0x88e0dc: CheckStackOverflow
    //     0x88e0dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e0e0: cmp             SP, x16
    //     0x88e0e4: b.ls            #0x88e0f8
    // 0x88e0e8: r0 = space()
    //     0x88e0e8: bl              #0x88e100  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::space
    // 0x88e0ec: LeaveFrame
    //     0x88e0ec: mov             SP, fp
    //     0x88e0f0: ldp             fp, lr, [SP], #0x10
    // 0x88e0f4: ret
    //     0x88e0f4: ret             
    // 0x88e0f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e0f8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e0fc: b               #0x88e0e8
  }
  _ space(/* No info */) {
    // ** addr: 0x88e100, size: 0x34
    // 0x88e100: EnterFrame
    //     0x88e100: stp             fp, lr, [SP, #-0x10]!
    //     0x88e104: mov             fp, SP
    // 0x88e108: CheckStackOverflow
    //     0x88e108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e10c: cmp             SP, x16
    //     0x88e110: b.ls            #0x88e12c
    // 0x88e114: r0 = whitespace()
    //     0x88e114: bl              #0x88c754  ; [package:petitparser/src/parser/character/whitespace.dart] ::whitespace
    // 0x88e118: mov             x1, x0
    // 0x88e11c: r0 = RepeatingCharacterParserExtension.plusString()
    //     0x88e11c: bl              #0x88e134  ; [package:petitparser/src/parser/repeater/character.dart] ::RepeatingCharacterParserExtension.plusString
    // 0x88e120: LeaveFrame
    //     0x88e120: mov             SP, fp
    //     0x88e124: ldp             fp, lr, [SP], #0x10
    // 0x88e128: ret
    //     0x88e128: ret             
    // 0x88e12c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e12c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e130: b               #0x88e114
  }
  [closure] Parser<XmlProcessingEvent> processing(dynamic) {
    // ** addr: 0x88e168, size: 0x38
    // 0x88e168: EnterFrame
    //     0x88e168: stp             fp, lr, [SP, #-0x10]!
    //     0x88e16c: mov             fp, SP
    // 0x88e170: ldr             x0, [fp, #0x10]
    // 0x88e174: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88e174: ldur            w1, [x0, #0x17]
    // 0x88e178: DecompressPointer r1
    //     0x88e178: add             x1, x1, HEAP, lsl #32
    // 0x88e17c: CheckStackOverflow
    //     0x88e17c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e180: cmp             SP, x16
    //     0x88e184: b.ls            #0x88e198
    // 0x88e188: r0 = processing()
    //     0x88e188: bl              #0x88e1a0  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::processing
    // 0x88e18c: LeaveFrame
    //     0x88e18c: mov             SP, fp
    //     0x88e190: ldp             fp, lr, [SP], #0x10
    // 0x88e194: ret
    //     0x88e194: ret             
    // 0x88e198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e198: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e19c: b               #0x88e188
  }
  _ processing(/* No info */) {
    // ** addr: 0x88e1a0, size: 0x1ac
    // 0x88e1a0: EnterFrame
    //     0x88e1a0: stp             fp, lr, [SP, #-0x10]!
    //     0x88e1a4: mov             fp, SP
    // 0x88e1a8: AllocStack(0x48)
    //     0x88e1a8: sub             SP, SP, #0x48
    // 0x88e1ac: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88e1ac: mov             x2, x1
    //     0x88e1b0: stur            x1, [fp, #-8]
    // 0x88e1b4: CheckStackOverflow
    //     0x88e1b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e1b8: cmp             SP, x16
    //     0x88e1bc: b.ls            #0x88e344
    // 0x88e1c0: r1 = "<\?"
    //     0x88e1c0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a50] "<\?"
    //     0x88e1c4: ldr             x1, [x1, #0xa50]
    // 0x88e1c8: r0 = PredicateStringExtension.toParser()
    //     0x88e1c8: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88e1cc: ldur            x2, [fp, #-8]
    // 0x88e1d0: r1 = Function 'nameToken':.
    //     0x88e1d0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88e1d4: ldr             x1, [x1, #0x680]
    // 0x88e1d8: stur            x0, [fp, #-0x10]
    // 0x88e1dc: r0 = AllocateClosure()
    //     0x88e1dc: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e1e0: r16 = <String>
    //     0x88e1e0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88e1e4: stp             x0, x16, [SP]
    // 0x88e1e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e1e8: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e1ec: r0 = ref0()
    //     0x88e1ec: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e1f0: ldur            x2, [fp, #-8]
    // 0x88e1f4: r1 = Function 'space':.
    //     0x88e1f4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26678] AnonymousClosure: (0x88e0c8), in [package:xml/src/xml_events/parser.dart] XmlEventParser::space (0x88e100)
    //     0x88e1f8: ldr             x1, [x1, #0x678]
    // 0x88e1fc: stur            x0, [fp, #-8]
    // 0x88e200: r0 = AllocateClosure()
    //     0x88e200: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e204: r16 = <String>
    //     0x88e204: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88e208: stp             x0, x16, [SP]
    // 0x88e20c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e20c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e210: r0 = ref0()
    //     0x88e210: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e214: stur            x0, [fp, #-0x18]
    // 0x88e218: r0 = any()
    //     0x88e218: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88e21c: r1 = "\?>"
    //     0x88e21c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a58] "\?>"
    //     0x88e220: ldr             x1, [x1, #0xa58]
    // 0x88e224: stur            x0, [fp, #-0x20]
    // 0x88e228: r0 = PredicateStringExtension.toParser()
    //     0x88e228: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88e22c: r16 = <String>
    //     0x88e22c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88e230: ldur            lr, [fp, #-0x20]
    // 0x88e234: stp             lr, x16, [SP, #8]
    // 0x88e238: str             x0, [SP]
    // 0x88e23c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88e23c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88e240: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88e240: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88e244: r16 = <List<String>>
    //     0x88e244: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0x88e248: ldr             x16, [x16, #0xb40]
    // 0x88e24c: stp             x0, x16, [SP, #8]
    // 0x88e250: r16 = "\"\?>\" expected"
    //     0x88e250: add             x16, PP, #0x26, lsl #12  ; [pp+0x26a60] "\"\?>\" expected"
    //     0x88e254: ldr             x16, [x16, #0xa60]
    // 0x88e258: str             x16, [SP]
    // 0x88e25c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88e25c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88e260: r0 = FlattenParserExtension.flatten()
    //     0x88e260: bl              #0x88b93c  ; [package:petitparser/src/parser/action/flatten.dart] ::FlattenParserExtension.flatten
    // 0x88e264: r16 = <String, String>
    //     0x88e264: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x88e268: ldr             x16, [x16, #0x668]
    // 0x88e26c: ldur            lr, [fp, #-0x18]
    // 0x88e270: stp             lr, x16, [SP, #8]
    // 0x88e274: str             x0, [SP]
    // 0x88e278: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88e278: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88e27c: r0 = seq2()
    //     0x88e27c: bl              #0x88cf2c  ; [package:petitparser/src/parser/combinator/generated/sequence_2.dart] ::seq2
    // 0x88e280: r1 = Function '<anonymous closure>':.
    //     0x88e280: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a68] AnonymousClosure: (0xebd554), in [package:flutter/src/services/restoration.dart] RestorationBucket::_visitChildren (0x69ffb0)
    //     0x88e284: ldr             x1, [x1, #0xa68]
    // 0x88e288: r2 = Null
    //     0x88e288: mov             x2, NULL
    // 0x88e28c: stur            x0, [fp, #-0x18]
    // 0x88e290: r0 = AllocateClosure()
    //     0x88e290: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e294: r16 = <String, String, String>
    //     0x88e294: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88e298: ldr             x16, [x16, #0x810]
    // 0x88e29c: ldur            lr, [fp, #-0x18]
    // 0x88e2a0: stp             lr, x16, [SP, #8]
    // 0x88e2a4: str             x0, [SP]
    // 0x88e2a8: r4 = const [0x3, 0x2, 0x2, 0x2, null]
    //     0x88e2a8: ldr             x4, [PP, #0x1930]  ; [pp+0x1930] List(5) [0x3, 0x2, 0x2, 0x2, Null]
    // 0x88e2ac: r0 = RecordParserExtension2.map2()
    //     0x88e2ac: bl              #0x88d2e4  ; [package:petitparser/src/parser/combinator/generated/sequence_2.dart] ::RecordParserExtension2.map2
    // 0x88e2b0: r16 = <String>
    //     0x88e2b0: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88e2b4: stp             x0, x16, [SP, #8]
    // 0x88e2b8: r16 = ""
    //     0x88e2b8: ldr             x16, [PP, #0x288]  ; [pp+0x288] ""
    // 0x88e2bc: str             x16, [SP]
    // 0x88e2c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88e2c0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88e2c4: r0 = OptionalParserExtension.optionalWith()
    //     0x88e2c4: bl              #0x88e510  ; [package:petitparser/src/parser/combinator/optional.dart] ::OptionalParserExtension.optionalWith
    // 0x88e2c8: r1 = "\?>"
    //     0x88e2c8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a58] "\?>"
    //     0x88e2cc: ldr             x1, [x1, #0xa58]
    // 0x88e2d0: stur            x0, [fp, #-0x18]
    // 0x88e2d4: r0 = PredicateStringExtension.toParser()
    //     0x88e2d4: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88e2d8: r16 = <String, String, String, String>
    //     0x88e2d8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26710] TypeArguments: <String, String, String, String>
    //     0x88e2dc: ldr             x16, [x16, #0x710]
    // 0x88e2e0: ldur            lr, [fp, #-0x10]
    // 0x88e2e4: stp             lr, x16, [SP, #0x18]
    // 0x88e2e8: ldur            x16, [fp, #-8]
    // 0x88e2ec: ldur            lr, [fp, #-0x18]
    // 0x88e2f0: stp             lr, x16, [SP, #8]
    // 0x88e2f4: str             x0, [SP]
    // 0x88e2f8: r4 = const [0x4, 0x4, 0x4, 0x4, null]
    //     0x88e2f8: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a70] List(5) [0x4, 0x4, 0x4, 0x4, Null]
    //     0x88e2fc: ldr             x4, [x4, #0xa70]
    // 0x88e300: r0 = seq4()
    //     0x88e300: bl              #0x88e484  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::seq4
    // 0x88e304: r1 = Function '<anonymous closure>':.
    //     0x88e304: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a78] AnonymousClosure: (0x88e58c), in [package:xml/src/xml_events/parser.dart] XmlEventParser::processing (0x88e1a0)
    //     0x88e308: ldr             x1, [x1, #0xa78]
    // 0x88e30c: r2 = Null
    //     0x88e30c: mov             x2, NULL
    // 0x88e310: stur            x0, [fp, #-8]
    // 0x88e314: r0 = AllocateClosure()
    //     0x88e314: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e318: r16 = <String, String, String, String, XmlProcessingEvent>
    //     0x88e318: add             x16, PP, #0x26, lsl #12  ; [pp+0x26a80] TypeArguments: <String, String, String, String, XmlProcessingEvent>
    //     0x88e31c: ldr             x16, [x16, #0xa80]
    // 0x88e320: ldur            lr, [fp, #-8]
    // 0x88e324: stp             lr, x16, [SP, #8]
    // 0x88e328: str             x0, [SP]
    // 0x88e32c: r4 = const [0x5, 0x2, 0x2, 0x2, null]
    //     0x88e32c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a88] List(5) [0x5, 0x2, 0x2, 0x2, Null]
    //     0x88e330: ldr             x4, [x4, #0xa88]
    // 0x88e334: r0 = RecordParserExtension4.map4()
    //     0x88e334: bl              #0x88e34c  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::RecordParserExtension4.map4
    // 0x88e338: LeaveFrame
    //     0x88e338: mov             SP, fp
    //     0x88e33c: ldp             fp, lr, [SP], #0x10
    // 0x88e340: ret
    //     0x88e340: ret             
    // 0x88e344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e344: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e348: b               #0x88e1c0
  }
  [closure] XmlProcessingEvent <anonymous closure>(dynamic, String, String, String, String) {
    // ** addr: 0x88e58c, size: 0x28
    // 0x88e58c: EnterFrame
    //     0x88e58c: stp             fp, lr, [SP, #-0x10]!
    //     0x88e590: mov             fp, SP
    // 0x88e594: r0 = XmlProcessingEvent()
    //     0x88e594: bl              #0x88e5b4  ; AllocateXmlProcessingEventStub -> XmlProcessingEvent (size=0x1c)
    // 0x88e598: ldr             x1, [fp, #0x20]
    // 0x88e59c: StoreField: r0->field_13 = r1
    //     0x88e59c: stur            w1, [x0, #0x13]
    // 0x88e5a0: ldr             x1, [fp, #0x18]
    // 0x88e5a4: ArrayStore: r0[0] = r1  ; List_4
    //     0x88e5a4: stur            w1, [x0, #0x17]
    // 0x88e5a8: LeaveFrame
    //     0x88e5a8: mov             SP, fp
    //     0x88e5ac: ldp             fp, lr, [SP], #0x10
    // 0x88e5b0: ret
    //     0x88e5b0: ret             
  }
  [closure] Parser<XmlDeclarationEvent> declaration(dynamic) {
    // ** addr: 0x88e5c0, size: 0x38
    // 0x88e5c0: EnterFrame
    //     0x88e5c0: stp             fp, lr, [SP, #-0x10]!
    //     0x88e5c4: mov             fp, SP
    // 0x88e5c8: ldr             x0, [fp, #0x10]
    // 0x88e5cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88e5cc: ldur            w1, [x0, #0x17]
    // 0x88e5d0: DecompressPointer r1
    //     0x88e5d0: add             x1, x1, HEAP, lsl #32
    // 0x88e5d4: CheckStackOverflow
    //     0x88e5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e5d8: cmp             SP, x16
    //     0x88e5dc: b.ls            #0x88e5f0
    // 0x88e5e0: r0 = declaration()
    //     0x88e5e0: bl              #0x88e5f8  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::declaration
    // 0x88e5e4: LeaveFrame
    //     0x88e5e4: mov             SP, fp
    //     0x88e5e8: ldp             fp, lr, [SP], #0x10
    // 0x88e5ec: ret
    //     0x88e5ec: ret             
    // 0x88e5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e5f0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e5f4: b               #0x88e5e0
  }
  _ declaration(/* No info */) {
    // ** addr: 0x88e5f8, size: 0xfc
    // 0x88e5f8: EnterFrame
    //     0x88e5f8: stp             fp, lr, [SP, #-0x10]!
    //     0x88e5fc: mov             fp, SP
    // 0x88e600: AllocStack(0x40)
    //     0x88e600: sub             SP, SP, #0x40
    // 0x88e604: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88e604: mov             x2, x1
    //     0x88e608: stur            x1, [fp, #-8]
    // 0x88e60c: CheckStackOverflow
    //     0x88e60c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e610: cmp             SP, x16
    //     0x88e614: b.ls            #0x88e6ec
    // 0x88e618: r1 = "<\?xml"
    //     0x88e618: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ab0] "<\?xml"
    //     0x88e61c: ldr             x1, [x1, #0xab0]
    // 0x88e620: r0 = PredicateStringExtension.toParser()
    //     0x88e620: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88e624: ldur            x2, [fp, #-8]
    // 0x88e628: r1 = Function 'attributes':.
    //     0x88e628: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ab8] AnonymousClosure: (0x88e720), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributes (0x88e758)
    //     0x88e62c: ldr             x1, [x1, #0xab8]
    // 0x88e630: stur            x0, [fp, #-0x10]
    // 0x88e634: r0 = AllocateClosure()
    //     0x88e634: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e638: r16 = <List<XmlEventAttribute>>
    //     0x88e638: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ac0] TypeArguments: <List<XmlEventAttribute>>
    //     0x88e63c: ldr             x16, [x16, #0xac0]
    // 0x88e640: stp             x0, x16, [SP]
    // 0x88e644: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e644: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e648: r0 = ref0()
    //     0x88e648: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e64c: ldur            x2, [fp, #-8]
    // 0x88e650: r1 = Function 'spaceOptional':.
    //     0x88e650: add             x1, PP, #0x26, lsl #12  ; [pp+0x26698] AnonymousClosure: (0x88c650), in [package:xml/src/xml_events/parser.dart] XmlEventParser::spaceOptional (0x88c688)
    //     0x88e654: ldr             x1, [x1, #0x698]
    // 0x88e658: stur            x0, [fp, #-8]
    // 0x88e65c: r0 = AllocateClosure()
    //     0x88e65c: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e660: r16 = <String>
    //     0x88e660: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88e664: stp             x0, x16, [SP]
    // 0x88e668: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e668: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e66c: r0 = ref0()
    //     0x88e66c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e670: r1 = "\?>"
    //     0x88e670: add             x1, PP, #0x26, lsl #12  ; [pp+0x26a58] "\?>"
    //     0x88e674: ldr             x1, [x1, #0xa58]
    // 0x88e678: stur            x0, [fp, #-0x18]
    // 0x88e67c: r0 = PredicateStringExtension.toParser()
    //     0x88e67c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88e680: r16 = <String, List<XmlEventAttribute>, String, String>
    //     0x88e680: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ac8] TypeArguments: <String, List<XmlEventAttribute>, String, String>
    //     0x88e684: ldr             x16, [x16, #0xac8]
    // 0x88e688: ldur            lr, [fp, #-0x10]
    // 0x88e68c: stp             lr, x16, [SP, #0x18]
    // 0x88e690: ldur            x16, [fp, #-8]
    // 0x88e694: ldur            lr, [fp, #-0x18]
    // 0x88e698: stp             lr, x16, [SP, #8]
    // 0x88e69c: str             x0, [SP]
    // 0x88e6a0: r4 = const [0x4, 0x4, 0x4, 0x4, null]
    //     0x88e6a0: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a70] List(5) [0x4, 0x4, 0x4, 0x4, Null]
    //     0x88e6a4: ldr             x4, [x4, #0xa70]
    // 0x88e6a8: r0 = seq4()
    //     0x88e6a8: bl              #0x88e484  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::seq4
    // 0x88e6ac: r1 = Function '<anonymous closure>':.
    //     0x88e6ac: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ad0] AnonymousClosure: (0x88e6f4), in [package:xml/src/xml_events/parser.dart] XmlEventParser::declaration (0x88e5f8)
    //     0x88e6b0: ldr             x1, [x1, #0xad0]
    // 0x88e6b4: r2 = Null
    //     0x88e6b4: mov             x2, NULL
    // 0x88e6b8: stur            x0, [fp, #-8]
    // 0x88e6bc: r0 = AllocateClosure()
    //     0x88e6bc: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e6c0: r16 = <String, List<XmlEventAttribute>, String, String, XmlDeclarationEvent>
    //     0x88e6c0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ad8] TypeArguments: <String, List<XmlEventAttribute>, String, String, XmlDeclarationEvent>
    //     0x88e6c4: ldr             x16, [x16, #0xad8]
    // 0x88e6c8: ldur            lr, [fp, #-8]
    // 0x88e6cc: stp             lr, x16, [SP, #8]
    // 0x88e6d0: str             x0, [SP]
    // 0x88e6d4: r4 = const [0x5, 0x2, 0x2, 0x2, null]
    //     0x88e6d4: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a88] List(5) [0x5, 0x2, 0x2, 0x2, Null]
    //     0x88e6d8: ldr             x4, [x4, #0xa88]
    // 0x88e6dc: r0 = RecordParserExtension4.map4()
    //     0x88e6dc: bl              #0x88e34c  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::RecordParserExtension4.map4
    // 0x88e6e0: LeaveFrame
    //     0x88e6e0: mov             SP, fp
    //     0x88e6e4: ldp             fp, lr, [SP], #0x10
    // 0x88e6e8: ret
    //     0x88e6e8: ret             
    // 0x88e6ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e6ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e6f0: b               #0x88e618
  }
  [closure] XmlDeclarationEvent <anonymous closure>(dynamic, String, List<XmlEventAttribute>, String, String) {
    // ** addr: 0x88e6f4, size: 0x20
    // 0x88e6f4: EnterFrame
    //     0x88e6f4: stp             fp, lr, [SP, #-0x10]!
    //     0x88e6f8: mov             fp, SP
    // 0x88e6fc: r0 = XmlDeclarationEvent()
    //     0x88e6fc: bl              #0x88e714  ; AllocateXmlDeclarationEventStub -> XmlDeclarationEvent (size=0x18)
    // 0x88e700: ldr             x1, [fp, #0x20]
    // 0x88e704: StoreField: r0->field_13 = r1
    //     0x88e704: stur            w1, [x0, #0x13]
    // 0x88e708: LeaveFrame
    //     0x88e708: mov             SP, fp
    //     0x88e70c: ldp             fp, lr, [SP], #0x10
    // 0x88e710: ret
    //     0x88e710: ret             
  }
  [closure] Parser<List<XmlEventAttribute>> attributes(dynamic) {
    // ** addr: 0x88e720, size: 0x38
    // 0x88e720: EnterFrame
    //     0x88e720: stp             fp, lr, [SP, #-0x10]!
    //     0x88e724: mov             fp, SP
    // 0x88e728: ldr             x0, [fp, #0x10]
    // 0x88e72c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88e72c: ldur            w1, [x0, #0x17]
    // 0x88e730: DecompressPointer r1
    //     0x88e730: add             x1, x1, HEAP, lsl #32
    // 0x88e734: CheckStackOverflow
    //     0x88e734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e738: cmp             SP, x16
    //     0x88e73c: b.ls            #0x88e750
    // 0x88e740: r0 = attributes()
    //     0x88e740: bl              #0x88e758  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::attributes
    // 0x88e744: LeaveFrame
    //     0x88e744: mov             SP, fp
    //     0x88e748: ldp             fp, lr, [SP], #0x10
    // 0x88e74c: ret
    //     0x88e74c: ret             
    // 0x88e750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e750: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e754: b               #0x88e740
  }
  _ attributes(/* No info */) {
    // ** addr: 0x88e758, size: 0x64
    // 0x88e758: EnterFrame
    //     0x88e758: stp             fp, lr, [SP, #-0x10]!
    //     0x88e75c: mov             fp, SP
    // 0x88e760: AllocStack(0x10)
    //     0x88e760: sub             SP, SP, #0x10
    // 0x88e764: SetupParameters(XmlEventParser this /* r1 => r2 */)
    //     0x88e764: mov             x2, x1
    // 0x88e768: CheckStackOverflow
    //     0x88e768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e76c: cmp             SP, x16
    //     0x88e770: b.ls            #0x88e7b4
    // 0x88e774: r1 = Function 'attribute':.
    //     0x88e774: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ae0] AnonymousClosure: (0x88e7bc), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attribute (0x88e7f4)
    //     0x88e778: ldr             x1, [x1, #0xae0]
    // 0x88e77c: r0 = AllocateClosure()
    //     0x88e77c: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e780: r16 = <XmlEventAttribute>
    //     0x88e780: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ae8] TypeArguments: <XmlEventAttribute>
    //     0x88e784: ldr             x16, [x16, #0xae8]
    // 0x88e788: stp             x0, x16, [SP]
    // 0x88e78c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e78c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e790: r0 = ref0()
    //     0x88e790: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e794: r16 = <XmlEventAttribute>
    //     0x88e794: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ae8] TypeArguments: <XmlEventAttribute>
    //     0x88e798: ldr             x16, [x16, #0xae8]
    // 0x88e79c: stp             x0, x16, [SP]
    // 0x88e7a0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e7a0: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e7a4: r0 = PossessiveRepeatingParserExtension.star()
    //     0x88e7a4: bl              #0x88cfa0  ; [package:petitparser/src/parser/repeater/possessive.dart] ::PossessiveRepeatingParserExtension.star
    // 0x88e7a8: LeaveFrame
    //     0x88e7a8: mov             SP, fp
    //     0x88e7ac: ldp             fp, lr, [SP], #0x10
    // 0x88e7b0: ret
    //     0x88e7b0: ret             
    // 0x88e7b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e7b4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e7b8: b               #0x88e774
  }
  [closure] Parser<XmlEventAttribute> attribute(dynamic) {
    // ** addr: 0x88e7bc, size: 0x38
    // 0x88e7bc: EnterFrame
    //     0x88e7bc: stp             fp, lr, [SP, #-0x10]!
    //     0x88e7c0: mov             fp, SP
    // 0x88e7c4: ldr             x0, [fp, #0x10]
    // 0x88e7c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88e7c8: ldur            w1, [x0, #0x17]
    // 0x88e7cc: DecompressPointer r1
    //     0x88e7cc: add             x1, x1, HEAP, lsl #32
    // 0x88e7d0: CheckStackOverflow
    //     0x88e7d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e7d4: cmp             SP, x16
    //     0x88e7d8: b.ls            #0x88e7ec
    // 0x88e7dc: r0 = attribute()
    //     0x88e7dc: bl              #0x88e7f4  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::attribute
    // 0x88e7e0: LeaveFrame
    //     0x88e7e0: mov             SP, fp
    //     0x88e7e4: ldp             fp, lr, [SP], #0x10
    // 0x88e7e8: ret
    //     0x88e7e8: ret             
    // 0x88e7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e7ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e7f0: b               #0x88e7dc
  }
  _ attribute(/* No info */) {
    // ** addr: 0x88e7f4, size: 0x110
    // 0x88e7f4: EnterFrame
    //     0x88e7f4: stp             fp, lr, [SP, #-0x10]!
    //     0x88e7f8: mov             fp, SP
    // 0x88e7fc: AllocStack(0x38)
    //     0x88e7fc: sub             SP, SP, #0x38
    // 0x88e800: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88e800: mov             x2, x1
    //     0x88e804: stur            x1, [fp, #-8]
    // 0x88e808: CheckStackOverflow
    //     0x88e808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e80c: cmp             SP, x16
    //     0x88e810: b.ls            #0x88e8fc
    // 0x88e814: r1 = 1
    //     0x88e814: movz            x1, #0x1
    // 0x88e818: r0 = AllocateContext()
    //     0x88e818: bl              #0xec126c  ; AllocateContextStub
    // 0x88e81c: mov             x3, x0
    // 0x88e820: ldur            x0, [fp, #-8]
    // 0x88e824: stur            x3, [fp, #-0x10]
    // 0x88e828: StoreField: r3->field_f = r0
    //     0x88e828: stur            w0, [x3, #0xf]
    // 0x88e82c: mov             x2, x0
    // 0x88e830: r1 = Function 'space':.
    //     0x88e830: add             x1, PP, #0x26, lsl #12  ; [pp+0x26678] AnonymousClosure: (0x88e0c8), in [package:xml/src/xml_events/parser.dart] XmlEventParser::space (0x88e100)
    //     0x88e834: ldr             x1, [x1, #0x678]
    // 0x88e838: r0 = AllocateClosure()
    //     0x88e838: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e83c: r16 = <String>
    //     0x88e83c: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88e840: stp             x0, x16, [SP]
    // 0x88e844: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e844: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e848: r0 = ref0()
    //     0x88e848: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e84c: ldur            x2, [fp, #-8]
    // 0x88e850: r1 = Function 'nameToken':.
    //     0x88e850: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88e854: ldr             x1, [x1, #0x680]
    // 0x88e858: stur            x0, [fp, #-0x18]
    // 0x88e85c: r0 = AllocateClosure()
    //     0x88e85c: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e860: r16 = <String>
    //     0x88e860: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88e864: stp             x0, x16, [SP]
    // 0x88e868: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e868: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e86c: r0 = ref0()
    //     0x88e86c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e870: ldur            x2, [fp, #-8]
    // 0x88e874: r1 = Function 'attributeAssignment':.
    //     0x88e874: add             x1, PP, #0x26, lsl #12  ; [pp+0x26af0] AnonymousClosure: (0x88ee88), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeAssignment (0x88eec0)
    //     0x88e878: ldr             x1, [x1, #0xaf0]
    // 0x88e87c: stur            x0, [fp, #-8]
    // 0x88e880: r0 = AllocateClosure()
    //     0x88e880: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e884: r16 = <(String, XmlAttributeType)>
    //     0x88e884: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88e888: ldr             x16, [x16, #0x730]
    // 0x88e88c: stp             x0, x16, [SP]
    // 0x88e890: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88e890: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88e894: r0 = ref0()
    //     0x88e894: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88e898: r16 = <String, String, (String, XmlAttributeType)>
    //     0x88e898: add             x16, PP, #0x26, lsl #12  ; [pp+0x26868] TypeArguments: <String, String, (String, XmlAttributeType)>
    //     0x88e89c: ldr             x16, [x16, #0x868]
    // 0x88e8a0: ldur            lr, [fp, #-0x18]
    // 0x88e8a4: stp             lr, x16, [SP, #0x10]
    // 0x88e8a8: ldur            x16, [fp, #-8]
    // 0x88e8ac: stp             x0, x16, [SP]
    // 0x88e8b0: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88e8b0: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88e8b4: ldr             x4, [x4, #0xe10]
    // 0x88e8b8: r0 = seq3()
    //     0x88e8b8: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88e8bc: ldur            x2, [fp, #-0x10]
    // 0x88e8c0: r1 = Function '<anonymous closure>':.
    //     0x88e8c0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26af8] AnonymousClosure: (0x88e904), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attribute (0x88e7f4)
    //     0x88e8c4: ldr             x1, [x1, #0xaf8]
    // 0x88e8c8: stur            x0, [fp, #-8]
    // 0x88e8cc: r0 = AllocateClosure()
    //     0x88e8cc: bl              #0xec1630  ; AllocateClosureStub
    // 0x88e8d0: r16 = <String, String, (String, XmlAttributeType), XmlEventAttribute>
    //     0x88e8d0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b00] TypeArguments: <String, String, (String, XmlAttributeType), XmlEventAttribute>
    //     0x88e8d4: ldr             x16, [x16, #0xb00]
    // 0x88e8d8: ldur            lr, [fp, #-8]
    // 0x88e8dc: stp             lr, x16, [SP, #8]
    // 0x88e8e0: str             x0, [SP]
    // 0x88e8e4: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88e8e4: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88e8e8: ldr             x4, [x4, #0x718]
    // 0x88e8ec: r0 = RecordParserExtension3.map3()
    //     0x88e8ec: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88e8f0: LeaveFrame
    //     0x88e8f0: mov             SP, fp
    //     0x88e8f4: ldp             fp, lr, [SP], #0x10
    // 0x88e8f8: ret
    //     0x88e8f8: ret             
    // 0x88e8fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e8fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e900: b               #0x88e814
  }
  [closure] XmlEventAttribute <anonymous closure>(dynamic, String, String, (String, XmlAttributeType)) {
    // ** addr: 0x88e904, size: 0x8c
    // 0x88e904: EnterFrame
    //     0x88e904: stp             fp, lr, [SP, #-0x10]!
    //     0x88e908: mov             fp, SP
    // 0x88e90c: AllocStack(0x10)
    //     0x88e90c: sub             SP, SP, #0x10
    // 0x88e910: SetupParameters()
    //     0x88e910: ldr             x0, [fp, #0x28]
    //     0x88e914: ldur            w1, [x0, #0x17]
    //     0x88e918: add             x1, x1, HEAP, lsl #32
    // 0x88e91c: CheckStackOverflow
    //     0x88e91c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88e920: cmp             SP, x16
    //     0x88e924: b.ls            #0x88e988
    // 0x88e928: LoadField: r0 = r1->field_f
    //     0x88e928: ldur            w0, [x1, #0xf]
    // 0x88e92c: DecompressPointer r0
    //     0x88e92c: add             x0, x0, HEAP, lsl #32
    // 0x88e930: LoadField: r1 = r0->field_7
    //     0x88e930: ldur            w1, [x0, #7]
    // 0x88e934: DecompressPointer r1
    //     0x88e934: add             x1, x1, HEAP, lsl #32
    // 0x88e938: ldr             x0, [fp, #0x10]
    // 0x88e93c: LoadField: r2 = r0->field_f
    //     0x88e93c: ldur            w2, [x0, #0xf]
    // 0x88e940: DecompressPointer r2
    //     0x88e940: add             x2, x2, HEAP, lsl #32
    // 0x88e944: r0 = decode()
    //     0x88e944: bl              #0x88e99c  ; [package:xml/src/xml/entities/entity_mapping.dart] XmlEntityMapping::decode
    // 0x88e948: mov             x1, x0
    // 0x88e94c: ldr             x0, [fp, #0x10]
    // 0x88e950: stur            x1, [fp, #-0x10]
    // 0x88e954: LoadField: r2 = r0->field_13
    //     0x88e954: ldur            w2, [x0, #0x13]
    // 0x88e958: DecompressPointer r2
    //     0x88e958: add             x2, x2, HEAP, lsl #32
    // 0x88e95c: stur            x2, [fp, #-8]
    // 0x88e960: r0 = XmlEventAttribute()
    //     0x88e960: bl              #0x88e990  ; AllocateXmlEventAttributeStub -> XmlEventAttribute (size=0x14)
    // 0x88e964: ldr             x1, [fp, #0x18]
    // 0x88e968: StoreField: r0->field_7 = r1
    //     0x88e968: stur            w1, [x0, #7]
    // 0x88e96c: ldur            x1, [fp, #-0x10]
    // 0x88e970: StoreField: r0->field_b = r1
    //     0x88e970: stur            w1, [x0, #0xb]
    // 0x88e974: ldur            x1, [fp, #-8]
    // 0x88e978: StoreField: r0->field_f = r1
    //     0x88e978: stur            w1, [x0, #0xf]
    // 0x88e97c: LeaveFrame
    //     0x88e97c: mov             SP, fp
    //     0x88e980: ldp             fp, lr, [SP], #0x10
    // 0x88e984: ret
    //     0x88e984: ret             
    // 0x88e988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88e988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88e98c: b               #0x88e928
  }
  [closure] Parser<(String, XmlAttributeType)> attributeAssignment(dynamic) {
    // ** addr: 0x88ee88, size: 0x38
    // 0x88ee88: EnterFrame
    //     0x88ee88: stp             fp, lr, [SP, #-0x10]!
    //     0x88ee8c: mov             fp, SP
    // 0x88ee90: ldr             x0, [fp, #0x10]
    // 0x88ee94: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88ee94: ldur            w1, [x0, #0x17]
    // 0x88ee98: DecompressPointer r1
    //     0x88ee98: add             x1, x1, HEAP, lsl #32
    // 0x88ee9c: CheckStackOverflow
    //     0x88ee9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88eea0: cmp             SP, x16
    //     0x88eea4: b.ls            #0x88eeb8
    // 0x88eea8: r0 = attributeAssignment()
    //     0x88eea8: bl              #0x88eec0  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeAssignment
    // 0x88eeac: LeaveFrame
    //     0x88eeac: mov             SP, fp
    //     0x88eeb0: ldp             fp, lr, [SP], #0x10
    // 0x88eeb4: ret
    //     0x88eeb4: ret             
    // 0x88eeb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88eeb8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88eebc: b               #0x88eea8
  }
  _ attributeAssignment(/* No info */) {
    // ** addr: 0x88eec0, size: 0x124
    // 0x88eec0: EnterFrame
    //     0x88eec0: stp             fp, lr, [SP, #-0x10]!
    //     0x88eec4: mov             fp, SP
    // 0x88eec8: AllocStack(0x48)
    //     0x88eec8: sub             SP, SP, #0x48
    // 0x88eecc: SetupParameters(XmlEventParser this /* r1 => r0, fp-0x8 */)
    //     0x88eecc: mov             x0, x1
    //     0x88eed0: stur            x1, [fp, #-8]
    // 0x88eed4: CheckStackOverflow
    //     0x88eed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88eed8: cmp             SP, x16
    //     0x88eedc: b.ls            #0x88efdc
    // 0x88eee0: mov             x2, x0
    // 0x88eee4: r1 = Function 'spaceOptional':.
    //     0x88eee4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26698] AnonymousClosure: (0x88c650), in [package:xml/src/xml_events/parser.dart] XmlEventParser::spaceOptional (0x88c688)
    //     0x88eee8: ldr             x1, [x1, #0x698]
    // 0x88eeec: r0 = AllocateClosure()
    //     0x88eeec: bl              #0xec1630  ; AllocateClosureStub
    // 0x88eef0: stur            x0, [fp, #-0x10]
    // 0x88eef4: r16 = <String>
    //     0x88eef4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88eef8: stp             x0, x16, [SP]
    // 0x88eefc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88eefc: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88ef00: r0 = ref0()
    //     0x88ef00: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88ef04: r1 = "="
    //     0x88ef04: ldr             x1, [PP, #0xde8]  ; [pp+0xde8] "="
    // 0x88ef08: stur            x0, [fp, #-0x18]
    // 0x88ef0c: r0 = PredicateStringExtension.toParser()
    //     0x88ef0c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88ef10: stur            x0, [fp, #-0x20]
    // 0x88ef14: r16 = <String>
    //     0x88ef14: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88ef18: ldur            lr, [fp, #-0x10]
    // 0x88ef1c: stp             lr, x16, [SP]
    // 0x88ef20: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88ef20: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88ef24: r0 = ref0()
    //     0x88ef24: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88ef28: ldur            x2, [fp, #-8]
    // 0x88ef2c: r1 = Function 'attributeValue':.
    //     0x88ef2c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26728] AnonymousClosure: (0x88bd90), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributeValue (0x88bdc8)
    //     0x88ef30: ldr             x1, [x1, #0x728]
    // 0x88ef34: stur            x0, [fp, #-8]
    // 0x88ef38: r0 = AllocateClosure()
    //     0x88ef38: bl              #0xec1630  ; AllocateClosureStub
    // 0x88ef3c: r16 = <(String, XmlAttributeType)>
    //     0x88ef3c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88ef40: ldr             x16, [x16, #0x730]
    // 0x88ef44: stp             x0, x16, [SP]
    // 0x88ef48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88ef48: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88ef4c: r0 = ref0()
    //     0x88ef4c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88ef50: r16 = <String, String, String, (String, XmlAttributeType)>
    //     0x88ef50: add             x16, PP, #0x26, lsl #12  ; [pp+0x26790] TypeArguments: <String, String, String, (String, XmlAttributeType)>
    //     0x88ef54: ldr             x16, [x16, #0x790]
    // 0x88ef58: ldur            lr, [fp, #-0x18]
    // 0x88ef5c: stp             lr, x16, [SP, #0x18]
    // 0x88ef60: ldur            x16, [fp, #-0x20]
    // 0x88ef64: ldur            lr, [fp, #-8]
    // 0x88ef68: stp             lr, x16, [SP, #8]
    // 0x88ef6c: str             x0, [SP]
    // 0x88ef70: r4 = const [0x4, 0x4, 0x4, 0x4, null]
    //     0x88ef70: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a70] List(5) [0x4, 0x4, 0x4, 0x4, Null]
    //     0x88ef74: ldr             x4, [x4, #0xa70]
    // 0x88ef78: r0 = seq4()
    //     0x88ef78: bl              #0x88e484  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::seq4
    // 0x88ef7c: r1 = Function '<anonymous closure>':.
    //     0x88ef7c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b08] AnonymousClosure: (0xebd554), in [package:flutter/src/services/restoration.dart] RestorationBucket::_visitChildren (0x69ffb0)
    //     0x88ef80: ldr             x1, [x1, #0xb08]
    // 0x88ef84: r2 = Null
    //     0x88ef84: mov             x2, NULL
    // 0x88ef88: stur            x0, [fp, #-8]
    // 0x88ef8c: r0 = AllocateClosure()
    //     0x88ef8c: bl              #0xec1630  ; AllocateClosureStub
    // 0x88ef90: r16 = <String, String, String, (String, XmlAttributeType), (String, XmlAttributeType)>
    //     0x88ef90: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b10] TypeArguments: <String, String, String, (String, XmlAttributeType), (String, XmlAttributeType)>
    //     0x88ef94: ldr             x16, [x16, #0xb10]
    // 0x88ef98: ldur            lr, [fp, #-8]
    // 0x88ef9c: stp             lr, x16, [SP, #8]
    // 0x88efa0: str             x0, [SP]
    // 0x88efa4: r4 = const [0x5, 0x2, 0x2, 0x2, null]
    //     0x88efa4: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a88] List(5) [0x5, 0x2, 0x2, 0x2, Null]
    //     0x88efa8: ldr             x4, [x4, #0xa88]
    // 0x88efac: r0 = RecordParserExtension4.map4()
    //     0x88efac: bl              #0x88e34c  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::RecordParserExtension4.map4
    // 0x88efb0: r16 = <(String, XmlAttributeType)>
    //     0x88efb0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26730] TypeArguments: <(String, XmlAttributeType)>
    //     0x88efb4: ldr             x16, [x16, #0x730]
    // 0x88efb8: stp             x0, x16, [SP, #8]
    // 0x88efbc: r16 = Record (, Instance of 'XmlAttributeType')
    //     0x88efbc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b18] Record(String, XmlAttributeType) = ("", Obj!XmlAttributeType@e2d4b1)
    //     0x88efc0: ldr             x16, [x16, #0xb18]
    // 0x88efc4: str             x16, [SP]
    // 0x88efc8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88efc8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88efcc: r0 = OptionalParserExtension.optionalWith()
    //     0x88efcc: bl              #0x88e510  ; [package:petitparser/src/parser/combinator/optional.dart] ::OptionalParserExtension.optionalWith
    // 0x88efd0: LeaveFrame
    //     0x88efd0: mov             SP, fp
    //     0x88efd4: ldp             fp, lr, [SP], #0x10
    // 0x88efd8: ret
    //     0x88efd8: ret             
    // 0x88efdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88efdc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88efe0: b               #0x88eee0
  }
  [closure] Parser<XmlCDATAEvent> cdata(dynamic) {
    // ** addr: 0x88efe4, size: 0x38
    // 0x88efe4: EnterFrame
    //     0x88efe4: stp             fp, lr, [SP, #-0x10]!
    //     0x88efe8: mov             fp, SP
    // 0x88efec: ldr             x0, [fp, #0x10]
    // 0x88eff0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88eff0: ldur            w1, [x0, #0x17]
    // 0x88eff4: DecompressPointer r1
    //     0x88eff4: add             x1, x1, HEAP, lsl #32
    // 0x88eff8: CheckStackOverflow
    //     0x88eff8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88effc: cmp             SP, x16
    //     0x88f000: b.ls            #0x88f014
    // 0x88f004: r0 = cdata()
    //     0x88f004: bl              #0x88f01c  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::cdata
    // 0x88f008: LeaveFrame
    //     0x88f008: mov             SP, fp
    //     0x88f00c: ldp             fp, lr, [SP], #0x10
    // 0x88f010: ret
    //     0x88f010: ret             
    // 0x88f014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f014: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f018: b               #0x88f004
  }
  _ cdata(/* No info */) {
    // ** addr: 0x88f01c, size: 0xf0
    // 0x88f01c: EnterFrame
    //     0x88f01c: stp             fp, lr, [SP, #-0x10]!
    //     0x88f020: mov             fp, SP
    // 0x88f024: AllocStack(0x30)
    //     0x88f024: sub             SP, SP, #0x30
    // 0x88f028: CheckStackOverflow
    //     0x88f028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f02c: cmp             SP, x16
    //     0x88f030: b.ls            #0x88f104
    // 0x88f034: r1 = "<![CDATA["
    //     0x88f034: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b20] "<![CDATA["
    //     0x88f038: ldr             x1, [x1, #0xb20]
    // 0x88f03c: r0 = PredicateStringExtension.toParser()
    //     0x88f03c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f040: stur            x0, [fp, #-8]
    // 0x88f044: r0 = any()
    //     0x88f044: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88f048: r1 = "]]>"
    //     0x88f048: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b28] "]]>"
    //     0x88f04c: ldr             x1, [x1, #0xb28]
    // 0x88f050: stur            x0, [fp, #-0x10]
    // 0x88f054: r0 = PredicateStringExtension.toParser()
    //     0x88f054: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f058: r16 = <String>
    //     0x88f058: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f05c: ldur            lr, [fp, #-0x10]
    // 0x88f060: stp             lr, x16, [SP, #8]
    // 0x88f064: str             x0, [SP]
    // 0x88f068: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88f068: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88f06c: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88f06c: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88f070: r16 = <List<String>>
    //     0x88f070: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0x88f074: ldr             x16, [x16, #0xb40]
    // 0x88f078: stp             x0, x16, [SP, #8]
    // 0x88f07c: r16 = "\"]]>\" expected"
    //     0x88f07c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b30] "\"]]>\" expected"
    //     0x88f080: ldr             x16, [x16, #0xb30]
    // 0x88f084: str             x16, [SP]
    // 0x88f088: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88f088: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88f08c: r0 = FlattenParserExtension.flatten()
    //     0x88f08c: bl              #0x88b93c  ; [package:petitparser/src/parser/action/flatten.dart] ::FlattenParserExtension.flatten
    // 0x88f090: r1 = "]]>"
    //     0x88f090: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b28] "]]>"
    //     0x88f094: ldr             x1, [x1, #0xb28]
    // 0x88f098: stur            x0, [fp, #-0x10]
    // 0x88f09c: r0 = PredicateStringExtension.toParser()
    //     0x88f09c: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f0a0: r16 = <String, String, String>
    //     0x88f0a0: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88f0a4: ldr             x16, [x16, #0x810]
    // 0x88f0a8: ldur            lr, [fp, #-8]
    // 0x88f0ac: stp             lr, x16, [SP, #0x10]
    // 0x88f0b0: ldur            x16, [fp, #-0x10]
    // 0x88f0b4: stp             x0, x16, [SP]
    // 0x88f0b8: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88f0b8: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88f0bc: ldr             x4, [x4, #0xe10]
    // 0x88f0c0: r0 = seq3()
    //     0x88f0c0: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88f0c4: r1 = Function '<anonymous closure>':.
    //     0x88f0c4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b38] AnonymousClosure: (0x88f10c), in [package:xml/src/xml_events/parser.dart] XmlEventParser::cdata (0x88f01c)
    //     0x88f0c8: ldr             x1, [x1, #0xb38]
    // 0x88f0cc: r2 = Null
    //     0x88f0cc: mov             x2, NULL
    // 0x88f0d0: stur            x0, [fp, #-8]
    // 0x88f0d4: r0 = AllocateClosure()
    //     0x88f0d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f0d8: r16 = <String, String, String, XmlCDATAEvent>
    //     0x88f0d8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b40] TypeArguments: <String, String, String, XmlCDATAEvent>
    //     0x88f0dc: ldr             x16, [x16, #0xb40]
    // 0x88f0e0: ldur            lr, [fp, #-8]
    // 0x88f0e4: stp             lr, x16, [SP, #8]
    // 0x88f0e8: str             x0, [SP]
    // 0x88f0ec: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88f0ec: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88f0f0: ldr             x4, [x4, #0x718]
    // 0x88f0f4: r0 = RecordParserExtension3.map3()
    //     0x88f0f4: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88f0f8: LeaveFrame
    //     0x88f0f8: mov             SP, fp
    //     0x88f0fc: ldp             fp, lr, [SP], #0x10
    // 0x88f100: ret
    //     0x88f100: ret             
    // 0x88f104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f104: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f108: b               #0x88f034
  }
  [closure] XmlCDATAEvent <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x88f10c, size: 0x20
    // 0x88f10c: EnterFrame
    //     0x88f10c: stp             fp, lr, [SP, #-0x10]!
    //     0x88f110: mov             fp, SP
    // 0x88f114: r0 = XmlCDATAEvent()
    //     0x88f114: bl              #0x88f12c  ; AllocateXmlCDATAEventStub -> XmlCDATAEvent (size=0x18)
    // 0x88f118: ldr             x1, [fp, #0x18]
    // 0x88f11c: StoreField: r0->field_13 = r1
    //     0x88f11c: stur            w1, [x0, #0x13]
    // 0x88f120: LeaveFrame
    //     0x88f120: mov             SP, fp
    //     0x88f124: ldp             fp, lr, [SP], #0x10
    // 0x88f128: ret
    //     0x88f128: ret             
  }
  [closure] Parser<XmlCommentEvent> comment(dynamic) {
    // ** addr: 0x88f138, size: 0x38
    // 0x88f138: EnterFrame
    //     0x88f138: stp             fp, lr, [SP, #-0x10]!
    //     0x88f13c: mov             fp, SP
    // 0x88f140: ldr             x0, [fp, #0x10]
    // 0x88f144: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88f144: ldur            w1, [x0, #0x17]
    // 0x88f148: DecompressPointer r1
    //     0x88f148: add             x1, x1, HEAP, lsl #32
    // 0x88f14c: CheckStackOverflow
    //     0x88f14c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f150: cmp             SP, x16
    //     0x88f154: b.ls            #0x88f168
    // 0x88f158: r0 = comment()
    //     0x88f158: bl              #0x88f170  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::comment
    // 0x88f15c: LeaveFrame
    //     0x88f15c: mov             SP, fp
    //     0x88f160: ldp             fp, lr, [SP], #0x10
    // 0x88f164: ret
    //     0x88f164: ret             
    // 0x88f168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f168: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f16c: b               #0x88f158
  }
  _ comment(/* No info */) {
    // ** addr: 0x88f170, size: 0xf0
    // 0x88f170: EnterFrame
    //     0x88f170: stp             fp, lr, [SP, #-0x10]!
    //     0x88f174: mov             fp, SP
    // 0x88f178: AllocStack(0x30)
    //     0x88f178: sub             SP, SP, #0x30
    // 0x88f17c: CheckStackOverflow
    //     0x88f17c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f180: cmp             SP, x16
    //     0x88f184: b.ls            #0x88f258
    // 0x88f188: r1 = "<!--"
    //     0x88f188: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b48] "<!--"
    //     0x88f18c: ldr             x1, [x1, #0xb48]
    // 0x88f190: r0 = PredicateStringExtension.toParser()
    //     0x88f190: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f194: stur            x0, [fp, #-8]
    // 0x88f198: r0 = any()
    //     0x88f198: bl              #0x88ba98  ; [package:petitparser/src/parser/predicate/any.dart] ::any
    // 0x88f19c: r1 = "-->"
    //     0x88f19c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b50] "-->"
    //     0x88f1a0: ldr             x1, [x1, #0xb50]
    // 0x88f1a4: stur            x0, [fp, #-0x10]
    // 0x88f1a8: r0 = PredicateStringExtension.toParser()
    //     0x88f1a8: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f1ac: r16 = <String>
    //     0x88f1ac: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f1b0: ldur            lr, [fp, #-0x10]
    // 0x88f1b4: stp             lr, x16, [SP, #8]
    // 0x88f1b8: str             x0, [SP]
    // 0x88f1bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88f1bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88f1c0: r0 = LazyRepeatingParserExtension.starLazy()
    //     0x88f1c0: bl              #0x88b9b0  ; [package:petitparser/src/parser/repeater/lazy.dart] ::LazyRepeatingParserExtension.starLazy
    // 0x88f1c4: r16 = <List<String>>
    //     0x88f1c4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10b40] TypeArguments: <List<String>>
    //     0x88f1c8: ldr             x16, [x16, #0xb40]
    // 0x88f1cc: stp             x0, x16, [SP, #8]
    // 0x88f1d0: r16 = "\"-->\" expected"
    //     0x88f1d0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b58] "\"-->\" expected"
    //     0x88f1d4: ldr             x16, [x16, #0xb58]
    // 0x88f1d8: str             x16, [SP]
    // 0x88f1dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x88f1dc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x88f1e0: r0 = FlattenParserExtension.flatten()
    //     0x88f1e0: bl              #0x88b93c  ; [package:petitparser/src/parser/action/flatten.dart] ::FlattenParserExtension.flatten
    // 0x88f1e4: r1 = "-->"
    //     0x88f1e4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b50] "-->"
    //     0x88f1e8: ldr             x1, [x1, #0xb50]
    // 0x88f1ec: stur            x0, [fp, #-0x10]
    // 0x88f1f0: r0 = PredicateStringExtension.toParser()
    //     0x88f1f0: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f1f4: r16 = <String, String, String>
    //     0x88f1f4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10810] TypeArguments: <String, String, String>
    //     0x88f1f8: ldr             x16, [x16, #0x810]
    // 0x88f1fc: ldur            lr, [fp, #-8]
    // 0x88f200: stp             lr, x16, [SP, #0x10]
    // 0x88f204: ldur            x16, [fp, #-0x10]
    // 0x88f208: stp             x0, x16, [SP]
    // 0x88f20c: r4 = const [0x3, 0x3, 0x3, 0x3, null]
    //     0x88f20c: add             x4, PP, #0xd, lsl #12  ; [pp+0xde10] List(5) [0x3, 0x3, 0x3, 0x3, Null]
    //     0x88f210: ldr             x4, [x4, #0xe10]
    // 0x88f214: r0 = seq3()
    //     0x88f214: bl              #0x88b8bc  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::seq3
    // 0x88f218: r1 = Function '<anonymous closure>':.
    //     0x88f218: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b60] AnonymousClosure: (0x88f260), in [package:xml/src/xml_events/parser.dart] XmlEventParser::comment (0x88f170)
    //     0x88f21c: ldr             x1, [x1, #0xb60]
    // 0x88f220: r2 = Null
    //     0x88f220: mov             x2, NULL
    // 0x88f224: stur            x0, [fp, #-8]
    // 0x88f228: r0 = AllocateClosure()
    //     0x88f228: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f22c: r16 = <String, String, String, XmlCommentEvent>
    //     0x88f22c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b68] TypeArguments: <String, String, String, XmlCommentEvent>
    //     0x88f230: ldr             x16, [x16, #0xb68]
    // 0x88f234: ldur            lr, [fp, #-8]
    // 0x88f238: stp             lr, x16, [SP, #8]
    // 0x88f23c: str             x0, [SP]
    // 0x88f240: r4 = const [0x4, 0x2, 0x2, 0x2, null]
    //     0x88f240: add             x4, PP, #0x26, lsl #12  ; [pp+0x26718] List(5) [0x4, 0x2, 0x2, 0x2, Null]
    //     0x88f244: ldr             x4, [x4, #0x718]
    // 0x88f248: r0 = RecordParserExtension3.map3()
    //     0x88f248: bl              #0x88b790  ; [package:petitparser/src/parser/combinator/generated/sequence_3.dart] ::RecordParserExtension3.map3
    // 0x88f24c: LeaveFrame
    //     0x88f24c: mov             SP, fp
    //     0x88f250: ldp             fp, lr, [SP], #0x10
    // 0x88f254: ret
    //     0x88f254: ret             
    // 0x88f258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f258: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f25c: b               #0x88f188
  }
  [closure] XmlCommentEvent <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x88f260, size: 0x20
    // 0x88f260: EnterFrame
    //     0x88f260: stp             fp, lr, [SP, #-0x10]!
    //     0x88f264: mov             fp, SP
    // 0x88f268: r0 = XmlCommentEvent()
    //     0x88f268: bl              #0x88f280  ; AllocateXmlCommentEventStub -> XmlCommentEvent (size=0x18)
    // 0x88f26c: ldr             x1, [fp, #0x18]
    // 0x88f270: StoreField: r0->field_13 = r1
    //     0x88f270: stur            w1, [x0, #0x13]
    // 0x88f274: LeaveFrame
    //     0x88f274: mov             SP, fp
    //     0x88f278: ldp             fp, lr, [SP], #0x10
    // 0x88f27c: ret
    //     0x88f27c: ret             
  }
  [closure] Parser<XmlEndElementEvent> endElement(dynamic) {
    // ** addr: 0x88f28c, size: 0x38
    // 0x88f28c: EnterFrame
    //     0x88f28c: stp             fp, lr, [SP, #-0x10]!
    //     0x88f290: mov             fp, SP
    // 0x88f294: ldr             x0, [fp, #0x10]
    // 0x88f298: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88f298: ldur            w1, [x0, #0x17]
    // 0x88f29c: DecompressPointer r1
    //     0x88f29c: add             x1, x1, HEAP, lsl #32
    // 0x88f2a0: CheckStackOverflow
    //     0x88f2a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f2a4: cmp             SP, x16
    //     0x88f2a8: b.ls            #0x88f2bc
    // 0x88f2ac: r0 = endElement()
    //     0x88f2ac: bl              #0x88f2c4  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::endElement
    // 0x88f2b0: LeaveFrame
    //     0x88f2b0: mov             SP, fp
    //     0x88f2b4: ldp             fp, lr, [SP], #0x10
    // 0x88f2b8: ret
    //     0x88f2b8: ret             
    // 0x88f2bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f2bc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f2c0: b               #0x88f2ac
  }
  _ endElement(/* No info */) {
    // ** addr: 0x88f2c4, size: 0xf4
    // 0x88f2c4: EnterFrame
    //     0x88f2c4: stp             fp, lr, [SP, #-0x10]!
    //     0x88f2c8: mov             fp, SP
    // 0x88f2cc: AllocStack(0x40)
    //     0x88f2cc: sub             SP, SP, #0x40
    // 0x88f2d0: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88f2d0: mov             x2, x1
    //     0x88f2d4: stur            x1, [fp, #-8]
    // 0x88f2d8: CheckStackOverflow
    //     0x88f2d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f2dc: cmp             SP, x16
    //     0x88f2e0: b.ls            #0x88f3b0
    // 0x88f2e4: r1 = "</"
    //     0x88f2e4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b70] "</"
    //     0x88f2e8: ldr             x1, [x1, #0xb70]
    // 0x88f2ec: r0 = PredicateStringExtension.toParser()
    //     0x88f2ec: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f2f0: ldur            x2, [fp, #-8]
    // 0x88f2f4: r1 = Function 'nameToken':.
    //     0x88f2f4: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88f2f8: ldr             x1, [x1, #0x680]
    // 0x88f2fc: stur            x0, [fp, #-0x10]
    // 0x88f300: r0 = AllocateClosure()
    //     0x88f300: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f304: r16 = <String>
    //     0x88f304: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f308: stp             x0, x16, [SP]
    // 0x88f30c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88f30c: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88f310: r0 = ref0()
    //     0x88f310: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88f314: ldur            x2, [fp, #-8]
    // 0x88f318: r1 = Function 'spaceOptional':.
    //     0x88f318: add             x1, PP, #0x26, lsl #12  ; [pp+0x26698] AnonymousClosure: (0x88c650), in [package:xml/src/xml_events/parser.dart] XmlEventParser::spaceOptional (0x88c688)
    //     0x88f31c: ldr             x1, [x1, #0x698]
    // 0x88f320: stur            x0, [fp, #-8]
    // 0x88f324: r0 = AllocateClosure()
    //     0x88f324: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f328: r16 = <String>
    //     0x88f328: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f32c: stp             x0, x16, [SP]
    // 0x88f330: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88f330: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88f334: r0 = ref0()
    //     0x88f334: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88f338: r1 = ">"
    //     0x88f338: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88f33c: stur            x0, [fp, #-0x18]
    // 0x88f340: r0 = PredicateStringExtension.toParser()
    //     0x88f340: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f344: r16 = <String, String, String, String>
    //     0x88f344: add             x16, PP, #0x26, lsl #12  ; [pp+0x26710] TypeArguments: <String, String, String, String>
    //     0x88f348: ldr             x16, [x16, #0x710]
    // 0x88f34c: ldur            lr, [fp, #-0x10]
    // 0x88f350: stp             lr, x16, [SP, #0x18]
    // 0x88f354: ldur            x16, [fp, #-8]
    // 0x88f358: ldur            lr, [fp, #-0x18]
    // 0x88f35c: stp             lr, x16, [SP, #8]
    // 0x88f360: str             x0, [SP]
    // 0x88f364: r4 = const [0x4, 0x4, 0x4, 0x4, null]
    //     0x88f364: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a70] List(5) [0x4, 0x4, 0x4, 0x4, Null]
    //     0x88f368: ldr             x4, [x4, #0xa70]
    // 0x88f36c: r0 = seq4()
    //     0x88f36c: bl              #0x88e484  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::seq4
    // 0x88f370: r1 = Function '<anonymous closure>':.
    //     0x88f370: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b78] AnonymousClosure: (0x88f3b8), in [package:xml/src/xml_events/parser.dart] XmlEventParser::endElement (0x88f2c4)
    //     0x88f374: ldr             x1, [x1, #0xb78]
    // 0x88f378: r2 = Null
    //     0x88f378: mov             x2, NULL
    // 0x88f37c: stur            x0, [fp, #-8]
    // 0x88f380: r0 = AllocateClosure()
    //     0x88f380: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f384: r16 = <String, String, String, String, XmlEndElementEvent>
    //     0x88f384: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b80] TypeArguments: <String, String, String, String, XmlEndElementEvent>
    //     0x88f388: ldr             x16, [x16, #0xb80]
    // 0x88f38c: ldur            lr, [fp, #-8]
    // 0x88f390: stp             lr, x16, [SP, #8]
    // 0x88f394: str             x0, [SP]
    // 0x88f398: r4 = const [0x5, 0x2, 0x2, 0x2, null]
    //     0x88f398: add             x4, PP, #0x26, lsl #12  ; [pp+0x26a88] List(5) [0x5, 0x2, 0x2, 0x2, Null]
    //     0x88f39c: ldr             x4, [x4, #0xa88]
    // 0x88f3a0: r0 = RecordParserExtension4.map4()
    //     0x88f3a0: bl              #0x88e34c  ; [package:petitparser/src/parser/combinator/generated/sequence_4.dart] ::RecordParserExtension4.map4
    // 0x88f3a4: LeaveFrame
    //     0x88f3a4: mov             SP, fp
    //     0x88f3a8: ldp             fp, lr, [SP], #0x10
    // 0x88f3ac: ret
    //     0x88f3ac: ret             
    // 0x88f3b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f3b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f3b4: b               #0x88f2e4
  }
  [closure] XmlEndElementEvent <anonymous closure>(dynamic, String, String, String, String) {
    // ** addr: 0x88f3b8, size: 0x20
    // 0x88f3b8: EnterFrame
    //     0x88f3b8: stp             fp, lr, [SP, #-0x10]!
    //     0x88f3bc: mov             fp, SP
    // 0x88f3c0: r0 = XmlEndElementEvent()
    //     0x88f3c0: bl              #0x88f3d8  ; AllocateXmlEndElementEventStub -> XmlEndElementEvent (size=0x18)
    // 0x88f3c4: ldr             x1, [fp, #0x20]
    // 0x88f3c8: StoreField: r0->field_13 = r1
    //     0x88f3c8: stur            w1, [x0, #0x13]
    // 0x88f3cc: LeaveFrame
    //     0x88f3cc: mov             SP, fp
    //     0x88f3d0: ldp             fp, lr, [SP], #0x10
    // 0x88f3d4: ret
    //     0x88f3d4: ret             
  }
  [closure] Parser<XmlStartElementEvent> startElement(dynamic) {
    // ** addr: 0x88f3e4, size: 0x38
    // 0x88f3e4: EnterFrame
    //     0x88f3e4: stp             fp, lr, [SP, #-0x10]!
    //     0x88f3e8: mov             fp, SP
    // 0x88f3ec: ldr             x0, [fp, #0x10]
    // 0x88f3f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88f3f0: ldur            w1, [x0, #0x17]
    // 0x88f3f4: DecompressPointer r1
    //     0x88f3f4: add             x1, x1, HEAP, lsl #32
    // 0x88f3f8: CheckStackOverflow
    //     0x88f3f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f3fc: cmp             SP, x16
    //     0x88f400: b.ls            #0x88f414
    // 0x88f404: r0 = startElement()
    //     0x88f404: bl              #0x88f41c  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::startElement
    // 0x88f408: LeaveFrame
    //     0x88f408: mov             SP, fp
    //     0x88f40c: ldp             fp, lr, [SP], #0x10
    // 0x88f410: ret
    //     0x88f410: ret             
    // 0x88f414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f414: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f418: b               #0x88f404
  }
  _ startElement(/* No info */) {
    // ** addr: 0x88f41c, size: 0x194
    // 0x88f41c: EnterFrame
    //     0x88f41c: stp             fp, lr, [SP, #-0x10]!
    //     0x88f420: mov             fp, SP
    // 0x88f424: AllocStack(0x68)
    //     0x88f424: sub             SP, SP, #0x68
    // 0x88f428: SetupParameters(XmlEventParser this /* r1 => r2, fp-0x8 */)
    //     0x88f428: mov             x2, x1
    //     0x88f42c: stur            x1, [fp, #-8]
    // 0x88f430: CheckStackOverflow
    //     0x88f430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f434: cmp             SP, x16
    //     0x88f438: b.ls            #0x88f5a8
    // 0x88f43c: r1 = "<"
    //     0x88f43c: ldr             x1, [PP, #0x510]  ; [pp+0x510] "<"
    // 0x88f440: r0 = PredicateStringExtension.toParser()
    //     0x88f440: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f444: ldur            x2, [fp, #-8]
    // 0x88f448: r1 = Function 'nameToken':.
    //     0x88f448: add             x1, PP, #0x26, lsl #12  ; [pp+0x26680] AnonymousClosure: (0x88ce30), in [package:xml/src/xml_events/parser.dart] XmlEventParser::nameToken (0x88ce68)
    //     0x88f44c: ldr             x1, [x1, #0x680]
    // 0x88f450: stur            x0, [fp, #-0x10]
    // 0x88f454: r0 = AllocateClosure()
    //     0x88f454: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f458: r16 = <String>
    //     0x88f458: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f45c: stp             x0, x16, [SP]
    // 0x88f460: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88f460: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88f464: r0 = ref0()
    //     0x88f464: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88f468: ldur            x2, [fp, #-8]
    // 0x88f46c: r1 = Function 'attributes':.
    //     0x88f46c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ab8] AnonymousClosure: (0x88e720), in [package:xml/src/xml_events/parser.dart] XmlEventParser::attributes (0x88e758)
    //     0x88f470: ldr             x1, [x1, #0xab8]
    // 0x88f474: stur            x0, [fp, #-0x18]
    // 0x88f478: r0 = AllocateClosure()
    //     0x88f478: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f47c: r16 = <List<XmlEventAttribute>>
    //     0x88f47c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ac0] TypeArguments: <List<XmlEventAttribute>>
    //     0x88f480: ldr             x16, [x16, #0xac0]
    // 0x88f484: stp             x0, x16, [SP]
    // 0x88f488: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88f488: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88f48c: r0 = ref0()
    //     0x88f48c: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88f490: ldur            x2, [fp, #-8]
    // 0x88f494: r1 = Function 'spaceOptional':.
    //     0x88f494: add             x1, PP, #0x26, lsl #12  ; [pp+0x26698] AnonymousClosure: (0x88c650), in [package:xml/src/xml_events/parser.dart] XmlEventParser::spaceOptional (0x88c688)
    //     0x88f498: ldr             x1, [x1, #0x698]
    // 0x88f49c: stur            x0, [fp, #-8]
    // 0x88f4a0: r0 = AllocateClosure()
    //     0x88f4a0: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f4a4: r16 = <String>
    //     0x88f4a4: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f4a8: stp             x0, x16, [SP]
    // 0x88f4ac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x88f4ac: ldr             x4, [PP, #0xae8]  ; [pp+0xae8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x88f4b0: r0 = ref0()
    //     0x88f4b0: bl              #0x88a29c  ; [package:petitparser/src/definition/reference.dart] ::ref0
    // 0x88f4b4: r1 = ">"
    //     0x88f4b4: ldr             x1, [PP, #0x9a8]  ; [pp+0x9a8] ">"
    // 0x88f4b8: stur            x0, [fp, #-0x20]
    // 0x88f4bc: r0 = PredicateStringExtension.toParser()
    //     0x88f4bc: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f4c0: r1 = "/>"
    //     0x88f4c0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b88] "/>"
    //     0x88f4c4: ldr             x1, [x1, #0xb88]
    // 0x88f4c8: stur            x0, [fp, #-0x28]
    // 0x88f4cc: r0 = PredicateStringExtension.toParser()
    //     0x88f4cc: bl              #0x88aef0  ; [package:petitparser/src/parser/predicate/string.dart] ::PredicateStringExtension.toParser
    // 0x88f4d0: r1 = Null
    //     0x88f4d0: mov             x1, NULL
    // 0x88f4d4: r2 = 4
    //     0x88f4d4: movz            x2, #0x4
    // 0x88f4d8: stur            x0, [fp, #-0x30]
    // 0x88f4dc: r0 = AllocateArray()
    //     0x88f4dc: bl              #0xec22fc  ; AllocateArrayStub
    // 0x88f4e0: mov             x2, x0
    // 0x88f4e4: ldur            x0, [fp, #-0x28]
    // 0x88f4e8: stur            x2, [fp, #-0x38]
    // 0x88f4ec: StoreField: r2->field_f = r0
    //     0x88f4ec: stur            w0, [x2, #0xf]
    // 0x88f4f0: ldur            x0, [fp, #-0x30]
    // 0x88f4f4: StoreField: r2->field_13 = r0
    //     0x88f4f4: stur            w0, [x2, #0x13]
    // 0x88f4f8: r1 = <Parser<String>>
    //     0x88f4f8: add             x1, PP, #0x26, lsl #12  ; [pp+0x26b90] TypeArguments: <Parser<String>>
    //     0x88f4fc: ldr             x1, [x1, #0xb90]
    // 0x88f500: r0 = AllocateGrowableArray()
    //     0x88f500: bl              #0xec1230  ; AllocateGrowableArrayStub
    // 0x88f504: mov             x1, x0
    // 0x88f508: ldur            x0, [fp, #-0x38]
    // 0x88f50c: StoreField: r1->field_f = r0
    //     0x88f50c: stur            w0, [x1, #0xf]
    // 0x88f510: r0 = 4
    //     0x88f510: movz            x0, #0x4
    // 0x88f514: StoreField: r1->field_b = r0
    //     0x88f514: stur            w0, [x1, #0xb]
    // 0x88f518: r16 = <String>
    //     0x88f518: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f51c: stp             x1, x16, [SP, #8]
    // 0x88f520: r16 = Closure: (Failure, Failure) => Failure from Function 'selectFirst': static.
    //     0x88f520: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b98] Closure: (Failure, Failure) => Failure from Function 'selectFirst': static. (0x7e54fb28bacc)
    //     0x88f524: ldr             x16, [x16, #0xb98]
    // 0x88f528: str             x16, [SP]
    // 0x88f52c: r4 = const [0x1, 0x2, 0x2, 0x1, failureJoiner, 0x1, null]
    //     0x88f52c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26668] List(7) [0x1, 0x2, 0x2, 0x1, "failureJoiner", 0x1, Null]
    //     0x88f530: ldr             x4, [x4, #0x668]
    // 0x88f534: r0 = ChoiceIterableExtension.toChoiceParser()
    //     0x88f534: bl              #0x88a538  ; [package:petitparser/src/parser/combinator/choice.dart] ::ChoiceIterableExtension.toChoiceParser
    // 0x88f538: r16 = <String, String, List<XmlEventAttribute>, String, String>
    //     0x88f538: add             x16, PP, #0x26, lsl #12  ; [pp+0x26ba0] TypeArguments: <String, String, List<XmlEventAttribute>, String, String>
    //     0x88f53c: ldr             x16, [x16, #0xba0]
    // 0x88f540: ldur            lr, [fp, #-0x10]
    // 0x88f544: stp             lr, x16, [SP, #0x20]
    // 0x88f548: ldur            x16, [fp, #-0x18]
    // 0x88f54c: ldur            lr, [fp, #-8]
    // 0x88f550: stp             lr, x16, [SP, #0x10]
    // 0x88f554: ldur            x16, [fp, #-0x20]
    // 0x88f558: stp             x0, x16, [SP]
    // 0x88f55c: r4 = const [0x5, 0x5, 0x5, 0x5, null]
    //     0x88f55c: add             x4, PP, #0x26, lsl #12  ; [pp+0x26828] List(5) [0x5, 0x5, 0x5, 0x5, Null]
    //     0x88f560: ldr             x4, [x4, #0x828]
    // 0x88f564: r0 = seq5()
    //     0x88f564: bl              #0x88cbb4  ; [package:petitparser/src/parser/combinator/generated/sequence_5.dart] ::seq5
    // 0x88f568: r1 = Function '<anonymous closure>':.
    //     0x88f568: add             x1, PP, #0x26, lsl #12  ; [pp+0x26ba8] AnonymousClosure: (0x88f5b0), in [package:xml/src/xml_events/parser.dart] XmlEventParser::startElement (0x88f41c)
    //     0x88f56c: ldr             x1, [x1, #0xba8]
    // 0x88f570: r2 = Null
    //     0x88f570: mov             x2, NULL
    // 0x88f574: stur            x0, [fp, #-8]
    // 0x88f578: r0 = AllocateClosure()
    //     0x88f578: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f57c: r16 = <String, String, List<XmlEventAttribute>, String, String, XmlStartElementEvent>
    //     0x88f57c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26bb0] TypeArguments: <String, String, List<XmlEventAttribute>, String, String, XmlStartElementEvent>
    //     0x88f580: ldr             x16, [x16, #0xbb0]
    // 0x88f584: ldur            lr, [fp, #-8]
    // 0x88f588: stp             lr, x16, [SP, #8]
    // 0x88f58c: str             x0, [SP]
    // 0x88f590: r4 = const [0x6, 0x2, 0x2, 0x2, null]
    //     0x88f590: add             x4, PP, #0x26, lsl #12  ; [pp+0x26840] List(5) [0x6, 0x2, 0x2, 0x2, Null]
    //     0x88f594: ldr             x4, [x4, #0x840]
    // 0x88f598: r0 = RecordParserExtension5.map5()
    //     0x88f598: bl              #0x88ca74  ; [package:petitparser/src/parser/combinator/generated/sequence_5.dart] ::RecordParserExtension5.map5
    // 0x88f59c: LeaveFrame
    //     0x88f59c: mov             SP, fp
    //     0x88f5a0: ldp             fp, lr, [SP], #0x10
    // 0x88f5a4: ret
    //     0x88f5a4: ret             
    // 0x88f5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f5a8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f5ac: b               #0x88f43c
  }
  [closure] XmlStartElementEvent <anonymous closure>(dynamic, String, String, List<XmlEventAttribute>, String, String) {
    // ** addr: 0x88f5b0, size: 0x74
    // 0x88f5b0: EnterFrame
    //     0x88f5b0: stp             fp, lr, [SP, #-0x10]!
    //     0x88f5b4: mov             fp, SP
    // 0x88f5b8: AllocStack(0x18)
    //     0x88f5b8: sub             SP, SP, #0x18
    // 0x88f5bc: CheckStackOverflow
    //     0x88f5bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f5c0: cmp             SP, x16
    //     0x88f5c4: b.ls            #0x88f61c
    // 0x88f5c8: ldr             x0, [fp, #0x10]
    // 0x88f5cc: r1 = LoadClassIdInstr(r0)
    //     0x88f5cc: ldur            x1, [x0, #-1]
    //     0x88f5d0: ubfx            x1, x1, #0xc, #0x14
    // 0x88f5d4: r16 = "/>"
    //     0x88f5d4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26b88] "/>"
    //     0x88f5d8: ldr             x16, [x16, #0xb88]
    // 0x88f5dc: stp             x16, x0, [SP]
    // 0x88f5e0: mov             x0, x1
    // 0x88f5e4: mov             lr, x0
    // 0x88f5e8: ldr             lr, [x21, lr, lsl #3]
    // 0x88f5ec: blr             lr
    // 0x88f5f0: stur            x0, [fp, #-8]
    // 0x88f5f4: r0 = XmlStartElementEvent()
    //     0x88f5f4: bl              #0x88f624  ; AllocateXmlStartElementEventStub -> XmlStartElementEvent (size=0x20)
    // 0x88f5f8: ldr             x1, [fp, #0x28]
    // 0x88f5fc: StoreField: r0->field_13 = r1
    //     0x88f5fc: stur            w1, [x0, #0x13]
    // 0x88f600: ldr             x1, [fp, #0x20]
    // 0x88f604: ArrayStore: r0[0] = r1  ; List_4
    //     0x88f604: stur            w1, [x0, #0x17]
    // 0x88f608: ldur            x1, [fp, #-8]
    // 0x88f60c: StoreField: r0->field_1b = r1
    //     0x88f60c: stur            w1, [x0, #0x1b]
    // 0x88f610: LeaveFrame
    //     0x88f610: mov             SP, fp
    //     0x88f614: ldp             fp, lr, [SP], #0x10
    // 0x88f618: ret
    //     0x88f618: ret             
    // 0x88f61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f61c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f620: b               #0x88f5c8
  }
  [closure] Parser<XmlTextEvent> characterData(dynamic) {
    // ** addr: 0x88f630, size: 0x38
    // 0x88f630: EnterFrame
    //     0x88f630: stp             fp, lr, [SP, #-0x10]!
    //     0x88f634: mov             fp, SP
    // 0x88f638: ldr             x0, [fp, #0x10]
    // 0x88f63c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x88f63c: ldur            w1, [x0, #0x17]
    // 0x88f640: DecompressPointer r1
    //     0x88f640: add             x1, x1, HEAP, lsl #32
    // 0x88f644: CheckStackOverflow
    //     0x88f644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f648: cmp             SP, x16
    //     0x88f64c: b.ls            #0x88f660
    // 0x88f650: r0 = characterData()
    //     0x88f650: bl              #0x88f668  ; [package:xml/src/xml_events/parser.dart] XmlEventParser::characterData
    // 0x88f654: LeaveFrame
    //     0x88f654: mov             SP, fp
    //     0x88f658: ldp             fp, lr, [SP], #0x10
    // 0x88f65c: ret
    //     0x88f65c: ret             
    // 0x88f660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f660: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f664: b               #0x88f650
  }
  _ characterData(/* No info */) {
    // ** addr: 0x88f668, size: 0x94
    // 0x88f668: EnterFrame
    //     0x88f668: stp             fp, lr, [SP, #-0x10]!
    //     0x88f66c: mov             fp, SP
    // 0x88f670: AllocStack(0x28)
    //     0x88f670: sub             SP, SP, #0x28
    // 0x88f674: SetupParameters(XmlEventParser this /* r1 => r1, fp-0x8 */)
    //     0x88f674: stur            x1, [fp, #-8]
    // 0x88f678: CheckStackOverflow
    //     0x88f678: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88f67c: cmp             SP, x16
    //     0x88f680: b.ls            #0x88f6f4
    // 0x88f684: r1 = 1
    //     0x88f684: movz            x1, #0x1
    // 0x88f688: r0 = AllocateContext()
    //     0x88f688: bl              #0xec126c  ; AllocateContextStub
    // 0x88f68c: mov             x2, x0
    // 0x88f690: ldur            x0, [fp, #-8]
    // 0x88f694: stur            x2, [fp, #-0x10]
    // 0x88f698: StoreField: r2->field_f = r0
    //     0x88f698: stur            w0, [x2, #0xf]
    // 0x88f69c: r1 = <String>
    //     0x88f69c: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    // 0x88f6a0: r0 = XmlCharacterDataParser()
    //     0x88f6a0: bl              #0x88c094  ; AllocateXmlCharacterDataParserStub -> XmlCharacterDataParser (size=0x18)
    // 0x88f6a4: mov             x3, x0
    // 0x88f6a8: r0 = "<"
    //     0x88f6a8: ldr             x0, [PP, #0x510]  ; [pp+0x510] "<"
    // 0x88f6ac: stur            x3, [fp, #-8]
    // 0x88f6b0: StoreField: r3->field_b = r0
    //     0x88f6b0: stur            w0, [x3, #0xb]
    // 0x88f6b4: r0 = 1
    //     0x88f6b4: movz            x0, #0x1
    // 0x88f6b8: StoreField: r3->field_f = r0
    //     0x88f6b8: stur            x0, [x3, #0xf]
    // 0x88f6bc: ldur            x2, [fp, #-0x10]
    // 0x88f6c0: r1 = Function '<anonymous closure>':.
    //     0x88f6c0: add             x1, PP, #0x26, lsl #12  ; [pp+0x26bb8] AnonymousClosure: (0x88f6fc), in [package:xml/src/xml_events/parser.dart] XmlEventParser::characterData (0x88f668)
    //     0x88f6c4: ldr             x1, [x1, #0xbb8]
    // 0x88f6c8: r0 = AllocateClosure()
    //     0x88f6c8: bl              #0xec1630  ; AllocateClosureStub
    // 0x88f6cc: r16 = <String, XmlTextEvent>
    //     0x88f6cc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26bc0] TypeArguments: <String, XmlTextEvent>
    //     0x88f6d0: ldr             x16, [x16, #0xbc0]
    // 0x88f6d4: ldur            lr, [fp, #-8]
    // 0x88f6d8: stp             lr, x16, [SP, #8]
    // 0x88f6dc: str             x0, [SP]
    // 0x88f6e0: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x88f6e0: ldr             x4, [PP, #0x15b0]  ; [pp+0x15b0] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x88f6e4: r0 = MapParserExtension.map()
    //     0x88f6e4: bl              #0x88ab3c  ; [package:petitparser/src/parser/action/map.dart] ::MapParserExtension.map
    // 0x88f6e8: LeaveFrame
    //     0x88f6e8: mov             SP, fp
    //     0x88f6ec: ldp             fp, lr, [SP], #0x10
    // 0x88f6f0: ret
    //     0x88f6f0: ret             
    // 0x88f6f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88f6f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88f6f8: b               #0x88f684
  }
  [closure] XmlRawTextEvent <anonymous closure>(dynamic, String) {
    // ** addr: 0x88f6fc, size: 0x54
    // 0x88f6fc: EnterFrame
    //     0x88f6fc: stp             fp, lr, [SP, #-0x10]!
    //     0x88f700: mov             fp, SP
    // 0x88f704: AllocStack(0x8)
    //     0x88f704: sub             SP, SP, #8
    // 0x88f708: SetupParameters()
    //     0x88f708: ldr             x0, [fp, #0x18]
    //     0x88f70c: ldur            w1, [x0, #0x17]
    //     0x88f710: add             x1, x1, HEAP, lsl #32
    // 0x88f714: LoadField: r0 = r1->field_f
    //     0x88f714: ldur            w0, [x1, #0xf]
    // 0x88f718: DecompressPointer r0
    //     0x88f718: add             x0, x0, HEAP, lsl #32
    // 0x88f71c: LoadField: r1 = r0->field_7
    //     0x88f71c: ldur            w1, [x0, #7]
    // 0x88f720: DecompressPointer r1
    //     0x88f720: add             x1, x1, HEAP, lsl #32
    // 0x88f724: stur            x1, [fp, #-8]
    // 0x88f728: r0 = XmlRawTextEvent()
    //     0x88f728: bl              #0x88f750  ; AllocateXmlRawTextEventStub -> XmlRawTextEvent (size=0x20)
    // 0x88f72c: r1 = Sentinel
    //     0x88f72c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x88f730: StoreField: r0->field_1b = r1
    //     0x88f730: stur            w1, [x0, #0x1b]
    // 0x88f734: ldr             x1, [fp, #0x10]
    // 0x88f738: StoreField: r0->field_13 = r1
    //     0x88f738: stur            w1, [x0, #0x13]
    // 0x88f73c: ldur            x1, [fp, #-8]
    // 0x88f740: ArrayStore: r0[0] = r1  ; List_4
    //     0x88f740: stur            w1, [x0, #0x17]
    // 0x88f744: LeaveFrame
    //     0x88f744: mov             SP, fp
    //     0x88f748: ldp             fp, lr, [SP], #0x10
    // 0x88f74c: ret
    //     0x88f74c: ret             
  }
}
