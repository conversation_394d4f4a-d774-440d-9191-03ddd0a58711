// lib: , url: package:xml/src/xml_events/utils/conversion_sink.dart

// class id: 1051343, size: 0x8
class :: {
}

// class id: 192, size: 0x10, field offset: 0x8
class ConversionSink<X0> extends Object
    implements Sink<X0> {

  dynamic add(dynamic) {
    // ** addr: 0x755854, size: 0x24
    // 0x755854: EnterFrame
    //     0x755854: stp             fp, lr, [SP, #-0x10]!
    //     0x755858: mov             fp, SP
    // 0x75585c: ldr             x2, [fp, #0x10]
    // 0x755860: r1 = Function 'add':.
    //     0x755860: add             x1, PP, #0x31, lsl #12  ; [pp+0x31098] AnonymousClosure: (0x755878), in [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve (0xdbb244)
    //     0x755864: ldr             x1, [x1, #0x98]
    // 0x755868: r0 = AllocateClosure()
    //     0x755868: bl              #0xec1630  ; AllocateClosureStub
    // 0x75586c: LeaveFrame
    //     0x75586c: mov             SP, fp
    //     0x755870: ldp             fp, lr, [SP], #0x10
    // 0x755874: ret
    //     0x755874: ret             
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x755878, size: 0x3c
    // 0x755878: EnterFrame
    //     0x755878: stp             fp, lr, [SP, #-0x10]!
    //     0x75587c: mov             fp, SP
    // 0x755880: ldr             x0, [fp, #0x18]
    // 0x755884: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x755884: ldur            w1, [x0, #0x17]
    // 0x755888: DecompressPointer r1
    //     0x755888: add             x1, x1, HEAP, lsl #32
    // 0x75588c: CheckStackOverflow
    //     0x75588c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x755890: cmp             SP, x16
    //     0x755894: b.ls            #0x7558ac
    // 0x755898: ldr             x2, [fp, #0x10]
    // 0x75589c: r0 = resolve()
    //     0x75589c: bl              #0xdbb244  ; [package:flutter/src/widgets/widget_state.dart] _WidgetStatePropertyWith::resolve
    // 0x7558a0: LeaveFrame
    //     0x7558a0: mov             SP, fp
    //     0x7558a4: ldp             fp, lr, [SP], #0x10
    // 0x7558a8: ret
    //     0x7558a8: ret             
    // 0x7558ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7558ac: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7558b0: b               #0x755898
  }
}
