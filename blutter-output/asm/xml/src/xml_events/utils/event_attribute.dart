// lib: , url: package:xml/src/xml_events/utils/event_attribute.dart

// class id: 1051344, size: 0x8
class :: {
}

// class id: 189, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _XmlEventAttribute&Object&XmlNamed extends Object
     with XmlNamed {
}

// class id: 190, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _XmlEventAttribute&Object&XmlNamed&XmlHasParent extends _XmlEventAttribute&Object&XmlNamed
     with XmlHasParent {
}

// class id: 191, size: 0x14, field offset: 0x8
class XmlEventAttribute extends _XmlEventAttribute&Object&XmlNamed&XmlHasParent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf45f4, size: 0x6c
    // 0xbf45f4: EnterFrame
    //     0xbf45f4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf45f8: mov             fp, SP
    // 0xbf45fc: AllocStack(0x8)
    //     0xbf45fc: sub             SP, SP, #8
    // 0xbf4600: CheckStackOverflow
    //     0xbf4600: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4604: cmp             SP, x16
    //     0xbf4608: b.ls            #0xbf4658
    // 0xbf460c: ldr             x0, [fp, #0x10]
    // 0xbf4610: LoadField: r1 = r0->field_7
    //     0xbf4610: ldur            w1, [x0, #7]
    // 0xbf4614: DecompressPointer r1
    //     0xbf4614: add             x1, x1, HEAP, lsl #32
    // 0xbf4618: LoadField: r2 = r0->field_b
    //     0xbf4618: ldur            w2, [x0, #0xb]
    // 0xbf461c: DecompressPointer r2
    //     0xbf461c: add             x2, x2, HEAP, lsl #32
    // 0xbf4620: LoadField: r3 = r0->field_f
    //     0xbf4620: ldur            w3, [x0, #0xf]
    // 0xbf4624: DecompressPointer r3
    //     0xbf4624: add             x3, x3, HEAP, lsl #32
    // 0xbf4628: str             x3, [SP]
    // 0xbf462c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbf462c: ldr             x4, [PP, #0x7d0]  ; [pp+0x7d0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbf4630: r0 = hash()
    //     0xbf4630: bl              #0x6c438c  ; [dart:core] Object::hash
    // 0xbf4634: mov             x2, x0
    // 0xbf4638: r0 = BoxInt64Instr(r2)
    //     0xbf4638: sbfiz           x0, x2, #1, #0x1f
    //     0xbf463c: cmp             x2, x0, asr #1
    //     0xbf4640: b.eq            #0xbf464c
    //     0xbf4644: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf4648: stur            x2, [x0, #7]
    // 0xbf464c: LeaveFrame
    //     0xbf464c: mov             SP, fp
    //     0xbf4650: ldp             fp, lr, [SP], #0x10
    // 0xbf4654: ret
    //     0xbf4654: ret             
    // 0xbf4658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4658: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf465c: b               #0xbf460c
  }
  _ ==(/* No info */) {
    // ** addr: 0xd80614, size: 0x100
    // 0xd80614: EnterFrame
    //     0xd80614: stp             fp, lr, [SP, #-0x10]!
    //     0xd80618: mov             fp, SP
    // 0xd8061c: AllocStack(0x10)
    //     0xd8061c: sub             SP, SP, #0x10
    // 0xd80620: CheckStackOverflow
    //     0xd80620: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd80624: cmp             SP, x16
    //     0xd80628: b.ls            #0xd8070c
    // 0xd8062c: ldr             x1, [fp, #0x10]
    // 0xd80630: cmp             w1, NULL
    // 0xd80634: b.ne            #0xd80648
    // 0xd80638: r0 = false
    //     0xd80638: add             x0, NULL, #0x30  ; false
    // 0xd8063c: LeaveFrame
    //     0xd8063c: mov             SP, fp
    //     0xd80640: ldp             fp, lr, [SP], #0x10
    // 0xd80644: ret
    //     0xd80644: ret             
    // 0xd80648: r0 = 60
    //     0xd80648: movz            x0, #0x3c
    // 0xd8064c: branchIfSmi(r1, 0xd80658)
    //     0xd8064c: tbz             w1, #0, #0xd80658
    // 0xd80650: r0 = LoadClassIdInstr(r1)
    //     0xd80650: ldur            x0, [x1, #-1]
    //     0xd80654: ubfx            x0, x0, #0xc, #0x14
    // 0xd80658: cmp             x0, #0xbf
    // 0xd8065c: b.ne            #0xd806fc
    // 0xd80660: ldr             x2, [fp, #0x18]
    // 0xd80664: LoadField: r0 = r1->field_7
    //     0xd80664: ldur            w0, [x1, #7]
    // 0xd80668: DecompressPointer r0
    //     0xd80668: add             x0, x0, HEAP, lsl #32
    // 0xd8066c: LoadField: r3 = r2->field_7
    //     0xd8066c: ldur            w3, [x2, #7]
    // 0xd80670: DecompressPointer r3
    //     0xd80670: add             x3, x3, HEAP, lsl #32
    // 0xd80674: r4 = LoadClassIdInstr(r0)
    //     0xd80674: ldur            x4, [x0, #-1]
    //     0xd80678: ubfx            x4, x4, #0xc, #0x14
    // 0xd8067c: stp             x3, x0, [SP]
    // 0xd80680: mov             x0, x4
    // 0xd80684: mov             lr, x0
    // 0xd80688: ldr             lr, [x21, lr, lsl #3]
    // 0xd8068c: blr             lr
    // 0xd80690: tbnz            w0, #4, #0xd806fc
    // 0xd80694: ldr             x2, [fp, #0x18]
    // 0xd80698: ldr             x1, [fp, #0x10]
    // 0xd8069c: LoadField: r0 = r1->field_b
    //     0xd8069c: ldur            w0, [x1, #0xb]
    // 0xd806a0: DecompressPointer r0
    //     0xd806a0: add             x0, x0, HEAP, lsl #32
    // 0xd806a4: LoadField: r3 = r2->field_b
    //     0xd806a4: ldur            w3, [x2, #0xb]
    // 0xd806a8: DecompressPointer r3
    //     0xd806a8: add             x3, x3, HEAP, lsl #32
    // 0xd806ac: r4 = LoadClassIdInstr(r0)
    //     0xd806ac: ldur            x4, [x0, #-1]
    //     0xd806b0: ubfx            x4, x4, #0xc, #0x14
    // 0xd806b4: stp             x3, x0, [SP]
    // 0xd806b8: mov             x0, x4
    // 0xd806bc: mov             lr, x0
    // 0xd806c0: ldr             lr, [x21, lr, lsl #3]
    // 0xd806c4: blr             lr
    // 0xd806c8: tbnz            w0, #4, #0xd806fc
    // 0xd806cc: ldr             x2, [fp, #0x18]
    // 0xd806d0: ldr             x1, [fp, #0x10]
    // 0xd806d4: LoadField: r3 = r1->field_f
    //     0xd806d4: ldur            w3, [x1, #0xf]
    // 0xd806d8: DecompressPointer r3
    //     0xd806d8: add             x3, x3, HEAP, lsl #32
    // 0xd806dc: LoadField: r1 = r2->field_f
    //     0xd806dc: ldur            w1, [x2, #0xf]
    // 0xd806e0: DecompressPointer r1
    //     0xd806e0: add             x1, x1, HEAP, lsl #32
    // 0xd806e4: cmp             w3, w1
    // 0xd806e8: r16 = true
    //     0xd806e8: add             x16, NULL, #0x20  ; true
    // 0xd806ec: r17 = false
    //     0xd806ec: add             x17, NULL, #0x30  ; false
    // 0xd806f0: csel            x2, x16, x17, eq
    // 0xd806f4: mov             x0, x2
    // 0xd806f8: b               #0xd80700
    // 0xd806fc: r0 = false
    //     0xd806fc: add             x0, NULL, #0x30  ; false
    // 0xd80700: LeaveFrame
    //     0xd80700: mov             SP, fp
    //     0xd80704: ldp             fp, lr, [SP], #0x10
    // 0xd80708: ret
    //     0xd80708: ret             
    // 0xd8070c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8070c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd80710: b               #0xd8062c
  }
}
