// lib: xml_events, url: package:xml/xml_events.dart

// class id: 1051348, size: 0x8
class :: {

  static _ parseEvents(/* No info */) {
    // ** addr: 0xacf988, size: 0x128
    // 0xacf988: EnterFrame
    //     0xacf988: stp             fp, lr, [SP, #-0x10]!
    //     0xacf98c: mov             fp, SP
    // 0xacf990: AllocStack(0x18)
    //     0xacf990: sub             SP, SP, #0x18
    // 0xacf994: SetupParameters(dynamic _ /* r1 => r1, fp-0x18 */, {dynamic validateDocument = false /* r3, fp-0x10 */, dynamic validateNesting = false /* r0, fp-0x8 */})
    //     0xacf994: stur            x1, [fp, #-0x18]
    //     0xacf998: ldur            w0, [x4, #0x13]
    //     0xacf99c: ldur            w2, [x4, #0x1f]
    //     0xacf9a0: add             x2, x2, HEAP, lsl #32
    //     0xacf9a4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26cb8] "validateDocument"
    //     0xacf9a8: ldr             x16, [x16, #0xcb8]
    //     0xacf9ac: cmp             w2, w16
    //     0xacf9b0: b.ne            #0xacf9d4
    //     0xacf9b4: ldur            w2, [x4, #0x23]
    //     0xacf9b8: add             x2, x2, HEAP, lsl #32
    //     0xacf9bc: sub             w3, w0, w2
    //     0xacf9c0: add             x2, fp, w3, sxtw #2
    //     0xacf9c4: ldr             x2, [x2, #8]
    //     0xacf9c8: mov             x3, x2
    //     0xacf9cc: movz            x2, #0x1
    //     0xacf9d0: b               #0xacf9dc
    //     0xacf9d4: add             x3, NULL, #0x30  ; false
    //     0xacf9d8: movz            x2, #0
    //     0xacf9dc: stur            x3, [fp, #-0x10]
    //     0xacf9e0: lsl             x5, x2, #1
    //     0xacf9e4: lsl             w2, w5, #1
    //     0xacf9e8: add             w5, w2, #8
    //     0xacf9ec: add             x16, x4, w5, sxtw #1
    //     0xacf9f0: ldur            w6, [x16, #0xf]
    //     0xacf9f4: add             x6, x6, HEAP, lsl #32
    //     0xacf9f8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26cc0] "validateNesting"
    //     0xacf9fc: ldr             x16, [x16, #0xcc0]
    //     0xacfa00: cmp             w6, w16
    //     0xacfa04: b.ne            #0xacfa28
    //     0xacfa08: add             w5, w2, #0xa
    //     0xacfa0c: add             x16, x4, w5, sxtw #1
    //     0xacfa10: ldur            w2, [x16, #0xf]
    //     0xacfa14: add             x2, x2, HEAP, lsl #32
    //     0xacfa18: sub             w4, w0, w2
    //     0xacfa1c: add             x0, fp, w4, sxtw #2
    //     0xacfa20: ldr             x0, [x0, #8]
    //     0xacfa24: b               #0xacfa2c
    //     0xacfa28: add             x0, NULL, #0x30  ; false
    //     0xacfa2c: stur            x0, [fp, #-8]
    // 0xacfa30: CheckStackOverflow
    //     0xacfa30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacfa34: cmp             SP, x16
    //     0xacfa38: b.ls            #0xacfaa8
    // 0xacfa3c: r0 = InitLateStaticField(0xc04) // [package:xml/src/xml/entities/default_mapping.dart] ::defaultEntityMapping
    //     0xacfa3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xacfa40: ldr             x0, [x0, #0x1808]
    //     0xacfa44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xacfa48: cmp             w0, w16
    //     0xacfa4c: b.ne            #0xacfa5c
    //     0xacfa50: add             x2, PP, #0x26, lsl #12  ; [pp+0x26cc8] Field <::.defaultEntityMapping>: static late (offset: 0xc04)
    //     0xacfa54: ldr             x2, [x2, #0xcc8]
    //     0xacfa58: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0xacfa5c: r1 = <XmlEvent>
    //     0xacfa5c: add             x1, PP, #0x26, lsl #12  ; [pp+0x26590] TypeArguments: <XmlEvent>
    //     0xacfa60: ldr             x1, [x1, #0x590]
    // 0xacfa64: r0 = XmlEventIterable()
    //     0xacfa64: bl              #0xacfab0  ; AllocateXmlEventIterableStub -> XmlEventIterable (size=0x28)
    // 0xacfa68: ldur            x1, [fp, #-0x18]
    // 0xacfa6c: StoreField: r0->field_b = r1
    //     0xacfa6c: stur            w1, [x0, #0xb]
    // 0xacfa70: r1 = Instance_XmlDefaultEntityMapping
    //     0xacfa70: add             x1, PP, #0x26, lsl #12  ; [pp+0x265c0] Obj!XmlDefaultEntityMapping@e0bbf1
    //     0xacfa74: ldr             x1, [x1, #0x5c0]
    // 0xacfa78: StoreField: r0->field_f = r1
    //     0xacfa78: stur            w1, [x0, #0xf]
    // 0xacfa7c: ldur            x1, [fp, #-8]
    // 0xacfa80: StoreField: r0->field_13 = r1
    //     0xacfa80: stur            w1, [x0, #0x13]
    // 0xacfa84: ldur            x1, [fp, #-0x10]
    // 0xacfa88: ArrayStore: r0[0] = r1  ; List_4
    //     0xacfa88: stur            w1, [x0, #0x17]
    // 0xacfa8c: r1 = false
    //     0xacfa8c: add             x1, NULL, #0x30  ; false
    // 0xacfa90: StoreField: r0->field_1b = r1
    //     0xacfa90: stur            w1, [x0, #0x1b]
    // 0xacfa94: StoreField: r0->field_1f = r1
    //     0xacfa94: stur            w1, [x0, #0x1f]
    // 0xacfa98: StoreField: r0->field_23 = r1
    //     0xacfa98: stur            w1, [x0, #0x23]
    // 0xacfa9c: LeaveFrame
    //     0xacfa9c: mov             SP, fp
    //     0xacfaa0: ldp             fp, lr, [SP], #0x10
    // 0xacfaa4: ret
    //     0xacfaa4: ret             
    // 0xacfaa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacfaa8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacfaac: b               #0xacfa3c
  }
}
