// lib: , url: package:youtube_player_iframe/src/controller/youtube_player_controller.dart

// class id: 1051349, size: 0x8
class :: {
}

// class id: 186, size: 0x8, field offset: 0x8
//   const constructor, 
class YoutubeVideoState extends Object {

  factory _ YoutubeVideoState.fromJson(/* No info */) {
    // ** addr: 0x97b7d8, size: 0x1b0
    // 0x97b7d8: EnterFrame
    //     0x97b7d8: stp             fp, lr, [SP, #-0x10]!
    //     0x97b7dc: mov             fp, SP
    // 0x97b7e0: AllocStack(0x20)
    //     0x97b7e0: sub             SP, SP, #0x20
    // 0x97b7e4: SetupParameters(dynamic _ /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x97b7e4: mov             x0, x1
    //     0x97b7e8: mov             x1, x2
    // 0x97b7ec: CheckStackOverflow
    //     0x97b7ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b7f0: cmp             SP, x16
    //     0x97b7f4: b.ls            #0x97b980
    // 0x97b7f8: r0 = jsonDecode()
    //     0x97b7f8: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x97b7fc: stur            x0, [fp, #-8]
    // 0x97b800: r16 = "currentTime"
    //     0x97b800: add             x16, PP, #0x49, lsl #12  ; [pp+0x49be0] "currentTime"
    //     0x97b804: ldr             x16, [x16, #0xbe0]
    // 0x97b808: stp             x16, x0, [SP]
    // 0x97b80c: r4 = 0
    //     0x97b80c: movz            x4, #0
    // 0x97b810: ldr             x0, [SP, #8]
    // 0x97b814: r16 = UnlinkedCall_0x5f3c08
    //     0x97b814: add             x16, PP, #0x49, lsl #12  ; [pp+0x49be8] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x97b818: add             x16, x16, #0xbe8
    // 0x97b81c: ldp             x5, lr, [x16]
    // 0x97b820: blr             lr
    // 0x97b824: mov             x3, x0
    // 0x97b828: r2 = Null
    //     0x97b828: mov             x2, NULL
    // 0x97b82c: r1 = Null
    //     0x97b82c: mov             x1, NULL
    // 0x97b830: stur            x3, [fp, #-0x10]
    // 0x97b834: branchIfSmi(r0, 0x97b860)
    //     0x97b834: tbz             w0, #0, #0x97b860
    // 0x97b838: r4 = LoadClassIdInstr(r0)
    //     0x97b838: ldur            x4, [x0, #-1]
    //     0x97b83c: ubfx            x4, x4, #0xc, #0x14
    // 0x97b840: sub             x4, x4, #0x3c
    // 0x97b844: cmp             x4, #2
    // 0x97b848: b.ls            #0x97b860
    // 0x97b84c: r8 = num?
    //     0x97b84c: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x97b850: ldr             x8, [x8, #0x4f0]
    // 0x97b854: r3 = Null
    //     0x97b854: add             x3, PP, #0x49, lsl #12  ; [pp+0x49bf8] Null
    //     0x97b858: ldr             x3, [x3, #0xbf8]
    // 0x97b85c: r0 = DefaultNullableTypeTest()
    //     0x97b85c: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x97b860: ldur            x0, [fp, #-0x10]
    // 0x97b864: cmp             w0, NULL
    // 0x97b868: b.ne            #0x97b870
    // 0x97b86c: r0 = 0
    //     0x97b86c: movz            x0, #0
    // 0x97b870: stur            x0, [fp, #-0x10]
    // 0x97b874: ldur            x16, [fp, #-8]
    // 0x97b878: r30 = "loadedFraction"
    //     0x97b878: add             lr, PP, #0x49, lsl #12  ; [pp+0x49c08] "loadedFraction"
    //     0x97b87c: ldr             lr, [lr, #0xc08]
    // 0x97b880: stp             lr, x16, [SP]
    // 0x97b884: r4 = 0
    //     0x97b884: movz            x4, #0
    // 0x97b888: ldr             x0, [SP, #8]
    // 0x97b88c: r16 = UnlinkedCall_0x5f3c08
    //     0x97b88c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49c10] UnlinkedCall: 0x5f3c08 - SwitchableCallMissStub
    //     0x97b890: add             x16, x16, #0xc10
    // 0x97b894: ldp             x5, lr, [x16]
    // 0x97b898: blr             lr
    // 0x97b89c: mov             x3, x0
    // 0x97b8a0: r2 = Null
    //     0x97b8a0: mov             x2, NULL
    // 0x97b8a4: r1 = Null
    //     0x97b8a4: mov             x1, NULL
    // 0x97b8a8: stur            x3, [fp, #-8]
    // 0x97b8ac: branchIfSmi(r0, 0x97b8d8)
    //     0x97b8ac: tbz             w0, #0, #0x97b8d8
    // 0x97b8b0: r4 = LoadClassIdInstr(r0)
    //     0x97b8b0: ldur            x4, [x0, #-1]
    //     0x97b8b4: ubfx            x4, x4, #0xc, #0x14
    // 0x97b8b8: sub             x4, x4, #0x3c
    // 0x97b8bc: cmp             x4, #2
    // 0x97b8c0: b.ls            #0x97b8d8
    // 0x97b8c4: r8 = num?
    //     0x97b8c4: add             x8, PP, #0x20, lsl #12  ; [pp+0x204f0] Type: num?
    //     0x97b8c8: ldr             x8, [x8, #0x4f0]
    // 0x97b8cc: r3 = Null
    //     0x97b8cc: add             x3, PP, #0x49, lsl #12  ; [pp+0x49c20] Null
    //     0x97b8d0: ldr             x3, [x3, #0xc20]
    // 0x97b8d4: r0 = DefaultNullableTypeTest()
    //     0x97b8d4: bl              #0xec00a8  ; DefaultNullableTypeTestStub
    // 0x97b8d8: ldur            x0, [fp, #-8]
    // 0x97b8dc: cmp             w0, NULL
    // 0x97b8e0: b.ne            #0x97b8ec
    // 0x97b8e4: r1 = 0
    //     0x97b8e4: movz            x1, #0
    // 0x97b8e8: b               #0x97b8f0
    // 0x97b8ec: mov             x1, x0
    // 0x97b8f0: ldur            x0, [fp, #-0x10]
    // 0x97b8f4: stur            x1, [fp, #-8]
    // 0x97b8f8: r2 = 60
    //     0x97b8f8: movz            x2, #0x3c
    // 0x97b8fc: branchIfSmi(r0, 0x97b908)
    //     0x97b8fc: tbz             w0, #0, #0x97b908
    // 0x97b900: r2 = LoadClassIdInstr(r0)
    //     0x97b900: ldur            x2, [x0, #-1]
    //     0x97b904: ubfx            x2, x2, #0xc, #0x14
    // 0x97b908: r16 = 2000
    //     0x97b908: movz            x16, #0x7d0
    // 0x97b90c: stp             x16, x0, [SP]
    // 0x97b910: mov             x0, x2
    // 0x97b914: r0 = GDT[cid_x0 + -0xffd]()
    //     0x97b914: sub             lr, x0, #0xffd
    //     0x97b918: ldr             lr, [x21, lr, lsl #3]
    //     0x97b91c: blr             lr
    // 0x97b920: r1 = 60
    //     0x97b920: movz            x1, #0x3c
    // 0x97b924: branchIfSmi(r0, 0x97b930)
    //     0x97b924: tbz             w0, #0, #0x97b930
    // 0x97b928: r1 = LoadClassIdInstr(r0)
    //     0x97b928: ldur            x1, [x0, #-1]
    //     0x97b92c: ubfx            x1, x1, #0xc, #0x14
    // 0x97b930: mov             x16, x0
    // 0x97b934: mov             x0, x1
    // 0x97b938: mov             x1, x16
    // 0x97b93c: r0 = GDT[cid_x0 + -0xfa9]()
    //     0x97b93c: sub             lr, x0, #0xfa9
    //     0x97b940: ldr             lr, [x21, lr, lsl #3]
    //     0x97b944: blr             lr
    // 0x97b948: ldur            x0, [fp, #-8]
    // 0x97b94c: r1 = 60
    //     0x97b94c: movz            x1, #0x3c
    // 0x97b950: branchIfSmi(r0, 0x97b95c)
    //     0x97b950: tbz             w0, #0, #0x97b95c
    // 0x97b954: r1 = LoadClassIdInstr(r0)
    //     0x97b954: ldur            x1, [x0, #-1]
    //     0x97b958: ubfx            x1, x1, #0xc, #0x14
    // 0x97b95c: str             x0, [SP]
    // 0x97b960: mov             x0, x1
    // 0x97b964: r0 = GDT[cid_x0 + -0xffa]()
    //     0x97b964: sub             lr, x0, #0xffa
    //     0x97b968: ldr             lr, [x21, lr, lsl #3]
    //     0x97b96c: blr             lr
    // 0x97b970: r0 = YoutubeVideoState()
    //     0x97b970: bl              #0x97b9a8  ; AllocateYoutubeVideoStateStub -> YoutubeVideoState (size=0x8)
    // 0x97b974: LeaveFrame
    //     0x97b974: mov             SP, fp
    //     0x97b978: ldp             fp, lr, [SP], #0x10
    // 0x97b97c: ret
    //     0x97b97c: ret             
    // 0x97b980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b980: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b984: b               #0x97b7f8
  }
}

// class id: 188, size: 0x28, field offset: 0x8
class YoutubePlayerController extends Object
    implements YoutubePlayerIFrameAPI {

  late final WebViewController webViewController; // offset: 0x10
  late final YoutubePlayerEventHandler _eventHandler; // offset: 0x14

  _ listen(/* No info */) {
    // ** addr: 0x965810, size: 0xa8
    // 0x965810: EnterFrame
    //     0x965810: stp             fp, lr, [SP, #-0x10]!
    //     0x965814: mov             fp, SP
    // 0x965818: AllocStack(0x30)
    //     0x965818: sub             SP, SP, #0x30
    // 0x96581c: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x96581c: stur            x1, [fp, #-8]
    //     0x965820: stur            x2, [fp, #-0x10]
    // 0x965824: CheckStackOverflow
    //     0x965824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x965828: cmp             SP, x16
    //     0x96582c: b.ls            #0x9658b0
    // 0x965830: r1 = 2
    //     0x965830: movz            x1, #0x2
    // 0x965834: r0 = AllocateContext()
    //     0x965834: bl              #0xec126c  ; AllocateContextStub
    // 0x965838: mov             x2, x0
    // 0x96583c: ldur            x0, [fp, #-8]
    // 0x965840: stur            x2, [fp, #-0x18]
    // 0x965844: StoreField: r2->field_f = r0
    //     0x965844: stur            w0, [x2, #0xf]
    // 0x965848: ldur            x1, [fp, #-0x10]
    // 0x96584c: StoreField: r2->field_13 = r1
    //     0x96584c: stur            w1, [x2, #0x13]
    // 0x965850: LoadField: r3 = r0->field_1b
    //     0x965850: ldur            w3, [x0, #0x1b]
    // 0x965854: DecompressPointer r3
    //     0x965854: add             x3, x3, HEAP, lsl #32
    // 0x965858: stur            x3, [fp, #-0x10]
    // 0x96585c: LoadField: r1 = r3->field_7
    //     0x96585c: ldur            w1, [x3, #7]
    // 0x965860: DecompressPointer r1
    //     0x965860: add             x1, x1, HEAP, lsl #32
    // 0x965864: r0 = _BroadcastStream()
    //     0x965864: bl              #0x836570  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x965868: mov             x3, x0
    // 0x96586c: ldur            x0, [fp, #-0x10]
    // 0x965870: stur            x3, [fp, #-8]
    // 0x965874: StoreField: r3->field_b = r0
    //     0x965874: stur            w0, [x3, #0xb]
    // 0x965878: ldur            x2, [fp, #-0x18]
    // 0x96587c: r1 = Function '<anonymous closure>':.
    //     0x96587c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48fe8] AnonymousClosure: (0x9658b8), in [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::listen (0x965810)
    //     0x965880: ldr             x1, [x1, #0xfe8]
    // 0x965884: r0 = AllocateClosure()
    //     0x965884: bl              #0xec1630  ; AllocateClosureStub
    // 0x965888: stp             NULL, NULL, [SP, #8]
    // 0x96588c: str             NULL, [SP]
    // 0x965890: ldur            x1, [fp, #-8]
    // 0x965894: mov             x2, x0
    // 0x965898: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0x965898: add             x4, PP, #0xd, lsl #12  ; [pp+0xdf78] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    //     0x96589c: ldr             x4, [x4, #0xf78]
    // 0x9658a0: r0 = listen()
    //     0x9658a0: bl              #0xd0b0b4  ; [dart:async] _StreamImpl::listen
    // 0x9658a4: LeaveFrame
    //     0x9658a4: mov             SP, fp
    //     0x9658a8: ldp             fp, lr, [SP], #0x10
    // 0x9658ac: ret
    //     0x9658ac: ret             
    // 0x9658b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9658b0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9658b4: b               #0x965830
  }
  [closure] void <anonymous closure>(dynamic, YoutubePlayerValue) {
    // ** addr: 0x9658b8, size: 0x80
    // 0x9658b8: EnterFrame
    //     0x9658b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9658bc: mov             fp, SP
    // 0x9658c0: AllocStack(0x10)
    //     0x9658c0: sub             SP, SP, #0x10
    // 0x9658c4: SetupParameters()
    //     0x9658c4: ldr             x0, [fp, #0x18]
    //     0x9658c8: ldur            w1, [x0, #0x17]
    //     0x9658cc: add             x1, x1, HEAP, lsl #32
    // 0x9658d0: CheckStackOverflow
    //     0x9658d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9658d4: cmp             SP, x16
    //     0x9658d8: b.ls            #0x965930
    // 0x9658dc: LoadField: r2 = r1->field_f
    //     0x9658dc: ldur            w2, [x1, #0xf]
    // 0x9658e0: DecompressPointer r2
    //     0x9658e0: add             x2, x2, HEAP, lsl #32
    // 0x9658e4: ldr             x0, [fp, #0x10]
    // 0x9658e8: StoreField: r2->field_1f = r0
    //     0x9658e8: stur            w0, [x2, #0x1f]
    //     0x9658ec: ldurb           w16, [x2, #-1]
    //     0x9658f0: ldurb           w17, [x0, #-1]
    //     0x9658f4: and             x16, x17, x16, lsr #2
    //     0x9658f8: tst             x16, HEAP, lsr #32
    //     0x9658fc: b.eq            #0x965904
    //     0x965900: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x965904: LoadField: r0 = r1->field_13
    //     0x965904: ldur            w0, [x1, #0x13]
    // 0x965908: DecompressPointer r0
    //     0x965908: add             x0, x0, HEAP, lsl #32
    // 0x96590c: ldr             x16, [fp, #0x10]
    // 0x965910: stp             x16, x0, [SP]
    // 0x965914: ClosureCall
    //     0x965914: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x965918: ldur            x2, [x0, #0x1f]
    //     0x96591c: blr             x2
    // 0x965920: r0 = Null
    //     0x965920: mov             x0, NULL
    // 0x965924: LeaveFrame
    //     0x965924: mov             SP, fp
    //     0x965928: ldp             fp, lr, [SP], #0x10
    // 0x96592c: ret
    //     0x96592c: ret             
    // 0x965930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x965930: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x965934: b               #0x9658dc
  }
  factory _ YoutubePlayerController.fromVideoId(/* No info */) {
    // ** addr: 0x965958, size: 0x80
    // 0x965958: EnterFrame
    //     0x965958: stp             fp, lr, [SP, #-0x10]!
    //     0x96595c: mov             fp, SP
    // 0x965960: AllocStack(0x18)
    //     0x965960: sub             SP, SP, #0x18
    // 0x965964: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r2, fp-0x18 */)
    //     0x965964: mov             x0, x2
    //     0x965968: stur            x2, [fp, #-8]
    //     0x96596c: mov             x2, x5
    //     0x965970: stur            x3, [fp, #-0x10]
    //     0x965974: stur            x5, [fp, #-0x18]
    // 0x965978: CheckStackOverflow
    //     0x965978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96597c: cmp             SP, x16
    //     0x965980: b.ls            #0x9659d0
    // 0x965984: r0 = YoutubePlayerController()
    //     0x965984: bl              #0x97d444  ; AllocateYoutubePlayerControllerStub -> YoutubePlayerController (size=0x28)
    // 0x965988: mov             x1, x0
    // 0x96598c: ldur            x2, [fp, #-0x18]
    // 0x965990: ldur            x3, [fp, #-0x10]
    // 0x965994: stur            x0, [fp, #-0x10]
    // 0x965998: r0 = YoutubePlayerController()
    //     0x965998: bl              #0x970b18  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::YoutubePlayerController
    // 0x96599c: ldur            x0, [fp, #-8]
    // 0x9659a0: tbnz            w0, #4, #0x9659b4
    // 0x9659a4: ldur            x1, [fp, #-0x10]
    // 0x9659a8: ldur            x2, [fp, #-0x18]
    // 0x9659ac: r0 = loadVideoById()
    //     0x9659ac: bl              #0x970a74  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::loadVideoById
    // 0x9659b0: b               #0x9659c0
    // 0x9659b4: ldur            x1, [fp, #-0x10]
    // 0x9659b8: ldur            x2, [fp, #-0x18]
    // 0x9659bc: r0 = cueVideoById()
    //     0x9659bc: bl              #0x9659d8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::cueVideoById
    // 0x9659c0: ldur            x0, [fp, #-0x10]
    // 0x9659c4: LeaveFrame
    //     0x9659c4: mov             SP, fp
    //     0x9659c8: ldp             fp, lr, [SP], #0x10
    // 0x9659cc: ret
    //     0x9659cc: ret             
    // 0x9659d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9659d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9659d4: b               #0x965984
  }
  _ cueVideoById(/* No info */) {
    // ** addr: 0x9659d8, size: 0xa4
    // 0x9659d8: EnterFrame
    //     0x9659d8: stp             fp, lr, [SP, #-0x10]!
    //     0x9659dc: mov             fp, SP
    // 0x9659e0: AllocStack(0x20)
    //     0x9659e0: sub             SP, SP, #0x20
    // 0x9659e4: SetupParameters(YoutubePlayerController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9659e4: mov             x3, x1
    //     0x9659e8: mov             x0, x2
    //     0x9659ec: stur            x1, [fp, #-8]
    //     0x9659f0: stur            x2, [fp, #-0x10]
    // 0x9659f4: CheckStackOverflow
    //     0x9659f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9659f8: cmp             SP, x16
    //     0x9659fc: b.ls            #0x965a74
    // 0x965a00: r1 = Null
    //     0x965a00: mov             x1, NULL
    // 0x965a04: r2 = 12
    //     0x965a04: movz            x2, #0xc
    // 0x965a08: r0 = AllocateArray()
    //     0x965a08: bl              #0xec22fc  ; AllocateArrayStub
    // 0x965a0c: r16 = "videoId"
    //     0x965a0c: add             x16, PP, #0x48, lsl #12  ; [pp+0x48ff0] "videoId"
    //     0x965a10: ldr             x16, [x16, #0xff0]
    // 0x965a14: StoreField: r0->field_f = r16
    //     0x965a14: stur            w16, [x0, #0xf]
    // 0x965a18: ldur            x1, [fp, #-0x10]
    // 0x965a1c: StoreField: r0->field_13 = r1
    //     0x965a1c: stur            w1, [x0, #0x13]
    // 0x965a20: r16 = "startSeconds"
    //     0x965a20: add             x16, PP, #0x48, lsl #12  ; [pp+0x48ff8] "startSeconds"
    //     0x965a24: ldr             x16, [x16, #0xff8]
    // 0x965a28: ArrayStore: r0[0] = r16  ; List_4
    //     0x965a28: stur            w16, [x0, #0x17]
    // 0x965a2c: StoreField: r0->field_1b = rNULL
    //     0x965a2c: stur            NULL, [x0, #0x1b]
    // 0x965a30: r16 = "endSeconds"
    //     0x965a30: add             x16, PP, #0x49, lsl #12  ; [pp+0x49000] "endSeconds"
    //     0x965a34: ldr             x16, [x16]
    // 0x965a38: StoreField: r0->field_1f = r16
    //     0x965a38: stur            w16, [x0, #0x1f]
    // 0x965a3c: StoreField: r0->field_23 = rNULL
    //     0x965a3c: stur            NULL, [x0, #0x23]
    // 0x965a40: r16 = <String, dynamic>
    //     0x965a40: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x965a44: stp             x0, x16, [SP]
    // 0x965a48: r0 = Map._fromLiteral()
    //     0x965a48: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x965a4c: str             x0, [SP]
    // 0x965a50: ldur            x1, [fp, #-8]
    // 0x965a54: r2 = "cueVideoById"
    //     0x965a54: add             x2, PP, #0x49, lsl #12  ; [pp+0x49008] "cueVideoById"
    //     0x965a58: ldr             x2, [x2, #8]
    // 0x965a5c: r4 = const [0, 0x3, 0x1, 0x2, data, 0x2, null]
    //     0x965a5c: add             x4, PP, #0x12, lsl #12  ; [pp+0x123b0] List(7) [0, 0x3, 0x1, 0x2, "data", 0x2, Null]
    //     0x965a60: ldr             x4, [x4, #0x3b0]
    // 0x965a64: r0 = _run()
    //     0x965a64: bl              #0x965a7c  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_run
    // 0x965a68: LeaveFrame
    //     0x965a68: mov             SP, fp
    //     0x965a6c: ldp             fp, lr, [SP], #0x10
    // 0x965a70: ret
    //     0x965a70: ret             
    // 0x965a74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x965a74: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x965a78: b               #0x965a00
  }
  _ _run(/* No info */) async {
    // ** addr: 0x965a7c, size: 0x130
    // 0x965a7c: EnterFrame
    //     0x965a7c: stp             fp, lr, [SP, #-0x10]!
    //     0x965a80: mov             fp, SP
    // 0x965a84: AllocStack(0x30)
    //     0x965a84: sub             SP, SP, #0x30
    // 0x965a88: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */, {dynamic data = Null /* r3, fp-0x10 */})
    //     0x965a88: stur            NULL, [fp, #-8]
    //     0x965a8c: stur            x1, [fp, #-0x18]
    //     0x965a90: stur            x2, [fp, #-0x20]
    //     0x965a94: ldur            w0, [x4, #0x13]
    //     0x965a98: ldur            w3, [x4, #0x1f]
    //     0x965a9c: add             x3, x3, HEAP, lsl #32
    //     0x965aa0: ldr             x16, [PP, #0xfb0]  ; [pp+0xfb0] "data"
    //     0x965aa4: cmp             w3, w16
    //     0x965aa8: b.ne            #0x965ac8
    //     0x965aac: ldur            w3, [x4, #0x23]
    //     0x965ab0: add             x3, x3, HEAP, lsl #32
    //     0x965ab4: sub             w4, w0, w3
    //     0x965ab8: add             x0, fp, w4, sxtw #2
    //     0x965abc: ldr             x0, [x0, #8]
    //     0x965ac0: mov             x3, x0
    //     0x965ac4: b               #0x965acc
    //     0x965ac8: mov             x3, NULL
    //     0x965acc: stur            x3, [fp, #-0x10]
    // 0x965ad0: CheckStackOverflow
    //     0x965ad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x965ad4: cmp             SP, x16
    //     0x965ad8: b.ls            #0x965b98
    // 0x965adc: InitAsync() -> Future<void?>
    //     0x965adc: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x965ae0: bl              #0x661298  ; InitAsyncStub
    // 0x965ae4: ldur            x1, [fp, #-0x18]
    // 0x965ae8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x965ae8: ldur            w0, [x1, #0x17]
    // 0x965aec: DecompressPointer r0
    //     0x965aec: add             x0, x0, HEAP, lsl #32
    // 0x965af0: LoadField: r2 = r0->field_b
    //     0x965af0: ldur            w2, [x0, #0xb]
    // 0x965af4: DecompressPointer r2
    //     0x965af4: add             x2, x2, HEAP, lsl #32
    // 0x965af8: mov             x0, x2
    // 0x965afc: stur            x2, [fp, #-0x28]
    // 0x965b00: r0 = Await()
    //     0x965b00: bl              #0x661044  ; AwaitStub
    // 0x965b04: ldur            x1, [fp, #-0x18]
    // 0x965b08: ldur            x2, [fp, #-0x10]
    // 0x965b0c: r0 = _prepareData()
    //     0x965b0c: bl              #0x9709d4  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_prepareData
    // 0x965b10: mov             x1, x0
    // 0x965b14: stur            x1, [fp, #-0x10]
    // 0x965b18: r0 = Await()
    //     0x965b18: bl              #0x661044  ; AwaitStub
    // 0x965b1c: mov             x3, x0
    // 0x965b20: ldur            x0, [fp, #-0x18]
    // 0x965b24: stur            x3, [fp, #-0x28]
    // 0x965b28: LoadField: r4 = r0->field_f
    //     0x965b28: ldur            w4, [x0, #0xf]
    // 0x965b2c: DecompressPointer r4
    //     0x965b2c: add             x4, x4, HEAP, lsl #32
    // 0x965b30: r16 = Sentinel
    //     0x965b30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x965b34: cmp             w4, w16
    // 0x965b38: b.eq            #0x965ba0
    // 0x965b3c: stur            x4, [fp, #-0x10]
    // 0x965b40: r1 = Null
    //     0x965b40: mov             x1, NULL
    // 0x965b44: r2 = 10
    //     0x965b44: movz            x2, #0xa
    // 0x965b48: r0 = AllocateArray()
    //     0x965b48: bl              #0xec22fc  ; AllocateArrayStub
    // 0x965b4c: r16 = "player."
    //     0x965b4c: add             x16, PP, #0x48, lsl #12  ; [pp+0x48f38] "player."
    //     0x965b50: ldr             x16, [x16, #0xf38]
    // 0x965b54: StoreField: r0->field_f = r16
    //     0x965b54: stur            w16, [x0, #0xf]
    // 0x965b58: ldur            x1, [fp, #-0x20]
    // 0x965b5c: StoreField: r0->field_13 = r1
    //     0x965b5c: stur            w1, [x0, #0x13]
    // 0x965b60: r16 = "("
    //     0x965b60: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0x965b64: ldr             x16, [x16, #0xf08]
    // 0x965b68: ArrayStore: r0[0] = r16  ; List_4
    //     0x965b68: stur            w16, [x0, #0x17]
    // 0x965b6c: ldur            x1, [fp, #-0x28]
    // 0x965b70: StoreField: r0->field_1b = r1
    //     0x965b70: stur            w1, [x0, #0x1b]
    // 0x965b74: r16 = ");"
    //     0x965b74: add             x16, PP, #0x48, lsl #12  ; [pp+0x48f40] ");"
    //     0x965b78: ldr             x16, [x16, #0xf40]
    // 0x965b7c: StoreField: r0->field_1f = r16
    //     0x965b7c: stur            w16, [x0, #0x1f]
    // 0x965b80: str             x0, [SP]
    // 0x965b84: r0 = _interpolate()
    //     0x965b84: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x965b88: ldur            x1, [fp, #-0x10]
    // 0x965b8c: mov             x2, x0
    // 0x965b90: r0 = runJavaScript()
    //     0x965b90: bl              #0x965bac  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::runJavaScript
    // 0x965b94: r0 = ReturnAsync()
    //     0x965b94: b               #0x6576a4  ; ReturnAsyncStub
    // 0x965b98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x965b98: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x965b9c: b               #0x965adc
    // 0x965ba0: r9 = webViewController
    //     0x965ba0: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f20] Field <YoutubePlayerController.webViewController>: late final (offset: 0x10)
    //     0x965ba4: ldr             x9, [x9, #0xf20]
    // 0x965ba8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x965ba8: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _prepareData(/* No info */) async {
    // ** addr: 0x9709d4, size: 0xa0
    // 0x9709d4: EnterFrame
    //     0x9709d4: stp             fp, lr, [SP, #-0x10]!
    //     0x9709d8: mov             fp, SP
    // 0x9709dc: AllocStack(0x18)
    //     0x9709dc: sub             SP, SP, #0x18
    // 0x9709e0: SetupParameters(YoutubePlayerController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x9709e0: stur            NULL, [fp, #-8]
    //     0x9709e4: stur            x1, [fp, #-0x10]
    //     0x9709e8: mov             x16, x2
    //     0x9709ec: mov             x2, x1
    //     0x9709f0: mov             x1, x16
    //     0x9709f4: stur            x1, [fp, #-0x18]
    // 0x9709f8: CheckStackOverflow
    //     0x9709f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9709fc: cmp             SP, x16
    //     0x970a00: b.ls            #0x970a60
    // 0x970a04: InitAsync() -> Future<String>
    //     0x970a04: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0x970a08: bl              #0x661298  ; InitAsyncStub
    // 0x970a0c: ldur            x0, [fp, #-0x10]
    // 0x970a10: LoadField: r1 = r0->field_13
    //     0x970a10: ldur            w1, [x0, #0x13]
    // 0x970a14: DecompressPointer r1
    //     0x970a14: add             x1, x1, HEAP, lsl #32
    // 0x970a18: r16 = Sentinel
    //     0x970a18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x970a1c: cmp             w1, w16
    // 0x970a20: b.eq            #0x970a68
    // 0x970a24: LoadField: r0 = r1->field_f
    //     0x970a24: ldur            w0, [x1, #0xf]
    // 0x970a28: DecompressPointer r0
    //     0x970a28: add             x0, x0, HEAP, lsl #32
    // 0x970a2c: LoadField: r1 = r0->field_b
    //     0x970a2c: ldur            w1, [x0, #0xb]
    // 0x970a30: DecompressPointer r1
    //     0x970a30: add             x1, x1, HEAP, lsl #32
    // 0x970a34: mov             x0, x1
    // 0x970a38: stur            x1, [fp, #-0x10]
    // 0x970a3c: r0 = Await()
    //     0x970a3c: bl              #0x661044  ; AwaitStub
    // 0x970a40: ldur            x1, [fp, #-0x18]
    // 0x970a44: cmp             w1, NULL
    // 0x970a48: b.ne            #0x970a54
    // 0x970a4c: r0 = ""
    //     0x970a4c: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x970a50: b               #0x970a5c
    // 0x970a54: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x970a54: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x970a58: r0 = jsonEncode()
    //     0x970a58: bl              #0x727b0c  ; [dart:convert] ::jsonEncode
    // 0x970a5c: r0 = ReturnAsyncNotFuture()
    //     0x970a5c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x970a60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x970a60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x970a64: b               #0x970a04
    // 0x970a68: r9 = _eventHandler
    //     0x970a68: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f28] Field <YoutubePlayerController._eventHandler@2476072021>: late final (offset: 0x14)
    //     0x970a6c: ldr             x9, [x9, #0xf28]
    // 0x970a70: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x970a70: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ loadVideoById(/* No info */) {
    // ** addr: 0x970a74, size: 0xa4
    // 0x970a74: EnterFrame
    //     0x970a74: stp             fp, lr, [SP, #-0x10]!
    //     0x970a78: mov             fp, SP
    // 0x970a7c: AllocStack(0x20)
    //     0x970a7c: sub             SP, SP, #0x20
    // 0x970a80: SetupParameters(YoutubePlayerController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x970a80: mov             x3, x1
    //     0x970a84: mov             x0, x2
    //     0x970a88: stur            x1, [fp, #-8]
    //     0x970a8c: stur            x2, [fp, #-0x10]
    // 0x970a90: CheckStackOverflow
    //     0x970a90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x970a94: cmp             SP, x16
    //     0x970a98: b.ls            #0x970b10
    // 0x970a9c: r1 = Null
    //     0x970a9c: mov             x1, NULL
    // 0x970aa0: r2 = 12
    //     0x970aa0: movz            x2, #0xc
    // 0x970aa4: r0 = AllocateArray()
    //     0x970aa4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x970aa8: r16 = "videoId"
    //     0x970aa8: add             x16, PP, #0x48, lsl #12  ; [pp+0x48ff0] "videoId"
    //     0x970aac: ldr             x16, [x16, #0xff0]
    // 0x970ab0: StoreField: r0->field_f = r16
    //     0x970ab0: stur            w16, [x0, #0xf]
    // 0x970ab4: ldur            x1, [fp, #-0x10]
    // 0x970ab8: StoreField: r0->field_13 = r1
    //     0x970ab8: stur            w1, [x0, #0x13]
    // 0x970abc: r16 = "startSeconds"
    //     0x970abc: add             x16, PP, #0x48, lsl #12  ; [pp+0x48ff8] "startSeconds"
    //     0x970ac0: ldr             x16, [x16, #0xff8]
    // 0x970ac4: ArrayStore: r0[0] = r16  ; List_4
    //     0x970ac4: stur            w16, [x0, #0x17]
    // 0x970ac8: StoreField: r0->field_1b = rNULL
    //     0x970ac8: stur            NULL, [x0, #0x1b]
    // 0x970acc: r16 = "endSeconds"
    //     0x970acc: add             x16, PP, #0x49, lsl #12  ; [pp+0x49000] "endSeconds"
    //     0x970ad0: ldr             x16, [x16]
    // 0x970ad4: StoreField: r0->field_1f = r16
    //     0x970ad4: stur            w16, [x0, #0x1f]
    // 0x970ad8: StoreField: r0->field_23 = rNULL
    //     0x970ad8: stur            NULL, [x0, #0x23]
    // 0x970adc: r16 = <String, dynamic>
    //     0x970adc: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x970ae0: stp             x0, x16, [SP]
    // 0x970ae4: r0 = Map._fromLiteral()
    //     0x970ae4: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x970ae8: str             x0, [SP]
    // 0x970aec: ldur            x1, [fp, #-8]
    // 0x970af0: r2 = "loadVideoById"
    //     0x970af0: add             x2, PP, #0x49, lsl #12  ; [pp+0x49010] "loadVideoById"
    //     0x970af4: ldr             x2, [x2, #0x10]
    // 0x970af8: r4 = const [0, 0x3, 0x1, 0x2, data, 0x2, null]
    //     0x970af8: add             x4, PP, #0x12, lsl #12  ; [pp+0x123b0] List(7) [0, 0x3, 0x1, 0x2, "data", 0x2, Null]
    //     0x970afc: ldr             x4, [x4, #0x3b0]
    // 0x970b00: r0 = _run()
    //     0x970b00: bl              #0x965a7c  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_run
    // 0x970b04: LeaveFrame
    //     0x970b04: mov             SP, fp
    //     0x970b08: ldp             fp, lr, [SP], #0x10
    // 0x970b0c: ret
    //     0x970b0c: ret             
    // 0x970b10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x970b10: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x970b14: b               #0x970a9c
  }
  _ YoutubePlayerController(/* No info */) {
    // ** addr: 0x970b18, size: 0x368
    // 0x970b18: EnterFrame
    //     0x970b18: stp             fp, lr, [SP, #-0x10]!
    //     0x970b1c: mov             fp, SP
    // 0x970b20: AllocStack(0x30)
    //     0x970b20: sub             SP, SP, #0x30
    // 0x970b24: SetupParameters(YoutubePlayerController this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x970b24: stur            x1, [fp, #-8]
    //     0x970b28: mov             x16, x2
    //     0x970b2c: mov             x2, x1
    //     0x970b30: mov             x1, x16
    //     0x970b34: mov             x0, x3
    //     0x970b38: stur            x1, [fp, #-0x10]
    //     0x970b3c: stur            x3, [fp, #-0x18]
    // 0x970b40: CheckStackOverflow
    //     0x970b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x970b44: cmp             SP, x16
    //     0x970b48: b.ls            #0x970e78
    // 0x970b4c: r1 = 1
    //     0x970b4c: movz            x1, #0x1
    // 0x970b50: r0 = AllocateContext()
    //     0x970b50: bl              #0xec126c  ; AllocateContextStub
    // 0x970b54: ldur            x2, [fp, #-8]
    // 0x970b58: stur            x0, [fp, #-0x20]
    // 0x970b5c: StoreField: r0->field_f = r2
    //     0x970b5c: stur            w2, [x0, #0xf]
    // 0x970b60: r1 = Sentinel
    //     0x970b60: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x970b64: StoreField: r2->field_f = r1
    //     0x970b64: stur            w1, [x2, #0xf]
    // 0x970b68: StoreField: r2->field_13 = r1
    //     0x970b68: stur            w1, [x2, #0x13]
    // 0x970b6c: r1 = <void?>
    //     0x970b6c: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x970b70: r0 = _Future()
    //     0x970b70: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x970b74: stur            x0, [fp, #-0x28]
    // 0x970b78: StoreField: r0->field_b = rZR
    //     0x970b78: stur            xzr, [x0, #0xb]
    // 0x970b7c: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x970b7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x970b80: ldr             x0, [x0, #0x7a0]
    //     0x970b84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x970b88: cmp             w0, w16
    //     0x970b8c: b.ne            #0x970b98
    //     0x970b90: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x970b94: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x970b98: mov             x1, x0
    // 0x970b9c: ldur            x0, [fp, #-0x28]
    // 0x970ba0: StoreField: r0->field_13 = r1
    //     0x970ba0: stur            w1, [x0, #0x13]
    // 0x970ba4: r1 = <void?>
    //     0x970ba4: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x970ba8: r0 = _AsyncCompleter()
    //     0x970ba8: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x970bac: mov             x1, x0
    // 0x970bb0: ldur            x0, [fp, #-0x28]
    // 0x970bb4: StoreField: r1->field_b = r0
    //     0x970bb4: stur            w0, [x1, #0xb]
    // 0x970bb8: mov             x0, x1
    // 0x970bbc: ldur            x2, [fp, #-8]
    // 0x970bc0: ArrayStore: r2[0] = r0  ; List_4
    //     0x970bc0: stur            w0, [x2, #0x17]
    //     0x970bc4: ldurb           w16, [x2, #-1]
    //     0x970bc8: ldurb           w17, [x0, #-1]
    //     0x970bcc: and             x16, x17, x16, lsr #2
    //     0x970bd0: tst             x16, HEAP, lsr #32
    //     0x970bd4: b.eq            #0x970bdc
    //     0x970bd8: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x970bdc: r1 = <YoutubePlayerValue>
    //     0x970bdc: add             x1, PP, #0x49, lsl #12  ; [pp+0x49018] TypeArguments: <YoutubePlayerValue>
    //     0x970be0: ldr             x1, [x1, #0x18]
    // 0x970be4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x970be4: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x970be8: r0 = StreamController.broadcast()
    //     0x970be8: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0x970bec: ldur            x2, [fp, #-8]
    // 0x970bf0: StoreField: r2->field_1b = r0
    //     0x970bf0: stur            w0, [x2, #0x1b]
    //     0x970bf4: ldurb           w16, [x2, #-1]
    //     0x970bf8: ldurb           w17, [x0, #-1]
    //     0x970bfc: and             x16, x17, x16, lsr #2
    //     0x970c00: tst             x16, HEAP, lsr #32
    //     0x970c04: b.eq            #0x970c0c
    //     0x970c08: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x970c0c: r0 = YoutubePlayerValue()
    //     0x970c0c: bl              #0x97cc4c  ; AllocateYoutubePlayerValueStub -> YoutubePlayerValue (size=0x24)
    // 0x970c10: mov             x1, x0
    // 0x970c14: r0 = Instance_FullScreenOption
    //     0x970c14: add             x0, PP, #0x49, lsl #12  ; [pp+0x49020] Obj!FullScreenOption@e0bbc1
    //     0x970c18: ldr             x0, [x0, #0x20]
    // 0x970c1c: StoreField: r1->field_7 = r0
    //     0x970c1c: stur            w0, [x1, #7]
    // 0x970c20: r0 = Instance_PlayerState
    //     0x970c20: add             x0, PP, #0x49, lsl #12  ; [pp+0x49028] Obj!PlayerState@e2d351
    //     0x970c24: ldr             x0, [x0, #0x28]
    // 0x970c28: StoreField: r1->field_b = r0
    //     0x970c28: stur            w0, [x1, #0xb]
    // 0x970c2c: d0 = 1.000000
    //     0x970c2c: fmov            d0, #1.00000000
    // 0x970c30: StoreField: r1->field_f = d0
    //     0x970c30: stur            d0, [x1, #0xf]
    // 0x970c34: r0 = Instance_YoutubeError
    //     0x970c34: add             x0, PP, #0x49, lsl #12  ; [pp+0x49030] Obj!YoutubeError@e2d271
    //     0x970c38: ldr             x0, [x0, #0x30]
    // 0x970c3c: ArrayStore: r1[0] = r0  ; List_4
    //     0x970c3c: stur            w0, [x1, #0x17]
    // 0x970c40: r0 = Instance_YoutubeMetaData
    //     0x970c40: add             x0, PP, #0x49, lsl #12  ; [pp+0x49038] Obj!YoutubeMetaData@e0bbd1
    //     0x970c44: ldr             x0, [x0, #0x38]
    // 0x970c48: StoreField: r1->field_1f = r0
    //     0x970c48: stur            w0, [x1, #0x1f]
    // 0x970c4c: mov             x0, x1
    // 0x970c50: ldur            x2, [fp, #-8]
    // 0x970c54: StoreField: r2->field_1f = r0
    //     0x970c54: stur            w0, [x2, #0x1f]
    //     0x970c58: ldurb           w16, [x2, #-1]
    //     0x970c5c: ldurb           w17, [x0, #-1]
    //     0x970c60: and             x16, x17, x16, lsr #2
    //     0x970c64: tst             x16, HEAP, lsr #32
    //     0x970c68: b.eq            #0x970c70
    //     0x970c6c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x970c70: ldur            x0, [fp, #-0x18]
    // 0x970c74: StoreField: r2->field_b = r0
    //     0x970c74: stur            w0, [x2, #0xb]
    //     0x970c78: ldurb           w16, [x2, #-1]
    //     0x970c7c: ldurb           w17, [x0, #-1]
    //     0x970c80: and             x16, x17, x16, lsr #2
    //     0x970c84: tst             x16, HEAP, lsr #32
    //     0x970c88: b.eq            #0x970c90
    //     0x970c8c: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x970c90: ldur            x0, [fp, #-0x10]
    // 0x970c94: StoreField: r2->field_7 = r0
    //     0x970c94: stur            w0, [x2, #7]
    //     0x970c98: ldurb           w16, [x2, #-1]
    //     0x970c9c: ldurb           w17, [x0, #-1]
    //     0x970ca0: and             x16, x17, x16, lsr #2
    //     0x970ca4: tst             x16, HEAP, lsr #32
    //     0x970ca8: b.eq            #0x970cb0
    //     0x970cac: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x970cb0: r0 = YoutubePlayerEventHandler()
    //     0x970cb0: bl              #0x97cc40  ; AllocateYoutubePlayerEventHandlerStub -> YoutubePlayerEventHandler (size=0x18)
    // 0x970cb4: mov             x1, x0
    // 0x970cb8: ldur            x2, [fp, #-8]
    // 0x970cbc: stur            x0, [fp, #-0x18]
    // 0x970cc0: r0 = YoutubePlayerEventHandler()
    //     0x970cc0: bl              #0x97b30c  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::YoutubePlayerEventHandler
    // 0x970cc4: ldur            x0, [fp, #-8]
    // 0x970cc8: LoadField: r1 = r0->field_13
    //     0x970cc8: ldur            w1, [x0, #0x13]
    // 0x970ccc: DecompressPointer r1
    //     0x970ccc: add             x1, x1, HEAP, lsl #32
    // 0x970cd0: r16 = Sentinel
    //     0x970cd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x970cd4: cmp             w1, w16
    // 0x970cd8: b.ne            #0x970ce4
    // 0x970cdc: mov             x2, x0
    // 0x970ce0: b               #0x970cf8
    // 0x970ce4: r16 = "_eventHandler@2476072021"
    //     0x970ce4: add             x16, PP, #0x49, lsl #12  ; [pp+0x49040] "_eventHandler@2476072021"
    //     0x970ce8: ldr             x16, [x16, #0x40]
    // 0x970cec: str             x16, [SP]
    // 0x970cf0: r0 = _throwFieldAlreadyInitialized()
    //     0x970cf0: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x970cf4: ldur            x2, [fp, #-8]
    // 0x970cf8: ldur            x3, [fp, #-0x10]
    // 0x970cfc: ldur            x0, [fp, #-0x18]
    // 0x970d00: StoreField: r2->field_13 = r0
    //     0x970d00: stur            w0, [x2, #0x13]
    //     0x970d04: ldurb           w16, [x2, #-1]
    //     0x970d08: ldurb           w17, [x0, #-1]
    //     0x970d0c: and             x16, x17, x16, lsr #2
    //     0x970d10: tst             x16, HEAP, lsr #32
    //     0x970d14: b.eq            #0x970d1c
    //     0x970d18: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x970d1c: r1 = Null
    //     0x970d1c: mov             x1, NULL
    // 0x970d20: r0 = PlatformNavigationDelegate()
    //     0x970d20: bl              #0x97a428  ; [package:webview_flutter_platform_interface/src/platform_navigation_delegate.dart] PlatformNavigationDelegate::PlatformNavigationDelegate
    // 0x970d24: r1 = Function '<anonymous closure>':.
    //     0x970d24: add             x1, PP, #0x49, lsl #12  ; [pp+0x49048] AnonymousClosure: (0x97d3e8), in [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::YoutubePlayerController (0x970b18)
    //     0x970d28: ldr             x1, [x1, #0x48]
    // 0x970d2c: r2 = Null
    //     0x970d2c: mov             x2, NULL
    // 0x970d30: stur            x0, [fp, #-0x18]
    // 0x970d34: r0 = AllocateClosure()
    //     0x970d34: bl              #0xec1630  ; AllocateClosureStub
    // 0x970d38: ldur            x2, [fp, #-0x20]
    // 0x970d3c: r1 = Function '<anonymous closure>':.
    //     0x970d3c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49050] AnonymousClosure: (0x97cfd0), in [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::YoutubePlayerController (0x970b18)
    //     0x970d40: ldr             x1, [x1, #0x50]
    // 0x970d44: stur            x0, [fp, #-0x20]
    // 0x970d48: r0 = AllocateClosure()
    //     0x970d48: bl              #0xec1630  ; AllocateClosureStub
    // 0x970d4c: stur            x0, [fp, #-0x28]
    // 0x970d50: r0 = NavigationDelegate()
    //     0x970d50: bl              #0x97a41c  ; AllocateNavigationDelegateStub -> NavigationDelegate (size=0x20)
    // 0x970d54: mov             x1, x0
    // 0x970d58: ldur            x2, [fp, #-0x18]
    // 0x970d5c: ldur            x3, [fp, #-0x28]
    // 0x970d60: ldur            x5, [fp, #-0x20]
    // 0x970d64: stur            x0, [fp, #-0x18]
    // 0x970d68: r0 = NavigationDelegate.fromPlatform()
    //     0x970d68: bl              #0x979fac  ; [package:webview_flutter/src/navigation_delegate.dart] NavigationDelegate::NavigationDelegate.fromPlatform
    // 0x970d6c: r1 = Null
    //     0x970d6c: mov             x1, NULL
    // 0x970d70: r0 = PlatformWebViewController()
    //     0x970d70: bl              #0x974360  ; [package:webview_flutter_platform_interface/src/platform_webview_controller.dart] PlatformWebViewController::PlatformWebViewController
    // 0x970d74: stur            x0, [fp, #-0x20]
    // 0x970d78: r0 = WebViewController()
    //     0x970d78: bl              #0x974354  ; AllocateWebViewControllerStub -> WebViewController (size=0xc)
    // 0x970d7c: mov             x2, x0
    // 0x970d80: ldur            x0, [fp, #-0x20]
    // 0x970d84: stur            x2, [fp, #-0x28]
    // 0x970d88: StoreField: r2->field_7 = r0
    //     0x970d88: stur            w0, [x2, #7]
    // 0x970d8c: mov             x1, x2
    // 0x970d90: r0 = setJavaScriptMode()
    //     0x970d90: bl              #0x973fe4  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::setJavaScriptMode
    // 0x970d94: ldur            x1, [fp, #-0x28]
    // 0x970d98: ldur            x2, [fp, #-0x18]
    // 0x970d9c: r0 = setNavigationDelegate()
    //     0x970d9c: bl              #0x9730ec  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::setNavigationDelegate
    // 0x970da0: ldur            x1, [fp, #-0x28]
    // 0x970da4: r0 = setUserAgent()
    //     0x970da4: bl              #0x972d8c  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::setUserAgent
    // 0x970da8: r1 = Null
    //     0x970da8: mov             x1, NULL
    // 0x970dac: r2 = 4
    //     0x970dac: movz            x2, #0x4
    // 0x970db0: r0 = AllocateArray()
    //     0x970db0: bl              #0xec22fc  ; AllocateArrayStub
    // 0x970db4: r16 = "Youtube"
    //     0x970db4: add             x16, PP, #0x49, lsl #12  ; [pp+0x49058] "Youtube"
    //     0x970db8: ldr             x16, [x16, #0x58]
    // 0x970dbc: StoreField: r0->field_f = r16
    //     0x970dbc: stur            w16, [x0, #0xf]
    // 0x970dc0: ldur            x1, [fp, #-0x10]
    // 0x970dc4: StoreField: r0->field_13 = r1
    //     0x970dc4: stur            w1, [x0, #0x13]
    // 0x970dc8: str             x0, [SP]
    // 0x970dcc: r0 = _interpolate()
    //     0x970dcc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x970dd0: mov             x3, x0
    // 0x970dd4: ldur            x0, [fp, #-8]
    // 0x970dd8: stur            x3, [fp, #-0x10]
    // 0x970ddc: LoadField: r2 = r0->field_13
    //     0x970ddc: ldur            w2, [x0, #0x13]
    // 0x970de0: DecompressPointer r2
    //     0x970de0: add             x2, x2, HEAP, lsl #32
    // 0x970de4: r1 = Function 'call':.
    //     0x970de4: add             x1, PP, #0x49, lsl #12  ; [pp+0x49060] AnonymousClosure: (0x97cc58), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::call (0x97cc94)
    //     0x970de8: ldr             x1, [x1, #0x60]
    // 0x970dec: r0 = AllocateClosure()
    //     0x970dec: bl              #0xec1630  ; AllocateClosureStub
    // 0x970df0: ldur            x1, [fp, #-0x28]
    // 0x970df4: ldur            x2, [fp, #-0x10]
    // 0x970df8: mov             x3, x0
    // 0x970dfc: r0 = addJavaScriptChannel()
    //     0x970dfc: bl              #0x971da0  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::addJavaScriptChannel
    // 0x970e00: ldur            x1, [fp, #-0x28]
    // 0x970e04: r0 = enableZoom()
    //     0x970e04: bl              #0x971a3c  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::enableZoom
    // 0x970e08: ldur            x0, [fp, #-8]
    // 0x970e0c: LoadField: r1 = r0->field_f
    //     0x970e0c: ldur            w1, [x0, #0xf]
    // 0x970e10: DecompressPointer r1
    //     0x970e10: add             x1, x1, HEAP, lsl #32
    // 0x970e14: r16 = Sentinel
    //     0x970e14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x970e18: cmp             w1, w16
    // 0x970e1c: b.ne            #0x970e28
    // 0x970e20: mov             x1, x0
    // 0x970e24: b               #0x970e3c
    // 0x970e28: r16 = "webViewController"
    //     0x970e28: add             x16, PP, #0x49, lsl #12  ; [pp+0x49068] "webViewController"
    //     0x970e2c: ldr             x16, [x16, #0x68]
    // 0x970e30: str             x16, [SP]
    // 0x970e34: r0 = _throwFieldAlreadyInitialized()
    //     0x970e34: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x970e38: ldur            x1, [fp, #-8]
    // 0x970e3c: ldur            x0, [fp, #-0x28]
    // 0x970e40: StoreField: r1->field_f = r0
    //     0x970e40: stur            w0, [x1, #0xf]
    //     0x970e44: ldurb           w16, [x1, #-1]
    //     0x970e48: ldurb           w17, [x0, #-1]
    //     0x970e4c: and             x16, x17, x16, lsr #2
    //     0x970e50: tst             x16, HEAP, lsr #32
    //     0x970e54: b.eq            #0x970e5c
    //     0x970e58: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x970e5c: r0 = enableDebugging()
    //     0x970e5c: bl              #0x9715bc  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::enableDebugging
    // 0x970e60: ldur            x1, [fp, #-0x20]
    // 0x970e64: r0 = setMediaPlaybackRequiresUserGesture()
    //     0x970e64: bl              #0x970e80  ; [package:webview_flutter_android/src/android_webview_controller.dart] AndroidWebViewController::setMediaPlaybackRequiresUserGesture
    // 0x970e68: r0 = Null
    //     0x970e68: mov             x0, NULL
    // 0x970e6c: LeaveFrame
    //     0x970e6c: mov             SP, fp
    //     0x970e70: ldp             fp, lr, [SP], #0x10
    // 0x970e74: ret
    //     0x970e74: ret             
    // 0x970e78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x970e78: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x970e7c: b               #0x970b4c
  }
  _ toggleFullScreen(/* No info */) {
    // ** addr: 0x97b9fc, size: 0x54
    // 0x97b9fc: EnterFrame
    //     0x97b9fc: stp             fp, lr, [SP, #-0x10]!
    //     0x97ba00: mov             fp, SP
    // 0x97ba04: CheckStackOverflow
    //     0x97ba04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ba08: cmp             SP, x16
    //     0x97ba0c: b.ls            #0x97ba48
    // 0x97ba10: LoadField: r0 = r1->field_1f
    //     0x97ba10: ldur            w0, [x1, #0x1f]
    // 0x97ba14: DecompressPointer r0
    //     0x97ba14: add             x0, x0, HEAP, lsl #32
    // 0x97ba18: LoadField: r2 = r0->field_7
    //     0x97ba18: ldur            w2, [x0, #7]
    // 0x97ba1c: DecompressPointer r2
    //     0x97ba1c: add             x2, x2, HEAP, lsl #32
    // 0x97ba20: LoadField: r0 = r2->field_7
    //     0x97ba20: ldur            w0, [x2, #7]
    // 0x97ba24: DecompressPointer r0
    //     0x97ba24: add             x0, x0, HEAP, lsl #32
    // 0x97ba28: tbnz            w0, #4, #0x97ba34
    // 0x97ba2c: r0 = exitFullScreen()
    //     0x97ba2c: bl              #0x97be4c  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::exitFullScreen
    // 0x97ba30: b               #0x97ba38
    // 0x97ba34: r0 = enterFullScreen()
    //     0x97ba34: bl              #0x97ba50  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::enterFullScreen
    // 0x97ba38: r0 = Null
    //     0x97ba38: mov             x0, NULL
    // 0x97ba3c: LeaveFrame
    //     0x97ba3c: mov             SP, fp
    //     0x97ba40: ldp             fp, lr, [SP], #0x10
    // 0x97ba44: ret
    //     0x97ba44: ret             
    // 0x97ba48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ba48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ba4c: b               #0x97ba10
  }
  _ enterFullScreen(/* No info */) {
    // ** addr: 0x97ba50, size: 0x88
    // 0x97ba50: EnterFrame
    //     0x97ba50: stp             fp, lr, [SP, #-0x10]!
    //     0x97ba54: mov             fp, SP
    // 0x97ba58: AllocStack(0x18)
    //     0x97ba58: sub             SP, SP, #0x18
    // 0x97ba5c: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x8 */)
    //     0x97ba5c: stur            x1, [fp, #-8]
    // 0x97ba60: CheckStackOverflow
    //     0x97ba60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ba64: cmp             SP, x16
    //     0x97ba68: b.ls            #0x97bad0
    // 0x97ba6c: r0 = FullScreenOption()
    //     0x97ba6c: bl              #0x97be40  ; AllocateFullScreenOptionStub -> FullScreenOption (size=0x10)
    // 0x97ba70: mov             x1, x0
    // 0x97ba74: r0 = true
    //     0x97ba74: add             x0, NULL, #0x20  ; true
    // 0x97ba78: StoreField: r1->field_7 = r0
    //     0x97ba78: stur            w0, [x1, #7]
    // 0x97ba7c: StoreField: r1->field_b = r0
    //     0x97ba7c: stur            w0, [x1, #0xb]
    // 0x97ba80: str             x1, [SP]
    // 0x97ba84: ldur            x1, [fp, #-8]
    // 0x97ba88: r4 = const [0, 0x2, 0x1, 0x1, fullScreenOption, 0x1, null]
    //     0x97ba88: add             x4, PP, #0x49, lsl #12  ; [pp+0x49c30] List(7) [0, 0x2, 0x1, 0x1, "fullScreenOption", 0x1, Null]
    //     0x97ba8c: ldr             x4, [x4, #0xc30]
    // 0x97ba90: r0 = update()
    //     0x97ba90: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97ba94: ldur            x0, [fp, #-8]
    // 0x97ba98: LoadField: r1 = r0->field_23
    //     0x97ba98: ldur            w1, [x0, #0x23]
    // 0x97ba9c: DecompressPointer r1
    //     0x97ba9c: add             x1, x1, HEAP, lsl #32
    // 0x97baa0: cmp             w1, NULL
    // 0x97baa4: b.eq            #0x97bac0
    // 0x97baa8: r16 = true
    //     0x97baa8: add             x16, NULL, #0x20  ; true
    // 0x97baac: stp             x16, x1, [SP]
    // 0x97bab0: mov             x0, x1
    // 0x97bab4: ClosureCall
    //     0x97bab4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97bab8: ldur            x2, [x0, #0x1f]
    //     0x97babc: blr             x2
    // 0x97bac0: r0 = Null
    //     0x97bac0: mov             x0, NULL
    // 0x97bac4: LeaveFrame
    //     0x97bac4: mov             SP, fp
    //     0x97bac8: ldp             fp, lr, [SP], #0x10
    // 0x97bacc: ret
    //     0x97bacc: ret             
    // 0x97bad0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97bad0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97bad4: b               #0x97ba6c
  }
  _ update(/* No info */) {
    // ** addr: 0x97bad8, size: 0x368
    // 0x97bad8: EnterFrame
    //     0x97bad8: stp             fp, lr, [SP, #-0x10]!
    //     0x97badc: mov             fp, SP
    // 0x97bae0: AllocStack(0x38)
    //     0x97bae0: sub             SP, SP, #0x38
    // 0x97bae4: SetupParameters({dynamic error = Null /* r3 */, dynamic fullScreenOption = Null /* r5 */, dynamic metaData = Null /* r6 */, dynamic playbackQuality = Null /* r7 */, dynamic playbackRate = Null /* r8 */, dynamic playerState = Null /* r0 */})
    //     0x97bae4: ldur            w0, [x4, #0x13]
    //     0x97bae8: ldur            w2, [x4, #0x1f]
    //     0x97baec: add             x2, x2, HEAP, lsl #32
    //     0x97baf0: ldr             x16, [PP, #0x20a8]  ; [pp+0x20a8] "error"
    //     0x97baf4: cmp             w2, w16
    //     0x97baf8: b.ne            #0x97bb1c
    //     0x97bafc: ldur            w2, [x4, #0x23]
    //     0x97bb00: add             x2, x2, HEAP, lsl #32
    //     0x97bb04: sub             w3, w0, w2
    //     0x97bb08: add             x2, fp, w3, sxtw #2
    //     0x97bb0c: ldr             x2, [x2, #8]
    //     0x97bb10: mov             x3, x2
    //     0x97bb14: movz            x2, #0x1
    //     0x97bb18: b               #0x97bb24
    //     0x97bb1c: mov             x3, NULL
    //     0x97bb20: movz            x2, #0
    //     0x97bb24: lsl             x5, x2, #1
    //     0x97bb28: lsl             w6, w5, #1
    //     0x97bb2c: add             w7, w6, #8
    //     0x97bb30: add             x16, x4, w7, sxtw #1
    //     0x97bb34: ldur            w8, [x16, #0xf]
    //     0x97bb38: add             x8, x8, HEAP, lsl #32
    //     0x97bb3c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49c38] "fullScreenOption"
    //     0x97bb40: ldr             x16, [x16, #0xc38]
    //     0x97bb44: cmp             w8, w16
    //     0x97bb48: b.ne            #0x97bb7c
    //     0x97bb4c: add             w2, w6, #0xa
    //     0x97bb50: add             x16, x4, w2, sxtw #1
    //     0x97bb54: ldur            w6, [x16, #0xf]
    //     0x97bb58: add             x6, x6, HEAP, lsl #32
    //     0x97bb5c: sub             w2, w0, w6
    //     0x97bb60: add             x6, fp, w2, sxtw #2
    //     0x97bb64: ldr             x6, [x6, #8]
    //     0x97bb68: add             w2, w5, #2
    //     0x97bb6c: sbfx            x5, x2, #1, #0x1f
    //     0x97bb70: mov             x2, x5
    //     0x97bb74: mov             x5, x6
    //     0x97bb78: b               #0x97bb80
    //     0x97bb7c: mov             x5, NULL
    //     0x97bb80: lsl             x6, x2, #1
    //     0x97bb84: lsl             w7, w6, #1
    //     0x97bb88: add             w8, w7, #8
    //     0x97bb8c: add             x16, x4, w8, sxtw #1
    //     0x97bb90: ldur            w9, [x16, #0xf]
    //     0x97bb94: add             x9, x9, HEAP, lsl #32
    //     0x97bb98: add             x16, PP, #0x49, lsl #12  ; [pp+0x49c40] "metaData"
    //     0x97bb9c: ldr             x16, [x16, #0xc40]
    //     0x97bba0: cmp             w9, w16
    //     0x97bba4: b.ne            #0x97bbd8
    //     0x97bba8: add             w2, w7, #0xa
    //     0x97bbac: add             x16, x4, w2, sxtw #1
    //     0x97bbb0: ldur            w7, [x16, #0xf]
    //     0x97bbb4: add             x7, x7, HEAP, lsl #32
    //     0x97bbb8: sub             w2, w0, w7
    //     0x97bbbc: add             x7, fp, w2, sxtw #2
    //     0x97bbc0: ldr             x7, [x7, #8]
    //     0x97bbc4: add             w2, w6, #2
    //     0x97bbc8: sbfx            x6, x2, #1, #0x1f
    //     0x97bbcc: mov             x2, x6
    //     0x97bbd0: mov             x6, x7
    //     0x97bbd4: b               #0x97bbdc
    //     0x97bbd8: mov             x6, NULL
    //     0x97bbdc: lsl             x7, x2, #1
    //     0x97bbe0: lsl             w8, w7, #1
    //     0x97bbe4: add             w9, w8, #8
    //     0x97bbe8: add             x16, x4, w9, sxtw #1
    //     0x97bbec: ldur            w10, [x16, #0xf]
    //     0x97bbf0: add             x10, x10, HEAP, lsl #32
    //     0x97bbf4: add             x16, PP, #0x49, lsl #12  ; [pp+0x49c48] "playbackQuality"
    //     0x97bbf8: ldr             x16, [x16, #0xc48]
    //     0x97bbfc: cmp             w10, w16
    //     0x97bc00: b.ne            #0x97bc34
    //     0x97bc04: add             w2, w8, #0xa
    //     0x97bc08: add             x16, x4, w2, sxtw #1
    //     0x97bc0c: ldur            w8, [x16, #0xf]
    //     0x97bc10: add             x8, x8, HEAP, lsl #32
    //     0x97bc14: sub             w2, w0, w8
    //     0x97bc18: add             x8, fp, w2, sxtw #2
    //     0x97bc1c: ldr             x8, [x8, #8]
    //     0x97bc20: add             w2, w7, #2
    //     0x97bc24: sbfx            x7, x2, #1, #0x1f
    //     0x97bc28: mov             x2, x7
    //     0x97bc2c: mov             x7, x8
    //     0x97bc30: b               #0x97bc38
    //     0x97bc34: mov             x7, NULL
    //     0x97bc38: lsl             x8, x2, #1
    //     0x97bc3c: lsl             w9, w8, #1
    //     0x97bc40: add             w10, w9, #8
    //     0x97bc44: add             x16, x4, w10, sxtw #1
    //     0x97bc48: ldur            w11, [x16, #0xf]
    //     0x97bc4c: add             x11, x11, HEAP, lsl #32
    //     0x97bc50: add             x16, PP, #0x49, lsl #12  ; [pp+0x49c50] "playbackRate"
    //     0x97bc54: ldr             x16, [x16, #0xc50]
    //     0x97bc58: cmp             w11, w16
    //     0x97bc5c: b.ne            #0x97bc90
    //     0x97bc60: add             w2, w9, #0xa
    //     0x97bc64: add             x16, x4, w2, sxtw #1
    //     0x97bc68: ldur            w9, [x16, #0xf]
    //     0x97bc6c: add             x9, x9, HEAP, lsl #32
    //     0x97bc70: sub             w2, w0, w9
    //     0x97bc74: add             x9, fp, w2, sxtw #2
    //     0x97bc78: ldr             x9, [x9, #8]
    //     0x97bc7c: add             w2, w8, #2
    //     0x97bc80: sbfx            x8, x2, #1, #0x1f
    //     0x97bc84: mov             x2, x8
    //     0x97bc88: mov             x8, x9
    //     0x97bc8c: b               #0x97bc94
    //     0x97bc90: mov             x8, NULL
    //     0x97bc94: lsl             x9, x2, #1
    //     0x97bc98: lsl             w2, w9, #1
    //     0x97bc9c: add             w9, w2, #8
    //     0x97bca0: add             x16, x4, w9, sxtw #1
    //     0x97bca4: ldur            w10, [x16, #0xf]
    //     0x97bca8: add             x10, x10, HEAP, lsl #32
    //     0x97bcac: add             x16, PP, #0x49, lsl #12  ; [pp+0x49c58] "playerState"
    //     0x97bcb0: ldr             x16, [x16, #0xc58]
    //     0x97bcb4: cmp             w10, w16
    //     0x97bcb8: b.ne            #0x97bcdc
    //     0x97bcbc: add             w9, w2, #0xa
    //     0x97bcc0: add             x16, x4, w9, sxtw #1
    //     0x97bcc4: ldur            w2, [x16, #0xf]
    //     0x97bcc8: add             x2, x2, HEAP, lsl #32
    //     0x97bccc: sub             w4, w0, w2
    //     0x97bcd0: add             x0, fp, w4, sxtw #2
    //     0x97bcd4: ldr             x0, [x0, #8]
    //     0x97bcd8: b               #0x97bce0
    //     0x97bcdc: mov             x0, NULL
    // 0x97bce0: CheckStackOverflow
    //     0x97bce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97bce4: cmp             SP, x16
    //     0x97bce8: b.ls            #0x97be38
    // 0x97bcec: LoadField: r2 = r1->field_1b
    //     0x97bcec: ldur            w2, [x1, #0x1b]
    // 0x97bcf0: DecompressPointer r2
    //     0x97bcf0: add             x2, x2, HEAP, lsl #32
    // 0x97bcf4: stur            x2, [fp, #-0x30]
    // 0x97bcf8: LoadField: r4 = r2->field_13
    //     0x97bcf8: ldur            x4, [x2, #0x13]
    // 0x97bcfc: tbz             w4, #2, #0x97bd10
    // 0x97bd00: r0 = Null
    //     0x97bd00: mov             x0, NULL
    // 0x97bd04: LeaveFrame
    //     0x97bd04: mov             SP, fp
    //     0x97bd08: ldp             fp, lr, [SP], #0x10
    // 0x97bd0c: ret
    //     0x97bd0c: ret             
    // 0x97bd10: cmp             w5, NULL
    // 0x97bd14: b.ne            #0x97bd30
    // 0x97bd18: LoadField: r4 = r1->field_1f
    //     0x97bd18: ldur            w4, [x1, #0x1f]
    // 0x97bd1c: DecompressPointer r4
    //     0x97bd1c: add             x4, x4, HEAP, lsl #32
    // 0x97bd20: LoadField: r5 = r4->field_7
    //     0x97bd20: ldur            w5, [x4, #7]
    // 0x97bd24: DecompressPointer r5
    //     0x97bd24: add             x5, x5, HEAP, lsl #32
    // 0x97bd28: mov             x4, x5
    // 0x97bd2c: b               #0x97bd34
    // 0x97bd30: mov             x4, x5
    // 0x97bd34: stur            x4, [fp, #-0x28]
    // 0x97bd38: cmp             w0, NULL
    // 0x97bd3c: b.ne            #0x97bd54
    // 0x97bd40: LoadField: r0 = r1->field_1f
    //     0x97bd40: ldur            w0, [x1, #0x1f]
    // 0x97bd44: DecompressPointer r0
    //     0x97bd44: add             x0, x0, HEAP, lsl #32
    // 0x97bd48: LoadField: r5 = r0->field_b
    //     0x97bd48: ldur            w5, [x0, #0xb]
    // 0x97bd4c: DecompressPointer r5
    //     0x97bd4c: add             x5, x5, HEAP, lsl #32
    // 0x97bd50: mov             x0, x5
    // 0x97bd54: stur            x0, [fp, #-0x20]
    // 0x97bd58: cmp             w8, NULL
    // 0x97bd5c: b.ne            #0x97bd70
    // 0x97bd60: LoadField: r5 = r1->field_1f
    //     0x97bd60: ldur            w5, [x1, #0x1f]
    // 0x97bd64: DecompressPointer r5
    //     0x97bd64: add             x5, x5, HEAP, lsl #32
    // 0x97bd68: LoadField: d0 = r5->field_f
    //     0x97bd68: ldur            d0, [x5, #0xf]
    // 0x97bd6c: b               #0x97bd74
    // 0x97bd70: LoadField: d0 = r8->field_7
    //     0x97bd70: ldur            d0, [x8, #7]
    // 0x97bd74: stur            d0, [fp, #-0x38]
    // 0x97bd78: cmp             w7, NULL
    // 0x97bd7c: b.ne            #0x97bd98
    // 0x97bd80: LoadField: r5 = r1->field_1f
    //     0x97bd80: ldur            w5, [x1, #0x1f]
    // 0x97bd84: DecompressPointer r5
    //     0x97bd84: add             x5, x5, HEAP, lsl #32
    // 0x97bd88: LoadField: r7 = r5->field_1b
    //     0x97bd88: ldur            w7, [x5, #0x1b]
    // 0x97bd8c: DecompressPointer r7
    //     0x97bd8c: add             x7, x7, HEAP, lsl #32
    // 0x97bd90: mov             x5, x7
    // 0x97bd94: b               #0x97bd9c
    // 0x97bd98: mov             x5, x7
    // 0x97bd9c: stur            x5, [fp, #-0x18]
    // 0x97bda0: cmp             w3, NULL
    // 0x97bda4: b.ne            #0x97bdbc
    // 0x97bda8: LoadField: r3 = r1->field_1f
    //     0x97bda8: ldur            w3, [x1, #0x1f]
    // 0x97bdac: DecompressPointer r3
    //     0x97bdac: add             x3, x3, HEAP, lsl #32
    // 0x97bdb0: ArrayLoad: r7 = r3[0]  ; List_4
    //     0x97bdb0: ldur            w7, [x3, #0x17]
    // 0x97bdb4: DecompressPointer r7
    //     0x97bdb4: add             x7, x7, HEAP, lsl #32
    // 0x97bdb8: mov             x3, x7
    // 0x97bdbc: stur            x3, [fp, #-0x10]
    // 0x97bdc0: cmp             w6, NULL
    // 0x97bdc4: b.ne            #0x97bddc
    // 0x97bdc8: LoadField: r6 = r1->field_1f
    //     0x97bdc8: ldur            w6, [x1, #0x1f]
    // 0x97bdcc: DecompressPointer r6
    //     0x97bdcc: add             x6, x6, HEAP, lsl #32
    // 0x97bdd0: LoadField: r1 = r6->field_1f
    //     0x97bdd0: ldur            w1, [x6, #0x1f]
    // 0x97bdd4: DecompressPointer r1
    //     0x97bdd4: add             x1, x1, HEAP, lsl #32
    // 0x97bdd8: b               #0x97bde0
    // 0x97bddc: mov             x1, x6
    // 0x97bde0: stur            x1, [fp, #-8]
    // 0x97bde4: r0 = YoutubePlayerValue()
    //     0x97bde4: bl              #0x97cc4c  ; AllocateYoutubePlayerValueStub -> YoutubePlayerValue (size=0x24)
    // 0x97bde8: mov             x1, x0
    // 0x97bdec: ldur            x0, [fp, #-0x28]
    // 0x97bdf0: StoreField: r1->field_7 = r0
    //     0x97bdf0: stur            w0, [x1, #7]
    // 0x97bdf4: ldur            x0, [fp, #-0x20]
    // 0x97bdf8: StoreField: r1->field_b = r0
    //     0x97bdf8: stur            w0, [x1, #0xb]
    // 0x97bdfc: ldur            d0, [fp, #-0x38]
    // 0x97be00: StoreField: r1->field_f = d0
    //     0x97be00: stur            d0, [x1, #0xf]
    // 0x97be04: ldur            x0, [fp, #-0x18]
    // 0x97be08: StoreField: r1->field_1b = r0
    //     0x97be08: stur            w0, [x1, #0x1b]
    // 0x97be0c: ldur            x0, [fp, #-0x10]
    // 0x97be10: ArrayStore: r1[0] = r0  ; List_4
    //     0x97be10: stur            w0, [x1, #0x17]
    // 0x97be14: ldur            x0, [fp, #-8]
    // 0x97be18: StoreField: r1->field_1f = r0
    //     0x97be18: stur            w0, [x1, #0x1f]
    // 0x97be1c: mov             x2, x1
    // 0x97be20: ldur            x1, [fp, #-0x30]
    // 0x97be24: r0 = add()
    //     0x97be24: bl              #0xc7541c  ; [dart:async] _BroadcastStreamController::add
    // 0x97be28: r0 = Null
    //     0x97be28: mov             x0, NULL
    // 0x97be2c: LeaveFrame
    //     0x97be2c: mov             SP, fp
    //     0x97be30: ldp             fp, lr, [SP], #0x10
    // 0x97be34: ret
    //     0x97be34: ret             
    // 0x97be38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97be38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97be3c: b               #0x97bcec
  }
  _ exitFullScreen(/* No info */) {
    // ** addr: 0x97be4c, size: 0x8c
    // 0x97be4c: EnterFrame
    //     0x97be4c: stp             fp, lr, [SP, #-0x10]!
    //     0x97be50: mov             fp, SP
    // 0x97be54: AllocStack(0x18)
    //     0x97be54: sub             SP, SP, #0x18
    // 0x97be58: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x8 */)
    //     0x97be58: stur            x1, [fp, #-8]
    // 0x97be5c: CheckStackOverflow
    //     0x97be5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97be60: cmp             SP, x16
    //     0x97be64: b.ls            #0x97bed0
    // 0x97be68: r0 = FullScreenOption()
    //     0x97be68: bl              #0x97be40  ; AllocateFullScreenOptionStub -> FullScreenOption (size=0x10)
    // 0x97be6c: mov             x1, x0
    // 0x97be70: r0 = false
    //     0x97be70: add             x0, NULL, #0x30  ; false
    // 0x97be74: StoreField: r1->field_7 = r0
    //     0x97be74: stur            w0, [x1, #7]
    // 0x97be78: r0 = true
    //     0x97be78: add             x0, NULL, #0x20  ; true
    // 0x97be7c: StoreField: r1->field_b = r0
    //     0x97be7c: stur            w0, [x1, #0xb]
    // 0x97be80: str             x1, [SP]
    // 0x97be84: ldur            x1, [fp, #-8]
    // 0x97be88: r4 = const [0, 0x2, 0x1, 0x1, fullScreenOption, 0x1, null]
    //     0x97be88: add             x4, PP, #0x49, lsl #12  ; [pp+0x49c30] List(7) [0, 0x2, 0x1, 0x1, "fullScreenOption", 0x1, Null]
    //     0x97be8c: ldr             x4, [x4, #0xc30]
    // 0x97be90: r0 = update()
    //     0x97be90: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97be94: ldur            x0, [fp, #-8]
    // 0x97be98: LoadField: r1 = r0->field_23
    //     0x97be98: ldur            w1, [x0, #0x23]
    // 0x97be9c: DecompressPointer r1
    //     0x97be9c: add             x1, x1, HEAP, lsl #32
    // 0x97bea0: cmp             w1, NULL
    // 0x97bea4: b.eq            #0x97bec0
    // 0x97bea8: r16 = false
    //     0x97bea8: add             x16, NULL, #0x30  ; false
    // 0x97beac: stp             x16, x1, [SP]
    // 0x97beb0: mov             x0, x1
    // 0x97beb4: ClosureCall
    //     0x97beb4: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97beb8: ldur            x2, [x0, #0x1f]
    //     0x97bebc: blr             x2
    // 0x97bec0: r0 = Null
    //     0x97bec0: mov             x0, NULL
    // 0x97bec4: LeaveFrame
    //     0x97bec4: mov             SP, fp
    //     0x97bec8: ldp             fp, lr, [SP], #0x10
    // 0x97becc: ret
    //     0x97becc: ret             
    // 0x97bed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97bed0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97bed4: b               #0x97be68
  }
  get _ videoData(/* No info */) async {
    // ** addr: 0x97c498, size: 0x80
    // 0x97c498: EnterFrame
    //     0x97c498: stp             fp, lr, [SP, #-0x10]!
    //     0x97c49c: mov             fp, SP
    // 0x97c4a0: AllocStack(0x10)
    //     0x97c4a0: sub             SP, SP, #0x10
    // 0x97c4a4: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x10 */)
    //     0x97c4a4: stur            NULL, [fp, #-8]
    //     0x97c4a8: stur            x1, [fp, #-0x10]
    // 0x97c4ac: CheckStackOverflow
    //     0x97c4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c4b0: cmp             SP, x16
    //     0x97c4b4: b.ls            #0x97c510
    // 0x97c4b8: InitAsync() -> Future<VideoData>
    //     0x97c4b8: add             x0, PP, #0x49, lsl #12  ; [pp+0x49cf8] TypeArguments: <VideoData>
    //     0x97c4bc: ldr             x0, [x0, #0xcf8]
    //     0x97c4c0: bl              #0x661298  ; InitAsyncStub
    // 0x97c4c4: ldur            x1, [fp, #-0x10]
    // 0x97c4c8: r0 = _evalWithResult()
    //     0x97c4c8: bl              #0x97c7d4  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_evalWithResult
    // 0x97c4cc: mov             x1, x0
    // 0x97c4d0: stur            x1, [fp, #-0x10]
    // 0x97c4d4: r0 = Await()
    //     0x97c4d4: bl              #0x661044  ; AwaitStub
    // 0x97c4d8: mov             x1, x0
    // 0x97c4dc: r0 = jsonDecode()
    //     0x97c4dc: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x97c4e0: mov             x3, x0
    // 0x97c4e4: r2 = Null
    //     0x97c4e4: mov             x2, NULL
    // 0x97c4e8: r1 = Null
    //     0x97c4e8: mov             x1, NULL
    // 0x97c4ec: stur            x3, [fp, #-0x10]
    // 0x97c4f0: r8 = Map<String, dynamic>
    //     0x97c4f0: ldr             x8, [PP, #0x210]  ; [pp+0x210] Type: Map<String, dynamic>
    // 0x97c4f4: r3 = Null
    //     0x97c4f4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49d00] Null
    //     0x97c4f8: ldr             x3, [x3, #0xd00]
    // 0x97c4fc: r0 = Map<String, dynamic>()
    //     0x97c4fc: bl              #0x68b398  ; IsType_Map<String, dynamic>_Stub
    // 0x97c500: ldur            x2, [fp, #-0x10]
    // 0x97c504: r1 = Null
    //     0x97c504: mov             x1, NULL
    // 0x97c508: r0 = VideoData.fromMap()
    //     0x97c508: bl              #0x97c518  ; [package:youtube_player_iframe/src/iframe_api/src/functions/video_information.dart] VideoData::VideoData.fromMap
    // 0x97c50c: r0 = ReturnAsyncNotFuture()
    //     0x97c50c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97c510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c510: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c514: b               #0x97c4b8
  }
  _ _evalWithResult(/* No info */) async {
    // ** addr: 0x97c7d4, size: 0xdc
    // 0x97c7d4: EnterFrame
    //     0x97c7d4: stp             fp, lr, [SP, #-0x10]!
    //     0x97c7d8: mov             fp, SP
    // 0x97c7dc: AllocStack(0x20)
    //     0x97c7dc: sub             SP, SP, #0x20
    // 0x97c7e0: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x10 */)
    //     0x97c7e0: stur            NULL, [fp, #-8]
    //     0x97c7e4: stur            x1, [fp, #-0x10]
    // 0x97c7e8: CheckStackOverflow
    //     0x97c7e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c7ec: cmp             SP, x16
    //     0x97c7f0: b.ls            #0x97c890
    // 0x97c7f4: InitAsync() -> Future<String>
    //     0x97c7f4: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0x97c7f8: bl              #0x661298  ; InitAsyncStub
    // 0x97c7fc: ldur            x1, [fp, #-0x10]
    // 0x97c800: LoadField: r0 = r1->field_13
    //     0x97c800: ldur            w0, [x1, #0x13]
    // 0x97c804: DecompressPointer r0
    //     0x97c804: add             x0, x0, HEAP, lsl #32
    // 0x97c808: r16 = Sentinel
    //     0x97c808: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97c80c: cmp             w0, w16
    // 0x97c810: b.eq            #0x97c898
    // 0x97c814: LoadField: r2 = r0->field_f
    //     0x97c814: ldur            w2, [x0, #0xf]
    // 0x97c818: DecompressPointer r2
    //     0x97c818: add             x2, x2, HEAP, lsl #32
    // 0x97c81c: LoadField: r3 = r2->field_b
    //     0x97c81c: ldur            w3, [x2, #0xb]
    // 0x97c820: DecompressPointer r3
    //     0x97c820: add             x3, x3, HEAP, lsl #32
    // 0x97c824: mov             x0, x3
    // 0x97c828: stur            x3, [fp, #-0x18]
    // 0x97c82c: r0 = Await()
    //     0x97c82c: bl              #0x661044  ; AwaitStub
    // 0x97c830: ldur            x0, [fp, #-0x10]
    // 0x97c834: LoadField: r1 = r0->field_f
    //     0x97c834: ldur            w1, [x0, #0xf]
    // 0x97c838: DecompressPointer r1
    //     0x97c838: add             x1, x1, HEAP, lsl #32
    // 0x97c83c: r16 = Sentinel
    //     0x97c83c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97c840: cmp             w1, w16
    // 0x97c844: b.eq            #0x97c8a4
    // 0x97c848: r2 = "getVideoData()"
    //     0x97c848: add             x2, PP, #0x49, lsl #12  ; [pp+0x49d78] "getVideoData()"
    //     0x97c84c: ldr             x2, [x2, #0xd78]
    // 0x97c850: r0 = runJavaScriptReturningResult()
    //     0x97c850: bl              #0x97c8b0  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::runJavaScriptReturningResult
    // 0x97c854: mov             x1, x0
    // 0x97c858: stur            x1, [fp, #-0x10]
    // 0x97c85c: r0 = Await()
    //     0x97c85c: bl              #0x661044  ; AwaitStub
    // 0x97c860: r1 = 60
    //     0x97c860: movz            x1, #0x3c
    // 0x97c864: branchIfSmi(r0, 0x97c870)
    //     0x97c864: tbz             w0, #0, #0x97c870
    // 0x97c868: r1 = LoadClassIdInstr(r0)
    //     0x97c868: ldur            x1, [x0, #-1]
    //     0x97c86c: ubfx            x1, x1, #0xc, #0x14
    // 0x97c870: str             x0, [SP]
    // 0x97c874: mov             x0, x1
    // 0x97c878: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x97c878: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x97c87c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x97c87c: movz            x17, #0x2b03
    //     0x97c880: add             lr, x0, x17
    //     0x97c884: ldr             lr, [x21, lr, lsl #3]
    //     0x97c888: blr             lr
    // 0x97c88c: r0 = ReturnAsyncNotFuture()
    //     0x97c88c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97c890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c890: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c894: b               #0x97c7f4
    // 0x97c898: r9 = _eventHandler
    //     0x97c898: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f28] Field <YoutubePlayerController._eventHandler@2476072021>: late final (offset: 0x14)
    //     0x97c89c: ldr             x9, [x9, #0xf28]
    // 0x97c8a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97c8a0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x97c8a4: r9 = webViewController
    //     0x97c8a4: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f20] Field <YoutubePlayerController.webViewController>: late final (offset: 0x10)
    //     0x97c8a8: ldr             x9, [x9, #0xf20]
    // 0x97c8ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97c8ac: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ duration(/* No info */) async {
    // ** addr: 0x97c9dc, size: 0x9c
    // 0x97c9dc: EnterFrame
    //     0x97c9dc: stp             fp, lr, [SP, #-0x10]!
    //     0x97c9e0: mov             fp, SP
    // 0x97c9e4: AllocStack(0x10)
    //     0x97c9e4: sub             SP, SP, #0x10
    // 0x97c9e8: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x10 */)
    //     0x97c9e8: stur            NULL, [fp, #-8]
    //     0x97c9ec: stur            x1, [fp, #-0x10]
    // 0x97c9f0: CheckStackOverflow
    //     0x97c9f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c9f4: cmp             SP, x16
    //     0x97c9f8: b.ls            #0x97ca60
    // 0x97c9fc: InitAsync() -> Future<double>
    //     0x97c9fc: ldr             x0, [PP, #0x4158]  ; [pp+0x4158] TypeArguments: <double>
    //     0x97ca00: bl              #0x661298  ; InitAsyncStub
    // 0x97ca04: ldur            x1, [fp, #-0x10]
    // 0x97ca08: r0 = _runWithResult()
    //     0x97ca08: bl              #0x97ca78  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_runWithResult
    // 0x97ca0c: mov             x1, x0
    // 0x97ca10: stur            x1, [fp, #-0x10]
    // 0x97ca14: r0 = Await()
    //     0x97ca14: bl              #0x661044  ; AwaitStub
    // 0x97ca18: mov             x1, x0
    // 0x97ca1c: r0 = _parse()
    //     0x97ca1c: bl              #0x61caa4  ; [dart:core] double::_parse
    // 0x97ca20: cmp             w0, NULL
    // 0x97ca24: b.ne            #0x97ca30
    // 0x97ca28: d0 = 0.000000
    //     0x97ca28: eor             v0.16b, v0.16b, v0.16b
    // 0x97ca2c: b               #0x97ca34
    // 0x97ca30: LoadField: d0 = r0->field_7
    //     0x97ca30: ldur            d0, [x0, #7]
    // 0x97ca34: r0 = inline_Allocate_Double()
    //     0x97ca34: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x97ca38: add             x0, x0, #0x10
    //     0x97ca3c: cmp             x1, x0
    //     0x97ca40: b.ls            #0x97ca68
    //     0x97ca44: str             x0, [THR, #0x50]  ; THR::top
    //     0x97ca48: sub             x0, x0, #0xf
    //     0x97ca4c: movz            x1, #0xe15c
    //     0x97ca50: movk            x1, #0x3, lsl #16
    //     0x97ca54: stur            x1, [x0, #-1]
    // 0x97ca58: StoreField: r0->field_7 = d0
    //     0x97ca58: stur            d0, [x0, #7]
    // 0x97ca5c: r0 = ReturnAsyncNotFuture()
    //     0x97ca5c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97ca60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ca60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ca64: b               #0x97c9fc
    // 0x97ca68: SaveReg d0
    //     0x97ca68: str             q0, [SP, #-0x10]!
    // 0x97ca6c: r0 = AllocateDouble()
    //     0x97ca6c: bl              #0xec2254  ; AllocateDoubleStub
    // 0x97ca70: RestoreReg d0
    //     0x97ca70: ldr             q0, [SP], #0x10
    // 0x97ca74: b               #0x97ca58
  }
  _ _runWithResult(/* No info */) async {
    // ** addr: 0x97ca78, size: 0x12c
    // 0x97ca78: EnterFrame
    //     0x97ca78: stp             fp, lr, [SP, #-0x10]!
    //     0x97ca7c: mov             fp, SP
    // 0x97ca80: AllocStack(0x28)
    //     0x97ca80: sub             SP, SP, #0x28
    // 0x97ca84: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x10 */)
    //     0x97ca84: stur            NULL, [fp, #-8]
    //     0x97ca88: stur            x1, [fp, #-0x10]
    // 0x97ca8c: CheckStackOverflow
    //     0x97ca8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ca90: cmp             SP, x16
    //     0x97ca94: b.ls            #0x97cb90
    // 0x97ca98: InitAsync() -> Future<String>
    //     0x97ca98: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0x97ca9c: bl              #0x661298  ; InitAsyncStub
    // 0x97caa0: ldur            x1, [fp, #-0x10]
    // 0x97caa4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x97caa4: ldur            w0, [x1, #0x17]
    // 0x97caa8: DecompressPointer r0
    //     0x97caa8: add             x0, x0, HEAP, lsl #32
    // 0x97caac: LoadField: r2 = r0->field_b
    //     0x97caac: ldur            w2, [x0, #0xb]
    // 0x97cab0: DecompressPointer r2
    //     0x97cab0: add             x2, x2, HEAP, lsl #32
    // 0x97cab4: mov             x0, x2
    // 0x97cab8: stur            x2, [fp, #-0x18]
    // 0x97cabc: r0 = Await()
    //     0x97cabc: bl              #0x661044  ; AwaitStub
    // 0x97cac0: ldur            x1, [fp, #-0x10]
    // 0x97cac4: r2 = Null
    //     0x97cac4: mov             x2, NULL
    // 0x97cac8: r0 = _prepareData()
    //     0x97cac8: bl              #0x9709d4  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_prepareData
    // 0x97cacc: mov             x1, x0
    // 0x97cad0: stur            x1, [fp, #-0x18]
    // 0x97cad4: r0 = Await()
    //     0x97cad4: bl              #0x661044  ; AwaitStub
    // 0x97cad8: mov             x3, x0
    // 0x97cadc: ldur            x0, [fp, #-0x10]
    // 0x97cae0: stur            x3, [fp, #-0x20]
    // 0x97cae4: LoadField: r4 = r0->field_f
    //     0x97cae4: ldur            w4, [x0, #0xf]
    // 0x97cae8: DecompressPointer r4
    //     0x97cae8: add             x4, x4, HEAP, lsl #32
    // 0x97caec: r16 = Sentinel
    //     0x97caec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97caf0: cmp             w4, w16
    // 0x97caf4: b.eq            #0x97cb98
    // 0x97caf8: stur            x4, [fp, #-0x18]
    // 0x97cafc: r1 = Null
    //     0x97cafc: mov             x1, NULL
    // 0x97cb00: r2 = 10
    //     0x97cb00: movz            x2, #0xa
    // 0x97cb04: r0 = AllocateArray()
    //     0x97cb04: bl              #0xec22fc  ; AllocateArrayStub
    // 0x97cb08: r16 = "player."
    //     0x97cb08: add             x16, PP, #0x48, lsl #12  ; [pp+0x48f38] "player."
    //     0x97cb0c: ldr             x16, [x16, #0xf38]
    // 0x97cb10: StoreField: r0->field_f = r16
    //     0x97cb10: stur            w16, [x0, #0xf]
    // 0x97cb14: r16 = "getDuration"
    //     0x97cb14: add             x16, PP, #0x49, lsl #12  ; [pp+0x49d80] "getDuration"
    //     0x97cb18: ldr             x16, [x16, #0xd80]
    // 0x97cb1c: StoreField: r0->field_13 = r16
    //     0x97cb1c: stur            w16, [x0, #0x13]
    // 0x97cb20: r16 = "("
    //     0x97cb20: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "("
    //     0x97cb24: ldr             x16, [x16, #0xf08]
    // 0x97cb28: ArrayStore: r0[0] = r16  ; List_4
    //     0x97cb28: stur            w16, [x0, #0x17]
    // 0x97cb2c: ldur            x1, [fp, #-0x20]
    // 0x97cb30: StoreField: r0->field_1b = r1
    //     0x97cb30: stur            w1, [x0, #0x1b]
    // 0x97cb34: r16 = ");"
    //     0x97cb34: add             x16, PP, #0x48, lsl #12  ; [pp+0x48f40] ");"
    //     0x97cb38: ldr             x16, [x16, #0xf40]
    // 0x97cb3c: StoreField: r0->field_1f = r16
    //     0x97cb3c: stur            w16, [x0, #0x1f]
    // 0x97cb40: str             x0, [SP]
    // 0x97cb44: r0 = _interpolate()
    //     0x97cb44: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x97cb48: ldur            x1, [fp, #-0x18]
    // 0x97cb4c: mov             x2, x0
    // 0x97cb50: r0 = runJavaScriptReturningResult()
    //     0x97cb50: bl              #0x97c8b0  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::runJavaScriptReturningResult
    // 0x97cb54: mov             x1, x0
    // 0x97cb58: stur            x1, [fp, #-0x10]
    // 0x97cb5c: r0 = Await()
    //     0x97cb5c: bl              #0x661044  ; AwaitStub
    // 0x97cb60: r1 = 60
    //     0x97cb60: movz            x1, #0x3c
    // 0x97cb64: branchIfSmi(r0, 0x97cb70)
    //     0x97cb64: tbz             w0, #0, #0x97cb70
    // 0x97cb68: r1 = LoadClassIdInstr(r0)
    //     0x97cb68: ldur            x1, [x0, #-1]
    //     0x97cb6c: ubfx            x1, x1, #0xc, #0x14
    // 0x97cb70: str             x0, [SP]
    // 0x97cb74: mov             x0, x1
    // 0x97cb78: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x97cb78: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x97cb7c: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x97cb7c: movz            x17, #0x2b03
    //     0x97cb80: add             lr, x0, x17
    //     0x97cb84: ldr             lr, [x21, lr, lsl #3]
    //     0x97cb88: blr             lr
    // 0x97cb8c: r0 = ReturnAsyncNotFuture()
    //     0x97cb8c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97cb90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97cb90: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97cb94: b               #0x97ca98
    // 0x97cb98: r9 = webViewController
    //     0x97cb98: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f20] Field <YoutubePlayerController.webViewController>: late final (offset: 0x10)
    //     0x97cb9c: ldr             x9, [x9, #0xf20]
    // 0x97cba0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97cba0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] NavigationDecision <anonymous closure>(dynamic, NavigationRequest) {
    // ** addr: 0x97cfd0, size: 0x6c
    // 0x97cfd0: EnterFrame
    //     0x97cfd0: stp             fp, lr, [SP, #-0x10]!
    //     0x97cfd4: mov             fp, SP
    // 0x97cfd8: AllocStack(0x8)
    //     0x97cfd8: sub             SP, SP, #8
    // 0x97cfdc: SetupParameters()
    //     0x97cfdc: ldr             x0, [fp, #0x18]
    //     0x97cfe0: ldur            w2, [x0, #0x17]
    //     0x97cfe4: add             x2, x2, HEAP, lsl #32
    //     0x97cfe8: stur            x2, [fp, #-8]
    // 0x97cfec: CheckStackOverflow
    //     0x97cfec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97cff0: cmp             SP, x16
    //     0x97cff4: b.ls            #0x97d034
    // 0x97cff8: ldr             x0, [fp, #0x10]
    // 0x97cffc: LoadField: r1 = r0->field_7
    //     0x97cffc: ldur            w1, [x0, #7]
    // 0x97d000: DecompressPointer r1
    //     0x97d000: add             x1, x1, HEAP, lsl #32
    // 0x97d004: r0 = tryParse()
    //     0x97d004: bl              #0x65bc0c  ; [dart:core] Uri::tryParse
    // 0x97d008: mov             x1, x0
    // 0x97d00c: ldur            x0, [fp, #-8]
    // 0x97d010: LoadField: r2 = r0->field_f
    //     0x97d010: ldur            w2, [x0, #0xf]
    // 0x97d014: DecompressPointer r2
    //     0x97d014: add             x2, x2, HEAP, lsl #32
    // 0x97d018: mov             x16, x1
    // 0x97d01c: mov             x1, x2
    // 0x97d020: mov             x2, x16
    // 0x97d024: r0 = _decideNavigation()
    //     0x97d024: bl              #0x97d03c  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_decideNavigation
    // 0x97d028: LeaveFrame
    //     0x97d028: mov             SP, fp
    //     0x97d02c: ldp             fp, lr, [SP], #0x10
    // 0x97d030: ret
    //     0x97d030: ret             
    // 0x97d034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97d034: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97d038: b               #0x97cff8
  }
  _ _decideNavigation(/* No info */) {
    // ** addr: 0x97d03c, size: 0x2c8
    // 0x97d03c: EnterFrame
    //     0x97d03c: stp             fp, lr, [SP, #-0x10]!
    //     0x97d040: mov             fp, SP
    // 0x97d044: AllocStack(0x38)
    //     0x97d044: sub             SP, SP, #0x38
    // 0x97d048: SetupParameters(YoutubePlayerController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x97d048: mov             x3, x1
    //     0x97d04c: stur            x1, [fp, #-8]
    //     0x97d050: stur            x2, [fp, #-0x10]
    // 0x97d054: CheckStackOverflow
    //     0x97d054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97d058: cmp             SP, x16
    //     0x97d05c: b.ls            #0x97d2fc
    // 0x97d060: cmp             w2, NULL
    // 0x97d064: b.ne            #0x97d07c
    // 0x97d068: r0 = Instance_NavigationDecision
    //     0x97d068: add             x0, PP, #0x49, lsl #12  ; [pp+0x490a0] Obj!NavigationDecision@e2d731
    //     0x97d06c: ldr             x0, [x0, #0xa0]
    // 0x97d070: LeaveFrame
    //     0x97d070: mov             SP, fp
    //     0x97d074: ldp             fp, lr, [SP], #0x10
    // 0x97d078: ret
    //     0x97d078: ret             
    // 0x97d07c: r0 = LoadClassIdInstr(r2)
    //     0x97d07c: ldur            x0, [x2, #-1]
    //     0x97d080: ubfx            x0, x0, #0xc, #0x14
    // 0x97d084: mov             x1, x2
    // 0x97d088: r0 = GDT[cid_x0 + -0xe4b]()
    //     0x97d088: sub             lr, x0, #0xe4b
    //     0x97d08c: ldr             lr, [x21, lr, lsl #3]
    //     0x97d090: blr             lr
    // 0x97d094: mov             x3, x0
    // 0x97d098: ldur            x2, [fp, #-0x10]
    // 0x97d09c: stur            x3, [fp, #-0x18]
    // 0x97d0a0: r0 = LoadClassIdInstr(r2)
    //     0x97d0a0: ldur            x0, [x2, #-1]
    //     0x97d0a4: ubfx            x0, x0, #0xc, #0x14
    // 0x97d0a8: mov             x1, x2
    // 0x97d0ac: r0 = GDT[cid_x0 + -0xf97]()
    //     0x97d0ac: sub             lr, x0, #0xf97
    //     0x97d0b0: ldr             lr, [x21, lr, lsl #3]
    //     0x97d0b4: blr             lr
    // 0x97d0b8: mov             x3, x0
    // 0x97d0bc: ldur            x2, [fp, #-0x10]
    // 0x97d0c0: stur            x3, [fp, #-0x20]
    // 0x97d0c4: r0 = LoadClassIdInstr(r2)
    //     0x97d0c4: ldur            x0, [x2, #-1]
    //     0x97d0c8: ubfx            x0, x0, #0xc, #0x14
    // 0x97d0cc: mov             x1, x2
    // 0x97d0d0: r0 = GDT[cid_x0 + -0xffa]()
    //     0x97d0d0: sub             lr, x0, #0xffa
    //     0x97d0d4: ldr             lr, [x21, lr, lsl #3]
    //     0x97d0d8: blr             lr
    // 0x97d0dc: mov             x4, x0
    // 0x97d0e0: ldur            x3, [fp, #-0x20]
    // 0x97d0e4: stur            x4, [fp, #-0x28]
    // 0x97d0e8: r0 = LoadClassIdInstr(r3)
    //     0x97d0e8: ldur            x0, [x3, #-1]
    //     0x97d0ec: ubfx            x0, x0, #0xc, #0x14
    // 0x97d0f0: mov             x1, x3
    // 0x97d0f4: r2 = "facebook"
    //     0x97d0f4: add             x2, PP, #0x49, lsl #12  ; [pp+0x490a8] "facebook"
    //     0x97d0f8: ldr             x2, [x2, #0xa8]
    // 0x97d0fc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x97d0fc: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x97d100: r0 = GDT[cid_x0 + -0xffc]()
    //     0x97d100: sub             lr, x0, #0xffc
    //     0x97d104: ldr             lr, [x21, lr, lsl #3]
    //     0x97d108: blr             lr
    // 0x97d10c: tbz             w0, #4, #0x97d168
    // 0x97d110: ldur            x3, [fp, #-0x20]
    // 0x97d114: r0 = LoadClassIdInstr(r3)
    //     0x97d114: ldur            x0, [x3, #-1]
    //     0x97d118: ubfx            x0, x0, #0xc, #0x14
    // 0x97d11c: mov             x1, x3
    // 0x97d120: r2 = "twitter"
    //     0x97d120: add             x2, PP, #0x49, lsl #12  ; [pp+0x490b0] "twitter"
    //     0x97d124: ldr             x2, [x2, #0xb0]
    // 0x97d128: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x97d128: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x97d12c: r0 = GDT[cid_x0 + -0xffc]()
    //     0x97d12c: sub             lr, x0, #0xffc
    //     0x97d130: ldr             lr, [x21, lr, lsl #3]
    //     0x97d134: blr             lr
    // 0x97d138: tbz             w0, #4, #0x97d168
    // 0x97d13c: ldur            x0, [fp, #-0x20]
    // 0x97d140: r1 = LoadClassIdInstr(r0)
    //     0x97d140: ldur            x1, [x0, #-1]
    //     0x97d144: ubfx            x1, x1, #0xc, #0x14
    // 0x97d148: r16 = "youtu"
    //     0x97d148: add             x16, PP, #0x49, lsl #12  ; [pp+0x490b8] "youtu"
    //     0x97d14c: ldr             x16, [x16, #0xb8]
    // 0x97d150: stp             x16, x0, [SP]
    // 0x97d154: mov             x0, x1
    // 0x97d158: mov             lr, x0
    // 0x97d15c: ldr             lr, [x21, lr, lsl #3]
    // 0x97d160: blr             lr
    // 0x97d164: tbnz            w0, #4, #0x97d174
    // 0x97d168: r0 = "social"
    //     0x97d168: add             x0, PP, #0x49, lsl #12  ; [pp+0x490c0] "social"
    //     0x97d16c: ldr             x0, [x0, #0xc0]
    // 0x97d170: b               #0x97d200
    // 0x97d174: ldur            x3, [fp, #-0x18]
    // 0x97d178: r0 = LoadClassIdInstr(r3)
    //     0x97d178: ldur            x0, [x3, #-1]
    //     0x97d17c: ubfx            x0, x0, #0xc, #0x14
    // 0x97d180: mov             x1, x3
    // 0x97d184: r2 = "feature"
    //     0x97d184: add             x2, PP, #0x49, lsl #12  ; [pp+0x490c8] "feature"
    //     0x97d188: ldr             x2, [x2, #0xc8]
    // 0x97d18c: r0 = GDT[cid_x0 + 0x55f]()
    //     0x97d18c: add             lr, x0, #0x55f
    //     0x97d190: ldr             lr, [x21, lr, lsl #3]
    //     0x97d194: blr             lr
    // 0x97d198: tbnz            w0, #4, #0x97d1c4
    // 0x97d19c: ldur            x3, [fp, #-0x18]
    // 0x97d1a0: r0 = LoadClassIdInstr(r3)
    //     0x97d1a0: ldur            x0, [x3, #-1]
    //     0x97d1a4: ubfx            x0, x0, #0xc, #0x14
    // 0x97d1a8: mov             x1, x3
    // 0x97d1ac: r2 = "feature"
    //     0x97d1ac: add             x2, PP, #0x49, lsl #12  ; [pp+0x490c8] "feature"
    //     0x97d1b0: ldr             x2, [x2, #0xc8]
    // 0x97d1b4: r0 = GDT[cid_x0 + -0x114]()
    //     0x97d1b4: sub             lr, x0, #0x114
    //     0x97d1b8: ldr             lr, [x21, lr, lsl #3]
    //     0x97d1bc: blr             lr
    // 0x97d1c0: b               #0x97d200
    // 0x97d1c4: ldur            x0, [fp, #-0x28]
    // 0x97d1c8: r1 = LoadClassIdInstr(r0)
    //     0x97d1c8: ldur            x1, [x0, #-1]
    //     0x97d1cc: ubfx            x1, x1, #0xc, #0x14
    // 0x97d1d0: r16 = "/watch"
    //     0x97d1d0: add             x16, PP, #0x49, lsl #12  ; [pp+0x490d0] "/watch"
    //     0x97d1d4: ldr             x16, [x16, #0xd0]
    // 0x97d1d8: stp             x16, x0, [SP]
    // 0x97d1dc: mov             x0, x1
    // 0x97d1e0: mov             lr, x0
    // 0x97d1e4: ldr             lr, [x21, lr, lsl #3]
    // 0x97d1e8: blr             lr
    // 0x97d1ec: tbnz            w0, #4, #0x97d1fc
    // 0x97d1f0: r0 = "emb_info"
    //     0x97d1f0: add             x0, PP, #0x49, lsl #12  ; [pp+0x490d8] "emb_info"
    //     0x97d1f4: ldr             x0, [x0, #0xd8]
    // 0x97d1f8: b               #0x97d200
    // 0x97d1fc: r0 = Null
    //     0x97d1fc: mov             x0, NULL
    // 0x97d200: stur            x0, [fp, #-0x20]
    // 0x97d204: r16 = "emb_rel_pause"
    //     0x97d204: add             x16, PP, #0x49, lsl #12  ; [pp+0x490e0] "emb_rel_pause"
    //     0x97d208: ldr             x16, [x16, #0xe0]
    // 0x97d20c: stp             x0, x16, [SP]
    // 0x97d210: r0 = ==()
    //     0x97d210: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x97d214: tbz             w0, #4, #0x97d248
    // 0x97d218: r16 = "emb_rel_end"
    //     0x97d218: add             x16, PP, #0x49, lsl #12  ; [pp+0x490e8] "emb_rel_end"
    //     0x97d21c: ldr             x16, [x16, #0xe8]
    // 0x97d220: ldur            lr, [fp, #-0x20]
    // 0x97d224: stp             lr, x16, [SP]
    // 0x97d228: r0 = ==()
    //     0x97d228: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x97d22c: tbz             w0, #4, #0x97d248
    // 0x97d230: r16 = "emb_info"
    //     0x97d230: add             x16, PP, #0x49, lsl #12  ; [pp+0x490d8] "emb_info"
    //     0x97d234: ldr             x16, [x16, #0xd8]
    // 0x97d238: ldur            lr, [fp, #-0x20]
    // 0x97d23c: stp             lr, x16, [SP]
    // 0x97d240: r0 = ==()
    //     0x97d240: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x97d244: tbnz            w0, #4, #0x97d280
    // 0x97d248: ldur            x1, [fp, #-0x18]
    // 0x97d24c: r0 = LoadClassIdInstr(r1)
    //     0x97d24c: ldur            x0, [x1, #-1]
    //     0x97d250: ubfx            x0, x0, #0xc, #0x14
    // 0x97d254: r2 = "v"
    //     0x97d254: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b820] "v"
    //     0x97d258: ldr             x2, [x2, #0x820]
    // 0x97d25c: r0 = GDT[cid_x0 + -0x114]()
    //     0x97d25c: sub             lr, x0, #0x114
    //     0x97d260: ldr             lr, [x21, lr, lsl #3]
    //     0x97d264: blr             lr
    // 0x97d268: cmp             w0, NULL
    // 0x97d26c: b.eq            #0x97d2e8
    // 0x97d270: ldur            x1, [fp, #-8]
    // 0x97d274: mov             x2, x0
    // 0x97d278: r0 = loadVideoById()
    //     0x97d278: bl              #0x970a74  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::loadVideoById
    // 0x97d27c: b               #0x97d2e8
    // 0x97d280: r16 = "emb_title"
    //     0x97d280: add             x16, PP, #0x49, lsl #12  ; [pp+0x490f0] "emb_title"
    //     0x97d284: ldr             x16, [x16, #0xf0]
    // 0x97d288: ldur            lr, [fp, #-0x20]
    // 0x97d28c: stp             lr, x16, [SP]
    // 0x97d290: r0 = ==()
    //     0x97d290: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x97d294: tbz             w0, #4, #0x97d2e0
    // 0x97d298: r16 = "emb_logo"
    //     0x97d298: add             x16, PP, #0x49, lsl #12  ; [pp+0x490f8] "emb_logo"
    //     0x97d29c: ldr             x16, [x16, #0xf8]
    // 0x97d2a0: ldur            lr, [fp, #-0x20]
    // 0x97d2a4: stp             lr, x16, [SP]
    // 0x97d2a8: r0 = ==()
    //     0x97d2a8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x97d2ac: tbz             w0, #4, #0x97d2e0
    // 0x97d2b0: r16 = "social"
    //     0x97d2b0: add             x16, PP, #0x49, lsl #12  ; [pp+0x490c0] "social"
    //     0x97d2b4: ldr             x16, [x16, #0xc0]
    // 0x97d2b8: ldur            lr, [fp, #-0x20]
    // 0x97d2bc: stp             lr, x16, [SP]
    // 0x97d2c0: r0 = ==()
    //     0x97d2c0: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x97d2c4: tbz             w0, #4, #0x97d2e0
    // 0x97d2c8: r16 = "wl_button"
    //     0x97d2c8: add             x16, PP, #0x49, lsl #12  ; [pp+0x49100] "wl_button"
    //     0x97d2cc: ldr             x16, [x16, #0x100]
    // 0x97d2d0: ldur            lr, [fp, #-0x20]
    // 0x97d2d4: stp             lr, x16, [SP]
    // 0x97d2d8: r0 = ==()
    //     0x97d2d8: bl              #0xd80e68  ; [dart:core] _OneByteString::==
    // 0x97d2dc: tbnz            w0, #4, #0x97d2e8
    // 0x97d2e0: ldur            x1, [fp, #-0x10]
    // 0x97d2e4: r0 = launchUrl()
    //     0x97d2e4: bl              #0x97d304  ; [package:url_launcher/src/url_launcher_uri.dart] ::launchUrl
    // 0x97d2e8: r0 = Instance_NavigationDecision
    //     0x97d2e8: add             x0, PP, #0x49, lsl #12  ; [pp+0x490a0] Obj!NavigationDecision@e2d731
    //     0x97d2ec: ldr             x0, [x0, #0xa0]
    // 0x97d2f0: LeaveFrame
    //     0x97d2f0: mov             SP, fp
    //     0x97d2f4: ldp             fp, lr, [SP], #0x10
    // 0x97d2f8: ret
    //     0x97d2f8: ret             
    // 0x97d2fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97d2fc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97d300: b               #0x97d060
  }
  [closure] void <anonymous closure>(dynamic, WebResourceError) {
    // ** addr: 0x97d3e8, size: 0x5c
    // 0x97d3e8: EnterFrame
    //     0x97d3e8: stp             fp, lr, [SP, #-0x10]!
    //     0x97d3ec: mov             fp, SP
    // 0x97d3f0: AllocStack(0x10)
    //     0x97d3f0: sub             SP, SP, #0x10
    // 0x97d3f4: CheckStackOverflow
    //     0x97d3f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97d3f8: cmp             SP, x16
    //     0x97d3fc: b.ls            #0x97d43c
    // 0x97d400: ldr             x0, [fp, #0x10]
    // 0x97d404: LoadField: r2 = r0->field_7
    //     0x97d404: ldur            w2, [x0, #7]
    // 0x97d408: DecompressPointer r2
    //     0x97d408: add             x2, x2, HEAP, lsl #32
    // 0x97d40c: stur            x2, [fp, #-8]
    // 0x97d410: LoadField: r1 = r0->field_b
    //     0x97d410: ldur            w1, [x0, #0xb]
    // 0x97d414: DecompressPointer r1
    //     0x97d414: add             x1, x1, HEAP, lsl #32
    // 0x97d418: r0 = _enumToString()
    //     0x97d418: bl              #0xc4ef90  ; [package:webview_flutter_platform_interface/src/types/web_resource_error.dart] WebResourceErrorType::_enumToString
    // 0x97d41c: str             x0, [SP]
    // 0x97d420: ldur            x1, [fp, #-8]
    // 0x97d424: r4 = const [0, 0x2, 0x1, 0x1, name, 0x1, null]
    //     0x97d424: ldr             x4, [PP, #0x248]  ; [pp+0x248] List(7) [0, 0x2, 0x1, 0x1, "name", 0x1, Null]
    // 0x97d428: r0 = log()
    //     0x97d428: bl              #0x615d0c  ; [dart:developer] ::log
    // 0x97d42c: r0 = Null
    //     0x97d42c: mov             x0, NULL
    // 0x97d430: LeaveFrame
    //     0x97d430: mov             SP, fp
    //     0x97d434: ldp             fp, lr, [SP], #0x10
    // 0x97d438: ret
    //     0x97d438: ret             
    // 0x97d43c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97d43c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97d440: b               #0x97d400
  }
  _ playVideo(/* No info */) {
    // ** addr: 0x97db64, size: 0x38
    // 0x97db64: EnterFrame
    //     0x97db64: stp             fp, lr, [SP, #-0x10]!
    //     0x97db68: mov             fp, SP
    // 0x97db6c: CheckStackOverflow
    //     0x97db6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97db70: cmp             SP, x16
    //     0x97db74: b.ls            #0x97db94
    // 0x97db78: r2 = "playVideo"
    //     0x97db78: add             x2, PP, #0x48, lsl #12  ; [pp+0x48fe0] "playVideo"
    //     0x97db7c: ldr             x2, [x2, #0xfe0]
    // 0x97db80: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x97db80: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x97db84: r0 = _run()
    //     0x97db84: bl              #0x965a7c  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_run
    // 0x97db88: LeaveFrame
    //     0x97db88: mov             SP, fp
    //     0x97db8c: ldp             fp, lr, [SP], #0x10
    // 0x97db90: ret
    //     0x97db90: ret             
    // 0x97db94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97db94: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97db98: b               #0x97db78
  }
  _ init(/* No info */) async {
    // ** addr: 0x981c94, size: 0xbc
    // 0x981c94: EnterFrame
    //     0x981c94: stp             fp, lr, [SP, #-0x10]!
    //     0x981c98: mov             fp, SP
    // 0x981c9c: AllocStack(0x20)
    //     0x981c9c: sub             SP, SP, #0x20
    // 0x981ca0: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x10 */)
    //     0x981ca0: stur            NULL, [fp, #-8]
    //     0x981ca4: stur            x1, [fp, #-0x10]
    // 0x981ca8: CheckStackOverflow
    //     0x981ca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981cac: cmp             SP, x16
    //     0x981cb0: b.ls            #0x981d48
    // 0x981cb4: InitAsync() -> Future<void?>
    //     0x981cb4: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x981cb8: bl              #0x661298  ; InitAsyncStub
    // 0x981cbc: ldur            x0, [fp, #-0x10]
    // 0x981cc0: LoadField: r3 = r0->field_b
    //     0x981cc0: ldur            w3, [x0, #0xb]
    // 0x981cc4: DecompressPointer r3
    //     0x981cc4: add             x3, x3, HEAP, lsl #32
    // 0x981cc8: stur            x3, [fp, #-0x18]
    // 0x981ccc: r1 = Null
    //     0x981ccc: mov             x1, NULL
    // 0x981cd0: r2 = 4
    //     0x981cd0: movz            x2, #0x4
    // 0x981cd4: r0 = AllocateArray()
    //     0x981cd4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x981cd8: r16 = "Youtube"
    //     0x981cd8: add             x16, PP, #0x49, lsl #12  ; [pp+0x49058] "Youtube"
    //     0x981cdc: ldr             x16, [x16, #0x58]
    // 0x981ce0: StoreField: r0->field_f = r16
    //     0x981ce0: stur            w16, [x0, #0xf]
    // 0x981ce4: ldur            x1, [fp, #-0x10]
    // 0x981ce8: LoadField: r2 = r1->field_7
    //     0x981ce8: ldur            w2, [x1, #7]
    // 0x981cec: DecompressPointer r2
    //     0x981cec: add             x2, x2, HEAP, lsl #32
    // 0x981cf0: StoreField: r0->field_13 = r2
    //     0x981cf0: stur            w2, [x0, #0x13]
    // 0x981cf4: str             x0, [SP]
    // 0x981cf8: r0 = _interpolate()
    //     0x981cf8: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x981cfc: ldur            x1, [fp, #-0x10]
    // 0x981d00: mov             x2, x0
    // 0x981d04: ldur            x3, [fp, #-0x18]
    // 0x981d08: r0 = load()
    //     0x981d08: bl              #0x981d50  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::load
    // 0x981d0c: mov             x1, x0
    // 0x981d10: stur            x1, [fp, #-0x18]
    // 0x981d14: r0 = Await()
    //     0x981d14: bl              #0x661044  ; AwaitStub
    // 0x981d18: ldur            x0, [fp, #-0x10]
    // 0x981d1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x981d1c: ldur            w1, [x0, #0x17]
    // 0x981d20: DecompressPointer r1
    //     0x981d20: add             x1, x1, HEAP, lsl #32
    // 0x981d24: LoadField: r0 = r1->field_b
    //     0x981d24: ldur            w0, [x1, #0xb]
    // 0x981d28: DecompressPointer r0
    //     0x981d28: add             x0, x0, HEAP, lsl #32
    // 0x981d2c: LoadField: r2 = r0->field_b
    //     0x981d2c: ldur            x2, [x0, #0xb]
    // 0x981d30: tst             x2, #0x1e
    // 0x981d34: b.ne            #0x981d40
    // 0x981d38: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x981d38: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x981d3c: r0 = complete()
    //     0x981d3c: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0x981d40: r0 = Null
    //     0x981d40: mov             x0, NULL
    // 0x981d44: r0 = ReturnAsyncNotFuture()
    //     0x981d44: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x981d48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981d48: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981d4c: b               #0x981cb4
  }
  _ load(/* No info */) async {
    // ** addr: 0x981d50, size: 0x184
    // 0x981d50: EnterFrame
    //     0x981d50: stp             fp, lr, [SP, #-0x10]!
    //     0x981d54: mov             fp, SP
    // 0x981d58: AllocStack(0x40)
    //     0x981d58: sub             SP, SP, #0x40
    // 0x981d5c: SetupParameters(YoutubePlayerController this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x981d5c: stur            NULL, [fp, #-8]
    //     0x981d60: stur            x1, [fp, #-0x10]
    //     0x981d64: mov             x16, x3
    //     0x981d68: mov             x3, x1
    //     0x981d6c: mov             x1, x16
    //     0x981d70: stur            x2, [fp, #-0x18]
    //     0x981d74: stur            x1, [fp, #-0x20]
    // 0x981d78: CheckStackOverflow
    //     0x981d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981d7c: cmp             SP, x16
    //     0x981d80: b.ls            #0x981ec0
    // 0x981d84: InitAsync() -> Future<void?>
    //     0x981d84: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x981d88: bl              #0x661298  ; InitAsyncStub
    // 0x981d8c: r16 = "android"
    //     0x981d8c: ldr             x16, [PP, #0x39c8]  ; [pp+0x39c8] "android"
    // 0x981d90: str             x16, [SP]
    // 0x981d94: r0 = toLowerCase()
    //     0x981d94: bl              #0xebeae4  ; [dart:core] _OneByteString::toLowerCase
    // 0x981d98: r1 = Null
    //     0x981d98: mov             x1, NULL
    // 0x981d9c: r2 = 20
    //     0x981d9c: movz            x2, #0x14
    // 0x981da0: stur            x0, [fp, #-0x28]
    // 0x981da4: r0 = AllocateArray()
    //     0x981da4: bl              #0xec22fc  ; AllocateArrayStub
    // 0x981da8: stur            x0, [fp, #-0x30]
    // 0x981dac: r16 = "playerId"
    //     0x981dac: add             x16, PP, #0x29, lsl #12  ; [pp+0x29c50] "playerId"
    //     0x981db0: ldr             x16, [x16, #0xc50]
    // 0x981db4: StoreField: r0->field_f = r16
    //     0x981db4: stur            w16, [x0, #0xf]
    // 0x981db8: ldur            x1, [fp, #-0x18]
    // 0x981dbc: StoreField: r0->field_13 = r1
    //     0x981dbc: stur            w1, [x0, #0x13]
    // 0x981dc0: r16 = "pointerEvents"
    //     0x981dc0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b30] "pointerEvents"
    //     0x981dc4: ldr             x16, [x16, #0xb30]
    // 0x981dc8: ArrayStore: r0[0] = r16  ; List_4
    //     0x981dc8: stur            w16, [x0, #0x17]
    // 0x981dcc: r16 = "initial"
    //     0x981dcc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b38] "initial"
    //     0x981dd0: ldr             x16, [x16, #0xb38]
    // 0x981dd4: StoreField: r0->field_1b = r16
    //     0x981dd4: stur            w16, [x0, #0x1b]
    // 0x981dd8: r16 = "playerVars"
    //     0x981dd8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b40] "playerVars"
    //     0x981ddc: ldr             x16, [x16, #0xb40]
    // 0x981de0: StoreField: r0->field_1f = r16
    //     0x981de0: stur            w16, [x0, #0x1f]
    // 0x981de4: ldur            x1, [fp, #-0x20]
    // 0x981de8: r0 = toJson()
    //     0x981de8: bl              #0x97d4a4  ; [package:youtube_player_iframe/src/player_params.dart] YoutubePlayerParams::toJson
    // 0x981dec: ldur            x1, [fp, #-0x30]
    // 0x981df0: ArrayStore: r1[5] = r0  ; List_4
    //     0x981df0: add             x25, x1, #0x23
    //     0x981df4: str             w0, [x25]
    //     0x981df8: tbz             w0, #0, #0x981e14
    //     0x981dfc: ldurb           w16, [x1, #-1]
    //     0x981e00: ldurb           w17, [x0, #-1]
    //     0x981e04: and             x16, x17, x16, lsr #2
    //     0x981e08: tst             x16, HEAP, lsr #32
    //     0x981e0c: b.eq            #0x981e14
    //     0x981e10: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x981e14: ldur            x2, [fp, #-0x30]
    // 0x981e18: r16 = "platform"
    //     0x981e18: ldr             x16, [PP, #0x5308]  ; [pp+0x5308] "platform"
    // 0x981e1c: StoreField: r2->field_27 = r16
    //     0x981e1c: stur            w16, [x2, #0x27]
    // 0x981e20: mov             x1, x2
    // 0x981e24: ldur            x0, [fp, #-0x28]
    // 0x981e28: ArrayStore: r1[7] = r0  ; List_4
    //     0x981e28: add             x25, x1, #0x2b
    //     0x981e2c: str             w0, [x25]
    //     0x981e30: tbz             w0, #0, #0x981e4c
    //     0x981e34: ldurb           w16, [x1, #-1]
    //     0x981e38: ldurb           w17, [x0, #-1]
    //     0x981e3c: and             x16, x17, x16, lsr #2
    //     0x981e40: tst             x16, HEAP, lsr #32
    //     0x981e44: b.eq            #0x981e4c
    //     0x981e48: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x981e4c: r16 = "host"
    //     0x981e4c: ldr             x16, [PP, #0x3630]  ; [pp+0x3630] "host"
    // 0x981e50: StoreField: r2->field_2f = r16
    //     0x981e50: stur            w16, [x2, #0x2f]
    // 0x981e54: r16 = "https://www.youtube.com"
    //     0x981e54: add             x16, PP, #0x48, lsl #12  ; [pp+0x48fa0] "https://www.youtube.com"
    //     0x981e58: ldr             x16, [x16, #0xfa0]
    // 0x981e5c: StoreField: r2->field_33 = r16
    //     0x981e5c: stur            w16, [x2, #0x33]
    // 0x981e60: r16 = <String, String>
    //     0x981e60: add             x16, PP, #0xd, lsl #12  ; [pp+0xd668] TypeArguments: <String, String>
    //     0x981e64: ldr             x16, [x16, #0x668]
    // 0x981e68: stp             x2, x16, [SP]
    // 0x981e6c: r0 = Map._fromLiteral()
    //     0x981e6c: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x981e70: ldur            x1, [fp, #-0x10]
    // 0x981e74: LoadField: r3 = r1->field_f
    //     0x981e74: ldur            w3, [x1, #0xf]
    // 0x981e78: DecompressPointer r3
    //     0x981e78: add             x3, x3, HEAP, lsl #32
    // 0x981e7c: r16 = Sentinel
    //     0x981e7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x981e80: cmp             w3, w16
    // 0x981e84: b.eq            #0x981ec8
    // 0x981e88: mov             x2, x0
    // 0x981e8c: stur            x3, [fp, #-0x18]
    // 0x981e90: r0 = _buildPlayerHTML()
    //     0x981e90: bl              #0x982248  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_buildPlayerHTML
    // 0x981e94: mov             x1, x0
    // 0x981e98: stur            x1, [fp, #-0x10]
    // 0x981e9c: r0 = Await()
    //     0x981e9c: bl              #0x661044  ; AwaitStub
    // 0x981ea0: ldur            x1, [fp, #-0x18]
    // 0x981ea4: mov             x2, x0
    // 0x981ea8: r0 = loadHtmlString()
    //     0x981ea8: bl              #0x981ed4  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::loadHtmlString
    // 0x981eac: mov             x1, x0
    // 0x981eb0: stur            x1, [fp, #-0x10]
    // 0x981eb4: r0 = Await()
    //     0x981eb4: bl              #0x661044  ; AwaitStub
    // 0x981eb8: r0 = Null
    //     0x981eb8: mov             x0, NULL
    // 0x981ebc: r0 = ReturnAsyncNotFuture()
    //     0x981ebc: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x981ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981ec0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981ec4: b               #0x981d84
    // 0x981ec8: r9 = webViewController
    //     0x981ec8: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f20] Field <YoutubePlayerController.webViewController>: late final (offset: 0x10)
    //     0x981ecc: ldr             x9, [x9, #0xf20]
    // 0x981ed0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x981ed0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildPlayerHTML(/* No info */) async {
    // ** addr: 0x982248, size: 0xdc
    // 0x982248: EnterFrame
    //     0x982248: stp             fp, lr, [SP, #-0x10]!
    //     0x98224c: mov             fp, SP
    // 0x982250: AllocStack(0x50)
    //     0x982250: sub             SP, SP, #0x50
    // 0x982254: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x982254: stur            NULL, [fp, #-8]
    //     0x982258: stur            x1, [fp, #-0x10]
    //     0x98225c: stur            x2, [fp, #-0x18]
    // 0x982260: CheckStackOverflow
    //     0x982260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x982264: cmp             SP, x16
    //     0x982268: b.ls            #0x98231c
    // 0x98226c: r1 = 1
    //     0x98226c: movz            x1, #0x1
    // 0x982270: r0 = AllocateContext()
    //     0x982270: bl              #0xec126c  ; AllocateContextStub
    // 0x982274: mov             x1, x0
    // 0x982278: ldur            x0, [fp, #-0x18]
    // 0x98227c: stur            x1, [fp, #-0x20]
    // 0x982280: StoreField: r1->field_f = r0
    //     0x982280: stur            w0, [x1, #0xf]
    // 0x982284: InitAsync() -> Future<String>
    //     0x982284: ldr             x0, [PP, #0x2c0]  ; [pp+0x2c0] TypeArguments: <String>
    //     0x982288: bl              #0x661298  ; InitAsyncStub
    // 0x98228c: r0 = InitLateStaticField(0x698) // [package:flutter/src/services/asset_bundle.dart] ::rootBundle
    //     0x98228c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x982290: ldr             x0, [x0, #0xd30]
    //     0x982294: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x982298: cmp             w0, w16
    //     0x98229c: b.ne            #0x9822a8
    //     0x9822a0: ldr             x2, [PP, #0x3260]  ; [pp+0x3260] Field <::.rootBundle>: static late final (offset: 0x698)
    //     0x9822a4: bl              #0xec0378  ; InitLateFinalStaticFieldStub
    // 0x9822a8: mov             x1, x0
    // 0x9822ac: r2 = "packages/youtube_player_iframe/assets/player.html"
    //     0x9822ac: add             x2, PP, #0x57, lsl #12  ; [pp+0x57b80] "packages/youtube_player_iframe/assets/player.html"
    //     0x9822b0: ldr             x2, [x2, #0xb80]
    // 0x9822b4: r0 = loadString()
    //     0x9822b4: bl              #0x72bd88  ; [package:flutter/src/services/asset_bundle.dart] CachingAssetBundle::loadString
    // 0x9822b8: mov             x1, x0
    // 0x9822bc: stur            x1, [fp, #-0x18]
    // 0x9822c0: r0 = Await()
    //     0x9822c0: bl              #0x661044  ; AwaitStub
    // 0x9822c4: stur            x0, [fp, #-0x10]
    // 0x9822c8: r16 = "<<([a-zA-Z]+)>>"
    //     0x9822c8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b88] "<<([a-zA-Z]+)>>"
    //     0x9822cc: ldr             x16, [x16, #0xb88]
    // 0x9822d0: stp             x16, NULL, [SP, #0x20]
    // 0x9822d4: r16 = false
    //     0x9822d4: add             x16, NULL, #0x30  ; false
    // 0x9822d8: r30 = true
    //     0x9822d8: add             lr, NULL, #0x20  ; true
    // 0x9822dc: stp             lr, x16, [SP, #0x10]
    // 0x9822e0: r16 = false
    //     0x9822e0: add             x16, NULL, #0x30  ; false
    // 0x9822e4: r30 = false
    //     0x9822e4: add             lr, NULL, #0x30  ; false
    // 0x9822e8: stp             lr, x16, [SP]
    // 0x9822ec: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x9822ec: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x9822f0: r0 = _RegExp()
    //     0x9822f0: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0x9822f4: ldur            x2, [fp, #-0x20]
    // 0x9822f8: r1 = Function '<anonymous closure>':.
    //     0x9822f8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b90] AnonymousClosure: (0x982324), in [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_buildPlayerHTML (0x982248)
    //     0x9822fc: ldr             x1, [x1, #0xb90]
    // 0x982300: stur            x0, [fp, #-0x18]
    // 0x982304: r0 = AllocateClosure()
    //     0x982304: bl              #0xec1630  ; AllocateClosureStub
    // 0x982308: ldur            x1, [fp, #-0x10]
    // 0x98230c: ldur            x2, [fp, #-0x18]
    // 0x982310: mov             x3, x0
    // 0x982314: r0 = replaceAllMapped()
    //     0x982314: bl              #0x65c46c  ; [dart:core] _StringBase::replaceAllMapped
    // 0x982318: r0 = ReturnAsyncNotFuture()
    //     0x982318: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x98231c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98231c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x982320: b               #0x98226c
  }
  [closure] String <anonymous closure>(dynamic, Match) {
    // ** addr: 0x982324, size: 0xc4
    // 0x982324: EnterFrame
    //     0x982324: stp             fp, lr, [SP, #-0x10]!
    //     0x982328: mov             fp, SP
    // 0x98232c: AllocStack(0x8)
    //     0x98232c: sub             SP, SP, #8
    // 0x982330: SetupParameters()
    //     0x982330: ldr             x0, [fp, #0x18]
    //     0x982334: ldur            w1, [x0, #0x17]
    //     0x982338: add             x1, x1, HEAP, lsl #32
    // 0x98233c: CheckStackOverflow
    //     0x98233c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x982340: cmp             SP, x16
    //     0x982344: b.ls            #0x9823dc
    // 0x982348: LoadField: r3 = r1->field_f
    //     0x982348: ldur            w3, [x1, #0xf]
    // 0x98234c: DecompressPointer r3
    //     0x98234c: add             x3, x3, HEAP, lsl #32
    // 0x982350: ldr             x4, [fp, #0x10]
    // 0x982354: stur            x3, [fp, #-8]
    // 0x982358: r0 = LoadClassIdInstr(r4)
    //     0x982358: ldur            x0, [x4, #-1]
    //     0x98235c: ubfx            x0, x0, #0xc, #0x14
    // 0x982360: mov             x1, x4
    // 0x982364: r2 = 1
    //     0x982364: movz            x2, #0x1
    // 0x982368: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x982368: sub             lr, x0, #0xfdd
    //     0x98236c: ldr             lr, [x21, lr, lsl #3]
    //     0x982370: blr             lr
    // 0x982374: ldur            x1, [fp, #-8]
    // 0x982378: mov             x2, x0
    // 0x98237c: r0 = _getValueOrData()
    //     0x98237c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x982380: mov             x1, x0
    // 0x982384: ldur            x0, [fp, #-8]
    // 0x982388: LoadField: r2 = r0->field_f
    //     0x982388: ldur            w2, [x0, #0xf]
    // 0x98238c: DecompressPointer r2
    //     0x98238c: add             x2, x2, HEAP, lsl #32
    // 0x982390: cmp             w2, w1
    // 0x982394: b.ne            #0x9823a0
    // 0x982398: r0 = Null
    //     0x982398: mov             x0, NULL
    // 0x98239c: b               #0x9823a4
    // 0x9823a0: mov             x0, x1
    // 0x9823a4: cmp             w0, NULL
    // 0x9823a8: b.ne            #0x9823d0
    // 0x9823ac: ldr             x1, [fp, #0x10]
    // 0x9823b0: r0 = LoadClassIdInstr(r1)
    //     0x9823b0: ldur            x0, [x1, #-1]
    //     0x9823b4: ubfx            x0, x0, #0xc, #0x14
    // 0x9823b8: r2 = 0
    //     0x9823b8: movz            x2, #0
    // 0x9823bc: r0 = GDT[cid_x0 + -0xfdd]()
    //     0x9823bc: sub             lr, x0, #0xfdd
    //     0x9823c0: ldr             lr, [x21, lr, lsl #3]
    //     0x9823c4: blr             lr
    // 0x9823c8: cmp             w0, NULL
    // 0x9823cc: b.eq            #0x9823e4
    // 0x9823d0: LeaveFrame
    //     0x9823d0: mov             SP, fp
    //     0x9823d4: ldp             fp, lr, [SP], #0x10
    // 0x9823d8: ret
    //     0x9823d8: ret             
    // 0x9823dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9823dc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9823e0: b               #0x982348
    // 0x9823e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9823e4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ close(/* No info */) async {
    // ** addr: 0xa8373c, size: 0x124
    // 0xa8373c: EnterFrame
    //     0xa8373c: stp             fp, lr, [SP, #-0x10]!
    //     0xa83740: mov             fp, SP
    // 0xa83744: AllocStack(0x28)
    //     0xa83744: sub             SP, SP, #0x28
    // 0xa83748: SetupParameters(YoutubePlayerController this /* r1 => r1, fp-0x10 */)
    //     0xa83748: stur            NULL, [fp, #-8]
    //     0xa8374c: stur            x1, [fp, #-0x10]
    // 0xa83750: CheckStackOverflow
    //     0xa83750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83754: cmp             SP, x16
    //     0xa83758: b.ls            #0xa83840
    // 0xa8375c: InitAsync() -> Future<void?>
    //     0xa8375c: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0xa83760: bl              #0x661298  ; InitAsyncStub
    // 0xa83764: ldur            x1, [fp, #-0x10]
    // 0xa83768: r0 = stopVideo()
    //     0xa83768: bl              #0xa83958  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::stopVideo
    // 0xa8376c: mov             x1, x0
    // 0xa83770: stur            x1, [fp, #-0x18]
    // 0xa83774: r0 = Await()
    //     0xa83774: bl              #0x661044  ; AwaitStub
    // 0xa83778: ldur            x0, [fp, #-0x10]
    // 0xa8377c: LoadField: r3 = r0->field_f
    //     0xa8377c: ldur            w3, [x0, #0xf]
    // 0xa83780: DecompressPointer r3
    //     0xa83780: add             x3, x3, HEAP, lsl #32
    // 0xa83784: r16 = Sentinel
    //     0xa83784: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa83788: cmp             w3, w16
    // 0xa8378c: b.eq            #0xa83848
    // 0xa83790: stur            x3, [fp, #-0x18]
    // 0xa83794: r1 = Null
    //     0xa83794: mov             x1, NULL
    // 0xa83798: r2 = 4
    //     0xa83798: movz            x2, #0x4
    // 0xa8379c: r0 = AllocateArray()
    //     0xa8379c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xa837a0: stur            x0, [fp, #-0x20]
    // 0xa837a4: r16 = "youtube-"
    //     0xa837a4: add             x16, PP, #0x47, lsl #12  ; [pp+0x47f18] "youtube-"
    //     0xa837a8: ldr             x16, [x16, #0xf18]
    // 0xa837ac: StoreField: r0->field_f = r16
    //     0xa837ac: stur            w16, [x0, #0xf]
    // 0xa837b0: ldur            x16, [fp, #-0x10]
    // 0xa837b4: str             x16, [SP]
    // 0xa837b8: r0 = _getHash()
    //     0xa837b8: bl              #0x62ab48  ; [dart:core] ::_getHash
    // 0xa837bc: mov             x1, x0
    // 0xa837c0: ldur            x0, [fp, #-0x20]
    // 0xa837c4: StoreField: r0->field_13 = r1
    //     0xa837c4: stur            w1, [x0, #0x13]
    // 0xa837c8: str             x0, [SP]
    // 0xa837cc: r0 = _interpolate()
    //     0xa837cc: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xa837d0: ldur            x1, [fp, #-0x18]
    // 0xa837d4: mov             x2, x0
    // 0xa837d8: r0 = removeJavaScriptChannel()
    //     0xa837d8: bl              #0xa83860  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::removeJavaScriptChannel
    // 0xa837dc: mov             x1, x0
    // 0xa837e0: stur            x1, [fp, #-0x18]
    // 0xa837e4: r0 = Await()
    //     0xa837e4: bl              #0x661044  ; AwaitStub
    // 0xa837e8: ldur            x0, [fp, #-0x10]
    // 0xa837ec: LoadField: r1 = r0->field_13
    //     0xa837ec: ldur            w1, [x0, #0x13]
    // 0xa837f0: DecompressPointer r1
    //     0xa837f0: add             x1, x1, HEAP, lsl #32
    // 0xa837f4: r16 = Sentinel
    //     0xa837f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa837f8: cmp             w1, w16
    // 0xa837fc: b.eq            #0xa83854
    // 0xa83800: LoadField: r2 = r1->field_b
    //     0xa83800: ldur            w2, [x1, #0xb]
    // 0xa83804: DecompressPointer r2
    //     0xa83804: add             x2, x2, HEAP, lsl #32
    // 0xa83808: mov             x1, x2
    // 0xa8380c: r0 = close()
    //     0xa8380c: bl              #0xc4f240  ; [dart:async] _BroadcastStreamController::close
    // 0xa83810: mov             x1, x0
    // 0xa83814: stur            x1, [fp, #-0x18]
    // 0xa83818: r0 = Await()
    //     0xa83818: bl              #0x661044  ; AwaitStub
    // 0xa8381c: ldur            x0, [fp, #-0x10]
    // 0xa83820: LoadField: r1 = r0->field_1b
    //     0xa83820: ldur            w1, [x0, #0x1b]
    // 0xa83824: DecompressPointer r1
    //     0xa83824: add             x1, x1, HEAP, lsl #32
    // 0xa83828: r0 = close()
    //     0xa83828: bl              #0xc4f240  ; [dart:async] _BroadcastStreamController::close
    // 0xa8382c: mov             x1, x0
    // 0xa83830: stur            x1, [fp, #-0x10]
    // 0xa83834: r0 = Await()
    //     0xa83834: bl              #0x661044  ; AwaitStub
    // 0xa83838: r0 = Null
    //     0xa83838: mov             x0, NULL
    // 0xa8383c: r0 = ReturnAsyncNotFuture()
    //     0xa8383c: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0xa83840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83840: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa83844: b               #0xa8375c
    // 0xa83848: r9 = webViewController
    //     0xa83848: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f20] Field <YoutubePlayerController.webViewController>: late final (offset: 0x10)
    //     0xa8384c: ldr             x9, [x9, #0xf20]
    // 0xa83850: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa83850: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa83854: r9 = _eventHandler
    //     0xa83854: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f28] Field <YoutubePlayerController._eventHandler@2476072021>: late final (offset: 0x14)
    //     0xa83858: ldr             x9, [x9, #0xf28]
    // 0xa8385c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa8385c: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ stopVideo(/* No info */) {
    // ** addr: 0xa83958, size: 0x38
    // 0xa83958: EnterFrame
    //     0xa83958: stp             fp, lr, [SP, #-0x10]!
    //     0xa8395c: mov             fp, SP
    // 0xa83960: CheckStackOverflow
    //     0xa83960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa83964: cmp             SP, x16
    //     0xa83968: b.ls            #0xa83988
    // 0xa8396c: r2 = "stopVideo"
    //     0xa8396c: add             x2, PP, #0x48, lsl #12  ; [pp+0x48f30] "stopVideo"
    //     0xa83970: ldr             x2, [x2, #0xf30]
    // 0xa83974: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa83974: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa83978: r0 = _run()
    //     0xa83978: bl              #0x965a7c  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::_run
    // 0xa8397c: LeaveFrame
    //     0xa8397c: mov             SP, fp
    //     0xa83980: ldp             fp, lr, [SP], #0x10
    // 0xa83984: ret
    //     0xa83984: ret             
    // 0xa83988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa83988: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8398c: b               #0xa8396c
  }
  static _ convertUrlToId(/* No info */) {
    // ** addr: 0xdc06a4, size: 0x224
    // 0xdc06a4: EnterFrame
    //     0xdc06a4: stp             fp, lr, [SP, #-0x10]!
    //     0xdc06a8: mov             fp, SP
    // 0xdc06ac: AllocStack(0x58)
    //     0xdc06ac: sub             SP, SP, #0x58
    // 0xdc06b0: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xdc06b0: mov             x3, x1
    //     0xdc06b4: stur            x1, [fp, #-8]
    // 0xdc06b8: CheckStackOverflow
    //     0xdc06b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc06bc: cmp             SP, x16
    //     0xdc06c0: b.ls            #0xdc08b8
    // 0xdc06c4: r0 = LoadClassIdInstr(r3)
    //     0xdc06c4: ldur            x0, [x3, #-1]
    //     0xdc06c8: ubfx            x0, x0, #0xc, #0x14
    // 0xdc06cc: mov             x1, x3
    // 0xdc06d0: r2 = "http"
    //     0xdc06d0: ldr             x2, [PP, #0xc58]  ; [pp+0xc58] "http"
    // 0xdc06d4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xdc06d4: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xdc06d8: r0 = GDT[cid_x0 + -0xffc]()
    //     0xdc06d8: sub             lr, x0, #0xffc
    //     0xdc06dc: ldr             lr, [x21, lr, lsl #3]
    //     0xdc06e0: blr             lr
    // 0xdc06e4: tbz             w0, #4, #0xdc0704
    // 0xdc06e8: ldur            x0, [fp, #-8]
    // 0xdc06ec: LoadField: r1 = r0->field_7
    //     0xdc06ec: ldur            w1, [x0, #7]
    // 0xdc06f0: cmp             w1, #0x16
    // 0xdc06f4: b.ne            #0xdc0708
    // 0xdc06f8: LeaveFrame
    //     0xdc06f8: mov             SP, fp
    //     0xdc06fc: ldp             fp, lr, [SP], #0x10
    // 0xdc0700: ret
    //     0xdc0700: ret             
    // 0xdc0704: ldur            x0, [fp, #-8]
    // 0xdc0708: mov             x1, x0
    // 0xdc070c: r0 = trim()
    //     0xdc070c: bl              #0x61d36c  ; [dart:core] _StringBase::trim
    // 0xdc0710: r1 = Null
    //     0xdc0710: mov             x1, NULL
    // 0xdc0714: r2 = 10
    //     0xdc0714: movz            x2, #0xa
    // 0xdc0718: stur            x0, [fp, #-8]
    // 0xdc071c: r0 = AllocateArray()
    //     0xdc071c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xdc0720: mov             x3, x0
    // 0xdc0724: stur            x3, [fp, #-0x20]
    // 0xdc0728: r16 = "^https:\\/\\/(\?:www\\.|m\\.)\?youtube\\.com\\/watch\\\?v=([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc0728: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f60] "^https:\\/\\/(\?:www\\.|m\\.)\?youtube\\.com\\/watch\\\?v=([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc072c: ldr             x16, [x16, #0xf60]
    // 0xdc0730: StoreField: r3->field_f = r16
    //     0xdc0730: stur            w16, [x3, #0xf]
    // 0xdc0734: r16 = "^https:\\/\\/(\?:www\\.|m\\.)\?youtube(\?:-nocookie)\?\\.com\\/embed\\/([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc0734: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f68] "^https:\\/\\/(\?:www\\.|m\\.)\?youtube(\?:-nocookie)\?\\.com\\/embed\\/([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc0738: ldr             x16, [x16, #0xf68]
    // 0xdc073c: StoreField: r3->field_13 = r16
    //     0xdc073c: stur            w16, [x3, #0x13]
    // 0xdc0740: r16 = "^https:\\/\\/youtu\\.be\\/([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc0740: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f70] "^https:\\/\\/youtu\\.be\\/([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc0744: ldr             x16, [x16, #0xf70]
    // 0xdc0748: ArrayStore: r3[0] = r16  ; List_4
    //     0xdc0748: stur            w16, [x3, #0x17]
    // 0xdc074c: r16 = "^https:\\/\\/(\?:www\\.|m\\.)\?youtube\\.com\\/shorts\\/([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc074c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f78] "^https:\\/\\/(\?:www\\.|m\\.)\?youtube\\.com\\/shorts\\/([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc0750: ldr             x16, [x16, #0xf78]
    // 0xdc0754: StoreField: r3->field_1b = r16
    //     0xdc0754: stur            w16, [x3, #0x1b]
    // 0xdc0758: r16 = "^https:\\/\\/(\?:music\\.)\?youtube\\.com\\/watch\\\?\?v=([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc0758: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f80] "^https:\\/\\/(\?:music\\.)\?youtube\\.com\\/watch\\\?\?v=([_\\-a-zA-Z0-9]{11}).*$"
    //     0xdc075c: ldr             x16, [x16, #0xf80]
    // 0xdc0760: StoreField: r3->field_1f = r16
    //     0xdc0760: stur            w16, [x3, #0x1f]
    // 0xdc0764: r0 = 0
    //     0xdc0764: movz            x0, #0
    // 0xdc0768: ldur            x4, [fp, #-8]
    // 0xdc076c: CheckStackOverflow
    //     0xdc076c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdc0770: cmp             SP, x16
    //     0xdc0774: b.ls            #0xdc08c0
    // 0xdc0778: cmp             x0, #5
    // 0xdc077c: b.ge            #0xdc08a8
    // 0xdc0780: ArrayLoad: r5 = r3[r0]  ; Unknown_4
    //     0xdc0780: add             x16, x3, x0, lsl #2
    //     0xdc0784: ldur            w5, [x16, #0xf]
    // 0xdc0788: DecompressPointer r5
    //     0xdc0788: add             x5, x5, HEAP, lsl #32
    // 0xdc078c: stur            x5, [fp, #-0x18]
    // 0xdc0790: add             x6, x0, #1
    // 0xdc0794: stur            x6, [fp, #-0x10]
    // 0xdc0798: cmp             w5, NULL
    // 0xdc079c: b.ne            #0xdc07d8
    // 0xdc07a0: mov             x0, x5
    // 0xdc07a4: r2 = Null
    //     0xdc07a4: mov             x2, NULL
    // 0xdc07a8: r1 = Null
    //     0xdc07a8: mov             x1, NULL
    // 0xdc07ac: r4 = 60
    //     0xdc07ac: movz            x4, #0x3c
    // 0xdc07b0: branchIfSmi(r0, 0xdc07bc)
    //     0xdc07b0: tbz             w0, #0, #0xdc07bc
    // 0xdc07b4: r4 = LoadClassIdInstr(r0)
    //     0xdc07b4: ldur            x4, [x0, #-1]
    //     0xdc07b8: ubfx            x4, x4, #0xc, #0x14
    // 0xdc07bc: sub             x4, x4, #0x5e
    // 0xdc07c0: cmp             x4, #1
    // 0xdc07c4: b.ls            #0xdc07d8
    // 0xdc07c8: r8 = String
    //     0xdc07c8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0xdc07cc: r3 = Null
    //     0xdc07cc: add             x3, PP, #0x57, lsl #12  ; [pp+0x57f88] Null
    //     0xdc07d0: ldr             x3, [x3, #0xf88]
    // 0xdc07d4: r0 = String()
    //     0xdc07d4: bl              #0xed43b0  ; IsType_String_Stub
    // 0xdc07d8: ldur            x16, [fp, #-0x18]
    // 0xdc07dc: stp             x16, NULL, [SP, #0x20]
    // 0xdc07e0: r16 = false
    //     0xdc07e0: add             x16, NULL, #0x30  ; false
    // 0xdc07e4: r30 = true
    //     0xdc07e4: add             lr, NULL, #0x20  ; true
    // 0xdc07e8: stp             lr, x16, [SP, #0x10]
    // 0xdc07ec: r16 = false
    //     0xdc07ec: add             x16, NULL, #0x30  ; false
    // 0xdc07f0: r30 = false
    //     0xdc07f0: add             lr, NULL, #0x30  ; false
    // 0xdc07f4: stp             lr, x16, [SP]
    // 0xdc07f8: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xdc07f8: ldr             x4, [PP, #0x610]  ; [pp+0x610] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xdc07fc: r0 = _RegExp()
    //     0xdc07fc: bl              #0x609534  ; [dart:core] _RegExp::_RegExp
    // 0xdc0800: stur            x0, [fp, #-0x18]
    // 0xdc0804: ldur            x16, [fp, #-8]
    // 0xdc0808: stp             x16, x0, [SP, #8]
    // 0xdc080c: str             xzr, [SP]
    // 0xdc0810: r0 = _ExecuteMatch()
    //     0xdc0810: bl              #0x644494  ; [dart:core] _RegExp::_ExecuteMatch
    // 0xdc0814: stur            x0, [fp, #-0x28]
    // 0xdc0818: cmp             w0, NULL
    // 0xdc081c: b.ne            #0xdc082c
    // 0xdc0820: ldur            x0, [fp, #-8]
    // 0xdc0824: r1 = Null
    //     0xdc0824: mov             x1, NULL
    // 0xdc0828: b               #0xdc0854
    // 0xdc082c: ldur            x2, [fp, #-8]
    // 0xdc0830: ldur            x1, [fp, #-0x18]
    // 0xdc0834: r0 = _RegExpMatch()
    //     0xdc0834: bl              #0x6443a8  ; Allocate_RegExpMatchStub -> _RegExpMatch (size=0x14)
    // 0xdc0838: mov             x1, x0
    // 0xdc083c: ldur            x0, [fp, #-0x18]
    // 0xdc0840: StoreField: r1->field_7 = r0
    //     0xdc0840: stur            w0, [x1, #7]
    // 0xdc0844: ldur            x0, [fp, #-8]
    // 0xdc0848: StoreField: r1->field_b = r0
    //     0xdc0848: stur            w0, [x1, #0xb]
    // 0xdc084c: ldur            x2, [fp, #-0x28]
    // 0xdc0850: StoreField: r1->field_f = r2
    //     0xdc0850: stur            w2, [x1, #0xf]
    // 0xdc0854: stur            x1, [fp, #-0x18]
    // 0xdc0858: cmp             w1, NULL
    // 0xdc085c: b.eq            #0xdc089c
    // 0xdc0860: LoadField: r2 = r1->field_7
    //     0xdc0860: ldur            w2, [x1, #7]
    // 0xdc0864: DecompressPointer r2
    //     0xdc0864: add             x2, x2, HEAP, lsl #32
    // 0xdc0868: str             x2, [SP]
    // 0xdc086c: r0 = _groupCount()
    //     0xdc086c: bl              #0xd5fd08  ; [dart:core] _RegExp::_groupCount
    // 0xdc0870: r1 = LoadInt32Instr(r0)
    //     0xdc0870: sbfx            x1, x0, #1, #0x1f
    //     0xdc0874: tbz             w0, #0, #0xdc087c
    //     0xdc0878: ldur            x1, [x0, #7]
    // 0xdc087c: cmp             x1, #1
    // 0xdc0880: b.lt            #0xdc089c
    // 0xdc0884: ldur            x1, [fp, #-0x18]
    // 0xdc0888: r2 = 1
    //     0xdc0888: movz            x2, #0x1
    // 0xdc088c: r0 = group()
    //     0xdc088c: bl              #0xd60da0  ; [dart:core] _RegExpMatch::group
    // 0xdc0890: LeaveFrame
    //     0xdc0890: mov             SP, fp
    //     0xdc0894: ldp             fp, lr, [SP], #0x10
    // 0xdc0898: ret
    //     0xdc0898: ret             
    // 0xdc089c: ldur            x0, [fp, #-0x10]
    // 0xdc08a0: ldur            x3, [fp, #-0x20]
    // 0xdc08a4: b               #0xdc0768
    // 0xdc08a8: r0 = Null
    //     0xdc08a8: mov             x0, NULL
    // 0xdc08ac: LeaveFrame
    //     0xdc08ac: mov             SP, fp
    //     0xdc08b0: ldp             fp, lr, [SP], #0x10
    // 0xdc08b4: ret
    //     0xdc08b4: ret             
    // 0xdc08b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc08b8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc08bc: b               #0xdc06c4
    // 0xdc08c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdc08c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdc08c4: b               #0xdc0778
  }
}
