// lib: , url: package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart

// class id: 1051350, size: 0x8
class :: {
}

// class id: 185, size: 0x18, field offset: 0x8
class YoutubePlayerEventHandler extends Object {

  late final Map<String, (dynamic, Object) => void> _events; // offset: 0x14

  _ YoutubePlayerEventHandler(/* No info */) {
    // ** addr: 0x97b30c, size: 0x3c0
    // 0x97b30c: EnterFrame
    //     0x97b30c: stp             fp, lr, [SP, #-0x10]!
    //     0x97b310: mov             fp, SP
    // 0x97b314: AllocStack(0x28)
    //     0x97b314: sub             SP, SP, #0x28
    // 0x97b318: r0 = Sentinel
    //     0x97b318: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97b31c: mov             x3, x1
    // 0x97b320: stur            x1, [fp, #-8]
    // 0x97b324: stur            x2, [fp, #-0x10]
    // 0x97b328: CheckStackOverflow
    //     0x97b328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b32c: cmp             SP, x16
    //     0x97b330: b.ls            #0x97b6c4
    // 0x97b334: StoreField: r3->field_13 = r0
    //     0x97b334: stur            w0, [x3, #0x13]
    // 0x97b338: r1 = <YoutubeVideoState>
    //     0x97b338: add             x1, PP, #0x49, lsl #12  ; [pp+0x49b40] TypeArguments: <YoutubeVideoState>
    //     0x97b33c: ldr             x1, [x1, #0xb40]
    // 0x97b340: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97b340: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97b344: r0 = StreamController.broadcast()
    //     0x97b344: bl              #0x83522c  ; [dart:async] StreamController::StreamController.broadcast
    // 0x97b348: ldur            x2, [fp, #-8]
    // 0x97b34c: StoreField: r2->field_b = r0
    //     0x97b34c: stur            w0, [x2, #0xb]
    //     0x97b350: ldurb           w16, [x2, #-1]
    //     0x97b354: ldurb           w17, [x0, #-1]
    //     0x97b358: and             x16, x17, x16, lsr #2
    //     0x97b35c: tst             x16, HEAP, lsr #32
    //     0x97b360: b.eq            #0x97b368
    //     0x97b364: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0x97b368: r1 = <void?>
    //     0x97b368: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x97b36c: r0 = _Future()
    //     0x97b36c: bl              #0x66121c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x97b370: stur            x0, [fp, #-0x18]
    // 0x97b374: StoreField: r0->field_b = rZR
    //     0x97b374: stur            xzr, [x0, #0xb]
    // 0x97b378: r0 = InitLateStaticField(0x3d0) // [dart:async] Zone::_current
    //     0x97b378: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x97b37c: ldr             x0, [x0, #0x7a0]
    //     0x97b380: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x97b384: cmp             w0, w16
    //     0x97b388: b.ne            #0x97b394
    //     0x97b38c: ldr             x2, [PP, #0x138]  ; [pp+0x138] Field <Zone._current@5048458>: static late (offset: 0x3d0)
    //     0x97b390: bl              #0xec03e0  ; InitLateStaticFieldStub
    // 0x97b394: mov             x1, x0
    // 0x97b398: ldur            x0, [fp, #-0x18]
    // 0x97b39c: StoreField: r0->field_13 = r1
    //     0x97b39c: stur            w1, [x0, #0x13]
    // 0x97b3a0: r1 = <void?>
    //     0x97b3a0: ldr             x1, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    // 0x97b3a4: r0 = _AsyncCompleter()
    //     0x97b3a4: bl              #0x661210  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x97b3a8: mov             x1, x0
    // 0x97b3ac: ldur            x0, [fp, #-0x18]
    // 0x97b3b0: StoreField: r1->field_b = r0
    //     0x97b3b0: stur            w0, [x1, #0xb]
    // 0x97b3b4: mov             x0, x1
    // 0x97b3b8: ldur            x3, [fp, #-8]
    // 0x97b3bc: StoreField: r3->field_f = r0
    //     0x97b3bc: stur            w0, [x3, #0xf]
    //     0x97b3c0: ldurb           w16, [x3, #-1]
    //     0x97b3c4: ldurb           w17, [x0, #-1]
    //     0x97b3c8: and             x16, x17, x16, lsr #2
    //     0x97b3cc: tst             x16, HEAP, lsr #32
    //     0x97b3d0: b.eq            #0x97b3d8
    //     0x97b3d4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x97b3d8: ldur            x0, [fp, #-0x10]
    // 0x97b3dc: StoreField: r3->field_7 = r0
    //     0x97b3dc: stur            w0, [x3, #7]
    //     0x97b3e0: ldurb           w16, [x3, #-1]
    //     0x97b3e4: ldurb           w17, [x0, #-1]
    //     0x97b3e8: and             x16, x17, x16, lsr #2
    //     0x97b3ec: tst             x16, HEAP, lsr #32
    //     0x97b3f0: b.eq            #0x97b3f8
    //     0x97b3f4: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x97b3f8: r1 = Null
    //     0x97b3f8: mov             x1, NULL
    // 0x97b3fc: r2 = 32
    //     0x97b3fc: movz            x2, #0x20
    // 0x97b400: r0 = AllocateArray()
    //     0x97b400: bl              #0xec22fc  ; AllocateArrayStub
    // 0x97b404: stur            x0, [fp, #-0x10]
    // 0x97b408: r16 = "Ready"
    //     0x97b408: add             x16, PP, #0x49, lsl #12  ; [pp+0x49b48] "Ready"
    //     0x97b40c: ldr             x16, [x16, #0xb48]
    // 0x97b410: StoreField: r0->field_f = r16
    //     0x97b410: stur            w16, [x0, #0xf]
    // 0x97b414: ldur            x2, [fp, #-8]
    // 0x97b418: r1 = Function 'onReady':.
    //     0x97b418: add             x1, PP, #0x49, lsl #12  ; [pp+0x49b50] AnonymousClosure: (0x97cbb0), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onReady (0x97cbec)
    //     0x97b41c: ldr             x1, [x1, #0xb50]
    // 0x97b420: r0 = AllocateClosure()
    //     0x97b420: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b424: ldur            x1, [fp, #-0x10]
    // 0x97b428: ArrayStore: r1[1] = r0  ; List_4
    //     0x97b428: add             x25, x1, #0x13
    //     0x97b42c: str             w0, [x25]
    //     0x97b430: tbz             w0, #0, #0x97b44c
    //     0x97b434: ldurb           w16, [x1, #-1]
    //     0x97b438: ldurb           w17, [x0, #-1]
    //     0x97b43c: and             x16, x17, x16, lsr #2
    //     0x97b440: tst             x16, HEAP, lsr #32
    //     0x97b444: b.eq            #0x97b44c
    //     0x97b448: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b44c: ldur            x0, [fp, #-0x10]
    // 0x97b450: r16 = "StateChange"
    //     0x97b450: add             x16, PP, #0x49, lsl #12  ; [pp+0x49b58] "StateChange"
    //     0x97b454: ldr             x16, [x16, #0xb58]
    // 0x97b458: ArrayStore: r0[0] = r16  ; List_4
    //     0x97b458: stur            w16, [x0, #0x17]
    // 0x97b45c: ldur            x2, [fp, #-8]
    // 0x97b460: r1 = Function 'onStateChange':.
    //     0x97b460: add             x1, PP, #0x49, lsl #12  ; [pp+0x49b60] AnonymousClosure: (0x97c1f4), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onStateChange (0x97c230)
    //     0x97b464: ldr             x1, [x1, #0xb60]
    // 0x97b468: r0 = AllocateClosure()
    //     0x97b468: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b46c: ldur            x1, [fp, #-0x10]
    // 0x97b470: ArrayStore: r1[3] = r0  ; List_4
    //     0x97b470: add             x25, x1, #0x1b
    //     0x97b474: str             w0, [x25]
    //     0x97b478: tbz             w0, #0, #0x97b494
    //     0x97b47c: ldurb           w16, [x1, #-1]
    //     0x97b480: ldurb           w17, [x0, #-1]
    //     0x97b484: and             x16, x17, x16, lsr #2
    //     0x97b488: tst             x16, HEAP, lsr #32
    //     0x97b48c: b.eq            #0x97b494
    //     0x97b490: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b494: ldur            x0, [fp, #-0x10]
    // 0x97b498: r16 = "PlaybackQualityChange"
    //     0x97b498: add             x16, PP, #0x49, lsl #12  ; [pp+0x49b68] "PlaybackQualityChange"
    //     0x97b49c: ldr             x16, [x16, #0xb68]
    // 0x97b4a0: StoreField: r0->field_1f = r16
    //     0x97b4a0: stur            w16, [x0, #0x1f]
    // 0x97b4a4: ldur            x2, [fp, #-8]
    // 0x97b4a8: r1 = Function 'onPlaybackQualityChange':.
    //     0x97b4a8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49b70] AnonymousClosure: (0x97c124), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onPlaybackQualityChange (0x97c160)
    //     0x97b4ac: ldr             x1, [x1, #0xb70]
    // 0x97b4b0: r0 = AllocateClosure()
    //     0x97b4b0: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b4b4: ldur            x1, [fp, #-0x10]
    // 0x97b4b8: ArrayStore: r1[5] = r0  ; List_4
    //     0x97b4b8: add             x25, x1, #0x23
    //     0x97b4bc: str             w0, [x25]
    //     0x97b4c0: tbz             w0, #0, #0x97b4dc
    //     0x97b4c4: ldurb           w16, [x1, #-1]
    //     0x97b4c8: ldurb           w17, [x0, #-1]
    //     0x97b4cc: and             x16, x17, x16, lsr #2
    //     0x97b4d0: tst             x16, HEAP, lsr #32
    //     0x97b4d4: b.eq            #0x97b4dc
    //     0x97b4d8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b4dc: ldur            x0, [fp, #-0x10]
    // 0x97b4e0: r16 = "PlaybackRateChange"
    //     0x97b4e0: add             x16, PP, #0x49, lsl #12  ; [pp+0x49b78] "PlaybackRateChange"
    //     0x97b4e4: ldr             x16, [x16, #0xb78]
    // 0x97b4e8: StoreField: r0->field_27 = r16
    //     0x97b4e8: stur            w16, [x0, #0x27]
    // 0x97b4ec: ldur            x2, [fp, #-8]
    // 0x97b4f0: r1 = Function 'onPlaybackRateChange':.
    //     0x97b4f0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49b80] AnonymousClosure: (0x97c034), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onPlaybackRateChange (0x97c070)
    //     0x97b4f4: ldr             x1, [x1, #0xb80]
    // 0x97b4f8: r0 = AllocateClosure()
    //     0x97b4f8: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b4fc: ldur            x1, [fp, #-0x10]
    // 0x97b500: ArrayStore: r1[7] = r0  ; List_4
    //     0x97b500: add             x25, x1, #0x2b
    //     0x97b504: str             w0, [x25]
    //     0x97b508: tbz             w0, #0, #0x97b524
    //     0x97b50c: ldurb           w16, [x1, #-1]
    //     0x97b510: ldurb           w17, [x0, #-1]
    //     0x97b514: and             x16, x17, x16, lsr #2
    //     0x97b518: tst             x16, HEAP, lsr #32
    //     0x97b51c: b.eq            #0x97b524
    //     0x97b520: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b524: ldur            x0, [fp, #-0x10]
    // 0x97b528: r16 = "PlayerError"
    //     0x97b528: add             x16, PP, #0x49, lsl #12  ; [pp+0x49b88] "PlayerError"
    //     0x97b52c: ldr             x16, [x16, #0xb88]
    // 0x97b530: StoreField: r0->field_2f = r16
    //     0x97b530: stur            w16, [x0, #0x2f]
    // 0x97b534: ldur            x2, [fp, #-8]
    // 0x97b538: r1 = Function 'onError':.
    //     0x97b538: add             x1, PP, #0x49, lsl #12  ; [pp+0x49b90] AnonymousClosure: (0x97bed8), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onError (0x97bf14)
    //     0x97b53c: ldr             x1, [x1, #0xb90]
    // 0x97b540: r0 = AllocateClosure()
    //     0x97b540: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b544: ldur            x1, [fp, #-0x10]
    // 0x97b548: ArrayStore: r1[9] = r0  ; List_4
    //     0x97b548: add             x25, x1, #0x33
    //     0x97b54c: str             w0, [x25]
    //     0x97b550: tbz             w0, #0, #0x97b56c
    //     0x97b554: ldurb           w16, [x1, #-1]
    //     0x97b558: ldurb           w17, [x0, #-1]
    //     0x97b55c: and             x16, x17, x16, lsr #2
    //     0x97b560: tst             x16, HEAP, lsr #32
    //     0x97b564: b.eq            #0x97b56c
    //     0x97b568: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b56c: ldur            x0, [fp, #-0x10]
    // 0x97b570: r16 = "FullscreenButtonPressed"
    //     0x97b570: add             x16, PP, #0x49, lsl #12  ; [pp+0x49b98] "FullscreenButtonPressed"
    //     0x97b574: ldr             x16, [x16, #0xb98]
    // 0x97b578: StoreField: r0->field_37 = r16
    //     0x97b578: stur            w16, [x0, #0x37]
    // 0x97b57c: ldur            x2, [fp, #-8]
    // 0x97b580: r1 = Function 'onFullscreenButtonPressed':.
    //     0x97b580: add             x1, PP, #0x49, lsl #12  ; [pp+0x49ba0] AnonymousClosure: (0x97b9b4), of [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler
    //     0x97b584: ldr             x1, [x1, #0xba0]
    // 0x97b588: r0 = AllocateClosure()
    //     0x97b588: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b58c: ldur            x1, [fp, #-0x10]
    // 0x97b590: ArrayStore: r1[11] = r0  ; List_4
    //     0x97b590: add             x25, x1, #0x3b
    //     0x97b594: str             w0, [x25]
    //     0x97b598: tbz             w0, #0, #0x97b5b4
    //     0x97b59c: ldurb           w16, [x1, #-1]
    //     0x97b5a0: ldurb           w17, [x0, #-1]
    //     0x97b5a4: and             x16, x17, x16, lsr #2
    //     0x97b5a8: tst             x16, HEAP, lsr #32
    //     0x97b5ac: b.eq            #0x97b5b4
    //     0x97b5b0: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b5b4: ldur            x0, [fp, #-0x10]
    // 0x97b5b8: r16 = "VideoState"
    //     0x97b5b8: add             x16, PP, #0x49, lsl #12  ; [pp+0x49ba8] "VideoState"
    //     0x97b5bc: ldr             x16, [x16, #0xba8]
    // 0x97b5c0: StoreField: r0->field_3f = r16
    //     0x97b5c0: stur            w16, [x0, #0x3f]
    // 0x97b5c4: ldur            x2, [fp, #-8]
    // 0x97b5c8: r1 = Function 'onVideoState':.
    //     0x97b5c8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49bb0] AnonymousClosure: (0x97b708), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onVideoState (0x97b744)
    //     0x97b5cc: ldr             x1, [x1, #0xbb0]
    // 0x97b5d0: r0 = AllocateClosure()
    //     0x97b5d0: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b5d4: ldur            x1, [fp, #-0x10]
    // 0x97b5d8: ArrayStore: r1[13] = r0  ; List_4
    //     0x97b5d8: add             x25, x1, #0x43
    //     0x97b5dc: str             w0, [x25]
    //     0x97b5e0: tbz             w0, #0, #0x97b5fc
    //     0x97b5e4: ldurb           w16, [x1, #-1]
    //     0x97b5e8: ldurb           w17, [x0, #-1]
    //     0x97b5ec: and             x16, x17, x16, lsr #2
    //     0x97b5f0: tst             x16, HEAP, lsr #32
    //     0x97b5f4: b.eq            #0x97b5fc
    //     0x97b5f8: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b5fc: ldur            x0, [fp, #-0x10]
    // 0x97b600: r16 = "AutoplayBlocked"
    //     0x97b600: add             x16, PP, #0x49, lsl #12  ; [pp+0x49bb8] "AutoplayBlocked"
    //     0x97b604: ldr             x16, [x16, #0xbb8]
    // 0x97b608: StoreField: r0->field_47 = r16
    //     0x97b608: stur            w16, [x0, #0x47]
    // 0x97b60c: ldur            x2, [fp, #-8]
    // 0x97b610: r1 = Function 'onAutoplayBlocked':.
    //     0x97b610: add             x1, PP, #0x49, lsl #12  ; [pp+0x49bc0] AnonymousClosure: (0x97b6cc), of [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler
    //     0x97b614: ldr             x1, [x1, #0xbc0]
    // 0x97b618: r0 = AllocateClosure()
    //     0x97b618: bl              #0xec1630  ; AllocateClosureStub
    // 0x97b61c: ldur            x1, [fp, #-0x10]
    // 0x97b620: ArrayStore: r1[15] = r0  ; List_4
    //     0x97b620: add             x25, x1, #0x4b
    //     0x97b624: str             w0, [x25]
    //     0x97b628: tbz             w0, #0, #0x97b644
    //     0x97b62c: ldurb           w16, [x1, #-1]
    //     0x97b630: ldurb           w17, [x0, #-1]
    //     0x97b634: and             x16, x17, x16, lsr #2
    //     0x97b638: tst             x16, HEAP, lsr #32
    //     0x97b63c: b.eq            #0x97b644
    //     0x97b640: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x97b644: r16 = <String, (dynamic this, Object) => void?>
    //     0x97b644: add             x16, PP, #0x49, lsl #12  ; [pp+0x49bc8] TypeArguments: <String, (dynamic this, Object) => void?>
    //     0x97b648: ldr             x16, [x16, #0xbc8]
    // 0x97b64c: ldur            lr, [fp, #-0x10]
    // 0x97b650: stp             lr, x16, [SP]
    // 0x97b654: r0 = Map._fromLiteral()
    //     0x97b654: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x97b658: mov             x1, x0
    // 0x97b65c: ldur            x0, [fp, #-8]
    // 0x97b660: stur            x1, [fp, #-0x10]
    // 0x97b664: LoadField: r2 = r0->field_13
    //     0x97b664: ldur            w2, [x0, #0x13]
    // 0x97b668: DecompressPointer r2
    //     0x97b668: add             x2, x2, HEAP, lsl #32
    // 0x97b66c: r16 = Sentinel
    //     0x97b66c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97b670: cmp             w2, w16
    // 0x97b674: b.ne            #0x97b680
    // 0x97b678: mov             x1, x0
    // 0x97b67c: b               #0x97b694
    // 0x97b680: r16 = "_events@2782244153"
    //     0x97b680: add             x16, PP, #0x49, lsl #12  ; [pp+0x49bd0] "_events@2782244153"
    //     0x97b684: ldr             x16, [x16, #0xbd0]
    // 0x97b688: str             x16, [SP]
    // 0x97b68c: r0 = _throwFieldAlreadyInitialized()
    //     0x97b68c: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x97b690: ldur            x1, [fp, #-8]
    // 0x97b694: ldur            x0, [fp, #-0x10]
    // 0x97b698: StoreField: r1->field_13 = r0
    //     0x97b698: stur            w0, [x1, #0x13]
    //     0x97b69c: ldurb           w16, [x1, #-1]
    //     0x97b6a0: ldurb           w17, [x0, #-1]
    //     0x97b6a4: and             x16, x17, x16, lsr #2
    //     0x97b6a8: tst             x16, HEAP, lsr #32
    //     0x97b6ac: b.eq            #0x97b6b4
    //     0x97b6b0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x97b6b4: r0 = Null
    //     0x97b6b4: mov             x0, NULL
    // 0x97b6b8: LeaveFrame
    //     0x97b6b8: mov             SP, fp
    //     0x97b6bc: ldp             fp, lr, [SP], #0x10
    // 0x97b6c0: ret
    //     0x97b6c0: ret             
    // 0x97b6c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b6c4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b6c8: b               #0x97b334
  }
  [closure] void onAutoplayBlocked(dynamic, Object) {
    // ** addr: 0x97b6cc, size: 0x3c
    // 0x97b6cc: EnterFrame
    //     0x97b6cc: stp             fp, lr, [SP, #-0x10]!
    //     0x97b6d0: mov             fp, SP
    // 0x97b6d4: CheckStackOverflow
    //     0x97b6d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b6d8: cmp             SP, x16
    //     0x97b6dc: b.ls            #0x97b700
    // 0x97b6e0: r1 = "Autoplay was blocked by browser. Most modern browser does not allow video with sound to autoplay. Try muting the video to autoplay."
    //     0x97b6e0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49bd8] "Autoplay was blocked by browser. Most modern browser does not allow video with sound to autoplay. Try muting the video to autoplay."
    //     0x97b6e4: ldr             x1, [x1, #0xbd8]
    // 0x97b6e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97b6e8: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97b6ec: r0 = log()
    //     0x97b6ec: bl              #0x615d0c  ; [dart:developer] ::log
    // 0x97b6f0: r0 = Null
    //     0x97b6f0: mov             x0, NULL
    // 0x97b6f4: LeaveFrame
    //     0x97b6f4: mov             SP, fp
    //     0x97b6f8: ldp             fp, lr, [SP], #0x10
    // 0x97b6fc: ret
    //     0x97b6fc: ret             
    // 0x97b700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b700: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b704: b               #0x97b6e0
  }
  [closure] void onVideoState(dynamic, Object) {
    // ** addr: 0x97b708, size: 0x3c
    // 0x97b708: EnterFrame
    //     0x97b708: stp             fp, lr, [SP, #-0x10]!
    //     0x97b70c: mov             fp, SP
    // 0x97b710: ldr             x0, [fp, #0x18]
    // 0x97b714: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97b714: ldur            w1, [x0, #0x17]
    // 0x97b718: DecompressPointer r1
    //     0x97b718: add             x1, x1, HEAP, lsl #32
    // 0x97b71c: CheckStackOverflow
    //     0x97b71c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b720: cmp             SP, x16
    //     0x97b724: b.ls            #0x97b73c
    // 0x97b728: ldr             x2, [fp, #0x10]
    // 0x97b72c: r0 = onVideoState()
    //     0x97b72c: bl              #0x97b744  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onVideoState
    // 0x97b730: LeaveFrame
    //     0x97b730: mov             SP, fp
    //     0x97b734: ldp             fp, lr, [SP], #0x10
    // 0x97b738: ret
    //     0x97b738: ret             
    // 0x97b73c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b73c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b740: b               #0x97b728
  }
  _ onVideoState(/* No info */) {
    // ** addr: 0x97b744, size: 0x94
    // 0x97b744: EnterFrame
    //     0x97b744: stp             fp, lr, [SP, #-0x10]!
    //     0x97b748: mov             fp, SP
    // 0x97b74c: AllocStack(0x10)
    //     0x97b74c: sub             SP, SP, #0x10
    // 0x97b750: CheckStackOverflow
    //     0x97b750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b754: cmp             SP, x16
    //     0x97b758: b.ls            #0x97b7d0
    // 0x97b75c: LoadField: r3 = r1->field_b
    //     0x97b75c: ldur            w3, [x1, #0xb]
    // 0x97b760: DecompressPointer r3
    //     0x97b760: add             x3, x3, HEAP, lsl #32
    // 0x97b764: stur            x3, [fp, #-8]
    // 0x97b768: LoadField: r0 = r3->field_13
    //     0x97b768: ldur            x0, [x3, #0x13]
    // 0x97b76c: tbz             w0, #2, #0x97b780
    // 0x97b770: r0 = Null
    //     0x97b770: mov             x0, NULL
    // 0x97b774: LeaveFrame
    //     0x97b774: mov             SP, fp
    //     0x97b778: ldp             fp, lr, [SP], #0x10
    // 0x97b77c: ret
    //     0x97b77c: ret             
    // 0x97b780: r0 = 60
    //     0x97b780: movz            x0, #0x3c
    // 0x97b784: branchIfSmi(r2, 0x97b790)
    //     0x97b784: tbz             w2, #0, #0x97b790
    // 0x97b788: r0 = LoadClassIdInstr(r2)
    //     0x97b788: ldur            x0, [x2, #-1]
    //     0x97b78c: ubfx            x0, x0, #0xc, #0x14
    // 0x97b790: str             x2, [SP]
    // 0x97b794: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x97b794: ldr             x4, [PP, #0x488]  ; [pp+0x488] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x97b798: r0 = GDT[cid_x0 + 0x2b03]()
    //     0x97b798: movz            x17, #0x2b03
    //     0x97b79c: add             lr, x0, x17
    //     0x97b7a0: ldr             lr, [x21, lr, lsl #3]
    //     0x97b7a4: blr             lr
    // 0x97b7a8: mov             x2, x0
    // 0x97b7ac: r1 = Null
    //     0x97b7ac: mov             x1, NULL
    // 0x97b7b0: r0 = YoutubeVideoState.fromJson()
    //     0x97b7b0: bl              #0x97b7d8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubeVideoState::YoutubeVideoState.fromJson
    // 0x97b7b4: ldur            x1, [fp, #-8]
    // 0x97b7b8: mov             x2, x0
    // 0x97b7bc: r0 = add()
    //     0x97b7bc: bl              #0xc7541c  ; [dart:async] _BroadcastStreamController::add
    // 0x97b7c0: r0 = Null
    //     0x97b7c0: mov             x0, NULL
    // 0x97b7c4: LeaveFrame
    //     0x97b7c4: mov             SP, fp
    //     0x97b7c8: ldp             fp, lr, [SP], #0x10
    // 0x97b7cc: ret
    //     0x97b7cc: ret             
    // 0x97b7d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b7d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b7d4: b               #0x97b75c
  }
  [closure] void onFullscreenButtonPressed(dynamic, Object) {
    // ** addr: 0x97b9b4, size: 0x48
    // 0x97b9b4: EnterFrame
    //     0x97b9b4: stp             fp, lr, [SP, #-0x10]!
    //     0x97b9b8: mov             fp, SP
    // 0x97b9bc: ldr             x0, [fp, #0x18]
    // 0x97b9c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97b9c0: ldur            w1, [x0, #0x17]
    // 0x97b9c4: DecompressPointer r1
    //     0x97b9c4: add             x1, x1, HEAP, lsl #32
    // 0x97b9c8: CheckStackOverflow
    //     0x97b9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97b9cc: cmp             SP, x16
    //     0x97b9d0: b.ls            #0x97b9f4
    // 0x97b9d4: LoadField: r0 = r1->field_7
    //     0x97b9d4: ldur            w0, [x1, #7]
    // 0x97b9d8: DecompressPointer r0
    //     0x97b9d8: add             x0, x0, HEAP, lsl #32
    // 0x97b9dc: mov             x1, x0
    // 0x97b9e0: r0 = toggleFullScreen()
    //     0x97b9e0: bl              #0x97b9fc  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::toggleFullScreen
    // 0x97b9e4: r0 = Null
    //     0x97b9e4: mov             x0, NULL
    // 0x97b9e8: LeaveFrame
    //     0x97b9e8: mov             SP, fp
    //     0x97b9ec: ldp             fp, lr, [SP], #0x10
    // 0x97b9f0: ret
    //     0x97b9f0: ret             
    // 0x97b9f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97b9f4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97b9f8: b               #0x97b9d4
  }
  [closure] void onError(dynamic, Object) {
    // ** addr: 0x97bed8, size: 0x3c
    // 0x97bed8: EnterFrame
    //     0x97bed8: stp             fp, lr, [SP, #-0x10]!
    //     0x97bedc: mov             fp, SP
    // 0x97bee0: ldr             x0, [fp, #0x18]
    // 0x97bee4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97bee4: ldur            w1, [x0, #0x17]
    // 0x97bee8: DecompressPointer r1
    //     0x97bee8: add             x1, x1, HEAP, lsl #32
    // 0x97beec: CheckStackOverflow
    //     0x97beec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97bef0: cmp             SP, x16
    //     0x97bef4: b.ls            #0x97bf0c
    // 0x97bef8: ldr             x2, [fp, #0x10]
    // 0x97befc: r0 = onError()
    //     0x97befc: bl              #0x97bf14  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onError
    // 0x97bf00: LeaveFrame
    //     0x97bf00: mov             SP, fp
    //     0x97bf04: ldp             fp, lr, [SP], #0x10
    // 0x97bf08: ret
    //     0x97bf08: ret             
    // 0x97bf0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97bf0c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97bf10: b               #0x97bef8
  }
  _ onError(/* No info */) {
    // ** addr: 0x97bf14, size: 0xb0
    // 0x97bf14: EnterFrame
    //     0x97bf14: stp             fp, lr, [SP, #-0x10]!
    //     0x97bf18: mov             fp, SP
    // 0x97bf1c: AllocStack(0x18)
    //     0x97bf1c: sub             SP, SP, #0x18
    // 0x97bf20: SetupParameters(YoutubePlayerEventHandler this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x97bf20: stur            x1, [fp, #-8]
    //     0x97bf24: stur            x2, [fp, #-0x10]
    // 0x97bf28: CheckStackOverflow
    //     0x97bf28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97bf2c: cmp             SP, x16
    //     0x97bf30: b.ls            #0x97bfbc
    // 0x97bf34: r1 = 1
    //     0x97bf34: movz            x1, #0x1
    // 0x97bf38: r0 = AllocateContext()
    //     0x97bf38: bl              #0xec126c  ; AllocateContextStub
    // 0x97bf3c: mov             x1, x0
    // 0x97bf40: ldur            x0, [fp, #-0x10]
    // 0x97bf44: StoreField: r1->field_f = r0
    //     0x97bf44: stur            w0, [x1, #0xf]
    // 0x97bf48: mov             x2, x1
    // 0x97bf4c: r1 = Function '<anonymous closure>':.
    //     0x97bf4c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49c60] AnonymousClosure: (0x97bfd0), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onError (0x97bf14)
    //     0x97bf50: ldr             x1, [x1, #0xc60]
    // 0x97bf54: r0 = AllocateClosure()
    //     0x97bf54: bl              #0xec1630  ; AllocateClosureStub
    // 0x97bf58: r1 = Function '<anonymous closure>':.
    //     0x97bf58: add             x1, PP, #0x49, lsl #12  ; [pp+0x49c68] AnonymousClosure: (0x97bfc4), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onError (0x97bf14)
    //     0x97bf5c: ldr             x1, [x1, #0xc68]
    // 0x97bf60: r2 = Null
    //     0x97bf60: mov             x2, NULL
    // 0x97bf64: stur            x0, [fp, #-0x10]
    // 0x97bf68: r0 = AllocateClosure()
    //     0x97bf68: bl              #0xec1630  ; AllocateClosureStub
    // 0x97bf6c: str             x0, [SP]
    // 0x97bf70: ldur            x2, [fp, #-0x10]
    // 0x97bf74: r1 = const [Instance of 'YoutubeError', Instance of 'YoutubeError', Instance of 'YoutubeError', Instance of 'YoutubeError', Instance of 'YoutubeError', Instance of 'YoutubeError', Instance of 'YoutubeError', Instance of 'YoutubeError']
    //     0x97bf74: add             x1, PP, #0x49, lsl #12  ; [pp+0x49c70] List<YoutubeError>(8)
    //     0x97bf78: ldr             x1, [x1, #0xc70]
    // 0x97bf7c: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x97bf7c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25ea0] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x97bf80: ldr             x4, [x4, #0xea0]
    // 0x97bf84: r0 = firstWhere()
    //     0x97bf84: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0x97bf88: mov             x1, x0
    // 0x97bf8c: ldur            x0, [fp, #-8]
    // 0x97bf90: LoadField: r2 = r0->field_7
    //     0x97bf90: ldur            w2, [x0, #7]
    // 0x97bf94: DecompressPointer r2
    //     0x97bf94: add             x2, x2, HEAP, lsl #32
    // 0x97bf98: str             x1, [SP]
    // 0x97bf9c: mov             x1, x2
    // 0x97bfa0: r4 = const [0, 0x2, 0x1, 0x1, error, 0x1, null]
    //     0x97bfa0: add             x4, PP, #0x23, lsl #12  ; [pp+0x23cd8] List(7) [0, 0x2, 0x1, 0x1, "error", 0x1, Null]
    //     0x97bfa4: ldr             x4, [x4, #0xcd8]
    // 0x97bfa8: r0 = update()
    //     0x97bfa8: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97bfac: r0 = Null
    //     0x97bfac: mov             x0, NULL
    // 0x97bfb0: LeaveFrame
    //     0x97bfb0: mov             SP, fp
    //     0x97bfb4: ldp             fp, lr, [SP], #0x10
    // 0x97bfb8: ret
    //     0x97bfb8: ret             
    // 0x97bfbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97bfbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97bfc0: b               #0x97bf34
  }
  [closure] YoutubeError <anonymous closure>(dynamic) {
    // ** addr: 0x97bfc4, size: 0xc
    // 0x97bfc4: r0 = Instance_YoutubeError
    //     0x97bfc4: add             x0, PP, #0x49, lsl #12  ; [pp+0x49c78] Obj!YoutubeError@e2d191
    //     0x97bfc8: ldr             x0, [x0, #0xc78]
    // 0x97bfcc: ret
    //     0x97bfcc: ret             
  }
  [closure] bool <anonymous closure>(dynamic, YoutubeError) {
    // ** addr: 0x97bfd0, size: 0x64
    // 0x97bfd0: EnterFrame
    //     0x97bfd0: stp             fp, lr, [SP, #-0x10]!
    //     0x97bfd4: mov             fp, SP
    // 0x97bfd8: AllocStack(0x10)
    //     0x97bfd8: sub             SP, SP, #0x10
    // 0x97bfdc: SetupParameters()
    //     0x97bfdc: ldr             x0, [fp, #0x18]
    //     0x97bfe0: ldur            w1, [x0, #0x17]
    //     0x97bfe4: add             x1, x1, HEAP, lsl #32
    // 0x97bfe8: CheckStackOverflow
    //     0x97bfe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97bfec: cmp             SP, x16
    //     0x97bff0: b.ls            #0x97c02c
    // 0x97bff4: ldr             x0, [fp, #0x10]
    // 0x97bff8: LoadField: r2 = r0->field_13
    //     0x97bff8: ldur            x2, [x0, #0x13]
    // 0x97bffc: LoadField: r3 = r1->field_f
    //     0x97bffc: ldur            w3, [x1, #0xf]
    // 0x97c000: DecompressPointer r3
    //     0x97c000: add             x3, x3, HEAP, lsl #32
    // 0x97c004: r0 = BoxInt64Instr(r2)
    //     0x97c004: sbfiz           x0, x2, #1, #0x1f
    //     0x97c008: cmp             x2, x0, asr #1
    //     0x97c00c: b.eq            #0x97c018
    //     0x97c010: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0x97c014: stur            x2, [x0, #7]
    // 0x97c018: stp             x3, x0, [SP]
    // 0x97c01c: r0 = ==()
    //     0x97c01c: bl              #0xd81764  ; [dart:core] _IntegerImplementation::==
    // 0x97c020: LeaveFrame
    //     0x97c020: mov             SP, fp
    //     0x97c024: ldp             fp, lr, [SP], #0x10
    // 0x97c028: ret
    //     0x97c028: ret             
    // 0x97c02c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c02c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c030: b               #0x97bff4
  }
  [closure] void onPlaybackRateChange(dynamic, Object) {
    // ** addr: 0x97c034, size: 0x3c
    // 0x97c034: EnterFrame
    //     0x97c034: stp             fp, lr, [SP, #-0x10]!
    //     0x97c038: mov             fp, SP
    // 0x97c03c: ldr             x0, [fp, #0x18]
    // 0x97c040: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97c040: ldur            w1, [x0, #0x17]
    // 0x97c044: DecompressPointer r1
    //     0x97c044: add             x1, x1, HEAP, lsl #32
    // 0x97c048: CheckStackOverflow
    //     0x97c048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c04c: cmp             SP, x16
    //     0x97c050: b.ls            #0x97c068
    // 0x97c054: ldr             x2, [fp, #0x10]
    // 0x97c058: r0 = onPlaybackRateChange()
    //     0x97c058: bl              #0x97c070  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onPlaybackRateChange
    // 0x97c05c: LeaveFrame
    //     0x97c05c: mov             SP, fp
    //     0x97c060: ldp             fp, lr, [SP], #0x10
    // 0x97c064: ret
    //     0x97c064: ret             
    // 0x97c068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c068: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c06c: b               #0x97c054
  }
  _ onPlaybackRateChange(/* No info */) {
    // ** addr: 0x97c070, size: 0xb4
    // 0x97c070: EnterFrame
    //     0x97c070: stp             fp, lr, [SP, #-0x10]!
    //     0x97c074: mov             fp, SP
    // 0x97c078: AllocStack(0x18)
    //     0x97c078: sub             SP, SP, #0x18
    // 0x97c07c: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x97c07c: mov             x3, x2
    //     0x97c080: stur            x2, [fp, #-0x10]
    // 0x97c084: CheckStackOverflow
    //     0x97c084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c088: cmp             SP, x16
    //     0x97c08c: b.ls            #0x97c11c
    // 0x97c090: LoadField: r4 = r1->field_7
    //     0x97c090: ldur            w4, [x1, #7]
    // 0x97c094: DecompressPointer r4
    //     0x97c094: add             x4, x4, HEAP, lsl #32
    // 0x97c098: mov             x0, x3
    // 0x97c09c: stur            x4, [fp, #-8]
    // 0x97c0a0: r2 = Null
    //     0x97c0a0: mov             x2, NULL
    // 0x97c0a4: r1 = Null
    //     0x97c0a4: mov             x1, NULL
    // 0x97c0a8: branchIfSmi(r0, 0x97c0d0)
    //     0x97c0a8: tbz             w0, #0, #0x97c0d0
    // 0x97c0ac: r4 = LoadClassIdInstr(r0)
    //     0x97c0ac: ldur            x4, [x0, #-1]
    //     0x97c0b0: ubfx            x4, x4, #0xc, #0x14
    // 0x97c0b4: sub             x4, x4, #0x3c
    // 0x97c0b8: cmp             x4, #2
    // 0x97c0bc: b.ls            #0x97c0d0
    // 0x97c0c0: r8 = num
    //     0x97c0c0: ldr             x8, [PP, #0x1658]  ; [pp+0x1658] Type: num
    // 0x97c0c4: r3 = Null
    //     0x97c0c4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49c80] Null
    //     0x97c0c8: ldr             x3, [x3, #0xc80]
    // 0x97c0cc: r0 = num()
    //     0x97c0cc: bl              #0xed4df4  ; IsType_num_Stub
    // 0x97c0d0: ldur            x0, [fp, #-0x10]
    // 0x97c0d4: r1 = 60
    //     0x97c0d4: movz            x1, #0x3c
    // 0x97c0d8: branchIfSmi(r0, 0x97c0e4)
    //     0x97c0d8: tbz             w0, #0, #0x97c0e4
    // 0x97c0dc: r1 = LoadClassIdInstr(r0)
    //     0x97c0dc: ldur            x1, [x0, #-1]
    //     0x97c0e0: ubfx            x1, x1, #0xc, #0x14
    // 0x97c0e4: str             x0, [SP]
    // 0x97c0e8: mov             x0, x1
    // 0x97c0ec: r0 = GDT[cid_x0 + -0xffa]()
    //     0x97c0ec: sub             lr, x0, #0xffa
    //     0x97c0f0: ldr             lr, [x21, lr, lsl #3]
    //     0x97c0f4: blr             lr
    // 0x97c0f8: str             x0, [SP]
    // 0x97c0fc: ldur            x1, [fp, #-8]
    // 0x97c100: r4 = const [0, 0x2, 0x1, 0x1, playbackRate, 0x1, null]
    //     0x97c100: add             x4, PP, #0x49, lsl #12  ; [pp+0x49c90] List(7) [0, 0x2, 0x1, 0x1, "playbackRate", 0x1, Null]
    //     0x97c104: ldr             x4, [x4, #0xc90]
    // 0x97c108: r0 = update()
    //     0x97c108: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97c10c: r0 = Null
    //     0x97c10c: mov             x0, NULL
    // 0x97c110: LeaveFrame
    //     0x97c110: mov             SP, fp
    //     0x97c114: ldp             fp, lr, [SP], #0x10
    // 0x97c118: ret
    //     0x97c118: ret             
    // 0x97c11c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c11c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c120: b               #0x97c090
  }
  [closure] void onPlaybackQualityChange(dynamic, Object) {
    // ** addr: 0x97c124, size: 0x3c
    // 0x97c124: EnterFrame
    //     0x97c124: stp             fp, lr, [SP, #-0x10]!
    //     0x97c128: mov             fp, SP
    // 0x97c12c: ldr             x0, [fp, #0x18]
    // 0x97c130: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97c130: ldur            w1, [x0, #0x17]
    // 0x97c134: DecompressPointer r1
    //     0x97c134: add             x1, x1, HEAP, lsl #32
    // 0x97c138: CheckStackOverflow
    //     0x97c138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c13c: cmp             SP, x16
    //     0x97c140: b.ls            #0x97c158
    // 0x97c144: ldr             x2, [fp, #0x10]
    // 0x97c148: r0 = onPlaybackQualityChange()
    //     0x97c148: bl              #0x97c160  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onPlaybackQualityChange
    // 0x97c14c: LeaveFrame
    //     0x97c14c: mov             SP, fp
    //     0x97c150: ldp             fp, lr, [SP], #0x10
    // 0x97c154: ret
    //     0x97c154: ret             
    // 0x97c158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c158: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c15c: b               #0x97c144
  }
  _ onPlaybackQualityChange(/* No info */) {
    // ** addr: 0x97c160, size: 0x94
    // 0x97c160: EnterFrame
    //     0x97c160: stp             fp, lr, [SP, #-0x10]!
    //     0x97c164: mov             fp, SP
    // 0x97c168: AllocStack(0x18)
    //     0x97c168: sub             SP, SP, #0x18
    // 0x97c16c: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x97c16c: mov             x3, x2
    //     0x97c170: stur            x2, [fp, #-0x10]
    // 0x97c174: CheckStackOverflow
    //     0x97c174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c178: cmp             SP, x16
    //     0x97c17c: b.ls            #0x97c1ec
    // 0x97c180: LoadField: r4 = r1->field_7
    //     0x97c180: ldur            w4, [x1, #7]
    // 0x97c184: DecompressPointer r4
    //     0x97c184: add             x4, x4, HEAP, lsl #32
    // 0x97c188: mov             x0, x3
    // 0x97c18c: stur            x4, [fp, #-8]
    // 0x97c190: r2 = Null
    //     0x97c190: mov             x2, NULL
    // 0x97c194: r1 = Null
    //     0x97c194: mov             x1, NULL
    // 0x97c198: r4 = 60
    //     0x97c198: movz            x4, #0x3c
    // 0x97c19c: branchIfSmi(r0, 0x97c1a8)
    //     0x97c19c: tbz             w0, #0, #0x97c1a8
    // 0x97c1a0: r4 = LoadClassIdInstr(r0)
    //     0x97c1a0: ldur            x4, [x0, #-1]
    //     0x97c1a4: ubfx            x4, x4, #0xc, #0x14
    // 0x97c1a8: sub             x4, x4, #0x5e
    // 0x97c1ac: cmp             x4, #1
    // 0x97c1b0: b.ls            #0x97c1c4
    // 0x97c1b4: r8 = String
    //     0x97c1b4: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x97c1b8: r3 = Null
    //     0x97c1b8: add             x3, PP, #0x49, lsl #12  ; [pp+0x49c98] Null
    //     0x97c1bc: ldr             x3, [x3, #0xc98]
    // 0x97c1c0: r0 = String()
    //     0x97c1c0: bl              #0xed43b0  ; IsType_String_Stub
    // 0x97c1c4: ldur            x16, [fp, #-0x10]
    // 0x97c1c8: str             x16, [SP]
    // 0x97c1cc: ldur            x1, [fp, #-8]
    // 0x97c1d0: r4 = const [0, 0x2, 0x1, 0x1, playbackQuality, 0x1, null]
    //     0x97c1d0: add             x4, PP, #0x49, lsl #12  ; [pp+0x49ca8] List(7) [0, 0x2, 0x1, 0x1, "playbackQuality", 0x1, Null]
    //     0x97c1d4: ldr             x4, [x4, #0xca8]
    // 0x97c1d8: r0 = update()
    //     0x97c1d8: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97c1dc: r0 = Null
    //     0x97c1dc: mov             x0, NULL
    // 0x97c1e0: LeaveFrame
    //     0x97c1e0: mov             SP, fp
    //     0x97c1e4: ldp             fp, lr, [SP], #0x10
    // 0x97c1e8: ret
    //     0x97c1e8: ret             
    // 0x97c1ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c1ec: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c1f0: b               #0x97c180
  }
  [closure] Future<void> onStateChange(dynamic, Object) {
    // ** addr: 0x97c1f4, size: 0x3c
    // 0x97c1f4: EnterFrame
    //     0x97c1f4: stp             fp, lr, [SP, #-0x10]!
    //     0x97c1f8: mov             fp, SP
    // 0x97c1fc: ldr             x0, [fp, #0x18]
    // 0x97c200: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97c200: ldur            w1, [x0, #0x17]
    // 0x97c204: DecompressPointer r1
    //     0x97c204: add             x1, x1, HEAP, lsl #32
    // 0x97c208: CheckStackOverflow
    //     0x97c208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c20c: cmp             SP, x16
    //     0x97c210: b.ls            #0x97c228
    // 0x97c214: ldr             x2, [fp, #0x10]
    // 0x97c218: r0 = onStateChange()
    //     0x97c218: bl              #0x97c230  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onStateChange
    // 0x97c21c: LeaveFrame
    //     0x97c21c: mov             SP, fp
    //     0x97c220: ldp             fp, lr, [SP], #0x10
    // 0x97c224: ret
    //     0x97c224: ret             
    // 0x97c228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c228: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c22c: b               #0x97c214
  }
  _ onStateChange(/* No info */) async {
    // ** addr: 0x97c230, size: 0x25c
    // 0x97c230: EnterFrame
    //     0x97c230: stp             fp, lr, [SP, #-0x10]!
    //     0x97c234: mov             fp, SP
    // 0x97c238: AllocStack(0x58)
    //     0x97c238: sub             SP, SP, #0x58
    // 0x97c23c: SetupParameters(YoutubePlayerEventHandler this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x97c23c: stur            NULL, [fp, #-8]
    //     0x97c240: stur            x1, [fp, #-0x10]
    //     0x97c244: mov             x16, x2
    //     0x97c248: mov             x2, x1
    //     0x97c24c: mov             x1, x16
    //     0x97c250: stur            x1, [fp, #-0x18]
    // 0x97c254: CheckStackOverflow
    //     0x97c254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c258: cmp             SP, x16
    //     0x97c25c: b.ls            #0x97c458
    // 0x97c260: InitAsync() -> Future<void?>
    //     0x97c260: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x97c264: bl              #0x661298  ; InitAsyncStub
    // 0x97c268: ldur            x0, [fp, #-0x18]
    // 0x97c26c: r2 = Null
    //     0x97c26c: mov             x2, NULL
    // 0x97c270: r1 = Null
    //     0x97c270: mov             x1, NULL
    // 0x97c274: branchIfSmi(r0, 0x97c29c)
    //     0x97c274: tbz             w0, #0, #0x97c29c
    // 0x97c278: r4 = LoadClassIdInstr(r0)
    //     0x97c278: ldur            x4, [x0, #-1]
    //     0x97c27c: ubfx            x4, x4, #0xc, #0x14
    // 0x97c280: sub             x4, x4, #0x3c
    // 0x97c284: cmp             x4, #1
    // 0x97c288: b.ls            #0x97c29c
    // 0x97c28c: r8 = int
    //     0x97c28c: ldr             x8, [PP, #0x5a0]  ; [pp+0x5a0] Type: int
    // 0x97c290: r3 = Null
    //     0x97c290: add             x3, PP, #0x49, lsl #12  ; [pp+0x49cb0] Null
    //     0x97c294: ldr             x3, [x3, #0xcb0]
    // 0x97c298: r0 = int()
    //     0x97c298: bl              #0xed4dc4  ; IsType_int_Stub
    // 0x97c29c: r1 = 1
    //     0x97c29c: movz            x1, #0x1
    // 0x97c2a0: r0 = AllocateContext()
    //     0x97c2a0: bl              #0xec126c  ; AllocateContextStub
    // 0x97c2a4: mov             x3, x0
    // 0x97c2a8: ldur            x0, [fp, #-0x18]
    // 0x97c2ac: stur            x3, [fp, #-0x20]
    // 0x97c2b0: StoreField: r3->field_f = r0
    //     0x97c2b0: stur            w0, [x3, #0xf]
    // 0x97c2b4: mov             x2, x3
    // 0x97c2b8: r1 = Function '<anonymous closure>':.
    //     0x97c2b8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49cc0] AnonymousClosure: (0x8ea8c8), in [package:nuonline/app/data/repositories/tafsir_repository.dart] TafsirRepository::findByVerseId (0x8ea7b8)
    //     0x97c2bc: ldr             x1, [x1, #0xcc0]
    // 0x97c2c0: r0 = AllocateClosure()
    //     0x97c2c0: bl              #0xec1630  ; AllocateClosureStub
    // 0x97c2c4: r1 = Function '<anonymous closure>':.
    //     0x97c2c4: add             x1, PP, #0x49, lsl #12  ; [pp+0x49cc8] AnonymousClosure: (0x97cba4), in [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onStateChange (0x97c230)
    //     0x97c2c8: ldr             x1, [x1, #0xcc8]
    // 0x97c2cc: r2 = Null
    //     0x97c2cc: mov             x2, NULL
    // 0x97c2d0: stur            x0, [fp, #-0x18]
    // 0x97c2d4: r0 = AllocateClosure()
    //     0x97c2d4: bl              #0xec1630  ; AllocateClosureStub
    // 0x97c2d8: str             x0, [SP]
    // 0x97c2dc: ldur            x2, [fp, #-0x18]
    // 0x97c2e0: r1 = const [Instance of 'PlayerState', Instance of 'PlayerState', Instance of 'PlayerState', Instance of 'PlayerState', Instance of 'PlayerState', Instance of 'PlayerState', Instance of 'PlayerState']
    //     0x97c2e0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49cd0] List<PlayerState>(7)
    //     0x97c2e4: ldr             x1, [x1, #0xcd0]
    // 0x97c2e8: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x97c2e8: add             x4, PP, #0x25, lsl #12  ; [pp+0x25ea0] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x97c2ec: ldr             x4, [x4, #0xea0]
    // 0x97c2f0: r0 = firstWhere()
    //     0x97c2f0: bl              #0x89fe00  ; [dart:collection] ListBase::firstWhere
    // 0x97c2f4: r16 = Instance_PlayerState
    //     0x97c2f4: add             x16, PP, #0x49, lsl #12  ; [pp+0x49cd8] Obj!PlayerState@e2d291
    //     0x97c2f8: ldr             x16, [x16, #0xcd8]
    // 0x97c2fc: cmp             w0, w16
    // 0x97c300: b.ne            #0x97c430
    // 0x97c304: ldur            x2, [fp, #-0x10]
    // 0x97c308: LoadField: r3 = r2->field_7
    //     0x97c308: ldur            w3, [x2, #7]
    // 0x97c30c: DecompressPointer r3
    //     0x97c30c: add             x3, x3, HEAP, lsl #32
    // 0x97c310: stur            x3, [fp, #-0x18]
    // 0x97c314: r16 = Instance_YoutubeError
    //     0x97c314: add             x16, PP, #0x49, lsl #12  ; [pp+0x49030] Obj!YoutubeError@e2d271
    //     0x97c318: ldr             x16, [x16, #0x30]
    // 0x97c31c: stp             x16, x0, [SP]
    // 0x97c320: mov             x1, x3
    // 0x97c324: r4 = const [0, 0x3, 0x2, 0x1, error, 0x2, playerState, 0x1, null]
    //     0x97c324: add             x4, PP, #0x49, lsl #12  ; [pp+0x49ce0] List(9) [0, 0x3, 0x2, 0x1, "error", 0x2, "playerState", 0x1, Null]
    //     0x97c328: ldr             x4, [x4, #0xce0]
    // 0x97c32c: r0 = update()
    //     0x97c32c: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97c330: ldur            x1, [fp, #-0x18]
    // 0x97c334: r0 = duration()
    //     0x97c334: bl              #0x97c9dc  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::duration
    // 0x97c338: mov             x1, x0
    // 0x97c33c: stur            x1, [fp, #-0x28]
    // 0x97c340: r0 = Await()
    //     0x97c340: bl              #0x661044  ; AwaitStub
    // 0x97c344: ldur            x1, [fp, #-0x18]
    // 0x97c348: stur            x0, [fp, #-0x28]
    // 0x97c34c: r0 = videoData()
    //     0x97c34c: bl              #0x97c498  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::videoData
    // 0x97c350: mov             x1, x0
    // 0x97c354: stur            x1, [fp, #-0x30]
    // 0x97c358: r0 = Await()
    //     0x97c358: bl              #0x661044  ; AwaitStub
    // 0x97c35c: mov             x1, x0
    // 0x97c360: ldur            x0, [fp, #-0x28]
    // 0x97c364: stur            x1, [fp, #-0x20]
    // 0x97c368: cmp             w0, NULL
    // 0x97c36c: b.eq            #0x97c460
    // 0x97c370: LoadField: d0 = r0->field_7
    //     0x97c370: ldur            d0, [x0, #7]
    // 0x97c374: d1 = 1000.000000
    //     0x97c374: add             x17, PP, #0x27, lsl #12  ; [pp+0x27238] IMM: double(1000) from 0x408f400000000000
    //     0x97c378: ldr             d1, [x17, #0x238]
    // 0x97c37c: fmul            d2, d0, d1
    // 0x97c380: fcmp            d2, d2
    // 0x97c384: b.vs            #0x97c464
    // 0x97c388: fcvtzs          x0, d2
    // 0x97c38c: asr             x16, x0, #0x1e
    // 0x97c390: cmp             x16, x0, asr #63
    // 0x97c394: b.ne            #0x97c464
    // 0x97c398: lsl             x0, x0, #1
    // 0x97c39c: r2 = LoadInt32Instr(r0)
    //     0x97c39c: sbfx            x2, x0, #1, #0x1f
    //     0x97c3a0: tbz             w0, #0, #0x97c3a8
    //     0x97c3a4: ldur            x2, [x0, #7]
    // 0x97c3a8: r16 = 1000
    //     0x97c3a8: movz            x16, #0x3e8
    // 0x97c3ac: mul             x0, x2, x16
    // 0x97c3b0: stur            x0, [fp, #-0x38]
    // 0x97c3b4: r0 = Duration()
    //     0x97c3b4: bl              #0x6223bc  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x97c3b8: mov             x1, x0
    // 0x97c3bc: ldur            x0, [fp, #-0x38]
    // 0x97c3c0: stur            x1, [fp, #-0x48]
    // 0x97c3c4: StoreField: r1->field_7 = r0
    //     0x97c3c4: stur            x0, [x1, #7]
    // 0x97c3c8: ldur            x0, [fp, #-0x20]
    // 0x97c3cc: LoadField: r2 = r0->field_7
    //     0x97c3cc: ldur            w2, [x0, #7]
    // 0x97c3d0: DecompressPointer r2
    //     0x97c3d0: add             x2, x2, HEAP, lsl #32
    // 0x97c3d4: stur            x2, [fp, #-0x40]
    // 0x97c3d8: LoadField: r3 = r0->field_b
    //     0x97c3d8: ldur            w3, [x0, #0xb]
    // 0x97c3dc: DecompressPointer r3
    //     0x97c3dc: add             x3, x3, HEAP, lsl #32
    // 0x97c3e0: stur            x3, [fp, #-0x30]
    // 0x97c3e4: LoadField: r4 = r0->field_f
    //     0x97c3e4: ldur            w4, [x0, #0xf]
    // 0x97c3e8: DecompressPointer r4
    //     0x97c3e8: add             x4, x4, HEAP, lsl #32
    // 0x97c3ec: stur            x4, [fp, #-0x28]
    // 0x97c3f0: r0 = YoutubeMetaData()
    //     0x97c3f0: bl              #0x97c48c  ; AllocateYoutubeMetaDataStub -> YoutubeMetaData (size=0x18)
    // 0x97c3f4: mov             x1, x0
    // 0x97c3f8: ldur            x0, [fp, #-0x40]
    // 0x97c3fc: StoreField: r1->field_7 = r0
    //     0x97c3fc: stur            w0, [x1, #7]
    // 0x97c400: ldur            x0, [fp, #-0x28]
    // 0x97c404: StoreField: r1->field_b = r0
    //     0x97c404: stur            w0, [x1, #0xb]
    // 0x97c408: ldur            x0, [fp, #-0x30]
    // 0x97c40c: StoreField: r1->field_f = r0
    //     0x97c40c: stur            w0, [x1, #0xf]
    // 0x97c410: ldur            x0, [fp, #-0x48]
    // 0x97c414: StoreField: r1->field_13 = r0
    //     0x97c414: stur            w0, [x1, #0x13]
    // 0x97c418: str             x1, [SP]
    // 0x97c41c: ldur            x1, [fp, #-0x18]
    // 0x97c420: r4 = const [0, 0x2, 0x1, 0x1, metaData, 0x1, null]
    //     0x97c420: add             x4, PP, #0x49, lsl #12  ; [pp+0x49ce8] List(7) [0, 0x2, 0x1, 0x1, "metaData", 0x1, Null]
    //     0x97c424: ldr             x4, [x4, #0xce8]
    // 0x97c428: r0 = update()
    //     0x97c428: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97c42c: b               #0x97c450
    // 0x97c430: ldur            x1, [fp, #-0x10]
    // 0x97c434: LoadField: r2 = r1->field_7
    //     0x97c434: ldur            w2, [x1, #7]
    // 0x97c438: DecompressPointer r2
    //     0x97c438: add             x2, x2, HEAP, lsl #32
    // 0x97c43c: str             x0, [SP]
    // 0x97c440: mov             x1, x2
    // 0x97c444: r4 = const [0, 0x2, 0x1, 0x1, playerState, 0x1, null]
    //     0x97c444: add             x4, PP, #0x49, lsl #12  ; [pp+0x49cf0] List(7) [0, 0x2, 0x1, 0x1, "playerState", 0x1, Null]
    //     0x97c448: ldr             x4, [x4, #0xcf0]
    // 0x97c44c: r0 = update()
    //     0x97c44c: bl              #0x97bad8  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::update
    // 0x97c450: r0 = Null
    //     0x97c450: mov             x0, NULL
    // 0x97c454: r0 = ReturnAsyncNotFuture()
    //     0x97c454: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x97c458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c458: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c45c: b               #0x97c260
    // 0x97c460: r0 = NullErrorSharedWithoutFPURegs()
    //     0x97c460: bl              #0xec2b5c  ; NullErrorSharedWithoutFPURegsStub
    // 0x97c464: SaveReg d2
    //     0x97c464: str             q2, [SP, #-0x10]!
    // 0x97c468: SaveReg r1
    //     0x97c468: str             x1, [SP, #-8]!
    // 0x97c46c: d0 = 0.000000
    //     0x97c46c: fmov            d0, d2
    // 0x97c470: r0 = 74
    //     0x97c470: movz            x0, #0x4a
    // 0x97c474: r30 = DoubleToIntegerStub
    //     0x97c474: ldr             lr, [PP, #0x2050]  ; [pp+0x2050] Stub: DoubleToInteger (0x5f19d4)
    // 0x97c478: LoadField: r30 = r30->field_7
    //     0x97c478: ldur            lr, [lr, #7]
    // 0x97c47c: blr             lr
    // 0x97c480: RestoreReg r1
    //     0x97c480: ldr             x1, [SP], #8
    // 0x97c484: RestoreReg d2
    //     0x97c484: ldr             q2, [SP], #0x10
    // 0x97c488: b               #0x97c39c
  }
  [closure] PlayerState <anonymous closure>(dynamic) {
    // ** addr: 0x97cba4, size: 0xc
    // 0x97cba4: r0 = Instance_PlayerState
    //     0x97cba4: add             x0, PP, #0x49, lsl #12  ; [pp+0x49028] Obj!PlayerState@e2d351
    //     0x97cba8: ldr             x0, [x0, #0x28]
    // 0x97cbac: ret
    //     0x97cbac: ret             
  }
  [closure] void onReady(dynamic, Object) {
    // ** addr: 0x97cbb0, size: 0x3c
    // 0x97cbb0: EnterFrame
    //     0x97cbb0: stp             fp, lr, [SP, #-0x10]!
    //     0x97cbb4: mov             fp, SP
    // 0x97cbb8: ldr             x0, [fp, #0x18]
    // 0x97cbbc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97cbbc: ldur            w1, [x0, #0x17]
    // 0x97cbc0: DecompressPointer r1
    //     0x97cbc0: add             x1, x1, HEAP, lsl #32
    // 0x97cbc4: CheckStackOverflow
    //     0x97cbc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97cbc8: cmp             SP, x16
    //     0x97cbcc: b.ls            #0x97cbe4
    // 0x97cbd0: ldr             x2, [fp, #0x10]
    // 0x97cbd4: r0 = onReady()
    //     0x97cbd4: bl              #0x97cbec  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::onReady
    // 0x97cbd8: LeaveFrame
    //     0x97cbd8: mov             SP, fp
    //     0x97cbdc: ldp             fp, lr, [SP], #0x10
    // 0x97cbe0: ret
    //     0x97cbe0: ret             
    // 0x97cbe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97cbe4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97cbe8: b               #0x97cbd0
  }
  _ onReady(/* No info */) {
    // ** addr: 0x97cbec, size: 0x54
    // 0x97cbec: EnterFrame
    //     0x97cbec: stp             fp, lr, [SP, #-0x10]!
    //     0x97cbf0: mov             fp, SP
    // 0x97cbf4: CheckStackOverflow
    //     0x97cbf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97cbf8: cmp             SP, x16
    //     0x97cbfc: b.ls            #0x97cc38
    // 0x97cc00: LoadField: r0 = r1->field_f
    //     0x97cc00: ldur            w0, [x1, #0xf]
    // 0x97cc04: DecompressPointer r0
    //     0x97cc04: add             x0, x0, HEAP, lsl #32
    // 0x97cc08: LoadField: r1 = r0->field_b
    //     0x97cc08: ldur            w1, [x0, #0xb]
    // 0x97cc0c: DecompressPointer r1
    //     0x97cc0c: add             x1, x1, HEAP, lsl #32
    // 0x97cc10: LoadField: r2 = r1->field_b
    //     0x97cc10: ldur            x2, [x1, #0xb]
    // 0x97cc14: tst             x2, #0x1e
    // 0x97cc18: b.ne            #0x97cc28
    // 0x97cc1c: mov             x1, x0
    // 0x97cc20: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97cc20: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97cc24: r0 = complete()
    //     0x97cc24: bl              #0xd68c64  ; [dart:async] _AsyncCompleter::complete
    // 0x97cc28: r0 = Null
    //     0x97cc28: mov             x0, NULL
    // 0x97cc2c: LeaveFrame
    //     0x97cc2c: mov             SP, fp
    //     0x97cc30: ldp             fp, lr, [SP], #0x10
    // 0x97cc34: ret
    //     0x97cc34: ret             
    // 0x97cc38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97cc38: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97cc3c: b               #0x97cc00
  }
  [closure] void call(dynamic, JavaScriptMessage) {
    // ** addr: 0x97cc58, size: 0x3c
    // 0x97cc58: EnterFrame
    //     0x97cc58: stp             fp, lr, [SP, #-0x10]!
    //     0x97cc5c: mov             fp, SP
    // 0x97cc60: ldr             x0, [fp, #0x18]
    // 0x97cc64: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x97cc64: ldur            w1, [x0, #0x17]
    // 0x97cc68: DecompressPointer r1
    //     0x97cc68: add             x1, x1, HEAP, lsl #32
    // 0x97cc6c: CheckStackOverflow
    //     0x97cc6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97cc70: cmp             SP, x16
    //     0x97cc74: b.ls            #0x97cc8c
    // 0x97cc78: ldr             x2, [fp, #0x10]
    // 0x97cc7c: r0 = call()
    //     0x97cc7c: bl              #0x97cc94  ; [package:youtube_player_iframe/src/controller/youtube_player_event_handler.dart] YoutubePlayerEventHandler::call
    // 0x97cc80: LeaveFrame
    //     0x97cc80: mov             SP, fp
    //     0x97cc84: ldp             fp, lr, [SP], #0x10
    // 0x97cc88: ret
    //     0x97cc88: ret             
    // 0x97cc8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97cc8c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97cc90: b               #0x97cc78
  }
  _ call(/* No info */) {
    // ** addr: 0x97cc94, size: 0x33c
    // 0x97cc94: EnterFrame
    //     0x97cc94: stp             fp, lr, [SP, #-0x10]!
    //     0x97cc98: mov             fp, SP
    // 0x97cc9c: AllocStack(0x50)
    //     0x97cc9c: sub             SP, SP, #0x50
    // 0x97cca0: SetupParameters(YoutubePlayerEventHandler this /* r1 => r0, fp-0x8 */)
    //     0x97cca0: mov             x0, x1
    //     0x97cca4: stur            x1, [fp, #-8]
    // 0x97cca8: CheckStackOverflow
    //     0x97cca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97ccac: cmp             SP, x16
    //     0x97ccb0: b.ls            #0x97cfb4
    // 0x97ccb4: LoadField: r1 = r2->field_7
    //     0x97ccb4: ldur            w1, [x2, #7]
    // 0x97ccb8: DecompressPointer r1
    //     0x97ccb8: add             x1, x1, HEAP, lsl #32
    // 0x97ccbc: r0 = jsonDecode()
    //     0x97ccbc: bl              #0x72bd44  ; [dart:convert] ::jsonDecode
    // 0x97ccc0: mov             x3, x0
    // 0x97ccc4: r2 = Null
    //     0x97ccc4: mov             x2, NULL
    // 0x97ccc8: r1 = Null
    //     0x97ccc8: mov             x1, NULL
    // 0x97cccc: stur            x3, [fp, #-0x10]
    // 0x97ccd0: r8 = Map
    //     0x97ccd0: ldr             x8, [PP, #0x1f28]  ; [pp+0x1f28] Type: Map
    // 0x97ccd4: r3 = Null
    //     0x97ccd4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49070] Null
    //     0x97ccd8: ldr             x3, [x3, #0x70]
    // 0x97ccdc: r0 = Map()
    //     0x97ccdc: bl              #0xed6ae8  ; IsType_Map_Stub
    // 0x97cce0: ldur            x2, [fp, #-0x10]
    // 0x97cce4: r1 = Null
    //     0x97cce4: mov             x1, NULL
    // 0x97cce8: r0 = LinkedHashMap.from()
    //     0x97cce8: bl              #0x63c6f0  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0x97ccec: mov             x1, x0
    // 0x97ccf0: r2 = "playerId"
    //     0x97ccf0: add             x2, PP, #0x29, lsl #12  ; [pp+0x29c50] "playerId"
    //     0x97ccf4: ldr             x2, [x2, #0xc50]
    // 0x97ccf8: stur            x0, [fp, #-0x10]
    // 0x97ccfc: r0 = _getValueOrData()
    //     0x97ccfc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x97cd00: mov             x1, x0
    // 0x97cd04: ldur            x0, [fp, #-0x10]
    // 0x97cd08: LoadField: r2 = r0->field_f
    //     0x97cd08: ldur            w2, [x0, #0xf]
    // 0x97cd0c: DecompressPointer r2
    //     0x97cd0c: add             x2, x2, HEAP, lsl #32
    // 0x97cd10: cmp             w2, w1
    // 0x97cd14: b.ne            #0x97cd20
    // 0x97cd18: r4 = Null
    //     0x97cd18: mov             x4, NULL
    // 0x97cd1c: b               #0x97cd24
    // 0x97cd20: mov             x4, x1
    // 0x97cd24: ldur            x3, [fp, #-8]
    // 0x97cd28: stur            x4, [fp, #-0x20]
    // 0x97cd2c: LoadField: r5 = r3->field_7
    //     0x97cd2c: ldur            w5, [x3, #7]
    // 0x97cd30: DecompressPointer r5
    //     0x97cd30: add             x5, x5, HEAP, lsl #32
    // 0x97cd34: stur            x5, [fp, #-0x18]
    // 0x97cd38: r1 = Null
    //     0x97cd38: mov             x1, NULL
    // 0x97cd3c: r2 = 4
    //     0x97cd3c: movz            x2, #0x4
    // 0x97cd40: r0 = AllocateArray()
    //     0x97cd40: bl              #0xec22fc  ; AllocateArrayStub
    // 0x97cd44: r16 = "Youtube"
    //     0x97cd44: add             x16, PP, #0x49, lsl #12  ; [pp+0x49058] "Youtube"
    //     0x97cd48: ldr             x16, [x16, #0x58]
    // 0x97cd4c: StoreField: r0->field_f = r16
    //     0x97cd4c: stur            w16, [x0, #0xf]
    // 0x97cd50: ldur            x1, [fp, #-0x18]
    // 0x97cd54: LoadField: r2 = r1->field_7
    //     0x97cd54: ldur            w2, [x1, #7]
    // 0x97cd58: DecompressPointer r2
    //     0x97cd58: add             x2, x2, HEAP, lsl #32
    // 0x97cd5c: StoreField: r0->field_13 = r2
    //     0x97cd5c: stur            w2, [x0, #0x13]
    // 0x97cd60: str             x0, [SP]
    // 0x97cd64: r0 = _interpolate()
    //     0x97cd64: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0x97cd68: mov             x1, x0
    // 0x97cd6c: ldur            x0, [fp, #-0x20]
    // 0x97cd70: r2 = 60
    //     0x97cd70: movz            x2, #0x3c
    // 0x97cd74: branchIfSmi(r0, 0x97cd80)
    //     0x97cd74: tbz             w0, #0, #0x97cd80
    // 0x97cd78: r2 = LoadClassIdInstr(r0)
    //     0x97cd78: ldur            x2, [x0, #-1]
    //     0x97cd7c: ubfx            x2, x2, #0xc, #0x14
    // 0x97cd80: stp             x1, x0, [SP]
    // 0x97cd84: mov             x0, x2
    // 0x97cd88: mov             lr, x0
    // 0x97cd8c: ldr             lr, [x21, lr, lsl #3]
    // 0x97cd90: blr             lr
    // 0x97cd94: tbz             w0, #4, #0x97cda8
    // 0x97cd98: r0 = Null
    //     0x97cd98: mov             x0, NULL
    // 0x97cd9c: LeaveFrame
    //     0x97cd9c: mov             SP, fp
    //     0x97cda0: ldp             fp, lr, [SP], #0x10
    // 0x97cda4: ret
    //     0x97cda4: ret             
    // 0x97cda8: ldur            x1, [fp, #-0x10]
    // 0x97cdac: r0 = entries()
    //     0x97cdac: bl              #0x89c028  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin::entries
    // 0x97cdb0: mov             x1, x0
    // 0x97cdb4: r0 = iterator()
    //     0x97cdb4: bl              #0x887d48  ; [dart:_internal] MappedIterable::iterator
    // 0x97cdb8: mov             x2, x0
    // 0x97cdbc: stur            x2, [fp, #-0x28]
    // 0x97cdc0: LoadField: r3 = r2->field_f
    //     0x97cdc0: ldur            w3, [x2, #0xf]
    // 0x97cdc4: DecompressPointer r3
    //     0x97cdc4: add             x3, x3, HEAP, lsl #32
    // 0x97cdc8: stur            x3, [fp, #-0x20]
    // 0x97cdcc: LoadField: r4 = r2->field_13
    //     0x97cdcc: ldur            w4, [x2, #0x13]
    // 0x97cdd0: DecompressPointer r4
    //     0x97cdd0: add             x4, x4, HEAP, lsl #32
    // 0x97cdd4: stur            x4, [fp, #-0x18]
    // 0x97cdd8: LoadField: r5 = r2->field_7
    //     0x97cdd8: ldur            w5, [x2, #7]
    // 0x97cddc: DecompressPointer r5
    //     0x97cddc: add             x5, x5, HEAP, lsl #32
    // 0x97cde0: stur            x5, [fp, #-0x10]
    // 0x97cde4: ldur            x6, [fp, #-8]
    // 0x97cde8: CheckStackOverflow
    //     0x97cde8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97cdec: cmp             SP, x16
    //     0x97cdf0: b.ls            #0x97cfbc
    // 0x97cdf4: r0 = LoadClassIdInstr(r3)
    //     0x97cdf4: ldur            x0, [x3, #-1]
    //     0x97cdf8: ubfx            x0, x0, #0xc, #0x14
    // 0x97cdfc: mov             x1, x3
    // 0x97ce00: r0 = GDT[cid_x0 + 0x1292d]()
    //     0x97ce00: movz            x17, #0x292d
    //     0x97ce04: movk            x17, #0x1, lsl #16
    //     0x97ce08: add             lr, x0, x17
    //     0x97ce0c: ldr             lr, [x21, lr, lsl #3]
    //     0x97ce10: blr             lr
    // 0x97ce14: tbnz            w0, #4, #0x97cf9c
    // 0x97ce18: ldur            x2, [fp, #-0x28]
    // 0x97ce1c: ldur            x3, [fp, #-0x20]
    // 0x97ce20: r0 = LoadClassIdInstr(r3)
    //     0x97ce20: ldur            x0, [x3, #-1]
    //     0x97ce24: ubfx            x0, x0, #0xc, #0x14
    // 0x97ce28: mov             x1, x3
    // 0x97ce2c: r0 = GDT[cid_x0 + 0x1384d]()
    //     0x97ce2c: movz            x17, #0x384d
    //     0x97ce30: movk            x17, #0x1, lsl #16
    //     0x97ce34: add             lr, x0, x17
    //     0x97ce38: ldr             lr, [x21, lr, lsl #3]
    //     0x97ce3c: blr             lr
    // 0x97ce40: ldur            x16, [fp, #-0x18]
    // 0x97ce44: stp             x0, x16, [SP]
    // 0x97ce48: ldur            x0, [fp, #-0x18]
    // 0x97ce4c: ClosureCall
    //     0x97ce4c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97ce50: ldur            x2, [x0, #0x1f]
    //     0x97ce54: blr             x2
    // 0x97ce58: mov             x4, x0
    // 0x97ce5c: ldur            x3, [fp, #-0x28]
    // 0x97ce60: stur            x4, [fp, #-0x30]
    // 0x97ce64: StoreField: r3->field_b = r0
    //     0x97ce64: stur            w0, [x3, #0xb]
    //     0x97ce68: tbz             w0, #0, #0x97ce84
    //     0x97ce6c: ldurb           w16, [x3, #-1]
    //     0x97ce70: ldurb           w17, [x0, #-1]
    //     0x97ce74: and             x16, x17, x16, lsr #2
    //     0x97ce78: tst             x16, HEAP, lsr #32
    //     0x97ce7c: b.eq            #0x97ce84
    //     0x97ce80: bl              #0xec0a68  ; WriteBarrierWrappersStub
    // 0x97ce84: cmp             w4, NULL
    // 0x97ce88: b.ne            #0x97cebc
    // 0x97ce8c: mov             x0, x4
    // 0x97ce90: ldur            x2, [fp, #-0x10]
    // 0x97ce94: r1 = Null
    //     0x97ce94: mov             x1, NULL
    // 0x97ce98: cmp             w2, NULL
    // 0x97ce9c: b.eq            #0x97cebc
    // 0x97cea0: LoadField: r4 = r2->field_1b
    //     0x97cea0: ldur            w4, [x2, #0x1b]
    // 0x97cea4: DecompressPointer r4
    //     0x97cea4: add             x4, x4, HEAP, lsl #32
    // 0x97cea8: r8 = X1
    //     0x97cea8: ldr             x8, [PP, #0x1b0]  ; [pp+0x1b0] TypeParameter: X1
    // 0x97ceac: LoadField: r9 = r4->field_7
    //     0x97ceac: ldur            x9, [x4, #7]
    // 0x97ceb0: r3 = Null
    //     0x97ceb0: add             x3, PP, #0x49, lsl #12  ; [pp+0x49080] Null
    //     0x97ceb4: ldr             x3, [x3, #0x80]
    // 0x97ceb8: blr             x9
    // 0x97cebc: ldur            x1, [fp, #-0x30]
    // 0x97cec0: LoadField: r2 = r1->field_b
    //     0x97cec0: ldur            w2, [x1, #0xb]
    // 0x97cec4: DecompressPointer r2
    //     0x97cec4: add             x2, x2, HEAP, lsl #32
    // 0x97cec8: stur            x2, [fp, #-0x38]
    // 0x97cecc: r0 = 60
    //     0x97cecc: movz            x0, #0x3c
    // 0x97ced0: branchIfSmi(r2, 0x97cedc)
    //     0x97ced0: tbz             w2, #0, #0x97cedc
    // 0x97ced4: r0 = LoadClassIdInstr(r2)
    //     0x97ced4: ldur            x0, [x2, #-1]
    //     0x97ced8: ubfx            x0, x0, #0xc, #0x14
    // 0x97cedc: r16 = "ApiChange"
    //     0x97cedc: add             x16, PP, #0x49, lsl #12  ; [pp+0x49090] "ApiChange"
    //     0x97cee0: ldr             x16, [x16, #0x90]
    // 0x97cee4: stp             x16, x2, [SP]
    // 0x97cee8: mov             lr, x0
    // 0x97ceec: ldr             lr, [x21, lr, lsl #3]
    // 0x97cef0: blr             lr
    // 0x97cef4: tbz             w0, #4, #0x97cf88
    // 0x97cef8: ldur            x0, [fp, #-8]
    // 0x97cefc: LoadField: r3 = r0->field_13
    //     0x97cefc: ldur            w3, [x0, #0x13]
    // 0x97cf00: DecompressPointer r3
    //     0x97cf00: add             x3, x3, HEAP, lsl #32
    // 0x97cf04: r16 = Sentinel
    //     0x97cf04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x97cf08: cmp             w3, w16
    // 0x97cf0c: b.eq            #0x97cfc4
    // 0x97cf10: mov             x1, x3
    // 0x97cf14: ldur            x2, [fp, #-0x38]
    // 0x97cf18: stur            x3, [fp, #-0x40]
    // 0x97cf1c: r0 = _getValueOrData()
    //     0x97cf1c: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x97cf20: mov             x1, x0
    // 0x97cf24: ldur            x0, [fp, #-0x40]
    // 0x97cf28: LoadField: r2 = r0->field_f
    //     0x97cf28: ldur            w2, [x0, #0xf]
    // 0x97cf2c: DecompressPointer r2
    //     0x97cf2c: add             x2, x2, HEAP, lsl #32
    // 0x97cf30: cmp             w2, w1
    // 0x97cf34: b.ne            #0x97cf40
    // 0x97cf38: r0 = Null
    //     0x97cf38: mov             x0, NULL
    // 0x97cf3c: b               #0x97cf44
    // 0x97cf40: mov             x0, x1
    // 0x97cf44: stur            x0, [fp, #-0x38]
    // 0x97cf48: cmp             w0, NULL
    // 0x97cf4c: b.eq            #0x97cf88
    // 0x97cf50: ldur            x1, [fp, #-0x30]
    // 0x97cf54: LoadField: r2 = r1->field_f
    //     0x97cf54: ldur            w2, [x1, #0xf]
    // 0x97cf58: DecompressPointer r2
    //     0x97cf58: add             x2, x2, HEAP, lsl #32
    // 0x97cf5c: cmp             w2, NULL
    // 0x97cf60: b.ne            #0x97cf6c
    // 0x97cf64: r0 = Object()
    //     0x97cf64: bl              #0x60cd48  ; AllocateObjectStub -> Object (size=0x8)
    // 0x97cf68: b               #0x97cf70
    // 0x97cf6c: mov             x0, x2
    // 0x97cf70: ldur            x16, [fp, #-0x38]
    // 0x97cf74: stp             x0, x16, [SP]
    // 0x97cf78: ldur            x0, [fp, #-0x38]
    // 0x97cf7c: ClosureCall
    //     0x97cf7c: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x97cf80: ldur            x2, [x0, #0x1f]
    //     0x97cf84: blr             x2
    // 0x97cf88: ldur            x2, [fp, #-0x28]
    // 0x97cf8c: ldur            x5, [fp, #-0x10]
    // 0x97cf90: ldur            x3, [fp, #-0x20]
    // 0x97cf94: ldur            x4, [fp, #-0x18]
    // 0x97cf98: b               #0x97cde4
    // 0x97cf9c: ldur            x1, [fp, #-0x28]
    // 0x97cfa0: StoreField: r1->field_b = rNULL
    //     0x97cfa0: stur            NULL, [x1, #0xb]
    // 0x97cfa4: r0 = Null
    //     0x97cfa4: mov             x0, NULL
    // 0x97cfa8: LeaveFrame
    //     0x97cfa8: mov             SP, fp
    //     0x97cfac: ldp             fp, lr, [SP], #0x10
    // 0x97cfb0: ret
    //     0x97cfb0: ret             
    // 0x97cfb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97cfb4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97cfb8: b               #0x97ccb4
    // 0x97cfbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97cfbc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97cfc0: b               #0x97cdf4
    // 0x97cfc4: r9 = _events
    //     0x97cfc4: add             x9, PP, #0x49, lsl #12  ; [pp+0x49098] Field <YoutubePlayerEventHandler._events@2782244153>: late final (offset: 0x14)
    //     0x97cfc8: ldr             x9, [x9, #0x98]
    // 0x97cfcc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x97cfcc: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
