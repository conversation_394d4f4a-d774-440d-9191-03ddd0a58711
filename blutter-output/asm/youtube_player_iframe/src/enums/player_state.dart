// lib: , url: package:youtube_player_iframe/src/enums/player_state.dart

// class id: 1051351, size: 0x8
class :: {
}

// class id: 6730, size: 0x1c, field offset: 0x14
enum PlayerState extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  _Mint field_14;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4fd00, size: 0x64
    // 0xc4fd00: EnterFrame
    //     0xc4fd00: stp             fp, lr, [SP, #-0x10]!
    //     0xc4fd04: mov             fp, SP
    // 0xc4fd08: AllocStack(0x10)
    //     0xc4fd08: sub             SP, SP, #0x10
    // 0xc4fd0c: SetupParameters(PlayerState this /* r1 => r0, fp-0x8 */)
    //     0xc4fd0c: mov             x0, x1
    //     0xc4fd10: stur            x1, [fp, #-8]
    // 0xc4fd14: CheckStackOverflow
    //     0xc4fd14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4fd18: cmp             SP, x16
    //     0xc4fd1c: b.ls            #0xc4fd5c
    // 0xc4fd20: r1 = Null
    //     0xc4fd20: mov             x1, NULL
    // 0xc4fd24: r2 = 4
    //     0xc4fd24: movz            x2, #0x4
    // 0xc4fd28: r0 = AllocateArray()
    //     0xc4fd28: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4fd2c: r16 = "PlayerState."
    //     0xc4fd2c: add             x16, PP, #0x39, lsl #12  ; [pp+0x39418] "PlayerState."
    //     0xc4fd30: ldr             x16, [x16, #0x418]
    // 0xc4fd34: StoreField: r0->field_f = r16
    //     0xc4fd34: stur            w16, [x0, #0xf]
    // 0xc4fd38: ldur            x1, [fp, #-8]
    // 0xc4fd3c: LoadField: r2 = r1->field_f
    //     0xc4fd3c: ldur            w2, [x1, #0xf]
    // 0xc4fd40: DecompressPointer r2
    //     0xc4fd40: add             x2, x2, HEAP, lsl #32
    // 0xc4fd44: StoreField: r0->field_13 = r2
    //     0xc4fd44: stur            w2, [x0, #0x13]
    // 0xc4fd48: str             x0, [SP]
    // 0xc4fd4c: r0 = _interpolate()
    //     0xc4fd4c: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4fd50: LeaveFrame
    //     0xc4fd50: mov             SP, fp
    //     0xc4fd54: ldp             fp, lr, [SP], #0x10
    // 0xc4fd58: ret
    //     0xc4fd58: ret             
    // 0xc4fd5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4fd5c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4fd60: b               #0xc4fd20
  }
}
