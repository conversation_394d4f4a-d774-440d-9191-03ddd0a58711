// lib: , url: package:youtube_player_iframe/src/enums/youtube_error.dart

// class id: 1051352, size: 0x8
class :: {
}

// class id: 6729, size: 0x1c, field offset: 0x14
enum YoutubeError extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  _Mint field_14;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4fd64, size: 0x64
    // 0xc4fd64: EnterFrame
    //     0xc4fd64: stp             fp, lr, [SP, #-0x10]!
    //     0xc4fd68: mov             fp, SP
    // 0xc4fd6c: AllocStack(0x10)
    //     0xc4fd6c: sub             SP, SP, #0x10
    // 0xc4fd70: SetupParameters(YoutubeError this /* r1 => r0, fp-0x8 */)
    //     0xc4fd70: mov             x0, x1
    //     0xc4fd74: stur            x1, [fp, #-8]
    // 0xc4fd78: CheckStackOverflow
    //     0xc4fd78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4fd7c: cmp             SP, x16
    //     0xc4fd80: b.ls            #0xc4fdc0
    // 0xc4fd84: r1 = Null
    //     0xc4fd84: mov             x1, NULL
    // 0xc4fd88: r2 = 4
    //     0xc4fd88: movz            x2, #0x4
    // 0xc4fd8c: r0 = AllocateArray()
    //     0xc4fd8c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4fd90: r16 = "YoutubeError."
    //     0xc4fd90: add             x16, PP, #0x51, lsl #12  ; [pp+0x51368] "YoutubeError."
    //     0xc4fd94: ldr             x16, [x16, #0x368]
    // 0xc4fd98: StoreField: r0->field_f = r16
    //     0xc4fd98: stur            w16, [x0, #0xf]
    // 0xc4fd9c: ldur            x1, [fp, #-8]
    // 0xc4fda0: LoadField: r2 = r1->field_f
    //     0xc4fda0: ldur            w2, [x1, #0xf]
    // 0xc4fda4: DecompressPointer r2
    //     0xc4fda4: add             x2, x2, HEAP, lsl #32
    // 0xc4fda8: StoreField: r0->field_13 = r2
    //     0xc4fda8: stur            w2, [x0, #0x13]
    // 0xc4fdac: str             x0, [SP]
    // 0xc4fdb0: r0 = _interpolate()
    //     0xc4fdb0: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4fdb4: LeaveFrame
    //     0xc4fdb4: mov             SP, fp
    //     0xc4fdb8: ldp             fp, lr, [SP], #0x10
    // 0xc4fdbc: ret
    //     0xc4fdbc: ret             
    // 0xc4fdc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4fdc0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4fdc4: b               #0xc4fd84
  }
}
