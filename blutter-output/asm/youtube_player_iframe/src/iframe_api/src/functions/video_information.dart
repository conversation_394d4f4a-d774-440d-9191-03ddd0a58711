// lib: , url: package:youtube_player_iframe/src/iframe_api/src/functions/video_information.dart

// class id: 1051357, size: 0x8
class :: {
}

// class id: 179, size: 0x14, field offset: 0x8
//   const constructor, 
class VideoData extends Object {

  factory _ VideoData.fromMap(/* No info */) {
    // ** addr: 0x97c518, size: 0x2b0
    // 0x97c518: EnterFrame
    //     0x97c518: stp             fp, lr, [SP, #-0x10]!
    //     0x97c51c: mov             fp, SP
    // 0x97c520: AllocStack(0x20)
    //     0x97c520: sub             SP, SP, #0x20
    // 0x97c524: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x97c524: mov             x0, x2
    //     0x97c528: stur            x2, [fp, #-8]
    // 0x97c52c: CheckStackOverflow
    //     0x97c52c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c530: cmp             SP, x16
    //     0x97c534: b.ls            #0x97c7c0
    // 0x97c538: mov             x1, x0
    // 0x97c53c: r2 = "video_id"
    //     0x97c53c: add             x2, PP, #0x49, lsl #12  ; [pp+0x49d10] "video_id"
    //     0x97c540: ldr             x2, [x2, #0xd10]
    // 0x97c544: r0 = _getValueOrData()
    //     0x97c544: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x97c548: ldur            x3, [fp, #-8]
    // 0x97c54c: LoadField: r1 = r3->field_f
    //     0x97c54c: ldur            w1, [x3, #0xf]
    // 0x97c550: DecompressPointer r1
    //     0x97c550: add             x1, x1, HEAP, lsl #32
    // 0x97c554: cmp             w1, w0
    // 0x97c558: b.ne            #0x97c560
    // 0x97c55c: r0 = Null
    //     0x97c55c: mov             x0, NULL
    // 0x97c560: cmp             w0, NULL
    // 0x97c564: b.ne            #0x97c570
    // 0x97c568: r4 = ""
    //     0x97c568: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x97c56c: b               #0x97c574
    // 0x97c570: mov             x4, x0
    // 0x97c574: mov             x0, x4
    // 0x97c578: stur            x4, [fp, #-0x10]
    // 0x97c57c: r2 = Null
    //     0x97c57c: mov             x2, NULL
    // 0x97c580: r1 = Null
    //     0x97c580: mov             x1, NULL
    // 0x97c584: r4 = 60
    //     0x97c584: movz            x4, #0x3c
    // 0x97c588: branchIfSmi(r0, 0x97c594)
    //     0x97c588: tbz             w0, #0, #0x97c594
    // 0x97c58c: r4 = LoadClassIdInstr(r0)
    //     0x97c58c: ldur            x4, [x0, #-1]
    //     0x97c590: ubfx            x4, x4, #0xc, #0x14
    // 0x97c594: sub             x4, x4, #0x5e
    // 0x97c598: cmp             x4, #1
    // 0x97c59c: b.ls            #0x97c5b0
    // 0x97c5a0: r8 = String
    //     0x97c5a0: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x97c5a4: r3 = Null
    //     0x97c5a4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49d18] Null
    //     0x97c5a8: ldr             x3, [x3, #0xd18]
    // 0x97c5ac: r0 = String()
    //     0x97c5ac: bl              #0xed43b0  ; IsType_String_Stub
    // 0x97c5b0: ldur            x1, [fp, #-8]
    // 0x97c5b4: r2 = "author"
    //     0x97c5b4: add             x2, PP, #0x37, lsl #12  ; [pp+0x37bf8] "author"
    //     0x97c5b8: ldr             x2, [x2, #0xbf8]
    // 0x97c5bc: r0 = _getValueOrData()
    //     0x97c5bc: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x97c5c0: ldur            x3, [fp, #-8]
    // 0x97c5c4: LoadField: r1 = r3->field_f
    //     0x97c5c4: ldur            w1, [x3, #0xf]
    // 0x97c5c8: DecompressPointer r1
    //     0x97c5c8: add             x1, x1, HEAP, lsl #32
    // 0x97c5cc: cmp             w1, w0
    // 0x97c5d0: b.ne            #0x97c5d8
    // 0x97c5d4: r0 = Null
    //     0x97c5d4: mov             x0, NULL
    // 0x97c5d8: cmp             w0, NULL
    // 0x97c5dc: b.ne            #0x97c5e8
    // 0x97c5e0: r4 = ""
    //     0x97c5e0: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x97c5e4: b               #0x97c5ec
    // 0x97c5e8: mov             x4, x0
    // 0x97c5ec: mov             x0, x4
    // 0x97c5f0: stur            x4, [fp, #-0x18]
    // 0x97c5f4: r2 = Null
    //     0x97c5f4: mov             x2, NULL
    // 0x97c5f8: r1 = Null
    //     0x97c5f8: mov             x1, NULL
    // 0x97c5fc: r4 = 60
    //     0x97c5fc: movz            x4, #0x3c
    // 0x97c600: branchIfSmi(r0, 0x97c60c)
    //     0x97c600: tbz             w0, #0, #0x97c60c
    // 0x97c604: r4 = LoadClassIdInstr(r0)
    //     0x97c604: ldur            x4, [x0, #-1]
    //     0x97c608: ubfx            x4, x4, #0xc, #0x14
    // 0x97c60c: sub             x4, x4, #0x5e
    // 0x97c610: cmp             x4, #1
    // 0x97c614: b.ls            #0x97c628
    // 0x97c618: r8 = String
    //     0x97c618: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x97c61c: r3 = Null
    //     0x97c61c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49d28] Null
    //     0x97c620: ldr             x3, [x3, #0xd28]
    // 0x97c624: r0 = String()
    //     0x97c624: bl              #0xed43b0  ; IsType_String_Stub
    // 0x97c628: ldur            x1, [fp, #-8]
    // 0x97c62c: r2 = "title"
    //     0x97c62c: add             x2, PP, #8, lsl #12  ; [pp+0x8748] "title"
    //     0x97c630: ldr             x2, [x2, #0x748]
    // 0x97c634: r0 = _getValueOrData()
    //     0x97c634: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x97c638: ldur            x3, [fp, #-8]
    // 0x97c63c: LoadField: r1 = r3->field_f
    //     0x97c63c: ldur            w1, [x3, #0xf]
    // 0x97c640: DecompressPointer r1
    //     0x97c640: add             x1, x1, HEAP, lsl #32
    // 0x97c644: cmp             w1, w0
    // 0x97c648: b.ne            #0x97c650
    // 0x97c64c: r0 = Null
    //     0x97c64c: mov             x0, NULL
    // 0x97c650: cmp             w0, NULL
    // 0x97c654: b.ne            #0x97c660
    // 0x97c658: r4 = ""
    //     0x97c658: ldr             x4, [PP, #0x288]  ; [pp+0x288] ""
    // 0x97c65c: b               #0x97c664
    // 0x97c660: mov             x4, x0
    // 0x97c664: mov             x0, x4
    // 0x97c668: stur            x4, [fp, #-0x20]
    // 0x97c66c: r2 = Null
    //     0x97c66c: mov             x2, NULL
    // 0x97c670: r1 = Null
    //     0x97c670: mov             x1, NULL
    // 0x97c674: r4 = 60
    //     0x97c674: movz            x4, #0x3c
    // 0x97c678: branchIfSmi(r0, 0x97c684)
    //     0x97c678: tbz             w0, #0, #0x97c684
    // 0x97c67c: r4 = LoadClassIdInstr(r0)
    //     0x97c67c: ldur            x4, [x0, #-1]
    //     0x97c680: ubfx            x4, x4, #0xc, #0x14
    // 0x97c684: sub             x4, x4, #0x5e
    // 0x97c688: cmp             x4, #1
    // 0x97c68c: b.ls            #0x97c6a0
    // 0x97c690: r8 = String
    //     0x97c690: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x97c694: r3 = Null
    //     0x97c694: add             x3, PP, #0x49, lsl #12  ; [pp+0x49d38] Null
    //     0x97c698: ldr             x3, [x3, #0xd38]
    // 0x97c69c: r0 = String()
    //     0x97c69c: bl              #0xed43b0  ; IsType_String_Stub
    // 0x97c6a0: ldur            x1, [fp, #-8]
    // 0x97c6a4: r2 = "videoQuality"
    //     0x97c6a4: add             x2, PP, #0x49, lsl #12  ; [pp+0x49d48] "videoQuality"
    //     0x97c6a8: ldr             x2, [x2, #0xd48]
    // 0x97c6ac: r0 = _getValueOrData()
    //     0x97c6ac: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x97c6b0: ldur            x3, [fp, #-8]
    // 0x97c6b4: LoadField: r1 = r3->field_f
    //     0x97c6b4: ldur            w1, [x3, #0xf]
    // 0x97c6b8: DecompressPointer r1
    //     0x97c6b8: add             x1, x1, HEAP, lsl #32
    // 0x97c6bc: cmp             w1, w0
    // 0x97c6c0: b.ne            #0x97c6c8
    // 0x97c6c4: r0 = Null
    //     0x97c6c4: mov             x0, NULL
    // 0x97c6c8: cmp             w0, NULL
    // 0x97c6cc: b.ne            #0x97c6d4
    // 0x97c6d0: r0 = ""
    //     0x97c6d0: ldr             x0, [PP, #0x288]  ; [pp+0x288] ""
    // 0x97c6d4: r2 = Null
    //     0x97c6d4: mov             x2, NULL
    // 0x97c6d8: r1 = Null
    //     0x97c6d8: mov             x1, NULL
    // 0x97c6dc: r4 = 60
    //     0x97c6dc: movz            x4, #0x3c
    // 0x97c6e0: branchIfSmi(r0, 0x97c6ec)
    //     0x97c6e0: tbz             w0, #0, #0x97c6ec
    // 0x97c6e4: r4 = LoadClassIdInstr(r0)
    //     0x97c6e4: ldur            x4, [x0, #-1]
    //     0x97c6e8: ubfx            x4, x4, #0xc, #0x14
    // 0x97c6ec: sub             x4, x4, #0x5e
    // 0x97c6f0: cmp             x4, #1
    // 0x97c6f4: b.ls            #0x97c708
    // 0x97c6f8: r8 = String
    //     0x97c6f8: ldr             x8, [PP, #0x2a0]  ; [pp+0x2a0] Type: String
    // 0x97c6fc: r3 = Null
    //     0x97c6fc: add             x3, PP, #0x49, lsl #12  ; [pp+0x49d50] Null
    //     0x97c700: ldr             x3, [x3, #0xd50]
    // 0x97c704: r0 = String()
    //     0x97c704: bl              #0xed43b0  ; IsType_String_Stub
    // 0x97c708: ldur            x1, [fp, #-8]
    // 0x97c70c: r2 = "videoQualityFeatures"
    //     0x97c70c: add             x2, PP, #0x49, lsl #12  ; [pp+0x49d60] "videoQualityFeatures"
    //     0x97c710: ldr             x2, [x2, #0xd60]
    // 0x97c714: r0 = _getValueOrData()
    //     0x97c714: bl              #0xeba224  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x97c718: mov             x1, x0
    // 0x97c71c: ldur            x0, [fp, #-8]
    // 0x97c720: LoadField: r2 = r0->field_f
    //     0x97c720: ldur            w2, [x0, #0xf]
    // 0x97c724: DecompressPointer r2
    //     0x97c724: add             x2, x2, HEAP, lsl #32
    // 0x97c728: cmp             w2, w1
    // 0x97c72c: b.ne            #0x97c738
    // 0x97c730: r0 = Null
    //     0x97c730: mov             x0, NULL
    // 0x97c734: b               #0x97c73c
    // 0x97c738: mov             x0, x1
    // 0x97c73c: cmp             w0, NULL
    // 0x97c740: b.ne            #0x97c758
    // 0x97c744: r1 = Null
    //     0x97c744: mov             x1, NULL
    // 0x97c748: r2 = 0
    //     0x97c748: movz            x2, #0
    // 0x97c74c: r0 = _GrowableList()
    //     0x97c74c: bl              #0x6024ac  ; [dart:core] _GrowableList::_GrowableList
    // 0x97c750: mov             x6, x0
    // 0x97c754: b               #0x97c75c
    // 0x97c758: mov             x6, x0
    // 0x97c75c: ldur            x5, [fp, #-0x10]
    // 0x97c760: ldur            x4, [fp, #-0x18]
    // 0x97c764: ldur            x3, [fp, #-0x20]
    // 0x97c768: mov             x0, x6
    // 0x97c76c: stur            x6, [fp, #-8]
    // 0x97c770: r2 = Null
    //     0x97c770: mov             x2, NULL
    // 0x97c774: r1 = Null
    //     0x97c774: mov             x1, NULL
    // 0x97c778: r8 = Iterable
    //     0x97c778: ldr             x8, [PP, #0xdb8]  ; [pp+0xdb8] Type: Iterable
    // 0x97c77c: r3 = Null
    //     0x97c77c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49d68] Null
    //     0x97c780: ldr             x3, [x3, #0xd68]
    // 0x97c784: r0 = Iterable()
    //     0x97c784: bl              #0x60480c  ; IsType_Iterable_Stub
    // 0x97c788: ldur            x2, [fp, #-8]
    // 0x97c78c: r1 = <Object>
    //     0x97c78c: ldr             x1, [PP, #0x1138]  ; [pp+0x1138] TypeArguments: <Object>
    // 0x97c790: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x97c790: ldr             x4, [PP, #0x1f8]  ; [pp+0x1f8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x97c794: r0 = List.from()
    //     0x97c794: bl              #0x6b9500  ; [dart:core] List::List.from
    // 0x97c798: r0 = VideoData()
    //     0x97c798: bl              #0x97c7c8  ; AllocateVideoDataStub -> VideoData (size=0x14)
    // 0x97c79c: ldur            x1, [fp, #-0x10]
    // 0x97c7a0: StoreField: r0->field_7 = r1
    //     0x97c7a0: stur            w1, [x0, #7]
    // 0x97c7a4: ldur            x1, [fp, #-0x18]
    // 0x97c7a8: StoreField: r0->field_b = r1
    //     0x97c7a8: stur            w1, [x0, #0xb]
    // 0x97c7ac: ldur            x1, [fp, #-0x20]
    // 0x97c7b0: StoreField: r0->field_f = r1
    //     0x97c7b0: stur            w1, [x0, #0xf]
    // 0x97c7b4: LeaveFrame
    //     0x97c7b4: mov             SP, fp
    //     0x97c7b8: ldp             fp, lr, [SP], #0x10
    // 0x97c7bc: ret
    //     0x97c7bc: ret             
    // 0x97c7c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c7c0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c7c4: b               #0x97c538
  }
}

// class id: 180, size: 0x8, field offset: 0x8
abstract class VideoInformation extends Object {
}
