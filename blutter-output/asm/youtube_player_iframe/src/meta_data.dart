// lib: , url: package:youtube_player_iframe/src/meta_data.dart

// class id: 1051359, size: 0x8
class :: {
}

// class id: 178, size: 0x18, field offset: 0x8
//   const constructor, 
class YoutubeMetaData extends Object {

  _OneByteString field_8;
  _OneByteString field_c;
  _OneByteString field_10;
  Duration field_14;

  _ toString(/* No info */) {
    // ** addr: 0xc45780, size: 0xfc
    // 0xc45780: EnterFrame
    //     0xc45780: stp             fp, lr, [SP, #-0x10]!
    //     0xc45784: mov             fp, SP
    // 0xc45788: AllocStack(0x8)
    //     0xc45788: sub             SP, SP, #8
    // 0xc4578c: CheckStackOverflow
    //     0xc4578c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc45790: cmp             SP, x16
    //     0xc45794: b.ls            #0xc45874
    // 0xc45798: r1 = Null
    //     0xc45798: mov             x1, NULL
    // 0xc4579c: r2 = 18
    //     0xc4579c: movz            x2, #0x12
    // 0xc457a0: r0 = AllocateArray()
    //     0xc457a0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc457a4: mov             x2, x0
    // 0xc457a8: r16 = "YoutubeMetaData(videoId: "
    //     0xc457a8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51340] "YoutubeMetaData(videoId: "
    //     0xc457ac: ldr             x16, [x16, #0x340]
    // 0xc457b0: StoreField: r2->field_f = r16
    //     0xc457b0: stur            w16, [x2, #0xf]
    // 0xc457b4: ldr             x0, [fp, #0x10]
    // 0xc457b8: LoadField: r1 = r0->field_7
    //     0xc457b8: ldur            w1, [x0, #7]
    // 0xc457bc: DecompressPointer r1
    //     0xc457bc: add             x1, x1, HEAP, lsl #32
    // 0xc457c0: StoreField: r2->field_13 = r1
    //     0xc457c0: stur            w1, [x2, #0x13]
    // 0xc457c4: r16 = ", title: "
    //     0xc457c4: add             x16, PP, #0x51, lsl #12  ; [pp+0x51348] ", title: "
    //     0xc457c8: ldr             x16, [x16, #0x348]
    // 0xc457cc: ArrayStore: r2[0] = r16  ; List_4
    //     0xc457cc: stur            w16, [x2, #0x17]
    // 0xc457d0: LoadField: r1 = r0->field_b
    //     0xc457d0: ldur            w1, [x0, #0xb]
    // 0xc457d4: DecompressPointer r1
    //     0xc457d4: add             x1, x1, HEAP, lsl #32
    // 0xc457d8: StoreField: r2->field_1b = r1
    //     0xc457d8: stur            w1, [x2, #0x1b]
    // 0xc457dc: r16 = ", author: "
    //     0xc457dc: add             x16, PP, #0x51, lsl #12  ; [pp+0x51350] ", author: "
    //     0xc457e0: ldr             x16, [x16, #0x350]
    // 0xc457e4: StoreField: r2->field_1f = r16
    //     0xc457e4: stur            w16, [x2, #0x1f]
    // 0xc457e8: LoadField: r1 = r0->field_f
    //     0xc457e8: ldur            w1, [x0, #0xf]
    // 0xc457ec: DecompressPointer r1
    //     0xc457ec: add             x1, x1, HEAP, lsl #32
    // 0xc457f0: StoreField: r2->field_23 = r1
    //     0xc457f0: stur            w1, [x2, #0x23]
    // 0xc457f4: r16 = ", duration: "
    //     0xc457f4: add             x16, PP, #0x51, lsl #12  ; [pp+0x51358] ", duration: "
    //     0xc457f8: ldr             x16, [x16, #0x358]
    // 0xc457fc: StoreField: r2->field_27 = r16
    //     0xc457fc: stur            w16, [x2, #0x27]
    // 0xc45800: LoadField: r1 = r0->field_13
    //     0xc45800: ldur            w1, [x0, #0x13]
    // 0xc45804: DecompressPointer r1
    //     0xc45804: add             x1, x1, HEAP, lsl #32
    // 0xc45808: LoadField: r0 = r1->field_7
    //     0xc45808: ldur            x0, [x1, #7]
    // 0xc4580c: r1 = 1000000
    //     0xc4580c: movz            x1, #0x4240
    //     0xc45810: movk            x1, #0xf, lsl #16
    // 0xc45814: sdiv            x3, x0, x1
    // 0xc45818: r0 = BoxInt64Instr(r3)
    //     0xc45818: sbfiz           x0, x3, #1, #0x1f
    //     0xc4581c: cmp             x3, x0, asr #1
    //     0xc45820: b.eq            #0xc4582c
    //     0xc45824: bl              #0xec2584  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc45828: stur            x3, [x0, #7]
    // 0xc4582c: mov             x1, x2
    // 0xc45830: ArrayStore: r1[7] = r0  ; List_4
    //     0xc45830: add             x25, x1, #0x2b
    //     0xc45834: str             w0, [x25]
    //     0xc45838: tbz             w0, #0, #0xc45854
    //     0xc4583c: ldurb           w16, [x1, #-1]
    //     0xc45840: ldurb           w17, [x0, #-1]
    //     0xc45844: and             x16, x17, x16, lsr #2
    //     0xc45848: tst             x16, HEAP, lsr #32
    //     0xc4584c: b.eq            #0xc45854
    //     0xc45850: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc45854: r16 = " sec.)"
    //     0xc45854: add             x16, PP, #0x51, lsl #12  ; [pp+0x51360] " sec.)"
    //     0xc45858: ldr             x16, [x16, #0x360]
    // 0xc4585c: StoreField: r2->field_2f = r16
    //     0xc4585c: stur            w16, [x2, #0x2f]
    // 0xc45860: str             x2, [SP]
    // 0xc45864: r0 = _interpolate()
    //     0xc45864: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc45868: LeaveFrame
    //     0xc45868: mov             SP, fp
    //     0xc4586c: ldp             fp, lr, [SP], #0x10
    // 0xc45870: ret
    //     0xc45870: ret             
    // 0xc45874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc45874: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc45878: b               #0xc45798
  }
}
