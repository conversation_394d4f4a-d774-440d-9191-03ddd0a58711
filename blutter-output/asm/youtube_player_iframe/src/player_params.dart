// lib: , url: package:youtube_player_iframe/src/player_params.dart

// class id: 1051360, size: 0x8
class :: {
}

// class id: 177, size: 0x48, field offset: 0x8
//   const constructor, 
class YoutubePlayerParams extends Object {

  String toJson(YoutubePlayerParams) {
    // ** addr: 0x97d474, size: 0x48
    // 0x97d474: EnterFrame
    //     0x97d474: stp             fp, lr, [SP, #-0x10]!
    //     0x97d478: mov             fp, SP
    // 0x97d47c: CheckStackOverflow
    //     0x97d47c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97d480: cmp             SP, x16
    //     0x97d484: b.ls            #0x97d49c
    // 0x97d488: ldr             x1, [fp, #0x10]
    // 0x97d48c: r0 = toJson()
    //     0x97d48c: bl              #0x97d4a4  ; [package:youtube_player_iframe/src/player_params.dart] YoutubePlayerParams::toJson
    // 0x97d490: LeaveFrame
    //     0x97d490: mov             SP, fp
    //     0x97d494: ldp             fp, lr, [SP], #0x10
    // 0x97d498: ret
    //     0x97d498: ret             
    // 0x97d49c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97d49c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97d4a0: b               #0x97d488
  }
  String toJson(YoutubePlayerParams) {
    // ** addr: 0x97d4a4, size: 0x38
    // 0x97d4a4: EnterFrame
    //     0x97d4a4: stp             fp, lr, [SP, #-0x10]!
    //     0x97d4a8: mov             fp, SP
    // 0x97d4ac: CheckStackOverflow
    //     0x97d4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97d4b0: cmp             SP, x16
    //     0x97d4b4: b.ls            #0x97d4d4
    // 0x97d4b8: r0 = toMap()
    //     0x97d4b8: bl              #0x97d4dc  ; [package:youtube_player_iframe/src/player_params.dart] YoutubePlayerParams::toMap
    // 0x97d4bc: mov             x1, x0
    // 0x97d4c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x97d4c0: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x97d4c4: r0 = jsonEncode()
    //     0x97d4c4: bl              #0x727b0c  ; [dart:convert] ::jsonEncode
    // 0x97d4c8: LeaveFrame
    //     0x97d4c8: mov             SP, fp
    //     0x97d4cc: ldp             fp, lr, [SP], #0x10
    // 0x97d4d0: ret
    //     0x97d4d0: ret             
    // 0x97d4d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97d4d4: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97d4d8: b               #0x97d4b8
  }
  _ toMap(/* No info */) {
    // ** addr: 0x97d4dc, size: 0x1fc
    // 0x97d4dc: EnterFrame
    //     0x97d4dc: stp             fp, lr, [SP, #-0x10]!
    //     0x97d4e0: mov             fp, SP
    // 0x97d4e4: AllocStack(0x20)
    //     0x97d4e4: sub             SP, SP, #0x20
    // 0x97d4e8: SetupParameters(YoutubePlayerParams this /* r1 => r1, fp-0x8 */)
    //     0x97d4e8: stur            x1, [fp, #-8]
    // 0x97d4ec: CheckStackOverflow
    //     0x97d4ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97d4f0: cmp             SP, x16
    //     0x97d4f4: b.ls            #0x97d6d0
    // 0x97d4f8: r16 = <String, dynamic>
    //     0x97d4f8: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x97d4fc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x97d500: stp             lr, x16, [SP]
    // 0x97d504: r0 = Map._fromLiteral()
    //     0x97d504: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x97d508: mov             x1, x0
    // 0x97d50c: r2 = "autoplay"
    //     0x97d50c: add             x2, PP, #0x51, lsl #12  ; [pp+0x512c8] "autoplay"
    //     0x97d510: ldr             x2, [x2, #0x2c8]
    // 0x97d514: r3 = 2
    //     0x97d514: movz            x3, #0x2
    // 0x97d518: stur            x0, [fp, #-0x10]
    // 0x97d51c: r0 = []=()
    //     0x97d51c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d520: ldur            x1, [fp, #-0x10]
    // 0x97d524: r2 = "mute"
    //     0x97d524: add             x2, PP, #0x51, lsl #12  ; [pp+0x512d0] "mute"
    //     0x97d528: ldr             x2, [x2, #0x2d0]
    // 0x97d52c: r3 = 0
    //     0x97d52c: movz            x3, #0
    // 0x97d530: r0 = []=()
    //     0x97d530: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d534: ldur            x1, [fp, #-0x10]
    // 0x97d538: r2 = "cc_lang_pref"
    //     0x97d538: add             x2, PP, #0x51, lsl #12  ; [pp+0x512d8] "cc_lang_pref"
    //     0x97d53c: ldr             x2, [x2, #0x2d8]
    // 0x97d540: r3 = "en"
    //     0x97d540: add             x3, PP, #8, lsl #12  ; [pp+0x8e60] "en"
    //     0x97d544: ldr             x3, [x3, #0xe60]
    // 0x97d548: r0 = []=()
    //     0x97d548: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d54c: ldur            x1, [fp, #-0x10]
    // 0x97d550: r2 = "cc_load_policy"
    //     0x97d550: add             x2, PP, #0x51, lsl #12  ; [pp+0x512e0] "cc_load_policy"
    //     0x97d554: ldr             x2, [x2, #0x2e0]
    // 0x97d558: r3 = 2
    //     0x97d558: movz            x3, #0x2
    // 0x97d55c: r0 = []=()
    //     0x97d55c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d560: ldur            x1, [fp, #-0x10]
    // 0x97d564: r2 = "color"
    //     0x97d564: ldr             x2, [PP, #0x4720]  ; [pp+0x4720] "color"
    // 0x97d568: r3 = "white"
    //     0x97d568: add             x3, PP, #0x48, lsl #12  ; [pp+0x48f98] "white"
    //     0x97d56c: ldr             x3, [x3, #0xf98]
    // 0x97d570: r0 = []=()
    //     0x97d570: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d574: ldur            x1, [fp, #-0x10]
    // 0x97d578: r2 = "controls"
    //     0x97d578: add             x2, PP, #0x51, lsl #12  ; [pp+0x512e8] "controls"
    //     0x97d57c: ldr             x2, [x2, #0x2e8]
    // 0x97d580: r3 = 2
    //     0x97d580: movz            x3, #0x2
    // 0x97d584: r0 = []=()
    //     0x97d584: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d588: ldur            x1, [fp, #-0x10]
    // 0x97d58c: r2 = "disablekb"
    //     0x97d58c: add             x2, PP, #0x51, lsl #12  ; [pp+0x512f0] "disablekb"
    //     0x97d590: ldr             x2, [x2, #0x2f0]
    // 0x97d594: r3 = 2
    //     0x97d594: movz            x3, #0x2
    // 0x97d598: r0 = []=()
    //     0x97d598: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d59c: ldur            x1, [fp, #-0x10]
    // 0x97d5a0: r2 = "enablejsapi"
    //     0x97d5a0: add             x2, PP, #0x51, lsl #12  ; [pp+0x512f8] "enablejsapi"
    //     0x97d5a4: ldr             x2, [x2, #0x2f8]
    // 0x97d5a8: r3 = 2
    //     0x97d5a8: movz            x3, #0x2
    // 0x97d5ac: r0 = []=()
    //     0x97d5ac: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d5b0: ldur            x0, [fp, #-8]
    // 0x97d5b4: LoadField: r1 = r0->field_27
    //     0x97d5b4: ldur            w1, [x0, #0x27]
    // 0x97d5b8: DecompressPointer r1
    //     0x97d5b8: add             x1, x1, HEAP, lsl #32
    // 0x97d5bc: tst             x1, #0x10
    // 0x97d5c0: cset            x3, eq
    // 0x97d5c4: lsl             x3, x3, #1
    // 0x97d5c8: ldur            x1, [fp, #-0x10]
    // 0x97d5cc: r2 = "fs"
    //     0x97d5cc: add             x2, PP, #0x51, lsl #12  ; [pp+0x51300] "fs"
    //     0x97d5d0: ldr             x2, [x2, #0x300]
    // 0x97d5d4: r0 = []=()
    //     0x97d5d4: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d5d8: ldur            x1, [fp, #-0x10]
    // 0x97d5dc: r2 = "hl"
    //     0x97d5dc: add             x2, PP, #0x51, lsl #12  ; [pp+0x51308] "hl"
    //     0x97d5e0: ldr             x2, [x2, #0x308]
    // 0x97d5e4: r3 = "en"
    //     0x97d5e4: add             x3, PP, #8, lsl #12  ; [pp+0x8e60] "en"
    //     0x97d5e8: ldr             x3, [x3, #0xe60]
    // 0x97d5ec: r0 = []=()
    //     0x97d5ec: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d5f0: ldur            x1, [fp, #-0x10]
    // 0x97d5f4: r2 = "iv_load_policy"
    //     0x97d5f4: add             x2, PP, #0x51, lsl #12  ; [pp+0x51310] "iv_load_policy"
    //     0x97d5f8: ldr             x2, [x2, #0x310]
    // 0x97d5fc: r3 = 2
    //     0x97d5fc: movz            x3, #0x2
    // 0x97d600: r0 = []=()
    //     0x97d600: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d604: ldur            x1, [fp, #-0x10]
    // 0x97d608: r2 = "loop"
    //     0x97d608: add             x2, PP, #0x51, lsl #12  ; [pp+0x51318] "loop"
    //     0x97d60c: ldr             x2, [x2, #0x318]
    // 0x97d610: r3 = 0
    //     0x97d610: movz            x3, #0
    // 0x97d614: r0 = []=()
    //     0x97d614: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d618: ldur            x1, [fp, #-0x10]
    // 0x97d61c: r2 = "modestbranding"
    //     0x97d61c: add             x2, PP, #0x51, lsl #12  ; [pp+0x51320] "modestbranding"
    //     0x97d620: ldr             x2, [x2, #0x320]
    // 0x97d624: r3 = "1"
    //     0x97d624: add             x3, PP, #0x18, lsl #12  ; [pp+0x18718] "1"
    //     0x97d628: ldr             x3, [x3, #0x718]
    // 0x97d62c: r0 = []=()
    //     0x97d62c: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d630: r1 = Null
    //     0x97d630: mov             x1, NULL
    // 0x97d634: r2 = 8
    //     0x97d634: movz            x2, #0x8
    // 0x97d638: r0 = AllocateArray()
    //     0x97d638: bl              #0xec22fc  ; AllocateArrayStub
    // 0x97d63c: r16 = "origin"
    //     0x97d63c: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3b220] "origin"
    //     0x97d640: ldr             x16, [x16, #0x220]
    // 0x97d644: StoreField: r0->field_f = r16
    //     0x97d644: stur            w16, [x0, #0xf]
    // 0x97d648: ldur            x1, [fp, #-8]
    // 0x97d64c: LoadField: r2 = r1->field_37
    //     0x97d64c: ldur            w2, [x1, #0x37]
    // 0x97d650: DecompressPointer r2
    //     0x97d650: add             x2, x2, HEAP, lsl #32
    // 0x97d654: StoreField: r0->field_13 = r2
    //     0x97d654: stur            w2, [x0, #0x13]
    // 0x97d658: r16 = "widget_referrer"
    //     0x97d658: add             x16, PP, #0x51, lsl #12  ; [pp+0x51328] "widget_referrer"
    //     0x97d65c: ldr             x16, [x16, #0x328]
    // 0x97d660: ArrayStore: r0[0] = r16  ; List_4
    //     0x97d660: stur            w16, [x0, #0x17]
    // 0x97d664: StoreField: r0->field_1b = r2
    //     0x97d664: stur            w2, [x0, #0x1b]
    // 0x97d668: r16 = <String, dynamic>
    //     0x97d668: ldr             x16, [PP, #0x1f0]  ; [pp+0x1f0] TypeArguments: <String, dynamic>
    // 0x97d66c: stp             x0, x16, [SP]
    // 0x97d670: r0 = Map._fromLiteral()
    //     0x97d670: bl              #0x5fafec  ; [dart:core] Map::Map._fromLiteral
    // 0x97d674: ldur            x1, [fp, #-0x10]
    // 0x97d678: mov             x2, x0
    // 0x97d67c: r0 = addAll()
    //     0x97d67c: bl              #0xd6cf24  ; [dart:_compact_hash] _Map::addAll
    // 0x97d680: ldur            x1, [fp, #-0x10]
    // 0x97d684: r2 = "playsinline"
    //     0x97d684: add             x2, PP, #0x51, lsl #12  ; [pp+0x51330] "playsinline"
    //     0x97d688: ldr             x2, [x2, #0x330]
    // 0x97d68c: r3 = 2
    //     0x97d68c: movz            x3, #0x2
    // 0x97d690: r0 = []=()
    //     0x97d690: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d694: ldur            x0, [fp, #-8]
    // 0x97d698: LoadField: r1 = r0->field_3f
    //     0x97d698: ldur            w1, [x0, #0x3f]
    // 0x97d69c: DecompressPointer r1
    //     0x97d69c: add             x1, x1, HEAP, lsl #32
    // 0x97d6a0: eor             x0, x1, #0x10
    // 0x97d6a4: tst             x0, #0x10
    // 0x97d6a8: cset            x3, eq
    // 0x97d6ac: lsl             x3, x3, #1
    // 0x97d6b0: ldur            x1, [fp, #-0x10]
    // 0x97d6b4: r2 = "rel"
    //     0x97d6b4: add             x2, PP, #0x51, lsl #12  ; [pp+0x51338] "rel"
    //     0x97d6b8: ldr             x2, [x2, #0x338]
    // 0x97d6bc: r0 = []=()
    //     0x97d6bc: bl              #0xd825c4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x97d6c0: ldur            x0, [fp, #-0x10]
    // 0x97d6c4: LeaveFrame
    //     0x97d6c4: mov             SP, fp
    //     0x97d6c8: ldp             fp, lr, [SP], #0x10
    // 0x97d6cc: ret
    //     0x97d6cc: ret             
    // 0x97d6d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97d6d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97d6d4: b               #0x97d4f8
  }
}

// class id: 6728, size: 0x18, field offset: 0x14
enum PointerEvents extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
  _OneByteString field_14;

  _ _enumToString(/* No info */) {
    // ** addr: 0xc4fdc8, size: 0x64
    // 0xc4fdc8: EnterFrame
    //     0xc4fdc8: stp             fp, lr, [SP, #-0x10]!
    //     0xc4fdcc: mov             fp, SP
    // 0xc4fdd0: AllocStack(0x10)
    //     0xc4fdd0: sub             SP, SP, #0x10
    // 0xc4fdd4: SetupParameters(PointerEvents this /* r1 => r0, fp-0x8 */)
    //     0xc4fdd4: mov             x0, x1
    //     0xc4fdd8: stur            x1, [fp, #-8]
    // 0xc4fddc: CheckStackOverflow
    //     0xc4fddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4fde0: cmp             SP, x16
    //     0xc4fde4: b.ls            #0xc4fe24
    // 0xc4fde8: r1 = Null
    //     0xc4fde8: mov             x1, NULL
    // 0xc4fdec: r2 = 4
    //     0xc4fdec: movz            x2, #0x4
    // 0xc4fdf0: r0 = AllocateArray()
    //     0xc4fdf0: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc4fdf4: r16 = "PointerEvents."
    //     0xc4fdf4: add             x16, PP, #0x51, lsl #12  ; [pp+0x512c0] "PointerEvents."
    //     0xc4fdf8: ldr             x16, [x16, #0x2c0]
    // 0xc4fdfc: StoreField: r0->field_f = r16
    //     0xc4fdfc: stur            w16, [x0, #0xf]
    // 0xc4fe00: ldur            x1, [fp, #-8]
    // 0xc4fe04: LoadField: r2 = r1->field_f
    //     0xc4fe04: ldur            w2, [x1, #0xf]
    // 0xc4fe08: DecompressPointer r2
    //     0xc4fe08: add             x2, x2, HEAP, lsl #32
    // 0xc4fe0c: StoreField: r0->field_13 = r2
    //     0xc4fe0c: stur            w2, [x0, #0x13]
    // 0xc4fe10: str             x0, [SP]
    // 0xc4fe14: r0 = _interpolate()
    //     0xc4fe14: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc4fe18: LeaveFrame
    //     0xc4fe18: mov             SP, fp
    //     0xc4fe1c: ldp             fp, lr, [SP], #0x10
    // 0xc4fe20: ret
    //     0xc4fe20: ret             
    // 0xc4fe24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc4fe24: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc4fe28: b               #0xc4fde8
  }
}
