// lib: , url: package:youtube_player_iframe/src/player_value.dart

// class id: 1051361, size: 0x8
class :: {
}

// class id: 175, size: 0x10, field offset: 0x8
//   const constructor, 
class FullScreenOption extends Object {

  bool field_8;
  bool field_c;

  get _ hashCode(/* No info */) {
    // ** addr: 0xbf4660, size: 0x58
    // 0xbf4660: ldr             x1, [SP]
    // 0xbf4664: LoadField: r2 = r1->field_7
    //     0xbf4664: ldur            w2, [x1, #7]
    // 0xbf4668: DecompressPointer r2
    //     0xbf4668: add             x2, x2, HEAP, lsl #32
    // 0xbf466c: tst             x2, #0x10
    // 0xbf4670: cset            x3, ne
    // 0xbf4674: sub             x3, x3, #1
    // 0xbf4678: r16 = -12
    //     0xbf4678: movn            x16, #0xb
    // 0xbf467c: and             x3, x3, x16
    // 0xbf4680: add             x3, x3, #0x9aa
    // 0xbf4684: LoadField: r2 = r1->field_b
    //     0xbf4684: ldur            w2, [x1, #0xb]
    // 0xbf4688: DecompressPointer r2
    //     0xbf4688: add             x2, x2, HEAP, lsl #32
    // 0xbf468c: tst             x2, #0x10
    // 0xbf4690: cset            x1, ne
    // 0xbf4694: sub             x1, x1, #1
    // 0xbf4698: r16 = -12
    //     0xbf4698: movn            x16, #0xb
    // 0xbf469c: and             x1, x1, x16
    // 0xbf46a0: add             x1, x1, #0x9aa
    // 0xbf46a4: r2 = LoadInt32Instr(r3)
    //     0xbf46a4: sbfx            x2, x3, #1, #0x1f
    // 0xbf46a8: r3 = LoadInt32Instr(r1)
    //     0xbf46a8: sbfx            x3, x1, #1, #0x1f
    // 0xbf46ac: eor             x1, x2, x3
    // 0xbf46b0: lsl             x0, x1, #1
    // 0xbf46b4: ret
    //     0xbf46b4: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xd8098c, size: 0xdc
    // 0xd8098c: EnterFrame
    //     0xd8098c: stp             fp, lr, [SP, #-0x10]!
    //     0xd80990: mov             fp, SP
    // 0xd80994: AllocStack(0x10)
    //     0xd80994: sub             SP, SP, #0x10
    // 0xd80998: CheckStackOverflow
    //     0xd80998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8099c: cmp             SP, x16
    //     0xd809a0: b.ls            #0xd80a60
    // 0xd809a4: ldr             x0, [fp, #0x10]
    // 0xd809a8: cmp             w0, NULL
    // 0xd809ac: b.ne            #0xd809c0
    // 0xd809b0: r0 = false
    //     0xd809b0: add             x0, NULL, #0x30  ; false
    // 0xd809b4: LeaveFrame
    //     0xd809b4: mov             SP, fp
    //     0xd809b8: ldp             fp, lr, [SP], #0x10
    // 0xd809bc: ret
    //     0xd809bc: ret             
    // 0xd809c0: ldr             x1, [fp, #0x18]
    // 0xd809c4: cmp             w1, w0
    // 0xd809c8: b.ne            #0xd809d4
    // 0xd809cc: r0 = true
    //     0xd809cc: add             x0, NULL, #0x20  ; true
    // 0xd809d0: b               #0xd80a54
    // 0xd809d4: r2 = 60
    //     0xd809d4: movz            x2, #0x3c
    // 0xd809d8: branchIfSmi(r0, 0xd809e4)
    //     0xd809d8: tbz             w0, #0, #0xd809e4
    // 0xd809dc: r2 = LoadClassIdInstr(r0)
    //     0xd809dc: ldur            x2, [x0, #-1]
    //     0xd809e0: ubfx            x2, x2, #0xc, #0x14
    // 0xd809e4: cmp             x2, #0xaf
    // 0xd809e8: b.ne            #0xd80a50
    // 0xd809ec: r16 = FullScreenOption
    //     0xd809ec: add             x16, PP, #0x51, lsl #12  ; [pp+0x51280] Type: FullScreenOption
    //     0xd809f0: ldr             x16, [x16, #0x280]
    // 0xd809f4: r30 = FullScreenOption
    //     0xd809f4: add             lr, PP, #0x51, lsl #12  ; [pp+0x51280] Type: FullScreenOption
    //     0xd809f8: ldr             lr, [lr, #0x280]
    // 0xd809fc: stp             lr, x16, [SP]
    // 0xd80a00: r0 = ==()
    //     0xd80a00: bl              #0xd819f8  ; [dart:core] _Type::==
    // 0xd80a04: tbnz            w0, #4, #0xd80a50
    // 0xd80a08: ldr             x2, [fp, #0x18]
    // 0xd80a0c: ldr             x1, [fp, #0x10]
    // 0xd80a10: LoadField: r3 = r2->field_7
    //     0xd80a10: ldur            w3, [x2, #7]
    // 0xd80a14: DecompressPointer r3
    //     0xd80a14: add             x3, x3, HEAP, lsl #32
    // 0xd80a18: LoadField: r4 = r1->field_7
    //     0xd80a18: ldur            w4, [x1, #7]
    // 0xd80a1c: DecompressPointer r4
    //     0xd80a1c: add             x4, x4, HEAP, lsl #32
    // 0xd80a20: cmp             w3, w4
    // 0xd80a24: b.ne            #0xd80a50
    // 0xd80a28: LoadField: r3 = r2->field_b
    //     0xd80a28: ldur            w3, [x2, #0xb]
    // 0xd80a2c: DecompressPointer r3
    //     0xd80a2c: add             x3, x3, HEAP, lsl #32
    // 0xd80a30: LoadField: r2 = r1->field_b
    //     0xd80a30: ldur            w2, [x1, #0xb]
    // 0xd80a34: DecompressPointer r2
    //     0xd80a34: add             x2, x2, HEAP, lsl #32
    // 0xd80a38: cmp             w3, w2
    // 0xd80a3c: r16 = true
    //     0xd80a3c: add             x16, NULL, #0x20  ; true
    // 0xd80a40: r17 = false
    //     0xd80a40: add             x17, NULL, #0x30  ; false
    // 0xd80a44: csel            x1, x16, x17, eq
    // 0xd80a48: mov             x0, x1
    // 0xd80a4c: b               #0xd80a54
    // 0xd80a50: r0 = false
    //     0xd80a50: add             x0, NULL, #0x30  ; false
    // 0xd80a54: LeaveFrame
    //     0xd80a54: mov             SP, fp
    //     0xd80a58: ldp             fp, lr, [SP], #0x10
    // 0xd80a5c: ret
    //     0xd80a5c: ret             
    // 0xd80a60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd80a60: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd80a64: b               #0xd809a4
  }
}

// class id: 176, size: 0x24, field offset: 0x8
class YoutubePlayerValue extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xc4587c, size: 0x1f4
    // 0xc4587c: EnterFrame
    //     0xc4587c: stp             fp, lr, [SP, #-0x10]!
    //     0xc45880: mov             fp, SP
    // 0xc45884: AllocStack(0x10)
    //     0xc45884: sub             SP, SP, #0x10
    // 0xc45888: CheckStackOverflow
    //     0xc45888: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc4588c: cmp             SP, x16
    //     0xc45890: b.ls            #0xc45a50
    // 0xc45894: r1 = Null
    //     0xc45894: mov             x1, NULL
    // 0xc45898: r2 = 28
    //     0xc45898: movz            x2, #0x1c
    // 0xc4589c: r0 = AllocateArray()
    //     0xc4589c: bl              #0xec22fc  ; AllocateArrayStub
    // 0xc458a0: stur            x0, [fp, #-8]
    // 0xc458a4: r16 = YoutubePlayerValue
    //     0xc458a4: add             x16, PP, #0x51, lsl #12  ; [pp+0x51288] Type: YoutubePlayerValue
    //     0xc458a8: ldr             x16, [x16, #0x288]
    // 0xc458ac: StoreField: r0->field_f = r16
    //     0xc458ac: stur            w16, [x0, #0xf]
    // 0xc458b0: r16 = "(metaData: "
    //     0xc458b0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51290] "(metaData: "
    //     0xc458b4: ldr             x16, [x16, #0x290]
    // 0xc458b8: StoreField: r0->field_13 = r16
    //     0xc458b8: stur            w16, [x0, #0x13]
    // 0xc458bc: ldr             x1, [fp, #0x10]
    // 0xc458c0: LoadField: r2 = r1->field_1f
    //     0xc458c0: ldur            w2, [x1, #0x1f]
    // 0xc458c4: DecompressPointer r2
    //     0xc458c4: add             x2, x2, HEAP, lsl #32
    // 0xc458c8: str             x2, [SP]
    // 0xc458cc: r0 = toString()
    //     0xc458cc: bl              #0xc45780  ; [package:youtube_player_iframe/src/meta_data.dart] YoutubeMetaData::toString
    // 0xc458d0: ldur            x1, [fp, #-8]
    // 0xc458d4: ArrayStore: r1[2] = r0  ; List_4
    //     0xc458d4: add             x25, x1, #0x17
    //     0xc458d8: str             w0, [x25]
    //     0xc458dc: tbz             w0, #0, #0xc458f8
    //     0xc458e0: ldurb           w16, [x1, #-1]
    //     0xc458e4: ldurb           w17, [x0, #-1]
    //     0xc458e8: and             x16, x17, x16, lsr #2
    //     0xc458ec: tst             x16, HEAP, lsr #32
    //     0xc458f0: b.eq            #0xc458f8
    //     0xc458f4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc458f8: ldur            x2, [fp, #-8]
    // 0xc458fc: r16 = ", playerState: "
    //     0xc458fc: add             x16, PP, #0x51, lsl #12  ; [pp+0x51298] ", playerState: "
    //     0xc45900: ldr             x16, [x16, #0x298]
    // 0xc45904: StoreField: r2->field_1b = r16
    //     0xc45904: stur            w16, [x2, #0x1b]
    // 0xc45908: ldr             x3, [fp, #0x10]
    // 0xc4590c: LoadField: r0 = r3->field_b
    //     0xc4590c: ldur            w0, [x3, #0xb]
    // 0xc45910: DecompressPointer r0
    //     0xc45910: add             x0, x0, HEAP, lsl #32
    // 0xc45914: mov             x1, x2
    // 0xc45918: ArrayStore: r1[4] = r0  ; List_4
    //     0xc45918: add             x25, x1, #0x1f
    //     0xc4591c: str             w0, [x25]
    //     0xc45920: tbz             w0, #0, #0xc4593c
    //     0xc45924: ldurb           w16, [x1, #-1]
    //     0xc45928: ldurb           w17, [x0, #-1]
    //     0xc4592c: and             x16, x17, x16, lsr #2
    //     0xc45930: tst             x16, HEAP, lsr #32
    //     0xc45934: b.eq            #0xc4593c
    //     0xc45938: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc4593c: r16 = ", playbackRate: "
    //     0xc4593c: add             x16, PP, #0x51, lsl #12  ; [pp+0x512a0] ", playbackRate: "
    //     0xc45940: ldr             x16, [x16, #0x2a0]
    // 0xc45944: StoreField: r2->field_23 = r16
    //     0xc45944: stur            w16, [x2, #0x23]
    // 0xc45948: LoadField: d0 = r3->field_f
    //     0xc45948: ldur            d0, [x3, #0xf]
    // 0xc4594c: r0 = inline_Allocate_Double()
    //     0xc4594c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc45950: add             x0, x0, #0x10
    //     0xc45954: cmp             x1, x0
    //     0xc45958: b.ls            #0xc45a58
    //     0xc4595c: str             x0, [THR, #0x50]  ; THR::top
    //     0xc45960: sub             x0, x0, #0xf
    //     0xc45964: movz            x1, #0xe15c
    //     0xc45968: movk            x1, #0x3, lsl #16
    //     0xc4596c: stur            x1, [x0, #-1]
    // 0xc45970: StoreField: r0->field_7 = d0
    //     0xc45970: stur            d0, [x0, #7]
    // 0xc45974: mov             x1, x2
    // 0xc45978: ArrayStore: r1[6] = r0  ; List_4
    //     0xc45978: add             x25, x1, #0x27
    //     0xc4597c: str             w0, [x25]
    //     0xc45980: tbz             w0, #0, #0xc4599c
    //     0xc45984: ldurb           w16, [x1, #-1]
    //     0xc45988: ldurb           w17, [x0, #-1]
    //     0xc4598c: and             x16, x17, x16, lsr #2
    //     0xc45990: tst             x16, HEAP, lsr #32
    //     0xc45994: b.eq            #0xc4599c
    //     0xc45998: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc4599c: r16 = ", playbackQuality: "
    //     0xc4599c: add             x16, PP, #0x51, lsl #12  ; [pp+0x512a8] ", playbackQuality: "
    //     0xc459a0: ldr             x16, [x16, #0x2a8]
    // 0xc459a4: StoreField: r2->field_2b = r16
    //     0xc459a4: stur            w16, [x2, #0x2b]
    // 0xc459a8: LoadField: r0 = r3->field_1b
    //     0xc459a8: ldur            w0, [x3, #0x1b]
    // 0xc459ac: DecompressPointer r0
    //     0xc459ac: add             x0, x0, HEAP, lsl #32
    // 0xc459b0: mov             x1, x2
    // 0xc459b4: ArrayStore: r1[8] = r0  ; List_4
    //     0xc459b4: add             x25, x1, #0x2f
    //     0xc459b8: str             w0, [x25]
    //     0xc459bc: tbz             w0, #0, #0xc459d8
    //     0xc459c0: ldurb           w16, [x1, #-1]
    //     0xc459c4: ldurb           w17, [x0, #-1]
    //     0xc459c8: and             x16, x17, x16, lsr #2
    //     0xc459cc: tst             x16, HEAP, lsr #32
    //     0xc459d0: b.eq            #0xc459d8
    //     0xc459d4: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc459d8: r16 = ", isFullScreen: "
    //     0xc459d8: add             x16, PP, #0x51, lsl #12  ; [pp+0x512b0] ", isFullScreen: "
    //     0xc459dc: ldr             x16, [x16, #0x2b0]
    // 0xc459e0: StoreField: r2->field_33 = r16
    //     0xc459e0: stur            w16, [x2, #0x33]
    // 0xc459e4: LoadField: r0 = r3->field_7
    //     0xc459e4: ldur            w0, [x3, #7]
    // 0xc459e8: DecompressPointer r0
    //     0xc459e8: add             x0, x0, HEAP, lsl #32
    // 0xc459ec: LoadField: r1 = r0->field_7
    //     0xc459ec: ldur            w1, [x0, #7]
    // 0xc459f0: DecompressPointer r1
    //     0xc459f0: add             x1, x1, HEAP, lsl #32
    // 0xc459f4: StoreField: r2->field_37 = r1
    //     0xc459f4: stur            w1, [x2, #0x37]
    // 0xc459f8: r16 = ", error: "
    //     0xc459f8: add             x16, PP, #0x51, lsl #12  ; [pp+0x512b8] ", error: "
    //     0xc459fc: ldr             x16, [x16, #0x2b8]
    // 0xc45a00: StoreField: r2->field_3b = r16
    //     0xc45a00: stur            w16, [x2, #0x3b]
    // 0xc45a04: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xc45a04: ldur            w0, [x3, #0x17]
    // 0xc45a08: DecompressPointer r0
    //     0xc45a08: add             x0, x0, HEAP, lsl #32
    // 0xc45a0c: mov             x1, x2
    // 0xc45a10: ArrayStore: r1[12] = r0  ; List_4
    //     0xc45a10: add             x25, x1, #0x3f
    //     0xc45a14: str             w0, [x25]
    //     0xc45a18: tbz             w0, #0, #0xc45a34
    //     0xc45a1c: ldurb           w16, [x1, #-1]
    //     0xc45a20: ldurb           w17, [x0, #-1]
    //     0xc45a24: and             x16, x17, x16, lsr #2
    //     0xc45a28: tst             x16, HEAP, lsr #32
    //     0xc45a2c: b.eq            #0xc45a34
    //     0xc45a30: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0xc45a34: r16 = ")"
    //     0xc45a34: ldr             x16, [PP, #0x9b8]  ; [pp+0x9b8] ")"
    // 0xc45a38: StoreField: r2->field_43 = r16
    //     0xc45a38: stur            w16, [x2, #0x43]
    // 0xc45a3c: str             x2, [SP]
    // 0xc45a40: r0 = _interpolate()
    //     0xc45a40: bl              #0x662058  ; [dart:core] _StringBase::_interpolate
    // 0xc45a44: LeaveFrame
    //     0xc45a44: mov             SP, fp
    //     0xc45a48: ldp             fp, lr, [SP], #0x10
    // 0xc45a4c: ret
    //     0xc45a4c: ret             
    // 0xc45a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc45a50: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc45a54: b               #0xc45894
    // 0xc45a58: SaveReg d0
    //     0xc45a58: str             q0, [SP, #-0x10]!
    // 0xc45a5c: stp             x2, x3, [SP, #-0x10]!
    // 0xc45a60: r0 = AllocateDouble()
    //     0xc45a60: bl              #0xec2254  ; AllocateDoubleStub
    // 0xc45a64: ldp             x2, x3, [SP], #0x10
    // 0xc45a68: RestoreReg d0
    //     0xc45a68: ldr             q0, [SP], #0x10
    // 0xc45a6c: b               #0xc45970
  }
}
