// lib: , url: package:youtube_player_iframe/src/widgets/youtube_player.dart

// class id: 1051362, size: 0x8
class :: {
}

// class id: 4078, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class __YoutubePlayerState&State&AutomaticKeepAliveClientMixin extends State<dynamic>
     with AutomaticKeepAliveClientMixin<X0 bound StatefulWidget> {

  _ build(/* No info */) {
    // ** addr: 0xa5159c, size: 0x28
    // 0xa5159c: LoadField: r2 = r1->field_b
    //     0xa5159c: ldur            w2, [x1, #0xb]
    // 0xa515a0: DecompressPointer r2
    //     0xa515a0: add             x2, x2, HEAP, lsl #32
    // 0xa515a4: cmp             w2, NULL
    // 0xa515a8: b.eq            #0xa515b8
    // 0xa515ac: r0 = Instance__NullWidget
    //     0xa515ac: add             x0, PP, #0x40, lsl #12  ; [pp+0x407d8] Obj!_NullWidget@e25381
    //     0xa515b0: ldr             x0, [x0, #0x7d8]
    // 0xa515b4: ret
    //     0xa515b4: ret             
    // 0xa515b8: EnterFrame
    //     0xa515b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa515bc: mov             fp, SP
    // 0xa515c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa515c0: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4079, size: 0x1c, field offset: 0x18
class _YoutubePlayerState extends __YoutubePlayerState&State&AutomaticKeepAliveClientMixin {

  late final YoutubePlayerController _controller; // offset: 0x18

  _ initState(/* No info */) {
    // ** addr: 0x981a54, size: 0xb0
    // 0x981a54: EnterFrame
    //     0x981a54: stp             fp, lr, [SP, #-0x10]!
    //     0x981a58: mov             fp, SP
    // 0x981a5c: AllocStack(0x18)
    //     0x981a5c: sub             SP, SP, #0x18
    // 0x981a60: SetupParameters(_YoutubePlayerState this /* r1 => r0, fp-0x8 */)
    //     0x981a60: mov             x0, x1
    //     0x981a64: stur            x1, [fp, #-8]
    // 0x981a68: CheckStackOverflow
    //     0x981a68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981a6c: cmp             SP, x16
    //     0x981a70: b.ls            #0x981af8
    // 0x981a74: mov             x1, x0
    // 0x981a78: r0 = dispose()
    //     0x981a78: bl              #0xa81ec8  ; [package:flutter/src/widgets/will_pop_scope.dart] _WillPopScopeState::dispose
    // 0x981a7c: ldur            x1, [fp, #-8]
    // 0x981a80: LoadField: r0 = r1->field_b
    //     0x981a80: ldur            w0, [x1, #0xb]
    // 0x981a84: DecompressPointer r0
    //     0x981a84: add             x0, x0, HEAP, lsl #32
    // 0x981a88: cmp             w0, NULL
    // 0x981a8c: b.eq            #0x981b00
    // 0x981a90: LoadField: r2 = r0->field_b
    //     0x981a90: ldur            w2, [x0, #0xb]
    // 0x981a94: DecompressPointer r2
    //     0x981a94: add             x2, x2, HEAP, lsl #32
    // 0x981a98: stur            x2, [fp, #-0x10]
    // 0x981a9c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x981a9c: ldur            w0, [x1, #0x17]
    // 0x981aa0: DecompressPointer r0
    //     0x981aa0: add             x0, x0, HEAP, lsl #32
    // 0x981aa4: r16 = Sentinel
    //     0x981aa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x981aa8: cmp             w0, w16
    // 0x981aac: b.eq            #0x981ac4
    // 0x981ab0: r16 = "_controller@2483033003"
    //     0x981ab0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ad8] "_controller@2483033003"
    //     0x981ab4: ldr             x16, [x16, #0xad8]
    // 0x981ab8: str             x16, [SP]
    // 0x981abc: r0 = _throwFieldAlreadyInitialized()
    //     0x981abc: bl              #0x6416b4  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x981ac0: ldur            x1, [fp, #-8]
    // 0x981ac4: ldur            x0, [fp, #-0x10]
    // 0x981ac8: ArrayStore: r1[0] = r0  ; List_4
    //     0x981ac8: stur            w0, [x1, #0x17]
    //     0x981acc: ldurb           w16, [x1, #-1]
    //     0x981ad0: ldurb           w17, [x0, #-1]
    //     0x981ad4: and             x16, x17, x16, lsr #2
    //     0x981ad8: tst             x16, HEAP, lsr #32
    //     0x981adc: b.eq            #0x981ae4
    //     0x981ae0: bl              #0xec0a28  ; WriteBarrierWrappersStub
    // 0x981ae4: r0 = _initPlayer()
    //     0x981ae4: bl              #0x981b28  ; [package:youtube_player_iframe/src/widgets/youtube_player.dart] _YoutubePlayerState::_initPlayer
    // 0x981ae8: r0 = Null
    //     0x981ae8: mov             x0, NULL
    // 0x981aec: LeaveFrame
    //     0x981aec: mov             SP, fp
    //     0x981af0: ldp             fp, lr, [SP], #0x10
    // 0x981af4: ret
    //     0x981af4: ret             
    // 0x981af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981af8: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981afc: b               #0x981a74
    // 0x981b00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x981b00: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _initPlayer(/* No info */) async {
    // ** addr: 0x981b28, size: 0x16c
    // 0x981b28: EnterFrame
    //     0x981b28: stp             fp, lr, [SP, #-0x10]!
    //     0x981b2c: mov             fp, SP
    // 0x981b30: AllocStack(0x30)
    //     0x981b30: sub             SP, SP, #0x30
    // 0x981b34: SetupParameters(_YoutubePlayerState this /* r1 => r1, fp-0x10 */)
    //     0x981b34: stur            NULL, [fp, #-8]
    //     0x981b38: stur            x1, [fp, #-0x10]
    // 0x981b3c: CheckStackOverflow
    //     0x981b3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x981b40: cmp             SP, x16
    //     0x981b44: b.ls            #0x981c7c
    // 0x981b48: r1 = 1
    //     0x981b48: movz            x1, #0x1
    // 0x981b4c: r0 = AllocateContext()
    //     0x981b4c: bl              #0xec126c  ; AllocateContextStub
    // 0x981b50: mov             x2, x0
    // 0x981b54: ldur            x1, [fp, #-0x10]
    // 0x981b58: stur            x2, [fp, #-0x18]
    // 0x981b5c: StoreField: r2->field_f = r1
    //     0x981b5c: stur            w1, [x2, #0xf]
    // 0x981b60: InitAsync() -> Future<void?>
    //     0x981b60: ldr             x0, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <void?>
    //     0x981b64: bl              #0x661298  ; InitAsyncStub
    // 0x981b68: r0 = LoadStaticField(0x958)
    //     0x981b68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x981b6c: ldr             x0, [x0, #0x12b0]
    // 0x981b70: cmp             w0, NULL
    // 0x981b74: b.eq            #0x981c84
    // 0x981b78: LoadField: r3 = r0->field_53
    //     0x981b78: ldur            w3, [x0, #0x53]
    // 0x981b7c: DecompressPointer r3
    //     0x981b7c: add             x3, x3, HEAP, lsl #32
    // 0x981b80: stur            x3, [fp, #-0x28]
    // 0x981b84: LoadField: r0 = r3->field_7
    //     0x981b84: ldur            w0, [x3, #7]
    // 0x981b88: DecompressPointer r0
    //     0x981b88: add             x0, x0, HEAP, lsl #32
    // 0x981b8c: ldur            x2, [fp, #-0x18]
    // 0x981b90: stur            x0, [fp, #-0x20]
    // 0x981b94: r1 = Function '<anonymous closure>':.
    //     0x981b94: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ae0] AnonymousClosure: (0x9823e8), in [package:youtube_player_iframe/src/widgets/youtube_player.dart] _YoutubePlayerState::_initPlayer (0x981b28)
    //     0x981b98: ldr             x1, [x1, #0xae0]
    // 0x981b9c: r0 = AllocateClosure()
    //     0x981b9c: bl              #0xec1630  ; AllocateClosureStub
    // 0x981ba0: ldur            x2, [fp, #-0x20]
    // 0x981ba4: mov             x3, x0
    // 0x981ba8: r1 = Null
    //     0x981ba8: mov             x1, NULL
    // 0x981bac: stur            x3, [fp, #-0x20]
    // 0x981bb0: cmp             w2, NULL
    // 0x981bb4: b.eq            #0x981bd4
    // 0x981bb8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x981bb8: ldur            w4, [x2, #0x17]
    // 0x981bbc: DecompressPointer r4
    //     0x981bbc: add             x4, x4, HEAP, lsl #32
    // 0x981bc0: r8 = X0
    //     0x981bc0: ldr             x8, [PP, #0x160]  ; [pp+0x160] TypeParameter: X0
    // 0x981bc4: LoadField: r9 = r4->field_7
    //     0x981bc4: ldur            x9, [x4, #7]
    // 0x981bc8: r3 = Null
    //     0x981bc8: add             x3, PP, #0x57, lsl #12  ; [pp+0x57ae8] Null
    //     0x981bcc: ldr             x3, [x3, #0xae8]
    // 0x981bd0: blr             x9
    // 0x981bd4: ldur            x0, [fp, #-0x28]
    // 0x981bd8: LoadField: r1 = r0->field_b
    //     0x981bd8: ldur            w1, [x0, #0xb]
    // 0x981bdc: LoadField: r2 = r0->field_f
    //     0x981bdc: ldur            w2, [x0, #0xf]
    // 0x981be0: DecompressPointer r2
    //     0x981be0: add             x2, x2, HEAP, lsl #32
    // 0x981be4: LoadField: r3 = r2->field_b
    //     0x981be4: ldur            w3, [x2, #0xb]
    // 0x981be8: r2 = LoadInt32Instr(r1)
    //     0x981be8: sbfx            x2, x1, #1, #0x1f
    // 0x981bec: stur            x2, [fp, #-0x30]
    // 0x981bf0: r1 = LoadInt32Instr(r3)
    //     0x981bf0: sbfx            x1, x3, #1, #0x1f
    // 0x981bf4: cmp             x2, x1
    // 0x981bf8: b.ne            #0x981c04
    // 0x981bfc: mov             x1, x0
    // 0x981c00: r0 = _growToNextCapacity()
    //     0x981c00: bl              #0x60883c  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x981c04: ldur            x3, [fp, #-0x10]
    // 0x981c08: ldur            x0, [fp, #-0x28]
    // 0x981c0c: ldur            x2, [fp, #-0x30]
    // 0x981c10: add             x1, x2, #1
    // 0x981c14: lsl             x4, x1, #1
    // 0x981c18: StoreField: r0->field_b = r4
    //     0x981c18: stur            w4, [x0, #0xb]
    // 0x981c1c: LoadField: r1 = r0->field_f
    //     0x981c1c: ldur            w1, [x0, #0xf]
    // 0x981c20: DecompressPointer r1
    //     0x981c20: add             x1, x1, HEAP, lsl #32
    // 0x981c24: ldur            x0, [fp, #-0x20]
    // 0x981c28: ArrayStore: r1[r2] = r0  ; List_4
    //     0x981c28: add             x25, x1, x2, lsl #2
    //     0x981c2c: add             x25, x25, #0xf
    //     0x981c30: str             w0, [x25]
    //     0x981c34: tbz             w0, #0, #0x981c50
    //     0x981c38: ldurb           w16, [x1, #-1]
    //     0x981c3c: ldurb           w17, [x0, #-1]
    //     0x981c40: and             x16, x17, x16, lsr #2
    //     0x981c44: tst             x16, HEAP, lsr #32
    //     0x981c48: b.eq            #0x981c50
    //     0x981c4c: bl              #0xec04dc  ; ArrayWriteBarrierStub
    // 0x981c50: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x981c50: ldur            w1, [x3, #0x17]
    // 0x981c54: DecompressPointer r1
    //     0x981c54: add             x1, x1, HEAP, lsl #32
    // 0x981c58: r16 = Sentinel
    //     0x981c58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x981c5c: cmp             w1, w16
    // 0x981c60: b.eq            #0x981c88
    // 0x981c64: r0 = init()
    //     0x981c64: bl              #0x981c94  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::init
    // 0x981c68: mov             x1, x0
    // 0x981c6c: stur            x1, [fp, #-0x10]
    // 0x981c70: r0 = Await()
    //     0x981c70: bl              #0x661044  ; AwaitStub
    // 0x981c74: r0 = Null
    //     0x981c74: mov             x0, NULL
    // 0x981c78: r0 = ReturnAsyncNotFuture()
    //     0x981c78: b               #0x660e8c  ; ReturnAsyncNotFutureStub
    // 0x981c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x981c7c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x981c80: b               #0x981b48
    // 0x981c84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x981c84: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x981c88: r9 = _controller
    //     0x981c88: add             x9, PP, #0x57, lsl #12  ; [pp+0x57aa0] Field <_YoutubePlayerState@2483033003._controller@2483033003>: late final (offset: 0x18)
    //     0x981c8c: ldr             x9, [x9, #0xaa0]
    // 0x981c90: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x981c90: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9823e8, size: 0x5c
    // 0x9823e8: EnterFrame
    //     0x9823e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9823ec: mov             fp, SP
    // 0x9823f0: ldr             x0, [fp, #0x18]
    // 0x9823f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9823f4: ldur            w1, [x0, #0x17]
    // 0x9823f8: DecompressPointer r1
    //     0x9823f8: add             x1, x1, HEAP, lsl #32
    // 0x9823fc: CheckStackOverflow
    //     0x9823fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x982400: cmp             SP, x16
    //     0x982404: b.ls            #0x982438
    // 0x982408: LoadField: r0 = r1->field_f
    //     0x982408: ldur            w0, [x1, #0xf]
    // 0x98240c: DecompressPointer r0
    //     0x98240c: add             x0, x0, HEAP, lsl #32
    // 0x982410: LoadField: r1 = r0->field_b
    //     0x982410: ldur            w1, [x0, #0xb]
    // 0x982414: DecompressPointer r1
    //     0x982414: add             x1, x1, HEAP, lsl #32
    // 0x982418: cmp             w1, NULL
    // 0x98241c: b.eq            #0x982440
    // 0x982420: mov             x1, x0
    // 0x982424: r0 = _updateBackgroundColor()
    //     0x982424: bl              #0x982444  ; [package:youtube_player_iframe/src/widgets/youtube_player.dart] _YoutubePlayerState::_updateBackgroundColor
    // 0x982428: r0 = Null
    //     0x982428: mov             x0, NULL
    // 0x98242c: LeaveFrame
    //     0x98242c: mov             SP, fp
    //     0x982430: ldp             fp, lr, [SP], #0x10
    // 0x982434: ret
    //     0x982434: ret             
    // 0x982438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x982438: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98243c: b               #0x982408
    // 0x982440: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x982440: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateBackgroundColor(/* No info */) {
    // ** addr: 0x982444, size: 0xac
    // 0x982444: EnterFrame
    //     0x982444: stp             fp, lr, [SP, #-0x10]!
    //     0x982448: mov             fp, SP
    // 0x98244c: AllocStack(0x8)
    //     0x98244c: sub             SP, SP, #8
    // 0x982450: SetupParameters(_YoutubePlayerState this /* r1 => r0, fp-0x8 */)
    //     0x982450: mov             x0, x1
    //     0x982454: stur            x1, [fp, #-8]
    // 0x982458: CheckStackOverflow
    //     0x982458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98245c: cmp             SP, x16
    //     0x982460: b.ls            #0x9824cc
    // 0x982464: LoadField: r1 = r0->field_f
    //     0x982464: ldur            w1, [x0, #0xf]
    // 0x982468: DecompressPointer r1
    //     0x982468: add             x1, x1, HEAP, lsl #32
    // 0x98246c: cmp             w1, NULL
    // 0x982470: b.eq            #0x9824d4
    // 0x982474: r0 = of()
    //     0x982474: bl              #0x624ea4  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x982478: LoadField: r1 = r0->field_3f
    //     0x982478: ldur            w1, [x0, #0x3f]
    // 0x98247c: DecompressPointer r1
    //     0x98247c: add             x1, x1, HEAP, lsl #32
    // 0x982480: LoadField: r2 = r1->field_7b
    //     0x982480: ldur            w2, [x1, #0x7b]
    // 0x982484: DecompressPointer r2
    //     0x982484: add             x2, x2, HEAP, lsl #32
    // 0x982488: ldur            x0, [fp, #-8]
    // 0x98248c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x98248c: ldur            w1, [x0, #0x17]
    // 0x982490: DecompressPointer r1
    //     0x982490: add             x1, x1, HEAP, lsl #32
    // 0x982494: r16 = Sentinel
    //     0x982494: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x982498: cmp             w1, w16
    // 0x98249c: b.eq            #0x9824d8
    // 0x9824a0: LoadField: r0 = r1->field_f
    //     0x9824a0: ldur            w0, [x1, #0xf]
    // 0x9824a4: DecompressPointer r0
    //     0x9824a4: add             x0, x0, HEAP, lsl #32
    // 0x9824a8: r16 = Sentinel
    //     0x9824a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9824ac: cmp             w0, w16
    // 0x9824b0: b.eq            #0x9824e4
    // 0x9824b4: mov             x1, x0
    // 0x9824b8: r0 = setBackgroundColor()
    //     0x9824b8: bl              #0x9824f0  ; [package:webview_flutter/src/webview_controller.dart] WebViewController::setBackgroundColor
    // 0x9824bc: r0 = Null
    //     0x9824bc: mov             x0, NULL
    // 0x9824c0: LeaveFrame
    //     0x9824c0: mov             SP, fp
    //     0x9824c4: ldp             fp, lr, [SP], #0x10
    // 0x9824c8: ret
    //     0x9824c8: ret             
    // 0x9824cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9824cc: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9824d0: b               #0x982464
    // 0x9824d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9824d4: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9824d8: r9 = _controller
    //     0x9824d8: add             x9, PP, #0x57, lsl #12  ; [pp+0x57aa0] Field <_YoutubePlayerState@2483033003._controller@2483033003>: late final (offset: 0x18)
    //     0x9824dc: ldr             x9, [x9, #0xaa0]
    // 0x9824e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9824e0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9824e4: r9 = webViewController
    //     0x9824e4: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f20] Field <YoutubePlayerController.webViewController>: late final (offset: 0x10)
    //     0x9824e8: ldr             x9, [x9, #0xf20]
    // 0x9824ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9824ec: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x9a2810, size: 0xbc
    // 0x9a2810: EnterFrame
    //     0x9a2810: stp             fp, lr, [SP, #-0x10]!
    //     0x9a2814: mov             fp, SP
    // 0x9a2818: AllocStack(0x10)
    //     0x9a2818: sub             SP, SP, #0x10
    // 0x9a281c: SetupParameters(_YoutubePlayerState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9a281c: mov             x0, x2
    //     0x9a2820: mov             x4, x1
    //     0x9a2824: mov             x3, x2
    //     0x9a2828: stur            x1, [fp, #-8]
    //     0x9a282c: stur            x2, [fp, #-0x10]
    // 0x9a2830: r2 = Null
    //     0x9a2830: mov             x2, NULL
    // 0x9a2834: r1 = Null
    //     0x9a2834: mov             x1, NULL
    // 0x9a2838: r4 = 60
    //     0x9a2838: movz            x4, #0x3c
    // 0x9a283c: branchIfSmi(r0, 0x9a2848)
    //     0x9a283c: tbz             w0, #0, #0x9a2848
    // 0x9a2840: r4 = LoadClassIdInstr(r0)
    //     0x9a2840: ldur            x4, [x0, #-1]
    //     0x9a2844: ubfx            x4, x4, #0xc, #0x14
    // 0x9a2848: r17 = 4685
    //     0x9a2848: movz            x17, #0x124d
    // 0x9a284c: cmp             x4, x17
    // 0x9a2850: b.eq            #0x9a2868
    // 0x9a2854: r8 = YoutubePlayer
    //     0x9a2854: add             x8, PP, #0x57, lsl #12  ; [pp+0x57ab0] Type: YoutubePlayer
    //     0x9a2858: ldr             x8, [x8, #0xab0]
    // 0x9a285c: r3 = Null
    //     0x9a285c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57ab8] Null
    //     0x9a2860: ldr             x3, [x3, #0xab8]
    // 0x9a2864: r0 = YoutubePlayer()
    //     0x9a2864: bl              #0x981b04  ; IsType_YoutubePlayer_Stub
    // 0x9a2868: ldur            x3, [fp, #-8]
    // 0x9a286c: LoadField: r2 = r3->field_7
    //     0x9a286c: ldur            w2, [x3, #7]
    // 0x9a2870: DecompressPointer r2
    //     0x9a2870: add             x2, x2, HEAP, lsl #32
    // 0x9a2874: ldur            x0, [fp, #-0x10]
    // 0x9a2878: r1 = Null
    //     0x9a2878: mov             x1, NULL
    // 0x9a287c: cmp             w2, NULL
    // 0x9a2880: b.eq            #0x9a28a4
    // 0x9a2884: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9a2884: ldur            w4, [x2, #0x17]
    // 0x9a2888: DecompressPointer r4
    //     0x9a2888: add             x4, x4, HEAP, lsl #32
    // 0x9a288c: r8 = X0 bound StatefulWidget
    //     0x9a288c: add             x8, PP, #0x23, lsl #12  ; [pp+0x237f8] TypeParameter: X0 bound StatefulWidget
    //     0x9a2890: ldr             x8, [x8, #0x7f8]
    // 0x9a2894: LoadField: r9 = r4->field_7
    //     0x9a2894: ldur            x9, [x4, #7]
    // 0x9a2898: r3 = Null
    //     0x9a2898: add             x3, PP, #0x57, lsl #12  ; [pp+0x57ac8] Null
    //     0x9a289c: ldr             x3, [x3, #0xac8]
    // 0x9a28a0: blr             x9
    // 0x9a28a4: ldur            x1, [fp, #-8]
    // 0x9a28a8: LoadField: r2 = r1->field_b
    //     0x9a28a8: ldur            w2, [x1, #0xb]
    // 0x9a28ac: DecompressPointer r2
    //     0x9a28ac: add             x2, x2, HEAP, lsl #32
    // 0x9a28b0: cmp             w2, NULL
    // 0x9a28b4: b.eq            #0x9a28c8
    // 0x9a28b8: r0 = Null
    //     0x9a28b8: mov             x0, NULL
    // 0x9a28bc: LeaveFrame
    //     0x9a28bc: mov             SP, fp
    //     0x9a28c0: ldp             fp, lr, [SP], #0x10
    // 0x9a28c4: ret
    //     0x9a28c4: ret             
    // 0x9a28c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9a28c8: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xa5143c, size: 0x160
    // 0xa5143c: EnterFrame
    //     0xa5143c: stp             fp, lr, [SP, #-0x10]!
    //     0xa51440: mov             fp, SP
    // 0xa51444: AllocStack(0x28)
    //     0xa51444: sub             SP, SP, #0x28
    // 0xa51448: SetupParameters(_YoutubePlayerState this /* r1 => r0, fp-0x8 */)
    //     0xa51448: mov             x0, x1
    //     0xa5144c: stur            x1, [fp, #-8]
    // 0xa51450: CheckStackOverflow
    //     0xa51450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa51454: cmp             SP, x16
    //     0xa51458: b.ls            #0xa51574
    // 0xa5145c: r1 = 2
    //     0xa5145c: movz            x1, #0x2
    // 0xa51460: r0 = AllocateContext()
    //     0xa51460: bl              #0xec126c  ; AllocateContextStub
    // 0xa51464: ldur            x2, [fp, #-8]
    // 0xa51468: stur            x0, [fp, #-0x18]
    // 0xa5146c: StoreField: r0->field_f = r2
    //     0xa5146c: stur            w2, [x0, #0xf]
    // 0xa51470: LoadField: r1 = r2->field_b
    //     0xa51470: ldur            w1, [x2, #0xb]
    // 0xa51474: DecompressPointer r1
    //     0xa51474: add             x1, x1, HEAP, lsl #32
    // 0xa51478: cmp             w1, NULL
    // 0xa5147c: b.eq            #0xa5157c
    // 0xa51480: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa51480: ldur            w1, [x2, #0x17]
    // 0xa51484: DecompressPointer r1
    //     0xa51484: add             x1, x1, HEAP, lsl #32
    // 0xa51488: r16 = Sentinel
    //     0xa51488: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa5148c: cmp             w1, w16
    // 0xa51490: b.eq            #0xa51580
    // 0xa51494: LoadField: r3 = r1->field_f
    //     0xa51494: ldur            w3, [x1, #0xf]
    // 0xa51498: DecompressPointer r3
    //     0xa51498: add             x3, x3, HEAP, lsl #32
    // 0xa5149c: r16 = Sentinel
    //     0xa5149c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa514a0: cmp             w3, w16
    // 0xa514a4: b.eq            #0xa5158c
    // 0xa514a8: stur            x3, [fp, #-0x10]
    // 0xa514ac: r0 = WebViewWidget()
    //     0xa514ac: bl              #0xa51890  ; AllocateWebViewWidgetStub -> WebViewWidget (size=0x10)
    // 0xa514b0: mov             x1, x0
    // 0xa514b4: ldur            x2, [fp, #-0x10]
    // 0xa514b8: stur            x0, [fp, #-0x10]
    // 0xa514bc: r0 = WebViewWidget()
    //     0xa514bc: bl              #0xa515d0  ; [package:webview_flutter/src/webview_widget.dart] WebViewWidget::WebViewWidget
    // 0xa514c0: ldur            x0, [fp, #-0x10]
    // 0xa514c4: ldur            x2, [fp, #-0x18]
    // 0xa514c8: StoreField: r2->field_13 = r0
    //     0xa514c8: stur            w0, [x2, #0x13]
    //     0xa514cc: ldurb           w16, [x2, #-1]
    //     0xa514d0: ldurb           w17, [x0, #-1]
    //     0xa514d4: and             x16, x17, x16, lsr #2
    //     0xa514d8: tst             x16, HEAP, lsr #32
    //     0xa514dc: b.eq            #0xa514e4
    //     0xa514e0: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa514e4: ldur            x0, [fp, #-8]
    // 0xa514e8: LoadField: r1 = r0->field_b
    //     0xa514e8: ldur            w1, [x0, #0xb]
    // 0xa514ec: DecompressPointer r1
    //     0xa514ec: add             x1, x1, HEAP, lsl #32
    // 0xa514f0: cmp             w1, NULL
    // 0xa514f4: b.eq            #0xa51598
    // 0xa514f8: r0 = GestureDetector()
    //     0xa514f8: bl              #0x7e5874  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa514fc: ldur            x2, [fp, #-8]
    // 0xa51500: r1 = Function '_fullscreenGesture@2483033003':.
    //     0xa51500: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a88] AnonymousClosure: (0xa5199c), in [package:youtube_player_iframe/src/widgets/youtube_player.dart] _YoutubePlayerState::_fullscreenGesture (0xa519d8)
    //     0xa51504: ldr             x1, [x1, #0xa88]
    // 0xa51508: stur            x0, [fp, #-8]
    // 0xa5150c: r0 = AllocateClosure()
    //     0xa5150c: bl              #0xec1630  ; AllocateClosureStub
    // 0xa51510: ldur            x16, [fp, #-0x10]
    // 0xa51514: stp             x16, x0, [SP]
    // 0xa51518: ldur            x1, [fp, #-8]
    // 0xa5151c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onVerticalDragUpdate, 0x1, null]
    //     0xa5151c: add             x4, PP, #0x57, lsl #12  ; [pp+0x57a90] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onVerticalDragUpdate", 0x1, Null]
    //     0xa51520: ldr             x4, [x4, #0xa90]
    // 0xa51524: r0 = GestureDetector()
    //     0xa51524: bl              #0x7e5134  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa51528: ldur            x0, [fp, #-8]
    // 0xa5152c: ldur            x2, [fp, #-0x18]
    // 0xa51530: StoreField: r2->field_13 = r0
    //     0xa51530: stur            w0, [x2, #0x13]
    //     0xa51534: ldurb           w16, [x2, #-1]
    //     0xa51538: ldurb           w17, [x0, #-1]
    //     0xa5153c: and             x16, x17, x16, lsr #2
    //     0xa51540: tst             x16, HEAP, lsr #32
    //     0xa51544: b.eq            #0xa5154c
    //     0xa51548: bl              #0xec0a48  ; WriteBarrierWrappersStub
    // 0xa5154c: r1 = Function '<anonymous closure>':.
    //     0xa5154c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57a98] AnonymousClosure: (0xa5189c), in [package:youtube_player_iframe/src/widgets/youtube_player.dart] _YoutubePlayerState::build (0xa5143c)
    //     0xa51550: ldr             x1, [x1, #0xa98]
    // 0xa51554: r0 = AllocateClosure()
    //     0xa51554: bl              #0xec1630  ; AllocateClosureStub
    // 0xa51558: stur            x0, [fp, #-8]
    // 0xa5155c: r0 = OrientationBuilder()
    //     0xa5155c: bl              #0xa515c4  ; AllocateOrientationBuilderStub -> OrientationBuilder (size=0x10)
    // 0xa51560: ldur            x1, [fp, #-8]
    // 0xa51564: StoreField: r0->field_b = r1
    //     0xa51564: stur            w1, [x0, #0xb]
    // 0xa51568: LeaveFrame
    //     0xa51568: mov             SP, fp
    //     0xa5156c: ldp             fp, lr, [SP], #0x10
    // 0xa51570: ret
    //     0xa51570: ret             
    // 0xa51574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa51574: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa51578: b               #0xa5145c
    // 0xa5157c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5157c: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa51580: r9 = _controller
    //     0xa51580: add             x9, PP, #0x57, lsl #12  ; [pp+0x57aa0] Field <_YoutubePlayerState@2483033003._controller@2483033003>: late final (offset: 0x18)
    //     0xa51584: ldr             x9, [x9, #0xaa0]
    // 0xa51588: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa51588: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa5158c: r9 = webViewController
    //     0xa5158c: add             x9, PP, #0x47, lsl #12  ; [pp+0x47f20] Field <YoutubePlayerController.webViewController>: late final (offset: 0x10)
    //     0xa51590: ldr             x9, [x9, #0xf20]
    // 0xa51594: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa51594: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa51598: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51598: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AspectRatio <anonymous closure>(dynamic, BuildContext, Orientation) {
    // ** addr: 0xa5189c, size: 0xbc
    // 0xa5189c: EnterFrame
    //     0xa5189c: stp             fp, lr, [SP, #-0x10]!
    //     0xa518a0: mov             fp, SP
    // 0xa518a4: AllocStack(0x18)
    //     0xa518a4: sub             SP, SP, #0x18
    // 0xa518a8: SetupParameters()
    //     0xa518a8: ldr             x0, [fp, #0x20]
    //     0xa518ac: ldur            w2, [x0, #0x17]
    //     0xa518b0: add             x2, x2, HEAP, lsl #32
    //     0xa518b4: stur            x2, [fp, #-8]
    // 0xa518b8: CheckStackOverflow
    //     0xa518b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa518bc: cmp             SP, x16
    //     0xa518c0: b.ls            #0xa5194c
    // 0xa518c4: ldr             x0, [fp, #0x10]
    // 0xa518c8: r16 = Instance_Orientation
    //     0xa518c8: add             x16, PP, #0x29, lsl #12  ; [pp+0x29a38] Obj!Orientation@e34341
    //     0xa518cc: ldr             x16, [x16, #0xa38]
    // 0xa518d0: cmp             w0, w16
    // 0xa518d4: b.ne            #0xa518f8
    // 0xa518d8: ldr             x1, [fp, #0x18]
    // 0xa518dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa518dc: ldr             x4, [PP, #0x250]  ; [pp+0x250] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa518e0: r0 = _of()
    //     0xa518e0: bl              #0x6a7e74  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa518e4: LoadField: r1 = r0->field_7
    //     0xa518e4: ldur            w1, [x0, #7]
    // 0xa518e8: DecompressPointer r1
    //     0xa518e8: add             x1, x1, HEAP, lsl #32
    // 0xa518ec: r0 = aspectRatio()
    //     0xa518ec: bl              #0xa51958  ; [dart:ui] Size::aspectRatio
    // 0xa518f0: ldur            x0, [fp, #-8]
    // 0xa518f4: b               #0xa5191c
    // 0xa518f8: mov             x0, x2
    // 0xa518fc: LoadField: r1 = r0->field_f
    //     0xa518fc: ldur            w1, [x0, #0xf]
    // 0xa51900: DecompressPointer r1
    //     0xa51900: add             x1, x1, HEAP, lsl #32
    // 0xa51904: LoadField: r2 = r1->field_b
    //     0xa51904: ldur            w2, [x1, #0xb]
    // 0xa51908: DecompressPointer r2
    //     0xa51908: add             x2, x2, HEAP, lsl #32
    // 0xa5190c: cmp             w2, NULL
    // 0xa51910: b.eq            #0xa51954
    // 0xa51914: d0 = 1.777778
    //     0xa51914: add             x17, PP, #0x38, lsl #12  ; [pp+0x38120] IMM: double(1.7777777777777777) from 0x3ffc71c71c71c71c
    //     0xa51918: ldr             d0, [x17, #0x120]
    // 0xa5191c: stur            d0, [fp, #-0x18]
    // 0xa51920: LoadField: r1 = r0->field_13
    //     0xa51920: ldur            w1, [x0, #0x13]
    // 0xa51924: DecompressPointer r1
    //     0xa51924: add             x1, x1, HEAP, lsl #32
    // 0xa51928: stur            x1, [fp, #-0x10]
    // 0xa5192c: r0 = AspectRatio()
    //     0xa5192c: bl              #0x9d2c98  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xa51930: ldur            d0, [fp, #-0x18]
    // 0xa51934: StoreField: r0->field_f = d0
    //     0xa51934: stur            d0, [x0, #0xf]
    // 0xa51938: ldur            x1, [fp, #-0x10]
    // 0xa5193c: StoreField: r0->field_b = r1
    //     0xa5193c: stur            w1, [x0, #0xb]
    // 0xa51940: LeaveFrame
    //     0xa51940: mov             SP, fp
    //     0xa51944: ldp             fp, lr, [SP], #0x10
    // 0xa51948: ret
    //     0xa51948: ret             
    // 0xa5194c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5194c: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa51950: b               #0xa518c4
    // 0xa51954: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa51954: bl              #0xec29ac  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _fullscreenGesture(dynamic, DragUpdateDetails) {
    // ** addr: 0xa5199c, size: 0x3c
    // 0xa5199c: EnterFrame
    //     0xa5199c: stp             fp, lr, [SP, #-0x10]!
    //     0xa519a0: mov             fp, SP
    // 0xa519a4: ldr             x0, [fp, #0x18]
    // 0xa519a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa519a8: ldur            w1, [x0, #0x17]
    // 0xa519ac: DecompressPointer r1
    //     0xa519ac: add             x1, x1, HEAP, lsl #32
    // 0xa519b0: CheckStackOverflow
    //     0xa519b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa519b4: cmp             SP, x16
    //     0xa519b8: b.ls            #0xa519d0
    // 0xa519bc: ldr             x2, [fp, #0x10]
    // 0xa519c0: r0 = _fullscreenGesture()
    //     0xa519c0: bl              #0xa519d8  ; [package:youtube_player_iframe/src/widgets/youtube_player.dart] _YoutubePlayerState::_fullscreenGesture
    // 0xa519c4: LeaveFrame
    //     0xa519c4: mov             SP, fp
    //     0xa519c8: ldp             fp, lr, [SP], #0x10
    // 0xa519cc: ret
    //     0xa519cc: ret             
    // 0xa519d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa519d0: bl              #0xec2404  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa519d4: b               #0xa519bc
  }
  _ _fullscreenGesture(/* No info */) {
    // ** addr: 0xa519d8, size: 0xdc
    // 0xa519d8: EnterFrame
    //     0xa519d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa519dc: mov             fp, SP
    // 0xa519e0: d0 = 0.000000
    //     0xa519e0: eor             v0.16b, v0.16b, v0.16b
    // 0xa519e4: CheckStackOverflow
    //     0xa519e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa519e8: cmp             SP, x16
    //     0xa519ec: b.ls            #0xa51a94
    // 0xa519f0: LoadField: r0 = r2->field_b
    //     0xa519f0: ldur            w0, [x2, #0xb]
    // 0xa519f4: DecompressPointer r0
    //     0xa519f4: add             x0, x0, HEAP, lsl #32
    // 0xa519f8: LoadField: d1 = r0->field_f
    //     0xa519f8: ldur            d1, [x0, #0xf]
    // 0xa519fc: fcmp            d1, d0
    // 0xa51a00: b.ne            #0xa51a0c
    // 0xa51a04: d2 = 0.000000
    //     0xa51a04: eor             v2.16b, v2.16b, v2.16b
    // 0xa51a08: b               #0xa51a24
    // 0xa51a0c: fcmp            d0, d1
    // 0xa51a10: b.le            #0xa51a1c
    // 0xa51a14: fneg            d0, d1
    // 0xa51a18: b               #0xa51a20
    // 0xa51a1c: mov             v0.16b, v1.16b
    // 0xa51a20: mov             v2.16b, v0.16b
    // 0xa51a24: d0 = 10.000000
    //     0xa51a24: fmov            d0, #10.00000000
    // 0xa51a28: fcmp            d2, d0
    // 0xa51a2c: b.le            #0xa51a84
    // 0xa51a30: fcmp            d1, #0.0
    // 0xa51a34: b.vs            #0xa51a68
    // 0xa51a38: b.ne            #0xa51a44
    // 0xa51a3c: r0 = 0.000000
    //     0xa51a3c: fmov            x0, d1
    // 0xa51a40: cmp             x0, #0
    // 0xa51a44: b.ge            #0xa51a68
    // 0xa51a48: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa51a48: ldur            w0, [x1, #0x17]
    // 0xa51a4c: DecompressPointer r0
    //     0xa51a4c: add             x0, x0, HEAP, lsl #32
    // 0xa51a50: r16 = Sentinel
    //     0xa51a50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa51a54: cmp             w0, w16
    // 0xa51a58: b.eq            #0xa51a9c
    // 0xa51a5c: mov             x1, x0
    // 0xa51a60: r0 = enterFullScreen()
    //     0xa51a60: bl              #0x97ba50  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::enterFullScreen
    // 0xa51a64: b               #0xa51a84
    // 0xa51a68: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa51a68: ldur            w0, [x1, #0x17]
    // 0xa51a6c: DecompressPointer r0
    //     0xa51a6c: add             x0, x0, HEAP, lsl #32
    // 0xa51a70: r16 = Sentinel
    //     0xa51a70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa51a74: cmp             w0, w16
    // 0xa51a78: b.eq            #0xa51aa8
    // 0xa51a7c: mov             x1, x0
    // 0xa51a80: r0 = exitFullScreen()
    //     0xa51a80: bl              #0x97be4c  ; [package:youtube_player_iframe/src/controller/youtube_player_controller.dart] YoutubePlayerController::exitFullScreen
    // 0xa51a84: r0 = Null
    //     0xa51a84: mov             x0, NULL
    // 0xa51a88: LeaveFrame
    //     0xa51a88: mov             SP, fp
    //     0xa51a8c: ldp             fp, lr, [SP], #0x10
    // 0xa51a90: ret
    //     0xa51a90: ret             
    // 0xa51a94: r0 = StackOverflowSharedWithFPURegs()
    //     0xa51a94: bl              #0xec2484  ; StackOverflowSharedWithFPURegsStub
    // 0xa51a98: b               #0xa519f0
    // 0xa51a9c: r9 = _controller
    //     0xa51a9c: add             x9, PP, #0x57, lsl #12  ; [pp+0x57aa0] Field <_YoutubePlayerState@2483033003._controller@2483033003>: late final (offset: 0x18)
    //     0xa51aa0: ldr             x9, [x9, #0xaa0]
    // 0xa51aa4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa51aa4: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa51aa8: r9 = _controller
    //     0xa51aa8: add             x9, PP, #0x57, lsl #12  ; [pp+0x57aa0] Field <_YoutubePlayerState@2483033003._controller@2483033003>: late final (offset: 0x18)
    //     0xa51aac: ldr             x9, [x9, #0xaa0]
    // 0xa51ab0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa51ab0: bl              #0xec2c34  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4685, size: 0x28, field offset: 0xc
//   const constructor, 
class YoutubePlayer extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xa955b8, size: 0x2c
    // 0xa955b8: EnterFrame
    //     0xa955b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa955bc: mov             fp, SP
    // 0xa955c0: mov             x0, x1
    // 0xa955c4: r1 = <YoutubePlayer>
    //     0xa955c4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51278] TypeArguments: <YoutubePlayer>
    //     0xa955c8: ldr             x1, [x1, #0x278]
    // 0xa955cc: r0 = _YoutubePlayerState()
    //     0xa955cc: bl              #0xa955e4  ; Allocate_YoutubePlayerStateStub -> _YoutubePlayerState (size=0x1c)
    // 0xa955d0: r1 = Sentinel
    //     0xa955d0: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa955d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xa955d4: stur            w1, [x0, #0x17]
    // 0xa955d8: LeaveFrame
    //     0xa955d8: mov             SP, fp
    //     0xa955dc: ldp             fp, lr, [SP], #0x10
    // 0xa955e0: ret
    //     0xa955e0: ret             
  }
}
